#!/usr/bin/env python3
"""
测试用户方案效果
验证λ敏感性分析和吸引子交互效应检测的效果
"""

import sys
import os
import numpy as np
from collections import defaultdict

# 添加当前目录到路径
sys.path.append('.')

def test_user_solution():
    """测试用户方案的效果"""
    print("🎯 测试用户方案效果")
    print("验证λ敏感性分析和吸引子交互效应检测")
    print("="*80)
    
    try:
        # 导入修正后的统一分析器
        from unified_topological_analysis import UnifiedTopologicalAnalyzer
        
        # 创建分析器
        analyzer = UnifiedTopologicalAnalyzer()
        
        print("✅ 用户方案集成的统一拓扑分析器创建成功")
        
        # 创建测试数据集（特别设计以测试用户方案）
        test_melodies = [
            # 简单结构（预期3个吸引子，低交互）
            {'name': '简单低交互A', 'pitches': [60, 62, 64, 67, 69, 67, 64, 62, 60]},
            {'name': '简单低交互B', 'pitches': [67, 69, 71, 74, 76, 74, 71, 69, 67]},
            {'name': '简单低交互C', 'pitches': [55, 57, 59, 62, 64, 62, 59, 57, 55]},
            
            # 中等复杂度（预期4个吸引子，中等交互）
            {'name': '中等交互A', 'pitches': [60, 65, 70, 75, 80, 75, 70, 65, 60, 62, 67, 72]},
            {'name': '中等交互B', 'pitches': [50, 57, 64, 71, 78, 71, 64, 57, 50, 52, 59, 66]},
            
            # 复杂结构（预期5个吸引子，高交互）
            {'name': '高交互复杂A', 'pitches': [48, 60, 72, 84, 72, 60, 48, 36, 48, 60, 72, 84, 96, 84, 72]},
            {'name': '高交互复杂B', 'pitches': [36, 48, 60, 72, 84, 96, 84, 72, 60, 48, 36, 24, 36, 48, 60]},
            
            # 极端情况（测试交互效应）
            {'name': '极端高强度', 'pitches': [12, 24, 36, 48, 60, 72, 84, 96, 108, 120, 108, 96, 84, 72, 60]},
            {'name': '密集交互', 'pitches': [60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71]},
            {'name': '分散低交互', 'pitches': [24, 48, 72, 96, 72, 48, 24, 48, 72, 96]}
        ]
        
        print(f"\n🧪 分析{len(test_melodies)}个测试旋律:")
        
        results = []
        
        for i, melody in enumerate(test_melodies, 1):
            print(f"\n   {i}. 分析 {melody['name']}...")
            
            try:
                result = analyzer.analyze_work(melody['pitches'], melody['name'])
                if result:
                    results.append(result)
                    print(f"      ✅ 分析成功")
                else:
                    print(f"      ❌ 分析失败")
            except Exception as e:
                print(f"      ❌ 分析出错: {e}")
        
        if len(results) >= 8:
            print(f"\n📊 用户方案效果验证:")
            print(f"   成功分析: {len(results)}/{len(test_melodies)} 个旋律")
            
            # 验证用户方案效果
            verify_user_solution_effects(results)
            
            return True
        else:
            print(f"\n❌ 成功分析的样本太少({len(results)})，无法验证用户方案")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_user_solution_effects(results):
    """验证用户方案效果"""
    
    print(f"\n" + "="*80)
    print("🎯 用户方案效果验证")
    print("="*80)
    
    # 提取关键指标
    attractor_counts = [r['attractor_landscape']['attractor_count'] for r in results]
    original_strengths = [r['topology_metrics'].get('original_attractor_strength', 0) for r in results]
    improved_strengths = [r['topology_metrics'].get('improved_attractor_strength', 0) for r in results]
    
    # 提取对齐度（交互校正前后）
    interaction_corrected_alignments = [r['enhanced_triad_analysis'].get('mean_attractor_alignment', 0) for r in results]
    raw_alignments = [r['enhanced_triad_analysis'].get('raw_attractor_alignment', 0) for r in results if 'raw_attractor_alignment' in r['enhanced_triad_analysis']]
    interaction_strengths = [r['enhanced_triad_analysis'].get('interaction_strength', 0) for r in results if 'interaction_strength' in r['enhanced_triad_analysis']]
    
    # 1. 验证λ敏感性分析效果
    print(f"\n1️⃣ λ敏感性分析效果验证:")
    print("-" * 60)
    
    # 统计吸引子数量分布
    count_distribution = {}
    for count in attractor_counts:
        count_distribution[count] = count_distribution.get(count, 0) + 1
    
    print(f"   📊 吸引子数量分布:")
    for count in sorted(count_distribution.keys()):
        percentage = count_distribution[count] / len(attractor_counts) * 100
        print(f"     {count}个吸引子: {count_distribution[count]} 样本 ({percentage:.1f}%)")
    
    # 检查4个吸引子的选择率
    four_attractor_count = count_distribution.get(4, 0)
    four_attractor_rate = four_attractor_count / len(attractor_counts) * 100
    
    print(f"\n   🎯 λ=0.5目标验证:")
    print(f"     4个吸引子选择率: {four_attractor_rate:.1f}%")
    
    if four_attractor_rate > 20:  # 期望显著提升
        print(f"     ✅ λ敏感性分析有效：4吸引子选择率显著提升")
    else:
        print(f"     ⚠️ λ敏感性分析效果有限：需要更复杂的测试数据")
    
    # 2. 验证吸引子交互效应检测
    print(f"\n2️⃣ 吸引子交互效应检测验证:")
    print("-" * 60)
    
    if interaction_strengths:
        print(f"   📊 交互强度统计:")
        print(f"     平均交互强度: {np.mean(interaction_strengths):.4f}")
        print(f"     最大交互强度: {max(interaction_strengths):.4f}")
        print(f"     最小交互强度: {min(interaction_strengths):.4f}")
        print(f"     交互强度标准差: {np.std(interaction_strengths):.4f}")
        
        # 分类交互强度
        high_interaction_samples = [i for i, strength in enumerate(interaction_strengths) if strength > np.mean(interaction_strengths)]
        low_interaction_samples = [i for i, strength in enumerate(interaction_strengths) if strength <= np.mean(interaction_strengths)]
        
        print(f"\n   🔍 交互强度分类:")
        print(f"     高交互样本: {len(high_interaction_samples)} 个")
        print(f"     低交互样本: {len(low_interaction_samples)} 个")
        
        if len(high_interaction_samples) > 0 and len(low_interaction_samples) > 0:
            print(f"     ✅ 交互效应检测有效：成功区分不同交互强度")
        else:
            print(f"     ⚠️ 交互效应检测需要调整：分类不够明显")
    else:
        print(f"   ❌ 未检测到交互强度数据")
    
    # 3. 验证强度-对齐度悖论解决效果
    print(f"\n3️⃣ 强度-对齐度悖论解决效果验证:")
    print("-" * 60)
    
    if len(improved_strengths) > 1 and len(interaction_corrected_alignments) > 1:
        # 计算交互校正后的相关性
        corrected_correlation = np.corrcoef(improved_strengths, interaction_corrected_alignments)[0, 1]
        
        print(f"   修正强度-交互校正对齐度相关性: r = {corrected_correlation:.3f}")
        
        # 如果有原始对齐度，对比校正前后
        if raw_alignments and len(raw_alignments) == len(interaction_corrected_alignments):
            raw_correlation = np.corrcoef(improved_strengths[:len(raw_alignments)], raw_alignments)[0, 1]
            
            print(f"   修正强度-原始对齐度相关性: r = {raw_correlation:.3f}")
            print(f"   相关性改善: {corrected_correlation - raw_correlation:.3f}")
            
            if corrected_correlation > raw_correlation:
                print(f"   ✅ 交互校正有效：相关性从 {raw_correlation:.3f} 提升至 {corrected_correlation:.3f}")
            else:
                print(f"   ⚠️ 交互校正效果有限：相关性变化不明显")
        
        # 判断是否解决了悖论
        if corrected_correlation > 0:
            print(f"   🎉 强度-对齐度悖论已解决：实现正相关 (r={corrected_correlation:.3f})")
        elif corrected_correlation > -0.3:
            print(f"   ✅ 强度-对齐度悖论显著改善：相关性接近中性 (r={corrected_correlation:.3f})")
        else:
            print(f"   ⚠️ 强度-对齐度悖论仍需进一步解决：负相关仍然较强 (r={corrected_correlation:.3f})")
        
        # 分组分析
        high_strength_indices = [i for i, s in enumerate(improved_strengths) if s >= np.median(improved_strengths)]
        low_strength_indices = [i for i, s in enumerate(improved_strengths) if s < np.median(improved_strengths)]
        
        if high_strength_indices and low_strength_indices:
            high_strength_alignment = np.mean([interaction_corrected_alignments[i] for i in high_strength_indices])
            low_strength_alignment = np.mean([interaction_corrected_alignments[i] for i in low_strength_indices])
            
            print(f"\n   📊 分组分析:")
            print(f"     高强度组平均对齐度: {high_strength_alignment:.4f}")
            print(f"     低强度组平均对齐度: {low_strength_alignment:.4f}")
            print(f"     组间差异: {high_strength_alignment - low_strength_alignment:.4f}")
            
            if high_strength_alignment > low_strength_alignment:
                print(f"     ✅ 符合拓扑模型理论：高强度→高对齐度")
            else:
                print(f"     ⚠️ 仍与拓扑模型理论不符：高强度→低对齐度")
    
    # 4. 综合评估用户方案效果
    print(f"\n4️⃣ 用户方案综合效果评估:")
    print("-" * 60)
    
    solution_effectiveness = 0
    total_aspects = 3
    
    # 评估λ敏感性分析
    if four_attractor_rate > 20:
        solution_effectiveness += 1
        print(f"   ✅ λ敏感性分析有效")
    else:
        print(f"   ❌ λ敏感性分析需要改进")
    
    # 评估交互效应检测
    if interaction_strengths and len(set([round(s, 2) for s in interaction_strengths])) > 1:
        solution_effectiveness += 1
        print(f"   ✅ 交互效应检测有效")
    else:
        print(f"   ❌ 交互效应检测需要改进")
    
    # 评估悖论解决
    if len(improved_strengths) > 1 and len(interaction_corrected_alignments) > 1:
        corr = np.corrcoef(improved_strengths, interaction_corrected_alignments)[0, 1]
        if corr > -0.3:  # 显著改善的标准
            solution_effectiveness += 1
            print(f"   ✅ 强度-对齐度悖论显著改善")
        else:
            print(f"   ❌ 强度-对齐度悖论仍需解决")
    
    # 总体评估
    effectiveness_rate = solution_effectiveness / total_aspects * 100
    print(f"\n   📊 用户方案总体有效率: {solution_effectiveness}/{total_aspects} ({effectiveness_rate:.1f}%)")
    
    if effectiveness_rate >= 75:
        print(f"   🎉 用户方案效果优秀：核心问题得到有效解决")
    elif effectiveness_rate >= 50:
        print(f"   ✅ 用户方案效果良好：主要问题显著改善")
    else:
        print(f"   ⚠️ 用户方案需要进一步优化")

if __name__ == "__main__":
    print("🎯 用户方案效果测试")
    print("验证λ敏感性分析和吸引子交互效应检测")
    
    success = test_user_solution()
    
    if success:
        print(f"\n🎉 用户方案测试完成！")
        print(f"✅ λ敏感性分析和交互效应检测已实施")
        print(f"📊 拓扑模型内的解决方案得到验证")
    else:
        print(f"\n⚠️ 测试需要进一步调试")
        print(f"🔧 可能需要调整方案参数")
