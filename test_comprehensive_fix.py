#!/usr/bin/env python3
"""
测试综合分析修复
"""

import sys
import os

# 添加当前目录到路径
sys.path.append('.')

# 导入分析器
from enhanced_music_analysis_complete import EnhancedMusicAnalyzer

def test_comprehensive_fix():
    """测试综合分析修复"""
    print("🧪 测试综合分析修复")
    print("="*50)
    
    try:
        # 创建分析器
        analyzer = EnhancedMusicAnalyzer()
        
        # 测试单个作品分析
        test_pitch_series = [60, 62, 64, 65, 67, 69, 71, 72, 74, 76, 77, 79, 81, 83, 84]
        
        print("🎵 测试单个作品分析...")
        result = analyzer.analyze_single_work(test_pitch_series, "测试旋律")
        
        if result:
            print("✅ 单个作品分析成功")
            print(f"   作品名: {result['work_name']}")
            print(f"   音符数: {result['note_count']}")
        else:
            print("❌ 单个作品分析失败")
            return False
        
        # 测试批量分析
        test_works = [
            ("测试旋律1", [60, 62, 64, 65, 67, 69, 71, 72]),
            ("测试旋律2", [72, 71, 69, 67, 65, 64, 62, 60]),
            ("测试旋律3", [60, 67, 64, 71, 68, 65, 62, 69])
        ]
        
        print("\n🎼 测试批量分析...")
        successful_count = analyzer.analyze_multiple_works(test_works)
        
        if successful_count > 0:
            print(f"✅ 批量分析成功，分析了 {successful_count} 首作品")
            
            # 测试统计信息
            stats = analyzer.get_analysis_statistics()
            print(f"📊 统计信息: {stats}")
            
            return True
        else:
            print("❌ 批量分析失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_comprehensive_fix()
    if success:
        print("\n🎉 所有测试通过！综合分析修复成功！")
    else:
        print("\n❌ 测试失败，需要进一步修复")
