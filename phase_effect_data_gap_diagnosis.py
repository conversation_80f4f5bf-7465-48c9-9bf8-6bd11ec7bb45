#!/usr/bin/env python3
"""
跨层级相位效应数据缺口诊断
分析最严重的信息遗漏异常
"""

import numpy as np
import sys
from scipy import stats
sys.path.append('.')

def analyze_data_gap_severity():
    """分析数据缺口的严重程度"""
    print("🔍 跨层级相位效应数据缺口诊断")
    print("分析最严重的信息遗漏异常")
    print("="*80)
    
    # 报告的极有限数据
    phase_effect_data = {
        'mean': 0.1651,
        'std': 0.1167,
        'cv': 0.1167 / 0.1651,
        'missing_info': [
            '范围(最小值-最大值)',
            '分布形态(偏度、峰度)',
            '正态性检验结果',
            '统计检验详情',
            '离群点信息',
            '置信区间',
            '效应量',
            '理论期望对比'
        ]
    }
    
    print("\n1. 📊 数据完整性评估")
    print("-" * 60)
    
    print(f"   已提供信息:")
    print(f"      平均效应显著性: {phase_effect_data['mean']:.4f}")
    print(f"      效应显著性标准差: {phase_effect_data['std']:.4f}")
    print(f"      变异系数: {phase_effect_data['cv']:.1%}")
    
    print(f"\n   🚨 严重缺失信息:")
    for i, missing in enumerate(phase_effect_data['missing_info'], 1):
        print(f"      {i}. {missing}")
    
    print(f"\n   📊 数据完整性评分:")
    provided_info = 2  # 均值和标准差
    total_required = len(phase_effect_data['missing_info']) + provided_info
    completeness_score = (provided_info / total_required) * 100
    
    print(f"      提供信息: {provided_info}/{total_required}")
    print(f"      完整性评分: {completeness_score:.1f}%")
    print(f"      评级: {'严重不足' if completeness_score < 30 else '不足' if completeness_score < 60 else '基本完整'}")

def analyze_cv_warning_signal():
    """分析变异系数的警示信号"""
    print(f"\n2. 🚨 变异系数警示信号分析")
    print("-" * 60)
    
    # 与其他指标的CV对比
    cv_comparison = {
        '跨层级相位效应': 70.7,
        '吸引子强度': 95.9,
        '对齐度': 18.7,
        '收敛比例': 11.1,
        '吸引子数量': 23.1
    }
    
    print("   变异系数排序:")
    sorted_cv = sorted(cv_comparison.items(), key=lambda x: x[1], reverse=True)
    
    for i, (indicator, cv) in enumerate(sorted_cv, 1):
        if cv > 70:
            level = "极端变异性"
            warning = "🚨"
        elif cv > 50:
            level = "高变异性"
            warning = "⚠️"
        elif cv > 30:
            level = "中等变异性"
            warning = "📊"
        else:
            level = "低变异性"
            warning = "✅"
        
        print(f"     {i}. {warning} {indicator}: {cv:.1f}% ({level})")
    
    print(f"\n   🔍 跨层级相位效应特征:")
    print(f"      • CV=70.7%，仅次于吸引子强度的95.9%")
    print(f"      • 属于极端变异性范畴")
    print(f"      • 强烈暗示高度偏态分布")
    print(f"      • 可能包含极端离群值")
    print(f"      • 数据异质性极高")

def investigate_distribution_implications():
    """调查分布特征的含义"""
    print(f"\n3. 📈 分布特征推断")
    print("-" * 60)
    
    mean_val = 0.1651
    std_val = 0.1167
    cv_val = 70.7
    
    print("   基于CV=70.7%的分布推断:")
    
    # 估计可能的分布特征
    print(f"\n   🎯 可能的分布类型:")
    distribution_types = {
        '右偏分布': {
            'description': '少数极高值拉高均值',
            'evidence': 'CV>50%通常表明右偏',
            'implication': '大多数样本效应较低，少数样本效应极高',
            'likelihood': 'high'
        },
        '双峰分布': {
            'description': '两个不同的效应群体',
            'evidence': '极高变异性可能来自群体差异',
            'implication': '存在高效应和低效应两类作品',
            'likelihood': 'medium'
        },
        '均匀分布': {
            'description': '效应值在宽范围内均匀分布',
            'evidence': 'CV约58%对应均匀分布',
            'implication': '效应显著性缺乏集中趋势',
            'likelihood': 'low'
        },
        '指数分布': {
            'description': '大量低值，少量高值',
            'evidence': 'CV=100%对应指数分布',
            'implication': '效应显著性呈指数衰减模式',
            'likelihood': 'medium'
        }
    }
    
    for dist_type, details in distribution_types.items():
        print(f"\n   📌 {dist_type}:")
        print(f"      描述: {details['description']}")
        print(f"      证据: {details['evidence']}")
        print(f"      含义: {details['implication']}")
        print(f"      可能性: {details['likelihood']}")
    
    # 估计可能的范围
    print(f"\n   📊 估计数据范围:")
    
    # 基于不同分布假设估计范围
    if cv_val > 70:  # 高度偏态
        # 假设右偏分布
        estimated_min = max(0, mean_val - 2 * std_val)
        estimated_max = mean_val + 3 * std_val  # 右偏分布右尾更长
        
        print(f"      假设右偏分布:")
        print(f"        估计最小值: {estimated_min:.4f}")
        print(f"        估计最大值: {estimated_max:.4f}")
        print(f"        估计范围: {estimated_max - estimated_min:.4f}")
        print(f"        范围/均值比: {(estimated_max - estimated_min)/mean_val:.1f}倍")

def analyze_missing_critical_info():
    """分析缺失的关键信息"""
    print(f"\n4. 🔍 缺失关键信息分析")
    print("-" * 60)
    
    critical_missing = {
        '范围信息': {
            'importance': 'critical',
            'impact': '无法检测极端值和离群点',
            'consequence': '数据质量无法验证',
            'audit_risk': 'high'
        },
        '分布形态': {
            'importance': 'critical',
            'impact': '无法选择合适的统计方法',
            'consequence': '统计推断可能错误',
            'audit_risk': 'high'
        },
        '正态性检验': {
            'importance': 'high',
            'impact': '无法确定参数vs非参数方法',
            'consequence': '统计检验选择错误',
            'audit_risk': 'medium'
        },
        '统计检验': {
            'importance': 'high',
            'impact': '无法评估统计显著性',
            'consequence': '科学结论缺乏支撑',
            'audit_risk': 'high'
        },
        '离群点分析': {
            'importance': 'medium',
            'impact': '异常样本未被识别',
            'consequence': '可能遗漏重要发现',
            'audit_risk': 'medium'
        }
    }
    
    print("   缺失信息影响评估:")
    
    for info_type, details in critical_missing.items():
        risk_symbol = "🚨" if details['audit_risk'] == 'high' else "⚠️" if details['audit_risk'] == 'medium' else "📊"
        importance_symbol = "🔴" if details['importance'] == 'critical' else "🟡" if details['importance'] == 'high' else "🟢"
        
        print(f"\n   {risk_symbol} {info_type} {importance_symbol}:")
        print(f"      重要性: {details['importance']}")
        print(f"      影响: {details['impact']}")
        print(f"      后果: {details['consequence']}")
        print(f"      审计风险: {details['audit_risk']}")

def investigate_polarization_hypothesis():
    """调查两极分化假设"""
    print(f"\n5. 🎯 两极分化假设调查")
    print("-" * 60)
    
    print("   基于CV=70.7%的两极分化分析:")
    
    mean_val = 0.1651
    std_val = 0.1167
    
    # 模拟两极分化情况
    print(f"\n   📊 可能的两极分化模式:")
    
    polarization_scenarios = {
        '高-低两极': {
            'description': '一部分样本效应极高，一部分极低',
            'high_group': f'效应 > {mean_val + std_val:.3f}',
            'low_group': f'效应 < {mean_val - std_val:.3f}',
            'middle_group': f'中间值稀少',
            'evidence': 'CV=70.7%支持此模式'
        },
        '显著-非显著分化': {
            'description': '统计显著vs非显著的明确分化',
            'high_group': '效应 > 0.05 (显著)',
            'low_group': '效应 < 0.01 (非常显著)',
            'middle_group': '0.01-0.05 (边界区域)',
            'evidence': '效应显著性的自然分界'
        },
        '强-弱效应分化': {
            'description': '强效应作品vs弱效应作品',
            'high_group': '效应 > 0.3 (强效应)',
            'low_group': '效应 < 0.1 (弱效应)',
            'middle_group': '0.1-0.3 (中等效应)',
            'evidence': '音乐结构复杂性差异'
        }
    }
    
    for scenario, details in polarization_scenarios.items():
        print(f"\n   📌 {scenario}:")
        print(f"      描述: {details['description']}")
        print(f"      高值组: {details['high_group']}")
        print(f"      低值组: {details['low_group']}")
        print(f"      中间组: {details['middle_group']}")
        print(f"      证据: {details['evidence']}")
    
    print(f"\n   🚨 两极分化的危险:")
    print(f"      • 均值0.1651可能掩盖了复杂现实")
    print(f"      • 可能存在两种完全不同的作品类型")
    print(f"      • 统一的统计描述可能误导")
    print(f"      • 需要分层分析而非整体分析")

def recommend_immediate_actions():
    """推荐立即行动"""
    print(f"\n6. 🚀 立即行动建议")
    print("-" * 60)
    
    immediate_actions = {
        '数据完整性修复': {
            'priority': 'critical',
            'actions': [
                '提取完整的效应显著性数据',
                '计算范围、分位数、偏度、峰度',
                '进行正态性检验',
                '识别和分析离群点'
            ],
            'timeline': '立即'
        },
        '分布特征分析': {
            'priority': 'high',
            'actions': [
                '绘制直方图和箱线图',
                '检查分布形态(右偏/双峰/均匀)',
                '验证两极分化假设',
                '分析分布的音乐学意义'
            ],
            'timeline': '24小时内'
        },
        '统计检验补充': {
            'priority': 'high',
            'actions': [
                '与理论期望值比较',
                '与随机基线比较',
                '计算效应量和置信区间',
                '选择合适的统计方法'
            ],
            'timeline': '48小时内'
        },
        '异常样本调查': {
            'priority': 'medium',
            'actions': [
                '识别极高效应样本',
                '识别极低效应样本',
                '分析异常样本的音乐特征',
                '建立效应强度的预测模型'
            ],
            'timeline': '1周内'
        }
    }
    
    for action_category, details in immediate_actions.items():
        priority_symbol = "🚨" if details['priority'] == 'critical' else "⚠️" if details['priority'] == 'high' else "📊"
        
        print(f"\n   {priority_symbol} {action_category} ({details['priority']}):")
        print(f"      时间线: {details['timeline']}")
        print(f"      行动:")
        for action in details['actions']:
            print(f"        • {action}")
    
    print(f"\n🎯 核心目标:")
    print(f"   将'数据黑洞'转化为'洞察来源'")
    print(f"   从'信息遗漏'提升到'深度分析'")
    print(f"   建立跨层级相位效应的完整画像")

if __name__ == "__main__":
    print("🔍 跨层级相位效应数据缺口诊断")
    print("分析最严重的信息遗漏异常")
    
    # 1. 数据缺口严重程度
    analyze_data_gap_severity()
    
    # 2. 变异系数警示信号
    analyze_cv_warning_signal()
    
    # 3. 分布特征推断
    investigate_distribution_implications()
    
    # 4. 缺失关键信息
    analyze_missing_critical_info()
    
    # 5. 两极分化假设
    investigate_polarization_hypothesis()
    
    # 6. 立即行动建议
    recommend_immediate_actions()
    
    print(f"\n🎉 诊断完成！")
    print(f"✅ 识别了最严重的数据缺口问题")
    print(f"🔧 提供了全面的修复方案")
    print(f"📊 建立了数据完整性评估框架")
