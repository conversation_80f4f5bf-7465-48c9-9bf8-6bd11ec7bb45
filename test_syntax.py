#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试语法是否正确
"""

def test_syntax():
    """测试语法"""
    try:
        # 尝试编译文件
        with open('unified_topological_analysis.py', 'r') as f:
            code = f.read()
        
        compile(code, 'unified_topological_analysis.py', 'exec')
        print("✅ 语法检查通过！")
        
        # 检查是否还有time相关的错误
        if 'step_start = time.time()' in code:
            print("❌ 仍有time.time()代码")
            return False
        
        if 'step_time = time.time()' in code:
            print("❌ 仍有time.time()代码")
            return False
            
        print("✅ 所有time相关代码已清理")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        print(f"   行号: {e.lineno}")
        print(f"   位置: {e.offset}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    print("🔍 检查unified_topological_analysis.py语法...")
    success = test_syntax()
    if success:
        print("🎉 修复成功！现在可以运行了")
        print("使用命令: python3 unified_topological_analysis.py")
    else:
        print("❌ 仍有问题需要修复")
