#!/usr/bin/env python3
"""
测试修正后的吸引子强度计算
验证分母效应校正是否有效
"""

import sys
import os
import numpy as np

# 添加当前目录到路径
sys.path.append('.')

def test_corrected_strength():
    """测试修正后的吸引子强度计算"""
    print("🧪 测试修正后的吸引子强度计算")
    print("验证分母效应校正是否有效")
    print("="*80)
    
    try:
        # 导入修正后的统一分析器
        from unified_topological_analysis import UnifiedTopologicalAnalyzer
        
        # 创建分析器
        analyzer = UnifiedTopologicalAnalyzer()
        
        print("✅ 修正后的统一拓扑分析器创建成功")
        print("✅ 已集成分母效应校正")
        
        # 创建测试数据集（不同复杂度，预期不同吸引子数量）
        test_melodies = [
            {
                'name': '简单旋律(预期3个吸引子)',
                'pitches': [60, 62, 64, 67, 69, 67, 64, 62, 60],
                'expected_k': 3,
                'description': '简单五声音阶'
            },
            {
                'name': '中等复杂旋律(预期4个吸引子)', 
                'pitches': [60, 62, 64, 65, 67, 69, 71, 72, 71, 69, 67, 65, 64, 62, 60],
                'expected_k': 4,
                'description': '包含半音的复杂旋律'
            },
            {
                'name': '复杂旋律(预期5个吸引子)',
                'pitches': [48, 52, 55, 60, 64, 67, 72, 76, 79, 84, 79, 76, 72, 67, 64, 60, 55, 52, 48],
                'expected_k': 5,
                'description': '大跨度复杂旋律'
            }
        ]
        
        print(f"\n🧪 分析{len(test_melodies)}个测试旋律:")
        
        results = []
        
        for i, melody in enumerate(test_melodies, 1):
            print(f"\n   {i}. 分析 {melody['name']}...")
            print(f"      预期吸引子数: {melody['expected_k']}")
            print(f"      描述: {melody['description']}")
            
            try:
                result = analyzer.analyze_work(melody['pitches'], melody['name'])
                if result:
                    # 提取强度相关数据
                    original_strength = result['topology_metrics'].get('original_attractor_strength', 0)
                    corrected_strength = result['topology_metrics'].get('improved_attractor_strength', 0)
                    correction_factor = result['topology_metrics'].get('denominator_correction_factor', 1)
                    attractor_count = result['attractor_landscape']['attractor_count']
                    
                    print(f"      ✅ 实际吸引子数: {attractor_count}")
                    print(f"      📊 原始强度: {original_strength:.4f}")
                    print(f"      🔧 修正强度: {corrected_strength:.4f}")
                    print(f"      📈 校正系数: {correction_factor:.3f}")
                    print(f"      💡 强度提升: {(corrected_strength/original_strength-1)*100:.1f}%")
                    
                    results.append({
                        'name': melody['name'],
                        'expected_k': melody['expected_k'],
                        'actual_k': attractor_count,
                        'original_strength': original_strength,
                        'corrected_strength': corrected_strength,
                        'correction_factor': correction_factor
                    })
                else:
                    print(f"      ❌ 分析失败")
            except Exception as e:
                print(f"      ❌ 分析出错: {e}")
        
        if len(results) >= 2:
            print(f"\n📊 分母效应校正效果分析:")
            print(f"   成功分析: {len(results)}/{len(test_melodies)} 个旋律")
            
            # 分析校正效果
            print(f"\n🔍 校正前后对比:")
            for r in results:
                print(f"   {r['name']}:")
                print(f"     吸引子数: {r['actual_k']} (预期{r['expected_k']})")
                print(f"     原始强度: {r['original_strength']:.4f}")
                print(f"     修正强度: {r['corrected_strength']:.4f}")
                print(f"     校正系数: {r['correction_factor']:.3f}")
                
                # 分析分母效应
                theoretical_denominator_effect = 1.0 / r['actual_k']
                actual_strength_ratio = r['original_strength']
                
                print(f"     理论分母效应: 1/{r['actual_k']} = {theoretical_denominator_effect:.3f}")
                print(f"     实际强度比例: {actual_strength_ratio:.3f}")
                print()
            
            # 检查校正是否减少了分母效应
            original_strengths = [r['original_strength'] for r in results]
            corrected_strengths = [r['corrected_strength'] for r in results]
            attractor_counts = [r['actual_k'] for r in results]
            
            # 计算与吸引子数量的相关性
            if len(set(attractor_counts)) > 1:  # 确保有变异
                original_corr = np.corrcoef(attractor_counts, original_strengths)[0, 1]
                corrected_corr = np.corrcoef(attractor_counts, corrected_strengths)[0, 1]
                
                print(f"📈 分母效应校正效果:")
                print(f"   原始强度与吸引子数量相关性: {original_corr:.3f}")
                print(f"   修正强度与吸引子数量相关性: {corrected_corr:.3f}")
                print(f"   相关性变化: {abs(corrected_corr) - abs(original_corr):.3f}")
                
                if abs(corrected_corr) < abs(original_corr):
                    print(f"   ✅ 校正有效：减少了分母效应")
                else:
                    print(f"   ⚠️ 校正效果有限：需要进一步调整")
            
            # 变异系数分析
            original_cv = np.std(original_strengths) / np.mean(original_strengths)
            corrected_cv = np.std(corrected_strengths) / np.mean(corrected_strengths)
            
            print(f"\n📊 变异性分析:")
            print(f"   原始强度CV: {original_cv:.1%}")
            print(f"   修正强度CV: {corrected_cv:.1%}")
            print(f"   CV变化: {corrected_cv - original_cv:.1%}")
            
            if corrected_cv < original_cv:
                print(f"   ✅ 校正减少了极端变异性")
            else:
                print(f"   ⚠️ 变异性仍然较高")
            
            return True
        else:
            print(f"\n❌ 成功分析的样本太少({len(results)})，无法评估校正效果")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_correction_theory():
    """分析校正理论"""
    print(f"\n" + "="*80)
    print("📚 分母效应校正理论分析")
    print("="*80)
    
    print("🎯 校正公式:")
    print("   修正强度 = 原始强度 × √(吸引子数量)")
    print("   其中：原始强度 = (主导权重/吸引子数量) × 音高跨度 × 集中度")
    
    print(f"\n🧮 校正效果模拟:")
    for k in range(3, 6):
        original = 1.0 / k  # 模拟分母效应
        corrected = original * np.sqrt(k)
        print(f"   {k}个吸引子: 原始={original:.3f} → 修正={corrected:.3f} (提升{(corrected/original-1)*100:.0f}%)")
    
    print(f"\n💡 校正原理:")
    print(f"   • 平方根校正部分补偿分母效应")
    print(f"   • 保持相对关系，但减少极端差异")
    print(f"   • 使不同吸引子数量的强度更具可比性")
    
    print(f"\n⚠️ 注意事项:")
    print(f"   • 校正不能完全消除分母效应")
    print(f"   • 需要重新解释强度的物理意义")
    print(f"   • 应该报告原始值和修正值")

if __name__ == "__main__":
    print("🧪 修正后吸引子强度测试")
    print("验证分母效应校正的有效性")
    
    # 1. 主要测试
    success = test_corrected_strength()
    
    # 2. 理论分析
    analyze_correction_theory()
    
    if success:
        print(f"\n🎉 分母效应校正测试完成！")
        print(f"✅ 校正方法已成功实施")
        print(f"📊 可以观察到强度计算的改善")
        print(f"🔧 减少了分母效应的影响")
    else:
        print(f"\n⚠️ 测试需要进一步调试")
        print(f"🔧 可能需要调整校正参数")
