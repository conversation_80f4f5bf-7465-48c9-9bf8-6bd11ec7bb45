#!/usr/bin/env python3
"""
测试参数诚实性
验证对"完全无参数化"错误主张的修正
"""

import sys
import os
import numpy as np

# 添加当前目录到路径
sys.path.append('.')

def test_parameter_honesty():
    """测试参数诚实性"""
    print("🔍 测试参数诚实性")
    print("修正'完全无参数化'的错误主张")
    print("="*80)
    
    try:
        # 导入更新后的统一分析器
        from unified_topological_analysis import UnifiedTopologicalAnalyzer
        
        # 创建分析器
        analyzer = UnifiedTopologicalAnalyzer()
        
        print("✅ 参数诚实性集成分析器创建成功")
        
        # 测试一个简单的旋律来获取参数分析
        test_melody = [60, 62, 61, 63, 64, 63, 65, 66, 65]
        
        print(f"\n🎵 测试旋律: {test_melody}")
        
        try:
            result = analyzer.analyze_work(test_melody, "参数诚实性测试")
            
            if result and 'topological_invariants' in result:
                # 分析参数承认结果
                analyze_parameter_acknowledgment(result)
                return True
            else:
                print(f"   ❌ 分析失败")
                return False
                
        except Exception as e:
            print(f"   ❌ 分析出错: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_parameter_acknowledgment(result):
    """分析参数承认结果"""
    
    print(f"\n📊 参数诚实性分析:")
    print("="*80)
    
    topo_inv = result['topological_invariants']
    
    # 检查参数承认
    if 'theoretical_foundation' in topo_inv and 'topological_clarification' in topo_inv['theoretical_foundation']:
        clarification = topo_inv['theoretical_foundation']['topological_clarification']
        
        # 1. 检查参数承认
        if 'parameter_acknowledgment' in clarification:
            param_ack = clarification['parameter_acknowledgment']
            
            print(f"\n1️⃣ 参数清单:")
            print("-" * 60)
            
            if 'parameters_inventory' in param_ack:
                inventory = param_ack['parameters_inventory']
                
                # 严格三音组定义参数
                if 'strict_triad_definition' in inventory:
                    triad_params = inventory['strict_triad_definition']
                    print(f"   📐 严格三音组定义参数:")
                    
                    for param_name, param_info in triad_params.items():
                        print(f"     • {param_name}: {param_info['value']}")
                        print(f"       理论依据: {param_info['rationale']}")
                        print(f"       来源: {param_info['source']}")
                        print(f"       可调整: {'是' if param_info['adjustable'] else '否'}")
                
                # 转调检测参数
                if 'modulation_detection' in inventory:
                    mod_params = inventory['modulation_detection']
                    print(f"\n   🔄 转调检测参数:")
                    
                    for param_name, param_info in mod_params.items():
                        print(f"     • {param_name}: {param_info['value']}")
                        print(f"       理论依据: {param_info['rationale']}")
                        print(f"       来源: {param_info['source']}")
                        print(f"       可调整: {'是' if param_info['adjustable'] else '否'}")
                
                # 螺旋分析参数
                if 'spiral_analysis' in inventory:
                    spiral_params = inventory['spiral_analysis']
                    print(f"\n   🌀 螺旋分析参数:")
                    
                    for param_name, param_info in spiral_params.items():
                        print(f"     • {param_name}: {param_info['value']}")
                        print(f"       理论依据: {param_info['rationale']}")
                        print(f"       来源: {param_info['source']}")
                        print(f"       可调整: {'是' if param_info['adjustable'] else '否'}")
                
                # 中国音乐加权参数
                if 'chinese_music_weighting' in inventory:
                    chinese_params = inventory['chinese_music_weighting']
                    print(f"\n   🎼 中国音乐加权参数:")
                    
                    for param_name, param_info in chinese_params.items():
                        print(f"     • {param_name}: {param_info['value']}")
                        print(f"       理论依据: {param_info['rationale']}")
                        print(f"       来源: {param_info['source']}")
                        print(f"       可调整: {'是' if param_info['adjustable'] else '否'}")
            
            # 2. 参数设定哲学
            print(f"\n2️⃣ 参数设定哲学:")
            print("-" * 60)
            
            if 'parameter_philosophy' in param_ack:
                philosophy = param_ack['parameter_philosophy']
                
                for key, value in philosophy.items():
                    print(f"   • {key}: {value}")
            
            # 3. 诚实评估
            print(f"\n3️⃣ 诚实评估:")
            print("-" * 60)
            
            if 'honest_assessment' in param_ack:
                assessment = param_ack['honest_assessment']
                
                print(f"   ❌ 之前的错误主张: {assessment['previous_claim']}")
                print(f"   ✅ 现实检查: {assessment['reality_check']}")
                print(f"   🔧 修正后的主张: {assessment['corrected_claim']}")
                print(f"   💪 我们的优势: {assessment['advantage']}")
                print(f"   📝 透明度承诺: {assessment['transparency_commitment']}")
            
            # 4. 建议
            if 'recommendation' in param_ack:
                print(f"\n4️⃣ 论文修正建议:")
                print("-" * 60)
                print(f"   📝 {param_ack['recommendation']}")
        
        # 5. 理论修正检查
        if 'theoretical_correction' in clarification:
            correction = clarification['theoretical_correction']
            
            if 'parameter_honesty' in correction:
                print(f"\n5️⃣ 参数诚实性修正:")
                print("-" * 60)
                print(f"   🔧 {correction['parameter_honesty']}")
    
    # 6. 参数数量统计
    print(f"\n6️⃣ 参数统计:")
    print("-" * 60)
    
    # 统计所有参数
    total_parameters = 0
    theory_based_parameters = 0
    adjustable_parameters = 0
    
    if ('theoretical_foundation' in topo_inv and 
        'topological_clarification' in topo_inv['theoretical_foundation'] and
        'parameter_acknowledgment' in topo_inv['theoretical_foundation']['topological_clarification']):
        
        param_ack = topo_inv['theoretical_foundation']['topological_clarification']['parameter_acknowledgment']
        
        if 'parameters_inventory' in param_ack:
            inventory = param_ack['parameters_inventory']
            
            for category, params in inventory.items():
                for param_name, param_info in params.items():
                    total_parameters += 1
                    
                    if 'source' in param_info and '理论' in param_info['source']:
                        theory_based_parameters += 1
                    
                    if param_info.get('adjustable', False):
                        adjustable_parameters += 1
    
    print(f"   📊 总参数数量: {total_parameters}")
    print(f"   📚 基于理论的参数: {theory_based_parameters}")
    print(f"   🔧 可调整参数: {adjustable_parameters}")
    print(f"   📈 理论依据比例: {theory_based_parameters/total_parameters:.1%}" if total_parameters > 0 else "   📈 理论依据比例: 0%")
    
    # 7. 最终评估
    print(f"\n7️⃣ 最终评估:")
    print("-" * 60)
    
    if total_parameters > 0:
        theory_ratio = theory_based_parameters / total_parameters
        
        if theory_ratio >= 0.8:
            print(f"   🎉 参数设定优秀!")
            print(f"     ✅ 大部分参数有理论依据")
            print(f"     ✅ 诚实承认参数存在")
            print(f"     ✅ 提供透明的设定理由")
        elif theory_ratio >= 0.6:
            print(f"   ✅ 参数设定良好")
            print(f"     • 多数参数有理论依据")
            print(f"     • 需要进一步加强理论基础")
        else:
            print(f"   ⚠️ 参数设定需要改进")
            print(f"     • 理论依据不足")
            print(f"     • 需要更多音乐理论支撑")
        
        print(f"\n   🏆 关键成就:")
        print(f"     ✅ 修正了'完全无参数化'的错误主张")
        print(f"     ✅ 诚实承认了方法中的参数")
        print(f"     ✅ 提供了每个参数的理论依据")
        print(f"     ✅ 建立了透明的参数设定框架")
    else:
        print(f"   ❌ 未找到参数信息")

if __name__ == "__main__":
    print("🔍 参数诚实性测试")
    print("修正'完全无参数化'的错误主张")
    
    success = test_parameter_honesty()
    
    if success:
        print(f"\n🎉 参数诚实性测试完成！")
        print(f"✅ 成功修正了错误主张")
        print(f"🔍 建立了透明的参数框架")
    else:
        print(f"\n⚠️ 测试需要进一步调试")
        print(f"🔧 可能需要优化参数承认机制")
