#!/usr/bin/env python3
"""
测试修复后的增强音乐分析系统
验证跨层级效应分析和三音组分析是否正常工作
"""

import sys
import os
import numpy as np

# 添加当前目录到路径
sys.path.append('.')

# 导入分析器
from enhanced_music_analysis_complete import EnhancedMusicAnalyzer

def test_fixed_analysis():
    """测试修复后的分析系统"""
    print("🧪 测试修复后的增强音乐分析系统")
    print("="*60)
    
    # 创建不同特征的测试旋律
    test_melodies = [
        {
            'name': '平滑音阶（低波动性）',
            'pitches': [60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72],
            'expected_features': '低音程均幅，低局部波动性'
        },
        {
            'name': '跳跃旋律（高波动性）',
            'pitches': [60, 72, 48, 75, 45, 78, 42, 81, 39, 84, 36, 87, 33],
            'expected_features': '高音程均幅，高局部波动性'
        },
        {
            'name': '三音组密集型',
            'pitches': [60, 62, 61, 63, 62, 64, 63, 65, 64, 66, 65, 67, 66],
            'expected_features': '高三音组密度，中等波动性'
        },
        {
            'name': '吸引子主导型',
            'pitches': [60, 61, 60, 62, 60, 61, 60, 63, 60, 61, 60, 62, 60],
            'expected_features': '强吸引子，低波动性'
        },
        {
            'name': '混合模式',
            'pitches': [60, 67, 55, 72, 48, 69, 52, 74, 46, 71, 49, 76, 44],
            'expected_features': '中等特征，复杂动力学'
        }
    ]
    
    # 创建分析器
    analyzer = EnhancedMusicAnalyzer()
    
    results = []
    
    for i, melody in enumerate(test_melodies):
        print(f"\n🎵 测试旋律 {i+1}: {melody['name']}")
        print(f"   预期特征: {melody['expected_features']}")
        print(f"   音符序列: {melody['pitches'][:8]}... (共{len(melody['pitches'])}个音符)")
        
        try:
            # 分析旋律
            result = analyzer.analyze_single_work(melody['pitches'], melody['name'])
            
            if result:
                print(f"   ✅ 分析成功")
                
                # 提取关键指标
                dynamics = result.get('dynamics', {})
                triad_significance = result.get('triad_significance', {})
                orthogonality = result.get('orthogonality', {})
                
                # 显示动力系统指标
                print(f"   🌊 动力系统:")
                print(f"      吸引子强度: {dynamics.get('attractor_strength', 0.0):.4f}")
                print(f"      稳定性评分: {dynamics.get('stability_score', 0.0):.4f}")
                print(f"      李雅普诺夫指数: {dynamics.get('lyapunov_exponent', 0.0):.4f}")
                
                # 显示三音组分析
                print(f"   🎯 三音组分析:")
                print(f"      结构密度: {triad_significance.get('structural_density', 0.0):.4f}")
                print(f"      轮廓控制力: {triad_significance.get('contour_control', 0.0):.4f}")
                print(f"      动态贡献度: {triad_significance.get('dynamic_contribution', 0.0):.4f}")
                print(f"      稳定性指数: {triad_significance.get('stability_index', 0.0):.4f}")
                
                # 显示正交性分析
                if orthogonality:
                    local_ortho = orthogonality.get('local_orthogonality', {})
                    cross_level = orthogonality.get('cross_level_orthogonality', {})
                    
                    print(f"   🔄 正交性分析:")
                    print(f"      局部相关性: {local_ortho.get('correlation', 0.0):.4f}")
                    print(f"      评估: {local_ortho.get('assessment', 'N/A')}")
                    
                    # 检查跨层级效应数据
                    attractor_effect = cross_level.get('attractor_effect', {})
                    stability_effect = cross_level.get('stability_effect', {})
                    energy_effect = cross_level.get('energy_effect', {})
                    
                    print(f"      吸引子效应组: {attractor_effect.get('group', 'N/A')}")
                    print(f"      稳定性水平: {stability_effect.get('stability_level', 'N/A')}")
                    print(f"      能量水平: {energy_effect.get('energy_level', 'N/A')}")
                    
                    # 检查数据是否为0的问题
                    intervallic_dist = attractor_effect.get('intervallic_distribution', {})
                    volatility_dist = attractor_effect.get('volatility_distribution', {})
                    
                    print(f"      音程均幅均值: {intervallic_dist.get('mean', 0.0):.4f}")
                    print(f"      波动性均值: {volatility_dist.get('mean', 0.0):.4f}")
                
                results.append({
                    'name': melody['name'],
                    'dynamics': dynamics,
                    'triad_significance': triad_significance,
                    'orthogonality': orthogonality
                })
                
            else:
                print(f"   ❌ 分析失败")
                
        except Exception as e:
            print(f"   ❌ 分析出错: {e}")
            import traceback
            traceback.print_exc()
    
    # 验证修复效果
    print(f"\n" + "="*60)
    print("🎯 修复效果验证")
    print("="*60)
    
    if results:
        print(f"成功分析: {len(results)}/{len(test_melodies)} 个旋律")
        
        # 检查三音组分析的多样性
        triad_densities = [r['triad_significance'].get('structural_density', 0.0) for r in results]
        triad_controls = [r['triad_significance'].get('contour_control', 0.0) for r in results]
        triad_dynamics = [r['triad_significance'].get('dynamic_contribution', 0.0) for r in results]
        triad_stabilities = [r['triad_significance'].get('stability_index', 0.0) for r in results]
        
        print(f"\n📊 三音组分析多样性检查:")
        print(f"   结构密度范围: {min(triad_densities):.4f} ~ {max(triad_densities):.4f}")
        print(f"   结构密度标准差: {np.std(triad_densities):.4f}")
        print(f"   轮廓控制力范围: {min(triad_controls):.4f} ~ {max(triad_controls):.4f}")
        print(f"   轮廓控制力标准差: {np.std(triad_controls):.4f}")
        
        # 检查跨层级效应数据
        print(f"\n🔍 跨层级效应数据检查:")
        non_zero_count = 0
        for r in results:
            ortho = r.get('orthogonality', {})
            cross_level = ortho.get('cross_level_orthogonality', {})
            attractor_effect = cross_level.get('attractor_effect', {})
            intervallic_dist = attractor_effect.get('intervallic_distribution', {})
            volatility_dist = attractor_effect.get('volatility_distribution', {})
            
            intervallic_mean = intervallic_dist.get('mean', 0.0)
            volatility_mean = volatility_dist.get('mean', 0.0)
            
            if intervallic_mean != 0.0 or volatility_mean != 0.0:
                non_zero_count += 1
        
        print(f"   有非零跨层级数据的旋律: {non_zero_count}/{len(results)}")
        
        # 验证修复成功的标准
        triad_diversity = np.std(triad_densities) > 0.01  # 三音组分析有多样性
        cross_level_data = non_zero_count > 0  # 跨层级效应有数据
        
        if triad_diversity and cross_level_data:
            print(f"\n✅ 修复成功！")
            print(f"   ✅ 三音组分析产生了多样化的结果")
            print(f"   ✅ 跨层级效应分析有非零数据")
            return True
        else:
            print(f"\n⚠️ 部分修复成功:")
            print(f"   {'✅' if triad_diversity else '❌'} 三音组分析多样性")
            print(f"   {'✅' if cross_level_data else '❌'} 跨层级效应数据")
            return False
    else:
        print("❌ 没有成功的分析结果")
        return False

if __name__ == "__main__":
    success = test_fixed_analysis()
    if success:
        print("\n🎉 增强音乐分析系统修复成功！")
        print("✅ 跨层级效应分析和三音组分析现在能产生有意义的、多样化的结果")
    else:
        print("\n❌ 修复不完全，需要进一步调试")
