#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def fix_progress_bar():
    """修复进度条，移除复杂的时间监控"""
    
    # 读取文件
    with open('unified_topological_analysis.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 移除所有time相关的代码
    patterns_to_remove = [
        r'step_start = time\.time\(\)',
        r'step_time = time\.time\(\) - step_start',
        r'print\(f".*耗时.*{step_time.*}.*"\)',
        r'total_time = time\.time\(\) - start_time',
        r'print\(f".*总耗时.*{total_time.*}.*"\)',
        r'print\(f".*性能统计.*{total_time.*}.*"\)',
        r'\[步骤\d+/8\]',
        r'import time\s*\n\s*start_time = time\.time\(\)',
        r'⏱️.*耗时.*秒',
        r'⏰.*时间.*',
        r'📈.*性能.*',
    ]
    
    for pattern in patterns_to_remove:
        content = re.sub(pattern, '', content)
    
    # 清理多余的空行
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    
    # 确保进度条代码正确
    progress_code = '''        for i, (work_name, pitch_series) in enumerate(works_data, 1):
            # 简单的进度条
            progress = i / len(works_data)
            bar_length = 30
            filled_length = int(bar_length * progress)
            bar = '█' * filled_length + '░' * (bar_length - filled_length)
            
            print(f"\\n进度: [{bar}] {i}/{len(works_data)} ({progress*100:.1f}%)")
            print(f"正在分析: {work_name}")
            print("-" * 50)
            
            result = self.analyze_work(pitch_series, work_name)
            
            if result:
                all_results.append(result)
                successful_count += 1
                print(f"✅ 完成")
            else:
                print(f"❌ 失败")'''
    
    # 写回文件
    with open('unified_topological_analysis.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 修复完成！")

if __name__ == "__main__":
    fix_progress_bar()
