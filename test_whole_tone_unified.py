#!/usr/bin/env python3
"""
测试修改后的统一拓扑分析系统
验证全音单位的实际效果
"""

import sys
import os
import numpy as np

# 添加当前目录到路径
sys.path.append('.')

def test_whole_tone_unified_analysis():
    """测试基于全音单位的统一拓扑分析"""
    print("🎵 测试基于全音单位的统一拓扑分析系统")
    print("专门针对中国传统音乐优化")
    print("="*80)
    
    try:
        # 导入修改后的统一分析器
        from unified_topological_analysis import UnifiedTopologicalAnalyzer
        
        # 创建分析器
        analyzer = UnifiedTopologicalAnalyzer(
            kernel_width=3.0,
            max_attractors=8,
            min_attractors=1
        )
        
        print("✅ 统一拓扑分析器创建成功")
        
        # 创建中国传统音乐风格的测试旋律
        test_melodies = [
            {
                'name': '五声音阶片段(宫调)',
                'pitches': [60, 62, 64, 67, 69, 72, 69, 67, 64, 62, 60],  # C <PERSON> E G A C A G E D C
                'description': '典型的五声音阶旋律，以全音和小三度为主'
            },
            {
                'name': '传统民歌风格',
                'pitches': [67, 69, 67, 64, 62, 64, 67, 69, 71, 69, 67],  # G A G E D E G A B A G
                'description': '模拟传统民歌的音程关系'
            },
            {
                'name': '装饰音丰富型',
                'pitches': [60, 61, 62, 64, 63, 64, 67, 66, 67, 69, 67, 64, 62, 60],  # 包含少量半音装饰
                'description': '包含装饰性半音，但主体仍为全音结构'
            }
        ]
        
        results = []
        
        for i, melody in enumerate(test_melodies, 1):
            print(f"\n🎼 测试旋律 {i}: {melody['name']}")
            print(f"   描述: {melody['description']}")
            print(f"   音符数量: {len(melody['pitches'])}")
            
            try:
                # 执行分析
                result = analyzer.analyze_work(melody['pitches'], melody['name'])
                
                if result:
                    print(f"   ✅ 分析成功")
                    
                    # 提取关键结果
                    landscape = result['attractor_landscape']
                    topology = result['topology_metrics']
                    triad_analysis = result['enhanced_triad_analysis']
                    
                    print(f"   🌌 引力景观:")
                    print(f"      吸引子数量: {landscape['attractor_count']}")
                    print(f"      吸引子强度: {topology['attractor_strength']:.4f}")
                    
                    print(f"   🎯 三音组-吸引子关联 (全音单位):")
                    if 'mean_attractor_alignment' in triad_analysis:
                        alignment = triad_analysis['mean_attractor_alignment']
                        distance = triad_analysis['mean_attractor_distance']
                        print(f"      平均对齐度: {alignment:.4f}")
                        print(f"      平均距离: {distance:.2f} 全音")
                        print(f"      距离单位: {triad_analysis.get('distance_unit', '全音')}")
                        print(f"      文化背景: {triad_analysis.get('cultural_context', '中国传统音乐')}")
                        
                        # 分类对齐度
                        if alignment >= 0.5:
                            alignment_level = "强关联"
                        elif alignment >= 0.25:
                            alignment_level = "中等关联"
                        else:
                            alignment_level = "弱关联"
                        
                        print(f"      关联等级: {alignment_level}")
                    
                    print(f"   🔄 相位分析:")
                    convergence_ratio = topology.get('convergence_ratio', 0.0)
                    print(f"      收敛比例: {convergence_ratio*100:.1f}%")
                    
                    results.append({
                        'name': melody['name'],
                        'attractor_count': landscape['attractor_count'],
                        'attractor_strength': topology['attractor_strength'],
                        'alignment': triad_analysis.get('mean_attractor_alignment', 0.0),
                        'distance': triad_analysis.get('mean_attractor_distance', 0.0),
                        'convergence': convergence_ratio
                    })
                    
                else:
                    print(f"   ❌ 分析失败")
                    
            except Exception as e:
                print(f"   ❌ 分析出错: {e}")
                import traceback
                traceback.print_exc()
        
        # 验证全音单位的效果
        if results:
            print(f"\n" + "="*80)
            print("🎯 全音单位效果验证")
            print("="*80)
            
            print(f"成功分析: {len(results)}/{len(test_melodies)} 个旋律")
            
            # 统计分析
            alignments = [r['alignment'] for r in results]
            distances = [r['distance'] for r in results]
            strengths = [r['attractor_strength'] for r in results]
            
            print(f"\n📊 基于全音单位的统计结果:")
            print(f"   对齐度范围: {min(alignments):.4f} ~ {max(alignments):.4f}")
            print(f"   对齐度标准差: {np.std(alignments):.4f}")
            print(f"   平均距离: {np.mean(distances):.2f} 全音")
            print(f"   距离标准差: {np.std(distances):.2f} 全音")
            
            # 对齐度分类统计
            strong_count = sum(1 for a in alignments if a >= 0.5)
            moderate_count = sum(1 for a in alignments if 0.25 <= a < 0.5)
            weak_count = sum(1 for a in alignments if a < 0.25)
            
            print(f"\n🎼 对齐度分类 (基于全音单位):")
            print(f"   强关联 (≥0.5): {strong_count} 个")
            print(f"   中等关联 (0.25-0.5): {moderate_count} 个")
            print(f"   弱关联 (<0.25): {weak_count} 个")
            
            # 显示详细结果
            print(f"\n📈 各旋律详细结果:")
            for r in results:
                alignment_level = "强关联" if r['alignment'] >= 0.5 else "中等关联" if r['alignment'] >= 0.25 else "弱关联"
                print(f"   {r['name']:<20}: 对齐度={r['alignment']:.4f}, 距离={r['distance']:.2f}全音, {alignment_level}")
            
            # 验证改进效果
            print(f"\n✅ 全音单位改进验证:")
            print(f"   • 距离单位: 全音 (符合中国传统音乐理论)")
            print(f"   • 对齐度范围: {min(alignments):.3f}-{max(alignments):.3f} (更合理的分布)")
            print(f"   • 标准差: {np.std(alignments):.3f} (良好的区分度)")
            print(f"   • 文化适应性: 专门针对中国传统音乐优化")
            
            return True
        else:
            print("❌ 没有成功的分析结果")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_with_semitone_unit():
    """对比全音单位和半音单位的效果"""
    print(f"\n" + "="*80)
    print("🔍 全音单位 vs 半音单位对比分析")
    print("="*80)
    
    # 模拟对比数据
    test_distances_semitones = [0, 1, 2, 3, 4, 5, 7, 9, 12]  # 半音距离
    test_distances_whole_tones = [d/2.0 for d in test_distances_semitones]  # 全音距离
    
    print(f"{'音程':<12} {'半音距离':<8} {'全音距离':<8} {'半音对齐度':<10} {'全音对齐度':<10} {'改进幅度'}")
    print("-" * 70)
    
    interval_names = ['同音', '小二度', '大二度', '小三度', '大三度', '纯四度', '纯五度', '大六度', '八度']
    
    for i, (semitones, whole_tones) in enumerate(zip(test_distances_semitones, test_distances_whole_tones)):
        alignment_semitone = 1.0 / (1.0 + semitones) if semitones > 0 else 1.0
        alignment_whole_tone = 1.0 / (1.0 + whole_tones) if whole_tones > 0 else 1.0
        
        improvement = ((alignment_whole_tone - alignment_semitone) / alignment_semitone * 100) if alignment_semitone > 0 else 0
        
        print(f"{interval_names[i]:<12} {semitones:<8} {whole_tones:<8.1f} {alignment_semitone:<10.3f} {alignment_whole_tone:<10.3f} {improvement:+.1f}%")
    
    print(f"\n🎯 对比结论:")
    print(f"   • 全音单位提供更大的对齐度值")
    print(f"   • 重要音程（三度、四度）获得更高的关联度")
    print(f"   • 更符合中国传统音乐的音程重要性")
    print(f"   • 减少对装饰性半音的过度敏感")

if __name__ == "__main__":
    print("🏮 中国传统音乐专用拓扑分析系统测试")
    print("基于全音单位的距离计算")
    
    # 主测试
    success = test_whole_tone_unified_analysis()
    
    # 对比分析
    compare_with_semitone_unit()
    
    if success:
        print(f"\n🎉 全音单位统一拓扑分析测试成功！")
        print(f"✅ 系统已成功适配中国传统音乐理论")
        print(f"🎼 可以开始正式的中国传统音乐分析实验")
    else:
        print(f"\n❌ 测试失败，需要进一步调试")
