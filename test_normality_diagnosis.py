#!/usr/bin/env python3
"""
测试对齐度正态性诊断功能
验证正态性异常的诊断和解释
"""

import sys
import os
import numpy as np

# 添加当前目录到路径
sys.path.append('.')

def test_normality_diagnosis():
    """测试正态性诊断功能"""
    print("🧪 测试对齐度正态性诊断功能")
    print("验证正态性异常的诊断和解释")
    print("="*80)
    
    try:
        # 导入修正后的统一分析器
        from unified_topological_analysis import UnifiedTopologicalAnalyzer
        
        # 创建分析器
        analyzer = UnifiedTopologicalAnalyzer()
        
        print("✅ 修正后的统一拓扑分析器创建成功")
        print("✅ 已集成正态性异常诊断功能")
        
        # 创建测试数据集（设计不同的分布特征）
        test_melodies = [
            {
                'name': '正态分布测试1',
                'pitches': [60, 62, 64, 67, 69, 67, 64, 62, 60, 61, 63, 65],
                'description': '中等复杂度，预期产生正态对齐度'
            },
            {
                'name': '正态分布测试2', 
                'pitches': [67, 69, 71, 74, 76, 74, 71, 69, 67, 68, 70, 72],
                'description': '另一个中等复杂度旋律'
            },
            {
                'name': '正态分布测试3',
                'pitches': [55, 57, 59, 62, 64, 62, 59, 57, 55, 56, 58, 60],
                'description': '第三个测试旋律'
            },
            {
                'name': '正态分布测试4',
                'pitches': [72, 74, 76, 79, 81, 79, 76, 74, 72, 73, 75, 77],
                'description': '第四个测试旋律'
            },
            {
                'name': '正态分布测试5',
                'pitches': [48, 50, 52, 55, 57, 55, 52, 50, 48, 49, 51, 53],
                'description': '第五个测试旋律'
            }
        ]
        
        print(f"\n🧪 分析{len(test_melodies)}个测试旋律:")
        
        results = []
        alignment_scores = []
        
        for i, melody in enumerate(test_melodies, 1):
            print(f"\n   {i}. 分析 {melody['name']}...")
            print(f"      描述: {melody['description']}")
            
            try:
                result = analyzer.analyze_work(melody['pitches'], melody['name'])
                if result:
                    # 提取对齐度数据
                    alignment = result['enhanced_triad_analysis'].get('mean_attractor_alignment', 0)
                    
                    print(f"      ✅ 对齐度: {alignment:.4f}")
                    
                    results.append(result)
                    alignment_scores.append(alignment)
                else:
                    print(f"      ❌ 分析失败")
            except Exception as e:
                print(f"      ❌ 分析出错: {e}")
        
        if len(alignment_scores) >= 3:
            print(f"\n📊 批量正态性诊断:")
            print(f"   成功分析: {len(results)}/{len(test_melodies)} 个旋律")
            
            # 执行批量分析摘要（包含正态性诊断）
            print(f"\n🔍 执行批量分析摘要（含正态性诊断）:")
            analyzer._generate_batch_summary(results)
            
            # 额外的正态性分析
            print(f"\n📈 额外正态性分析:")
            
            # 计算基础统计
            mean_align = np.mean(alignment_scores)
            std_align = np.std(alignment_scores, ddof=1)
            cv_align = std_align / mean_align
            
            print(f"   对齐度基础统计:")
            print(f"     均值: {mean_align:.4f}")
            print(f"     标准差: {std_align:.4f}")
            print(f"     变异系数: {cv_align:.1%}")
            print(f"     范围: [{min(alignment_scores):.4f}, {max(alignment_scores):.4f}]")
            
            # 正态性检验
            from scipy.stats import shapiro
            stat, p_value = shapiro(alignment_scores)
            
            print(f"\n   正态性检验结果:")
            print(f"     Shapiro-Wilk统计量: {stat:.4f}")
            print(f"     p值: {p_value:.3f}")
            print(f"     正态性: {'✅ 正态分布' if p_value > 0.05 else '❌ 非正态分布'}")
            
            # 分析正态性的含义
            if p_value > 0.05:
                print(f"\n   🎯 正态性含义分析:")
                print(f"     ✅ 确认: 对齐度呈现正态分布")
                print(f"     📊 稳定性: CV={cv_align:.1%} (相对稳定)")
                print(f"     🔍 可能原因:")
                print(f"       • 中心极限定理: 多个三音组距离的平均")
                print(f"       • 算法平滑: 线性变换和边界约束")
                print(f"       • 音乐特征: 对齐度反映的结构相对稳定")
                
                print(f"\n   💡 诊断结论:")
                print(f"     • 正态性是算法设计的自然结果")
                print(f"     • 平均操作减少了极端变异")
                print(f"     • 这种稳定性可能是优势而非缺陷")
            else:
                print(f"\n   ⚠️ 意外: 测试数据未呈现正态性")
                print(f"     可能原因: 样本量太小或数据特征不同")
            
            return True
        else:
            print(f"\n❌ 成功分析的样本太少({len(alignment_scores)})，无法进行正态性诊断")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_normality_implications():
    """分析正态性的含义"""
    print(f"\n" + "="*80)
    print("📚 对齐度正态性的理论含义")
    print("="*80)
    
    print("🎯 正态性的双重解释:")
    
    interpretations = {
        '算法人工制品': {
            'description': '正态性是计算过程的副产品',
            'evidence': [
                '中心极限定理: 多个距离值的平均',
                '线性变换: 保持正态性',
                '边界约束: 轻微影响但不破坏正态性',
                '平滑效应: 算法天然减少极端值'
            ],
            'implication': '正态性反映算法设计而非音乐特征',
            'action': '需要报告并解释算法影响'
        },
        '真实音乐特征': {
            'description': '正态性反映音乐结构的内在稳定性',
            'evidence': [
                '对齐度衡量结构一致性',
                '音乐作品在对齐维度上相对同质',
                '传统音乐理论支持结构稳定性',
                '与其他指标的差异有音乐学意义'
            ],
            'implication': '正态性揭示了音乐结构的普遍特征',
            'action': '可以作为音乐学发现报告'
        }
    }
    
    for interpretation, details in interpretations.items():
        print(f"\n   📌 {interpretation}:")
        print(f"      描述: {details['description']}")
        print(f"      证据:")
        for evidence in details['evidence']:
            print(f"        • {evidence}")
        print(f"      含义: {details['implication']}")
        print(f"      行动: {details['action']}")
    
    print(f"\n🎼 音乐学视角:")
    print(f"   • 对齐度的稳定性可能反映音乐创作的普遍规律")
    print(f"   • 不同于强度等指标的极端变异，对齐度显示结构一致性")
    print(f"   • 这种一致性可能是中国传统音乐的重要特征")
    
    print(f"\n📊 统计学视角:")
    print(f"   • 正态性使得参数检验更可靠")
    print(f"   • 稳定的变异性便于建立置信区间")
    print(f"   • 与其他指标的对比更有意义")
    
    print(f"\n🔧 方法论建议:")
    print(f"   • 明确报告正态性及其可能原因")
    print(f"   • 提供算法设计对分布的影响分析")
    print(f"   • 将正态性作为方法特征而非缺陷")
    print(f"   • 利用正态性进行更精确的统计推断")

if __name__ == "__main__":
    print("🧪 对齐度正态性诊断测试")
    print("验证正态性异常的诊断功能")
    
    # 1. 主要测试
    success = test_normality_diagnosis()
    
    # 2. 理论含义分析
    analyze_normality_implications()
    
    if success:
        print(f"\n🎉 正态性诊断测试完成！")
        print(f"✅ 正态性异常诊断功能已实施")
        print(f"📊 提供了完整的理论解释")
        print(f"🎼 建立了音乐学与统计学的桥梁")
    else:
        print(f"\n⚠️ 测试需要进一步调试")
        print(f"🔧 可能需要调整诊断参数")
