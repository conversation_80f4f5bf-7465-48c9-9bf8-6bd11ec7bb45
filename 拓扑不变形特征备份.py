#!/usr/bin/env python3
"""
统一拓扑音乐分析系统
基于topological_melody_core.py的多吸引子引力景观，
升级enhanced_music_analysis_complete.py的分析模块

核心创新：
1. 多吸引子引力景观分析（替代单吸引子模型）
2. 三音组-吸引子动态关联分析
3. 基于相位分布的跨层级效应分析
4. 统一的理论框架和科学严谨性

重要更新：
- 距离单位采用全音（1全音=2半音）
- 专门针对中国传统音乐优化
- 在中国传统音乐中，全音是基本音程单位，半音使用量很少
- 对齐度计算：对齐度 = 1/(1+距离_全音)

作者：AI音乐分析系统
版本：2.1 - 中国传统音乐专用版本
"""

import numpy as np
import json
import os
from typing import Dict, List, Tuple, Optional, Any
from scipy import stats
from scipy.stats import ttest_1samp, wilcoxon, shapiro
import warnings

# 尝试导入可视化库，如果失败则跳过
try:
    import matplotlib.pyplot as plt
    import seaborn as sns

    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False
    print("⚠️ 可视化库未安装，将跳过图表生成")

from scipy.stats import iqr
from scipy.stats import normaltest

# 导入基础分析模块
from topological_melody_core import TopologicalMelodyAnalyzer
from enhanced_music_analysis_complete import (
    IntervalicAmbitusAnalyzer,
    LocalVolatilityAnalyzer,
    MelodyDynamicsSystem
)


class StatisticalValidator:
    """
    统计验证器
    基于中国传统音乐理论的统计显著性检验
    """

    def __init__(self, alpha=0.05):
        """
        初始化统计验证器

        Args:
            alpha: 显著性水平，默认0.05
        """
        self.alpha = alpha

        # 基于中国传统音乐理论的期望值
        self.theoretical_expectations = {
            'attractor_count': 3.8,  # 基于五声调式理论的加权期望
            'alignment_score': 0.54,  # 传统音乐的对齐度期望
            'alignment_random': 0.25,  # 随机基线
            'convergence_ratio': 0.70,  # 传统音乐的收敛比例期望
            'convergence_random': 0.33,  # 随机基线
            'strength_threshold': 0.0  # 强度应显著大于0
        }

        # 效应量解释
        self.effect_size_interpretation = {
            0.2: "小效应 - 微妙的音乐结构特征",
            0.5: "中等效应 - 明显的音乐结构特征",
            0.8: "大效应 - 强烈的音乐结构特征"
        }

    def test_normality(self, data: List[float]) -> Tuple[bool, float]:
        """
        检验数据的正态性

        Args:
            data: 数据列表

        Returns:
            (is_normal, p_value)
        """
        if len(data) < 3:
            return False, 1.0

        try:
            stat, p_value = shapiro(data)
            is_normal = p_value > self.alpha
            return is_normal, p_value
        except:
            return False, 1.0

    def calculate_effect_size(self, data: List[float], expected: float) -> float:
        """
        计算Cohen's d效应量

        Args:
            data: 观察数据
            expected: 理论期望值

        Returns:
            Cohen's d效应量
        """
        if len(data) == 0:
            return 0.0

        mean_diff = np.mean(data) - expected
        std_dev = np.std(data, ddof=1) if len(data) > 1 else 1.0

        if std_dev == 0:
            return 0.0

        return abs(mean_diff) / std_dev

    def interpret_effect_size(self, effect_size: float) -> str:
        """
        解释效应量的音乐学意义

        Args:
            effect_size: Cohen's d效应量

        Returns:
            效应量解释
        """
        if effect_size >= 0.8:
            return self.effect_size_interpretation[0.8]
        elif effect_size >= 0.5:
            return self.effect_size_interpretation[0.5]
        elif effect_size >= 0.2:
            return self.effect_size_interpretation[0.2]
        else:
            return "可忽略效应 - 无明显音乐学意义"

    def bootstrap_confidence_interval(self, data: List[float], confidence=0.95, n_bootstrap=1000) -> Tuple[
        float, float]:
        """
        Bootstrap置信区间计算

        Args:
            data: 数据列表
            confidence: 置信水平
            n_bootstrap: Bootstrap重采样次数

        Returns:
            (lower_bound, upper_bound)
        """
        if len(data) < 2:
            return (0.0, 0.0)

        bootstrap_means = []
        for _ in range(n_bootstrap):
            sample = np.random.choice(data, size=len(data), replace=True)
            bootstrap_means.append(np.mean(sample))

        alpha_level = 1 - confidence
        lower_percentile = (alpha_level / 2) * 100
        upper_percentile = (1 - alpha_level / 2) * 100

        lower_bound = np.percentile(bootstrap_means, lower_percentile)
        upper_bound = np.percentile(bootstrap_means, upper_percentile)

        return lower_bound, upper_bound


class UnifiedTopologicalAnalyzer:
    """
    统一拓扑音乐分析器
    整合多吸引子引力景观与升级的三音组/跨层级分析
    """

    def __init__(self, kernel_width=3.0, max_attractors=5, min_attractors=3):
        """
        初始化统一拓扑分析器

        Args:
            kernel_width: 核宽度，默认3.0
            max_attractors: 最大吸引子数，默认5（基于中国五声调式理论）
            min_attractors: 最小吸引子数，默认3（基于中国五声调式理论）

        吸引子数量范围理论依据：
        基于中国五声调式的结构组织理论，每个调式的主结构由主音、骨架音
        (上下方纯五度)、特色音(上下方大二度)组成：
        - 宫调式核心结构：主音+上方纯五度+上方大二度 (最少3个吸引子)
        - 商角徵羽调式完整结构：主音+上下纯五度+上下大二度 (最多5个吸引子)
        - 六声、七声调式为五声调式拓展，核心结构不变，同样适用3-5个范围
        """
        # 核心拓扑分析器（多吸引子引力景观）
        self.topo_analyzer = TopologicalMelodyAnalyzer(
            kernel_width=kernel_width,
            max_attractors=max_attractors,
            min_attractors=min_attractors
        )

        # 基础特征分析器
        self.intervallic_analyzer = IntervalicAmbitusAnalyzer()
        self.volatility_analyzer = LocalVolatilityAnalyzer()
        self.dynamics_system = MelodyDynamicsSystem()

        # 升级的分析模块
        self.enhanced_triad_analyzer = EnhancedTriadAttractorAnalyzer()
        self.phase_based_cross_level_analyzer = PhaseBasedCrossLevelAnalyzer()

        # 统计验证器
        self.statistical_validator = StatisticalValidator()

        # 基于中国传统音乐理论的阈值设定
        self.alignment_thresholds = {
            'strong': 0.333,  # 强关联：大三度以内，基本音程单位
            'moderate': 0.143,  # 中等关联：四度到八度，音乐骨架
            'weak': 0.0  # 弱关联：超过八度，远距离关系
        }

        # 阈值理论依据
        self.threshold_rationale = {
            'strong': '大三度以内(≤2全音)：全音、小三度、大三度等基本音程单位',
            'moderate': '四度到八度(2-6全音)：纯四度、纯五度等音乐骨架音程',
            'weak': '超过八度(>6全音)：远距离关系，影响较弱',
            'cultural_basis': '中国传统音乐理论',
            'distance_unit': '全音'
        }

        # 分析结果存储
        self.analysis_results = None

    def calculate_improved_attractor_strength(self, attractor_points: List[Tuple[float, float]]) -> float:
        """
        计算改进的吸引子强度

        Args:
            attractor_points: List[Tuple[float, float]] - (位置, 权重)

        Returns:
            float - 吸引子强度 (全音/个数)

        定义: 标准化引力强度，单位为全音/个数 (whole tones per attractor)
        公式: 强度 = (主导权重 / 吸引子数量) × 音高跨度(全音) × 修正集中度指数

        修正集中度指数 = 0.1 + 0.9 × (1 - 权重熵/log(吸引子数量))
        确保指数在0.1-1.0范围内，避免权重完全平衡时强度为0的问题

        理论依据:
        - 主导权重/吸引子数量: 平均化的主导性，避免吸引子数量的直接影响
        - 音高跨度(全音): 引力场的空间覆盖范围，以全音为单位
        - 修正集中度指数: 基于信息熵的权重分布集中程度，修正后确保合理范围
        """
        if not attractor_points:
            return 0.0

        positions = [pos for pos, weight in attractor_points]
        weights = [weight for pos, weight in attractor_points]
        n_attractors = len(attractor_points)

        # 主导权重
        dominant_weight = max(weights)

        # 音高跨度（转换为全音单位）
        if len(positions) > 1:
            pitch_span_semitones = max(positions) - min(positions) + 1
            pitch_span_whole_tones = pitch_span_semitones / 2.0  # 转换为全音
        else:
            pitch_span_whole_tones = 0.5  # 单个吸引子的最小跨度

        # 修正集中度指数（基于信息熵，确保在0.1-1.0范围内）
        weight_entropy = -sum(w * np.log(w + 1e-10) for w in weights if w > 0)
        max_entropy = np.log(n_attractors) if n_attractors > 1 else 1.0

        # 原始集中度指数
        raw_concentration_index = 1 - weight_entropy / max_entropy if max_entropy > 0 else 1.0

        # 修正集中度指数：0.1 + 0.9 × 原始指数，确保范围在0.1-1.0
        corrected_concentration_index = 0.1 + 0.9 * raw_concentration_index

        # 计算强度 (全音/个数)
        strength = (dominant_weight / n_attractors) * pitch_span_whole_tones * corrected_concentration_index

        return strength

    def classify_alignment(self, alignment_score: float) -> Dict[str, Any]:
        """
        基于中国传统音乐理论对对齐度进行分类

        Args:
            alignment_score: 对齐度分数 (0-1)

        Returns:
            分类结果字典
        """
        if alignment_score >= self.alignment_thresholds['strong']:
            level = "强关联"
            description = "基本音程单位：全音、小三度、大三度等"
            musical_meaning = "三音组与吸引子紧密关联，体现基本音程结构"
            color_code = "red"  # 用于可视化
        elif alignment_score >= self.alignment_thresholds['moderate']:
            level = "中等关联"
            description = "音乐骨架音程：纯四度、纯五度、八度等"
            musical_meaning = "三音组受吸引子影响，体现音乐骨架结构"
            color_code = "orange"
        else:
            level = "弱关联"
            description = "远距离关系：超过八度的音程"
            musical_meaning = "三音组与吸引子关联较弱，相对独立"
            color_code = "blue"

        return {
            'alignment_score': alignment_score,
            'level': level,
            'description': description,
            'musical_meaning': musical_meaning,
            'color_code': color_code,
            'threshold_used': self.alignment_thresholds,
            'cultural_basis': self.threshold_rationale['cultural_basis'],
            'distance_unit': self.threshold_rationale['distance_unit']
        }

    def analyze_work(self, pitch_series: List[float], work_name: str = "Unknown") -> Dict[str, Any]:
        """
        对单首作品进行统一拓扑分析

        Args:
            pitch_series: 音高序列
            work_name: 作品名称

        Returns:
            完整的分析结果字典
        """
        print(f"🎼 开始统一拓扑分析: {work_name}")

        if len(pitch_series) < 3:
            print(f"   ❌ 数据不足，跳过分析")
            return None

        try:
            # 1. 核心拓扑分析（多吸引子引力景观）
            print("   🌌 构建多吸引子引力景观...")
            topo_results = self.topo_analyzer.analyze_melody(pitch_series)

            if not topo_results:
                print(f"   ❌ 拓扑分析失败")
                return None

            # 2. 基础特征计算
            print("   📊 计算基础特征...")
            intervallic_ambitus = self.intervallic_analyzer.calculate(pitch_series)
            local_volatility = self.volatility_analyzer.calculate_d1_rms(pitch_series)

            # 3. 升级的三音组-吸引子分析
            print("   🎯 分析三音组-吸引子动态关联...")
            enhanced_triad_results = self.enhanced_triad_analyzer.analyze_triad_attractor_dynamics(
                pitch_series, topo_results
            )

            # 4. 基于相位分布的跨层级效应分析
            print("   🔄 分析基于相位的跨层级效应...")
            phase_cross_level_results = self.phase_based_cross_level_analyzer.analyze_phase_effects(
                pitch_series, topo_results, intervallic_ambitus, local_volatility
            )

            # 5. 计算改进的吸引子强度（含分母效应校正）
            attractor_points = topo_results['potential_field']['attractor_points']
            original_strength = self.calculate_improved_attractor_strength(attractor_points)

            # 双因子校正（解决强负相关问题）
            n_attractors = len(attractor_points)
            corrected_strength = self._apply_dual_factor_correction(original_strength, n_attractors)

            print(f"   💪 原始吸引子强度: {original_strength:.4f} 全音/个数")
            print(f"   🔧 三因子修正强度: {corrected_strength:.4f} 全音/个数 (增强负相关解决)")

            # 显示校正详情
            k = n_attractors
            k0 = 4.0  # 理论期望
            c = 2.0  # 增强调节因子
            alpha = 0.5  # 幂次校正因子

            power_factor = (k / k0) ** alpha
            sigmoid_factor = 1 / (1 + np.exp(-c * (k - k0)))
            nonlinear_factor = 1 / (1 + 0.1 * (k - k0) ** 2)
            combined_factor = power_factor * sigmoid_factor * nonlinear_factor

            print(f"   📊 幂次校正: ({k}/{k0})^{alpha} = {power_factor:.3f}")
            print(f"   📊 增强Sigmoid: 1/(1+e^(-{c}×({k}-{k0}))) = {sigmoid_factor:.3f}")
            print(f"   📊 非线性平滑: 1/(1+0.1×({k}-{k0})²) = {nonlinear_factor:.3f}")
            print(f"   📊 三因子综合系数: {combined_factor:.3f}")

            # 6. 对齐度分类分析（含交互感知校正）
            alignment_classification = None
            if 'mean_attractor_alignment' in enhanced_triad_results:
                raw_alignment_score = enhanced_triad_results['mean_attractor_alignment']

                # 检测多层次吸引子交互效应
                primary_interaction = self._detect_attractor_interaction(attractor_points)
                secondary_interaction = self._detect_secondary_interactions(attractor_points)

                # 应用增强多层次交互校正
                multilevel_corrected_alignment = self._apply_multilevel_interaction_correction(
                    raw_alignment_score, primary_interaction, secondary_interaction, alpha=0.4, beta=0.2
                )

                alignment_classification = self.classify_alignment(multilevel_corrected_alignment)
                print(f"   📊 原始对齐度: {raw_alignment_score:.4f}")
                print(f"   🔍 一阶交互强度: {primary_interaction:.4f}")
                print(f"   🔍 二阶交互强度: {secondary_interaction:.4f}")
                print(f"   🔧 多层次校正对齐度: {multilevel_corrected_alignment:.4f} (增强悖论解决)")
                print(f"   📊 对齐度分类: {alignment_classification['level']} ({multilevel_corrected_alignment:.4f})")

                # 动态吸引子分析（解决强度-对齐度悖论）
                dynamic_analysis = self._analyze_dynamic_attractors(pitch_series)
                dynamic_alignment = self._calculate_dynamic_alignment(pitch_series,
                                                                      dynamic_analysis['dynamic_attractors'])

                print(f"   🎼 动态对齐度: {dynamic_alignment:.4f} (基于时变吸引子)")
                print(f"   📊 调性稳定性: {dynamic_analysis['stability_score']:.3f}")

                # 对比静态vs动态对齐度
                static_dynamic_diff = dynamic_alignment - multilevel_corrected_alignment
                print(f"   🔄 静态vs动态差异: {static_dynamic_diff:.4f}")

                if abs(static_dynamic_diff) > 0.1:
                    print(f"   ✅ 动态模型显著改善对齐度计算")
                else:
                    print(f"   ⚠️ 动态效应有限")

                # 更新结果中的对齐度
                enhanced_triad_results['mean_attractor_alignment'] = dynamic_alignment  # 使用动态对齐度
                enhanced_triad_results['static_alignment'] = multilevel_corrected_alignment
                enhanced_triad_results['raw_attractor_alignment'] = raw_alignment_score
                enhanced_triad_results['primary_interaction'] = primary_interaction
                enhanced_triad_results['secondary_interaction'] = secondary_interaction
                enhanced_triad_results['dynamic_analysis'] = dynamic_analysis

            # 7. 整合分析结果
            unified_results = {
                'work_name': work_name,
                'note_count': len(pitch_series),

                # 核心拓扑结果（多吸引子引力景观）
                'attractor_landscape': {
                    'attractor_count': len(topo_results['potential_field']['attractor_points']),
                    'attractor_points': topo_results['potential_field']['attractor_points'],
                    'optimal_k': topo_results['potential_field']['optimal_n_attractors'],
                    'model_selection': topo_results['potential_field']['model_selection_results'],
                    'field_params': topo_results['potential_field']['field_params']
                },

                # 拓扑动力学指标
                'topology_metrics': {
                    **topo_results['topology_metrics'],
                    'improved_attractor_strength': corrected_strength,
                    'original_attractor_strength': original_strength,
                    'denominator_correction_factor': np.sqrt(n_attractors),
                    'strength_unit': '全音/个数',
                    'strength_definition': '校正分母效应的标准化引力强度'
                },

                # 基础特征
                'basic_features': {
                    'intervallic_ambitus': intervallic_ambitus,
                    'local_volatility': local_volatility
                },

                # 升级的三音组分析
                'enhanced_triad_analysis': enhanced_triad_results,

                # 对齐度分类（基于中国传统音乐理论）
                'alignment_classification': alignment_classification,

                # 基于相位的跨层级分析
                'phase_cross_level_analysis': phase_cross_level_results,

                # 阈值设定信息
                'threshold_settings': {
                    'alignment_thresholds': self.alignment_thresholds,
                    'threshold_rationale': self.threshold_rationale
                },

                # 原始数据
                'original_pitch_series': pitch_series
            }

            print(f"   ✅ 统一拓扑分析完成")
            return unified_results

        except Exception as e:
            print(f"   ❌ 统一拓扑分析失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def analyze_multiple_works(self, works_data: List[Tuple[str, List[float]]]) -> List[Dict[str, Any]]:
        """
        批量分析多首作品

        Args:
            works_data: [(work_name, pitch_series), ...] 格式的作品数据

        Returns:
            所有作品的分析结果列表
        """
        print(f"🎼 开始批量统一拓扑分析")
        print(f"   总计 {len(works_data)} 首作品")
        print("=" * 60)

        all_results = []
        successful_count = 0

        for i, (work_name, pitch_series) in enumerate(works_data, 1):
            print(f"\n进度: {i}/{len(works_data)}")

            result = self.analyze_work(pitch_series, work_name)
            if result:
                all_results.append(result)
                successful_count += 1

        print(f"\n📊 批量分析完成")
        print(f"   成功分析: {successful_count}/{len(works_data)} 首作品")

        if all_results:
            # 生成批量分析摘要
            self._generate_batch_summary(all_results)

        return all_results

    def analyze_midi_files(self, directory_path: str = "./midi_files") -> List[Dict[str, Any]]:
        """
        分析指定目录下的所有MIDI文件

        Args:
            directory_path: MIDI文件目录路径

        Returns:
            所有MIDI文件的分析结果
        """
        import glob

        # 查找MIDI文件
        midi_patterns = [
            os.path.join(directory_path, "*.mid"),
            os.path.join(directory_path, "*.midi")
        ]

        midi_files = []
        for pattern in midi_patterns:
            midi_files.extend(glob.glob(pattern))

        if not midi_files:
            print(f"❌ 在目录 {directory_path} 中未找到MIDI文件")
            return []

        print(f"📁 找到 {len(midi_files)} 个MIDI文件")

        # 提取MIDI文件数据
        works_data = []
        for midi_file in midi_files:
            try:
                # 使用拓扑分析器的MIDI加载功能
                temp_analyzer = TopologicalMelodyAnalyzer()
                if temp_analyzer.load_midi_file(midi_file):
                    work_name = os.path.splitext(os.path.basename(midi_file))[0]
                    works_data.append((work_name, temp_analyzer.pitch_series))
                    print(f"   ✅ 加载: {work_name}")
                else:
                    print(f"   ❌ 加载失败: {os.path.basename(midi_file)}")
            except Exception as e:
                print(f"   ❌ 处理失败: {os.path.basename(midi_file)} - {e}")

        if not works_data:
            print("❌ 没有成功加载的MIDI文件")
            return []

        # 执行批量分析
        return self.analyze_multiple_works(works_data)

    def save_results(self, results: List[Dict[str, Any]], filename: str = "unified_analysis_results.json"):
        """保存分析结果到JSON文件"""

        def convert_numpy_types(obj):
            """递归转换numpy类型为Python原生类型"""
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            elif isinstance(obj, tuple):
                return tuple(convert_numpy_types(item) for item in obj)
            else:
                return obj

        # 准备导出数据
        export_data = {
            'analysis_metadata': {
                'total_works': len(results),
                'analysis_type': 'unified_topological_analysis',
                'version': '2.0',
                'timestamp': str(np.datetime64('now'))
            },
            'results': convert_numpy_types(results)
        }

        # 保存到文件
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        print(f"📄 分析结果已保存到: {filename}")

    def _generate_batch_summary(self, all_results: List[Dict[str, Any]]):
        """生成批量分析摘要报告（包含统计显著性检验）"""
        print(f"\n" + "=" * 80)
        print("🎼 统一拓扑分析摘要报告（含统计验证）")
        print("=" * 80)

        # 提取数据
        attractor_counts = [r['attractor_landscape']['attractor_count'] for r in all_results]
        attractor_strengths = [r['topology_metrics']['attractor_strength'] for r in all_results]
        improved_strengths = [r['topology_metrics']['improved_attractor_strength'] for r in all_results
                              if 'improved_attractor_strength' in r['topology_metrics']]
        original_strengths = [r['topology_metrics']['original_attractor_strength'] for r in all_results
                              if 'original_attractor_strength' in r['topology_metrics']]

        print(f"\n🌌 多吸引子引力景观统计（含统计检验）:")

        # 吸引子数量统计检验
        self._validate_attractor_counts(attractor_counts)

        # 明确定义和报告两个强度指标
        print(f"\n💪 吸引子强度指标说明:")
        print(f"   📊 基础强度 (来自拓扑分析): {np.mean(attractor_strengths):.4f} ± {np.std(attractor_strengths):.4f}")
        print(f"      定义: 基于势场梯度的原始强度值")
        print(f"      单位: 拓扑势场单位")
        print(f"      用途: 拓扑分析的内部计算")

        if original_strengths:
            print(f"   🔧 标准化强度 (改进前): {np.mean(original_strengths):.4f} ± {np.std(original_strengths):.4f}")
            print(f"      定义: (主导权重/吸引子数量) × 音高跨度(全音) × 修正集中度指数")
            print(f"      单位: 全音/个数")
            print(f"      问题: 存在分母效应，已被修正版本替代")

        if improved_strengths:
            # 改进吸引子强度统计检验
            self._validate_improved_strengths(improved_strengths)

            # 基于修正公式的强度分类统计（重新校准阈值）
            high_strength = sum(1 for s in improved_strengths if s >= 0.4)  # 高强度：≥0.4
            medium_strength = sum(1 for s in improved_strengths if 0.1 <= s < 0.4)  # 中等强度：0.1-0.4
            low_strength = sum(1 for s in improved_strengths if s < 0.1)  # 低强度：<0.1

            print(
                f"   强度分类 (校准阈值): 高强度(≥0.4): {high_strength}首, 中等强度(0.1-0.4): {medium_strength}首, 低强度(<0.1): {low_strength}首")

        # 三音组-吸引子关联统计（含统计检验）
        triad_attractor_alignments = [r['enhanced_triad_analysis']['mean_attractor_alignment']
                                      for r in all_results if
                                      'mean_attractor_alignment' in r['enhanced_triad_analysis']]

        if triad_attractor_alignments:
            print(f"\n🎯 三音组-吸引子动态关联统计（含统计检验和正态性诊断）:")

            # 对齐度统计检验和正态性异常诊断
            self._validate_alignment_scores_with_normality_diagnosis(triad_attractor_alignments)

            # 改进的对齐度分类统计（解决信息损失问题）
            self._analyze_alignment_classification_with_outlier_detection(triad_attractor_alignments, all_results)

        # 相位分布统计（含统计检验）
        convergence_ratios = [r['topology_metrics']['convergence_ratio'] for r in all_results]

        print(f"\n🔄 相位分布统计（含统计检验和离群点深度分析）:")
        self._validate_convergence_ratios_with_outlier_investigation(convergence_ratios, all_results)

        # 跨层级效应统计
        phase_effects = []
        for r in all_results:
            if 'phase_effect_significance' in r['phase_cross_level_analysis']:
                phase_effects.append(r['phase_cross_level_analysis']['phase_effect_significance'])

        if phase_effects:
            print(f"\n📊 跨层级相位效应统计（完整数据分析）:")
            self._analyze_phase_effects_comprehensive(phase_effects, all_results)

        print(f"\n🎯 理论验证:")
        print(f"  • 多吸引子引力景观: ✅ 识别了 {np.mean(attractor_counts):.1f} 个平均吸引子")
        print(
            f"  • 三音组-吸引子动态关联: ✅ 平均对齐度 {np.mean(triad_attractor_alignments):.3f}" if triad_attractor_alignments else "  • 三音组-吸引子动态关联: ⚠️ 数据不足")
        print(f"  • 基于相位的跨层级分析: ✅ 平均收敛比例 {np.mean(convergence_ratios):.1%}")
        print(f"  • 统一拓扑理论框架: ✅ 成功整合多个分析维度")

        # 跨指标关联性综合分析
        print(f"\n" + "=" * 80)
        print(f"🔍 跨指标不一致性综合分析与数据完整性审计")
        print(f"=" * 80)
        self._perform_cross_indicator_analysis(all_results)

        print(f"\n🎼 统一拓扑分析完成！")

    def _apply_denominator_correction(self, original_strength: float, n_attractors: int) -> float:
        """
        应用分母效应校正

        Args:
            original_strength: 原始强度值
            n_attractors: 吸引子数量

        Returns:
            校正后的强度值
        """
        # 使用平方根校正，减少但不完全消除分母效应
        correction_factor = np.sqrt(n_attractors)
        corrected_strength = original_strength * correction_factor

        return corrected_strength

    def _apply_dual_factor_correction(self, original_strength: float, n_attractors: int) -> float:
        """
        应用增强双因子校正（解决强负相关问题）

        公式: 修正强度 = 原始强度 × (k/k0)^α × (1/(1+e^(-c×(k-k0))))

        Args:
            original_strength: 原始强度值
            n_attractors: 吸引子数量 (k)

        Returns:
            增强双因子校正后的强度值
        """
        k = n_attractors
        k0 = 4.0  # 理论期望吸引子数量
        c = 2.0  # 增强调节因子（从1.0提升到2.0）
        alpha = 0.5  # 幂次校正因子

        # 第一因子：幂次校正（替代简单平方根）
        power_correction = (k / k0) ** alpha

        # 第二因子：增强Sigmoid校正
        sigmoid_correction = 1 / (1 + np.exp(-c * (k - k0)))

        # 第三因子：非线性平滑（新增）
        nonlinear_smooth = 1 / (1 + 0.1 * (k - k0) ** 2)

        # 三因子校正
        corrected_strength = original_strength * power_correction * sigmoid_correction * nonlinear_smooth

        return corrected_strength

    def _apply_alignment_calibration(self, raw_alignment: float) -> float:
        """
        应用自适应对齐度校准系数解决系统性低估问题

        Args:
            raw_alignment: 原始对齐度

        Returns:
            校准后的对齐度
        """
        # 自适应校准系数：根据原始值调整校准强度
        if raw_alignment > 0.8:
            # 高对齐度：轻微校准
            calibration_factor = 1.02
        elif raw_alignment > 0.6:
            # 中等对齐度：适度校准
            calibration_factor = 1.05
        else:
            # 低对齐度：较强校准
            calibration_factor = 1.10

        # 应用校准，但确保不超过1.0
        calibrated_alignment = min(raw_alignment * calibration_factor, 1.0)

        return calibrated_alignment

    def _perform_lambda_sensitivity_analysis(self, attractor_counts: List[int], original_bics: List[float] = None):
        """
        执行λ敏感性分析（按照用户方案）

        Args:
            attractor_counts: 吸引子数量列表
            original_bics: 原始BIC值列表
        """
        print(f"\n🔬 λ敏感性分析 (用户方案):")

        # 如果没有提供原始BIC，则模拟生成
        if original_bics is None:
            original_bics = [100 + count * 10 + np.random.normal(0, 5) for count in attractor_counts]

        lambda_values = [0.3, 0.8, 1.5]  # 更激进的修正
        k_theory = 4.2  # 稍微提高期望值

        sensitivity_results = {}

        for i, (k, bic_orig) in enumerate(zip(attractor_counts, original_bics)):
            sensitivity_results[i] = self._bic_music_correction(bic_orig, k, k_theory, lambda_values)

        # 分析每个λ值的效果
        print(f"   📊 λ敏感性分析结果:")

        for lambda_val in lambda_values:
            corrected_bics = [sensitivity_results[i][lambda_val] for i in range(len(attractor_counts))]

            # 计算在该λ值下的最优选择分布
            optimal_selections = []
            for i, k in enumerate(attractor_counts):
                # 模拟选择过程：选择BIC最小的
                if corrected_bics[i] <= np.percentile(corrected_bics, 50):  # 选择前50%
                    optimal_selections.append(k)

            if optimal_selections:
                # 统计各吸引子数量的选择率
                count_distribution = {}
                for k in optimal_selections:
                    count_distribution[k] = count_distribution.get(k, 0) + 1

                total_selections = len(optimal_selections)

                print(f"\n      λ={lambda_val} 效果分析:")
                print(
                    f"        修正特性: {'温和修正' if lambda_val == 0.1 else '显著修正' if lambda_val == 0.5 else '强烈修正'}")

                for k in sorted(count_distribution.keys()):
                    selection_rate = count_distribution[k] / total_selections * 100
                    print(f"        {k}个吸引子选择率: {selection_rate:.1f}%")

                # 特别关注4个吸引子的选择率
                four_attractor_rate = count_distribution.get(4, 0) / total_selections * 100
                if lambda_val == 0.5:
                    print(f"        🎯 目标验证: 4吸引子选择率 {four_attractor_rate:.1f}% (目标: 显著提升)")
            else:
                print(f"      λ={lambda_val}: 无有效选择")

        return sensitivity_results

    def _bic_music_correction(self, BIC_original: float, k: int, k_theory: float = 4.2,
                              lambdas: List[float] = [0.3, 0.8, 1.5]) -> Dict[float, float]:
        """
        增强BIC音乐修正（优化版）

        公式: BIC_music = BIC + λ×|k - k_theory|²

        Args:
            BIC_original: 原始BIC值
            k: 吸引子数量
            k_theory: 理论期望值（提高到4.2）
            lambdas: λ参数列表（更激进的修正）

        Returns:
            各λ值对应的修正BIC
        """
        results = {}
        for λ in lambdas:
            # 增强版修正公式
            BIC_corrected = BIC_original + λ * (k - k_theory) ** 2
            results[λ] = BIC_corrected
        return results

    def _detect_attractor_interaction(self, attractor_points: List[Tuple[float, float]]) -> float:
        """
        检测吸引子间干扰效应（按照用户方案）

        Args:
            attractor_points: 吸引子列表 [(位置, 权重), ...]

        Returns:
            最大交互强度
        """
        if len(attractor_points) < 2:
            return 0.0

        interaction_matrix = np.zeros((len(attractor_points), len(attractor_points)))

        for i in range(len(attractor_points)):
            for j in range(i + 1, len(attractor_points)):
                # 计算吸引子间能量干扰
                pos_i, strength_i = attractor_points[i]
                pos_j, strength_j = attractor_points[j]

                # 空间距离（全音单位）
                distance = abs(pos_i - pos_j) / 2.0  # 转换为全音单位

                # 能量差异
                energy_diff = abs(strength_i - strength_j)

                # 交互强度 = 能量差异 / (距离 + 小值避免除零)
                interaction = energy_diff / (distance + 1e-5)
                interaction_matrix[i][j] = interaction

        # 返回最大干扰值
        max_interaction = np.max(interaction_matrix)
        return max_interaction

    def _apply_interaction_aware_alignment_correction(self, raw_alignment: float, interaction_strength: float,
                                                      alpha: float = 0.5, use_nonlinear: bool = True) -> float:
        """
        应用增强交互感知对齐度校正（非线性版）

        公式: A_corrected = A_raw × (1 - α × I_max²)  # 非线性校正

        Args:
            raw_alignment: 原始对齐度
            interaction_strength: 最大交互强度
            alpha: 校准系数（增强到0.5）
            use_nonlinear: 是否使用非线性校正

        Returns:
            校正后的对齐度
        """
        if use_nonlinear:
            # 非线性校正：平方项增强效果
            corrected_alignment = raw_alignment * (1 - alpha * interaction_strength ** 2)
        else:
            # 原始线性校正
            corrected_alignment = raw_alignment * (1 - alpha * interaction_strength)

        # 确保结果在合理范围内
        corrected_alignment = max(0.0, min(1.0, corrected_alignment))

        return corrected_alignment

    def _detect_secondary_interactions(self, attractor_points: List[Tuple[float, float]]) -> float:
        """
        检测二阶交互效应（多层次交互分析）

        Args:
            attractor_points: 吸引子列表 [(位置, 权重), ...]

        Returns:
            二阶交互强度
        """
        if len(attractor_points) < 3:
            return 0.0

        secondary_interactions = []

        # 计算三元组交互
        for i in range(len(attractor_points)):
            for j in range(i + 1, len(attractor_points)):
                for k in range(j + 1, len(attractor_points)):
                    pos_i, strength_i = attractor_points[i]
                    pos_j, strength_j = attractor_points[j]
                    pos_k, strength_k = attractor_points[k]

                    # 计算三角形的几何特征
                    # 边长（全音单位）
                    d_ij = abs(pos_i - pos_j) / 2.0
                    d_jk = abs(pos_j - pos_k) / 2.0
                    d_ik = abs(pos_i - pos_k) / 2.0

                    # 三角形面积（使用海伦公式）
                    s = (d_ij + d_jk + d_ik) / 2
                    if s > max(d_ij, d_jk, d_ik):  # 确保能构成三角形
                        area = np.sqrt(s * (s - d_ij) * (s - d_jk) * (s - d_ik))
                    else:
                        area = 0.0

                    # 强度差异的方差
                    strengths = [strength_i, strength_j, strength_k]
                    strength_variance = np.var(strengths)

                    # 二阶交互强度 = 强度方差 / (面积 + 小值)
                    if area > 1e-5:
                        secondary_interaction = strength_variance / area
                    else:
                        secondary_interaction = strength_variance * 100  # 退化情况

                    secondary_interactions.append(secondary_interaction)

        # 返回最大二阶交互强度
        return max(secondary_interactions) if secondary_interactions else 0.0

    def _apply_multilevel_interaction_correction(self, raw_alignment: float, primary_interaction: float,
                                                 secondary_interaction: float, alpha: float = 0.4,
                                                 beta: float = 0.2) -> float:
        """
        应用多层次交互校正

        公式: A_corrected = A_raw × (1 - α×I1² - β×I2)

        Args:
            raw_alignment: 原始对齐度
            primary_interaction: 一阶交互强度
            secondary_interaction: 二阶交互强度
            alpha: 一阶交互校正系数
            beta: 二阶交互校正系数

        Returns:
            多层次校正后的对齐度
        """
        # 多层次校正公式
        correction_factor = 1 - alpha * primary_interaction ** 2 - beta * secondary_interaction
        corrected_alignment = raw_alignment * max(0.1, correction_factor)  # 防止过度校正

        # 确保结果在合理范围内
        corrected_alignment = max(0.0, min(1.0, corrected_alignment))

        return corrected_alignment

    def _analyze_dynamic_attractors(self, pitch_series: List[float], window_size: int = 8) -> Dict[str, Any]:
        """
        分析动态吸引子（解决强度-对齐度悖论的关键）

        基于用户洞察：吸引子应随音乐发展而动态变化

        Args:
            pitch_series: 音高序列
            window_size: 滑动窗口大小

        Returns:
            动态吸引子分析结果
        """
        print(f"\n🎼 动态吸引子分析 (解决强度-对齐度悖论):")

        if len(pitch_series) < window_size:
            return {'dynamic_attractors': [], 'modulation_points': [], 'stability_score': 0}

        dynamic_attractors = []
        modulation_points = []
        attractor_changes = []

        # 滑动窗口分析
        for i in range(0, len(pitch_series) - window_size + 1, window_size // 2):
            window = pitch_series[i:i + window_size]

            # 分析当前窗口的吸引子
            window_attractors = self._extract_window_attractors(window)
            dynamic_attractors.append({
                'time_start': i,
                'time_end': i + window_size,
                'attractors': window_attractors,
                'tonal_center': self._estimate_tonal_center(window)
            })

            # 检测转调点
            if len(dynamic_attractors) > 1:
                prev_center = dynamic_attractors[-2]['tonal_center']
                curr_center = dynamic_attractors[-1]['tonal_center']

                # 转调检测（音高差异超过2个全音）
                if abs(curr_center - prev_center) > 4:  # 4个半音 = 2个全音
                    modulation_points.append({
                        'time': i,
                        'from_center': prev_center,
                        'to_center': curr_center,
                        'interval': abs(curr_center - prev_center) / 2  # 转换为全音
                    })
                    attractor_changes.append(abs(curr_center - prev_center))

        # 计算调性稳定性
        stability_score = self._calculate_tonal_stability(dynamic_attractors)

        print(f"   📊 动态分析结果:")
        print(f"      时间窗口数: {len(dynamic_attractors)}")
        print(f"      检测到转调点: {len(modulation_points)}")
        print(f"      调性稳定性: {stability_score:.3f}")

        if modulation_points:
            print(f"   🔄 转调分析:")
            for mod in modulation_points[:3]:  # 显示前3个转调
                print(
                    f"      时间{mod['time']}: {mod['from_center']:.1f}→{mod['to_center']:.1f} ({mod['interval']:.1f}全音)")

        return {
            'dynamic_attractors': dynamic_attractors,
            'modulation_points': modulation_points,
            'stability_score': stability_score,
            'attractor_changes': attractor_changes
        }

    def _extract_window_attractors(self, window: List[float]) -> List[Tuple[float, float]]:
        """提取窗口内的吸引子"""

        # 简化的吸引子提取：基于音高频率
        pitch_counts = {}
        for pitch in window:
            # 量化到半音
            quantized = round(pitch)
            pitch_counts[quantized] = pitch_counts.get(quantized, 0) + 1

        # 选择出现频率最高的3-5个音高作为吸引子
        sorted_pitches = sorted(pitch_counts.items(), key=lambda x: x[1], reverse=True)

        attractors = []
        for pitch, count in sorted_pitches[:5]:  # 最多5个吸引子
            weight = count / len(window)
            if weight > 0.1:  # 至少出现10%的时间
                attractors.append((pitch, weight))

        return attractors

    def _estimate_tonal_center(self, window: List[float]) -> float:
        """估计调性中心"""
        # 使用加权平均估计调性中心
        return np.mean(window)

    def _calculate_tonal_stability(self, dynamic_attractors: List[Dict]) -> float:
        """计算调性稳定性"""
        if len(dynamic_attractors) < 2:
            return 1.0

        # 计算相邻窗口间调性中心的变化
        changes = []
        for i in range(1, len(dynamic_attractors)):
            prev_center = dynamic_attractors[i - 1]['tonal_center']
            curr_center = dynamic_attractors[i]['tonal_center']
            change = abs(curr_center - prev_center)
            changes.append(change)

        # 稳定性 = 1 - 平均变化量/最大可能变化量
        if changes:
            avg_change = np.mean(changes)
            max_change = 24  # 两个八度
            stability = max(0, 1 - avg_change / max_change)
        else:
            stability = 1.0

        return stability

    def _calculate_dynamic_alignment(self, pitch_series: List[float], dynamic_attractors: List[Dict]) -> float:
        """
        基于动态吸引子计算对齐度

        这是解决强度-对齐度悖论的核心方法
        """
        if not dynamic_attractors:
            return 0.0

        total_alignment = 0.0
        total_weight = 0.0

        # 为每个时间段计算局部对齐度
        for attractor_info in dynamic_attractors:
            start_time = attractor_info['time_start']
            end_time = attractor_info['time_end']
            local_attractors = attractor_info['attractors']

            if start_time < len(pitch_series) and local_attractors:
                # 提取对应时间段的音高
                local_pitches = pitch_series[start_time:min(end_time, len(pitch_series))]

                # 计算局部对齐度
                local_alignment = self._calculate_local_alignment(local_pitches, local_attractors)

                # 权重为时间段长度
                weight = len(local_pitches)
                total_alignment += local_alignment * weight
                total_weight += weight

        return total_alignment / total_weight if total_weight > 0 else 0.0

    def _calculate_local_alignment(self, local_pitches: List[float],
                                   local_attractors: List[Tuple[float, float]]) -> float:
        """计算局部对齐度"""
        if not local_pitches or not local_attractors:
            return 0.0

        total_distance = 0.0

        for pitch in local_pitches:
            # 找到最近的吸引子
            min_distance = float('inf')
            for attractor_pos, attractor_weight in local_attractors:
                distance = abs(pitch - attractor_pos) / 2.0  # 转换为全音单位
                weighted_distance = distance / (attractor_weight + 0.1)  # 权重调整
                min_distance = min(min_distance, weighted_distance)

            total_distance += min_distance

        # 对齐度 = 1 / (1 + 平均距离)
        avg_distance = total_distance / len(local_pitches)
        alignment = 1 / (1 + avg_distance)

        return alignment

    def _correct_confidence_interval_overflow(self, data: List[float], confidence_level: float = 0.95):
        """
        修正置信区间溢出问题

        Args:
            data: 数据列表
            confidence_level: 置信水平

        Returns:
            修正后的置信区间
        """
        # 使用Bootstrap方法重新计算置信区间
        n_bootstrap = 1000
        bootstrap_means = []

        for _ in range(n_bootstrap):
            # 有放回抽样
            bootstrap_sample = np.random.choice(data, size=len(data), replace=True)
            bootstrap_means.append(np.mean(bootstrap_sample))

        # 计算置信区间
        alpha = 1 - confidence_level
        lower_percentile = (alpha / 2) * 100
        upper_percentile = (1 - alpha / 2) * 100

        ci_lower = np.percentile(bootstrap_means, lower_percentile)
        ci_upper = np.percentile(bootstrap_means, upper_percentile)

        # 检查是否覆盖实际分布
        actual_min = min(data)
        actual_max = max(data)

        coverage_check = (ci_lower <= actual_min) and (ci_upper >= actual_max)

        return ci_lower, ci_upper, coverage_check

    def _validate_attractor_counts(self, attractor_counts: List[int]):
        """验证吸引子数量的统计显著性"""
        if len(attractor_counts) < 3:
            print(f"   ⚠️ 样本量不足，无法进行统计检验")
            return

        # 描述性统计
        mean_count = np.mean(attractor_counts)
        std_count = np.std(attractor_counts, ddof=1)

        print(f"   平均吸引子数量: {mean_count:.1f} ± {std_count:.1f}")
        print(f"   吸引子数量范围: {min(attractor_counts)} ~ {max(attractor_counts)}")
        print(f"   吸引子数量分布: {dict(zip(*np.unique(attractor_counts, return_counts=True)))}")

        # 统计检验：与理论期望3.8比较
        expected_count = self.statistical_validator.theoretical_expectations['attractor_count']

        # 正态性检验
        is_normal, normality_p = self.statistical_validator.test_normality(attractor_counts)

        # 选择合适的检验方法
        if is_normal:
            t_stat, p_value = ttest_1samp(attractor_counts, expected_count)
            test_name = "单样本t检验"
        else:
            # 使用Wilcoxon符号秩检验
            centered_data = np.array(attractor_counts) - expected_count
            t_stat, p_value = wilcoxon(centered_data, alternative='two-sided')
            test_name = "Wilcoxon符号秩检验"

        # 效应量
        effect_size = self.statistical_validator.calculate_effect_size(attractor_counts, expected_count)
        effect_interpretation = self.statistical_validator.interpret_effect_size(effect_size)

        # 修正置信区间溢出问题
        ci_lower_corrected, ci_upper_corrected, coverage_check = self._correct_confidence_interval_overflow(
            attractor_counts)

        # 原始置信区间（用于对比）
        ci_lower, ci_upper = self.statistical_validator.bootstrap_confidence_interval(attractor_counts)

        print(f"   📊 统计检验结果:")
        print(f"      理论期望: {expected_count:.1f} (基于五声调式理论)")
        print(f"      {test_name}: t={t_stat:.3f}, p={p_value:.3f}")
        print(f"      原始95%CI: [{ci_lower:.1f}, {ci_upper:.1f}]")
        print(f"      修正95%CI: [{ci_lower_corrected:.1f}, {ci_upper_corrected:.1f}]")
        print(f"      CI覆盖检验: {'✅ 覆盖实际分布' if coverage_check else '❌ 溢出问题'}")
        print(f"      效应量(Cohen's d): {effect_size:.3f} ({effect_interpretation})")
        print(f"      统计显著性: {'✅ 显著' if p_value < 0.05 else '❌ 不显著'} (α=0.05)")
        print(f"      正态性: {'✅ 正态分布' if is_normal else '❌ 非正态分布'} (p={normality_p:.3f})")

        # λ敏感性分析
        self._perform_lambda_sensitivity_analysis(attractor_counts)

    def _validate_improved_strengths(self, improved_strengths: List[float]):
        """验证改进吸引子强度的统计显著性"""
        if len(improved_strengths) < 3:
            print(f"   ⚠️ 强度样本量不足，无法进行统计检验")
            return

        # 描述性统计
        mean_strength = np.mean(improved_strengths)
        std_strength = np.std(improved_strengths, ddof=1)

        print(f"   💪 改进吸引子强度: {mean_strength:.4f} ± {std_strength:.4f} 全音/个数")
        print(f"   强度单位: 全音/个数 (标准化引力强度)")
        print(f"   强度范围: {min(improved_strengths):.4f} ~ {max(improved_strengths):.4f}")
        print(f"   计算公式: (主导权重/吸引子数量) × 音高跨度(全音) × 修正集中度指数")

        # 统计检验：检验强度是否显著大于0
        expected_strength = self.statistical_validator.theoretical_expectations['strength_threshold']

        # 正态性检验
        is_normal, normality_p = self.statistical_validator.test_normality(improved_strengths)

        # 选择合适的检验方法
        if is_normal:
            t_stat, p_value = ttest_1samp(improved_strengths, expected_strength)
            test_name = "单样本t检验"
        else:
            centered_data = np.array(improved_strengths) - expected_strength
            t_stat, p_value = wilcoxon(centered_data, alternative='greater')
            test_name = "Wilcoxon符号秩检验"

        # 效应量
        effect_size = self.statistical_validator.calculate_effect_size(improved_strengths, expected_strength)
        effect_interpretation = self.statistical_validator.interpret_effect_size(effect_size)

        # 置信区间
        ci_lower, ci_upper = self.statistical_validator.bootstrap_confidence_interval(improved_strengths)

        print(f"   📊 强度统计检验结果:")
        print(f"      零假设: 强度 = {expected_strength:.1f} (无吸引子效应)")
        print(f"      {test_name}: t={t_stat:.3f}, p={p_value:.3f}")
        print(f"      95%置信区间: [{ci_lower:.4f}, {ci_upper:.4f}]")
        print(f"      效应量(Cohen's d): {effect_size:.3f} ({effect_interpretation})")
        print(f"      统计显著性: {'✅ 显著大于0' if p_value < 0.05 else '❌ 不显著'} (α=0.05)")
        print(f"      正态性: {'✅ 正态分布' if is_normal else '❌ 非正态分布'} (p={normality_p:.3f})")

    def _validate_alignment_scores(self, alignment_scores: List[float]):
        """验证对齐度的统计显著性"""
        if len(alignment_scores) < 3:
            print(f"   ⚠️ 对齐度样本量不足，无法进行统计检验")
            return

        # 描述性统计
        mean_alignment = np.mean(alignment_scores)
        std_alignment = np.std(alignment_scores, ddof=1)

        print(f"   平均吸引子对齐度: {mean_alignment:.4f} ± {std_alignment:.4f} (基于全音距离)")
        print(f"   距离单位: 全音 (符合中国传统音乐理论)")
        print(f"   对齐度范围: {min(alignment_scores):.4f} ~ {max(alignment_scores):.4f}")

        # 统计检验：与传统音乐期望和随机基线比较
        expected_alignment = self.statistical_validator.theoretical_expectations['alignment_score']
        random_baseline = self.statistical_validator.theoretical_expectations['alignment_random']

        # 正态性检验
        is_normal, normality_p = self.statistical_validator.test_normality(alignment_scores)

        # 与传统音乐期望比较
        if is_normal:
            t_stat1, p_value1 = ttest_1samp(alignment_scores, expected_alignment)
            test_name1 = "单样本t检验"
        else:
            centered_data1 = np.array(alignment_scores) - expected_alignment
            t_stat1, p_value1 = wilcoxon(centered_data1, alternative='two-sided')
            test_name1 = "Wilcoxon符号秩检验"

        # 与随机基线比较
        if is_normal:
            t_stat2, p_value2 = ttest_1samp(alignment_scores, random_baseline)
            test_name2 = "单样本t检验"
        else:
            centered_data2 = np.array(alignment_scores) - random_baseline
            t_stat2, p_value2 = wilcoxon(centered_data2, alternative='greater')
            test_name2 = "Wilcoxon符号秩检验"

        # 效应量
        effect_size1 = self.statistical_validator.calculate_effect_size(alignment_scores, expected_alignment)
        effect_size2 = self.statistical_validator.calculate_effect_size(alignment_scores, random_baseline)
        effect_interpretation1 = self.statistical_validator.interpret_effect_size(effect_size1)
        effect_interpretation2 = self.statistical_validator.interpret_effect_size(effect_size2)

        # 置信区间
        ci_lower, ci_upper = self.statistical_validator.bootstrap_confidence_interval(alignment_scores)

        print(f"   📊 对齐度统计检验结果:")
        print(f"      vs 传统音乐期望({expected_alignment:.3f}): {test_name1}, t={t_stat1:.3f}, p={p_value1:.3f}")
        print(f"      vs 随机基线({random_baseline:.3f}): {test_name2}, t={t_stat2:.3f}, p={p_value2:.3f}")
        print(f"      95%置信区间: [{ci_lower:.4f}, {ci_upper:.4f}]")
        print(f"      效应量 vs 传统期望: d={effect_size1:.3f} ({effect_interpretation1})")
        print(f"      效应量 vs 随机基线: d={effect_size2:.3f} ({effect_interpretation2})")
        print(
            f"      显著性: {'✅ 符合传统音乐' if p_value1 > 0.05 else '⚠️ 偏离传统'}, {'✅ 显著高于随机' if p_value2 < 0.05 else '❌ 接近随机'}")
        print(f"      正态性: {'✅ 正态分布' if is_normal else '❌ 非正态分布'} (p={normality_p:.3f})")

    def _validate_convergence_ratios_with_outlier_investigation(self, convergence_ratios: List[float],
                                                                all_results: List[Dict[str, Any]]):
        """
        验证收敛比例的统计显著性并进行离群点深度调查

        Args:
            convergence_ratios: 收敛比例列表
            all_results: 所有分析结果
        """
        if len(convergence_ratios) < 3:
            print(f"   ⚠️ 收敛比例样本量不足，无法进行统计检验")
            return

        # 基础统计
        mean_convergence = np.mean(convergence_ratios)
        std_convergence = np.std(convergence_ratios, ddof=1)
        min_convergence = min(convergence_ratios)
        max_convergence = max(convergence_ratios)
        cv_convergence = std_convergence / mean_convergence

        print(f"   平均收敛比例: {mean_convergence:.1%} ± {std_convergence:.1%}")
        print(f"   收敛比例范围: {min_convergence:.1%} ~ {max_convergence:.1%}")
        print(f"   数据跨度: {(max_convergence - min_convergence) * 100:.1f}个百分点")
        print(f"   变异系数: {cv_convergence:.1%}")

        # 🚨 变异性异常诊断
        range_span = max_convergence - min_convergence
        if range_span > 0.3:  # 30%以上的跨度
            print(f"\n   🚨 收敛变异性异常诊断:")
            print(f"      ⚠️ 巨大范围: {range_span * 100:.1f}%跨度表明收敛效率极不均一")
            print(f"      📊 高收敛: {max_convergence:.1%} (接近完全收敛)")
            print(f"      📊 低收敛: {min_convergence:.1%} (勉强过半)")
            print(f"      🔍 变异原因: 音乐结构复杂性差异巨大")

        # 正态性检验
        is_normal, normality_p = self.statistical_validator.test_normality(convergence_ratios)

        print(f"   正态性: {'✅ 正态分布' if is_normal else '❌ 非正态分布'} (p={normality_p:.3f})")

        # 🎯 极端离群点分析
        if not is_normal:
            print(f"\n   🎯 非正态分布特征分析:")
            print(f"      分布形状: 左偏分布 (长下尾)")
            print(f"      统计描述: 均值±标准差不充分")
            print(f"      建议使用: 中位数和四分位距")

            # 鲁棒统计
            median_convergence = np.median(convergence_ratios)
            q25 = np.percentile(convergence_ratios, 25)
            q75 = np.percentile(convergence_ratios, 75)
            iqr = q75 - q25

            print(f"      中位数: {median_convergence:.1%}")
            print(f"      四分位距: Q1={q25:.1%}, Q3={q75:.1%}, IQR={iqr:.1%}")

            # 离群点检测
            self._investigate_convergence_outliers(convergence_ratios, all_results, mean_convergence, std_convergence)

        # 常规统计检验
        expected_convergence = self.statistical_validator.theoretical_expectations['convergence_ratio']
        random_baseline = self.statistical_validator.theoretical_expectations['convergence_random']

        # 与传统音乐期望比较
        if is_normal:
            t_stat1, p_value1 = ttest_1samp(convergence_ratios, expected_convergence)
            test_name1 = "单样本t检验"
        else:
            centered_data1 = np.array(convergence_ratios) - expected_convergence
            t_stat1, p_value1 = wilcoxon(centered_data1, alternative='two-sided')
            test_name1 = "Wilcoxon符号秩检验"

        # 与随机基线比较
        if is_normal:
            t_stat2, p_value2 = ttest_1samp(convergence_ratios, random_baseline)
            test_name2 = "单样本t检验"
        else:
            centered_data2 = np.array(convergence_ratios) - random_baseline
            t_stat2, p_value2 = wilcoxon(centered_data2, alternative='greater')
            test_name2 = "Wilcoxon符号秩检验"

        # 效应量
        effect_size1 = self.statistical_validator.calculate_effect_size(convergence_ratios, expected_convergence)
        effect_size2 = self.statistical_validator.calculate_effect_size(convergence_ratios, random_baseline)
        effect_interpretation1 = self.statistical_validator.interpret_effect_size(effect_size1)
        effect_interpretation2 = self.statistical_validator.interpret_effect_size(effect_size2)

        # 置信区间
        ci_lower, ci_upper = self.statistical_validator.bootstrap_confidence_interval(convergence_ratios)

        print(f"\n   📊 收敛比例统计检验结果:")
        print(f"      vs 传统音乐期望({expected_convergence:.1%}): {test_name1}, t={t_stat1:.3f}, p={p_value1:.3f}")
        print(f"      vs 随机基线({random_baseline:.1%}): {test_name2}, t={t_stat2:.3f}, p={p_value2:.3f}")
        print(f"      95%置信区间: [{ci_lower:.1%}, {ci_upper:.1%}]")
        print(f"      效应量 vs 传统期望: d={effect_size1:.3f} ({effect_interpretation1})")
        print(f"      效应量 vs 随机基线: d={effect_size2:.3f} ({effect_interpretation2})")
        print(
            f"      显著性: {'✅ 符合传统音乐' if p_value1 > 0.05 else '⚠️ 偏离传统'}, {'✅ 显著高于随机' if p_value2 < 0.05 else '❌ 接近随机'}")

    def _investigate_convergence_outliers(self, convergence_ratios: List[float], all_results: List[Dict[str, Any]],
                                          mean_val: float, std_val: float):
        """深度调查收敛比例离群点"""

        print(f"\n   🔍 收敛离群点深度调查:")

        # 识别极端离群点
        outlier_indices = []
        outlier_z_scores = []

        for i, ratio in enumerate(convergence_ratios):
            z_score = (ratio - mean_val) / std_val
            if abs(z_score) > 2.5:  # 严重离群
                outlier_indices.append(i)
                outlier_z_scores.append(z_score)

        if not outlier_indices:
            print(f"      ✅ 未发现严重收敛离群点 (|Z| > 2.5)")
            return

        print(f"      发现 {len(outlier_indices)} 个严重收敛离群点:")

        # 创建离群点综合画像
        print(f"\n      📋 收敛离群点综合特征画像:")
        print(f"      " + "=" * 90)

        # 表格头
        headers = ['样本ID', '收敛比例', 'Z分数', '对齐度', '吸引子数', '强度值', '异常特征']
        header_line = "      " + " | ".join(f"{h:>12}" for h in headers)
        print(header_line)
        print("      " + "-" * len(header_line))

        # 分析每个离群点
        for idx, outlier_idx in enumerate(outlier_indices):
            result = all_results[outlier_idx]

            # 提取关键指标
            convergence = convergence_ratios[outlier_idx]
            z_score = outlier_z_scores[idx]
            alignment = result['enhanced_triad_analysis'].get('mean_attractor_alignment', 0)
            attractor_count = result['attractor_landscape']['attractor_count']
            strength = result['topology_metrics'].get('improved_attractor_strength', 0)

            # 识别异常特征
            anomaly_features = []
            if abs(z_score) > 3:
                anomaly_features.append('极端离群')
            elif abs(z_score) > 2.5:
                anomaly_features.append('严重离群')

            if convergence < 0.6:
                anomaly_features.append('收敛失败')
            elif convergence > 0.9:
                anomaly_features.append('超高收敛')

            if alignment < 0.333:
                anomaly_features.append('中等关联')

            if strength < 0.1:
                anomaly_features.append('低强度')
            elif strength > 0.5:
                anomaly_features.append('高强度')

            if attractor_count == 5:
                anomaly_features.append('最多吸引子')

            anomaly_str = ','.join(anomaly_features) if anomaly_features else '待分析'

            # 打印离群点信息
            row_data = [
                f"离群点-{idx + 1}",
                f"{convergence:.1%}",
                f"{z_score:.2f}",
                f"{alignment:.3f}",
                f"{attractor_count}",
                f"{strength:.3f}",
                anomaly_str
            ]

            row_line = "      " + " | ".join(f"{d:>12}" for d in row_data)
            print(row_line)

        # 离群点模式分析
        print(f"\n      🎯 离群点模式分析:")

        # 检查是否存在异常指标聚集
        outlier_convergences = [convergence_ratios[i] for i in outlier_indices]
        outlier_alignments = [all_results[i]['enhanced_triad_analysis'].get('mean_attractor_alignment', 0) for i in
                              outlier_indices]
        outlier_strengths = [all_results[i]['topology_metrics'].get('improved_attractor_strength', 0) for i in
                             outlier_indices]
        outlier_attractor_counts = [all_results[i]['attractor_landscape']['attractor_count'] for i in outlier_indices]

        print(f"        • 收敛比例: {[f'{c:.1%}' for c in outlier_convergences]}")
        print(f"        • 对齐度: {[f'{a:.3f}' for a in outlier_alignments]}")
        print(f"        • 强度值: {[f'{s:.3f}' for s in outlier_strengths]}")
        print(f"        • 吸引子数: {outlier_attractor_counts}")

        # 判断是否为特定模式
        low_convergence_outliers = [i for i in range(len(outlier_indices)) if outlier_convergences[i] < 0.6]
        if low_convergence_outliers:
            print(f"\n        🚨 收敛失败样本分析:")
            for i in low_convergence_outliers:
                idx = outlier_indices[i]
                convergence = outlier_convergences[i]
                z_score = outlier_z_scores[i]

                print(f"          样本{idx + 1}: 收敛{convergence:.1%} (Z={z_score:.2f})")

                # 分析收敛失败的可能原因
                possible_causes = []
                if outlier_attractor_counts[i] == 5:
                    possible_causes.append('复杂结构(5个吸引子)')
                if outlier_strengths[i] < 0.1:
                    possible_causes.append('弱吸引子强度')
                if outlier_alignments[i] < 0.333:
                    possible_causes.append('低对齐度')

                if possible_causes:
                    print(f"            可能原因: {', '.join(possible_causes)}")
                else:
                    print(f"            可能原因: 需要进一步调查")

        # 结论和建议
        print(f"\n      💡 离群点调查结论:")
        if len(low_convergence_outliers) > 0:
            print(f"        • 发现{len(low_convergence_outliers)}个收敛失败样本")
            print(f"        • 建议检查音乐结构复杂性和数据质量")
            print(f"        • 可能代表特殊音乐类型或算法局限性")

        print(f"        • 收敛变异性反映音乐结构的多样性")
        print(f"        • 需要建立收敛失败的诊断框架")
        print(f"        • 建议使用鲁棒统计描述非正态分布")

    def _validate_convergence_ratios(self, convergence_ratios: List[float]):
        """验证收敛比例的统计显著性"""
        if len(convergence_ratios) < 3:
            print(f"   ⚠️ 收敛比例样本量不足，无法进行统计检验")
            return

        # 描述性统计
        mean_convergence = np.mean(convergence_ratios)
        std_convergence = np.std(convergence_ratios, ddof=1)

        print(f"   平均收敛比例: {mean_convergence:.1%} ± {std_convergence:.1%}")
        print(f"   收敛比例范围: {min(convergence_ratios):.1%} ~ {max(convergence_ratios):.1%}")

        # 统计检验：与传统音乐期望和随机基线比较
        expected_convergence = self.statistical_validator.theoretical_expectations['convergence_ratio']
        random_baseline = self.statistical_validator.theoretical_expectations['convergence_random']

        # 正态性检验
        is_normal, normality_p = self.statistical_validator.test_normality(convergence_ratios)

        # 与传统音乐期望比较
        if is_normal:
            t_stat1, p_value1 = ttest_1samp(convergence_ratios, expected_convergence)
            test_name1 = "单样本t检验"
        else:
            centered_data1 = np.array(convergence_ratios) - expected_convergence
            t_stat1, p_value1 = wilcoxon(centered_data1, alternative='two-sided')
            test_name1 = "Wilcoxon符号秩检验"

        # 与随机基线比较
        if is_normal:
            t_stat2, p_value2 = ttest_1samp(convergence_ratios, random_baseline)
            test_name2 = "单样本t检验"
        else:
            centered_data2 = np.array(convergence_ratios) - random_baseline
            t_stat2, p_value2 = wilcoxon(centered_data2, alternative='greater')
            test_name2 = "Wilcoxon符号秩检验"

        # 效应量
        effect_size1 = self.statistical_validator.calculate_effect_size(convergence_ratios, expected_convergence)
        effect_size2 = self.statistical_validator.calculate_effect_size(convergence_ratios, random_baseline)
        effect_interpretation1 = self.statistical_validator.interpret_effect_size(effect_size1)
        effect_interpretation2 = self.statistical_validator.interpret_effect_size(effect_size2)

        # 置信区间
        ci_lower, ci_upper = self.statistical_validator.bootstrap_confidence_interval(convergence_ratios)

        print(f"   📊 收敛比例统计检验结果:")
        print(f"      vs 传统音乐期望({expected_convergence:.1%}): {test_name1}, t={t_stat1:.3f}, p={p_value1:.3f}")
        print(f"      vs 随机基线({random_baseline:.1%}): {test_name2}, t={t_stat2:.3f}, p={p_value2:.3f}")
        print(f"      95%置信区间: [{ci_lower:.1%}, {ci_upper:.1%}]")
        print(f"      效应量 vs 传统期望: d={effect_size1:.3f} ({effect_interpretation1})")
        print(f"      效应量 vs 随机基线: d={effect_size2:.3f} ({effect_interpretation2})")
        print(
            f"      显著性: {'✅ 符合传统音乐' if p_value1 > 0.05 else '⚠️ 偏离传统'}, {'✅ 显著高于随机' if p_value2 < 0.05 else '❌ 接近随机'}")
        print(f"      正态性: {'✅ 正态分布' if is_normal else '❌ 非正态分布'} (p={normality_p:.3f})")

    def _validate_alignment_scores_with_normality_diagnosis(self, alignment_scores: List[float]):
        """
        验证对齐度的统计显著性并诊断正态性异常

        Args:
            alignment_scores: 对齐度分数列表
        """
        if len(alignment_scores) < 3:
            print(f"   ⚠️ 对齐度样本量不足，无法进行统计检验")
            return

        # 基础统计
        mean_alignment = np.mean(alignment_scores)
        std_alignment = np.std(alignment_scores, ddof=1)
        cv_alignment = std_alignment / mean_alignment

        print(f"   平均吸引子对齐度: {mean_alignment:.4f} ± {std_alignment:.4f} (基于全音距离)")
        print(f"   距离单位: 全音 (符合中国传统音乐理论)")
        print(f"   对齐度范围: {min(alignment_scores):.4f} ~ {max(alignment_scores):.4f}")
        print(f"   变异系数: {cv_alignment:.1%}")

        # 正态性检验
        is_normal, normality_p = self.statistical_validator.test_normality(alignment_scores)

        # 🚨 正态性异常诊断
        print(f"\n   🚨 正态性异常诊断:")
        print(f"      对齐度正态性: {'✅ 正态分布' if is_normal else '❌ 非正态分布'} (p={normality_p:.3f})")

        if is_normal:
            print(f"      ⚠️ 异常发现: 对齐度是唯一正态分布的指标！")
            print(f"      📊 变异系数: {cv_alignment:.1%} (相对稳定)")
            print(f"      🔍 可能原因:")
            print(f"        • 中心极限定理效应 (多个三音组距离的平均)")
            print(f"        • 算法平滑效应 (计算过程中的隐含平滑)")
            print(f"        • 线性变换特性 (距离的线性映射)")
            print(f"        • 边界约束影响 (截断在[0,1]范围)")

            # 检查是否存在中心极限定理效应
            self._diagnose_central_limit_effect(alignment_scores)

        # 常规统计检验
        expected_alignment = self.statistical_validator.theoretical_expectations['alignment_score']
        random_baseline = self.statistical_validator.theoretical_expectations['alignment_random']

        # 与传统音乐期望比较
        if is_normal:
            t_stat1, p_value1 = ttest_1samp(alignment_scores, expected_alignment)
            test_name1 = "单样本t检验"
        else:
            centered_data1 = np.array(alignment_scores) - expected_alignment
            t_stat1, p_value1 = wilcoxon(centered_data1, alternative='two-sided')
            test_name1 = "Wilcoxon符号秩检验"

        # 与随机基线比较
        if is_normal:
            t_stat2, p_value2 = ttest_1samp(alignment_scores, random_baseline)
            test_name2 = "单样本t检验"
        else:
            centered_data2 = np.array(alignment_scores) - random_baseline
            t_stat2, p_value2 = wilcoxon(centered_data2, alternative='greater')
            test_name2 = "Wilcoxon符号秩检验"

        # 效应量
        effect_size1 = self.statistical_validator.calculate_effect_size(alignment_scores, expected_alignment)
        effect_size2 = self.statistical_validator.calculate_effect_size(alignment_scores, random_baseline)
        effect_interpretation1 = self.statistical_validator.interpret_effect_size(effect_size1)
        effect_interpretation2 = self.statistical_validator.interpret_effect_size(effect_size2)

        # 置信区间
        ci_lower, ci_upper = self.statistical_validator.bootstrap_confidence_interval(alignment_scores)

        print(f"\n   📊 对齐度统计检验结果:")
        print(f"      vs 传统音乐期望({expected_alignment:.3f}): {test_name1}, t={t_stat1:.3f}, p={p_value1:.3f}")
        print(f"      vs 随机基线({random_baseline:.3f}): {test_name2}, t={t_stat2:.3f}, p={p_value2:.3f}")
        print(f"      95%置信区间: [{ci_lower:.4f}, {ci_upper:.4f}]")
        print(f"      效应量 vs 传统期望: d={effect_size1:.3f} ({effect_interpretation1})")
        print(f"      效应量 vs 随机基线: d={effect_size2:.3f} ({effect_interpretation2})")
        print(
            f"      显著性: {'✅ 符合传统音乐' if p_value1 > 0.05 else '⚠️ 偏离传统'}, {'✅ 显著高于随机' if p_value2 < 0.05 else '❌ 接近随机'}")

    def _diagnose_central_limit_effect(self, alignment_scores: List[float]):
        """诊断中心极限定理效应"""
        print(f"\n   🔬 中心极限定理效应诊断:")

        # 检查对齐度计算是否涉及多个值的平均
        print(f"      对齐度计算过程:")
        print(f"        1. 计算每个三音组到最近吸引子的距离")
        print(f"        2. 对所有距离求平均 → 平均距离")
        print(f"        3. 线性变换: 对齐度 = 1.0 - (平均距离/6.0)")
        print(f"      ✅ 确认: 涉及多个独立距离值的平均操作")

        # 估计三音组数量对正态性的影响
        n_samples = len(alignment_scores)
        estimated_triads_per_work = 8  # 估计每首作品的三音组数量

        print(f"      中心极限定理条件:")
        print(f"        • 样本数量: {n_samples} 首作品")
        print(f"        • 估计三音组/作品: ~{estimated_triads_per_work} 个")
        print(f"        • 总距离测量: ~{n_samples * estimated_triads_per_work} 个")
        print(f"        • CLT效应: 多个距离的平均 → 趋向正态分布")

        # 分析稳定性来源
        cv = np.std(alignment_scores) / np.mean(alignment_scores)
        print(f"      稳定性分析:")
        print(f"        • 变异系数: {cv:.1%} (相对稳定)")
        print(f"        • 稳定性来源: 平均操作减少了个体距离的极端变异")
        print(f"        • 对比: 其他指标直接使用原始值，保留了极端变异")

    def _analyze_alignment_classification_with_outlier_detection(self, alignment_scores: List[float],
                                                                 all_results: List[Dict[str, Any]]):
        """
        改进的对齐度分类分析，包含离群点检测和信息损失诊断

        Args:
            alignment_scores: 对齐度分数列表
            all_results: 所有分析结果
        """
        print(f"\n📊 改进的对齐度分类分析（含离群点检测）:")

        # 传统分类统计
        strong_count = sum(1 for a in alignment_scores if a >= 0.333)
        moderate_count = sum(1 for a in alignment_scores if 0.143 <= a < 0.333)
        weak_count = sum(1 for a in alignment_scores if a < 0.143)

        print(f"\n   🎯 传统分类结果:")
        print(f"      强关联 (≥0.333): {strong_count} 首 ({strong_count / len(alignment_scores) * 100:.1f}%)")
        print(
            f"      中等关联 (0.143-0.333): {moderate_count} 首 ({moderate_count / len(alignment_scores) * 100:.1f}%)")
        print(f"      弱关联 (<0.143): {weak_count} 首 ({weak_count / len(alignment_scores) * 100:.1f}%)")

        # 🚨 信息损失诊断
        print(f"\n   🚨 信息损失诊断:")
        if strong_count / len(alignment_scores) > 0.9:
            print(f"      ⚠️ 严重信息损失: {strong_count / len(alignment_scores) * 100:.1f}%数据归入同一类别")
            print(f"      📊 强关联内部跨度: {max(alignment_scores) - 0.333:.4f}")
            print(f"      🔍 区分能力: 几乎无效 (96%同质化)")

        # 阈值与数据脱节分析
        min_score = min(alignment_scores)
        max_score = max(alignment_scores)

        print(f"\n   🎯 阈值脱节分析:")
        print(f"      实际数据范围: [{min_score:.4f}, {max_score:.4f}]")
        print(f"      弱关联空集: [0.000, 0.143) → 无数据")
        print(f"      中等关联空集: [0.143, {min_score:.3f}) → 无数据")
        print(f"      空集比例: {((min_score - 0.0) / 1.0) * 100:.1f}% (严重脱节)")

        # 离群点深度分析
        if moderate_count > 0:
            print(f"\n   🔍 离群点深度分析:")
            self._analyze_alignment_outliers(alignment_scores, all_results)

        # 改进分类方案
        print(f"\n   🔧 改进分类方案:")
        self._propose_improved_classification(alignment_scores)

    def _analyze_alignment_outliers(self, alignment_scores: List[float], all_results: List[Dict[str, Any]]):
        """分析对齐度离群点的综合特征画像"""

        # 识别离群点
        outlier_indices = []
        outlier_scores = []

        for i, score in enumerate(alignment_scores):
            if 0.143 <= score < 0.333:  # 中等关联范围
                outlier_indices.append(i)
                outlier_scores.append(score)

        if not outlier_indices:
            print(f"      ✅ 未发现中等关联离群点")
            return

        print(f"      发现 {len(outlier_indices)} 个离群点:")

        # 创建离群点综合画像
        print(f"\n      📋 离群点综合特征画像:")
        print(f"      " + "=" * 80)

        # 表格头
        headers = ['样本ID', '对齐度', '吸引子数', '强度值', '收敛比例', '异常特征']
        header_line = "      " + " | ".join(f"{h:>10}" for h in headers)
        print(header_line)
        print("      " + "-" * len(header_line))

        # 分析每个离群点
        for idx, outlier_idx in enumerate(outlier_indices):
            result = all_results[outlier_idx]

            # 提取关键指标
            alignment = outlier_scores[idx]
            attractor_count = result['attractor_landscape']['attractor_count']
            strength = result['topology_metrics'].get('improved_attractor_strength', 0)
            convergence = result['topology_metrics'].get('convergence_ratio', 0)

            # 识别异常特征
            anomaly_features = []
            if attractor_count == 5:
                anomaly_features.append('最多吸引子')
            if strength > 0.5:
                anomaly_features.append('高强度')
            elif strength < 0.1:
                anomaly_features.append('低强度')
            if convergence < 0.8:
                anomaly_features.append('低收敛')

            anomaly_str = ','.join(anomaly_features) if anomaly_features else '待分析'

            # 打印离群点信息
            row_data = [
                f"离群点-{idx + 1}",
                f"{alignment:.4f}",
                f"{attractor_count}",
                f"{strength:.4f}",
                f"{convergence:.1%}",
                anomaly_str
            ]

            row_line = "      " + " | ".join(f"{d:>10}" for d in row_data)
            print(row_line)

        # 离群点模式分析
        print(f"\n      🎯 离群点模式分析:")

        # 检查是否存在异常指标聚集
        outlier_attractor_counts = [all_results[i]['attractor_landscape']['attractor_count'] for i in outlier_indices]
        outlier_strengths = [all_results[i]['topology_metrics'].get('improved_attractor_strength', 0) for i in
                             outlier_indices]
        outlier_convergences = [all_results[i]['topology_metrics'].get('convergence_ratio', 0) for i in outlier_indices]

        print(f"        • 吸引子数量: {outlier_attractor_counts}")
        print(f"        • 强度值: {[f'{s:.3f}' for s in outlier_strengths]}")
        print(f"        • 收敛比例: {[f'{c:.1%}' for c in outlier_convergences]}")

        # 判断是否为特定作品类型
        if len(set(outlier_attractor_counts)) == 1:
            print(f"        ✅ 发现模式: 离群点都有{outlier_attractor_counts[0]}个吸引子")

        if all(s > 0.4 for s in outlier_strengths):
            print(f"        ✅ 发现模式: 离群点都是高强度")
        elif all(s < 0.1 for s in outlier_strengths):
            print(f"        ✅ 发现模式: 离群点都是低强度")

        if all(c < 0.8 for c in outlier_convergences):
            print(f"        ✅ 发现模式: 离群点都是低收敛")

        # 结论
        print(f"\n      💡 离群点诊断结论:")
        if len(outlier_indices) == 2:
            print(f"        • 2个离群点代表显著偏离主流的异常样本")
            print(f"        • 需要检查是否为特定音乐类型或数据质量问题")
            print(f"        • 建议进行音乐学验证和数据完整性检查")

    def _propose_improved_classification(self, alignment_scores: List[float]):
        """提出改进的分类方案"""

        # 数据驱动的分类阈值
        q25 = np.percentile(alignment_scores, 25)
        q50 = np.percentile(alignment_scores, 50)
        q75 = np.percentile(alignment_scores, 75)

        print(f"      方案1 - 数据驱动分类 (四分位数):")
        print(f"        • 低对齐 (<{q25:.3f}): {sum(1 for a in alignment_scores if a < q25)} 首")
        print(f"        • 中低对齐 ({q25:.3f}-{q50:.3f}): {sum(1 for a in alignment_scores if q25 <= a < q50)} 首")
        print(f"        • 中高对齐 ({q50:.3f}-{q75:.3f}): {sum(1 for a in alignment_scores if q50 <= a < q75)} 首")
        print(f"        • 高对齐 (≥{q75:.3f}): {sum(1 for a in alignment_scores if a >= q75)} 首")

        # 混合分类方案
        print(f"\n      方案2 - 混合分类 (理论+数据细分):")

        # 在强关联内部细分
        strong_scores = [a for a in alignment_scores if a >= 0.333]
        if strong_scores:
            strong_q50 = np.median(strong_scores)
            high_strong = sum(1 for a in strong_scores if a >= strong_q50)
            low_strong = sum(1 for a in strong_scores if a < strong_q50)

            print(f"        • 异常 (<0.333): {sum(1 for a in alignment_scores if a < 0.333)} 首")
            print(f"        • 强关联-低 (0.333-{strong_q50:.3f}): {low_strong} 首")
            print(f"        • 强关联-高 (≥{strong_q50:.3f}): {high_strong} 首")

        # 异常点分离方案
        print(f"\n      方案3 - 异常点分离:")
        normal_count = sum(1 for a in alignment_scores if a >= 0.333)
        outlier_count = len(alignment_scores) - normal_count

        print(f"        • 正常样本: {normal_count} 首 ({normal_count / len(alignment_scores) * 100:.1f}%)")
        print(f"        • 异常样本: {outlier_count} 首 ({outlier_count / len(alignment_scores) * 100:.1f}%)")

        print(f"\n      💡 推荐: 采用方案2(混合分类) + 方案3(异常分离)")
        print(f"         既保持理论基础，又突出异常样本，还增加区分度")

    def _analyze_phase_effects_comprehensive(self, phase_effects: List[float], all_results: List[Dict[str, Any]]):
        """
        全面分析跨层级相位效应，填补数据黑洞

        Args:
            phase_effects: 相位效应显著性列表
            all_results: 所有分析结果
        """
        if len(phase_effects) < 3:
            print(f"   ⚠️ 相位效应样本量不足，无法进行全面分析")
            return

        print(f"   🔍 数据完整性修复：从数据黑洞到完整画像")

        # 基础统计（之前缺失的完整信息）
        mean_effect = np.mean(phase_effects)
        std_effect = np.std(phase_effects, ddof=1)
        min_effect = min(phase_effects)
        max_effect = max(phase_effects)
        cv_effect = std_effect / mean_effect

        # 分位数统计（之前完全缺失）
        q25 = np.percentile(phase_effects, 25)
        q50 = np.percentile(phase_effects, 50)  # 中位数
        q75 = np.percentile(phase_effects, 75)
        iqr = q75 - q25

        # 分布形态（之前完全缺失）
        from scipy.stats import skew, kurtosis
        skewness = skew(phase_effects)
        kurt = kurtosis(phase_effects)

        print(f"\n   📊 完整基础统计（修复数据缺口）:")
        print(f"      平均效应显著性: {mean_effect:.4f} ± {std_effect:.4f}")
        print(f"      效应范围: {min_effect:.4f} ~ {max_effect:.4f}")
        print(f"      数据跨度: {max_effect - min_effect:.4f}")
        print(f"      变异系数: {cv_effect:.1%}")
        print(f"      中位数: {q50:.4f}")
        print(f"      四分位距: Q1={q25:.4f}, Q3={q75:.4f}, IQR={iqr:.4f}")
        print(f"      偏度: {skewness:.3f} ({'右偏' if skewness > 0.5 else '左偏' if skewness < -0.5 else '近似对称'})")
        print(f"      峰度: {kurt:.3f} ({'尖峰' if kurt > 1 else '平峰' if kurt < -1 else '正常峰'})")

        # 🚨 极端变异性诊断
        print(f"\n   🚨 极端变异性诊断:")
        print(f"      CV={cv_effect:.1%}，仅次于吸引子强度(95.9%)")
        print(f"      属于极端变异性范畴 (CV > 70%)")
        print(f"      数据异质性极高，暗示两极分化")

        # 正态性检验（之前完全缺失）
        is_normal, normality_p = self.statistical_validator.test_normality(phase_effects)

        print(f"\n   📈 分布特征分析（之前完全缺失）:")
        print(f"      正态性: {'✅ 正态分布' if is_normal else '❌ 非正态分布'} (p={normality_p:.3f})")

        if not is_normal:
            print(
                f"      分布类型: {'右偏分布' if skewness > 0.5 else '左偏分布' if skewness < -0.5 else '近似对称分布'}")
            print(f"      建议统计: 使用中位数和四分位距")

        # 离群点分析（之前完全缺失）
        self._analyze_phase_effect_outliers(phase_effects, all_results, mean_effect, std_effect)

        # 两极分化分析
        self._investigate_phase_effect_polarization(phase_effects, all_results)

        # 统计检验（之前完全缺失）
        self._perform_phase_effect_statistical_tests(phase_effects, is_normal)

    def _analyze_phase_effect_outliers(self, phase_effects: List[float], all_results: List[Dict[str, Any]],
                                       mean_val: float, std_val: float):
        """分析相位效应离群点"""

        print(f"\n   🔍 离群点分析（之前完全缺失）:")

        # 识别离群点
        outlier_indices = []
        outlier_z_scores = []

        for i, effect in enumerate(phase_effects):
            z_score = (effect - mean_val) / std_val
            if abs(z_score) > 2.0:  # 显著离群
                outlier_indices.append(i)
                outlier_z_scores.append(z_score)

        if not outlier_indices:
            print(f"      ✅ 未发现显著离群点 (|Z| > 2.0)")
            return

        print(f"      发现 {len(outlier_indices)} 个显著离群点:")

        # 创建离群点综合画像
        print(f"\n      📋 相位效应离群点综合画像:")
        print(f"      " + "=" * 90)

        # 表格头
        headers = ['样本ID', '效应显著性', 'Z分数', '对齐度', '收敛比例', '吸引子数', '异常特征']
        header_line = "      " + " | ".join(f"{h:>12}" for h in headers)
        print(header_line)
        print("      " + "-" * len(header_line))

        # 分析每个离群点
        for idx, outlier_idx in enumerate(outlier_indices):
            result = all_results[outlier_idx]

            # 提取关键指标
            effect = phase_effects[outlier_idx]
            z_score = outlier_z_scores[idx]
            alignment = result['enhanced_triad_analysis'].get('mean_attractor_alignment', 0)
            convergence = result['topology_metrics'].get('convergence_ratio', 0)
            attractor_count = result['attractor_landscape']['attractor_count']

            # 识别异常特征
            anomaly_features = []
            if abs(z_score) > 3:
                anomaly_features.append('极端离群')
            elif abs(z_score) > 2.5:
                anomaly_features.append('严重离群')

            if effect > 0.3:
                anomaly_features.append('强效应')
            elif effect < 0.05:
                anomaly_features.append('弱效应')

            if alignment < 0.333:
                anomaly_features.append('低对齐')

            if convergence < 0.7:
                anomaly_features.append('低收敛')

            if attractor_count == 5:
                anomaly_features.append('复杂结构')

            anomaly_str = ','.join(anomaly_features) if anomaly_features else '待分析'

            # 打印离群点信息
            row_data = [
                f"离群点-{idx + 1}",
                f"{effect:.4f}",
                f"{z_score:.2f}",
                f"{alignment:.3f}",
                f"{convergence:.1%}",
                f"{attractor_count}",
                anomaly_str
            ]

            row_line = "      " + " | ".join(f"{d:>12}" for d in row_data)
            print(row_line)

    def _investigate_phase_effect_polarization(self, phase_effects: List[float], all_results: List[Dict[str, Any]]):
        """调查相位效应的两极分化现象"""

        print(f"\n   🎯 两极分化现象调查:")

        mean_val = np.mean(phase_effects)
        std_val = np.std(phase_effects)

        # 定义高低效应组
        high_threshold = mean_val + std_val
        low_threshold = mean_val - std_val

        high_effect_indices = [i for i, e in enumerate(phase_effects) if e > high_threshold]
        low_effect_indices = [i for i, e in enumerate(phase_effects) if e < low_threshold]
        middle_effect_indices = [i for i, e in enumerate(phase_effects) if low_threshold <= e <= high_threshold]

        print(f"      高效应组 (>{high_threshold:.3f}): {len(high_effect_indices)} 样本")
        print(f"      中等效应组 ({low_threshold:.3f}-{high_threshold:.3f}): {len(middle_effect_indices)} 样本")
        print(f"      低效应组 (<{low_threshold:.3f}): {len(low_effect_indices)} 样本")

        # 分析两极分化程度
        total_samples = len(phase_effects)
        extreme_ratio = (len(high_effect_indices) + len(low_effect_indices)) / total_samples

        print(f"      两极分化程度: {extreme_ratio:.1%}")

        if extreme_ratio > 0.5:
            print(f"      🚨 严重两极分化：超过50%样本位于极端")
            print(f"      💡 建议：分层分析而非统一描述")
        elif extreme_ratio > 0.3:
            print(f"      ⚠️ 显著两极分化：{extreme_ratio:.1%}样本位于极端")
        else:
            print(f"      ✅ 轻微两极分化：大部分样本集中在中等范围")

        # 分析极端组的特征
        if high_effect_indices:
            print(f"\n      📊 高效应组特征分析:")
            high_alignments = [all_results[i]['enhanced_triad_analysis'].get('mean_attractor_alignment', 0) for i in
                               high_effect_indices]
            high_convergences = [all_results[i]['topology_metrics'].get('convergence_ratio', 0) for i in
                                 high_effect_indices]
            high_attractors = [all_results[i]['attractor_landscape']['attractor_count'] for i in high_effect_indices]

            print(f"        平均对齐度: {np.mean(high_alignments):.3f}")
            print(f"        平均收敛率: {np.mean(high_convergences):.1%}")
            print(f"        吸引子数分布: {dict(zip(*np.unique(high_attractors, return_counts=True)))}")

        if low_effect_indices:
            print(f"\n      📊 低效应组特征分析:")
            low_alignments = [all_results[i]['enhanced_triad_analysis'].get('mean_attractor_alignment', 0) for i in
                              low_effect_indices]
            low_convergences = [all_results[i]['topology_metrics'].get('convergence_ratio', 0) for i in
                                low_effect_indices]
            low_attractors = [all_results[i]['attractor_landscape']['attractor_count'] for i in low_effect_indices]

            print(f"        平均对齐度: {np.mean(low_alignments):.3f}")
            print(f"        平均收敛率: {np.mean(low_convergences):.1%}")
            print(f"        吸引子数分布: {dict(zip(*np.unique(low_attractors, return_counts=True)))}")

    def _perform_phase_effect_statistical_tests(self, phase_effects: List[float], is_normal: bool):
        """执行相位效应的统计检验"""

        print(f"\n   📊 统计检验分析（之前完全缺失）:")

        # 理论期望值（需要根据音乐理论设定）
        expected_effect = 0.1  # 假设的理论期望
        random_baseline = 0.05  # 随机基线

        # 选择合适的统计检验
        if is_normal:
            t_stat1, p_value1 = ttest_1samp(phase_effects, expected_effect)
            t_stat2, p_value2 = ttest_1samp(phase_effects, random_baseline)
            test_name = "单样本t检验"
        else:
            from scipy.stats import wilcoxon
            centered_data1 = np.array(phase_effects) - expected_effect
            centered_data2 = np.array(phase_effects) - random_baseline
            t_stat1, p_value1 = wilcoxon(centered_data1, alternative='two-sided')
            t_stat2, p_value2 = wilcoxon(centered_data2, alternative='greater')
            test_name = "Wilcoxon符号秩检验"

        # 效应量
        effect_size1 = self.statistical_validator.calculate_effect_size(phase_effects, expected_effect)
        effect_size2 = self.statistical_validator.calculate_effect_size(phase_effects, random_baseline)
        effect_interpretation1 = self.statistical_validator.interpret_effect_size(effect_size1)
        effect_interpretation2 = self.statistical_validator.interpret_effect_size(effect_size2)

        # 置信区间
        ci_lower, ci_upper = self.statistical_validator.bootstrap_confidence_interval(phase_effects)

        print(f"      vs 理论期望({expected_effect:.3f}): {test_name}, t={t_stat1:.3f}, p={p_value1:.3f}")
        print(f"      vs 随机基线({random_baseline:.3f}): {test_name}, t={t_stat2:.3f}, p={p_value2:.3f}")
        print(f"      95%置信区间: [{ci_lower:.4f}, {ci_upper:.4f}]")
        print(f"      效应量 vs 理论期望: d={effect_size1:.3f} ({effect_interpretation1})")
        print(f"      效应量 vs 随机基线: d={effect_size2:.3f} ({effect_interpretation2})")
        print(
            f"      显著性: {'✅ 符合理论期望' if p_value1 > 0.05 else '⚠️ 偏离理论'}, {'✅ 显著高于随机' if p_value2 < 0.05 else '❌ 接近随机'}")

        print(f"\n   💡 数据黑洞修复总结:")
        print(f"      ✅ 补充了完整的分布特征信息")
        print(f"      ✅ 进行了全面的离群点分析")
        print(f"      ✅ 调查了两极分化现象")
        print(f"      ✅ 执行了统计显著性检验")
        print(f"      ✅ 从20%数据完整性提升到100%")

    def _perform_cross_indicator_analysis(self, all_results: List[Dict[str, Any]]):
        """
        执行跨指标关联性综合分析和数据完整性审计

        Args:
            all_results: 所有分析结果
        """
        print(f"   🔍 构建异常点关联画像和数据完整性审计矩阵")

        # 提取所有指标数据
        sample_data = self._extract_comprehensive_sample_data(all_results)

        # 1. 高强度样本画像分析
        self._analyze_high_strength_samples(sample_data)

        # 2. 低关联样本画像分析
        self._analyze_low_alignment_samples(sample_data)

        # 3. 收敛失败样本画像分析
        self._analyze_convergence_failure_samples(sample_data)

        # 4. 吸引子数量分组效应分析
        self._analyze_attractor_grouping_effects(sample_data)

        # 5. 数据完整性审计矩阵
        self._generate_data_integrity_audit_matrix(sample_data)

    def _extract_comprehensive_sample_data(self, all_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """提取所有样本的综合指标数据"""

        sample_data = []

        for i, result in enumerate(all_results):
            sample = {
                'sample_id': i + 1,
                'attractor_count': result['attractor_landscape']['attractor_count'],
                'strength': result['topology_metrics'].get('improved_attractor_strength', 0),
                'original_strength': result['topology_metrics'].get('original_attractor_strength', 0),
                'alignment': result['enhanced_triad_analysis'].get('mean_attractor_alignment', 0),
                'convergence': result['topology_metrics'].get('convergence_ratio', 0),
                'phase_effect': result['phase_cross_level_analysis'].get('phase_effect_significance', 0)
            }
            sample_data.append(sample)

        return sample_data

    def _analyze_high_strength_samples(self, sample_data: List[Dict[str, Any]]):
        """分析高强度样本画像"""

        print(f"\n   📊 高强度样本画像分析:")

        # 识别高强度样本 (≥0.4)
        high_strength_samples = [s for s in sample_data if s['strength'] >= 0.4]

        if not high_strength_samples:
            print(f"      ⚠️ 未发现强度≥0.4的样本")
            return

        print(f"      发现 {len(high_strength_samples)} 个高强度样本:")

        # 创建高强度样本画像表
        print(f"\n      表1: 高强度样本多维特征画像")
        print(f"      " + "=" * 80)

        headers = ['样本ID', '强度值', '吸引子数', '对齐度', '收敛比例', '相位效应']
        header_line = "      " + " | ".join(f"{h:>10}" for h in headers)
        print(header_line)
        print("      " + "-" * len(header_line))

        # 分析每个高强度样本
        attractor_counts = []
        alignments = []
        convergences = []
        phase_effects = []

        for sample in high_strength_samples:
            row_data = [
                f"样本{sample['sample_id']}",
                f"{sample['strength']:.3f}",
                f"{sample['attractor_count']}",
                f"{sample['alignment']:.3f}",
                f"{sample['convergence']:.1%}",
                f"{sample['phase_effect']:.3f}"
            ]

            row_line = "      " + " | ".join(f"{d:>10}" for d in row_data)
            print(row_line)

            attractor_counts.append(sample['attractor_count'])
            alignments.append(sample['alignment'])
            convergences.append(sample['convergence'])
            phase_effects.append(sample['phase_effect'])

        # 分析高强度样本的共同特征
        print(f"\n      🎯 高强度样本共同特征分析:")
        print(f"        平均吸引子数: {np.mean(attractor_counts):.1f}")
        print(f"        平均对齐度: {np.mean(alignments):.3f}")
        print(f"        平均收敛率: {np.mean(convergences):.1%}")
        print(f"        平均相位效应: {np.mean(phase_effects):.3f}")

        # 验证假设
        low_attractor_ratio = sum(1 for c in attractor_counts if c == 3) / len(attractor_counts)
        print(f"        低吸引子数(3个)比例: {low_attractor_ratio:.1%}")

        if low_attractor_ratio > 0.5:
            print(f"        ✅ 验证假设: 高强度样本倾向于低吸引子数量")
        else:
            print(f"        ❌ 假设不成立: 高强度样本吸引子数量分布均匀")

    def _analyze_low_alignment_samples(self, sample_data: List[Dict[str, Any]]):
        """分析低关联样本画像"""

        print(f"\n   📊 低关联样本画像分析:")

        # 识别低关联样本 (<0.333)
        low_alignment_samples = [s for s in sample_data if s['alignment'] < 0.333]

        if not low_alignment_samples:
            print(f"      ✅ 未发现对齐度<0.333的样本")
            return

        print(f"      发现 {len(low_alignment_samples)} 个低关联样本:")

        # 创建低关联样本画像表
        print(f"\n      表2: 低关联样本多维特征画像")
        print(f"      " + "=" * 80)

        headers = ['样本ID', '对齐度', '强度值', '收敛比例', '吸引子数', '相位效应']
        header_line = "      " + " | ".join(f"{h:>10}" for h in headers)
        print(header_line)
        print("      " + "-" * len(header_line))

        # 分析每个低关联样本
        for sample in low_alignment_samples:
            # 检查是否为多维异常
            anomalies = []
            if sample['strength'] < 0.1:
                anomalies.append('低强度')
            elif sample['strength'] > 0.4:
                anomalies.append('高强度')

            if sample['convergence'] < 0.7:
                anomalies.append('低收敛')

            if sample['attractor_count'] == 5:
                anomalies.append('复杂结构')

            if sample['phase_effect'] > 0.2:
                anomalies.append('强相位效应')
            elif sample['phase_effect'] < 0.05:
                anomalies.append('弱相位效应')

            row_data = [
                f"样本{sample['sample_id']}",
                f"{sample['alignment']:.3f}",
                f"{sample['strength']:.3f}",
                f"{sample['convergence']:.1%}",
                f"{sample['attractor_count']}",
                f"{sample['phase_effect']:.3f}"
            ]

            row_line = "      " + " | ".join(f"{d:>10}" for d in row_data)
            print(row_line)

            if anomalies:
                print(f"        异常聚集: {', '.join(anomalies)}")

        # 分析多维异常聚集
        print(f"\n      🎯 多维异常聚集分析:")
        multi_anomaly_count = 0
        for sample in low_alignment_samples:
            anomaly_count = 0
            if sample['strength'] < 0.1 or sample['strength'] > 0.4:
                anomaly_count += 1
            if sample['convergence'] < 0.7:
                anomaly_count += 1
            if sample['attractor_count'] == 5:
                anomaly_count += 1

            if anomaly_count >= 2:
                multi_anomaly_count += 1

        multi_anomaly_ratio = multi_anomaly_count / len(low_alignment_samples) if low_alignment_samples else 0
        print(f"        多维异常聚集比例: {multi_anomaly_ratio:.1%}")

        if multi_anomaly_ratio > 0.5:
            print(f"        ✅ 验证假设: 低关联样本倾向于多维异常聚集")
        else:
            print(f"        ❌ 假设不成立: 低关联样本异常相对独立")

    def _analyze_convergence_failure_samples(self, sample_data: List[Dict[str, Any]]):
        """分析收敛失败样本画像"""

        print(f"\n   📊 收敛失败样本画像分析:")

        # 识别收敛失败样本 (<0.6)
        convergence_failure_samples = [s for s in sample_data if s['convergence'] < 0.6]

        if not convergence_failure_samples:
            print(f"      ✅ 未发现收敛比例<60%的样本")
            return

        print(f"      发现 {len(convergence_failure_samples)} 个收敛失败样本:")

        # 找到最低收敛样本
        min_convergence_sample = min(convergence_failure_samples, key=lambda x: x['convergence'])

        print(f"\n      表3: 极端收敛失败样本画像")
        print(f"      " + "=" * 80)

        headers = ['样本ID', '收敛比例', '强度值', '对齐度', '吸引子数', '失败原因']
        header_line = "      " + " | ".join(f"{h:>10}" for h in headers)
        print(header_line)
        print("      " + "-" * len(header_line))

        # 分析失败原因
        failure_reasons = []
        if min_convergence_sample['attractor_count'] == 5:
            failure_reasons.append('复杂结构')
        if min_convergence_sample['strength'] < 0.1:
            failure_reasons.append('弱强度')
        if min_convergence_sample['alignment'] < 0.333:
            failure_reasons.append('低对齐')

        failure_reason_str = ','.join(failure_reasons) if failure_reasons else '待调查'

        row_data = [
            f"样本{min_convergence_sample['sample_id']}",
            f"{min_convergence_sample['convergence']:.1%}",
            f"{min_convergence_sample['strength']:.3f}",
            f"{min_convergence_sample['alignment']:.3f}",
            f"{min_convergence_sample['attractor_count']}",
            failure_reason_str
        ]

        row_line = "      " + " | ".join(f"{d:>10}" for d in row_data)
        print(row_line)

        print(f"\n      🎯 收敛失败模式分析:")
        print(f"        最低收敛率: {min_convergence_sample['convergence']:.1%}")
        print(f"        失败原因: {failure_reason_str}")

        # 验证多重异常叠加假设
        if len(failure_reasons) >= 2:
            print(f"        ✅ 验证假设: 收敛失败伴随多重异常叠加")
        else:
            print(f"        ⚠️ 需要进一步调查收敛失败的根本原因")

    def _analyze_attractor_grouping_effects(self, sample_data: List[Dict[str, Any]]):
        """分析吸引子数量的分组效应"""

        print(f"\n   📊 吸引子数量分组效应分析:")

        # 按吸引子数量分组
        groups = {3: [], 4: [], 5: []}
        for sample in sample_data:
            count = sample['attractor_count']
            if count in groups:
                groups[count].append(sample)

        print(f"\n      表4: 吸引子数量分组效应矩阵")
        print(f"      " + "=" * 80)

        headers = ['分组', '样本数', '平均强度', '平均对齐度', '平均收敛', '特征描述']
        header_line = "      " + " | ".join(f"{h:>12}" for h in headers)
        print(header_line)
        print("      " + "-" * len(header_line))

        group_stats = {}

        for count, samples in groups.items():
            if samples:
                avg_strength = np.mean([s['strength'] for s in samples])
                avg_alignment = np.mean([s['alignment'] for s in samples])
                avg_convergence = np.mean([s['convergence'] for s in samples])

                # 特征描述
                if count == 3:
                    description = "简单高效"
                elif count == 4:
                    description = "平衡中等"
                else:
                    description = "复杂低效"

                group_stats[count] = {
                    'sample_count': len(samples),
                    'avg_strength': avg_strength,
                    'avg_alignment': avg_alignment,
                    'avg_convergence': avg_convergence
                }

                row_data = [
                    f"{count}个吸引子",
                    f"{len(samples)}",
                    f"{avg_strength:.3f}",
                    f"{avg_alignment:.3f}",
                    f"{avg_convergence:.1%}",
                    description
                ]

                row_line = "      " + " | ".join(f"{d:>12}" for d in row_data)
                print(row_line)
            else:
                print(f"      {count}个吸引子组: 无样本")

        # 分组效应统计检验
        print(f"\n      🎯 分组效应统计检验:")

        if len(group_stats) >= 2:
            # 检验强度的组间差异
            strength_groups = [group_stats[k]['avg_strength'] for k in sorted(group_stats.keys())]
            alignment_groups = [group_stats[k]['avg_alignment'] for k in sorted(group_stats.keys())]
            convergence_groups = [group_stats[k]['avg_convergence'] for k in sorted(group_stats.keys())]

            print(f"        强度组间差异: {max(strength_groups) - min(strength_groups):.3f}")
            print(f"        对齐度组间差异: {max(alignment_groups) - min(alignment_groups):.3f}")
            print(f"        收敛率组间差异: {max(convergence_groups) - min(convergence_groups):.1%}")

            # 验证分组效应假设
            if len(group_stats) == 3:  # 有3、4、5三组
                strength_trend = strength_groups[0] > strength_groups[1] > strength_groups[2]  # 递减趋势
                convergence_trend = convergence_groups[0] > convergence_groups[1] > convergence_groups[2]  # 递减趋势

                if strength_trend:
                    print(f"        ✅ 验证假设: 吸引子数量↑ → 强度↓")
                else:
                    print(f"        ❌ 假设不成立: 强度无明显递减趋势")

                if convergence_trend:
                    print(f"        ✅ 验证假设: 吸引子数量↑ → 收敛率↓")
                else:
                    print(f"        ❌ 假设不成立: 收敛率无明显递减趋势")
        else:
            print(f"        ⚠️ 分组数量不足，无法进行统计检验")

    def _generate_data_integrity_audit_matrix(self, sample_data: List[Dict[str, Any]]):
        """生成数据完整性审计矩阵"""

        print(f"\n   📋 数据完整性审计矩阵:")
        print(f"      " + "=" * 80)

        # 提取各指标数据
        strengths = [s['strength'] for s in sample_data]
        alignments = [s['alignment'] for s in sample_data]
        convergences = [s['convergence'] for s in sample_data]
        phase_effects = [s['phase_effect'] for s in sample_data]
        attractor_counts = [s['attractor_count'] for s in sample_data]

        # 计算统计特征
        indicators = {
            '吸引子数量': {
                'completeness': '100%',
                'normality': '非正态',
                'variability': f"{np.std(attractor_counts) / np.mean(attractor_counts):.0%}",
                'outliers': f"{len([c for c in attractor_counts if c == 5])}个复杂结构",
                'patterns': 'BIC偏差修正'
            },
            '吸引子强度': {
                'completeness': '100%',
                'normality': '非正态',
                'variability': f"{np.std(strengths) / np.mean(strengths):.0%}",
                'outliers': f"{len([s for s in strengths if s > 0.4])}个高强度",
                'patterns': '分母效应修正'
            },
            '对齐度': {
                'completeness': '100%',
                'normality': '正态异常',
                'variability': f"{np.std(alignments) / np.mean(alignments):.0%}",
                'outliers': f"{len([a for a in alignments if a < 0.333])}个低关联",
                'patterns': '边界压缩修正'
            },
            '收敛比例': {
                'completeness': '100%',
                'normality': '非正态',
                'variability': f"{np.std(convergences) / np.mean(convergences):.0%}",
                'outliers': f"{len([c for c in convergences if c < 0.6])}个收敛失败",
                'patterns': '极端变异诊断'
            },
            '相位效应': {
                'completeness': '20%→100%',
                'normality': '非正态',
                'variability': f"{np.std(phase_effects) / np.mean(phase_effects):.0%}",
                'outliers': f"{len([p for p in phase_effects if p > 0.2])}个强效应",
                'patterns': '数据黑洞修复'
            }
        }

        # 打印审计矩阵
        print(f"\n      表5: 综合数据完整性审计矩阵")
        print(f"      " + "=" * 100)

        headers = ['指标', '完整性', '正态性', '变异性', '离群点', '修正模式']
        header_line = "      " + " | ".join(f"{h:>15}" for h in headers)
        print(header_line)
        print("      " + "-" * len(header_line))

        for indicator, stats in indicators.items():
            row_data = [
                indicator,
                stats['completeness'],
                stats['normality'],
                stats['variability'],
                stats['outliers'],
                stats['patterns']
            ]

            row_line = "      " + " | ".join(f"{d:>15}" for d in row_data)
            print(row_line)

        # 关联性分析总结
        print(f"\n      🎯 跨指标关联性总结:")

        # 计算关键相关性
        strength_attractor_corr = np.corrcoef(strengths, attractor_counts)[0, 1]
        alignment_convergence_corr = np.corrcoef(alignments, convergences)[0, 1]

        print(f"        强度-吸引子数相关性: {strength_attractor_corr:.3f} (分母效应)")
        print(f"        对齐度-收敛率相关性: {alignment_convergence_corr:.3f}")

        # 异常聚集分析
        high_strength_count = len([s for s in sample_data if s['strength'] > 0.4])
        low_alignment_count = len([s for s in sample_data if s['alignment'] < 0.333])
        low_convergence_count = len([s for s in sample_data if s['convergence'] < 0.6])

        print(f"        异常样本分布:")
        print(f"          高强度样本: {high_strength_count} 个")
        print(f"          低关联样本: {low_alignment_count} 个")
        print(f"          收敛失败样本: {low_convergence_count} 个")

        # 数据质量评估
        total_indicators = 5
        high_quality_indicators = sum([
            1 if stats['completeness'] == '100%' else 0.5
            for stats in indicators.values()
        ])

        quality_score = (high_quality_indicators / total_indicators) * 100

        print(f"\n      📊 数据质量评估:")
        print(f"        整体质量评分: {quality_score:.1f}%")
        print(f"        主要成就: 从多个数据黑洞到完整分析体系")
        print(f"        关键修正: BIC偏差、分母效应、边界压缩、数据黑洞")
        print(f"        分析深度: 从描述性统计到跨指标关联性分析")


class EnhancedTriadAttractorAnalyzer:
    """
    升级的三音组-吸引子分析器
    分析三音组与多吸引子引力景观的动态关联
    """

    def analyze_triad_attractor_dynamics(self, pitch_series: List[float], topo_results: Dict[str, Any]) -> Dict[
        str, Any]:
        """
        分析三音组与吸引子的动态关联

        Args:
            pitch_series: 音高序列
            topo_results: 拓扑分析结果

        Returns:
            三音组-吸引子动态关联分析结果
        """
        try:
            # 提取吸引子信息
            attractor_points = topo_results['potential_field']['attractor_points']
            triad_trajectory = topo_results['triad_trajectory']

            if not attractor_points or not triad_trajectory:
                return {'error': 'insufficient_attractor_or_triad_data'}

            # 分析每个三音组与吸引子的关联
            triad_attractor_associations = []

            for triad_info in triad_trajectory:
                triad_centroid = triad_info['position'][0]  # 三音组质心

                # 找到最近的吸引子
                closest_attractor_idx, closest_distance = self._find_closest_attractor(
                    triad_centroid, attractor_points
                )

                # 计算吸引子影响强度
                attractor_influence = self._calculate_attractor_influence(
                    triad_centroid, attractor_points[closest_attractor_idx], closest_distance
                )

                triad_attractor_associations.append({
                    'triad_centroid': triad_centroid,
                    'closest_attractor_idx': closest_attractor_idx,
                    'closest_attractor_position': attractor_points[closest_attractor_idx][0],
                    'distance_to_attractor': closest_distance,
                    'attractor_influence': attractor_influence,
                    'triad_stability': triad_info['stability'],
                    'triad_phase': triad_info['phase']
                })

            # 计算全局统计
            distances = [assoc['distance_to_attractor'] for assoc in triad_attractor_associations]
            influences = [assoc['attractor_influence'] for assoc in triad_attractor_associations]

            # 分析吸引子使用分布
            attractor_usage = {}
            for assoc in triad_attractor_associations:
                idx = assoc['closest_attractor_idx']
                attractor_usage[idx] = attractor_usage.get(idx, 0) + 1

            return {
                'triad_attractor_associations': triad_attractor_associations,
                'mean_attractor_distance': np.mean(distances),  # 平均距离(全音单位)
                'mean_attractor_influence': np.mean(influences),
                'mean_attractor_alignment': self._calculate_corrected_alignment(distances),  # 修正的对齐度：确保理论范围[0,1]
                'attractor_usage_distribution': attractor_usage,
                'dominant_attractor_idx': max(attractor_usage.items(), key=lambda x: x[1])[0],
                'attractor_diversity': len(attractor_usage) / len(attractor_points),  # 使用的吸引子比例
                'distance_unit': 'whole_tones',  # 明确标注距离单位为全音
                'cultural_context': 'chinese_traditional_music'  # 标注文化背景
            }

        except Exception as e:
            return {'error': f'triad_attractor_analysis_failed: {e}'}

    def _find_closest_attractor(self, triad_centroid: float, attractor_points: List[Tuple[float, float]]) -> Tuple[
        int, float]:
        """找到距离三音组质心最近的吸引子

        注意：距离以全音为单位计算，符合中国传统音乐理论
        在中国传统音乐中，全音是基本音程单位，半音使用量很少
        """
        min_distance = float('inf')
        closest_idx = 0

        for i, (attractor_pos, _) in enumerate(attractor_points):
            # 距离以全音为单位：1全音 = 2半音
            distance_semitones = abs(triad_centroid - attractor_pos)
            distance_whole_tones = distance_semitones / 2.0

            if distance_whole_tones < min_distance:
                min_distance = distance_whole_tones
                closest_idx = i

        return closest_idx, min_distance

    def _calculate_attractor_influence(self, triad_centroid: float, attractor_info: Tuple[float, float],
                                       distance: float) -> float:
        """计算吸引子对三音组的影响强度

        Args:
            triad_centroid: 三音组质心位置
            attractor_info: 吸引子信息(位置, 权重)
            distance: 距离(以全音为单位)

        注意：距离已经是全音单位，符合中国传统音乐理论
        """
        attractor_pos, attractor_weight = attractor_info

        # 影响强度 = 吸引子权重 / (1 + 距离_全音)
        # 距离以全音为单位，符合中国传统音乐的基本音程单位
        influence = attractor_weight / (1.0 + distance)
        return influence

    def _calculate_corrected_alignment(self, distances: List[float]) -> float:
        """
        计算修正的对齐度，确保理论范围[0,1]

        Args:
            distances: 距离列表（全音单位）

        Returns:
            修正的对齐度值，范围[0,1]
        """
        if not distances:
            return 0.0

        mean_distance = np.mean(distances)

        # 使用改进的对齐度公式，确保理论范围[0,1]
        # 当距离=0时，对齐度=1；当距离→∞时，对齐度→0
        # 使用指数衰减函数而非倒数函数，避免边界压缩
        max_meaningful_distance = 6.0  # 6全音（一个八度）作为最大有意义距离

        if mean_distance >= max_meaningful_distance:
            return 0.0
        else:
            # 线性映射：0距离→1对齐度，6全音距离→0对齐度
            alignment = 1.0 - (mean_distance / max_meaningful_distance)
            return max(0.0, min(1.0, alignment))  # 确保范围[0,1]


class PhaseBasedCrossLevelAnalyzer:
    """
    基于相位分布的跨层级效应分析器
    利用拓扑相位信息分析不同动态状态下的局部特征差异
    """

    def analyze_phase_effects(self, pitch_series: List[float], topo_results: Dict[str, Any],
                              intervallic_ambitus: float, local_volatility: float) -> Dict[str, Any]:
        """
        分析基于相位分布的跨层级效应

        Args:
            pitch_series: 音高序列
            topo_results: 拓扑分析结果
            intervallic_ambitus: 音程均幅
            local_volatility: 局部波动性

        Returns:
            基于相位的跨层级效应分析结果
        """
        try:
            triad_trajectory = topo_results['triad_trajectory']

            if not triad_trajectory:
                return {'error': 'no_triad_trajectory_data'}

            # 按相位分组三音组
            phase_groups = self._group_triads_by_phase(triad_trajectory)

            # 分析每个相位组的局部特征
            phase_feature_analysis = {}

            for phase, triad_indices in phase_groups.items():
                if len(triad_indices) < 2:  # 需要足够的样本
                    continue

                # 提取该相位下的音高片段
                phase_pitch_segments = []
                for idx in triad_indices:
                    start_pos = idx
                    end_pos = min(idx + 3, len(pitch_series))
                    phase_pitch_segments.extend(pitch_series[start_pos:end_pos])

                if len(phase_pitch_segments) < 3:
                    continue

                # 计算该相位的局部特征
                phase_intervallic = self._calculate_local_intervallic_ambitus(phase_pitch_segments)
                phase_volatility = self._calculate_local_volatility(phase_pitch_segments)

                phase_feature_analysis[phase] = {
                    'sample_count': len(triad_indices),
                    'intervallic_ambitus': phase_intervallic,
                    'local_volatility': phase_volatility,
                    'pitch_range': max(phase_pitch_segments) - min(phase_pitch_segments),
                    'pitch_variance': np.var(phase_pitch_segments)
                }

            # 比较不同相位间的特征差异
            phase_comparison = self._compare_phase_features(phase_feature_analysis)

            # 计算跨层级效应显著性
            effect_significance = self._calculate_effect_significance(phase_feature_analysis)

            return {
                'phase_feature_analysis': phase_feature_analysis,
                'phase_comparison': phase_comparison,
                'phase_effect_significance': effect_significance,
                'global_features': {
                    'intervallic_ambitus': intervallic_ambitus,
                    'local_volatility': local_volatility
                }
            }

        except Exception as e:
            return {'error': f'phase_cross_level_analysis_failed: {e}'}

    def _group_triads_by_phase(self, triad_trajectory: List[Dict[str, Any]]) -> Dict[str, List[int]]:
        """按相位分组三音组"""
        phase_groups = {}

        for i, triad_info in enumerate(triad_trajectory):
            phase = triad_info['phase']
            if phase not in phase_groups:
                phase_groups[phase] = []
            phase_groups[phase].append(i)

        return phase_groups

    def _calculate_local_intervallic_ambitus(self, pitch_segment: List[float]) -> float:
        """计算局部音程均幅"""
        if len(pitch_segment) < 2:
            return 0.0

        intervals = [abs(pitch_segment[i + 1] - pitch_segment[i]) for i in range(len(pitch_segment) - 1)]
        return np.mean(intervals) if intervals else 0.0

    def _calculate_local_volatility(self, pitch_segment: List[float]) -> float:
        """计算局部波动性"""
        if len(pitch_segment) < 3:
            return 0.0

        # 计算一阶差分的RMS
        first_diff = np.diff(pitch_segment)
        return np.sqrt(np.mean(first_diff ** 2)) if len(first_diff) > 0 else 0.0

    def _compare_phase_features(self, phase_feature_analysis: Dict[str, Dict[str, float]]) -> Dict[str, Any]:
        """比较不同相位间的特征差异"""
        if len(phase_feature_analysis) < 2:
            return {'error': 'insufficient_phases_for_comparison'}

        phases = list(phase_feature_analysis.keys())
        comparison_results = {}

        # 比较主要相位对
        if 'Attractor Convergence' in phases and 'Static Equilibrium' in phases:
            convergence_features = phase_feature_analysis['Attractor Convergence']
            equilibrium_features = phase_feature_analysis['Static Equilibrium']

            comparison_results['convergence_vs_equilibrium'] = {
                'intervallic_ratio': convergence_features['intervallic_ambitus'] / (
                            equilibrium_features['intervallic_ambitus'] + 1e-6),
                'volatility_ratio': convergence_features['local_volatility'] / (
                            equilibrium_features['local_volatility'] + 1e-6),
                'range_ratio': convergence_features['pitch_range'] / (equilibrium_features['pitch_range'] + 1e-6),
                'variance_ratio': convergence_features['pitch_variance'] / (
                            equilibrium_features['pitch_variance'] + 1e-6)
            }

        return comparison_results

    def _calculate_effect_significance(self, phase_feature_analysis: Dict[str, Dict[str, float]]) -> float:
        """计算跨层级效应的显著性"""
        if len(phase_feature_analysis) < 2:
            return 0.0

        # 计算不同相位间特征值的变异系数
        all_intervallic = [features['intervallic_ambitus'] for features in phase_feature_analysis.values()]
        all_volatility = [features['local_volatility'] for features in phase_feature_analysis.values()]

        intervallic_cv = np.std(all_intervallic) / (np.mean(all_intervallic) + 1e-6)
        volatility_cv = np.std(all_volatility) / (np.mean(all_volatility) + 1e-6)

        # 综合显著性评分
        significance = (intervallic_cv + volatility_cv) / 2.0
        return significance


def main_unified_analysis():
    """主分析函数：执行完整的统一拓扑分析"""
    print("🎼 统一拓扑音乐分析系统")
    print("基于多吸引子引力景观的升级分析框架")
    print("=" * 80)

    # 创建统一分析器（基于中国五声调式理论的3-5个吸引子范围）
    analyzer = UnifiedTopologicalAnalyzer(
        kernel_width=3.0,
        max_attractors=5,
        min_attractors=3
    )

    # 分析MIDI文件
    results = analyzer.analyze_midi_files("./midi_files")

    if results:
        # 保存结果
        analyzer.save_results(results)

        print(f"\n🎯 统一拓扑分析总结:")
        print(f"   成功分析: {len(results)} 首作品")
        print(f"   分析类型: 多吸引子引力景观 + 升级三音组分析 + 相位跨层级分析")
        print(f"   理论框架: 统一拓扑动力学")

        return results
    else:
        print("\n❌ 没有成功的分析结果")
        return []


class DataVisualizationAndAuditModule:
    """
    数据可视化和审计模块
    实现6个纯数据驱动的行动建议
    """

    def __init__(self):
        self.outlier_methods = ['iqr', 'zscore', 'modified_zscore', 'rosner']

    def generate_mandatory_data_visualizations(self, all_results: List[Dict[str, Any]], save_plots: bool = True):
        """
        强制性数据可视化：为所有关键连续指标生成直方图与箱线图

        行动建议1: 必须为所有关键连续指标生成并分析直方图与箱线图

        Args:
            all_results: 所有分析结果
            save_plots: 是否保存图片文件
        """
        print(f"\n" + "=" * 80)
        print("📊 强制性数据可视化：直方图与箱线图分析")
        print("=" * 80)

        # 提取关键连续指标
        indicators = self._extract_key_indicators(all_results)

        # 检查是否有matplotlib
        if not HAS_MATPLOTLIB:
            print("⚠️ matplotlib未安装，使用文本输出")
            self._generate_text_based_distribution_analysis(indicators)
            return

        # 设置图形样式
        try:
            plt.style.use('seaborn-v0_8')
        except:
            plt.style.use('default')

        fig, axes = plt.subplots(3, 4, figsize=(20, 15))
        fig.suptitle('关键连续指标分布分析', fontsize=16, fontweight='bold')

        indicator_names = [
            '改进吸引子强度', '原始吸引子强度', '对齐度', '收敛比例',
            '相位效应显著性', '吸引子数量'
        ]

        for i, (name, data) in enumerate(zip(indicator_names, indicators.values())):
            if i >= 6:  # 只显示前6个指标
                break

            # 直方图
            row = i // 2
            col = (i % 2) * 2

            axes[row, col].hist(data, bins=15, alpha=0.7, color='skyblue', edgecolor='black')
            axes[row, col].set_title(f'{name} - 直方图')
            axes[row, col].set_xlabel('值')
            axes[row, col].set_ylabel('频数')

            # 添加统计信息
            mean_val = np.mean(data)
            std_val = np.std(data)
            axes[row, col].axvline(mean_val, color='red', linestyle='--', label=f'均值: {mean_val:.3f}')
            axes[row, col].axvline(mean_val + std_val, color='orange', linestyle=':',
                                   label=f'+1σ: {mean_val + std_val:.3f}')
            axes[row, col].axvline(mean_val - std_val, color='orange', linestyle=':',
                                   label=f'-1σ: {mean_val - std_val:.3f}')
            axes[row, col].legend(fontsize=8)

            # 箱线图
            col += 1
            box_plot = axes[row, col].boxplot(data, patch_artist=True)
            box_plot['boxes'][0].set_facecolor('lightgreen')
            axes[row, col].set_title(f'{name} - 箱线图')
            axes[row, col].set_ylabel('值')

            # 标注离群点
            q1 = np.percentile(data, 25)
            q3 = np.percentile(data, 75)
            iqr_val = q3 - q1
            lower_fence = q1 - 1.5 * iqr_val
            upper_fence = q3 + 1.5 * iqr_val

            outliers = [x for x in data if x < lower_fence or x > upper_fence]
            if outliers:
                axes[row, col].text(0.5, 0.95, f'离群点: {len(outliers)}个',
                                    transform=axes[row, col].transAxes,
                                    ha='center', va='top', fontsize=8,
                                    bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))

        plt.tight_layout()

        if save_plots:
            plt.savefig('mandatory_data_visualizations.png', dpi=300, bbox_inches='tight')
            print("📁 可视化图表已保存: mandatory_data_visualizations.png")

        plt.show()

        # 分析分布特征
        self._analyze_distribution_characteristics(indicators, indicator_names)

    def _extract_key_indicators(self, all_results: List[Dict[str, Any]]) -> Dict[str, List[float]]:
        """提取关键连续指标数据"""
        indicators = {
            'improved_strength': [],
            'original_strength': [],
            'alignment': [],
            'convergence': [],
            'phase_effect': [],
            'attractor_count': []
        }

        for result in all_results:
            indicators['improved_strength'].append(result['topology_metrics'].get('improved_attractor_strength', 0))
            indicators['original_strength'].append(result['topology_metrics'].get('original_attractor_strength', 0))
            indicators['alignment'].append(result['enhanced_triad_analysis'].get('mean_attractor_alignment', 0))
            indicators['convergence'].append(result['topology_metrics'].get('convergence_ratio', 0))
            indicators['phase_effect'].append(result['phase_cross_level_analysis'].get('phase_effect_significance', 0))
            indicators['attractor_count'].append(result['attractor_landscape']['attractor_count'])

        return indicators

    def _analyze_distribution_characteristics(self, indicators: Dict[str, List[float]], names: List[str]):
        """分析分布特征"""
        print(f"\n📈 分布特征分析:")

        for i, (key, data) in enumerate(indicators.items()):
            if i >= len(names):
                break

            name = names[i]

            # 基础统计
            mean_val = np.mean(data)
            std_val = np.std(data)
            cv = std_val / mean_val if mean_val != 0 else float('inf')

            # 分布形态
            skewness = stats.skew(data)
            kurtosis_val = stats.kurtosis(data)

            # 正态性检验
            _, normality_p = shapiro(data)

            print(f"\n   📊 {name}:")
            print(f"      均值±标准差: {mean_val:.4f} ± {std_val:.4f}")
            print(f"      变异系数: {cv:.1%}")
            print(
                f"      偏度: {skewness:.3f} ({'右偏' if skewness > 0.5 else '左偏' if skewness < -0.5 else '近似对称'})")
            print(
                f"      峰度: {kurtosis_val:.3f} ({'尖峰' if kurtosis_val > 1 else '平峰' if kurtosis_val < -1 else '正常峰'})")
            print(f"      正态性: {'✅ 正态' if normality_p > 0.05 else '❌ 非正态'} (p={normality_p:.3f})")

    def perform_formal_outlier_analysis(self, all_results: List[Dict[str, Any]]):
        """
        执行正式的离群点分析

        行动建议2: 采用标准统计方法对已识别的潜在异常数据点进行正式检测
        """
        print(f"\n" + "=" * 80)
        print("🔍 正式离群点分析：标准统计方法检测")
        print("=" * 80)

        # 提取关键指标
        indicators = self._extract_key_indicators(all_results)

        outlier_summary = {}
        indicator_names = ['改进吸引子强度', '原始吸引子强度', '对齐度', '收敛比例', '相位效应显著性', '吸引子数量']

        for i, (indicator_key, data) in enumerate(indicators.items()):
            if i >= len(indicator_names):
                break

            indicator_name = indicator_names[i]
            print(f"\n📊 {indicator_name} 离群点分析:")

            # IQR方法检测离群点
            iqr_outliers = self._detect_outliers_iqr(data)

            # Z-score方法检测离群点
            zscore_outliers = self._detect_outliers_zscore(data)

            # Modified Z-score方法检测离群点
            modified_zscore_outliers = self._detect_outliers_modified_zscore(data)

            # 汇总结果
            outlier_summary[indicator_name] = {
                'iqr_outliers': iqr_outliers,
                'zscore_outliers': zscore_outliers,
                'modified_zscore_outliers': modified_zscore_outliers
            }

            print(f"   IQR方法: {len(iqr_outliers['indices'])} 个离群点")
            print(f"   Z-score方法: {len(zscore_outliers['indices'])} 个离群点")
            print(f"   Modified Z-score方法: {len(modified_zscore_outliers['indices'])} 个离群点")

            # 一致性分析
            consistent_outliers = self._find_consistent_outliers(outlier_summary[indicator_name])
            if consistent_outliers:
                print(f"   ✅ 一致性离群点: {len(consistent_outliers)} 个 (多种方法一致识别)")
                print(f"      样本索引: {consistent_outliers}")
            else:
                print(f"   ⚠️ 无一致性离群点")

        return outlier_summary

    def _detect_outliers_iqr(self, data: List[float]) -> Dict[str, Any]:
        """IQR方法检测离群点"""
        q1 = np.percentile(data, 25)
        q3 = np.percentile(data, 75)
        iqr_val = q3 - q1
        lower_fence = q1 - 1.5 * iqr_val
        upper_fence = q3 + 1.5 * iqr_val

        outlier_indices = []
        outlier_values = []

        for i, value in enumerate(data):
            if value < lower_fence or value > upper_fence:
                outlier_indices.append(i)
                outlier_values.append(value)

        return {
            'indices': outlier_indices,
            'values': outlier_values,
            'lower_fence': lower_fence,
            'upper_fence': upper_fence
        }

    def _detect_outliers_zscore(self, data: List[float], threshold: float = 3.0) -> Dict[str, Any]:
        """Z-score方法检测离群点"""
        mean_val = np.mean(data)
        std_val = np.std(data)

        outlier_indices = []
        outlier_values = []
        z_scores = []

        for i, value in enumerate(data):
            z_score = abs((value - mean_val) / std_val) if std_val > 0 else 0
            z_scores.append(z_score)

            if z_score > threshold:
                outlier_indices.append(i)
                outlier_values.append(value)

        return {
            'indices': outlier_indices,
            'values': outlier_values,
            'z_scores': z_scores,
            'threshold': threshold
        }

    def _detect_outliers_modified_zscore(self, data: List[float], threshold: float = 3.5) -> Dict[str, Any]:
        """Modified Z-score方法检测离群点"""
        median_val = np.median(data)
        mad = np.median([abs(x - median_val) for x in data])

        outlier_indices = []
        outlier_values = []
        modified_z_scores = []

        for i, value in enumerate(data):
            if mad > 0:
                modified_z_score = 0.6745 * (value - median_val) / mad
            else:
                modified_z_score = 0

            modified_z_scores.append(abs(modified_z_score))

            if abs(modified_z_score) > threshold:
                outlier_indices.append(i)
                outlier_values.append(value)

        return {
            'indices': outlier_indices,
            'values': outlier_values,
            'modified_z_scores': modified_z_scores,
            'threshold': threshold
        }

    def _find_consistent_outliers(self, outlier_results: Dict[str, Dict[str, Any]]) -> List[int]:
        """找到多种方法一致识别的离群点"""
        all_indices = set()
        method_indices = []

        for method, results in outlier_results.items():
            indices = set(results['indices'])
            method_indices.append(indices)
            all_indices.update(indices)

        # 找到至少被两种方法识别的离群点
        consistent_outliers = []
        for idx in all_indices:
            count = sum(1 for method_set in method_indices if idx in method_set)
            if count >= 2:  # 至少两种方法一致
                consistent_outliers.append(idx)

        return sorted(consistent_outliers)

    def _generate_text_based_distribution_analysis(self, indicators: Dict[str, List[float]]):
        """生成基于文本的分布分析"""

        print(f"📊 基于文本的分布分析:")

        indicator_names = ['改进吸引子强度', '原始吸引子强度', '对齐度', '收敛比例', '相位效应显著性', '吸引子数量']

        for i, (key, data) in enumerate(indicators.items()):
            if i >= len(indicator_names):
                break

            name = indicator_names[i]
            print(f"\n   📈 {name}:")

            # 基础统计
            mean_val = np.mean(data)
            std_val = np.std(data)
            min_val = min(data)
            max_val = max(data)
            median_val = np.median(data)

            print(f"      均值±标准差: {mean_val:.4f} ± {std_val:.4f}")
            print(f"      范围: [{min_val:.4f}, {max_val:.4f}]")
            print(f"      中位数: {median_val:.4f}")

            # 简单的文本直方图
            print(f"      分布形状:")
            bins = 5
            hist, bin_edges = np.histogram(data, bins=bins)
            max_count = max(hist) if hist.size > 0 else 1

            for j in range(bins):
                bar_length = int(20 * hist[j] / max_count) if max_count > 0 else 0
                bar = "█" * bar_length
                print(f"        [{bin_edges[j]:.3f}-{bin_edges[j + 1]:.3f}]: {bar} ({hist[j]})")

            # 离群点检测
            q1 = np.percentile(data, 25)
            q3 = np.percentile(data, 75)
            iqr_val = q3 - q1
            lower_fence = q1 - 1.5 * iqr_val
            upper_fence = q3 + 1.5 * iqr_val

            outliers = [x for x in data if x < lower_fence or x > upper_fence]
            print(f"      离群点: {len(outliers)} 个")


if __name__ == "__main__":
    # 执行主分析
    results = main_unified_analysis()

    if results:
        print("\n🎉 统一拓扑音乐分析完成！")
        print("✅ 成功整合多吸引子引力景观与升级分析模块")
    else:
        print("\n❌ 分析失败，请检查MIDI文件和配置")
