#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版中国音乐分析系统 - 完整版
包含：音程均幅、局部波动性、双方法分析、旋律动力系统
重构核心逻辑框架 - 按照新的程序逻辑框架要求构建
"""

import numpy as np
import pandas as pd
import math
import os
import glob
from typing import List, Dict, Any, Tuple

# 尝试导入可选库
try:
    import pretty_midi
    PRETTY_MIDI_AVAILABLE = True
    print("✅ pretty_midi库加载成功")
except ImportError:
    PRETTY_MIDI_AVAILABLE = False
    print("⚠️ pretty_midi库未安装，将跳过MIDI文件处理")

try:
    import matplotlib
    matplotlib.use('Agg')  # 使用非交互式后端
    import matplotlib.pyplot as plt
    VISUALIZATION_AVAILABLE = True
    print("✅ 可视化库加载成功")
except ImportError:
    VISUALIZATION_AVAILABLE = False
    print("⚠️ 可视化库未安装，将跳过图表生成")


class SimpleMidiParser:
    """简化的MIDI解析器，不依赖pretty_midi"""
    
    @staticmethod
    def extract_pitch_from_midi(file_path):
        """从MIDI文件提取音高序列（简化版本）"""
        try:
            with open(file_path, 'rb') as f:
                data = f.read()
            
            # 简化的MIDI解析 - 查找Note On事件
            pitches = []
            i = 0
            while i < len(data) - 3:
                # 查找Note On事件 (0x90-0x9F)
                if data[i] >= 0x90 and data[i] <= 0x9F:
                    if i + 2 < len(data):
                        pitch = data[i + 1]
                        velocity = data[i + 2]
                        if velocity > 0 and 21 <= pitch <= 108:  # 有效的MIDI音高范围
                            pitches.append(pitch)
                i += 1
            
            # 如果没有找到音符，尝试其他方法
            if not pitches:
                # 查找所有可能的音高值
                for byte in data:
                    if 21 <= byte <= 108:  # MIDI音高范围
                        pitches.append(byte)
                
                # 去重并排序（保持原始顺序的去重）
                seen = set()
                unique_pitches = []
                for pitch in pitches:
                    if pitch not in seen:
                        seen.add(pitch)
                        unique_pitches.append(pitch)
                pitches = unique_pitches[:50]  # 限制数量
            
            return pitches[:100] if pitches else []  # 限制最大数量
            
        except Exception as e:
            print(f"     MIDI解析错误: {e}")
            return []


class IntervalicAmbitusAnalyzer:
    """音程均幅 (Intervallic_Ambitus) 分析器"""

    @staticmethod
    def calculate(pitch_series: List[float]) -> float:
        """计算音程均幅 E(log i)"""
        if len(pitch_series) < 2:
            return 0.0

        intervals = [abs(pitch_series[i+1] - pitch_series[i]) for i in range(len(pitch_series)-1)]
        log_intervals = [math.log(max(interval, 1)) for interval in intervals]
        return sum(log_intervals) / len(log_intervals) if log_intervals else 0.0

class LocalVolatilityAnalyzer:
    """局部音程波动性 (Local_Volatility) 分析器"""

    @staticmethod
    def calculate_d1_rms(pitch_series: List[float]) -> float:
        """计算d1_rms（主要的局部波动性指标）"""
        if len(pitch_series) < 2:
            return 0.0

        intervals = [pitch_series[i+1] - pitch_series[i] for i in range(len(pitch_series)-1)]
        return math.sqrt(sum(interval**2 for interval in intervals) / len(intervals)) if intervals else 0.0

    @staticmethod
    def calculate_d2_rms(pitch_series: List[float]) -> float:
        """计算d2_rms（中等尺度波动性，音程曲率）"""
        if len(pitch_series) < 3:
            return 0.0

        # 计算二阶差分（加速度）
        first_diff = [pitch_series[i+1] - pitch_series[i] for i in range(len(pitch_series)-1)]
        second_diff = [first_diff[i+1] - first_diff[i] for i in range(len(first_diff)-1)]

        return math.sqrt(sum(diff**2 for diff in second_diff) / len(second_diff)) if second_diff else 0.0

    @staticmethod
    def calculate_rms_ratio(d1_rms: float, d2_rms: float) -> float:
        """计算RMS比率（波动性特征比率）"""
        if d2_rms == 0:
            return float('inf') if d1_rms > 0 else 0.0
        return d1_rms / d2_rms

    @staticmethod
    def classify_volatility_type(rms_ratio: float) -> str:
        """分类波动性类型"""
        if rms_ratio > 3.0:
            return "尖锐毛刺型"  # 高比率：快速尖锐变化
        elif rms_ratio > 1.5:
            return "混合波动型"  # 中等比率：混合特征
        elif rms_ratio > 0.5:
            return "平滑波浪型"  # 低比率：平滑波浪状
        else:
            return "极平滑型"    # 极低比率：几乎无波动

    @staticmethod
    def identify_ornament_pattern(d1_rms: float, d2_rms: float, rms_ratio: float) -> str:
        """识别装饰音模式"""
        if d1_rms > 5.0 and rms_ratio > 2.0:
            return "颤音/震音型"
        elif d1_rms > 3.0 and rms_ratio > 1.5:
            return "回音/波音型"
        elif d1_rms > 1.0 and rms_ratio < 1.0:
            return "滑音/连音型"
        else:
            return "简单进行型"

class MelodyDynamicsSystem:
    """
    旋律动力系统分析器
    基于动力系统理论分析旋律的：
    - 导数系统（速度、加速度）
    - 吸引子识别（隐形引力线）
    - 曲率张量计算
    - 多尺度时间分析
    - 系统稳定性评估
    """

    def __init__(self, time_window=0.2, attractor_threshold=3.0):
        self.time_window = time_window
        self.attractor_threshold = attractor_threshold

    def analyze_dynamics(self, pitch_series: List[float]) -> Dict[str, Any]:
        """分析旋律动力系统特征"""
        if len(pitch_series) < 3:
            return {'error': 'insufficient_data_for_dynamics_analysis'}

        try:
            pitch_array = np.array(pitch_series)

            # 1. 计算导数系统
            velocity, acceleration = self._compute_derivatives(pitch_array)

            # 2. 识别吸引子
            attractors = self._detect_attractors(pitch_array)

            # 3. 计算曲率张量
            curvature = self._calculate_curvature(velocity, acceleration)

            # 4. 分析多尺度特征
            time_scales = self._multi_scale_analysis(pitch_array)

            # 5. 评估系统稳定性
            stability = self._assess_stability(attractors, curvature)

            # 6. 计算综合动力学指标
            dynamics_metrics = self._calculate_dynamics_metrics(
                velocity, acceleration, curvature, attractors
            )

            return {
                'velocity': velocity.tolist(),
                'acceleration': acceleration.tolist(),
                'attractors': attractors,
                'curvature': curvature.tolist(),
                'time_scales': time_scales,
                'stability': stability,
                'dynamics_metrics': dynamics_metrics,
                'system_type': self._classify_system_type(dynamics_metrics)
            }

        except Exception as e:
            return {'error': f'dynamics_analysis_failed: {e}'}

    def _compute_derivatives(self, pitch_series: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """计算音高导数（速度、加速度）"""
        n = len(pitch_series)
        window_size = max(1, int(n * self.time_window))

        # 一阶导数（速度）
        velocity = np.zeros(n)
        for i in range(n):
            start = max(0, i - window_size//2)
            end = min(n, i + window_size//2 + 1)
            if end - start > 1:
                velocity[i] = np.mean(np.diff(pitch_series[start:end]))
            else:
                velocity[i] = 0.0

        # 二阶导数（加速度）
        acceleration = np.gradient(velocity)

        return velocity, acceleration

    def _detect_attractors(self, pitch_series: np.ndarray) -> Dict[str, Any]:
        """识别吸引子（隐形引力线）"""
        try:
            # 简化的吸引子检测
            pitch_counts = {}
            for pitch in pitch_series:
                rounded_pitch = round(pitch)
                pitch_counts[rounded_pitch] = pitch_counts.get(rounded_pitch, 0) + 1

            # 找到主要吸引子
            total_notes = len(pitch_series)
            attractors = []
            for pitch, count in pitch_counts.items():
                if count / total_notes > 0.1:  # 出现频率超过10%
                    attractors.append(pitch)

            # 计算到最近吸引子的距离
            distances = []
            for pitch in pitch_series:
                if attractors:
                    min_dist = min(abs(pitch - attractor) for attractor in attractors)
                    distances.append(min_dist)
                else:
                    distances.append(0.0)

            avg_distance = sum(distances) / len(distances) if distances else 0.0

            return {
                'positions': attractors,
                'distances': distances,
                'strength': len(attractors) / total_notes if total_notes > 0 else 0.0,
                'count': len(attractors),
                'avg_distance': avg_distance
            }

        except Exception as e:
            return {
                'positions': [],
                'distances': [0.0] * len(pitch_series),
                'strength': 0.0,
                'count': 0,
                'avg_distance': 0.0,
                'error': str(e)
            }

    def _calculate_curvature(self, velocity: np.ndarray, acceleration: np.ndarray) -> np.ndarray:
        """计算曲率张量"""
        curvature = np.zeros(len(velocity))

        for i in range(len(velocity)):
            v = velocity[i]
            a = acceleration[i]

            if abs(v) > 1e-5:
                curvature[i] = abs(a) / (abs(v)**2)
            else:
                curvature[i] = 0.0

        return curvature

    def _multi_scale_analysis(self, pitch_series: np.ndarray) -> Dict[str, Dict]:
        """多尺度时间分析"""
        scales = {
            'micro_scale': self._analyze_scale(pitch_series, window=0.05),
            'meso_scale': self._analyze_scale(pitch_series, window=0.3),
            'macro_scale': self._analyze_scale(pitch_series, window=0.7)
        }
        return scales

    def _analyze_scale(self, pitch_series: np.ndarray, window: float) -> Dict:
        """特定时间尺度的分析"""
        n = len(pitch_series)
        window_size = max(1, int(n * window))

        features = []
        for i in range(n):
            start = max(0, i - window_size//2)
            end = min(n, i + window_size//2 + 1)
            segment = pitch_series[start:end]

            if len(segment) > 1:
                diff_segment = np.diff(segment)
                zcr = np.sum(np.diff(np.sign(diff_segment)) != 0) / len(diff_segment) if len(diff_segment) > 1 else 0.0
                envelope = np.max(segment) - np.min(segment)
                mean_val = np.mean(segment)
                dynamic_range = envelope / (abs(mean_val) + 1e-5)
                perceived_variation = zcr * envelope
                local_complexity = np.std(diff_segment) if len(diff_segment) > 1 else 0.0

                features.append({
                    'position': i,
                    'zero_crossing_rate': float(zcr),
                    'envelope_depth': float(envelope),
                    'dynamic_range': float(dynamic_range),
                    'perceived_variation': float(perceived_variation),
                    'local_complexity': float(local_complexity)
                })

        # 返回汇总统计
        if features:
            return {
                'avg_zero_crossing_rate': np.mean([f['zero_crossing_rate'] for f in features]),
                'avg_envelope_depth': np.mean([f['envelope_depth'] for f in features]),
                'avg_perceived_variation': np.mean([f['perceived_variation'] for f in features]),
                'avg_local_complexity': np.mean([f['local_complexity'] for f in features])
            }
        else:
            return {
                'avg_zero_crossing_rate': 0.0,
                'avg_envelope_depth': 0.0,
                'avg_perceived_variation': 0.0,
                'avg_local_complexity': 0.0
            }

    def _assess_stability(self, attractors: Dict, curvature: np.ndarray) -> str:
        """评估系统稳定性"""
        avg_distance = attractors.get('avg_distance', 0.0)
        avg_curvature = np.mean(curvature)
        curvature_variance = np.var(curvature)
        attractor_count = attractors.get('count', 0)

        if curvature_variance > 0.5 and avg_curvature > 0.1:
            return "高度不稳定（脉冲式）"
        elif avg_distance < 2.0 and curvature_variance < 0.1 and attractor_count > 0:
            return "高度稳定（吸引子主导）"
        elif avg_curvature < 0.05:
            return "稳定（低曲率）"
        elif attractor_count == 0:
            return "无吸引子（自由运动）"
        else:
            return "中等稳定"

    def _calculate_dynamics_metrics(self, velocity: np.ndarray, acceleration: np.ndarray,
                                  curvature: np.ndarray, attractors: Dict) -> Dict[str, float]:
        """计算综合动力学指标"""
        return {
            'mean_velocity': float(np.mean(np.abs(velocity))),
            'velocity_variance': float(np.var(velocity)),
            'max_velocity': float(np.max(np.abs(velocity))),
            'mean_acceleration': float(np.mean(np.abs(acceleration))),
            'acceleration_variance': float(np.var(acceleration)),
            'max_acceleration': float(np.max(np.abs(acceleration))),
            'mean_curvature': float(np.mean(curvature)),
            'curvature_variance': float(np.var(curvature)),
            'max_curvature': float(np.max(curvature)),
            'attractor_count': attractors.get('count', 0),
            'attractor_strength': attractors.get('strength', 0.0),
            'mean_attractor_distance': attractors.get('avg_distance', 0.0),
            'system_energy': float(np.mean(velocity**2 + acceleration**2)),
            'phase_space_volume': float(np.std(velocity) * np.std(acceleration)),
            'lyapunov_proxy': float(np.mean(np.abs(np.diff(curvature))))
        }

    def _classify_system_type(self, metrics: Dict[str, float]) -> str:
        """分类动力系统类型"""
        energy = metrics['system_energy']
        lyapunov = metrics['lyapunov_proxy']
        attractor_count = metrics['attractor_count']

        if lyapunov > 0.1 and energy > 10.0:
            return "混沌系统"
        elif attractor_count > 2 and metrics['attractor_strength'] > 0.3:
            return "多吸引子系统"
        elif attractor_count == 1 and metrics['mean_attractor_distance'] < 2.0:
            return "单吸引子系统"
        elif energy < 1.0 and lyapunov < 0.01:
            return "准静态系统"
        else:
            return "复杂动力系统"



class DualMethodAnalyzer:
    """双方法分析器"""
    
    @staticmethod
    def calculate_orthogonality(method1_values, method2_values):
        """计算正交性"""
        if len(method1_values) != len(method2_values) or len(method1_values) < 2:
            return {'correlation': 0.0, 'assessment': '数据不足'}
        
        # 简化的相关性计算
        mean1 = sum(method1_values) / len(method1_values)
        mean2 = sum(method2_values) / len(method2_values)
        
        numerator = sum((v1 - mean1) * (v2 - mean2) for v1, v2 in zip(method1_values, method2_values))
        
        sum_sq1 = sum((v1 - mean1)**2 for v1 in method1_values)
        sum_sq2 = sum((v2 - mean2)**2 for v2 in method2_values)
        
        if sum_sq1 == 0 or sum_sq2 == 0:
            correlation = 0.0
        else:
            correlation = numerator / math.sqrt(sum_sq1 * sum_sq2)
        
        # 正交性评估
        if abs(correlation) < 0.3:
            assessment = "高度正交"
        elif abs(correlation) < 0.6:
            assessment = "中度相关"
        else:
            assessment = "强相关"
        
        return {
            'correlation': correlation,
            'assessment': assessment
        }


class EnhancedChineseMusicAnalyzer:
    """增强版中国音乐分析器"""
    
    def __init__(self):
        self.intervallic_ambitus_analyzer = IntervalicAmbitusAnalyzer()
        self.local_volatility_analyzer = LocalVolatilityAnalyzer()
        self.melody_dynamics_analyzer = MelodyDynamicsSystem()
        self.dual_method_analyzer = DualMethodAnalyzer()
        self.pitch_series = None
        
    def load_midi_file(self, file_path):
        """加载MIDI文件"""
        try:
            if PRETTY_MIDI_AVAILABLE:
                # 使用pretty_midi（如果可用）
                midi_data = pretty_midi.PrettyMIDI(file_path)
                pitch_series = []
                for instrument in midi_data.instruments:
                    if not instrument.is_drum:
                        for note in instrument.notes:
                            pitch_series.append(note.pitch)
            else:
                # 使用简化的MIDI解析器
                pitch_series = SimpleMidiParser.extract_pitch_from_midi(file_path)
            
            if pitch_series:
                self.pitch_series = pd.Series(pitch_series)
                print(f"   从MIDI文件提取了 {len(pitch_series)} 个音符")
                return True
            else:
                print(f"   ⚠️ MIDI文件中未找到音符数据")
                return False
                
        except Exception as e:
            print(f"   ❌ MIDI文件处理失败: {e}")
            return False
    
    def load_csv_file(self, file_path):
        """加载CSV文件"""
        try:
            df = pd.read_csv(file_path)
            
            # 尝试不同的列名
            pitch_columns = ['pitch', 'Pitch', 'PITCH', 'note', 'Note', 'midi_note']
            pitch_column = None
            
            for col in pitch_columns:
                if col in df.columns:
                    pitch_column = col
                    break
            
            if pitch_column:
                self.pitch_series = df[pitch_column].dropna()
                print(f"   从CSV文件提取了 {len(self.pitch_series)} 个音符")
                return True
            else:
                print(f"   ⚠️ CSV文件中未找到音高列")
                return False
                
        except Exception as e:
            print(f"   ❌ CSV文件处理失败: {e}")
            return False
    
    def analyze_single_work(self, file_path):
        """分析单个作品"""
        print(f"🎵 分析文件: {os.path.basename(file_path)}")
        
        # 如果是测试数据，直接使用已设置的pitch_series
        if file_path.startswith('test_') and self.pitch_series is not None:
            pitch_list = self.pitch_series.tolist()
        else:
            # 加载文件
            success = False
            if file_path.endswith('.mid') or file_path.endswith('.midi'):
                success = self.load_midi_file(file_path)
            elif file_path.endswith('.csv'):
                success = self.load_csv_file(file_path)
            
            if not success or self.pitch_series is None or len(self.pitch_series) < 3:
                print(f"   ❌ 文件加载失败或数据不足")
                return None
            
            pitch_list = self.pitch_series.tolist()
        
        # 1. 音程均幅分析
        intervallic_ambitus = self.intervallic_ambitus_analyzer.calculate(pitch_list)
        
        # 2. 局部波动性分析
        d1_rms = self.local_volatility_analyzer.calculate_d1_rms(pitch_list)
        d2_rms = self.local_volatility_analyzer.calculate_d2_rms(pitch_list)
        rms_ratio = self.local_volatility_analyzer.calculate_rms_ratio(d1_rms, d2_rms)
        volatility_type = self.local_volatility_analyzer.classify_volatility_type(rms_ratio)
        ornament_pattern = self.local_volatility_analyzer.identify_ornament_pattern(d1_rms, d2_rms, rms_ratio)
        
        # 3. 旋律动力系统分析
        dynamics_result = self.melody_dynamics_analyzer.analyze_dynamics(pitch_list)
        
        # 打印分析结果
        print(f"      音程均幅 (Intervallic_Ambitus): {intervallic_ambitus:.4f}")
        print(f"      局部音程波动性 (Local_Volatility): {d1_rms:.4f}")
        print(f"      中等尺度波动性 (d2_rms): {d2_rms:.4f}")
        print(f"      波动性特征比率: {rms_ratio:.4f}")
        print(f"      波动性类型: {volatility_type}")
        print(f"      装饰音模式: {ornament_pattern}")
        
        if 'error' not in dynamics_result:
            metrics = dynamics_result['dynamics_metrics']
            print(f"      动力系统类型: {dynamics_result['system_type']}")
            print(f"      系统稳定性: {dynamics_result['stability']}")
            print(f"      吸引子数量: {metrics['attractor_count']}")
            print(f"      系统能量: {metrics['system_energy']:.4f}")
            print(f"      平均曲率: {metrics['mean_curvature']:.4f}")
            print(f"      李雅普诺夫代理: {metrics['lyapunov_proxy']:.4f}")
        else:
            print(f"      动力系统分析: 失败 - {dynamics_result['error']}")
        
        print(f"   ✅ 分析完成")
        
        return {
            'file_path': file_path,
            'note_count': len(pitch_list),
            'intervallic_ambitus': intervallic_ambitus,
            'local_volatility': d1_rms,
            'local_volatility_details': {
                'd1_rms': d1_rms,
                'd2_rms': d2_rms,
                'rms_ratio': rms_ratio,
                'volatility_type': volatility_type,
                'ornament_pattern': ornament_pattern
            },
            'dynamics_analysis': dynamics_result
        }
    
    def analyze_all_works(self):
        """分析所有作品"""
        print("🎼 增强版中国音乐分析系统")
        print("=" * 80)
        
        # 查找音乐文件 - 支持多个目录
        music_files = []
        search_directories = [
            '.',  # 当前目录
            './midi_files',  # midi_files文件夹
            './music',  # music文件夹
            './data',  # data文件夹
            './songs',  # songs文件夹
        ]
        
        search_patterns = ['*.mid', '*.midi', '*.csv']
        
        print("🔍 搜索音乐文件...")
        for directory in search_directories:
            if os.path.exists(directory):
                print(f"   检查目录: {directory}")
                for pattern in search_patterns:
                    search_path = os.path.join(directory, pattern)
                    files = glob.glob(search_path)
                    if files:
                        print(f"     找到 {len(files)} 个 {pattern} 文件")
                        music_files.extend(files)
        
        # 过滤出MIDI文件
        midi_files = [f for f in music_files if f.endswith(('.mid', '.midi'))]
        
        if not midi_files:
            print("\n❌ 未找到MIDI文件")
            print("💡 请将MIDI文件放在以下任一目录中:")
            print("   - 当前目录 (/Users/<USER>/Desktop/AI音乐/)")
            print("   - ./midi_files/ 文件夹")
            print("   - ./music/ 文件夹")
            print("   - ./data/ 文件夹")
            print("   - ./songs/ 文件夹")
            print("\n🧪 现在使用测试数据进行演示:")
            self.run_test_analysis()
            return
        
        print(f"📁 找到 {len(music_files)} 个音乐文件")
        
        # 分析每个文件
        per_work_results = []
        for file_path in music_files:
            result = self.analyze_single_work(file_path)
            if result:
                per_work_results.append(result)
        
        if per_work_results:
            print(f"\n📊 成功分析了 {len(per_work_results)} 个作品")
            self.print_summary_statistics(per_work_results)
        else:
            print("❌ 没有成功分析的作品")
    
    def run_test_analysis(self):
        """运行测试分析"""
        print("�� 运行测试分析")
        
        test_melodies = [
            {
                'name': '平滑音阶',
                'pitches': [60, 62, 64, 65, 67, 69, 71, 72, 74, 76, 77, 79, 81, 83, 84]
            },
            {
                'name': '跳跃旋律',
                'pitches': [60, 72, 48, 75, 45, 78, 42, 81, 39, 84, 36, 87, 33, 90, 30]
            },
            {
                'name': '三音组模式',
                'pitches': [60, 62, 61, 63, 62, 64, 63, 65, 64, 66, 65, 67, 66, 68, 67]
            },
            {
                'name': '吸引子主导',
                'pitches': [60, 61, 60, 62, 60, 61, 60, 63, 60, 61, 60, 62, 60, 64, 60]
            },
            {
                'name': '混沌模式',
                'pitches': [60, 67, 55, 72, 48, 69, 52, 74, 46, 71, 49, 76, 44, 73, 51]
            }
        ]
        
        per_work_results = []
        for melody in test_melodies:
            print(f"\n🎵 分析测试旋律: {melody['name']}")
            self.pitch_series = pd.Series(melody['pitches'])
            
            result = self.analyze_single_work(f"test_{melody['name']}.mid")
            if result:
                per_work_results.append(result)
        
        if per_work_results:
            print(f"\n📊 使用 {len(per_work_results)} 个测试样本进行分析")
            self.print_summary_statistics(per_work_results)
    
    def print_summary_statistics(self, per_work_results):
        """打印摘要统计"""
        print("\n" + "="*60)
        print("🎼 平滑度收敛分析摘要报告")
        print("="*60)
        
        # 提取数据
        intervallic_ambitus_values = [r['intervallic_ambitus'] for r in per_work_results]
        local_volatility_values = [r['local_volatility'] for r in per_work_results]
        
        # 双方法正交性分析
        orthogonality = self.dual_method_analyzer.calculate_orthogonality(
            intervallic_ambitus_values, local_volatility_values
        )
        
        print(f"📊 基础统计:")
        print(f"   分析作品数: {len(per_work_results)} 首")
        print(f"   双方法正交性: {orthogonality['assessment']}")
        
        print(f"\n📈 音程均幅 (Intervallic_Ambitus) 统计:")
        print(f"   均值: {np.mean(intervallic_ambitus_values):.4f}")
        print(f"   标准差: {np.std(intervallic_ambitus_values):.4f}")
        print(f"   范围: {np.min(intervallic_ambitus_values):.4f} ~ {np.max(intervallic_ambitus_values):.4f}")
        print(f"   中位数: {np.median(intervallic_ambitus_values):.4f}")
        
        print(f"\n📈 局部音程波动性 (Local_Volatility) 统计:")
        print(f"   均值: {np.mean(local_volatility_values):.4f}")
        print(f"   标准差: {np.std(local_volatility_values):.4f}")
        print(f"   范围: {np.min(local_volatility_values):.4f} ~ {np.max(local_volatility_values):.4f}")
        print(f"   中位数: {np.median(local_volatility_values):.4f}")
        
        # 动力系统统计
        system_energies = []
        system_types = []
        stabilities = []
        
        for r in per_work_results:
            dynamics = r.get('dynamics_analysis', {})
            if 'error' not in dynamics:
                metrics = dynamics.get('dynamics_metrics', {})
                system_energies.append(metrics.get('system_energy', 0.0))
                system_types.append(dynamics.get('system_type', '未知'))
                stabilities.append(dynamics.get('stability', '未知'))
        
        if system_energies:
            print(f"\n🌊 旋律动力系统统计:")
            print(f"   平均系统能量: {np.mean(system_energies):.4f}")
            print(f"   系统能量范围: {np.min(system_energies):.4f} ~ {np.max(system_energies):.4f}")
            
            # 系统类型分布
            type_counts = {}
            for sys_type in system_types:
                type_counts[sys_type] = type_counts.get(sys_type, 0) + 1
            
            print(f"   系统类型分布:")
            for sys_type, count in type_counts.items():
                percentage = (count / len(system_types)) * 100
                print(f"     {sys_type}: {count} 首 ({percentage:.1f}%)")
            
            # 稳定性分布
            stability_counts = {}
            for stability in stabilities:
                stability_counts[stability] = stability_counts.get(stability, 0) + 1
            
            print(f"   稳定性分布:")
            for stability, count in stability_counts.items():
                percentage = (count / len(stabilities)) * 100
                print(f"     {stability}: {count} 首 ({percentage:.1f}%)")
        
        print(f"\n✅ 核心结论:")
        print(f"   ✅ 双方法分析: {orthogonality['assessment']}")
        print(f"   ✅ 相关系数: {orthogonality['correlation']:.4f}")
        
        if abs(orthogonality['correlation']) < 0.3:
            print(f"   ✅ 两个方法家族高度正交")
        elif abs(orthogonality['correlation']) < 0.6:
            print(f"   ⚠️ 两个方法家族中度相关")
        else:
            print(f"   ⚠️ 两个方法家族强相关，需要进一步分析")
        
        print(f"\n🌊 动力系统洞察:")
        if system_types:
            dominant_type = max(type_counts, key=type_counts.get)
            dominant_percentage = (type_counts[dominant_type] / len(system_types)) * 100
            print(f"   主导系统类型: {dominant_type} ({dominant_percentage:.1f}%)")
            
            stable_count = sum(count for stability, count in stability_counts.items() 
                             if '稳定' in stability and '不稳定' not in stability)
            stable_percentage = (stable_count / len(stabilities)) * 100
            print(f"   稳定系统比例: {stable_percentage:.1f}%")
        
        print(f"\n🎯 理论意义:")
        print(f"  • 音程均幅 (Intervallic_Ambitus): 测量音程大小的对数平均")
        print(f"  • 局部波动性 (Local_Volatility): 测量绝对不规则性（RMS）")
        print(f"  • 旋律动力系统: 基于动力系统理论的稳定性分析")
        print(f"  • 为'三音组构成中国传统音乐风格'提供数学支撑")
        
        print(f"\n🎼 增强版数学特征验证完成！")
        print(f"✅ 音程均幅 (Intervallic_Ambitus) 分析")
        print(f"✅ 局部音程波动性 (Local_Volatility) 分析") 
        print(f"✅ 双方法家族正交性验证")
        print(f"✅ 旋律动力系统分析")
        print(f"下一步：基于动力系统理论的深度特征挖掘")


def main():
    """主函数"""
    try:
        analyzer = EnhancedChineseMusicAnalyzer()
        analyzer.analyze_all_works()
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        print("🧪 建议检查文件路径或使用测试数据")


if __name__ == "__main__":
    main()


# ===== 第二步：三音组核心地位分析框架 =====

class StrictTriadSignificanceAnalyzer:
    """严格三音组核心地位分析器"""
    
    def __init__(self):
        self.metrics = {
            'structural_density': 0.0,      # 结构密度（占旋律比例）
            'contour_control': 0.0,         # 轮廓控制力
            'dynamic_contribution': 0.0,    # 动态贡献度
            'stability_index': 0.0,         # 稳定性指数
            'cultural_signature': None      # 文化特征标记
        }
    
    def analyze_significance(self, pitch_series):
        """分析严格三音组的核心地位"""
        # 步骤1：识别所有严格三音组
        strict_groups = self._identify_strict_triads(pitch_series)
        
        if not strict_groups:
            return self.metrics
        
        # 步骤2：计算核心指标
        total_groups = len(pitch_series) - 2
        self.metrics['structural_density'] = len(strict_groups) / total_groups
        
        # 轮廓控制力：严格三音组位置与旋律转折点的匹配度
        contour_turning_points = self._find_contour_turning_points(pitch_series)
        matches = sum(1 for group in strict_groups if group['position'] in contour_turning_points)
        self.metrics['contour_control'] = matches / len(strict_groups) if strict_groups else 0.0
        
        # 动态贡献度：严格三音组对整体动态范围的贡献比例
        total_dynamic = max(pitch_series) - min(pitch_series)
        if total_dynamic > 0:
            triad_dynamic = sum(group['dynamic_range'] for group in strict_groups)
            self.metrics['dynamic_contribution'] = triad_dynamic / (total_dynamic * len(strict_groups))
        else:
            self.metrics['dynamic_contribution'] = 0.0
        
        # 稳定性指数：基于李雅普诺夫指数的稳定性评估
        self.metrics['stability_index'] = self._calculate_stability_index(strict_groups, pitch_series)
        
        # 文化特征标记：识别中国传统音乐特有模式
        self.metrics['cultural_signature'] = self._identify_cultural_signature(strict_groups)
        
        return self.metrics
    
    def _identify_strict_triads(self, pitch_series):
        """识别严格定义的三音组（方向改变）"""
        strict_groups = []
        for i in range(len(pitch_series)-2):
            p1, p2, p3 = pitch_series[i], pitch_series[i+1], pitch_series[i+2]
            i1 = p2 - p1
            i2 = p3 - p2
            
            if i1 == 0 or i2 == 0:
                continue
                
            if (i1 > 0) != (i2 > 0):  # 方向改变
                dynamic_range = max(p1, p2, p3) - min(p1, p2, p3)
                strict_groups.append({
                    'position': i,
                    'notes': [p1, p2, p3],
                    'intervals': [i1, i2],
                    'dynamic_range': dynamic_range
                })
        return strict_groups
    
    def _find_contour_turning_points(self, pitch_series):
        """识别旋律轮廓转折点"""
        turning_points = []
        for i in range(1, len(pitch_series)-1):
            prev_diff = pitch_series[i] - pitch_series[i-1]
            next_diff = pitch_series[i+1] - pitch_series[i]
            if prev_diff * next_diff < 0:  # 方向改变点
                turning_points.append(i)
        return turning_points
    
    def _calculate_stability_index(self, strict_groups, pitch_series):
        """计算三音组稳定性指数"""
        if not strict_groups:
            return 0.0
        
        # 吸引子位置（旋律中心）
        attractor = np.median(pitch_series)
        
        # 计算平均距离和波动性
        distances = []
        for group in strict_groups:
            centroid = np.mean(group['notes'])
            distances.append(abs(centroid - attractor))
        
        avg_distance = np.mean(distances)
        distance_variance = np.var(distances)
        
        # 稳定性指数公式（距离越小、方差越小越稳定）
        return 1 / (1 + avg_distance + distance_variance)
    
    def _identify_cultural_signature(self, strict_groups):
        """识别中国传统音乐特征模式"""
        if not strict_groups:
            return ["无三音组"]
        
        # 特征1：山谷型模式（先下后上）占比
        valley_patterns = sum(1 for g in strict_groups if g['intervals'][0] < 0 and g['intervals'][1] > 0)
        valley_ratio = valley_patterns / len(strict_groups)
        
        # 特征2：小波动模式（动态范围<3半音）
        small_variation = sum(1 for g in strict_groups if g['dynamic_range'] < 3)
        small_var_ratio = small_variation / len(strict_groups)
        
        # 特征3：对称性模式（|i1|≈|i2|）
        symmetric = sum(1 for g in strict_groups if abs(abs(g['intervals'][0]) - abs(g['intervals'][1])) < 1)
        sym_ratio = symmetric / len(strict_groups)
        
        # 综合文化特征标记
        signature = []
        if valley_ratio > 0.6: signature.append("山谷型主导")
        if small_var_ratio > 0.7: signature.append("微幅波动")
        if sym_ratio > 0.5: signature.append("对称运动")
        
        return signature if signature else ["无显著文化特征"]


class TriadPositionVisualizer:
    """三音组地位可视化器"""
    
    def visualize_significance(self, metrics, save_path=None):
        """可视化三音组地位分析结果"""
        if not VISUALIZATION_AVAILABLE:
            print("⚠️ 可视化库未安装，跳过图表生成")
            return
        
        try:
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # 雷达图数据准备
            categories = ['结构密度', '轮廓控制', '动态贡献', '稳定性']
            values = [
                metrics['structural_density'],
                metrics['contour_control'],
                metrics['dynamic_contribution'],
                metrics['stability_index']
            ]
            
            # 雷达图绘制
            angles = np.linspace(0, 2*np.pi, len(categories), endpoint=False).tolist()
            values += values[:1]  # 闭合图形
            angles += angles[:1]
            
            ax = fig.add_subplot(111, polar=True)
            ax.plot(angles, values, linewidth=2, linestyle='solid', label='三音组地位')
            ax.fill(angles, values, alpha=0.25)
            
            # 设置标签
            ax.set_thetagrids(np.degrees(angles[:-1]), categories)
            ax.set_rlabel_position(30)
            
            # 文化特征标注
            sig_text = "\n".join(metrics['cultural_signature'])
            ax.annotate(f"文化特征:\n{sig_text}", 
                       xy=(0.5, 0.5), xycoords='axes fraction',
                       ha='center', va='center', fontsize=12,
                       bbox=dict(boxstyle="round,pad=0.3", fc="yellow", alpha=0.2))
            
            plt.title('严格三音组在中国传统音乐中的地位', fontsize=14)
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path)
                print(f"✅ 雷达图已保存至: {save_path}")
            else:
                plt.show()
                
        except Exception as e:
            print(f"⚠️ 可视化生成失败: {e}")
    
    def generate_interpretation_report(self, metrics):
        """生成三音组地位解释报告"""
        report = "=== 严格三音组在中国传统音乐中的地位分析 ===\n\n"
        
        # 结构重要性
        density = metrics['structural_density']
        report += f"1. 结构重要性: 严格三音组占旋律结构的{density*100:.1f}%，"
        if density > 0.35:
            report += "表明它是旋律构建的核心元素。\n"
        elif density > 0.2:
            report += "表明它是重要的装饰性元素。\n"
        else:
            report += "表明它是辅助性元素。\n"
        
        # 轮廓控制
        control = metrics['contour_control']
        report += f"2. 轮廓控制力: {control*100:.1f}%的旋律转折点由严格三音组驱动，"
        report += "说明它主导着旋律的流动方向。\n"
        
        # 动态贡献
        dynamic = metrics['dynamic_contribution']
        report += f"3. 动态贡献: 三音组贡献了整体动态变化的{dynamic*100:.1f}%，"
        if dynamic > 0.7:
            report += "是旋律张力的主要来源。\n"
        else:
            report += "与其他元素共同塑造动态变化。\n"
        
        # 稳定性
        stability = metrics['stability_index']
        report += f"4. 稳定性: 指数为{stability:.2f}（0-1），"
        if stability > 0.7:
            report += "表明三音组主要围绕稳定中心运动。\n"
        else:
            report += "表明三音组常引发旋律偏离。\n"
        
        # 文化特征
        report += "5. 文化特征: "
        if metrics['cultural_signature']:
            report += "显著表现出" + "、".join(metrics['cultural_signature']) + "。\n"
        else:
            report += "无明显文化特异性。\n"
        
        # 本质角色
        report += "\n=== 核心结论 ===\n"
        report += self._determine_role(metrics)
        
        return report
    
    def _determine_role(self, metrics):
        """确定三音组的本质角色"""
        if metrics['structural_density'] > 0.35 and metrics['contour_control'] > 0.6:
            return "严格三音组是中国传统旋律的核心结构元素与轮廓塑造者。它作为旋律的'关节'，连接音乐段落并主导流动方向。"
        elif metrics['stability_index'] > 0.7 and '微幅波动' in metrics['cultural_signature']:
            return "严格三音组是旋律稳定性的维持者。通过微幅波动，它在保持整体稳定性的同时提供必要的动态变化。"
        elif metrics['dynamic_contribution'] > 0.7:
            return "严格三音组是动态变化的引擎，为旋律提供主要的张力和动力。"
        else:
            return "严格三音组在旋律中扮演重要辅助角色，但不是主导元素。"


def analyze_chinese_melody_triads(pitch_series, output_dir="triad_analysis_results"):
    """分析中国传统音乐旋律中的三音组地位"""
    # 1. 创建分析器
    analyzer = StrictTriadSignificanceAnalyzer()
    visualizer = TriadPositionVisualizer()
    
    # 2. 执行分析
    results = analyzer.analyze_significance(pitch_series)
    
    # 3. 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 4. 保存可视化结果
    plot_path = os.path.join(output_dir, "triad_significance_radar.png")
    visualizer.visualize_significance(results, save_path=plot_path)
    
    # 5. 生成解释报告
    report = visualizer.generate_interpretation_report(results)
    report_path = os.path.join(output_dir, "triad_analysis_report.txt")
    with open(report_path, "w", encoding="utf-8") as f:
        f.write(report)
    
    # 6. 打印核心结论
    print("\n=== 三音组地位分析完成 ===")
    print(f"可视化结果已保存至: {plot_path}")
    print(f"分析报告已保存至: {report_path}")
    print("\n核心结论:")
    print(visualizer._determine_role(results))
    
    return {
        'metrics': results,
        'plot_path': plot_path,
        'report_path': report_path
    }



# ===== 更新主分析器以集成三音组地位分析 =====

class EnhancedChineseMusicAnalyzerWithTriads(EnhancedChineseMusicAnalyzer):
    """增强版中国音乐分析器（包含三音组地位分析）"""
    
    def __init__(self):
        super().__init__()
        self.triad_analyzer = StrictTriadSignificanceAnalyzer()
        self.triad_visualizer = TriadPositionVisualizer()
    
    def analyze_single_work_with_triads(self, file_path):
        """分析单个作品（包含三音组地位分析）"""
        print(f"🎵 分析文件: {os.path.basename(file_path)}")
        
        # 如果是测试数据，直接使用已设置的pitch_series
        if file_path.startswith('test_') and self.pitch_series is not None:
            pitch_list = self.pitch_series.tolist()
        else:
            # 加载文件
            success = False
            if file_path.endswith('.mid') or file_path.endswith('.midi'):
                success = self.load_midi_file(file_path)
            elif file_path.endswith('.csv'):
                success = self.load_csv_file(file_path)
            
            if not success or self.pitch_series is None or len(self.pitch_series) < 3:
                print(f"   ❌ 文件加载失败或数据不足")
                return None
            
            pitch_list = self.pitch_series.tolist()
        
        # 1. 音程均幅分析
        intervallic_ambitus = self.intervallic_ambitus_analyzer.calculate(pitch_list)
        
        # 2. 局部波动性分析
        d1_rms = self.local_volatility_analyzer.calculate_d1_rms(pitch_list)
        d2_rms = self.local_volatility_analyzer.calculate_d2_rms(pitch_list)
        rms_ratio = self.local_volatility_analyzer.calculate_rms_ratio(d1_rms, d2_rms)
        volatility_type = self.local_volatility_analyzer.classify_volatility_type(rms_ratio)
        ornament_pattern = self.local_volatility_analyzer.identify_ornament_pattern(d1_rms, d2_rms, rms_ratio)
        
        # 3. 旋律动力系统分析
        dynamics_result = self.melody_dynamics_analyzer.analyze_dynamics(pitch_list)
        
        # 4. 三音组核心地位分析
        triad_significance = self.triad_analyzer.analyze_significance(pitch_list)
        
        # 打印分析结果
        print(f"      音程均幅 (Intervallic_Ambitus): {intervallic_ambitus:.4f}")
        print(f"      局部音程波动性 (Local_Volatility): {d1_rms:.4f}")
        print(f"      中等尺度波动性 (d2_rms): {d2_rms:.4f}")
        print(f"      波动性特征比率: {rms_ratio:.4f}")
        print(f"      波动性类型: {volatility_type}")
        print(f"      装饰音模式: {ornament_pattern}")
        
        if 'error' not in dynamics_result:
            metrics = dynamics_result['dynamics_metrics']
            print(f"      动力系统类型: {dynamics_result['system_type']}")
            print(f"      系统稳定性: {dynamics_result['stability']}")
            print(f"      吸引子数量: {metrics['attractor_count']}")
            print(f"      系统能量: {metrics['system_energy']:.4f}")
            print(f"      平均曲率: {metrics['mean_curvature']:.4f}")
            print(f"      李雅普诺夫代理: {metrics['lyapunov_proxy']:.4f}")
        else:
            print(f"      动力系统分析: 失败 - {dynamics_result['error']}")
        
        # 打印三音组地位分析结果
        print(f"      🎯 三音组地位分析:")
        print(f"        结构密度: {triad_significance['structural_density']:.3f}")
        print(f"        轮廓控制力: {triad_significance['contour_control']:.3f}")
        print(f"        动态贡献度: {triad_significance['dynamic_contribution']:.3f}")
        print(f"        稳定性指数: {triad_significance['stability_index']:.3f}")
        print(f"        文化特征: {', '.join(triad_significance['cultural_signature'])}")
        
        print(f"   ✅ 分析完成")
        
        return {
            'file_path': file_path,
            'note_count': len(pitch_list),
            'intervallic_ambitus': intervallic_ambitus,
            'local_volatility': d1_rms,
            'local_volatility_details': {
                'd1_rms': d1_rms,
                'd2_rms': d2_rms,
                'rms_ratio': rms_ratio,
                'volatility_type': volatility_type,
                'ornament_pattern': ornament_pattern
            },
            'dynamics_analysis': dynamics_result,
            'triad_significance': triad_significance
        }
    
    def analyze_all_works_with_triads(self):
        """分析所有作品（包含三音组地位分析）"""
        print("🎼 增强版中国音乐分析系统（包含三音组地位分析）")
        print("=" * 80)
        
        # 查找音乐文件 - 支持多个目录
        music_files = []
        search_directories = [
            '.',  # 当前目录
            './midi_files',  # midi_files文件夹
            './music',  # music文件夹
            './data',  # data文件夹
            './songs',  # songs文件夹
        ]
        
        search_patterns = ['*.mid', '*.midi', '*.csv']
        
        print("�� 搜索音乐文件...")
        for directory in search_directories:
            if os.path.exists(directory):
                print(f"   检查目录: {directory}")
                for pattern in search_patterns:
                    search_path = os.path.join(directory, pattern)
                    files = glob.glob(search_path)
                    if files:
                        print(f"     找到 {len(files)} 个 {pattern} 文件")
                        music_files.extend(files)
        
        # 过滤出MIDI文件
        midi_files = [f for f in music_files if f.endswith(('.mid', '.midi'))]
        
        if not midi_files:
            print("\n❌ 未找到MIDI文件")
            print("💡 请将MIDI文件放在以下任一目录中:")
            print("   - 当前目录 (/Users/<USER>/Desktop/AI音乐/)")
            print("   - ./midi_files/ 文件夹")
            print("   - ./music/ 文件夹")
            print("   - ./data/ 文件夹")
            print("   - ./songs/ 文件夹")
            print("\n🧪 现在使用测试数据进行演示:")
            self.run_test_analysis_with_triads()
            return
        
        print(f"📁 找到 {len(midi_files)} 个音乐文件")
        
        # 分析每个文件
        per_work_results = []
        for file_path in midi_files:
            result = self.analyze_single_work_with_triads(file_path)
            if result:
                per_work_results.append(result)
        
        if per_work_results:
            print(f"\n📊 成功分析了 {len(per_work_results)} 个作品")
            self.print_summary_statistics_with_triads(per_work_results)
        else:
            print("❌ 没有成功分析的作品")
    
    def run_test_analysis_with_triads(self):
        """运行测试分析（包含三音组地位分析）"""
        print("🧪 运行测试分析（包含三音组地位分析）")
        
        test_melodies = [
            {
                'name': '平滑音阶',
                'pitches': [60, 62, 64, 65, 67, 69, 71, 72, 74, 76, 77, 79, 81, 83, 84]
            },
            {
                'name': '跳跃旋律',
                'pitches': [60, 72, 48, 75, 45, 78, 42, 81, 39, 84, 36, 87, 33, 90, 30]
            },
            {
                'name': '三音组模式',
                'pitches': [60, 62, 61, 63, 62, 64, 63, 65, 64, 66, 65, 67, 66, 68, 67]
            },
            {
                'name': '吸引子主导',
                'pitches': [60, 61, 60, 62, 60, 61, 60, 63, 60, 61, 60, 62, 60, 64, 60]
            },
            {
                'name': '混沌模式',
                'pitches': [60, 67, 55, 72, 48, 69, 52, 74, 46, 71, 49, 76, 44, 73, 51]
            }
        ]
        
        per_work_results = []
        for melody in test_melodies:
            print(f"\n🎵 分析测试旋律: {melody['name']}")
            self.pitch_series = pd.Series(melody['pitches'])
            
            result = self.analyze_single_work_with_triads(f"test_{melody['name']}.mid")
            if result:
                per_work_results.append(result)
        
        if per_work_results:
            print(f"\n📊 使用 {len(per_work_results)} 个测试样本进行分析")
            self.print_summary_statistics_with_triads(per_work_results)
    
    def print_summary_statistics_with_triads(self, per_work_results):
        """打印摘要统计（包含三音组地位分析）"""
        print("\n" + "="*60)
        print("🎼 平滑度收敛分析摘要报告（包含三音组地位分析）")
        print("="*60)
        
        # 提取数据
        intervallic_ambitus_values = [r['intervallic_ambitus'] for r in per_work_results]
        local_volatility_values = [r['local_volatility'] for r in per_work_results]
        
        # 双方法正交性分析
        orthogonality = self.dual_method_analyzer.calculate_orthogonality(
            intervallic_ambitus_values, local_volatility_values
        )
        
        print(f"📊 基础统计:")
        print(f"   分析作品数: {len(per_work_results)} 首")
        print(f"   双方法正交性: {orthogonality['assessment']}")
        
        print(f"\n📈 音程均幅 (Intervallic_Ambitus) 统计:")
        print(f"   均值: {np.mean(intervallic_ambitus_values):.4f}")
        print(f"   标准差: {np.std(intervallic_ambitus_values):.4f}")
        print(f"   范围: {np.min(intervallic_ambitus_values):.4f} ~ {np.max(intervallic_ambitus_values):.4f}")
        print(f"   中位数: {np.median(intervallic_ambitus_values):.4f}")
        
        print(f"\n📈 局部音程波动性 (Local_Volatility) 统计:")
        print(f"   均值: {np.mean(local_volatility_values):.4f}")
        print(f"   标准差: {np.std(local_volatility_values):.4f}")
        print(f"   范围: {np.min(local_volatility_values):.4f} ~ {np.max(local_volatility_values):.4f}")
        print(f"   中位数: {np.median(local_volatility_values):.4f}")
        
        # 动力系统统计
        system_energies = []
        system_types = []
        stabilities = []
        
        for r in per_work_results:
            dynamics = r.get('dynamics_analysis', {})
            if 'error' not in dynamics:
                metrics = dynamics.get('dynamics_metrics', {})
                system_energies.append(metrics.get('system_energy', 0.0))
                system_types.append(dynamics.get('system_type', '未知'))
                stabilities.append(dynamics.get('stability', '未知'))
        
        if system_energies:
            print(f"\n🌊 旋律动力系统统计:")
            print(f"   平均系统能量: {np.mean(system_energies):.4f}")
            print(f"   系统能量范围: {np.min(system_energies):.4f} ~ {np.max(system_energies):.4f}")
            
            # 系统类型分布
            type_counts = {}
            for sys_type in system_types:
                type_counts[sys_type] = type_counts.get(sys_type, 0) + 1
            
            print(f"   系统类型分布:")
            for sys_type, count in type_counts.items():
                percentage = (count / len(system_types)) * 100
                print(f"     {sys_type}: {count} 首 ({percentage:.1f}%)")
            
            # 稳定性分布
            stability_counts = {}
            for stability in stabilities:
                stability_counts[stability] = stability_counts.get(stability, 0) + 1
            
            print(f"   稳定性分布:")
            for stability, count in stability_counts.items():
                percentage = (count / len(stabilities)) * 100
                print(f"     {stability}: {count} 首 ({percentage:.1f}%)")
        
        # 三音组地位统计
        triad_densities = []
        triad_controls = []
        triad_dynamics = []
        triad_stabilities = []
        cultural_signatures = []
        
        for r in per_work_results:
            triad = r.get('triad_significance', {})
            triad_densities.append(triad.get('structural_density', 0.0))
            triad_controls.append(triad.get('contour_control', 0.0))
            triad_dynamics.append(triad.get('dynamic_contribution', 0.0))
            triad_stabilities.append(triad.get('stability_index', 0.0))
            cultural_signatures.extend(triad.get('cultural_signature', []))
        
        if triad_densities:
            print(f"\n🎯 严格三音组地位统计:")
            print(f"   结构密度:")
            print(f"     均值: {np.mean(triad_densities):.3f}")
            print(f"     范围: {np.min(triad_densities):.3f} ~ {np.max(triad_densities):.3f}")
            
            print(f"   轮廓控制力:")
            print(f"     均值: {np.mean(triad_controls):.3f}")
            print(f"     范围: {np.min(triad_controls):.3f} ~ {np.max(triad_controls):.3f}")
            
            print(f"   动态贡献度:")
            print(f"     均值: {np.mean(triad_dynamics):.3f}")
            print(f"     范围: {np.min(triad_dynamics):.3f} ~ {np.max(triad_dynamics):.3f}")
            
            print(f"   稳定性指数:")
            print(f"     均值: {np.mean(triad_stabilities):.3f}")
            print(f"     范围: {np.min(triad_stabilities):.3f} ~ {np.max(triad_stabilities):.3f}")
            
            # 文化特征分布
            signature_counts = {}
            for sig in cultural_signatures:
                signature_counts[sig] = signature_counts.get(sig, 0) + 1
            
            print(f"   文化特征分布:")
            for sig, count in signature_counts.items():
                percentage = (count / len(per_work_results)) * 100
                print(f"     {sig}: {count} 首 ({percentage:.1f}%)")
        
        print(f"\n✅ 核心结论:")
        print(f"   ✅ 双方法分析: {orthogonality['assessment']}")
        print(f"   ✅ 相关系数: {orthogonality['correlation']:.4f}")
        
        if abs(orthogonality['correlation']) < 0.3:
            print(f"   ✅ 两个方法家族高度正交")
        elif abs(orthogonality['correlation']) < 0.6:
            print(f"   ⚠️ 两个方法家族中度相关")
        else:
            print(f"   ⚠️ 两个方法家族强相关，需要进一步分析")
        
        print(f"\n🌊 动力系统洞察:")
        if system_types:
            dominant_type = max(type_counts, key=type_counts.get)
            dominant_percentage = (type_counts[dominant_type] / len(system_types)) * 100
            print(f"   主导系统类型: {dominant_type} ({dominant_percentage:.1f}%)")
            
            stable_count = sum(count for stability, count in stability_counts.items() 
                             if '稳定' in stability and '不稳定' not in stability)
            stable_percentage = (stable_count / len(stabilities)) * 100
            print(f"   稳定系统比例: {stable_percentage:.1f}%")
        
        # 三音组地位洞察
        if triad_densities:
            avg_density = np.mean(triad_densities)
            avg_control = np.mean(triad_controls)
            print(f"\n🎯 三音组地位洞察:")
            print(f"   平均结构密度: {avg_density:.3f}")
            print(f"   平均轮廓控制力: {avg_control:.3f}")
            
            if avg_density > 0.35 and avg_control > 0.6:
                print(f"   ✅ 严格三音组是中国传统旋律的核心结构元素")
            elif avg_density > 0.2:
                print(f"   ⚠️ 严格三音组是重要的装饰性元素")
            else:
                print(f"   ℹ️ 严格三音组是辅助性元素")
        
        print(f"\n🎯 理论意义:")
        print(f"  • 音程均幅 (Intervallic_Ambitus): 测量音程大小的对数平均")
        print(f"  • 局部波动性 (Local_Volatility): 测量绝对不规则性（RMS）")
        print(f"  • 旋律动力系统: 基于动力系统理论的稳定性分析")
        print(f"  • 严格三音组地位: 量化三音组在中国传统音乐中的核心作用")
        print(f"  • 为'三音组构成中国传统音乐风格'提供数学支撑")
        
        print(f"\n🎼 增强版数学特征验证完成！")
        print(f"✅ 音程均幅 (Intervallic_Ambitus) 分析")
        print(f"✅ 局部音程波动性 (Local_Volatility) 分析") 
        print(f"✅ 双方法家族正交性验证")
        print(f"✅ 旋律动力系统分析")
        print(f"✅ 严格三音组核心地位分析")
        print(f"下一步：基于三音组理论的深度文化特征挖掘")


def main_with_triads():
    """主函数（包含三音组地位分析）"""
    try:
        analyzer = EnhancedChineseMusicAnalyzerWithTriads()
        analyzer.analyze_all_works_with_triads()
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        print("🧪 建议检查文件路径或使用测试数据")



# 更新主函数调用
if __name__ == "__main__":
    print("🎼 增强版中国音乐分析系统")
    print("选择分析模式:")
    print("1. 基础分析（音程均幅 + 局部波动性 + 动力系统）")
    print("2. 完整分析（包含严格三音组核心地位分析）")
    
    try:
        # 默认使用完整分析
        print("🚀 启动完整分析模式（包含三音组地位分析）")
        main_with_triads()
    except Exception as e:
        print(f"❌ 完整分析失败: {e}")
        print("🔄 回退到基础分析模式")
        try:
            analyzer = EnhancedChineseMusicAnalyzer()
            analyzer.analyze_all_works()
        except Exception as e2:
            print(f"❌ 基础分析也失败: {e2}")
            print("🧪 建议检查文件路径或依赖库")
