#!/usr/bin/env python3
"""
为unified_topological_analysis.py添加简单的进度条
"""

def add_progress_bar():
    """添加简单的进度条到批量分析方法"""
    
    # 读取原文件
    with open('unified_topological_analysis.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找并替换批量分析的循环部分
    old_pattern = '''        for i, (work_name, pitch_series) in enumerate(works_data, 1):
            # 简单的进度条
            progress = i / len(works_data)
            bar_length = 30
            filled_length = int(bar_length * progress)
            bar = '█' * filled_length + '░' * (bar_length - filled_length)
            
            print(f"\\n进度: [{bar}] {i}/{len(works_data)} ({progress*100:.1f}%)")
            print(f"正在分析: {work_name}")
            print("-" * 50)
            
            result = self.analyze_work(pitch_series, work_name)
            
            if result:
                all_results.append(result)
                successful_count += 1
                print(f"✅ 完成")
            else:
                print(f"❌ 失败")'''
    
    new_pattern = '''        for i, (work_name, pitch_series) in enumerate(works_data, 1):
            # 简单的进度条
            progress = i / len(works_data)
            bar_length = 30
            filled_length = int(bar_length * progress)
            bar = '█' * filled_length + '░' * (bar_length - filled_length)
            
            print(f"\\n进度: [{bar}] {i}/{len(works_data)} ({progress*100:.1f}%)")
            print(f"正在分析: {work_name}")
            print("-" * 50)
            
            result = self.analyze_work(pitch_series, work_name)
            
            if result:
                all_results.append(result)
                successful_count += 1
                print(f"✅ 完成")
            else:
                print(f"❌ 失败")'''
    
    # 替换内容
    if old_pattern in content:
        content = content.replace(old_pattern, new_pattern)
        print("✅ 进度条已存在")
    else:
        print("❌ 未找到目标代码段")
        return False
    
    # 移除所有复杂的时间监控
    # 移除step_start = time.time()
    content = content.replace('step_start = time.time()', '')
    content = content.replace('step_time = time.time() - step_start', '')
    content = content.replace('print(f"      ✅ 完成，耗时: {step_time:.2f}秒")', '')
    content = content.replace('print(f"      ⏱️ 拓扑特征计算耗时: {step_time:.2f}秒")', '')
    content = content.replace('print(f"      ⏱️ 拓扑澄清耗时: {step_time:.2f}秒")', '')
    content = content.replace('print(f"      ⏱️ 转调检测耗时: {step_time:.2f}秒")', '')
    content = content.replace('print(f"      ⏱️ 拓扑验证耗时: {step_time:.2f}秒")', '')
    
    # 移除复杂的时间统计
    content = content.replace('total_time = time.time() - start_time', '')
    content = content.replace('print(f"   ⏰ 总耗时: {total_time:.2f}秒 ({total_time/60:.1f}分钟)")', '')
    content = content.replace('print(f"   📈 性能统计: 平均每音符耗时 {total_time/len(pitch_series):.3f}秒")', '')
    
    # 移除步骤编号
    content = content.replace('[步骤1/8]', '')
    content = content.replace('[步骤2/8]', '')
    content = content.replace('[步骤3/8]', '')
    content = content.replace('[步骤4/8]', '')
    content = content.replace('[步骤5/8]', '')
    content = content.replace('[步骤6/8]', '')
    content = content.replace('[步骤7/8]', '')
    content = content.replace('[步骤8/8]', '')
    
    # 移除复杂的拓扑计算时间监控
    complex_monitoring = '''            print("      ⚠️ 警告：这是最耗时的步骤，可能需要较长时间...")

            # 构建整体音乐拓扑空间
            if len(triads) > 0:
                print("      🔧 构建三音组拓扑复形...")
                complex_start = time.time()
                # 使用第一个三音组构建示例复形
                sample_complex = self.topological_invariants._construct_triad_internal_topology(triads[0])
                complex_time = time.time() - complex_start
                print(f"      ⏱️ 复形构建耗时: {complex_time:.2f}秒")
                
                print("      🧮 计算欧拉特征数...")
                euler_start = time.time()
                euler_characteristic = self.topological_invariants.compute_euler_characteristic(sample_complex)
                euler_time = time.time() - euler_start
                print(f"      ⏱️ 欧拉特征数计算耗时: {euler_time:.2f}秒")
                
                print("      🔢 计算贝蒂数...")
                betti_start = time.time()
                betti_numbers = self.topological_invariants.compute_betti_numbers(sample_complex)
                betti_time = time.time() - betti_start
                print(f"      ⏱️ 贝蒂数计算耗时: {betti_time:.2f}秒")

                # 计算拓扑复杂度
                topological_complexity = euler_characteristic + sum(betti_numbers)
            else:
                euler_characteristic = 0
                betti_numbers = [0]
                topological_complexity = 0
            
            step_time = time.time() - step_start
            print(f"      ✅ 拓扑不变量计算完成，总耗时: {step_time:.2f}秒")'''
    
    simple_monitoring = '''            # 构建整体音乐拓扑空间
            if len(triads) > 0:
                # 使用第一个三音组构建示例复形
                sample_complex = self.topological_invariants._construct_triad_internal_topology(triads[0])
                euler_characteristic = self.topological_invariants.compute_euler_characteristic(sample_complex)
                betti_numbers = self.topological_invariants.compute_betti_numbers(sample_complex)

                # 计算拓扑复杂度
                topological_complexity = euler_characteristic + sum(betti_numbers)
            else:
                euler_characteristic = 0
                betti_numbers = [0]
                topological_complexity = 0'''
    
    content = content.replace(complex_monitoring, simple_monitoring)
    
    # 写回文件
    with open('unified_topological_analysis.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 简化完成！现在只有简单的进度条")
    return True

if __name__ == "__main__":
    print("🔧 添加简单进度条...")
    success = add_progress_bar()
    if success:
        print("✅ 完成！现在运行时会显示简单的进度条")
    else:
        print("❌ 失败！请检查文件")
