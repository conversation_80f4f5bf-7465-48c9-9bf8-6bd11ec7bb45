#!/usr/bin/env python3
"""
吸引子强度参数深度验证
检查极端变异性异常和分母效应问题
"""

import numpy as np
import sys
import json
from collections import Counter
sys.path.append('.')

def analyze_strength_variability():
    """分析吸引子强度的极端变异性"""
    print("🔍 吸引子强度极端变异性分析")
    print("检查CV=95.9%的异常变异和分母效应")
    print("="*80)
    
    # 报告的数据
    reported_data = {
        'mean': 0.2320,
        'std': 0.2224,
        'min': 0.0463,
        'max': 1.2332,
        'high_strength_count': 7,  # ≥0.4
        'medium_strength_count': 35,  # 0.1-0.4
        'low_strength_count': 8,  # <0.1
        'total_samples': 50
    }
    
    print("\n1. 📊 变异系数分析")
    print("-" * 60)
    
    cv = reported_data['std'] / reported_data['mean']
    print(f"   变异系数 (CV): {cv:.1%}")
    print(f"   CV解释:")
    print(f"     • CV < 15%: 低变异性（同质数据）")
    print(f"     • CV 15-35%: 中等变异性（可接受）")
    print(f"     • CV > 50%: 高变异性（异质数据）")
    print(f"     • CV > 100%: 极端异质性（可能有问题）")
    print(f"   ⚠️ 当前CV={cv:.1%}，属于极端异质性！")
    
    # 极值比分析
    max_min_ratio = reported_data['max'] / reported_data['min']
    max_mean_ratio = reported_data['max'] / reported_data['mean']
    
    print(f"\n   极值比分析:")
    print(f"     • 最大值/最小值: {max_min_ratio:.1f}倍")
    print(f"     • 最大值/均值: {max_mean_ratio:.1f}倍")
    print(f"   ⚠️ 最大值是最小值的{max_min_ratio:.0f}倍，是均值的{max_mean_ratio:.1f}倍！")

def analyze_denominator_effect():
    """分析分母效应问题"""
    print(f"\n2. 🧮 分母效应分析")
    print("-" * 60)
    
    print("   公式分析:")
    print("   改进吸引子强度 = (主导权重/吸引子数量) × 音高跨度 × 修正集中度指数")
    print("                    ↑")
    print("                 分母效应")
    
    print(f"\n   分母效应的问题:")
    print(f"     • 吸引子数量↑ → 强度↓ (数学必然性)")
    print(f"     • 这不是经验发现，而是公式人工制品")
    print(f"     • 可能导致伪相关")
    
    # 模拟分母效应
    print(f"\n   分母效应模拟:")
    base_numerator = 1.0  # 假设分子固定
    for k in range(3, 6):
        strength = base_numerator / k
        print(f"     • {k}个吸引子 → 强度 = {strength:.3f}")
    
    print(f"   ⚠️ 仅分母效应就能造成67%的强度差异！")

def validate_low_strength_samples():
    """验证低强度样本是否存在数据问题"""
    print(f"\n3. 🔍 低强度样本验证")
    print("-" * 60)
    
    print("   需要检查的问题:")
    print("     • 音高解析失败")
    print("     • 数据预处理错误")
    print("     • 空序列或单音")
    print("     • 数值计算异常")
    
    # 创建验证函数
    def check_data_integrity():
        """检查数据完整性"""
        try:
            from unified_topological_analysis import UnifiedTopologicalAnalyzer
            
            # 创建测试样本
            test_cases = {
                '正常样本': [60, 62, 64, 67, 69, 67, 64, 62, 60],
                '单音样本': [60, 60, 60, 60, 60],
                '空样本': [],
                '极短样本': [60, 62],
                '极长样本': list(range(48, 84)),
                '异常值样本': [0, 127, 0, 127, 0]
            }
            
            analyzer = UnifiedTopologicalAnalyzer()
            results = {}
            
            for name, pitches in test_cases.items():
                if len(pitches) == 0:
                    results[name] = {'error': '空序列'}
                    continue
                    
                try:
                    result = analyzer.analyze_work(pitches, name)
                    if result:
                        strength = result['topology_metrics'].get('improved_attractor_strength', 0)
                        attractor_count = result['attractor_landscape']['attractor_count']
                        results[name] = {
                            'strength': strength,
                            'attractor_count': attractor_count,
                            'status': 'success'
                        }
                    else:
                        results[name] = {'error': '分析失败'}
                except Exception as e:
                    results[name] = {'error': str(e)}
            
            return results
            
        except Exception as e:
            return {'error': f'验证失败: {e}'}
    
    print(f"\n   执行数据完整性检查...")
    integrity_results = check_data_integrity()
    
    for name, result in integrity_results.items():
        if 'error' in result:
            print(f"     ❌ {name}: {result['error']}")
        else:
            strength = result['strength']
            k = result['attractor_count']
            print(f"     ✅ {name}: 强度={strength:.4f}, 吸引子={k}个")
            
            if strength < 0.1:
                print(f"        ⚠️ 低强度样本！需要进一步检查")

def analyze_outlier_patterns():
    """分析离群点模式"""
    print(f"\n4. 📈 离群点模式分析")
    print("-" * 60)
    
    print("   需要验证的假设:")
    print("     H1: 高强度样本 ↔ 低吸引子数量 (分母效应)")
    print("     H2: 高强度样本 ↔ 高收敛比例 (特定作品类型)")
    print("     H3: 低强度样本 ↔ 数据质量问题")
    
    # 模拟分析
    print(f"\n   模拟分析结果:")
    
    # 假设的高强度样本特征
    high_strength_simulation = {
        'strength_range': (0.4, 1.2332),
        'expected_attractor_count': 3,  # 分母效应预测
        'expected_convergence': 'high',  # 如果是特定类型
        'sample_count': 7
    }
    
    # 假设的低强度样本特征
    low_strength_simulation = {
        'strength_range': (0.0463, 0.1),
        'potential_issues': ['数据解析错误', '极短序列', '单音重复'],
        'sample_count': 8
    }
    
    print(f"   高强度样本 (n={high_strength_simulation['sample_count']}):")
    print(f"     • 强度范围: {high_strength_simulation['strength_range']}")
    print(f"     • 预期吸引子数: {high_strength_simulation['expected_attractor_count']} (分母效应)")
    print(f"     • 预期收敛性: {high_strength_simulation['expected_convergence']}")
    
    print(f"\n   低强度样本 (n={low_strength_simulation['sample_count']}):")
    print(f"     • 强度范围: {low_strength_simulation['strength_range']}")
    print(f"     • 可能问题: {', '.join(low_strength_simulation['potential_issues'])}")

def recommend_corrections():
    """推荐修正方案"""
    print(f"\n5. 🔧 修正方案推荐")
    print("-" * 60)
    
    corrections = {
        '方案1：分母效应校正': {
            'problem': '吸引子数量作为分母导致人工相关',
            'solution': '使用标准化强度或移除分母',
            'formula': '标准化强度 = 原强度 × sqrt(吸引子数量)',
            'pros': '消除分母效应，保持相对关系',
            'cons': '需要重新解释物理意义'
        },
        
        '方案2：分层分析': {
            'problem': '数据异质性过高，不适合统一分析',
            'solution': '按吸引子数量分层分析',
            'formula': '分别计算3个、4个、5个吸引子的强度分布',
            'pros': '消除混杂因素，更精确',
            'cons': '样本量减少，复杂度增加'
        },
        
        '方案3：鲁棒统计': {
            'problem': '极端值影响均值和标准差',
            'solution': '使用中位数和四分位距',
            'formula': 'IQR = Q3 - Q1, 中位数±1.5×IQR',
            'pros': '对离群点不敏感',
            'cons': '信息损失，解释性降低'
        },
        
        '方案4：数据清洗': {
            'problem': '低强度样本可能是数据质量问题',
            'solution': '识别并处理异常样本',
            'formula': '基于数据完整性检查剔除异常',
            'pros': '提高数据质量',
            'cons': '可能引入选择偏差'
        }
    }
    
    for solution_name, details in corrections.items():
        print(f"\n   🛠️ {solution_name}:")
        print(f"      问题: {details['problem']}")
        print(f"      解决: {details['solution']}")
        print(f"      公式: {details['formula']}")
        print(f"      优点: {details['pros']}")
        print(f"      缺点: {details['cons']}")
    
    print(f"\n🎯 推荐组合方案:")
    print(f"   1. 首先执行数据清洗（方案4）")
    print(f"   2. 然后进行分母效应校正（方案1）")
    print(f"   3. 最后使用鲁棒统计报告（方案3）")
    print(f"   4. 补充分层分析验证（方案2）")

def generate_validation_report():
    """生成验证报告"""
    print(f"\n6. 📋 验证报告总结")
    print("-" * 60)
    
    findings = {
        '严重问题': [
            'CV=95.9%表明极端异质性',
            '分母效应导致人工相关',
            '最大值是最小值的26倍',
            '非正态分布(p=0.000)'
        ],
        '可能原因': [
            '数据预处理错误',
            '公式设计缺陷',
            '样本异质性过高',
            '离群点未处理'
        ],
        '立即行动': [
            '检查低强度样本数据质量',
            '验证高强度样本的吸引子数量',
            '实施分母效应校正',
            '使用鲁棒统计方法'
        ]
    }
    
    for category, items in findings.items():
        print(f"\n   📌 {category}:")
        for item in items:
            print(f"      • {item}")
    
    print(f"\n⚠️ 结论:")
    print(f"   当前的吸引子强度指标存在严重的方法论问题，")
    print(f"   需要立即进行数据验证和公式修正，")
    print(f"   否则可能导致错误的科学结论。")

if __name__ == "__main__":
    print("🔍 吸引子强度参数深度验证")
    print("检查极端变异性异常和方法论问题")
    
    # 1. 变异性分析
    analyze_strength_variability()
    
    # 2. 分母效应分析
    analyze_denominator_effect()
    
    # 3. 低强度样本验证
    validate_low_strength_samples()
    
    # 4. 离群点模式分析
    analyze_outlier_patterns()
    
    # 5. 修正方案
    recommend_corrections()
    
    # 6. 验证报告
    generate_validation_report()
    
    print(f"\n🎉 深度验证完成！")
    print(f"✅ 识别了关键的方法论问题")
    print(f"🔧 提供了具体的修正方案")
    print(f"⚠️ 强烈建议立即实施修正措施")
