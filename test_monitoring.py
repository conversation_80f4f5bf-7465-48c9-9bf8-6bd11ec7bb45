#!/usr/bin/env python3
"""
测试实时监控功能
验证unified_topological_analysis.py中的监控是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_monitoring():
    """测试监控功能"""
    
    print("🔍 测试实时运行监督功能")
    print("="*60)
    
    try:
        from unified_topological_analysis import UnifiedTopologicalAnalyzer
        print("✅ 成功导入UnifiedTopologicalAnalyzer")
        
        # 创建分析器
        analyzer = UnifiedTopologicalAnalyzer()
        print("✅ 成功创建分析器实例")
        
        # 测试单个样本分析的监控
        test_melody = [60, 62, 61, 63, 64, 63, 65, 66, 65, 67, 69, 67]
        print(f"\n🎵 测试单个样本监控:")
        print(f"   测试旋律: {test_melody}")
        print(f"   音符数量: {len(test_melody)}")
        
        print(f"\n⏰ 开始分析，观察实时监控...")
        print("="*60)
        
        result = analyzer.analyze_work(test_melody, "监控测试样本")
        
        if result:
            print(f"\n✅ 监控测试成功！")
            print(f"   可以看到详细的步骤进度和时间统计")
            return True
        else:
            print(f"\n❌ 分析失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_monitoring_features():
    """展示监控功能特性"""
    
    print(f"\n📊 实时监控功能特性:")
    print(f"="*60)
    
    print(f"🎯 单首作品监控:")
    print(f"   • ⏰ 开始时间显示")
    print(f"   • 📊 8个主要步骤的进度显示")
    print(f"   • ⏱️ 每个步骤的耗时统计")
    print(f"   • ⚠️ 最耗时步骤的特别警告")
    print(f"   • 📈 总耗时和性能统计")
    
    print(f"\n🎼 批量分析监控:")
    print(f"   • 📊 整体进度显示 (x/11)")
    print(f"   • ⏰ 已用时间统计")
    print(f"   • 📈 平均每首耗时")
    print(f"   • ⏳ 剩余时间预估")
    print(f"   • ✅ 每首完成状态")
    print(f"   • 📊 最终统计摘要")
    
    print(f"\n⚠️ 性能瓶颈警告:")
    print(f"   • 🔧 复形构建耗时")
    print(f"   • 🧮 欧拉特征数计算耗时")
    print(f"   • 🔢 贝蒂数计算耗时")
    print(f"   • 📐 拓扑不变量总耗时")
    
    print(f"\n📈 预期监控输出示例:")
    print(f"   🎼 开始三音组拓扑分析: 作品名")
    print(f"   📊 音符总数: 156")
    print(f"   ⏰ 开始时间: 14:30:25")
    print(f"   🧬 [步骤1/8] 分析三音组结构...")
    print(f"      ✅ 完成，耗时: 0.05秒")
    print(f"   🎯 [步骤2/8] 发现内部吸引子...")
    print(f"      ✅ 完成，耗时: 0.12秒")
    print(f"   📐 [步骤7/8] 计算经典拓扑不变量...")
    print(f"      ⚠️ 警告：这是最耗时的步骤，可能需要较长时间...")
    print(f"      🔧 构建三音组拓扑复形...")
    print(f"      ⏱️ 复形构建耗时: 15.23秒")
    print(f"   ✅ 作品分析完成")
    print(f"   ⏰ 总耗时: 45.67秒 (0.8分钟)")

if __name__ == "__main__":
    print("🚀 实时运行监督功能测试")
    print("="*60)
    
    # 展示功能特性
    show_monitoring_features()
    
    # 进行实际测试
    print(f"\n🔍 开始实际测试...")
    success = test_monitoring()
    
    if success:
        print(f"\n🎉 监控功能测试成功！")
        print(f"✅ unified_topological_analysis.py已具备完整的实时监控")
        print(f"✅ 可以实时观察11首作品的分析进度")
        print(f"✅ 每个步骤的耗时都会显示")
        print(f"✅ 总体进度和剩余时间会实时更新")
    else:
        print(f"\n❌ 监控功能测试失败")
        print(f"需要检查代码修改是否正确")
