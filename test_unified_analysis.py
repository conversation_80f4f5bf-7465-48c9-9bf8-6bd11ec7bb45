#!/usr/bin/env python3
"""
测试统一拓扑分析系统
验证多吸引子引力景观与升级分析模块的整合效果
"""

import sys
import os
import numpy as np

# 添加当前目录到路径
sys.path.append('.')

# 导入统一分析器
from unified_topological_analysis import UnifiedTopologicalAnalyzer

def test_unified_analysis():
    """测试统一拓扑分析系统"""
    print("🧪 测试统一拓扑分析系统")
    print("="*60)
    
    # 创建不同特征的测试旋律
    test_melodies = [
        {
            'name': '单一吸引子主导型',
            'pitches': [60] * 8 + [61, 60, 62, 60, 59, 60, 63, 60, 58, 60] * 2,
            'expected': '强单一吸引子，高三音组对齐度'
        },
        {
            'name': '双吸引子平衡型',
            'pitches': [60, 61, 60, 62, 60] * 4 + [72, 73, 72, 74, 72] * 4,
            'expected': '双吸引子系统，中等对齐度'
        },
        {
            'name': '多吸引子复杂型',
            'pitches': [60, 61, 60] * 2 + [67, 68, 67] * 2 + [74, 75, 74] * 2 + [81, 82, 81] * 2,
            'expected': '多吸引子分散，低对齐度，高相位多样性'
        },
        {
            'name': '连续音阶型',
            'pitches': list(range(60, 85)) + list(range(84, 59, -1)),
            'expected': '少量吸引子，高收敛相位比例'
        }
    ]
    
    # 创建统一分析器
    analyzer = UnifiedTopologicalAnalyzer(
        kernel_width=3.0,
        max_attractors=8,
        min_attractors=1
    )
    
    results = []
    
    for i, melody in enumerate(test_melodies):
        print(f"\n🎵 测试旋律 {i+1}: {melody['name']}")
        print(f"   预期特征: {melody['expected']}")
        print(f"   音符数量: {len(melody['pitches'])}")
        
        try:
            # 执行统一拓扑分析
            result = analyzer.analyze_work(melody['pitches'], melody['name'])
            
            if result:
                print(f"   ✅ 分析成功")
                
                # 显示核心结果
                landscape = result['attractor_landscape']
                topology = result['topology_metrics']
                triad_analysis = result['enhanced_triad_analysis']
                phase_analysis = result['phase_cross_level_analysis']
                
                print(f"   🌌 引力景观:")
                print(f"      吸引子数量: {landscape['attractor_count']}")
                print(f"      最优k值: {landscape['optimal_k']}")
                print(f"      吸引子强度: {topology['attractor_strength']:.4f}")
                
                print(f"   🎯 三音组-吸引子关联:")
                if 'mean_attractor_alignment' in triad_analysis:
                    print(f"      平均对齐度: {triad_analysis['mean_attractor_alignment']:.4f}")
                    print(f"      平均影响强度: {triad_analysis['mean_attractor_influence']:.4f}")
                    print(f"      吸引子多样性: {triad_analysis['attractor_diversity']:.4f}")
                
                print(f"   🔄 相位跨层级分析:")
                if 'phase_effect_significance' in phase_analysis:
                    print(f"      效应显著性: {phase_analysis['phase_effect_significance']:.4f}")
                
                if 'phase_comparison' in phase_analysis and 'convergence_vs_equilibrium' in phase_analysis['phase_comparison']:
                    comparison = phase_analysis['phase_comparison']['convergence_vs_equilibrium']
                    print(f"      收敛vs平衡比值:")
                    print(f"        音程比值: {comparison['intervallic_ratio']:.3f}")
                    print(f"        波动比值: {comparison['volatility_ratio']:.3f}")
                
                results.append({
                    'name': melody['name'],
                    'attractor_count': landscape['attractor_count'],
                    'attractor_strength': topology['attractor_strength'],
                    'alignment': triad_analysis.get('mean_attractor_alignment', 0.0),
                    'phase_significance': phase_analysis.get('phase_effect_significance', 0.0)
                })
                
            else:
                print(f"   ❌ 分析失败")
                
        except Exception as e:
            print(f"   ❌ 分析出错: {e}")
            import traceback
            traceback.print_exc()
    
    # 验证统一分析效果
    print(f"\n" + "="*60)
    print("🎯 统一分析效果验证")
    print("="*60)
    
    if results:
        print(f"成功分析: {len(results)}/{len(test_melodies)} 个旋律")
        
        # 验证多吸引子引力景观
        attractor_counts = [r['attractor_count'] for r in results]
        attractor_strengths = [r['attractor_strength'] for r in results]
        
        print(f"\n🌌 多吸引子引力景观验证:")
        print(f"   吸引子数量分布: {attractor_counts}")
        print(f"   吸引子数量范围: {min(attractor_counts)} ~ {max(attractor_counts)}")
        print(f"   吸引子强度范围: {min(attractor_strengths):.4f} ~ {max(attractor_strengths):.4f}")
        print(f"   吸引子强度标准差: {np.std(attractor_strengths):.4f}")
        
        # 验证三音组-吸引子关联
        alignments = [r['alignment'] for r in results if r['alignment'] > 0]
        
        if alignments:
            print(f"\n🎯 三音组-吸引子关联验证:")
            print(f"   对齐度范围: {min(alignments):.4f} ~ {max(alignments):.4f}")
            print(f"   对齐度标准差: {np.std(alignments):.4f}")
        
        # 验证相位跨层级分析
        phase_significances = [r['phase_significance'] for r in results if r['phase_significance'] > 0]
        
        if phase_significances:
            print(f"\n🔄 相位跨层级分析验证:")
            print(f"   效应显著性范围: {min(phase_significances):.4f} ~ {max(phase_significances):.4f}")
            print(f"   效应显著性标准差: {np.std(phase_significances):.4f}")
        
        # 验证成功标准
        landscape_diversity = len(set(attractor_counts)) > 1  # 吸引子数量有多样性
        strength_diversity = np.std(attractor_strengths) > 0.1  # 吸引子强度有差异
        alignment_diversity = len(alignments) > 0 and np.std(alignments) > 0.01  # 对齐度有变化
        
        print(f"\n✅ 验证结果:")
        print(f"   {'✅' if landscape_diversity else '❌'} 多吸引子引力景观多样性")
        print(f"   {'✅' if strength_diversity else '❌'} 吸引子强度差异性")
        print(f"   {'✅' if alignment_diversity else '❌'} 三音组-吸引子关联多样性")
        
        if landscape_diversity and strength_diversity and alignment_diversity:
            print(f"\n🎉 统一拓扑分析系统验证成功！")
            print(f"✅ 成功整合多吸引子引力景观与升级分析模块")
            return True
        else:
            print(f"\n⚠️ 部分功能需要进一步优化")
            return False
    else:
        print("❌ 没有成功的分析结果")
        return False


def test_real_midi_analysis():
    """测试真实MIDI文件的统一分析"""
    print(f"\n" + "="*60)
    print("🎼 测试真实MIDI文件的统一分析")
    print("="*60)
    
    try:
        # 创建统一分析器
        analyzer = UnifiedTopologicalAnalyzer()
        
        # 分析少量MIDI文件进行测试
        import glob
        midi_files = glob.glob("./midi_files/*.mid")[:5]  # 只测试前5个文件
        
        if not midi_files:
            print("❌ 未找到MIDI文件进行测试")
            return False
        
        print(f"📁 测试 {len(midi_files)} 个MIDI文件")
        
        # 提取MIDI数据
        works_data = []
        for midi_file in midi_files:
            try:
                from topological_melody_core import TopologicalMelodyAnalyzer
                temp_analyzer = TopologicalMelodyAnalyzer()
                if temp_analyzer.load_midi_file(midi_file):
                    work_name = os.path.splitext(os.path.basename(midi_file))[0]
                    works_data.append((work_name, temp_analyzer.pitch_series))
                    print(f"   ✅ 加载: {work_name}")
            except Exception as e:
                print(f"   ❌ 加载失败: {os.path.basename(midi_file)} - {e}")
        
        if not works_data:
            print("❌ 没有成功加载的MIDI文件")
            return False
        
        # 执行批量分析
        results = analyzer.analyze_multiple_works(works_data)
        
        if results:
            print(f"\n✅ 真实MIDI文件分析成功")
            print(f"   成功分析: {len(results)} 首作品")
            
            # 显示一些关键统计
            attractor_counts = [r['attractor_landscape']['attractor_count'] for r in results]
            print(f"   吸引子数量分布: {dict(zip(*np.unique(attractor_counts, return_counts=True)))}")
            
            return True
        else:
            print(f"❌ 真实MIDI文件分析失败")
            return False
            
    except Exception as e:
        print(f"❌ 真实MIDI文件分析出错: {e}")
        return False


if __name__ == "__main__":
    # 测试统一分析系统
    test_success = test_unified_analysis()
    
    # 测试真实MIDI文件
    midi_success = test_real_midi_analysis()
    
    if test_success and midi_success:
        print("\n🎉 所有测试通过！统一拓扑分析系统运行正常！")
        print("✅ 多吸引子引力景观 + 升级三音组分析 + 相位跨层级分析")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
