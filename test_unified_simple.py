#!/usr/bin/env python3
"""
简化的统一拓扑分析测试
"""

import sys
import os

# 添加当前目录到路径
sys.path.append('.')

def test_simple():
    """简化测试"""
    print("🧪 简化统一拓扑分析测试")
    print("="*50)
    
    try:
        # 导入模块
        print("1. 导入模块...")
        from unified_topological_analysis import UnifiedTopologicalAnalyzer
        print("   ✅ 导入成功")
        
        # 创建分析器
        print("2. 创建分析器...")
        analyzer = UnifiedTopologicalAnalyzer()
        print("   ✅ 创建成功")
        
        # 测试单个旋律分析
        print("3. 测试单个旋律分析...")
        test_melody = [60, 62, 64, 65, 67, 69, 71, 72]
        result = analyzer.analyze_work(test_melody, "测试旋律")
        
        if result:
            print("   ✅ 单个旋律分析成功")
            print(f"   吸引子数量: {result['attractor_landscape']['attractor_count']}")
            print(f"   吸引子强度: {result['topology_metrics']['attractor_strength']:.4f}")
            
            # 检查三音组分析
            if 'enhanced_triad_analysis' in result:
                triad_analysis = result['enhanced_triad_analysis']
                if 'mean_attractor_alignment' in triad_analysis:
                    print(f"   三音组对齐度: {triad_analysis['mean_attractor_alignment']:.4f}")
            
            # 检查相位分析
            if 'phase_cross_level_analysis' in result:
                phase_analysis = result['phase_cross_level_analysis']
                if 'phase_effect_significance' in phase_analysis:
                    print(f"   相位效应显著性: {phase_analysis['phase_effect_significance']:.4f}")
            
            return True
        else:
            print("   ❌ 单个旋律分析失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_midi_loading():
    """测试MIDI文件加载"""
    print("\n4. 测试MIDI文件加载...")
    
    try:
        import glob
        midi_files = glob.glob("./midi_files/*.mid")[:3]  # 只测试前3个文件
        
        if not midi_files:
            print("   ⚠️ 未找到MIDI文件")
            return True  # 不算失败
        
        print(f"   找到 {len(midi_files)} 个MIDI文件进行测试")
        
        from topological_melody_core import TopologicalMelodyAnalyzer
        
        works_data = []
        for midi_file in midi_files:
            try:
                temp_analyzer = TopologicalMelodyAnalyzer()
                if temp_analyzer.load_midi_file(midi_file):
                    work_name = os.path.splitext(os.path.basename(midi_file))[0]
                    works_data.append((work_name, temp_analyzer.pitch_series))
                    print(f"   ✅ 加载: {work_name}")
                else:
                    print(f"   ❌ 加载失败: {os.path.basename(midi_file)}")
            except Exception as e:
                print(f"   ❌ 处理失败: {os.path.basename(midi_file)} - {e}")
        
        if works_data:
            print(f"   ✅ 成功加载 {len(works_data)} 个MIDI文件")
            
            # 测试批量分析
            print("5. 测试批量分析...")
            from unified_topological_analysis import UnifiedTopologicalAnalyzer
            analyzer = UnifiedTopologicalAnalyzer()
            
            results = analyzer.analyze_multiple_works(works_data)
            
            if results:
                print(f"   ✅ 批量分析成功，分析了 {len(results)} 首作品")
                return True
            else:
                print("   ❌ 批量分析失败")
                return False
        else:
            print("   ⚠️ 没有成功加载的MIDI文件")
            return True  # 不算失败
            
    except Exception as e:
        print(f"   ❌ MIDI测试失败: {e}")
        return False

if __name__ == "__main__":
    # 运行简化测试
    test1_success = test_simple()
    test2_success = test_midi_loading()
    
    if test1_success and test2_success:
        print("\n🎉 所有简化测试通过！")
        print("✅ 统一拓扑分析系统基本功能正常")
    else:
        print("\n❌ 部分测试失败")
        if not test1_success:
            print("   - 基础功能测试失败")
        if not test2_success:
            print("   - MIDI文件测试失败")
