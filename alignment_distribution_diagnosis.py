#!/usr/bin/env python3
"""
对齐度分布深度诊断
检查边界压缩、分类矛盾和"幽灵"指标问题
"""

import numpy as np
import sys
sys.path.append('.')

def analyze_alignment_boundary_compression():
    """分析对齐度的边界压缩问题"""
    print("🔍 对齐度边界压缩分析")
    print("检查理论范围vs实际范围的差异")
    print("="*80)
    
    # 报告的数据
    reported_data = {
        'mean': 0.4881,
        'std': 0.0913,
        'min': 0.2884,
        'max': 0.6676,
        'theoretical_min': 0.0,
        'theoretical_max': 1.0
    }
    
    print("\n1. 📊 边界压缩检测")
    print("-" * 60)
    
    # 计算边界压缩程度
    min_compression = reported_data['min'] - reported_data['theoretical_min']
    max_compression = reported_data['theoretical_max'] - reported_data['max']
    total_range_loss = min_compression + max_compression
    
    print(f"   理论范围: [{reported_data['theoretical_min']:.3f}, {reported_data['theoretical_max']:.3f}]")
    print(f"   实际范围: [{reported_data['min']:.3f}, {reported_data['max']:.3f}]")
    print(f"   下界压缩: {min_compression:.3f} ({min_compression/1.0*100:.1f}%)")
    print(f"   上界压缩: {max_compression:.3f} ({max_compression/1.0*100:.1f}%)")
    print(f"   总范围损失: {total_range_loss:.3f} ({total_range_loss/1.0*100:.1f}%)")
    
    print(f"\n   ⚠️ 边界压缩问题:")
    if min_compression > 0.1:
        print(f"     • 下界严重压缩：缺失{min_compression:.1%}的理论范围")
    if max_compression > 0.1:
        print(f"     • 上界严重压缩：缺失{max_compression:.1%}的理论范围")
    
    # 检查可能的算法限制
    print(f"\n2. 🔍 可能的算法限制")
    print("-" * 60)
    
    potential_issues = {
        '距离计算截断': {
            'description': '距离计算可能有上下界限制',
            'evidence': f'最小值{reported_data["min"]:.3f} > 0，可能存在距离下界',
            'fix': '检查距离计算公式中的截断或归一化'
        },
        '对齐度公式限制': {
            'description': '对齐度公式可能无法达到极值',
            'evidence': f'最大值{reported_data["max"]:.3f} < 1，可能公式设计问题',
            'fix': '验证对齐度公式的理论范围'
        },
        '数据预处理影响': {
            'description': '数据预处理可能过滤了极值',
            'evidence': '实际范围明显小于理论范围',
            'fix': '检查数据清洗和预处理步骤'
        }
    }
    
    for issue, details in potential_issues.items():
        print(f"\n   📌 {issue}:")
        print(f"      描述: {details['description']}")
        print(f"      证据: {details['evidence']}")
        print(f"      修复: {details['fix']}")

def analyze_classification_contradiction():
    """分析分类矛盾问题"""
    print(f"\n" + "="*80)
    print("📊 对齐度分类矛盾分析")
    print("="*80)
    
    # 报告的分类数据
    classification_data = {
        'strong_threshold': 0.333,
        'strong_percentage': 96,  # 96%为强关联
        'mean_alignment': 0.4881,
        'std_alignment': 0.0913
    }
    
    print("\n1. 🎯 分类阈值分析")
    print("-" * 60)
    
    print(f"   强关联阈值: ≥{classification_data['strong_threshold']:.3f}")
    print(f"   实际均值: {classification_data['mean_alignment']:.3f}")
    print(f"   均值超出阈值: {classification_data['mean_alignment'] - classification_data['strong_threshold']:.3f}")
    
    # 计算理论分类比例
    # 假设正态分布，计算超过阈值的理论比例
    z_score = (classification_data['strong_threshold'] - classification_data['mean_alignment']) / classification_data['std_alignment']
    from scipy.stats import norm
    theoretical_percentage = (1 - norm.cdf(z_score)) * 100
    
    print(f"\n2. 📈 理论vs实际分类比例")
    print("-" * 60)
    
    print(f"   Z分数: {z_score:.3f}")
    print(f"   理论强关联比例: {theoretical_percentage:.1f}%")
    print(f"   报告强关联比例: {classification_data['strong_percentage']:.1f}%")
    print(f"   差异: {abs(theoretical_percentage - classification_data['strong_percentage']):.1f}%")
    
    if abs(theoretical_percentage - classification_data['strong_percentage']) > 10:
        print(f"   ⚠️ 严重矛盾：理论与实际分类比例差异超过10%")
    
    # 分析分类稳定性
    print(f"\n3. 🔄 分类稳定性分析")
    print("-" * 60)
    
    # 计算在阈值附近的样本比例
    threshold_band = 0.05  # 阈值±0.05的范围
    lower_band = classification_data['strong_threshold'] - threshold_band
    upper_band = classification_data['strong_threshold'] + threshold_band
    
    # 计算在阈值带内的理论比例
    z_lower = (lower_band - classification_data['mean_alignment']) / classification_data['std_alignment']
    z_upper = (upper_band - classification_data['mean_alignment']) / classification_data['std_alignment']
    band_percentage = (norm.cdf(z_upper) - norm.cdf(z_lower)) * 100
    
    print(f"   阈值带范围: [{lower_band:.3f}, {upper_band:.3f}]")
    print(f"   阈值带内样本比例: {band_percentage:.1f}%")
    
    if band_percentage > 20:
        print(f"   ⚠️ 分类不稳定：{band_percentage:.1f}%的样本在阈值附近徘徊")

def analyze_ghost_indicator():
    """分析"幽灵"指标问题"""
    print(f"\n" + "="*80)
    print("👻 '幽灵'指标分析")
    print("="*80)
    
    # 报告的两个强度指标
    strength_indicators = {
        'original': {
            'name': '原始吸引子强度',
            'mean': 9.1472,
            'std': 2.7273,
            'cv': 2.7273 / 9.1472,
            'unit': '未知',
            'definition': '未定义'
        },
        'improved': {
            'name': '改进吸引子强度',
            'mean': 0.2320,
            'std': 0.2224,
            'cv': 0.2224 / 0.2320,
            'unit': '全音/个数',
            'definition': '(主导权重/吸引子数量) × 音高跨度 × 修正集中度指数'
        }
    }
    
    print("\n1. 👻 幽灵指标识别")
    print("-" * 60)
    
    for key, indicator in strength_indicators.items():
        print(f"\n   📊 {indicator['name']}:")
        print(f"      均值: {indicator['mean']:.4f}")
        print(f"      标准差: {indicator['std']:.4f}")
        print(f"      变异系数: {indicator['cv']:.1%}")
        print(f"      单位: {indicator['unit']}")
        print(f"      定义: {indicator['definition']}")
    
    print(f"\n2. 🔍 指标间矛盾分析")
    print("-" * 60)
    
    cv_ratio = strength_indicators['improved']['cv'] / strength_indicators['original']['cv']
    mean_ratio = strength_indicators['improved']['mean'] / strength_indicators['original']['mean']
    
    print(f"   变异系数比较:")
    print(f"     原始CV: {strength_indicators['original']['cv']:.1%}")
    print(f"     改进CV: {strength_indicators['improved']['cv']:.1%}")
    print(f"     CV比值: {cv_ratio:.1f}倍")
    print(f"   ⚠️ '改进'过程使变异性增加了{cv_ratio:.1f}倍！")
    
    print(f"\n   数值范围比较:")
    print(f"     原始均值: {strength_indicators['original']['mean']:.4f}")
    print(f"     改进均值: {strength_indicators['improved']['mean']:.4f}")
    print(f"     均值比值: {mean_ratio:.4f}")
    print(f"   ⚠️ 两个指标的数值范围完全不同！")
    
    print(f"\n3. 🚨 关键问题")
    print("-" * 60)
    
    critical_issues = [
        "原始指标缺乏定义和计算公式",
        "两个指标之间的转换关系未说明",
        "'改进'过程使变异性急剧增加",
        "无法判断哪个指标更可靠",
        "报告中存在未解释的指标"
    ]
    
    for i, issue in enumerate(critical_issues, 1):
        print(f"     {i}. {issue}")

def investigate_alignment_formula():
    """调查对齐度公式的理论范围"""
    print(f"\n" + "="*80)
    print("🔬 对齐度公式理论范围调查")
    print("="*80)
    
    print("\n1. 📐 对齐度公式分析")
    print("-" * 60)
    
    print("   当前公式（推测）:")
    print("   对齐度 = f(三音组中心, 吸引子位置, 距离)")
    print("   可能形式: 1 / (1 + 距离_全音)")
    
    print(f"\n2. 🧮 理论范围计算")
    print("-" * 60)
    
    # 模拟不同距离下的对齐度
    distances = [0, 0.5, 1.0, 2.0, 5.0, 10.0, float('inf')]
    
    print("   距离(全音) → 对齐度:")
    for d in distances:
        if d == float('inf'):
            alignment = 0.0
            print(f"     {d:>8} → {alignment:.3f}")
        else:
            alignment = 1.0 / (1.0 + d)
            print(f"     {d:>8.1f} → {alignment:.3f}")
    
    print(f"\n   理论范围: [0.000, 1.000]")
    print(f"   实际范围: [0.288, 0.668]")
    print(f"   ⚠️ 实际范围明显小于理论范围！")
    
    print(f"\n3. 🔍 边界限制原因推测")
    print("-" * 60)
    
    boundary_reasons = {
        '下界限制(0.288)': [
            '距离计算可能有最小值限制',
            '三音组与吸引子不可能完全重合',
            '数据预处理过滤了极近距离',
            '算法设计避免了零距离情况'
        ],
        '上界限制(0.668)': [
            '距离计算可能有最大值截断',
            '音乐数据的自然约束（音域限制）',
            '吸引子分布相对集中',
            '算法设计限制了最大距离'
        ]
    }
    
    for boundary, reasons in boundary_reasons.items():
        print(f"\n   {boundary}:")
        for reason in reasons:
            print(f"     • {reason}")

def recommend_comprehensive_fixes():
    """推荐综合修复方案"""
    print(f"\n" + "="*80)
    print("🔧 综合修复方案")
    print("="*80)
    
    fixes = {
        '问题1：边界压缩': {
            'immediate_actions': [
                '检查对齐度计算公式的理论范围',
                '验证距离计算是否有截断',
                '测试极端情况（零距离、最大距离）',
                '检查数据预处理步骤'
            ],
            'code_changes': [
                '修改对齐度公式确保理论范围[0,1]',
                '移除不必要的距离截断',
                '添加边界情况处理'
            ]
        },
        
        '问题2：分类矛盾': {
            'immediate_actions': [
                '重新验证分类阈值的合理性',
                '检查分类统计的计算方法',
                '分析阈值附近样本的分布'
            ],
            'code_changes': [
                '调整分类阈值或增加中间类别',
                '添加分类稳定性指标',
                '提供分类置信度'
            ]
        },
        
        '问题3：幽灵指标': {
            'immediate_actions': [
                '明确定义原始吸引子强度',
                '说明两个指标的转换关系',
                '解释为何需要"改进"',
                '验证改进过程的合理性'
            ],
            'code_changes': [
                '添加详细的指标定义和公式',
                '提供转换关系的数学表达',
                '解释改进的目的和方法',
                '同时报告两个指标并说明差异'
            ]
        }
    }
    
    for problem, solution in fixes.items():
        print(f"\n🎯 {problem}:")
        print(f"   立即行动:")
        for action in solution['immediate_actions']:
            print(f"     • {action}")
        print(f"   代码修改:")
        for change in solution['code_changes']:
            print(f"     • {change}")
    
    print(f"\n🏆 优先级排序:")
    print(f"   1. 高优先级：解决幽灵指标问题（影响可信度）")
    print(f"   2. 中优先级：修复边界压缩问题（影响完整性）")
    print(f"   3. 低优先级：调整分类矛盾问题（影响解释性）")

if __name__ == "__main__":
    print("🔍 对齐度分布深度诊断")
    print("检查边界压缩、分类矛盾和幽灵指标问题")
    
    # 1. 边界压缩分析
    analyze_alignment_boundary_compression()
    
    # 2. 分类矛盾分析
    analyze_classification_contradiction()
    
    # 3. 幽灵指标分析
    analyze_ghost_indicator()
    
    # 4. 对齐度公式调查
    investigate_alignment_formula()
    
    # 5. 综合修复方案
    recommend_comprehensive_fixes()
    
    print(f"\n🎉 深度诊断完成！")
    print(f"✅ 识别了三个关键问题")
    print(f"🔧 提供了具体的修复方案")
    print(f"⚠️ 建议立即解决幽灵指标问题")
