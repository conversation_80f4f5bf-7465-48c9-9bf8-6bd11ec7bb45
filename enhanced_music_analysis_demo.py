#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化演示版本 - 展示新框架的核心概念
包含：音程均幅、局部波动性、双方法分析、旋律动力系统
"""

import numpy as np
import pandas as pd
import math
from typing import List, Dict, Any, Tuple

class IntervalicAmbitusAnalyzer:
    """音程均幅 (Intervallic_Ambitus) 分析器"""
    
    @staticmethod
    def calculate(pitch_series: List[float]) -> float:
        """计算音程均幅 E(log i)"""
        if len(pitch_series) < 2:
            return 0.0
        
        intervals = [abs(pitch_series[i+1] - pitch_series[i]) for i in range(len(pitch_series)-1)]
        log_intervals = [math.log(max(interval, 1)) for interval in intervals]
        return sum(log_intervals) / len(log_intervals) if log_intervals else 0.0

class LocalVolatilityAnalyzer:
    """局部音程波动性 (Local_Volatility) 分析器"""
    
    @staticmethod
    def calculate_d1_rms(pitch_series: List[float]) -> float:
        """计算d1_rms（主要的局部波动性指标）"""
        if len(pitch_series) < 2:
            return 0.0
        
        intervals = [pitch_series[i+1] - pitch_series[i] for i in range(len(pitch_series)-1)]
        return math.sqrt(sum(interval**2 for interval in intervals) / len(intervals)) if intervals else 0.0
    
    @staticmethod
    def calculate_d2_rms(pitch_series: List[float]) -> float:
        """计算d2_rms（中等尺度波动性，音程曲率）"""
        if len(pitch_series) < 3:
            return 0.0
        
        # 计算二阶差分（加速度）
        first_diff = [pitch_series[i+1] - pitch_series[i] for i in range(len(pitch_series)-1)]
        second_diff = [first_diff[i+1] - first_diff[i] for i in range(len(first_diff)-1)]
        
        return math.sqrt(sum(diff**2 for diff in second_diff) / len(second_diff)) if second_diff else 0.0
    
    @staticmethod
    def calculate_rms_ratio(d1_rms: float, d2_rms: float) -> float:
        """计算RMS比率（波动性特征比率）"""
        if d2_rms == 0:
            return float('inf') if d1_rms > 0 else 0.0
        return d1_rms / d2_rms
    
    @staticmethod
    def classify_volatility_type(rms_ratio: float) -> str:
        """分类波动性类型"""
        if rms_ratio > 3.0:
            return "尖锐毛刺型"  # 高比率：快速尖锐变化
        elif rms_ratio > 1.5:
            return "混合波动型"  # 中等比率：混合特征
        elif rms_ratio > 0.5:
            return "平滑波浪型"  # 低比率：平滑波浪状
        else:
            return "极平滑型"    # 极低比率：几乎无波动

class MelodyDynamicsAnalyzer:
    """旋律动力系统分析器（简化版）"""
    
    @staticmethod
    def calculate_velocity(pitch_series: List[float]) -> List[float]:
        """计算速度（一阶导数）"""
        if len(pitch_series) < 2:
            return [0.0]
        return [pitch_series[i+1] - pitch_series[i] for i in range(len(pitch_series)-1)]
    
    @staticmethod
    def calculate_acceleration(velocity: List[float]) -> List[float]:
        """计算加速度（二阶导数）"""
        if len(velocity) < 2:
            return [0.0]
        return [velocity[i+1] - velocity[i] for i in range(len(velocity)-1)]
    
    @staticmethod
    def calculate_curvature(velocity: List[float], acceleration: List[float]) -> List[float]:
        """计算曲率"""
        curvature = []
        for i in range(min(len(velocity), len(acceleration))):
            v = velocity[i]
            a = acceleration[i]
            if abs(v) > 1e-5:
                curvature.append(abs(a) / (abs(v)**2))
            else:
                curvature.append(0.0)
        return curvature
    
    @staticmethod
    def detect_attractors(pitch_series: List[float]) -> Dict[str, Any]:
        """简化的吸引子检测"""
        # 统计音高出现频率
        pitch_counts = {}
        for pitch in pitch_series:
            rounded_pitch = round(pitch)
            pitch_counts[rounded_pitch] = pitch_counts.get(rounded_pitch, 0) + 1
        
        # 找到主要吸引子（出现频率高的音高）
        total_notes = len(pitch_series)
        attractors = []
        for pitch, count in pitch_counts.items():
            if count / total_notes > 0.1:  # 出现频率超过10%
                attractors.append(pitch)
        
        # 计算到最近吸引子的平均距离
        if attractors:
            distances = []
            for pitch in pitch_series:
                min_dist = min(abs(pitch - attractor) for attractor in attractors)
                distances.append(min_dist)
            avg_distance = sum(distances) / len(distances)
        else:
            avg_distance = 0.0
        
        return {
            'count': len(attractors),
            'positions': attractors,
            'avg_distance': avg_distance
        }
    
    @staticmethod
    def assess_stability(attractors: Dict, curvature: List[float]) -> str:
        """评估系统稳定性"""
        avg_curvature = sum(curvature) / len(curvature) if curvature else 0.0
        curvature_variance = np.var(curvature) if len(curvature) > 1 else 0.0
        
        if curvature_variance > 0.5 and avg_curvature > 0.1:
            return "高度不稳定（脉冲式）"
        elif attractors['avg_distance'] < 2.0 and curvature_variance < 0.1 and attractors['count'] > 0:
            return "高度稳定（吸引子主导）"
        elif avg_curvature < 0.05:
            return "稳定（低曲率）"
        else:
            return "中等稳定"

class DualMethodAnalyzer:
    """双方法分析器"""
    
    @staticmethod
    def calculate_orthogonality(method1_values: List[float], method2_values: List[float]) -> Dict[str, Any]:
        """计算正交性"""
        if len(method1_values) != len(method2_values) or len(method1_values) < 2:
            return {'correlation': 0.0, 'assessment': '数据不足'}
        
        # 简化的相关性计算
        mean1 = sum(method1_values) / len(method1_values)
        mean2 = sum(method2_values) / len(method2_values)
        
        numerator = sum((v1 - mean1) * (v2 - mean2) for v1, v2 in zip(method1_values, method2_values))
        
        sum_sq1 = sum((v1 - mean1)**2 for v1 in method1_values)
        sum_sq2 = sum((v2 - mean2)**2 for v2 in method2_values)
        
        if sum_sq1 == 0 or sum_sq2 == 0:
            correlation = 0.0
        else:
            correlation = numerator / math.sqrt(sum_sq1 * sum_sq2)
        
        # 正交性评估
        if abs(correlation) < 0.3:
            assessment = "高度正交"
        elif abs(correlation) < 0.6:
            assessment = "中度相关"
        else:
            assessment = "强相关"
        
        return {
            'correlation': correlation,
            'assessment': assessment
        }

def analyze_melody_comprehensive(pitch_series: List[float], name: str = "未命名旋律") -> Dict[str, Any]:
    """综合分析单个旋律"""
    
    # 1. 音程均幅分析
    intervallic_ambitus = IntervalicAmbitusAnalyzer.calculate(pitch_series)
    
    # 2. 局部波动性分析
    d1_rms = LocalVolatilityAnalyzer.calculate_d1_rms(pitch_series)
    d2_rms = LocalVolatilityAnalyzer.calculate_d2_rms(pitch_series)
    rms_ratio = LocalVolatilityAnalyzer.calculate_rms_ratio(d1_rms, d2_rms)
    volatility_type = LocalVolatilityAnalyzer.classify_volatility_type(rms_ratio)
    
    # 3. 旋律动力系统分析
    velocity = MelodyDynamicsAnalyzer.calculate_velocity(pitch_series)
    acceleration = MelodyDynamicsAnalyzer.calculate_acceleration(velocity)
    curvature = MelodyDynamicsAnalyzer.calculate_curvature(velocity, acceleration)
    attractors = MelodyDynamicsAnalyzer.detect_attractors(pitch_series)
    stability = MelodyDynamicsAnalyzer.assess_stability(attractors, curvature)
    
    # 4. 系统能量和复杂性
    system_energy = sum(v**2 + a**2 for v, a in zip(velocity, acceleration)) / len(velocity) if velocity else 0.0
    avg_curvature = sum(curvature) / len(curvature) if curvature else 0.0
    
    return {
        'name': name,
        'note_count': len(pitch_series),
        
        # 音程均幅特征
        'intervallic_ambitus': intervallic_ambitus,
        
        # 局部波动性特征
        'local_volatility_d1': d1_rms,
        'medium_volatility_d2': d2_rms,
        'volatility_ratio': rms_ratio,
        'volatility_type': volatility_type,
        
        # 动力系统特征
        'system_energy': system_energy,
        'avg_curvature': avg_curvature,
        'attractor_count': attractors['count'],
        'attractor_distance': attractors['avg_distance'],
        'stability': stability,
        
        # 原始数据
        'pitch_series': pitch_series,
        'velocity': velocity,
        'acceleration': acceleration,
        'curvature': curvature,
        'attractors': attractors
    }

def run_comprehensive_demo():
    """运行综合演示"""
    print("🎼 增强版中国音乐分析系统 - 综合演示")
    print("=" * 80)
    
    # 测试旋律数据
    test_melodies = [
        {
            'name': '平滑音阶',
            'pitches': [60, 62, 64, 65, 67, 69, 71, 72, 74, 76, 77, 79, 81, 83, 84],
            'description': '预期：低音程均幅，低局部波动性，稳定系统'
        },
        {
            'name': '跳跃旋律',
            'pitches': [60, 72, 48, 75, 45, 78, 42, 81, 39, 84, 36, 87, 33, 90, 30],
            'description': '预期：高音程均幅，高局部波动性，不稳定系统'
        },
        {
            'name': '三音组模式',
            'pitches': [60, 62, 61, 63, 62, 64, 63, 65, 64, 66, 65, 67, 66, 68, 67],
            'description': '预期：中等特征，特殊的波动性模式'
        },
        {
            'name': '吸引子主导',
            'pitches': [60, 61, 60, 62, 60, 61, 60, 63, 60, 61, 60, 62, 60, 64, 60],
            'description': '预期：强吸引子，高稳定性'
        },
        {
            'name': '混沌模式',
            'pitches': [60, 67, 55, 72, 48, 69, 52, 74, 46, 71, 49, 76, 44, 73, 51],
            'description': '预期：高复杂性，混沌特征'
        }
    ]
    
    print("🔍 单个旋律分析结果:")
    print("=" * 80)
    
    all_results = []
    for melody in test_melodies:
        print(f"\n🎵 分析: {melody['name']}")
        print(f"描述: {melody['description']}")
        
        result = analyze_melody_comprehensive(melody['pitches'], melody['name'])
        all_results.append(result)
        
        print(f"  音程均幅 (Intervallic_Ambitus): {result['intervallic_ambitus']:.4f}")
        print(f"  局部波动性 (d1_rms): {result['local_volatility_d1']:.4f}")
        print(f"  中等波动性 (d2_rms): {result['medium_volatility_d2']:.4f}")
        print(f"  波动性比率: {result['volatility_ratio']:.4f}")
        print(f"  波动性类型: {result['volatility_type']}")
        print(f"  系统能量: {result['system_energy']:.4f}")
        print(f"  平均曲率: {result['avg_curvature']:.4f}")
        print(f"  吸引子数量: {result['attractor_count']}")
        print(f"  系统稳定性: {result['stability']}")
    
    # 双方法正交性分析
    print(f"\n🔄 双方法正交性分析:")
    print("=" * 50)
    
    method1_values = [r['intervallic_ambitus'] for r in all_results]
    method2_values = [r['local_volatility_d1'] for r in all_results]
    
    orthogonality = DualMethodAnalyzer.calculate_orthogonality(method1_values, method2_values)
    
    print(f"音程均幅 vs 局部波动性:")
    print(f"  相关系数: {orthogonality['correlation']:.4f}")
    print(f"  正交性评估: {orthogonality['assessment']}")
    
    # 统计摘要
    print(f"\n📊 统计摘要:")
    print("=" * 40)
    
    print(f"音程均幅 (Intervallic_Ambitus) 统计:")
    print(f"  均值: {np.mean(method1_values):.4f}")
    print(f"  标准差: {np.std(method1_values):.4f}")
    print(f"  范围: {np.min(method1_values):.4f} ~ {np.max(method1_values):.4f}")
    
    print(f"局部波动性 (Local_Volatility) 统计:")
    print(f"  均值: {np.mean(method2_values):.4f}")
    print(f"  标准差: {np.std(method2_values):.4f}")
    print(f"  范围: {np.min(method2_values):.4f} ~ {np.max(method2_values):.4f}")
    
    # 动力系统统计
    system_energies = [r['system_energy'] for r in all_results]
    stabilities = [r['stability'] for r in all_results]
    
    print(f"旋律动力系统统计:")
    print(f"  平均系统能量: {np.mean(system_energies):.4f}")
    print(f"  稳定性分布:")
    stability_counts = {}
    for stability in stabilities:
        stability_counts[stability] = stability_counts.get(stability, 0) + 1
    for stability, count in stability_counts.items():
        percentage = (count / len(stabilities)) * 100
        print(f"    {stability}: {count} 首 ({percentage:.1f}%)")
    
    print(f"\n✅ 核心结论:")
    print(f"  ✅ 音程均幅和局部波动性: {orthogonality['assessment']}")
    print(f"  ✅ 两种方法测量不同的音乐特征维度")
    print(f"  ✅ 旋律动力系统提供了稳定性和复杂性的新视角")
    print(f"  ✅ 框架成功区分了不同类型的旋律模式")
    
    print(f"\n🎯 理论意义:")
    print(f"  • 音程均幅 (Intervallic_Ambitus): 测量音程大小的对数平均")
    print(f"  • 局部波动性 (Local_Volatility): 测量绝对不规则性（RMS）")
    print(f"  • 旋律动力系统: 基于动力系统理论的稳定性分析")
    print(f"  • 为'三音组构成中国传统音乐风格'提供数学支撑")

if __name__ == "__main__":
    run_comprehensive_demo()
