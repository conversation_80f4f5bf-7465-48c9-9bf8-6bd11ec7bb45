#!/usr/bin/env python3
"""
基于张量场的拓扑动力学旋律分析器
Topological Melody Analyzer Based on Tensor Field Dynamics

核心理论：
1. 旋律是R³相空间中的动力系统(p, ṗ, p̈)
2. 存在隐式引力场V(p)产生拓扑吸引子
3. 三音组是相空间中的局部流形Mt ⊂ R³
4. 旋律发展遵循 d/dt Mt = -∇V(Mt)
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from scipy.optimize import minimize
from scipy.spatial.distance import pdist, squareform
from sklearn.mixture import GaussianMixture
from sklearn.cluster import DBSCAN
import warnings
warnings.filterwarnings('ignore')

class MelodyPotentialField:
    """
    旋律势能场 - 替代离散吸引子
    基于连续引力场建模，使用高斯混合模型表示势能场V(p)
    """
    
    def __init__(self, kernel_width=3.0, n_attractors=5):
        self.kernel_width = kernel_width  # 势场宽度参数
        self.n_attractors = n_attractors  # 最大吸引子数量
        self.pitch_range = None
        self.attractor_points = []
        self.field_params = []
        
    def fit(self, pitch_series):
        """从音高序列学习势能场V(p)"""
        self.pitch_range = (np.min(pitch_series), np.max(pitch_series))
        self.attractor_points = self._find_attractor_candidates(pitch_series)
        self.field_params = self._compute_field_parameters(pitch_series)
        
    def _find_attractor_candidates(self, pitch_series):
        """通过拓扑持久性识别关键吸引点"""
        # 使用密度聚类识别高密度区域作为吸引子候选
        pitch_array = np.array(pitch_series).reshape(-1, 1)
        
        # DBSCAN聚类识别密集区域
        clustering = DBSCAN(eps=1.0, min_samples=3).fit(pitch_array)
        labels = clustering.labels_
        
        candidates = []
        for label in set(labels):
            if label != -1:  # 排除噪声点
                cluster_points = pitch_array[labels == label]
                centroid = np.mean(cluster_points)
                strength = len(cluster_points) / len(pitch_series)
                candidates.append((centroid, strength))
        
        # 按强度排序，取前n_attractors个
        candidates.sort(key=lambda x: x[1], reverse=True)
        return candidates[:self.n_attractors]
    
    def _compute_field_parameters(self, pitch_series):
        """计算高斯混合势场参数"""
        # V(p) = -∑_i α_i exp(-(p-μ_i)²/(2σ²))
        params = []
        
        if not self.attractor_points:
            # 如果没有找到吸引子，使用均值作为单一吸引子
            μ = np.mean(pitch_series)
            α = 1.0
            σ = self.kernel_width
            params.append((μ, α, σ))
        else:
            for μ, strength in self.attractor_points:
                α = strength * 10.0  # 放大强度
                σ = self.kernel_width
                params.append((μ, α, σ))
        
        return params
    
    def compute_gradient(self, position):
        """计算引力场梯度∇V(p)"""
        grad = 0
        for μ, α, σ in self.field_params:
            diff = position - μ
            exp_term = np.exp(-(diff**2)/(2*σ**2))
            grad += α * diff / (σ**2) * exp_term
        return -grad
    
    def compute_potential(self, position):
        """计算位置p的势能V(p)"""
        pot = 0
        for μ, α, σ in self.field_params:
            pot += -α * np.exp(-((position-μ)**2)/(2*σ**2))
        return pot

class TriadManifoldDynamics:
    """
    三音组流形动力学分析
    分析三音组序列在相空间中的演化轨迹
    """
    
    def __init__(self, potential_field):
        self.potential_field = potential_field
    
    def analyze_triad_trajectory(self, triad_sequence):
        """分析三音组序列的动力学演化"""
        trajectory_metrics = []
        
        for i, triad in enumerate(triad_sequence):
            # 三音组在相空间中的表示
            positions = [note['position'] for note in triad['notes']]
            centroid = np.mean(positions)
            velocity = self._compute_triad_velocity(triad)
            curvature = self._compute_triad_curvature(triad)
            
            # 势场作用分析
            field_gradient = self.potential_field.compute_gradient(centroid)
            field_potential = self.potential_field.compute_potential(centroid)
            
            # 流形稳定性指标
            stability = self._compute_manifold_stability(
                centroid, velocity, field_gradient)
            
            # 分类流形动态相位
            phase = self._classify_manifold_phase(velocity, field_gradient)
            
            trajectory_metrics.append({
                'position': (centroid, velocity, curvature),
                'field_interaction': (field_gradient, field_potential),
                'stability': stability,
                'phase': phase,
                'triad_index': i
            })
        
        return trajectory_metrics
    
    def _compute_triad_velocity(self, triad):
        """计算三音组流形切向量(一阶导数)"""
        positions = [note['position'] for note in triad['notes']]
        times = [note['time'] for note in triad['notes']]
        
        # 使用线性拟合计算斜率作为速度
        if len(set(times)) > 1:
            velocity = np.polyfit(times, positions, 1)[0]
        else:
            velocity = 0.0
        return velocity
    
    def _compute_triad_curvature(self, triad):
        """计算三音组流形曲率(二阶导数)"""
        positions = [note['position'] for note in triad['notes']]
        
        # 使用二次拟合的二次项系数作为曲率
        try:
            curvature = np.polyfit(range(3), positions, 2)[0] * 2  # 二次项系数×2
        except:
            curvature = 0.0
        return curvature
    
    def _compute_manifold_stability(self, centroid, velocity, field_gradient):
        """计算流形稳定性: λ = -<v, ∇V> / ||v||²"""
        if abs(velocity) < 1e-8:
            return 0.0
        
        dot_product = velocity * field_gradient
        stability = -dot_product / (velocity**2 + 1e-8)
        return stability
    
    def _classify_manifold_phase(self, velocity, field_gradient):
        """分类流形动态相位"""
        if abs(velocity) < 1e-8 or abs(field_gradient) < 1e-8:
            return "Static Equilibrium"
        
        # 计算速度与梯度的夹角
        cos_angle = (velocity * field_gradient) / (abs(velocity) * abs(field_gradient))
        cos_angle = np.clip(cos_angle, -1, 1)  # 确保在[-1,1]范围内
        angle = np.arccos(abs(cos_angle))
        
        if angle < np.pi/4:
            return "Attractor Convergence"
        elif angle < 3*np.pi/4:
            return "Orbital Trajectory"
        else:
            return "Repulsive Divergence"

class TopologicalMelodyAnalyzer:
    """
    多尺度拓扑旋律分析器
    整合势能场和三音组动力学分析
    """
    
    def __init__(self, kernel_width=3.0, n_attractors=5):
        self.potential_field = MelodyPotentialField(kernel_width, n_attractors)
        self.triad_dynamics = None
        self.analysis_results = None
    
    def analyze_melody(self, pitch_series):
        """执行完整的多尺度拓扑分析"""
        print("开始拓扑旋律分析...")
        
        # 学习全局势能场
        print("1. 学习势能场...")
        self.potential_field.fit(pitch_series)
        
        # 提取三音组序列
        print("2. 提取三音组序列...")
        triad_sequence = self._extract_triad_sequence(pitch_series)
        
        # 初始化三音组动力学分析
        print("3. 分析三音组动力学...")
        self.triad_dynamics = TriadManifoldDynamics(self.potential_field)
        
        # 分析三音组轨迹
        triad_trajectory = self.triad_dynamics.analyze_triad_trajectory(triad_sequence)
        
        # 计算全局拓扑指标
        print("4. 计算拓扑指标...")
        topology_metrics = self._compute_global_topology(triad_trajectory)
        
        self.analysis_results = {
            'potential_field': {
                'attractor_points': self.potential_field.attractor_points,
                'field_params': self.potential_field.field_params,
                'pitch_range': self.potential_field.pitch_range
            },
            'triad_trajectory': triad_trajectory,
            'topology_metrics': topology_metrics,
            'original_pitch_series': pitch_series
        }
        
        print("分析完成！")
        return self.analysis_results
    
    def _extract_triad_sequence(self, pitch_series):
        """提取重叠三音组序列"""
        sequence = []
        for i in range(len(pitch_series) - 2):
            triad = {
                'position': i,
                'notes': [
                    {'position': pitch_series[i], 'time': i},
                    {'position': pitch_series[i+1], 'time': i+1},
                    {'position': pitch_series[i+2], 'time': i+2}
                ]
            }
            sequence.append(triad)
        return sequence
    
    def _compute_global_topology(self, triad_trajectory):
        """计算全局拓扑指标"""
        if not triad_trajectory:
            return {}
        
        # 1. 吸引子收敛比例
        convergence_count = sum(1 for t in triad_trajectory 
                              if t['phase'] == "Attractor Convergence")
        convergence_ratio = convergence_count / len(triad_trajectory)
        
        # 2. 平均李雅普诺夫指数
        stability_values = [t['stability'] for t in triad_trajectory]
        mean_stability = np.mean(stability_values)
        stability_variance = np.var(stability_values)
        
        # 3. 拓扑熵
        phase_transitions = 0
        for i in range(1, len(triad_trajectory)):
            if triad_trajectory[i]['phase'] != triad_trajectory[i-1]['phase']:
                phase_transitions += 1
        topological_entropy = phase_transitions / len(triad_trajectory)
        
        # 4. 吸引子强度
        attractor_strength = self._compute_attractor_strength()
        
        # 5. 相位分布
        phase_distribution = {}
        for t in triad_trajectory:
            phase = t['phase']
            phase_distribution[phase] = phase_distribution.get(phase, 0) + 1
        
        # 归一化相位分布
        total = len(triad_trajectory)
        for phase in phase_distribution:
            phase_distribution[phase] /= total
        
        return {
            'convergence_ratio': convergence_ratio,
            'mean_stability': mean_stability,
            'stability_variance': stability_variance,
            'topological_entropy': topological_entropy,
            'attractor_strength': attractor_strength,
            'phase_distribution': phase_distribution,
            'trajectory_length': len(triad_trajectory)
        }
    
    def _compute_attractor_strength(self):
        """计算吸引子强度指标"""
        if not self.potential_field.field_params:
            return 0.0
        
        # 基于势场参数的深度和宽度计算
        total_strength = 0
        for μ, α, σ in self.potential_field.field_params:
            # 强度与振幅成正比，与宽度成反比
            strength = α / σ
            total_strength += strength
        return total_strength

def plot_triad_manifold(analyzer, save_path=None):
    """可视化相空间中的三音组流形演化"""
    if analyzer.analysis_results is None:
        print("请先运行analyze_melody()方法")
        return

    results = analyzer.analysis_results
    potential_field = analyzer.potential_field
    triad_trajectory = results['triad_trajectory']

    fig = plt.figure(figsize=(16, 12))

    # 创建3D子图
    ax1 = fig.add_subplot(221, projection='3d')

    # 绘制势能场背景
    if potential_field.pitch_range:
        p_range = np.linspace(potential_field.pitch_range[0],
                            potential_field.pitch_range[1], 30)
        v_range = np.linspace(-5, 5, 30)
        P, V = np.meshgrid(p_range, v_range)
        Z = np.zeros_like(P)

        for i in range(P.shape[0]):
            for j in range(P.shape[1]):
                Z[i,j] = potential_field.compute_potential(P[i,j])

        ax1.plot_surface(P, V, Z, alpha=0.2, cmap='viridis')

    # 提取轨迹数据
    if triad_trajectory:
        positions = [t['position'] for t in triad_trajectory]
        centroids = [p[0] for p in positions]
        velocities = [p[1] for p in positions]
        curvatures = [p[2] for p in positions]

        # 使用相位映射颜色
        phase_colors = {
            'Attractor Convergence': 'red',
            'Orbital Trajectory': 'blue',
            'Repulsive Divergence': 'orange',
            'Static Equilibrium': 'gray'
        }
        colors = [phase_colors.get(t['phase'], 'black') for t in triad_trajectory]

        # 绘制主轨迹
        ax1.plot(centroids, velocities, curvatures, 'k-', alpha=0.5, linewidth=2)

        # 绘制三音组点
        ax1.scatter(centroids, velocities, curvatures, c=colors, s=50, alpha=0.8)

        # 绘制引力场梯度向量（每5个点绘制一个）
        for i in range(0, len(triad_trajectory), 5):
            t = triad_trajectory[i]
            c, v, k = positions[i]
            grad = t['field_interaction'][0]

            if abs(grad) > 1e-8:  # 只绘制非零梯度
                ax1.quiver(c, v, k, grad*2, 0, 0,
                         length=0.5, color='red', alpha=0.7, arrow_length_ratio=0.1)

    ax1.set_xlabel('Position (Pitch)')
    ax1.set_ylabel('Velocity (ΔPitch)')
    ax1.set_zlabel('Curvature (Acceleration)')
    ax1.set_title('Triad Manifold Dynamics in Phase Space')

    # 2D势能场图
    ax2 = fig.add_subplot(222)
    if potential_field.pitch_range:
        p_range = np.linspace(potential_field.pitch_range[0],
                            potential_field.pitch_range[1], 100)
        potentials = [potential_field.compute_potential(p) for p in p_range]
        gradients = [potential_field.compute_gradient(p) for p in p_range]

        ax2.plot(p_range, potentials, 'b-', linewidth=2, label='Potential V(p)')
        ax2_twin = ax2.twinx()
        ax2_twin.plot(p_range, gradients, 'r--', linewidth=2, label='Gradient ∇V(p)')

        # 标记吸引子位置
        for μ, α, σ in potential_field.field_params:
            ax2.axvline(x=μ, color='green', linestyle=':', alpha=0.7)
            ax2.text(μ, min(potentials), f'α={α:.2f}', rotation=90,
                    verticalalignment='bottom', fontsize=8)

    ax2.set_xlabel('Pitch Position')
    ax2.set_ylabel('Potential Energy', color='b')
    ax2_twin.set_ylabel('Gradient', color='r')
    ax2.set_title('Melody Potential Field V(p)')
    ax2.legend(loc='upper left')
    ax2_twin.legend(loc='upper right')

    # 相位分布饼图
    ax3 = fig.add_subplot(223)
    if triad_trajectory:
        phase_dist = results['topology_metrics']['phase_distribution']
        if phase_dist:
            phases = list(phase_dist.keys())
            values = list(phase_dist.values())
            colors_pie = [phase_colors.get(phase, 'gray') for phase in phases]

            ax3.pie(values, labels=phases, colors=colors_pie, autopct='%1.1f%%')
            ax3.set_title('Phase Distribution')

    # 拓扑指标表
    ax4 = fig.add_subplot(224)
    ax4.axis('off')

    if 'topology_metrics' in results:
        metrics = results['topology_metrics']

        # 创建指标文本
        metrics_text = f"""
拓扑指标 (Topological Metrics)

收敛比例: {metrics.get('convergence_ratio', 0):.3f}
平均稳定性: {metrics.get('mean_stability', 0):.3f}
稳定性方差: {metrics.get('stability_variance', 0):.3f}
拓扑熵: {metrics.get('topological_entropy', 0):.3f}
吸引子强度: {metrics.get('attractor_strength', 0):.3f}
轨迹长度: {metrics.get('trajectory_length', 0)}

吸引子参数:
"""

        # 添加吸引子信息
        for i, (μ, α, σ) in enumerate(potential_field.field_params):
            metrics_text += f"  吸引子{i+1}: μ={μ:.2f}, α={α:.2f}, σ={σ:.2f}\n"

        ax4.text(0.1, 0.9, metrics_text, transform=ax4.transAxes,
                fontsize=10, verticalalignment='top', fontfamily='monospace')

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图像已保存到: {save_path}")
    else:
        plt.show()

def plot_trajectory_evolution(analyzer, save_path=None):
    """绘制轨迹演化时间序列"""
    if analyzer.analysis_results is None:
        print("请先运行analyze_melody()方法")
        return

    results = analyzer.analysis_results
    triad_trajectory = results['triad_trajectory']

    if not triad_trajectory:
        print("没有轨迹数据可绘制")
        return

    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 提取时间序列数据
    times = [t['triad_index'] for t in triad_trajectory]
    centroids = [t['position'][0] for t in triad_trajectory]
    velocities = [t['position'][1] for t in triad_trajectory]
    curvatures = [t['position'][2] for t in triad_trajectory]
    stabilities = [t['stability'] for t in triad_trajectory]
    potentials = [t['field_interaction'][1] for t in triad_trajectory]

    # 位置演化
    axes[0,0].plot(times, centroids, 'b-', linewidth=2, label='Centroid Position')
    axes[0,0].set_xlabel('Triad Index')
    axes[0,0].set_ylabel('Pitch Position')
    axes[0,0].set_title('Position Evolution')
    axes[0,0].grid(True, alpha=0.3)

    # 速度和曲率
    axes[0,1].plot(times, velocities, 'r-', linewidth=2, label='Velocity')
    axes[0,1].plot(times, curvatures, 'g-', linewidth=2, label='Curvature')
    axes[0,1].set_xlabel('Triad Index')
    axes[0,1].set_ylabel('Derivative Values')
    axes[0,1].set_title('Velocity & Curvature Evolution')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)

    # 稳定性指标
    axes[1,0].plot(times, stabilities, 'purple', linewidth=2)
    axes[1,0].axhline(y=0, color='black', linestyle='--', alpha=0.5)
    axes[1,0].set_xlabel('Triad Index')
    axes[1,0].set_ylabel('Stability (Lyapunov)')
    axes[1,0].set_title('Manifold Stability Evolution')
    axes[1,0].grid(True, alpha=0.3)

    # 势能演化
    axes[1,1].plot(times, potentials, 'orange', linewidth=2)
    axes[1,1].set_xlabel('Triad Index')
    axes[1,1].set_ylabel('Potential Energy')
    axes[1,1].set_title('Potential Energy Evolution')
    axes[1,1].grid(True, alpha=0.3)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"演化图已保存到: {save_path}")
    else:
        plt.show()

def compare_models(pitch_series, save_path=None):
    """比较不同参数设置的模型效果"""
    print("开始模型对比分析...")

    # 不同参数配置
    configs = [
        {'kernel_width': 2.0, 'n_attractors': 3, 'name': 'Narrow Field (σ=2.0)'},
        {'kernel_width': 3.0, 'n_attractors': 5, 'name': 'Medium Field (σ=3.0)'},
        {'kernel_width': 5.0, 'n_attractors': 7, 'name': 'Wide Field (σ=5.0)'}
    ]

    results = []

    for config in configs:
        print(f"分析配置: {config['name']}")
        analyzer = TopologicalMelodyAnalyzer(
            kernel_width=config['kernel_width'],
            n_attractors=config['n_attractors']
        )
        result = analyzer.analyze_melody(pitch_series)
        results.append({
            'config': config,
            'analyzer': analyzer,
            'metrics': result['topology_metrics']
        })

    # 可视化对比
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))

    metrics_to_plot = [
        ('convergence_ratio', '收敛比例'),
        ('mean_stability', '平均稳定性'),
        ('topological_entropy', '拓扑熵'),
        ('attractor_strength', '吸引子强度')
    ]

    # 绘制指标对比
    for i, (metric, title) in enumerate(metrics_to_plot):
        row, col = i // 2, i % 2
        ax = axes[row, col]

        names = [r['config']['name'] for r in results]
        values = [r['metrics'].get(metric, 0) for r in results]

        bars = ax.bar(range(len(names)), values, alpha=0.7)
        ax.set_xticks(range(len(names)))
        ax.set_xticklabels(names, rotation=45, ha='right')
        ax.set_ylabel(title)
        ax.set_title(f'{title}对比')

        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{value:.3f}', ha='center', va='bottom')

    # 相位分布对比
    ax = axes[1, 2]
    phase_types = ['Attractor Convergence', 'Orbital Trajectory',
                   'Repulsive Divergence', 'Static Equilibrium']

    x_pos = np.arange(len(phase_types))
    width = 0.25

    for i, result in enumerate(results):
        phase_dist = result['metrics'].get('phase_distribution', {})
        values = [phase_dist.get(phase, 0) for phase in phase_types]

        ax.bar(x_pos + i*width, values, width,
               label=result['config']['name'], alpha=0.7)

    ax.set_xlabel('Phase Types')
    ax.set_ylabel('Proportion')
    ax.set_title('相位分布对比')
    ax.set_xticks(x_pos + width)
    ax.set_xticklabels(phase_types, rotation=45, ha='right')
    ax.legend()

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"对比图已保存到: {save_path}")
    else:
        plt.show()

    return results

def generate_test_melody(length=50, complexity='medium'):
    """生成测试旋律数据"""
    np.random.seed(42)  # 确保可重复性

    if complexity == 'simple':
        # 简单旋律：围绕单一吸引子
        base = 60  # C4
        melody = [base + np.random.normal(0, 2) + 3*np.sin(i/5) for i in range(length)]

    elif complexity == 'medium':
        # 中等复杂度：双吸引子系统
        melody = []
        for i in range(length):
            if i < length//2:
                # 第一个吸引子区域
                base = 60 + 5*np.sin(i/8) + np.random.normal(0, 1.5)
            else:
                # 第二个吸引子区域
                base = 67 + 3*np.cos(i/6) + np.random.normal(0, 1.5)
            melody.append(base)

    elif complexity == 'complex':
        # 复杂旋律：多吸引子动态系统
        melody = []
        attractors = [60, 64, 67, 72]  # C, E, G, C
        for i in range(length):
            # 动态选择吸引子
            attractor_idx = int(4 * np.sin(i/10)**2)
            base_pitch = attractors[attractor_idx]

            # 添加噪声和趋势
            noise = np.random.normal(0, 1)
            trend = 2 * np.sin(i/15)
            melody.append(base_pitch + noise + trend)

    return melody

def print_analysis_summary(analyzer):
    """打印分析结果摘要"""
    if analyzer.analysis_results is None:
        print("没有分析结果可显示")
        return

    results = analyzer.analysis_results
    metrics = results['topology_metrics']

    print("\n" + "="*60)
    print("拓扑旋律分析结果摘要")
    print("="*60)

    print(f"\n【势能场信息】")
    print(f"音高范围: {results['potential_field']['pitch_range']}")
    print(f"吸引子数量: {len(results['potential_field']['attractor_points'])}")

    for i, (pos, strength) in enumerate(results['potential_field']['attractor_points']):
        print(f"  吸引子{i+1}: 位置={pos:.2f}, 强度={strength:.3f}")

    print(f"\n【动力学指标】")
    print(f"收敛比例: {metrics['convergence_ratio']:.3f}")
    print(f"平均稳定性: {metrics['mean_stability']:.3f}")
    print(f"稳定性方差: {metrics['stability_variance']:.3f}")
    print(f"拓扑熵: {metrics['topological_entropy']:.3f}")
    print(f"吸引子强度: {metrics['attractor_strength']:.3f}")

    print(f"\n【相位分布】")
    phase_dist = metrics['phase_distribution']
    for phase, ratio in phase_dist.items():
        print(f"  {phase}: {ratio:.1%}")

    print(f"\n【轨迹信息】")
    print(f"三音组数量: {metrics['trajectory_length']}")

    # 分析结果解释
    print(f"\n【结果解释】")
    if metrics['convergence_ratio'] > 0.6:
        print("✓ 旋律表现出强烈的吸引子收敛特性")
    elif metrics['convergence_ratio'] > 0.3:
        print("◐ 旋律具有中等程度的吸引子特性")
    else:
        print("✗ 旋律缺乏明显的吸引子结构")

    if metrics['topological_entropy'] > 0.5:
        print("✓ 旋律具有丰富的动态变化")
    elif metrics['topological_entropy'] > 0.2:
        print("◐ 旋律具有适度的动态变化")
    else:
        print("✗ 旋律变化相对单调")

    if abs(metrics['mean_stability']) < 0.1:
        print("✓ 旋律系统整体稳定")
    else:
        print("◐ 旋律系统存在不稳定因素")

def main():
    """主函数：演示拓扑旋律分析器的使用"""
    print("拓扑旋律分析器演示")
    print("="*50)

    # 生成测试旋律
    print("\n1. 生成测试旋律...")
    melody_simple = generate_test_melody(40, 'simple')
    melody_medium = generate_test_melody(50, 'medium')
    melody_complex = generate_test_melody(60, 'complex')

    melodies = [
        (melody_simple, "简单旋律"),
        (melody_medium, "中等复杂旋律"),
        (melody_complex, "复杂旋律")
    ]

    # 分析每个旋律
    analyzers = []
    for melody, name in melodies:
        print(f"\n2. 分析{name}...")
        analyzer = TopologicalMelodyAnalyzer(kernel_width=3.0, n_attractors=5)
        analyzer.analyze_melody(melody)
        analyzers.append((analyzer, name))

        # 打印摘要
        print_analysis_summary(analyzer)

    # 可视化结果
    print(f"\n3. 生成可视化...")
    for i, (analyzer, name) in enumerate(analyzers):
        print(f"绘制{name}的分析图...")
        plot_triad_manifold(analyzer, f"triad_manifold_{i+1}.png")
        plot_trajectory_evolution(analyzer, f"trajectory_evolution_{i+1}.png")

    # 模型对比
    print(f"\n4. 进行模型对比...")
    comparison_results = compare_models(melody_medium, "model_comparison.png")

    print(f"\n5. 对比分析结果:")
    for result in comparison_results:
        config = result['config']
        metrics = result['metrics']
        print(f"\n{config['name']}:")
        print(f"  收敛比例: {metrics['convergence_ratio']:.3f}")
        print(f"  拓扑熵: {metrics['topological_entropy']:.3f}")
        print(f"  吸引子强度: {metrics['attractor_strength']:.3f}")

    print(f"\n分析完成！所有图像已保存到当前目录。")

if __name__ == "__main__":
    main()
