#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版中国音乐分析系统 - 完整版
包含：音程均幅、局部波动性、双方法分析、旋律动力系统
重构核心逻辑框架 - 按照新的程序逻辑框架要求构建
"""

import numpy as np
import pandas as pd
import math
import os
import glob
from typing import List, Dict, Any, Tuple

# 尝试导入可选库
try:
    import pretty_midi
    PRETTY_MIDI_AVAILABLE = True
    print("✅ pretty_midi库加载成功")
except ImportError:
    PRETTY_MIDI_AVAILABLE = False
    print("⚠️ pretty_midi库未安装，将跳过MIDI文件处理")

try:
    import matplotlib
    matplotlib.use('Agg')  # 使用非交互式后端
    import matplotlib.pyplot as plt
    VISUALIZATION_AVAILABLE = True
    print("✅ 可视化库加载成功")
except ImportError:
    VISUALIZATION_AVAILABLE = False
    print("⚠️ 可视化库未安装，将跳过图表生成")

class IntervalicAmbitusAnalyzer:
    """音程均幅 (Intervallic_Ambitus) 分析器"""

    @staticmethod
    def calculate(pitch_series: List[float]) -> float:
        """计算音程均幅 E(log i)"""
        if len(pitch_series) < 2:
            return 0.0

        intervals = [abs(pitch_series[i+1] - pitch_series[i]) for i in range(len(pitch_series)-1)]
        log_intervals = [math.log(max(interval, 1)) for interval in intervals]
        return sum(log_intervals) / len(log_intervals) if log_intervals else 0.0

class LocalVolatilityAnalyzer:
    """局部音程波动性 (Local_Volatility) 分析器"""

    @staticmethod
    def calculate_d1_rms(pitch_series: List[float]) -> float:
        """计算d1_rms（主要的局部波动性指标）"""
        if len(pitch_series) < 2:
            return 0.0

        intervals = [pitch_series[i+1] - pitch_series[i] for i in range(len(pitch_series)-1)]
        return math.sqrt(sum(interval**2 for interval in intervals) / len(intervals)) if intervals else 0.0

    @staticmethod
    def calculate_d2_rms(pitch_series: List[float]) -> float:
        """计算d2_rms（中等尺度波动性，音程曲率）"""
        if len(pitch_series) < 3:
            return 0.0

        # 计算二阶差分（加速度）
        first_diff = [pitch_series[i+1] - pitch_series[i] for i in range(len(pitch_series)-1)]
        second_diff = [first_diff[i+1] - first_diff[i] for i in range(len(first_diff)-1)]

        return math.sqrt(sum(diff**2 for diff in second_diff) / len(second_diff)) if second_diff else 0.0

    @staticmethod
    def calculate_rms_ratio(d1_rms: float, d2_rms: float) -> float:
        """计算RMS比率（波动性特征比率）"""
        if d2_rms == 0:
            return float('inf') if d1_rms > 0 else 0.0
        return d1_rms / d2_rms

    @staticmethod
    def classify_volatility_type(rms_ratio: float) -> str:
        """分类波动性类型"""
        if rms_ratio > 3.0:
            return "尖锐毛刺型"  # 高比率：快速尖锐变化
        elif rms_ratio > 1.5:
            return "混合波动型"  # 中等比率：混合特征
        elif rms_ratio > 0.5:
            return "平滑波浪型"  # 低比率：平滑波浪状
        else:
            return "极平滑型"    # 极低比率：几乎无波动

    @staticmethod
    def identify_ornament_pattern(d1_rms: float, d2_rms: float, rms_ratio: float) -> str:
        """识别装饰音模式"""
        if d1_rms > 5.0 and rms_ratio > 2.0:
            return "颤音/震音型"
        elif d1_rms > 3.0 and rms_ratio > 1.5:
            return "回音/波音型"
        elif d1_rms > 1.0 and rms_ratio < 1.0:
            return "滑音/连音型"
        else:
            return "简单进行型"

class MelodyDynamicsSystem:
    """
    旋律动力系统分析器
    基于动力系统理论分析旋律的：
    - 导数系统（速度、加速度）
    - 吸引子识别（隐形引力线）
    - 曲率张量计算
    - 多尺度时间分析
    - 系统稳定性评估
    """

    def __init__(self, time_window=0.2, attractor_threshold=3.0):
        self.time_window = time_window
        self.attractor_threshold = attractor_threshold

    def analyze_dynamics(self, pitch_series: List[float]) -> Dict[str, Any]:
        """分析旋律动力系统特征"""
        if len(pitch_series) < 3:
            return {'error': 'insufficient_data_for_dynamics_analysis'}

        try:
            pitch_array = np.array(pitch_series)

            # 1. 计算导数系统
            velocity, acceleration = self._compute_derivatives(pitch_array)

            # 2. 识别吸引子
            attractors = self._detect_attractors(pitch_array)

            # 3. 计算曲率张量
            curvature = self._calculate_curvature(velocity, acceleration)

            # 4. 分析多尺度特征
            time_scales = self._multi_scale_analysis(pitch_array)

            # 5. 评估系统稳定性
            stability = self._assess_stability(attractors, curvature)

            # 6. 计算综合动力学指标
            dynamics_metrics = self._calculate_dynamics_metrics(
                velocity, acceleration, curvature, attractors
            )

            return {
                'velocity': velocity.tolist(),
                'acceleration': acceleration.tolist(),
                'attractors': attractors,
                'curvature': curvature.tolist(),
                'time_scales': time_scales,
                'stability': stability,
                'dynamics_metrics': dynamics_metrics,
                'system_type': self._classify_system_type(dynamics_metrics)
            }

        except Exception as e:
            return {'error': f'dynamics_analysis_failed: {e}'}

    def _compute_derivatives(self, pitch_series: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """计算音高导数（速度、加速度）"""
        n = len(pitch_series)
        window_size = max(1, int(n * self.time_window))

        # 一阶导数（速度）
        velocity = np.zeros(n)
        for i in range(n):
            start = max(0, i - window_size//2)
            end = min(n, i + window_size//2 + 1)
            if end - start > 1:
                velocity[i] = np.mean(np.diff(pitch_series[start:end]))
            else:
                velocity[i] = 0.0

        # 二阶导数（加速度）
        acceleration = np.gradient(velocity)

        return velocity, acceleration

    def _detect_attractors(self, pitch_series: np.ndarray) -> Dict[str, Any]:
        """识别吸引子（隐形引力线）"""
        try:
            # 简化的吸引子检测
            pitch_counts = {}
            for pitch in pitch_series:
                rounded_pitch = round(pitch)
                pitch_counts[rounded_pitch] = pitch_counts.get(rounded_pitch, 0) + 1

            # 找到主要吸引子
            total_notes = len(pitch_series)
            attractors = []
            for pitch, count in pitch_counts.items():
                if count / total_notes > 0.1:  # 出现频率超过10%
                    attractors.append(pitch)

            # 计算到最近吸引子的距离
            distances = []
            for pitch in pitch_series:
                if attractors:
                    min_dist = min(abs(pitch - attractor) for attractor in attractors)
                    distances.append(min_dist)
                else:
                    distances.append(0.0)

            avg_distance = sum(distances) / len(distances) if distances else 0.0

            return {
                'positions': attractors,
                'distances': distances,
                'strength': len(attractors) / total_notes if total_notes > 0 else 0.0,
                'count': len(attractors),
                'avg_distance': avg_distance
            }

        except Exception as e:
            return {
                'positions': [],
                'distances': [0.0] * len(pitch_series),
                'strength': 0.0,
                'count': 0,
                'avg_distance': 0.0,
                'error': str(e)
            }

    def _calculate_curvature(self, velocity: np.ndarray, acceleration: np.ndarray) -> np.ndarray:
        """计算曲率张量"""
        curvature = np.zeros(len(velocity))

        for i in range(len(velocity)):
            v = velocity[i]
            a = acceleration[i]

            if abs(v) > 1e-5:
                curvature[i] = abs(a) / (abs(v)**2)
            else:
                curvature[i] = 0.0

        return curvature

    def _multi_scale_analysis(self, pitch_series: np.ndarray) -> Dict[str, Dict]:
        """多尺度时间分析"""
        scales = {
            'micro_scale': self._analyze_scale(pitch_series, window=0.05),
            'meso_scale': self._analyze_scale(pitch_series, window=0.3),
            'macro_scale': self._analyze_scale(pitch_series, window=0.7)
        }
        return scales

    def _analyze_scale(self, pitch_series: np.ndarray, window: float) -> Dict:
        """特定时间尺度的分析"""
        n = len(pitch_series)
        window_size = max(1, int(n * window))

        features = []
        for i in range(n):
            start = max(0, i - window_size//2)
            end = min(n, i + window_size//2 + 1)
            segment = pitch_series[start:end]

            if len(segment) > 1:
                diff_segment = np.diff(segment)
                zcr = np.sum(np.diff(np.sign(diff_segment)) != 0) / len(diff_segment) if len(diff_segment) > 1 else 0.0
                envelope = np.max(segment) - np.min(segment)
                mean_val = np.mean(segment)
                dynamic_range = envelope / (abs(mean_val) + 1e-5)
                perceived_variation = zcr * envelope
                local_complexity = np.std(diff_segment) if len(diff_segment) > 1 else 0.0

                features.append({
                    'position': i,
                    'zero_crossing_rate': float(zcr),
                    'envelope_depth': float(envelope),
                    'dynamic_range': float(dynamic_range),
                    'perceived_variation': float(perceived_variation),
                    'local_complexity': float(local_complexity)
                })

        # 返回汇总统计
        if features:
            return {
                'avg_zero_crossing_rate': np.mean([f['zero_crossing_rate'] for f in features]),
                'avg_envelope_depth': np.mean([f['envelope_depth'] for f in features]),
                'avg_perceived_variation': np.mean([f['perceived_variation'] for f in features]),
                'avg_local_complexity': np.mean([f['local_complexity'] for f in features])
            }
        else:
            return {
                'avg_zero_crossing_rate': 0.0,
                'avg_envelope_depth': 0.0,
                'avg_perceived_variation': 0.0,
                'avg_local_complexity': 0.0
            }

    def _assess_stability(self, attractors: Dict, curvature: np.ndarray) -> str:
        """评估系统稳定性"""
        avg_distance = attractors.get('avg_distance', 0.0)
        avg_curvature = np.mean(curvature)
        curvature_variance = np.var(curvature)
        attractor_count = attractors.get('count', 0)

        if curvature_variance > 0.5 and avg_curvature > 0.1:
            return "高度不稳定（脉冲式）"
        elif avg_distance < 2.0 and curvature_variance < 0.1 and attractor_count > 0:
            return "高度稳定（吸引子主导）"
        elif avg_curvature < 0.05:
            return "稳定（低曲率）"
        elif attractor_count == 0:
            return "无吸引子（自由运动）"
        else:
            return "中等稳定"

    def _calculate_dynamics_metrics(self, velocity: np.ndarray, acceleration: np.ndarray,
                                  curvature: np.ndarray, attractors: Dict) -> Dict[str, float]:
        """计算综合动力学指标"""
        return {
            'mean_velocity': float(np.mean(np.abs(velocity))),
            'velocity_variance': float(np.var(velocity)),
            'max_velocity': float(np.max(np.abs(velocity))),
            'mean_acceleration': float(np.mean(np.abs(acceleration))),
            'acceleration_variance': float(np.var(acceleration)),
            'max_acceleration': float(np.max(np.abs(acceleration))),
            'mean_curvature': float(np.mean(curvature)),
            'curvature_variance': float(np.var(curvature)),
            'max_curvature': float(np.max(curvature)),
            'attractor_count': attractors.get('count', 0),
            'attractor_strength': attractors.get('strength', 0.0),
            'mean_attractor_distance': attractors.get('avg_distance', 0.0),
            'system_energy': float(np.mean(velocity**2 + acceleration**2)),
            'phase_space_volume': float(np.std(velocity) * np.std(acceleration)),
            'lyapunov_proxy': float(np.mean(np.abs(np.diff(curvature))))
        }

    def _classify_system_type(self, metrics: Dict[str, float]) -> str:
        """分类动力系统类型"""
        energy = metrics['system_energy']
        lyapunov = metrics['lyapunov_proxy']
        attractor_count = metrics['attractor_count']

        if lyapunov > 0.1 and energy > 10.0:
            return "混沌系统"
        elif attractor_count > 2 and metrics['attractor_strength'] > 0.3:
            return "多吸引子系统"
        elif attractor_count == 1 and metrics['mean_attractor_distance'] < 2.0:
            return "单吸引子系统"
        elif energy < 1.0 and lyapunov < 0.01:
            return "准静态系统"
        else:
            return "复杂动力系统"



class DualMethodAnalyzer:
    """双方法分析器"""
    
    @staticmethod
    def calculate_orthogonality(method1_values, method2_values):
        """计算正交性"""
        if len(method1_values) != len(method2_values) or len(method1_values) < 2:
            return {'correlation': 0.0, 'assessment': '数据不足'}
        
        # 简化的相关性计算
        mean1 = sum(method1_values) / len(method1_values)
        mean2 = sum(method2_values) / len(method2_values)
        
        numerator = sum((v1 - mean1) * (v2 - mean2) for v1, v2 in zip(method1_values, method2_values))
        
        sum_sq1 = sum((v1 - mean1)**2 for v1 in method1_values)
        sum_sq2 = sum((v2 - mean2)**2 for v2 in method2_values)
        
        if sum_sq1 == 0 or sum_sq2 == 0:
            correlation = 0.0
        else:
            correlation = numerator / math.sqrt(sum_sq1 * sum_sq2)
        
        # 正交性评估
        if abs(correlation) < 0.3:
            assessment = "高度正交"
        elif abs(correlation) < 0.6:
            assessment = "中度相关"
        else:
            assessment = "强相关"
        
        return {
            'correlation': correlation,
            'assessment': assessment
        }


class EnhancedChineseMusicAnalyzer:
    """增强版中国音乐分析器"""
    
    def __init__(self):
        self.intervallic_ambitus_analyzer = IntervalicAmbitusAnalyzer()
        self.local_volatility_analyzer = LocalVolatilityAnalyzer()
        self.melody_dynamics_analyzer = MelodyDynamicsSystem()
        self.dual_method_analyzer = DualMethodAnalyzer()
        self.pitch_series = None
        
    def load_midi_file(self, file_path):
        """加载MIDI文件"""
        if not PRETTY_MIDI_AVAILABLE:
            print(f"⚠️ pretty_midi库未安装，跳过MIDI文件: {file_path}")
            return False
        
        try:
            midi_data = pretty_midi.PrettyMIDI(file_path)
            pitch_series = []
            for instrument in midi_data.instruments:
                if not instrument.is_drum:
                    for note in instrument.notes:
                        pitch_series.append(note.pitch)
            
            if pitch_series:
                self.pitch_series = pd.Series(pitch_series)
                print(f"   从MIDI文件提取了 {len(pitch_series)} 个音符")
                return True
            else:
                print(f"   ⚠️ MIDI文件中未找到音符数据")
                return False
                
        except Exception as e:
            print(f"   ❌ MIDI文件处理失败: {e}")
            return False
    
    def load_csv_file(self, file_path):
        """加载CSV文件"""
        try:
            df = pd.read_csv(file_path)
            
            # 尝试不同的列名
            pitch_columns = ['pitch', 'Pitch', 'PITCH', 'note', 'Note', 'midi_note']
            pitch_column = None
            
            for col in pitch_columns:
                if col in df.columns:
                    pitch_column = col
                    break
            
            if pitch_column:
                self.pitch_series = df[pitch_column].dropna()
                print(f"   从CSV文件提取了 {len(self.pitch_series)} 个音符")
                return True
            else:
                print(f"   ⚠️ CSV文件中未找到音高列")
                return False
                
        except Exception as e:
            print(f"   ❌ CSV文件处理失败: {e}")
            return False
    
    def analyze_single_work(self, file_path):
        """分析单个作品"""
        print(f"🎵 分析文件: {os.path.basename(file_path)}")
        
        # 如果是测试数据，直接使用已设置的pitch_series
        if file_path.startswith('test_') and self.pitch_series is not None:
            pitch_list = self.pitch_series.tolist()
        else:
            # 加载文件
            success = False
            if file_path.endswith('.mid') or file_path.endswith('.midi'):
                success = self.load_midi_file(file_path)
            elif file_path.endswith('.csv'):
                success = self.load_csv_file(file_path)
            
            if not success or self.pitch_series is None or len(self.pitch_series) < 3:
                print(f"   ❌ 文件加载失败或数据不足")
                return None
            
            pitch_list = self.pitch_series.tolist()
        
        # 1. 音程均幅分析
        intervallic_ambitus = self.intervallic_ambitus_analyzer.calculate(pitch_list)
        
        # 2. 局部波动性分析
        d1_rms = self.local_volatility_analyzer.calculate_d1_rms(pitch_list)
        d2_rms = self.local_volatility_analyzer.calculate_d2_rms(pitch_list)
        rms_ratio = self.local_volatility_analyzer.calculate_rms_ratio(d1_rms, d2_rms)
        volatility_type = self.local_volatility_analyzer.classify_volatility_type(rms_ratio)
        ornament_pattern = self.local_volatility_analyzer.identify_ornament_pattern(d1_rms, d2_rms, rms_ratio)
        
        # 3. 旋律动力系统分析
        dynamics_result = self.melody_dynamics_analyzer.analyze_dynamics(pitch_list)
        
        # 打印分析结果
        print(f"      音程均幅 (Intervallic_Ambitus): {intervallic_ambitus:.4f}")
        print(f"      局部音程波动性 (Local_Volatility): {d1_rms:.4f}")
        print(f"      中等尺度波动性 (d2_rms): {d2_rms:.4f}")
        print(f"      波动性特征比率: {rms_ratio:.4f}")
        print(f"      波动性类型: {volatility_type}")
        print(f"      装饰音模式: {ornament_pattern}")
        
        if 'error' not in dynamics_result:
            metrics = dynamics_result['dynamics_metrics']
            print(f"      动力系统类型: {dynamics_result['system_type']}")
            print(f"      系统稳定性: {dynamics_result['stability']}")
            print(f"      吸引子数量: {metrics['attractor_count']}")
            print(f"      系统能量: {metrics['system_energy']:.4f}")
            print(f"      平均曲率: {metrics['mean_curvature']:.4f}")
            print(f"      李雅普诺夫代理: {metrics['lyapunov_proxy']:.4f}")
        else:
            print(f"      动力系统分析: 失败 - {dynamics_result['error']}")
        
        print(f"   ✅ 分析完成")
        
        return {
            'file_path': file_path,
            'note_count': len(pitch_list),
            'intervallic_ambitus': intervallic_ambitus,
            'local_volatility': d1_rms,
            'local_volatility_details': {
                'd1_rms': d1_rms,
                'd2_rms': d2_rms,
                'rms_ratio': rms_ratio,
                'volatility_type': volatility_type,
                'ornament_pattern': ornament_pattern
            },
            'dynamics_analysis': dynamics_result
        }
    
    def analyze_all_works(self):
        """分析所有作品"""
        print("🎼 增强版中国音乐分析系统")
        print("=" * 80)
        
        # 查找音乐文件
        music_files = []
        search_patterns = ['*.mid', '*.midi', '*.csv']
        
        for pattern in search_patterns:
            files = glob.glob(pattern)
            music_files.extend(files)
        
        if not music_files or len([f for f in music_files if f.endswith((".mid", ".midi"))]) == 0:
            print("❌ 未找到音乐文件，使用测试数据进行演示")
            self.run_test_analysis()
            return
        
        print(f"📁 找到 {len(music_files)} 个音乐文件")
        
        # 分析每个文件
        per_work_results = []
        for file_path in music_files:
            result = self.analyze_single_work(file_path)
            if result:
                per_work_results.append(result)
        
        if per_work_results:
            print(f"\n📊 成功分析了 {len(per_work_results)} 个作品")
            self.print_summary_statistics(per_work_results)
        else:
            print("❌ 没有成功分析的作品")
    
    def run_test_analysis(self):
        """运行测试分析"""
        print("�� 运行测试分析")
        
        test_melodies = [
            {
                'name': '平滑音阶',
                'pitches': [60, 62, 64, 65, 67, 69, 71, 72, 74, 76, 77, 79, 81, 83, 84]
            },
            {
                'name': '跳跃旋律',
                'pitches': [60, 72, 48, 75, 45, 78, 42, 81, 39, 84, 36, 87, 33, 90, 30]
            },
            {
                'name': '三音组模式',
                'pitches': [60, 62, 61, 63, 62, 64, 63, 65, 64, 66, 65, 67, 66, 68, 67]
            },
            {
                'name': '吸引子主导',
                'pitches': [60, 61, 60, 62, 60, 61, 60, 63, 60, 61, 60, 62, 60, 64, 60]
            },
            {
                'name': '混沌模式',
                'pitches': [60, 67, 55, 72, 48, 69, 52, 74, 46, 71, 49, 76, 44, 73, 51]
            }
        ]
        
        per_work_results = []
        for melody in test_melodies:
            print(f"\n🎵 分析测试旋律: {melody['name']}")
            self.pitch_series = pd.Series(melody['pitches'])
            
            result = self.analyze_single_work(f"test_{melody['name']}.mid")
            if result:
                per_work_results.append(result)
        
        if per_work_results:
            print(f"\n📊 使用 {len(per_work_results)} 个测试样本进行分析")
            self.print_summary_statistics(per_work_results)
    
    def print_summary_statistics(self, per_work_results):
        """打印摘要统计"""
        print("\n" + "="*60)
        print("🎼 平滑度收敛分析摘要报告")
        print("="*60)
        
        # 提取数据
        intervallic_ambitus_values = [r['intervallic_ambitus'] for r in per_work_results]
        local_volatility_values = [r['local_volatility'] for r in per_work_results]
        
        # 双方法正交性分析
        orthogonality = self.dual_method_analyzer.calculate_orthogonality(
            intervallic_ambitus_values, local_volatility_values
        )
        
        print(f"📊 基础统计:")
        print(f"   分析作品数: {len(per_work_results)} 首")
        print(f"   双方法正交性: {orthogonality['assessment']}")
        
        print(f"\n📈 音程均幅 (Intervallic_Ambitus) 统计:")
        print(f"   均值: {np.mean(intervallic_ambitus_values):.4f}")
        print(f"   标准差: {np.std(intervallic_ambitus_values):.4f}")
        print(f"   范围: {np.min(intervallic_ambitus_values):.4f} ~ {np.max(intervallic_ambitus_values):.4f}")
        print(f"   中位数: {np.median(intervallic_ambitus_values):.4f}")
        
        print(f"\n📈 局部音程波动性 (Local_Volatility) 统计:")
        print(f"   均值: {np.mean(local_volatility_values):.4f}")
        print(f"   标准差: {np.std(local_volatility_values):.4f}")
        print(f"   范围: {np.min(local_volatility_values):.4f} ~ {np.max(local_volatility_values):.4f}")
        print(f"   中位数: {np.median(local_volatility_values):.4f}")
        
        # 动力系统统计
        system_energies = []
        system_types = []
        stabilities = []
        
        for r in per_work_results:
            dynamics = r.get('dynamics_analysis', {})
            if 'error' not in dynamics:
                metrics = dynamics.get('dynamics_metrics', {})
                system_energies.append(metrics.get('system_energy', 0.0))
                system_types.append(dynamics.get('system_type', '未知'))
                stabilities.append(dynamics.get('stability', '未知'))
        
        if system_energies:
            print(f"\n🌊 旋律动力系统统计:")
            print(f"   平均系统能量: {np.mean(system_energies):.4f}")
            print(f"   系统能量范围: {np.min(system_energies):.4f} ~ {np.max(system_energies):.4f}")
            
            # 系统类型分布
            type_counts = {}
            for sys_type in system_types:
                type_counts[sys_type] = type_counts.get(sys_type, 0) + 1
            
            print(f"   系统类型分布:")
            for sys_type, count in type_counts.items():
                percentage = (count / len(system_types)) * 100
                print(f"     {sys_type}: {count} 首 ({percentage:.1f}%)")
            
            # 稳定性分布
            stability_counts = {}
            for stability in stabilities:
                stability_counts[stability] = stability_counts.get(stability, 0) + 1
            
            print(f"   稳定性分布:")
            for stability, count in stability_counts.items():
                percentage = (count / len(stabilities)) * 100
                print(f"     {stability}: {count} 首 ({percentage:.1f}%)")
        
        print(f"\n✅ 核心结论:")
        print(f"   ✅ 双方法分析: {orthogonality['assessment']}")
        print(f"   ✅ 相关系数: {orthogonality['correlation']:.4f}")
        
        if abs(orthogonality['correlation']) < 0.3:
            print(f"   ✅ 两个方法家族高度正交")
        elif abs(orthogonality['correlation']) < 0.6:
            print(f"   ⚠️ 两个方法家族中度相关")
        else:
            print(f"   ⚠️ 两个方法家族强相关，需要进一步分析")
        
        print(f"\n🌊 动力系统洞察:")
        if system_types:
            dominant_type = max(type_counts, key=type_counts.get)
            dominant_percentage = (type_counts[dominant_type] / len(system_types)) * 100
            print(f"   主导系统类型: {dominant_type} ({dominant_percentage:.1f}%)")
            
            stable_count = sum(count for stability, count in stability_counts.items() 
                             if '稳定' in stability and '不稳定' not in stability)
            stable_percentage = (stable_count / len(stabilities)) * 100
            print(f"   稳定系统比例: {stable_percentage:.1f}%")
        
        print(f"\n🎯 理论意义:")
        print(f"  • 音程均幅 (Intervallic_Ambitus): 测量音程大小的对数平均")
        print(f"  • 局部波动性 (Local_Volatility): 测量绝对不规则性（RMS）")
        print(f"  • 旋律动力系统: 基于动力系统理论的稳定性分析")
        print(f"  • 为'三音组构成中国传统音乐风格'提供数学支撑")
        
        print(f"\n🎼 增强版数学特征验证完成！")
        print(f"✅ 音程均幅 (Intervallic_Ambitus) 分析")
        print(f"✅ 局部音程波动性 (Local_Volatility) 分析") 
        print(f"✅ 双方法家族正交性验证")
        print(f"✅ 旋律动力系统分析")
        print(f"下一步：基于动力系统理论的深度特征挖掘")


def main():
    """主函数"""
    try:
        analyzer = EnhancedChineseMusicAnalyzer()
        analyzer.analyze_all_works()
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        print("🧪 建议检查文件路径或使用测试数据")


if __name__ == "__main__":
    main()
