#!/usr/bin/env python3
"""
测试基础分析功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.append('.')

# 导入分析器
from enhanced_music_analysis_complete import EnhancedChineseMusicAnalyzer

def test_basic_analysis():
    """测试基础分析功能"""
    print("🧪 测试基础分析功能")
    print("="*50)
    
    try:
        # 创建分析器
        analyzer = EnhancedChineseMusicAnalyzer()
        
        # 运行分析
        analyzer.analyze_all_works()
        
        print("✅ 基础分析测试成功")
        
    except Exception as e:
        print(f"❌ 基础分析测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_basic_analysis()
