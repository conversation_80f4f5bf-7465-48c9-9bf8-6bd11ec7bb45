#!/usr/bin/env python3
"""
测试修复后的真实MIDI文件分析
"""

import sys
import os

# 添加当前目录到路径
sys.path.append('.')

# 导入分析器
from enhanced_music_analysis_complete import main_comprehensive_analysis

def test_real_midi_fixed():
    """测试修复后的真实MIDI文件分析"""
    print("🎼 测试修复后的真实MIDI文件分析")
    print("="*60)
    
    try:
        # 直接调用综合分析主函数
        main_comprehensive_analysis()
        
        print("\n✅ 真实MIDI文件分析测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 真实MIDI文件分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_real_midi_fixed()
    if success:
        print("\n🎉 真实MIDI文件分析修复成功！")
    else:
        print("\n❌ 真实MIDI文件分析仍有问题")
