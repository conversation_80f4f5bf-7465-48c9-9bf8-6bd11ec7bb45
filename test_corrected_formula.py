#!/usr/bin/env python3
"""
测试修正后的吸引子强度计算公式
验证新公式的效果和阈值设定
"""

import sys
import os
import numpy as np

# 添加当前目录到路径
sys.path.append('.')

def test_corrected_formula():
    """测试修正后的吸引子强度公式"""
    print("🔧 测试修正后的吸引子强度计算公式")
    print("公式: 强度 = (主导权重/吸引子数量) × 音高跨度(全音) × 修正集中度指数")
    print("修正集中度指数 = 0.1 + 0.9 × (1 - 权重熵/log(吸引子数量))")
    print("="*80)
    
    try:
        # 导入修正后的统一分析器
        from unified_topological_analysis import UnifiedTopologicalAnalyzer
        
        # 创建分析器
        analyzer = UnifiedTopologicalAnalyzer()
        
        print("✅ 统一拓扑分析器创建成功")
        
        # 测试不同的吸引子配置
        test_cases = [
            {
                'name': '单一强吸引子',
                'attractors': [(60, 0.8), (67, 0.2)],
                'expected_category': '高强度',
                'description': '一个主导吸引子，权重集中'
            },
            {
                'name': '多个平衡吸引子',
                'attractors': [(60, 0.25), (64, 0.25), (67, 0.25), (72, 0.25)],
                'expected_category': '中等强度',
                'description': '四个平衡的吸引子，权重分散'
            },
            {
                'name': '分散弱吸引子',
                'attractors': [(60, 0.15), (65, 0.15), (70, 0.15), (75, 0.15), (80, 0.15), (85, 0.25)],
                'expected_category': '低强度',
                'description': '六个分散的吸引子，覆盖范围大'
            },
            {
                'name': '紧密强吸引子',
                'attractors': [(60, 0.6), (62, 0.4)],
                'expected_category': '高强度',
                'description': '两个紧密的强吸引子'
            },
            {
                'name': '极端分散型',
                'attractors': [(48, 0.2), (60, 0.2), (72, 0.2), (84, 0.2), (96, 0.2)],
                'expected_category': '低强度',
                'description': '五个吸引子跨越四个八度'
            },
            {
                'name': '中等集中型',
                'attractors': [(60, 0.4), (64, 0.3), (67, 0.3)],
                'expected_category': '中等强度',
                'description': '三个吸引子，适度集中'
            }
        ]
        
        print(f"\n🧪 测试不同吸引子配置的修正强度计算:")
        print(f"{'配置名称':<15} {'修正强度':<12} {'修正集中度':<12} {'分类':<10} {'预期':<10} {'验证'}")
        print("-" * 90)
        
        results = []
        
        for case in test_cases:
            attractors = case['attractors']
            
            # 计算修正的吸引子强度
            corrected_strength = analyzer.calculate_improved_attractor_strength(attractors)
            
            # 计算修正集中度指数用于显示
            positions = [pos for pos, weight in attractors]
            weights = [weight for pos, weight in attractors]
            n_attractors = len(attractors)
            
            weight_entropy = -sum(w * np.log(w + 1e-10) for w in weights if w > 0)
            max_entropy = np.log(n_attractors) if n_attractors > 1 else 1.0
            raw_concentration_index = 1 - weight_entropy / max_entropy if max_entropy > 0 else 1.0
            corrected_concentration_index = 0.1 + 0.9 * raw_concentration_index
            
            # 分类强度（使用修正阈值）
            if corrected_strength >= 3.0:
                actual_category = "高强度"
            elif corrected_strength >= 1.0:
                actual_category = "中等强度"
            else:
                actual_category = "低强度"
            
            # 验证是否符合预期
            validation = "✅" if actual_category == case['expected_category'] else "⚠️"
            
            print(f"{case['name']:<15} {corrected_strength:<12.4f} {corrected_concentration_index:<12.4f} {actual_category:<10} {case['expected_category']:<10} {validation}")
            
            results.append({
                'name': case['name'],
                'attractors': attractors,
                'corrected_strength': corrected_strength,
                'corrected_concentration_index': corrected_concentration_index,
                'actual_category': actual_category,
                'expected_category': case['expected_category'],
                'validation_passed': actual_category == case['expected_category'],
                'description': case['description']
            })
        
        # 分析测试结果
        print(f"\n📊 修正公式测试结果分析:")
        
        passed_tests = sum(1 for r in results if r['validation_passed'])
        total_tests = len(results)
        accuracy = passed_tests / total_tests * 100
        
        print(f"   验证通过: {passed_tests}/{total_tests} ({accuracy:.1f}%)")
        
        # 强度范围分析
        strengths = [r['corrected_strength'] for r in results]
        concentrations = [r['corrected_concentration_index'] for r in results]
        
        print(f"   修正强度范围: {min(strengths):.4f} - {max(strengths):.4f} 全音/个数")
        print(f"   平均强度: {np.mean(strengths):.4f}")
        print(f"   标准差: {np.std(strengths):.4f}")
        print(f"   修正集中度范围: {min(concentrations):.4f} - {max(concentrations):.4f}")
        
        # 验证修正集中度指数范围
        print(f"\n🔍 修正集中度指数验证:")
        print(f"   所有值都在0.1-1.0范围内: {'✅' if all(0.1 <= c <= 1.0 for c in concentrations) else '❌'}")
        print(f"   最小值: {min(concentrations):.4f} (应该≥0.1)")
        print(f"   最大值: {max(concentrations):.4f} (应该≤1.0)")
        
        # 详细分析每个案例
        print(f"\n🔍 详细案例分析:")
        
        for r in results:
            print(f"\n   {r['name']}:")
            print(f"     描述: {r['description']}")
            print(f"     吸引子配置: {r['attractors']}")
            print(f"     修正强度: {r['corrected_strength']:.4f} 全音/个数")
            print(f"     修正集中度指数: {r['corrected_concentration_index']:.4f}")
            print(f"     分类: {r['actual_category']} (预期: {r['expected_category']})")
            
            # 计算组件分析
            positions = [pos for pos, weight in r['attractors']]
            weights = [weight for pos, weight in r['attractors']]
            n_attractors = len(r['attractors'])
            
            dominant_weight = max(weights)
            pitch_span_semitones = max(positions) - min(positions) + 1 if len(positions) > 1 else 1
            pitch_span_whole_tones = pitch_span_semitones / 2.0
            
            print(f"     组件分析:")
            print(f"       主导权重: {dominant_weight:.3f}")
            print(f"       吸引子数量: {n_attractors}")
            print(f"       音高跨度: {pitch_span_whole_tones:.1f} 全音")
            print(f"       修正集中度指数: {r['corrected_concentration_index']:.3f}")
            print(f"       计算: ({dominant_weight:.3f}/{n_attractors}) × {pitch_span_whole_tones:.1f} × {r['corrected_concentration_index']:.3f} = {r['corrected_strength']:.4f}")
        
        return accuracy >= 80  # 80%以上准确率认为成功
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_old_vs_corrected():
    """对比原始公式、改进公式和修正公式"""
    print(f"\n" + "="*80)
    print("🔄 公式演进对比：原始 → 改进 → 修正")
    print("="*80)
    
    print("📊 公式演进:")
    print("   1️⃣ 原始公式:")
    print("      强度 = 主导权重 × √吸引子数量 × (1 + 位置分散度) × (1 + 权重集中度)")
    print("      单位: [√个数 × 半音] (无物理意义)")
    print("      问题: 单位复杂，√个数缺乏依据")
    
    print("\n   2️⃣ 改进公式:")
    print("      强度 = (主导权重/吸引子数量) × 音高跨度(全音) × 集中度指数")
    print("      单位: [全音/个数] (明确物理意义)")
    print("      问题: 权重完全平衡时集中度指数为0，导致强度为0")
    
    print("\n   3️⃣ 修正公式:")
    print("      强度 = (主导权重/吸引子数量) × 音高跨度(全音) × 修正集中度指数")
    print("      修正集中度指数 = 0.1 + 0.9 × (1 - 权重熵/log(吸引子数量))")
    print("      单位: [全音/个数] (明确物理意义)")
    print("      优势: 确保集中度指数在0.1-1.0范围内，避免强度为0")
    
    # 数值对比
    test_case = [(60, 0.25), (64, 0.25), (67, 0.25), (72, 0.25)]  # 完全平衡的权重
    
    print(f"\n📊 数值对比 (完全平衡权重案例):")
    print(f"   吸引子配置: {test_case}")
    
    # 模拟计算
    positions = [pos for pos, weight in test_case]
    weights = [weight for pos, weight in test_case]
    n_attractors = len(test_case)
    dominant_weight = max(weights)
    pitch_span_whole_tones = (max(positions) - min(positions) + 1) / 2.0
    
    # 原始公式 (模拟)
    original_strength = dominant_weight * np.sqrt(n_attractors) * (1 + pitch_span_whole_tones * 2) * (1 + 0.25)
    
    # 改进公式
    weight_entropy = -sum(w * np.log(w + 1e-10) for w in weights if w > 0)
    max_entropy = np.log(n_attractors)
    raw_concentration_index = 1 - weight_entropy / max_entropy
    improved_strength = (dominant_weight / n_attractors) * pitch_span_whole_tones * raw_concentration_index
    
    # 修正公式
    corrected_concentration_index = 0.1 + 0.9 * raw_concentration_index
    corrected_strength = (dominant_weight / n_attractors) * pitch_span_whole_tones * corrected_concentration_index
    
    print(f"   原始强度: {original_strength:.4f} [√个数×半音]")
    print(f"   改进强度: {improved_strength:.4f} [全音/个数] (问题：为0)")
    print(f"   修正强度: {corrected_strength:.4f} [全音/个数] (解决：>0)")
    
    print(f"\n✅ 修正效果:")
    print(f"   • 保持了改进公式的物理意义")
    print(f"   • 解决了权重平衡时强度为0的问题")
    print(f"   • 确保集中度指数在合理范围内")
    print(f"   • 提供了稳定可靠的强度计算")

if __name__ == "__main__":
    print("🔧 修正后的吸引子强度公式测试")
    print("验证修正集中度指数的效果")
    
    # 1. 测试修正公式
    formula_success = test_corrected_formula()
    
    # 2. 对比分析
    compare_old_vs_corrected()
    
    if formula_success:
        print(f"\n🎉 修正公式测试成功！")
        print(f"✅ 修正集中度指数有效解决了权重平衡问题")
        print(f"💪 强度计算稳定可靠，具有明确的物理意义")
        print(f"🎼 单位为全音/个数，符合中国传统音乐理论")
        print(f"📊 阈值重新校准：高强度≥3.0，中等强度1.0-3.0，低强度<1.0")
    else:
        print(f"\n⚠️ 修正公式需要进一步优化")
        print(f"   - 检查阈值设定是否合理")
        print(f"   - 验证修正集中度指数的计算")
