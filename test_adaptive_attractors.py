#!/usr/bin/env python3
"""
测试自适应吸引子数量选择
验证BIC/AIC模型选择是否正常工作
"""

import sys
import os
import numpy as np

# 添加当前目录到路径
sys.path.append('.')

# 导入分析器
from topological_melody_core import TopologicalMelodyAnalyzer

def test_adaptive_attractors():
    """测试自适应吸引子数量选择"""
    print("🧪 测试自适应吸引子数量选择")
    print("="*60)
    
    # 创建不同特征的测试旋律
    test_melodies = [
        {
            'name': '单一吸引子（重复音符）',
            'pitches': [60] * 10 + [61] * 2 + [60] * 8,  # 主要围绕60
            'expected_attractors': 1
        },
        {
            'name': '双吸引子（两个中心）',
            'pitches': [60, 61, 60, 62, 60] * 4 + [72, 73, 72, 74, 72] * 4,  # 两个明显的中心
            'expected_attractors': 2
        },
        {
            'name': '多吸引子（复杂模式）',
            'pitches': [60, 61, 60] * 3 + [67, 68, 67] * 3 + [74, 75, 74] * 3 + [81, 82, 81] * 3,  # 四个中心
            'expected_attractors': 4
        },
        {
            'name': '平滑音阶（连续变化）',
            'pitches': list(range(60, 85)),  # 连续音阶，应该选择较少的吸引子
            'expected_attractors': 2
        },
        {
            'name': '随机跳跃（噪声模式）',
            'pitches': [60, 80, 45, 90, 30, 85, 40, 95, 35, 88, 42, 92, 38, 87],  # 随机跳跃
            'expected_attractors': 3
        }
    ]
    
    # 创建分析器
    analyzer = TopologicalMelodyAnalyzer(kernel_width=3.0, max_attractors=8, min_attractors=1)
    
    results = []
    
    for melody in test_melodies:
        print(f"\n🎵 测试旋律: {melody['name']}")
        print(f"   音符序列长度: {len(melody['pitches'])}")
        print(f"   预期吸引子数量: {melody['expected_attractors']}")
        
        try:
            # 分析旋律
            result = analyzer.analyze_melody(melody['pitches'])
            
            if result:
                # 获取实际选择的吸引子数量
                actual_attractors = len(result['potential_field']['attractor_points'])
                optimal_k = result['potential_field']['optimal_n_attractors']
                model_selection = result['potential_field']['model_selection_results']
                
                print(f"   ✅ 分析成功")
                print(f"   实际吸引子数量: {actual_attractors}")
                print(f"   BIC最优k值: {optimal_k}")
                print(f"   测试的k值范围: {model_selection.get('tested_k_values', [])}")
                print(f"   最佳BIC分数: {model_selection.get('best_bic', 'N/A'):.2f}")
                
                # 显示吸引子位置
                attractors = result['potential_field']['attractor_points']
                print(f"   吸引子位置: {[f'{pos:.1f}(权重:{weight:.3f})' for pos, weight in attractors]}")
                
                # 验证结果合理性
                is_reasonable = 1 <= actual_attractors <= len(set(melody['pitches']))
                print(f"   结果合理性: {'✅' if is_reasonable else '❌'}")
                
                results.append({
                    'name': melody['name'],
                    'expected': melody['expected_attractors'],
                    'actual': actual_attractors,
                    'optimal_k': optimal_k,
                    'reasonable': is_reasonable,
                    'model_selection': model_selection
                })
                
            else:
                print(f"   ❌ 分析失败")
                
        except Exception as e:
            print(f"   ❌ 分析出错: {e}")
            import traceback
            traceback.print_exc()
    
    # 总结测试结果
    print(f"\n" + "="*60)
    print("🎯 测试结果总结")
    print("="*60)
    
    if results:
        print(f"成功测试: {len(results)}/{len(test_melodies)} 个旋律")
        
        # 统计吸引子数量分布
        attractor_counts = [r['actual'] for r in results]
        unique_counts = list(set(attractor_counts))
        unique_counts.sort()
        
        print(f"\n📊 吸引子数量分布:")
        for count in unique_counts:
            freq = attractor_counts.count(count)
            print(f"   {count}个吸引子: {freq} 首旋律")
        
        print(f"\n📈 统计信息:")
        print(f"   平均吸引子数量: {np.mean(attractor_counts):.2f}")
        print(f"   吸引子数量范围: {min(attractor_counts)} ~ {max(attractor_counts)}")
        print(f"   标准差: {np.std(attractor_counts):.2f}")
        
        # 验证是否消除了固定数量问题
        all_same = len(set(attractor_counts)) == 1
        if all_same:
            print(f"   ⚠️ 警告: 所有旋律都有相同的吸引子数量 ({attractor_counts[0]})，可能仍存在问题")
        else:
            print(f"   ✅ 成功: 不同旋律有不同的吸引子数量，自适应选择正常工作")
        
        # 检查合理性
        reasonable_count = sum(1 for r in results if r['reasonable'])
        print(f"   合理结果比例: {reasonable_count}/{len(results)} ({reasonable_count/len(results)*100:.1f}%)")
        
        return len(set(attractor_counts)) > 1  # 如果有不同的吸引子数量，说明修复成功
    else:
        print("❌ 没有成功的测试结果")
        return False

if __name__ == "__main__":
    success = test_adaptive_attractors()
    if success:
        print("\n🎉 自适应吸引子数量选择测试成功！")
        print("✅ 已成功修复固定吸引子数量的问题")
    else:
        print("\n❌ 测试失败，需要进一步调试")
