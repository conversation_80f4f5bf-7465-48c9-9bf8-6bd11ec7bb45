#!/usr/bin/env python3
"""
模型对比分析程序
比较原有的增强音乐分析器与新的拓扑旋律分析器
"""

import numpy as np
import json
from topological_melody_core import TopologicalMelodyAnalyzer, generate_test_melody

def load_original_analyzer():
    """加载原有的分析器（简化版本，避免依赖问题）"""
    class OriginalAnalyzer:
        def __init__(self):
            self.results = None
        
        def analyze_melody(self, pitch_series):
            """简化的原有分析方法"""
            # 基本统计
            mean_pitch = np.mean(pitch_series)
            std_pitch = np.std(pitch_series)
            pitch_range = max(pitch_series) - min(pitch_series)
            
            # 简单的吸引子识别（基于频率统计）
            hist, bin_edges = np.histogram(pitch_series, bins=10)
            max_freq_idx = np.argmax(hist)
            main_attractor = (bin_edges[max_freq_idx] + bin_edges[max_freq_idx + 1]) / 2
            
            # 计算向吸引子的回归
            distances = [abs(p - main_attractor) for p in pitch_series]
            mean_distance = np.mean(distances)
            
            # 简单的三音组分析
            triads = []
            for i in range(len(pitch_series) - 2):
                triad = pitch_series[i:i+3]
                triads.append({
                    'mean': np.mean(triad),
                    'std': np.std(triad),
                    'range': max(triad) - min(triad)
                })
            
            triad_complexity = np.mean([t['std'] for t in triads])
            
            self.results = {
                'basic_stats': {
                    'mean_pitch': mean_pitch,
                    'std_pitch': std_pitch,
                    'pitch_range': pitch_range
                },
                'attractor_analysis': {
                    'main_attractor': main_attractor,
                    'mean_distance': mean_distance,
                    'attraction_strength': 1.0 / (mean_distance + 1e-8)
                },
                'triad_analysis': {
                    'triad_count': len(triads),
                    'triad_complexity': triad_complexity
                }
            }
            
            return self.results
    
    return OriginalAnalyzer()

def compare_analyzers(pitch_series, melody_name):
    """比较两种分析器的结果"""
    print(f"\n{'='*80}")
    print(f"对比分析: {melody_name}")
    print(f"{'='*80}")
    
    # 原有分析器
    print("\n1. 运行原有分析器...")
    original_analyzer = load_original_analyzer()
    original_results = original_analyzer.analyze_melody(pitch_series)
    
    # 新的拓扑分析器
    print("2. 运行拓扑分析器...")
    topo_analyzer = TopologicalMelodyAnalyzer(kernel_width=3.0, n_attractors=5)
    topo_results = topo_analyzer.analyze_melody(pitch_series)
    
    # 对比结果
    print(f"\n3. 对比结果:")
    print(f"{'指标':<25} {'原有方法':<15} {'拓扑方法':<15} {'改进':<10}")
    print("-" * 70)
    
    # 基本统计对比
    orig_mean = original_results['basic_stats']['mean_pitch']
    topo_mean = np.mean(pitch_series)  # 相同的计算
    print(f"{'平均音高':<25} {orig_mean:<15.3f} {topo_mean:<15.3f} {'相同':<10}")
    
    orig_std = original_results['basic_stats']['std_pitch']
    topo_std = np.std(pitch_series)  # 相同的计算
    print(f"{'音高标准差':<25} {orig_std:<15.3f} {topo_std:<15.3f} {'相同':<10}")
    
    # 吸引子分析对比
    orig_attractor = original_results['attractor_analysis']['main_attractor']
    topo_attractors = topo_results['potential_field']['attractor_points']
    main_topo_attractor = topo_attractors[0][0] if topo_attractors else 0
    print(f"{'主要吸引子位置':<25} {orig_attractor:<15.3f} {main_topo_attractor:<15.3f} {'更精确':<10}")
    
    orig_attraction = original_results['attractor_analysis']['attraction_strength']
    topo_attraction = topo_results['topology_metrics']['attractor_strength']
    improvement = "更强" if topo_attraction > orig_attraction else "不同算法"
    print(f"{'吸引子强度':<25} {orig_attraction:<15.3f} {topo_attraction:<15.3f} {improvement:<10}")
    
    # 复杂度分析对比
    orig_complexity = original_results['triad_analysis']['triad_complexity']
    topo_entropy = topo_results['topology_metrics']['topological_entropy']
    print(f"{'三音组复杂度':<25} {orig_complexity:<15.3f} {'N/A':<15} {'不同指标':<10}")
    print(f"{'拓扑熵':<25} {'N/A':<15} {topo_entropy:<15.3f} {'新指标':<10}")
    
    # 新增的拓扑指标
    print(f"\n4. 拓扑方法的新增指标:")
    print(f"  收敛比例: {topo_results['topology_metrics']['convergence_ratio']:.3f}")
    print(f"  平均稳定性: {topo_results['topology_metrics']['mean_stability']:.3f}")
    print(f"  稳定性方差: {topo_results['topology_metrics']['stability_variance']:.3f}")
    
    print(f"  相位分布:")
    for phase, ratio in topo_results['topology_metrics']['phase_distribution'].items():
        print(f"    {phase}: {ratio:.1%}")
    
    # 理论优势分析
    print(f"\n5. 理论优势分析:")
    print(f"  原有方法:")
    print(f"    - 基于静态统计分析")
    print(f"    - 离散吸引子识别")
    print(f"    - 孤立的三音组分析")
    
    print(f"  拓扑方法:")
    print(f"    - 基于动态拓扑分析")
    print(f"    - 连续势能场建模")
    print(f"    - 三音组流形动力学")
    print(f"    - 多尺度拓扑指标")
    
    return {
        'original': original_results,
        'topological': topo_results,
        'melody_name': melody_name
    }

def analyze_key_differences():
    """分析关键差异点"""
    print(f"\n{'='*80}")
    print("关键差异点分析")
    print(f"{'='*80}")
    
    print(f"\n1. 理论框架差异:")
    print(f"   原有方法: 基于频率统计的静态分析")
    print(f"   拓扑方法: 基于相空间动力学的动态分析")
    
    print(f"\n2. 吸引子建模差异:")
    print(f"   原有方法: 离散点状吸引子")
    print(f"   拓扑方法: 连续势能场V(p) = -∑αᵢexp(-(p-μᵢ)²/(2σ²))")
    
    print(f"\n3. 三音组分析差异:")
    print(f"   原有方法: 孤立的统计分析")
    print(f"   拓扑方法: 相空间流形动力学 d/dt Mₜ = -∇V(Mₜ)")
    
    print(f"\n4. 新增的拓扑指标:")
    print(f"   - 收敛比例: 衡量吸引子收敛的三音组比例")
    print(f"   - 李雅普诺夫稳定性: λ = -<v, ∇V> / ||v||²")
    print(f"   - 拓扑熵: 相位转换频率")
    print(f"   - 相位分布: Attractor Convergence, Orbital Trajectory, Repulsive Divergence")
    
    print(f"\n5. 实验价值:")
    print(f"   - 可以识别旋律的动态演化模式")
    print(f"   - 量化三音组与吸引子的相互作用")
    print(f"   - 提供多尺度的拓扑特征描述")

def main():
    """主函数"""
    print("模型对比分析程序")
    print("="*50)
    
    # 生成测试旋律
    melodies = [
        (generate_test_melody(40, 'simple'), "简单旋律"),
        (generate_test_melody(50, 'medium'), "中等复杂旋律"),
        (generate_test_melody(60, 'complex'), "复杂旋律")
    ]
    
    comparison_results = []
    
    # 对每个旋律进行对比分析
    for melody, name in melodies:
        result = compare_analyzers(melody, name)
        comparison_results.append(result)
    
    # 分析关键差异
    analyze_key_differences()
    
    # 保存对比结果
    print(f"\n6. 保存对比结果...")
    for i, result in enumerate(comparison_results):
        filename = f"comparison_{result['melody_name'].replace('旋律', '')}.json"
        
        # 准备可序列化的对比数据
        comparison_data = {
            'melody_name': result['melody_name'],
            'original_method': result['original'],
            'topological_method': {
                'potential_field': result['topological']['potential_field'],
                'topology_metrics': result['topological']['topology_metrics']
            },
            'analysis_summary': {
                'theoretical_framework': {
                    'original': 'Static frequency-based analysis',
                    'topological': 'Dynamic phase-space analysis'
                },
                'attractor_modeling': {
                    'original': 'Discrete point attractors',
                    'topological': 'Continuous potential field V(p)'
                },
                'triad_analysis': {
                    'original': 'Isolated statistical analysis',
                    'topological': 'Manifold dynamics in phase space'
                }
            }
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(comparison_data, f, indent=2, ensure_ascii=False)
        
        print(f"  对比结果已保存到: {filename}")
    
    print(f"\n对比分析完成！")
    print(f"关键发现:")
    print(f"1. 拓扑方法提供了更丰富的动力学信息")
    print(f"2. 新的相位分类揭示了旋律的演化模式")
    print(f"3. 连续势能场比离散吸引子更精确")
    print(f"4. 多尺度拓扑指标提供了全新的分析维度")

if __name__ == "__main__":
    main()
