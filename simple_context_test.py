#!/usr/bin/env python3
"""
简化的上下文分析测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def simple_test():
    print("🔍 简化的上下文分析测试")
    print("="*50)
    
    try:
        from unified_topological_analysis import UnifiedTopologicalAnalyzer
        print("✅ 导入成功")
        
        analyzer = UnifiedTopologicalAnalyzer()
        print("✅ 创建分析器成功")
        
        # 测试上下文分析方法是否存在
        if hasattr(analyzer, '_provide_theoretical_context'):
            print("✅ _provide_theoretical_context 方法存在")
        else:
            print("❌ _provide_theoretical_context 方法不存在")
            
        if hasattr(analyzer, '_provide_statistical_significance'):
            print("✅ _provide_statistical_significance 方法存在")
        else:
            print("❌ _provide_statistical_significance 方法不存在")
            
        if hasattr(analyzer, '_address_editor_concerns'):
            print("✅ _address_editor_concerns 方法存在")
        else:
            print("❌ _address_editor_concerns 方法不存在")
        
        # 测试批量摘要方法
        if hasattr(analyzer, '_generate_batch_summary'):
            print("✅ _generate_batch_summary 方法存在")
            
            # 创建简单的测试数据
            mock_results = [{
                'derived_features': {
                    'internal_attractor_alignment': 0.75,
                    'chinese_music_characteristic': 0.85,
                    'spiral_phase_entropy': 0.15,
                    'attractor_interaction_strength': 1.2
                },
                'internal_attractors': [
                    {'pitch': 60, 'strength': 1.0},
                    {'pitch': 64, 'strength': 0.8}
                ]
            }]
            
            print("📊 测试批量摘要生成...")
            analyzer._generate_batch_summary(mock_results)
            print("✅ 批量摘要生成成功")
            
        else:
            print("❌ _generate_batch_summary 方法不存在")
        
        print("\n🎉 上下文分析功能已成功集成到unified_topological_analysis.py中！")
        return True
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    simple_test()
