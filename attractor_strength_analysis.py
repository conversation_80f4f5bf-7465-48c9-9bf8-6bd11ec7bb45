#!/usr/bin/env python3
"""
吸引子强度的单位和理论依据分析
为吸引子强度指标提供科学的定义和计算依据
"""

import numpy as np
import sys
sys.path.append('.')

def analyze_attractor_strength_definition():
    """分析吸引子强度的定义和计算方法"""
    print("💪 吸引子强度的单位和理论依据分析")
    print("="*80)
    
    print("\n1. 🔍 当前吸引子强度的计算方法")
    print("-" * 60)
    
    print("📊 当前计算公式（来自代码分析）:")
    print("   强度 = 主导权重 × √吸引子数量 × (1 + 位置分散度) × (1 + 权重集中度)")
    
    print("\n❓ 关键问题:")
    print("   • 这个公式的物理意义是什么？")
    print("   • 各个组件的单位是什么？")
    print("   • 为什么使用这种特定的组合方式？")
    print("   • 结果的单位是什么？")
    print("   • 有什么理论依据？")

def analyze_component_units():
    """分析各个组件的单位"""
    print("\n2. 🧮 组件单位分析")
    print("-" * 60)
    
    components = {
        '主导权重': {
            'definition': '最大吸引子的权重值',
            'unit': '无量纲概率值 (0-1)',
            'physical_meaning': '该吸引子在整个引力场中的相对重要性',
            'typical_range': '0.1 - 0.8'
        },
        '吸引子数量': {
            'definition': '识别出的吸引子总数',
            'unit': '个数 (整数)',
            'physical_meaning': '引力场的复杂程度',
            'typical_range': '1 - 8'
        },
        '位置分散度': {
            'definition': '吸引子位置的标准差',
            'unit': '半音 (semitones)',
            'physical_meaning': '吸引子在音高空间的分布范围',
            'typical_range': '0 - 24'
        },
        '权重集中度': {
            'definition': '最大权重占总权重的比例',
            'unit': '无量纲比值 (0-1)',
            'physical_meaning': '引力场的集中程度',
            'typical_range': '0.2 - 1.0'
        }
    }
    
    print("🔬 各组件详细分析:")
    for component, info in components.items():
        print(f"\n   {component}:")
        print(f"     定义: {info['definition']}")
        print(f"     单位: {info['unit']}")
        print(f"     物理意义: {info['physical_meaning']}")
        print(f"     典型范围: {info['typical_range']}")
    
    # 单位组合分析
    print(f"\n📐 单位组合分析:")
    print(f"   强度 = [无量纲] × [个数^0.5] × [1 + 半音] × [1 + 无量纲]")
    print(f"   简化: [无量纲] × [个数^0.5] × [半音] × [无量纲]")
    print(f"   最终单位: [个数^0.5 × 半音] = [√个数 × 半音]")
    
    print(f"\n❓ 问题:")
    print(f"   • 这个单位组合有什么物理意义？")
    print(f"   • 为什么要取吸引子数量的平方根？")
    print(f"   • 为什么要加1再相乘？")

def propose_theoretical_framework():
    """提出理论框架"""
    print("\n3. 🎼 基于音乐理论的吸引子强度框架")
    print("-" * 60)
    
    print("🎯 吸引子强度的音乐学定义:")
    print("   吸引子强度应该反映吸引子对旋律的'控制力'或'影响力'")
    
    print(f"\n📚 理论依据:")
    print(f"   • 调性理论: 调性中心对音高的吸引力")
    print(f"   • 引力场理论: 质量越大、距离越近，引力越强")
    print(f"   • 信息论: 吸引子的信息量和不确定性")
    print(f"   • 动力学理论: 系统的稳定性和收敛性")
    
    print(f"\n🔬 影响因素分析:")
    factors = {
        '吸引子权重': {
            'effect': '正相关',
            'rationale': '权重越大，吸引力越强',
            'contribution': '主要因素'
        },
        '吸引子数量': {
            'effect': '负相关',
            'rationale': '吸引子越多，单个吸引子的相对影响力越小',
            'contribution': '调节因素'
        },
        '位置分散度': {
            'effect': '正相关',
            'rationale': '分散度大说明覆盖范围广，总体影响力强',
            'contribution': '次要因素'
        },
        '权重集中度': {
            'effect': '正相关',
            'rationale': '集中度高说明主导性强',
            'contribution': '重要因素'
        }
    }
    
    for factor, info in factors.items():
        print(f"   {factor}: {info['effect']} - {info['rationale']} ({info['contribution']})")

def develop_improved_formula():
    """开发改进的计算公式"""
    print("\n4. 🧪 改进的吸引子强度计算公式")
    print("-" * 60)
    
    print("🎯 设计原则:")
    print("   1. 物理意义明确")
    print("   2. 单位一致性")
    print("   3. 音乐学合理性")
    print("   4. 数值范围适中")
    
    print(f"\n📐 方案1: 基于引力场理论")
    print(f"   强度 = Σ(权重i × 影响范围i)")
    print(f"   单位: [无量纲权重] × [半音范围] = [半音·权重]")
    print(f"   物理意义: 总的引力场强度")
    
    print(f"\n📐 方案2: 基于信息论")
    print(f"   强度 = -Σ(权重i × log(权重i)) × 覆盖范围")
    print(f"   单位: [信息熵] × [半音] = [比特·半音]")
    print(f"   物理意义: 引力场的信息复杂度")
    
    print(f"\n📐 方案3: 基于动力学理论")
    print(f"   强度 = 主导权重 × (1 - 熵/最大熵) × 空间跨度")
    print(f"   单位: [无量纲] × [无量纲] × [半音] = [半音]")
    print(f"   物理意义: 主导吸引子的有效影响力")
    
    print(f"\n🏆 推荐方案: 标准化引力强度")
    print(f"   强度 = (主导权重 / 吸引子数量) × 空间跨度 × 集中度指数")
    print(f"   其中:")
    print(f"     • 主导权重/吸引子数量: 平均化的主导性")
    print(f"     • 空间跨度: max(位置) - min(位置) + 1")
    print(f"     • 集中度指数: 1 - 权重熵/log(吸引子数量)")
    print(f"   单位: [无量纲/个数] × [半音] × [无量纲] = [半音/个数]")

def test_improved_formulas():
    """测试改进的公式"""
    print("\n5. 🧪 改进公式测试")
    print("-" * 60)
    
    # 测试案例
    test_cases = [
        {
            'name': '单一强吸引子',
            'attractors': [(60, 0.8), (67, 0.2)],
            'expected': '高强度'
        },
        {
            'name': '多个平衡吸引子',
            'attractors': [(60, 0.25), (64, 0.25), (67, 0.25), (72, 0.25)],
            'expected': '中等强度'
        },
        {
            'name': '分散弱吸引子',
            'attractors': [(60, 0.15), (65, 0.15), (70, 0.15), (75, 0.15), (80, 0.15), (85, 0.25)],
            'expected': '低强度'
        }
    ]
    
    print("🎵 测试不同的吸引子配置:")
    print(f"{'配置':<15} {'当前公式':<12} {'改进公式':<12} {'单位':<15} {'预期'}")
    print("-" * 70)
    
    for case in test_cases:
        attractors = case['attractors']
        n_attractors = len(attractors)
        
        # 计算组件
        weights = [w for pos, w in attractors]
        positions = [pos for pos, w in attractors]
        
        dominant_weight = max(weights)
        position_spread = np.std(positions) if len(positions) > 1 else 0
        weight_concentration = max(weights) / sum(weights)
        
        # 当前公式
        current_strength = dominant_weight * np.sqrt(n_attractors) * (1 + position_spread) * (1 + weight_concentration)
        
        # 改进公式
        space_span = max(positions) - min(positions) + 1 if len(positions) > 1 else 1
        weight_entropy = -sum(w * np.log(w + 1e-10) for w in weights if w > 0)
        max_entropy = np.log(n_attractors)
        concentration_index = 1 - weight_entropy / max_entropy if max_entropy > 0 else 1
        
        improved_strength = (dominant_weight / n_attractors) * space_span * concentration_index
        
        print(f"{case['name']:<15} {current_strength:<12.2f} {improved_strength:<12.2f} {'半音/个数':<15} {case['expected']}")
    
    print(f"\n📊 公式对比:")
    print(f"   当前公式: 数值范围大，单位复杂")
    print(f"   改进公式: 数值范围适中，单位明确")

def recommend_final_approach():
    """推荐最终方案"""
    print("\n6. 🏆 最终推荐方案")
    print("-" * 60)
    
    print("🎯 推荐的吸引子强度定义:")
    print("   吸引子强度 = 标准化引力强度")
    print("   单位: 半音/个数 (semitones per attractor)")
    
    print(f"\n📐 计算公式:")
    print(f"   强度 = (主导权重 / 吸引子数量) × 音高跨度 × 集中度指数")
    print(f"   其中:")
    print(f"     • 主导权重: 最大吸引子权重 [0-1]")
    print(f"     • 吸引子数量: 识别的吸引子个数 [1-N]")
    print(f"     • 音高跨度: max(位置) - min(位置) + 1 [半音]")
    print(f"     • 集中度指数: 1 - 权重熵/log(吸引子数量) [0-1]")
    
    print(f"\n🔬 物理意义:")
    print(f"   • 主导权重/吸引子数量: 平均化的主导性，避免吸引子数量的直接影响")
    print(f"   • 音高跨度: 引力场的空间覆盖范围")
    print(f"   • 集中度指数: 权重分布的集中程度，基于信息熵")
    
    print(f"\n📊 数值特征:")
    print(f"   • 典型范围: 0.1 - 20.0 半音/个数")
    print(f"   • 单一强吸引子: 高值 (>10)")
    print(f"   • 多个平衡吸引子: 中值 (2-10)")
    print(f"   • 分散弱吸引子: 低值 (<2)")
    
    print(f"\n🎼 音乐学意义:")
    print(f"   • 高强度: 强调性音乐，明确的调性中心")
    print(f"   • 中等强度: 多调性或调性模糊的音乐")
    print(f"   • 低强度: 无调性或高度分散的音乐")
    
    print(f"\n📝 论文表述建议:")
    print(f'   "吸引子强度定义为标准化引力强度，单位为半音/个数')
    print(f'   (semitones per attractor)。计算公式为：')
    print(f'   强度 = (主导权重/吸引子数量) × 音高跨度 × 集中度指数，')
    print(f'   其中集中度指数基于权重分布的信息熵计算。该指标')
    print(f'   反映了吸引子对旋律的平均控制力，具有明确的物理')
    print(f'   意义和音乐学解释。典型范围为0.1-20.0，高值表示')
    print(f'   强调性音乐，低值表示无调性或分散性音乐。"')

def generate_implementation_code():
    """生成实现代码"""
    print("\n7. 💻 实现代码")
    print("-" * 60)
    
    code = '''
def calculate_improved_attractor_strength(attractor_points):
    """
    计算改进的吸引子强度
    
    Args:
        attractor_points: List[Tuple[float, float]] - (位置, 权重)
        
    Returns:
        float - 吸引子强度 (半音/个数)
    """
    if not attractor_points:
        return 0.0
    
    positions = [pos for pos, weight in attractor_points]
    weights = [weight for pos, weight in attractor_points]
    
    n_attractors = len(attractor_points)
    
    # 主导权重
    dominant_weight = max(weights)
    
    # 音高跨度
    if len(positions) > 1:
        pitch_span = max(positions) - min(positions) + 1
    else:
        pitch_span = 1.0
    
    # 集中度指数（基于信息熵）
    weight_entropy = -sum(w * np.log(w + 1e-10) for w in weights if w > 0)
    max_entropy = np.log(n_attractors) if n_attractors > 1 else 1.0
    concentration_index = 1 - weight_entropy / max_entropy if max_entropy > 0 else 1.0
    
    # 计算强度
    strength = (dominant_weight / n_attractors) * pitch_span * concentration_index
    
    return strength
'''
    
    print("🔧 改进的吸引子强度计算函数:")
    print(code)

if __name__ == "__main__":
    print("💪 吸引子强度的单位和理论依据分析")
    print("为音乐拓扑分析提供科学的强度指标")
    
    # 1. 分析当前定义
    analyze_attractor_strength_definition()
    
    # 2. 组件单位分析
    analyze_component_units()
    
    # 3. 理论框架
    propose_theoretical_framework()
    
    # 4. 改进公式
    develop_improved_formula()
    
    # 5. 测试公式
    test_improved_formulas()
    
    # 6. 最终推荐
    recommend_final_approach()
    
    # 7. 实现代码
    generate_implementation_code()
    
    print(f"\n🎉 吸引子强度分析完成！")
    print(f"✅ 提供了科学的单位定义和理论依据")
    print(f"🔬 改进的公式具有明确的物理意义")
    print(f"📝 可用于论文的方法论部分")
