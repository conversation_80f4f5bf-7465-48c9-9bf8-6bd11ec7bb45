=== 中国传统音乐综合分析报告 ===

🌊 全局动力系统指标统计:
- 吸引子强度: μ=0.690, σ=0.306
- 李雅普诺夫代理: μ=1.808, σ=0.565
- 系统能量: μ=0.394, σ=0.148
- 平均曲率: μ=8.670, σ=4.903

🔄 局部特征正交性分析:
- 高度正交比例: 100.0%
- 中度相关比例: 0.0%
- 强相关比例: 0.0%
- 平均相关系数: 0.0000

🎯 跨层级效应分析:
吸引子效应分布:
  - 强吸引子 (40.0%): 平均音程均幅=1.421, 平均局部波动性=9.876
  - 弱吸引子 (24.0%): 平均音程均幅=0.463, 平均局部波动性=3.285
  - 中吸引子 (36.0%): 平均音程均幅=1.122, 平均局部波动性=7.212
稳定性效应分布:
  - 低稳定性 (100.0%): 平均音程均幅=1.083, 平均局部波动性=7.335
能量效应分布:
  - 低能量 (100.0%): 平均音程均幅=1.083, 平均局部波动性=7.335

🎯 严格三音组核心地位分析:
- 结构密度: μ=0.360, σ=0.186, 范围=[0.020, 0.735]
- 轮廓控制力: μ=0.453, σ=0.214, 范围=[0.000, 0.833]
- 动态贡献度: μ=0.385, σ=0.192, 范围=[0.080, 1.000]
- 稳定性指数: μ=0.128, σ=0.146, 范围=[0.030, 0.944]

🌊 动力系统稳定性分析:
- 吸引子强度: μ=0.690, σ=0.306, 范围=[0.086, 1.287]
- 李雅普诺夫指数: μ=1.808, σ=0.565, 范围=[0.374, 2.705]
- 稳定性评分: μ=0.394, σ=0.148, 范围=[0.066, 0.708]
稳定性分类分布:
  - Moderately Stable: 46.0%
  - Unstable: 52.0%
  - Highly Stable: 2.0%

📊 全局指标正交性分析:
- 第一主成分解释方差: 87.0%
- 第二主成分解释方差: 9.7%
- 第三主成分解释方差: 3.0%
- 正交性评分: 0.130 (越高表示全局指标正交性越高)

=== 核心研究结论 ===
1. 中国传统音乐具有中等强度的旋律吸引子，显示出一定的音高中心性。
2. 旋律系统稳定性较低，表现出较强的动态变化特征。
3. 音程均幅与局部波动性高度正交，说明这两个特征捕捉了旋律的不同维度。
4. 严格三音组在中国传统音乐中具有重要地位，是重要的结构元素。

=== 详细数据摘要 ===
{
  "global_metrics_summary": {
    "attractor_strength": {
      "mean": 0.6895202733616936,
      "std": 0.3055703161471643,
      "min": 0.08569734313017163,
      "max": 1.2865656781167774,
      "median": 0.7084327508329737,
      "q1": 0.5194356206921478,
      "q3": 0.9237951269995361
    },
    "lyapunov_proxy": {
      "mean": 1.8083437255759405,
      "std": 0.5648405110044212,
      "min": 0.3743832731599567,
      "max": 2.7047075543868497,
      "median": 1.7920099560518472,
      "q1": 1.5085655786222838,
      "q3": 2.193425404079745
    },
    "system_energy": {
      "mean": 0.394022623809329,
      "std": 0.14771854657300204,
      "min": 0.06550841386399203,
      "max": 0.707962376298558,
      "median": 0.39322804804922984,
      "q1": 0.29437069993116083,
      "q3": 0.5074930205002214
    },
    "mean_curvature": {
      "mean": 8.670204081632653,
      "std": 4.902600029276682,
      "min": 0.8979591836734694,
      "max": 19.163265306122447,
      "median": 7.948979591836736,
      "q1": 5.392857142857142,
      "q3": 12.90561224489796
    }
  },
  "local_orthogonality_summary": {
    "correlation_stats": {
      "mean": 0.0,
      "std": 0.0,
      "min": 0.0,
      "max": 0.0,
      "median": 0.0,
      "q1": 0.0,
      "q3": 0.0
    },
    "orthogonality_distribution": {
      "high_orthogonality": 1.0,
      "medium_correlation": 0.0,
      "high_correlation": 0.0
    }
  },
  "cross_level_effects_summary": {
    "attractor_effect_summary": {
      "强吸引子": {
        "proportion": 0.4,
        "avg_intervallic": 1.4211976831451536,
        "avg_volatility": 9.876484184531398
      },
      "弱吸引子": {
        "proportion": 0.24,
        "avg_intervallic": 0.4627888916228868,
        "avg_volatility": 3.2850387134829813
      },
      "中吸引子": {
        "proportion": 0.36,
        "avg_intervallic": 1.1218103410161728,
        "avg_volatility": 7.212198030274641
      }
    },
    "stability_effect_summary": {
      "低稳定性": {
        "proportion": 1.0,
        "avg_intervallic": 1.0834001300133764,
        "avg_volatility": 7.335394255947346
      }
    },
    "energy_effect_summary": {
      "低能量": {
        "proportion": 1.0,
        "avg_intervallic": 1.0834001300133764,
        "avg_volatility": 7.335394255947346
      }
    }
  },
  "triad_significance_summary": {
    "structural_density": {
      "mean": 0.3599911020408164,
      "std": 0.18619694556679048,
      "min": 0.02040816326530612,
      "max": 0.7346938775510204,
      "median": 0.37244897959183676,
      "q1": 0.18622448979591838,
      "q3": 0.5204081632653061
    },
    "contour_control": {
      "mean": 0.4528555218983773,
      "std": 0.21445556973255456,
      "min": 0.0,
      "max": 0.8333333333333334,
      "median": 0.4812865497076023,
      "q1": 0.31294642857142857,
      "q3": 0.6211890243902439
    },
    "dynamic_contribution": {
      "mean": 0.3852117401548163,
      "std": 0.1916959709459738,
      "min": 0.08,
      "max": 1.0,
      "median": 0.3832158603086205,
      "q1": 0.25683212568199953,
      "q3": 0.48427301864801864
    },
    "stability_index": {
      "mean": 0.12802654895295723,
      "std": 0.14625375174955044,
      "min": 0.030407064196587917,
      "max": 0.9441,
      "median": 0.08284075363122961,
      "q1": 0.057635027594237484,
      "q3": 0.11450877682714006
    }
  },
  "dynamics_analysis_summary": {
    "attractor_strength": {
      "mean": 0.6895202733616936,
      "std": 0.3055703161471643,
      "min": 0.08569734313017163,
      "max": 1.2865656781167774,
      "median": 0.7084327508329737,
      "q1": 0.5194356206921478,
      "q3": 0.9237951269995361
    },
    "lyapunov_exponent": {
      "mean": 1.8083437255759405,
      "std": 0.5648405110044212,
      "min": 0.3743832731599567,
      "max": 2.7047075543868497,
      "median": 1.7920099560518472,
      "q1": 1.5085655786222838,
      "q3": 2.193425404079745
    },
    "stability_score": {
      "mean": 0.394022623809329,
      "std": 0.14771854657300204,
      "min": 0.06550841386399203,
      "max": 0.707962376298558,
      "median": 0.39322804804922984,
      "q1": 0.29437069993116083,
      "q3": 0.5074930205002214
    },
    "stability_classification_distribution": {
      "Moderately Stable": 0.46,
      "Unstable": 0.52,
      "Highly Stable": 0.02
    }
  },
  "global_orthogonality_analysis": {
    "explained_variance_ratio": [
      0.8702465874602968,
      0.09717910287981284,
      0.029822372022658923,
      0.0027519376372313873
    ],
    "components": [
      [
        0.5143432497321093,
        0.5008018668041471,
        0.5000815753843812,
        0.48432110177192333
      ],
      [
        -0.4249720525426712,
        0.4111860938832556,
        -0.5478242329684888,
        0.5917882733944511
      ],
      [
        0.16075688940782176,
        0.7117367430714158,
        -0.2694196418209249,
        -0.6284911198028972
      ],
      [
        0.7273286729674577,
        -0.27120895074574036,
        -0.6141824519917893,
        0.14219220154475684
      ]
    ],
    "orthogonality_score": 0.12975341253970318,
    "method": "PCA_analysis"
  }
}