#!/usr/bin/env python3
"""
统一拓扑分析系统的可解释性分析工具
提供详细的物理意义解释、可视化和SHAP值分析

核心功能：
1. 详细解释对齐度、吸引子强度、相位收敛的物理意义
2. 可视化吸引子盆地与相位收敛路径
3. 量化三音组对吸引子的贡献度
4. 生成论文级别的解释性图表

作者：AI音乐分析系统
版本：2.0 - 可解释性分析
"""

import numpy as np
import json
from typing import Dict, List, Tuple, Any
import os

# 尝试导入matplotlib，如果不可用则跳过可视化功能
try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️ matplotlib不可用，将跳过可视化功能")

class InterpretabilityAnalyzer:
    """
    可解释性分析器
    提供统一拓扑分析结果的深度解释
    """
    
    def __init__(self):
        self.alignment_thresholds = {
            'strong': 0.7,      # 强关联阈值
            'moderate': 0.3,    # 中等关联阈值
            'weak': 0.0         # 弱关联阈值
        }
        
        self.strength_ranges = {
            'very_high': 20.0,  # 极高强度
            'high': 15.0,       # 高强度
            'moderate': 10.0,   # 中等强度
            'low': 5.0,         # 低强度
            'very_low': 0.0     # 极低强度
        }
    
    def explain_alignment_score(self, alignment: float) -> Dict[str, Any]:
        """
        详细解释对齐度分数的物理意义
        
        Args:
            alignment: 对齐度分数 (0-1)
            
        Returns:
            详细的解释字典
        """
        # 确定关联强度等级
        if alignment >= self.alignment_thresholds['strong']:
            level = "强关联"
            description = "三音组紧密围绕吸引子，显示强烈的引力作用"
            musical_meaning = "旋律高度结构化，三音组作为稳定的音乐单元"
        elif alignment >= self.alignment_thresholds['moderate']:
            level = "中等关联"
            description = "三音组受吸引子影响但保持一定独立性"
            musical_meaning = "旋律在结构性和自由性之间平衡"
        else:
            level = "弱关联"
            description = "三音组相对独立于吸引子，显示较强的随机性"
            musical_meaning = "旋律更加自由，三音组不受强烈的调性约束"
        
        # 计算距离信息
        avg_distance = (1.0 / alignment) - 1.0 if alignment > 0 else float('inf')
        
        return {
            'alignment_score': alignment,
            'level': level,
            'description': description,
            'musical_meaning': musical_meaning,
            'average_distance': avg_distance,
            'distance_unit': '半音',
            'calculation_formula': '对齐度 = 1 / (1 + 平均距离)',
            'threshold_context': {
                'current_level': level,
                'strong_threshold': f"> {self.alignment_thresholds['strong']}",
                'moderate_threshold': f"{self.alignment_thresholds['moderate']}-{self.alignment_thresholds['strong']}",
                'weak_threshold': f"< {self.alignment_thresholds['moderate']}"
            }
        }
    
    def explain_attractor_strength(self, strength: float, n_attractors: int, 
                                 attractor_points: List[Tuple[float, float]]) -> Dict[str, Any]:
        """
        详细解释吸引子强度的物理意义
        
        Args:
            strength: 吸引子强度值
            n_attractors: 吸引子数量
            attractor_points: 吸引子位置和权重列表
            
        Returns:
            详细的解释字典
        """
        # 确定强度等级
        if strength >= self.strength_ranges['very_high']:
            level = "极高强度"
            description = "吸引子对旋律具有极强的控制力"
            musical_meaning = "高度调性化的音乐，强烈的音高中心"
        elif strength >= self.strength_ranges['high']:
            level = "高强度"
            description = "吸引子对旋律具有强烈的引力作用"
            musical_meaning = "明确的调性中心，结构化程度高"
        elif strength >= self.strength_ranges['moderate']:
            level = "中等强度"
            description = "吸引子与旋律之间存在平衡的相互作用"
            musical_meaning = "调性与自由度并存的音乐风格"
        elif strength >= self.strength_ranges['low']:
            level = "低强度"
            description = "吸引子对旋律的影响相对较弱"
            musical_meaning = "较为自由的音乐风格，弱调性特征"
        else:
            level = "极低强度"
            description = "吸引子几乎不对旋律产生约束"
            musical_meaning = "高度自由的音乐，接近无调性"
        
        # 分析吸引子分布
        if attractor_points:
            positions = [pos for pos, weight in attractor_points]
            weights = [weight for pos, weight in attractor_points]
            
            position_spread = np.std(positions) if len(positions) > 1 else 0.0
            weight_concentration = np.max(weights) / np.sum(weights) if weights else 0.0
            dominant_attractor = attractor_points[np.argmax(weights)]
        else:
            position_spread = 0.0
            weight_concentration = 0.0
            dominant_attractor = (0.0, 0.0)
        
        return {
            'strength_value': strength,
            'level': level,
            'description': description,
            'musical_meaning': musical_meaning,
            'unit': '无量纲相对强度',
            'typical_range': '1-30',
            'calculation_components': {
                'n_attractors': n_attractors,
                'position_spread': position_spread,
                'weight_concentration': weight_concentration,
                'dominant_attractor_position': dominant_attractor[0],
                'dominant_attractor_weight': dominant_attractor[1]
            },
            'calculation_formula': '强度 = 主导权重 × √吸引子数量 × (1 + 位置分散度) × (1 + 权重集中度)',
            'strength_context': {
                'current_level': level,
                'very_high_threshold': f"> {self.strength_ranges['very_high']}",
                'high_threshold': f"{self.strength_ranges['high']}-{self.strength_ranges['very_high']}",
                'moderate_threshold': f"{self.strength_ranges['moderate']}-{self.strength_ranges['high']}",
                'low_threshold': f"{self.strength_ranges['low']}-{self.strength_ranges['moderate']}",
                'very_low_threshold': f"< {self.strength_ranges['low']}"
            }
        }
    
    def explain_phase_convergence(self, convergence_ratio: float, 
                                triad_trajectory: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        详细解释相位收敛的物理意义
        
        Args:
            convergence_ratio: 收敛比例 (0-1)
            triad_trajectory: 三音组轨迹数据
            
        Returns:
            详细的解释字典
        """
        convergence_percentage = convergence_ratio * 100
        equilibrium_percentage = (1 - convergence_ratio) * 100
        
        # 分析相位分布
        phase_counts = {}
        for triad in triad_trajectory:
            phase = triad.get('phase', 'Unknown')
            phase_counts[phase] = phase_counts.get(phase, 0) + 1
        
        total_triads = len(triad_trajectory)
        phase_distribution = {phase: count/total_triads for phase, count in phase_counts.items()}
        
        # 确定收敛特征
        if convergence_percentage >= 80:
            convergence_level = "高度收敛"
            description = "旋律强烈趋向于吸引子，显示高度的方向性"
            musical_meaning = "目标导向的旋律发展，强烈的音乐张力"
        elif convergence_percentage >= 60:
            convergence_level = "中度收敛"
            description = "旋律在收敛和平衡之间保持动态平衡"
            musical_meaning = "平衡的音乐发展，张力与松弛并存"
        elif convergence_percentage >= 40:
            convergence_level = "低度收敛"
            description = "旋律更多处于平衡状态，收敛趋势较弱"
            musical_meaning = "相对静态的音乐特征，强调稳定性"
        else:
            convergence_level = "极低收敛"
            description = "旋律主要处于静态平衡，缺乏明确方向"
            musical_meaning = "高度静态的音乐，强调持续和稳定"
        
        return {
            'convergence_ratio': convergence_ratio,
            'convergence_percentage': convergence_percentage,
            'equilibrium_percentage': equilibrium_percentage,
            'convergence_level': convergence_level,
            'description': description,
            'musical_meaning': musical_meaning,
            'phase_distribution': phase_distribution,
            'total_triads_analyzed': total_triads,
            'attribution': '音乐内在的动力学属性，非模型强制',
            'physical_interpretation': {
                'convergence_phase': '三音组向最近吸引子移动的动态过程',
                'equilibrium_phase': '三音组在吸引子附近的相对稳定状态',
                'measurement_method': '基于三音组轨迹的相位空间分析'
            }
        }
    
    def calculate_triad_contributions(self, triad_attractor_associations: List[Dict[str, Any]], 
                                    attractor_points: List[Tuple[float, float]]) -> Dict[str, Any]:
        """
        计算三音组对各个吸引子的贡献度（类似SHAP值的概念）
        
        Args:
            triad_attractor_associations: 三音组-吸引子关联数据
            attractor_points: 吸引子位置和权重
            
        Returns:
            贡献度分析结果
        """
        n_attractors = len(attractor_points)
        
        # 初始化贡献度矩阵
        attractor_contributions = {i: [] for i in range(n_attractors)}
        
        # 计算每个三音组对各吸引子的贡献
        for assoc in triad_attractor_associations:
            closest_idx = assoc['closest_attractor_idx']
            influence = assoc['attractor_influence']
            distance = assoc['distance_to_attractor']
            
            # 主要贡献给最近的吸引子
            attractor_contributions[closest_idx].append({
                'influence': influence,
                'distance': distance,
                'contribution_type': 'primary'
            })
            
            # 计算对其他吸引子的次要贡献
            triad_pos = assoc['triad_centroid']
            for i, (attr_pos, attr_weight) in enumerate(attractor_points):
                if i != closest_idx:
                    secondary_distance = abs(triad_pos - attr_pos)
                    secondary_influence = attr_weight / (1.0 + secondary_distance)
                    
                    attractor_contributions[i].append({
                        'influence': secondary_influence,
                        'distance': secondary_distance,
                        'contribution_type': 'secondary'
                    })
        
        # 汇总贡献度统计
        contribution_summary = {}
        for i in range(n_attractors):
            contributions = attractor_contributions[i]
            primary_contributions = [c for c in contributions if c['contribution_type'] == 'primary']
            secondary_contributions = [c for c in contributions if c['contribution_type'] == 'secondary']
            
            total_influence = sum(c['influence'] for c in contributions)
            primary_influence = sum(c['influence'] for c in primary_contributions)
            secondary_influence = sum(c['influence'] for c in secondary_contributions)
            
            avg_distance = np.mean([c['distance'] for c in contributions]) if contributions else 0.0
            
            contribution_summary[f'attractor_{i}'] = {
                'position': attractor_points[i][0],
                'weight': attractor_points[i][1],
                'total_influence': total_influence,
                'primary_influence': primary_influence,
                'secondary_influence': secondary_influence,
                'primary_triad_count': len(primary_contributions),
                'total_interaction_count': len(contributions),
                'average_distance': avg_distance,
                'influence_ratio': primary_influence / total_influence if total_influence > 0 else 0.0
            }
        
        return {
            'contribution_summary': contribution_summary,
            'methodology': 'SHAP-like contribution analysis',
            'interpretation': {
                'total_influence': '三音组对该吸引子的总影响强度',
                'primary_influence': '作为最近吸引子时的直接影响',
                'secondary_influence': '作为次近吸引子时的间接影响',
                'influence_ratio': '直接影响占总影响的比例'
            }
        }
    
    def generate_comprehensive_explanation(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成综合性的可解释性报告
        
        Args:
            analysis_result: 统一拓扑分析结果
            
        Returns:
            综合解释报告
        """
        # 提取核心数据
        landscape = analysis_result['attractor_landscape']
        topology = analysis_result['topology_metrics']
        triad_analysis = analysis_result['enhanced_triad_analysis']
        
        # 生成各项解释
        alignment_explanation = self.explain_alignment_score(
            triad_analysis.get('mean_attractor_alignment', 0.0)
        )
        
        strength_explanation = self.explain_attractor_strength(
            topology['attractor_strength'],
            landscape['attractor_count'],
            landscape['attractor_points']
        )
        
        # 如果有轨迹数据，分析相位收敛
        phase_explanation = None
        if 'triad_trajectory' in analysis_result:
            convergence_ratio = topology.get('convergence_ratio', 0.0)
            phase_explanation = self.explain_phase_convergence(
                convergence_ratio,
                analysis_result['triad_trajectory']
            )
        
        # 计算贡献度
        contribution_analysis = None
        if 'triad_attractor_associations' in triad_analysis:
            contribution_analysis = self.calculate_triad_contributions(
                triad_analysis['triad_attractor_associations'],
                landscape['attractor_points']
            )
        
        return {
            'work_name': analysis_result['work_name'],
            'alignment_explanation': alignment_explanation,
            'strength_explanation': strength_explanation,
            'phase_explanation': phase_explanation,
            'contribution_analysis': contribution_analysis,
            'summary': {
                'key_findings': [
                    f"吸引子强度 {topology['attractor_strength']:.3f} 表示{strength_explanation['level']}",
                    f"对齐度 {triad_analysis.get('mean_attractor_alignment', 0.0):.3f} 显示{alignment_explanation['level']}",
                    f"相位收敛 {topology.get('convergence_ratio', 0.0)*100:.1f}% 反映{phase_explanation['convergence_level'] if phase_explanation else '未知'}的动力学特征"
                ],
                'physical_interpretation': '基于多吸引子引力景观的统一拓扑动力学分析',
                'musical_significance': '揭示了音乐中三音组与调性中心的复杂相互作用机制'
            }
        }


def create_interpretability_report(analysis_results: List[Dict[str, Any]], 
                                 output_file: str = "interpretability_report.json"):
    """
    为批量分析结果创建可解释性报告
    
    Args:
        analysis_results: 批量分析结果
        output_file: 输出文件名
    """
    analyzer = InterpretabilityAnalyzer()
    
    interpretability_reports = []
    
    print("🔍 生成可解释性报告...")
    
    for i, result in enumerate(analysis_results, 1):
        print(f"   处理作品 {i}/{len(analysis_results)}: {result['work_name']}")
        
        try:
            explanation = analyzer.generate_comprehensive_explanation(result)
            interpretability_reports.append(explanation)
        except Exception as e:
            print(f"   ❌ 处理失败: {e}")
            continue
    
    # 生成汇总统计
    if interpretability_reports:
        alignments = [r['alignment_explanation']['alignment_score'] for r in interpretability_reports]
        strengths = [r['strength_explanation']['strength_value'] for r in interpretability_reports]
        
        summary_stats = {
            'total_works': len(interpretability_reports),
            'alignment_statistics': {
                'mean': np.mean(alignments),
                'std': np.std(alignments),
                'min': np.min(alignments),
                'max': np.max(alignments),
                'distribution': {
                    'strong': sum(1 for a in alignments if a >= 0.7),
                    'moderate': sum(1 for a in alignments if 0.3 <= a < 0.7),
                    'weak': sum(1 for a in alignments if a < 0.3)
                }
            },
            'strength_statistics': {
                'mean': np.mean(strengths),
                'std': np.std(strengths),
                'min': np.min(strengths),
                'max': np.max(strengths),
                'distribution': {
                    'very_high': sum(1 for s in strengths if s >= 20.0),
                    'high': sum(1 for s in strengths if 15.0 <= s < 20.0),
                    'moderate': sum(1 for s in strengths if 10.0 <= s < 15.0),
                    'low': sum(1 for s in strengths if 5.0 <= s < 10.0),
                    'very_low': sum(1 for s in strengths if s < 5.0)
                }
            }
        }
    else:
        summary_stats = {}
    
    # 保存报告
    report_data = {
        'metadata': {
            'analysis_type': 'interpretability_analysis',
            'version': '2.0',
            'total_works': len(interpretability_reports)
        },
        'summary_statistics': summary_stats,
        'detailed_reports': interpretability_reports
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    print(f"📄 可解释性报告已保存到: {output_file}")
    return report_data


class VisualizationGenerator:
    """
    可视化生成器
    创建论文级别的图表来展示吸引子盆地与相位收敛路径
    """

    def __init__(self):
        self.matplotlib_available = MATPLOTLIB_AVAILABLE

        if self.matplotlib_available:
            # 设置中文字体（如果可用）
            try:
                plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
                plt.rcParams['axes.unicode_minus'] = False
            except:
                pass
        else:
            print("⚠️ matplotlib不可用，可视化功能将被禁用")

    def plot_attractor_landscape(self, analysis_result: Dict[str, Any],
                                save_path: str = "attractor_landscape.png"):
        """
        绘制吸引子引力景观图

        Args:
            analysis_result: 分析结果
            save_path: 保存路径
        """
        if not self.matplotlib_available:
            print("   ⚠️ matplotlib不可用，跳过吸引子景观图生成")
            return

        try:
            # 提取数据
            attractor_points = analysis_result['attractor_landscape']['attractor_points']
            pitch_series = analysis_result['original_pitch_series']
            work_name = analysis_result['work_name']

            if not attractor_points:
                print("   ⚠️ 无吸引子数据，跳过可视化")
                return

            # 创建图形
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

            # 上图：旋律线与吸引子
            ax1.plot(range(len(pitch_series)), pitch_series, 'b-', alpha=0.7, linewidth=1.5, label='旋律线')

            # 绘制吸引子
            for i, (pos, weight) in enumerate(attractor_points):
                ax1.axhline(y=pos, color='red', linestyle='--', alpha=0.8, linewidth=2)
                ax1.scatter([len(pitch_series)*0.95], [pos], s=weight*1000, c='red', alpha=0.7,
                           marker='o', label=f'吸引子{i+1}' if i < 3 else '')
                ax1.text(len(pitch_series)*0.97, pos, f'A{i+1}\n({pos:.1f})',
                        fontsize=9, ha='left', va='center')

            ax1.set_xlabel('时间 (音符序号)')
            ax1.set_ylabel('音高 (MIDI)')
            ax1.set_title(f'{work_name} - 吸引子引力景观')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 下图：吸引子强度分布
            positions = [pos for pos, weight in attractor_points]
            weights = [weight for pos, weight in attractor_points]

            bars = ax2.bar(range(len(positions)), weights, alpha=0.7, color='orange')
            ax2.set_xlabel('吸引子编号')
            ax2.set_ylabel('权重')
            ax2.set_title('吸引子权重分布')
            ax2.set_xticks(range(len(positions)))
            ax2.set_xticklabels([f'A{i+1}\n({pos:.1f})' for i, pos in enumerate(positions)])

            # 添加数值标签
            for i, (bar, weight) in enumerate(zip(bars, weights)):
                ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                        f'{weight:.3f}', ha='center', va='bottom', fontsize=9)

            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"   ✅ 吸引子景观图已保存: {save_path}")

        except Exception as e:
            print(f"   ❌ 吸引子景观图生成失败: {e}")

    def plot_phase_convergence_path(self, analysis_result: Dict[str, Any],
                                  save_path: str = "phase_convergence.png"):
        """
        绘制相位收敛路径图

        Args:
            analysis_result: 分析结果
            save_path: 保存路径
        """
        if not self.matplotlib_available:
            print("   ⚠️ matplotlib不可用，跳过相位收敛图生成")
            return

        try:
            # 检查是否有轨迹数据
            if 'triad_trajectory' not in analysis_result:
                print("   ⚠️ 无三音组轨迹数据，跳过相位收敛图")
                return

            triad_trajectory = analysis_result['triad_trajectory']
            work_name = analysis_result['work_name']

            # 提取相位信息
            positions = []
            phases = []
            stabilities = []

            for triad in triad_trajectory:
                if 'position' in triad and triad['position']:
                    positions.append(triad['position'][0])  # 三音组质心
                    phases.append(triad.get('phase', 'Unknown'))
                    stabilities.append(triad.get('stability', 0.0))

            if not positions:
                print("   ⚠️ 无有效的三音组位置数据")
                return

            # 创建图形
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

            # 上图：相位收敛路径
            convergence_mask = [p == 'Attractor Convergence' for p in phases]
            equilibrium_mask = [p == 'Static Equilibrium' for p in phases]

            conv_positions = [pos for i, pos in enumerate(positions) if convergence_mask[i]]
            equil_positions = [pos for i, pos in enumerate(positions) if equilibrium_mask[i]]

            conv_indices = [i for i, mask in enumerate(convergence_mask) if mask]
            equil_indices = [i for i, mask in enumerate(equilibrium_mask) if mask]

            if conv_positions:
                ax1.scatter(conv_indices, conv_positions, c='red', alpha=0.7, s=50,
                           label=f'收敛相位 ({len(conv_positions)}个)', marker='^')

            if equil_positions:
                ax1.scatter(equil_indices, equil_positions, c='blue', alpha=0.7, s=50,
                           label=f'平衡相位 ({len(equil_positions)}个)', marker='o')

            # 连接线显示轨迹
            ax1.plot(range(len(positions)), positions, 'gray', alpha=0.5, linewidth=1,
                    linestyle='-', label='三音组轨迹')

            ax1.set_xlabel('三音组序号')
            ax1.set_ylabel('音高位置 (MIDI)')
            ax1.set_title(f'{work_name} - 相位收敛路径')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 下图：稳定性变化
            ax2.plot(range(len(stabilities)), stabilities, 'g-', linewidth=2, alpha=0.8)
            ax2.fill_between(range(len(stabilities)), stabilities, alpha=0.3, color='green')
            ax2.set_xlabel('三音组序号')
            ax2.set_ylabel('稳定性指数')
            ax2.set_title('三音组稳定性变化')
            ax2.grid(True, alpha=0.3)

            # 添加相位区域标记
            for i, phase in enumerate(phases):
                if phase == 'Attractor Convergence':
                    ax2.axvspan(i-0.4, i+0.4, alpha=0.2, color='red')
                elif phase == 'Static Equilibrium':
                    ax2.axvspan(i-0.4, i+0.4, alpha=0.2, color='blue')

            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"   ✅ 相位收敛图已保存: {save_path}")

        except Exception as e:
            print(f"   ❌ 相位收敛图生成失败: {e}")

    def plot_triad_attractor_relationship(self, analysis_result: Dict[str, Any],
                                        save_path: str = "triad_attractor_relationship.png"):
        """
        绘制三音组-吸引子关系图

        Args:
            analysis_result: 分析结果
            save_path: 保存路径
        """
        if not self.matplotlib_available:
            print("   ⚠️ matplotlib不可用，跳过三音组-吸引子关系图生成")
            return

        try:
            # 检查数据
            if 'enhanced_triad_analysis' not in analysis_result:
                print("   ⚠️ 无三音组分析数据")
                return

            triad_analysis = analysis_result['enhanced_triad_analysis']
            if 'triad_attractor_associations' not in triad_analysis:
                print("   ⚠️ 无三音组-吸引子关联数据")
                return

            associations = triad_analysis['triad_attractor_associations']
            attractor_points = analysis_result['attractor_landscape']['attractor_points']
            work_name = analysis_result['work_name']

            # 创建图形
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

            # 左图：三音组与最近吸引子的距离分布
            distances = [assoc['distance_to_attractor'] for assoc in associations]
            influences = [assoc['attractor_influence'] for assoc in associations]

            scatter = ax1.scatter(distances, influences, alpha=0.6, s=50, c=distances,
                                cmap='viridis_r')
            ax1.set_xlabel('距离最近吸引子的距离 (半音)')
            ax1.set_ylabel('吸引子影响强度')
            ax1.set_title('三音组-吸引子距离vs影响强度')
            ax1.grid(True, alpha=0.3)

            # 添加颜色条
            cbar = plt.colorbar(scatter, ax=ax1)
            cbar.set_label('距离 (半音)')

            # 右图：吸引子使用频率
            attractor_usage = triad_analysis.get('attractor_usage_distribution', {})

            if attractor_usage:
                indices = list(attractor_usage.keys())
                counts = list(attractor_usage.values())

                bars = ax2.bar(indices, counts, alpha=0.7, color='skyblue')
                ax2.set_xlabel('吸引子编号')
                ax2.set_ylabel('被选择次数')
                ax2.set_title('吸引子使用频率分布')
                ax2.set_xticks(indices)

                # 添加百分比标签
                total_count = sum(counts)
                for bar, count in zip(bars, counts):
                    percentage = count / total_count * 100
                    ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                            f'{percentage:.1f}%', ha='center', va='bottom', fontsize=9)

            plt.suptitle(f'{work_name} - 三音组与吸引子关系分析', fontsize=14)
            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"   ✅ 三音组-吸引子关系图已保存: {save_path}")

        except Exception as e:
            print(f"   ❌ 三音组-吸引子关系图生成失败: {e}")


def generate_paper_visualizations(analysis_results: List[Dict[str, Any]],
                                output_dir: str = "paper_figures"):
    """
    为论文生成所有可视化图表

    Args:
        analysis_results: 分析结果列表
        output_dir: 输出目录
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    visualizer = VisualizationGenerator()

    print("🎨 生成论文级别的可视化图表...")

    # 选择几个代表性的作品进行可视化
    representative_works = analysis_results[:3]  # 前3个作品

    for i, result in enumerate(representative_works):
        work_name = result['work_name']
        safe_name = "".join(c for c in work_name if c.isalnum() or c in (' ', '-', '_')).rstrip()

        print(f"\n📊 生成 {work_name} 的可视化图表...")

        # 吸引子景观图
        landscape_path = os.path.join(output_dir, f"{safe_name}_landscape.png")
        visualizer.plot_attractor_landscape(result, landscape_path)

        # 相位收敛图
        phase_path = os.path.join(output_dir, f"{safe_name}_phase.png")
        visualizer.plot_phase_convergence_path(result, phase_path)

        # 三音组-吸引子关系图
        relationship_path = os.path.join(output_dir, f"{safe_name}_relationship.png")
        visualizer.plot_triad_attractor_relationship(result, relationship_path)

    print(f"\n✅ 所有可视化图表已保存到: {output_dir}")


if __name__ == "__main__":
    print("🔍 统一拓扑分析可解释性工具")
    print("提供详细的物理意义解释和可视化分析")
    print("="*60)

    # 示例：创建解释器
    analyzer = InterpretabilityAnalyzer()

    # 示例解释
    print("\n📊 示例解释:")

    # 对齐度解释
    alignment_exp = analyzer.explain_alignment_score(0.382)
    print(f"\n🎯 对齐度 0.382 解释:")
    print(f"   等级: {alignment_exp['level']}")
    print(f"   含义: {alignment_exp['description']}")
    print(f"   音乐意义: {alignment_exp['musical_meaning']}")
    print(f"   阈值上下文: {alignment_exp['threshold_context']}")

    # 强度解释
    strength_exp = analyzer.explain_attractor_strength(7.684, 5, [(60, 0.3), (67, 0.25), (72, 0.2), (64, 0.15), (69, 0.1)])
    print(f"\n💪 吸引子强度 7.684 解释:")
    print(f"   等级: {strength_exp['level']}")
    print(f"   含义: {strength_exp['description']}")
    print(f"   音乐意义: {strength_exp['musical_meaning']}")
    print(f"   计算公式: {strength_exp['calculation_formula']}")

    # 相位收敛解释
    phase_exp = analyzer.explain_phase_convergence(0.729, [
        {'phase': 'Attractor Convergence', 'stability': 0.8},
        {'phase': 'Static Equilibrium', 'stability': 0.6},
        {'phase': 'Attractor Convergence', 'stability': 0.9}
    ])
    print(f"\n🔄 相位收敛 72.9% 解释:")
    print(f"   等级: {phase_exp['convergence_level']}")
    print(f"   含义: {phase_exp['description']}")
    print(f"   音乐意义: {phase_exp['musical_meaning']}")
    print(f"   归因: {phase_exp['attribution']}")

    print("\n✅ 可解释性工具初始化完成")
