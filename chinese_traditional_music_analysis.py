#!/usr/bin/env python3
"""
中国传统音乐的拓扑分析框架
基于全音单位的距离计算，符合中国传统音乐理论
"""

import numpy as np
import sys
sys.path.append('.')

def analyze_chinese_traditional_music_theory():
    """分析中国传统音乐理论对距离单位选择的影响"""
    print("🎵 中国传统音乐的拓扑分析框架")
    print("基于全音单位的理论依据")
    print("="*80)
    
    # 1. 中国传统音乐理论基础
    print("\n1. 🏮 中国传统音乐理论基础")
    print("-" * 60)
    
    print("📚 五声音阶系统:")
    print("   • 宫、商、角、徵、羽 - 五个基本音级")
    print("   • 相邻音级间距主要为全音和小三度")
    print("   • 半音使用极少，主要出现在装饰音和特殊调式中")
    
    print("\n🎼 传统音程关系:")
    print("   • 全音（大二度）：商-角、徵-羽、羽-宫")
    print("   • 小三度：角-徵、宫-商")
    print("   • 大三度：由两个全音构成")
    print("   • 纯四度、纯五度：协和音程")
    
    print("\n🎯 半音的地位:")
    print("   • 半音在传统音乐中地位较低")
    print("   • 主要用于:")
    print("     - 装饰音（如倚音、回音）")
    print("     - 特殊调式（如燕乐调式）")
    print("     - 现代化改编中的和声需要")
    print("   • 不是构建音阶的基本单位")
    
    # 2. 五声音阶的距离分析
    print("\n2. 🎶 五声音阶的距离分析")
    print("-" * 60)
    
    # 以C宫调五声音阶为例：C D E G A
    pentatonic_scale = {
        '宫': 0,    # C
        '商': 2,    # D (全音)
        '角': 4,    # E (全音)
        '徵': 7,    # G (小三度)
        '羽': 9     # A (全音)
    }
    
    print("🎵 C宫调五声音阶分析 (C-D-E-G-A):")
    print(f"{'音级':<6} {'音名':<6} {'半音数':<8} {'全音数':<8} {'与宫的关系'}")
    print("-" * 50)
    
    note_names = ['C', 'D', 'E', 'G', 'A']
    for i, (level, semitones) in enumerate(pentatonic_scale.items()):
        whole_tones = semitones / 2.0
        relationship = f"{semitones}半音 = {whole_tones}全音"
        print(f"{level:<6} {note_names[i]:<6} {semitones:<8} {whole_tones:<8.1f} {relationship}")
    
    # 3. 相邻音级间距分析
    print(f"\n📏 相邻音级间距:")
    intervals = [
        ('宫→商', 2, '全音'),
        ('商→角', 2, '全音'), 
        ('角→徵', 3, '小三度'),
        ('徵→羽', 2, '全音'),
        ('羽→宫', 3, '小三度（八度内）')
    ]
    
    for interval_name, semitones, description in intervals:
        whole_tones = semitones / 2.0
        print(f"   {interval_name}: {semitones}半音 = {whole_tones}全音 ({description})")
    
    # 4. 对齐度计算对比
    print(f"\n3. 📊 基于中国传统音乐的对齐度分析")
    print("-" * 60)
    
    print("🎯 传统音程的对齐度计算:")
    print(f"{'音程':<12} {'半音数':<8} {'全音数':<8} {'半音单位对齐度':<12} {'全音单位对齐度':<12} {'音乐地位'}")
    print("-" * 80)
    
    traditional_intervals = [
        ('同音', 0, '基础音'),
        ('全音', 2, '基本音程单位'),
        ('小三度', 3, '五声音阶核心音程'),
        ('大三度', 4, '和谐音程'),
        ('纯四度', 5, '稳定音程'),
        ('纯五度', 7, '最重要的协和音程'),
        ('大六度', 9, '羽音关系'),
        ('八度', 12, '同名音')
    ]
    
    for interval_name, semitones, status in traditional_intervals:
        whole_tones = semitones / 2.0 if semitones > 0 else 0
        
        alignment_semitone = 1.0 / (1.0 + semitones) if semitones > 0 else 1.0
        alignment_whole_tone = 1.0 / (1.0 + whole_tones) if whole_tones > 0 else 1.0
        
        print(f"{interval_name:<12} {semitones:<8} {whole_tones:<8.1f} {alignment_semitone:<12.3f} {alignment_whole_tone:<12.3f} {status}")

def analyze_chinese_music_characteristics():
    """分析中国传统音乐特征对拓扑分析的影响"""
    print(f"\n4. 🏮 中国传统音乐特征分析")
    print("-" * 60)
    
    print("🎼 中国传统音乐的独特性:")
    print("   • 以五声音阶为基础，七声为辅")
    print("   • 重视音程的纵向关系和横向进行")
    print("   • 强调'骨干音'概念（宫、徵、羽）")
    print("   • 装饰音丰富但不改变基本音阶结构")
    
    print(f"\n🎯 对拓扑分析的启示:")
    print("   • 吸引子应该主要位于五声音阶的骨干音上")
    print("   • 全音距离比半音距离更有音乐意义")
    print("   • 小三度关系（角-徵、羽-宫）应被视为重要关联")
    print("   • 半音装饰不应过度影响整体拓扑结构")
    
    # 5. 重新定义阈值
    print(f"\n5. 📏 基于中国传统音乐的阈值重新定义")
    print("-" * 60)
    
    print("🎵 传统阈值定义问题:")
    print("   • 原阈值基于西方音乐理论")
    print("   • 未考虑中国音乐的音程特点")
    print("   • 对小三度关系重视不足")
    
    print(f"\n✅ 新的阈值定义建议:")
    
    # 基于全音单位重新计算关键音程的对齐度
    key_intervals_whole_tone = {
        '同音': 0.0,
        '全音': 1.0,
        '小三度': 1.5,
        '大三度': 2.0,
        '纯四度': 2.5,
        '纯五度': 3.5
    }
    
    print(f"\n{'音程':<8} {'全音数':<8} {'对齐度':<10} {'建议分类'}")
    print("-" * 40)
    
    for interval, whole_tones in key_intervals_whole_tone.items():
        alignment = 1.0 / (1.0 + whole_tones) if whole_tones > 0 else 1.0
        
        if alignment >= 0.5:
            category = "强关联"
        elif alignment >= 0.25:
            category = "中等关联"
        else:
            category = "弱关联"
        
        print(f"{interval:<8} {whole_tones:<8.1f} {alignment:<10.3f} {category}")
    
    print(f"\n🎯 建议的新阈值:")
    print(f"   • 强关联: ≥ 0.5 (同音、全音)")
    print(f"   • 中等关联: 0.25-0.5 (小三度、大三度、纯四度)")
    print(f"   • 弱关联: < 0.25 (纯五度及以上)")
    
    print(f"\n📚 理论依据:")
    print(f"   • 强关联：同音和全音是五声音阶的基本构建单位")
    print(f"   • 中等关联：三度和四度是重要的和谐音程")
    print(f"   • 弱关联：五度虽然协和但距离较远")

def demonstrate_chinese_music_example():
    """演示中国传统音乐的分析例子"""
    print(f"\n6. 🎼 中国传统音乐分析示例")
    print("-" * 60)
    
    print("🎵 分析曲目：《茉莉花》片段")
    print("调式：G宫调五声音阶")
    
    # 茉莉花的一个片段（简化）
    # 假设旋律：G A B D E (宫商角徵羽)
    melody_notes = [67, 69, 71, 74, 76]  # G A B D E
    note_names = ['G(宫)', 'A(商)', 'B(角)', 'D(徵)', 'E(羽)']
    
    # 假设主要吸引子在G(宫)
    attractor_position = 67  # G
    
    print(f"\n旋律片段: G-A-B-D-E")
    print(f"主吸引子: G(宫) - MIDI {attractor_position}")
    
    print(f"\n{'音符':<8} {'MIDI':<6} {'距离(半音)':<10} {'距离(全音)':<10} {'半音对齐度':<10} {'全音对齐度':<10} {'五声地位'}")
    print("-" * 85)
    
    total_alignment_semitone = 0
    total_alignment_whole_tone = 0
    
    for i, note in enumerate(melody_notes):
        distance_semitones = abs(note - attractor_position)
        distance_whole_tones = distance_semitones / 2.0
        
        alignment_semitone = 1.0 / (1.0 + distance_semitones) if distance_semitones > 0 else 1.0
        alignment_whole_tone = 1.0 / (1.0 + distance_whole_tones) if distance_whole_tones > 0 else 1.0
        
        total_alignment_semitone += alignment_semitone
        total_alignment_whole_tone += alignment_whole_tone
        
        # 确定五声地位
        pentatonic_roles = ['宫(主音)', '商(上主音)', '角(中音)', '徵(属音)', '羽(下主音)']
        role = pentatonic_roles[i]
        
        print(f"{note_names[i]:<8} {note:<6} {distance_semitones:<10} {distance_whole_tones:<10.1f} {alignment_semitone:<10.3f} {alignment_whole_tone:<10.3f} {role}")
    
    avg_alignment_semitone = total_alignment_semitone / len(melody_notes)
    avg_alignment_whole_tone = total_alignment_whole_tone / len(melody_notes)
    
    print(f"\n📊 分析结果:")
    print(f"   半音单位平均对齐度: {avg_alignment_semitone:.3f}")
    print(f"   全音单位平均对齐度: {avg_alignment_whole_tone:.3f}")
    print(f"   提升幅度: {((avg_alignment_whole_tone - avg_alignment_semitone) / avg_alignment_semitone * 100):.1f}%")
    
    print(f"\n🎯 音乐学意义:")
    print(f"   • 全音单位更好地反映了五声音阶的内在结构")
    print(f"   • 商、角、徵、羽与宫的关系得到更合理的量化")
    print(f"   • 符合中国传统音乐理论的音程重要性排序")

def conclusion_for_chinese_music():
    """中国传统音乐分析的结论"""
    print(f"\n7. 🏆 结论：全音单位在中国传统音乐分析中的优势")
    print("-" * 60)
    
    print(f"🎵 文化音乐学依据:")
    print(f"   ✅ 符合中国传统音乐理论：全音是基本音程单位")
    print(f"   ✅ 反映五声音阶特征：宫商角徵羽的音程关系")
    print(f"   ✅ 减少半音干扰：装饰性半音不过度影响分析")
    print(f"   ✅ 突出骨干音地位：重要音级获得应有的权重")
    
    print(f"\n📊 数值分析优势:")
    print(f"   ✅ 更大的对齐度范围：提高区分能力")
    print(f"   ✅ 更合理的阈值分布：符合中国音乐的音程重要性")
    print(f"   ✅ 更好的音乐意义：小三度获得中等关联地位")
    
    print(f"\n🔬 方法论创新:")
    print(f"   ✅ 文化特异性：针对中国音乐的专门化分析框架")
    print(f"   ✅ 理论一致性：与传统音乐理论高度吻合")
    print(f"   ✅ 实用性：更准确地反映中国音乐的结构特征")
    
    print(f"\n📝 论文表述建议:")
    print(f'   "考虑到本研究分析的是中国传统音乐，而非西方音乐，')
    print(f'   距离单位的选择必须符合中国传统音乐理论。在中国传统')
    print(f'   音乐中，全音（大二度）是构建五声音阶的基本单位，')
    print(f'   而半音使用量极少，主要出现在装饰音中。因此，本研究')
    print(f'   采用全音作为距离单位，计算公式为：')
    print(f'   对齐度 = 1/(1+距离_全音)，其中距离以全音为单位。')
    print(f'   这一选择不仅符合中国传统音乐理论，也产生了更合理')
    print(f'   的数值分布和音乐学意义。"')

if __name__ == "__main__":
    print("🏮 中国传统音乐的拓扑分析框架")
    print("基于全音单位的文化音乐学方法")
    
    # 1. 理论基础分析
    analyze_chinese_traditional_music_theory()
    
    # 2. 音乐特征分析
    analyze_chinese_music_characteristics()
    
    # 3. 实例演示
    demonstrate_chinese_music_example()
    
    # 4. 总结
    conclusion_for_chinese_music()
    
    print(f"\n🎉 中国传统音乐分析框架构建完成！")
    print(f"✅ 全音单位选择具有充分的文化音乐学依据")
    print(f"🎼 为中国传统音乐提供了专门化的拓扑分析方法")
