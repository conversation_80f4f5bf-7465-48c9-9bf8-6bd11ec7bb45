2025-06-12 10:54:08 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:08 - MusicAnalysis - INFO - 发现 50 个有效MIDI文件
2025-06-12 10:54:08 - MusicAnalysis - INFO - 开始批量分析 50 个文件
2025-06-12 10:54:10 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:10 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:10 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲4~玉鹤轩琵琶谱.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:10 - MusicAnalysis - INFO - 开始分析文件: midi_files/云庆.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 开始分析文件: midi_files/《四静板》交.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:10 - MusicAnalysis - INFO - 开始分析文件: midi_files/自来生长.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 成功加载MIDI: 云庆.mid (1582 音符)
2025-06-12 10:54:10 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲4~玉鹤轩琵琶谱.mid (1684 音符)
2025-06-12 10:54:10 - MusicAnalysis - INFO - 识别到 1469 个三音组，其中严格三音组 672 个
2025-06-12 10:54:10 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 512, 严格三音组 333
2025-06-12 10:54:10 - MusicAnalysis - INFO - 文件分析完成: 云庆.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 识别到 1630 个三音组，其中严格三音组 749 个
2025-06-12 10:54:10 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:10 - MusicAnalysis - INFO - 开始分析文件: midi_files/《四时景》交.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 成功加载MIDI: 《四静板》交.mid (2194 音符)
2025-06-12 10:54:10 - MusicAnalysis - INFO - 成功加载MIDI: 自来生长.mid (2169 音符)
2025-06-12 10:54:10 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 598, 严格三音组 353
2025-06-12 10:54:10 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲4~玉鹤轩琵琶谱.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:10 - MusicAnalysis - INFO - 开始分析文件: midi_files/海青拿鹤.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 识别到 1990 个三音组，其中严格三音组 654 个
2025-06-12 10:54:10 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 442, 严格三音组 324
2025-06-12 10:54:10 - MusicAnalysis - INFO - 文件分析完成: 《四静板》交.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:10 - MusicAnalysis - INFO - 开始分析文件: midi_files/夕阳萧鼓2.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 识别到 1651 个三音组，其中严格三音组 248 个
2025-06-12 10:54:10 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 206, 严格三音组 130
2025-06-12 10:54:10 - MusicAnalysis - INFO - 文件分析完成: 自来生长.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 成功加载MIDI: 《四时景》交.mid (1424 音符)
2025-06-12 10:54:10 - MusicAnalysis - INFO - 识别到 1178 个三音组，其中严格三音组 481 个
2025-06-12 10:54:10 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 342, 严格三音组 239
2025-06-12 10:54:10 - MusicAnalysis - INFO - 文件分析完成: 《四时景》交.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 成功加载MIDI: 夕阳萧鼓2.mid (1451 音符)
2025-06-12 10:54:10 - MusicAnalysis - INFO - 识别到 1369 个三音组，其中严格三音组 662 个
2025-06-12 10:54:10 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 559, 严格三音组 321
2025-06-12 10:54:10 - MusicAnalysis - INFO - 文件分析完成: 夕阳萧鼓2.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:10 - MusicAnalysis - INFO - 开始分析文件: midi_files/阳春白雪.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:10 - MusicAnalysis - INFO - 开始分析文件: midi_files/《走马》交.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:10 - MusicAnalysis - INFO - 开始分析文件: midi_files/龙船.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 成功加载MIDI: 海青拿鹤.mid (2870 音符)
2025-06-12 10:54:10 - MusicAnalysis - INFO - 识别到 2733 个三音组，其中严格三音组 1195 个
2025-06-12 10:54:10 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 919, 严格三音组 618
2025-06-12 10:54:10 - MusicAnalysis - INFO - 文件分析完成: 海青拿鹤.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:10 - MusicAnalysis - INFO - 开始分析文件: midi_files/普庵咒3.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 成功加载MIDI: 《走马》交.mid (1530 音符)
2025-06-12 10:54:10 - MusicAnalysis - INFO - 成功加载MIDI: 阳春白雪.mid (1623 音符)
2025-06-12 10:54:10 - MusicAnalysis - INFO - 识别到 1100 个三音组，其中严格三音组 510 个
2025-06-12 10:54:10 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 332, 严格三音组 259
2025-06-12 10:54:10 - MusicAnalysis - INFO - 文件分析完成: 《走马》交.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 识别到 1457 个三音组，其中严格三音组 781 个
2025-06-12 10:54:10 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:10 - MusicAnalysis - INFO - 开始分析文件: midi_files/浔阳琵琶1.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 562, 严格三音组 402
2025-06-12 10:54:10 - MusicAnalysis - INFO - 文件分析完成: 阳春白雪.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:10 - MusicAnalysis - INFO - 开始分析文件: midi_files/陈隋古音.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 成功加载MIDI: 龙船.mid (1596 音符)
2025-06-12 10:54:10 - MusicAnalysis - INFO - 识别到 1215 个三音组，其中严格三音组 504 个
2025-06-12 10:54:10 - MusicAnalysis - INFO - 成功加载MIDI: 普庵咒3.mid (2258 音符)
2025-06-12 10:54:10 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 368, 严格三音组 250
2025-06-12 10:54:10 - MusicAnalysis - INFO - 文件分析完成: 龙船.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:10 - MusicAnalysis - INFO - 开始分析文件: midi_files/浔阳琵琶2.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 识别到 2203 个三音组，其中严格三音组 1042 个
2025-06-12 10:54:10 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 925, 严格三音组 520
2025-06-12 10:54:10 - MusicAnalysis - INFO - 文件分析完成: 普庵咒3.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:10 - MusicAnalysis - INFO - 开始分析文件: midi_files/普庵咒1.mid
2025-06-12 10:54:10 - MusicAnalysis - INFO - 成功加载MIDI: 浔阳琵琶1.mid (1689 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 陈隋古音.mid (1692 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1544 个三音组，其中严格三音组 737 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1476 个三音组，其中严格三音组 750 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 564, 严格三音组 376
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 浔阳琵琶1.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 558, 严格三音组 379
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 陈隋古音.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/《梅花操》交.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲2。.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 浔阳琵琶2.mid (2043 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1800 个三音组，其中严格三音组 892 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 普庵咒1.mid (2122 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 709, 严格三音组 451
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 浔阳琵琶2.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 《梅花操》交.mid (1569 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/塞上曲2.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 2090 个三音组，其中严格三音组 1247 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1377 个三音组，其中严格三音组 563 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 928, 严格三音组 615
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 普庵咒1.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 329, 严格三音组 275
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 《梅花操》交.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/塞上曲.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/照见我.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲2。.mid (3117 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 塞上曲2.mid (1468 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1361 个三音组，其中严格三音组 526 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 2834 个三音组，其中严格三音组 1205 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 482, 严格三音组 262
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 塞上曲2.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/海青拿鹅1～玉鹤轩琵琶谱.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 1130, 严格三音组 583
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲2。.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 塞上曲.mid (1532 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1480 个三音组，其中严格三音组 651 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 540, 严格三音组 323
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 塞上曲.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 照见我.mid (3319 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 2176 个三音组，其中严格三音组 637 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 417, 严格三音组 322
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 照见我.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 海青拿鹅1～玉鹤轩琵琶谱.mid (2673 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/行街四合.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/浔阳夜月.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 2628 个三音组，其中严格三音组 1307 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/十面埋伏1.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 991, 严格三音组 672
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 海青拿鹅1～玉鹤轩琵琶谱.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/阳春古曲2.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 行街四合.mid (2229 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 2116 个三音组，其中严格三音组 1266 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 909, 严格三音组 633
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 行街四合.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 阳春古曲2.mid (1961 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/十面埋伏2.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1655 个三音组，其中严格三音组 883 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 十面埋伏1.mid (2669 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 591, 严格三音组 438
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 阳春古曲2.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/必肝跋碎.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 2522 个三音组，其中严格三音组 1240 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 1044, 严格三音组 615
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 十面埋伏1.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/一纸相思.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 浔阳夜月.mid (1527 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1280 个三音组，其中严格三音组 460 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 395, 严格三音组 242
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 浔阳夜月.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/灯月交辉.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 一纸相思.mid (1945 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 必肝跋碎.mid (2354 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1499 个三音组，其中严格三音组 231 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 200, 严格三音组 121
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 一纸相思.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/霓裳曲.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 灯月交辉.mid (2345 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1985 个三音组，其中严格三音组 711 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 599, 严格三音组 368
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 灯月交辉.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/趁赏花灯.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 十面埋伏2.mid (2901 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 2570 个三音组，其中严格三音组 1249 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 983, 严格三音组 617
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 十面埋伏2.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 霓裳曲.mid (1756 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/十面埋伏3 ~玉鹤轩琵琶谱.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1735 个三音组，其中严格三音组 318 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1649 个三音组，其中严格三音组 840 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 252, 严格三音组 141
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 必肝跋碎.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 623, 严格三音组 400
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 霓裳曲.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 趁赏花灯.mid (2309 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 十面埋伏3 ~玉鹤轩琵琶谱.mid (2221 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1721 个三音组，其中严格三音组 291 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 242, 严格三音组 150
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 趁赏花灯.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1809 个三音组，其中严格三音组 600 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 475, 严格三音组 289
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 十面埋伏3 ~玉鹤轩琵琶谱.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/平沙落雁3.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/北派将军令.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/因为欢喜.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/平沙落雁2.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 平沙落雁2.mid (1695 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1611 个三音组，其中严格三音组 871 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 660, 严格三音组 425
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 平沙落雁2.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/阳关三叠 交.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 平沙落雁3.mid (2097 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 因为欢喜.mid (2289 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 北派将军令.mid (1877 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1868 个三音组，其中严格三音组 894 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1505 个三音组，其中严格三音组 366 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 724, 严格三音组 455
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 平沙落雁3.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 232, 严格三音组 179
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 因为欢喜.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/平沙落雁1.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/月儿高3.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1617 个三音组，其中严格三音组 180 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 142, 严格三音组 84
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 北派将军令.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 阳关三叠 交.mid (2265 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲3.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 平沙落雁1.mid (2335 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1376 个三音组，其中严格三音组 351 个
2025-06-12 10:54:11 - MusicAnalysis - INFO - 成功加载MIDI: 月儿高3.mid (1753 音符)
2025-06-12 10:54:11 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 300, 严格三音组 177
2025-06-12 10:54:11 - MusicAnalysis - INFO - 文件分析完成: 阳关三叠 交.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:11 - MusicAnalysis - INFO - 开始分析文件: midi_files/三六.mid
2025-06-12 10:54:11 - MusicAnalysis - INFO - 识别到 1977 个三音组，其中严格三音组 794 个
2025-06-12 10:54:12 - MusicAnalysis - INFO - 识别到 1688 个三音组，其中严格三音组 778 个
2025-06-12 10:54:12 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 646, 严格三音组 397
2025-06-12 10:54:12 - MusicAnalysis - INFO - 文件分析完成: 平沙落雁1.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 591, 严格三音组 399
2025-06-12 10:54:12 - MusicAnalysis - INFO - 文件分析完成: 月儿高3.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:12 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲2.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:12 - MusicAnalysis - INFO - 开始分析文件: midi_files/平沙落雁4.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 成功加载MIDI: 三六.mid (796 音符)
2025-06-12 10:54:12 - MusicAnalysis - INFO - 识别到 690 个三音组，其中严格三音组 215 个
2025-06-12 10:54:12 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 180, 严格三音组 95
2025-06-12 10:54:12 - MusicAnalysis - INFO - 文件分析完成: 三六.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:12 - MusicAnalysis - INFO - 开始分析文件: midi_files/月儿高2.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲3.mid (2071 音符)
2025-06-12 10:54:12 - MusicAnalysis - INFO - 识别到 1854 个三音组，其中严格三音组 844 个
2025-06-12 10:54:12 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 716, 严格三音组 427
2025-06-12 10:54:12 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲3.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:12 - MusicAnalysis - INFO - 开始分析文件: midi_files/老六板.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 成功加载MIDI: 平沙落雁4.mid (2324 音符)
2025-06-12 10:54:12 - MusicAnalysis - INFO - 识别到 2098 个三音组，其中严格三音组 884 个
2025-06-12 10:54:12 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 682, 严格三音组 444
2025-06-12 10:54:12 - MusicAnalysis - INFO - 文件分析完成: 平沙落雁4.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:12 - MusicAnalysis - INFO - 开始分析文件: midi_files/普庵咒4 ～玉鹤轩琵琶谱.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲2.mid (2883 音符)
2025-06-12 10:54:12 - MusicAnalysis - INFO - 成功加载MIDI: 月儿高2.mid (1799 音符)
2025-06-12 10:54:12 - MusicAnalysis - INFO - 成功加载MIDI: 老六板.mid (1524 音符)
2025-06-12 10:54:12 - MusicAnalysis - INFO - 识别到 1741 个三音组，其中严格三音组 797 个
2025-06-12 10:54:12 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 640, 严格三音组 411
2025-06-12 10:54:12 - MusicAnalysis - INFO - 文件分析完成: 月儿高2.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 识别到 1445 个三音组，其中严格三音组 654 个
2025-06-12 10:54:12 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:12 - MusicAnalysis - INFO - 开始分析文件: midi_files/灯月交辉2.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 550, 严格三音组 331
2025-06-12 10:54:12 - MusicAnalysis - INFO - 文件分析完成: 老六板.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 成功加载MIDI: 普庵咒4 ～玉鹤轩琵琶谱.mid (1496 音符)
2025-06-12 10:54:12 - MusicAnalysis - INFO - 识别到 1477 个三音组，其中严格三音组 833 个
2025-06-12 10:54:12 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 659, 严格三音组 420
2025-06-12 10:54:12 - MusicAnalysis - INFO - 文件分析完成: 普庵咒4 ～玉鹤轩琵琶谱.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 识别到 2612 个三音组，其中严格三音组 1231 个
2025-06-12 10:54:12 - MusicAnalysis - INFO - 成功加载MIDI: 灯月交辉2.mid (1662 音符)
2025-06-12 10:54:12 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 1023, 严格三音组 606
2025-06-12 10:54:12 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲2.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 识别到 1400 个三音组，其中严格三音组 457 个
2025-06-12 10:54:12 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 417, 严格三音组 217
2025-06-12 10:54:12 - MusicAnalysis - INFO - 文件分析完成: 灯月交辉2.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:12 - MusicAnalysis - INFO - 开始分析文件: midi_files/六板.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:12 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲1.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 10:54:12 - MusicAnalysis - INFO - 开始分析文件: midi_files/月儿高1.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 成功加载MIDI: 六板.mid (1756 音符)
2025-06-12 10:54:12 - MusicAnalysis - INFO - 识别到 1571 个三音组，其中严格三音组 779 个
2025-06-12 10:54:12 - MusicAnalysis - INFO - 成功加载MIDI: 月儿高1.mid (1597 音符)
2025-06-12 10:54:12 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 577, 严格三音组 384
2025-06-12 10:54:12 - MusicAnalysis - INFO - 文件分析完成: 六板.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲1.mid (2124 音符)
2025-06-12 10:54:12 - MusicAnalysis - INFO - 识别到 1528 个三音组，其中严格三音组 737 个
2025-06-12 10:54:12 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 555, 严格三音组 379
2025-06-12 10:54:12 - MusicAnalysis - INFO - 文件分析完成: 月儿高1.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 识别到 1773 个三音组，其中严格三音组 746 个
2025-06-12 10:54:12 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 578, 严格三音组 350
2025-06-12 10:54:12 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲1.mid
2025-06-12 10:54:12 - MusicAnalysis - INFO - 批量分析完成: 成功 50, 失败 0
2025-06-12 10:54:12 - MusicAnalysis - INFO - 开始生成分析报告...
2025-06-12 10:54:12 - MusicAnalysis - INFO - 分析结果已保存到: analysis_results/music_analysis_report.csv
2025-06-12 10:54:12 - MusicAnalysis - INFO - 统计摘要已保存: analysis_results/music_analysis_report_summary.txt
2025-06-12 10:54:13 - MusicAnalysis - INFO - 特征对比图已保存: music_analysis_report_feature_comparison.png
2025-06-12 10:54:13 - MusicAnalysis - ERROR - 生成可视化时出错: 'FigureCanvasInterAgg' object has no attribute 'tostring_rgb'
2025-06-12 10:54:13 - MusicAnalysis - INFO - 分析报告生成完成: music_analysis_report
2025-06-12 14:05:47 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:47 - MusicAnalysis - INFO - 发现 50 个有效MIDI文件
2025-06-12 14:05:47 - MusicAnalysis - INFO - 开始批量分析 50 个文件
2025-06-12 14:05:49 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:49 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲4~玉鹤轩琵琶谱.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:49 - MusicAnalysis - INFO - 开始分析文件: midi_files/云庆.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:49 - MusicAnalysis - INFO - 开始分析文件: midi_files/《四静板》交.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:49 - MusicAnalysis - INFO - 开始分析文件: midi_files/自来生长.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 成功加载MIDI: 云庆.mid (1582 音符)
2025-06-12 14:05:49 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲4~玉鹤轩琵琶谱.mid (1684 音符)
2025-06-12 14:05:49 - MusicAnalysis - INFO - 识别到 1469 个三音组，其中严格三音组 672 个
2025-06-12 14:05:49 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 512, 严格三音组 333
2025-06-12 14:05:49 - MusicAnalysis - INFO - 文件分析完成: 云庆.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 识别到 1630 个三音组，其中严格三音组 749 个
2025-06-12 14:05:49 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:49 - MusicAnalysis - INFO - 开始分析文件: midi_files/《四时景》交.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 成功加载MIDI: 《四静板》交.mid (2194 音符)
2025-06-12 14:05:49 - MusicAnalysis - INFO - 成功加载MIDI: 自来生长.mid (2169 音符)
2025-06-12 14:05:49 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 598, 严格三音组 353
2025-06-12 14:05:49 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲4~玉鹤轩琵琶谱.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:49 - MusicAnalysis - INFO - 开始分析文件: midi_files/海青拿鹤.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 识别到 1990 个三音组，其中严格三音组 654 个
2025-06-12 14:05:49 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 442, 严格三音组 324
2025-06-12 14:05:49 - MusicAnalysis - INFO - 文件分析完成: 《四静板》交.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:49 - MusicAnalysis - INFO - 开始分析文件: midi_files/夕阳萧鼓2.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 识别到 1651 个三音组，其中严格三音组 248 个
2025-06-12 14:05:49 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 206, 严格三音组 130
2025-06-12 14:05:49 - MusicAnalysis - INFO - 文件分析完成: 自来生长.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 成功加载MIDI: 《四时景》交.mid (1424 音符)
2025-06-12 14:05:49 - MusicAnalysis - INFO - 识别到 1178 个三音组，其中严格三音组 481 个
2025-06-12 14:05:49 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 342, 严格三音组 239
2025-06-12 14:05:49 - MusicAnalysis - INFO - 文件分析完成: 《四时景》交.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 成功加载MIDI: 夕阳萧鼓2.mid (1451 音符)
2025-06-12 14:05:49 - MusicAnalysis - INFO - 识别到 1369 个三音组，其中严格三音组 662 个
2025-06-12 14:05:49 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 559, 严格三音组 321
2025-06-12 14:05:49 - MusicAnalysis - INFO - 文件分析完成: 夕阳萧鼓2.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:49 - MusicAnalysis - INFO - 开始分析文件: midi_files/阳春白雪.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:49 - MusicAnalysis - INFO - 开始分析文件: midi_files/《走马》交.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:49 - MusicAnalysis - INFO - 开始分析文件: midi_files/龙船.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 成功加载MIDI: 海青拿鹤.mid (2870 音符)
2025-06-12 14:05:49 - MusicAnalysis - INFO - 识别到 2733 个三音组，其中严格三音组 1195 个
2025-06-12 14:05:49 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 919, 严格三音组 618
2025-06-12 14:05:49 - MusicAnalysis - INFO - 文件分析完成: 海青拿鹤.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:49 - MusicAnalysis - INFO - 开始分析文件: midi_files/普庵咒3.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 成功加载MIDI: 《走马》交.mid (1530 音符)
2025-06-12 14:05:49 - MusicAnalysis - INFO - 识别到 1100 个三音组，其中严格三音组 510 个
2025-06-12 14:05:49 - MusicAnalysis - INFO - 成功加载MIDI: 阳春白雪.mid (1623 音符)
2025-06-12 14:05:49 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 332, 严格三音组 259
2025-06-12 14:05:49 - MusicAnalysis - INFO - 文件分析完成: 《走马》交.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:49 - MusicAnalysis - INFO - 开始分析文件: midi_files/浔阳琵琶1.mid
2025-06-12 14:05:49 - MusicAnalysis - INFO - 识别到 1457 个三音组，其中严格三音组 781 个
2025-06-12 14:05:49 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 562, 严格三音组 402
2025-06-12 14:05:49 - MusicAnalysis - INFO - 文件分析完成: 阳春白雪.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/陈隋古音.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 龙船.mid (1596 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 1215 个三音组，其中严格三音组 504 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 普庵咒3.mid (2258 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 368, 严格三音组 250
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 龙船.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/浔阳琵琶2.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 2203 个三音组，其中严格三音组 1042 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 925, 严格三音组 520
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 普庵咒3.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 浔阳琵琶1.mid (1689 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/普庵咒1.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 1544 个三音组，其中严格三音组 737 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 陈隋古音.mid (1692 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 564, 严格三音组 376
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 浔阳琵琶1.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/《梅花操》交.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 1476 个三音组，其中严格三音组 750 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 558, 严格三音组 379
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 陈隋古音.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲2。.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 《梅花操》交.mid (1569 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 浔阳琵琶2.mid (2043 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 1377 个三音组，其中严格三音组 563 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 普庵咒1.mid (2122 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 329, 严格三音组 275
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 《梅花操》交.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 1800 个三音组，其中严格三音组 892 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/塞上曲2.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 2090 个三音组，其中严格三音组 1247 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 709, 严格三音组 451
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 浔阳琵琶2.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/塞上曲.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 928, 严格三音组 615
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 普庵咒1.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/照见我.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲2。.mid (3117 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 塞上曲2.mid (1468 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 2834 个三音组，其中严格三音组 1205 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 1361 个三音组，其中严格三音组 526 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 1130, 严格三音组 583
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲2。.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 塞上曲.mid (1532 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 482, 严格三音组 262
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 塞上曲2.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/海青拿鹅1～玉鹤轩琵琶谱.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 1480 个三音组，其中严格三音组 651 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 540, 严格三音组 323
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 塞上曲.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 照见我.mid (3319 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 海青拿鹅1～玉鹤轩琵琶谱.mid (2673 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/行街四合.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/浔阳夜月.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 2628 个三音组，其中严格三音组 1307 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 991, 严格三音组 672
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 海青拿鹅1～玉鹤轩琵琶谱.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/十面埋伏1.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 浔阳夜月.mid (1527 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 2176 个三音组，其中严格三音组 637 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 1280 个三音组，其中严格三音组 460 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 417, 严格三音组 322
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 照见我.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 395, 严格三音组 242
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 浔阳夜月.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/阳春古曲2.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/十面埋伏2.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 行街四合.mid (2229 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 2116 个三音组，其中严格三音组 1266 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 909, 严格三音组 633
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 行街四合.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/必肝跋碎.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 阳春古曲2.mid (1961 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 十面埋伏1.mid (2669 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 1655 个三音组，其中严格三音组 883 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 591, 严格三音组 438
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 阳春古曲2.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/一纸相思.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 十面埋伏2.mid (2901 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 2570 个三音组，其中严格三音组 1249 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 983, 严格三音组 617
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 十面埋伏2.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/灯月交辉.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 2522 个三音组，其中严格三音组 1240 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 1044, 严格三音组 615
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 十面埋伏1.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 一纸相思.mid (1945 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/霓裳曲.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 1499 个三音组，其中严格三音组 231 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 200, 严格三音组 121
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 一纸相思.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/趁赏花灯.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 必肝跋碎.mid (2354 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 1735 个三音组，其中严格三音组 318 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 252, 严格三音组 141
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 必肝跋碎.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/十面埋伏3 ~玉鹤轩琵琶谱.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 灯月交辉.mid (2345 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 霓裳曲.mid (1756 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 1985 个三音组，其中严格三音组 711 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 1649 个三音组，其中严格三音组 840 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 趁赏花灯.mid (2309 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 599, 严格三音组 368
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 灯月交辉.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 623, 严格三音组 400
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 霓裳曲.mid
2025-06-12 14:05:50 - MusicAnalysis - INFO - 成功加载MIDI: 十面埋伏3 ~玉鹤轩琵琶谱.mid (2221 音符)
2025-06-12 14:05:50 - MusicAnalysis - INFO - 识别到 1809 个三音组，其中严格三音组 600 个
2025-06-12 14:05:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 475, 严格三音组 289
2025-06-12 14:05:50 - MusicAnalysis - INFO - 文件分析完成: 十面埋伏3 ~玉鹤轩琵琶谱.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/平沙落雁3.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 1721 个三音组，其中严格三音组 291 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/北派将军令.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 242, 严格三音组 150
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 趁赏花灯.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/因为欢喜.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/平沙落雁2.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 成功加载MIDI: 平沙落雁2.mid (1695 音符)
2025-06-12 14:05:51 - MusicAnalysis - INFO - 成功加载MIDI: 平沙落雁3.mid (2097 音符)
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 1611 个三音组，其中严格三音组 871 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 成功加载MIDI: 北派将军令.mid (1877 音符)
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 660, 严格三音组 425
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 平沙落雁2.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/阳关三叠 交.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 1868 个三音组，其中严格三音组 894 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 1617 个三音组，其中严格三音组 180 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 142, 严格三音组 84
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 北派将军令.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 724, 严格三音组 455
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 平沙落雁3.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 成功加载MIDI: 因为欢喜.mid (2289 音符)
2025-06-12 14:05:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/平沙落雁1.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/月儿高3.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 1505 个三音组，其中严格三音组 366 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 232, 严格三音组 179
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 因为欢喜.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲3.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 成功加载MIDI: 月儿高3.mid (1753 音符)
2025-06-12 14:05:51 - MusicAnalysis - INFO - 成功加载MIDI: 阳关三叠 交.mid (2265 音符)
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 1688 个三音组，其中严格三音组 778 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 1376 个三音组，其中严格三音组 351 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 成功加载MIDI: 平沙落雁1.mid (2335 音符)
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 591, 严格三音组 399
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 月儿高3.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲3.mid (2071 音符)
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 300, 严格三音组 177
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 阳关三叠 交.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/三六.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲2.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 1977 个三音组，其中严格三音组 794 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 1854 个三音组，其中严格三音组 844 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 646, 严格三音组 397
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 平沙落雁1.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 716, 严格三音组 427
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲3.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/平沙落雁4.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/月儿高2.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 成功加载MIDI: 三六.mid (796 音符)
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 690 个三音组，其中严格三音组 215 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 180, 严格三音组 95
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 三六.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/老六板.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 成功加载MIDI: 月儿高2.mid (1799 音符)
2025-06-12 14:05:51 - MusicAnalysis - INFO - 成功加载MIDI: 平沙落雁4.mid (2324 音符)
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 1741 个三音组，其中严格三音组 797 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 640, 严格三音组 411
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 月儿高2.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/普庵咒4 ～玉鹤轩琵琶谱.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲2.mid (2883 音符)
2025-06-12 14:05:51 - MusicAnalysis - INFO - 成功加载MIDI: 老六板.mid (1524 音符)
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 1445 个三音组，其中严格三音组 654 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 2612 个三音组，其中严格三音组 1231 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 550, 严格三音组 331
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 老六板.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/灯月交辉2.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 1023, 严格三音组 606
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲2.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/六板.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 成功加载MIDI: 普庵咒4 ～玉鹤轩琵琶谱.mid (1496 音符)
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 2098 个三音组，其中严格三音组 884 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 1477 个三音组，其中严格三音组 833 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 682, 严格三音组 444
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 659, 严格三音组 420
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 平沙落雁4.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 普庵咒4 ～玉鹤轩琵琶谱.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 成功加载MIDI: 灯月交辉2.mid (1662 音符)
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 1400 个三音组，其中严格三音组 457 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 417, 严格三音组 217
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 灯月交辉2.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 成功加载MIDI: 六板.mid (1756 音符)
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 1571 个三音组，其中严格三音组 779 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 577, 严格三音组 384
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 六板.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲1.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:05:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/月儿高1.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 成功加载MIDI: 月儿高1.mid (1597 音符)
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 1528 个三音组，其中严格三音组 737 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲1.mid (2124 音符)
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 555, 严格三音组 379
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 月儿高1.mid
2025-06-12 14:05:51 - MusicAnalysis - INFO - 识别到 1773 个三音组，其中严格三音组 746 个
2025-06-12 14:05:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 578, 严格三音组 350
2025-06-12 14:05:51 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲1.mid
2025-06-12 14:05:52 - MusicAnalysis - INFO - 批量分析完成: 成功 50, 失败 0
2025-06-12 14:05:52 - MusicAnalysis - INFO - 开始生成分析报告...
2025-06-12 14:05:52 - MusicAnalysis - INFO - 分析结果已保存到: analysis_results/music_analysis_report.csv
2025-06-12 14:05:52 - MusicAnalysis - INFO - 统计摘要已保存: analysis_results/music_analysis_report_summary.txt
2025-06-12 14:05:52 - MusicAnalysis - INFO - 特征对比图已保存: music_analysis_report_feature_comparison.png
2025-06-12 14:05:53 - MusicAnalysis - ERROR - 生成可视化时出错: 'FigureCanvasInterAgg' object has no attribute 'tostring_rgb'
2025-06-12 14:05:53 - MusicAnalysis - INFO - 分析报告生成完成: music_analysis_report
2025-06-12 14:07:48 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:48 - MusicAnalysis - INFO - 发现 50 个有效MIDI文件
2025-06-12 14:07:48 - MusicAnalysis - INFO - 开始批量分析 50 个文件
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲4~玉鹤轩琵琶谱.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/云庆.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/《四静板》交.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/自来生长.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 云庆.mid (1582 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲4~玉鹤轩琵琶谱.mid (1684 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 1469 个三音组，其中严格三音组 672 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 512, 严格三音组 333
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 云庆.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 1630 个三音组，其中严格三音组 749 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/《四时景》交.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 《四静板》交.mid (2194 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 自来生长.mid (2169 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 598, 严格三音组 353
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲4~玉鹤轩琵琶谱.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/海青拿鹤.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 1990 个三音组，其中严格三音组 654 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 442, 严格三音组 324
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 《四静板》交.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/夕阳萧鼓2.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 1651 个三音组，其中严格三音组 248 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 206, 严格三音组 130
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 自来生长.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 《四时景》交.mid (1424 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 1178 个三音组，其中严格三音组 481 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 342, 严格三音组 239
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 《四时景》交.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 夕阳萧鼓2.mid (1451 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 1369 个三音组，其中严格三音组 662 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 559, 严格三音组 321
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 夕阳萧鼓2.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/阳春白雪.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/《走马》交.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/龙船.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 海青拿鹤.mid (2870 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 2733 个三音组，其中严格三音组 1195 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 919, 严格三音组 618
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 海青拿鹤.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/普庵咒3.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 《走马》交.mid (1530 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 阳春白雪.mid (1623 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 1100 个三音组，其中严格三音组 510 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 332, 严格三音组 259
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 《走马》交.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/浔阳琵琶1.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 1457 个三音组，其中严格三音组 781 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 562, 严格三音组 402
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 阳春白雪.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/陈隋古音.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 龙船.mid (1596 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 1215 个三音组，其中严格三音组 504 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 368, 严格三音组 250
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 龙船.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 普庵咒3.mid (2258 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/浔阳琵琶2.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 2203 个三音组，其中严格三音组 1042 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 925, 严格三音组 520
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 普庵咒3.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 浔阳琵琶1.mid (1689 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/普庵咒1.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 1544 个三音组，其中严格三音组 737 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 564, 严格三音组 376
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 浔阳琵琶1.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 陈隋古音.mid (1692 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/《梅花操》交.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 1476 个三音组，其中严格三音组 750 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 558, 严格三音组 379
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 陈隋古音.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲2。.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 浔阳琵琶2.mid (2043 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 1800 个三音组，其中严格三音组 892 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 709, 严格三音组 451
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 浔阳琵琶2.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 《梅花操》交.mid (1569 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/塞上曲2.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 1377 个三音组，其中严格三音组 563 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 329, 严格三音组 275
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 《梅花操》交.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 普庵咒1.mid (2122 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/塞上曲.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 2090 个三音组，其中严格三音组 1247 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 928, 严格三音组 615
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 普庵咒1.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/照见我.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 塞上曲2.mid (1468 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 1361 个三音组，其中严格三音组 526 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 482, 严格三音组 262
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 塞上曲2.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/海青拿鹅1～玉鹤轩琵琶谱.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲2。.mid (3117 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 塞上曲.mid (1532 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 2834 个三音组，其中严格三音组 1205 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 1480 个三音组，其中严格三音组 651 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 540, 严格三音组 323
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 塞上曲.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 1130, 严格三音组 583
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲2。.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/行街四合.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/浔阳夜月.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 照见我.mid (3319 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 海青拿鹅1～玉鹤轩琵琶谱.mid (2673 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 2628 个三音组，其中严格三音组 1307 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 991, 严格三音组 672
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 海青拿鹅1～玉鹤轩琵琶谱.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/十面埋伏1.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 浔阳夜月.mid (1527 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 1280 个三音组，其中严格三音组 460 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 成功加载MIDI: 行街四合.mid (2229 音符)
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 395, 严格三音组 242
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 浔阳夜月.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/阳春古曲2.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 2116 个三音组，其中严格三音组 1266 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 909, 严格三音组 633
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 行街四合.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 识别到 2176 个三音组，其中严格三音组 637 个
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/十面埋伏2.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 417, 严格三音组 322
2025-06-12 14:07:50 - MusicAnalysis - INFO - 文件分析完成: 照见我.mid
2025-06-12 14:07:50 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:50 - MusicAnalysis - INFO - 开始分析文件: midi_files/必肝跋碎.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 十面埋伏1.mid (2669 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 阳春古曲2.mid (1961 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 2522 个三音组，其中严格三音组 1240 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 1655 个三音组，其中严格三音组 883 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 1044, 严格三音组 615
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 十面埋伏1.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 591, 严格三音组 438
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 阳春古曲2.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/一纸相思.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/灯月交辉.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 必肝跋碎.mid (2354 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 1735 个三音组，其中严格三音组 318 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 252, 严格三音组 141
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 必肝跋碎.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 十面埋伏2.mid (2901 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/霓裳曲.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 2570 个三音组，其中严格三音组 1249 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 983, 严格三音组 617
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 十面埋伏2.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/趁赏花灯.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 一纸相思.mid (1945 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 霓裳曲.mid (1756 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 1649 个三音组，其中严格三音组 840 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 623, 严格三音组 400
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 霓裳曲.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/十面埋伏3 ~玉鹤轩琵琶谱.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 1499 个三音组，其中严格三音组 231 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 200, 严格三音组 121
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 一纸相思.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 趁赏花灯.mid (2309 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 灯月交辉.mid (2345 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 1985 个三音组，其中严格三音组 711 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 599, 严格三音组 368
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 灯月交辉.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 十面埋伏3 ~玉鹤轩琵琶谱.mid (2221 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 1809 个三音组，其中严格三音组 600 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 475, 严格三音组 289
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 十面埋伏3 ~玉鹤轩琵琶谱.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 1721 个三音组，其中严格三音组 291 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 242, 严格三音组 150
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 趁赏花灯.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/平沙落雁3.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/北派将军令.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/因为欢喜.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/平沙落雁2.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 平沙落雁3.mid (2097 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 北派将军令.mid (1877 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 平沙落雁2.mid (1695 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 1617 个三音组，其中严格三音组 180 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 1868 个三音组，其中严格三音组 894 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 142, 严格三音组 84
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 北派将军令.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 724, 严格三音组 455
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 1611 个三音组，其中严格三音组 871 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/阳关三叠 交.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 平沙落雁3.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/平沙落雁1.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 660, 严格三音组 425
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 平沙落雁2.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 因为欢喜.mid (2289 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/月儿高3.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 1505 个三音组，其中严格三音组 366 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 232, 严格三音组 179
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 因为欢喜.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲3.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 月儿高3.mid (1753 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 平沙落雁1.mid (2335 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 1688 个三音组，其中严格三音组 778 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 阳关三叠 交.mid (2265 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 591, 严格三音组 399
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 月儿高3.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 1977 个三音组，其中严格三音组 794 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 1376 个三音组，其中严格三音组 351 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/三六.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲3.mid (2071 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 300, 严格三音组 177
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 阳关三叠 交.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 646, 严格三音组 397
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 平沙落雁1.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲2.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/平沙落雁4.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 1854 个三音组，其中严格三音组 844 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 716, 严格三音组 427
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲3.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/月儿高2.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 三六.mid (796 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 690 个三音组，其中严格三音组 215 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 180, 严格三音组 95
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 三六.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/老六板.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 平沙落雁4.mid (2324 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 月儿高2.mid (1799 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 2098 个三音组，其中严格三音组 884 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 682, 严格三音组 444
2025-06-12 14:07:51 - MusicAnalysis - INFO - 文件分析完成: 平沙落雁4.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:51 - MusicAnalysis - INFO - 开始分析文件: midi_files/普庵咒4 ～玉鹤轩琵琶谱.mid
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲2.mid (2883 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 成功加载MIDI: 老六板.mid (1524 音符)
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 2612 个三音组，其中严格三音组 1231 个
2025-06-12 14:07:51 - MusicAnalysis - INFO - 识别到 1445 个三音组，其中严格三音组 654 个
2025-06-12 14:07:52 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 1023, 严格三音组 606
2025-06-12 14:07:52 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲2.mid
2025-06-12 14:07:52 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 550, 严格三音组 331
2025-06-12 14:07:52 - MusicAnalysis - INFO - 文件分析完成: 老六板.mid
2025-06-12 14:07:52 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:52 - MusicAnalysis - INFO - 开始分析文件: midi_files/灯月交辉2.mid
2025-06-12 14:07:52 - MusicAnalysis - INFO - 成功加载MIDI: 普庵咒4 ～玉鹤轩琵琶谱.mid (1496 音符)
2025-06-12 14:07:52 - MusicAnalysis - INFO - 识别到 1477 个三音组，其中严格三音组 833 个
2025-06-12 14:07:52 - MusicAnalysis - INFO - 识别到 1741 个三音组，其中严格三音组 797 个
2025-06-12 14:07:52 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 659, 严格三音组 420
2025-06-12 14:07:52 - MusicAnalysis - INFO - 文件分析完成: 普庵咒4 ～玉鹤轩琵琶谱.mid
2025-06-12 14:07:52 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 640, 严格三音组 411
2025-06-12 14:07:52 - MusicAnalysis - INFO - 文件分析完成: 月儿高2.mid
2025-06-12 14:07:52 - MusicAnalysis - INFO - 成功加载MIDI: 灯月交辉2.mid (1662 音符)
2025-06-12 14:07:52 - MusicAnalysis - INFO - 识别到 1400 个三音组，其中严格三音组 457 个
2025-06-12 14:07:52 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 417, 严格三音组 217
2025-06-12 14:07:52 - MusicAnalysis - INFO - 文件分析完成: 灯月交辉2.mid
2025-06-12 14:07:52 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:52 - MusicAnalysis - INFO - 开始分析文件: midi_files/六板.mid
2025-06-12 14:07:52 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:52 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲1.mid
2025-06-12 14:07:52 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:07:52 - MusicAnalysis - INFO - 开始分析文件: midi_files/月儿高1.mid
2025-06-12 14:07:52 - MusicAnalysis - INFO - 成功加载MIDI: 六板.mid (1756 音符)
2025-06-12 14:07:52 - MusicAnalysis - INFO - 识别到 1571 个三音组，其中严格三音组 779 个
2025-06-12 14:07:52 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 577, 严格三音组 384
2025-06-12 14:07:52 - MusicAnalysis - INFO - 文件分析完成: 六板.mid
2025-06-12 14:07:52 - MusicAnalysis - INFO - 成功加载MIDI: 月儿高1.mid (1597 音符)
2025-06-12 14:07:52 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲1.mid (2124 音符)
2025-06-12 14:07:52 - MusicAnalysis - INFO - 识别到 1528 个三音组，其中严格三音组 737 个
2025-06-12 14:07:52 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 555, 严格三音组 379
2025-06-12 14:07:52 - MusicAnalysis - INFO - 文件分析完成: 月儿高1.mid
2025-06-12 14:07:52 - MusicAnalysis - INFO - 识别到 1773 个三音组，其中严格三音组 746 个
2025-06-12 14:07:52 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 578, 严格三音组 350
2025-06-12 14:07:52 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲1.mid
2025-06-12 14:07:52 - MusicAnalysis - INFO - 批量分析完成: 成功 50, 失败 0
2025-06-12 14:07:52 - MusicAnalysis - INFO - 开始生成分析报告...
2025-06-12 14:07:52 - MusicAnalysis - INFO - 分析结果已保存到: analysis_results/music_analysis_report.csv
2025-06-12 14:07:52 - MusicAnalysis - INFO - 统计摘要已保存: analysis_results/music_analysis_report_summary.txt
2025-06-12 14:07:53 - MusicAnalysis - INFO - 特征对比图已保存: music_analysis_report_feature_comparison.png
2025-06-12 14:07:53 - MusicAnalysis - INFO - 成功加载MIDI: 云庆.mid (1582 音符)
2025-06-12 14:07:53 - MusicAnalysis - INFO - 识别到 1469 个三音组，其中严格三音组 672 个
2025-06-12 14:07:54 - MusicAnalysis - INFO - 三音组分析图已保存: music_analysis_report_three_note_analysis.png
2025-06-12 14:07:55 - MusicAnalysis - INFO - 音高序列图已保存: music_analysis_report_pitch_series.png
2025-06-12 14:07:55 - MusicAnalysis - INFO - 分析报告生成完成: music_analysis_report
2025-06-12 14:23:55 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:55 - MusicAnalysis - INFO - 发现 50 个有效MIDI文件
2025-06-12 14:23:55 - MusicAnalysis - INFO - 开始批量分析 50 个文件
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲4~玉鹤轩琵琶谱.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/云庆.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/《四静板》交.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/自来生长.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 云庆.mid (1582 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲4~玉鹤轩琵琶谱.mid (1684 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 1469 个三音组，其中严格三音组 672 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 512, 严格三音组 333
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 云庆.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 1630 个三音组，其中严格三音组 749 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/《四时景》交.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 自来生长.mid (2169 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 《四静板》交.mid (2194 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 598, 严格三音组 353
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲4~玉鹤轩琵琶谱.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/海青拿鹤.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 1990 个三音组，其中严格三音组 654 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 1651 个三音组，其中严格三音组 248 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 206, 严格三音组 130
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 自来生长.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 442, 严格三音组 324
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 《四静板》交.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/夕阳萧鼓2.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 《四时景》交.mid (1424 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 1178 个三音组，其中严格三音组 481 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 342, 严格三音组 239
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 《四时景》交.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 夕阳萧鼓2.mid (1451 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 1369 个三音组，其中严格三音组 662 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 559, 严格三音组 321
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 夕阳萧鼓2.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 海青拿鹤.mid (2870 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 2733 个三音组，其中严格三音组 1195 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 919, 严格三音组 618
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 海青拿鹤.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/阳春白雪.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/《走马》交.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/龙船.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/普庵咒3.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 阳春白雪.mid (1623 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 《走马》交.mid (1530 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 1457 个三音组，其中严格三音组 781 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 1100 个三音组，其中严格三音组 510 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 562, 严格三音组 402
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 阳春白雪.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 332, 严格三音组 259
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 《走马》交.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/浔阳琵琶1.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/陈隋古音.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 普庵咒3.mid (2258 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 2203 个三音组，其中严格三音组 1042 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 龙船.mid (1596 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 925, 严格三音组 520
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 普庵咒3.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/浔阳琵琶2.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 1215 个三音组，其中严格三音组 504 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 368, 严格三音组 250
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 龙船.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/普庵咒1.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 浔阳琵琶1.mid (1689 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 1544 个三音组，其中严格三音组 737 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 564, 严格三音组 376
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 浔阳琵琶1.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 陈隋古音.mid (1692 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/《梅花操》交.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 1476 个三音组，其中严格三音组 750 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 558, 严格三音组 379
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 陈隋古音.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲2。.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 浔阳琵琶2.mid (2043 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 普庵咒1.mid (2122 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 1800 个三音组，其中严格三音组 892 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 709, 严格三音组 451
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 浔阳琵琶2.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/塞上曲2.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 2090 个三音组，其中严格三音组 1247 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 《梅花操》交.mid (1569 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 928, 严格三音组 615
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 普庵咒1.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 1377 个三音组，其中严格三音组 563 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/塞上曲.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 329, 严格三音组 275
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 《梅花操》交.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/照见我.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 塞上曲2.mid (1468 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 1361 个三音组，其中严格三音组 526 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 482, 严格三音组 262
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 塞上曲2.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:57 - MusicAnalysis - INFO - 开始分析文件: midi_files/海青拿鹅1～玉鹤轩琵琶谱.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 塞上曲.mid (1532 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲2。.mid (3117 音符)
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 1480 个三音组，其中严格三音组 651 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 540, 严格三音组 323
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 塞上曲.mid
2025-06-12 14:23:57 - MusicAnalysis - INFO - 识别到 2834 个三音组，其中严格三音组 1205 个
2025-06-12 14:23:57 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 1130, 严格三音组 583
2025-06-12 14:23:57 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲2。.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 照见我.mid (3319 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 2176 个三音组，其中严格三音组 637 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 417, 严格三音组 322
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 照见我.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/行街四合.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/浔阳夜月.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/十面埋伏1.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 浔阳夜月.mid (1527 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 1280 个三音组，其中严格三音组 460 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 395, 严格三音组 242
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 浔阳夜月.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 海青拿鹅1～玉鹤轩琵琶谱.mid (2673 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/阳春古曲2.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 行街四合.mid (2229 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 2628 个三音组，其中严格三音组 1307 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 2116 个三音组，其中严格三音组 1266 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 991, 严格三音组 672
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 海青拿鹅1～玉鹤轩琵琶谱.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 909, 严格三音组 633
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 行街四合.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/十面埋伏2.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 十面埋伏1.mid (2669 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/必肝跋碎.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 阳春古曲2.mid (1961 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 2522 个三音组，其中严格三音组 1240 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 1044, 严格三音组 615
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 十面埋伏1.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/一纸相思.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 十面埋伏2.mid (2901 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 必肝跋碎.mid (2354 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 2570 个三音组，其中严格三音组 1249 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 1735 个三音组，其中严格三音组 318 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 983, 严格三音组 617
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 十面埋伏2.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 252, 严格三音组 141
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 必肝跋碎.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/灯月交辉.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 1655 个三音组，其中严格三音组 883 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/霓裳曲.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 591, 严格三音组 438
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 阳春古曲2.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/趁赏花灯.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 一纸相思.mid (1945 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 1499 个三音组，其中严格三音组 231 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 200, 严格三音组 121
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 一纸相思.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/十面埋伏3 ~玉鹤轩琵琶谱.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 霓裳曲.mid (1756 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 1649 个三音组，其中严格三音组 840 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 623, 严格三音组 400
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 霓裳曲.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 灯月交辉.mid (2345 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 1985 个三音组，其中严格三音组 711 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 599, 严格三音组 368
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 灯月交辉.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 趁赏花灯.mid (2309 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 1721 个三音组，其中严格三音组 291 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 242, 严格三音组 150
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 趁赏花灯.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 十面埋伏3 ~玉鹤轩琵琶谱.mid (2221 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 1809 个三音组，其中严格三音组 600 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 475, 严格三音组 289
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 十面埋伏3 ~玉鹤轩琵琶谱.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/平沙落雁3.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/北派将军令.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/因为欢喜.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/平沙落雁2.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 平沙落雁3.mid (2097 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 平沙落雁2.mid (1695 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 北派将军令.mid (1877 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 1868 个三音组，其中严格三音组 894 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 1611 个三音组，其中严格三音组 871 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 724, 严格三音组 455
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 平沙落雁3.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 1617 个三音组，其中严格三音组 180 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 142, 严格三音组 84
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 北派将军令.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/阳关三叠 交.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 660, 严格三音组 425
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 平沙落雁2.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/平沙落雁1.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 因为欢喜.mid (2289 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/月儿高3.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 1505 个三音组，其中严格三音组 366 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 232, 严格三音组 179
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 因为欢喜.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲3.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 月儿高3.mid (1753 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 1688 个三音组，其中严格三音组 778 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 591, 严格三音组 399
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 月儿高3.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/三六.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲3.mid (2071 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 1854 个三音组，其中严格三音组 844 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 716, 严格三音组 427
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲3.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲2.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 三六.mid (796 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 阳关三叠 交.mid (2265 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 690 个三音组，其中严格三音组 215 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 平沙落雁1.mid (2335 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 180, 严格三音组 95
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 三六.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/平沙落雁4.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 1376 个三音组，其中严格三音组 351 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 300, 严格三音组 177
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 阳关三叠 交.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 1977 个三音组，其中严格三音组 794 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/月儿高2.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 646, 严格三音组 397
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 平沙落雁1.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/老六板.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 老六板.mid (1524 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲2.mid (2883 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 1445 个三音组，其中严格三音组 654 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 月儿高2.mid (1799 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 550, 严格三音组 331
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 老六板.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/普庵咒4 ～玉鹤轩琵琶谱.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 1741 个三音组，其中严格三音组 797 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 2612 个三音组，其中严格三音组 1231 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 640, 严格三音组 411
2025-06-12 14:23:58 - MusicAnalysis - INFO - 成功加载MIDI: 平沙落雁4.mid (2324 音符)
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 月儿高2.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/灯月交辉2.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 1023, 严格三音组 606
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲2.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:58 - MusicAnalysis - INFO - 开始分析文件: midi_files/六板.mid
2025-06-12 14:23:58 - MusicAnalysis - INFO - 识别到 2098 个三音组，其中严格三音组 884 个
2025-06-12 14:23:58 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 682, 严格三音组 444
2025-06-12 14:23:58 - MusicAnalysis - INFO - 文件分析完成: 平沙落雁4.mid
2025-06-12 14:23:59 - MusicAnalysis - INFO - 成功加载MIDI: 普庵咒4 ～玉鹤轩琵琶谱.mid (1496 音符)
2025-06-12 14:23:59 - MusicAnalysis - INFO - 识别到 1477 个三音组，其中严格三音组 833 个
2025-06-12 14:23:59 - MusicAnalysis - INFO - 成功加载MIDI: 灯月交辉2.mid (1662 音符)
2025-06-12 14:23:59 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 659, 严格三音组 420
2025-06-12 14:23:59 - MusicAnalysis - INFO - 文件分析完成: 普庵咒4 ～玉鹤轩琵琶谱.mid
2025-06-12 14:23:59 - MusicAnalysis - INFO - 识别到 1400 个三音组，其中严格三音组 457 个
2025-06-12 14:23:59 - MusicAnalysis - INFO - 成功加载MIDI: 六板.mid (1756 音符)
2025-06-12 14:23:59 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 417, 严格三音组 217
2025-06-12 14:23:59 - MusicAnalysis - INFO - 文件分析完成: 灯月交辉2.mid
2025-06-12 14:23:59 - MusicAnalysis - INFO - 识别到 1571 个三音组，其中严格三音组 779 个
2025-06-12 14:23:59 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 577, 严格三音组 384
2025-06-12 14:23:59 - MusicAnalysis - INFO - 文件分析完成: 六板.mid
2025-06-12 14:23:59 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:59 - MusicAnalysis - INFO - 开始分析文件: midi_files/霸王卸甲1.mid
2025-06-12 14:23:59 - MusicAnalysis - INFO - 音乐分析引擎初始化完成
2025-06-12 14:23:59 - MusicAnalysis - INFO - 开始分析文件: midi_files/月儿高1.mid
2025-06-12 14:23:59 - MusicAnalysis - INFO - 成功加载MIDI: 月儿高1.mid (1597 音符)
2025-06-12 14:23:59 - MusicAnalysis - INFO - 成功加载MIDI: 霸王卸甲1.mid (2124 音符)
2025-06-12 14:23:59 - MusicAnalysis - INFO - 识别到 1528 个三音组，其中严格三音组 737 个
2025-06-12 14:23:59 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 555, 严格三音组 379
2025-06-12 14:23:59 - MusicAnalysis - INFO - 文件分析完成: 月儿高1.mid
2025-06-12 14:23:59 - MusicAnalysis - INFO - 识别到 1773 个三音组，其中严格三音组 746 个
2025-06-12 14:23:59 - MusicAnalysis - INFO - 环绕音程分析完成: 总三音组 578, 严格三音组 350
2025-06-12 14:23:59 - MusicAnalysis - INFO - 文件分析完成: 霸王卸甲1.mid
2025-06-12 14:23:59 - MusicAnalysis - INFO - 批量分析完成: 成功 50, 失败 0
2025-06-12 14:23:59 - MusicAnalysis - INFO - 开始生成分析报告...
2025-06-12 14:23:59 - MusicAnalysis - INFO - 分析结果已保存到: analysis_results/music_analysis_report.csv
2025-06-12 14:23:59 - MusicAnalysis - INFO - 统计摘要已保存: analysis_results/music_analysis_report_summary.txt
2025-06-12 14:24:00 - MusicAnalysis - INFO - 特征对比图已保存: music_analysis_report_feature_comparison.png
2025-06-12 14:24:00 - MusicAnalysis - INFO - 成功加载MIDI: 云庆.mid (1582 音符)
2025-06-12 14:24:00 - MusicAnalysis - INFO - 识别到 1469 个三音组，其中严格三音组 672 个
2025-06-12 14:24:01 - MusicAnalysis - INFO - 三音组分析图已保存: music_analysis_report_three_note_analysis.png
2025-06-12 14:24:02 - MusicAnalysis - INFO - 音高序列图已保存: music_analysis_report_pitch_series.png
2025-06-12 14:24:02 - MusicAnalysis - INFO - 分析报告生成完成: music_analysis_report
