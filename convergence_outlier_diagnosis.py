#!/usr/bin/env python3
"""
相位收敛离群点深度诊断
分析收敛比例的巨大变异性和极端离群点
"""

import numpy as np
import sys
from scipy import stats
sys.path.append('.')

def analyze_convergence_variability():
    """分析收敛比例的巨大变异性"""
    print("🔍 相位收敛比例变异性分析")
    print("检查35.9%的巨大数据范围和离群点")
    print("="*80)
    
    # 报告的数据
    convergence_data = {
        'mean': 72.9,
        'std': 8.1,
        'min': 49.7,
        'max': 85.6,
        'normality_p': 0.011,
        'is_normal': False
    }
    
    print("\n1. 📊 收敛比例基础统计")
    print("-" * 60)
    
    print(f"   平均收敛比例: {convergence_data['mean']:.1f}% ± {convergence_data['std']:.1f}%")
    print(f"   收敛比例范围: {convergence_data['min']:.1f}% ~ {convergence_data['max']:.1f}%")
    print(f"   数据跨度: {convergence_data['max'] - convergence_data['min']:.1f}个百分点")
    print(f"   变异系数: {convergence_data['std'] / convergence_data['mean']:.1%}")
    print(f"   正态性: {'✅ 正态' if convergence_data['is_normal'] else '❌ 非正态'} (p={convergence_data['normality_p']:.3f})")
    
    print(f"\n   🚨 变异性异常:")
    print(f"      • 35.9%的巨大范围表明收敛效率极不均一")
    print(f"      • 一些作品接近完全收敛(85.6%)")
    print(f"      • 另一些作品勉强过半(49.7%)")
    print(f"      • 非正态分布暗示存在长下尾")

def analyze_extreme_outlier():
    """分析极端离群点(49.7%)"""
    print(f"\n2. 🎯 极端离群点分析")
    print("-" * 60)
    
    # 离群点数据
    outlier_value = 49.7
    mean_value = 72.9
    std_value = 8.1
    
    # 计算Z分数
    z_score = (outlier_value - mean_value) / std_value
    
    print(f"   极端离群点: {outlier_value:.1f}%")
    print(f"   Z分数: {z_score:.2f}")
    print(f"   偏离程度: {abs(z_score):.2f}个标准差")
    
    # 评估离群程度
    if abs(z_score) > 3:
        outlier_level = "极端离群"
    elif abs(z_score) > 2.5:
        outlier_level = "严重离群"
    elif abs(z_score) > 2:
        outlier_level = "显著离群"
    else:
        outlier_level = "轻微离群"
    
    print(f"   离群等级: {outlier_level}")
    
    # 在非正态分布中的意义
    print(f"\n   🔍 非正态分布中的意义:")
    print(f"      • 在左偏分布中，这是下尾的'锚点'")
    print(f"      • 均值±标准差描述不充分")
    print(f"      • 需要使用中位数和四分位距")
    print(f"      • 这个样本代表收敛失败的典型案例")
    
    # 推测其他指标的关联
    print(f"\n   🎯 关联指标推测:")
    potential_correlations = [
        "可能是'中等关联'对齐度样本",
        "可能具有'低强度'吸引子",
        "可能有异常的吸引子数量(5个？)",
        "可能存在数据质量问题",
        "可能代表特殊的音乐类型"
    ]
    
    for correlation in potential_correlations:
        print(f"      • {correlation}")

def analyze_distribution_characteristics():
    """分析分布特征"""
    print(f"\n3. 📈 分布特征分析")
    print("-" * 60)
    
    # 模拟收敛比例分布
    np.random.seed(42)
    
    # 基于报告数据模拟左偏分布
    # 使用Beta分布模拟左偏的收敛比例
    # 调整参数使得均值≈72.9%, 标准差≈8.1%, 最小值≈49.7%
    
    # 将百分比转换为[0,1]范围
    mean_norm = 0.729
    std_norm = 0.081
    min_norm = 0.497
    max_norm = 0.856
    
    print("   分布特征推断:")
    print(f"      均值: {mean_norm:.3f} (73%)")
    print(f"      标准差: {std_norm:.3f} (8%)")
    print(f"      范围: [{min_norm:.3f}, {max_norm:.3f}]")
    print(f"      偏度: 负偏(左偏) - 长下尾")
    
    # 估计四分位数
    # 对于左偏分布，Q1会更接近最小值
    estimated_q1 = 0.68  # 估计
    estimated_median = 0.75  # 估计
    estimated_q3 = 0.80  # 估计
    
    print(f"\n   估计四分位数:")
    print(f"      Q1: {estimated_q1:.1%} (68%)")
    print(f"      中位数: {estimated_median:.1%} (75%)")
    print(f"      Q3: {estimated_q3:.1%} (80%)")
    print(f"      IQR: {estimated_q3 - estimated_q1:.1%} (12%)")
    
    # 离群点检测
    iqr = estimated_q3 - estimated_q1
    lower_fence = estimated_q1 - 1.5 * iqr
    upper_fence = estimated_q3 + 1.5 * iqr
    
    print(f"\n   箱线图离群点检测:")
    print(f"      下围栏: {lower_fence:.1%} (50%)")
    print(f"      上围栏: {upper_fence:.1%} (98%)")
    print(f"      49.7%样本: {'✅ 在围栏内' if min_norm > lower_fence else '❌ 离群点'}")

def investigate_convergence_failure():
    """调查收敛失败的原因"""
    print(f"\n4. 🔬 收敛失败原因调查")
    print("-" * 60)
    
    print("   收敛失败的可能原因:")
    
    failure_causes = {
        '音乐结构复杂': {
            'description': '复杂的音乐结构导致相位难以收敛',
            'indicators': ['多个吸引子(5个)', '高音高跨度', '复杂调式'],
            'likelihood': 'high'
        },
        '吸引子分布不均': {
            'description': '吸引子位置分散，缺乏主导中心',
            'indicators': ['低吸引子强度', '均匀权重分布', '无明显主导'],
            'likelihood': 'high'
        },
        '数据质量问题': {
            'description': '音高解析错误或数据预处理问题',
            'indicators': ['异常音高值', '解析错误', '预处理失败'],
            'likelihood': 'medium'
        },
        '算法参数不适': {
            'description': '算法参数不适合该特定样本',
            'indicators': ['收敛阈值过严', '迭代次数不足', '学习率问题'],
            'likelihood': 'low'
        },
        '特殊音乐类型': {
            'description': '代表特殊的音乐风格或创作技法',
            'indicators': ['现代技法', '无调性', '实验音乐'],
            'likelihood': 'medium'
        }
    }
    
    for cause, details in failure_causes.items():
        print(f"\n   📌 {cause}:")
        print(f"      描述: {details['description']}")
        print(f"      指标: {', '.join(details['indicators'])}")
        print(f"      可能性: {details['likelihood']}")

def design_outlier_investigation_protocol():
    """设计离群点调查协议"""
    print(f"\n5. 📋 离群点调查协议")
    print("-" * 60)
    
    print("   49.7%收敛样本深度调查清单:")
    
    investigation_checklist = {
        '基础信息': [
            '样本标识和作品名称',
            '音高序列长度和范围',
            '三音组数量和分布',
            '数据预处理日志'
        ],
        '吸引子特征': [
            '吸引子数量(是否为5个？)',
            '吸引子位置分布',
            '吸引子权重分布',
            '主导吸引子强度'
        ],
        '对齐度特征': [
            '三音组-吸引子对齐度',
            '是否为中等关联样本？',
            '距离分布特征',
            '对齐度计算过程'
        ],
        '强度特征': [
            '改进吸引子强度值',
            '是否为低强度样本？',
            '强度计算各组成部分',
            '与其他样本的对比'
        ],
        '收敛过程': [
            '收敛迭代次数',
            '收敛曲线形状',
            '最终收敛误差',
            '收敛失败的具体阶段'
        ],
        '质量检查': [
            '音高解析准确性',
            '数据完整性检查',
            '异常值检测',
            '计算过程验证'
        ]
    }
    
    for category, items in investigation_checklist.items():
        print(f"\n   📂 {category}:")
        for item in items:
            print(f"      • {item}")

def recommend_analysis_improvements():
    """推荐分析改进"""
    print(f"\n6. 🔧 分析改进建议")
    print("-" * 60)
    
    improvements = {
        '统计描述改进': {
            'current': '使用均值±标准差描述非正态分布',
            'improved': '使用中位数、四分位距和箱线图',
            'benefit': '更准确地描述左偏分布特征'
        },
        '离群点处理': {
            'current': '简单报告最小值49.7%',
            'improved': '深度调查离群点的多维特征画像',
            'benefit': '从发现异常提升到解释异常'
        },
        '分布可视化': {
            'current': '仅报告数值统计',
            'improved': '提供直方图和箱线图',
            'benefit': '直观展示分布形状和离群点'
        },
        '关联分析': {
            'current': '各指标独立报告',
            'improved': '交叉验证离群点在多个指标上的表现',
            'benefit': '识别异常样本的综合模式'
        },
        '收敛诊断': {
            'current': '仅报告最终收敛比例',
            'improved': '分析收敛过程和失败原因',
            'benefit': '理解收敛机制和改进算法'
        }
    }
    
    for improvement, details in improvements.items():
        print(f"\n   🛠️ {improvement}:")
        print(f"      当前: {details['current']}")
        print(f"      改进: {details['improved']}")
        print(f"      效益: {details['benefit']}")
    
    print(f"\n🎯 优先级建议:")
    priorities = [
        "立即识别49.7%收敛样本的身份",
        "检查该样本在其他指标上的表现",
        "实施鲁棒统计描述(中位数+IQR)",
        "建立收敛失败的诊断框架",
        "增加分布可视化和离群点标注"
    ]
    
    for i, priority in enumerate(priorities, 1):
        print(f"   {i}. {priority}")

if __name__ == "__main__":
    print("🔍 相位收敛离群点深度诊断")
    print("分析收敛比例的巨大变异性和报告缺失")
    
    # 1. 变异性分析
    analyze_convergence_variability()
    
    # 2. 极端离群点分析
    analyze_extreme_outlier()
    
    # 3. 分布特征分析
    analyze_distribution_characteristics()
    
    # 4. 收敛失败调查
    investigate_convergence_failure()
    
    # 5. 调查协议设计
    design_outlier_investigation_protocol()
    
    # 6. 改进建议
    recommend_analysis_improvements()
    
    print(f"\n🎉 诊断完成！")
    print(f"✅ 识别了收敛比例的严重变异性问题")
    print(f"🔧 设计了离群点深度调查协议")
    print(f"📊 提供了统计描述和分析改进方案")
