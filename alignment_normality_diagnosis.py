#!/usr/bin/env python3
"""
对齐度正态性异常诊断
分析为什么对齐度指标呈现反常的正态性
"""

import numpy as np
import sys
from scipy import stats
sys.path.append('.')

def analyze_normality_anomaly():
    """分析对齐度正态性异常"""
    print("🔍 对齐度正态性异常诊断")
    print("分析为什么对齐度指标'逆势而行'呈现正态性")
    print("="*80)
    
    # 报告的数据对比
    indicators_data = {
        '吸引子对齐度': {
            'mean': 0.4881,
            'std': 0.0913,
            'cv': 0.0913 / 0.4881,
            'normality_p': 0.498,
            'is_normal': True,
            'range': (0.2884, 0.6676),
            'unit': '全音'
        },
        '吸引子数量': {
            'mean': 3.9,
            'std': 0.9,
            'cv': 0.9 / 3.9,
            'normality_p': 0.000,
            'is_normal': False,
            'range': (3, 5),
            'unit': '个数'
        },
        '吸引子强度': {
            'mean': 0.2320,
            'std': 0.2224,
            'cv': 0.2224 / 0.2320,
            'normality_p': 0.000,
            'is_normal': False,
            'range': (0.0463, 1.2332),
            'unit': '全音/个数'
        },
        '收敛比例': {
            'mean': 0.929,
            'std': 0.055,
            'cv': 0.055 / 0.929,
            'normality_p': 0.000,
            'is_normal': False,
            'range': (0.8, 1.0),
            'unit': '比例'
        }
    }
    
    print("\n1. 📊 指标正态性对比")
    print("-" * 60)
    
    for name, data in indicators_data.items():
        status = "✅ 正态" if data['is_normal'] else "❌ 非正态"
        print(f"\n   {name}:")
        print(f"      均值±标准差: {data['mean']:.4f} ± {data['std']:.4f}")
        print(f"      变异系数: {data['cv']:.1%}")
        print(f"      正态性: {status} (p={data['normality_p']:.3f})")
        print(f"      范围: {data['range']}")
        print(f"      单位: {data['unit']}")
    
    print(f"\n🚨 异常发现:")
    print(f"   • 对齐度是唯一正态分布的指标 (p=0.498)")
    print(f"   • 对齐度的CV最低 (18.7%)")
    print(f"   • 其他指标都是非正态分布 (p=0.000)")

def analyze_cv_patterns():
    """分析变异系数模式"""
    print(f"\n2. 📈 变异系数模式分析")
    print("-" * 60)
    
    cv_data = {
        '对齐度': 18.7,
        '收敛比例': 5.9,
        '吸引子数量': 23.1,
        '吸引子强度': 95.9
    }
    
    print("   变异系数排序:")
    sorted_cv = sorted(cv_data.items(), key=lambda x: x[1])
    
    for i, (name, cv) in enumerate(sorted_cv, 1):
        if cv < 15:
            level = "低变异性"
        elif cv < 35:
            level = "中等变异性"
        elif cv < 60:
            level = "高变异性"
        else:
            level = "极端变异性"
        
        print(f"     {i}. {name}: {cv:.1f}% ({level})")
    
    print(f"\n💡 模式解读:")
    print(f"   • 收敛比例: 极低变异 (5.9%) - 可能存在上界效应")
    print(f"   • 对齐度: 低变异 (18.7%) - 稳定的中心趋势")
    print(f"   • 吸引子数量: 中等变异 (23.1%) - 离散分布特征")
    print(f"   • 吸引子强度: 极端变异 (95.9%) - 高度异质性")

def investigate_normality_causes():
    """调查正态性的可能原因"""
    print(f"\n3. 🔍 正态性原因调查")
    print("-" * 60)
    
    potential_causes = {
        '中心极限定理效应': {
            'description': '对齐度可能是多个独立因素的平均',
            'evidence': '如果对齐度是多个三音组距离的平均，CLT会导致正态性',
            'likelihood': 'high',
            'test': '检查对齐度计算是否涉及多个值的平均'
        },
        '算法平滑效应': {
            'description': '计算过程可能包含平滑或正则化步骤',
            'evidence': '算法设计可能天然地减少极端值',
            'likelihood': 'medium',
            'test': '检查对齐度计算公式中的平滑机制'
        },
        '数据预处理影响': {
            'description': '预处理步骤可能过滤了极端值',
            'evidence': '边界压缩表明可能存在数据截断',
            'likelihood': 'medium',
            'test': '检查数据清洗和预处理步骤'
        },
        '真实音乐特征': {
            'description': '对齐度反映的音乐特征本身就更稳定',
            'evidence': '音乐作品在某些维度上可能确实更同质',
            'likelihood': 'low',
            'test': '与随机数据对比验证'
        },
        '公式设计偏差': {
            'description': '对齐度公式可能天然地产生正态分布',
            'evidence': '某些数学变换会导致正态性',
            'likelihood': 'high',
            'test': '分析对齐度公式的数学性质'
        }
    }
    
    for cause, details in potential_causes.items():
        print(f"\n   📌 {cause}:")
        print(f"      描述: {details['description']}")
        print(f"      证据: {details['evidence']}")
        print(f"      可能性: {details['likelihood']}")
        print(f"      验证: {details['test']}")

def analyze_formula_properties():
    """分析对齐度公式的数学性质"""
    print(f"\n4. 🧮 对齐度公式数学性质分析")
    print("-" * 60)
    
    print("   当前公式（修正后）:")
    print("   对齐度 = 1.0 - (平均距离 / 6.0全音)")
    print("   其中：平均距离 = mean(三音组到吸引子的距离)")
    
    print(f"\n   数学性质分析:")
    
    properties = {
        '线性变换': {
            'description': '对齐度是距离的线性变换',
            'implication': '如果距离正态分布，对齐度也正态分布',
            'normality_effect': '保持正态性'
        },
        '平均操作': {
            'description': '距离是多个三音组距离的平均',
            'implication': '中心极限定理：多个值的平均趋向正态',
            'normality_effect': '产生正态性'
        },
        '边界约束': {
            'description': '对齐度被约束在[0,1]范围内',
            'implication': '截断效应可能影响分布形状',
            'normality_effect': '可能破坏正态性，但实际数据远离边界'
        },
        '距离分布': {
            'description': '底层距离分布的性质决定对齐度分布',
            'implication': '需要检查原始距离数据的分布',
            'normality_effect': '传递底层分布特征'
        }
    }
    
    for prop, details in properties.items():
        print(f"\n   🔧 {prop}:")
        print(f"      描述: {details['description']}")
        print(f"      含义: {details['implication']}")
        print(f"      正态性影响: {details['normality_effect']}")

def simulate_alignment_distribution():
    """模拟对齐度分布"""
    print(f"\n5. 🎲 对齐度分布模拟")
    print("-" * 60)
    
    print("   模拟实验：验证中心极限定理效应")
    
    # 模拟多个三音组距离的平均
    n_triads = 10  # 假设每个作品有10个三音组
    n_samples = 50  # 50首作品
    
    # 模拟距离分布（可能是指数分布或伽马分布）
    np.random.seed(42)
    
    simulated_alignments = []
    
    for _ in range(n_samples):
        # 模拟一首作品的多个三音组距离
        # 使用指数分布模拟距离（音乐中近距离更常见）
        distances = np.random.exponential(scale=1.5, size=n_triads)
        
        # 计算平均距离
        mean_distance = np.mean(distances)
        
        # 转换为对齐度
        alignment = 1.0 - (mean_distance / 6.0)
        alignment = max(0.0, min(1.0, alignment))  # 边界约束
        
        simulated_alignments.append(alignment)
    
    # 分析模拟结果
    sim_mean = np.mean(simulated_alignments)
    sim_std = np.std(simulated_alignments)
    sim_cv = sim_std / sim_mean
    
    # 正态性检验
    _, sim_p_value = stats.shapiro(simulated_alignments)
    
    print(f"\n   模拟结果:")
    print(f"      模拟均值: {sim_mean:.4f}")
    print(f"      模拟标准差: {sim_std:.4f}")
    print(f"      模拟CV: {sim_cv:.1%}")
    print(f"      模拟正态性: p={sim_p_value:.3f}")
    
    print(f"\n   与实际数据对比:")
    print(f"      实际均值: 0.4881 vs 模拟: {sim_mean:.4f}")
    print(f"      实际CV: 18.7% vs 模拟: {sim_cv:.1%}")
    print(f"      实际正态性: p=0.498 vs 模拟: p={sim_p_value:.3f}")
    
    if sim_p_value > 0.05:
        print(f"   ✅ 模拟验证：中心极限定理可以解释正态性")
    else:
        print(f"   ❌ 模拟不符合：需要其他解释")

def recommend_diagnostic_actions():
    """推荐诊断行动"""
    print(f"\n6. 🔧 诊断行动建议")
    print("-" * 60)
    
    actions = {
        '立即验证': [
            '检查对齐度计算中的平均操作数量',
            '分析原始距离数据的分布特征',
            '验证是否存在数据预处理平滑',
            '检查边界约束的实际影响'
        ],
        '深入分析': [
            '对比不同作品的三音组数量',
            '分析距离分布的底层特征',
            '验证中心极限定理假设',
            '检查算法中的隐含平滑机制'
        ],
        '方法改进': [
            '报告原始距离数据的统计特征',
            '提供对齐度正态性的理论解释',
            '增加分布形状的诊断指标',
            '对比不同计算方法的分布特征'
        ]
    }
    
    for category, action_list in actions.items():
        print(f"\n   📋 {category}:")
        for action in action_list:
            print(f"      • {action}")
    
    print(f"\n🎯 核心问题:")
    print(f"   对齐度的正态性是算法人工制品还是真实音乐特征？")
    print(f"   这种稳定性是优势还是信息损失的表现？")

if __name__ == "__main__":
    print("🔍 对齐度正态性异常诊断")
    print("分析统计行为的根本差异")
    
    # 1. 正态性异常分析
    analyze_normality_anomaly()
    
    # 2. 变异系数模式
    analyze_cv_patterns()
    
    # 3. 正态性原因调查
    investigate_normality_causes()
    
    # 4. 公式数学性质
    analyze_formula_properties()
    
    # 5. 分布模拟
    simulate_alignment_distribution()
    
    # 6. 诊断建议
    recommend_diagnostic_actions()
    
    print(f"\n🎉 诊断完成！")
    print(f"✅ 识别了正态性异常的可能原因")
    print(f"🔧 提供了验证和改进方案")
    print(f"⚠️ 需要进一步验证中心极限定理假设")
