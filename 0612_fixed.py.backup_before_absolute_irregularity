#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的可运行版本 - 专注于解决当前问题
"""

import numpy as np
import pandas as pd
import pretty_midi
import glob
import os
import logging
import math
from typing import List, Dict, Tuple, Union, Optional, Any

# 设置警告过滤
import warnings
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')

# 尝试导入pywt
try:
    import pywt
    PYWT_AVAILABLE = True
except ImportError:
    PYWT_AVAILABLE = False


class WaveletMethod:
    """
    严格按照用户要求的小波分析方法
    
    核心改进：
    1. 反向加权：weight = 1.0/level 或 weight = max_levels - i + 1
    2. 强调高频细节（d1, d2）
    3. 返回详细特征向量而非单一得分
    4. 支持多种加权方案对比
    """
    
    def __init__(self, 
                 wavelet_type: str = 'haar',
                 max_decomp_levels: int = 2,  # 聚焦1-2层
                 use_intervals: bool = True,
                 weighting_scheme: str = 'reverse_weight'):
        self.wavelet_type = wavelet_type
        self.max_decomp_levels = max_decomp_levels
        self.use_intervals = use_intervals
        self.weighting_scheme = weighting_scheme
        self.last_analysis = None
    
    def analyze_single_work_smoothness(self, pitch_series):
        """为了兼容性保留的单一得分方法"""
        features = self.analyze_single_work_features(pitch_series)
        
        if 'error' in features:
            return 0.0
        
        # 根据加权方案选择不规则性得分
        irregularity_score = features.get(f'weighted_irregularity_{self.weighting_scheme}', 0.0)
        
        # 转换为平滑度
        smoothness = 1.0 / (1.0 + irregularity_score)
        
        return smoothness
    
    def analyze_single_work_features(self, pitch_series):
        """分析单个作品的小波特征（返回特征向量）"""
        if len(pitch_series) < 4:
            return {'error': 'sequence_too_short'}
        
        try:
            # 准备分析序列
            if self.use_intervals:
                analysis_sequence = np.diff(pitch_series)
            else:
                analysis_sequence = np.array(pitch_series)
            
            if len(analysis_sequence) < 2:
                return {'error': 'analysis_sequence_too_short'}
            
            # 小波分析
            wavelet_result = self._compute_wavelet_features(analysis_sequence)
            
            # 缓存分析结果
            self.last_analysis = wavelet_result
            
            return wavelet_result
            
        except Exception as e:
            return {'error': f'analysis_failed: {e}'}
    
    def _compute_wavelet_features(self, sequence):
        """计算小波特征（核心实现）"""
        # 确定分解层数
        max_levels = min(
            self.max_decomp_levels,
            int(np.floor(np.log2(len(sequence)))) - 1
        )
        
        if max_levels < 1:
            return {'error': 'insufficient_data_for_decomposition'}
        
        return self._haar_wavelet_features(sequence, max_levels)
    
    def _haar_wavelet_features(self, sequence, max_levels):
        """使用简化Haar小波的特征计算"""
        current_signal = sequence.copy()
        detail_energies = []
        detail_stats = []
        
        for level in range(1, max_levels + 1):
            if len(current_signal) < 2:
                break
            
            # 确保偶数长度
            n = len(current_signal)
            if n % 2 == 1:
                current_signal = current_signal[:-1]
                n = len(current_signal)
            
            if n < 2:
                break
            
            # Haar小波分解
            pairs = current_signal.reshape(-1, 2)
            sqrt2 = math.sqrt(2)
            
            # 近似和细节系数
            approximation = (pairs[:, 0] + pairs[:, 1]) / sqrt2
            details = (pairs[:, 0] - pairs[:, 1]) / sqrt2
            
            # 细节能量和统计
            energy = np.sum(details ** 2)
            detail_energies.append(energy)
            
            stats = {
                'level': level,
                'length': len(details),
                'energy': float(energy),
                'mean': float(np.mean(details)),
                'std': float(np.std(details)),
                'max_abs': float(np.max(np.abs(details))),
                'rms': float(np.sqrt(np.mean(details ** 2))),
                'variance': float(np.var(details))
            }
            detail_stats.append(stats)
            
            # 为下一层准备
            current_signal = approximation
        
        # 最终近似能量
        approx_energy = np.sum(current_signal ** 2)
        
        return self._compute_all_weighting_schemes(
            detail_energies, approx_energy, detail_stats, max_levels
        )
    
    def _compute_all_weighting_schemes(self, detail_energies, approx_energy, detail_stats, max_levels):
        """计算所有加权方案的结果（严格按照用户要求）"""
        
        if not detail_energies:
            return {'error': 'no_detail_energies'}
        
        total_energy = approx_energy + sum(detail_energies)
        
        if total_energy == 0:
            return {'error': 'zero_total_energy'}
        
        # 基础特征
        features = {
            'sequence_type': 'intervals' if self.use_intervals else 'pitches',
            'wavelet_type': self.wavelet_type,
            'decomposition_levels': len(detail_energies),
            'total_energy': float(total_energy),
            'approx_energy': float(approx_energy),
            'detail_energies': detail_energies,
            'detail_stats': detail_stats
        }
        
        # 各层级能量比例（用户要求的特征向量）
        features['d1_energy_ratio'] = detail_energies[0] / total_energy if len(detail_energies) > 0 else 0.0
        features['d2_energy_ratio'] = detail_energies[1] / total_energy if len(detail_energies) > 1 else 0.0
        features['d3_energy_ratio'] = detail_energies[2] / total_energy if len(detail_energies) > 2 else 0.0
        
        # 总细节能量比例
        features['total_detail_energy_ratio'] = sum(detail_energies) / total_energy
        
        # 能量集中度和主导层级
        features['energy_concentration'] = max(detail_energies) / sum(detail_energies) if sum(detail_energies) > 0 else 0
        features['dominant_level'] = detail_energies.index(max(detail_energies)) + 1 if detail_energies else 0
        
        # === 用户要求的多种加权方案 ===
        
        # 1. 反向加权方案A：weight = 1.0 / level（强调高频细节）
        weighted_sum_reverse = 0.0
        total_weight_reverse = 0.0
        for i, energy in enumerate(detail_energies):
            level = i + 1
            weight = 1.0 / level  # d1权重1, d2权重0.5, d3权重0.33
            weighted_sum_reverse += weight * energy
            total_weight_reverse += weight
        
        features['weighted_irregularity_reverse_weight'] = (
            weighted_sum_reverse / total_weight_reverse / total_energy 
            if total_weight_reverse > 0 else 0.0
        )
        
        # 2. 反向加权方案B：weight = max_levels - i + 1（更强调高频）
        weighted_sum_reverse_linear = 0.0
        total_weight_reverse_linear = 0.0
        for i, energy in enumerate(detail_energies):
            level = i + 1
            weight = max_levels - i  # 例如3层时：d1权重3, d2权重2, d3权重1
            weighted_sum_reverse_linear += weight * energy
            total_weight_reverse_linear += weight
        
        features['weighted_irregularity_reverse_linear'] = (
            weighted_sum_reverse_linear / total_weight_reverse_linear / total_energy 
            if total_weight_reverse_linear > 0 else 0.0
        )
        
        # 3. 无加权方案：所有层级权重相等
        features['weighted_irregularity_equal'] = (
            sum(detail_energies) / len(detail_energies) / total_energy
        )
        
        # 4. 仅d1层级
        features['weighted_irregularity_d1_only'] = (
            detail_energies[0] / total_energy if detail_energies else 0.0
        )
        
        # 5. 仅d1+d2层级
        d1_d2_energy = detail_energies[0] + (detail_energies[1] if len(detail_energies) > 1 else 0)
        features['weighted_irregularity_d1_d2_only'] = d1_d2_energy / total_energy
        
        # 6. 原始方案（层级越高权重越大）- 用于对比
        weighted_sum_original = 0.0
        total_weight_original = 0.0
        for i, energy in enumerate(detail_energies):
            level = i + 1
            weight = level  # d1权重1, d2权重2, d3权重3
            weighted_sum_original += weight * energy
            total_weight_original += weight
        
        features['weighted_irregularity_original'] = (
            weighted_sum_original / total_weight_original / total_energy 
            if total_weight_original > 0 else 0.0
        )
        
        return features
    
    def get_analysis_details(self):
        """获取最后一次分析的详细信息"""
        return self.last_analysis or {}



class EnhancedChineseMusicAnalyzer:
    """增强版中国传统音乐分析器"""
    
    def __init__(self, epsilon=1e-6, min_groups_for_analysis=10):
        self.epsilon = epsilon
        self.min_groups_for_analysis = min_groups_for_analysis
        self.pitch_series = None
        self.indexed_diffs = []
        self.three_note_groups = []
        self.strict_groups = []
        self.non_strict_groups = []
        self.swave_curve = []
        self.holder_indices = []
        self.wavelet_energies = []
        
        # 添加改进的小波方法
        self.wavelet_method = WaveletMethod(
            wavelet_type='haar',
            max_decomp_levels=2,  # 聚焦1-2层，适合三音组分析
            use_intervals=True,
            weighting_scheme='reverse_weight'  # 反向加权，强调高频细节
        )
        
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def get_midi_files(self, folder_path: str) -> List[str]:
        """从文件夹中检索所有 MIDI 文件路径"""
        return glob.glob(os.path.join(folder_path, '*.mid'))
    
    def load_and_preprocess(self, midi_file_path: Union[str, List[str]]) -> Tuple[pd.Series, List[Tuple]]:
        """增强版MIDI加载和预处理"""
        self.three_note_groups = []
        self.strict_groups = []
        self.non_strict_groups = []
        self.swave_curve = []
        self.holder_indices = []
        self.wavelet_energies = []
        
        if os.path.isdir(midi_file_path):
            midi_file_path = self.get_midi_files(midi_file_path)
        elif isinstance(midi_file_path, str):
            midi_file_path = [midi_file_path]
        
        all_pitches = []
        all_indices = []
        current_index_offset = 0
        
        for single_path in midi_file_path:
            try:
                midi_data = pretty_midi.PrettyMIDI(single_path)
            except Exception as e:
                self.logger.error(f"处理文件 {single_path} 出错: {e}")
                continue
            
            melody_track = self._extract_melody_track(midi_data)
            if melody_track is None:
                self.logger.error(f"文件 {single_path} 未找到有效的旋律轨道")
                continue
            
            pitches = [note.pitch for note in melody_track.notes]
            pitch_series_part = pd.Series(pitches, name='pitch').astype(float)
            
            diffs = pitch_series_part.diff().dropna()
            valid_mask = (diffs != 0)
            valid_indices = diffs[valid_mask].index.tolist()
            signed_diffs = diffs[valid_mask].tolist()
            
            self.logger.info(f"[{single_path}] 加载了 {len(pitch_series_part)} 个音符")
            self.logger.info(f"[{single_path}] 有效音程数: {len(signed_diffs)}")
            
            all_pitches.extend(pitch_series_part.tolist())
            for idx, diff in zip(valid_indices, signed_diffs):
                global_idx = current_index_offset + idx
                all_indices.append((global_idx, diff))
            current_index_offset += len(pitch_series_part)
        
        self.pitch_series = pd.Series(all_pitches, name='pitch').astype(float)
        self.indexed_diffs = all_indices
        return self.pitch_series, all_indices
    
    def _extract_melody_track(self, midi_data):
        """提取主旋律轨道"""
        melody_track = None
        for instrument in midi_data.instruments:
            if not instrument.is_drum and len(instrument.notes) > 0:
                melody_track = instrument
                break
        return melody_track
    
    def identify_three_note_groups(self) -> List[Dict]:
        """识别和分类三音组"""
        if self.pitch_series is None or len(self.pitch_series) < 3:
            self.logger.error("音高序列不足，无法分析三音组")
            return []
        
        self.three_note_groups = []
        self.strict_groups = []
        self.non_strict_groups = []
        
        groups = []
        pitches = self.pitch_series.values
        
        for i in range(len(pitches) - 2):
            note1, note2, note3 = pitches[i], pitches[i + 1], pitches[i + 2]
            
            interval1 = note2 - note1
            interval2 = note3 - note2
            
            if interval1 == 0 and interval2 == 0:
                continue
            
            group = {
                'index': i,
                'notes': [note1, note2, note3],
                'intervals': [interval1, interval2],
                'is_strict': self._is_strict_three_note_group(interval1, interval2),
                'dynamic_range': max(note1, note2, note3) - min(note1, note2, note3),
                'total_displacement': abs(note3 - note1),
                'direction_changes': 1 if interval1 * interval2 < 0 else 0,
                'complexity': abs(interval1) + abs(interval2)
            }
            
            groups.append(group)
            
            if group['is_strict']:
                self.strict_groups.append(group)
            else:
                self.non_strict_groups.append(group)
        
        self.three_note_groups = groups
        self.logger.info(f"识别出 {len(groups)} 个三音组，其中严格定义 {len(self.strict_groups)} 个")
        
        return groups
    
    def _is_strict_three_note_group(self, interval1: float, interval2: float) -> bool:
        """判断是否为严格定义的三音组（方向相反）"""
        return (interval1 != 0 and interval2 != 0 and
                ((interval1 > 0 and interval2 < 0) or (interval1 < 0 and interval2 > 0)))
    
    def analyze_per_work_smoothness(self, midi_file_path: Union[str, List[str]]) -> Dict:
        """按作品分别分析平滑度收敛"""
        print("\n🔧 修正研究方法：按作品分别分析平滑度收敛")
        print("=" * 60)
        
        if os.path.isdir(midi_file_path):
            midi_file_path = self.get_midi_files(midi_file_path)
        elif isinstance(midi_file_path, str):
            midi_file_path = [midi_file_path]
        
        per_work_results = []
        failed_files = []
        
        for i, single_path in enumerate(midi_file_path):
            print(f"\n📋 分析第 {i + 1}/{len(midi_file_path)} 首: {os.path.basename(single_path)}")
            
            try:
                self._reset_analyzer_state()
                pitch_series, indexed_diffs = self._load_single_file(single_path)
                
                if pitch_series is None or len(pitch_series) < 10:
                    print(f"   ❌ 文件无效或音符过少")
                    failed_files.append(single_path)
                    continue
                
                work_result = self._calculate_single_work_smoothness(single_path)
                work_result['file_path'] = single_path
                work_result['file_name'] = os.path.basename(single_path)
                
                per_work_results.append(work_result)
                print(f"   ✅ 分析完成")
                
            except Exception as e:
                print(f"   ❌ 分析失败: {e}")
                failed_files.append(single_path)
                continue
        
        if per_work_results:
            summary = self._summarize_per_work_results(per_work_results)
            summary['failed_files'] = failed_files
            summary['total_files'] = len(midi_file_path)
            summary['successful_files'] = len(per_work_results)
            
            self.print_smoothness_summary(summary)
            
            print(f"\n📊 总体分析完成:")
            print(f"   成功: {len(per_work_results)} 首")
            print(f"   失败: {len(failed_files)} 首")
            
            return {
                'per_work_results': per_work_results,
                'failed_files': failed_files,
                'summary_statistics': summary
            }
        else:
            print(f"\n❌ 没有成功分析任何文件")
            return {'error': '没有成功分析任何文件', 'failed_files': failed_files}
    
    def _reset_analyzer_state(self):
        """重置分析器状态"""
        self.pitch_series = None
        self.indexed_diffs = []
        self.three_note_groups = []
        self.strict_groups = []
        self.non_strict_groups = []
    
    def _load_single_file(self, file_path: str) -> Tuple[pd.Series, List[Tuple]]:
        """加载单个文件"""
        return self.load_and_preprocess(file_path)
    
    def _calculate_single_work_smoothness(self, file_path: str) -> Dict:
        """计算单个作品的平滑度"""
        groups = self.identify_three_note_groups()
        
        if len(self.strict_groups) < 3:
            return {'error': '严格三音组数量不足'}
        
        method1_result = self._method1_single_work_smoothness()
        method2_result = self._method2_single_work_smoothness(self.pitch_series.values)
        
        consistency = abs(method1_result['smoothness'] - method2_result['average_holder']) < 1.0
        
        print(f"   ✅ 成功分析: {len(self.strict_groups)} 个严格三音组")
        print(f"      方法1 E(log i): {method1_result['smoothness']:.4f}")
        print(f"      方法2 小波: {method2_result['average_holder']:.4f}")
        print(f"      一致性: {'✓' if consistency else '✗'}")
        
        return {
            'file_path': file_path,
            'total_groups': len(groups),
            'strict_groups': len(self.strict_groups),
            'method1_smoothness': method1_result['smoothness'],
            'method2_smoothness': method2_result['average_holder'],
            'consistency': consistency,
            'method1_details': method1_result,
            'method2_details': method2_result
        }
    
    def _method1_single_work_smoothness(self) -> Dict:
        """方法1: E(log i) 计算"""
        interval_logs = []
        
        for group in self.strict_groups:
            interval1, interval2 = group['intervals']
            if abs(interval1) > 0:
                interval_logs.append(np.log(abs(interval1)))
            if abs(interval2) > 0:
                interval_logs.append(np.log(abs(interval2)))
        
        smoothness = np.mean(interval_logs) if interval_logs else 0.0
        
        return {
            'smoothness': smoothness,
            'converged': True,
            'total_intervals': len(interval_logs),
            'interval_range': f"{np.min(interval_logs):.3f}~{np.max(interval_logs):.3f}" if interval_logs else "0.000~0.000",
            'interval_std': np.std(interval_logs) if len(interval_logs) > 1 else 0.0
        }
    
    def _method2_single_work_smoothness(self, pitch_series):
        """方法2: 基于改进小波变换的平滑度分析"""
        try:
            smoothness = self.wavelet_method.analyze_single_work_smoothness(pitch_series)
            
            return {
                'average_holder': smoothness,
                'converged': True,
                'total_groups': len(pitch_series) - 2 if len(pitch_series) > 2 else 0,
                'holder_range': f"{smoothness:.3f}~{smoothness:.3f}",
                'holder_std': 0.0
            }
        except Exception as e:
            print(f"小波分析出错: {e}")
            return {
                'average_holder': 0.0,
                'converged': False,
                'total_groups': 0,
                'holder_range': "0.000~0.000",
                'holder_std': 0.0
            }
    
    def _summarize_per_work_results(self, per_work_results: List[Dict]) -> Dict:
        """汇总所有作品的结果"""
        method1_values = [r['method1_smoothness'] for r in per_work_results]
        method2_values = [r['method2_smoothness'] for r in per_work_results]
        consistency_count = sum(1 for r in per_work_results if r['consistency'])
        
        return {
            'total_works': len(per_work_results),
            'consistency_percentage': (consistency_count / len(per_work_results)) * 100,
            'method1_stats': {
                'mean': np.mean(method1_values),
                'std': np.std(method1_values),
                'min': np.min(method1_values),
                'max': np.max(method1_values),
                'median': np.median(method1_values)
            },
            'method2_stats': {
                'mean': np.mean(method2_values),
                'std': np.std(method2_values),
                'min': np.min(method2_values),
                'max': np.max(method2_values),
                'median': np.median(method2_values)
            }
        }
    
    def print_smoothness_summary(self, results: Dict):
        """打印平滑度分析摘要"""
        print("\n" + "=" * 60)
        print("🎼 平滑度收敛分析摘要报告")
        print("=" * 60)
        print(f"📊 基础统计:")
        print(f"   分析作品数: {results['total_works']} 首")
        print(f"   方法一致性: {results['consistency_percentage']:.1f}%")
        
        print(f"\n📈 方法1 (E(log i)) 统计:")
        m1 = results['method1_stats']
        print(f"   均值: {m1['mean']:.4f}")
        print(f"   标准差: {m1['std']:.4f}")
        print(f"   范围: {m1['min']:.4f} ~ {m1['max']:.4f}")
        print(f"   中位数: {m1['median']:.4f}")
        
        print(f"\n📈 方法2 (小波分析) 统计:")
        m2 = results['method2_stats']
        print(f"   均值: {m2['mean']:.4f}")
        print(f"   标准差: {m2['std']:.4f}")
        print(f"   范围: {m2['min']:.4f} ~ {m2['max']:.4f}")
        print(f"   中位数: {m2['median']:.4f}")
        
        print(f"\n✅ 核心结论:")
        if results['consistency_percentage'] > 70:
            print(f"   ✅ 两种方法具有良好的一致性")
        elif results['consistency_percentage'] > 40:
            print(f"   ⚠️ 两种方法一致性中等")
        else:
            print(f"   ⚠️ 平滑度收敛特征需要进一步验证")
            print(f"   ⚠️ 方法一致性偏低")

def main():
    """主函数"""
    print("🎼 第二个数学特征验证：平滑度收敛")
    print("=" * 80)
    
    analyzer = EnhancedChineseMusicAnalyzer()
    
    # 分析MIDI文件
    results = analyzer.analyze_per_work_smoothness('/Users/<USER>/Desktop/AI音乐/midi_files')
    
    print(f"\n🎼 第二个数学特征验证完成！")
    print(f"下一步：进行第三个数学特征（熵最大化）的验证")

if __name__ == "__main__":
    main()
