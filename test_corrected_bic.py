#!/usr/bin/env python3
"""
测试修正后的BIC算法
验证音乐理论先验是否能改善U型分布问题
"""

import sys
import os
import numpy as np

# 添加当前目录到路径
sys.path.append('.')

def test_corrected_bic():
    """测试修正后的BIC算法"""
    print("🧪 测试修正后的BIC算法")
    print("验证音乐理论先验是否改善U型分布")
    print("="*80)
    
    try:
        # 导入修正后的统一分析器
        from unified_topological_analysis import UnifiedTopologicalAnalyzer
        
        # 创建分析器
        analyzer = UnifiedTopologicalAnalyzer()
        
        print("✅ 修正后的统一拓扑分析器创建成功")
        print("✅ 已集成音乐理论先验修正")
        
        # 创建测试数据集（模拟不同复杂度的调式）
        test_melodies = [
            {
                'name': '宫调式测试1',
                'pitches': [60, 62, 64, 67, 69, 67, 64, 62, 60],
                'expected_k': 3,
                'description': '简单宫调式，应该选择3个吸引子'
            },
            {
                'name': '宫调式测试2', 
                'pitches': [67, 69, 72, 74, 76, 74, 72, 69, 67],
                'expected_k': 3,
                'description': '另一个宫调式，应该选择3个吸引子'
            },
            {
                'name': '商调式测试1',
                'pitches': [62, 64, 67, 69, 71, 74, 71, 69, 67, 64, 62],
                'expected_k': 4,
                'description': '商调式，应该选择4个吸引子'
            },
            {
                'name': '商调式测试2',
                'pitches': [64, 67, 69, 71, 74, 76, 74, 71, 69, 67, 64],
                'expected_k': 4,
                'description': '另一个商调式，应该选择4个吸引子'
            },
            {
                'name': '角调式测试',
                'pitches': [64, 67, 69, 72, 74, 76, 79, 76, 74, 72, 69, 67, 64],
                'expected_k': 4,
                'description': '角调式，应该选择4个吸引子'
            },
            {
                'name': '七声调式测试1',
                'pitches': [60, 62, 64, 65, 67, 69, 71, 72, 71, 69, 67, 65, 64, 62, 60],
                'expected_k': 5,
                'description': '七声调式，应该选择5个吸引子'
            },
            {
                'name': '七声调式测试2',
                'pitches': [67, 69, 71, 72, 74, 76, 78, 79, 78, 76, 74, 72, 71, 69, 67],
                'expected_k': 5,
                'description': '另一个七声调式，应该选择5个吸引子'
            }
        ]
        
        print(f"\n🧪 分析{len(test_melodies)}个测试旋律:")
        
        results = []
        k_selections = []
        
        for i, melody in enumerate(test_melodies, 1):
            print(f"\n   {i}. 分析 {melody['name']}...")
            print(f"      期望k值: {melody['expected_k']}")
            print(f"      描述: {melody['description']}")
            
            try:
                result = analyzer.analyze_work(melody['pitches'], melody['name'])
                if result:
                    selected_k = result['attractor_landscape']['attractor_count']
                    optimal_k = result['attractor_landscape']['optimal_k']
                    
                    print(f"      ✅ 选择的k值: {selected_k}")
                    print(f"      📊 BIC最优k: {optimal_k}")
                    print(f"      🎯 是否符合期望: {'✅' if selected_k == melody['expected_k'] else '❌'}")
                    
                    results.append(result)
                    k_selections.append({
                        'name': melody['name'],
                        'expected': melody['expected_k'],
                        'selected': selected_k,
                        'optimal': optimal_k,
                        'correct': selected_k == melody['expected_k']
                    })
                else:
                    print(f"      ❌ 分析失败")
            except Exception as e:
                print(f"      ❌ 分析出错: {e}")
        
        if len(k_selections) >= 3:
            print(f"\n📊 修正后的BIC选择结果分析:")
            print(f"   成功分析: {len(k_selections)}/{len(test_melodies)} 个旋律")
            
            # 统计选择准确性
            correct_selections = sum(1 for s in k_selections if s['correct'])
            accuracy = correct_selections / len(k_selections)
            
            print(f"   选择准确率: {correct_selections}/{len(k_selections)} = {accuracy:.1%}")
            
            # 统计k值分布
            selected_ks = [s['selected'] for s in k_selections]
            k_distribution = {}
            for k in selected_ks:
                k_distribution[k] = k_distribution.get(k, 0) + 1
            
            print(f"   k值分布: {k_distribution}")
            
            # 分析改善情况
            print(f"\n🔍 改善情况分析:")
            k3_count = k_distribution.get(3, 0)
            k4_count = k_distribution.get(4, 0)
            k5_count = k_distribution.get(5, 0)
            
            print(f"   3个吸引子: {k3_count} 个 ({k3_count/len(k_selections):.1%})")
            print(f"   4个吸引子: {k4_count} 个 ({k4_count/len(k_selections):.1%})")
            print(f"   5个吸引子: {k5_count} 个 ({k5_count/len(k_selections):.1%})")
            
            # 检查是否还是U型分布
            if k4_count < k3_count and k4_count < k5_count:
                print(f"   ⚠️ 仍然存在U型分布趋势")
            else:
                print(f"   ✅ U型分布问题得到改善")
            
            # 详细结果
            print(f"\n📋 详细选择结果:")
            for s in k_selections:
                status = "✅" if s['correct'] else "❌"
                print(f"   {status} {s['name']}: 期望{s['expected']} → 选择{s['selected']} (BIC最优{s['optimal']})")
            
            return True
        else:
            print(f"\n❌ 成功分析的样本太少({len(k_selections)})，无法评估改善效果")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_with_original():
    """与原始BIC进行对比"""
    print(f"\n" + "="*80)
    print("📊 与原始BIC算法对比")
    print("="*80)
    
    print("🎯 理论预期改善:")
    print("   • 原始BIC: 偏向3和5，低估4")
    print("   • 修正BIC: 应该更平衡地选择3、4、5")
    print("   • 音乐理论先验: 基于40%、30%、30%的理论分布")
    
    print(f"\n📈 期望的改善效果:")
    print(f"   • 4个吸引子的选择率应该提高")
    print(f"   • U型分布应该变为更平衡的分布")
    print(f"   • 选择准确率应该提高")

if __name__ == "__main__":
    print("🧪 修正后BIC算法测试")
    print("验证音乐理论先验是否改善U型分布问题")
    
    # 1. 主要测试
    success = test_corrected_bic()
    
    # 2. 对比分析
    compare_with_original()
    
    if success:
        print(f"\n🎉 修正后BIC算法测试完成！")
        print(f"✅ 音乐理论先验已成功集成")
        print(f"📊 可以观察到选择行为的改变")
        print(f"🎼 更符合中国传统音乐理论")
    else:
        print(f"\n⚠️ 测试需要进一步调试")
        print(f"🔧 可能需要调整先验参数")
