#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的可运行版本 - 专注于解决当前问题
"""

import numpy as np
import pandas as pd
# 尝试导入pretty_midi（可选）
try:
    import pretty_midi
    PRETTY_MIDI_AVAILABLE = True
    print("✅ pretty_midi库加载成功")
except ImportError:
    PRETTY_MIDI_AVAILABLE = False
    print("⚠️ pretty_midi库未安装，将跳过MIDI文件处理")
import glob
import os
import logging
import math

# 尝试导入可视化库
try:
    import matplotlib
    # 设置后端以避免兼容性问题
    matplotlib.use('Agg')  # 使用非交互式后端
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    from matplotlib.gridspec import GridSpec
    try:
        from sklearn.preprocessing import MinMaxScaler
    except ImportError:
        # 提供简化的MinMaxScaler替代
        class MinMaxScaler:
            def __init__(self):
                self.min_ = None
                self.scale_ = None
            
            def fit_transform(self, X):
                import numpy as np
                X = np.array(X).reshape(-1, 1) if len(np.array(X).shape) == 1 else np.array(X)
                self.min_ = np.min(X, axis=0)
                self.scale_ = np.max(X, axis=0) - self.min_
                return (X - self.min_) / (self.scale_ + 1e-8)
    except ImportError:
        # 提供简化的MinMaxScaler替代
        class MinMaxScaler:
            def __init__(self):
                self.min_ = None
                self.scale_ = None
            
            def fit_transform(self, X):
                import numpy as np
                X = np.array(X).reshape(-1, 1) if len(np.array(X).shape) == 1 else np.array(X)
                self.min_ = np.min(X, axis=0)
                self.scale_ = np.max(X, axis=0) - self.min_
                return (X - self.min_) / (self.scale_ + 1e-8)
    VISUALIZATION_AVAILABLE = True
    
    # 设置中文字体
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    except:
        pass  # 如果字体设置失败，使用默认字体
    
    print("✅ 可视化库加载成功")
except ImportError as e:
    VISUALIZATION_AVAILABLE = False
    print(f"⚠️ 可视化库未安装: {e}")
    print("   可以运行: pip install matplotlib scikit-learn")

# 添加标准化处理和动力系统分析导入
try:
    from scipy import stats
    from scipy.stats import gaussian_kde
    from scipy.signal import find_peaks
    SCIPY_AVAILABLE = True
    print("✅ scipy库加载成功")
except ImportError:
    SCIPY_AVAILABLE = False
    print("⚠️ scipy库未安装，部分功能将受限")

try:
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
    print("✅ sklearn库加载成功")
except ImportError:
    SKLEARN_AVAILABLE = False
    print("⚠️ sklearn库未安装，将使用简化的标准化方法")
    
    # 提供简化的StandardScaler替代
    class StandardScaler:
        def __init__(self):
            self.mean_ = None
            self.scale_ = None
        
        def fit_transform(self, X):
            import numpy as np
            X = np.array(X)
            self.mean_ = np.mean(X, axis=0)
            self.scale_ = np.std(X, axis=0)
            return (X - self.mean_) / (self.scale_ + 1e-8)


class MelodyDynamicsSystem:
    """
    旋律动力系统分析器
    
    基于动力系统理论分析旋律的：
    - 导数系统（速度、加速度）
    - 吸引子识别（隐形引力线）
    - 曲率张量计算
    - 多尺度时间分析
    - 系统稳定性评估
    """
    
    def __init__(self, time_window=0.2, attractor_threshold=3.0):
        """
        初始化旋律动力系统分析器
        
        Args:
            time_window: 时间窗比例 (0.0-1.0)
            attractor_threshold: 吸引子判定阈值
        """
        self.time_window = time_window
        self.attractor_threshold = attractor_threshold
        
    def analyze_dynamics(self, pitch_series: List[float]) -> Dict[str, Any]:
        """
        分析旋律动力系统特征
        
        Args:
            pitch_series: 音高序列
            
        Returns:
            包含完整动力系统分析的字典
        """
        if len(pitch_series) < 3:
            return {'error': 'insufficient_data_for_dynamics_analysis'}
        
        try:
            pitch_array = np.array(pitch_series)
            
            # 1. 计算导数系统
            velocity, acceleration = self._compute_derivatives(pitch_array)
            
            # 2. 识别吸引子
            attractors = self._detect_attractors(pitch_array)
            
            # 3. 计算曲率张量
            curvature = self._calculate_curvature(velocity, acceleration)
            
            # 4. 分析多尺度特征
            time_scales = self._multi_scale_analysis(pitch_array)
            
            # 5. 评估系统稳定性
            stability = self._assess_stability(attractors, curvature)
            
            # 6. 计算综合动力学指标
            dynamics_metrics = self._calculate_dynamics_metrics(
                velocity, acceleration, curvature, attractors
            )
            
            return {
                'velocity': velocity.tolist(),
                'acceleration': acceleration.tolist(),
                'attractors': attractors,
                'curvature': curvature.tolist(),
                'time_scales': time_scales,
                'stability': stability,
                'dynamics_metrics': dynamics_metrics,
                'system_type': self._classify_system_type(dynamics_metrics)
            }
            
        except Exception as e:
            return {'error': f'dynamics_analysis_failed: {e}'}
    
    def _compute_derivatives(self, pitch_series: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算音高导数（速度、加速度）
        
        Args:
            pitch_series: 音高序列数组
            
        Returns:
            (velocity, acceleration) 元组
        """
        n = len(pitch_series)
        window_size = max(1, int(n * self.time_window))
        
        # 一阶导数（速度）- 使用滑动窗口平均
        velocity = np.zeros(n)
        for i in range(n):
            start = max(0, i - window_size//2)
            end = min(n, i + window_size//2 + 1)
            if end - start > 1:
                velocity[i] = np.mean(np.diff(pitch_series[start:end]))
            else:
                velocity[i] = 0.0
        
        # 二阶导数（加速度）
        acceleration = np.gradient(velocity)
        
        return velocity, acceleration
    
    def _detect_attractors(self, pitch_series: np.ndarray) -> Dict[str, Any]:
        """
        识别吸引子（隐形引力线）
        
        Args:
            pitch_series: 音高序列数组
            
        Returns:
            吸引子分析结果字典
        """
        try:
            # 使用核密度估计寻找音高密度峰值
            if SCIPY_AVAILABLE:
                if SCIPY_AVAILABLE:
                    kde = gaussian_kde(pitch_series)
            else:
                # 简化的密度估计
                import numpy as np
                hist, bin_edges = np.histogram(pitch_series, bins=20, density=True)
                bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
                
                # 创建简化的kde对象
                class SimplifiedKDE:
                    def __init__(self, hist, bin_centers):
                        self.hist = hist
                        self.bin_centers = bin_centers
                    
                    def __call__(self, x):
                        # 简单的线性插值
                        return np.interp(x, self.bin_centers, self.hist)
                
                kde = SimplifiedKDE(hist, bin_centers)
            else:
                # 简化的密度估计
                import numpy as np
                hist, bin_edges = np.histogram(pitch_series, bins=20, density=True)
                bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
                
                # 创建简化的kde对象
                class SimplifiedKDE:
                    def __init__(self, hist, bin_centers):
                        self.hist = hist
                        self.bin_centers = bin_centers
                    
                    def __call__(self, x):
                        # 简单的线性插值
                        return np.interp(x, self.bin_centers, self.hist)
                
                kde = SimplifiedKDE(hist, bin_centers)
            x = np.linspace(np.min(pitch_series), np.max(pitch_series), 100)
            density = kde(x)
            
            # 识别主要吸引子
            if SCIPY_AVAILABLE:
                if SCIPY_AVAILABLE:
                peaks, properties = find_peaks(density, height=np.max(density) * 0.1)
            else:
                # 简化的峰值检测
                import numpy as np
                peaks = []
                threshold = np.max(density) * 0.1
                for i in range(1, len(density) - 1):
                    if density[i] > density[i-1] and density[i] > density[i+1] and density[i] > threshold:
                        peaks.append(i)
                peaks = np.array(peaks)
            else:
                # 简化的峰值检测
                import numpy as np
                peaks = []
                threshold = np.max(density) * 0.1
                for i in range(1, len(density) - 1):
                    if density[i] > density[i-1] and density[i] > density[i+1] and density[i] > threshold:
                        peaks.append(i)
                peaks = np.array(peaks)
            attractors = x[peaks]
            
            # 计算每个音符到最近吸引子的距离
            distances = []
            attractor_assignments = []
            
            for p in pitch_series:
                if len(attractors) > 0:
                    dists = [abs(p - a) for a in attractors]
                    min_dist = min(dists)
                    closest_attractor = np.argmin(dists)
                    distances.append(min_dist)
                    attractor_assignments.append(closest_attractor)
                else:
                    distances.append(0.0)
                    attractor_assignments.append(-1)
            
            # 计算吸引子强度
            attractor_strength = np.mean(density[peaks]) if len(peaks) > 0 else 0.0
            
            return {
                'positions': attractors.tolist(),
                'distances': distances,
                'assignments': attractor_assignments,
                'strength': float(attractor_strength),
                'count': len(attractors),
                'density_profile': density.tolist(),
                'density_x': x.tolist()
            }
            
        except Exception as e:
            return {
                'positions': [],
                'distances': [0.0] * len(pitch_series),
                'assignments': [-1] * len(pitch_series),
                'strength': 0.0,
                'count': 0,
                'error': str(e)
            }
    
    def _calculate_curvature(self, velocity: np.ndarray, acceleration: np.ndarray) -> np.ndarray:
        """
        计算曲率张量 κ(t) = |v × a| / |v|^3
        
        Args:
            velocity: 速度数组
            acceleration: 加速度数组
            
        Returns:
            曲率数组
        """
        curvature = np.zeros(len(velocity))
        
        for i in range(len(velocity)):
            v = velocity[i]
            a = acceleration[i]
            
            if abs(v) > 1e-5:  # 避免除以零
                # 在一维情况下，曲率简化为 |a| / |v|^2
                curvature[i] = abs(a) / (abs(v)**2)
            else:
                curvature[i] = 0.0
        
        return curvature
    
    def _multi_scale_analysis(self, pitch_series: np.ndarray) -> Dict[str, List[Dict]]:
        """
        多尺度时间分析
        
        Args:
            pitch_series: 音高序列数组
            
        Returns:
            多尺度分析结果字典
        """
        scales = {
            'micro_scale': self._analyze_scale(pitch_series, window=0.05),    # 微观尺度
            'meso_scale': self._analyze_scale(pitch_series, window=0.3),     # 中观尺度
            'macro_scale': self._analyze_scale(pitch_series, window=0.7)     # 宏观尺度
        }
        return scales
    
    def _analyze_scale(self, pitch_series: np.ndarray, window: float) -> List[Dict]:
        """
        特定时间尺度的分析
        
        Args:
            pitch_series: 音高序列数组
            window: 窗口大小比例
            
        Returns:
            该尺度的特征列表
        """
        n = len(pitch_series)
        window_size = max(1, int(n * window))
        features = []
        
        for i in range(n):
            start = max(0, i - window_size//2)
            end = min(n, i + window_size//2 + 1)
            segment = pitch_series[start:end]
            
            if len(segment) > 1:
                # 零交叉率（方向变化频率）
                diff_segment = np.diff(segment)
                zcr = np.sum(np.diff(np.sign(diff_segment)) != 0) / len(diff_segment) if len(diff_segment) > 1 else 0.0
                
                # 包络调制深度
                envelope = np.max(segment) - np.min(segment)
                
                # 动态范围
                mean_val = np.mean(segment)
                dynamic_range = envelope / (abs(mean_val) + 1e-5)
                
                # 感知起伏度
                perceived_variation = zcr * envelope
                
                # 局部复杂度
                local_complexity = np.std(diff_segment) if len(diff_segment) > 1 else 0.0
                
                features.append({
                    'position': i,
                    'zero_crossing_rate': float(zcr),
                    'envelope_depth': float(envelope),
                    'dynamic_range': float(dynamic_range),
                    'perceived_variation': float(perceived_variation),
                    'local_complexity': float(local_complexity)
                })
            else:
                features.append({
                    'position': i,
                    'zero_crossing_rate': 0.0,
                    'envelope_depth': 0.0,
                    'dynamic_range': 0.0,
                    'perceived_variation': 0.0,
                    'local_complexity': 0.0
                })
        
        return features
    
    def _assess_stability(self, attractors: Dict, curvature: np.ndarray) -> str:
        """
        评估系统稳定性
        
        Args:
            attractors: 吸引子分析结果
            curvature: 曲率数组
            
        Returns:
            稳定性评估字符串
        """
        avg_distance = np.mean(attractors['distances']) if attractors['distances'] else 0.0
        avg_curvature = np.mean(curvature)
        curvature_variance = np.var(curvature)
        attractor_count = attractors['count']
        
        # 基于动力系统理论的稳定性分类
        if curvature_variance > 0.5 and avg_curvature > 0.1:
            return "高度不稳定（脉冲式）"
        elif avg_distance < 2.0 and curvature_variance < 0.1 and attractor_count > 0:
            return "高度稳定（吸引子主导）"
        elif avg_curvature < 0.05:
            return "稳定（低曲率）"
        elif attractor_count == 0:
            return "无吸引子（自由运动）"
        else:
            return "中等稳定"
    
    def _calculate_dynamics_metrics(self, velocity: np.ndarray, acceleration: np.ndarray, 
                                  curvature: np.ndarray, attractors: Dict) -> Dict[str, float]:
        """
        计算综合动力学指标
        
        Args:
            velocity: 速度数组
            acceleration: 加速度数组
            curvature: 曲率数组
            attractors: 吸引子分析结果
            
        Returns:
            动力学指标字典
        """
        return {
            # 速度特征
            'mean_velocity': float(np.mean(np.abs(velocity))),
            'velocity_variance': float(np.var(velocity)),
            'max_velocity': float(np.max(np.abs(velocity))),
            
            # 加速度特征
            'mean_acceleration': float(np.mean(np.abs(acceleration))),
            'acceleration_variance': float(np.var(acceleration)),
            'max_acceleration': float(np.max(np.abs(acceleration))),
            
            # 曲率特征
            'mean_curvature': float(np.mean(curvature)),
            'curvature_variance': float(np.var(curvature)),
            'max_curvature': float(np.max(curvature)),
            
            # 吸引子特征
            'attractor_count': attractors['count'],
            'attractor_strength': attractors['strength'],
            'mean_attractor_distance': float(np.mean(attractors['distances'])),
            
            # 综合指标
            'system_energy': float(np.mean(velocity**2 + acceleration**2)),
            'phase_space_volume': float(np.std(velocity) * np.std(acceleration)),
            'lyapunov_proxy': float(np.mean(np.abs(np.diff(curvature))))  # 李雅普诺夫指数代理
        }
    
    def _classify_system_type(self, metrics: Dict[str, float]) -> str:
        """
        分类动力系统类型
        
        Args:
            metrics: 动力学指标字典
            
        Returns:
            系统类型字符串
        """
        energy = metrics['system_energy']
        lyapunov = metrics['lyapunov_proxy']
        attractor_count = metrics['attractor_count']
        
        if lyapunov > 0.1 and energy > 10.0:
            return "混沌系统"
        elif attractor_count > 2 and metrics['attractor_strength'] > 1.0:
            return "多吸引子系统"
        elif attractor_count == 1 and metrics['mean_attractor_distance'] < 2.0:
            return "单吸引子系统"
        elif energy < 1.0 and lyapunov < 0.01:
            return "准静态系统"
        else:
            return "复杂动力系统"



class EnhancedWaveletFeatureAnalyzer:
    """
    增强的小波特征分析器
    充分利用局部波动性, d2_rms, rms_ratio的全部信息
    """
    
    def __init__(self):
        self.scaler = StandardScaler()
    
    def extract_comprehensive_features(self, wavelet_features):
        """
        提取全面的小波特征
        
        Args:
            wavelet_features: 小波分析结果
            
        Returns:
            包含所有标准化特征的字典
        """
        if 'error' in wavelet_features:
            return {'error': wavelet_features['error']}
        
        # 提取原始特征
        局部波动性 = wavelet_features.get('局部波动性', 0.0)
        d2_rms = wavelet_features.get('d2_rms', 0.0)
        rms_ratio = wavelet_features.get('rms_ratio', 0.0)
        
        # 计算增强特征
        enhanced_features = {
            # 核心波动性特征
            'local_volatility_d1': 局部波动性,  # 局部音程波动性（主要）
            'medium_volatility_d2': d2_rms,  # 中等尺度波动性（音程曲率）
            'volatility_character_ratio': rms_ratio,  # 波动性特征比率
            
            # 波动性类型分类
            'volatility_type': self._classify_volatility_type(局部波动性, d2_rms, rms_ratio),
            
            # 装饰音模式识别
            'ornament_pattern': self._identify_ornament_pattern(局部波动性, d2_rms, rms_ratio),
            
            # 波动性强度等级
            'volatility_intensity': self._calculate_volatility_intensity(局部波动性, d2_rms),
            
            # 波动性平衡度
            'volatility_balance': self._calculate_volatility_balance(局部波动性, d2_rms),
            
            # 原始特征（用于标准化）
            'raw_局部波动性': 局部波动性,
            'raw_d2_rms': d2_rms,
            'raw_rms_ratio': rms_ratio
        }
        
        return enhanced_features
    
    def _classify_volatility_type(self, 局部波动性, d2_rms, rms_ratio):
        """
        分类波动性类型
        
        Returns:
            波动性类型字符串
        """
        if rms_ratio > 3.0:
            return "尖锐毛刺型"  # 高比率：快速尖锐变化
        elif rms_ratio > 1.5:
            return "混合波动型"  # 中等比率：混合特征
        elif rms_ratio > 0.5:
            return "平滑波浪型"  # 低比率：平滑波浪状
        else:
            return "极平滑型"    # 极低比率：几乎无波动
    
    def _identify_ornament_pattern(self, 局部波动性, d2_rms, rms_ratio):
        """
        识别装饰音模式
        
        Returns:
            装饰音模式字符串
        """
        if 局部波动性 > 5.0 and rms_ratio > 2.0:
            return "颤音/震音型"
        elif 局部波动性 > 3.0 and rms_ratio > 1.5:
            return "回音/波音型"
        elif 局部波动性 > 1.0 and rms_ratio < 1.0:
            return "滑音/连音型"
        else:
            return "简单进行型"
    
    def _calculate_volatility_intensity(self, 局部波动性, d2_rms):
        """
        计算波动性强度等级
        
        Returns:
            强度等级 (1-5)
        """
        combined_volatility = 局部波动性 + 0.5 * d2_rms  # 加权组合
        
        if combined_volatility > 15.0:
            return 5  # 极高强度
        elif combined_volatility > 10.0:
            return 4  # 高强度
        elif combined_volatility > 5.0:
            return 3  # 中等强度
        elif combined_volatility > 2.0:
            return 2  # 低强度
        else:
            return 1  # 极低强度
    
    def _calculate_volatility_balance(self, 局部波动性, d2_rms):
        """
        计算波动性平衡度
        
        Returns:
            平衡度 (0-1)，1表示完全平衡
        """
        if 局部波动性 == 0 and d2_rms == 0:
            return 1.0
        
        total = 局部波动性 + d2_rms
        if total == 0:
            return 1.0
        
        # 计算平衡度：越接近0.5越平衡
        d1_proportion = 局部波动性 / total
        balance = 1.0 - abs(d1_proportion - 0.5) * 2
        
        return balance
    
    def standardize_features(self, feature_list, feature_names):
        """
        对特征进行Z-score标准化
        
        Args:
            feature_list: 特征值列表的列表
            feature_names: 特征名称列表
            
        Returns:
            标准化后的特征字典
        """
        if not feature_list or not feature_names:
            return {}
        
        # 转换为numpy数组
        feature_array = np.array(feature_list)
        
        # Z-score标准化
        if SCIPY_AVAILABLE:
                if SCIPY_AVAILABLE:
                standardized_array = stats.zscore(feature_array, axis=0, nan_policy='omit')
            else:
                # 简化的Z-score标准化
                import numpy as np
                mean = np.mean(feature_array, axis=0)
                std = np.std(feature_array, axis=0)
                standardized_array = (feature_array - mean) / (std + 1e-8)
            else:
                # 简化的Z-score标准化
                import numpy as np
                mean = np.mean(feature_array, axis=0)
                std = np.std(feature_array, axis=0)
                standardized_array = (feature_array - mean) / (std + 1e-8)
        
        # 处理NaN值
        standardized_array = np.nan_to_num(standardized_array, nan=0.0)
        
        # 转换回字典格式
        standardized_features = {}
        for i, name in enumerate(feature_names):
            standardized_features[f'{name}_zscore'] = standardized_array[:, i].tolist()
        
        return standardized_features




class EnhancedWaveletFeatureAnalyzer:
    """
    增强的小波特征分析器
    充分利用d1_rms, d2_rms, rms_ratio的全部信息
    """
    
    def __init__(self):
        self.scaler = StandardScaler()
    
    def extract_comprehensive_features(self, wavelet_features):
        """
        提取全面的小波特征
        
        Args:
            wavelet_features: 小波分析结果
            
        Returns:
            包含所有标准化特征的字典
        """
        if 'error' in wavelet_features:
            return {'error': wavelet_features['error']}
        
        # 提取原始特征
        d1_rms = wavelet_features.get('d1_rms', 0.0)
        d2_rms = wavelet_features.get('d2_rms', 0.0)
        rms_ratio = wavelet_features.get('rms_ratio', 0.0)
        
        # 计算增强特征
        enhanced_features = {
            # 核心波动性特征
            'local_volatility_d1': d1_rms,  # 局部音程波动性（主要）
            'medium_volatility_d2': d2_rms,  # 中等尺度波动性（音程曲率）
            'volatility_character_ratio': rms_ratio,  # 波动性特征比率
            
            # 波动性类型分类
            'volatility_type': self._classify_volatility_type(d1_rms, d2_rms, rms_ratio),
            
            # 装饰音模式识别
            'ornament_pattern': self._identify_ornament_pattern(d1_rms, d2_rms, rms_ratio),
            
            # 波动性强度等级
            'volatility_intensity': self._calculate_volatility_intensity(d1_rms, d2_rms),
            
            # 波动性平衡度
            'volatility_balance': self._calculate_volatility_balance(d1_rms, d2_rms),
            
            # 原始特征（用于标准化）
            'raw_d1_rms': d1_rms,
            'raw_d2_rms': d2_rms,
            'raw_rms_ratio': rms_ratio
        }
        
        return enhanced_features
    
    def _classify_volatility_type(self, d1_rms, d2_rms, rms_ratio):
        """
        分类波动性类型
        
        Returns:
            波动性类型字符串
        """
        if rms_ratio > 3.0:
            return "尖锐毛刺型"  # 高比率：快速尖锐变化
        elif rms_ratio > 1.5:
            return "混合波动型"  # 中等比率：混合特征
        elif rms_ratio > 0.5:
            return "平滑波浪型"  # 低比率：平滑波浪状
        else:
            return "极平滑型"    # 极低比率：几乎无波动
    
    def _identify_ornament_pattern(self, d1_rms, d2_rms, rms_ratio):
        """
        识别装饰音模式
        
        Returns:
            装饰音模式字符串
        """
        if d1_rms > 5.0 and rms_ratio > 2.0:
            return "颤音/震音型"
        elif d1_rms > 3.0 and rms_ratio > 1.5:
            return "回音/波音型"
        elif d1_rms > 1.0 and rms_ratio < 1.0:
            return "滑音/连音型"
        else:
            return "简单进行型"
    
    def _calculate_volatility_intensity(self, d1_rms, d2_rms):
        """
        计算波动性强度等级
        
        Returns:
            强度等级 (1-5)
        """
        combined_volatility = d1_rms + 0.5 * d2_rms  # 加权组合
        
        if combined_volatility > 15.0:
            return 5  # 极高强度
        elif combined_volatility > 10.0:
            return 4  # 高强度
        elif combined_volatility > 5.0:
            return 3  # 中等强度
        elif combined_volatility > 2.0:
            return 2  # 低强度
        else:
            return 1  # 极低强度
    
    def _calculate_volatility_balance(self, d1_rms, d2_rms):
        """
        计算波动性平衡度
        
        Returns:
            平衡度 (0-1)，1表示完全平衡
        """
        if d1_rms == 0 and d2_rms == 0:
            return 1.0
        
        total = d1_rms + d2_rms
        if total == 0:
            return 1.0
        
        # 计算平衡度：越接近0.5越平衡
        d1_proportion = d1_rms / total
        balance = 1.0 - abs(d1_proportion - 0.5) * 2
        
        return balance
    
    def standardize_features(self, feature_list, feature_names):
        """
        对特征进行Z-score标准化
        
        Args:
            feature_list: 特征值列表的列表
            feature_names: 特征名称列表
            
        Returns:
            标准化后的特征字典
        """
        if not feature_list or not feature_names:
            return {}
        
        # 转换为numpy数组
        feature_array = np.array(feature_list)
        
        # Z-score标准化
        if SCIPY_AVAILABLE:
                if SCIPY_AVAILABLE:
                standardized_array = stats.zscore(feature_array, axis=0, nan_policy='omit')
            else:
                # 简化的Z-score标准化
                import numpy as np
                mean = np.mean(feature_array, axis=0)
                std = np.std(feature_array, axis=0)
                standardized_array = (feature_array - mean) / (std + 1e-8)
            else:
                # 简化的Z-score标准化
                import numpy as np
                mean = np.mean(feature_array, axis=0)
                std = np.std(feature_array, axis=0)
                standardized_array = (feature_array - mean) / (std + 1e-8)
        
        # 处理NaN值
        standardized_array = np.nan_to_num(standardized_array, nan=0.0)
        
        # 转换回字典格式
        standardized_features = {}
        for i, name in enumerate(feature_names):
            standardized_features[f'{name}_zscore'] = standardized_array[:, i].tolist()
        
        return standardized_features



class WaveletMethod:
    """
    基于绝对不规则性的绝对不规则性分析方法
    
    核心改进：
    1. 使用RMS测量细节系数的绝对幅度
    2. 停止归一化到total_energy  
    3. 局部波动性作为主要的"方法2"得分
    4. 提供长度归一化的绝对不规则性度量
    """
    
    def __init__(self, 
                 wavelet_type: str = 'haar',
                 max_decomp_levels: int = 2,
                 use_intervals: bool = True,
                 weighting_scheme: str = 'absolute_rms'):
        self.wavelet_type = wavelet_type
        self.max_decomp_levels = max_decomp_levels
        self.use_intervals = use_intervals
        self.weighting_scheme = weighting_scheme
        self.last_analysis = None
    
    def analyze_single_work_smoothness(self, pitch_series):
        """分析单个作品的平滑度（基于局部波动性的倒数）"""
        features = self.analyze_single_work_features(pitch_series)
        
        if 'error' in features:
            return 0.0
        
        # 使用局部波动性作为主要的不规则性度量
        局部波动性 = features.get('局部波动性', 0.0)
        
        # 转换为平滑度：RMS越大，平滑度越低
        smoothness = 1.0 / (1.0 + 局部波动性)
        
        return smoothness
    
    def get_method2_score(self, pitch_series):
        """获取方法2得分（局部波动性）用于与音程均幅直接比较"""
        features = self.analyze_single_work_features(pitch_series)
        
        if 'error' in features:
            return 0.0
        
        return features.get('局部波动性', 0.0)
    
    def analyze_single_work_features(self, pitch_series):
        """分析单个作品的绝对不规则性特征"""
        if len(pitch_series) < 4:
            return {'error': 'sequence_too_short'}
        
        try:
            # 准备分析序列
            if self.use_intervals:
                analysis_sequence = np.diff(pitch_series)
            else:
                analysis_sequence = np.array(pitch_series)
            
            if len(analysis_sequence) < 2:
                return {'error': 'analysis_sequence_too_short'}
            
            # 绝对不规则性分析
            wavelet_result = self._compute_absolute_irregularity_features(analysis_sequence)
            
            # 缓存分析结果
            self.last_analysis = wavelet_result
            
            return wavelet_result
            
        except Exception as e:
            return {'error': f'analysis_failed: {e}'}
    
    def _compute_absolute_irregularity_features(self, sequence):
        """计算绝对不规则性特征（核心实现）"""
        # 确定分解层数
        max_levels = min(
            self.max_decomp_levels,
            int(np.floor(np.log2(len(sequence)))) - 1
        )
        
        if max_levels < 1:
            return {'error': 'insufficient_data_for_decomposition'}
        
        return self._haar_absolute_features(sequence, max_levels)
    
    def _haar_absolute_features(self, sequence, max_levels):
        """使用简化Haar小波的绝对不规则性特征计算"""
        current_signal = sequence.copy()
        detail_rms = []
        detail_stats = []
        
        for level in range(1, max_levels + 1):
            if len(current_signal) < 2:
                break
            
            # 确保偶数长度
            n = len(current_signal)
            if n % 2 == 1:
                current_signal = current_signal[:-1]
                n = len(current_signal)
            
            if n < 2:
                break
            
            # Haar小波分解
            pairs = current_signal.reshape(-1, 2)
            sqrt2 = math.sqrt(2)
            
            # 近似和细节系数
            approximation = (pairs[:, 0] + pairs[:, 1]) / sqrt2
            details = (pairs[:, 0] - pairs[:, 1]) / sqrt2
            
            # 计算RMS：sqrt(mean(coeffs^2))
            rms = np.sqrt(np.mean(details ** 2))
            detail_rms.append(rms)
            
            # 详细统计
            stats = {
                'level': level,
                'length': len(details),
                'rms': float(rms),
                'energy': float(np.sum(details ** 2)),
                'mean': float(np.mean(details)),
                'std': float(np.std(details)),
                'max_abs': float(np.max(np.abs(details))),
                'variance': float(np.var(details))
            }
            detail_stats.append(stats)
            
            # 为下一层准备
            current_signal = approximation
        
        # 最终近似RMS
        approx_rms = np.sqrt(np.mean(current_signal ** 2))
        
        return self._compute_absolute_features(
            detail_rms, approx_rms, detail_stats, max_levels
        )
    
    def _compute_absolute_features(self, detail_rms, approx_rms, detail_stats, max_levels):
        """计算绝对不规则性特征（用户建议的新特征向量）"""
        
        if not detail_rms:
            return {'error': 'no_detail_rms'}
        
        # 基础特征
        features = {
            'sequence_type': 'intervals' if self.use_intervals else 'pitches',
            'wavelet_type': self.wavelet_type,
            'decomposition_levels': len(detail_rms),
            'approx_rms': float(approx_rms),
            'detail_rms_values': detail_rms,
            'detail_stats': detail_stats
        }
        
        # === 核心绝对不规则性特征 ===
        
        # 1. 局部波动性 (Primary Roughness Score) - 主要的方法2得分
        features['局部波动性'] = detail_rms[0] if len(detail_rms) > 0 else 0.0
        
        # 2. d2_rms (Secondary Roughness Score) - 次要粗糙度得分
        features['d2_rms'] = detail_rms[1] if len(detail_rms) > 1 else 0.0
        
        # 3. rms_ratio (Roughness Character) - 粗糙度特征比率
        if len(detail_rms) > 1 and detail_rms[1] > 0:
            features['rms_ratio'] = detail_rms[0] / detail_rms[1]
        else:
            features['rms_ratio'] = float('inf') if detail_rms[0] > 0 else 0.0
        
        # 4. 总体RMS（反向加权）
        if len(detail_rms) > 1:
            weights = [1.0 / (i + 1) for i in range(len(detail_rms))]
            total_weight = sum(weights)
            weighted_rms = sum(w * rms for w, rms in zip(weights, detail_rms)) / total_weight
        else:
            weighted_rms = detail_rms[0]
        
        features['weighted_rms'] = weighted_rms
        
        # 5. RMS对比度（细节vs近似）
        if approx_rms > 0:
            features['detail_to_approx_rms_ratio'] = detail_rms[0] / approx_rms
        else:
            features['detail_to_approx_rms_ratio'] = float('inf') if detail_rms[0] > 0 else 0.0
        
        # 6. 多层级RMS特征
        features['max_detail_rms'] = max(detail_rms) if detail_rms else 0.0
        features['min_detail_rms'] = min(detail_rms) if detail_rms else 0.0
        features['rms_range'] = features['max_detail_rms'] - features['min_detail_rms']
        
        return features
    
    def get_analysis_details(self):
        """获取最后一次分析的详细信息"""
        return self.last_analysis or {}




# === 双方法家族分析系统 ===

class MethodFamilyA:
    """
    方法家族A：基于音程的指标（分析性处理的代理）
    专注于量化窗口内音程的大小和分布，测量局部波动性或"激动"程度
    """
    
    @staticmethod
    def mean_absolute_interval(i1: float, i2: float) -> float:
        """平均绝对音程：(|I1| + |I2|) / 2"""
        return (abs(i1) + abs(i2)) / 2
    
    @staticmethod
    def max_absolute_interval(i1: float, i2: float) -> float:
        """最大绝对音程：max(|I1|, |I2|)"""
        return max(abs(i1), abs(i2))
    
    @staticmethod
    def local_intervallic_rms(i1: float, i2: float) -> float:
        """
        局部音程RMS：sqrt((I1^2 + I2^2) / 2)
        优越的局部激动度量，对较大跳动给予更高权重
        """
        return math.sqrt((i1**2 + i2**2) / 2)
    
    @staticmethod
    def analyze_three_note_group(note1: float, note2: float, note3: float) -> Dict[str, float]:
        """分析单个三音组的所有家族A指标"""
        i1 = note2 - note1
        i2 = note3 - note2
        
        return {
            'mean_abs_interval': MethodFamilyA.mean_absolute_interval(i1, i2),
            'max_abs_interval': MethodFamilyA.max_absolute_interval(i1, i2),
            'local_rms': MethodFamilyA.local_intervallic_rms(i1, i2),
            'intervals': [i1, i2]
        }


class MethodFamilyB:
    """
    方法家族B：基于轮廓的指标（整体性处理的代理）
    专注于量化旋律线条的形状和方向变化，测量"曲折度"或"锯齿度"
    """
    
    @staticmethod
    def directional_change(i1: float, i2: float) -> int:
        """
        方向变化：二元指标
        如果同向返回0，如果方向改变返回1
        """
        if i1 == 0 or i2 == 0:
            return 0  # 处理平音的情况
        return 0 if (i1 > 0) == (i2 > 0) else 1
    
    @staticmethod
    def angle_of_inflection(i1: float, i2: float) -> float:
        """
        拐点角度：计算两个音程向量之间的夹角
        v1 = (1, I1), v2 = (1, I2)
        返回角度（度数）
        """
        # 向量表示：(时间步长=1, 音程值)
        v1 = np.array([1, i1])
        v2 = np.array([1, i2])
        
        # 计算夹角
        cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
        
        # 防止数值误差导致的域外值
        cos_angle = np.clip(cos_angle, -1.0, 1.0)
        
        # 转换为度数
        angle_rad = np.arccos(cos_angle)
        angle_deg = np.degrees(angle_rad)
        
        return angle_deg
    
    @staticmethod
    def contour_complexity(i1: float, i2: float) -> float:
        """
        轮廓复杂度：结合方向变化和角度的综合指标
        """
        direction_change = MethodFamilyB.directional_change(i1, i2)
        angle = MethodFamilyB.angle_of_inflection(i1, i2)
        
        # 如果有方向变化，角度越小（转折越急）复杂度越高
        if direction_change == 1:
            # 将角度转换为复杂度：90度最复杂，180度最简单
            complexity = 1.0 - (angle - 90) / 90 if angle >= 90 else 1.0
        else:
            # 无方向变化时复杂度较低
            complexity = 0.1
        
        return max(0.0, min(1.0, complexity))
    
    @staticmethod
    def analyze_three_note_group(note1: float, note2: float, note3: float) -> Dict[str, float]:
        """分析单个三音组的所有家族B指标"""
        i1 = note2 - note1
        i2 = note3 - note2
        
        return {
            'directional_change': MethodFamilyB.directional_change(i1, i2),
            'angle_of_inflection': MethodFamilyB.angle_of_inflection(i1, i2),
            'contour_complexity': MethodFamilyB.contour_complexity(i1, i2),
            'intervals': [i1, i2]
        }


class DualMethodAnalyzer:
    """双方法分析器（替代原来的一致性比较）"""
    
    def __init__(self):
        pass
        
    def analyze_melody_dual_methods(self, pitch_series):
        """
        分析整个旋律的两个方法家族指标
        
        Returns:
            (family_a_results, family_b_results): 两个结果列表
        """
        if len(pitch_series) < 3:
            return [], []
        
        family_a_results = []
        family_b_results = []
        
        # 滑动窗口分析每个三音组
        for i in range(len(pitch_series) - 2):
            note1, note2, note3 = pitch_series[i], pitch_series[i+1], pitch_series[i+2]
            
            # 分析家族A指标
            a_metrics = MethodFamilyA.analyze_three_note_group(note1, note2, note3)
            a_metrics['position'] = i
            family_a_results.append(a_metrics)
            
            # 分析家族B指标
            b_metrics = MethodFamilyB.analyze_three_note_group(note1, note2, note3)
            b_metrics['position'] = i
            family_b_results.append(b_metrics)
        
        return family_a_results, family_b_results
    
    def compute_orthogonality_analysis(self, family_a_results, family_b_results):
        """计算正交性分析"""
        
        if not family_a_results or not family_b_results:
            return {'error': 'insufficient_data'}
        
        # 提取关键指标
        a_rms = [result['local_rms'] for result in family_a_results]
        b_complexity = [result['contour_complexity'] for result in family_b_results]
        
        # 计算相关性（处理NaN情况）
        try:
            correlation_matrix = np.corrcoef(a_rms, b_complexity)
            if np.isnan(correlation_matrix).any():
                correlation = 0.0
            else:
                correlation = correlation_matrix[0, 1]
        except:
            correlation = 0.0
        
        # 正交性评估
        if abs(correlation) < 0.3:
            orthogonality_assessment = "高度正交"
        elif abs(correlation) < 0.6:
            orthogonality_assessment = "中度相关"
        else:
            orthogonality_assessment = "强相关"
        
        return {
            'correlation': correlation,
            'orthogonality_assessment': orthogonality_assessment,
            'a_rms_stats': {
                'mean': np.mean(a_rms),
                'std': np.std(a_rms),
                'min': np.min(a_rms),
                'max': np.max(a_rms)
            },
            'b_complexity_stats': {
                'mean': np.mean(b_complexity),
                'std': np.std(b_complexity),
                'min': np.min(b_complexity),
                'max': np.max(b_complexity)
            },
            'direction_changes': sum(result['directional_change'] for result in family_b_results),
            'total_groups': len(family_b_results)
        }


    def create_dual_method_visualization(self, pitch_series, family_a_results, family_b_results, 
                                       title="双方法家族对比分析", save_path=None):
        """
        创建双方法可视化图表
        
        Args:
            pitch_series: 原始音高序列
            family_a_results: 家族A分析结果
            family_b_results: 家族B分析结果
            title: 图表标题
            save_path: 保存路径（可选）
            
        Returns:
            matplotlib Figure对象（如果可视化库可用）
        """
        if not VISUALIZATION_AVAILABLE:
            print("❌ 可视化库不可用，跳过图表生成")
            return None
        
        try:
            # 创建图表布局
            fig = plt.figure(figsize=(16, 12))
            gs = GridSpec(3, 2, figure=fig, height_ratios=[1, 2, 2], width_ratios=[2, 1])
            
            # 1. 顶部：原始音高曲线（音乐上下文）
            ax_pitch = fig.add_subplot(gs[0, :])
            self._plot_pitch_context(ax_pitch, pitch_series)
            
            # 2. 左上：叠加时间序列图（核心方案）
            ax_overlay = fig.add_subplot(gs[1, 0])
            correlation = self._plot_overlay_time_series(ax_overlay, family_a_results, family_b_results)
            
            # 3. 右上：二维散点图（正交性检验）
            ax_scatter = fig.add_subplot(gs[1, 1])
            self._plot_orthogonality_scatter(ax_scatter, family_a_results, family_b_results)
            
            # 4. 左下：家族A详细分析
            ax_family_a = fig.add_subplot(gs[2, 0])
            self._plot_family_a_details(ax_family_a, family_a_results)
            
            # 5. 右下：家族B详细分析
            ax_family_b = fig.add_subplot(gs[2, 1])
            self._plot_family_b_details(ax_family_b, family_b_results)
            
            # 设置总标题
            fig.suptitle(title, fontsize=16, fontweight='bold')
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图表（健壮处理）
            if save_path:
                try:
                    plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                               facecolor='white', edgecolor='none')
                    print(f"📊 图表已保存到: {save_path}")
                except Exception as e:
                    print(f"⚠️ 图表保存失败: {e}")
                    # 尝试简化保存
                    try:
                        plt.savefig(save_path, dpi=150)
                        print(f"📊 图表已保存到: {save_path} (简化模式)")
                    except Exception as e2:
                        print(f"❌ 图表保存完全失败: {e2}")
            
            # 不显示图表，只保存（避免交互式显示问题）
            # plt.show()  # 注释掉以避免兼容性问题
            
            # 清理内存
            plt.close(fig)
            
            return fig
            
        except Exception as e:
            print(f"❌ 可视化创建失败: {e}")
            # 清理可能的残留图表
            try:
                plt.close('all')
            except:
                pass
            return None
    
    def _plot_pitch_context(self, ax, pitch_series):
        """绘制原始音高曲线作为上下文"""
        positions = range(len(pitch_series))
        ax.plot(positions, pitch_series, 'o-', color='gray', alpha=0.7, linewidth=2, markersize=4)
        ax.set_title('原始音高序列（音乐上下文）', fontsize=12, fontweight='bold')
        ax.set_xlabel('音符位置')
        ax.set_ylabel('音高（MIDI）')
        ax.grid(True, alpha=0.3)
        
        # 添加三音组窗口标记
        for i in range(len(pitch_series) - 2):
            ax.axvspan(i, i+2, alpha=0.1, color='blue')
    
    def _plot_overlay_time_series(self, ax, family_a_results, family_b_results):
        """绘制叠加时间序列图（核心方案）"""
        positions = [result['position'] for result in family_a_results]
        
        # 提取关键指标
        a_values = [result['local_rms'] for result in family_a_results]
        b_values = [result['contour_complexity'] for result in family_b_results]
        
        # 归一化到[0,1]区间（关键改进）
        scaler_a = MinMaxScaler()
        scaler_b = MinMaxScaler()
        
        a_normalized = scaler_a.fit_transform(np.array(a_values).reshape(-1, 1)).flatten()
        b_normalized = scaler_b.fit_transform(np.array(b_values).reshape(-1, 1)).flatten()
        
        # 绘制两条曲线
        line1 = ax.plot(positions, a_normalized, 'r-o', label='家族A: 局部RMS (归一化)', 
                       linewidth=2, markersize=4, alpha=0.8)
        line2 = ax.plot(positions, b_normalized, 'b-s', label='家族B: 轮廓复杂度 (归一化)', 
                       linewidth=2, markersize=4, alpha=0.8)
        
        ax.set_title('叠加时间序列图（核心方案）', fontsize=12, fontweight='bold')
        ax.set_xlabel('三音组位置')
        ax.set_ylabel('归一化指标值 [0,1]')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 计算并显示相关性信息
        correlation = np.corrcoef(a_normalized, b_normalized)[0, 1] if len(a_normalized) > 1 else 0.0
        ax.text(0.02, 0.98, f'相关系数: {correlation:.3f}', 
                transform=ax.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        return correlation
    
    def _plot_orthogonality_scatter(self, ax, family_a_results, family_b_results):
        """绘制二维散点图（正交性检验）"""
        # 提取关键指标
        a_values = [result['local_rms'] for result in family_a_results]
        b_values = [result['contour_complexity'] for result in family_b_results]
        positions = [result['position'] for result in family_a_results]
        
        # 归一化
        scaler_a = MinMaxScaler()
        scaler_b = MinMaxScaler()
        
        a_normalized = scaler_a.fit_transform(np.array(a_values).reshape(-1, 1)).flatten()
        b_normalized = scaler_b.fit_transform(np.array(b_values).reshape(-1, 1)).flatten()
        
        # 创建散点图
        scatter = ax.scatter(a_normalized, b_normalized, 
                           c=positions, cmap='viridis', 
                           alpha=0.7, s=50, edgecolors='black', linewidth=0.5)
        
        ax.set_title('二维散点图（正交性检验）', fontsize=12, fontweight='bold')
        ax.set_xlabel('家族A: 局部RMS (归一化)')
        ax.set_ylabel('家族B: 轮廓复杂度 (归一化)')
        
        # 添加颜色条（安全处理）
        try:
            cbar = plt.colorbar(scatter, ax=ax)
            cbar.set_label('三音组位置')
        except Exception as e:
            print(f"⚠️ 颜色条创建失败: {e}")
        
        # 计算并显示相关性统计
        correlation = np.corrcoef(a_normalized, b_normalized)[0, 1] if len(a_normalized) > 1 else 0.0
        
        # 正交性评估
        if abs(correlation) < 0.3:
            orthogonality_text = "高度正交"
            color = 'green'
        elif abs(correlation) < 0.6:
            orthogonality_text = "中度相关"
            color = 'orange'
        else:
            orthogonality_text = "强相关"
            color = 'red'
        
        stats_text = f'相关系数: {correlation:.3f}\n正交性: {orthogonality_text}'
        
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                verticalalignment='top', fontsize=10,
                bbox=dict(boxstyle='round', facecolor=color, alpha=0.3))
        
        # 添加对角线参考
        ax.plot([0, 1], [0, 1], 'k--', alpha=0.5, linewidth=1, label='正相关参考线')
        ax.plot([0, 1], [1, 0], 'k:', alpha=0.5, linewidth=1, label='负相关参考线')
        ax.legend(loc='lower right', fontsize=8)
        ax.grid(True, alpha=0.3)
    
    def _plot_family_a_details(self, ax, family_a_results):
        """绘制家族A详细分析"""
        positions = [result['position'] for result in family_a_results]
        
        # 绘制多个指标
        ax.plot(positions, [result['mean_abs_interval'] for result in family_a_results], 
               'r-o', label='平均绝对音程', linewidth=1.5, markersize=3)
        ax.plot(positions, [result['max_abs_interval'] for result in family_a_results], 
               'g-s', label='最大绝对音程', linewidth=1.5, markersize=3)
        ax.plot(positions, [result['local_rms'] for result in family_a_results], 
               'b-^', label='局部RMS', linewidth=1.5, markersize=3)
        
        ax.set_title('家族A：基于音程的指标（分析性处理代理）', fontsize=12, fontweight='bold')
        ax.set_xlabel('三音组位置')
        ax.set_ylabel('指标值')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    
    def create_simplified_visualization(self, pitch_series, family_a_results, family_b_results, 
                                      title="双方法家族对比分析", save_path=None):
        """
        创建简化版可视化（兼容性更好）
        """
        if not VISUALIZATION_AVAILABLE:
            print("❌ 可视化库不可用，跳过图表生成")
            return None
        
        try:
            # 创建简单的2x2布局
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
            
            # 1. 原始音高曲线
            positions = range(len(pitch_series))
            ax1.plot(positions, pitch_series, 'o-', color='gray', alpha=0.7)
            ax1.set_title('原始音高序列')
            ax1.set_xlabel('音符位置')
            ax1.set_ylabel('音高')
            ax1.grid(True, alpha=0.3)
            
            # 2. 叠加时间序列图
            group_positions = [result['position'] for result in family_a_results]
            a_values = [result['local_rms'] for result in family_a_results]
            b_values = [result['contour_complexity'] for result in family_b_results]
            
            # 简单归一化
            a_max = max(a_values) if a_values else 1
            b_max = max(b_values) if b_values else 1
            a_norm = [v/a_max for v in a_values]
            b_norm = [v/b_max for v in b_values]
            
            ax2.plot(group_positions, a_norm, 'r-o', label='家族A: 局部RMS', markersize=3)
            ax2.plot(group_positions, b_norm, 'b-s', label='家族B: 轮廓复杂度', markersize=3)
            ax2.set_title('叠加时间序列图')
            ax2.set_xlabel('三音组位置')
            ax2.set_ylabel('归一化值')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 3. 散点图
            ax3.scatter(a_norm, b_norm, alpha=0.7)
            ax3.set_title('正交性散点图')
            ax3.set_xlabel('家族A (归一化)')
            ax3.set_ylabel('家族B (归一化)')
            ax3.grid(True, alpha=0.3)
            
            # 4. 统计信息
            ax4.axis('off')
            stats_text = f"分析统计\n"
            stats_text += f"三音组数: {len(family_a_results)}\n"
            stats_text += f"A均值: {np.mean(a_values):.3f}\n"
            stats_text += f"B均值: {np.mean(b_values):.3f}\n"
            if len(a_values) > 1:
                correlation = np.corrcoef(a_values, b_values)[0, 1]
                stats_text += f"相关系数: {correlation:.3f}"
            ax4.text(0.1, 0.5, stats_text, fontsize=12, verticalalignment='center')
            
            plt.suptitle(title, fontsize=14)
            plt.tight_layout()
            
            # 保存图表
            if save_path:
                try:
                    plt.savefig(save_path, dpi=150, bbox_inches='tight')
                    print(f"📊 简化图表已保存到: {save_path}")
                except Exception as e:
                    print(f"⚠️ 简化图表保存失败: {e}")
            
            plt.close(fig)
            return fig
            
        except Exception as e:
            print(f"❌ 简化可视化创建失败: {e}")
            try:
                plt.close('all')
            except:
                pass
            return None

    def _plot_family_b_details(self, ax, family_b_results):
        """绘制家族B详细分析"""
        positions = [result['position'] for result in family_b_results]
        
        # 创建双Y轴
        ax2 = ax.twinx()
        
        # 绘制多个指标
        line1 = ax.plot(positions, [result['directional_change'] for result in family_b_results], 
                       'r-o', label='方向变化', linewidth=1.5, markersize=3)
        line2 = ax.plot(positions, [result['contour_complexity'] for result in family_b_results], 
                       'g-s', label='轮廓复杂度', linewidth=1.5, markersize=3)
        line3 = ax2.plot(positions, [result['angle_of_inflection'] for result in family_b_results], 
                        'b-^', label='拐点角度', linewidth=1.5, markersize=3)
        
        ax.set_title('家族B：基于轮廓的指标（整体性处理代理）', fontsize=12, fontweight='bold')
        ax.set_xlabel('三音组位置')
        ax.set_ylabel('二元/复杂度指标', color='red')
        ax2.set_ylabel('角度（度）', color='blue')
        
        # 合并图例
        lines = line1 + line2 + line3
        labels = [l.get_label() for l in lines]
        ax.legend(lines, labels, loc='upper left')
        
        ax.grid(True, alpha=0.3)


class EnhancedChineseMusicAnalyzer:
    """增强版中国传统音乐分析器"""
    
    def __init__(self, epsilon=1e-6, min_groups_for_analysis=10):
        self.epsilon = epsilon
        self.min_groups_for_analysis = min_groups_for_analysis
        self.pitch_series = None
        self.indexed_diffs = []
        self.three_note_groups = []
        self.strict_groups = []
        self.non_strict_groups = []
        self.swave_curve = []
        self.holder_indices = []
        self.wavelet_energies = []
        
        # 添加改进的小波方法
        self.wavelet_method = WaveletMethod(
            wavelet_type='haar',
            max_decomp_levels=2,  # 聚焦1-2层，适合三音组分析
            use_intervals=True,
            weighting_scheme='absolute_rms'  # 绝对不规则性度量
        )
        
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 添加双方法分析器
        self.dual_method_analyzer = DualMethodAnalyzer()
        
        # 添加增强的小波特征分析器
        self.enhanced_wavelet_analyzer = EnhancedWaveletFeatureAnalyzer()
        
        # 添加旋律动力系统分析器
        self.melody_dynamics_analyzer = MelodyDynamicsSystem(
            time_window=0.2,
            attractor_threshold=3.0
        )
        
        # 添加增强的小波特征分析器
        self.enhanced_wavelet_analyzer = EnhancedWaveletFeatureAnalyzer()
        
        # 添加旋律动力系统分析器
        self.melody_dynamics_analyzer = MelodyDynamicsSystem(
            time_window=0.2,
            attractor_threshold=3.0
        )
    
    def get_midi_files(self, folder_path: str) -> List[str]:
        """从文件夹中检索所有 MIDI 文件路径"""
        return glob.glob(os.path.join(folder_path, '*.mid'))
    
    def load_and_preprocess(self, midi_file_path: Union[str, List[str]]) -> Tuple[pd.Series, List[Tuple]]:
        """增强版MIDI加载和预处理"""
        self.three_note_groups = []
        self.strict_groups = []
        self.non_strict_groups = []
        self.swave_curve = []
        self.holder_indices = []
        self.wavelet_energies = []
        
        if os.path.isdir(midi_file_path):
            midi_file_path = self.get_midi_files(midi_file_path)
        elif isinstance(midi_file_path, str):
            midi_file_path = [midi_file_path]
        
        all_pitches = []
        all_indices = []
        current_index_offset = 0
        
        for single_path in midi_file_path:
            try:
                midi_data = pretty_midi.PrettyMIDI(single_path)
            except Exception as e:
                self.logger.error(f"处理文件 {single_path} 出错: {e}")
                continue
            
            melody_track = self._extract_melody_track(midi_data)
            if melody_track is None:
                self.logger.error(f"文件 {single_path} 未找到有效的旋律轨道")
                continue
            
            pitches = [note.pitch for note in melody_track.notes]
            pitch_series_part = pd.Series(pitches, name='pitch').astype(float)
            
            diffs = pitch_series_part.diff().dropna()
            valid_mask = (diffs != 0)
            valid_indices = diffs[valid_mask].index.tolist()
            signed_diffs = diffs[valid_mask].tolist()
            
            self.logger.info(f"[{single_path}] 加载了 {len(pitch_series_part)} 个音符")
            self.logger.info(f"[{single_path}] 有效音程数: {len(signed_diffs)}")
            
            all_pitches.extend(pitch_series_part.tolist())
            for idx, diff in zip(valid_indices, signed_diffs):
                global_idx = current_index_offset + idx
                all_indices.append((global_idx, diff))
            current_index_offset += len(pitch_series_part)
        
        self.pitch_series = pd.Series(all_pitches, name='pitch').astype(float)
        self.indexed_diffs = all_indices
        return self.pitch_series, all_indices
    
    def _extract_melody_track(self, midi_data):
        """提取主旋律轨道"""
        melody_track = None
        for instrument in midi_data.instruments:
            if not instrument.is_drum and len(instrument.notes) > 0:
                melody_track = instrument
                break
        return melody_track
    
    def identify_three_note_groups(self) -> List[Dict]:
        """识别和分类三音组"""
        if self.pitch_series is None or len(self.pitch_series) < 3:
            self.logger.error("音高序列不足，无法分析三音组")
            return []
        
        self.three_note_groups = []
        self.strict_groups = []
        self.non_strict_groups = []
        
        groups = []
        pitches = self.pitch_series.values
        
        for i in range(len(pitches) - 2):
            note1, note2, note3 = pitches[i], pitches[i + 1], pitches[i + 2]
            
            interval1 = note2 - note1
            interval2 = note3 - note2
            
            if interval1 == 0 and interval2 == 0:
                continue
            
            group = {
                'index': i,
                'notes': [note1, note2, note3],
                'intervals': [interval1, interval2],
                'is_strict': self._is_strict_three_note_group(interval1, interval2),
                'dynamic_range': max(note1, note2, note3) - min(note1, note2, note3),
                'total_displacement': abs(note3 - note1),
                'direction_changes': 1 if interval1 * interval2 < 0 else 0,
                'complexity': abs(interval1) + abs(interval2)
            }
            
            groups.append(group)
            
            if group['is_strict']:
                self.strict_groups.append(group)
            else:
                self.non_strict_groups.append(group)
        
        self.three_note_groups = groups
        self.logger.info(f"识别出 {len(groups)} 个三音组，其中严格定义 {len(self.strict_groups)} 个")
        
        return groups
    
    def _is_strict_three_note_group(self, interval1: float, interval2: float) -> bool:
        """判断是否为严格定义的三音组（方向相反）"""
        return (interval1 != 0 and interval2 != 0 and
                ((interval1 > 0 and interval2 < 0) or (interval1 < 0 and interval2 > 0)))
    
    def analyze_per_work_smoothness(self, midi_file_path: Union[str, List[str]]) -> Dict:
        """按作品分别分析平滑度收敛"""
        print("\n🔧 修正研究方法：按作品分别分析平滑度收敛")
        print("=" * 60)
        
        if os.path.isdir(midi_file_path):
            midi_file_path = self.get_midi_files(midi_file_path)
        elif isinstance(midi_file_path, str):
            midi_file_path = [midi_file_path]
        
        per_work_results = []
        failed_files = []
        
        for i, single_path in enumerate(midi_file_path):
            print(f"\n📋 分析第 {i + 1}/{len(midi_file_path)} 首: {os.path.basename(single_path)}")
            
            try:
                self._reset_analyzer_state()
                pitch_series, indexed_diffs = self._load_single_file(single_path)
                
                if pitch_series is None or len(pitch_series) < 10:
                    print(f"   ❌ 文件无效或音符过少")
                    failed_files.append(single_path)
                    continue
                
                work_result = self._calculate_single_work_smoothness(single_path)
                work_result['file_path'] = single_path
                work_result['file_name'] = os.path.basename(single_path)
                
                per_work_results.append(work_result)
                print(f"   ✅ 分析完成")
                
                # 生成可视化图表
                if 'dual_method_analysis' in work_result and 'error' not in work_result['dual_method_analysis']:
                    dual_analysis = work_result['dual_method_analysis']
                    if 'family_a_results' in dual_analysis and 'family_b_results' in dual_analysis:
                        self.create_dual_method_visualization_for_work(
                            single_path,
                            dual_analysis['family_a_results'],
                            dual_analysis['family_b_results']
                        )
                
            except Exception as e:
                print(f"   ❌ 分析失败: {e}")
                failed_files.append(single_path)
                continue
        
        if per_work_results:
            summary = self._summarize_per_work_results(per_work_results)
            summary['failed_files'] = failed_files
            summary['total_files'] = len(midi_file_path)
            summary['successful_files'] = len(per_work_results)
            
            self.print_smoothness_summary(summary)
            
            print(f"\n📊 总体分析完成:")
            print(f"   成功: {len(per_work_results)} 首")
            print(f"   失败: {len(failed_files)} 首")
            
            return {
                'per_work_results': per_work_results,
                'failed_files': failed_files,
                'summary_statistics': summary
            }
        else:
            print(f"\n❌ 没有成功分析任何文件")
            return {'error': '没有成功分析任何文件', 'failed_files': failed_files}
    
    def _reset_analyzer_state(self):
        """重置分析器状态"""
        self.pitch_series = None
        self.indexed_diffs = []
        self.three_note_groups = []
        self.strict_groups = []
        self.non_strict_groups = []
    
    def _load_single_file(self, file_path: str) -> Tuple[pd.Series, List[Tuple]]:
        """加载单个文件"""
        return self.load_and_preprocess(file_path)
    
    def _calculate_single_work_smoothness(self, file_path: str) -> Dict:
        """计算单个作品的平滑度"""
        groups = self.identify_three_note_groups()
        
        if len(self.strict_groups) < 3:
            return {'error': '严格三音组数量不足'}
        
        intervallic_ambitus_result = self._calculate_intervallic_ambitus_single_work()
        local_volatility_result = self._calculate_local_volatility_single_work(self.pitch_series.values)
        
        # 使用双方法分析替代一致性比较
        dual_analysis = self._analyze_dual_methods(self.pitch_series.values)
        consistency = dual_analysis.get('orthogonality_assessment', 'unknown') == '高度正交'
        
        # 添加旋律动力系统分析
        dynamics_analysis = self._analyze_melody_dynamics(self.pitch_series.values)
        
        print(f"   ✅ 成功分析: {len(self.strict_groups)} 个严格三音组")
        print(f"      音程均幅 (Intervallic_Ambitus): {intervallic_ambitus_result['smoothness']:.4f}")
        print(f"      局部音程波动性 (Local_Volatility): {local_volatility_result['average_holder']:.4f}")
        print(f"      中等尺度波动性 (d2_rms): {local_volatility_result.get('medium_volatility_d2', 0.0):.4f}")
        print(f"      波动性特征比率: {local_volatility_result.get('volatility_character_ratio', 0.0):.4f}")
        print(f"      波动性类型: {local_volatility_result.get('volatility_type', '未知')}")
        print(f"      装饰音模式: {local_volatility_result.get('ornament_pattern', '未知')}")
        print(f"      双方法分析: {dual_analysis.get('orthogonality_assessment', 'unknown')}")
        print(f"      相关系数: {dual_analysis.get('correlation', 0.0):.4f}")
        
        # 显示动力系统分析结果
        if 'error' not in dynamics_analysis:
            print(f"      动力系统类型: {dynamics_analysis.get('system_type', '未知')}")
            print(f"      系统稳定性: {dynamics_analysis.get('stability', '未知')}")
            print(f"      吸引子数量: {dynamics_analysis.get('attractor_count', 0)}")
            print(f"      系统能量: {dynamics_analysis.get('system_energy', 0.0):.4f}")
            print(f"      平均曲率: {dynamics_analysis.get('mean_curvature', 0.0):.4f}")
            print(f"      李雅普诺夫代理: {dynamics_analysis.get('lyapunov_proxy', 0.0):.4f}")
        else:
            print(f"      动力系统分析: 失败 - {dynamics_analysis.get('error', '未知错误')}")
        
        return {
            'file_path': file_path,
            'total_groups': len(groups),
            'strict_groups': len(self.strict_groups),
            'intervallic_ambitus': intervallic_ambitus_result['smoothness'],
            'local_volatility': local_volatility_result['average_holder'],
            'consistency': consistency,
            'intervallic_ambitus_details': intervallic_ambitus_result,
            'local_volatility_details': local_volatility_result,
            'dual_method_analysis': dual_analysis,
            'melody_dynamics_analysis': dynamics_analysis
        }
    
    def _calculate_intervallic_ambitus_single_work(self) -> Dict:
        """方法1: 音程均幅 计算"""
        interval_logs = []
        
        for group in self.strict_groups:
            interval1, interval2 = group['intervals']
            if abs(interval1) > 0:
                interval_logs.append(np.log(abs(interval1)))
            if abs(interval2) > 0:
                interval_logs.append(np.log(abs(interval2)))
        
        smoothness = np.mean(interval_logs) if interval_logs else 0.0
        
        return {
            'smoothness': smoothness,
            'converged': True,
            'total_intervals': len(interval_logs),
            'interval_range': f"{np.min(interval_logs):.3f}~{np.max(interval_logs):.3f}" if interval_logs else "0.000~0.000",
            'interval_std': np.std(interval_logs) if len(interval_logs) > 1 else 0.0
        }
    
    def _calculate_local_volatility_single_work(self, pitch_series):
        """方法2: 基于绝对不规则性绝对不规则性分析"""
        try:
            # 获取局部波动性作为主要的方法2得分
            局部波动性 = self.wavelet_method.get_method2_score(pitch_series)
            smoothness = self.wavelet_method.analyze_single_work_smoothness(pitch_series)
            
            # 获取详细特征
            features = self.wavelet_method.analyze_single_work_features(pitch_series)
            
            return {
                'average_holder': 局部波动性,  # 现在返回局部波动性而不是平滑度
                'smoothness': smoothness,  # 额外提供平滑度
                'converged': True,
                'total_groups': len(pitch_series) - 2 if len(pitch_series) > 2 else 0,
                'holder_range': f"{局部波动性:.3f}~{局部波动性:.3f}",
                'holder_std': 0.0,
                '局部波动性': 局部波动性,
                'd2_rms': features.get('d2_rms', 0.0) if 'error' not in features else 0.0,
                'rms_ratio': features.get('rms_ratio', 0.0) if 'error' not in features else 0.0
            }
        except Exception as e:
            print(f"绝对不规则性绝对不规则性分析出错: {e}")
            return {
                'average_holder': 0.0,
                'smoothness': 0.0,
                'converged': False,
                'total_groups': 0,
                'holder_range': "0.000~0.000",
                'holder_std': 0.0,
                '局部波动性': 0.0,
                'd2_rms': 0.0,
                'rms_ratio': 0.0
            }
    
    def _summarize_per_work_results(self, per_work_results: List[Dict]) -> Dict:
        """汇总所有作品的结果"""
        method1_values = [r['intervallic_ambitus'] for r in per_work_results]
        method2_values = [r['local_volatility'] for r in per_work_results]
        consistency_count = sum(1 for r in per_work_results if r.get('dual_method_analysis', {}).get('orthogonality_assessment') == '高度正交')
        
        return {
            'total_works': len(per_work_results),
            'consistency_percentage': (consistency_count / len(per_work_results)) * 100,
            'method1_stats': {
                'mean': np.mean(method1_values),
                'std': np.std(method1_values),
                'min': np.min(method1_values),
                'max': np.max(method1_values),
                'median': np.median(method1_values)
            },
            'method2_stats': {
                'mean': np.mean(method2_values),
                'std': np.std(method2_values),
                'min': np.min(method2_values),
                'max': np.max(method2_values),
                'median': np.median(method2_values)
            }
        }
    

    
    def _analyze_melody_dynamics(self, pitch_series):
        """分析旋律动力系统特征"""
        try:
            dynamics_result = self.melody_dynamics_analyzer.analyze_dynamics(pitch_series)
            
            if 'error' in dynamics_result:
                self.logger.error(f"动力系统分析失败: {dynamics_result['error']}")
                return {'error': dynamics_result['error']}
            
            # 提取关键动力学特征
            dynamics_summary = {
                'system_type': dynamics_result['system_type'],
                'stability': dynamics_result['stability'],
                'attractor_count': dynamics_result['attractors']['count'],
                'attractor_strength': dynamics_result['attractors']['strength'],
                'mean_curvature': dynamics_result['dynamics_metrics']['mean_curvature'],
                'system_energy': dynamics_result['dynamics_metrics']['system_energy'],
                'lyapunov_proxy': dynamics_result['dynamics_metrics']['lyapunov_proxy'],
                'phase_space_volume': dynamics_result['dynamics_metrics']['phase_space_volume'],
                'mean_velocity': dynamics_result['dynamics_metrics']['mean_velocity'],
                'mean_acceleration': dynamics_result['dynamics_metrics']['mean_acceleration'],
                
                # 多尺度特征摘要
                'micro_scale_complexity': self._summarize_scale_features(
                    dynamics_result['time_scales']['micro_scale']
                ),
                'meso_scale_complexity': self._summarize_scale_features(
                    dynamics_result['time_scales']['meso_scale']
                ),
                'macro_scale_complexity': self._summarize_scale_features(
                    dynamics_result['time_scales']['macro_scale']
                ),
                
                # 完整结果（用于详细分析）
                'full_dynamics_result': dynamics_result
            }
            
            return dynamics_summary
            
        except Exception as e:
            self.logger.error(f"动力系统分析异常: {e}")
            return {'error': str(e)}
    
    def _summarize_scale_features(self, scale_features):
        """汇总特定尺度的特征"""
        if not scale_features:
            return {
                'avg_zero_crossing_rate': 0.0,
                'avg_envelope_depth': 0.0,
                'avg_perceived_variation': 0.0,
                'avg_local_complexity': 0.0
            }
        
        return {
            'avg_zero_crossing_rate': np.mean([f['zero_crossing_rate'] for f in scale_features]),
            'avg_envelope_depth': np.mean([f['envelope_depth'] for f in scale_features]),
            'avg_perceived_variation': np.mean([f['perceived_variation'] for f in scale_features]),
            'avg_local_complexity': np.mean([f['local_complexity'] for f in scale_features])
        }

    def _analyze_dual_methods(self, pitch_series):
        """分析双方法家族"""
        try:
            family_a_results, family_b_results = self.dual_method_analyzer.analyze_melody_dual_methods(pitch_series)
            orthogonality = self.dual_method_analyzer.compute_orthogonality_analysis(family_a_results, family_b_results)
            
            return {
                'family_a_results': family_a_results,
                'family_b_results': family_b_results,
                'orthogonality_assessment': orthogonality.get('orthogonality_assessment', 'unknown'),
                'correlation': orthogonality.get('correlation', 0.0),
                'a_rms_mean': orthogonality.get('a_rms_stats', {}).get('mean', 0.0),
                'b_complexity_mean': orthogonality.get('b_complexity_stats', {}).get('mean', 0.0),
                'direction_changes': orthogonality.get('direction_changes', 0),
                'total_groups': orthogonality.get('total_groups', 0)
            }
        except Exception as e:
            self.logger.error(f"双方法分析失败: {e}")
            return {'error': str(e)}
    

    def generate_test_data(self):
        """生成测试数据（当没有可用的音乐文件时）"""
        print("🧪 生成测试数据进行演示")
        
        # 生成几种不同类型的测试旋律
        test_melodies = [
            {
                'name': '平滑音阶测试',
                'pitches': [60, 62, 64, 65, 67, 69, 71, 72, 74, 76, 77, 79, 81, 83, 84]
            },
            {
                'name': '跳跃旋律测试', 
                'pitches': [60, 72, 48, 75, 45, 78, 42, 81, 39, 84, 36, 87, 33, 90, 30]
            },
            {
                'name': '三音组模式测试',
                'pitches': [60, 62, 61, 63, 62, 64, 63, 65, 64, 66, 65, 67, 66, 68, 67]
            },
            {
                'name': '吸引子主导测试',
                'pitches': [60, 61, 60, 62, 60, 61, 60, 63, 60, 61, 60, 62, 60, 64, 60]
            },
            {
                'name': '混沌模式测试',
                'pitches': [60, 67, 55, 72, 48, 69, 52, 74, 46, 71, 49, 76, 44, 73, 51]
            }
        ]
        
        results = []
        for melody in test_melodies:
            print(f"\n🎵 分析测试旋律: {melody['name']}")
            self.pitch_series = pd.Series(melody['pitches'])
            
            # 执行分析
            result = self._analyze_single_work(f"test_{melody['name']}.mid")
            if result:
                results.append(result)
        
        return results

    def create_dual_method_visualization_for_work(self, file_path, family_a_results, family_b_results):
        """为单个作品创建双方法可视化"""
        if not VISUALIZATION_AVAILABLE:
            return
        
        try:
            # 生成保存路径
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            save_path = f"dual_method_analysis_{base_name}.png"
            
            # 创建可视化
            self.dual_method_analyzer.create_dual_method_visualization(
                self.pitch_series.values.tolist(),
                family_a_results,
                family_b_results,
                title=f"双方法家族分析 - {base_name}",
                save_path=save_path
            )
            
            print(f"📊 已生成可视化图表: {save_path}")
            
        except Exception as e:
            self.logger.error(f"可视化生成失败: {e}")

    def print_smoothness_summary(self, results: Dict):
        """打印平滑度分析摘要"""
        print("\n" + "=" * 60)
        print("🎼 平滑度收敛分析摘要报告")
        print("=" * 60)
        print(f"📊 基础统计:")
        print(f"   分析作品数: {results['total_works']} 首")
        print(f"   双方法正交性: {results['consistency_percentage']:.1f}%")
        
        print(f"\n📈 音程均幅 (Intervallic_Ambitus) 统计:")
        m1 = results['method1_stats']
        print(f"   均值: {m1['mean']:.4f}")
        print(f"   标准差: {m1['std']:.4f}")
        print(f"   范围: {m1['min']:.4f} ~ {m1['max']:.4f}")
        print(f"   中位数: {m1['median']:.4f}")
        
        print(f"\n📈 局部音程波动性 (Local_Volatility) 统计:")
        m2 = results['method2_stats']
        print(f"   均值: {m2['mean']:.4f}")
        print(f"   标准差: {m2['std']:.4f}")
        print(f"   范围: {m2['min']:.4f} ~ {m2['max']:.4f}")
        print(f"   中位数: {m2['median']:.4f}")
        
        print(f"\n✅ 核心结论:")
        if results['consistency_percentage'] > 70:
            print(f"   ✅ 两个方法家族高度正交")
        elif results['consistency_percentage'] > 40:
            print(f"   ⚠️ 两个方法家族中度相关")
        else:
            print(f"   ⚠️ 平滑度收敛特征需要进一步验证")
            print(f"   ⚠️ 两个方法家族强相关，需要进一步分析")

def main():
    """主函数"""
    print("🎼 第二个数学特征验证：平滑度收敛")
    print("=" * 80)
    
    analyzer = EnhancedChineseMusicAnalyzer()
    
    # 分析MIDI文件
    results = analyzer.analyze_per_work_smoothness('/Users/<USER>/Desktop/AI音乐/midi_files')
    
    print(f"\n🎼 第二个数学特征验证完成！")
    print(f"下一步：进行第三个数学特征（熵最大化）的验证")

if __name__ == "__main__":
    main()
