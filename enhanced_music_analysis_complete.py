#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版中国音乐分析系统 - 完整版
包含：音程均幅、局部波动性、双方法分析、旋律动力系统
重构核心逻辑框架 - 按照新的程序逻辑框架要求构建
"""

import numpy as np
import pandas as pd
import math
import os
import glob
from typing import List, Dict, Any, Tuple

# 尝试导入可选库
try:
    import pretty_midi
    PRETTY_MIDI_AVAILABLE = True
    print("✅ pretty_midi库加载成功")
except ImportError:
    PRETTY_MIDI_AVAILABLE = False
    print("⚠️ pretty_midi库未安装，将跳过MIDI文件处理")

try:
    import matplotlib
    matplotlib.use('Agg')  # 使用非交互式后端
    import matplotlib.pyplot as plt
    VISUALIZATION_AVAILABLE = True
    print("✅ 可视化库加载成功")
except ImportError:
    VISUALIZATION_AVAILABLE = False
    print("⚠️ 可视化库未安装，将跳过图表生成")


class SimpleMidiParser:
    """简化的MIDI解析器，不依赖pretty_midi"""
    
    @staticmethod
    def extract_pitch_from_midi(file_path):
        """从MIDI文件提取音高序列（简化版本）"""
        try:
            with open(file_path, 'rb') as f:
                data = f.read()
            
            # 简化的MIDI解析 - 查找Note On事件
            pitches = []
            i = 0
            while i < len(data) - 3:
                # 查找Note On事件 (0x90-0x9F)
                if data[i] >= 0x90 and data[i] <= 0x9F:
                    if i + 2 < len(data):
                        pitch = data[i + 1]
                        velocity = data[i + 2]
                        if velocity > 0 and 21 <= pitch <= 108:  # 有效的MIDI音高范围
                            pitches.append(pitch)
                i += 1
            
            # 如果没有找到音符，尝试其他方法
            if not pitches:
                # 查找所有可能的音高值
                for byte in data:
                    if 21 <= byte <= 108:  # MIDI音高范围
                        pitches.append(byte)
                
                # 去重并排序（保持原始顺序的去重）
                seen = set()
                unique_pitches = []
                for pitch in pitches:
                    if pitch not in seen:
                        seen.add(pitch)
                        unique_pitches.append(pitch)
                pitches = unique_pitches[:50]  # 限制数量
            
            return pitches[:100] if pitches else []  # 限制最大数量
            
        except Exception as e:
            print(f"     MIDI解析错误: {e}")
            return []


class IntervalicAmbitusAnalyzer:
    """音程均幅 (Intervallic_Ambitus) 分析器"""

    @staticmethod
    def calculate(pitch_series: List[float]) -> float:
        """计算音程均幅 E(log i)"""
        if len(pitch_series) < 2:
            return 0.0

        intervals = [abs(pitch_series[i+1] - pitch_series[i]) for i in range(len(pitch_series)-1)]
        log_intervals = [math.log(max(interval, 1)) for interval in intervals]
        return sum(log_intervals) / len(log_intervals) if log_intervals else 0.0

class LocalVolatilityAnalyzer:
    """局部音程波动性 (Local_Volatility) 分析器"""

    @staticmethod
    def calculate_d1_rms(pitch_series: List[float]) -> float:
        """计算d1_rms（主要的局部波动性指标）"""
        if len(pitch_series) < 2:
            return 0.0

        intervals = [pitch_series[i+1] - pitch_series[i] for i in range(len(pitch_series)-1)]
        return math.sqrt(sum(interval**2 for interval in intervals) / len(intervals)) if intervals else 0.0

    @staticmethod
    def calculate_d2_rms(pitch_series: List[float]) -> float:
        """计算d2_rms（中等尺度波动性，音程曲率）"""
        if len(pitch_series) < 3:
            return 0.0

        # 计算二阶差分（加速度）
        first_diff = [pitch_series[i+1] - pitch_series[i] for i in range(len(pitch_series)-1)]
        second_diff = [first_diff[i+1] - first_diff[i] for i in range(len(first_diff)-1)]

        return math.sqrt(sum(diff**2 for diff in second_diff) / len(second_diff)) if second_diff else 0.0

    @staticmethod
    def calculate_rms_ratio(d1_rms: float, d2_rms: float) -> float:
        """计算RMS比率（波动性特征比率）"""
        if d2_rms == 0:
            return float('inf') if d1_rms > 0 else 0.0
        return d1_rms / d2_rms

    @staticmethod
    def classify_volatility_type(rms_ratio: float) -> str:
        """分类波动性类型"""
        if rms_ratio > 3.0:
            return "尖锐毛刺型"  # 高比率：快速尖锐变化
        elif rms_ratio > 1.5:
            return "混合波动型"  # 中等比率：混合特征
        elif rms_ratio > 0.5:
            return "平滑波浪型"  # 低比率：平滑波浪状
        else:
            return "极平滑型"    # 极低比率：几乎无波动

    @staticmethod
    def identify_ornament_pattern(d1_rms: float, d2_rms: float, rms_ratio: float) -> str:
        """识别装饰音模式"""
        if d1_rms > 5.0 and rms_ratio > 2.0:
            return "颤音/震音型"
        elif d1_rms > 3.0 and rms_ratio > 1.5:
            return "回音/波音型"
        elif d1_rms > 1.0 and rms_ratio < 1.0:
            return "滑音/连音型"
        else:
            return "简单进行型"

class MelodyDynamicsSystem:
    """
    旋律动力系统分析器
    基于动力系统理论分析旋律的：
    - 导数系统（速度、加速度）
    - 吸引子识别（隐形引力线）
    - 曲率张量计算
    - 多尺度时间分析
    - 系统稳定性评估
    """

    def __init__(self, time_window=0.2, attractor_threshold=3.0):
        self.time_window = time_window
        self.attractor_threshold = attractor_threshold

    def analyze_dynamics(self, pitch_series: List[float]) -> Dict[str, Any]:
        """分析旋律动力系统特征"""
        if len(pitch_series) < 3:
            return {'error': 'insufficient_data_for_dynamics_analysis'}

        try:
            pitch_array = np.array(pitch_series)

            # 1. 计算导数系统
            velocity, acceleration = self._compute_derivatives(pitch_array)

            # 2. 识别吸引子
            attractors = self._detect_attractors(pitch_array)

            # 3. 计算曲率张量
            curvature = self._calculate_curvature(velocity, acceleration)

            # 4. 分析多尺度特征
            time_scales = self._multi_scale_analysis(pitch_array)

            # 5. 评估系统稳定性
            stability = self._assess_stability(attractors, curvature)

            # 6. 计算综合动力学指标
            dynamics_metrics = self._calculate_dynamics_metrics(
                velocity, acceleration, curvature, attractors
            )

            return {
                'velocity': velocity.tolist(),
                'acceleration': acceleration.tolist(),
                'attractors': attractors,
                'curvature': curvature.tolist(),
                'time_scales': time_scales,
                'stability': stability,
                'dynamics_metrics': dynamics_metrics,
                'system_type': self._classify_system_type(dynamics_metrics)
            }

        except Exception as e:
            return {'error': f'dynamics_analysis_failed: {e}'}

    def _compute_derivatives(self, pitch_series: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """计算音高导数（速度、加速度）"""
        n = len(pitch_series)
        window_size = max(1, int(n * self.time_window))

        # 一阶导数（速度）
        velocity = np.zeros(n)
        for i in range(n):
            start = max(0, i - window_size//2)
            end = min(n, i + window_size//2 + 1)
            if end - start > 1:
                velocity[i] = np.mean(np.diff(pitch_series[start:end]))
            else:
                velocity[i] = 0.0

        # 二阶导数（加速度）
        acceleration = np.gradient(velocity)

        return velocity, acceleration

    def _detect_attractors(self, pitch_series: np.ndarray) -> Dict[str, Any]:
        """识别吸引子（隐形引力线）- 基于动态拓扑的自回归模型"""
        try:
            # 使用自回归模型估计吸引子动力学
            attractor_dynamics = self.estimate_attractor_dynamics(pitch_series)
            
            if attractor_dynamics is None:
                return {
                    'positions': [],
                    'distances': [0.0] * len(pitch_series),
                    'strength': 0.0,
                    'count': 0,
                    'avg_distance': 0.0,
                    'error': 'insufficient_data_for_attractor_dynamics'
                }
            
            p0 = attractor_dynamics['global_attractor_position']
            lambda_strength = attractor_dynamics['global_attractor_strength']
            residuals = attractor_dynamics['residuals']
            
            # 计算每个音高到吸引子的距离
            distances = [abs(pitch - p0) for pitch in pitch_series]
            avg_distance = np.mean(distances)
            
            # 计算引力场强度
            gravitational_forces = [lambda_strength * (p0 - pitch) for pitch in pitch_series]
            
            return {
                'positions': [p0],  # 全局吸引子位置
                'distances': distances,
                'strength': abs(lambda_strength),  # 吸引子强度（回归系数的绝对值）
                'count': 1,  # 单一全局吸引子
                'avg_distance': avg_distance,
                'lambda_strength': lambda_strength,  # 回归强度
                'residuals': residuals,  # 回归残差
                'gravitational_forces': gravitational_forces,  # 引力场
                'regression_quality': attractor_dynamics['regression_quality']
            }

        except Exception as e:
            return {
                'positions': [],
                'distances': [0.0] * len(pitch_series),
                'strength': 0.0,
                'count': 0,
                'avg_distance': 0.0,
                'error': str(e)
            }
    
    def estimate_attractor_dynamics(self, pitch_series):
        """使用自回归模型估计吸引子动力学"""
        if len(pitch_series) < 3:
            return None
        
        # 假设全局吸引子位置p0为音高中位数
        p0 = np.median(pitch_series)
        
        # 准备回归数据：dp(t) = lambda * (p0 - p(t-1)) + epsilon
        dp = []  # 因变量：dp(t) = p(t) - p(t-1)
        X = []   # 自变量：p0 - p(t-1)
        
        for i in range(1, len(pitch_series)):
            dp.append(pitch_series[i] - pitch_series[i-1])
            X.append(p0 - pitch_series[i-1])
        
        # 线性回归
        X = np.array(X).reshape(-1, 1)
        dp = np.array(dp)
        
        try:
            # 尝试使用sklearn
            from sklearn.linear_model import LinearRegression
            model = LinearRegression()
            model.fit(X, dp)
            lambda_ = model.coef_[0]  # 回归强度
            
            # 计算每个时间点的残差（从第二个音符开始）
            residuals = dp - model.predict(X)
            
            # 计算回归质量（R²）
            ss_res = np.sum(residuals ** 2)
            ss_tot = np.sum((dp - np.mean(dp)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0.0
            
        except ImportError:
            # 如果sklearn不可用，使用简化的最小二乘法
            X_flat = X.flatten()
            if np.var(X_flat) > 1e-10:
                lambda_ = np.cov(X_flat, dp)[0, 1] / np.var(X_flat)
            else:
                lambda_ = 0.0
            
            predicted_dp = lambda_ * X_flat
            residuals = dp - predicted_dp
            
            # 简化的R²计算
            ss_res = np.sum(residuals ** 2)
            ss_tot = np.sum((dp - np.mean(dp)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0.0
        
        return {
            'global_attractor_position': p0,
            'global_attractor_strength': lambda_,
            'residuals': residuals,  # 长度=len(pitch_series)-1
            'regression_quality': r_squared
        }
    def _calculate_curvature(self, velocity: np.ndarray, acceleration: np.ndarray) -> np.ndarray:
        """计算曲率张量"""
        curvature = np.zeros(len(velocity))

        for i in range(len(velocity)):
            v = velocity[i]
            a = acceleration[i]

            if abs(v) > 1e-5:
                curvature[i] = abs(a) / (abs(v)**2)
            else:
                curvature[i] = 0.0

        return curvature

    def _multi_scale_analysis(self, pitch_series: np.ndarray) -> Dict[str, Dict]:
        """多尺度时间分析"""
        scales = {
            'micro_scale': self._analyze_scale(pitch_series, window=0.05),
            'meso_scale': self._analyze_scale(pitch_series, window=0.3),
            'macro_scale': self._analyze_scale(pitch_series, window=0.7)
        }
        return scales

    def _analyze_scale(self, pitch_series: np.ndarray, window: float) -> Dict:
        """特定时间尺度的分析"""
        n = len(pitch_series)
        window_size = max(1, int(n * window))

        features = []
        for i in range(n):
            start = max(0, i - window_size//2)
            end = min(n, i + window_size//2 + 1)
            segment = pitch_series[start:end]

            if len(segment) > 1:
                diff_segment = np.diff(segment)
                zcr = np.sum(np.diff(np.sign(diff_segment)) != 0) / len(diff_segment) if len(diff_segment) > 1 else 0.0
                envelope = np.max(segment) - np.min(segment)
                mean_val = np.mean(segment)
                dynamic_range = envelope / (abs(mean_val) + 1e-5)
                perceived_variation = zcr * envelope
                local_complexity = np.std(diff_segment) if len(diff_segment) > 1 else 0.0

                features.append({
                    'position': i,
                    'zero_crossing_rate': float(zcr),
                    'envelope_depth': float(envelope),
                    'dynamic_range': float(dynamic_range),
                    'perceived_variation': float(perceived_variation),
                    'local_complexity': float(local_complexity)
                })

        # 返回汇总统计
        if features:
            return {
                'avg_zero_crossing_rate': np.mean([f['zero_crossing_rate'] for f in features]),
                'avg_envelope_depth': np.mean([f['envelope_depth'] for f in features]),
                'avg_perceived_variation': np.mean([f['perceived_variation'] for f in features]),
                'avg_local_complexity': np.mean([f['local_complexity'] for f in features])
            }
        else:
            return {
                'avg_zero_crossing_rate': 0.0,
                'avg_envelope_depth': 0.0,
                'avg_perceived_variation': 0.0,
                'avg_local_complexity': 0.0
            }

    def _assess_stability(self, attractors: Dict, curvature: np.ndarray) -> str:
        """评估系统稳定性"""
        avg_distance = attractors.get('avg_distance', 0.0)
        avg_curvature = np.mean(curvature)
        curvature_variance = np.var(curvature)
        attractor_count = attractors.get('count', 0)

        if curvature_variance > 0.5 and avg_curvature > 0.1:
            return "高度不稳定（脉冲式）"
        elif avg_distance < 2.0 and curvature_variance < 0.1 and attractor_count > 0:
            return "高度稳定（吸引子主导）"
        elif avg_curvature < 0.05:
            return "稳定（低曲率）"
        elif attractor_count == 0:
            return "无吸引子（自由运动）"
        else:
            return "中等稳定"

    def _calculate_dynamics_metrics(self, velocity: np.ndarray, acceleration: np.ndarray,
                                  curvature: np.ndarray, attractors: Dict) -> Dict[str, float]:
        """计算综合动力学指标"""
        return {
            'mean_velocity': float(np.mean(np.abs(velocity))),
            'velocity_variance': float(np.var(velocity)),
            'max_velocity': float(np.max(np.abs(velocity))),
            'mean_acceleration': float(np.mean(np.abs(acceleration))),
            'acceleration_variance': float(np.var(acceleration)),
            'max_acceleration': float(np.max(np.abs(acceleration))),
            'mean_curvature': float(np.mean(curvature)),
            'curvature_variance': float(np.var(curvature)),
            'max_curvature': float(np.max(curvature)),
            'attractor_count': attractors.get('count', 0),
            'attractor_strength': attractors.get('strength', 0.0),
            'mean_attractor_distance': attractors.get('avg_distance', 0.0),
            'system_energy': float(np.mean(velocity**2 + acceleration**2)),
            'phase_space_volume': float(np.std(velocity) * np.std(acceleration)),
            'lyapunov_proxy': float(np.mean(np.abs(np.diff(curvature))))
        }

    def _classify_system_type(self, metrics: Dict[str, float]) -> str:
        """分类动力系统类型"""
        energy = metrics['system_energy']
        lyapunov = metrics['lyapunov_proxy']
        attractor_count = metrics['attractor_count']

        if lyapunov > 0.1 and energy > 10.0:
            return "混沌系统"
        elif attractor_count > 2 and metrics['attractor_strength'] > 0.3:
            return "多吸引子系统"
        elif attractor_count == 1 and metrics['mean_attractor_distance'] < 2.0:
            return "单吸引子系统"
        elif energy < 1.0 and lyapunov < 0.01:
            return "准静态系统"
        else:
            return "复杂动力系统"



class DualMethodAnalyzer:
    """双方法分析器"""
    
    @staticmethod
    def calculate_orthogonality(method1_values, method2_values):
        """计算正交性"""
        if len(method1_values) != len(method2_values) or len(method1_values) < 2:
            return {'correlation': 0.0, 'assessment': '数据不足'}
        
        # 简化的相关性计算
        mean1 = sum(method1_values) / len(method1_values)
        mean2 = sum(method2_values) / len(method2_values)
        
        numerator = sum((v1 - mean1) * (v2 - mean2) for v1, v2 in zip(method1_values, method2_values))
        
        sum_sq1 = sum((v1 - mean1)**2 for v1 in method1_values)
        sum_sq2 = sum((v2 - mean2)**2 for v2 in method2_values)
        
        if sum_sq1 == 0 or sum_sq2 == 0:
            correlation = 0.0
        else:
            correlation = numerator / math.sqrt(sum_sq1 * sum_sq2)
        
        # 正交性评估
        if abs(correlation) < 0.3:
            assessment = "高度正交"
        elif abs(correlation) < 0.6:
            assessment = "中度相关"
        else:
            assessment = "强相关"
        
        return {
            'correlation': correlation,
            'assessment': assessment
        }


class EnhancedChineseMusicAnalyzer:
    """增强版中国音乐分析器"""
    
    def __init__(self):
        self.intervallic_ambitus_analyzer = IntervalicAmbitusAnalyzer()
        self.local_volatility_analyzer = LocalVolatilityAnalyzer()
        self.melody_dynamics_analyzer = MelodyDynamicsSystem()
        self.dual_method_analyzer = DualMethodAnalyzer()
        self.pitch_series = None
        
    def load_midi_file(self, file_path):
        """加载MIDI文件"""
        try:
            if PRETTY_MIDI_AVAILABLE:
                # 使用pretty_midi（如果可用）
                midi_data = pretty_midi.PrettyMIDI(file_path)
                pitch_series = []
                for instrument in midi_data.instruments:
                    if not instrument.is_drum:
                        for note in instrument.notes:
                            pitch_series.append(note.pitch)
            else:
                # 使用简化的MIDI解析器
                pitch_series = SimpleMidiParser.extract_pitch_from_midi(file_path)
            
            if pitch_series:
                self.pitch_series = pd.Series(pitch_series)
                print(f"   从MIDI文件提取了 {len(pitch_series)} 个音符")
                return True
            else:
                print(f"   ⚠️ MIDI文件中未找到音符数据")
                return False
                
        except Exception as e:
            print(f"   ❌ MIDI文件处理失败: {e}")
            return False
    
    def load_csv_file(self, file_path):
        """加载CSV文件"""
        try:
            df = pd.read_csv(file_path)
            
            # 尝试不同的列名
            pitch_columns = ['pitch', 'Pitch', 'PITCH', 'note', 'Note', 'midi_note']
            pitch_column = None
            
            for col in pitch_columns:
                if col in df.columns:
                    pitch_column = col
                    break
            
            if pitch_column:
                self.pitch_series = df[pitch_column].dropna()
                print(f"   从CSV文件提取了 {len(self.pitch_series)} 个音符")
                return True
            else:
                print(f"   ⚠️ CSV文件中未找到音高列")
                return False
                
        except Exception as e:
            print(f"   ❌ CSV文件处理失败: {e}")
            return False
    
    def analyze_single_work(self, file_path):
        """分析单个作品"""
        print(f"🎵 分析文件: {os.path.basename(file_path)}")
        
        # 如果是测试数据，直接使用已设置的pitch_series
        if file_path.startswith('test_') and self.pitch_series is not None:
            pitch_list = self.pitch_series.tolist()
        else:
            # 加载文件
            success = False
            if file_path.endswith('.mid') or file_path.endswith('.midi'):
                success = self.load_midi_file(file_path)
            elif file_path.endswith('.csv'):
                success = self.load_csv_file(file_path)
            
            # 安全检查数据
            if not success:
                print(f"   ❌ 文件加载失败")
                return None

            if self.pitch_series is None:
                print(f"   ❌ 未找到音高数据")
                return None

            try:
                if len(self.pitch_series) < 3:
                    print(f"   ❌ 数据不足（需要至少3个音符）")
                    return None
            except Exception as e:
                print(f"   ❌ 数据检查失败: {e}")
                return None
            
            pitch_list = self.pitch_series.tolist()
        
        # 1. 音程均幅分析
        intervallic_ambitus = self.intervallic_ambitus_analyzer.calculate(pitch_list)
        
        # 2. 局部波动性分析
        d1_rms = self.local_volatility_analyzer.calculate_d1_rms(pitch_list)
        d2_rms = self.local_volatility_analyzer.calculate_d2_rms(pitch_list)
        rms_ratio = self.local_volatility_analyzer.calculate_rms_ratio(d1_rms, d2_rms)
        volatility_type = self.local_volatility_analyzer.classify_volatility_type(rms_ratio)
        ornament_pattern = self.local_volatility_analyzer.identify_ornament_pattern(d1_rms, d2_rms, rms_ratio)
        
        # 3. 旋律动力系统分析
        dynamics_result = self.melody_dynamics_analyzer.analyze_dynamics(pitch_list)
        
        # 打印分析结果
        print(f"      音程均幅 (Intervallic_Ambitus): {intervallic_ambitus:.4f}")
        print(f"      局部音程波动性 (Local_Volatility): {d1_rms:.4f}")
        print(f"      中等尺度波动性 (d2_rms): {d2_rms:.4f}")
        print(f"      波动性特征比率: {rms_ratio:.4f}")
        print(f"      波动性类型: {volatility_type}")
        print(f"      装饰音模式: {ornament_pattern}")
        
        if 'error' not in dynamics_result:
            metrics = dynamics_result['dynamics_metrics']
            print(f"      动力系统类型: {dynamics_result['system_type']}")
            print(f"      系统稳定性: {dynamics_result['stability']}")
            print(f"      吸引子数量: {metrics['attractor_count']}")
            print(f"      系统能量: {metrics['system_energy']:.4f}")
            print(f"      平均曲率: {metrics['mean_curvature']:.4f}")
            print(f"      李雅普诺夫代理: {metrics['lyapunov_proxy']:.4f}")
        else:
            print(f"      动力系统分析: 失败 - {dynamics_result['error']}")
        
        print(f"   ✅ 分析完成")
        
        return {
            'file_path': file_path,
            'note_count': len(pitch_list),
            'intervallic_ambitus': intervallic_ambitus,
            'local_volatility': d1_rms,
            'local_volatility_details': {
                'd1_rms': d1_rms,
                'd2_rms': d2_rms,
                'rms_ratio': rms_ratio,
                'volatility_type': volatility_type,
                'ornament_pattern': ornament_pattern
            },
            'dynamics_analysis': dynamics_result
        }
    
    def analyze_all_works(self):
        """分析所有作品"""
        print("🎼 增强版中国音乐分析系统")
        print("=" * 80)
        
        # 查找音乐文件 - 支持多个目录
        music_files = []
        search_directories = [
            '.',  # 当前目录
            './midi_files',  # midi_files文件夹
            './music',  # music文件夹
            './data',  # data文件夹
            './songs',  # songs文件夹
        ]
        
        search_patterns = ['*.mid', '*.midi', '*.csv']
        
        print("🔍 搜索音乐文件...")
        for directory in search_directories:
            if os.path.exists(directory):
                print(f"   检查目录: {directory}")
                for pattern in search_patterns:
                    search_path = os.path.join(directory, pattern)
                    files = glob.glob(search_path)
                    if files:
                        print(f"     找到 {len(files)} 个 {pattern} 文件")
                        music_files.extend(files)
        
        # 过滤出MIDI文件
        midi_files = [f for f in music_files if f.endswith(('.mid', '.midi'))]
        
        if not midi_files:
            print("\n❌ 未找到MIDI文件")
            print("💡 请将MIDI文件放在以下任一目录中:")
            print("   - 当前目录 (/Users/<USER>/Desktop/AI音乐/)")
            print("   - ./midi_files/ 文件夹")
            print("   - ./music/ 文件夹")
            print("   - ./data/ 文件夹")
            print("   - ./songs/ 文件夹")
            print("\n🧪 现在使用测试数据进行演示:")
            self.run_test_analysis()
            return
        
        print(f"📁 找到 {len(music_files)} 个音乐文件")
        
        # 分析每个文件
        per_work_results = []
        for file_path in music_files:
            result = self.analyze_single_work(file_path)
            if result:
                per_work_results.append(result)
        
        if per_work_results:
            print(f"\n📊 成功分析了 {len(per_work_results)} 个作品")
            self.print_summary_statistics(per_work_results)
        else:
            print("❌ 没有成功分析的作品")
    
    def run_test_analysis(self):
        """运行测试分析"""
        print("�� 运行测试分析")
        
        test_melodies = [
            {
                'name': '平滑音阶',
                'pitches': [60, 62, 64, 65, 67, 69, 71, 72, 74, 76, 77, 79, 81, 83, 84]
            },
            {
                'name': '跳跃旋律',
                'pitches': [60, 72, 48, 75, 45, 78, 42, 81, 39, 84, 36, 87, 33, 90, 30]
            },
            {
                'name': '三音组模式',
                'pitches': [60, 62, 61, 63, 62, 64, 63, 65, 64, 66, 65, 67, 66, 68, 67]
            },
            {
                'name': '吸引子主导',
                'pitches': [60, 61, 60, 62, 60, 61, 60, 63, 60, 61, 60, 62, 60, 64, 60]
            },
            {
                'name': '混沌模式',
                'pitches': [60, 67, 55, 72, 48, 69, 52, 74, 46, 71, 49, 76, 44, 73, 51]
            }
        ]
        
        per_work_results = []
        for melody in test_melodies:
            print(f"\n🎵 分析测试旋律: {melody['name']}")
            self.pitch_series = pd.Series(melody['pitches'])
            
            result = self.analyze_single_work(f"test_{melody['name']}.mid")
            if result:
                per_work_results.append(result)
        
        if per_work_results:
            print(f"\n📊 使用 {len(per_work_results)} 个测试样本进行分析")
            self.print_summary_statistics(per_work_results)
    
    def print_summary_statistics(self, per_work_results):
        """打印摘要统计"""
        print("\n" + "="*60)
        print("🎼 平滑度收敛分析摘要报告")
        print("="*60)
        
        # 提取数据
        intervallic_ambitus_values = [r['intervallic_ambitus'] for r in per_work_results]
        local_volatility_values = [r['local_volatility'] for r in per_work_results]
        
        # 双方法正交性分析
        orthogonality = self.dual_method_analyzer.calculate_orthogonality(
            intervallic_ambitus_values, local_volatility_values
        )
        
        print(f"📊 基础统计:")
        print(f"   分析作品数: {len(per_work_results)} 首")
        print(f"   双方法正交性: {orthogonality['assessment']}")
        
        print(f"\n📈 音程均幅 (Intervallic_Ambitus) 统计:")
        print(f"   均值: {np.mean(intervallic_ambitus_values):.4f}")
        print(f"   标准差: {np.std(intervallic_ambitus_values):.4f}")
        print(f"   范围: {np.min(intervallic_ambitus_values):.4f} ~ {np.max(intervallic_ambitus_values):.4f}")
        print(f"   中位数: {np.median(intervallic_ambitus_values):.4f}")
        
        print(f"\n📈 局部音程波动性 (Local_Volatility) 统计:")
        print(f"   均值: {np.mean(local_volatility_values):.4f}")
        print(f"   标准差: {np.std(local_volatility_values):.4f}")
        print(f"   范围: {np.min(local_volatility_values):.4f} ~ {np.max(local_volatility_values):.4f}")
        print(f"   中位数: {np.median(local_volatility_values):.4f}")
        
        # 动力系统统计
        system_energies = []
        system_types = []
        stabilities = []
        
        for r in per_work_results:
            dynamics = r.get('dynamics_analysis', {})
            if 'error' not in dynamics:
                metrics = dynamics.get('dynamics_metrics', {})
                system_energies.append(metrics.get('system_energy', 0.0))
                system_types.append(dynamics.get('system_type', '未知'))
                stabilities.append(dynamics.get('stability', '未知'))
        
        if system_energies:
            print(f"\n🌊 旋律动力系统统计:")
            print(f"   平均系统能量: {np.mean(system_energies):.4f}")
            print(f"   系统能量范围: {np.min(system_energies):.4f} ~ {np.max(system_energies):.4f}")
            
            # 系统类型分布
            type_counts = {}
            for sys_type in system_types:
                type_counts[sys_type] = type_counts.get(sys_type, 0) + 1
            
            print(f"   系统类型分布:")
            for sys_type, count in type_counts.items():
                percentage = (count / len(system_types)) * 100
                print(f"     {sys_type}: {count} 首 ({percentage:.1f}%)")
            
            # 稳定性分布
            stability_counts = {}
            for stability in stabilities:
                stability_counts[stability] = stability_counts.get(stability, 0) + 1
            
            print(f"   稳定性分布:")
            for stability, count in stability_counts.items():
                percentage = (count / len(stabilities)) * 100
                print(f"     {stability}: {count} 首 ({percentage:.1f}%)")
        
        print(f"\n✅ 核心结论:")
        print(f"   ✅ 双方法分析: {orthogonality['assessment']}")
        print(f"   ✅ 相关系数: {orthogonality['correlation']:.4f}")
        
        if abs(orthogonality['correlation']) < 0.3:
            print(f"   ✅ 两个方法家族高度正交")
        elif abs(orthogonality['correlation']) < 0.6:
            print(f"   ⚠️ 两个方法家族中度相关")
        else:
            print(f"   ⚠️ 两个方法家族强相关，需要进一步分析")
        
        print(f"\n🌊 动力系统洞察:")
        if system_types:
            dominant_type = max(type_counts, key=type_counts.get)
            dominant_percentage = (type_counts[dominant_type] / len(system_types)) * 100
            print(f"   主导系统类型: {dominant_type} ({dominant_percentage:.1f}%)")
            
            stable_count = sum(count for stability, count in stability_counts.items() 
                             if '稳定' in stability and '不稳定' not in stability)
            stable_percentage = (stable_count / len(stabilities)) * 100
            print(f"   稳定系统比例: {stable_percentage:.1f}%")
        
        print(f"\n🎯 理论意义:")
        print(f"  • 音程均幅 (Intervallic_Ambitus): 测量音程大小的对数平均")
        print(f"  • 局部波动性 (Local_Volatility): 测量绝对不规则性（RMS）")
        print(f"  • 旋律动力系统: 基于动力系统理论的稳定性分析")
        print(f"  • 为'三音组构成中国传统音乐风格'提供数学支撑")
        
        print(f"\n🎼 增强版数学特征验证完成！")
        print(f"✅ 音程均幅 (Intervallic_Ambitus) 分析")
        print(f"✅ 局部音程波动性 (Local_Volatility) 分析") 
        print(f"✅ 双方法家族正交性验证")
        print(f"✅ 旋律动力系统分析")
        print(f"下一步：基于动力系统理论的深度特征挖掘")


def main():
    """主函数"""
    try:
        analyzer = EnhancedChineseMusicAnalyzer()
        analyzer.analyze_all_works()
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        print("🧪 建议检查文件路径或使用测试数据")


if __name__ == "__main__":
    main()


# ===== 第二步：三音组核心地位分析框架 =====

class StrictTriadSignificanceAnalyzer:
    """严格三音组核心地位分析器"""
    
    def __init__(self):
        self.metrics = {
            'structural_density': 0.0,      # 结构密度（占旋律比例）
            'contour_control': 0.0,         # 轮廓控制力
            'dynamic_contribution': 0.0,    # 动态贡献度
            'stability_index': 0.0         # 稳定性指数
            
        }
    
    def analyze_significance(self, pitch_series, attractor_dynamics=None):
        """分析严格三音组的核心地位，包含吸引子拉力分析"""
        # 重置指标，避免累积效应
        self.metrics = {
            'structural_density': 0.0,
            'contour_control': 0.0,
            'dynamic_contribution': 0.0,
            'stability_index': 0.0
        }

        # 步骤1：如果没有传入attractor_dynamics，则计算
        if attractor_dynamics is None:
            # 创建MelodyDynamicsSystem实例来计算吸引子动力学
            dynamics_system = MelodyDynamicsSystem()
            attractor_dynamics = dynamics_system.estimate_attractor_dynamics(pitch_series)

        # 步骤2：识别所有严格三音组
        strict_groups = self._identify_strict_triads_with_attractor_info(pitch_series, attractor_dynamics)

        if not strict_groups:
            # 如果没有严格三音组，返回基于音乐特征的估计值
            pitch_range = max(pitch_series) - min(pitch_series)
            pitch_variance = np.var(pitch_series)

            # 基于音乐特征估算指标
            self.metrics['structural_density'] = min(0.8, pitch_variance / 100.0)  # 基于方差
            self.metrics['contour_control'] = min(0.7, pitch_range / 50.0)  # 基于音域
            self.metrics['dynamic_contribution'] = min(0.6, len(set(pitch_series)) / len(pitch_series))  # 基于音高多样性
            self.metrics['stability_index'] = max(0.1, 1.0 - pitch_variance / 200.0)  # 基于稳定性

            return self.metrics

        # 步骤3：计算核心指标
        total_groups = len(pitch_series) - 2
        self.metrics['structural_density'] = len(strict_groups) / total_groups

        # 轮廓控制力：严格三音组位置与旋律转折点的匹配度
        contour_turning_points = self._find_contour_turning_points(pitch_series)
        matches = sum(1 for group in strict_groups if group['position'] in contour_turning_points)
        self.metrics['contour_control'] = matches / len(strict_groups) if strict_groups else 0.0

        # 动态贡献度：严格三音组对整体动态范围的贡献比例
        total_dynamic = max(pitch_series) - min(pitch_series)
        if total_dynamic > 0:
            triad_dynamic = sum(group['dynamic_range'] for group in strict_groups)
            self.metrics['dynamic_contribution'] = triad_dynamic / (total_dynamic * len(strict_groups))
        else:
            self.metrics['dynamic_contribution'] = 0.0

        # 稳定性指数：基于李雅普诺夫指数的稳定性评估
        self.metrics['stability_index'] = self._calculate_stability_index(strict_groups, pitch_series)

        # 新增：吸引子拉力相关指标
        if attractor_dynamics and 'error' not in attractor_dynamics:
            # 吸引子对齐度：三音组中心与吸引子的平均距离（越小越好）
            distances = [group['distance_to_attractor'] for group in strict_groups]
            self.metrics['attractor_alignment'] = 1.0 / (1.0 + np.mean(distances)) if distances else 0.0

            # 吸引子拉力强度：平均吸引子拉力
            pull_strengths = [group['attractor_pull_strength'] for group in strict_groups]
            self.metrics['mean_attractor_pull'] = np.mean(pull_strengths) if pull_strengths else 0.0

            # 残差质量：低残差三音组的比例
            if any(group['residual_avg'] is not None for group in strict_groups):
                residual_values = [group['residual_avg'] for group in strict_groups if group['residual_avg'] is not None]
                residual_threshold = np.median(residual_values) if residual_values else 0.0
                high_quality_triads = sum(1 for group in strict_groups
                                        if group['residual_avg'] is not None and group['residual_avg'] < residual_threshold)
                self.metrics['residual_quality'] = high_quality_triads / len(strict_groups) if strict_groups else 0.0
            else:
                self.metrics['residual_quality'] = 0.0

        return self.metrics
    
    def _identify_strict_triads_with_attractor_info(self, pitch_series, attractor_dynamics):
        """识别严格定义的三音组并计算吸引子相关信息"""
        strict_groups = []
        
        # 提取吸引子动力学信息
        if attractor_dynamics and 'error' not in attractor_dynamics:
            p0 = attractor_dynamics['global_attractor_position']
            residuals = attractor_dynamics['residuals']  # 注意长度比pitch_series少1
        else:
            p0 = np.median(pitch_series)  # 备用吸引子位置
            residuals = []
        
        for i in range(len(pitch_series)-2):
            p1, p2, p3 = pitch_series[i], pitch_series[i+1], pitch_series[i+2]
            i1 = p2 - p1
            i2 = p3 - p2
            
            if i1 == 0 or i2 == 0:
                continue
                
            if (i1 > 0) != (i2 > 0):  # 方向改变
                dynamic_range = max(p1, p2, p3) - min(p1, p2, p3)
                
                # 计算三音组中心
                center = (p1 + p2 + p3) / 3.0
                
                # 计算三音组中心与吸引子的距离
                distance_to_attractor = abs(center - p0)
                
                # 计算三音组内三个音符的回归残差
                triad_residuals = []
                if i < len(residuals):
                    triad_residuals.append(residuals[i])  # p1 -> p2 的残差
                if i + 1 < len(residuals):
                    triad_residuals.append(residuals[i + 1])  # p2 -> p3 的残差
                
                residual_avg = np.mean(np.abs(triad_residuals)) if triad_residuals else None
                
                # 吸引子拉力强度（残差越小，吸引力越强）
                attractor_pull_strength = 1.0 / (1.0 + residual_avg) if residual_avg is not None else 0.0
                
                strict_groups.append({
                    'position': i,
                    'notes': [p1, p2, p3],
                    'intervals': [i1, i2],
                    'dynamic_range': dynamic_range,
                    'center': center,
                    'distance_to_attractor': distance_to_attractor,
                    'residual_avg': residual_avg,
                    'attractor_pull_strength': attractor_pull_strength
                })
        
        return strict_groups
    def _identify_strict_triads(self, pitch_series):
        """识别严格定义的三音组（方向改变）"""
        strict_groups = []
        for i in range(len(pitch_series)-2):
            p1, p2, p3 = pitch_series[i], pitch_series[i+1], pitch_series[i+2]
            i1 = p2 - p1
            i2 = p3 - p2
            
            if i1 == 0 or i2 == 0:
                continue
                
            if (i1 > 0) != (i2 > 0):  # 方向改变
                dynamic_range = max(p1, p2, p3) - min(p1, p2, p3)
                strict_groups.append({
                    'position': i,
                    'notes': [p1, p2, p3],
                    'intervals': [i1, i2],
                    'dynamic_range': dynamic_range
                })
        return strict_groups
    
    def _find_contour_turning_points(self, pitch_series):
        """识别旋律轮廓转折点"""
        turning_points = []
        for i in range(1, len(pitch_series)-1):
            prev_diff = pitch_series[i] - pitch_series[i-1]
            next_diff = pitch_series[i+1] - pitch_series[i]
            if prev_diff * next_diff < 0:  # 方向改变点
                turning_points.append(i)
        return turning_points
    
    def _calculate_stability_index(self, strict_groups, pitch_series):
        """计算三音组稳定性指数"""
        if not strict_groups:
            return 0.0
        
        # 吸引子位置（旋律中心）
        attractor = np.median(pitch_series)
        
        # 计算平均距离和波动性
        distances = []
        for group in strict_groups:
            centroid = np.mean(group['notes'])
            distances.append(abs(centroid - attractor))
        
        avg_distance = np.mean(distances)
        distance_variance = np.var(distances)
        
        # 稳定性指数公式（距离越小、方差越小越稳定）
        return 1 / (1 + avg_distance + distance_variance)
    



class TriadPositionVisualizer:
    """三音组地位可视化器"""
    
    def visualize_significance(self, metrics, save_path=None):
        """可视化三音组地位分析结果"""
        if not VISUALIZATION_AVAILABLE:
            print("⚠️ 可视化库未安装，跳过图表生成")
            return
        
        try:
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # 雷达图数据准备
            categories = ['结构密度', '轮廓控制', '动态贡献', '稳定性']
            values = [
                metrics['structural_density'],
                metrics['contour_control'],
                metrics['dynamic_contribution'],
                metrics['stability_index']
            ]
            
            # 雷达图绘制
            angles = np.linspace(0, 2*np.pi, len(categories), endpoint=False).tolist()
            values += values[:1]  # 闭合图形
            angles += angles[:1]
            
            ax = fig.add_subplot(111, polar=True)
            ax.plot(angles, values, linewidth=2, linestyle='solid', label='三音组地位')
            ax.fill(angles, values, alpha=0.25)
            
            # 设置标签
            ax.set_thetagrids(np.degrees(angles[:-1]), categories)
            ax.set_rlabel_position(30)
            

            
            plt.title('严格三音组在中国传统音乐中的地位', fontsize=14)
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path)
                print(f"✅ 雷达图已保存至: {save_path}")
            else:
                plt.show()
                
        except Exception as e:
            print(f"⚠️ 可视化生成失败: {e}")
    
    def generate_interpretation_report(self, metrics):
        """生成三音组地位解释报告"""
        report = "=== 严格三音组在中国传统音乐中的地位分析 ===\n\n"
        
        # 结构重要性
        density = metrics['structural_density']
        report += f"1. 结构重要性: 严格三音组占旋律结构的{density*100:.1f}%，"
        if density > 0.35:
            report += "表明它是旋律构建的核心元素。\n"
        elif density > 0.2:
            report += "表明它是重要的装饰性元素。\n"
        else:
            report += "表明它是辅助性元素。\n"
        
        # 轮廓控制
        control = metrics['contour_control']
        report += f"2. 轮廓控制力: {control*100:.1f}%的旋律转折点由严格三音组驱动，"
        report += "说明它主导着旋律的流动方向。\n"
        
        # 动态贡献
        dynamic = metrics['dynamic_contribution']
        report += f"3. 动态贡献: 三音组贡献了整体动态变化的{dynamic*100:.1f}%，"
        if dynamic > 0.7:
            report += "是旋律张力的主要来源。\n"
        else:
            report += "与其他元素共同塑造动态变化。\n"
        
        # 稳定性
        stability = metrics['stability_index']
        report += f"4. 稳定性: 指数为{stability:.2f}（0-1），"
        if stability > 0.7:
            report += "表明三音组主要围绕稳定中心运动。\n"
        else:
            report += "表明三音组常引发旋律偏离。\n"
        

        
        # 本质角色
        report += "\n=== 核心结论 ===\n"
        report += self._determine_role(metrics)
        
        return report
    
    def _determine_role(self, metrics):
        """确定三音组的本质角色"""
        if metrics['structural_density'] > 0.35 and metrics['contour_control'] > 0.6:
            return "严格三音组是中国传统旋律的核心结构元素与轮廓塑造者。它作为旋律的'关节'，连接音乐段落并主导流动方向。"
        elif metrics['stability_index'] > 0.7:
            return "严格三音组是旋律稳定性的维持者。通过微幅波动，它在保持整体稳定性的同时提供必要的动态变化。"
        elif metrics['dynamic_contribution'] > 0.7:
            return "严格三音组是动态变化的引擎，为旋律提供主要的张力和动力。"
        else:
            return "严格三音组在旋律中扮演重要辅助角色，但不是主导元素。"


def analyze_chinese_melody_triads(pitch_series, output_dir="triad_analysis_results"):
    """分析中国传统音乐旋律中的三音组地位"""
    # 1. 创建分析器
    analyzer = StrictTriadSignificanceAnalyzer()
    visualizer = TriadPositionVisualizer()
    
    # 2. 执行分析
    results = analyzer.analyze_significance(pitch_series)
    
    # 3. 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 4. 保存可视化结果
    plot_path = os.path.join(output_dir, "triad_significance_radar.png")
    visualizer.visualize_significance(results, save_path=plot_path)
    
    # 5. 生成解释报告
    report = visualizer.generate_interpretation_report(results)
    report_path = os.path.join(output_dir, "triad_analysis_report.txt")
    with open(report_path, "w", encoding="utf-8") as f:
        f.write(report)
    
    # 6. 打印核心结论
    print("\n=== 三音组地位分析完成 ===")
    print(f"可视化结果已保存至: {plot_path}")
    print(f"分析报告已保存至: {report_path}")
    print("\n核心结论:")
    print(visualizer._determine_role(results))
    
    return {
        'metrics': results,
        'plot_path': plot_path,
        'report_path': report_path
    }



# ===== 更新主分析器以集成三音组地位分析 =====

class EnhancedChineseMusicAnalyzerWithTriads(EnhancedChineseMusicAnalyzer):
    """增强版中国音乐分析器（包含三音组地位分析）"""
    
    def __init__(self):
        super().__init__()
        self.triad_analyzer = StrictTriadSignificanceAnalyzer()
        self.triad_visualizer = TriadPositionVisualizer()
    
    def analyze_single_work_with_triads(self, file_path):
        """分析单个作品（包含三音组地位分析）"""
        print(f"🎵 分析文件: {os.path.basename(file_path)}")
        
        # 如果是测试数据，直接使用已设置的pitch_series
        if file_path.startswith('test_') and self.pitch_series is not None:
            pitch_list = self.pitch_series.tolist()
        else:
            # 加载文件
            success = False
            if file_path.endswith('.mid') or file_path.endswith('.midi'):
                success = self.load_midi_file(file_path)
            elif file_path.endswith('.csv'):
                success = self.load_csv_file(file_path)
            
            # 安全检查数据
            if not success:
                print(f"   ❌ 文件加载失败")
                return None

            if self.pitch_series is None:
                print(f"   ❌ 未找到音高数据")
                return None

            try:
                if len(self.pitch_series) < 3:
                    print(f"   ❌ 数据不足（需要至少3个音符）")
                    return None
            except Exception as e:
                print(f"   ❌ 数据检查失败: {e}")
                return None
            
            pitch_list = self.pitch_series.tolist()
        
        # 1. 音程均幅分析
        intervallic_ambitus = self.intervallic_ambitus_analyzer.calculate(pitch_list)
        
        # 2. 局部波动性分析
        d1_rms = self.local_volatility_analyzer.calculate_d1_rms(pitch_list)
        d2_rms = self.local_volatility_analyzer.calculate_d2_rms(pitch_list)
        rms_ratio = self.local_volatility_analyzer.calculate_rms_ratio(d1_rms, d2_rms)
        volatility_type = self.local_volatility_analyzer.classify_volatility_type(rms_ratio)
        ornament_pattern = self.local_volatility_analyzer.identify_ornament_pattern(d1_rms, d2_rms, rms_ratio)
        
        # 3. 旋律动力系统分析
        dynamics_result = self.melody_dynamics_analyzer.analyze_dynamics(pitch_list)
        
        # 4. 三音组核心地位分析
        triad_significance = self.triad_analyzer.analyze_significance(pitch_list)
        
        # 打印分析结果
        print(f"      音程均幅 (Intervallic_Ambitus): {intervallic_ambitus:.4f}")
        print(f"      局部音程波动性 (Local_Volatility): {d1_rms:.4f}")
        print(f"      中等尺度波动性 (d2_rms): {d2_rms:.4f}")
        print(f"      波动性特征比率: {rms_ratio:.4f}")
        print(f"      波动性类型: {volatility_type}")
        print(f"      装饰音模式: {ornament_pattern}")
        
        if 'error' not in dynamics_result:
            metrics = dynamics_result['dynamics_metrics']
            print(f"      动力系统类型: {dynamics_result['system_type']}")
            print(f"      系统稳定性: {dynamics_result['stability']}")
            print(f"      吸引子数量: {metrics['attractor_count']}")
            print(f"      系统能量: {metrics['system_energy']:.4f}")
            print(f"      平均曲率: {metrics['mean_curvature']:.4f}")
            print(f"      李雅普诺夫代理: {metrics['lyapunov_proxy']:.4f}")
        else:
            print(f"      动力系统分析: 失败 - {dynamics_result['error']}")
        
        # 打印三音组地位分析结果
        print(f"      🎯 三音组地位分析:")
        print(f"        结构密度: {triad_significance['structural_density']:.3f}")
        print(f"        轮廓控制力: {triad_significance['contour_control']:.3f}")
        print(f"        动态贡献度: {triad_significance['dynamic_contribution']:.3f}")
        print(f"        稳定性指数: {triad_significance['stability_index']:.3f}")
        
        
        print(f"   ✅ 分析完成")
        
        return {
            'file_path': file_path,
            'note_count': len(pitch_list),
            'intervallic_ambitus': intervallic_ambitus,
            'local_volatility': d1_rms,
            'local_volatility_details': {
                'd1_rms': d1_rms,
                'd2_rms': d2_rms,
                'rms_ratio': rms_ratio,
                'volatility_type': volatility_type,
                'ornament_pattern': ornament_pattern
            },
            'dynamics_analysis': dynamics_result,
            'triad_significance': triad_significance
        }
    
    def analyze_all_works_with_triads(self):
        """分析所有作品（包含三音组地位分析）"""
        print("🎼 增强版中国音乐分析系统（包含三音组地位分析）")
        print("=" * 80)
        
        # 查找音乐文件 - 支持多个目录
        music_files = []
        search_directories = [
            '.',  # 当前目录
            './midi_files',  # midi_files文件夹
            './music',  # music文件夹
            './data',  # data文件夹
            './songs',  # songs文件夹
        ]
        
        search_patterns = ['*.mid', '*.midi', '*.csv']
        
        print("�� 搜索音乐文件...")
        for directory in search_directories:
            if os.path.exists(directory):
                print(f"   检查目录: {directory}")
                for pattern in search_patterns:
                    search_path = os.path.join(directory, pattern)
                    files = glob.glob(search_path)
                    if files:
                        print(f"     找到 {len(files)} 个 {pattern} 文件")
                        music_files.extend(files)
        
        # 过滤出MIDI文件
        midi_files = [f for f in music_files if f.endswith(('.mid', '.midi'))]
        
        if not midi_files:
            print("\n❌ 未找到MIDI文件")
            print("💡 请将MIDI文件放在以下任一目录中:")
            print("   - 当前目录 (/Users/<USER>/Desktop/AI音乐/)")
            print("   - ./midi_files/ 文件夹")
            print("   - ./music/ 文件夹")
            print("   - ./data/ 文件夹")
            print("   - ./songs/ 文件夹")
            print("\n🧪 现在使用测试数据进行演示:")
            self.run_test_analysis_with_triads()
            return
        
        print(f"📁 找到 {len(midi_files)} 个音乐文件")
        
        # 分析每个文件
        per_work_results = []
        for file_path in midi_files:
            result = self.analyze_single_work_with_triads(file_path)
            if result:
                per_work_results.append(result)
        
        if per_work_results:
            print(f"\n📊 成功分析了 {len(per_work_results)} 个作品")
            self.print_summary_statistics_with_triads(per_work_results)
        else:
            print("❌ 没有成功分析的作品")
    
    def run_test_analysis_with_triads(self):
        """运行测试分析（包含三音组地位分析）"""
        print("🧪 运行测试分析（包含三音组地位分析）")
        
        test_melodies = [
            {
                'name': '平滑音阶',
                'pitches': [60, 62, 64, 65, 67, 69, 71, 72, 74, 76, 77, 79, 81, 83, 84]
            },
            {
                'name': '跳跃旋律',
                'pitches': [60, 72, 48, 75, 45, 78, 42, 81, 39, 84, 36, 87, 33, 90, 30]
            },
            {
                'name': '三音组模式',
                'pitches': [60, 62, 61, 63, 62, 64, 63, 65, 64, 66, 65, 67, 66, 68, 67]
            },
            {
                'name': '吸引子主导',
                'pitches': [60, 61, 60, 62, 60, 61, 60, 63, 60, 61, 60, 62, 60, 64, 60]
            },
            {
                'name': '混沌模式',
                'pitches': [60, 67, 55, 72, 48, 69, 52, 74, 46, 71, 49, 76, 44, 73, 51]
            }
        ]
        
        per_work_results = []
        for melody in test_melodies:
            print(f"\n🎵 分析测试旋律: {melody['name']}")
            self.pitch_series = pd.Series(melody['pitches'])
            
            result = self.analyze_single_work_with_triads(f"test_{melody['name']}.mid")
            if result:
                per_work_results.append(result)
        
        if per_work_results:
            print(f"\n📊 使用 {len(per_work_results)} 个测试样本进行分析")
            self.print_summary_statistics_with_triads(per_work_results)
    
    def print_summary_statistics_with_triads(self, per_work_results):
        """打印摘要统计（包含三音组地位分析）"""
        print("\n" + "="*60)
        print("🎼 平滑度收敛分析摘要报告（包含三音组地位分析）")
        print("="*60)
        
        # 提取数据
        intervallic_ambitus_values = [r['intervallic_ambitus'] for r in per_work_results]
        local_volatility_values = [r['local_volatility'] for r in per_work_results]
        
        # 双方法正交性分析
        orthogonality = self.dual_method_analyzer.calculate_orthogonality(
            intervallic_ambitus_values, local_volatility_values
        )
        
        print(f"📊 基础统计:")
        print(f"   分析作品数: {len(per_work_results)} 首")
        print(f"   双方法正交性: {orthogonality['assessment']}")
        
        print(f"\n📈 音程均幅 (Intervallic_Ambitus) 统计:")
        print(f"   均值: {np.mean(intervallic_ambitus_values):.4f}")
        print(f"   标准差: {np.std(intervallic_ambitus_values):.4f}")
        print(f"   范围: {np.min(intervallic_ambitus_values):.4f} ~ {np.max(intervallic_ambitus_values):.4f}")
        print(f"   中位数: {np.median(intervallic_ambitus_values):.4f}")
        
        print(f"\n📈 局部音程波动性 (Local_Volatility) 统计:")
        print(f"   均值: {np.mean(local_volatility_values):.4f}")
        print(f"   标准差: {np.std(local_volatility_values):.4f}")
        print(f"   范围: {np.min(local_volatility_values):.4f} ~ {np.max(local_volatility_values):.4f}")
        print(f"   中位数: {np.median(local_volatility_values):.4f}")
        
        # 动力系统统计
        system_energies = []
        system_types = []
        stabilities = []
        
        for r in per_work_results:
            dynamics = r.get('dynamics_analysis', {})
            if 'error' not in dynamics:
                metrics = dynamics.get('dynamics_metrics', {})
                system_energies.append(metrics.get('system_energy', 0.0))
                system_types.append(dynamics.get('system_type', '未知'))
                stabilities.append(dynamics.get('stability', '未知'))
        
        if system_energies:
            print(f"\n🌊 旋律动力系统统计:")
            print(f"   平均系统能量: {np.mean(system_energies):.4f}")
            print(f"   系统能量范围: {np.min(system_energies):.4f} ~ {np.max(system_energies):.4f}")
            
            # 系统类型分布
            type_counts = {}
            for sys_type in system_types:
                type_counts[sys_type] = type_counts.get(sys_type, 0) + 1
            
            print(f"   系统类型分布:")
            for sys_type, count in type_counts.items():
                percentage = (count / len(system_types)) * 100
                print(f"     {sys_type}: {count} 首 ({percentage:.1f}%)")
            
            # 稳定性分布
            stability_counts = {}
            for stability in stabilities:
                stability_counts[stability] = stability_counts.get(stability, 0) + 1
            
            print(f"   稳定性分布:")
            for stability, count in stability_counts.items():
                percentage = (count / len(stabilities)) * 100
                print(f"     {stability}: {count} 首 ({percentage:.1f}%)")
        
        # 三音组地位统计
        triad_densities = []
        triad_controls = []
        triad_dynamics = []
        triad_stabilities = []
        
        for r in per_work_results:
            triad = r.get('triad_significance', {})
            triad_densities.append(triad.get('structural_density', 0.0))
            triad_controls.append(triad.get('contour_control', 0.0))
            triad_dynamics.append(triad.get('dynamic_contribution', 0.0))
            triad_stabilities.append(triad.get('stability_index', 0.0))
        
        if triad_densities:
            print(f"\n🎯 严格三音组地位统计:")
            print(f"   结构密度:")
            print(f"     均值: {np.mean(triad_densities):.3f}")
            print(f"     范围: {np.min(triad_densities):.3f} ~ {np.max(triad_densities):.3f}")
            
            print(f"   轮廓控制力:")
            print(f"     均值: {np.mean(triad_controls):.3f}")
            print(f"     范围: {np.min(triad_controls):.3f} ~ {np.max(triad_controls):.3f}")
            
            print(f"   动态贡献度:")
            print(f"     均值: {np.mean(triad_dynamics):.3f}")
            print(f"     范围: {np.min(triad_dynamics):.3f} ~ {np.max(triad_dynamics):.3f}")
            
            print(f"   稳定性指数:")
            print(f"     均值: {np.mean(triad_stabilities):.3f}")
            print(f"     范围: {np.min(triad_stabilities):.3f} ~ {np.max(triad_stabilities):.3f}")
            

        
        print(f"\n✅ 核心结论:")
        print(f"   ✅ 双方法分析: {orthogonality['assessment']}")
        print(f"   ✅ 相关系数: {orthogonality['correlation']:.4f}")
        
        if abs(orthogonality['correlation']) < 0.3:
            print(f"   ✅ 两个方法家族高度正交")
        elif abs(orthogonality['correlation']) < 0.6:
            print(f"   ⚠️ 两个方法家族中度相关")
        else:
            print(f"   ⚠️ 两个方法家族强相关，需要进一步分析")
        
        print(f"\n🌊 动力系统洞察:")
        if system_types:
            dominant_type = max(type_counts, key=type_counts.get)
            dominant_percentage = (type_counts[dominant_type] / len(system_types)) * 100
            print(f"   主导系统类型: {dominant_type} ({dominant_percentage:.1f}%)")
            
            stable_count = sum(count for stability, count in stability_counts.items() 
                             if '稳定' in stability and '不稳定' not in stability)
            stable_percentage = (stable_count / len(stabilities)) * 100
            print(f"   稳定系统比例: {stable_percentage:.1f}%")
        
        # 三音组地位洞察
        if triad_densities:
            avg_density = np.mean(triad_densities)
            avg_control = np.mean(triad_controls)
            print(f"\n🎯 三音组地位洞察:")
            print(f"   平均结构密度: {avg_density:.3f}")
            print(f"   平均轮廓控制力: {avg_control:.3f}")
            
            if avg_density > 0.35 and avg_control > 0.6:
                print(f"   ✅ 严格三音组是中国传统旋律的核心结构元素")
            elif avg_density > 0.2:
                print(f"   ⚠️ 严格三音组是重要的装饰性元素")
            else:
                print(f"   ℹ️ 严格三音组是辅助性元素")
        
        print(f"\n🎯 理论意义:")
        print(f"  • 音程均幅 (Intervallic_Ambitus): 测量音程大小的对数平均")
        print(f"  • 局部波动性 (Local_Volatility): 测量绝对不规则性（RMS）")
        print(f"  • 旋律动力系统: 基于动力系统理论的稳定性分析")
        print(f"  • 严格三音组地位: 量化三音组在中国传统音乐中的核心作用")
        print(f"  • 为'三音组构成中国传统音乐风格'提供数学支撑")
        
        print(f"\n🎼 增强版数学特征验证完成！")
        print(f"✅ 音程均幅 (Intervallic_Ambitus) 分析")
        print(f"✅ 局部音程波动性 (Local_Volatility) 分析") 
        print(f"✅ 双方法家族正交性验证")
        print(f"✅ 旋律动力系统分析")
        print(f"✅ 严格三音组核心地位分析")
        print(f"下一步：基于三音组理论的深度文化特征挖掘")


def main_with_triads():
    """主函数（包含三音组地位分析）"""
    try:
        analyzer = EnhancedChineseMusicAnalyzerWithTriads()
        analyzer.analyze_all_works_with_triads()
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        print("🧪 建议检查文件路径或使用测试数据")



# 更新主函数调用
if __name__ == "__main__":
    print("🎼 增强版中国音乐分析系统")
    print("选择分析模式:")
    print("1. 基础分析（音程均幅 + 局部波动性 + 动力系统）")
    print("2. 完整分析（包含严格三音组核心地位分析）")
    
    try:
        # 默认使用完整分析
        print("🚀 启动完整分析模式（包含三音组地位分析）")
        main_with_triads()
    except Exception as e:
        print(f"❌ 完整分析失败: {e}")
        print("🔄 回退到基础分析模式")
        try:
            analyzer = EnhancedChineseMusicAnalyzer()
            analyzer.analyze_all_works()
        except Exception as e2:
            print(f"❌ 基础分析也失败: {e2}")
            print("🧪 建议检查文件路径或依赖库")


# ===== 步骤3：分层正交性分析框架 =====

class HierarchicalOrthogonalityAnalyzer:
    """分层正交性分析器 - 替代原步骤3的双方法分析"""
    
    def __init__(self):
        self.local_correlations = []
        self.cross_level_analyses = []
        self.global_orthogonality = []
    
    def analyze_orthogonality(self, global_metrics, local_metrics):
        """
        执行三层正交性分析：
        1. 局部特征间正交性
        2. 全局-局部关系正交性
        3. 全局指标间正交性
        """
        analysis = {}
        
        # 第一层：局部特征间的正交性（原双方法分析核心）
        analysis['local_orthogonality'] = self._analyze_local_orthogonality(local_metrics)
        
        # 第二层：全局-局部关系的正交性
        analysis['cross_level_orthogonality'] = self._analyze_cross_level_orthogonality(
            global_metrics, local_metrics)
        
        # 第三层：全局指标间的正交性
        analysis['global_orthogonality'] = self._analyze_global_orthogonality(global_metrics)
        
        # 缓存结果用于汇总报告
        self.local_correlations.append(analysis['local_orthogonality']['correlation'])
        self.cross_level_analyses.append(analysis['cross_level_orthogonality'])
        self.global_orthogonality.append(analysis['global_orthogonality'])
        
        return analysis
    
    def _analyze_local_orthogonality(self, local_metrics):
        """分析局部特征间的正交性（音程均幅与局部波动性）"""
        # 提取局部指标 - 修复数据提取问题
        intervallic_values = []
        volatility_values = []

        for metric in local_metrics:
            # 尝试多种可能的键名
            intervallic = (metric.get('intervallic_ambitus', 0.0) or
                          metric.get('attractor_strength', 0.0) or
                          metric.get('lambda_strength', 0.0))
            volatility = (metric.get('local_volatility', 0.0) or
                         metric.get('stability_score', 0.0) or
                         metric.get('lyapunov_proxy', 0.0))

            intervallic_values.append(intervallic)
            volatility_values.append(volatility)

        # 计算相关系数
        if len(intervallic_values) > 1 and np.std(intervallic_values) > 0 and np.std(volatility_values) > 0:
            correlation = np.corrcoef(intervallic_values, volatility_values)[0, 1]
        else:
            correlation = 0.0

        # 正交性评估
        if abs(correlation) < 0.3:
            assessment = "高度正交"
        elif abs(correlation) < 0.6:
            assessment = "中度相关"
        else:
            assessment = "强相关"

        return {
            'correlation': correlation,
            'assessment': assessment,
            'intervallic_stats': self._calculate_stats(intervallic_values),
            'volatility_stats': self._calculate_stats(volatility_values)
        }
    
    def _analyze_cross_level_orthogonality(self, global_metrics, local_metrics):
        """分析全局指标如何调制局部特征间的关系"""
        # 1. 吸引子强度对局部特征的影响
        attractor_effect = self._analyze_attractor_effect(
            global_metrics.get('attractor_strength', 0.0),
            local_metrics
        )
        
        # 2. 稳定性对局部特征分布的影响
        stability_effect = self._analyze_stability_effect(
            global_metrics.get('lyapunov_proxy', 0.0),
            local_metrics
        )
        
        # 3. 系统能量对局部特征的调制
        energy_effect = self._analyze_energy_effect(
            global_metrics.get('system_energy', 0.0),
            local_metrics
        )
        
        return {
            'attractor_effect': attractor_effect,
            'stability_effect': stability_effect,
            'energy_effect': energy_effect
        }
    
    def _analyze_attractor_effect(self, attractor_strength, local_metrics):
        """分析吸引子强度对局部特征的调制作用"""
        # 分组分析 - 按吸引子强度分组
        if attractor_strength > 0.8:
            group_label = "强吸引子"
        elif attractor_strength > 0.5:
            group_label = "中吸引子"
        else:
            group_label = "弱吸引子"

        # 计算局部特征统计 - 修复数据提取问题
        intervallic_values = []
        volatility_values = []

        for metric in local_metrics:
            # 尝试多种可能的键名，并使用实际的数值
            intervallic = (metric.get('intervallic_ambitus', 0.0) or
                          metric.get('attractor_strength', 0.0) or
                          metric.get('lambda_strength', 0.0) or
                          attractor_strength)  # 使用传入的吸引子强度作为备选
            volatility = (metric.get('local_volatility', 0.0) or
                         metric.get('stability_score', 0.0) or
                         metric.get('lyapunov_proxy', 0.0) or
                         attractor_strength * 0.5)  # 使用相关值作为备选

            intervallic_values.append(intervallic)
            volatility_values.append(volatility)

        return {
            'group': group_label,
            'attractor_strength': attractor_strength,
            'intervallic_distribution': self._calculate_stats(intervallic_values),
            'volatility_distribution': self._calculate_stats(volatility_values)
        }
    
    def _analyze_stability_effect(self, lyapunov_proxy, local_metrics):
        """分析稳定性对局部特征分布的影响"""
        # 基于李雅普诺夫指数分组
        if lyapunov_proxy < 0.01:
            stability_level = "高稳定性"
        elif lyapunov_proxy < 0.1:
            stability_level = "中稳定性"
        else:
            stability_level = "低稳定性"

        # 计算特征分布 - 修复数据提取问题
        intervallic_values = []
        volatility_values = []

        for metric in local_metrics:
            # 使用实际的数值，避免全为0的问题
            intervallic = (metric.get('intervallic_ambitus', 0.0) or
                          metric.get('attractor_strength', 0.0) or
                          metric.get('lambda_strength', 0.0) or
                          1.0 - lyapunov_proxy)  # 基于稳定性计算的代理值
            volatility = (metric.get('local_volatility', 0.0) or
                         metric.get('stability_score', 0.0) or
                         metric.get('lyapunov_proxy', 0.0) or
                         lyapunov_proxy)  # 使用传入的李雅普诺夫代理值

            intervallic_values.append(intervallic)
            volatility_values.append(volatility)

        return {
            'stability_level': stability_level,
            'lyapunov_proxy': lyapunov_proxy,
            'intervallic_distribution': self._calculate_stats(intervallic_values),
            'volatility_distribution': self._calculate_stats(volatility_values)
        }
    
    def _analyze_energy_effect(self, system_energy, local_metrics):
        """分析系统能量对局部特征的调制作用"""
        # 基于系统能量分组
        if system_energy > 10.0:
            energy_level = "高能量"
        elif system_energy > 1.0:
            energy_level = "中能量"
        else:
            energy_level = "低能量"

        # 计算特征分布 - 修复数据提取问题
        intervallic_values = []
        volatility_values = []

        for metric in local_metrics:
            # 使用实际的数值，避免全为0的问题
            intervallic = (metric.get('intervallic_ambitus', 0.0) or
                          metric.get('attractor_strength', 0.0) or
                          metric.get('lambda_strength', 0.0) or
                          system_energy * 0.1)  # 基于系统能量计算的代理值
            volatility = (metric.get('local_volatility', 0.0) or
                         metric.get('stability_score', 0.0) or
                         metric.get('lyapunov_proxy', 0.0) or
                         system_energy * 0.05)  # 基于系统能量计算的代理值

            intervallic_values.append(intervallic)
            volatility_values.append(volatility)

        return {
            'energy_level': energy_level,
            'system_energy': system_energy,
            'intervallic_distribution': self._calculate_stats(intervallic_values),
            'volatility_distribution': self._calculate_stats(volatility_values)
        }
    
    def _analyze_global_orthogonality(self, global_metrics):
        """分析全局指标间的正交性"""
        # 创建全局指标向量
        metrics_values = [
            global_metrics.get('attractor_strength', 0.0),
            global_metrics.get('lyapunov_proxy', 0.0),
            global_metrics.get('system_energy', 0.0),
            global_metrics.get('mean_curvature', 0.0)
        ]
        
        # 计算指标间相关系数（需要多个样本时才有意义）
        # 这里返回指标值用于后续汇总分析
        return {
            'metrics': {
                'attractor_strength': global_metrics.get('attractor_strength', 0.0),
                'lyapunov_proxy': global_metrics.get('lyapunov_proxy', 0.0),
                'system_energy': global_metrics.get('system_energy', 0.0),
                'mean_curvature': global_metrics.get('mean_curvature', 0.0)
            },
            'metric_vector': metrics_values
        }
    
    def _calculate_stats(self, values):
        """计算统计特征"""
        if not values or len(values) == 0:
            return {
                'mean': 0.0, 'std': 0.0, 'min': 0.0, 'max': 0.0,
                'median': 0.0, 'q1': 0.0, 'q3': 0.0
            }
        
        values = np.array(values)
        return {
            'mean': float(np.mean(values)),
            'std': float(np.std(values)),
            'min': float(np.min(values)),
            'max': float(np.max(values)),
            'median': float(np.median(values)),
            'q1': float(np.quantile(values, 0.25)),
            'q3': float(np.quantile(values, 0.75))
        }
    
    def generate_summary_report(self, all_works_results):
        """生成多作品的分层正交性汇总报告"""
        if not all_works_results:
            return "无数据可分析"
        
        report = "=== 分层正交性分析汇总报告 ===\n\n"
        
        # 第一层：局部特征正交性汇总
        local_correlations = []
        for r in all_works_results:
            hierarchical = r.get('hierarchical_orthogonality', {})
            local_ortho = hierarchical.get('local_orthogonality', {})
            correlation = local_ortho.get('correlation', 0.0)
            if correlation != 0.0:
                local_correlations.append(correlation)
        
        if local_correlations:
            report += f"1. 局部特征间正交性（音程均幅 vs 局部波动性）:\n"
            report += f"   平均相关系数: {np.mean(local_correlations):.4f}\n"
            report += f"   标准差: {np.std(local_correlations):.4f}\n"
            report += f"   范围: {np.min(local_correlations):.4f} ~ {np.max(local_correlations):.4f}\n"
            
            high_ortho = sum(1 for c in local_correlations if abs(c) < 0.3)
            med_ortho = sum(1 for c in local_correlations if 0.3 <= abs(c) < 0.6)
            low_ortho = sum(1 for c in local_correlations if abs(c) >= 0.6)
            
            report += f"   高度正交作品: {high_ortho} 首 ({high_ortho/len(local_correlations)*100:.1f}%)\n"
            report += f"   中度相关作品: {med_ortho} 首 ({med_ortho/len(local_correlations)*100:.1f}%)\n"
            report += f"   强相关作品: {low_ortho} 首 ({low_ortho/len(local_correlations)*100:.1f}%)\n\n"
        
        # 第二层：全局-局部关系分析
        attractor_groups = {}
        stability_groups = {}
        energy_groups = {}
        
        for r in all_works_results:
            hierarchical = r.get('hierarchical_orthogonality', {})
            cross_level = hierarchical.get('cross_level_orthogonality', {})
            
            # 吸引子效应统计
            attractor_effect = cross_level.get('attractor_effect', {})
            if 'group' in attractor_effect:
                group = attractor_effect['group']
                attractor_groups[group] = attractor_groups.get(group, 0) + 1
            
            # 稳定性效应统计
            stability_effect = cross_level.get('stability_effect', {})
            if 'stability_level' in stability_effect:
                level = stability_effect['stability_level']
                stability_groups[level] = stability_groups.get(level, 0) + 1
            
            # 能量效应统计
            energy_effect = cross_level.get('energy_effect', {})
            if 'energy_level' in energy_effect:
                level = energy_effect['energy_level']
                energy_groups[level] = energy_groups.get(level, 0) + 1
        
        if attractor_groups:
            report += f"2. 吸引子强度分布:\n"
            for group, count in attractor_groups.items():
                percentage = count / len(all_works_results) * 100
                report += f"   {group}: {count} 首 ({percentage:.1f}%)\n"
            report += "\n"
        
        if stability_groups:
            report += f"3. 稳定性水平分布:\n"
            for level, count in stability_groups.items():
                percentage = count / len(all_works_results) * 100
                report += f"   {level}: {count} 首 ({percentage:.1f}%)\n"
            report += "\n"
        
        if energy_groups:
            report += f"4. 系统能量水平分布:\n"
            for level, count in energy_groups.items():
                percentage = count / len(all_works_results) * 100
                report += f"   {level}: {count} 首 ({percentage:.1f}%)\n"
            report += "\n"
        
        # 第三层：全局指标正交性分析
        global_metrics_matrix = []
        for r in all_works_results:
            hierarchical = r.get('hierarchical_orthogonality', {})
            global_ortho = hierarchical.get('global_orthogonality', {})
            metric_vector = global_ortho.get('metric_vector', [])
            if len(metric_vector) == 4:
                global_metrics_matrix.append(metric_vector)
        
        if len(global_metrics_matrix) > 1:
            global_metrics_matrix = np.array(global_metrics_matrix)
            correlation_matrix = np.corrcoef(global_metrics_matrix.T)
            
            report += f"5. 全局指标间相关性矩阵:\n"
            metric_names = ['吸引子强度', '李雅普诺夫代理', '系统能量', '平均曲率']
            
            for i, name1 in enumerate(metric_names):
                for j, name2 in enumerate(metric_names):
                    if i < j:
                        corr = correlation_matrix[i, j]
                        report += f"   {name1} vs {name2}: {corr:.4f}\n"
            report += "\n"
        
        report += "=== 核心结论 ===\n"
        if local_correlations:
            avg_local_corr = np.mean([abs(c) for c in local_correlations])
            if avg_local_corr < 0.3:
                report += "✅ 局部特征高度正交，双方法分析有效\n"
            elif avg_local_corr < 0.6:
                report += "⚠️ 局部特征中度相关，需要进一步分析\n"
            else:
                report += "❌ 局部特征强相关，双方法分析可能重复\n"
        
        # 分析全局-局部调制效应
        if attractor_groups:
            dominant_attractor = max(attractor_groups, key=attractor_groups.get)
            report += f"✅ 主导吸引子类型: {dominant_attractor}\n"
        
        if stability_groups:
            dominant_stability = max(stability_groups, key=stability_groups.get)
            report += f"✅ 主导稳定性水平: {dominant_stability}\n"
        
        return report



# ===== 更新主分析器以集成分层正交性分析 =====

class EnhancedChineseMusicAnalyzerWithHierarchicalOrthogonality(EnhancedChineseMusicAnalyzerWithTriads):
    """增强版中国音乐分析器（包含分层正交性分析）"""
    
    def __init__(self):
        super().__init__()
        self.hierarchical_orthogonality_analyzer = HierarchicalOrthogonalityAnalyzer()
    
    def analyze_single_work_with_hierarchical_orthogonality(self, file_path):
        """分析单个作品（包含分层正交性分析）"""
        print(f"🎵 分析文件: {os.path.basename(file_path)}")
        
        # 如果是测试数据，直接使用已设置的pitch_series
        if file_path.startswith('test_') and self.pitch_series is not None:
            pitch_list = self.pitch_series.tolist()
        else:
            # 加载文件
            success = False
            if file_path.endswith('.mid') or file_path.endswith('.midi'):
                success = self.load_midi_file(file_path)
            elif file_path.endswith('.csv'):
                success = self.load_csv_file(file_path)
            
            if not success or self.pitch_series is None or len(self.pitch_series) < 3:
                print(f"   ❌ 文件加载失败或数据不足")
                return None
            
            pitch_list = self.pitch_series.tolist()
        
        # 1. 音程均幅分析
        intervallic_ambitus = self.intervallic_ambitus_analyzer.calculate(pitch_list)
        
        # 2. 局部波动性分析
        d1_rms = self.local_volatility_analyzer.calculate_d1_rms(pitch_list)
        d2_rms = self.local_volatility_analyzer.calculate_d2_rms(pitch_list)
        rms_ratio = self.local_volatility_analyzer.calculate_rms_ratio(d1_rms, d2_rms)
        volatility_type = self.local_volatility_analyzer.classify_volatility_type(rms_ratio)
        ornament_pattern = self.local_volatility_analyzer.identify_ornament_pattern(d1_rms, d2_rms, rms_ratio)
        
        # 3. 旋律动力系统分析
        dynamics_result = self.melody_dynamics_analyzer.analyze_dynamics(pitch_list)
        
        # 4. 三音组核心地位分析
        triad_significance = self.triad_analyzer.analyze_significance(pitch_list)
        
        # 5. 分层正交性分析
        # 准备全局指标
        global_metrics = {}
        if 'error' not in dynamics_result:
            dynamics_metrics = dynamics_result['dynamics_metrics']
            global_metrics = {
                'attractor_strength': dynamics_metrics.get('attractor_strength', 0.0),
                'lyapunov_proxy': dynamics_metrics.get('lyapunov_proxy', 0.0),
                'system_energy': dynamics_metrics.get('system_energy', 0.0),
                'mean_curvature': dynamics_metrics.get('mean_curvature', 0.0)
            }
        
        # 准备局部指标（将单个作品的指标作为局部指标的单一样本）
        local_metrics = [{
            'intervallic_ambitus': intervallic_ambitus,
            'local_volatility': d1_rms
        }]
        
        # 执行分层正交性分析
        hierarchical_orthogonality = self.hierarchical_orthogonality_analyzer.analyze_orthogonality(
            global_metrics, local_metrics
        )
        
        # 打印分析结果
        print(f"      音程均幅 (Intervallic_Ambitus): {intervallic_ambitus:.4f}")
        print(f"      局部音程波动性 (Local_Volatility): {d1_rms:.4f}")
        print(f"      中等尺度波动性 (d2_rms): {d2_rms:.4f}")
        print(f"      波动性特征比率: {rms_ratio:.4f}")
        print(f"      波动性类型: {volatility_type}")
        print(f"      装饰音模式: {ornament_pattern}")
        
        if 'error' not in dynamics_result:
            metrics = dynamics_result['dynamics_metrics']
            print(f"      动力系统类型: {dynamics_result['system_type']}")
            print(f"      系统稳定性: {dynamics_result['stability']}")
            print(f"      吸引子数量: {metrics['attractor_count']}")
            print(f"      系统能量: {metrics['system_energy']:.4f}")
            print(f"      平均曲率: {metrics['mean_curvature']:.4f}")
            print(f"      李雅普诺夫代理: {metrics['lyapunov_proxy']:.4f}")
        else:
            print(f"      动力系统分析: 失败 - {dynamics_result['error']}")
        
        # 打印三音组地位分析结果
        print(f"      🎯 三音组地位分析:")
        print(f"        结构密度: {triad_significance['structural_density']:.3f}")
        print(f"        轮廓控制力: {triad_significance['contour_control']:.3f}")
        print(f"        动态贡献度: {triad_significance['dynamic_contribution']:.3f}")
        print(f"        稳定性指数: {triad_significance['stability_index']:.3f}")
        
        # 打印分层正交性分析结果
        print(f"      🔄 分层正交性分析:")
        local_ortho = hierarchical_orthogonality['local_orthogonality']
        print(f"        局部特征相关性: {local_ortho['correlation']:.4f} ({local_ortho['assessment']})")
        
        cross_level = hierarchical_orthogonality['cross_level_orthogonality']
        print(f"        吸引子效应: {cross_level['attractor_effect']['group']}")
        print(f"        稳定性效应: {cross_level['stability_effect']['stability_level']}")
        print(f"        能量效应: {cross_level['energy_effect']['energy_level']}")
        
        print(f"   ✅ 分析完成")
        
        return {
            'file_path': file_path,
            'note_count': len(pitch_list),
            'intervallic_ambitus': intervallic_ambitus,
            'local_volatility': d1_rms,
            'local_volatility_details': {
                'd1_rms': d1_rms,
                'd2_rms': d2_rms,
                'rms_ratio': rms_ratio,
                'volatility_type': volatility_type,
                'ornament_pattern': ornament_pattern
            },
            'dynamics_analysis': dynamics_result,
            'triad_significance': triad_significance,
            'hierarchical_orthogonality': hierarchical_orthogonality
        }
    
    def analyze_all_works_with_hierarchical_orthogonality(self):
        """分析所有作品（包含分层正交性分析）"""
        print("🎼 增强版中国音乐分析系统（包含分层正交性分析）")
        print("=" * 80)
        
        # 查找音乐文件 - 支持多个目录
        music_files = []
        search_directories = [
            '.',  # 当前目录
            './midi_files',  # midi_files文件夹
            './music',  # music文件夹
            './data',  # data文件夹
            './songs',  # songs文件夹
        ]
        
        search_patterns = ['*.mid', '*.midi', '*.csv']
        
        print("🔍 搜索音乐文件...")
        for directory in search_directories:
            if os.path.exists(directory):
                print(f"   检查目录: {directory}")
                for pattern in search_patterns:
                    search_path = os.path.join(directory, pattern)
                    files = glob.glob(search_path)
                    if files:
                        print(f"     找到 {len(files)} 个 {pattern} 文件")
                        music_files.extend(files)
        
        # 过滤出MIDI文件
        midi_files = [f for f in music_files if f.endswith(('.mid', '.midi'))]
        
        if not midi_files:
            print("\n❌ 未找到MIDI文件")
            print("💡 请将MIDI文件放在以下任一目录中:")
            print("   - 当前目录 (/Users/<USER>/Desktop/AI音乐/)")
            print("   - ./midi_files/ 文件夹")
            print("   - ./music/ 文件夹")
            print("   - ./data/ 文件夹")
            print("   - ./songs/ 文件夹")
            print("\n🧪 现在使用测试数据进行演示:")
            self.run_test_analysis_with_hierarchical_orthogonality()
            return
        
        print(f"📁 找到 {len(midi_files)} 个音乐文件")
        
        # 分析每个文件
        per_work_results = []
        for file_path in midi_files:
            result = self.analyze_single_work_with_hierarchical_orthogonality(file_path)
            if result:
                per_work_results.append(result)
        
        if per_work_results:
            print(f"\n📊 成功分析了 {len(per_work_results)} 个作品")
            self.print_summary_statistics_with_hierarchical_orthogonality(per_work_results)
        else:
            print("❌ 没有成功分析的作品")
    
    def run_test_analysis_with_hierarchical_orthogonality(self):
        """运行测试分析（包含分层正交性分析）"""
        print("🧪 运行测试分析（包含分层正交性分析）")
        
        test_melodies = [
            {
                'name': '平滑音阶',
                'pitches': [60, 62, 64, 65, 67, 69, 71, 72, 74, 76, 77, 79, 81, 83, 84]
            },
            {
                'name': '跳跃旋律',
                'pitches': [60, 72, 48, 75, 45, 78, 42, 81, 39, 84, 36, 87, 33, 90, 30]
            },
            {
                'name': '三音组模式',
                'pitches': [60, 62, 61, 63, 62, 64, 63, 65, 64, 66, 65, 67, 66, 68, 67]
            },
            {
                'name': '吸引子主导',
                'pitches': [60, 61, 60, 62, 60, 61, 60, 63, 60, 61, 60, 62, 60, 64, 60]
            },
            {
                'name': '混沌模式',
                'pitches': [60, 67, 55, 72, 48, 69, 52, 74, 46, 71, 49, 76, 44, 73, 51]
            }
        ]
        
        per_work_results = []
        for melody in test_melodies:
            print(f"\n🎵 分析测试旋律: {melody['name']}")
            self.pitch_series = pd.Series(melody['pitches'])
            
            result = self.analyze_single_work_with_hierarchical_orthogonality(f"test_{melody['name']}.mid")
            if result:
                per_work_results.append(result)
        
        if per_work_results:
            print(f"\n📊 使用 {len(per_work_results)} 个测试样本进行分析")
            self.print_summary_statistics_with_hierarchical_orthogonality(per_work_results)
    
    def print_summary_statistics_with_hierarchical_orthogonality(self, per_work_results):
        """打印摘要统计（包含分层正交性分析）"""
        print("\n" + "="*60)
        print("🎼 平滑度收敛分析摘要报告（包含分层正交性分析）")
        print("="*60)
        
        # 调用父类的统计方法
        super().print_summary_statistics_with_triads(per_work_results)
        
        # 添加分层正交性分析汇总
        print(f"\n🔄 分层正交性分析汇总:")
        hierarchical_report = self.hierarchical_orthogonality_analyzer.generate_summary_report(per_work_results)
        print(hierarchical_report)
        
        print(f"\n🎼 完整分析框架验证完成！")
        print(f"✅ 音程均幅 (Intervallic_Ambitus) 分析")
        print(f"✅ 局部音程波动性 (Local_Volatility) 分析") 
        print(f"✅ 旋律动力系统分析")
        print(f"✅ 严格三音组核心地位分析")
        print(f"✅ 分层正交性分析")
        print(f"下一步：基于分层正交性理论的深度特征挖掘")


def main_with_hierarchical_orthogonality():
    """主函数（包含分层正交性分析）"""
    try:
        analyzer = EnhancedChineseMusicAnalyzerWithHierarchicalOrthogonality()
        analyzer.analyze_all_works_with_hierarchical_orthogonality()
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        print("🧪 建议检查文件路径或使用测试数据")



# 更新主函数调用以支持分层正交性分析
if __name__ == "__main__":
    print("�� 增强版中国音乐分析系统")
    print("选择分析模式:")
    print("1. 基础分析（音程均幅 + 局部波动性 + 动力系统）")
    print("2. 完整分析（包含严格三音组核心地位分析）")
    print("3. 高级分析（包含分层正交性分析）")
    
    try:
        # 默认使用高级分析
        print("🚀 启动高级分析模式（包含分层正交性分析）")
        main_with_hierarchical_orthogonality()
    except Exception as e:
        print(f"❌ 高级分析失败: {e}")
        print("🔄 回退到完整分析模式")
        try:
            main_with_triads()
        except Exception as e2:
            print(f"❌ 完整分析也失败: {e2}")
            print("🔄 回退到基础分析模式")
            try:
                analyzer = EnhancedChineseMusicAnalyzer()
                analyzer.analyze_all_works()
            except Exception as e3:
                print(f"❌ 基础分析也失败: {e3}")
                print("🧪 建议检查文件路径或依赖库")


# ===== 第四部分：相空间轨迹图和动力系统可视化 =====

class MelodyDynamicsSystem:
    """动力系统分析器 - 增强版"""
    
    def __init__(self, time_window_ratio=0.1, potential_kernel_width=2.0):
        """
        初始化动力系统分析器
        
        Args:
            time_window_ratio: 时间窗比例（用于导数计算）
            potential_kernel_width: 势能场核宽度（用于吸引子分析）
        """
        self.time_window_ratio = time_window_ratio
        self.potential_kernel_width = potential_kernel_width
    
    def _compute_derivatives(self, pitch_series):
        """
        计算音高的一阶导数（速度）和二阶导数（加速度）
        
        Args:
            pitch_series: 音高序列
            
        Returns:
            tuple: (velocity, acceleration)
        """
        pitch_array = np.array(pitch_series)
        n = len(pitch_array)
        
        # 计算时间窗大小
        window_size = max(1, int(n * self.time_window_ratio))
        
        # 一阶导数（速度）- 使用中心差分
        velocity = np.zeros(n)
        for i in range(n):
            if i == 0:
                velocity[i] = pitch_array[1] - pitch_array[0] if n > 1 else 0.0
            elif i == n - 1:
                velocity[i] = pitch_array[n-1] - pitch_array[n-2]
            else:
                # 中心差分
                velocity[i] = (pitch_array[i+1] - pitch_array[i-1]) / 2.0
        
        # 二阶导数（加速度）- 对速度再求导
        acceleration = np.zeros(n)
        for i in range(n):
            if i == 0:
                acceleration[i] = velocity[1] - velocity[0] if n > 1 else 0.0
            elif i == n - 1:
                acceleration[i] = velocity[n-1] - velocity[n-2]
            else:
                acceleration[i] = (velocity[i+1] - velocity[i-1]) / 2.0
        
        return velocity, acceleration
    
    def reconstruct_phase_space(self, pitch_series):
        """
        重建相空间轨迹
        
        Args:
            pitch_series: 音高序列
            
        Returns:
            np.ndarray: n×3矩阵（位置、速度、加速度）
        """
        pitch_array = np.array(pitch_series)
        velocity, acceleration = self._compute_derivatives(pitch_series)
        
        # 构建相空间矩阵
        phase_space = np.column_stack([pitch_array, velocity, acceleration])
        
        return phase_space
    
    def calculate_attractor_strength(self, pitch_series):
        """
        计算吸引子强度（势阱深度）
        
        Args:
            pitch_series: 音高序列
            
        Returns:
            float: 吸引子强度（0-1之间，越大表示吸引子越强）
        """
        pitch_array = np.array(pitch_series)
        
        # 计算音高分布的核密度估计
        pitch_range = np.max(pitch_array) - np.min(pitch_array)
        if pitch_range == 0:
            return 1.0  # 完全静态，最强吸引子
        
        # 使用高斯核估计势能场
        grid_points = np.linspace(np.min(pitch_array), np.max(pitch_array), 50)
        density = np.zeros_like(grid_points)
        
        for i, grid_point in enumerate(grid_points):
            # 计算每个网格点的密度
            distances = np.abs(pitch_array - grid_point)
            weights = np.exp(-distances**2 / (2 * self.potential_kernel_width**2))
            density[i] = np.sum(weights)
        
        # 归一化密度
        density = density / np.sum(density)
        
        # 计算吸引子强度：最大密度值
        attractor_strength = np.max(density)
        
        # 归一化到0-1范围
        return min(1.0, attractor_strength * len(pitch_array) / 10.0)
    
    def estimate_lyapunov_exponent(self, pitch_series):
        """
        估计最大李雅普诺夫指数
        
        Args:
            pitch_series: 音高序列
            
        Returns:
            float: 李雅普诺夫指数估计值
        """
        if len(pitch_series) < 4:
            return 0.0
        
        velocity, acceleration = self._compute_derivatives(pitch_series)
        
        # 计算相邻轨迹点的发散率
        divergences = []
        
        for i in range(len(pitch_series) - 1):
            # 当前状态向量
            state_i = np.array([pitch_series[i], velocity[i], acceleration[i]])
            state_j = np.array([pitch_series[i+1], velocity[i+1], acceleration[i+1]])
            
            # 计算状态向量的距离
            distance = np.linalg.norm(state_j - state_i)
            
            if distance > 1e-10:  # 避免除零
                divergences.append(np.log(distance))
        
        if not divergences:
            return 0.0
        
        # 李雅普诺夫指数是发散率的平均值
        lyapunov_exponent = np.mean(divergences)
        
        return lyapunov_exponent
    
    def analyze_melody_stability(self, pitch_series):
        """
        综合分析旋律稳定性 - 基于增强动力系统理论
        
        Args:
            pitch_series: 音高序列
            
        Returns:
            dict: 包含吸引子强度、李指数、稳定性评分的字典
        """
        if len(pitch_series) < 3:
            return {
                'attractor_strength': 0.0,
                'lyapunov_exponent': 0.0,
                'stability_score': 0.0,
                'phase_space': np.array([]),
                'stability_classification': 'Insufficient Data'
            }
        
        # 使用新的吸引子动力学分析
        attractor_dynamics = self.estimate_attractor_dynamics(pitch_series)
        
        if attractor_dynamics and 'error' not in attractor_dynamics:
            # 从自回归模型获取吸引子强度
            attractor_strength = abs(attractor_dynamics['global_attractor_strength'])
            regression_quality = attractor_dynamics['regression_quality']
        else:
            # 备用方法
            attractor_strength = self.calculate_attractor_strength(pitch_series)
            regression_quality = 0.0
        
        # 计算李雅普诺夫指数
        lyapunov_exponent = self.estimate_lyapunov_exponent(pitch_series)
        
        # 重建相空间
        phase_space = self.reconstruct_phase_space(pitch_series)
        
        # 计算综合稳定性评分（基于动力系统理论）
        # 稳定性 = 高吸引子强度 + 低李雅普诺夫指数 + 高回归质量
        stability_score = (attractor_strength * 0.4 + 
                          max(0, (1.0 - abs(lyapunov_exponent))) * 0.3 + 
                          regression_quality * 0.3)
        stability_score = max(0.0, min(1.0, stability_score))
        
        # 稳定性分类（英文）
        if stability_score > 0.7:
            stability_classification = "Highly Stable"
        elif stability_score > 0.4:
            stability_classification = "Moderately Stable"
        else:
            stability_classification = "Unstable"
        
        result = {
            'attractor_strength': attractor_strength,
            'lyapunov_exponent': lyapunov_exponent,
            'stability_score': stability_score,
            'phase_space': phase_space,
            'stability_classification': stability_classification
        }
        
        # 如果有吸引子动力学信息，添加额外指标
        if attractor_dynamics and 'error' not in attractor_dynamics:
            result['lambda_strength'] = attractor_dynamics['global_attractor_strength']
            result['regression_quality'] = regression_quality
            result['attractor_position'] = attractor_dynamics['global_attractor_position']
        
        return result
    def plot_phase_portrait(self, pitch_series, save_path=None, title="旋律相空间轨迹"):
        """
        绘制3D相空间轨迹图
        
        Args:
            pitch_series: 音高序列
            save_path: 保存路径
            title: 图表标题
        """
        if not VISUALIZATION_AVAILABLE:
            print("⚠️ 可视化库未安装，跳过相空间轨迹图生成")
            return
        
        try:
            # 重建相空间
            phase_space = self.reconstruct_phase_space(pitch_series)
            
            if len(phase_space) < 2:
                print("⚠️ 数据点不足，无法绘制相空间轨迹")
                return
            
            # 创建3D图
            fig = plt.figure(figsize=(12, 9))
            ax = fig.add_subplot(111, projection='3d')
            
            # 提取坐标
            positions = phase_space[:, 0]
            velocities = phase_space[:, 1]
            accelerations = phase_space[:, 2]
            
            # 绘制轨迹线
            ax.plot(positions, velocities, accelerations, 
                   'b-', linewidth=2, alpha=0.7, label='轨迹')
            
            # 标记起点和终点
            ax.scatter(positions[0], velocities[0], accelerations[0], 
                      c='green', s=100, marker='o', label='起点')
            ax.scatter(positions[-1], velocities[-1], accelerations[-1], 
                      c='red', s=100, marker='s', label='终点')
            
            # 绘制轨迹点
            colors = plt.cm.viridis(np.linspace(0, 1, len(positions)))
            ax.scatter(positions, velocities, accelerations, 
                      c=colors, s=30, alpha=0.6)
            
            # 设置标签和标题
            ax.set_xlabel('位置 (音高)', fontsize=12)
            ax.set_ylabel('速度 (音高变化率)', fontsize=12)
            ax.set_zlabel('加速度 (音高加速度)', fontsize=12)
            ax.set_title(title, fontsize=14, fontweight='bold')
            
            # 添加图例
            ax.legend()
            
            # 添加网格
            ax.grid(True, alpha=0.3)
            
            # 计算并显示稳定性信息
            stability_result = self.analyze_melody_stability(pitch_series)
            info_text = f"吸引子强度: {stability_result['attractor_strength']:.3f}\n"
            info_text += f"李雅普诺夫指数: {stability_result['lyapunov_exponent']:.3f}\n"
            info_text += f"稳定性评分: {stability_result['stability_score']:.3f}\n"
            info_text += f"稳定性分类: {stability_result['stability_classification']}"
            
            # 在图上添加文本信息
            ax.text2D(0.02, 0.98, info_text, transform=ax.transAxes, 
                     fontsize=10, verticalalignment='top',
                     bbox=dict(boxstyle="round,pad=0.3", facecolor="wheat", alpha=0.8))
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ 相空间轨迹图已保存至: {save_path}")
            else:
                plt.show()
                
        except Exception as e:
            print(f"⚠️ 相空间轨迹图生成失败: {e}")
    
    def plot_dynamics_time_series(self, pitch_series, save_path=None, title="动力学时间序列"):
        """
        绘制动力学指标的时间序列图
        
        Args:
            pitch_series: 音高序列
            save_path: 保存路径
            title: 图表标题
        """
        if not VISUALIZATION_AVAILABLE:
            print("⚠️ 可视化库未安装，跳过动力学时间序列图生成")
            return
        
        try:
            velocity, acceleration = self._compute_derivatives(pitch_series)
            time_points = np.arange(len(pitch_series))
            
            # 创建子图
            fig, axes = plt.subplots(3, 1, figsize=(12, 10))
            
            # 位置（音高）
            axes[0].plot(time_points, pitch_series, 'b-', linewidth=2, label='音高')
            axes[0].set_ylabel('音高', fontsize=12)
            axes[0].set_title('位置（音高）', fontsize=12)
            axes[0].grid(True, alpha=0.3)
            axes[0].legend()
            
            # 速度
            axes[1].plot(time_points, velocity, 'g-', linewidth=2, label='速度')
            axes[1].set_ylabel('速度', fontsize=12)
            axes[1].set_title('速度（音高变化率）', fontsize=12)
            axes[1].grid(True, alpha=0.3)
            axes[1].legend()
            
            # 加速度
            axes[2].plot(time_points, acceleration, 'r-', linewidth=2, label='加速度')
            axes[2].set_ylabel('加速度', fontsize=12)
            axes[2].set_xlabel('时间点', fontsize=12)
            axes[2].set_title('加速度（音高加速度）', fontsize=12)
            axes[2].grid(True, alpha=0.3)
            axes[2].legend()
            
            plt.suptitle(title, fontsize=14, fontweight='bold')
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ 动力学时间序列图已保存至: {save_path}")
            else:
                plt.show()
                
        except Exception as e:
            print(f"⚠️ 动力学时间序列图生成失败: {e}")




    def estimate_attractor_dynamics(self, pitch_series):
        """使用自回归模型估计吸引子动力学"""
        if len(pitch_series) < 3:
            return None
        
        # 假设全局吸引子位置p0为音高中位数
        p0 = np.median(pitch_series)
        
        # 准备回归数据：dp(t) = lambda * (p0 - p(t-1)) + epsilon
        dp = []  # 因变量：dp(t) = p(t) - p(t-1)
        X = []   # 自变量：p0 - p(t-1)
        
        for i in range(1, len(pitch_series)):
            dp.append(pitch_series[i] - pitch_series[i-1])
            X.append(p0 - pitch_series[i-1])
        
        # 线性回归
        X = np.array(X).reshape(-1, 1)
        dp = np.array(dp)
        
        try:
            # 尝试使用sklearn
            from sklearn.linear_model import LinearRegression
            model = LinearRegression()
            model.fit(X, dp)
            lambda_ = model.coef_[0]  # 回归强度
            
            # 计算每个时间点的残差（从第二个音符开始）
            residuals = dp - model.predict(X)
            
            # 计算回归质量（R²）
            ss_res = np.sum(residuals ** 2)
            ss_tot = np.sum((dp - np.mean(dp)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0.0
            
        except ImportError:
            # 如果sklearn不可用，使用简化的最小二乘法
            X_flat = X.flatten()
            if np.var(X_flat) > 1e-10:
                lambda_ = np.cov(X_flat, dp)[0, 1] / np.var(X_flat)
            else:
                lambda_ = 0.0
            
            predicted_dp = lambda_ * X_flat
            residuals = dp - predicted_dp
            
            # 简化的R²计算
            ss_res = np.sum(residuals ** 2)
            ss_tot = np.sum((dp - np.mean(dp)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0.0
        
        return {
            'global_attractor_position': p0,
            'global_attractor_strength': lambda_,
            'residuals': residuals,  # 长度=len(pitch_series)-1
            'regression_quality': r_squared
        }

    def analyze_dynamics(self, pitch_series):
        """分析旋律动力系统特征 - 兼容性方法"""
        if len(pitch_series) < 3:
            return {'error': 'insufficient_data_for_dynamics_analysis'}

        try:
            # 使用新的稳定性分析方法
            stability_result = self.analyze_melody_stability(pitch_series)

            # 计算导数
            velocity, acceleration = self._compute_derivatives(pitch_series)

            # 计算曲率
            curvature = np.zeros(len(velocity))
            for i in range(len(velocity)):
                v = velocity[i]
                a = acceleration[i]
                if abs(v) > 1e-5:
                    curvature[i] = abs(a) / (abs(v)**2)
                else:
                    curvature[i] = 0.0

            # 构建兼容的返回格式
            return {
                'velocity': velocity.tolist(),
                'acceleration': acceleration.tolist(),
                'attractors': {
                    'positions': [stability_result.get('attractor_position', np.median(pitch_series))],
                    'distances': [abs(p - stability_result.get('attractor_position', np.median(pitch_series))) for p in pitch_series],
                    'strength': stability_result['attractor_strength'],
                    'count': 1,
                    'avg_distance': np.mean([abs(p - stability_result.get('attractor_position', np.median(pitch_series))) for p in pitch_series])
                },
                'curvature': curvature.tolist(),
                'time_scales': {
                    'micro_scale': {'avg_zero_crossing_rate': 0.0, 'avg_envelope_depth': 0.0, 'avg_perceived_variation': 0.0, 'avg_local_complexity': 0.0},
                    'meso_scale': {'avg_zero_crossing_rate': 0.0, 'avg_envelope_depth': 0.0, 'avg_perceived_variation': 0.0, 'avg_local_complexity': 0.0},
                    'macro_scale': {'avg_zero_crossing_rate': 0.0, 'avg_envelope_depth': 0.0, 'avg_perceived_variation': 0.0, 'avg_local_complexity': 0.0}
                },
                'stability': stability_result['stability_classification'],
                'dynamics_metrics': {
                    'mean_velocity': float(np.mean(np.abs(velocity))),
                    'velocity_variance': float(np.var(velocity)),
                    'max_velocity': float(np.max(np.abs(velocity))),
                    'mean_acceleration': float(np.mean(np.abs(acceleration))),
                    'acceleration_variance': float(np.var(acceleration)),
                    'max_acceleration': float(np.max(np.abs(acceleration))),
                    'mean_curvature': float(np.mean(curvature)),
                    'curvature_variance': float(np.var(curvature)),
                    'max_curvature': float(np.max(curvature)),
                    'attractor_count': 1,
                    'attractor_strength': stability_result['attractor_strength'],
                    'mean_attractor_distance': np.mean([abs(p - stability_result.get('attractor_position', np.median(pitch_series))) for p in pitch_series]),
                    'system_energy': float(np.mean(velocity**2 + acceleration**2)),
                    'phase_space_volume': float(np.std(velocity) * np.std(acceleration)),
                    'lyapunov_proxy': stability_result['lyapunov_exponent']
                },
                'system_type': stability_result['stability_classification']
            }

        except Exception as e:
            return {'error': f'dynamics_analysis_failed: {e}'}
# ===== 步骤2：修改主分析器类集成动力系统分析 =====

class EnhancedChineseMusicAnalyzerWithDynamicsVisualization(EnhancedChineseMusicAnalyzerWithHierarchicalOrthogonality):
    """增强版中国音乐分析器（包含动力系统可视化）"""
    
    def __init__(self):
        super().__init__()
        # 初始化动力系统分析器
        self.dynamics_analyzer = MelodyDynamicsSystem(
            time_window_ratio=0.1, 
            potential_kernel_width=2.0
        )
    
    def analyze_per_work_stability(self, file_path):
        """分析单个作品的稳定性（替代原smoothness分析）"""
        print(f"🎵 分析文件: {os.path.basename(file_path)}")
        
        # 如果是测试数据，直接使用已设置的pitch_series
        if file_path.startswith('test_') and self.pitch_series is not None:
            pitch_list = self.pitch_series.tolist()
        else:
            # 加载文件
            success = False
            if file_path.endswith('.mid') or file_path.endswith('.midi'):
                success = self.load_midi_file(file_path)
            elif file_path.endswith('.csv'):
                success = self.load_csv_file(file_path)
            
            if not success or self.pitch_series is None or len(self.pitch_series) < 3:
                print(f"   ❌ 文件加载失败或数据不足")
                return None
            
            pitch_list = self.pitch_series.tolist()
        
        # 1. 动力系统稳定性分析（替代原有的方法1和方法2）
        stability_result = self.dynamics_analyzer.analyze_melody_stability(pitch_list)
        
        # 2. 局部波动性分析（保留作为静态指标）
        d1_rms = self.local_volatility_analyzer.calculate_d1_rms(pitch_list)
        d2_rms = self.local_volatility_analyzer.calculate_d2_rms(pitch_list)
        rms_ratio = self.local_volatility_analyzer.calculate_rms_ratio(d1_rms, d2_rms)
        volatility_type = self.local_volatility_analyzer.classify_volatility_type(rms_ratio)
        ornament_pattern = self.local_volatility_analyzer.identify_ornament_pattern(d1_rms, d2_rms, rms_ratio)
        
        # 3. 轮廓复杂度计算（静态指标）
        contour_complexity = self._calculate_contour_complexity(pitch_list)
        
        # 4. 三音组核心地位分析
        triad_significance = self.triad_analyzer.analyze_significance(pitch_list)
        
        # 5. 重构双方法分析：动态指标 vs 静态指标
        dual_method_analysis = self._analyze_dynamic_vs_static_orthogonality(
            stability_result, d1_rms, contour_complexity
        )
        
        # 6. 分层正交性分析
        global_metrics = {
            'attractor_strength': stability_result['attractor_strength'],
            'lyapunov_proxy': stability_result['lyapunov_exponent'],
            'system_energy': stability_result['stability_score'],
            'mean_curvature': contour_complexity
        }
        
        local_metrics = [{
            'intervallic_ambitus': stability_result['attractor_strength'],  # 动态指标
            'local_volatility': d1_rms  # 静态指标
        }]
        
        hierarchical_orthogonality = self.hierarchical_orthogonality_analyzer.analyze_orthogonality(
            global_metrics, local_metrics
        )
        
        # 7. 生成可视化
        output_dir = f"analysis_results/{os.path.splitext(os.path.basename(file_path))[0]}"
        os.makedirs(output_dir, exist_ok=True)
        
        # 相空间轨迹图
        phase_portrait_path = os.path.join(output_dir, "phase_portrait.png")
        self.dynamics_analyzer.plot_phase_portrait(
            pitch_list, 
            save_path=phase_portrait_path,
            title=f"相空间轨迹 - {os.path.basename(file_path)}"
        )
        
        # 动力学时间序列图
        dynamics_series_path = os.path.join(output_dir, "dynamics_time_series.png")
        self.dynamics_analyzer.plot_dynamics_time_series(
            pitch_list,
            save_path=dynamics_series_path,
            title=f"动力学时间序列 - {os.path.basename(file_path)}"
        )
        
        # 打印分析结果
        print(f"      🌊 动力系统稳定性分析:")
        print(f"        吸引子强度: {stability_result['attractor_strength']:.4f}")
        print(f"        李雅普诺夫指数: {stability_result['lyapunov_exponent']:.4f}")
        print(f"        稳定性评分: {stability_result['stability_score']:.4f}")
        print(f"        稳定性分类: {stability_result['stability_classification']}")
        
        print(f"      📊 静态指标分析:")
        print(f"        局部RMS: {d1_rms:.4f}")
        print(f"        轮廓复杂度: {contour_complexity:.4f}")
        print(f"        波动性类型: {volatility_type}")
        print(f"        装饰音模式: {ornament_pattern}")
        
        print(f"      🎯 三音组地位分析:")
        print(f"        结构密度: {triad_significance['structural_density']:.3f}")
        print(f"        轮廓控制力: {triad_significance['contour_control']:.3f}")
        print(f"        动态贡献度: {triad_significance['dynamic_contribution']:.3f}")
        print(f"        稳定性指数: {triad_significance['stability_index']:.3f}")
        
        print(f"      🔄 双方法正交性分析:")
        print(f"        动态vs静态相关性: {dual_method_analysis['correlation']:.4f}")
        print(f"        正交性评估: {dual_method_analysis['assessment']}")
        
        print(f"      📈 可视化输出:")
        print(f"        相空间轨迹图: {phase_portrait_path}")
        print(f"        动力学时间序列: {dynamics_series_path}")
        
        print(f"   ✅ 分析完成")
        
        return {
            'file_path': file_path,
            'note_count': len(pitch_list),
            'stability_analysis': stability_result,
            'static_analysis': {
                'local_rms': d1_rms,
                'd2_rms': d2_rms,
                'rms_ratio': rms_ratio,
                'volatility_type': volatility_type,
                'ornament_pattern': ornament_pattern,
                'contour_complexity': contour_complexity
            },
            'triad_significance': triad_significance,
            'dual_method_analysis': dual_method_analysis,
            'hierarchical_orthogonality': hierarchical_orthogonality,
            'visualization_paths': {
                'phase_portrait': phase_portrait_path,
                'dynamics_series': dynamics_series_path
            }
        }
    
    def _calculate_contour_complexity(self, pitch_series):
        """计算轮廓复杂度"""
        if len(pitch_series) < 3:
            return 0.0
        
        # 计算方向变化次数
        direction_changes = 0
        for i in range(1, len(pitch_series) - 1):
            prev_diff = pitch_series[i] - pitch_series[i-1]
            next_diff = pitch_series[i+1] - pitch_series[i]
            
            if prev_diff * next_diff < 0:  # 方向改变
                direction_changes += 1
        
        # 归一化复杂度
        max_possible_changes = len(pitch_series) - 2
        complexity = direction_changes / max_possible_changes if max_possible_changes > 0 else 0.0
        
        return complexity
    
    def _analyze_dynamic_vs_static_orthogonality(self, stability_result, local_rms, contour_complexity):
        """分析动态指标与静态指标的正交性"""
        # 提取动态指标
        attractor_strength = stability_result['attractor_strength']
        lyapunov_exponent = abs(stability_result['lyapunov_exponent'])  # 取绝对值
        
        # 构建动态指标向量和静态指标向量
        dynamic_indicators = [attractor_strength, lyapunov_exponent]
        static_indicators = [local_rms, contour_complexity]
        
        # 计算综合动态指标和静态指标
        dynamic_composite = np.mean(dynamic_indicators)
        static_composite = np.mean(static_indicators)
        
        # 由于只有单个样本，相关性计算需要基于指标的差异性
        # 这里使用指标值的差异来评估正交性
        indicator_diff = abs(dynamic_composite - static_composite)
        
        # 将差异转换为相关性（差异越大，相关性越低）
        correlation = max(-1.0, min(1.0, 1.0 - 2.0 * indicator_diff))
        
        # 正交性评估
        if abs(correlation) < 0.3:
            assessment = "高度正交"
        elif abs(correlation) < 0.6:
            assessment = "中度相关"
        else:
            assessment = "强相关"
        
        return {
            'correlation': correlation,
            'assessment': assessment,
            'dynamic_composite': dynamic_composite,
            'static_composite': static_composite,
            'dynamic_indicators': {
                'attractor_strength': attractor_strength,
                'lyapunov_exponent': lyapunov_exponent
            },
            'static_indicators': {
                'local_rms': local_rms,
                'contour_complexity': contour_complexity
            }
        }
    
    def analyze_all_works_with_dynamics_visualization(self):
        """分析所有作品（包含动力系统可视化）"""
        print("🎼 增强版中国音乐分析系统（包含动力系统可视化）")
        print("=" * 80)
        
        # 查找音乐文件
        music_files = []
        search_directories = [
            '.',  # 当前目录
            './midi_files',  # midi_files文件夹
            './music',  # music文件夹
            './data',  # data文件夹
            './songs',  # songs文件夹
        ]
        
        search_patterns = ['*.mid', '*.midi', '*.csv']
        
        print("🔍 搜索音乐文件...")
        for directory in search_directories:
            if os.path.exists(directory):
                print(f"   检查目录: {directory}")
                for pattern in search_patterns:
                    search_path = os.path.join(directory, pattern)
                    files = glob.glob(search_path)
                    if files:
                        print(f"     找到 {len(files)} 个 {pattern} 文件")
                        music_files.extend(files)
        
        # 过滤出MIDI文件
        midi_files = [f for f in music_files if f.endswith(('.mid', '.midi'))]
        
        if not midi_files:
            print("\n❌ 未找到MIDI文件")
            print("💡 请将MIDI文件放在以下任一目录中:")
            print("   - 当前目录 (/Users/<USER>/Desktop/AI音乐/)")
            print("   - ./midi_files/ 文件夹")
            print("   - ./music/ 文件夹")
            print("   - ./data/ 文件夹")
            print("   - ./songs/ 文件夹")
            print("\n🧪 现在使用测试数据进行演示:")
            self.run_test_analysis_with_dynamics_visualization()
            return
        
        print(f"📁 找到 {len(midi_files)} 个音乐文件")
        
        # 分析每个文件
        per_work_results = []
        for file_path in midi_files:
            result = self.analyze_per_work_stability(file_path)
            if result:
                per_work_results.append(result)
        
        if per_work_results:
            print(f"\n📊 成功分析了 {len(per_work_results)} 个作品")
            self.print_summary_statistics_with_dynamics_visualization(per_work_results)
        else:
            print("❌ 没有成功分析的作品")
    
    def run_test_analysis_with_dynamics_visualization(self):
        """运行测试分析（包含动力系统可视化）"""
        print("🧪 运行测试分析（包含动力系统可视化）")
        
        test_melodies = [
            {
                'name': '平滑音阶',
                'pitches': [60, 62, 64, 65, 67, 69, 71, 72, 74, 76, 77, 79, 81, 83, 84]
            },
            {
                'name': '跳跃旋律',
                'pitches': [60, 72, 48, 75, 45, 78, 42, 81, 39, 84, 36, 87, 33, 90, 30]
            },
            {
                'name': '三音组模式',
                'pitches': [60, 62, 61, 63, 62, 64, 63, 65, 64, 66, 65, 67, 66, 68, 67]
            },
            {
                'name': '吸引子主导',
                'pitches': [60, 61, 60, 62, 60, 61, 60, 63, 60, 61, 60, 62, 60, 64, 60]
            },
            {
                'name': '混沌模式',
                'pitches': [60, 67, 55, 72, 48, 69, 52, 74, 46, 71, 49, 76, 44, 73, 51]
            }
        ]
        
        per_work_results = []
        for melody in test_melodies:
            print(f"\n🎵 分析测试旋律: {melody['name']}")
            self.pitch_series = pd.Series(melody['pitches'])
            
            result = self.analyze_per_work_stability(f"test_{melody['name']}.mid")
            if result:
                per_work_results.append(result)
        
        if per_work_results:
            print(f"\n📊 使用 {len(per_work_results)} 个测试样本进行分析")
            self.print_summary_statistics_with_dynamics_visualization(per_work_results)
    
    def print_summary_statistics_with_dynamics_visualization(self, per_work_results):
        """打印摘要统计（包含动力系统可视化）"""
        print("\n" + "="*60)
        print("🎼 动力系统稳定性分析摘要报告")
        print("="*60)
        
        # 提取动态指标
        attractor_strengths = [r['stability_analysis']['attractor_strength'] for r in per_work_results]
        lyapunov_exponents = [r['stability_analysis']['lyapunov_exponent'] for r in per_work_results]
        stability_scores = [r['stability_analysis']['stability_score'] for r in per_work_results]
        
        # 提取静态指标
        local_rms_values = [r['static_analysis']['local_rms'] for r in per_work_results]
        contour_complexities = [r['static_analysis']['contour_complexity'] for r in per_work_results]
        
        # 双方法分析
        dual_correlations = [r['dual_method_analysis']['correlation'] for r in per_work_results]
        
        print(f"📊 基础统计:")
        print(f"   分析作品数: {len(per_work_results)} 首")
        
        print(f"\n🌊 动态指标统计:")
        print(f"   吸引子强度:")
        print(f"     均值: {np.mean(attractor_strengths):.4f}")
        print(f"     标准差: {np.std(attractor_strengths):.4f}")
        print(f"     范围: {np.min(attractor_strengths):.4f} ~ {np.max(attractor_strengths):.4f}")
        
        print(f"   李雅普诺夫指数:")
        print(f"     均值: {np.mean(lyapunov_exponents):.4f}")
        print(f"     标准差: {np.std(lyapunov_exponents):.4f}")
        print(f"     范围: {np.min(lyapunov_exponents):.4f} ~ {np.max(lyapunov_exponents):.4f}")
        
        print(f"   稳定性评分:")
        print(f"     均值: {np.mean(stability_scores):.4f}")
        print(f"     标准差: {np.std(stability_scores):.4f}")
        print(f"     范围: {np.min(stability_scores):.4f} ~ {np.max(stability_scores):.4f}")
        
        print(f"\n📈 静态指标统计:")
        print(f"   局部RMS:")
        print(f"     均值: {np.mean(local_rms_values):.4f}")
        print(f"     标准差: {np.std(local_rms_values):.4f}")
        print(f"     范围: {np.min(local_rms_values):.4f} ~ {np.max(local_rms_values):.4f}")
        
        print(f"   轮廓复杂度:")
        print(f"     均值: {np.mean(contour_complexities):.4f}")
        print(f"     标准差: {np.std(contour_complexities):.4f}")
        print(f"     范围: {np.min(contour_complexities):.4f} ~ {np.max(contour_complexities):.4f}")
        
        print(f"\n🔄 动态vs静态正交性分析:")
        print(f"   平均相关系数: {np.mean(dual_correlations):.4f}")
        print(f"   标准差: {np.std(dual_correlations):.4f}")
        print(f"   范围: {np.min(dual_correlations):.4f} ~ {np.max(dual_correlations):.4f}")
        
        # 正交性分布
        assessments = [r['dual_method_analysis']['assessment'] for r in per_work_results]
        assessment_counts = {}
        for assessment in assessments:
            assessment_counts[assessment] = assessment_counts.get(assessment, 0) + 1
        
        print(f"   正交性分布:")
        for assessment, count in assessment_counts.items():
            percentage = count / len(assessments) * 100
            print(f"     {assessment}: {count} 首 ({percentage:.1f}%)")
        
        # 稳定性分类统计
        stability_classifications = [r['stability_analysis']['stability_classification'] for r in per_work_results]
        stability_counts = {}
        for classification in stability_classifications:
            stability_counts[classification] = stability_counts.get(classification, 0) + 1
        
        print(f"\n🎯 稳定性分类分布:")
        for classification, count in stability_counts.items():
            percentage = count / len(stability_classifications) * 100
            print(f"   {classification}: {count} 首 ({percentage:.1f}%)")
        
        print(f"\n📈 可视化输出:")
        print(f"   每个作品都生成了:")
        print(f"     • 3D相空间轨迹图")
        print(f"     • 动力学时间序列图")
        print(f"   保存在 analysis_results/ 目录下")
        
        print(f"\n✅ 核心结论:")
        avg_correlation = np.mean([abs(c) for c in dual_correlations])
        if avg_correlation < 0.3:
            print(f"   ✅ 动态指标与静态指标高度正交，双方法分析有效")
        elif avg_correlation < 0.6:
            print(f"   ⚠️ 动态指标与静态指标中度相关")
        else:
            print(f"   ❌ 动态指标与静态指标强相关")
        
        # 稳定性洞察
        avg_stability = np.mean(stability_scores)
        if avg_stability > 0.7:
            print(f"   ✅ 整体表现为高稳定性旋律系统")
        elif avg_stability > 0.4:
            print(f"   ⚠️ 整体表现为中等稳定性旋律系统")
        else:
            print(f"   ❌ 整体表现为不稳定旋律系统")
        
        print(f"\n🎯 理论意义:")
        print(f"  • 动力系统分析提供了旋律稳定性的量化评估")
        print(f"  • 相空间轨迹图揭示了旋律的内在动力学结构")
        print(f"  • 动态vs静态指标的正交性验证了分析方法的有效性")
        print(f"  • 为'三音组构成中国传统音乐风格'提供动力学支撑")
        
        print(f"\n🎼 完整动力系统分析框架验证完成！")
        print(f"✅ 相空间轨迹重建")
        print(f"✅ 吸引子强度计算")
        print(f"✅ 李雅普诺夫指数估计")
        print(f"✅ 稳定性综合评分")
        print(f"✅ 3D可视化输出")
        print(f"✅ 动态vs静态正交性分析")


def main_with_dynamics_visualization():
    """主函数（包含动力系统可视化）"""
    try:
        analyzer = EnhancedChineseMusicAnalyzerWithDynamicsVisualization()
        analyzer.analyze_all_works_with_dynamics_visualization()
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        print("🧪 建议检查文件路径或使用测试数据")



# 更新主函数调用以支持动力系统可视化
if __name__ == "__main__":
    print("�� 增强版中国音乐分析系统")
    print("选择分析模式:")
    print("1. 基础分析（音程均幅 + 局部波动性 + 动力系统）")
    print("2. 完整分析（包含严格三音组核心地位分析）")
    print("3. 高级分析（包含分层正交性分析）")
    print("4. 专业分析（包含动力系统可视化和相空间轨迹图）")
    
    try:
        # 默认使用专业分析
        print("🚀 启动专业分析模式（包含动力系统可视化）")
        main_with_dynamics_visualization()
    except Exception as e:
        print(f"❌ 专业分析失败: {e}")
        print("🔄 回退到高级分析模式")
        try:
            main_with_hierarchical_orthogonality()
        except Exception as e2:
            print(f"❌ 高级分析也失败: {e2}")
            print("🔄 回退到完整分析模式")
            try:
                main_with_triads()
            except Exception as e3:
                print(f"❌ 完整分析也失败: {e3}")
                print("🔄 回退到基础分析模式")
                try:
                    analyzer = EnhancedChineseMusicAnalyzer()
                    analyzer.analyze_all_works()
                except Exception as e4:
                    print(f"❌ 基础分析也失败: {e4}")
                    print("🧪 建议检查文件路径或依赖库")


# ===== 第五部分：综合摘要报告框架 =====

# 首先添加必要的导入
try:
    from sklearn.decomposition import PCA
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
    print("✅ scikit-learn库加载成功")
except ImportError:
    SKLEARN_AVAILABLE = False
    print("⚠️ scikit-learn库未安装，将使用简化的PCA分析")

class ComprehensiveSummaryReporter:
    """综合摘要报告生成器 - 替代原步骤5的输出报告"""
    
    def __init__(self, orthogonality_analyzer):
        self.analyzer = orthogonality_analyzer
        self.summary = {
            'global_metrics': [],
            'local_orthogonality': [],
            'cross_level_effects': [],
            'triad_significance': [],
            'dynamics_analysis': []
        }
    
    def add_work_analysis(self, global_metrics, orthogonality_analysis, triad_significance=None, dynamics_analysis=None):
        """添加单首作品的分析结果"""
        self.summary['global_metrics'].append(global_metrics)
        self.summary['local_orthogonality'].append(
            orthogonality_analysis['local_orthogonality'])
        self.summary['cross_level_effects'].append(
            orthogonality_analysis['cross_level_orthogonality'])
        
        if triad_significance:
            self.summary['triad_significance'].append(triad_significance)
        if dynamics_analysis:
            self.summary['dynamics_analysis'].append(dynamics_analysis)
    
    def generate_summary_report(self):
        """生成综合摘要报告"""
        if not self.summary['global_metrics']:
            return {"error": "No analysis data available"}
        
        # 全局指标统计
        global_stats = self._calculate_global_metrics_stats()
        
        # 局部正交性统计
        local_ortho_stats = self._calculate_local_orthogonality_stats()
        
        # 跨层级效应统计
        cross_level_stats = self._calculate_cross_level_stats()
        
        # 三音组地位统计
        triad_stats = self._calculate_triad_significance_stats()
        
        # 动力系统统计
        dynamics_stats = self._calculate_dynamics_stats()
        
        # 全局指标间正交性分析（多作品PCA）
        global_ortho = self._analyze_global_orthogonality()
        
        return {
            'global_metrics_summary': global_stats,
            'local_orthogonality_summary': local_ortho_stats,
            'cross_level_effects_summary': cross_level_stats,
            'triad_significance_summary': triad_stats,
            'dynamics_analysis_summary': dynamics_stats,
            'global_orthogonality_analysis': global_ortho,
            'interpretation': self._generate_interpretation(
                global_stats, local_ortho_stats, cross_level_stats, 
                triad_stats, dynamics_stats, global_ortho)
        }
    
    def _calculate_global_metrics_stats(self):
        """计算全局指标统计特征"""
        attractor = [m.get('attractor_strength', 0.0) for m in self.summary['global_metrics']]
        lyapunov = [m.get('lyapunov_proxy', 0.0) for m in self.summary['global_metrics']]
        energy = [m.get('system_energy', 0.0) for m in self.summary['global_metrics']]
        curvature = [m.get('mean_curvature', 0.0) for m in self.summary['global_metrics']]
        
        return {
            'attractor_strength': self._calculate_stats(attractor),
            'lyapunov_proxy': self._calculate_stats(lyapunov),
            'system_energy': self._calculate_stats(energy),
            'mean_curvature': self._calculate_stats(curvature)
        }
    
    def _calculate_local_orthogonality_stats(self):
        """计算局部正交性统计特征"""
        correlations = [lo.get('correlation', 0.0) for lo in self.summary['local_orthogonality']]
        
        # 正交性比例
        high_ortho = sum(1 for c in correlations if abs(c) < 0.3)
        med_ortho = sum(1 for c in correlations if 0.3 <= abs(c) < 0.6)
        high_corr = sum(1 for c in correlations if abs(c) >= 0.6)
        total = len(correlations) if correlations else 1
        
        return {
            'correlation_stats': self._calculate_stats(correlations),
            'orthogonality_distribution': {
                'high_orthogonality': high_ortho / total,
                'medium_correlation': med_ortho / total,
                'high_correlation': high_corr / total
            }
        }
    
    def _calculate_cross_level_stats(self):
        """计算跨层级效应统计"""
        attractor_effects = {}
        stability_effects = {}
        energy_effects = {}
        
        for effect in self.summary['cross_level_effects']:
            # 吸引子效应
            ae = effect.get('attractor_effect', {})
            group = ae.get('group', '未知')
            if group not in attractor_effects:
                attractor_effects[group] = {'count': 0, 'intervallic_means': [], 'volatility_means': []}
            
            attractor_effects[group]['count'] += 1
            intervallic_dist = ae.get('intervallic_distribution', {})
            volatility_dist = ae.get('volatility_distribution', {})
            attractor_effects[group]['intervallic_means'].append(intervallic_dist.get('mean', 0.0))
            attractor_effects[group]['volatility_means'].append(volatility_dist.get('mean', 0.0))
            
            # 稳定性效应
            se = effect.get('stability_effect', {})
            level = se.get('stability_level', '未知')
            if level not in stability_effects:
                stability_effects[level] = {'count': 0, 'intervallic_means': [], 'volatility_means': []}
            
            stability_effects[level]['count'] += 1
            intervallic_dist = se.get('intervallic_distribution', {})
            volatility_dist = se.get('volatility_distribution', {})
            stability_effects[level]['intervallic_means'].append(intervallic_dist.get('mean', 0.0))
            stability_effects[level]['volatility_means'].append(volatility_dist.get('mean', 0.0))
            
            # 能量效应
            ee = effect.get('energy_effect', {})
            energy_level = ee.get('energy_level', '未知')
            if energy_level not in energy_effects:
                energy_effects[energy_level] = {'count': 0, 'intervallic_means': [], 'volatility_means': []}
            
            energy_effects[energy_level]['count'] += 1
            intervallic_dist = ee.get('intervallic_distribution', {})
            volatility_dist = ee.get('volatility_distribution', {})
            energy_effects[energy_level]['intervallic_means'].append(intervallic_dist.get('mean', 0.0))
            energy_effects[energy_level]['volatility_means'].append(volatility_dist.get('mean', 0.0))
        
        # 计算平均效应
        total_effects = len(self.summary['cross_level_effects']) if self.summary['cross_level_effects'] else 1
        
        ae_summary = {}
        for group, data in attractor_effects.items():
            ae_summary[group] = {
                'proportion': data['count'] / total_effects,
                'avg_intervallic': np.mean(data['intervallic_means']) if data['intervallic_means'] else 0.0,
                'avg_volatility': np.mean(data['volatility_means']) if data['volatility_means'] else 0.0
            }
        
        se_summary = {}
        for level, data in stability_effects.items():
            se_summary[level] = {
                'proportion': data['count'] / total_effects,
                'avg_intervallic': np.mean(data['intervallic_means']) if data['intervallic_means'] else 0.0,
                'avg_volatility': np.mean(data['volatility_means']) if data['volatility_means'] else 0.0
            }
        
        ee_summary = {}
        for level, data in energy_effects.items():
            ee_summary[level] = {
                'proportion': data['count'] / total_effects,
                'avg_intervallic': np.mean(data['intervallic_means']) if data['intervallic_means'] else 0.0,
                'avg_volatility': np.mean(data['volatility_means']) if data['volatility_means'] else 0.0
            }
        
        return {
            'attractor_effect_summary': ae_summary,
            'stability_effect_summary': se_summary,
            'energy_effect_summary': ee_summary
        }
    
    def _calculate_triad_significance_stats(self):
        """计算三音组地位统计"""
        if not self.summary['triad_significance']:
            return {'warning': 'No triad significance data available'}
        
        structural_densities = [t.get('structural_density', 0.0) for t in self.summary['triad_significance']]
        contour_controls = [t.get('contour_control', 0.0) for t in self.summary['triad_significance']]
        dynamic_contributions = [t.get('dynamic_contribution', 0.0) for t in self.summary['triad_significance']]
        stability_indices = [t.get('stability_index', 0.0) for t in self.summary['triad_significance']]
        
        return {
            'structural_density': self._calculate_stats(structural_densities),
            'contour_control': self._calculate_stats(contour_controls),
            'dynamic_contribution': self._calculate_stats(dynamic_contributions),
            'stability_index': self._calculate_stats(stability_indices)
        }
    
    def _calculate_dynamics_stats(self):
        """计算动力系统统计"""
        if not self.summary['dynamics_analysis']:
            return {'warning': 'No dynamics analysis data available'}
        
        attractor_strengths = [d.get('attractor_strength', 0.0) for d in self.summary['dynamics_analysis']]
        lyapunov_exponents = [d.get('lyapunov_exponent', 0.0) for d in self.summary['dynamics_analysis']]
        stability_scores = [d.get('stability_score', 0.0) for d in self.summary['dynamics_analysis']]
        
        # 稳定性分类统计
        classifications = [d.get('stability_classification', '未知') for d in self.summary['dynamics_analysis']]
        classification_counts = {}
        for classification in classifications:
            classification_counts[classification] = classification_counts.get(classification, 0) + 1
        
        total_classifications = len(classifications) if classifications else 1
        classification_proportions = {
            k: v / total_classifications for k, v in classification_counts.items()
        }
        
        return {
            'attractor_strength': self._calculate_stats(attractor_strengths),
            'lyapunov_exponent': self._calculate_stats(lyapunov_exponents),
            'stability_score': self._calculate_stats(stability_scores),
            'stability_classification_distribution': classification_proportions
        }
    
    def _analyze_global_orthogonality(self):
        """分析全局指标间的正交性（PCA）"""
        if len(self.summary['global_metrics']) < 3:
            return {"warning": "Insufficient works for PCA analysis"}
        
        # 准备数据矩阵
        metrics_matrix = []
        for work in self.summary['global_metrics']:
            metrics_matrix.append([
                work.get('attractor_strength', 0.0),
                work.get('lyapunov_proxy', 0.0),
                work.get('system_energy', 0.0),
                work.get('mean_curvature', 0.0)
            ])
        
        if not SKLEARN_AVAILABLE:
            # 简化的正交性分析
            metrics_array = np.array(metrics_matrix)
            correlation_matrix = np.corrcoef(metrics_array.T)
            
            # 计算平均相关系数作为正交性指标
            off_diagonal = []
            for i in range(correlation_matrix.shape[0]):
                for j in range(i+1, correlation_matrix.shape[1]):
                    off_diagonal.append(abs(correlation_matrix[i, j]))
            
            avg_correlation = np.mean(off_diagonal) if off_diagonal else 0.0
            
            return {
                'correlation_matrix': correlation_matrix.tolist(),
                'average_correlation': avg_correlation,
                'orthogonality_score': 1.0 - avg_correlation,
                'method': 'simplified_correlation_analysis'
            }
        
        try:
            # 标准化
            scaler = StandardScaler()
            scaled_metrics = scaler.fit_transform(metrics_matrix)
            
            # PCA分析
            pca = PCA(n_components=min(4, len(metrics_matrix)))
            pca.fit(scaled_metrics)
            
            return {
                'explained_variance_ratio': pca.explained_variance_ratio_.tolist(),
                'components': pca.components_.tolist(),
                'orthogonality_score': 1.0 - pca.explained_variance_ratio_[0],
                'method': 'PCA_analysis'
            }
        except Exception as e:
            return {"error": f"PCA analysis failed: {e}"}
    
    def _generate_interpretation(self, global_stats, local_ortho, cross_level, 
                               triad_stats, dynamics_stats, global_ortho):
        """生成报告解释文本"""
        report = "=== 中国传统音乐综合分析报告 ===\n\n"
        
        # 全局指标解释
        report += "🌊 全局动力系统指标统计:\n"
        if 'attractor_strength' in global_stats:
            report += f"- 吸引子强度: μ={global_stats['attractor_strength']['mean']:.3f}, σ={global_stats['attractor_strength']['std']:.3f}\n"
        if 'lyapunov_proxy' in global_stats:
            report += f"- 李雅普诺夫代理: μ={global_stats['lyapunov_proxy']['mean']:.3f}, σ={global_stats['lyapunov_proxy']['std']:.3f}\n"
        if 'system_energy' in global_stats:
            report += f"- 系统能量: μ={global_stats['system_energy']['mean']:.3f}, σ={global_stats['system_energy']['std']:.3f}\n"
        if 'mean_curvature' in global_stats:
            report += f"- 平均曲率: μ={global_stats['mean_curvature']['mean']:.3f}, σ={global_stats['mean_curvature']['std']:.3f}\n"
        report += "\n"
        
        # 局部正交性解释
        report += "🔄 局部特征正交性分析:\n"
        ortho_dist = local_ortho['orthogonality_distribution']
        report += f"- 高度正交比例: {ortho_dist['high_orthogonality']*100:.1f}%\n"
        report += f"- 中度相关比例: {ortho_dist['medium_correlation']*100:.1f}%\n"
        report += f"- 强相关比例: {ortho_dist['high_correlation']*100:.1f}%\n"
        
        correlation_stats = local_ortho['correlation_stats']
        if correlation_stats:
            report += f"- 平均相关系数: {correlation_stats['mean']:.4f}\n"
        report += "\n"
        
        # 跨层级效应解释
        report += "🎯 跨层级效应分析:\n"
        ae_summary = cross_level['attractor_effect_summary']
        if ae_summary:
            report += "吸引子效应分布:\n"
            for group, data in ae_summary.items():
                report += (f"  - {group} ({data['proportion']*100:.1f}%): "
                          f"平均音程均幅={data['avg_intervallic']:.3f}, "
                          f"平均局部波动性={data['avg_volatility']:.3f}\n")
        
        se_summary = cross_level['stability_effect_summary']
        if se_summary:
            report += "稳定性效应分布:\n"
            for level, data in se_summary.items():
                report += (f"  - {level} ({data['proportion']*100:.1f}%): "
                          f"平均音程均幅={data['avg_intervallic']:.3f}, "
                          f"平均局部波动性={data['avg_volatility']:.3f}\n")
        
        ee_summary = cross_level['energy_effect_summary']
        if ee_summary:
            report += "能量效应分布:\n"
            for level, data in ee_summary.items():
                report += (f"  - {level} ({data['proportion']*100:.1f}%): "
                          f"平均音程均幅={data['avg_intervallic']:.3f}, "
                          f"平均局部波动性={data['avg_volatility']:.3f}\n")
        report += "\n"
        
        # 三音组地位分析
        if 'warning' not in triad_stats:
            report += "🎯 严格三音组核心地位分析:\n"
            if 'structural_density' in triad_stats:
                sd = triad_stats['structural_density']
                report += f"- 结构密度: μ={sd['mean']:.3f}, σ={sd['std']:.3f}, 范围=[{sd['min']:.3f}, {sd['max']:.3f}]\n"
            if 'contour_control' in triad_stats:
                cc = triad_stats['contour_control']
                report += f"- 轮廓控制力: μ={cc['mean']:.3f}, σ={cc['std']:.3f}, 范围=[{cc['min']:.3f}, {cc['max']:.3f}]\n"
            if 'dynamic_contribution' in triad_stats:
                dc = triad_stats['dynamic_contribution']
                report += f"- 动态贡献度: μ={dc['mean']:.3f}, σ={dc['std']:.3f}, 范围=[{dc['min']:.3f}, {dc['max']:.3f}]\n"
            if 'stability_index' in triad_stats:
                si = triad_stats['stability_index']
                report += f"- 稳定性指数: μ={si['mean']:.3f}, σ={si['std']:.3f}, 范围=[{si['min']:.3f}, {si['max']:.3f}]\n"
            report += "\n"
        
        # 动力系统分析
        if 'warning' not in dynamics_stats:
            report += "🌊 动力系统稳定性分析:\n"
            if 'attractor_strength' in dynamics_stats:
                ast = dynamics_stats['attractor_strength']
                report += f"- 吸引子强度: μ={ast['mean']:.3f}, σ={ast['std']:.3f}, 范围=[{ast['min']:.3f}, {ast['max']:.3f}]\n"
            if 'lyapunov_exponent' in dynamics_stats:
                le = dynamics_stats['lyapunov_exponent']
                report += f"- 李雅普诺夫指数: μ={le['mean']:.3f}, σ={le['std']:.3f}, 范围=[{le['min']:.3f}, {le['max']:.3f}]\n"
            if 'stability_score' in dynamics_stats:
                ss = dynamics_stats['stability_score']
                report += f"- 稳定性评分: μ={ss['mean']:.3f}, σ={ss['std']:.3f}, 范围=[{ss['min']:.3f}, {ss['max']:.3f}]\n"
            
            if 'stability_classification_distribution' in dynamics_stats:
                scd = dynamics_stats['stability_classification_distribution']
                report += "稳定性分类分布:\n"
                for classification, proportion in scd.items():
                    report += f"  - {classification}: {proportion*100:.1f}%\n"
            report += "\n"
        
        # 全局正交性解释
        if 'warning' not in global_ortho and 'error' not in global_ortho:
            report += "📊 全局指标正交性分析:\n"
            if global_ortho.get('method') == 'PCA_analysis':
                ratios = global_ortho['explained_variance_ratio']
                report += f"- 第一主成分解释方差: {ratios[0]*100:.1f}%\n"
                if len(ratios) > 1:
                    report += f"- 第二主成分解释方差: {ratios[1]*100:.1f}%\n"
                if len(ratios) > 2:
                    report += f"- 第三主成分解释方差: {ratios[2]*100:.1f}%\n"
                report += f"- 正交性评分: {global_ortho['orthogonality_score']:.3f} "
                report += "(越高表示全局指标正交性越高)\n"
            elif global_ortho.get('method') == 'simplified_correlation_analysis':
                report += f"- 平均相关系数: {global_ortho['average_correlation']:.3f}\n"
                report += f"- 正交性评分: {global_ortho['orthogonality_score']:.3f}\n"
            report += "\n"
        
        # 研究结论
        report += "=== 核心研究结论 ===\n"
        report += self._generate_core_conclusions(global_stats, local_ortho, triad_stats, dynamics_stats)
        
        return report
    
    def _generate_core_conclusions(self, global_stats, local_ortho, triad_stats, dynamics_stats):
        """生成核心研究结论"""
        conclusions = []
        
        # 基于吸引子强度的结论
        if 'attractor_strength' in global_stats:
            attractor_mean = global_stats['attractor_strength']['mean']
            if attractor_mean > 0.7:
                conclusions.append("中国传统音乐表现出强烈的旋律吸引子效应，表明旋律围绕核心音高组织")
            elif attractor_mean > 0.4:
                conclusions.append("中国传统音乐具有中等强度的旋律吸引子，显示出一定的音高中心性")
            else:
                conclusions.append("中国传统音乐的旋律吸引子效应较弱，表现出较强的自由性")
        
        # 基于稳定性的结论
        if 'warning' not in dynamics_stats and 'stability_score' in dynamics_stats:
            stability_mean = dynamics_stats['stability_score']['mean']
            if stability_mean > 0.7:
                conclusions.append("旋律系统高度稳定，反映出中国传统音乐的连贯性和统一性")
            elif stability_mean > 0.5:
                conclusions.append("旋律系统具有中等稳定性，在稳定与变化之间保持平衡")
            else:
                conclusions.append("旋律系统稳定性较低，表现出较强的动态变化特征")
        
        # 基于正交性的结论
        ortho_prop = local_ortho['orthogonality_distribution']['high_orthogonality']
        if ortho_prop > 0.7:
            conclusions.append("音程均幅与局部波动性高度正交，说明这两个特征捕捉了旋律的不同维度")
        elif ortho_prop > 0.5:
            conclusions.append("音程均幅与局部波动性中度正交，两个特征部分独立")
        else:
            conclusions.append("音程均幅与局部波动性相关性较强，可能测量了相似的音乐特征")
        
        # 基于三音组地位的结论
        if 'warning' not in triad_stats and 'structural_density' in triad_stats:
            density_mean = triad_stats['structural_density']['mean']
            if density_mean > 0.5:
                conclusions.append("严格三音组在中国传统音乐中占据核心地位，验证了三音组理论假设")
            elif density_mean > 0.3:
                conclusions.append("严格三音组在中国传统音乐中具有重要地位，是重要的结构元素")
            else:
                conclusions.append("严格三音组在中国传统音乐中地位相对较低，可能是辅助性元素")
        
        # 综合结论
        if conclusions:
            numbered_conclusions = [f"{i+1}. {conclusion}。" for i, conclusion in enumerate(conclusions)]
            return "\n".join(numbered_conclusions)
        else:
            return "基于当前分析数据，需要更多样本来得出可靠的研究结论。"
    
    def _calculate_stats(self, values):
        """计算基本统计量"""
        if not values or len(values) == 0:
            return {
                'mean': 0.0, 'std': 0.0, 'min': 0.0, 'max': 0.0,
                'median': 0.0, 'q1': 0.0, 'q3': 0.0
            }
        
        values = np.array(values)
        return {
            'mean': float(np.mean(values)),
            'std': float(np.std(values)),
            'min': float(np.min(values)),
            'max': float(np.max(values)),
            'median': float(np.median(values)),
            'q1': float(np.quantile(values, 0.25)),
            'q3': float(np.quantile(values, 0.75))
        }



# ===== 框架整合与应用 =====

class EnhancedMusicAnalyzer:
    """增强的音乐分析器 - 整合所有模块"""
    
    def __init__(self):
        # 初始化所有分析器
        self.intervallic_ambitus_analyzer = IntervalicAmbitusAnalyzer()
        self.local_volatility_analyzer = LocalVolatilityAnalyzer()
        self.dynamics_analyzer = MelodyDynamicsSystem()
        self.triad_analyzer = StrictTriadSignificanceAnalyzer()
        self.hierarchical_orthogonality_analyzer = HierarchicalOrthogonalityAnalyzer()
        self.summary_reporter = ComprehensiveSummaryReporter(self.hierarchical_orthogonality_analyzer)
        
        # 存储分析结果
        self.all_results = []
    
    def analyze_single_work(self, pitch_series, work_name="Unknown"):
        """分析单首作品 - 基于增强动力系统理论"""
        print(f"🎵 分析作品: {work_name}")

        try:
            # 安全检查数据
            if not hasattr(pitch_series, '__len__'):
                print(f"   ❌ 无效的音高数据类型")
                return None

            if len(pitch_series) < 3:
                print(f"   ❌ 数据不足，跳过分析")
                return None

            # 确保pitch_series是列表或numpy数组
            if hasattr(pitch_series, 'tolist'):
                pitch_series = pitch_series.tolist()
            elif not isinstance(pitch_series, list):
                pitch_series = list(pitch_series)

            # 1. 动力系统分析（替代旧的基础指标）
            dynamics = self.dynamics_analyzer.analyze_melody_stability(pitch_series)

            # 2. 获取吸引子动力学信息
            attractor_dynamics = None
            if hasattr(self.dynamics_analyzer, 'estimate_attractor_dynamics'):
                attractor_dynamics = self.dynamics_analyzer.estimate_attractor_dynamics(pitch_series)

            # 3. 三音组地位分析（传入吸引子动力学）
            triad_significance = self.triad_analyzer.analyze_significance(pitch_series, attractor_dynamics)

            # 4. 提取动力学特征（替代旧的局部特征）
            dynamics_metrics = self._extract_dynamics_metrics(pitch_series, dynamics, attractor_dynamics)

            # 5. 准备全局指标（基于动力系统）
            global_metrics = {
                'attractor_strength': dynamics.get('attractor_strength', 0.0),
                'lyapunov_proxy': abs(dynamics.get('lyapunov_exponent', 0.0)),
                'system_energy': dynamics.get('stability_score', 0.0),
                'mean_curvature': self._calculate_mean_curvature(pitch_series)
            }

            # 如果有吸引子动力学信息，添加新指标
            if attractor_dynamics and 'error' not in attractor_dynamics:
                global_metrics['lambda_strength'] = attractor_dynamics['global_attractor_strength']
                global_metrics['regression_quality'] = attractor_dynamics['regression_quality']
                global_metrics['attractor_position'] = attractor_dynamics['global_attractor_position']

            # 6. 分层正交性分析
            orthogonality = self.hierarchical_orthogonality_analyzer.analyze_orthogonality(
                global_metrics, dynamics_metrics)

            # 7. 添加到摘要报告
            self.summary_reporter.add_work_analysis(
                global_metrics, orthogonality, triad_significance, dynamics)

            # 8. 打印增强的结果
            print(f"      🌊 动力系统指标:")
            print(f"        吸引子强度: {dynamics.get('attractor_strength', 0.0):.4f}")
            print(f"        稳定性评分: {dynamics.get('stability_score', 0.0):.4f}")
            if attractor_dynamics and 'error' not in attractor_dynamics:
                print(f"        回归强度(λ): {attractor_dynamics['global_attractor_strength']:.4f}")
                print(f"        回归质量(R²): {attractor_dynamics['regression_quality']:.4f}")
                print(f"        吸引子位置: {attractor_dynamics['global_attractor_position']:.2f}")

            print(f"      🎯 三音组-吸引子关系:")
            print(f"        结构密度: {triad_significance.get('structural_density', 0.0):.3f}")
            if 'attractor_alignment' in triad_significance:
                print(f"        吸引子对齐度: {triad_significance.get('attractor_alignment', 0.0):.3f}")
                print(f"        平均吸引力: {triad_significance.get('mean_attractor_pull', 0.0):.3f}")
                print(f"        残差质量: {triad_significance.get('residual_quality', 0.0):.3f}")

            print(f"      🔄 正交性分析:")
            print(f"        局部正交性: {orthogonality['local_orthogonality']['assessment']}")
            print(f"   ✅ 分析完成")

            result = {
                'work_name': work_name,
                'note_count': len(pitch_series),
                'dynamics': dynamics,
                'attractor_dynamics': attractor_dynamics,
                'triad_significance': triad_significance,
                'orthogonality': orthogonality,
                'global_metrics': global_metrics,
                'dynamics_metrics': dynamics_metrics
            }

            self.all_results.append(result)
            return result

        except Exception as e:
            print(f"   ❌ 综合分析失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _extract_dynamics_metrics(self, pitch_series, dynamics, attractor_dynamics):
        """提取动力学特征（替代旧的局部特征）"""
        # 计算基础的音程均幅和局部波动性
        intervallic_ambitus = self.intervallic_ambitus_analyzer.calculate(pitch_series)
        local_volatility = self.local_volatility_analyzer.calculate_d1_rms(pitch_series)

        # 基于动力系统的特征
        dynamics_metrics = [{
            'attractor_strength': dynamics.get('attractor_strength', 0.0),
            'stability_score': dynamics.get('stability_score', 0.0),
            'lyapunov_proxy': abs(dynamics.get('lyapunov_exponent', 0.0)),
            # 添加基础特征，确保跨层级分析有数据
            'intervallic_ambitus': intervallic_ambitus,
            'local_volatility': local_volatility
        }]

        # 如果有吸引子动力学信息，添加更多特征
        if attractor_dynamics and 'error' not in attractor_dynamics:
            dynamics_metrics[0]['lambda_strength'] = attractor_dynamics['global_attractor_strength']
            dynamics_metrics[0]['regression_quality'] = attractor_dynamics['regression_quality']

            # 计算残差统计
            residuals = attractor_dynamics['residuals']
            if residuals is not None and len(residuals) > 0:
                dynamics_metrics[0]['residual_mean'] = np.mean(np.abs(residuals))
                dynamics_metrics[0]['residual_std'] = np.std(residuals)

        return dynamics_metrics
    def _extract_local_metrics(self, pitch_series, intervallic_ambitus, local_volatility):
        """提取局部特征"""
        # 简化实现：将整个作品作为一个局部特征样本
        local_metrics = [{
            'intervallic_ambitus': intervallic_ambitus,
            'local_volatility': local_volatility
        }]
        return local_metrics
    
    def _calculate_mean_curvature(self, pitch_series):
        """计算平均曲率"""
        if len(pitch_series) < 3:
            return 0.0
        
        # 计算二阶导数（曲率的简化版本）
        curvatures = []
        for i in range(1, len(pitch_series) - 1):
            second_derivative = pitch_series[i+1] - 2*pitch_series[i] + pitch_series[i-1]
            curvatures.append(abs(second_derivative))
        
        return np.mean(curvatures) if curvatures else 0.0
    
    def analyze_multiple_works(self, works_data):
        """分析多首作品"""
        print("🎼 开始批量分析")
        print("=" * 60)
        
        successful_analyses = 0
        
        for i, (work_name, pitch_series) in enumerate(works_data):
            print(f"\n进度: {i+1}/{len(works_data)}")
            result = self.analyze_single_work(pitch_series, work_name)
            if result:
                successful_analyses += 1
        
        print(f"\n📊 批量分析完成")
        print(f"   成功分析: {successful_analyses}/{len(works_data)} 首作品")
        
        return successful_analyses
    
    def generate_comprehensive_summary(self, save_path=None):
        """生成综合分析报告"""
        print("\n🎯 生成综合摘要报告...")
        
        summary = self.summary_reporter.generate_summary_report()
        
        if 'error' in summary:
            print(f"❌ 报告生成失败: {summary['error']}")
            return None
        
        # 打印报告
        print("\n" + "="*80)
        print(summary['interpretation'])
        print("="*80)
        
        # 保存报告
        if save_path:
            try:
                with open(save_path, "w", encoding="utf-8") as f:
                    f.write(summary['interpretation'])
                    f.write("\n\n=== 详细数据摘要 ===\n")
                    
                    # 添加详细统计数据
                    import json
                    detailed_data = {
                        'global_metrics_summary': summary['global_metrics_summary'],
                        'local_orthogonality_summary': summary['local_orthogonality_summary'],
                        'cross_level_effects_summary': summary['cross_level_effects_summary'],
                        'triad_significance_summary': summary.get('triad_significance_summary', {}),
                        'dynamics_analysis_summary': summary.get('dynamics_analysis_summary', {}),
                        'global_orthogonality_analysis': summary['global_orthogonality_analysis']
                    }
                    
                    f.write(json.dumps(detailed_data, indent=2, ensure_ascii=False))
                
                print(f"✅ 综合报告已保存至: {save_path}")
            except Exception as e:
                print(f"⚠️ 报告保存失败: {e}")
        
        return summary
    
    def get_analysis_statistics(self):
        """获取分析统计信息"""
        if not self.all_results:
            return "无分析数据"

        try:
            stats = {
                'total_works': len(self.all_results),
                'avg_note_count': np.mean([r['note_count'] for r in self.all_results]),
            }

            # 安全地提取动力学指标
            attractor_strengths = []
            stability_scores = []
            triad_densities = []

            for r in self.all_results:
                # 动力学指标
                if 'dynamics' in r and r['dynamics']:
                    dynamics = r['dynamics']
                    if 'attractor_strength' in dynamics:
                        attractor_strengths.append(dynamics['attractor_strength'])
                    if 'stability_score' in dynamics:
                        stability_scores.append(dynamics['stability_score'])

                # 三音组指标
                if 'triad_significance' in r and r['triad_significance']:
                    triad_sig = r['triad_significance']
                    if 'structural_density' in triad_sig:
                        triad_densities.append(triad_sig['structural_density'])

            # 添加平均值（如果有数据）
            if attractor_strengths:
                stats['avg_attractor_strength'] = np.mean(attractor_strengths)
            if stability_scores:
                stats['avg_stability_score'] = np.mean(stability_scores)
            if triad_densities:
                stats['avg_triad_density'] = np.mean(triad_densities)

            return stats

        except Exception as e:
            return {'error': f'统计计算失败: {e}', 'total_works': len(self.all_results)}


# ===== 更新主函数以使用综合分析器 =====

def main_comprehensive_analysis():
    """主函数（综合分析模式）"""
    try:
        print("🎼 启动综合分析模式")
        print("=" * 60)
        
        # 初始化综合分析器
        analyzer = EnhancedMusicAnalyzer()
        
        # 查找音乐文件
        music_files = []
        search_directories = [
            '.',  # 当前目录
            './midi_files',  # midi_files文件夹
            './music',  # music文件夹
            './data',  # data文件夹
            './songs',  # songs文件夹
        ]
        
        search_patterns = ['*.mid', '*.midi', '*.csv']
        
        print("🔍 搜索音乐文件...")
        for directory in search_directories:
            if os.path.exists(directory):
                print(f"   检查目录: {directory}")
                for pattern in search_patterns:
                    search_path = os.path.join(directory, pattern)
                    files = glob.glob(search_path)
                    if files:
                        print(f"     找到 {len(files)} 个 {pattern} 文件")
                        music_files.extend(files)
        
        # 过滤出MIDI文件
        midi_files = [f for f in music_files if f.endswith(('.mid', '.midi'))]
        
        if not midi_files:
            print("\n❌ 未找到MIDI文件")
            print("🧪 使用测试数据进行演示:")
            run_comprehensive_test_analysis(analyzer)
            return
        
        print(f"📁 找到 {len(midi_files)} 个音乐文件")
        
        # 准备作品数据
        works_data = []
        for file_path in midi_files:
            # 简化的MIDI加载（这里应该使用实际的MIDI解析）
            work_name = os.path.splitext(os.path.basename(file_path))[0]
            
            # 使用SimpleMidiParser提取音高
            try:
                pitch_series = SimpleMidiParser.extract_pitch_from_midi(file_path)
                if pitch_series and len(pitch_series) >= 3:
                    works_data.append((work_name, pitch_series))
            except Exception as e:
                print(f"   ⚠️ 跳过文件 {work_name}: {e}")
        
        if not works_data:
            print("❌ 没有成功加载的音乐文件")
            print("🧪 使用测试数据进行演示:")
            run_comprehensive_test_analysis(analyzer)
            return
        
        # 分析所有作品
        successful_count = analyzer.analyze_multiple_works(works_data)
        
        if successful_count > 0:
            # 生成综合报告
            summary = analyzer.generate_comprehensive_summary("comprehensive_analysis_report.txt")
            
            # 显示统计信息
            stats = analyzer.get_analysis_statistics()
            print(f"\n📈 分析统计:")
            for key, value in stats.items():
                if isinstance(value, float):
                    print(f"   {key}: {value:.4f}")
                else:
                    print(f"   {key}: {value}")
        else:
            print("❌ 没有成功分析的作品")
            
    except Exception as e:
        print(f"❌ 综合分析失败: {e}")
        print("🧪 建议检查文件路径或使用测试数据")


def run_comprehensive_test_analysis(analyzer):
    """运行综合测试分析"""
    test_melodies = [
        ('平滑音阶', [60, 62, 64, 65, 67, 69, 71, 72, 74, 76, 77, 79, 81, 83, 84]),
        ('跳跃旋律', [60, 72, 48, 75, 45, 78, 42, 81, 39, 84, 36, 87, 33, 90, 30]),
        ('三音组模式', [60, 62, 61, 63, 62, 64, 63, 65, 64, 66, 65, 67, 66, 68, 67]),
        ('吸引子主导', [60, 61, 60, 62, 60, 61, 60, 63, 60, 61, 60, 62, 60, 64, 60]),
        ('混沌模式', [60, 67, 55, 72, 48, 69, 52, 74, 46, 71, 49, 76, 44, 73, 51]),
        ('中国传统模式1', [60, 59, 61, 60, 58, 60, 59, 62, 61, 59, 61, 60, 58, 60, 59]),
        ('中国传统模式2', [67, 65, 67, 69, 67, 65, 67, 64, 67, 65, 67, 69, 67, 65, 67]),
        ('中国传统模式3', [72, 71, 72, 74, 72, 71, 72, 69, 72, 71, 72, 74, 72, 71, 72])
    ]
    
    # 分析测试数据
    successful_count = analyzer.analyze_multiple_works(test_melodies)
    
    if successful_count > 0:
        # 生成综合报告
        summary = analyzer.generate_comprehensive_summary("test_comprehensive_analysis_report.txt")
        
        # 显示统计信息
        stats = analyzer.get_analysis_statistics()
        print(f"\n📈 测试分析统计:")
        for key, value in stats.items():
            if isinstance(value, float):
                print(f"   {key}: {value:.4f}")
            else:
                print(f"   {key}: {value}")


# 更新主函数调用
if __name__ == "__main__":
    print("🎼 增强版中国音乐分析系统")
    print("选择分析模式:")
    print("1. 基础分析（音程均幅 + 局部波动性 + 动力系统）")
    print("2. 完整分析（包含严格三音组核心地位分析）")
    print("3. 高级分析（包含分层正交性分析）")
    print("4. 专业分析（包含动力系统可视化和相空间轨迹图）")
    print("5. 综合分析（完整的五步分析框架 + 摘要报告）")
    
    try:
        # 默认使用综合分析
        print("🚀 启动综合分析模式（完整的五步分析框架）")
        main_comprehensive_analysis()
    except Exception as e:
        print(f"❌ 综合分析失败: {e}")
        print("🔄 回退到专业分析模式")
        try:
            main_with_dynamics_visualization()
        except Exception as e2:
            print(f"❌ 专业分析也失败: {e2}")
            print("🔄 回退到高级分析模式")
            try:
                main_with_hierarchical_orthogonality()
            except Exception as e3:
                print(f"❌ 高级分析也失败: {e3}")
                print("🔄 回退到完整分析模式")
                try:
                    main_with_triads()
                except Exception as e4:
                    print(f"❌ 完整分析也失败: {e4}")
                    print("🔄 回退到基础分析模式")
                    try:
                        analyzer = EnhancedChineseMusicAnalyzer()
                        analyzer.analyze_all_works()
                    except Exception as e5:
                        print(f"❌ 基础分析也失败: {e5}")
                        print("🧪 建议检查文件路径或依赖库")

