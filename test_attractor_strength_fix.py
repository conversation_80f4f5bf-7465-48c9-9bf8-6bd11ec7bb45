#!/usr/bin/env python3
"""
测试修复后的吸引子强度计算
验证是否能产生不同的、有意义的吸引子强度值
"""

import sys
import os
import numpy as np

# 添加当前目录到路径
sys.path.append('.')

# 导入分析器
from topological_melody_core import TopologicalMelodyAnalyzer

def test_attractor_strength_fix():
    """测试修复后的吸引子强度计算"""
    print("🧪 测试修复后的吸引子强度计算")
    print("="*60)
    
    # 创建不同特征的测试旋律，预期产生不同的吸引子强度
    test_melodies = [
        {
            'name': '单一吸引子（重复音符）',
            'pitches': [60] * 15 + [61] * 2 + [60] * 8,  # 强单一吸引子
            'expected_strength': '高强度（单一主导吸引子）'
        },
        {
            'name': '双吸引子（平衡分布）',
            'pitches': [60, 61, 60, 62, 60] * 3 + [72, 73, 72, 74, 72] * 3,  # 两个平衡的吸引子
            'expected_strength': '中等强度（双吸引子平衡）'
        },
        {
            'name': '多吸引子（分散分布）',
            'pitches': [60, 61, 60] * 2 + [67, 68, 67] * 2 + [74, 75, 74] * 2 + [81, 82, 81] * 2,  # 多个分散的吸引子
            'expected_strength': '中低强度（多吸引子分散）'
        },
        {
            'name': '平滑音阶（无明显吸引子）',
            'pitches': list(range(60, 85)),  # 连续音阶，吸引子不明显
            'expected_strength': '低强度（无明显吸引子）'
        },
        {
            'name': '随机跳跃（噪声模式）',
            'pitches': [60, 80, 45, 90, 30, 85, 40, 95, 35, 88, 42, 92, 38, 87, 33, 84],  # 随机跳跃
            'expected_strength': '低强度（噪声主导）'
        },
        {
            'name': '强主导吸引子',
            'pitches': [64] * 20 + [65, 63, 64, 66, 64, 62, 64, 67, 64, 61, 64],  # 极强的单一吸引子
            'expected_strength': '极高强度（极强主导）'
        }
    ]
    
    # 创建分析器
    analyzer = TopologicalMelodyAnalyzer(kernel_width=3.0, max_attractors=8, min_attractors=1)
    
    results = []
    
    for i, melody in enumerate(test_melodies):
        print(f"\n🎵 测试旋律 {i+1}: {melody['name']}")
        print(f"   预期强度: {melody['expected_strength']}")
        print(f"   音符序列: {melody['pitches'][:8]}... (共{len(melody['pitches'])}个音符)")
        
        try:
            # 分析旋律
            result = analyzer.analyze_melody(melody['pitches'])
            
            if result:
                # 获取吸引子强度
                attractor_strength = result['topology_metrics']['attractor_strength']
                
                # 获取吸引子信息
                attractor_points = result['potential_field']['attractor_points']
                optimal_k = result['potential_field']['optimal_n_attractors']
                
                print(f"   ✅ 分析成功")
                print(f"   吸引子强度: {attractor_strength:.6f}")
                print(f"   吸引子数量: {len(attractor_points)}")
                print(f"   最优k值: {optimal_k}")
                
                # 显示吸引子详情
                print(f"   吸引子详情:")
                for j, (pos, weight) in enumerate(attractor_points):
                    print(f"     吸引子{j+1}: 位置={pos:.1f}, 权重={weight:.4f}")
                
                results.append({
                    'name': melody['name'],
                    'attractor_strength': attractor_strength,
                    'n_attractors': len(attractor_points),
                    'optimal_k': optimal_k,
                    'attractor_points': attractor_points
                })
                
            else:
                print(f"   ❌ 分析失败")
                
        except Exception as e:
            print(f"   ❌ 分析出错: {e}")
            import traceback
            traceback.print_exc()
    
    # 验证修复效果
    print(f"\n" + "="*60)
    print("🎯 修复效果验证")
    print("="*60)
    
    if results:
        print(f"成功分析: {len(results)}/{len(test_melodies)} 个旋律")
        
        # 提取吸引子强度值
        strengths = [r['attractor_strength'] for r in results]
        
        print(f"\n📊 吸引子强度统计:")
        print(f"   均值: {np.mean(strengths):.6f}")
        print(f"   标准差: {np.std(strengths):.6f}")
        print(f"   范围: {min(strengths):.6f} ~ {max(strengths):.6f}")
        print(f"   唯一值数量: {len(set(strengths))}")
        
        # 显示每个旋律的强度
        print(f"\n📈 各旋律吸引子强度:")
        sorted_results = sorted(results, key=lambda x: x['attractor_strength'], reverse=True)
        for r in sorted_results:
            print(f"   {r['name']:<25}: {r['attractor_strength']:.6f} ({r['n_attractors']}个吸引子)")
        
        # 验证修复成功的标准
        unique_strengths = len(set(strengths))
        std_dev = np.std(strengths)
        
        print(f"\n🔍 修复验证:")
        print(f"   不同强度值数量: {unique_strengths}")
        print(f"   标准差: {std_dev:.6f}")
        
        if unique_strengths > 1 and std_dev > 0.001:
            print(f"   ✅ 修复成功！吸引子强度现在能反映不同音乐的特征")
            
            # 验证强度与音乐特征的合理性
            print(f"\n🎯 合理性验证:")
            
            # 找到最强和最弱的吸引子强度
            max_strength = max(strengths)
            min_strength = min(strengths)
            max_result = next(r for r in results if r['attractor_strength'] == max_strength)
            min_result = next(r for r in results if r['attractor_strength'] == min_strength)
            
            print(f"   最强吸引子: {max_result['name']} ({max_strength:.6f})")
            print(f"   最弱吸引子: {min_result['name']} ({min_strength:.6f})")
            print(f"   强度比值: {max_strength/min_strength:.2f}")
            
            return True
        else:
            print(f"   ❌ 修复不完全：")
            if unique_strengths <= 1:
                print(f"     - 所有旋律仍有相同的吸引子强度")
            if std_dev <= 0.001:
                print(f"     - 标准差太小，变化不明显")
            return False
    else:
        print("❌ 没有成功的分析结果")
        return False

if __name__ == "__main__":
    success = test_attractor_strength_fix()
    if success:
        print("\n🎉 吸引子强度计算修复成功！")
        print("✅ 现在能够产生反映不同音乐特征的多样化强度值")
    else:
        print("\n❌ 修复失败，需要进一步调试")
