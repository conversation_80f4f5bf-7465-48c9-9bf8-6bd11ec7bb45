#!/usr/bin/env python3
"""
测试修正后的阈值设定
验证基于中国传统音乐理论的阈值在实际分析中的效果
"""

import sys
import os
import numpy as np

# 添加当前目录到路径
sys.path.append('.')

def test_corrected_thresholds():
    """测试修正后的阈值设定"""
    print("🏮 测试基于中国传统音乐理论的修正阈值")
    print("验证新阈值在实际分析中的效果")
    print("="*80)
    
    try:
        # 导入修正后的统一分析器
        from unified_topological_analysis import UnifiedTopologicalAnalyzer
        
        # 创建分析器
        analyzer = UnifiedTopologicalAnalyzer(
            kernel_width=3.0,
            max_attractors=8,
            min_attractors=1
        )
        
        print("✅ 统一拓扑分析器创建成功")
        print(f"📊 阈值设定:")
        print(f"   强关联: ≥ {analyzer.alignment_thresholds['strong']:.3f} (大三度以内)")
        print(f"   中等关联: {analyzer.alignment_thresholds['moderate']:.3f} - {analyzer.alignment_thresholds['strong']:.3f} (四度到八度)")
        print(f"   弱关联: < {analyzer.alignment_thresholds['moderate']:.3f} (超过八度)")
        
        # 创建不同特征的测试旋律
        test_melodies = [
            {
                'name': '基本音程主导型',
                'pitches': [60, 62, 64, 60, 62, 64, 60, 62, 64],  # 全音和大三度
                'expected_level': '强关联',
                'description': '主要包含全音和大三度等基本音程单位'
            },
            {
                'name': '音乐骨架型',
                'pitches': [60, 65, 67, 60, 65, 67, 60, 65, 67],  # 纯四度和纯五度
                'expected_level': '中等关联',
                'description': '主要包含纯四度和纯五度等音乐骨架音程'
            },
            {
                'name': '八度跨越型',
                'pitches': [60, 72, 84, 60, 72, 84, 60, 72, 84],  # 八度和两八度
                'expected_level': '弱关联',
                'description': '包含八度和超八度的远距离关系'
            },
            {
                'name': '五声音阶型',
                'pitches': [60, 62, 64, 67, 69, 72, 69, 67, 64, 62, 60],  # C D E G A C
                'expected_level': '强关联',
                'description': '典型的五声音阶，基本音程单位为主'
            },
            {
                'name': '混合音程型',
                'pitches': [60, 64, 67, 72, 76, 79, 84],  # 大三度+纯四度+纯五度+八度
                'expected_level': '中等关联',
                'description': '混合了基本音程和骨架音程'
            }
        ]
        
        results = []
        
        for i, melody in enumerate(test_melodies, 1):
            print(f"\n🎼 测试旋律 {i}: {melody['name']}")
            print(f"   描述: {melody['description']}")
            print(f"   预期分类: {melody['expected_level']}")
            print(f"   音符数量: {len(melody['pitches'])}")
            
            try:
                # 执行分析
                result = analyzer.analyze_work(melody['pitches'], melody['name'])
                
                if result and 'alignment_classification' in result and result['alignment_classification']:
                    classification = result['alignment_classification']
                    alignment_score = classification['alignment_score']
                    actual_level = classification['level']
                    
                    print(f"   ✅ 分析成功")
                    print(f"   📊 对齐度: {alignment_score:.4f}")
                    print(f"   🎯 实际分类: {actual_level}")
                    print(f"   📝 分类描述: {classification['description']}")
                    print(f"   🎼 音乐意义: {classification['musical_meaning']}")
                    
                    # 验证分类是否符合预期
                    classification_correct = (actual_level == melody['expected_level'])
                    print(f"   {'✅' if classification_correct else '⚠️'} 分类{'正确' if classification_correct else '需要调整'}")
                    
                    results.append({
                        'name': melody['name'],
                        'alignment_score': alignment_score,
                        'actual_level': actual_level,
                        'expected_level': melody['expected_level'],
                        'classification_correct': classification_correct,
                        'description': melody['description']
                    })
                    
                else:
                    print(f"   ❌ 分析失败或缺少分类信息")
                    
            except Exception as e:
                print(f"   ❌ 分析出错: {e}")
                import traceback
                traceback.print_exc()
        
        # 验证阈值效果
        if results:
            print(f"\n" + "="*80)
            print("🎯 阈值效果验证")
            print("="*80)
            
            print(f"成功分析: {len(results)}/{len(test_melodies)} 个旋律")
            
            # 分类准确性统计
            correct_classifications = sum(1 for r in results if r['classification_correct'])
            accuracy = correct_classifications / len(results) * 100
            
            print(f"\n📊 分类准确性:")
            print(f"   正确分类: {correct_classifications}/{len(results)} ({accuracy:.1f}%)")
            
            # 各级别分布
            level_distribution = {}
            for r in results:
                level = r['actual_level']
                level_distribution[level] = level_distribution.get(level, 0) + 1
            
            print(f"\n📈 实际分类分布:")
            for level, count in level_distribution.items():
                percentage = count / len(results) * 100
                print(f"   {level}: {count} 个 ({percentage:.1f}%)")
            
            # 对齐度范围分析
            alignments = [r['alignment_score'] for r in results]
            print(f"\n📏 对齐度分布:")
            print(f"   范围: {min(alignments):.4f} - {max(alignments):.4f}")
            print(f"   均值: {np.mean(alignments):.4f}")
            print(f"   标准差: {np.std(alignments):.4f}")
            
            # 详细结果展示
            print(f"\n📋 详细分类结果:")
            print(f"{'旋律类型':<15} {'对齐度':<10} {'实际分类':<10} {'预期分类':<10} {'结果'}")
            print("-" * 70)
            
            for r in results:
                status = "✅" if r['classification_correct'] else "⚠️"
                print(f"{r['name']:<15} {r['alignment_score']:<10.4f} {r['actual_level']:<10} {r['expected_level']:<10} {status}")
            
            # 阈值合理性验证
            print(f"\n🔍 阈值合理性验证:")
            
            strong_alignments = [r['alignment_score'] for r in results if r['actual_level'] == '强关联']
            moderate_alignments = [r['alignment_score'] for r in results if r['actual_level'] == '中等关联']
            weak_alignments = [r['alignment_score'] for r in results if r['actual_level'] == '弱关联']
            
            if strong_alignments:
                print(f"   强关联对齐度范围: {min(strong_alignments):.4f} - {max(strong_alignments):.4f}")
            if moderate_alignments:
                print(f"   中等关联对齐度范围: {min(moderate_alignments):.4f} - {max(moderate_alignments):.4f}")
            if weak_alignments:
                print(f"   弱关联对齐度范围: {min(weak_alignments):.4f} - {max(weak_alignments):.4f}")
            
            # 验证阈值边界
            threshold_validation = True
            
            # 检查强关联是否都≥0.333
            if strong_alignments and min(strong_alignments) < 0.333:
                print(f"   ⚠️ 强关联中有对齐度 < 0.333 的情况")
                threshold_validation = False
            
            # 检查中等关联是否在0.143-0.333之间
            if moderate_alignments:
                if min(moderate_alignments) < 0.143 or max(moderate_alignments) >= 0.333:
                    print(f"   ⚠️ 中等关联对齐度超出 0.143-0.333 范围")
                    threshold_validation = False
            
            # 检查弱关联是否都<0.143
            if weak_alignments and max(weak_alignments) >= 0.143:
                print(f"   ⚠️ 弱关联中有对齐度 ≥ 0.143 的情况")
                threshold_validation = False
            
            if threshold_validation:
                print(f"   ✅ 阈值边界验证通过")
            
            return accuracy >= 80  # 80%以上准确率认为成功
        else:
            print("❌ 没有成功的分析结果")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_threshold_classification_function():
    """测试阈值分类函数"""
    print(f"\n" + "="*80)
    print("🔍 测试阈值分类函数")
    print("="*80)
    
    try:
        from unified_topological_analysis import UnifiedTopologicalAnalyzer
        
        analyzer = UnifiedTopologicalAnalyzer()
        
        # 测试不同对齐度值的分类
        test_alignments = [
            (1.000, "同音"),
            (0.500, "全音"),
            (0.400, "小三度"),
            (0.333, "大三度"),
            (0.286, "纯四度"),
            (0.222, "纯五度"),
            (0.143, "八度"),
            (0.125, "九度"),
            (0.077, "两八度")
        ]
        
        print(f"🎵 对齐度分类测试:")
        print(f"{'对齐度':<10} {'对应音程':<8} {'分类结果':<10} {'描述'}")
        print("-" * 60)
        
        for alignment, interval in test_alignments:
            classification = analyzer.classify_alignment(alignment)
            print(f"{alignment:<10.3f} {interval:<8} {classification['level']:<10} {classification['description']}")
        
        print(f"\n✅ 阈值分类函数测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 分类函数测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🏮 基于中国传统音乐理论的阈值修正测试")
    print("验证新阈值设定的科学性和实用性")
    
    # 主测试
    main_success = test_corrected_thresholds()
    
    # 分类函数测试
    function_success = test_threshold_classification_function()
    
    if main_success and function_success:
        print(f"\n🎉 所有测试通过！")
        print(f"✅ 基于中国传统音乐理论的阈值设定验证成功")
        print(f"🎼 新阈值能够准确反映中国音乐的音程重要性层次")
        print(f"📊 分类准确性达到预期标准")
    else:
        print(f"\n⚠️ 部分测试需要进一步优化")
        if not main_success:
            print(f"   - 主要阈值测试需要调整")
        if not function_success:
            print(f"   - 分类函数需要完善")
