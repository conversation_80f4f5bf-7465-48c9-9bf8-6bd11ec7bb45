#!/usr/bin/env python3
"""
分类离群点深度诊断
分析"强关联"分类的信息损失和离群点特征
"""

import numpy as np
import sys
sys.path.append('.')

def analyze_classification_information_loss():
    """分析分类方案的信息损失"""
    print("🔍 分类方案信息损失分析")
    print("检查96%归入同一类别的分类缺陷")
    print("="*80)
    
    # 报告的分类数据
    classification_data = {
        '强关联': {'threshold': '≥0.333', 'count': 48, 'percentage': 96.0},
        '中等关联': {'threshold': '0.143-0.333', 'count': 2, 'percentage': 4.0},
        '弱关联': {'threshold': '<0.143', 'count': 0, 'percentage': 0.0}
    }
    
    alignment_range = (0.2884, 0.6676)
    total_samples = 50
    
    print("\n1. 📊 分类方案评估")
    print("-" * 60)
    
    for category, data in classification_data.items():
        print(f"\n   {category}:")
        print(f"      阈值: {data['threshold']}")
        print(f"      数量: {data['count']} 首")
        print(f"      比例: {data['percentage']:.1f}%")
    
    print(f"\n   🚨 信息损失问题:")
    print(f"      • 96%数据归入同一类别 → 几乎无区分能力")
    print(f"      • 强关联内部差异被完全遮蔽")
    print(f"      • 对齐度范围: {alignment_range[0]:.4f} ~ {alignment_range[1]:.4f}")
    print(f"      • 强关联内部跨度: {alignment_range[1] - 0.333:.4f} (巨大差异)")

def analyze_threshold_data_mismatch():
    """分析阈值与数据的脱节"""
    print(f"\n2. 🎯 阈值设计与数据脱节分析")
    print("-" * 60)
    
    # 理论阈值 vs 实际数据
    theoretical_thresholds = {
        '弱关联上限': 0.143,
        '中等关联下限': 0.143,
        '中等关联上限': 0.333,
        '强关联下限': 0.333
    }
    
    actual_data_range = {
        '实际最小值': 0.2884,
        '实际最大值': 0.6676
    }
    
    print("   理论阈值设计:")
    for name, threshold in theoretical_thresholds.items():
        print(f"      {name}: {threshold:.3f}")
    
    print(f"\n   实际数据范围:")
    for name, value in actual_data_range.items():
        print(f"      {name}: {value:.4f}")
    
    print(f"\n   🚨 脱节问题:")
    
    # 计算空集区间
    empty_weak = (0.0, 0.143)
    empty_medium_lower = (0.143, 0.2884)
    actual_medium = (0.2884, 0.333)
    
    print(f"      弱关联理论区间: [0.000, 0.143) → 空集 (无实际数据)")
    print(f"      中等关联下段: [0.143, 0.289) → 空集 (无实际数据)")
    print(f"      中等关联实际: [0.289, 0.333) → 2首作品 (离群点)")
    print(f"      强关联区间: [0.333, 0.668] → 48首作品 (96%)")
    
    print(f"\n   📊 空集比例:")
    total_theoretical_range = 1.0
    empty_range = 0.143 + (0.2884 - 0.143)
    empty_percentage = (empty_range / total_theoretical_range) * 100
    print(f"      理论空集范围: {empty_percentage:.1f}% (严重的阈值设计问题)")

def identify_outlier_characteristics():
    """识别离群点特征"""
    print(f"\n3. 🔍 离群点特征识别")
    print("-" * 60)
    
    print("   中等关联离群点分析:")
    print("      数量: 2首作品 (4%)")
    print("      对齐度范围: [0.2884, 0.333)")
    print("      特征: 显著偏离96%主流的异常样本")
    
    print(f"\n   🎯 关键问题:")
    questions = [
        "这2首作品的音乐特征有何不同？",
        "低对齐度是否伴随其他异常指标？",
        "它们是否代表特定的'作品类型'？",
        "异常是由数据质量还是真实音乐特征造成？"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"      {i}. {question}")
    
    print(f"\n   📋 需要检查的指标:")
    indicators_to_check = [
        "吸引子数量 (是否为极值5个？)",
        "吸引子强度 (是否异常高/低？)",
        "相位收敛比例 (是否异常低？)",
        "音高跨度 (是否过大/过小？)",
        "三音组数量 (是否异常？)",
        "数据质量 (是否存在解析错误？)"
    ]
    
    for indicator in indicators_to_check:
        print(f"      • {indicator}")

def design_improved_classification():
    """设计改进的分类方案"""
    print(f"\n4. 🔧 改进分类方案设计")
    print("-" * 60)
    
    print("   当前方案问题:")
    current_problems = [
        "96%数据归入同一类别",
        "阈值与实际数据脱节",
        "大量理论空集区间",
        "内部差异被完全遮蔽"
    ]
    
    for problem in current_problems:
        print(f"      ❌ {problem}")
    
    print(f"\n   改进方案选项:")
    
    improvement_options = {
        '数据驱动分类': {
            'method': '基于实际数据分布设置阈值',
            'example': '三分位数: Q1=0.45, Q2=0.55, Q3=0.65',
            'pros': '充分利用数据变异性',
            'cons': '失去理论依据'
        },
        '混合分类': {
            'method': '理论阈值+数据细分',
            'example': '强关联内部再分: 高强(>0.5), 中强(0.4-0.5), 低强(0.333-0.4)',
            'pros': '保持理论基础，增加区分度',
            'cons': '复杂度增加'
        },
        '异常点分离': {
            'method': '单独标识离群点',
            'example': '正常(96%) + 异常(4%)',
            'pros': '突出异常样本',
            'cons': '简化过度'
        },
        '连续评分': {
            'method': '使用连续对齐度分数',
            'example': '直接报告0.2884-0.6676范围',
            'pros': '保留完整信息',
            'cons': '失去分类便利性'
        }
    }
    
    for option, details in improvement_options.items():
        print(f"\n   🛠️ {option}:")
        print(f"      方法: {details['method']}")
        print(f"      示例: {details['example']}")
        print(f"      优点: {details['pros']}")
        print(f"      缺点: {details['cons']}")

def create_outlier_profile_template():
    """创建离群点画像模板"""
    print(f"\n5. 📋 离群点综合画像模板")
    print("-" * 60)
    
    print("   离群点深度剖析表格:")
    print("   " + "="*70)
    
    profile_template = {
        'headers': [
            '样本标识', '关联度分类', '对齐度值', '吸引子数量', 
            '吸引子强度', '收敛比例', '音高跨度', '异常特征'
        ],
        'outlier_1': [
            '中等关联-1', '中等', '[0.289-0.333)', '?', '?', '?', '?', '待分析'
        ],
        'outlier_2': [
            '中等关联-2', '中等', '[0.289-0.333)', '?', '?', '?', '?', '待分析'
        ]
    }
    
    # 打印表格头
    header_line = "   " + " | ".join(f"{h:>12}" for h in profile_template['headers'])
    print(header_line)
    print("   " + "-" * len(header_line))
    
    # 打印离群点行
    for outlier_key in ['outlier_1', 'outlier_2']:
        outlier_data = profile_template[outlier_key]
        data_line = "   " + " | ".join(f"{d:>12}" for d in outlier_data)
        print(data_line)
    
    print(f"\n   🎯 分析目标:")
    analysis_goals = [
        "识别离群点的具体对齐度值",
        "检查是否存在其他异常指标的聚集",
        "确定异常是数据质量还是音乐特征",
        "构建特定'作品类型'的模型",
        "从标记异常提升到解释异常"
    ]
    
    for goal in analysis_goals:
        print(f"      • {goal}")

def recommend_implementation_steps():
    """推荐实施步骤"""
    print(f"\n6. 🚀 实施步骤建议")
    print("-" * 60)
    
    implementation_steps = {
        '立即行动': [
            '识别2个中等关联样本的具体作品',
            '提取这些样本的完整指标画像',
            '检查数据质量和预处理问题',
            '分析异常指标的聚集模式'
        ],
        '分类改进': [
            '设计数据驱动的分类阈值',
            '在强关联内部增加细分类别',
            '单独标识和分析离群点',
            '提供连续分数作为补充'
        ],
        '深度分析': [
            '构建离群点的多维特征画像',
            '验证异常的音乐学意义',
            '建立特定作品类型的模型',
            '提供异常解释而非仅仅标记'
        ],
        '报告改进': [
            '明确说明分类方案的局限性',
            '报告信息损失的程度',
            '提供离群点的详细分析',
            '建议后续研究方向'
        ]
    }
    
    for category, steps in implementation_steps.items():
        print(f"\n   📋 {category}:")
        for step in steps:
            print(f"      • {step}")
    
    print(f"\n🎯 核心目标:")
    print(f"   从'96%强关联'的信息损失转向'2个异常样本'的深度理解")

if __name__ == "__main__":
    print("🔍 分类离群点深度诊断")
    print("解构'强关联'分类的信息损失问题")
    
    # 1. 信息损失分析
    analyze_classification_information_loss()
    
    # 2. 阈值脱节分析
    analyze_threshold_data_mismatch()
    
    # 3. 离群点特征识别
    identify_outlier_characteristics()
    
    # 4. 改进分类设计
    design_improved_classification()
    
    # 5. 离群点画像模板
    create_outlier_profile_template()
    
    # 6. 实施建议
    recommend_implementation_steps()
    
    print(f"\n🎉 诊断完成！")
    print(f"✅ 识别了严重的信息损失问题")
    print(f"🔧 提供了具体的改进方案")
    print(f"📊 建立了离群点深度分析框架")
