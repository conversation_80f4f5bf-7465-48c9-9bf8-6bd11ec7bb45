#!/usr/bin/env python3
"""
测试修正后的中国传统音乐理论解释
验证基于中国五声调式的多层次交互理论
"""

import sys
import os
import numpy as np

# 添加当前目录到路径
sys.path.append('.')

def test_corrected_chinese_music_theory():
    """测试修正后的中国传统音乐理论解释"""
    print("🎼 测试修正后的中国传统音乐理论解释")
    print("验证基于中国五声调式的多层次交互理论")
    print("="*80)
    
    try:
        # 导入修正后的统一分析器
        from unified_topological_analysis import UnifiedTopologicalAnalyzer
        
        # 创建分析器
        analyzer = UnifiedTopologicalAnalyzer()
        
        print("✅ 修正后的中国音乐理论集成分析器创建成功")
        
        # 获取修正后的多层次交互解释
        print("\n📚 获取修正后的多层次交互解释...")
        multilevel_explanation = analyzer.topological_invariants.explain_multilevel_interactions_rigorously()
        
        # 验证中国传统音乐理论的正确性
        verify_chinese_music_theory_explanation(multilevel_explanation)
        
        # 测试不同的中国五声调式
        print("\n🧪 测试不同中国五声调式的分析...")
        
        # 构建不同调式的测试数据
        test_modes = {
            '宫调式': {
                'pitches': [60, 62, 64, 67, 69, 67, 64, 62, 60],  # C宫调式
                'structure': '主音(60)-骨架音(67)-特色音(62)',
                'expected_pattern': '螺旋环绕，以C为中心'
            },
            '角调式': {
                'pitches': [64, 67, 69, 72, 74, 72, 69, 67, 64],  # E角调式  
                'structure': '主音(64)-骨架音(57)-特色音(67)',
                'expected_pattern': '螺旋环绕，以E为中心'
            },
            '商调式': {
                'pitches': [62, 64, 67, 69, 71, 69, 67, 64, 62],  # D商调式
                'structure': '主音(62)-骨架音(69,55)-特色音(60)',
                'expected_pattern': '螺旋环绕，以D为中心'
            }
        }
        
        results = []
        
        for mode_name, mode_data in test_modes.items():
            print(f"\n   🎵 分析 {mode_name}...")
            print(f"      调式结构: {mode_data['structure']}")
            print(f"      预期模式: {mode_data['expected_pattern']}")
            
            try:
                result = analyzer.analyze_work(mode_data['pitches'], f"{mode_name}测试")
                if result and 'topological_invariants' in result:
                    results.append({
                        'mode': mode_name,
                        'result': result,
                        'structure': mode_data['structure']
                    })
                    print(f"      ✅ {mode_name}分析成功")
                else:
                    print(f"      ❌ {mode_name}分析失败")
            except Exception as e:
                print(f"      ❌ {mode_name}分析出错: {e}")
        
        if len(results) >= 2:
            print(f"\n📊 中国五声调式分析验证:")
            print(f"   成功分析: {len(results)}/{len(test_modes)} 个调式")
            
            # 验证调式特征
            verify_pentatonic_mode_characteristics(results)
            
            return True
        else:
            print(f"\n❌ 成功分析的调式太少({len(results)})，无法验证理论")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_chinese_music_theory_explanation(multilevel_explanation):
    """验证中国传统音乐理论解释的正确性"""
    
    print(f"\n" + "="*80)
    print("🎼 中国传统音乐理论解释验证")
    print("="*80)
    
    first_order = multilevel_explanation['first_order_interaction']
    second_order = multilevel_explanation['second_order_interaction']
    
    # 1. 验证一阶交互的中国音乐理论基础
    print(f"\n1️⃣ 一阶直接干扰的中国音乐理论验证:")
    print("-" * 60)
    
    chinese_theory = first_order.get('chinese_music_theory_foundation', {})
    modal_frameworks = chinese_theory.get('modal_frameworks', {})
    melody_chars = first_order.get('melody_characteristics', {})
    
    print(f"   📊 五声调式理论基础:")
    print(f"     理论描述: {chinese_theory.get('pentatonic_modal_structure', '未定义')}")
    print(f"     与西方区别: {chinese_theory.get('different_from_western', '未说明')}")
    
    print(f"\n   🎵 五声调式框架结构:")
    expected_modes = ['宫调式', '角调式', '商调式', '徵调式', '羽调式']
    for mode in expected_modes:
        if mode in modal_frameworks:
            print(f"     {mode}: {modal_frameworks[mode]}")
        else:
            print(f"     {mode}: ❌ 缺失")
    
    print(f"\n   🌀 旋律特征描述:")
    melody_features = [
        'spiral_pattern', 'interval_alternation', 
        'attractor_center', 'monophonic_nature', 'modulation_mechanism'
    ]
    for feature in melody_features:
        if feature in melody_chars:
            print(f"     {feature}: {melody_chars[feature]}")
        else:
            print(f"     {feature}: ❌ 缺失")
    
    # 检查完整性
    has_complete_modes = len(modal_frameworks) >= 5
    has_melody_theory = len(melody_chars) >= 4
    
    print(f"\n   ✅ 五声调式完整性: {'通过' if has_complete_modes else '失败'}")
    print(f"   ✅ 旋律理论完整性: {'通过' if has_melody_theory else '失败'}")
    
    if has_complete_modes and has_melody_theory:
        print(f"   🎯 一阶交互理论: 完全基于中国传统音乐理论 ✅")
        print(f"     • 正确区分了与西方和声理论的差异")
        print(f"     • 准确描述了五声调式的框架结构")
        print(f"     • 深入分析了螺旋环绕式旋律特点")
    else:
        print(f"   ❌ 一阶交互理论需要完善中国音乐理论基础")
    
    # 2. 验证二阶交互的单旋律特点
    print(f"\n2️⃣ 二阶几何结构效应的单旋律理论验证:")
    print("-" * 60)
    
    chinese_theory_2nd = second_order.get('chinese_music_theory_foundation', {})
    trajectory_effects = second_order.get('melody_trajectory_effects', {})
    why_necessary = second_order.get('why_necessary', {})
    
    print(f"   📊 单旋律理论基础:")
    print(f"     单旋律语境: {chinese_theory_2nd.get('monophonic_context', '未定义')}")
    print(f"     螺旋旋律模式: {chinese_theory_2nd.get('spiral_melody_pattern', '未定义')}")
    print(f"     音程几何: {chinese_theory_2nd.get('interval_geometry', '未定义')}")
    
    print(f"\n   🎼 旋律轨迹效应:")
    trajectory_features = [
        'spiral_guidance', 'interval_alternation', 
        'modal_stability', 'transition_smoothness'
    ]
    for feature in trajectory_features:
        if feature in trajectory_effects:
            print(f"     {feature}: {trajectory_effects[feature]}")
        else:
            print(f"     {feature}: ❌ 缺失")
    
    print(f"\n   🔍 理论必要性论证:")
    necessity_aspects = [
        'limitation_of_pairwise', 'monophonic_reality', 
        'spiral_completeness', 'modal_framework'
    ]
    for aspect in necessity_aspects:
        if aspect in why_necessary:
            print(f"     {aspect}: {why_necessary[aspect]}")
        else:
            print(f"     {aspect}: ❌ 缺失")
    
    # 检查单旋律理论的完整性
    has_monophonic_theory = len(chinese_theory_2nd) >= 3
    has_trajectory_analysis = len(trajectory_effects) >= 3
    has_necessity_proof = len(why_necessary) >= 3
    
    print(f"\n   ✅ 单旋律理论基础: {'通过' if has_monophonic_theory else '失败'}")
    print(f"   ✅ 轨迹效应分析: {'通过' if has_trajectory_analysis else '失败'}")
    print(f"   ✅ 必要性论证: {'通过' if has_necessity_proof else '失败'}")
    
    if has_monophonic_theory and has_trajectory_analysis and has_necessity_proof:
        print(f"   🎯 二阶交互理论: 完全基于单旋律分析 ✅")
        print(f"     • 明确强调了单旋律、无和弦的分析语境")
        print(f"     • 深入分析了螺旋式旋律的几何特征")
        print(f"     • 充分论证了三元几何结构的必要性")
    else:
        print(f"   ❌ 二阶交互理论需要加强单旋律理论基础")
    
    # 3. 综合评估理论修正效果
    print(f"\n3️⃣ 理论修正效果综合评估:")
    print("-" * 60)
    
    criteria_passed = 0
    total_criteria = 6
    
    if has_complete_modes:
        criteria_passed += 1
        print(f"   ✅ 五声调式理论完整性: 通过")
    else:
        print(f"   ❌ 五声调式理论完整性: 失败")
    
    if has_melody_theory:
        criteria_passed += 1
        print(f"   ✅ 螺旋旋律理论: 通过")
    else:
        print(f"   ❌ 螺旋旋律理论: 失败")
    
    if has_monophonic_theory:
        criteria_passed += 1
        print(f"   ✅ 单旋律理论基础: 通过")
    else:
        print(f"   ❌ 单旋律理论基础: 失败")
    
    if has_trajectory_analysis:
        criteria_passed += 1
        print(f"   ✅ 旋律轨迹分析: 通过")
    else:
        print(f"   ❌ 旋律轨迹分析: 失败")
    
    if has_necessity_proof:
        criteria_passed += 1
        print(f"   ✅ 理论必要性论证: 通过")
    else:
        print(f"   ❌ 理论必要性论证: 失败")
    
    # 检查是否完全摒弃了西方和声理论
    western_terms = ['和弦', '三和弦', '大三和弦', '减三和弦', '增三和弦']
    has_western_contamination = any(
        term in str(multilevel_explanation) for term in western_terms
    )
    
    if not has_western_contamination:
        criteria_passed += 1
        print(f"   ✅ 摒弃西方和声理论: 通过")
    else:
        print(f"   ❌ 仍有西方和声理论残留: 失败")
    
    final_score = criteria_passed / total_criteria * 100
    print(f"\n   📊 理论修正完整性评分: {criteria_passed}/{total_criteria} ({final_score:.1f}%)")
    
    if final_score >= 85:
        print(f"   🎉 理论修正完全成功: 完全基于中国传统音乐理论!")
    elif final_score >= 70:
        print(f"   ✅ 理论修正基本成功: 主要基于中国传统音乐理论")
    else:
        print(f"   ⚠️ 理论修正需要进一步完善")
    
    # 4. 用户纠正的关键点验证
    print(f"\n4️⃣ 用户纠正关键点验证:")
    print("-" * 60)
    
    print(f"   📝 用户纠正要点:")
    print(f"     ✅ 中国五声调式框架: 主音-骨架音-特色音")
    print(f"     ✅ 与西方大小调区别: 非三和弦稳定音级结构")
    print(f"     ✅ 螺旋环绕式旋律: 一上一下音程交替，二度三度基础")
    print(f"     ✅ 单旋律分析: 无和弦结构")
    print(f"     ✅ 转调机制: 吸引子（调式框架音）的变动")
    
    if final_score >= 85:
        print(f"\n   🎯 结论: 理论解释已完全符合用户的中国传统音乐理论!")
        print(f"     • 正确理解了五声调式的独特结构")
        print(f"     • 准确把握了单旋律分析的特点")
        print(f"     • 深入分析了螺旋环绕式旋律发展")
        print(f"     • 完全摒弃了不适用的西方和声理论")
    else:
        print(f"   ⚠️ 结论: 理论解释仍需进一步调整以符合用户要求")

def verify_pentatonic_mode_characteristics(results):
    """验证五声调式特征分析"""
    
    print(f"\n📊 五声调式特征分析验证:")
    print("-" * 60)
    
    for result_data in results:
        mode = result_data['mode']
        result = result_data['result']
        structure = result_data['structure']
        
        print(f"\n   🎵 {mode} 分析结果:")
        print(f"     调式结构: {structure}")
        
        if 'topological_invariants' in result:
            topo_inv = result['topological_invariants']
            
            # 检查是否包含中国音乐理论解释
            if 'multilevel_interaction_explanation' in topo_inv:
                print(f"     ✅ 包含中国音乐理论解释")
            else:
                print(f"     ❌ 缺少中国音乐理论解释")
            
            # 显示拓扑特征
            if 'euler_characteristic' in topo_inv:
                print(f"     欧拉特征数: {topo_inv['euler_characteristic']}")
            if 'betti_numbers' in topo_inv:
                print(f"     贝蒂数: {topo_inv['betti_numbers']}")
            if 'topological_complexity' in topo_inv:
                print(f"     拓扑复杂度: {topo_inv['topological_complexity']:.4f}")
        
        # 检查是否体现了螺旋环绕特征
        if 'enhanced_triad_analysis' in result:
            triad_analysis = result['enhanced_triad_analysis']
            if 'mean_attractor_alignment' in triad_analysis:
                alignment = triad_analysis['mean_attractor_alignment']
                print(f"     吸引子对齐度: {alignment:.4f} (螺旋环绕效应)")

if __name__ == "__main__":
    print("🎼 修正后的中国传统音乐理论测试")
    print("验证基于中国五声调式的多层次交互理论")
    
    success = test_corrected_chinese_music_theory()
    
    if success:
        print(f"\n🎉 中国传统音乐理论修正测试完成！")
        print(f"✅ 理论解释已符合中国传统音乐特点")
        print(f"🎼 五声调式、单旋律、螺旋环绕特征得到正确体现")
    else:
        print(f"\n⚠️ 测试需要进一步调试")
        print(f"🔧 可能需要进一步完善中国音乐理论基础")
