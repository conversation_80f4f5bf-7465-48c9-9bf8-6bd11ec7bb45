#!/usr/bin/env python3
"""
统计参数的音乐理论依据分析
确定理论期望值、显著性水平等参数的中国音乐理论基础
"""

import numpy as np
import sys
sys.path.append('.')

def analyze_attractor_count_theory():
    """分析吸引子数量的理论期望"""
    print("🎼 吸引子数量的理论期望分析")
    print("="*80)
    
    print("\n1. 🎯 吸引子数量代表什么？")
    print("-" * 60)
    
    print("📚 音乐学意义:")
    print("   • 吸引子 = 旋律中的'引力中心'或'稳定音高区域'")
    print("   • 数量反映旋律的结构复杂度和调式特征")
    print("   • 对应传统音乐理论中的'骨干音'概念")
    
    print(f"\n🏛️ 中国传统音乐理论对应:")
    
    modal_expectations = {
        '宫调式': {
            'core_notes': ['宫(主音)', '徵(上方纯五度)', '商(上方大二度)'],
            'expected_attractors': 3,
            'theory_basis': '最简调式结构，主音+骨架音+特色音',
            'probability': 0.2  # 假设20%的传统音乐是宫调式
        },
        '商调式': {
            'core_notes': ['商(主音)', '羽(上方纯五度)', '徵(下方纯五度)', '角(上方大二度)', '宫(下方大二度)'],
            'expected_attractors': 4,
            'theory_basis': '复杂调式结构，主音+上下骨架音+主要特色音',
            'probability': 0.2
        },
        '角调式': {
            'core_notes': ['角(主音)', '宫(上方纯五度)', '羽(下方纯五度)', '徵(上方大二度)', '商(下方大二度)'],
            'expected_attractors': 4,
            'theory_basis': '复杂调式结构，主音+上下骨架音+主要特色音',
            'probability': 0.2
        },
        '徵调式': {
            'core_notes': ['徵(主音)', '商(上方纯五度)', '宫(下方纯五度)', '羽(上方大二度)', '角(下方大二度)'],
            'expected_attractors': 4,
            'theory_basis': '复杂调式结构，主音+上下骨架音+主要特色音',
            'probability': 0.2
        },
        '羽调式': {
            'core_notes': ['羽(主音)', '角(上方纯五度)', '商(下方纯五度)', '宫(上方大二度)', '徵(下方大二度)'],
            'expected_attractors': 4,
            'theory_basis': '复杂调式结构，主音+上下骨架音+主要特色音',
            'probability': 0.2
        }
    }
    
    print("🎵 各调式的理论期望:")
    total_expected = 0
    for mode, info in modal_expectations.items():
        print(f"\n   {mode}:")
        print(f"     核心音: {', '.join(info['core_notes'])}")
        print(f"     预期吸引子数: {info['expected_attractors']}")
        print(f"     理论依据: {info['theory_basis']}")
        print(f"     出现概率: {info['probability']*100:.0f}%")
        total_expected += info['expected_attractors'] * info['probability']
    
    print(f"\n📊 加权理论期望:")
    print(f"   E(吸引子数量) = {total_expected:.1f}")
    print(f"   范围: 3-4 (基于调式理论)")
    print(f"   解释: 平均每个旋律有{total_expected:.1f}个主要的音高引力中心")
    
    return total_expected

def analyze_alignment_theory():
    """分析对齐度的理论期望"""
    print(f"\n" + "="*80)
    print("🎯 对齐度的理论期望分析")
    print("="*80)
    
    print("\n1. 🎼 对齐度代表什么？")
    print("-" * 60)
    
    print("📚 音乐学意义:")
    print("   • 对齐度 = 三音组与吸引子的空间接近程度")
    print("   • 高对齐度 = 三音组紧密围绕吸引子分布")
    print("   • 低对齐度 = 三音组远离吸引子，结构松散")
    print("   • 单位: 基于全音距离的标准化指标")
    
    print(f"\n🏛️ 中国传统音乐理论对应:")
    
    alignment_scenarios = {
        '强调性音乐': {
            'description': '明确的调性中心，如传统民歌',
            'expected_alignment': 0.6,  # 1/(1+0.67全音) ≈ 0.6
            'distance_whole_tones': 0.67,
            'theory_basis': '三音组主要在主音附近，距离约2/3全音',
            'examples': '《茉莉花》、《小河淌水》等'
        },
        '中等调性音乐': {
            'description': '有调性但结构较复杂，如工尺谱音乐',
            'expected_alignment': 0.5,  # 1/(1+1.0全音) = 0.5
            'distance_whole_tones': 1.0,
            'theory_basis': '三音组分布在多个重要音级，平均距离1全音',
            'examples': '传统器乐曲、戏曲音乐'
        },
        '弱调性音乐': {
            'description': '调性模糊或多调性，如现代创作',
            'expected_alignment': 0.4,  # 1/(1+1.5全音) ≈ 0.4
            'distance_whole_tones': 1.5,
            'theory_basis': '三音组较分散，平均距离1.5全音',
            'examples': '现代民族音乐创作'
        },
        '随机音乐': {
            'description': '完全随机的音高序列',
            'expected_alignment': 0.25,  # 1/(1+3.0全音) = 0.25
            'distance_whole_tones': 3.0,
            'theory_basis': '无结构，三音组与吸引子距离很远',
            'examples': '理论对照基线'
        }
    }
    
    print("🎵 不同音乐类型的对齐度期望:")
    weighted_alignment = 0
    for music_type, info in alignment_scenarios.items():
        print(f"\n   {music_type}:")
        print(f"     描述: {info['description']}")
        print(f"     预期对齐度: {info['expected_alignment']:.3f}")
        print(f"     对应距离: {info['distance_whole_tones']:.1f}全音")
        print(f"     理论依据: {info['theory_basis']}")
        print(f"     典型例子: {info['examples']}")
        
        # 假设传统音乐70%强调性，20%中等调性，10%弱调性
        if music_type == '强调性音乐':
            weighted_alignment += info['expected_alignment'] * 0.7
        elif music_type == '中等调性音乐':
            weighted_alignment += info['expected_alignment'] * 0.2
        elif music_type == '弱调性音乐':
            weighted_alignment += info['expected_alignment'] * 0.1
    
    print(f"\n📊 传统音乐的加权期望:")
    print(f"   E(对齐度) = {weighted_alignment:.3f}")
    print(f"   随机基线: 0.25")
    print(f"   显著阈值: > 0.4 (明显高于随机)")
    
    return weighted_alignment, 0.25

def analyze_convergence_theory():
    """分析收敛比例的理论期望"""
    print(f"\n" + "="*80)
    print("🔄 收敛比例的理论期望分析")
    print("="*80)
    
    print("\n1. 🎼 收敛比例代表什么？")
    print("-" * 60)
    
    print("📚 音乐学意义:")
    print("   • 收敛比例 = 向吸引子收敛的三音组占总数的比例")
    print("   • 高收敛比例 = 旋律具有强烈的向心性")
    print("   • 低收敛比例 = 旋律结构松散，缺乏中心")
    
    print(f"\n🏛️ 中国传统音乐理论对应:")
    
    convergence_scenarios = {
        '传统民歌': {
            'description': '强烈的调性中心，围绕主音展开',
            'expected_convergence': 0.8,
            'theory_basis': '80%的旋律片段向主音收敛',
            'musical_principle': '起承转合，最终归宗'
        },
        '工尺谱音乐': {
            'description': '多个稳定音级，结构相对复杂',
            'expected_convergence': 0.65,
            'theory_basis': '65%的片段向主要音级收敛',
            'musical_principle': '宫调体系的多中心结构'
        },
        '戏曲音乐': {
            'description': '情感表达丰富，结构变化较大',
            'expected_convergence': 0.6,
            'theory_basis': '60%的片段显示收敛特征',
            'musical_principle': '声腔体系的灵活性'
        },
        '现代创作': {
            'description': '调性较弱或无调性特征',
            'expected_convergence': 0.45,
            'theory_basis': '45%的片段显示收敛，接近随机',
            'musical_principle': '现代作曲技法的多样性'
        },
        '随机音乐': {
            'description': '完全随机的音高序列',
            'expected_convergence': 0.33,
            'theory_basis': '纯随机情况下的理论期望',
            'musical_principle': '无音乐结构的对照基线'
        }
    }
    
    print("🎵 不同音乐类型的收敛期望:")
    for music_type, info in convergence_scenarios.items():
        print(f"\n   {music_type}:")
        print(f"     描述: {info['description']}")
        print(f"     预期收敛比例: {info['expected_convergence']*100:.0f}%")
        print(f"     理论依据: {info['theory_basis']}")
        print(f"     音乐原理: {info['musical_principle']}")
    
    # 传统音乐的加权期望
    traditional_expectation = 0.8 * 0.4 + 0.65 * 0.4 + 0.6 * 0.2  # 假设权重
    
    print(f"\n📊 传统音乐的加权期望:")
    print(f"   E(收敛比例) = {traditional_expectation:.2f}")
    print(f"   随机基线: 0.33")
    print(f"   显著阈值: > 0.5 (明显高于随机)")
    
    return traditional_expectation, 0.33

def analyze_effect_size_meaning():
    """分析效应量在音乐学中的意义"""
    print(f"\n" + "="*80)
    print("📏 效应量在音乐学中的意义")
    print("="*80)
    
    print("\n1. 🎼 效应量代表什么？")
    print("-" * 60)
    
    print("📚 统计学意义:")
    print("   • 效应量 = 实际效应的大小，独立于样本量")
    print("   • Cohen's d: (观察值 - 期望值) / 标准差")
    print("   • 衡量音乐结构特征的'强度'")
    
    print(f"\n🎵 音乐学解释:")
    
    effect_interpretations = {
        '小效应 (d=0.2)': {
            'musical_meaning': '微弱的音乐结构特征',
            'examples': '轻微的调性倾向，不明显的音乐模式',
            'practical_significance': '可能需要专业音乐家才能察觉'
        },
        '中等效应 (d=0.5)': {
            'musical_meaning': '明显的音乐结构特征',
            'examples': '清晰的调性中心，明确的音乐模式',
            'practical_significance': '普通听众可以感知的音乐特征'
        },
        '大效应 (d=0.8)': {
            'musical_meaning': '强烈的音乐结构特征',
            'examples': '非常明确的调性，强烈的音乐对比',
            'practical_significance': '显著的音乐特征，易于识别和分析'
        },
        '超大效应 (d>1.2)': {
            'musical_meaning': '极强的音乐结构特征',
            'examples': '极度明确的调性中心，极强的音乐对比',
            'practical_significance': '可能表示特殊的音乐风格或作曲技法'
        }
    }
    
    print("📊 效应量的音乐学解释:")
    for effect_level, info in effect_interpretations.items():
        print(f"\n   {effect_level}:")
        print(f"     音乐学意义: {info['musical_meaning']}")
        print(f"     典型例子: {info['examples']}")
        print(f"     实践意义: {info['practical_significance']}")
    
    print(f"\n🎯 音乐学中的效应量阈值建议:")
    print(f"   • d ≥ 0.3: 音乐学上有意义的最小效应")
    print(f"   • d ≥ 0.5: 明确的音乐结构特征")
    print(f"   • d ≥ 0.8: 强烈的音乐特征，值得深入研究")

def recommend_statistical_parameters():
    """推荐统计参数设定"""
    print(f"\n" + "="*80)
    print("📋 统计参数设定建议")
    print("="*80)
    
    # 基于前面的分析
    attractor_expectation = 3.8  # 加权期望
    alignment_expectation = 0.54  # 传统音乐期望
    alignment_random = 0.25  # 随机基线
    convergence_expectation = 0.68  # 传统音乐期望
    convergence_random = 0.33  # 随机基线
    
    print("🎯 推荐的理论期望值:")
    print(f"   吸引子数量: {attractor_expectation:.1f} (基于五声调式理论)")
    print(f"   对齐度: {alignment_expectation:.2f} (传统音乐期望) vs {alignment_random:.2f} (随机基线)")
    print(f"   收敛比例: {convergence_expectation:.2f} (传统音乐期望) vs {convergence_random:.2f} (随机基线)")
    
    print(f"\n📊 推荐的显著性水平:")
    print(f"   主要检验: α = 0.05 (标准水平)")
    print(f"   关键发现: α = 0.01 (更严格，用于核心结论)")
    print(f"   探索性分析: α = 0.10 (宽松，用于初步发现)")
    
    print(f"\n🔧 推荐的多重比较策略:")
    print(f"   方法: Bonferroni校正 (保守但可靠)")
    print(f"   原因: 确保音乐学结论的可靠性")
    print(f"   替代: FDR校正 (如果检验数量很多)")
    
    print(f"\n🎲 推荐的随机基线策略:")
    print(f"   方法1: 音高序列随机打乱 (保持音高分布)")
    print(f"   方法2: 蒙特卡罗随机旋律 (符合音域约束)")
    print(f"   方法3: 理论计算基线 (基于概率模型)")
    
    print(f"\n📏 推荐的效应量阈值:")
    print(f"   音乐学意义阈值: d ≥ 0.3")
    print(f"   实践意义阈值: d ≥ 0.5")
    print(f"   强效应阈值: d ≥ 0.8")
    
    print(f"\n🎼 音乐学解释指南:")
    print(f"   • 显著但小效应: 微妙的音乐结构特征")
    print(f"   • 显著且中等效应: 明确的音乐模式")
    print(f"   • 显著且大效应: 强烈的音乐特征")
    print(f"   • 不显著: 可能是随机变化或样本不足")

if __name__ == "__main__":
    print("📊 统计参数的音乐理论依据分析")
    print("确定理论期望值和显著性检验的科学基础")
    
    # 1. 吸引子数量分析
    attractor_expectation = analyze_attractor_count_theory()
    
    # 2. 对齐度分析
    alignment_expectation, alignment_random = analyze_alignment_theory()
    
    # 3. 收敛比例分析
    convergence_expectation, convergence_random = analyze_convergence_theory()
    
    # 4. 效应量意义
    analyze_effect_size_meaning()
    
    # 5. 参数建议
    recommend_statistical_parameters()
    
    print(f"\n🎉 理论分析完成！")
    print(f"✅ 为统计检验提供了充分的音乐理论依据")
    print(f"🎼 建立了音乐学与统计学的桥梁")
