#!/usr/bin/env python3
"""
基于张量场的拓扑动力学旋律分析器 - 核心版本
Topological Melody Analyzer Core Version (No Visualization Dependencies)

核心理论：
1. 旋律是R³相空间中的动力系统(p, ṗ, p̈)
2. 存在隐式引力场V(p)产生拓扑吸引子
3. 三音组是相空间中的局部流形Mt ⊂ R³
4. 旋律发展遵循 d/dt Mt = -∇V(Mt)
"""

import numpy as np
import math
import json
import os
import glob
import pandas as pd

# 检查依赖库
try:
    import pretty_midi
    PRETTY_MIDI_AVAILABLE = True
except ImportError:
    PRETTY_MIDI_AVAILABLE = False
    print("⚠️ pretty_midi未安装，将使用简化MIDI解析器")

class SimpleMidiParser:
    """简化的MIDI解析器，不依赖pretty_midi"""

    @staticmethod
    def extract_pitch_from_midi(file_path):
        """从MIDI文件提取音高序列（简化版本）"""
        try:
            with open(file_path, 'rb') as f:
                data = f.read()

            # 简化的MIDI解析 - 查找Note On事件
            pitches = []
            i = 0
            while i < len(data) - 3:
                # 查找Note On事件 (0x90-0x9F)
                if data[i] >= 0x90 and data[i] <= 0x9F:
                    if i + 2 < len(data):
                        pitch = data[i + 1]
                        velocity = data[i + 2]
                        if velocity > 0 and 21 <= pitch <= 108:  # 有效的MIDI音高范围
                            pitches.append(pitch)
                i += 1

            # 如果没有找到音符，尝试其他方法
            if not pitches:
                # 查找所有可能的音高值
                for byte in data:
                    if 21 <= byte <= 108:  # MIDI音高范围
                        pitches.append(byte)

                # 去重并排序（保持原始顺序的去重）
                seen = set()
                unique_pitches = []
                for pitch in pitches:
                    if pitch not in seen:
                        seen.add(pitch)
                        unique_pitches.append(pitch)
                pitches = unique_pitches[:50]  # 限制数量

            return pitches[:100] if pitches else []  # 限制最大数量

        except Exception as e:
            print(f"     MIDI解析错误: {e}")
            return []

class MelodyPotentialField:
    """
    旋律势能场 - 替代离散吸引子
    基于连续引力场建模，使用自适应高斯混合模型表示势能场V(p)
    使用BIC/AIC自动选择最优吸引子数量
    """

    def __init__(self, kernel_width=3.0, max_attractors=8, min_attractors=1):
        self.kernel_width = kernel_width  # 势场宽度参数
        self.max_attractors = max_attractors  # 最大吸引子数量
        self.min_attractors = min_attractors  # 最小吸引子数量
        self.optimal_n_attractors = None  # 自动选择的最优吸引子数量
        self.pitch_range = None
        self.attractor_points = []
        self.field_params = []
        self.model_selection_results = {}  # 存储模型选择过程的详细信息
        
    def fit(self, pitch_series):
        """从音高序列学习势能场V(p)"""
        self.pitch_range = (np.min(pitch_series), np.max(pitch_series))
        self.attractor_points = self._find_attractor_candidates(pitch_series)
        self.field_params = self._compute_field_parameters(pitch_series)
        
    def _find_attractor_candidates(self, pitch_series):
        """使用BIC/AIC自动选择最优吸引子数量并识别吸引点"""
        print("   🔍 使用BIC/AIC进行模型选择...")

        # 确保数据是numpy数组
        data = np.array(pitch_series).reshape(-1, 1)
        n_samples = len(data)

        # 测试不同的吸引子数量
        bic_scores = []
        aic_scores = []
        models = []

        # 限制搜索范围，避免过拟合
        max_k = min(self.max_attractors, n_samples // 3, len(set(pitch_series)))
        max_k = max(max_k, self.min_attractors)

        for k in range(self.min_attractors, max_k + 1):
            try:
                # 使用简化的高斯混合模型（手动实现，避免依赖sklearn）
                model_result = self._fit_gaussian_mixture(data, k)

                if model_result is not None:
                    log_likelihood = model_result['log_likelihood']
                    n_params = k * 3  # 每个高斯分量有3个参数：均值、方差、权重

                    # 计算BIC和AIC
                    bic = -2 * log_likelihood + n_params * np.log(n_samples)
                    aic = -2 * log_likelihood + 2 * n_params

                    bic_scores.append(bic)
                    aic_scores.append(aic)
                    models.append(model_result)

                    print(f"     k={k}: BIC={bic:.2f}, AIC={aic:.2f}")
                else:
                    bic_scores.append(float('inf'))
                    aic_scores.append(float('inf'))
                    models.append(None)

            except Exception as e:
                print(f"     k={k}: 拟合失败 - {e}")
                bic_scores.append(float('inf'))
                aic_scores.append(float('inf'))
                models.append(None)

        # 使用音乐理论先验修正的BIC选择
        if not bic_scores or all(score == float('inf') for score in bic_scores):
            print("   ⚠️ 所有模型拟合失败，使用单一吸引子")
            self.optimal_n_attractors = 1
            mean_pitch = np.mean(pitch_series)
            return [(mean_pitch, 1.0)]

        # 应用音乐理论先验修正BIC
        corrected_bic_scores = self._apply_music_theory_prior(bic_scores, n_samples)

        best_k_idx = np.argmin(corrected_bic_scores)
        self.optimal_n_attractors = best_k_idx + self.min_attractors
        best_model = models[best_k_idx]

        print(f"   ✅ 最优吸引子数量: {self.optimal_n_attractors} (原始BIC={bic_scores[best_k_idx]:.2f}, 修正BIC={corrected_bic_scores[best_k_idx]:.2f})")

        # 存储模型选择结果
        self.model_selection_results = {
            'tested_k_values': list(range(self.min_attractors, max_k + 1)),
            'bic_scores': bic_scores,
            'aic_scores': aic_scores,
            'optimal_k': self.optimal_n_attractors,
            'best_bic': bic_scores[best_k_idx],
            'best_aic': aic_scores[best_k_idx]
        }

        # 提取吸引子候选点
        if best_model is None:
            mean_pitch = np.mean(pitch_series)
            return [(mean_pitch, 1.0)]

        candidates = []
        for i in range(len(best_model['means'])):
            mean = best_model['means'][i]
            weight = best_model['weights'][i]
            candidates.append((mean, weight))

        # 按权重排序
        candidates.sort(key=lambda x: x[1], reverse=True)
        return candidates

    def _fit_gaussian_mixture(self, data, k, max_iter=50, tol=1e-4):
        """简化的高斯混合模型拟合（EM算法）"""
        try:
            n_samples = len(data)
            data_flat = data.flatten()

            # 初始化参数
            # 使用k-means++风格的初始化
            means = self._initialize_means(data_flat, k)
            variances = np.full(k, np.var(data_flat))
            weights = np.full(k, 1.0 / k)

            prev_log_likelihood = -np.inf

            for iteration in range(max_iter):
                # E步：计算责任度
                responsibilities = self._compute_responsibilities(data_flat, means, variances, weights)

                # M步：更新参数
                means, variances, weights = self._update_parameters(data_flat, responsibilities)

                # 计算对数似然
                log_likelihood = self._compute_log_likelihood(data_flat, means, variances, weights)

                # 检查收敛
                if abs(log_likelihood - prev_log_likelihood) < tol:
                    break

                prev_log_likelihood = log_likelihood

            return {
                'means': means,
                'variances': variances,
                'weights': weights,
                'log_likelihood': log_likelihood,
                'n_iterations': iteration + 1
            }

        except Exception as e:
            print(f"     高斯混合模型拟合失败: {e}")
            return None

    def _initialize_means(self, data, k):
        """使用k-means++风格初始化均值"""
        means = []

        # 第一个中心随机选择
        means.append(np.random.choice(data))

        # 后续中心基于距离权重选择
        for _ in range(1, k):
            distances = []
            for point in data:
                min_dist = min(abs(point - mean) for mean in means)
                distances.append(min_dist)

            # 基于距离的概率选择
            distances = np.array(distances)
            probabilities = distances / np.sum(distances)

            # 避免概率为0的情况
            probabilities = np.maximum(probabilities, 1e-10)
            probabilities = probabilities / np.sum(probabilities)

            next_mean = np.random.choice(data, p=probabilities)
            means.append(next_mean)

        return np.array(means)

    def _compute_responsibilities(self, data, means, variances, weights):
        """计算每个数据点对每个分量的责任度"""
        n_samples = len(data)
        k = len(means)
        responsibilities = np.zeros((n_samples, k))

        for i, x in enumerate(data):
            total_prob = 0
            probs = []

            for j in range(k):
                # 高斯概率密度
                var = max(variances[j], 1e-6)  # 避免方差为0
                prob = weights[j] * np.exp(-0.5 * (x - means[j])**2 / var) / np.sqrt(2 * np.pi * var)
                probs.append(prob)
                total_prob += prob

            # 归一化
            if total_prob > 1e-10:
                for j in range(k):
                    responsibilities[i, j] = probs[j] / total_prob
            else:
                # 如果总概率太小，均匀分配
                responsibilities[i, :] = 1.0 / k

        return responsibilities

    def _update_parameters(self, data, responsibilities):
        """更新高斯混合模型参数"""
        n_samples, k = responsibilities.shape

        # 更新权重
        weights = np.mean(responsibilities, axis=0)

        # 更新均值
        means = np.zeros(k)
        for j in range(k):
            if weights[j] > 1e-10:
                means[j] = np.sum(responsibilities[:, j] * data) / np.sum(responsibilities[:, j])
            else:
                means[j] = np.mean(data)  # 退化情况

        # 更新方差
        variances = np.zeros(k)
        for j in range(k):
            if weights[j] > 1e-10:
                weighted_sq_diff = responsibilities[:, j] * (data - means[j])**2
                variances[j] = np.sum(weighted_sq_diff) / np.sum(responsibilities[:, j])
                variances[j] = max(variances[j], 1e-6)  # 避免方差为0
            else:
                variances[j] = np.var(data)  # 退化情况

        return means, variances, weights

    def _compute_log_likelihood(self, data, means, variances, weights):
        """计算对数似然"""
        log_likelihood = 0

        for x in data:
            prob_sum = 0
            for j in range(len(means)):
                var = max(variances[j], 1e-6)
                prob = weights[j] * np.exp(-0.5 * (x - means[j])**2 / var) / np.sqrt(2 * np.pi * var)
                prob_sum += prob

            if prob_sum > 1e-10:
                log_likelihood += np.log(prob_sum)
            else:
                log_likelihood += -1e6  # 惩罚项

        return log_likelihood

    def _apply_music_theory_prior(self, bic_scores, n_samples):
        """应用音乐理论先验修正BIC分数"""
        corrected_scores = []

        # 基于中国传统音乐理论的先验分布
        # 理论期望：宫调式(3个) 40%, 商角徵羽(4个) 30%, 复杂调式(5个) 30%
        theory_prior = {
            3: 0.4,  # 宫调式等简单调式
            4: 0.3,  # 商角徵羽调式
            5: 0.3   # 复杂调式
        }

        # 先验强度参数（可调节）
        prior_strength = np.log(n_samples) * 0.5  # 增强先验强度

        for i, bic in enumerate(bic_scores):
            k = i + self.min_attractors

            # 计算音乐理论先验惩罚
            if k in theory_prior:
                # 符合理论期望的k值，给予奖励（减少BIC）
                prior_penalty = -prior_strength * np.log(theory_prior[k])
            else:
                # 不符合理论期望的k值，给予惩罚（增加BIC）
                prior_penalty = prior_strength * 2.0

            corrected_bic = bic + prior_penalty
            corrected_scores.append(corrected_bic)

            print(f"     k={k}: 原始BIC={bic:.2f}, 先验惩罚={prior_penalty:.2f}, 修正BIC={corrected_bic:.2f}")

        return corrected_scores

    def _compute_field_parameters(self, pitch_series):
        """计算高斯混合势场参数"""
        # V(p) = -∑_i α_i exp(-(p-μ_i)²/(2σ²))
        params = []
        
        if not self.attractor_points:
            # 如果没有找到吸引子，使用均值作为单一吸引子
            μ = np.mean(pitch_series)
            α = 1.0
            σ = self.kernel_width
            params.append((μ, α, σ))
        else:
            for μ, strength in self.attractor_points:
                α = strength * 10.0  # 放大强度
                σ = self.kernel_width
                params.append((μ, α, σ))
        
        return params
    
    def compute_gradient(self, position):
        """计算引力场梯度∇V(p)"""
        grad = 0
        for μ, α, σ in self.field_params:
            diff = position - μ
            exp_term = np.exp(-(diff**2)/(2*σ**2))
            grad += α * diff / (σ**2) * exp_term
        return -grad
    
    def compute_potential(self, position):
        """计算位置p的势能V(p)"""
        pot = 0
        for μ, α, σ in self.field_params:
            pot += -α * np.exp(-((position-μ)**2)/(2*σ**2))
        return pot

class TriadManifoldDynamics:
    """
    三音组流形动力学分析
    分析三音组序列在相空间中的演化轨迹
    """
    
    def __init__(self, potential_field):
        self.potential_field = potential_field
    
    def analyze_triad_trajectory(self, triad_sequence):
        """分析三音组序列的动力学演化"""
        trajectory_metrics = []
        
        for i, triad in enumerate(triad_sequence):
            # 三音组在相空间中的表示
            positions = [note['position'] for note in triad['notes']]
            centroid = np.mean(positions)
            velocity = self._compute_triad_velocity(triad)
            curvature = self._compute_triad_curvature(triad)
            
            # 势场作用分析
            field_gradient = self.potential_field.compute_gradient(centroid)
            field_potential = self.potential_field.compute_potential(centroid)
            
            # 流形稳定性指标
            stability = self._compute_manifold_stability(
                centroid, velocity, field_gradient)
            
            # 分类流形动态相位
            phase = self._classify_manifold_phase(velocity, field_gradient)
            
            trajectory_metrics.append({
                'position': (centroid, velocity, curvature),
                'field_interaction': (field_gradient, field_potential),
                'stability': stability,
                'phase': phase,
                'triad_index': i
            })
        
        return trajectory_metrics
    
    def _compute_triad_velocity(self, triad):
        """计算三音组流形切向量(一阶导数)"""
        positions = [note['position'] for note in triad['notes']]
        times = [note['time'] for note in triad['notes']]
        
        # 使用线性拟合计算斜率作为速度
        if len(set(times)) > 1:
            velocity = np.polyfit(times, positions, 1)[0]
        else:
            velocity = 0.0
        return velocity
    
    def _compute_triad_curvature(self, triad):
        """计算三音组流形曲率(二阶导数)"""
        positions = [note['position'] for note in triad['notes']]
        
        # 使用二次拟合的二次项系数作为曲率
        try:
            curvature = np.polyfit(range(3), positions, 2)[0] * 2  # 二次项系数×2
        except:
            curvature = 0.0
        return curvature
    
    def _compute_manifold_stability(self, centroid, velocity, field_gradient):
        """计算流形稳定性: λ = -<v, ∇V> / ||v||²"""
        if abs(velocity) < 1e-8:
            return 0.0
        
        dot_product = velocity * field_gradient
        stability = -dot_product / (velocity**2 + 1e-8)
        return stability
    
    def _classify_manifold_phase(self, velocity, field_gradient):
        """分类流形动态相位"""
        if abs(velocity) < 1e-8 or abs(field_gradient) < 1e-8:
            return "Static Equilibrium"
        
        # 计算速度与梯度的夹角
        cos_angle = (velocity * field_gradient) / (abs(velocity) * abs(field_gradient))
        cos_angle = np.clip(cos_angle, -1, 1)  # 确保在[-1,1]范围内
        angle = np.arccos(abs(cos_angle))
        
        if angle < np.pi/4:
            return "Attractor Convergence"
        elif angle < 3*np.pi/4:
            return "Orbital Trajectory"
        else:
            return "Repulsive Divergence"

class TopologicalMelodyAnalyzer:
    """
    多尺度拓扑旋律分析器
    整合势能场和三音组动力学分析
    支持MIDI文件和CSV文件加载
    使用自适应吸引子数量选择
    """

    def __init__(self, kernel_width=3.0, max_attractors=8, min_attractors=1):
        self.potential_field = MelodyPotentialField(kernel_width, max_attractors, min_attractors)
        self.triad_dynamics = None
        self.analysis_results = None
        self.pitch_series = None

    def load_midi_file(self, file_path):
        """加载MIDI文件"""
        try:
            if PRETTY_MIDI_AVAILABLE:
                # 使用pretty_midi（如果可用）
                midi_data = pretty_midi.PrettyMIDI(file_path)
                pitch_series = []
                for instrument in midi_data.instruments:
                    if not instrument.is_drum:
                        for note in instrument.notes:
                            pitch_series.append(note.pitch)
            else:
                # 使用简化的MIDI解析器
                pitch_series = SimpleMidiParser.extract_pitch_from_midi(file_path)

            if pitch_series:
                self.pitch_series = pitch_series
                print(f"   从MIDI文件提取了 {len(pitch_series)} 个音符")
                return True
            else:
                print(f"   ⚠️ MIDI文件中未找到音符数据")
                return False

        except Exception as e:
            print(f"   ❌ MIDI文件处理失败: {e}")
            return False

    def load_csv_file(self, file_path):
        """加载CSV文件"""
        try:
            df = pd.read_csv(file_path)

            # 尝试不同的列名
            pitch_columns = ['pitch', 'Pitch', 'PITCH', 'note', 'Note', 'midi_note']
            pitch_column = None

            for col in pitch_columns:
                if col in df.columns:
                    pitch_column = col
                    break

            if pitch_column:
                self.pitch_series = df[pitch_column].dropna().tolist()
                print(f"   从CSV文件提取了 {len(self.pitch_series)} 个音符")
                return True
            else:
                print(f"   ⚠️ CSV文件中未找到音高列")
                return False

        except Exception as e:
            print(f"   ❌ CSV文件处理失败: {e}")
            return False

    def analyze_file(self, file_path):
        """分析单个文件"""
        print(f"🎵 分析文件: {os.path.basename(file_path)}")

        # 加载文件
        success = False
        if file_path.endswith('.mid') or file_path.endswith('.midi'):
            success = self.load_midi_file(file_path)
        elif file_path.endswith('.csv'):
            success = self.load_csv_file(file_path)

        if not success or self.pitch_series is None or len(self.pitch_series) < 3:
            print(f"   ❌ 文件加载失败或数据不足")
            return None

        # 执行拓扑分析
        results = self.analyze_melody(self.pitch_series)

        # 添加文件信息
        if results:
            results['file_info'] = {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'note_count': len(self.pitch_series)
            }

        return results
    
    def analyze_melody(self, pitch_series):
        """执行完整的多尺度拓扑分析"""
        print("开始拓扑旋律分析...")
        
        # 学习全局势能场
        print("1. 学习势能场...")
        self.potential_field.fit(pitch_series)
        
        # 提取三音组序列
        print("2. 提取三音组序列...")
        triad_sequence = self._extract_triad_sequence(pitch_series)
        
        # 初始化三音组动力学分析
        print("3. 分析三音组动力学...")
        self.triad_dynamics = TriadManifoldDynamics(self.potential_field)
        
        # 分析三音组轨迹
        triad_trajectory = self.triad_dynamics.analyze_triad_trajectory(triad_sequence)
        
        # 计算全局拓扑指标
        print("4. 计算拓扑指标...")
        topology_metrics = self._compute_global_topology(triad_trajectory)
        
        self.analysis_results = {
            'potential_field': {
                'attractor_points': self.potential_field.attractor_points,
                'field_params': self.potential_field.field_params,
                'pitch_range': self.potential_field.pitch_range,
                'optimal_n_attractors': self.potential_field.optimal_n_attractors,
                'model_selection_results': self.potential_field.model_selection_results
            },
            'triad_trajectory': triad_trajectory,
            'topology_metrics': topology_metrics,
            'original_pitch_series': pitch_series
        }
        
        print("分析完成！")
        return self.analysis_results
    
    def _extract_triad_sequence(self, pitch_series):
        """提取重叠三音组序列"""
        sequence = []
        for i in range(len(pitch_series) - 2):
            triad = {
                'position': i,
                'notes': [
                    {'position': pitch_series[i], 'time': i},
                    {'position': pitch_series[i+1], 'time': i+1},
                    {'position': pitch_series[i+2], 'time': i+2}
                ]
            }
            sequence.append(triad)
        return sequence
    
    def _compute_global_topology(self, triad_trajectory):
        """计算全局拓扑指标"""
        if not triad_trajectory:
            return {}
        
        # 1. 吸引子收敛比例
        convergence_count = sum(1 for t in triad_trajectory 
                              if t['phase'] == "Attractor Convergence")
        convergence_ratio = convergence_count / len(triad_trajectory)
        
        # 2. 平均李雅普诺夫指数
        stability_values = [t['stability'] for t in triad_trajectory]
        mean_stability = np.mean(stability_values)
        stability_variance = np.var(stability_values)
        
        # 3. 拓扑熵
        phase_transitions = 0
        for i in range(1, len(triad_trajectory)):
            if triad_trajectory[i]['phase'] != triad_trajectory[i-1]['phase']:
                phase_transitions += 1
        topological_entropy = phase_transitions / len(triad_trajectory)
        
        # 4. 吸引子强度
        attractor_strength = self._compute_attractor_strength()
        
        # 5. 相位分布
        phase_distribution = {}
        for t in triad_trajectory:
            phase = t['phase']
            phase_distribution[phase] = phase_distribution.get(phase, 0) + 1
        
        # 归一化相位分布
        total = len(triad_trajectory)
        for phase in phase_distribution:
            phase_distribution[phase] /= total
        
        return {
            'convergence_ratio': convergence_ratio,
            'mean_stability': mean_stability,
            'stability_variance': stability_variance,
            'topological_entropy': topological_entropy,
            'attractor_strength': attractor_strength,
            'phase_distribution': phase_distribution,
            'trajectory_length': len(triad_trajectory)
        }
    
    def _compute_attractor_strength(self):
        """计算吸引子强度指标 - 修复版本"""
        if not self.potential_field.field_params:
            return 0.0

        # 计算真正反映音乐特征的吸引子强度
        # 基于吸引子数量、权重分布和音高分散度的综合指标

        # 1. 吸引子数量的影响
        n_attractors = len(self.potential_field.field_params)

        # 2. 权重分布的不均匀性（熵）
        weights = [α for μ, α, σ in self.potential_field.field_params]
        total_weight = sum(weights)
        if total_weight > 0:
            normalized_weights = [w / total_weight for w in weights]
            # 计算权重分布的熵（越不均匀，熵越小，强度越大）
            weight_entropy = -sum(w * np.log(w + 1e-10) for w in normalized_weights if w > 0)
            max_entropy = np.log(n_attractors) if n_attractors > 1 else 1.0
            weight_concentration = 1.0 - (weight_entropy / max_entropy) if max_entropy > 0 else 0.0
        else:
            weight_concentration = 0.0

        # 3. 吸引子位置的分散度
        positions = [μ for μ, α, σ in self.potential_field.field_params]
        if len(positions) > 1:
            position_variance = np.var(positions)
            # 归一化到音高范围
            pitch_range = self.potential_field.pitch_range
            if pitch_range and pitch_range[1] > pitch_range[0]:
                normalized_variance = position_variance / ((pitch_range[1] - pitch_range[0]) ** 2)
            else:
                normalized_variance = 0.0
        else:
            normalized_variance = 0.0

        # 4. 综合强度计算
        # 基础强度：主要吸引子的权重
        base_strength = max(weights) if weights else 0.0

        # 复杂度调制：吸引子数量和分散度
        complexity_factor = np.sqrt(n_attractors) * (1.0 + normalized_variance)

        # 集中度调制：权重分布的不均匀性
        concentration_factor = 1.0 + weight_concentration

        # 最终强度
        final_strength = base_strength * complexity_factor * concentration_factor

        return final_strength

    def analyze_all_works(self):
        """分析所有作品"""
        print("🎼 拓扑旋律分析系统")
        print("=" * 80)

        # 查找音乐文件 - 支持多个目录
        music_files = []
        search_directories = [
            '.',  # 当前目录
            './midi_files',  # midi_files文件夹
            './music',  # music文件夹
            './data',  # data文件夹
            './songs',  # songs文件夹
        ]

        search_patterns = ['*.mid', '*.midi', '*.csv']

        print("🔍 搜索音乐文件...")
        for directory in search_directories:
            if os.path.exists(directory):
                print(f"   检查目录: {directory}")
                for pattern in search_patterns:
                    search_path = os.path.join(directory, pattern)
                    files = glob.glob(search_path)
                    if files:
                        print(f"     找到 {len(files)} 个 {pattern} 文件")
                        music_files.extend(files)

        # 过滤出MIDI文件
        midi_files = [f for f in music_files if f.endswith(('.mid', '.midi'))]

        if not midi_files:
            print("\n❌ 未找到MIDI文件")
            print("💡 请将MIDI文件放在以下任一目录中:")
            print("   - 当前目录")
            print("   - ./midi_files/ 文件夹")
            print("   - ./music/ 文件夹")
            print("   - ./data/ 文件夹")
            print("   - ./songs/ 文件夹")
            print("\n🧪 现在使用测试数据进行演示:")
            self.run_test_analysis()
            return

        print(f"📁 找到 {len(music_files)} 个音乐文件")

        # 分析每个文件
        per_work_results = []
        for file_path in music_files:
            result = self.analyze_file(file_path)
            if result:
                per_work_results.append(result)

        if per_work_results:
            print(f"\n📊 成功分析了 {len(per_work_results)} 个作品")
            self.print_summary_statistics(per_work_results)

            # 保存批量分析结果
            self.save_batch_results(per_work_results)
        else:
            print("❌ 没有成功分析的作品")

    def run_test_analysis(self):
        """运行测试分析"""
        print("🧪 运行测试分析")

        test_melodies = [
            {
                'name': '平滑音阶',
                'pitches': [60, 62, 64, 65, 67, 69, 71, 72, 74, 76, 77, 79, 81, 83, 84]
            },
            {
                'name': '跳跃旋律',
                'pitches': [60, 72, 48, 75, 45, 78, 42, 81, 39, 84, 36, 87, 33, 90, 30]
            },
            {
                'name': '三音组模式',
                'pitches': [60, 62, 61, 63, 62, 64, 63, 65, 64, 66, 65, 67, 66, 68, 67]
            },
            {
                'name': '吸引子主导',
                'pitches': [60, 61, 60, 62, 60, 61, 60, 63, 60, 61, 60, 62, 60, 64, 60]
            },
            {
                'name': '混沌模式',
                'pitches': [60, 67, 55, 72, 48, 69, 52, 74, 46, 71, 49, 76, 44, 73, 51]
            }
        ]

        per_work_results = []
        for melody in test_melodies:
            print(f"\n🎵 分析测试旋律: {melody['name']}")

            # 直接分析旋律数据
            results = self.analyze_melody(melody['pitches'])
            if results:
                results['file_info'] = {
                    'file_path': f"test_{melody['name']}.mid",
                    'file_name': f"test_{melody['name']}.mid",
                    'note_count': len(melody['pitches'])
                }
                per_work_results.append(results)

        if per_work_results:
            print(f"\n📊 使用 {len(per_work_results)} 个测试样本进行分析")
            self.print_summary_statistics(per_work_results)
            self.save_batch_results(per_work_results, "test_batch_results.json")

    def print_summary_statistics(self, per_work_results):
        """打印摘要统计"""
        print("\n" + "="*80)
        print("🎼 拓扑旋律分析摘要报告")
        print("="*80)

        # 提取拓扑指标
        convergence_ratios = []
        mean_stabilities = []
        topological_entropies = []
        attractor_strengths = []
        trajectory_lengths = []

        for result in per_work_results:
            metrics = result.get('topology_metrics', {})
            convergence_ratios.append(metrics.get('convergence_ratio', 0))
            mean_stabilities.append(metrics.get('mean_stability', 0))
            topological_entropies.append(metrics.get('topological_entropy', 0))
            attractor_strengths.append(metrics.get('attractor_strength', 0))
            trajectory_lengths.append(metrics.get('trajectory_length', 0))

        print(f"📊 基础统计:")
        print(f"   分析作品数: {len(per_work_results)} 首")
        print(f"   平均音符数: {np.mean([r['file_info']['note_count'] for r in per_work_results]):.1f}")

        print(f"\n📈 收敛比例统计:")
        print(f"   均值: {np.mean(convergence_ratios):.4f}")
        print(f"   标准差: {np.std(convergence_ratios):.4f}")
        print(f"   范围: {np.min(convergence_ratios):.4f} ~ {np.max(convergence_ratios):.4f}")

        print(f"\n📈 平均稳定性统计:")
        print(f"   均值: {np.mean(mean_stabilities):.4f}")
        print(f"   标准差: {np.std(mean_stabilities):.4f}")
        print(f"   范围: {np.min(mean_stabilities):.4f} ~ {np.max(mean_stabilities):.4f}")

        print(f"\n📈 拓扑熵统计:")
        print(f"   均值: {np.mean(topological_entropies):.4f}")
        print(f"   标准差: {np.std(topological_entropies):.4f}")
        print(f"   范围: {np.min(topological_entropies):.4f} ~ {np.max(topological_entropies):.4f}")

        print(f"\n📈 吸引子强度统计:")
        print(f"   均值: {np.mean(attractor_strengths):.4f}")
        print(f"   标准差: {np.std(attractor_strengths):.4f}")
        print(f"   范围: {np.min(attractor_strengths):.4f} ~ {np.max(attractor_strengths):.4f}")

        # 相位分布统计
        all_phases = {}
        for result in per_work_results:
            phase_dist = result.get('topology_metrics', {}).get('phase_distribution', {})
            for phase, ratio in phase_dist.items():
                if phase not in all_phases:
                    all_phases[phase] = []
                all_phases[phase].append(ratio)

        print(f"\n🌊 相位分布统计:")
        for phase, ratios in all_phases.items():
            print(f"   {phase}: 平均 {np.mean(ratios):.1%} (标准差 {np.std(ratios):.1%})")

        # 吸引子分析（自适应选择结果）
        attractor_counts = []
        optimal_k_values = []
        for result in per_work_results:
            attractor_points = result.get('potential_field', {}).get('attractor_points', [])
            attractor_counts.append(len(attractor_points))

            # 获取模型选择结果
            if 'model_selection_results' in result.get('potential_field', {}):
                optimal_k = result['potential_field']['model_selection_results'].get('optimal_k', len(attractor_points))
                optimal_k_values.append(optimal_k)

        print(f"\n🎯 自适应吸引子分析:")
        print(f"   平均吸引子数量: {np.mean(attractor_counts):.1f}")
        print(f"   吸引子数量范围: {np.min(attractor_counts)} ~ {np.max(attractor_counts)}")
        print(f"   吸引子数量分布: {dict(zip(*np.unique(attractor_counts, return_counts=True)))}")

        if optimal_k_values:
            print(f"   BIC最优k值分布: {dict(zip(*np.unique(optimal_k_values, return_counts=True)))}")
            print(f"   平均最优k值: {np.mean(optimal_k_values):.1f}")

        # 拓扑特征解释
        print(f"\n✅ 拓扑特征解释:")
        high_convergence = sum(1 for r in convergence_ratios if r > 0.8)
        print(f"   高收敛作品 (>80%): {high_convergence}/{len(per_work_results)} ({high_convergence/len(per_work_results)*100:.1f}%)")

        stable_works = sum(1 for s in mean_stabilities if abs(s) < 0.1)
        print(f"   稳定系统 (|稳定性|<0.1): {stable_works}/{len(per_work_results)} ({stable_works/len(per_work_results)*100:.1f}%)")

        dynamic_works = sum(1 for e in topological_entropies if e > 0.2)
        print(f"   动态丰富作品 (熵>0.2): {dynamic_works}/{len(per_work_results)} ({dynamic_works/len(per_work_results)*100:.1f}%)")

        print(f"\n🎯 理论验证:")
        print(f"  • 连续势能场建模: ✅ 成功识别 {np.mean(attractor_counts):.1f} 个平均吸引子")
        print(f"  • 三音组流形动力学: ✅ 平均轨迹长度 {np.mean(trajectory_lengths):.1f}")
        print(f"  • 多尺度拓扑指标: ✅ 收敛比例、稳定性、拓扑熵")
        print(f"  • 相位空间分析: ✅ 识别 {len(all_phases)} 种动态相位")

        print(f"\n🎼 拓扑旋律分析完成！")

    def save_batch_results(self, per_work_results, filename="batch_analysis_results.json"):
        """保存批量分析结果"""

        def convert_numpy_types(obj):
            """递归转换numpy类型为Python原生类型"""
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            elif isinstance(obj, tuple):
                return tuple(convert_numpy_types(item) for item in obj)
            else:
                return obj

        # 准备可序列化的数据
        export_data = {
            'analysis_summary': {
                'total_works': len(per_work_results),
                'analysis_timestamp': str(np.datetime64('now')),
                'analyzer_config': {
                    'kernel_width': float(self.potential_field.kernel_width),
                    'max_attractors': int(self.potential_field.max_attractors),
                    'min_attractors': int(self.potential_field.min_attractors),
                    'adaptive_selection': True
                }
            },
            'individual_results': []
        }

        for result in per_work_results:
            # 简化每个结果，只保留关键信息
            simplified_result = {
                'file_info': convert_numpy_types(result.get('file_info', {})),
                'potential_field_summary': {
                    'attractor_count': len(result.get('potential_field', {}).get('attractor_points', [])),
                    'pitch_range': convert_numpy_types(result.get('potential_field', {}).get('pitch_range', [])),
                    'main_attractors': convert_numpy_types(result.get('potential_field', {}).get('attractor_points', [])[:3]),  # 前3个
                    'model_selection_results': convert_numpy_types(result.get('potential_field', {}).get('model_selection_results', {}))
                },
                'topology_metrics': convert_numpy_types(result.get('topology_metrics', {})),
                'trajectory_summary': {
                    'length': int(result.get('topology_metrics', {}).get('trajectory_length', 0)),
                    'phase_distribution': convert_numpy_types(result.get('topology_metrics', {}).get('phase_distribution', {}))
                }
            }
            export_data['individual_results'].append(simplified_result)

        # 计算汇总统计
        convergence_ratios = [r.get('topology_metrics', {}).get('convergence_ratio', 0) for r in per_work_results]
        mean_stabilities = [r.get('topology_metrics', {}).get('mean_stability', 0) for r in per_work_results]
        attractor_strengths = [r.get('topology_metrics', {}).get('attractor_strength', 0) for r in per_work_results]

        export_data['summary_statistics'] = {
            'convergence_ratio': {
                'mean': float(np.mean(convergence_ratios)),
                'std': float(np.std(convergence_ratios)),
                'min': float(np.min(convergence_ratios)),
                'max': float(np.max(convergence_ratios))
            },
            'mean_stability': {
                'mean': float(np.mean(mean_stabilities)),
                'std': float(np.std(mean_stabilities)),
                'min': float(np.min(mean_stabilities)),
                'max': float(np.max(mean_stabilities))
            },
            'attractor_strength': {
                'mean': float(np.mean(attractor_strengths)),
                'std': float(np.std(attractor_strengths)),
                'min': float(np.min(attractor_strengths)),
                'max': float(np.max(attractor_strengths))
            }
        }

        # 确保所有数据都是可序列化的
        export_data = convert_numpy_types(export_data)

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        print(f"批量分析结果已保存到: {filename}")

def generate_test_melody(length=50, complexity='medium'):
    """生成测试旋律数据"""
    np.random.seed(42)  # 确保可重复性

    if complexity == 'simple':
        # 简单旋律：围绕单一吸引子
        base = 60  # C4
        melody = [base + np.random.normal(0, 2) + 3*np.sin(i/5) for i in range(length)]

    elif complexity == 'medium':
        # 中等复杂度：双吸引子系统
        melody = []
        for i in range(length):
            if i < length//2:
                # 第一个吸引子区域
                base = 60 + 5*np.sin(i/8) + np.random.normal(0, 1.5)
            else:
                # 第二个吸引子区域
                base = 67 + 3*np.cos(i/6) + np.random.normal(0, 1.5)
            melody.append(base)

    elif complexity == 'complex':
        # 复杂旋律：多吸引子动态系统
        melody = []
        attractors = [60, 64, 67, 72]  # C, E, G, C
        for i in range(length):
            # 动态选择吸引子
            attractor_idx = int(4 * np.sin(i/10)**2)
            base_pitch = attractors[attractor_idx]

            # 添加噪声和趋势
            noise = np.random.normal(0, 1)
            trend = 2 * np.sin(i/15)
            melody.append(base_pitch + noise + trend)

    return melody

def print_analysis_summary(analyzer):
    """打印分析结果摘要"""
    if analyzer.analysis_results is None:
        print("没有分析结果可显示")
        return

    results = analyzer.analysis_results
    metrics = results['topology_metrics']

    print("\n" + "="*60)
    print("拓扑旋律分析结果摘要")
    print("="*60)

    print(f"\n【势能场信息】")
    print(f"音高范围: {results['potential_field']['pitch_range']}")
    print(f"吸引子数量: {len(results['potential_field']['attractor_points'])}")

    for i, (pos, strength) in enumerate(results['potential_field']['attractor_points']):
        print(f"  吸引子{i+1}: 位置={pos:.2f}, 强度={strength:.3f}")

    print(f"\n【动力学指标】")
    print(f"收敛比例: {metrics['convergence_ratio']:.3f}")
    print(f"平均稳定性: {metrics['mean_stability']:.3f}")
    print(f"稳定性方差: {metrics['stability_variance']:.3f}")
    print(f"拓扑熵: {metrics['topological_entropy']:.3f}")
    print(f"吸引子强度: {metrics['attractor_strength']:.3f}")

    print(f"\n【相位分布】")
    phase_dist = metrics['phase_distribution']
    for phase, ratio in phase_dist.items():
        print(f"  {phase}: {ratio:.1%}")

    print(f"\n【轨迹信息】")
    print(f"三音组数量: {metrics['trajectory_length']}")

    # 分析结果解释
    print(f"\n【结果解释】")
    if metrics['convergence_ratio'] > 0.6:
        print("✓ 旋律表现出强烈的吸引子收敛特性")
    elif metrics['convergence_ratio'] > 0.3:
        print("◐ 旋律具有中等程度的吸引子特性")
    else:
        print("✗ 旋律缺乏明显的吸引子结构")

    if metrics['topological_entropy'] > 0.5:
        print("✓ 旋律具有丰富的动态变化")
    elif metrics['topological_entropy'] > 0.2:
        print("◐ 旋律具有适度的动态变化")
    else:
        print("✗ 旋律变化相对单调")

    if abs(metrics['mean_stability']) < 0.1:
        print("✓ 旋律系统整体稳定")
    else:
        print("◐ 旋律系统存在不稳定因素")

def compare_models(pitch_series):
    """比较不同参数设置的模型效果"""
    print("开始模型对比分析...")

    # 不同参数配置
    configs = [
        {'kernel_width': 2.0, 'max_attractors': 6, 'name': 'Narrow Field (σ=2.0)'},
        {'kernel_width': 3.0, 'max_attractors': 8, 'name': 'Medium Field (σ=3.0)'},
        {'kernel_width': 5.0, 'max_attractors': 10, 'name': 'Wide Field (σ=5.0)'}
    ]

    results = []

    for config in configs:
        print(f"分析配置: {config['name']}")
        analyzer = TopologicalMelodyAnalyzer(
            kernel_width=config['kernel_width'],
            max_attractors=config['max_attractors']
        )
        result = analyzer.analyze_melody(pitch_series)
        results.append({
            'config': config,
            'analyzer': analyzer,
            'metrics': result['topology_metrics']
        })

    # 打印对比结果
    print(f"\n{'='*80}")
    print("模型对比结果")
    print(f"{'='*80}")

    print(f"{'配置':<20} {'收敛比例':<10} {'拓扑熵':<10} {'吸引子强度':<12} {'稳定性':<10}")
    print("-" * 80)

    for result in results:
        config = result['config']
        metrics = result['metrics']
        print(f"{config['name']:<20} "
              f"{metrics['convergence_ratio']:<10.3f} "
              f"{metrics['topological_entropy']:<10.3f} "
              f"{metrics['attractor_strength']:<12.3f} "
              f"{metrics['mean_stability']:<10.3f}")

    return results

def export_results_to_json(analyzer, filename):
    """导出分析结果到JSON文件"""
    if analyzer.analysis_results is None:
        print("没有分析结果可导出")
        return

    def convert_numpy_types(obj):
        """递归转换numpy类型为Python原生类型"""
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {key: convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(item) for item in obj]
        elif isinstance(obj, tuple):
            return tuple(convert_numpy_types(item) for item in obj)
        else:
            return obj

    # 准备可序列化的数据
    export_data = {
        'potential_field': convert_numpy_types(analyzer.analysis_results['potential_field']),
        'topology_metrics': convert_numpy_types(analyzer.analysis_results['topology_metrics']),
        'original_pitch_series': convert_numpy_types(analyzer.analysis_results['original_pitch_series'])
    }

    # 简化轨迹数据（只保留关键信息）
    simplified_trajectory = []
    for t in analyzer.analysis_results['triad_trajectory']:
        simplified_trajectory.append({
            'centroid': float(t['position'][0]),
            'velocity': float(t['position'][1]),
            'curvature': float(t['position'][2]),
            'field_gradient': float(t['field_interaction'][0]),
            'field_potential': float(t['field_interaction'][1]),
            'stability': float(t['stability']),
            'phase': str(t['phase']),
            'triad_index': int(t['triad_index'])
        })

    export_data['simplified_trajectory'] = simplified_trajectory

    # 确保所有数据都是可序列化的
    export_data = convert_numpy_types(export_data)

    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(export_data, f, indent=2, ensure_ascii=False)

    print(f"分析结果已导出到: {filename}")

def main():
    """主函数：拓扑旋律分析器"""
    print("🎼 拓扑旋律分析器 - 基于张量场的动力学分析")
    print("="*60)

    try:
        # 创建分析器实例
        analyzer = TopologicalMelodyAnalyzer(kernel_width=3.0, max_attractors=8)

        # 分析所有作品（会自动搜索MIDI文件或运行测试）
        analyzer.analyze_all_works()

    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        print("🧪 建议检查文件路径或使用测试数据")

def analyze_single_file(file_path):
    """分析单个文件的便捷函数"""
    print(f"🎵 单文件分析模式")
    print("="*40)

    analyzer = TopologicalMelodyAnalyzer(kernel_width=3.0, max_attractors=8)
    result = analyzer.analyze_file(file_path)

    if result:
        print_analysis_summary(analyzer)

        # 保存结果
        filename = f"single_analysis_{os.path.splitext(os.path.basename(file_path))[0]}.json"
        export_results_to_json(analyzer, filename)

        return result
    else:
        print("❌ 文件分析失败")
        return None

if __name__ == "__main__":
    main()
