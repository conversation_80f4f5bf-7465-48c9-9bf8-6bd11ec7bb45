<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright 2014 <PERSON> <<EMAIL>> -->
<component>
 <id>org.julialang.julia</id>
 <name>Julia</name>
 <launchable type="desktop-id">julia.desktop</launchable>
 <metadata_license>CC-BY-SA-3.0</metadata_license>
 <project_license>MIT and LGPL-2.1+ and GPL-2.0+</project_license>
 <summary>High-performance programming language for technical computing</summary>
 <provides>
  <binary>julia</binary>
 </provides>
 <description>
  <p>
   Julia is a high-level, high-performance dynamic programming language for
   technical computing, with syntax that is familiar to users of other
   technical computing environments. It provides a sophisticated compiler,
   distributed parallel execution, numerical accuracy, and an extensive
   mathematical function library.
  </p>
  <p>
   The library, largely written in Julia itself,
   also integrates mature, best-of-breed C and Fortran libraries for linear
   algebra, random number generation, signal processing, and string processing.
   In addition, the Julia developer community is contributing a number of
   external packages through <PERSON>’s built-in package manager at a rapid pace.
  </p>
 </description>
 <screenshots>
  <screenshot type="default">
   <image>https://julialang.org/assets/images/julia-gnome.png</image>
  </screenshot>
 </screenshots>
 <url type="homepage">https://julialang.org/</url>
</component>
