<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>C Interface · The Julia Language</title><meta name="title" content="C Interface · The Julia Language"/><meta property="og:title" content="C Interface · The Julia Language"/><meta property="twitter:title" content="C Interface · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox" checked/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="base.html">Essentials</a></li><li><a class="tocitem" href="collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="math.html">Mathematics</a></li><li><a class="tocitem" href="numbers.html">Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="arrays.html">Arrays</a></li><li><a class="tocitem" href="parallel.html">Tasks</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="constants.html">Constants</a></li><li><a class="tocitem" href="file.html">Filesystem</a></li><li><a class="tocitem" href="io-network.html">I/O and Network</a></li><li><a class="tocitem" href="punctuation.html">Punctuation</a></li><li><a class="tocitem" href="sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="reflection.html">Reflection and introspection</a></li><li class="is-active"><a class="tocitem" href="c.html">C Interface</a><ul class="internal"><li class="toplevel"><a class="tocitem" href="#LLVM-Interface"><span>LLVM Interface</span></a></li></ul></li><li><a class="tocitem" href="libc.html">C Standard Library</a></li><li><a class="tocitem" href="stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Base</a></li><li class="is-active"><a href="c.html">C Interface</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="c.html">C Interface</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/base/c.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="C-Interface"><a class="docs-heading-anchor" href="#C-Interface">C Interface</a><a id="C-Interface-1"></a><a class="docs-heading-anchor-permalink" href="#C-Interface" title="Permalink"></a></h1><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@ccall" href="#Base.@ccall"><code>Base.@ccall</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@ccall library.function_name(argvalue1::argtype1, ...)::returntype
@ccall function_name(argvalue1::argtype1, ...)::returntype
@ccall $function_pointer(argvalue1::argtype1, ...)::returntype</code></pre><p>Call a function in a C-exported shared library, specified by <code>library.function_name</code>, where <code>library</code> is a string constant or literal. The library may be omitted, in which case the <code>function_name</code> is resolved in the current process. Alternatively, <code>@ccall</code> may also be used to call a function pointer <code>$function_pointer</code>, such as one returned by <code>dlsym</code>.</p><p>Each <code>argvalue</code> to <code>@ccall</code> is converted to the corresponding <code>argtype</code>, by automatic insertion of calls to <code>unsafe_convert(argtype, cconvert(argtype, argvalue))</code>. (See also the documentation for <a href="c.html#Base.unsafe_convert"><code>unsafe_convert</code></a> and <a href="c.html#Base.cconvert"><code>cconvert</code></a> for further details.) In most cases, this simply results in a call to <code>convert(argtype, argvalue)</code>.</p><p><strong>Examples</strong></p><pre><code class="nohighlight hljs">@ccall strlen(s::Cstring)::Csize_t</code></pre><p>This calls the C standard library function:</p><pre><code class="nohighlight hljs">size_t strlen(char *)</code></pre><p>with a Julia variable named <code>s</code>. See also <code>ccall</code>.</p><p>Varargs are supported with the following convention:</p><pre><code class="nohighlight hljs">@ccall printf(&quot;%s = %d&quot;::Cstring ; &quot;foo&quot;::Cstring, foo::Cint)::Cint</code></pre><p>The semicolon is used to separate required arguments (of which there must be at least one) from variadic arguments.</p><p>Example using an external library:</p><pre><code class="nohighlight hljs"># C signature of g_uri_escape_string:
# char *g_uri_escape_string(const char *unescaped, const char *reserved_chars_allowed, gboolean allow_utf8);

const glib = &quot;libglib-2.0&quot;
@ccall glib.g_uri_escape_string(my_uri::Cstring, &quot;:/&quot;::Cstring, true::Cint)::Cstring</code></pre><p>The string literal could also be used directly before the function name, if desired <code>&quot;libglib-2.0&quot;.g_uri_escape_string(...</code></p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/c.jl#L360-L407">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="ccall" href="#ccall"><code>ccall</code></a> — <span class="docstring-category">Keyword</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ccall((function_name, library), returntype, (argtype1, ...), argvalue1, ...)
ccall(function_name, returntype, (argtype1, ...), argvalue1, ...)
ccall(function_pointer, returntype, (argtype1, ...), argvalue1, ...)</code></pre><p>Call a function in a C-exported shared library, specified by the tuple <code>(function_name, library)</code>, where each component is either a string or symbol. Instead of specifying a library, one can also use a <code>function_name</code> symbol or string, which is resolved in the current process. Alternatively, <code>ccall</code> may also be used to call a function pointer <code>function_pointer</code>, such as one returned by <code>dlsym</code>.</p><p>Note that the argument type tuple must be a literal tuple, and not a tuple-valued variable or expression.</p><p>Each <code>argvalue</code> to the <code>ccall</code> will be converted to the corresponding <code>argtype</code>, by automatic insertion of calls to <code>unsafe_convert(argtype, cconvert(argtype, argvalue))</code>. (See also the documentation for <a href="c.html#Base.unsafe_convert"><code>unsafe_convert</code></a> and <a href="c.html#Base.cconvert"><code>cconvert</code></a> for further details.) In most cases, this simply results in a call to <code>convert(argtype, argvalue)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L1304-L1322">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Intrinsics.cglobal" href="#Core.Intrinsics.cglobal"><code>Core.Intrinsics.cglobal</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cglobal((symbol, library) [, type=Cvoid])</code></pre><p>Obtain a pointer to a global variable in a C-exported shared library, specified exactly as in <a href="c.html#ccall"><code>ccall</code></a>. Returns a <code>Ptr{Type}</code>, defaulting to <code>Ptr{Cvoid}</code> if no <code>Type</code> argument is supplied. The values can be read or written by <a href="c.html#Base.unsafe_load"><code>unsafe_load</code></a> or <a href="c.html#Base.unsafe_store!"><code>unsafe_store!</code></a>, respectively.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/c.jl#L7-L16">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@cfunction" href="#Base.@cfunction"><code>Base.@cfunction</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@cfunction(callable, ReturnType, (ArgumentTypes...,)) -&gt; Ptr{Cvoid}
@cfunction($callable, ReturnType, (ArgumentTypes...,)) -&gt; CFunction</code></pre><p>Generate a C-callable function pointer from the Julia function <code>callable</code> for the given type signature. To pass the return value to a <code>ccall</code>, use the argument type <code>Ptr{Cvoid}</code> in the signature.</p><p>Note that the argument type tuple must be a literal tuple, and not a tuple-valued variable or expression (although it can include a splat expression). And that these arguments will be evaluated in global scope during compile-time (not deferred until runtime). Adding a &#39;<span>$</span>&#39; in front of the function argument changes this to instead create a runtime closure over the local variable <code>callable</code> (this is not supported on all architectures).</p><p>See <a href="../manual/calling-c-and-fortran-code.html#Calling-C-and-Fortran-Code">manual section on ccall and cfunction usage</a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; function foo(x::Int, y::Int)
           return x + y
       end

julia&gt; @cfunction(foo, Int, (Int, Int))
Ptr{Cvoid} @0x000000001b82fcd0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/c.jl#L38-L63">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.CFunction" href="#Base.CFunction"><code>Base.CFunction</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">CFunction struct</code></pre><p>Garbage-collection handle for the return value from <code>@cfunction</code> when the first argument is annotated with &#39;<span>$</span>&#39;. Like all <code>cfunction</code> handles, it should be passed to <code>ccall</code> as a <code>Ptr{Cvoid}</code>, and will be converted automatically at the call site to the appropriate type.</p><p>See <a href="c.html#Base.@cfunction"><code>@cfunction</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/c.jl#L19-L28">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unsafe_convert" href="#Base.unsafe_convert"><code>Base.unsafe_convert</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unsafe_convert(T, x)</code></pre><p>Convert <code>x</code> to a C argument of type <code>T</code> where the input <code>x</code> must be the return value of <code>cconvert(T, ...)</code>.</p><p>In cases where <a href="base.html#Base.convert"><code>convert</code></a> would need to take a Julia object and turn it into a <code>Ptr</code>, this function should be used to define and perform that conversion.</p><p>Be careful to ensure that a Julia reference to <code>x</code> exists as long as the result of this function will be used. Accordingly, the argument <code>x</code> to this function should never be an expression, only a variable name or field reference. For example, <code>x=a.b.c</code> is acceptable, but <code>x=[a,b,c]</code> is not.</p><p>The <code>unsafe</code> prefix on this function indicates that using the result of this function after the <code>x</code> argument to this function is no longer accessible to the program may cause undefined behavior, including program corruption or segfaults, at any later time.</p><p>See also <a href="c.html#Base.cconvert"><code>cconvert</code></a></p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/pointer.jl#L34-L54">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.cconvert" href="#Base.cconvert"><code>Base.cconvert</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cconvert(T,x)</code></pre><p>Convert <code>x</code> to a value to be passed to C code as type <code>T</code>, typically by calling <code>convert(T, x)</code>.</p><p>In cases where <code>x</code> cannot be safely converted to <code>T</code>, unlike <a href="base.html#Base.convert"><code>convert</code></a>, <code>cconvert</code> may return an object of a type different from <code>T</code>, which however is suitable for <a href="c.html#Base.unsafe_convert"><code>unsafe_convert</code></a> to handle. The result of this function should be kept valid (for the GC) until the result of <a href="c.html#Base.unsafe_convert"><code>unsafe_convert</code></a> is not needed anymore. This can be used to allocate memory that will be accessed by the <code>ccall</code>. If multiple objects need to be allocated, a tuple of the objects can be used as return value.</p><p>Neither <code>convert</code> nor <code>cconvert</code> should take a Julia object and turn it into a <code>Ptr</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/essentials.jl#L671-L684">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unsafe_load" href="#Base.unsafe_load"><code>Base.unsafe_load</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unsafe_load(p::Ptr{T}, i::Integer=1)
unsafe_load(p::Ptr{T}, order::Symbol)
unsafe_load(p::Ptr{T}, i::Integer, order::Symbol)</code></pre><p>Load a value of type <code>T</code> from the address of the <code>i</code>th element (1-indexed) starting at <code>p</code>. This is equivalent to the C expression <code>p[i-1]</code>. Optionally, an atomic memory ordering can be provided.</p><p>The <code>unsafe</code> prefix on this function indicates that no validation is performed on the pointer <code>p</code> to ensure that it is valid. Like C, the programmer is responsible for ensuring that referenced memory is not freed or garbage collected while invoking this function. Incorrect usage may segfault your program or return garbage answers. Unlike C, dereferencing memory region allocated as different type may be valid provided that the types are compatible.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.10</header><div class="admonition-body"><p>The <code>order</code> argument is available as of Julia 1.10.</p></div></div><p>See also: <a href="multi-threading.html#atomic"><code>atomic</code></a></p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/pointer.jl#L133-L152">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unsafe_store!" href="#Base.unsafe_store!"><code>Base.unsafe_store!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unsafe_store!(p::Ptr{T}, x, i::Integer=1)
unsafe_store!(p::Ptr{T}, x, order::Symbol)
unsafe_store!(p::Ptr{T}, x, i::Integer, order::Symbol)</code></pre><p>Store a value of type <code>T</code> to the address of the <code>i</code>th element (1-indexed) starting at <code>p</code>. This is equivalent to the C expression <code>p[i-1] = x</code>. Optionally, an atomic memory ordering can be provided.</p><p>The <code>unsafe</code> prefix on this function indicates that no validation is performed on the pointer <code>p</code> to ensure that it is valid. Like C, the programmer is responsible for ensuring that referenced memory is not freed or garbage collected while invoking this function. Incorrect usage may segfault your program. Unlike C, storing memory region allocated as different type may be valid provided that that the types are compatible.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.10</header><div class="admonition-body"><p>The <code>order</code> argument is available as of Julia 1.10.</p></div></div><p>See also: <a href="multi-threading.html#atomic"><code>atomic</code></a></p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/pointer.jl#L159-L178">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unsafe_modify!" href="#Base.unsafe_modify!"><code>Base.unsafe_modify!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unsafe_modify!(p::Ptr{T}, op, x, [order::Symbol]) -&gt; Pair</code></pre><p>These atomically perform the operations to get and set a memory address after applying the function <code>op</code>. If supported by the hardware (for example, atomic increment), this may be optimized to the appropriate hardware instruction, otherwise its execution will be similar to:</p><pre><code class="nohighlight hljs">y = unsafe_load(p)
z = op(y, x)
unsafe_store!(p, z)
return y =&gt; z</code></pre><p>The <code>unsafe</code> prefix on this function indicates that no validation is performed on the pointer <code>p</code> to ensure that it is valid. Like C, the programmer is responsible for ensuring that referenced memory is not freed or garbage collected while invoking this function. Incorrect usage may segfault your program.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.10</header><div class="admonition-body"><p>This function requires at least Julia 1.10.</p></div></div><p>See also: <a href="base.html#Base.modifyproperty!"><code>modifyproperty!</code></a>, <a href="multi-threading.html#atomic"><code>atomic</code></a></p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/pointer.jl#L186-L208">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unsafe_replace!" href="#Base.unsafe_replace!"><code>Base.unsafe_replace!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unsafe_replace!(p::Ptr{T}, expected, desired,
               [success_order::Symbol[, fail_order::Symbol=success_order]]) -&gt; (; old, success::Bool)</code></pre><p>These atomically perform the operations to get and conditionally set a memory address to a given value. If supported by the hardware, this may be optimized to the appropriate hardware instruction, otherwise its execution will be similar to:</p><pre><code class="nohighlight hljs">y = unsafe_load(p, fail_order)
ok = y === expected
if ok
    unsafe_store!(p, desired, success_order)
end
return (; old = y, success = ok)</code></pre><p>The <code>unsafe</code> prefix on this function indicates that no validation is performed on the pointer <code>p</code> to ensure that it is valid. Like C, the programmer is responsible for ensuring that referenced memory is not freed or garbage collected while invoking this function. Incorrect usage may segfault your program.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.10</header><div class="admonition-body"><p>This function requires at least Julia 1.10.</p></div></div><p>See also: <a href="base.html#Base.replaceproperty!"><code>replaceproperty!</code></a>, <a href="multi-threading.html#atomic"><code>atomic</code></a></p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/pointer.jl#L213-L237">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unsafe_swap!" href="#Base.unsafe_swap!"><code>Base.unsafe_swap!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unsafe_swap!(p::Ptr{T}, x, [order::Symbol])</code></pre><p>These atomically perform the operations to simultaneously get and set a memory address. If supported by the hardware, this may be optimized to the appropriate hardware instruction, otherwise its execution will be similar to:</p><pre><code class="nohighlight hljs">y = unsafe_load(p)
unsafe_store!(p, x)
return y</code></pre><p>The <code>unsafe</code> prefix on this function indicates that no validation is performed on the pointer <code>p</code> to ensure that it is valid. Like C, the programmer is responsible for ensuring that referenced memory is not freed or garbage collected while invoking this function. Incorrect usage may segfault your program.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.10</header><div class="admonition-body"><p>This function requires at least Julia 1.10.</p></div></div><p>See also: <a href="base.html#Base.swapproperty!"><code>swapproperty!</code></a>, <a href="multi-threading.html#atomic"><code>atomic</code></a></p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/pointer.jl#L247-L267">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unsafe_copyto!-Union{Tuple{T}, Tuple{Ptr{T}, Ptr{T}, Any}} where T" href="#Base.unsafe_copyto!-Union{Tuple{T}, Tuple{Ptr{T}, Ptr{T}, Any}} where T"><code>Base.unsafe_copyto!</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unsafe_copyto!(dest::Ptr{T}, src::Ptr{T}, N)</code></pre><p>Copy <code>N</code> elements from a source pointer to a destination, with no checking. The size of an element is determined by the type of the pointers.</p><p>The <code>unsafe</code> prefix on this function indicates that no validation is performed on the pointers <code>dest</code> and <code>src</code> to ensure that they are valid. Incorrect usage may corrupt or segfault your program, in the same manner as C.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L255-L264">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unsafe_copyto!-Tuple{Array, Any, Array, Any, Any}" href="#Base.unsafe_copyto!-Tuple{Array, Any, Array, Any, Any}"><code>Base.unsafe_copyto!</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unsafe_copyto!(dest::Array, do, src::Array, so, N)</code></pre><p>Copy <code>N</code> elements from a source array to a destination, starting at the linear index <code>so</code> in the source and <code>do</code> in the destination (1-indexed).</p><p>The <code>unsafe</code> prefix on this function indicates that no validation is performed to ensure that N is inbounds on either array. Incorrect usage may corrupt or segfault your program, in the same manner as C.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L272-L281">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.copyto!" href="#Base.copyto!"><code>Base.copyto!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">copyto!(B::AbstractMatrix, ir_dest::AbstractUnitRange, jr_dest::AbstractUnitRange,
        tM::AbstractChar,
        M::AbstractVecOrMat, ir_src::AbstractUnitRange, jr_src::AbstractUnitRange) -&gt; B</code></pre><p>Efficiently copy elements of matrix <code>M</code> to <code>B</code> conditioned on the character parameter <code>tM</code> as follows:</p><table><tr><th style="text-align: right"><code>tM</code></th><th style="text-align: left">Destination</th><th style="text-align: left">Source</th></tr><tr><td style="text-align: right"><code>&#39;N&#39;</code></td><td style="text-align: left"><code>B[ir_dest, jr_dest]</code></td><td style="text-align: left"><code>M[ir_src, jr_src]</code></td></tr><tr><td style="text-align: right"><code>&#39;T&#39;</code></td><td style="text-align: left"><code>B[ir_dest, jr_dest]</code></td><td style="text-align: left"><code>transpose(M)[ir_src, jr_src]</code></td></tr><tr><td style="text-align: right"><code>&#39;C&#39;</code></td><td style="text-align: left"><code>B[ir_dest, jr_dest]</code></td><td style="text-align: left"><code>adjoint(M)[ir_src, jr_src]</code></td></tr></table><p>The elements <code>B[ir_dest, jr_dest]</code> are overwritten. Furthermore, the index range parameters must satisfy <code>length(ir_dest) == length(ir_src)</code> and <code>length(jr_dest) == length(jr_src)</code>.</p><p>See also <a href="../stdlib/LinearAlgebra.html#LinearAlgebra.copy_transpose!"><code>copy_transpose!</code></a> and <a href="../stdlib/LinearAlgebra.html#LinearAlgebra.copy_adjoint!"><code>copy_adjoint!</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LinearAlgebra/src/matmul.jl#L714-L733">source</a></section><section><div><pre><code class="language-julia hljs">copyto!(dest::AbstractMatrix, src::UniformScaling)</code></pre><p>Copies a <a href="../stdlib/LinearAlgebra.html#LinearAlgebra.UniformScaling"><code>UniformScaling</code></a> onto a matrix.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p>In Julia 1.0 this method only supported a square destination matrix. Julia 1.1. added support for a rectangular matrix.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LinearAlgebra/src/uniformscaling.jl#L366-L374">source</a></section><section><div><pre><code class="language-julia hljs">copyto!(dest, do, src, so, N)</code></pre><p>Copy <code>N</code> elements from collection <code>src</code> starting at the linear index <code>so</code>, to array <code>dest</code> starting at the index <code>do</code>. Return <code>dest</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L288-L293">source</a></section><section><div><pre><code class="language-julia hljs">copyto!(dest::AbstractArray, src) -&gt; dest</code></pre><p>Copy all elements from collection <code>src</code> to array <code>dest</code>, whose length must be greater than or equal to the length <code>n</code> of <code>src</code>. The first <code>n</code> elements of <code>dest</code> are overwritten, the other elements are left untouched.</p><p>See also <a href="arrays.html#Base.copy!"><code>copy!</code></a>, <a href="base.html#Base.copy"><code>copy</code></a>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = [1., 0., 3., 0., 5.];

julia&gt; y = zeros(7);

julia&gt; copyto!(y, x);

julia&gt; y
7-element Vector{Float64}:
 1.0
 0.0
 3.0
 0.0
 5.0
 0.0
 0.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L1024-L1053">source</a></section><section><div><pre><code class="language-julia hljs">copyto!(dest, Rdest::CartesianIndices, src, Rsrc::CartesianIndices) -&gt; dest</code></pre><p>Copy the block of <code>src</code> in the range of <code>Rsrc</code> to the block of <code>dest</code> in the range of <code>Rdest</code>. The sizes of the two regions must match.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = zeros(5, 5);

julia&gt; B = [1 2; 3 4];

julia&gt; Ainds = CartesianIndices((2:3, 2:3));

julia&gt; Binds = CartesianIndices(B);

julia&gt; copyto!(A, Ainds, B, Binds)
5×5 Matrix{Float64}:
 0.0  0.0  0.0  0.0  0.0
 0.0  1.0  2.0  0.0  0.0
 0.0  3.0  4.0  0.0  0.0
 0.0  0.0  0.0  0.0  0.0
 0.0  0.0  0.0  0.0  0.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multidimensional.jl#L1177-L1201">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.pointer" href="#Base.pointer"><code>Base.pointer</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">pointer(array [, index])</code></pre><p>Get the native address of an array or string, optionally at a given location <code>index</code>.</p><p>This function is &quot;unsafe&quot;. Be careful to ensure that a Julia reference to <code>array</code> exists as long as this pointer will be used. The <a href="base.html#Base.GC.@preserve"><code>GC.@preserve</code></a> macro should be used to protect the <code>array</code> argument from garbage collection within a given block of code.</p><p>Calling <a href="c.html#Core.Ref"><code>Ref(array[, index])</code></a> is generally preferable to this function as it guarantees validity.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/cstring.jl#L41-L52">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unsafe_wrap-Union{Tuple{N}, Tuple{T}, Tuple{Union{Type{Array}, Type{Array{T}}, Type{Array{T, N}}}, Ptr{T}, NTuple{N, Int64}}} where {T, N}" href="#Base.unsafe_wrap-Union{Tuple{N}, Tuple{T}, Tuple{Union{Type{Array}, Type{Array{T}}, Type{Array{T, N}}}, Ptr{T}, NTuple{N, Int64}}} where {T, N}"><code>Base.unsafe_wrap</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unsafe_wrap(Array, pointer::Ptr{T}, dims; own = false)</code></pre><p>Wrap a Julia <code>Array</code> object around the data at the address given by <code>pointer</code>, without making a copy.  The pointer element type <code>T</code> determines the array element type. <code>dims</code> is either an integer (for a 1d array) or a tuple of the array dimensions. <code>own</code> optionally specifies whether Julia should take ownership of the memory, calling <code>free</code> on the pointer when the array is no longer referenced.</p><p>This function is labeled &quot;unsafe&quot; because it will crash if <code>pointer</code> is not a valid memory address to data of the requested length. Unlike <a href="c.html#Base.unsafe_load"><code>unsafe_load</code></a> and <a href="c.html#Base.unsafe_store!"><code>unsafe_store!</code></a>, the programmer is responsible also for ensuring that the underlying data is not accessed through two arrays of different element type, similar to the strict aliasing rule in C.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/pointer.jl#L93-L107">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.pointer_from_objref" href="#Base.pointer_from_objref"><code>Base.pointer_from_objref</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">pointer_from_objref(x)</code></pre><p>Get the memory address of a Julia object as a <code>Ptr</code>. The existence of the resulting <code>Ptr</code> will not protect the object from garbage collection, so you must ensure that the object remains referenced for the whole time that the <code>Ptr</code> will be used.</p><p>This function may not be called on immutable objects, since they do not have stable memory addresses.</p><p>See also <a href="c.html#Base.unsafe_pointer_to_objref"><code>unsafe_pointer_to_objref</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/pointer.jl#L289-L300">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unsafe_pointer_to_objref" href="#Base.unsafe_pointer_to_objref"><code>Base.unsafe_pointer_to_objref</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unsafe_pointer_to_objref(p::Ptr)</code></pre><p>Convert a <code>Ptr</code> to an object reference. Assumes the pointer refers to a valid heap-allocated Julia object. If this is not the case, undefined behavior results, hence this function is considered &quot;unsafe&quot; and should be used with care.</p><p>See also <a href="c.html#Base.pointer_from_objref"><code>pointer_from_objref</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/pointer.jl#L278-L286">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.disable_sigint" href="#Base.disable_sigint"><code>Base.disable_sigint</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">disable_sigint(f::Function)</code></pre><p>Disable Ctrl-C handler during execution of a function on the current task, for calling external code that may call julia code that is not interrupt safe. Intended to be called using <code>do</code> block syntax as follows:</p><pre><code class="nohighlight hljs">disable_sigint() do
    # interrupt-unsafe code
    ...
end</code></pre><p>This is not needed on worker threads (<code>Threads.threadid() != 1</code>) since the <code>InterruptException</code> will only be delivered to the master thread. External functions that do not call julia code or julia runtime automatically disable sigint during their execution.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/c.jl#L148-L164">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.reenable_sigint" href="#Base.reenable_sigint"><code>Base.reenable_sigint</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">reenable_sigint(f::Function)</code></pre><p>Re-enable Ctrl-C handler during execution of a function. Temporarily reverses the effect of <a href="c.html#Base.disable_sigint"><code>disable_sigint</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/c.jl#L173-L178">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.exit_on_sigint" href="#Base.exit_on_sigint"><code>Base.exit_on_sigint</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">exit_on_sigint(on::Bool)</code></pre><p>Set <code>exit_on_sigint</code> flag of the julia runtime.  If <code>false</code>, Ctrl-C (SIGINT) is capturable as <a href="base.html#Core.InterruptException"><code>InterruptException</code></a> in <code>try</code> block. This is the default behavior in REPL, any code run via <code>-e</code> and <code>-E</code> and in Julia script run with <code>-i</code> option.</p><p>If <code>true</code>, <code>InterruptException</code> is not thrown by Ctrl-C.  Running code upon such event requires <a href="base.html#Base.atexit"><code>atexit</code></a>.  This is the default behavior in Julia script run without <code>-i</code> option.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p>Function <code>exit_on_sigint</code> requires at least Julia 1.5.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/c.jl#L187-L201">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.systemerror" href="#Base.systemerror"><code>Base.systemerror</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">systemerror(sysfunc[, errno::Cint=Libc.errno()])
systemerror(sysfunc, iftrue::Bool)</code></pre><p>Raises a <code>SystemError</code> for <code>errno</code> with the descriptive string <code>sysfunc</code> if <code>iftrue</code> is <code>true</code></p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/error.jl#L169-L174">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.windowserror" href="#Base.windowserror"><code>Base.windowserror</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">windowserror(sysfunc[, code::UInt32=Libc.GetLastError()])
windowserror(sysfunc, iftrue::Bool)</code></pre><p>Like <a href="c.html#Base.systemerror"><code>systemerror</code></a>, but for Windows API functions that use <a href="libc.html#Base.Libc.GetLastError"><code>GetLastError</code></a> to return an error code instead of setting <a href="libc.html#Base.Libc.errno"><code>errno</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/error.jl#L183-L189">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Ptr" href="#Core.Ptr"><code>Core.Ptr</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Ptr{T}</code></pre><p>A memory address referring to data of type <code>T</code>.  However, there is no guarantee that the memory is actually valid, or that it actually represents data of the specified type.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/pointer.jl#L3-L8">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Ref" href="#Core.Ref"><code>Core.Ref</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Ref{T}</code></pre><p>An object that safely references data of type <code>T</code>. This type is guaranteed to point to valid, Julia-allocated memory of the correct type. The underlying data is protected from freeing by the garbage collector as long as the <code>Ref</code> itself is referenced.</p><p>In Julia, <code>Ref</code> objects are dereferenced (loaded or stored) with <code>[]</code>.</p><p>Creation of a <code>Ref</code> to a value <code>x</code> of type <code>T</code> is usually written <code>Ref(x)</code>. Additionally, for creating interior pointers to containers (such as Array or Ptr), it can be written <code>Ref(a, i)</code> for creating a reference to the <code>i</code>-th element of <code>a</code>.</p><p><code>Ref{T}()</code> creates a reference to a value of type <code>T</code> without initialization. For a bitstype <code>T</code>, the value will be whatever currently resides in the memory allocated. For a non-bitstype <code>T</code>, the reference will be undefined and attempting to dereference it will result in an error, &quot;UndefRefError: access to undefined reference&quot;.</p><p>To check if a <code>Ref</code> is an undefined reference, use <a href="c.html#Base.isassigned-Tuple{Base.RefValue}"><code>isassigned(ref::RefValue)</code></a>. For example, <code>isassigned(Ref{T}())</code> is <code>false</code> if <code>T</code> is not a bitstype. If <code>T</code> is a bitstype, <code>isassigned(Ref{T}())</code> will always be true.</p><p>When passed as a <code>ccall</code> argument (either as a <code>Ptr</code> or <code>Ref</code> type), a <code>Ref</code> object will be converted to a native pointer to the data it references. For most <code>T</code>, or when converted to a <code>Ptr{Cvoid}</code>, this is a pointer to the object data. When <code>T</code> is an <code>isbits</code> type, this value may be safely mutated, otherwise mutation is strictly undefined behavior.</p><p>As a special case, setting <code>T = Any</code> will instead cause the creation of a pointer to the reference itself when converted to a <code>Ptr{Any}</code> (a <code>jl_value_t const* const*</code> if T is immutable, else a <code>jl_value_t *const *</code>). When converted to a <code>Ptr{Cvoid}</code>, it will still return a pointer to the data region as for any other <code>T</code>.</p><p>A <code>C_NULL</code> instance of <code>Ptr</code> can be passed to a <code>ccall</code> <code>Ref</code> argument to initialize it.</p><p><strong>Use in broadcasting</strong></p><p><code>Ref</code> is sometimes used in broadcasting in order to treat the referenced values as a scalar.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; r = Ref(5) # Create a Ref with an initial value
Base.RefValue{Int64}(5)

julia&gt; r[] # Getting a value from a Ref
5

julia&gt; r[] = 7 # Storing a new value in a Ref
7

julia&gt; r # The Ref now contains 7
Base.RefValue{Int64}(7)

julia&gt; isa.(Ref([1,2,3]), [Array, Dict, Int]) # Treat reference values as scalar during broadcasting
3-element BitVector:
 1
 0
 0

julia&gt; Ref{Function}()  # Undefined reference to a non-bitstype, Function
Base.RefValue{Function}(#undef)

julia&gt; try
           Ref{Function}()[] # Dereferencing an undefined reference will result in an error
       catch e
           println(e)
       end
UndefRefError()

julia&gt; Ref{Int64}()[]; # A reference to a bitstype refers to an undetermined value if not given

julia&gt; isassigned(Ref{Int64}()) # A reference to a bitstype is always assigned
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/refpointer.jl#L3-L78">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isassigned-Tuple{Base.RefValue}" href="#Base.isassigned-Tuple{Base.RefValue}"><code>Base.isassigned</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isassigned(ref::RefValue) -&gt; Bool</code></pre><p>Test whether the given <a href="c.html#Core.Ref"><code>Ref</code></a> is associated with a value. This is always true for a <a href="c.html#Core.Ref"><code>Ref</code></a> of a bitstype object. Return <code>false</code> if the reference is undefined.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; ref = Ref{Function}()
Base.RefValue{Function}(#undef)

julia&gt; isassigned(ref)
false

julia&gt; ref[] = (foobar(x) = x)
foobar (generic function with 1 method)

julia&gt; isassigned(ref)
true

julia&gt; isassigned(Ref{Int}())
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/refvalue.jl#L11-L35">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cchar" href="#Base.Cchar"><code>Base.Cchar</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Cchar</code></pre><p>Equivalent to the native <code>char</code> c-type.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/c.jl#L86-L90">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cuchar" href="#Base.Cuchar"><code>Base.Cuchar</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Cuchar</code></pre><p>Equivalent to the native <code>unsigned char</code> c-type (<a href="numbers.html#Core.UInt8"><code>UInt8</code></a>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ctypes.jl#L6-L10">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cshort" href="#Base.Cshort"><code>Base.Cshort</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Cshort</code></pre><p>Equivalent to the native <code>signed short</code> c-type (<a href="numbers.html#Core.Int16"><code>Int16</code></a>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ctypes.jl#L14-L18">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cstring" href="#Base.Cstring"><code>Base.Cstring</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Cstring</code></pre><p>A C-style string composed of the native character type <a href="c.html#Base.Cchar"><code>Cchar</code></a>s. <code>Cstring</code>s are NUL-terminated. For C-style strings composed of the native wide character type, see <a href="c.html#Base.Cwstring"><code>Cwstring</code></a>. For more information about string interoperability with C, see the <a href="../manual/calling-c-and-fortran-code.html#man-bits-types">manual</a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/cstring.jl#L18-L27">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cushort" href="#Base.Cushort"><code>Base.Cushort</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Cushort</code></pre><p>Equivalent to the native <code>unsigned short</code> c-type (<a href="numbers.html#Core.UInt16"><code>UInt16</code></a>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ctypes.jl#L22-L26">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cint" href="#Base.Cint"><code>Base.Cint</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Cint</code></pre><p>Equivalent to the native <code>signed int</code> c-type (<a href="numbers.html#Core.Int32"><code>Int32</code></a>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ctypes.jl#L30-L34">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cuint" href="#Base.Cuint"><code>Base.Cuint</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Cuint</code></pre><p>Equivalent to the native <code>unsigned int</code> c-type (<a href="numbers.html#Core.UInt32"><code>UInt32</code></a>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ctypes.jl#L38-L42">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Clong" href="#Base.Clong"><code>Base.Clong</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Clong</code></pre><p>Equivalent to the native <code>signed long</code> c-type.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/c.jl#L104-L108">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Culong" href="#Base.Culong"><code>Base.Culong</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Culong</code></pre><p>Equivalent to the native <code>unsigned long</code> c-type.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/c.jl#L111-L115">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Clonglong" href="#Base.Clonglong"><code>Base.Clonglong</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Clonglong</code></pre><p>Equivalent to the native <code>signed long long</code> c-type (<a href="numbers.html#Core.Int64"><code>Int64</code></a>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ctypes.jl#L86-L90">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Culonglong" href="#Base.Culonglong"><code>Base.Culonglong</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Culonglong</code></pre><p>Equivalent to the native <code>unsigned long long</code> c-type (<a href="numbers.html#Core.UInt64"><code>UInt64</code></a>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ctypes.jl#L94-L98">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cintmax_t" href="#Base.Cintmax_t"><code>Base.Cintmax_t</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Cintmax_t</code></pre><p>Equivalent to the native <code>intmax_t</code> c-type (<a href="numbers.html#Core.Int64"><code>Int64</code></a>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ctypes.jl#L70-L74">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cuintmax_t" href="#Base.Cuintmax_t"><code>Base.Cuintmax_t</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Cuintmax_t</code></pre><p>Equivalent to the native <code>uintmax_t</code> c-type (<a href="numbers.html#Core.UInt64"><code>UInt64</code></a>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ctypes.jl#L78-L82">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Csize_t" href="#Base.Csize_t"><code>Base.Csize_t</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Csize_t</code></pre><p>Equivalent to the native <code>size_t</code> c-type (<code>UInt</code>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ctypes.jl#L54-L58">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cssize_t" href="#Base.Cssize_t"><code>Base.Cssize_t</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Cssize_t</code></pre><p>Equivalent to the native <code>ssize_t</code> c-type.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ctypes.jl#L62-L66">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cptrdiff_t" href="#Base.Cptrdiff_t"><code>Base.Cptrdiff_t</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Cptrdiff_t</code></pre><p>Equivalent to the native <code>ptrdiff_t</code> c-type (<code>Int</code>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ctypes.jl#L46-L50">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cwchar_t" href="#Base.Cwchar_t"><code>Base.Cwchar_t</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Cwchar_t</code></pre><p>Equivalent to the native <code>wchar_t</code> c-type (<a href="numbers.html#Core.Int32"><code>Int32</code></a>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/c.jl#L118-L122">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cwstring" href="#Base.Cwstring"><code>Base.Cwstring</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Cwstring</code></pre><p>A C-style string composed of the native wide character type <a href="c.html#Base.Cwchar_t"><code>Cwchar_t</code></a>s. <code>Cwstring</code>s are NUL-terminated. For C-style strings composed of the native character type, see <a href="c.html#Base.Cstring"><code>Cstring</code></a>. For more information about string interoperability with C, see the <a href="../manual/calling-c-and-fortran-code.html#man-bits-types">manual</a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/cstring.jl#L5-L15">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cfloat" href="#Base.Cfloat"><code>Base.Cfloat</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Cfloat</code></pre><p>Equivalent to the native <code>float</code> c-type (<a href="numbers.html#Core.Float32"><code>Float32</code></a>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ctypes.jl#L102-L106">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cdouble" href="#Base.Cdouble"><code>Base.Cdouble</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Cdouble</code></pre><p>Equivalent to the native <code>double</code> c-type (<a href="numbers.html#Core.Float64"><code>Float64</code></a>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ctypes.jl#L110-L114">source</a></section></article><h1 id="LLVM-Interface"><a class="docs-heading-anchor" href="#LLVM-Interface">LLVM Interface</a><a id="LLVM-Interface-1"></a><a class="docs-heading-anchor-permalink" href="#LLVM-Interface" title="Permalink"></a></h1><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Intrinsics.llvmcall" href="#Core.Intrinsics.llvmcall"><code>Core.Intrinsics.llvmcall</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">llvmcall(fun_ir::String, returntype, Tuple{argtype1, ...}, argvalue1, ...)
llvmcall((mod_ir::String, entry_fn::String), returntype, Tuple{argtype1, ...}, argvalue1, ...)
llvmcall((mod_bc::Vector{UInt8}, entry_fn::String), returntype, Tuple{argtype1, ...}, argvalue1, ...)</code></pre><p>Call the LLVM code provided in the first argument. There are several ways to specify this first argument:</p><ul><li>as a literal string, representing function-level IR (similar to an LLVM <code>define</code> block), with arguments are available as consecutive unnamed SSA variables (%0, %1, etc.);</li><li>as a 2-element tuple, containing a string of module IR and a string representing the name of the entry-point function to call;</li><li>as a 2-element tuple, but with the module provided as an <code>Vector{UInt8}</code> with bitcode.</li></ul><p>Note that contrary to <code>ccall</code>, the argument types must be specified as a tuple type, and not a tuple of types. All types, as well as the LLVM code, should be specified as literals, and not as variables or expressions (it may be necessary to use <code>@eval</code> to generate these literals).</p><p><a href="https://llvm.org/docs/OpaquePointers.html">Opaque pointers</a> (written as <code>ptr</code>) are not allowed in the LLVM code.</p><p>See <a href="https://github.com/JuliaLang/julia/blob/v1.11.5/test/llvmcall.jl"><code>test/llvmcall.jl</code></a> for usage examples.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L1325-L1349">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="reflection.html">« Reflection and introspection</a><a class="docs-footer-nextpage" href="libc.html">C Standard Library »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
