<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>File Events · The Julia Language</title><meta name="title" content="File Events · The Julia Language"/><meta property="og:title" content="File Events · The Julia Language"/><meta property="twitter:title" content="File Events · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li class="is-active"><a class="tocitem" href="FileWatching.html">File Events</a><ul class="internal"><li class="toplevel"><a class="tocitem" href="#Pidfile"><span>Pidfile</span></a></li><li><a class="tocitem" href="#Primary-Functions"><span>Primary Functions</span></a></li><li><a class="tocitem" href="#Helper-Functions"><span>Helper Functions</span></a></li></ul></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="FileWatching.html">File Events</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="FileWatching.html">File Events</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/stdlib/FileWatching/docs/src/index.md" title="View source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="lib-filewatching"><a class="docs-heading-anchor" href="#lib-filewatching">File Events</a><a id="lib-filewatching-1"></a><a class="docs-heading-anchor-permalink" href="#lib-filewatching" title="Permalink"></a></h1><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="FileWatching.poll_fd" href="#FileWatching.poll_fd"><code>FileWatching.poll_fd</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">poll_fd(fd, timeout_s::Real=-1; readable=false, writable=false)</code></pre><p>Monitor a file descriptor <code>fd</code> for changes in the read or write availability, and with a timeout given by <code>timeout_s</code> seconds.</p><p>The keyword arguments determine which of read and/or write status should be monitored; at least one of them must be set to <code>true</code>.</p><p>The returned value is an object with boolean fields <code>readable</code>, <code>writable</code>, and <code>timedout</code>, giving the result of the polling.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/FileWatching/src/FileWatching.jl#L704-L715">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="FileWatching.poll_file" href="#FileWatching.poll_file"><code>FileWatching.poll_file</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">poll_file(path::AbstractString, interval_s::Real=5.007, timeout_s::Real=-1) -&gt; (previous::StatStruct, current)</code></pre><p>Monitor a file for changes by polling every <code>interval_s</code> seconds until a change occurs or <code>timeout_s</code> seconds have elapsed. The <code>interval_s</code> should be a long period; the default is 5.007 seconds.</p><p>Returns a pair of status objects <code>(previous, current)</code> when a change is detected. The <code>previous</code> status is always a <code>StatStruct</code>, but it may have all of the fields zeroed (indicating the file didn&#39;t previously exist, or wasn&#39;t previously accessible).</p><p>The <code>current</code> status object may be a <code>StatStruct</code>, an <code>EOFError</code> (indicating the timeout elapsed), or some other <code>Exception</code> subtype (if the <code>stat</code> operation failed - for example, if the path does not exist).</p><p>To determine when a file was modified, compare <code>current isa StatStruct &amp;&amp; mtime(prev) != mtime(current)</code> to detect notification of changes. However, using <a href="FileWatching.html#FileWatching.watch_file"><code>watch_file</code></a> for this operation is preferred, since it is more reliable and efficient, although in some situations it may not be available.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/FileWatching/src/FileWatching.jl#L864-L881">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="FileWatching.watch_file" href="#FileWatching.watch_file"><code>FileWatching.watch_file</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">watch_file(path::AbstractString, timeout_s::Real=-1)</code></pre><p>Watch file or directory <code>path</code> for changes until a change occurs or <code>timeout_s</code> seconds have elapsed. This function does not poll the file system and instead uses platform-specific functionality to receive notifications from the operating system (e.g. via inotify on Linux). See the NodeJS documentation linked below for details.</p><p>The returned value is an object with boolean fields <code>renamed</code>, <code>changed</code>, and <code>timedout</code>, giving the result of watching the file.</p><p>This behavior of this function varies slightly across platforms. See <a href="https://nodejs.org/api/fs.html#fs_caveats">https://nodejs.org/api/fs.html#fs_caveats</a> for more detailed information.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/FileWatching/src/FileWatching.jl#L759-L772">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="FileWatching.watch_folder" href="#FileWatching.watch_folder"><code>FileWatching.watch_folder</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">watch_folder(path::AbstractString, timeout_s::Real=-1)</code></pre><p>Watches a file or directory <code>path</code> for changes until a change has occurred or <code>timeout_s</code> seconds have elapsed. This function does not poll the file system and instead uses platform-specific functionality to receive notifications from the operating system (e.g. via inotify on Linux). See the NodeJS documentation linked below for details.</p><p>This will continuing tracking changes for <code>path</code> in the background until <code>unwatch_folder</code> is called on the same <code>path</code>.</p><p>The returned value is an pair where the first field is the name of the changed file (if available) and the second field is an object with boolean fields <code>renamed</code>, <code>changed</code>, and <code>timedout</code>, giving the event.</p><p>This behavior of this function varies slightly across platforms. See <a href="https://nodejs.org/api/fs.html#fs_caveats">https://nodejs.org/api/fs.html#fs_caveats</a> for more detailed information.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/FileWatching/src/FileWatching.jl#L790-L807">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="FileWatching.unwatch_folder" href="#FileWatching.unwatch_folder"><code>FileWatching.unwatch_folder</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unwatch_folder(path::AbstractString)</code></pre><p>Stop background tracking of changes for <code>path</code>. It is not recommended to do this while another task is waiting for <code>watch_folder</code> to return on the same path, as the result may be unpredictable.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/FileWatching/src/FileWatching.jl#L848-L854">source</a></section></article><h1 id="Pidfile"><a class="docs-heading-anchor" href="#Pidfile">Pidfile</a><a id="Pidfile-1"></a><a class="docs-heading-anchor-permalink" href="#Pidfile" title="Permalink"></a></h1><p>A simple utility tool for creating advisory pidfiles (lock files).</p><h2 id="Primary-Functions"><a class="docs-heading-anchor" href="#Primary-Functions">Primary Functions</a><a id="Primary-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Primary-Functions" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="FileWatching.Pidfile.mkpidlock" href="#FileWatching.Pidfile.mkpidlock"><code>FileWatching.Pidfile.mkpidlock</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mkpidlock([f::Function], at::String, [pid::Cint]; kwopts...)
mkpidlock(at::String, proc::Process; kwopts...)</code></pre><p>Create a pidfile lock for the path &quot;at&quot; for the current process or the process identified by pid or proc. Can take a function to execute once locked, for usage in <code>do</code> blocks, after which the lock will be automatically closed. If the lock fails and <code>wait</code> is false, then an error is thrown.</p><p>The lock will be released by either <code>close</code>, a <code>finalizer</code>, or shortly after <code>proc</code> exits. Make sure the return value is live through the end of the critical section of your program, so the <code>finalizer</code> does not reclaim it early.</p><p>Optional keyword arguments:</p><ul><li><code>mode</code>: file access mode (modified by the process umask). Defaults to world-readable.</li><li><code>poll_interval</code>: Specify the maximum time to between attempts (if <code>watch_file</code> doesn&#39;t work)</li><li><code>stale_age</code>: Delete an existing pidfile (ignoring the lock) if it is older than this many seconds, based on its mtime.   The file won&#39;t be deleted until 5x longer than this if the pid in the file appears that it may be valid.   Or 25x longer if <code>refresh</code> is overridden to 0 to disable lock refreshing.   By default this is disabled (<code>stale_age</code> = 0), but a typical recommended value would be about 3-5x an   estimated normal completion time.</li><li><code>refresh</code>: Keeps a lock from becoming stale by updating the mtime every interval of time that passes.   By default, this is set to <code>stale_age/2</code>, which is the recommended value.</li><li><code>wait</code>: If true, block until we get the lock, if false, raise error if lock fails.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/FileWatching/src/pidfile.jl#L19-L43">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="FileWatching.Pidfile.trymkpidlock" href="#FileWatching.Pidfile.trymkpidlock"><code>FileWatching.Pidfile.trymkpidlock</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">trymkpidlock([f::Function], at::String, [pid::Cint]; kwopts...)
trymkpidlock(at::String, proc::Process; kwopts...)</code></pre><p>Like <code>mkpidlock</code> except returns <code>false</code> instead of waiting if the file is already locked.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.10</header><div class="admonition-body"><p>This function requires at least Julia 1.10.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/FileWatching/src/pidfile.jl#L46-L54">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.close-Tuple{FileWatching.Pidfile.LockMonitor}" href="#Base.close-Tuple{FileWatching.Pidfile.LockMonitor}"><code>Base.close</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">close(lock::LockMonitor)</code></pre><p>Release a pidfile lock.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/FileWatching/src/pidfile.jl#L321-L325">source</a></section></article><h2 id="Helper-Functions"><a class="docs-heading-anchor" href="#Helper-Functions">Helper Functions</a><a id="Helper-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Helper-Functions" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="FileWatching.Pidfile.open_exclusive" href="#FileWatching.Pidfile.open_exclusive"><code>FileWatching.Pidfile.open_exclusive</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">open_exclusive(path::String; mode, poll_interval, wait, stale_age, refresh) :: File</code></pre><p>Create a new a file for read-write advisory-exclusive access. If <code>wait</code> is <code>false</code> then error out if the lock files exist otherwise block until we get the lock.</p><p>For a description of the keyword arguments, see <a href="FileWatching.html#FileWatching.Pidfile.mkpidlock"><code>mkpidlock</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/FileWatching/src/pidfile.jl#L228-L236">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="FileWatching.Pidfile.tryopen_exclusive" href="#FileWatching.Pidfile.tryopen_exclusive"><code>FileWatching.Pidfile.tryopen_exclusive</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">tryopen_exclusive(path::String, mode::Integer = 0o444) :: Union{Void, File}</code></pre><p>Try to create a new file for read-write advisory-exclusive access, return nothing if it already exists.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/FileWatching/src/pidfile.jl#L209-L214">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="FileWatching.Pidfile.write_pidfile" href="#FileWatching.Pidfile.write_pidfile"><code>FileWatching.Pidfile.write_pidfile</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">write_pidfile(io, pid)</code></pre><p>Write our pidfile format to an open IO descriptor.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/FileWatching/src/pidfile.jl#L135-L139">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="FileWatching.Pidfile.parse_pidfile" href="#FileWatching.Pidfile.parse_pidfile"><code>FileWatching.Pidfile.parse_pidfile</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">parse_pidfile(file::Union{IO, String}) =&gt; (pid, hostname, age)</code></pre><p>Attempt to parse our pidfile format, replaced an element with (0, &quot;&quot;, 0.0), respectively, for any read that failed.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/FileWatching/src/pidfile.jl#L144-L149">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="FileWatching.Pidfile.stale_pidfile" href="#FileWatching.Pidfile.stale_pidfile"><code>FileWatching.Pidfile.stale_pidfile</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">stale_pidfile(path::String, stale_age::Real, refresh::Real) :: Bool</code></pre><p>Helper function for <code>open_exclusive</code> for deciding if a pidfile is stale.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/FileWatching/src/pidfile.jl#L192-L196">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="FileWatching.Pidfile.isvalidpid" href="#FileWatching.Pidfile.isvalidpid"><code>FileWatching.Pidfile.isvalidpid</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isvalidpid(hostname::String, pid::Cuint) :: Bool</code></pre><p>Attempt to conservatively estimate whether pid is a valid process id.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/FileWatching/src/pidfile.jl#L174-L178">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.touch-Tuple{FileWatching.Pidfile.LockMonitor}" href="#Base.Filesystem.touch-Tuple{FileWatching.Pidfile.LockMonitor}"><code>Base.Filesystem.touch</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.touch(::Pidfile.LockMonitor)</code></pre><p>Update the <code>mtime</code> on the lock, to indicate it is still fresh.</p><p>See also the <code>refresh</code> keyword in the <a href="FileWatching.html#FileWatching.Pidfile.mkpidlock"><code>mkpidlock</code></a> constructor.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/FileWatching/src/pidfile.jl#L126-L132">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="Downloads.html">« Downloads</a><a class="docs-footer-nextpage" href="Future.html">Future »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
