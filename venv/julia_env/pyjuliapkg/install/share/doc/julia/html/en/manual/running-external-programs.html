<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Running External Programs · The Julia Language</title><meta name="title" content="Running External Programs · The Julia Language"/><meta property="og:title" content="Running External Programs · The Julia Language"/><meta property="twitter:title" content="Running External Programs · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li class="is-active"><a class="tocitem" href="running-external-programs.html">Running External Programs</a><ul class="internal"><li><a class="tocitem" href="#command-interpolation"><span>Interpolation</span></a></li><li><a class="tocitem" href="#Quoting"><span>Quoting</span></a></li><li><a class="tocitem" href="#Pipelines"><span>Pipelines</span></a></li><li><a class="tocitem" href="#Cmd-Objects"><span><code>Cmd</code> Objects</span></a></li></ul></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="running-external-programs.html">Running External Programs</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="running-external-programs.html">Running External Programs</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/running-external-programs.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Running-External-Programs"><a class="docs-heading-anchor" href="#Running-External-Programs">Running External Programs</a><a id="Running-External-Programs-1"></a><a class="docs-heading-anchor-permalink" href="#Running-External-Programs" title="Permalink"></a></h1><p>Julia borrows backtick notation for commands from the shell, Perl, and Ruby. However, in Julia, writing</p><pre><code class="language-julia-repl hljs">julia&gt; `echo hello`
`echo hello`</code></pre><p>differs in several aspects from the behavior in various shells, Perl, or Ruby:</p><ul><li>Instead of immediately running the command, backticks create a <a href="../base/base.html#Base.Cmd"><code>Cmd</code></a> object to represent the command. You can use this object to connect the command to others via pipes, <a href="../base/base.html#Base.run"><code>run</code></a> it, and <a href="../base/io-network.html#Base.read"><code>read</code></a> or <a href="../base/io-network.html#Base.write"><code>write</code></a> to it.</li><li>When the command is run, Julia does not capture its output unless you specifically arrange for it to. Instead, the output of the command by default goes to <a href="../base/io-network.html#Base.stdout"><code>stdout</code></a> as it would using <code>libc</code>&#39;s <code>system</code> call.</li><li>The command is never run with a shell. Instead, Julia parses the command syntax directly, appropriately interpolating variables and splitting on words as the shell would, respecting shell quoting syntax. The command is run as <code>julia</code>&#39;s immediate child process, using <code>fork</code> and <code>exec</code> calls.</li></ul><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The following assumes a Posix environment as on Linux or MacOS. On Windows, many similar commands, such as <code>echo</code> and <code>dir</code>, are not external programs and instead are built into the shell <code>cmd.exe</code> itself. One option to run these commands is to invoke <code>cmd.exe</code>, for example <code>cmd /C echo hello</code>. Alternatively Julia can be run inside a Posix environment such as Cygwin.</p></div></div><p>Here&#39;s a simple example of running an external program:</p><pre><code class="language-julia-repl hljs">julia&gt; mycommand = `echo hello`
`echo hello`

julia&gt; typeof(mycommand)
Cmd

julia&gt; run(mycommand);
hello</code></pre><p>The <code>hello</code> is the output of the <code>echo</code> command, sent to <a href="../base/io-network.html#Base.stdout"><code>stdout</code></a>. If the external command fails to run successfully, the run method throws an <a href="../base/base.html#Base.ProcessFailedException"><code>ProcessFailedException</code></a>.</p><p>If you want to read the output of the external command, <a href="../base/io-network.html#Base.read"><code>read</code></a> or <a href="../base/io-network.html#Base.readchomp"><code>readchomp</code></a> can be used instead:</p><pre><code class="language-julia-repl hljs">julia&gt; read(`echo hello`, String)
&quot;hello\n&quot;

julia&gt; readchomp(`echo hello`)
&quot;hello&quot;</code></pre><p>More generally, you can use <a href="../base/io-network.html#Base.open"><code>open</code></a> to read from or write to an external command.</p><pre><code class="language-julia-repl hljs">julia&gt; open(`less`, &quot;w&quot;, stdout) do io
           for i = 1:3
               println(io, i)
           end
       end
1
2
3</code></pre><p>The program name and the individual arguments in a command can be accessed and iterated over as if the command were an array of strings:</p><pre><code class="language-julia-repl hljs">julia&gt; collect(`echo &quot;foo bar&quot;`)
2-element Vector{String}:
 &quot;echo&quot;
 &quot;foo bar&quot;

julia&gt; `echo &quot;foo bar&quot;`[2]
&quot;foo bar&quot;</code></pre><h2 id="command-interpolation"><a class="docs-heading-anchor" href="#command-interpolation">Interpolation</a><a id="command-interpolation-1"></a><a class="docs-heading-anchor-permalink" href="#command-interpolation" title="Permalink"></a></h2><p>Suppose you want to do something a bit more complicated and use the name of a file in the variable <code>file</code> as an argument to a command. You can use <code>$</code> for interpolation much as you would in a string literal (see <a href="../devdocs/ast.html#Strings">Strings</a>):</p><pre><code class="language-julia-repl hljs">julia&gt; file = &quot;/etc/passwd&quot;
&quot;/etc/passwd&quot;

julia&gt; `sort $file`
`sort /etc/passwd`</code></pre><p>A common pitfall when running external programs via a shell is that if a file name contains characters that are special to the shell, they may cause undesirable behavior. Suppose, for example, rather than <code>/etc/passwd</code>, we wanted to sort the contents of the file <code>/Volumes/External HD/data.csv</code>. Let&#39;s try it:</p><pre><code class="language-julia-repl hljs">julia&gt; file = &quot;/Volumes/External HD/data.csv&quot;
&quot;/Volumes/External HD/data.csv&quot;

julia&gt; `sort $file`
`sort &#39;/Volumes/External HD/data.csv&#39;`</code></pre><p>How did the file name get quoted? Julia knows that <code>file</code> is meant to be interpolated as a single argument, so it quotes the word for you. Actually, that is not quite accurate: the value of <code>file</code> is never interpreted by a shell, so there&#39;s no need for actual quoting; the quotes are inserted only for presentation to the user. This will even work if you interpolate a value as part of a shell word:</p><pre><code class="language-julia-repl hljs">julia&gt; path = &quot;/Volumes/External HD&quot;
&quot;/Volumes/External HD&quot;

julia&gt; name = &quot;data&quot;
&quot;data&quot;

julia&gt; ext = &quot;csv&quot;
&quot;csv&quot;

julia&gt; `sort $path/$name.$ext`
`sort &#39;/Volumes/External HD/data.csv&#39;`</code></pre><p>As you can see, the space in the <code>path</code> variable is appropriately escaped. But what if you <em>want</em> to interpolate multiple words? In that case, just use an array (or any other iterable container):</p><pre><code class="language-julia-repl hljs">julia&gt; files = [&quot;/etc/passwd&quot;,&quot;/Volumes/External HD/data.csv&quot;]
2-element Vector{String}:
 &quot;/etc/passwd&quot;
 &quot;/Volumes/External HD/data.csv&quot;

julia&gt; `grep foo $files`
`grep foo /etc/passwd &#39;/Volumes/External HD/data.csv&#39;`</code></pre><p>If you interpolate an array as part of a shell word, Julia emulates the shell&#39;s <code>{a,b,c}</code> argument generation:</p><pre><code class="language-julia-repl hljs">julia&gt; names = [&quot;foo&quot;,&quot;bar&quot;,&quot;baz&quot;]
3-element Vector{String}:
 &quot;foo&quot;
 &quot;bar&quot;
 &quot;baz&quot;

julia&gt; `grep xylophone $names.txt`
`grep xylophone foo.txt bar.txt baz.txt`</code></pre><p>Moreover, if you interpolate multiple arrays into the same word, the shell&#39;s Cartesian product generation behavior is emulated:</p><pre><code class="language-julia-repl hljs">julia&gt; names = [&quot;foo&quot;,&quot;bar&quot;,&quot;baz&quot;]
3-element Vector{String}:
 &quot;foo&quot;
 &quot;bar&quot;
 &quot;baz&quot;

julia&gt; exts = [&quot;aux&quot;,&quot;log&quot;]
2-element Vector{String}:
 &quot;aux&quot;
 &quot;log&quot;

julia&gt; `rm -f $names.$exts`
`rm -f foo.aux foo.log bar.aux bar.log baz.aux baz.log`</code></pre><p>Since you can interpolate literal arrays, you can use this generative functionality without needing to create temporary array objects first:</p><pre><code class="language-julia-repl hljs">julia&gt; `rm -rf $[&quot;foo&quot;,&quot;bar&quot;,&quot;baz&quot;,&quot;qux&quot;].$[&quot;aux&quot;,&quot;log&quot;,&quot;pdf&quot;]`
`rm -rf foo.aux foo.log foo.pdf bar.aux bar.log bar.pdf baz.aux baz.log baz.pdf qux.aux qux.log qux.pdf`</code></pre><h2 id="Quoting"><a class="docs-heading-anchor" href="#Quoting">Quoting</a><a id="Quoting-1"></a><a class="docs-heading-anchor-permalink" href="#Quoting" title="Permalink"></a></h2><p>Inevitably, one wants to write commands that aren&#39;t quite so simple, and it becomes necessary to use quotes. Here&#39;s a simple example of a Perl one-liner at a shell prompt:</p><pre><code class="nohighlight hljs">sh$ perl -le &#39;$|=1; for (0..3) { print }&#39;
0
1
2
3</code></pre><p>The Perl expression needs to be in single quotes for two reasons: so that spaces don&#39;t break the expression into multiple shell words, and so that uses of Perl variables like <code>$|</code> (yes, that&#39;s the name of a variable in Perl), don&#39;t cause interpolation. In other instances, you may want to use double quotes so that interpolation <em>does</em> occur:</p><pre><code class="nohighlight hljs">sh$ first=&quot;A&quot;
sh$ second=&quot;B&quot;
sh$ perl -le &#39;$|=1; print for @ARGV&#39; &quot;1: $first&quot; &quot;2: $second&quot;
1: A
2: B</code></pre><p>In general, the Julia backtick syntax is carefully designed so that you can just cut-and-paste shell commands as is into backticks and they will work: the escaping, quoting, and interpolation behaviors are the same as the shell&#39;s. The only difference is that the interpolation is integrated and aware of Julia&#39;s notion of what is a single string value, and what is a container for multiple values. Let&#39;s try the above two examples in Julia:</p><pre><code class="language-julia-repl hljs">julia&gt; A = `perl -le &#39;$|=1; for (0..3) { print }&#39;`
`perl -le &#39;$|=1; for (0..3) { print }&#39;`

julia&gt; run(A);
0
1
2
3

julia&gt; first = &quot;A&quot;; second = &quot;B&quot;;

julia&gt; B = `perl -le &#39;print for @ARGV&#39; &quot;1: $first&quot; &quot;2: $second&quot;`
`perl -le &#39;print for @ARGV&#39; &#39;1: A&#39; &#39;2: B&#39;`

julia&gt; run(B);
1: A
2: B</code></pre><p>The results are identical, and Julia&#39;s interpolation behavior mimics the shell&#39;s with some improvements due to the fact that Julia supports first-class iterable objects while most shells use strings split on spaces for this, which introduces ambiguities. When trying to port shell commands to Julia, try cut and pasting first. Since Julia shows commands to you before running them, you can easily and safely just examine its interpretation without doing any damage.</p><h2 id="Pipelines"><a class="docs-heading-anchor" href="#Pipelines">Pipelines</a><a id="Pipelines-1"></a><a class="docs-heading-anchor-permalink" href="#Pipelines" title="Permalink"></a></h2><p>Shell metacharacters, such as <code>|</code>, <code>&amp;</code>, and <code>&gt;</code>, need to be quoted (or escaped) inside of Julia&#39;s backticks:</p><pre><code class="language-julia-repl hljs">julia&gt; run(`echo hello &#39;|&#39; sort`);
hello | sort

julia&gt; run(`echo hello \| sort`);
hello | sort</code></pre><p>This expression invokes the <code>echo</code> command with three words as arguments: <code>hello</code>, <code>|</code>, and <code>sort</code>. The result is that a single line is printed: <code>hello | sort</code>. How, then, does one construct a pipeline? Instead of using <code>&#39;|&#39;</code> inside of backticks, one uses <a href="../base/base.html#Base.pipeline-Tuple{Any, Any, Any, Vararg{Any}}"><code>pipeline</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; run(pipeline(`echo hello`, `sort`));
hello</code></pre><p>This pipes the output of the <code>echo</code> command to the <code>sort</code> command. Of course, this isn&#39;t terribly interesting since there&#39;s only one line to sort, but we can certainly do much more interesting things:</p><pre><code class="language-julia-repl hljs">julia&gt; run(pipeline(`cut -d: -f3 /etc/passwd`, `sort -n`, `tail -n5`))
210
211
212
213
214</code></pre><p>This prints the highest five user IDs on a UNIX system. The <code>cut</code>, <code>sort</code> and <code>tail</code> commands are all spawned as immediate children of the current <code>julia</code> process, with no intervening shell process. Julia itself does the work to setup pipes and connect file descriptors that is normally done by the shell. Since Julia does this itself, it retains better control and can do some things that shells cannot.</p><p>Julia can run multiple commands in parallel:</p><pre><code class="language-julia-repl hljs">julia&gt; run(`echo hello` &amp; `echo world`);
world
hello</code></pre><p>The order of the output here is non-deterministic because the two <code>echo</code> processes are started nearly simultaneously, and race to make the first write to the <a href="../base/io-network.html#Base.stdout"><code>stdout</code></a> descriptor they share with each other and the <code>julia</code> parent process. Julia lets you pipe the output from both of these processes to another program:</p><pre><code class="language-julia-repl hljs">julia&gt; run(pipeline(`echo world` &amp; `echo hello`, `sort`));
hello
world</code></pre><p>In terms of UNIX plumbing, what&#39;s happening here is that a single UNIX pipe object is created and written to by both <code>echo</code> processes, and the other end of the pipe is read from by the <code>sort</code> command.</p><p>IO redirection can be accomplished by passing keyword arguments <code>stdin</code>, <code>stdout</code>, and <code>stderr</code> to the <code>pipeline</code> function:</p><pre><code class="language-julia hljs">pipeline(`do_work`, stdout=pipeline(`sort`, &quot;out.txt&quot;), stderr=&quot;errs.txt&quot;)</code></pre><h3 id="Avoiding-Deadlock-in-Pipelines"><a class="docs-heading-anchor" href="#Avoiding-Deadlock-in-Pipelines">Avoiding Deadlock in Pipelines</a><a id="Avoiding-Deadlock-in-Pipelines-1"></a><a class="docs-heading-anchor-permalink" href="#Avoiding-Deadlock-in-Pipelines" title="Permalink"></a></h3><p>When reading and writing to both ends of a pipeline from a single process, it is important to avoid forcing the kernel to buffer all of the data.</p><p>For example, when reading all of the output from a command, call <code>read(out, String)</code>, not <code>wait(process)</code>, since the former will actively consume all of the data written by the process, whereas the latter will attempt to store the data in the kernel&#39;s buffers while waiting for a reader to be connected.</p><p>Another common solution is to separate the reader and writer of the pipeline into separate <a href="../base/parallel.html#Core.Task"><code>Task</code></a>s:</p><pre><code class="language-julia hljs">writer = @async write(process, &quot;data&quot;)
reader = @async do_compute(read(process, String))
wait(writer)
fetch(reader)</code></pre><p>(commonly also, reader is not a separate task, since we immediately <code>fetch</code> it anyways).</p><h3 id="Complex-Example"><a class="docs-heading-anchor" href="#Complex-Example">Complex Example</a><a id="Complex-Example-1"></a><a class="docs-heading-anchor-permalink" href="#Complex-Example" title="Permalink"></a></h3><p>The combination of a high-level programming language, a first-class command abstraction, and automatic setup of pipes between processes is a powerful one. To give some sense of the complex pipelines that can be created easily, here are some more sophisticated examples, with apologies for the excessive use of Perl one-liners:</p><pre><code class="language-julia-repl hljs">julia&gt; prefixer(prefix, sleep) = `perl -nle &#39;$|=1; print &quot;&#39;$prefix&#39; &quot;, $_; sleep &#39;$sleep&#39;;&#39;`;

julia&gt; run(pipeline(`perl -le &#39;$|=1; for(0..5){ print; sleep 1 }&#39;`, prefixer(&quot;A&quot;,2) &amp; prefixer(&quot;B&quot;,2)));
B 0
A 1
B 2
A 3
B 4
A 5</code></pre><p>This is a classic example of a single producer feeding two concurrent consumers: one <code>perl</code> process generates lines with the numbers 0 through 5 on them, while two parallel processes consume that output, one prefixing lines with the letter &quot;A&quot;, the other with the letter &quot;B&quot;. Which consumer gets the first line is non-deterministic, but once that race has been won, the lines are consumed alternately by one process and then the other. (Setting <code>$|=1</code> in Perl causes each print statement to flush the <a href="../base/io-network.html#Base.stdout"><code>stdout</code></a> handle, which is necessary for this example to work. Otherwise all the output is buffered and printed to the pipe at once, to be read by just one consumer process.)</p><p>Here is an even more complex multi-stage producer-consumer example:</p><pre><code class="language-julia-repl hljs">julia&gt; run(pipeline(`perl -le &#39;$|=1; for(0..5){ print; sleep 1 }&#39;`,
           prefixer(&quot;X&quot;,3) &amp; prefixer(&quot;Y&quot;,3) &amp; prefixer(&quot;Z&quot;,3),
           prefixer(&quot;A&quot;,2) &amp; prefixer(&quot;B&quot;,2)));
A X 0
B Y 1
A Z 2
B X 3
A Y 4
B Z 5</code></pre><p>This example is similar to the previous one, except there are two stages of consumers, and the stages have different latency so they use a different number of parallel workers, to maintain saturated throughput.</p><p>We strongly encourage you to try all these examples to see how they work.</p><h2 id="Cmd-Objects"><a class="docs-heading-anchor" href="#Cmd-Objects"><code>Cmd</code> Objects</a><a id="Cmd-Objects-1"></a><a class="docs-heading-anchor-permalink" href="#Cmd-Objects" title="Permalink"></a></h2><p>The backtick syntax create an object of type <a href="../base/base.html#Base.Cmd"><code>Cmd</code></a>. Such object may also be constructed directly from an existing <code>Cmd</code> or list of arguments:</p><pre><code class="language-julia hljs">run(Cmd(`pwd`, dir=&quot;..&quot;))
run(Cmd([&quot;pwd&quot;], detach=true, ignorestatus=true))</code></pre><p>This allows you to specify several aspects of the <code>Cmd</code>&#39;s execution environment via keyword arguments. For example, the <code>dir</code> keyword provides control over the <code>Cmd</code>&#39;s working directory:</p><pre><code class="language-julia-repl hljs">julia&gt; run(Cmd(`pwd`, dir=&quot;/&quot;));
/</code></pre><p>And the <code>env</code> keyword allows you to set execution environment variables:</p><pre><code class="language-julia-repl hljs">julia&gt; run(Cmd(`sh -c &quot;echo foo \$HOWLONG&quot;`, env=(&quot;HOWLONG&quot; =&gt; &quot;ever!&quot;,)));
foo ever!</code></pre><p>See <a href="../base/base.html#Base.Cmd"><code>Cmd</code></a> for additional keyword arguments. The <a href="../base/base.html#Base.setenv"><code>setenv</code></a> and <a href="../base/base.html#Base.addenv"><code>addenv</code></a> commands provide another means for replacing or adding to the <code>Cmd</code> execution environment variables, respectively:</p><pre><code class="language-julia-repl hljs">julia&gt; run(setenv(`sh -c &quot;echo foo \$HOWLONG&quot;`, (&quot;HOWLONG&quot; =&gt; &quot;ever!&quot;,)));
foo ever!

julia&gt; run(addenv(`sh -c &quot;echo foo \$HOWLONG&quot;`, &quot;HOWLONG&quot; =&gt; &quot;ever!&quot;));
foo ever!</code></pre></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="distributed-computing.html">« Multi-processing and Distributed Computing</a><a class="docs-footer-nextpage" href="calling-c-and-fortran-code.html">Calling C and Fortran Code »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
