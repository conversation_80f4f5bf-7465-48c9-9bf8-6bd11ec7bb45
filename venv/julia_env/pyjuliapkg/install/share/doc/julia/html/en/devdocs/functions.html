<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Julia Functions · The Julia Language</title><meta name="title" content="Julia Functions · The Julia Language"/><meta property="og:title" content="Julia Functions · The Julia Language"/><meta property="twitter:title" content="Julia Functions · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox" checked/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="ast.html">Julia ASTs</a></li><li><a class="tocitem" href="types.html">More about types</a></li><li><a class="tocitem" href="object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li class="is-active"><a class="tocitem" href="functions.html">Julia Functions</a><ul class="internal"><li><a class="tocitem" href="#Method-Tables"><span>Method Tables</span></a></li><li><a class="tocitem" href="#Function-calls"><span>Function calls</span></a></li><li><a class="tocitem" href="#Adding-methods"><span>Adding methods</span></a></li><li><a class="tocitem" href="#Creating-generic-functions"><span>Creating generic functions</span></a></li><li><a class="tocitem" href="#Closures"><span>Closures</span></a></li><li><a class="tocitem" href="#Constructors"><span>Constructors</span></a></li><li><a class="tocitem" href="#Builtins"><span>Builtins</span></a></li><li><a class="tocitem" href="#Keyword-arguments"><span>Keyword arguments</span></a></li><li><a class="tocitem" href="#compiler-efficiency-issues"><span>Compiler efficiency issues</span></a></li></ul></li><li><a class="tocitem" href="cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="subarrays.html">SubArrays</a></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Documentation of Julia&#39;s Internals</a></li><li class="is-active"><a href="functions.html">Julia Functions</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="functions.html">Julia Functions</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/functions.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Julia-Functions"><a class="docs-heading-anchor" href="#Julia-Functions">Julia Functions</a><a id="Julia-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Julia-Functions" title="Permalink"></a></h1><p>This document will explain how functions, method definitions, and method tables work.</p><h2 id="Method-Tables"><a class="docs-heading-anchor" href="#Method-Tables">Method Tables</a><a id="Method-Tables-1"></a><a class="docs-heading-anchor-permalink" href="#Method-Tables" title="Permalink"></a></h2><p>Every function in Julia is a generic function. A generic function is conceptually a single function, but consists of many definitions, or methods. The methods of a generic function are stored in a method table. Method tables (type <code>MethodTable</code>) are associated with <code>TypeName</code>s. A <code>TypeName</code> describes a family of parameterized types. For example <code>Complex{Float32}</code> and <code>Complex{Float64}</code> share the same <code>Complex</code> type name object.</p><p>All objects in Julia are potentially callable, because every object has a type, which in turn has a <code>TypeName</code>.</p><h2 id="Function-calls"><a class="docs-heading-anchor" href="#Function-calls">Function calls</a><a id="Function-calls-1"></a><a class="docs-heading-anchor-permalink" href="#Function-calls" title="Permalink"></a></h2><p>Given the call <code>f(x, y)</code>, the following steps are performed: first, the method table to use is accessed as <code>typeof(f).name.mt</code>. Second, an argument tuple type is formed, <code>Tuple{typeof(f), typeof(x), typeof(y)}</code>. Note that the type of the function itself is the first element. This is because the type might have parameters, and so needs to take part in dispatch. This tuple type is looked up in the method table.</p><p>This dispatch process is performed by <code>jl_apply_generic</code>, which takes two arguments: a pointer to an array of the values <code>f</code>, <code>x</code>, and <code>y</code>, and the number of values (in this case 3).</p><p>Throughout the system, there are two kinds of APIs that handle functions and argument lists: those that accept the function and arguments separately, and those that accept a single argument structure. In the first kind of API, the &quot;arguments&quot; part does <em>not</em> contain information about the function, since that is passed separately. In the second kind of API, the function is the first element of the argument structure.</p><p>For example, the following function for performing a call accepts just an <code>args</code> pointer, so the first element of the args array will be the function to call:</p><pre><code class="language-c hljs">jl_value_t *jl_apply(jl_value_t **args, uint32_t nargs)</code></pre><p>This entry point for the same functionality accepts the function separately, so the <code>args</code> array does not contain the function:</p><pre><code class="language-c hljs">jl_value_t *jl_call(jl_function_t *f, jl_value_t **args, int32_t nargs);</code></pre><h2 id="Adding-methods"><a class="docs-heading-anchor" href="#Adding-methods">Adding methods</a><a id="Adding-methods-1"></a><a class="docs-heading-anchor-permalink" href="#Adding-methods" title="Permalink"></a></h2><p>Given the above dispatch process, conceptually all that is needed to add a new method is (1) a tuple type, and (2) code for the body of the method. <code>jl_method_def</code> implements this operation. <code>jl_method_table_for</code> is called to extract the relevant method table from what would be the type of the first argument. This is much more complicated than the corresponding procedure during dispatch, since the argument tuple type might be abstract. For example, we can define:</p><pre><code class="language-julia hljs">(::Union{Foo{Int},Foo{Int8}})(x) = 0</code></pre><p>which works since all possible matching methods would belong to the same method table.</p><h2 id="Creating-generic-functions"><a class="docs-heading-anchor" href="#Creating-generic-functions">Creating generic functions</a><a id="Creating-generic-functions-1"></a><a class="docs-heading-anchor-permalink" href="#Creating-generic-functions" title="Permalink"></a></h2><p>Since every object is callable, nothing special is needed to create a generic function. Therefore <code>jl_new_generic_function</code> simply creates a new singleton (0 size) subtype of <code>Function</code> and returns its instance. A function can have a mnemonic &quot;display name&quot; which is used in debug info and when printing objects. For example the name of <code>Base.sin</code> is <code>sin</code>. By convention, the name of the created <em>type</em> is the same as the function name, with a <code>#</code> prepended. So <code>typeof(sin)</code> is <code>Base.#sin</code>.</p><h2 id="Closures"><a class="docs-heading-anchor" href="#Closures">Closures</a><a id="Closures-1"></a><a class="docs-heading-anchor-permalink" href="#Closures" title="Permalink"></a></h2><p>A closure is simply a callable object with field names corresponding to captured variables. For example, the following code:</p><pre><code class="language-julia hljs">function adder(x)
    return y-&gt;x+y
end</code></pre><p>is lowered to (roughly):</p><pre><code class="language-julia hljs">struct ##1{T}
    x::T
end

(_::##1)(y) = _.x + y

function adder(x)
    return ##1(x)
end</code></pre><h2 id="Constructors"><a class="docs-heading-anchor" href="#Constructors">Constructors</a><a id="Constructors-1"></a><a class="docs-heading-anchor-permalink" href="#Constructors" title="Permalink"></a></h2><p>A constructor call is just a call to a type. The method table for <code>Type</code> contains all constructor definitions. All subtypes of <code>Type</code> (<code>Type</code>, <code>UnionAll</code>, <code>Union</code>, and <code>DataType</code>) currently share a method table via special arrangement.</p><h2 id="Builtins"><a class="docs-heading-anchor" href="#Builtins">Builtins</a><a id="Builtins-1"></a><a class="docs-heading-anchor-permalink" href="#Builtins" title="Permalink"></a></h2><p>The &quot;builtin&quot; functions, defined in the <code>Core</code> module, are:</p><pre><code class="nohighlight hljs">&lt;: === _abstracttype _apply_iterate _apply_pure _call_in_world
_call_in_world_total _call_latest _compute_sparams _equiv_typedef _expr
_primitivetype _setsuper! _structtype _svec_ref _typebody! _typevar applicable
apply_type compilerbarrier current_scope donotdelete fieldtype finalizer
get_binding_type getfield getglobal ifelse invoke isa isdefined
memoryref_isassigned memoryrefget memoryrefmodify! memoryrefnew memoryrefoffset
memoryrefreplace! memoryrefset! memoryrefsetonce! memoryrefswap! modifyfield!
modifyglobal! nfields replacefield! replaceglobal! set_binding_type! setfield!
setfieldonce! setglobal! setglobalonce! sizeof svec swapfield! swapglobal! throw
tuple typeassert typeof</code></pre><p>These are all singleton objects whose types are subtypes of <code>Builtin</code>, which is a subtype of <code>Function</code>. Their purpose is to expose entry points in the run time that use the &quot;jlcall&quot; calling convention:</p><pre><code class="language-c hljs">jl_value_t *(jl_value_t*, jl_value_t**, uint32_t)</code></pre><p>The method tables of builtins are empty. Instead, they have a single catch-all method cache entry (<code>Tuple{Vararg{Any}}</code>) whose jlcall fptr points to the correct function. This is kind of a hack but works reasonably well.</p><h2 id="Keyword-arguments"><a class="docs-heading-anchor" href="#Keyword-arguments">Keyword arguments</a><a id="Keyword-arguments-1"></a><a class="docs-heading-anchor-permalink" href="#Keyword-arguments" title="Permalink"></a></h2><p>Keyword arguments work by adding methods to the kwcall function. This function is usually the &quot;keyword argument sorter&quot; or &quot;keyword sorter&quot;, which then calls the inner body of the function (defined anonymously). Every definition in the kwsorter function has the same arguments as some definition in the normal method table, except with a single <code>NamedTuple</code> argument prepended, which gives the names and values of passed keyword arguments. The kwsorter&#39;s job is to move keyword arguments into their canonical positions based on name, plus evaluate and substitute any needed default value expressions. The result is a normal positional argument list, which is then passed to yet another compiler-generated function.</p><p>The easiest way to understand the process is to look at how a keyword argument method definition is lowered. The code:</p><pre><code class="language-julia hljs">function circle(center, radius; color = black, fill::Bool = true, options...)
    # draw
end</code></pre><p>actually produces <em>three</em> method definitions. The first is a function that accepts all arguments (including keyword arguments) as positional arguments, and includes the code for the method body. It has an auto-generated name:</p><pre><code class="language-julia hljs">function #circle#1(color, fill::Bool, options, circle, center, radius)
    # draw
end</code></pre><p>The second method is an ordinary definition for the original <code>circle</code> function, which handles the case where no keyword arguments are passed:</p><pre><code class="language-julia hljs">function circle(center, radius)
    #circle#1(black, true, pairs(NamedTuple()), circle, center, radius)
end</code></pre><p>This simply dispatches to the first method, passing along default values. <code>pairs</code> is applied to the named tuple of rest arguments to provide key-value pair iteration. Note that if the method doesn&#39;t accept rest keyword arguments then this argument is absent.</p><p>Finally there is the kwsorter definition:</p><pre><code class="nohighlight hljs">function (::Core.kwftype(typeof(circle)))(kws, circle, center, radius)
    if haskey(kws, :color)
        color = kws.color
    else
        color = black
    end
    # etc.

    # put remaining kwargs in `options`
    options = structdiff(kws, NamedTuple{(:color, :fill)})

    # if the method doesn&#39;t accept rest keywords, throw an error
    # unless `options` is empty

    #circle#1(color, fill, pairs(options), circle, center, radius)
end</code></pre><p>The function <code>Core.kwftype(t)</code> creates the field <code>t.name.mt.kwsorter</code> (if it hasn&#39;t been created yet), and returns the type of that function.</p><p>This design has the feature that call sites that don&#39;t use keyword arguments require no special handling; everything works as if they were not part of the language at all. Call sites that do use keyword arguments are dispatched directly to the called function&#39;s kwsorter. For example the call:</p><pre><code class="language-julia hljs">circle((0, 0), 1.0, color = red; other...)</code></pre><p>is lowered to:</p><pre><code class="language-julia hljs">kwcall(merge((color = red,), other), circle, (0, 0), 1.0)</code></pre><p><code>kwcall</code> (also in<code>Core</code>) denotes a kwcall signature and dispatch. The keyword splatting operation (written as <code>other...</code>) calls the named tuple <code>merge</code> function. This function further unpacks each <em>element</em> of <code>other</code>, expecting each one to contain two values (a symbol and a value). Naturally, a more efficient implementation is available if all splatted arguments are named tuples. Notice that the original <code>circle</code> function is passed through, to handle closures.</p><h2 id="compiler-efficiency-issues"><a class="docs-heading-anchor" href="#compiler-efficiency-issues">Compiler efficiency issues</a><a id="compiler-efficiency-issues-1"></a><a class="docs-heading-anchor-permalink" href="#compiler-efficiency-issues" title="Permalink"></a></h2><p>Generating a new type for every function has potentially serious consequences for compiler resource use when combined with Julia&#39;s &quot;specialize on all arguments by default&quot; design. Indeed, the initial implementation of this design suffered from much longer build and test times, higher memory use, and a system image nearly 2x larger than the baseline. In a naive implementation, the problem is bad enough to make the system nearly unusable. Several significant optimizations were needed to make the design practical.</p><p>The first issue is excessive specialization of functions for different values of function-valued arguments. Many functions simply &quot;pass through&quot; an argument to somewhere else, e.g. to another function or to a storage location. Such functions do not need to be specialized for every closure that might be passed in. Fortunately this case is easy to distinguish by simply considering whether a function <em>calls</em> one of its arguments (i.e. the argument appears in &quot;head position&quot; somewhere). Performance-critical higher-order functions like <code>map</code> certainly call their argument function and so will still be specialized as expected. This optimization is implemented by recording which arguments are called during the <code>analyze-variables</code> pass in the front end. When <code>cache_method</code> sees an argument in the <code>Function</code> type hierarchy passed to a slot declared as <code>Any</code> or <code>Function</code>, it behaves as if the <code>@nospecialize</code> annotation were applied. This heuristic seems to be extremely effective in practice.</p><p>The next issue concerns the structure of method cache hash tables. Empirical studies show that the vast majority of dynamically-dispatched calls involve one or two arguments. In turn, many of these cases can be resolved by considering only the first argument. (Aside: proponents of single dispatch would not be surprised by this at all. However, this argument means &quot;multiple dispatch is easy to optimize in practice&quot;, and that we should therefore use it, <em>not</em> &quot;we should use single dispatch&quot;!) So the method cache uses the type of the first argument as its primary key. Note, however, that this corresponds to the <em>second</em> element of the tuple type for a function call (the first element being the type of the function itself). Typically, type variation in head position is extremely low – indeed, the majority of functions belong to singleton types with no parameters. However, this is not the case for constructors, where a single method table holds constructors for every type. Therefore the <code>Type</code> method table is special-cased to use the <em>first</em> tuple type element instead of the second.</p><p>The front end generates type declarations for all closures. Initially, this was implemented by generating normal type declarations. However, this produced an extremely large number of constructors, all of which were trivial (simply passing all arguments through to <a href="../base/base.html#new"><code>new</code></a>). Since methods are partially ordered, inserting all of these methods is O(n²), plus there are just too many of them to keep around. This was optimized by generating <code>struct_type</code> expressions directly (bypassing default constructor generation), and using <code>new</code> directly to create closure instances. Not the prettiest thing ever, but you do what you gotta do.</p><p>The next problem was the <code>@test</code> macro, which generated a 0-argument closure for each test case. This is not really necessary, since each test case is simply run once in place. Therefore, <code>@test</code> was modified to expand to a try-catch block that records the test result (true, false, or exception raised) and calls the test suite handler on it.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="compiler.html">« High-level Overview of the Native-Code Generation Process</a><a class="docs-footer-nextpage" href="cartesian.html">Base.Cartesian »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
