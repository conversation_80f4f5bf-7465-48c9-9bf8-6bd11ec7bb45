<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Interfaces · The Julia Language</title><meta name="title" content="Interfaces · The Julia Language"/><meta property="og:title" content="Interfaces · The Julia Language"/><meta property="twitter:title" content="Interfaces · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li class="is-active"><a class="tocitem" href="interfaces.html">Interfaces</a><ul class="internal"><li><a class="tocitem" href="#man-interface-iteration"><span>Iteration</span></a></li><li><a class="tocitem" href="#Indexing"><span>Indexing</span></a></li><li><a class="tocitem" href="#man-interface-array"><span>Abstract Arrays</span></a></li><li><a class="tocitem" href="#man-interface-strided-arrays"><span>Strided Arrays</span></a></li><li><a class="tocitem" href="#man-interfaces-broadcasting"><span>Customizing broadcasting</span></a></li><li><a class="tocitem" href="#man-instance-properties"><span>Instance Properties</span></a></li><li><a class="tocitem" href="#man-rounding-interface"><span>Rounding</span></a></li></ul></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="interfaces.html">Interfaces</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="interfaces.html">Interfaces</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/interfaces.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Interfaces"><a class="docs-heading-anchor" href="#Interfaces">Interfaces</a><a id="Interfaces-1"></a><a class="docs-heading-anchor-permalink" href="#Interfaces" title="Permalink"></a></h1><p>A lot of the power and extensibility in Julia comes from a collection of informal interfaces.  By extending a few specific methods to work for a custom type, objects of that type not only receive those functionalities, but they are also able to be used in other methods that are written to generically build upon those behaviors.</p><h2 id="man-interface-iteration"><a class="docs-heading-anchor" href="#man-interface-iteration">Iteration</a><a id="man-interface-iteration-1"></a><a class="docs-heading-anchor-permalink" href="#man-interface-iteration" title="Permalink"></a></h2><p>There are two methods that are always required:</p><table><tr><th style="text-align: left">Required method</th><th style="text-align: left">Brief description</th></tr><tr><td style="text-align: left"><a href="../base/collections.html#Base.iterate"><code>iterate(iter)</code></a></td><td style="text-align: left">Returns either a tuple of the first item and initial state or <a href="../base/constants.html#Core.nothing"><code>nothing</code></a> if empty</td></tr><tr><td style="text-align: left"><code>iterate(iter, state)</code></td><td style="text-align: left">Returns either a tuple of the next item and next state or <code>nothing</code> if no items remain</td></tr></table><p>There are several more methods that should be defined in some circumstances. Please note that you should always define at least one of <code>Base.IteratorSize(IterType)</code> and <code>length(iter)</code> because the default definition of <code>Base.IteratorSize(IterType)</code> is <code>Base.HasLength()</code>.</p><table><tr><th style="text-align: left">Method</th><th style="text-align: left">When should this method be defined?</th><th style="text-align: left">Default definition</th><th style="text-align: left">Brief description</th></tr><tr><td style="text-align: left"><a href="../base/collections.html#Base.IteratorSize"><code>Base.IteratorSize(IterType)</code></a></td><td style="text-align: left">If default is not appropriate</td><td style="text-align: left"><code>Base.HasLength()</code></td><td style="text-align: left">One of <code>Base.HasLength()</code>, <code>Base.HasShape{N}()</code>, <code>Base.IsInfinite()</code>, or <code>Base.SizeUnknown()</code> as appropriate</td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.length-Tuple{AbstractArray}"><code>length(iter)</code></a></td><td style="text-align: left">If <code>Base.IteratorSize()</code> returns <code>Base.HasLength()</code> or <code>Base.HasShape{N}()</code></td><td style="text-align: left">(<em>undefined</em>)</td><td style="text-align: left">The number of items, if known</td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.size"><code>size(iter, [dim])</code></a></td><td style="text-align: left">If <code>Base.IteratorSize()</code> returns <code>Base.HasShape{N}()</code></td><td style="text-align: left">(<em>undefined</em>)</td><td style="text-align: left">The number of items in each dimension, if known</td></tr><tr><td style="text-align: left"><a href="../base/collections.html#Base.IteratorEltype"><code>Base.IteratorEltype(IterType)</code></a></td><td style="text-align: left">If default is not appropriate</td><td style="text-align: left"><code>Base.HasEltype()</code></td><td style="text-align: left">Either <code>Base.EltypeUnknown()</code> or <code>Base.HasEltype()</code> as appropriate</td></tr><tr><td style="text-align: left"><a href="../base/collections.html#Base.eltype"><code>eltype(IterType)</code></a></td><td style="text-align: left">If default is not appropriate</td><td style="text-align: left"><code>Any</code></td><td style="text-align: left">The type of the first entry of the tuple returned by <code>iterate()</code></td></tr><tr><td style="text-align: left"><a href="../base/collections.html#Base.isdone"><code>Base.isdone(iter, [state])</code></a></td><td style="text-align: left"><strong>Must</strong> be defined if iterator is stateful</td><td style="text-align: left"><code>missing</code></td><td style="text-align: left">Fast-path hint for iterator completion. If not defined for a stateful iterator then functions that check for done-ness, like <code>isempty()</code> and <code>zip()</code>, may mutate the iterator and cause buggy behaviour!</td></tr></table><p>Sequential iteration is implemented by the <a href="../base/collections.html#Base.iterate"><code>iterate</code></a> function. Instead of mutating objects as they are iterated over, Julia iterators may keep track of the iteration state externally from the object. The return value from iterate is always either a tuple of a value and a state, or <code>nothing</code> if no elements remain. The state object will be passed back to the iterate function on the next iteration and is generally considered an implementation detail private to the iterable object.</p><p>Any object that defines this function is iterable and can be used in the <a href="../base/collections.html#lib-collections-iteration">many functions that rely upon iteration</a>. It can also be used directly in a <a href="../base/base.html#for"><code>for</code></a> loop since the syntax:</p><pre><code class="language-julia hljs">for item in iter   # or  &quot;for item = iter&quot;
    # body
end</code></pre><p>is translated into:</p><pre><code class="language-julia hljs">next = iterate(iter)
while next !== nothing
    (item, state) = next
    # body
    next = iterate(iter, state)
end</code></pre><p>A simple example is an iterable sequence of square numbers with a defined length:</p><pre><code class="language-julia-repl hljs">julia&gt; struct Squares
           count::Int
       end

julia&gt; Base.iterate(S::Squares, state=1) = state &gt; S.count ? nothing : (state*state, state+1)</code></pre><p>With only <a href="../base/collections.html#Base.iterate"><code>iterate</code></a> definition, the <code>Squares</code> type is already pretty powerful. We can iterate over all the elements:</p><pre><code class="language-julia-repl hljs">julia&gt; for item in Squares(7)
           println(item)
       end
1
4
9
16
25
36
49</code></pre><p>We can use many of the builtin methods that work with iterables, like <a href="../base/collections.html#Base.in"><code>in</code></a> or <a href="../base/collections.html#Base.sum"><code>sum</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; 25 in Squares(10)
true

julia&gt; sum(Squares(100))
338350</code></pre><p>There are a few more methods we can extend to give Julia more information about this iterable collection.  We know that the elements in a <code>Squares</code> sequence will always be <code>Int</code>. By extending the <a href="../base/collections.html#Base.eltype"><code>eltype</code></a> method, we can give that information to Julia and help it make more specialized code in the more complicated methods. We also know the number of elements in our sequence, so we can extend <a href="../base/collections.html#Base.length"><code>length</code></a>, too:</p><pre><code class="language-julia-repl hljs">julia&gt; Base.eltype(::Type{Squares}) = Int # Note that this is defined for the type

julia&gt; Base.length(S::Squares) = S.count</code></pre><p>Now, when we ask Julia to <a href="../base/collections.html#Base.collect-Tuple{Any}"><code>collect</code></a> all the elements into an array it can preallocate a <code>Vector{Int}</code> of the right size instead of naively <a href="../base/collections.html#Base.push!"><code>push!</code></a>ing each element into a <code>Vector{Any}</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; collect(Squares(4))
4-element Vector{Int64}:
  1
  4
  9
 16</code></pre><p>While we can rely upon generic implementations, we can also extend specific methods where we know there is a simpler algorithm. For example, there&#39;s a formula to compute the sum of squares, so we can override the generic iterative version with a more performant solution:</p><pre><code class="language-julia-repl hljs">julia&gt; Base.sum(S::Squares) = (n = S.count; return n*(n+1)*(2n+1)÷6)

julia&gt; sum(Squares(1803))
1955361914</code></pre><p>This is a very common pattern throughout Julia Base: a small set of required methods define an informal interface that enable many fancier behaviors. In some cases, types will want to additionally specialize those extra behaviors when they know a more efficient algorithm can be used in their specific case.</p><p>It is also often useful to allow iteration over a collection in <em>reverse order</em> by iterating over <a href="../base/iterators.html#Base.Iterators.reverse"><code>Iterators.reverse(iterator)</code></a>.  To actually support reverse-order iteration, however, an iterator type <code>T</code> needs to implement <code>iterate</code> for <code>Iterators.Reverse{T}</code>. (Given <code>r::Iterators.Reverse{T}</code>, the underling iterator of type <code>T</code> is <code>r.itr</code>.) In our <code>Squares</code> example, we would implement <code>Iterators.Reverse{Squares}</code> methods:</p><pre><code class="language-julia-repl hljs">julia&gt; Base.iterate(rS::Iterators.Reverse{Squares}, state=rS.itr.count) = state &lt; 1 ? nothing : (state*state, state-1)

julia&gt; collect(Iterators.reverse(Squares(4)))
4-element Vector{Int64}:
 16
  9
  4
  1</code></pre><h2 id="Indexing"><a class="docs-heading-anchor" href="#Indexing">Indexing</a><a id="Indexing-1"></a><a class="docs-heading-anchor-permalink" href="#Indexing" title="Permalink"></a></h2><table><tr><th style="text-align: left">Methods to implement</th><th style="text-align: left">Brief description</th></tr><tr><td style="text-align: left"><code>getindex(X, i)</code></td><td style="text-align: left"><code>X[i]</code>, indexed access, non-scalar <code>i</code> should allocate a copy</td></tr><tr><td style="text-align: left"><code>setindex!(X, v, i)</code></td><td style="text-align: left"><code>X[i] = v</code>, indexed assignment</td></tr><tr><td style="text-align: left"><code>firstindex(X)</code></td><td style="text-align: left">The first index, used in <code>X[begin]</code></td></tr><tr><td style="text-align: left"><code>lastindex(X)</code></td><td style="text-align: left">The last index, used in <code>X[end]</code></td></tr></table><p>For the <code>Squares</code> iterable above, we can easily compute the <code>i</code>th element of the sequence by squaring it.  We can expose this as an indexing expression <code>S[i]</code>. To opt into this behavior, <code>Squares</code> simply needs to define <a href="../base/collections.html#Base.getindex"><code>getindex</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; function Base.getindex(S::Squares, i::Int)
           1 &lt;= i &lt;= S.count || throw(BoundsError(S, i))
           return i*i
       end

julia&gt; Squares(100)[23]
529</code></pre><p>Additionally, to support the syntax <code>S[begin]</code> and <code>S[end]</code>, we must define <a href="../base/collections.html#Base.firstindex"><code>firstindex</code></a> and <a href="../base/collections.html#Base.lastindex"><code>lastindex</code></a> to specify the first and last valid indices, respectively:</p><pre><code class="language-julia-repl hljs">julia&gt; Base.firstindex(S::Squares) = 1

julia&gt; Base.lastindex(S::Squares) = length(S)

julia&gt; Squares(23)[end]
529</code></pre><p>For multi-dimensional <code>begin</code>/<code>end</code> indexing as in <code>a[3, begin, 7]</code>, for example, you should define <code>firstindex(a, dim)</code> and <code>lastindex(a, dim)</code> (which default to calling <code>first</code> and <code>last</code> on <code>axes(a, dim)</code>, respectively).</p><p>Note, though, that the above <em>only</em> defines <a href="../base/collections.html#Base.getindex"><code>getindex</code></a> with one integer index. Indexing with anything other than an <code>Int</code> will throw a <a href="../base/base.html#Core.MethodError"><code>MethodError</code></a> saying that there was no matching method. In order to support indexing with ranges or vectors of <code>Int</code>s, separate methods must be written:</p><pre><code class="language-julia-repl hljs">julia&gt; Base.getindex(S::Squares, i::Number) = S[convert(Int, i)]

julia&gt; Base.getindex(S::Squares, I) = [S[i] for i in I]

julia&gt; Squares(10)[[3,4.,5]]
3-element Vector{Int64}:
  9
 16
 25</code></pre><p>While this is starting to support more of the <a href="arrays.html#man-array-indexing">indexing operations supported by some of the builtin types</a>, there&#39;s still quite a number of behaviors missing. This <code>Squares</code> sequence is starting to look more and more like a vector as we&#39;ve added behaviors to it. Instead of defining all these behaviors ourselves, we can officially define it as a subtype of an <a href="../base/arrays.html#Core.AbstractArray"><code>AbstractArray</code></a>.</p><h2 id="man-interface-array"><a class="docs-heading-anchor" href="#man-interface-array">Abstract Arrays</a><a id="man-interface-array-1"></a><a class="docs-heading-anchor-permalink" href="#man-interface-array" title="Permalink"></a></h2><table><tr><th style="text-align: left">Methods to implement</th><th style="text-align: left"></th><th style="text-align: left">Brief description</th></tr><tr><td style="text-align: left"><code>size(A)</code></td><td style="text-align: left"></td><td style="text-align: left">Returns a tuple containing the dimensions of <code>A</code></td></tr><tr><td style="text-align: left"><code>getindex(A, i::Int)</code></td><td style="text-align: left"></td><td style="text-align: left">(if <code>IndexLinear</code>) Linear scalar indexing</td></tr><tr><td style="text-align: left"><code>getindex(A, I::Vararg{Int, N})</code></td><td style="text-align: left"></td><td style="text-align: left">(if <code>IndexCartesian</code>, where <code>N = ndims(A)</code>) N-dimensional scalar indexing</td></tr><tr><td style="text-align: left"><strong>Optional methods</strong></td><td style="text-align: left"><strong>Default definition</strong></td><td style="text-align: left"><strong>Brief description</strong></td></tr><tr><td style="text-align: left"><code>IndexStyle(::Type)</code></td><td style="text-align: left"><code>IndexCartesian()</code></td><td style="text-align: left">Returns either <code>IndexLinear()</code> or <code>IndexCartesian()</code>. See the description below.</td></tr><tr><td style="text-align: left"><code>setindex!(A, v, i::Int)</code></td><td style="text-align: left"></td><td style="text-align: left">(if <code>IndexLinear</code>) Scalar indexed assignment</td></tr><tr><td style="text-align: left"><code>setindex!(A, v, I::Vararg{Int, N})</code></td><td style="text-align: left"></td><td style="text-align: left">(if <code>IndexCartesian</code>, where <code>N = ndims(A)</code>) N-dimensional scalar indexed assignment</td></tr><tr><td style="text-align: left"><code>getindex(A, I...)</code></td><td style="text-align: left">defined in terms of scalar <code>getindex</code></td><td style="text-align: left"><a href="arrays.html#man-array-indexing">Multidimensional and nonscalar indexing</a></td></tr><tr><td style="text-align: left"><code>setindex!(A, X, I...)</code></td><td style="text-align: left">defined in terms of scalar <code>setindex!</code></td><td style="text-align: left"><a href="arrays.html#man-array-indexing">Multidimensional and nonscalar indexed assignment</a></td></tr><tr><td style="text-align: left"><code>iterate</code></td><td style="text-align: left">defined in terms of scalar <code>getindex</code></td><td style="text-align: left">Iteration</td></tr><tr><td style="text-align: left"><code>length(A)</code></td><td style="text-align: left"><code>prod(size(A))</code></td><td style="text-align: left">Number of elements</td></tr><tr><td style="text-align: left"><code>similar(A)</code></td><td style="text-align: left"><code>similar(A, eltype(A), size(A))</code></td><td style="text-align: left">Return a mutable array with the same shape and element type</td></tr><tr><td style="text-align: left"><code>similar(A, ::Type{S})</code></td><td style="text-align: left"><code>similar(A, S, size(A))</code></td><td style="text-align: left">Return a mutable array with the same shape and the specified element type</td></tr><tr><td style="text-align: left"><code>similar(A, dims::Dims)</code></td><td style="text-align: left"><code>similar(A, eltype(A), dims)</code></td><td style="text-align: left">Return a mutable array with the same element type and size <em>dims</em></td></tr><tr><td style="text-align: left"><code>similar(A, ::Type{S}, dims::Dims)</code></td><td style="text-align: left"><code>Array{S}(undef, dims)</code></td><td style="text-align: left">Return a mutable array with the specified element type and size</td></tr><tr><td style="text-align: left"><strong>Non-traditional indices</strong></td><td style="text-align: left"><strong>Default definition</strong></td><td style="text-align: left"><strong>Brief description</strong></td></tr><tr><td style="text-align: left"><code>axes(A)</code></td><td style="text-align: left"><code>map(OneTo, size(A))</code></td><td style="text-align: left">Return a tuple of <code>AbstractUnitRange{&lt;:Integer}</code> of valid indices. The axes should be their own axes, that is <code>axes.(axes(A),1) == axes(A)</code> should be satisfied.</td></tr><tr><td style="text-align: left"><code>similar(A, ::Type{S}, inds)</code></td><td style="text-align: left"><code>similar(A, S, Base.to_shape(inds))</code></td><td style="text-align: left">Return a mutable array with the specified indices <code>inds</code> (see below)</td></tr><tr><td style="text-align: left"><code>similar(T::Union{Type,Function}, inds)</code></td><td style="text-align: left"><code>T(Base.to_shape(inds))</code></td><td style="text-align: left">Return an array similar to <code>T</code> with the specified indices <code>inds</code> (see below)</td></tr></table><p>If a type is defined as a subtype of <code>AbstractArray</code>, it inherits a very large set of rich behaviors including iteration and multidimensional indexing built on top of single-element access.  See the <a href="arrays.html#man-multi-dim-arrays">arrays manual page</a> and the <a href="../base/arrays.html#lib-arrays">Julia Base section</a> for more supported methods.</p><p>A key part in defining an <code>AbstractArray</code> subtype is <a href="../base/arrays.html#Base.IndexStyle"><code>IndexStyle</code></a>. Since indexing is such an important part of an array and often occurs in hot loops, it&#39;s important to make both indexing and indexed assignment as efficient as possible.  Array data structures are typically defined in one of two ways: either it most efficiently accesses its elements using just one index (linear indexing) or it intrinsically accesses the elements with indices specified for every dimension.  These two modalities are identified by Julia as <code>IndexLinear()</code> and <code>IndexCartesian()</code>.  Converting a linear index to multiple indexing subscripts is typically very expensive, so this provides a traits-based mechanism to enable efficient generic code for all array types.</p><p>This distinction determines which scalar indexing methods the type must define. <code>IndexLinear()</code> arrays are simple: just define <code>getindex(A::ArrayType, i::Int)</code>.  When the array is subsequently indexed with a multidimensional set of indices, the fallback <code>getindex(A::AbstractArray, I...)</code> efficiently converts the indices into one linear index and then calls the above method. <code>IndexCartesian()</code> arrays, on the other hand, require methods to be defined for each supported dimensionality with <code>ndims(A)</code> <code>Int</code> indices. For example, <a href="../stdlib/SparseArrays.html#SparseArrays.SparseMatrixCSC"><code>SparseMatrixCSC</code></a> from the <code>SparseArrays</code> standard library module, only supports two dimensions, so it just defines <code>getindex(A::SparseMatrixCSC, i::Int, j::Int)</code>. The same holds for <a href="../base/collections.html#Base.setindex!"><code>setindex!</code></a>.</p><p>Returning to the sequence of squares from above, we could instead define it as a subtype of an <code>AbstractArray{Int, 1}</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; struct SquaresVector &lt;: AbstractArray{Int, 1}
           count::Int
       end

julia&gt; Base.size(S::SquaresVector) = (S.count,)

julia&gt; Base.IndexStyle(::Type{&lt;:SquaresVector}) = IndexLinear()

julia&gt; Base.getindex(S::SquaresVector, i::Int) = i*i</code></pre><p>Note that it&#39;s very important to specify the two parameters of the <code>AbstractArray</code>; the first defines the <a href="../base/collections.html#Base.eltype"><code>eltype</code></a>, and the second defines the <a href="../base/arrays.html#Base.ndims"><code>ndims</code></a>. That supertype and those three methods are all it takes for <code>SquaresVector</code> to be an iterable, indexable, and completely functional array:</p><pre><code class="language-julia-repl hljs">julia&gt; s = SquaresVector(4)
4-element SquaresVector:
  1
  4
  9
 16

julia&gt; s[s .&gt; 8]
2-element Vector{Int64}:
  9
 16

julia&gt; s + s
4-element Vector{Int64}:
  2
  8
 18
 32

julia&gt; sin.(s)
4-element Vector{Float64}:
  0.8414709848078965
 -0.7568024953079282
  0.4121184852417566
 -0.2879033166650653</code></pre><p>As a more complicated example, let&#39;s define our own toy N-dimensional sparse-like array type built on top of <a href="../base/collections.html#Base.Dict"><code>Dict</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; struct SparseArray{T,N} &lt;: AbstractArray{T,N}
           data::Dict{NTuple{N,Int}, T}
           dims::NTuple{N,Int}
       end

julia&gt; SparseArray(::Type{T}, dims::Int...) where {T} = SparseArray(T, dims);

julia&gt; SparseArray(::Type{T}, dims::NTuple{N,Int}) where {T,N} = SparseArray{T,N}(Dict{NTuple{N,Int}, T}(), dims);

julia&gt; Base.size(A::SparseArray) = A.dims

julia&gt; Base.similar(A::SparseArray, ::Type{T}, dims::Dims) where {T} = SparseArray(T, dims)

julia&gt; Base.getindex(A::SparseArray{T,N}, I::Vararg{Int,N}) where {T,N} = get(A.data, I, zero(T))

julia&gt; Base.setindex!(A::SparseArray{T,N}, v, I::Vararg{Int,N}) where {T,N} = (A.data[I] = v)</code></pre><p>Notice that this is an <code>IndexCartesian</code> array, so we must manually define <a href="../base/collections.html#Base.getindex"><code>getindex</code></a> and <a href="../base/collections.html#Base.setindex!"><code>setindex!</code></a> at the dimensionality of the array. Unlike the <code>SquaresVector</code>, we are able to define <a href="../base/collections.html#Base.setindex!"><code>setindex!</code></a>, and so we can mutate the array:</p><pre><code class="language-julia-repl hljs">julia&gt; A = SparseArray(Float64, 3, 3)
3×3 SparseArray{Float64, 2}:
 0.0  0.0  0.0
 0.0  0.0  0.0
 0.0  0.0  0.0

julia&gt; fill!(A, 2)
3×3 SparseArray{Float64, 2}:
 2.0  2.0  2.0
 2.0  2.0  2.0
 2.0  2.0  2.0

julia&gt; A[:] = 1:length(A); A
3×3 SparseArray{Float64, 2}:
 1.0  4.0  7.0
 2.0  5.0  8.0
 3.0  6.0  9.0</code></pre><p>The result of indexing an <code>AbstractArray</code> can itself be an array (for instance when indexing by an <code>AbstractRange</code>). The <code>AbstractArray</code> fallback methods use <a href="../base/arrays.html#Base.similar"><code>similar</code></a> to allocate an <code>Array</code> of the appropriate size and element type, which is filled in using the basic indexing method described above. However, when implementing an array wrapper you often want the result to be wrapped as well:</p><pre><code class="language-julia-repl hljs">julia&gt; A[1:2,:]
2×3 SparseArray{Float64, 2}:
 1.0  4.0  7.0
 2.0  5.0  8.0</code></pre><p>In this example it is accomplished by defining <code>Base.similar(A::SparseArray, ::Type{T}, dims::Dims) where T</code> to create the appropriate wrapped array. (Note that while <code>similar</code> supports 1- and 2-argument forms, in most case you only need to specialize the 3-argument form.) For this to work it&#39;s important that <code>SparseArray</code> is mutable (supports <code>setindex!</code>). Defining <code>similar</code>, <code>getindex</code> and <code>setindex!</code> for <code>SparseArray</code> also makes it possible to <a href="../base/base.html#Base.copy"><code>copy</code></a> the array:</p><pre><code class="language-julia-repl hljs">julia&gt; copy(A)
3×3 SparseArray{Float64, 2}:
 1.0  4.0  7.0
 2.0  5.0  8.0
 3.0  6.0  9.0</code></pre><p>In addition to all the iterable and indexable methods from above, these types can also interact with each other and use most of the methods defined in Julia Base for <code>AbstractArrays</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; A[SquaresVector(3)]
3-element SparseArray{Float64, 1}:
 1.0
 4.0
 9.0

julia&gt; sum(A)
45.0</code></pre><p>If you are defining an array type that allows non-traditional indexing (indices that start at something other than 1), you should specialize <a href="../base/arrays.html#Base.axes-Tuple{Any}"><code>axes</code></a>. You should also specialize <a href="../base/arrays.html#Base.similar"><code>similar</code></a> so that the <code>dims</code> argument (ordinarily a <code>Dims</code> size-tuple) can accept <code>AbstractUnitRange</code> objects, perhaps range-types <code>Ind</code> of your own design. For more information, see <a href="../devdocs/offset-arrays.html#man-custom-indices">Arrays with custom indices</a>.</p><h2 id="man-interface-strided-arrays"><a class="docs-heading-anchor" href="#man-interface-strided-arrays">Strided Arrays</a><a id="man-interface-strided-arrays-1"></a><a class="docs-heading-anchor-permalink" href="#man-interface-strided-arrays" title="Permalink"></a></h2><table><tr><th style="text-align: left">Methods to implement</th><th style="text-align: left"></th><th style="text-align: left">Brief description</th></tr><tr><td style="text-align: left"><code>strides(A)</code></td><td style="text-align: left"></td><td style="text-align: left">Return the distance in memory (in number of elements) between adjacent elements in each dimension as a tuple. If <code>A</code> is an <code>AbstractArray{T,0}</code>, this should return an empty tuple.</td></tr><tr><td style="text-align: left"><code>Base.unsafe_convert(::Type{Ptr{T}}, A)</code></td><td style="text-align: left"></td><td style="text-align: left">Return the native address of an array.</td></tr><tr><td style="text-align: left"><code>Base.elsize(::Type{&lt;:A})</code></td><td style="text-align: left"></td><td style="text-align: left">Return the stride between consecutive elements in the array.</td></tr><tr><td style="text-align: left"><strong>Optional methods</strong></td><td style="text-align: left"><strong>Default definition</strong></td><td style="text-align: left"><strong>Brief description</strong></td></tr><tr><td style="text-align: left"><code>stride(A, i::Int)</code></td><td style="text-align: left"><code>strides(A)[i]</code></td><td style="text-align: left">Return the distance in memory (in number of elements) between adjacent elements in dimension k.</td></tr></table><p>A strided array is a subtype of <code>AbstractArray</code> whose entries are stored in memory with fixed strides. Provided the element type of the array is compatible with BLAS, a strided array can utilize BLAS and LAPACK routines for more efficient linear algebra routines.  A typical example of a user-defined strided array is one that wraps a standard <code>Array</code> with additional structure.</p><p>Warning: do not implement these methods if the underlying storage is not actually strided, as it may lead to incorrect results or segmentation faults.</p><p>Here are some examples to demonstrate which type of arrays are strided and which are not:</p><pre><code class="language-julia hljs">1:5   # not strided (there is no storage associated with this array.)
Vector(1:5)  # is strided with strides (1,)
A = [1 5; 2 6; 3 7; 4 8]  # is strided with strides (1,4)
V = view(A, 1:2, :)   # is strided with strides (1,4)
V = view(A, 1:2:3, 1:2)   # is strided with strides (2,4)
V = view(A, [1,2,4], :)   # is not strided, as the spacing between rows is not fixed.</code></pre><h2 id="man-interfaces-broadcasting"><a class="docs-heading-anchor" href="#man-interfaces-broadcasting">Customizing broadcasting</a><a id="man-interfaces-broadcasting-1"></a><a class="docs-heading-anchor-permalink" href="#man-interfaces-broadcasting" title="Permalink"></a></h2><table><tr><th style="text-align: left">Methods to implement</th><th style="text-align: left">Brief description</th></tr><tr><td style="text-align: left"><code>Base.BroadcastStyle(::Type{SrcType}) = SrcStyle()</code></td><td style="text-align: left">Broadcasting behavior of <code>SrcType</code></td></tr><tr><td style="text-align: left"><code>Base.similar(bc::Broadcasted{DestStyle}, ::Type{ElType})</code></td><td style="text-align: left">Allocation of output container</td></tr><tr><td style="text-align: left"><strong>Optional methods</strong></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>Base.BroadcastStyle(::Style1, ::Style2) = Style12()</code></td><td style="text-align: left">Precedence rules for mixing styles</td></tr><tr><td style="text-align: left"><code>Base.axes(x)</code></td><td style="text-align: left">Declaration of the indices of <code>x</code>, as per <a href="../base/arrays.html#Base.axes-Tuple{Any}"><code>axes(x)</code></a>.</td></tr><tr><td style="text-align: left"><code>Base.broadcastable(x)</code></td><td style="text-align: left">Convert <code>x</code> to an object that has <code>axes</code> and supports indexing</td></tr><tr><td style="text-align: left"><strong>Bypassing default machinery</strong></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>Base.copy(bc::Broadcasted{DestStyle})</code></td><td style="text-align: left">Custom implementation of <code>broadcast</code></td></tr><tr><td style="text-align: left"><code>Base.copyto!(dest, bc::Broadcasted{DestStyle})</code></td><td style="text-align: left">Custom implementation of <code>broadcast!</code>, specializing on <code>DestStyle</code></td></tr><tr><td style="text-align: left"><code>Base.copyto!(dest::DestType, bc::Broadcasted{Nothing})</code></td><td style="text-align: left">Custom implementation of <code>broadcast!</code>, specializing on <code>DestType</code></td></tr><tr><td style="text-align: left"><code>Base.Broadcast.broadcasted(f, args...)</code></td><td style="text-align: left">Override the default lazy behavior within a fused expression</td></tr><tr><td style="text-align: left"><code>Base.Broadcast.instantiate(bc::Broadcasted{DestStyle})</code></td><td style="text-align: left">Override the computation of the lazy broadcast&#39;s axes</td></tr></table><p><a href="arrays.html#Broadcasting">Broadcasting</a> is triggered by an explicit call to <code>broadcast</code> or <code>broadcast!</code>, or implicitly by &quot;dot&quot; operations like <code>A .+ b</code> or <code>f.(x, y)</code>. Any object that has <a href="../base/arrays.html#Base.axes-Tuple{Any}"><code>axes</code></a> and supports indexing can participate as an argument in broadcasting, and by default the result is stored in an <code>Array</code>. This basic framework is extensible in three major ways:</p><ul><li>Ensuring that all arguments support broadcast</li><li>Selecting an appropriate output array for the given set of arguments</li><li>Selecting an efficient implementation for the given set of arguments</li></ul><p>Not all types support <code>axes</code> and indexing, but many are convenient to allow in broadcast. The <a href="../base/arrays.html#Base.Broadcast.broadcastable"><code>Base.broadcastable</code></a> function is called on each argument to broadcast, allowing it to return something different that supports <code>axes</code> and indexing. By default, this is the identity function for all <code>AbstractArray</code>s and <code>Number</code>s — they already support <code>axes</code> and indexing.</p><p>If a type is intended to act like a &quot;0-dimensional scalar&quot; (a single object) rather than as a container for broadcasting, then the following method should be defined:</p><pre><code class="language-julia hljs">Base.broadcastable(o::MyType) = Ref(o)</code></pre><p>that returns the argument wrapped in a 0-dimensional <a href="../base/c.html#Core.Ref"><code>Ref</code></a> container.   For example, such a wrapper method is defined for types themselves, functions, special singletons like <a href="missing.html#missing"><code>missing</code></a> and <a href="../base/constants.html#Core.nothing"><code>nothing</code></a>, and dates.</p><p>Custom array-like types can specialize <code>Base.broadcastable</code> to define their shape, but they should follow the convention that <code>collect(Base.broadcastable(x)) == collect(x)</code>. A notable exception is <code>AbstractString</code>; strings are special-cased to behave as scalars for the purposes of broadcast even though they are iterable collections of their characters (see <a href="../devdocs/ast.html#Strings">Strings</a> for more).</p><p>The next two steps (selecting the output array and implementation) are dependent upon determining a single answer for a given set of arguments. Broadcast must take all the varied types of its arguments and collapse them down to just one output array and one implementation. Broadcast calls this single answer a &quot;style&quot;. Every broadcastable object each has its own preferred style, and a promotion-like system is used to combine these styles into a single answer — the &quot;destination style&quot;.</p><h3 id="Broadcast-Styles"><a class="docs-heading-anchor" href="#Broadcast-Styles">Broadcast Styles</a><a id="Broadcast-Styles-1"></a><a class="docs-heading-anchor-permalink" href="#Broadcast-Styles" title="Permalink"></a></h3><p><code>Base.BroadcastStyle</code> is the abstract type from which all broadcast styles are derived. When used as a function it has two possible forms, unary (single-argument) and binary. The unary variant states that you intend to implement specific broadcasting behavior and/or output type, and do not wish to rely on the default fallback <a href="../base/arrays.html#Base.Broadcast.DefaultArrayStyle"><code>Broadcast.DefaultArrayStyle</code></a>.</p><p>To override these defaults, you can define a custom <code>BroadcastStyle</code> for your object:</p><pre><code class="language-julia hljs">struct MyStyle &lt;: Broadcast.BroadcastStyle end
Base.BroadcastStyle(::Type{&lt;:MyType}) = MyStyle()</code></pre><p>In some cases it might be convenient not to have to define <code>MyStyle</code>, in which case you can leverage one of the general broadcast wrappers:</p><ul><li><code>Base.BroadcastStyle(::Type{&lt;:MyType}) = Broadcast.Style{MyType}()</code> can be used for arbitrary types.</li><li><code>Base.BroadcastStyle(::Type{&lt;:MyType}) = Broadcast.ArrayStyle{MyType}()</code> is preferred if <code>MyType</code> is an <code>AbstractArray</code>.</li><li>For <code>AbstractArrays</code> that only support a certain dimensionality, create a subtype of <code>Broadcast.AbstractArrayStyle{N}</code> (see below).</li></ul><p>When your broadcast operation involves several arguments, individual argument styles get combined to determine a single <code>DestStyle</code> that controls the type of the output container. For more details, see <a href="interfaces.html#writing-binary-broadcasting-rules">below</a>.</p><h3 id="Selecting-an-appropriate-output-array"><a class="docs-heading-anchor" href="#Selecting-an-appropriate-output-array">Selecting an appropriate output array</a><a id="Selecting-an-appropriate-output-array-1"></a><a class="docs-heading-anchor-permalink" href="#Selecting-an-appropriate-output-array" title="Permalink"></a></h3><p>The broadcast style is computed for every broadcasting operation to allow for dispatch and specialization. The actual allocation of the result array is handled by <code>similar</code>, using the Broadcasted object as its first argument.</p><pre><code class="language-julia hljs">Base.similar(bc::Broadcasted{DestStyle}, ::Type{ElType})</code></pre><p>The fallback definition is</p><pre><code class="language-julia hljs">similar(bc::Broadcasted{DefaultArrayStyle{N}}, ::Type{ElType}) where {N,ElType} =
    similar(Array{ElType}, axes(bc))</code></pre><p>However, if needed you can specialize on any or all of these arguments. The final argument <code>bc</code> is a lazy representation of a (potentially fused) broadcast operation, a <code>Broadcasted</code> object.  For these purposes, the most important fields of the wrapper are <code>f</code> and <code>args</code>, describing the function and argument list, respectively.  Note that the argument list can — and often does — include other nested <code>Broadcasted</code> wrappers.</p><p>For a complete example, let&#39;s say you have created a type, <code>ArrayAndChar</code>, that stores an array and a single character:</p><pre><code class="language-julia hljs">struct ArrayAndChar{T,N} &lt;: AbstractArray{T,N}
    data::Array{T,N}
    char::Char
end
Base.size(A::ArrayAndChar) = size(A.data)
Base.getindex(A::ArrayAndChar{T,N}, inds::Vararg{Int,N}) where {T,N} = A.data[inds...]
Base.setindex!(A::ArrayAndChar{T,N}, val, inds::Vararg{Int,N}) where {T,N} = A.data[inds...] = val
Base.showarg(io::IO, A::ArrayAndChar, toplevel) = print(io, typeof(A), &quot; with char &#39;&quot;, A.char, &quot;&#39;&quot;)</code></pre><p>You might want broadcasting to preserve the <code>char</code> &quot;metadata&quot;. First we define</p><pre><code class="language-julia hljs">Base.BroadcastStyle(::Type{&lt;:ArrayAndChar}) = Broadcast.ArrayStyle{ArrayAndChar}()</code></pre><p>This means we must also define a corresponding <code>similar</code> method:</p><pre><code class="language-julia hljs">function Base.similar(bc::Broadcast.Broadcasted{Broadcast.ArrayStyle{ArrayAndChar}}, ::Type{ElType}) where ElType
    # Scan the inputs for the ArrayAndChar:
    A = find_aac(bc)
    # Use the char field of A to create the output
    ArrayAndChar(similar(Array{ElType}, axes(bc)), A.char)
end

&quot;`A = find_aac(As)` returns the first ArrayAndChar among the arguments.&quot;
find_aac(bc::Base.Broadcast.Broadcasted) = find_aac(bc.args)
find_aac(args::Tuple) = find_aac(find_aac(args[1]), Base.tail(args))
find_aac(x) = x
find_aac(::Tuple{}) = nothing
find_aac(a::ArrayAndChar, rest) = a
find_aac(::Any, rest) = find_aac(rest)</code></pre><p>From these definitions, one obtains the following behavior:</p><pre><code class="language-julia-repl hljs">julia&gt; a = ArrayAndChar([1 2; 3 4], &#39;x&#39;)
2×2 ArrayAndChar{Int64, 2} with char &#39;x&#39;:
 1  2
 3  4

julia&gt; a .+ 1
2×2 ArrayAndChar{Int64, 2} with char &#39;x&#39;:
 2  3
 4  5

julia&gt; a .+ [5,10]
2×2 ArrayAndChar{Int64, 2} with char &#39;x&#39;:
  6   7
 13  14</code></pre><h3 id="extending-in-place-broadcast"><a class="docs-heading-anchor" href="#extending-in-place-broadcast">Extending broadcast with custom implementations</a><a id="extending-in-place-broadcast-1"></a><a class="docs-heading-anchor-permalink" href="#extending-in-place-broadcast" title="Permalink"></a></h3><p>In general, a broadcast operation is represented by a lazy <code>Broadcasted</code> container that holds onto the function to be applied alongside its arguments. Those arguments may themselves be more nested <code>Broadcasted</code> containers, forming a large expression tree to be evaluated. A nested tree of <code>Broadcasted</code> containers is directly constructed by the implicit dot syntax; <code>5 .+ 2.*x</code> is transiently represented by <code>Broadcasted(+, 5, Broadcasted(*, 2, x))</code>, for example. This is invisible to users as it is immediately realized through a call to <code>copy</code>, but it is this container that provides the basis for broadcast&#39;s extensibility for authors of custom types. The built-in broadcast machinery will then determine the result type and size based upon the arguments, allocate it, and then finally copy the realization of the <code>Broadcasted</code> object into it with a default <code>copyto!(::AbstractArray, ::Broadcasted)</code> method. The built-in fallback <code>broadcast</code> and <code>broadcast!</code> methods similarly construct a transient <code>Broadcasted</code> representation of the operation so they can follow the same codepath. This allows custom array implementations to provide their own <code>copyto!</code> specialization to customize and optimize broadcasting. This is again determined by the computed broadcast style. This is such an important part of the operation that it is stored as the first type parameter of the <code>Broadcasted</code> type, allowing for dispatch and specialization.</p><p>For some types, the machinery to &quot;fuse&quot; operations across nested levels of broadcasting is not available or could be done more efficiently incrementally. In such cases, you may need or want to evaluate <code>x .* (x .+ 1)</code> as if it had been written <code>broadcast(*, x, broadcast(+, x, 1))</code>, where the inner operation is evaluated before tackling the outer operation. This sort of eager operation is directly supported by a bit of indirection; instead of directly constructing <code>Broadcasted</code> objects, Julia lowers the fused expression <code>x .* (x .+ 1)</code> to <code>Broadcast.broadcasted(*, x, Broadcast.broadcasted(+, x, 1))</code>. Now, by default, <code>broadcasted</code> just calls the <code>Broadcasted</code> constructor to create the lazy representation of the fused expression tree, but you can choose to override it for a particular combination of function and arguments.</p><p>As an example, the builtin <code>AbstractRange</code> objects use this machinery to optimize pieces of broadcasted expressions that can be eagerly evaluated purely in terms of the start, step, and length (or stop) instead of computing every single element. Just like all the other machinery, <code>broadcasted</code> also computes and exposes the combined broadcast style of its arguments, so instead of specializing on <code>broadcasted(f, args...)</code>, you can specialize on <code>broadcasted(::DestStyle, f, args...)</code> for any combination of style, function, and arguments.</p><p>For example, the following definition supports the negation of ranges:</p><pre><code class="language-julia hljs">broadcasted(::DefaultArrayStyle{1}, ::typeof(-), r::OrdinalRange) = range(-first(r), step=-step(r), length=length(r))</code></pre><h3 id="extending-in-place-broadcast-2"><a class="docs-heading-anchor" href="#extending-in-place-broadcast-2">Extending in-place broadcasting</a><a class="docs-heading-anchor-permalink" href="#extending-in-place-broadcast-2" title="Permalink"></a></h3><p>In-place broadcasting can be supported by defining the appropriate <code>copyto!(dest, bc::Broadcasted)</code> method. Because you might want to specialize either on <code>dest</code> or the specific subtype of <code>bc</code>, to avoid ambiguities between packages we recommend the following convention.</p><p>If you wish to specialize on a particular style <code>DestStyle</code>, define a method for</p><pre><code class="language-julia hljs">copyto!(dest, bc::Broadcasted{DestStyle})</code></pre><p>Optionally, with this form you can also specialize on the type of <code>dest</code>.</p><p>If instead you want to specialize on the destination type <code>DestType</code> without specializing on <code>DestStyle</code>, then you should define a method with the following signature:</p><pre><code class="language-julia hljs">copyto!(dest::DestType, bc::Broadcasted{Nothing})</code></pre><p>This leverages a fallback implementation of <code>copyto!</code> that converts the wrapper into a <code>Broadcasted{Nothing}</code>. Consequently, specializing on <code>DestType</code> has lower precedence than methods that specialize on <code>DestStyle</code>.</p><p>Similarly, you can completely override out-of-place broadcasting with a <code>copy(::Broadcasted)</code> method.</p><h4 id="Working-with-Broadcasted-objects"><a class="docs-heading-anchor" href="#Working-with-Broadcasted-objects">Working with <code>Broadcasted</code> objects</a><a id="Working-with-Broadcasted-objects-1"></a><a class="docs-heading-anchor-permalink" href="#Working-with-Broadcasted-objects" title="Permalink"></a></h4><p>In order to implement such a <code>copy</code> or <code>copyto!</code>, method, of course, you must work with the <code>Broadcasted</code> wrapper to compute each element. There are two main ways of doing so:</p><ul><li><code>Broadcast.flatten</code> recomputes the potentially nested operation into a single function and flat list of arguments. You are responsible for implementing the broadcasting shape rules yourself, but this may be helpful in limited situations.</li><li>Iterating over the <code>CartesianIndices</code> of the <code>axes(::Broadcasted)</code> and using indexing with the resulting <code>CartesianIndex</code> object to compute the result.</li></ul><h3 id="writing-binary-broadcasting-rules"><a class="docs-heading-anchor" href="#writing-binary-broadcasting-rules">Writing binary broadcasting rules</a><a id="writing-binary-broadcasting-rules-1"></a><a class="docs-heading-anchor-permalink" href="#writing-binary-broadcasting-rules" title="Permalink"></a></h3><p>The precedence rules are defined by binary <code>BroadcastStyle</code> calls:</p><pre><code class="language-julia hljs">Base.BroadcastStyle(::Style1, ::Style2) = Style12()</code></pre><p>where <code>Style12</code> is the <code>BroadcastStyle</code> you want to choose for outputs involving arguments of <code>Style1</code> and <code>Style2</code>. For example,</p><pre><code class="language-julia hljs">Base.BroadcastStyle(::Broadcast.Style{Tuple}, ::Broadcast.AbstractArrayStyle{0}) = Broadcast.Style{Tuple}()</code></pre><p>indicates that <code>Tuple</code> &quot;wins&quot; over zero-dimensional arrays (the output container will be a tuple). It is worth noting that you do not need to (and should not) define both argument orders of this call; defining one is sufficient no matter what order the user supplies the arguments in.</p><p>For <code>AbstractArray</code> types, defining a <code>BroadcastStyle</code> supersedes the fallback choice, <a href="../base/arrays.html#Base.Broadcast.DefaultArrayStyle"><code>Broadcast.DefaultArrayStyle</code></a>. <code>DefaultArrayStyle</code> and the abstract supertype, <code>AbstractArrayStyle</code>, store the dimensionality as a type parameter to support specialized array types that have fixed dimensionality requirements.</p><p><code>DefaultArrayStyle</code> &quot;loses&quot; to any other <code>AbstractArrayStyle</code> that has been defined because of the following methods:</p><pre><code class="language-julia hljs">BroadcastStyle(a::AbstractArrayStyle{Any}, ::DefaultArrayStyle) = a
BroadcastStyle(a::AbstractArrayStyle{N}, ::DefaultArrayStyle{N}) where N = a
BroadcastStyle(a::AbstractArrayStyle{M}, ::DefaultArrayStyle{N}) where {M,N} =
    typeof(a)(Val(max(M, N)))</code></pre><p>You do not need to write binary <code>BroadcastStyle</code> rules unless you want to establish precedence for two or more non-<code>DefaultArrayStyle</code> types.</p><p>If your array type does have fixed dimensionality requirements, then you should subtype <code>AbstractArrayStyle</code>. For example, the sparse array code has the following definitions:</p><pre><code class="language-julia hljs">struct SparseVecStyle &lt;: Broadcast.AbstractArrayStyle{1} end
struct SparseMatStyle &lt;: Broadcast.AbstractArrayStyle{2} end
Base.BroadcastStyle(::Type{&lt;:SparseVector}) = SparseVecStyle()
Base.BroadcastStyle(::Type{&lt;:SparseMatrixCSC}) = SparseMatStyle()</code></pre><p>Whenever you subtype <code>AbstractArrayStyle</code>, you also need to define rules for combining dimensionalities, by creating a constructor for your style that takes a <code>Val(N)</code> argument. For example:</p><pre><code class="language-julia hljs">SparseVecStyle(::Val{0}) = SparseVecStyle()
SparseVecStyle(::Val{1}) = SparseVecStyle()
SparseVecStyle(::Val{2}) = SparseMatStyle()
SparseVecStyle(::Val{N}) where N = Broadcast.DefaultArrayStyle{N}()</code></pre><p>These rules indicate that the combination of a <code>SparseVecStyle</code> with 0- or 1-dimensional arrays yields another <code>SparseVecStyle</code>, that its combination with a 2-dimensional array yields a <code>SparseMatStyle</code>, and anything of higher dimensionality falls back to the dense arbitrary-dimensional framework. These rules allow broadcasting to keep the sparse representation for operations that result in one or two dimensional outputs, but produce an <code>Array</code> for any other dimensionality.</p><h2 id="man-instance-properties"><a class="docs-heading-anchor" href="#man-instance-properties">Instance Properties</a><a id="man-instance-properties-1"></a><a class="docs-heading-anchor-permalink" href="#man-instance-properties" title="Permalink"></a></h2><table><tr><th style="text-align: left">Methods to implement</th><th style="text-align: left">Default definition</th><th style="text-align: left">Brief description</th></tr><tr><td style="text-align: left"><code>propertynames(x::ObjType, private::Bool=false)</code></td><td style="text-align: left"><code>fieldnames(typeof(x))</code></td><td style="text-align: left">Return a tuple of the properties (<code>x.property</code>) of an object <code>x</code>. If <code>private=true</code>, also return property names intended to be kept as private</td></tr><tr><td style="text-align: left"><code>getproperty(x::ObjType, s::Symbol)</code></td><td style="text-align: left"><code>getfield(x, s)</code></td><td style="text-align: left">Return property <code>s</code> of <code>x</code>. <code>x.s</code> calls <code>getproperty(x, :s)</code>.</td></tr><tr><td style="text-align: left"><code>setproperty!(x::ObjType, s::Symbol, v)</code></td><td style="text-align: left"><code>setfield!(x, s, v)</code></td><td style="text-align: left">Set property <code>s</code> of <code>x</code> to <code>v</code>. <code>x.s = v</code> calls <code>setproperty!(x, :s, v)</code>. Should return <code>v</code>.</td></tr></table><p>Sometimes, it is desirable to change how the end-user interacts with the fields of an object. Instead of granting direct access to type fields, an extra layer of abstraction between the user and the code can be provided by overloading <code>object.field</code>. Properties are what the user <em>sees of</em> the object, fields what the object <em>actually is</em>.</p><p>By default, properties and fields are the same. However, this behavior can be changed. For example, take this representation of a point in a plane in <a href="https://en.wikipedia.org/wiki/Polar_coordinate_system">polar coordinates</a>:</p><pre><code class="language-julia-repl hljs">julia&gt; mutable struct Point
           r::Float64
           ϕ::Float64
       end

julia&gt; p = Point(7.0, pi/4)
Point(7.0, 0.7853981633974483)</code></pre><p>As described in the table above dot access <code>p.r</code> is the same as <code>getproperty(p, :r)</code> which is by default the same as <code>getfield(p, :r)</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; propertynames(p)
(:r, :ϕ)

julia&gt; getproperty(p, :r), getproperty(p, :ϕ)
(7.0, 0.7853981633974483)

julia&gt; p.r, p.ϕ
(7.0, 0.7853981633974483)

julia&gt; getfield(p, :r), getproperty(p, :ϕ)
(7.0, 0.7853981633974483)</code></pre><p>However, we may want users to be unaware that <code>Point</code> stores the coordinates as <code>r</code> and <code>ϕ</code> (fields), and instead interact with <code>x</code> and <code>y</code> (properties). The methods in the first column can be defined to add new functionality:</p><pre><code class="language-julia-repl hljs">julia&gt; Base.propertynames(::Point, private::Bool=false) = private ? (:x, :y, :r, :ϕ) : (:x, :y)

julia&gt; function Base.getproperty(p::Point, s::Symbol)
           if s === :x
               return getfield(p, :r) * cos(getfield(p, :ϕ))
           elseif s === :y
               return getfield(p, :r) * sin(getfield(p, :ϕ))
           else
               # This allows accessing fields with p.r and p.ϕ
               return getfield(p, s)
           end
       end

julia&gt; function Base.setproperty!(p::Point, s::Symbol, f)
           if s === :x
               y = p.y
               setfield!(p, :r, sqrt(f^2 + y^2))
               setfield!(p, :ϕ, atan(y, f))
               return f
           elseif s === :y
               x = p.x
               setfield!(p, :r, sqrt(x^2 + f^2))
               setfield!(p, :ϕ, atan(f, x))
               return f
           else
               # This allow modifying fields with p.r and p.ϕ
               return setfield!(p, s, f)
           end
       end</code></pre><p>It is important that <code>getfield</code> and <code>setfield</code> are used inside <code>getproperty</code> and <code>setproperty!</code> instead of the dot syntax, since the dot syntax would make the functions recursive which can lead to type inference issues. We can now try out the new functionality:</p><pre><code class="language-julia-repl hljs">julia&gt; propertynames(p)
(:x, :y)

julia&gt; p.x
4.949747468305833

julia&gt; p.y = 4.0
4.0

julia&gt; p.r
6.363961030678928</code></pre><p>Finally, it is worth noting that adding instance properties like this is quite rarely done in Julia and should in general only be done if there is a good reason for doing so.</p><h2 id="man-rounding-interface"><a class="docs-heading-anchor" href="#man-rounding-interface">Rounding</a><a id="man-rounding-interface-1"></a><a class="docs-heading-anchor-permalink" href="#man-rounding-interface" title="Permalink"></a></h2><table><tr><th style="text-align: left">Methods to implement</th><th style="text-align: left">Default definition</th><th style="text-align: left">Brief description</th></tr><tr><td style="text-align: left"><code>round(x::ObjType, r::RoundingMode)</code></td><td style="text-align: left">none</td><td style="text-align: left">Round <code>x</code> and return the result. If possible, round should return an object of the same type as <code>x</code></td></tr><tr><td style="text-align: left"><code>round(T::Type, x::ObjType, r::RoundingMode)</code></td><td style="text-align: left"><code>convert(T, round(x, r))</code></td><td style="text-align: left">Round <code>x</code>, returning the result as a <code>T</code></td></tr></table><p>To support rounding on a new type it is typically sufficient to define the single method <code>round(x::ObjType, r::RoundingMode)</code>. The passed rounding mode determines in which direction the value should be rounded. The most commonly used rounding modes are <code>RoundNearest</code>, <code>RoundToZero</code>, <code>RoundDown</code>, and <code>RoundUp</code>, as these rounding modes are used in the definitions of the one argument <code>round</code>, method, and <code>trunc</code>, <code>floor</code>, and <code>ceil</code>, respectively.</p><p>In some cases, it is possible to define a three-argument <code>round</code> method that is more accurate or performant than the two-argument method followed by conversion. In this case it is acceptable to define the three argument method in addition to the two argument method. If it is impossible to represent the rounded result as an object of the type <code>T</code>, then the three argument method should throw an <code>InexactError</code>.</p><p>For example, if we have an <code>Interval</code> type which represents a range of possible values similar to https://github.com/JuliaPhysics/Measurements.jl, we may define rounding on that type with the following</p><pre><code class="language-julia-repl hljs">julia&gt; struct Interval{T}
           min::T
           max::T
       end

julia&gt; Base.round(x::Interval, r::RoundingMode) = Interval(round(x.min, r), round(x.max, r))

julia&gt; x = Interval(1.7, 2.2)
Interval{Float64}(1.7, 2.2)

julia&gt; round(x)
Interval{Float64}(2.0, 2.0)

julia&gt; floor(x)
Interval{Float64}(1.0, 2.0)

julia&gt; ceil(x)
Interval{Float64}(2.0, 3.0)

julia&gt; trunc(x)
Interval{Float64}(1.0, 2.0)</code></pre></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="conversion-and-promotion.html">« Conversion and Promotion</a><a class="docs-footer-nextpage" href="modules.html">Modules »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
