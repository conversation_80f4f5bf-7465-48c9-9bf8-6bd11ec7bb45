<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Profiling · The Julia Language</title><meta name="title" content="Profiling · The Julia Language"/><meta property="og:title" content="Profiling · The Julia Language"/><meta property="twitter:title" content="Profiling · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li class="is-active"><a class="tocitem" href="profile.html">Profiling</a><ul class="internal"><li><a class="tocitem" href="#Basic-usage"><span>Basic usage</span></a></li><li><a class="tocitem" href="#Accumulation-and-clearing"><span>Accumulation and clearing</span></a></li><li><a class="tocitem" href="#Options-for-controlling-the-display-of-profile-results"><span>Options for controlling the display of profile results</span></a></li><li><a class="tocitem" href="#Configuration"><span>Configuration</span></a></li><li><a class="tocitem" href="#Memory-allocation-analysis"><span>Memory allocation analysis</span></a></li><li><a class="tocitem" href="#External-Profiling"><span>External Profiling</span></a></li></ul></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="profile.html">Profiling</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="profile.html">Profiling</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/profile.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Profiling"><a class="docs-heading-anchor" href="#Profiling">Profiling</a><a id="Profiling-1"></a><a class="docs-heading-anchor-permalink" href="#Profiling" title="Permalink"></a></h1><p>The <code>Profile</code> module provides tools to help developers improve the performance of their code. When used, it takes measurements on running code, and produces output that helps you understand how much time is spent on individual line(s). The most common usage is to identify &quot;bottlenecks&quot; as targets for optimization.</p><p><code>Profile</code> implements what is known as a &quot;sampling&quot; or <a href="https://en.wikipedia.org/wiki/Profiling_(computer_programming)">statistical profiler</a>.  It works by periodically taking a backtrace during the execution of any task. Each backtrace captures the currently-running function and line number, plus the complete chain of function calls that led to this line, and hence is a &quot;snapshot&quot; of the current state of execution.</p><p>If much of your run time is spent executing a particular line of code, this line will show up frequently in the set of all backtraces. In other words, the &quot;cost&quot; of a given line–or really, the cost of the sequence of function calls up to and including this line–is proportional to how often it appears in the set of all backtraces.</p><p>A sampling profiler does not provide complete line-by-line coverage, because the backtraces occur at intervals (by default, 1 ms on Unix systems and 10 ms on Windows, although the actual scheduling is subject to operating system load). Moreover, as discussed further below, because samples are collected at a sparse subset of all execution points, the data collected by a sampling profiler is subject to statistical noise.</p><p>Despite these limitations, sampling profilers have substantial strengths:</p><ul><li>You do not have to make any modifications to your code to take timing measurements.</li><li>It can profile into Julia&#39;s core code and even (optionally) into C and Fortran libraries.</li><li>By running &quot;infrequently&quot; there is very little performance overhead; while profiling, your code can run at nearly native speed.</li></ul><p>For these reasons, it&#39;s recommended that you try using the built-in sampling profiler before considering any alternatives.</p><h2 id="Basic-usage"><a class="docs-heading-anchor" href="#Basic-usage">Basic usage</a><a id="Basic-usage-1"></a><a class="docs-heading-anchor-permalink" href="#Basic-usage" title="Permalink"></a></h2><p>Let&#39;s work with a simple test case:</p><pre><code class="language-julia-repl hljs">julia&gt; function myfunc()
           A = rand(200, 200, 400)
           maximum(A)
       end</code></pre><p>It&#39;s a good idea to first run the code you intend to profile at least once (unless you want to profile Julia&#39;s JIT-compiler):</p><pre><code class="language-julia-repl hljs">julia&gt; myfunc() # run once to force compilation</code></pre><p>Now we&#39;re ready to profile this function:</p><pre><code class="language-julia-repl hljs">julia&gt; using Profile

julia&gt; @profile myfunc()</code></pre><p>To see the profiling results, there are several graphical browsers. One &quot;family&quot; of visualizers is based on <a href="https://github.com/timholy/FlameGraphs.jl">FlameGraphs.jl</a>, with each family member providing a different user interface:</p><ul><li><a href="https://www.julia-vscode.org/">VS Code</a> is a full IDE with built-in support for profile visualization</li><li><a href="https://github.com/timholy/ProfileView.jl">ProfileView.jl</a> is a stand-alone visualizer based on GTK</li><li><a href="https://github.com/davidanthoff/ProfileVega.jl">ProfileVega.jl</a> uses VegaLight and integrates well with Jupyter notebooks</li><li><a href="https://github.com/tkluck/StatProfilerHTML.jl">StatProfilerHTML.jl</a> produces HTML and presents some additional summaries, and also integrates well with Jupyter notebooks</li><li><a href="https://github.com/timholy/ProfileSVG.jl">ProfileSVG.jl</a> renders SVG</li><li><a href="https://github.com/JuliaPerf/PProf.jl">PProf.jl</a> serves a local website for inspecting graphs, flamegraphs and more</li><li><a href="https://github.com/pfitzseb/ProfileCanvas.jl">ProfileCanvas.jl</a> is a HTML canvas based profile viewer UI, used by the <a href="https://www.julia-vscode.org/">Julia VS Code extension</a>, but can also generate interactive HTML files.</li></ul><p>An entirely independent approach to profile visualization is <a href="https://github.com/vchuravy/PProf.jl">PProf.jl</a>, which uses the external <code>pprof</code> tool.</p><p>Here, though, we&#39;ll use the text-based display that comes with the standard library:</p><pre><code class="language-julia-repl hljs">julia&gt; Profile.print()
80 ./event.jl:73; (::Base.REPL.##1#2{Base.REPL.REPLBackend})()
 80 ./REPL.jl:97; macro expansion
  80 ./REPL.jl:66; eval_user_input(::Any, ::Base.REPL.REPLBackend)
   80 ./boot.jl:235; eval(::Module, ::Any)
    80 ./&lt;missing&gt;:?; anonymous
     80 ./profile.jl:23; macro expansion
      52 ./REPL[1]:2; myfunc()
       38 ./random.jl:431; rand!(::MersenneTwister, ::Array{Float64,3}, ::Int64, ::Type{B...
        38 ./dSFMT.jl:84; dsfmt_fill_array_close_open!(::Base.dSFMT.DSFMT_state, ::Ptr{F...
       14 ./random.jl:278; rand
        14 ./random.jl:277; rand
         14 ./random.jl:366; rand
          14 ./random.jl:369; rand
      28 ./REPL[1]:3; myfunc()
       28 ./reduce.jl:270; _mapreduce(::Base.#identity, ::Base.#scalarmax, ::IndexLinear,...
        3  ./reduce.jl:426; mapreduce_impl(::Base.#identity, ::Base.#scalarmax, ::Array{F...
        25 ./reduce.jl:428; mapreduce_impl(::Base.#identity, ::Base.#scalarmax, ::Array{F...</code></pre><p>Each line of this display represents a particular spot (line number) in the code. Indentation is used to indicate the nested sequence of function calls, with more-indented lines being deeper in the sequence of calls. In each line, the first &quot;field&quot; is the number of backtraces (samples) taken <em>at this line or in any functions executed by this line</em>. The second field is the file name and line number and the third field is the function name. Note that the specific line numbers may change as Julia&#39;s code changes; if you want to follow along, it&#39;s best to run this example yourself.</p><p>In this example, we can see that the top level function called is in the file <code>event.jl</code>. This is the function that runs the REPL when you launch Julia. If you examine line 97 of <code>REPL.jl</code>, you&#39;ll see this is where the function <code>eval_user_input()</code> is called. This is the function that evaluates what you type at the REPL, and since we&#39;re working interactively these functions were invoked when we entered <code>@profile myfunc()</code>. The next line reflects actions taken in the <a href="../stdlib/Profile.html#Profile.@profile"><code>@profile</code></a> macro.</p><p>The first line shows that 80 backtraces were taken at line 73 of <code>event.jl</code>, but it&#39;s not that this line was &quot;expensive&quot; on its own: the third line reveals that all 80 of these backtraces were actually triggered inside its call to <code>eval_user_input</code>, and so on. To find out which operations are actually taking the time, we need to look deeper in the call chain.</p><p>The first &quot;important&quot; line in this output is this one:</p><pre><code class="nohighlight hljs">52 ./REPL[1]:2; myfunc()</code></pre><p><code>REPL</code> refers to the fact that we defined <code>myfunc</code> in the REPL, rather than putting it in a file; if we had used a file, this would show the file name. The <code>[1]</code> shows that the function <code>myfunc</code> was the first expression evaluated in this REPL session. Line 2 of <code>myfunc()</code> contains the call to <code>rand</code>, and there were 52 (out of 80) backtraces that occurred at this line. Below that, you can see a call to <code>dsfmt_fill_array_close_open!</code> inside <code>dSFMT.jl</code>.</p><p>A little further down, you see:</p><pre><code class="nohighlight hljs">28 ./REPL[1]:3; myfunc()</code></pre><p>Line 3 of <code>myfunc</code> contains the call to <code>maximum</code>, and there were 28 (out of 80) backtraces taken here. Below that, you can see the specific places in <code>base/reduce.jl</code> that carry out the time-consuming operations in the <code>maximum</code> function for this type of input data.</p><p>Overall, we can tentatively conclude that generating the random numbers is approximately twice as expensive as finding the maximum element. We could increase our confidence in this result by collecting more samples:</p><pre><code class="language-julia-repl hljs">julia&gt; @profile (for i = 1:100; myfunc(); end)

julia&gt; Profile.print()
[....]
 3821 ./REPL[1]:2; myfunc()
  3511 ./random.jl:431; rand!(::MersenneTwister, ::Array{Float64,3}, ::Int64, ::Type...
   3511 ./dSFMT.jl:84; dsfmt_fill_array_close_open!(::Base.dSFMT.DSFMT_state, ::Ptr...
  310  ./random.jl:278; rand
   [....]
 2893 ./REPL[1]:3; myfunc()
  2893 ./reduce.jl:270; _mapreduce(::Base.#identity, ::Base.#scalarmax, ::IndexLinea...
   [....]</code></pre><p>In general, if you have <code>N</code> samples collected at a line, you can expect an uncertainty on the order of <code>sqrt(N)</code> (barring other sources of noise, like how busy the computer is with other tasks). The major exception to this rule is garbage collection, which runs infrequently but tends to be quite expensive. (Since Julia&#39;s garbage collector is written in C, such events can be detected using the <code>C=true</code> output mode described below, or by using <a href="https://github.com/timholy/ProfileView.jl">ProfileView.jl</a>.)</p><p>This illustrates the default &quot;tree&quot; dump; an alternative is the &quot;flat&quot; dump, which accumulates counts independent of their nesting:</p><pre><code class="language-julia-repl hljs">julia&gt; Profile.print(format=:flat)
 Count File          Line Function
  6714 ./&lt;missing&gt;     -1 anonymous
  6714 ./REPL.jl       66 eval_user_input(::Any, ::Base.REPL.REPLBackend)
  6714 ./REPL.jl       97 macro expansion
  3821 ./REPL[1]        2 myfunc()
  2893 ./REPL[1]        3 myfunc()
  6714 ./REPL[7]        1 macro expansion
  6714 ./boot.jl      235 eval(::Module, ::Any)
  3511 ./dSFMT.jl      84 dsfmt_fill_array_close_open!(::Base.dSFMT.DSFMT_s...
  6714 ./event.jl      73 (::Base.REPL.##1#2{Base.REPL.REPLBackend})()
  6714 ./profile.jl    23 macro expansion
  3511 ./random.jl    431 rand!(::MersenneTwister, ::Array{Float64,3}, ::In...
   310 ./random.jl    277 rand
   310 ./random.jl    278 rand
   310 ./random.jl    366 rand
   310 ./random.jl    369 rand
  2893 ./reduce.jl    270 _mapreduce(::Base.#identity, ::Base.#scalarmax, :...
     5 ./reduce.jl    420 mapreduce_impl(::Base.#identity, ::Base.#scalarma...
   253 ./reduce.jl    426 mapreduce_impl(::Base.#identity, ::Base.#scalarma...
  2592 ./reduce.jl    428 mapreduce_impl(::Base.#identity, ::Base.#scalarma...
    43 ./reduce.jl    429 mapreduce_impl(::Base.#identity, ::Base.#scalarma...</code></pre><p>If your code has recursion, one potentially-confusing point is that a line in a &quot;child&quot; function can accumulate more counts than there are total backtraces. Consider the following function definitions:</p><pre><code class="language-julia hljs">dumbsum(n::Integer) = n == 1 ? 1 : 1 + dumbsum(n-1)
dumbsum3() = dumbsum(3)</code></pre><p>If you were to profile <code>dumbsum3</code>, and a backtrace was taken while it was executing <code>dumbsum(1)</code>, the backtrace would look like this:</p><pre><code class="language-julia hljs">dumbsum3
    dumbsum(3)
        dumbsum(2)
            dumbsum(1)</code></pre><p>Consequently, this child function gets 3 counts, even though the parent only gets one. The &quot;tree&quot; representation makes this much clearer, and for this reason (among others) is probably the most useful way to view the results.</p><h2 id="Accumulation-and-clearing"><a class="docs-heading-anchor" href="#Accumulation-and-clearing">Accumulation and clearing</a><a id="Accumulation-and-clearing-1"></a><a class="docs-heading-anchor-permalink" href="#Accumulation-and-clearing" title="Permalink"></a></h2><p>Results from <a href="../stdlib/Profile.html#Profile.@profile"><code>@profile</code></a> accumulate in a buffer; if you run multiple pieces of code under <a href="../stdlib/Profile.html#Profile.@profile"><code>@profile</code></a>, then <a href="../stdlib/Profile.html#Profile.print"><code>Profile.print()</code></a> will show you the combined results. This can be very useful, but sometimes you want to start fresh; you can do so with <a href="../stdlib/Profile.html#Profile.clear"><code>Profile.clear()</code></a>.</p><h2 id="Options-for-controlling-the-display-of-profile-results"><a class="docs-heading-anchor" href="#Options-for-controlling-the-display-of-profile-results">Options for controlling the display of profile results</a><a id="Options-for-controlling-the-display-of-profile-results-1"></a><a class="docs-heading-anchor-permalink" href="#Options-for-controlling-the-display-of-profile-results" title="Permalink"></a></h2><p><a href="../stdlib/Profile.html#Profile.print"><code>Profile.print</code></a> has more options than we&#39;ve described so far. Let&#39;s see the full declaration:</p><pre><code class="language-julia hljs">function print(io::IO = stdout, data = fetch(); kwargs...)</code></pre><p>Let&#39;s first discuss the two positional arguments, and later the keyword arguments:</p><ul><li><p><code>io</code> – Allows you to save the results to a buffer, e.g. a file, but the default is to print to <code>stdout</code> (the console).</p></li><li><p><code>data</code> – Contains the data you want to analyze; by default that is obtained from <a href="../stdlib/Profile.html#Profile.fetch"><code>Profile.fetch()</code></a>, which pulls out the backtraces from a pre-allocated buffer. For example, if you want to profile the profiler, you could say:</p><pre><code class="language-julia hljs">data = copy(Profile.fetch())
Profile.clear()
@profile Profile.print(stdout, data) # Prints the previous results
Profile.print()                      # Prints results from Profile.print()</code></pre></li></ul><p>The keyword arguments can be any combination of:</p><ul><li><code>format</code> – Introduced above, determines whether backtraces are printed  with (default, <code>:tree</code>) or without (<code>:flat</code>) indentation indicating tree  structure.</li><li><code>C</code> – If <code>true</code>, backtraces from C and Fortran code are shown (normally they are excluded). Try running the introductory example with <code>Profile.print(C = true)</code>. This can be extremely helpful in deciding whether it&#39;s Julia code or C code that is causing a bottleneck; setting <code>C = true</code> also improves the interpretability of the nesting, at the cost of longer profile dumps.</li><li><code>combine</code> – Some lines of code contain multiple operations; for example, <code>s += A[i]</code> contains both an array reference (<code>A[i]</code>) and a sum operation. These correspond to different lines in the generated machine code, and hence there may be two or more different addresses captured during backtraces on this line. <code>combine = true</code> lumps them together, and is probably what you typically want, but you can generate an output separately for each unique instruction pointer with <code>combine = false</code>.</li><li><code>maxdepth</code> – Limits frames at a depth higher than <code>maxdepth</code> in the <code>:tree</code> format.</li><li><code>sortedby</code> – Controls the order in <code>:flat</code> format. <code>:filefuncline</code> (default) sorts by the source line, whereas <code>:count</code> sorts in order of number of collected samples.</li><li><code>noisefloor</code> – Limits frames that are below the heuristic noise floor of the sample (only applies to format <code>:tree</code>). A suggested value to try for this is 2.0 (the default is 0). This parameter hides samples for which <code>n &lt;= noisefloor * √N</code>, where <code>n</code> is the number of samples on this line, and <code>N</code> is the number of samples for the callee.</li><li><code>mincount</code> – Limits frames with less than <code>mincount</code> occurrences.</li></ul><p>File/function names are sometimes truncated (with <code>...</code>), and indentation is truncated with a <code>+n</code> at the beginning, where <code>n</code> is the number of extra spaces that would have been inserted, had there been room. If you want a complete profile of deeply-nested code, often a good idea is to save to a file using a wide <code>displaysize</code> in an <a href="../base/io-network.html#Base.IOContext"><code>IOContext</code></a>:</p><pre><code class="language-julia hljs">open(&quot;/tmp/prof.txt&quot;, &quot;w&quot;) do s
    Profile.print(IOContext(s, :displaysize =&gt; (24, 500)))
end</code></pre><h2 id="Configuration"><a class="docs-heading-anchor" href="#Configuration">Configuration</a><a id="Configuration-1"></a><a class="docs-heading-anchor-permalink" href="#Configuration" title="Permalink"></a></h2><p><a href="../stdlib/Profile.html#Profile.@profile"><code>@profile</code></a> just accumulates backtraces, and the analysis happens when you call <a href="../stdlib/Profile.html#Profile.print"><code>Profile.print()</code></a>. For a long-running computation, it&#39;s entirely possible that the pre-allocated buffer for storing backtraces will be filled. If that happens, the backtraces stop but your computation continues. As a consequence, you may miss some important profiling data (you will get a warning when that happens).</p><p>You can obtain and configure the relevant parameters this way:</p><pre><code class="language-julia hljs">Profile.init() # returns the current settings
Profile.init(n = 10^7, delay = 0.01)</code></pre><p><code>n</code> is the total number of instruction pointers you can store, with a default value of <code>10^6</code>. If your typical backtrace is 20 instruction pointers, then you can collect 50000 backtraces, which suggests a statistical uncertainty of less than 1%. This may be good enough for most applications.</p><p>Consequently, you are more likely to need to modify <code>delay</code>, expressed in seconds, which sets the amount of time that Julia gets between snapshots to perform the requested computations. A very long-running job might not need frequent backtraces. The default setting is <code>delay = 0.001</code>. Of course, you can decrease the delay as well as increase it; however, the overhead of profiling grows once the delay becomes similar to the amount of time needed to take a backtrace (~30 microseconds on the author&#39;s laptop).</p><h2 id="Memory-allocation-analysis"><a class="docs-heading-anchor" href="#Memory-allocation-analysis">Memory allocation analysis</a><a id="Memory-allocation-analysis-1"></a><a class="docs-heading-anchor-permalink" href="#Memory-allocation-analysis" title="Permalink"></a></h2><p>One of the most common techniques to improve performance is to reduce memory allocation. Julia provides several tools to measure this:</p><h3 id="@time"><a class="docs-heading-anchor" href="#@time"><code>@time</code></a><a id="@time-1"></a><a class="docs-heading-anchor-permalink" href="#@time" title="Permalink"></a></h3><p>The total amount of allocation can be measured with <a href="profile.html#@time"><code>@time</code></a>, <a href="../base/base.html#Base.@allocated"><code>@allocated</code></a> and <a href="../base/base.html#Base.@allocations"><code>@allocations</code></a>, and specific lines triggering allocation can often be inferred from profiling via the cost of garbage collection that these lines incur. However, sometimes it is more efficient to directly measure the amount of memory allocated by each line of code.</p><h3 id="GC-Logging"><a class="docs-heading-anchor" href="#GC-Logging">GC Logging</a><a id="GC-Logging-1"></a><a class="docs-heading-anchor-permalink" href="#GC-Logging" title="Permalink"></a></h3><p>While <a href="profile.html#@time"><code>@time</code></a> logs high-level stats about memory usage and garbage collection over the course of evaluating an expression, it can be useful to log each garbage collection event, to get an intuitive sense of how often the garbage collector is running, how long it&#39;s running each time, and how much garbage it collects each time. This can be enabled with <a href="../base/base.html#Base.GC.enable_logging"><code>GC.enable_logging(true)</code></a>, which causes Julia to log to stderr every time a garbage collection happens.</p><h3 id="allocation-profiler"><a class="docs-heading-anchor" href="#allocation-profiler">Allocation Profiler</a><a id="allocation-profiler-1"></a><a class="docs-heading-anchor-permalink" href="#allocation-profiler" title="Permalink"></a></h3><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>This functionality requires at least Julia 1.8.</p></div></div><p>The allocation profiler records the stack trace, type, and size of each allocation while it is running. It can be invoked with <a href="../stdlib/Profile.html#Profile.Allocs.@profile"><code>Profile.Allocs.@profile</code></a>.</p><p>This information about the allocations is returned as an array of <code>Alloc</code> objects, wrapped in an <code>AllocResults</code> object. The best way to visualize these is currently with the <a href="https://github.com/JuliaPerf/PProf.jl">PProf.jl</a> and <a href="https://github.com/pfitzseb/ProfileCanvas.jl">ProfileCanvas.jl</a> packages, which can visualize the call stacks which are making the most allocations.</p><p>The allocation profiler does have significant overhead, so a <code>sample_rate</code> argument can be passed to speed it up by making it skip some allocations. Passing <code>sample_rate=1.0</code> will make it record everything (which is slow); <code>sample_rate=0.1</code> will record only 10% of the allocations (faster), etc.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>Older versions of Julia could not capture types in all cases. In older versions of Julia, if you see an allocation of type <code>Profile.Allocs.UnknownType</code>, it means that the profiler doesn&#39;t know what type of object was allocated. This mainly happened when the allocation was coming from generated code produced by the compiler. See <a href="https://github.com/JuliaLang/julia/issues/43688">issue #43688</a> for more info.</p><p>Since Julia 1.11, all allocations should have a type reported.</p></div></div><p>For more details on how to use this tool, please see the following talk from JuliaCon 2022: https://www.youtube.com/watch?v=BFvpwC8hEWQ</p><h5 id="Allocation-Profiler-Example"><a class="docs-heading-anchor" href="#Allocation-Profiler-Example">Allocation Profiler Example</a><a id="Allocation-Profiler-Example-1"></a><a class="docs-heading-anchor-permalink" href="#Allocation-Profiler-Example" title="Permalink"></a></h5><p>In this simple example, we use PProf to visualize the alloc profile. You could use another visualization tool instead. We collect the profile (specifying a sample rate), then we visualize it.</p><pre><code class="language-julia hljs">using Profile, PProf
Profile.Allocs.clear()
Profile.Allocs.@profile sample_rate=0.0001 my_function()
PProf.Allocs.pprof()</code></pre><p>Here is a more in-depth example, showing how we can tune the sample rate. A good number of samples to aim for is around 1 - 10 thousand. Too many, and the profile visualizer can get overwhelmed, and profiling will be slow. Too few, and you don&#39;t have a representative sample.</p><pre><code class="language-julia-repl hljs">julia&gt; import Profile

julia&gt; @time my_function()  # Estimate allocations from a (second-run) of the function
  0.110018 seconds (1.50 M allocations: 58.725 MiB, 17.17% gc time)
500000

julia&gt; Profile.Allocs.clear()

julia&gt; Profile.Allocs.@profile sample_rate=0.001 begin   # 1.5 M * 0.001 = ~1.5K allocs.
           my_function()
       end
500000

julia&gt; prof = Profile.Allocs.fetch();  # If you want, you can also manually inspect the results.

julia&gt; length(prof.allocs)  # Confirm we have expected number of allocations.
1515

julia&gt; using PProf  # Now, visualize with an external tool, like PProf or ProfileCanvas.

julia&gt; PProf.Allocs.pprof(prof; from_c=false)  # You can optionally pass in a previously fetched profile result.
Analyzing 1515 allocation samples... 100%|████████████████████████████████| Time: 0:00:00
Main binary filename not available.
Serving web UI on http://localhost:62261
&quot;alloc-profile.pb.gz&quot;</code></pre><p>Then you can view the profile by navigating to http://localhost:62261, and the profile is saved to disk. See PProf package for more options.</p><h5 id="Allocation-Profiling-Tips"><a class="docs-heading-anchor" href="#Allocation-Profiling-Tips">Allocation Profiling Tips</a><a id="Allocation-Profiling-Tips-1"></a><a class="docs-heading-anchor-permalink" href="#Allocation-Profiling-Tips" title="Permalink"></a></h5><p>As stated above, aim for around 1-10 thousand samples in your profile.</p><p>Note that we are uniformly sampling in the space of <em>all allocations</em>, and are not weighting our samples by the size of the allocation. So a given allocation profile may not give a representative profile of where most bytes are allocated in your program, unless you had set <code>sample_rate=1</code>.</p><p>Allocations can come from users directly constructing objects, but can also come from inside the runtime or be inserted into compiled code to handle type instability. Looking at the &quot;source code&quot; view can be helpful to isolate them, and then other external tools such as <a href="https://github.com/JuliaDebug/Cthulhu.jl"><code>Cthulhu.jl</code></a> can be useful for identifying the cause of the allocation.</p><h5 id="Allocation-Profile-Visualization-Tools"><a class="docs-heading-anchor" href="#Allocation-Profile-Visualization-Tools">Allocation Profile Visualization Tools</a><a id="Allocation-Profile-Visualization-Tools-1"></a><a class="docs-heading-anchor-permalink" href="#Allocation-Profile-Visualization-Tools" title="Permalink"></a></h5><p>There are several profiling visualization tools now that can all display Allocation Profiles. Here is a small list of some of the main ones we know about:</p><ul><li><a href="https://github.com/JuliaPerf/PProf.jl">PProf.jl</a></li><li><a href="https://github.com/pfitzseb/ProfileCanvas.jl">ProfileCanvas.jl</a></li><li>VSCode&#39;s built-in profile visualizer (<code>@profview_allocs</code>) [docs needed]</li><li>Viewing the results directly in the REPL<ul><li>You can inspect the results in the REPL via <a href="../stdlib/Profile.html#Profile.Allocs.fetch"><code>Profile.Allocs.fetch()</code></a>, to view the stacktrace and type of each allocation.</li></ul></li></ul><h4 id="Line-by-Line-Allocation-Tracking"><a class="docs-heading-anchor" href="#Line-by-Line-Allocation-Tracking">Line-by-Line Allocation Tracking</a><a id="Line-by-Line-Allocation-Tracking-1"></a><a class="docs-heading-anchor-permalink" href="#Line-by-Line-Allocation-Tracking" title="Permalink"></a></h4><p>An alternative way to measure allocations is to start Julia with the <code>--track-allocation=&lt;setting&gt;</code> command-line option, for which you can choose <code>none</code> (the default, do not measure allocation), <code>user</code> (measure memory allocation everywhere except Julia&#39;s core code), or <code>all</code> (measure memory allocation at each line of Julia code). Allocation gets measured for each line of compiled code. When you quit Julia, the cumulative results are written to text files with <code>.mem</code> appended after the file name, residing in the same directory as the source file. Each line lists the total number of bytes allocated. The <a href="https://github.com/JuliaCI/Coverage.jl"><code>Coverage</code> package</a> contains some elementary analysis tools, for example to sort the lines in order of number of bytes allocated.</p><p>In interpreting the results, there are a few important details. Under the <code>user</code> setting, the first line of any function directly called from the REPL will exhibit allocation due to events that happen in the REPL code itself. More significantly, JIT-compilation also adds to allocation counts, because much of Julia&#39;s compiler is written in Julia (and compilation usually requires memory allocation). The recommended procedure is to force compilation by executing all the commands you want to analyze, then call <a href="../stdlib/Profile.html#Profile.clear_malloc_data"><code>Profile.clear_malloc_data()</code></a> to reset all allocation counters.  Finally, execute the desired commands and quit Julia to trigger the generation of the <code>.mem</code> files.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p><code>--track-allocation</code> changes code generation to log the allocations, and so the allocations may be different than what happens without the option. We recommend using the <a href="profile.html#allocation-profiler">allocation profiler</a> instead.</p></div></div><h2 id="External-Profiling"><a class="docs-heading-anchor" href="#External-Profiling">External Profiling</a><a id="External-Profiling-1"></a><a class="docs-heading-anchor-permalink" href="#External-Profiling" title="Permalink"></a></h2><p>Currently Julia supports <code>Intel VTune</code>, <code>OProfile</code> and <code>perf</code> as external profiling tools.</p><p>Depending on the tool you choose, compile with <code>USE_INTEL_JITEVENTS</code>, <code>USE_OPROFILE_JITEVENTS</code> and <code>USE_PERF_JITEVENTS</code> set to 1 in <code>Make.user</code>. Multiple flags are supported.</p><p>Before running Julia set the environment variable <a href="environment-variables.html#ENABLE_JITPROFILING"><code>ENABLE_JITPROFILING</code></a> to 1.</p><p>Now you have a multitude of ways to employ those tools! For example with <code>OProfile</code> you can try a simple recording :</p><pre><code class="nohighlight hljs">&gt;ENABLE_JITPROFILING=1 sudo operf -Vdebug ./julia test/fastmath.jl
&gt;opreport -l `which ./julia`</code></pre><p>Or similarly with <code>perf</code> :</p><pre><code class="nohighlight hljs">$ ENABLE_JITPROFILING=1 perf record -o /tmp/perf.data --call-graph dwarf -k 1 ./julia /test/fastmath.jl
$ perf inject --jit --input /tmp/perf.data --output /tmp/perf-jit.data
$ perf report --call-graph -G -i /tmp/perf-jit.data</code></pre><p>There are many more interesting things that you can measure about your program, to get a comprehensive list please read the <a href="https://www.brendangregg.com/perf.html">Linux perf examples page</a>.</p><p>Remember that perf saves for each execution a <code>perf.data</code> file that, even for small programs, can get quite large. Also the perf LLVM module saves temporarily debug objects in <code>~/.debug/jit</code>, remember to clean that folder frequently.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="code-loading.html">« Code Loading</a><a class="docs-footer-nextpage" href="stacktraces.html">Stack Traces »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
