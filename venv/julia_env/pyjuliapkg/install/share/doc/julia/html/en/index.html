<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Julia Documentation · The Julia Language</title><meta name="title" content="Julia Documentation · The Julia Language"/><meta property="og:title" content="Julia Documentation · The Julia Language"/><meta property="twitter:title" content="Julia Documentation · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL="."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="assets/documenter.js"></script><script src="search_index.js"></script><script src="siteinfo.js"></script><script src="../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="assets/themeswap.js"></script><link href="assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="index.html"><img class="docs-light-only" src="assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li class="is-active"><a class="tocitem" href="index.html">Julia Documentation</a><ul class="internal"><li><a class="tocitem" href="#man-important-links"><span>Important Links</span></a></li><li><a class="tocitem" href="#man-introduction"><span>Introduction</span></a></li><li><a class="tocitem" href="#man-julia-compared-other-languages"><span>Julia Compared to Other Languages</span></a></li></ul></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="manual/installation.html">Installation</a></li><li><a class="tocitem" href="manual/variables.html">Variables</a></li><li><a class="tocitem" href="manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="manual/strings.html">Strings</a></li><li><a class="tocitem" href="manual/functions.html">Functions</a></li><li><a class="tocitem" href="manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="manual/types.html">Types</a></li><li><a class="tocitem" href="manual/methods.html">Methods</a></li><li><a class="tocitem" href="manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="manual/modules.html">Modules</a></li><li><a class="tocitem" href="manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="manual/profile.html">Profiling</a></li><li><a class="tocitem" href="manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="base/base.html">Essentials</a></li><li><a class="tocitem" href="base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="base/math.html">Mathematics</a></li><li><a class="tocitem" href="base/numbers.html">Numbers</a></li><li><a class="tocitem" href="base/strings.html">Strings</a></li><li><a class="tocitem" href="base/arrays.html">Arrays</a></li><li><a class="tocitem" href="base/parallel.html">Tasks</a></li><li><a class="tocitem" href="base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="base/constants.html">Constants</a></li><li><a class="tocitem" href="base/file.html">Filesystem</a></li><li><a class="tocitem" href="base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="base/c.html">C Interface</a></li><li><a class="tocitem" href="base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="stdlib/Future.html">Future</a></li><li><a class="tocitem" href="stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="devdocs/types.html">More about types</a></li><li><a class="tocitem" href="devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li class="is-active"><a href="index.html">Julia Documentation</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="index.html">Julia Documentation</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/index.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1>Julia 1.11 Documentation</h1><p>Welcome to the documentation for Julia 1.11.</p><p>Please read the <a href="NEWS.html">release notes</a> to see what has changed since the last release.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The documentation is also available in PDF format: <a href="https://raw.githubusercontent.com/JuliaLang/docs.julialang.org/assets/julia-1.11.5.pdf">julia-1.11.5.pdf</a>.</p></div></div><h2 id="man-important-links"><a class="docs-heading-anchor" href="#man-important-links">Important Links</a><a id="man-important-links-1"></a><a class="docs-heading-anchor-permalink" href="#man-important-links" title="Permalink"></a></h2><p>Below is a non-exhaustive list of links that will be useful as you learn and use the Julia programming language.</p><ul><li><a href="https://julialang.org">Julia Homepage</a></li><li><a href="https://julialang.org/downloads/">Download Julia</a></li><li><a href="https://discourse.julialang.org">Discussion forum</a></li><li><a href="https://www.youtube.com/user/JuliaLanguage">Julia YouTube</a></li><li><a href="https://julialang.org/packages/">Find Julia Packages</a></li><li><a href="https://julialang.org/learning/">Learning Resources</a></li><li><a href="https://forem.julialang.org">Read and write blogs on Julia</a></li></ul><h2 id="man-introduction"><a class="docs-heading-anchor" href="#man-introduction">Introduction</a><a id="man-introduction-1"></a><a class="docs-heading-anchor-permalink" href="#man-introduction" title="Permalink"></a></h2><p>Scientific computing has traditionally required the highest performance, yet domain experts have largely moved to slower dynamic languages for daily work. We believe there are many good reasons to prefer dynamic languages for these applications, and we do not expect their use to diminish. Fortunately, modern language design and compiler techniques make it possible to mostly eliminate the performance trade-off and provide a single environment productive enough for prototyping and efficient enough for deploying performance-intensive applications. The Julia programming language fills this role: it is a flexible dynamic language, appropriate for scientific and numerical computing, with performance comparable to traditional statically-typed languages.</p><p>Because Julia&#39;s compiler is different from the interpreters used for languages like Python or R, you may find that Julia&#39;s performance is unintuitive at first. If you find that something is slow, we highly recommend reading through the <a href="manual/performance-tips.html#man-performance-tips">Performance Tips</a> section before trying anything else. Once you understand how Julia works, it is easy to write code that is nearly as fast as C.</p><h2 id="man-julia-compared-other-languages"><a class="docs-heading-anchor" href="#man-julia-compared-other-languages">Julia Compared to Other Languages</a><a id="man-julia-compared-other-languages-1"></a><a class="docs-heading-anchor-permalink" href="#man-julia-compared-other-languages" title="Permalink"></a></h2><p>Julia features optional typing, multiple dispatch, and good performance, achieved using type inference and <a href="https://en.wikipedia.org/wiki/Just-in-time_compilation">just-in-time (JIT) compilation</a> (and <a href="https://github.com/JuliaLang/PackageCompiler.jl">optional ahead-of-time compilation</a>), implemented using <a href="https://en.wikipedia.org/wiki/Low_Level_Virtual_Machine">LLVM</a>. It is multi-paradigm, combining features of imperative, functional, and object-oriented programming. Julia provides ease and expressiveness for high-level numerical computing, in the same way as languages such as R, MATLAB, and Python, but also supports general programming. To achieve this, Julia builds upon the lineage of mathematical programming languages, but also borrows much from popular dynamic languages, including <a href="https://en.wikipedia.org/wiki/Lisp_(programming_language)">Lisp</a>, <a href="https://en.wikipedia.org/wiki/Perl_(programming_language)">Perl</a>, <a href="https://en.wikipedia.org/wiki/Python_(programming_language)">Python</a>, <a href="https://en.wikipedia.org/wiki/Lua_(programming_language)">Lua</a>, and <a href="https://en.wikipedia.org/wiki/Ruby_(programming_language)">Ruby</a>.</p><p>The most significant departures of Julia from typical dynamic languages are:</p><ul><li>The core language imposes very little; Julia Base and the standard library are written in Julia itself, including primitive operations like integer arithmetic</li><li>A rich language of types for constructing and describing objects, that can also optionally be used to make type declarations</li><li>The ability to define function behavior across many combinations of argument types via <a href="https://en.wikipedia.org/wiki/Multiple_dispatch">multiple dispatch</a></li><li>Automatic generation of efficient, specialized code for different argument types</li><li>Good performance, approaching that of statically-compiled languages like C</li></ul><p>Although one sometimes speaks of dynamic languages as being &quot;typeless&quot;, they are definitely not. Every object, whether primitive or user-defined, has a type. The lack of type declarations in most dynamic languages, however, means that one cannot instruct the compiler about the types of values, and often cannot explicitly talk about types at all. In static languages, on the other hand, while one can – and usually must – annotate types for the compiler, types exist only at compile time and cannot be manipulated or expressed at run time. In Julia, types are themselves run-time objects, and can also be used to convey information to the compiler.</p><h3 id="man-what-makes-julia"><a class="docs-heading-anchor" href="#man-what-makes-julia">What Makes Julia, Julia?</a><a id="man-what-makes-julia-1"></a><a class="docs-heading-anchor-permalink" href="#man-what-makes-julia" title="Permalink"></a></h3><p>While the casual programmer need not explicitly use types or multiple dispatch, they are the core unifying features of Julia: functions are defined on different combinations of argument types, and applied by dispatching to the most specific matching definition. This model is a good fit for mathematical programming, where it is unnatural for the first argument to &quot;own&quot; an operation as in traditional object-oriented dispatch. Operators are just functions with special notation – to extend addition to new user-defined data types, you define new methods for the <code>+</code> function. Existing code then seamlessly applies to the new data types.</p><p>Partly because of run-time type inference (augmented by optional type annotations), and partly because of a strong focus on performance from the inception of the project, Julia&#39;s computational efficiency exceeds that of other dynamic languages, and even rivals that of statically-compiled languages. For large scale numerical problems, speed always has been, continues to be, and probably always will be crucial: the amount of data being processed has easily kept pace with Moore&#39;s Law over the past decades.</p><h3 id="man-advantages-of-julia"><a class="docs-heading-anchor" href="#man-advantages-of-julia">Advantages of Julia</a><a id="man-advantages-of-julia-1"></a><a class="docs-heading-anchor-permalink" href="#man-advantages-of-julia" title="Permalink"></a></h3><p>Julia aims to create an unprecedented combination of ease-of-use, power, and efficiency in a single language. In addition to the above, some advantages of Julia over comparable systems include:</p><ul><li>Free and open source (<a href="https://github.com/JuliaLang/julia/blob/master/LICENSE.md">MIT licensed</a>)</li><li>User-defined types are as fast and compact as built-ins</li><li>No need to vectorize code for performance; devectorized code is fast</li><li>Designed for parallelism and distributed computation</li><li>Lightweight &quot;green&quot; threading (<a href="https://en.wikipedia.org/wiki/Coroutine">coroutines</a>)</li><li>Unobtrusive yet powerful type system</li><li>Elegant and extensible conversions and promotions for numeric and other types</li><li>Efficient support for <a href="https://en.wikipedia.org/wiki/Unicode">Unicode</a>, including but not limited to <a href="https://en.wikipedia.org/wiki/UTF-8">UTF-8</a></li><li>Call C functions directly (no wrappers or special APIs needed)</li><li>Powerful shell-like capabilities for managing other processes</li><li>Lisp-like macros and other metaprogramming facilities</li></ul></article><nav class="docs-footer"><a class="docs-footer-nextpage" href="NEWS.html">Julia v1.11 Release Notes »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
