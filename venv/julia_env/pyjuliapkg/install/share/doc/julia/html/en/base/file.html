<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Filesystem · The Julia Language</title><meta name="title" content="Filesystem · The Julia Language"/><meta property="og:title" content="Filesystem · The Julia Language"/><meta property="twitter:title" content="Filesystem · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox" checked/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="base.html">Essentials</a></li><li><a class="tocitem" href="collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="math.html">Mathematics</a></li><li><a class="tocitem" href="numbers.html">Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="arrays.html">Arrays</a></li><li><a class="tocitem" href="parallel.html">Tasks</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="constants.html">Constants</a></li><li class="is-active"><a class="tocitem" href="file.html">Filesystem</a></li><li><a class="tocitem" href="io-network.html">I/O and Network</a></li><li><a class="tocitem" href="punctuation.html">Punctuation</a></li><li><a class="tocitem" href="sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="c.html">C Interface</a></li><li><a class="tocitem" href="libc.html">C Standard Library</a></li><li><a class="tocitem" href="stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Base</a></li><li class="is-active"><a href="file.html">Filesystem</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="file.html">Filesystem</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/base/file.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Filesystem"><a class="docs-heading-anchor" href="#Filesystem">Filesystem</a><a id="Filesystem-1"></a><a class="docs-heading-anchor-permalink" href="#Filesystem" title="Permalink"></a></h1><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.read-Tuple{String}" href="#Base.read-Tuple{String}"><code>Base.read</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">read(filename::AbstractString)</code></pre><p>Read the entire contents of a file as a <code>Vector{UInt8}</code>.</p><pre><code class="nohighlight hljs">read(filename::AbstractString, String)</code></pre><p>Read the entire contents of a file as a string.</p><pre><code class="nohighlight hljs">read(filename::AbstractString, args...)</code></pre><p>Open a file and read its contents. <code>args</code> is passed to <code>read</code>: this is equivalent to <code>open(io-&gt;read(io, args...), filename)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L491-L504">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.write-Tuple{String, Any}" href="#Base.write-Tuple{String, Any}"><code>Base.write</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">write(filename::AbstractString, content)</code></pre><p>Write the canonical binary representation of <code>content</code> to a file, which will be created if it does not exist yet or overwritten if it does exist.</p><p>Return the number of bytes written into the file.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L482-L488">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.pwd" href="#Base.Filesystem.pwd"><code>Base.Filesystem.pwd</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">pwd() -&gt; String</code></pre><p>Get the current working directory.</p><p>See also: <a href="file.html#Base.Filesystem.cd-Tuple{AbstractString}"><code>cd</code></a>, <a href="file.html#Base.Filesystem.tempdir"><code>tempdir</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; pwd()
&quot;/home/<USER>

julia&gt; cd(&quot;/home/<USER>/Projects/julia&quot;)

julia&gt; pwd()
&quot;/home/<USER>/Projects/julia&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L34-L51">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.cd-Tuple{AbstractString}" href="#Base.Filesystem.cd-Tuple{AbstractString}"><code>Base.Filesystem.cd</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cd(dir::AbstractString=homedir())</code></pre><p>Set the current working directory.</p><p>See also: <a href="file.html#Base.Filesystem.pwd"><code>pwd</code></a>, <a href="file.html#Base.Filesystem.mkdir"><code>mkdir</code></a>, <a href="file.html#Base.Filesystem.mkpath"><code>mkpath</code></a>, <a href="file.html#Base.Filesystem.mktempdir-Tuple{AbstractString}"><code>mktempdir</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; cd(&quot;/home/<USER>/Projects/julia&quot;)

julia&gt; pwd()
&quot;/home/<USER>/Projects/julia&quot;

julia&gt; cd()

julia&gt; pwd()
&quot;/home/<USER>/code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L69-L88">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.cd-Tuple{Function}" href="#Base.Filesystem.cd-Tuple{Function}"><code>Base.Filesystem.cd</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cd(f::Function, dir::AbstractString=homedir())</code></pre><p>Temporarily change the current working directory to <code>dir</code>, apply function <code>f</code> and finally return to the original directory.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; pwd()
&quot;/home/<USER>

julia&gt; cd(readdir, &quot;/home/<USER>/Projects/julia&quot;)
34-element Array{String,1}:
 &quot;.circleci&quot;
 &quot;.freebsdci.sh&quot;
 &quot;.git&quot;
 &quot;.gitattributes&quot;
 &quot;.github&quot;
 ⋮
 &quot;test&quot;
 &quot;ui&quot;
 &quot;usr&quot;
 &quot;usr-staging&quot;

julia&gt; pwd()
&quot;/home/<USER>/code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L119-L146">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.readdir" href="#Base.Filesystem.readdir"><code>Base.Filesystem.readdir</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">readdir(dir::AbstractString=pwd();
    join::Bool = false,
    sort::Bool = true,
) -&gt; Vector{String}</code></pre><p>Return the names in the directory <code>dir</code> or the current working directory if not given. When <code>join</code> is false, <code>readdir</code> returns just the names in the directory as is; when <code>join</code> is true, it returns <code>joinpath(dir, name)</code> for each <code>name</code> so that the returned strings are full paths. If you want to get absolute paths back, call <code>readdir</code> with an absolute directory path and <code>join</code> set to true.</p><p>By default, <code>readdir</code> sorts the list of names it returns. If you want to skip sorting the names and get them in the order that the file system lists them, you can use <code>readdir(dir, sort=false)</code> to opt out of sorting.</p><p>See also: <a href="file.html#Base.Filesystem.walkdir"><code>walkdir</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.4</header><div class="admonition-body"><p>The <code>join</code> and <code>sort</code> keyword arguments require at least Julia 1.4.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; cd(&quot;/home/<USER>/dev/julia&quot;)

julia&gt; readdir()
30-element Array{String,1}:
 &quot;.appveyor.yml&quot;
 &quot;.git&quot;
 &quot;.gitattributes&quot;
 ⋮
 &quot;ui&quot;
 &quot;usr&quot;
 &quot;usr-staging&quot;

julia&gt; readdir(join=true)
30-element Array{String,1}:
 &quot;/home/<USER>/dev/julia/.appveyor.yml&quot;
 &quot;/home/<USER>/dev/julia/.git&quot;
 &quot;/home/<USER>/dev/julia/.gitattributes&quot;
 ⋮
 &quot;/home/<USER>/dev/julia/ui&quot;
 &quot;/home/<USER>/dev/julia/usr&quot;
 &quot;/home/<USER>/dev/julia/usr-staging&quot;

julia&gt; readdir(&quot;base&quot;)
145-element Array{String,1}:
 &quot;.gitignore&quot;
 &quot;Base.jl&quot;
 &quot;Enums.jl&quot;
 ⋮
 &quot;version_git.sh&quot;
 &quot;views.jl&quot;
 &quot;weakkeydict.jl&quot;

julia&gt; readdir(&quot;base&quot;, join=true)
145-element Array{String,1}:
 &quot;base/.gitignore&quot;
 &quot;base/Base.jl&quot;
 &quot;base/Enums.jl&quot;
 ⋮
 &quot;base/version_git.sh&quot;
 &quot;base/views.jl&quot;
 &quot;base/weakkeydict.jl&quot;

julia&gt; readdir(abspath(&quot;base&quot;), join=true)
145-element Array{String,1}:
 &quot;/home/<USER>/dev/julia/base/.gitignore&quot;
 &quot;/home/<USER>/dev/julia/base/Base.jl&quot;
 &quot;/home/<USER>/dev/julia/base/Enums.jl&quot;
 ⋮
 &quot;/home/<USER>/dev/julia/base/version_git.sh&quot;
 &quot;/home/<USER>/dev/julia/base/views.jl&quot;
 &quot;/home/<USER>/dev/julia/base/weakkeydict.jl&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L840-L915">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.walkdir" href="#Base.Filesystem.walkdir"><code>Base.Filesystem.walkdir</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">walkdir(dir; topdown=true, follow_symlinks=false, onerror=throw)</code></pre><p>Return an iterator that walks the directory tree of a directory. The iterator returns a tuple containing <code>(rootpath, dirs, files)</code>. The directory tree can be traversed top-down or bottom-up. If <code>walkdir</code> or <code>stat</code> encounters a <code>IOError</code> it will rethrow the error by default. A custom error handling function can be provided through <code>onerror</code> keyword argument. <code>onerror</code> is called with a <code>IOError</code> as argument.</p><p>See also: <a href="file.html#Base.Filesystem.readdir"><code>readdir</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">for (root, dirs, files) in walkdir(&quot;.&quot;)
    println(&quot;Directories in $root&quot;)
    for dir in dirs
        println(joinpath(root, dir)) # path to directories
    end
    println(&quot;Files in $root&quot;)
    for file in files
        println(joinpath(root, file)) # path to files
    end
end</code></pre><pre><code class="language-julia-repl hljs">julia&gt; mkpath(&quot;my/test/dir&quot;);

julia&gt; itr = walkdir(&quot;my&quot;);

julia&gt; (root, dirs, files) = first(itr)
(&quot;my&quot;, [&quot;test&quot;], String[])

julia&gt; (root, dirs, files) = first(itr)
(&quot;my/test&quot;, [&quot;dir&quot;], String[])

julia&gt; (root, dirs, files) = first(itr)
(&quot;my/test/dir&quot;, String[], String[])</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L1022-L1062">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.mkdir" href="#Base.Filesystem.mkdir"><code>Base.Filesystem.mkdir</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mkdir(path::AbstractString; mode::Unsigned = 0o777)</code></pre><p>Make a new directory with name <code>path</code> and permissions <code>mode</code>. <code>mode</code> defaults to <code>0o777</code>, modified by the current file creation mask. This function never creates more than one directory. If the directory already exists, or some intermediate directories do not exist, this function throws an error. See <a href="file.html#Base.Filesystem.mkpath"><code>mkpath</code></a> for a function which creates all required intermediate directories. Return <code>path</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; mkdir(&quot;testingdir&quot;)
&quot;testingdir&quot;

julia&gt; cd(&quot;testingdir&quot;)

julia&gt; pwd()
&quot;/home/<USER>/testingdir&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L156-L176">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.mkpath" href="#Base.Filesystem.mkpath"><code>Base.Filesystem.mkpath</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mkpath(path::AbstractString; mode::Unsigned = 0o777)</code></pre><p>Create all intermediate directories in the <code>path</code> as required. Directories are created with the permissions <code>mode</code> which defaults to <code>0o777</code> and is modified by the current file creation mask. Unlike <a href="file.html#Base.Filesystem.mkdir"><code>mkdir</code></a>, <code>mkpath</code> does not error if <code>path</code> (or parts of it) already exists. However, an error will be thrown if <code>path</code> (or parts of it) points to an existing file. Return <code>path</code>.</p><p>If <code>path</code> includes a filename you will probably want to use <code>mkpath(dirname(path))</code> to avoid creating a directory using the filename.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; cd(mktempdir())

julia&gt; mkpath(&quot;my/test/dir&quot;) # creates three directories
&quot;my/test/dir&quot;

julia&gt; readdir()
1-element Array{String,1}:
 &quot;my&quot;

julia&gt; cd(&quot;my&quot;)

julia&gt; readdir()
1-element Array{String,1}:
 &quot;test&quot;

julia&gt; readdir(&quot;test&quot;)
1-element Array{String,1}:
 &quot;dir&quot;

julia&gt; mkpath(&quot;intermediate_dir/actually_a_directory.txt&quot;) # creates two directories
&quot;intermediate_dir/actually_a_directory.txt&quot;

julia&gt; isdir(&quot;intermediate_dir/actually_a_directory.txt&quot;)
true
</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L194-L234">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.hardlink" href="#Base.Filesystem.hardlink"><code>Base.Filesystem.hardlink</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hardlink(src::AbstractString, dst::AbstractString)</code></pre><p>Creates a hard link to an existing source file <code>src</code> with the name <code>dst</code>. The destination, <code>dst</code>, must not exist.</p><p>See also: <a href="file.html#Base.Filesystem.symlink"><code>symlink</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>This method was added in Julia 1.8.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L1148-L1158">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.symlink" href="#Base.Filesystem.symlink"><code>Base.Filesystem.symlink</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">symlink(target::AbstractString, link::AbstractString; dir_target = false)</code></pre><p>Creates a symbolic link to <code>target</code> with the name <code>link</code>.</p><p>On Windows, symlinks must be explicitly declared as referring to a directory or not.  If <code>target</code> already exists, by default the type of <code>link</code> will be auto- detected, however if <code>target</code> does not exist, this function defaults to creating a file symlink unless <code>dir_target</code> is set to <code>true</code>.  Note that if the user sets <code>dir_target</code> but <code>target</code> exists and is a file, a directory symlink will still be created, but dereferencing the symlink will fail, just as if the user creates a file symlink (by calling <code>symlink()</code> with <code>dir_target</code> set to <code>false</code> before the directory is created) and tries to dereference it to a directory.</p><p>Additionally, there are two methods of making a link on Windows; symbolic links and junction points.  Junction points are slightly more efficient, but do not support relative paths, so if a relative directory symlink is requested (as denoted by <code>isabspath(target)</code> returning <code>false</code>) a symlink will be used, else a junction point will be used.  Best practice for creating symlinks on Windows is to create them only after the files/directories they reference are already created.</p><p>See also: <a href="file.html#Base.Filesystem.hardlink"><code>hardlink</code></a>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This function raises an error under operating systems that do not support soft symbolic links, such as Windows XP.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>The <code>dir_target</code> keyword argument was added in Julia 1.6.  Prior to this, symlinks to nonexistent paths on windows would always be file symlinks, and relative symlinks to directories were not supported.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L1168-L1200">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.readlink" href="#Base.Filesystem.readlink"><code>Base.Filesystem.readlink</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">readlink(path::AbstractString) -&gt; String</code></pre><p>Return the target location a symbolic link <code>path</code> points to.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L1244-L1248">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.chmod" href="#Base.Filesystem.chmod"><code>Base.Filesystem.chmod</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">chmod(path::AbstractString, mode::Integer; recursive::Bool=false)</code></pre><p>Change the permissions mode of <code>path</code> to <code>mode</code>. Only integer <code>mode</code>s (e.g. <code>0o777</code>) are currently supported. If <code>recursive=true</code> and the path is a directory all permissions in that directory will be recursively changed. Return <code>path</code>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Prior to Julia 1.6, this did not correctly manipulate filesystem ACLs  on Windows, therefore it would only set read-only bits on files.  It  now is able to manipulate ACLs.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L1268-L1280">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.chown" href="#Base.Filesystem.chown"><code>Base.Filesystem.chown</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">chown(path::AbstractString, owner::Integer, group::Integer=-1)</code></pre><p>Change the owner and/or group of <code>path</code> to <code>owner</code> and/or <code>group</code>. If the value entered for <code>owner</code> or <code>group</code> is <code>-1</code> the corresponding ID will not change. Only integer <code>owner</code>s and <code>group</code>s are currently supported. Return <code>path</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L1294-L1300">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Libc.RawFD" href="#Base.Libc.RawFD"><code>Base.Libc.RawFD</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">RawFD</code></pre><p>Primitive type which wraps the native OS file descriptor. <code>RawFD</code>s can be passed to methods like <a href="file.html#Base.stat"><code>stat</code></a> to discover information about the underlying file, and can also be used to open streams, with the <code>RawFD</code> describing the OS file backing the stream.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/libc.jl#L25-L33">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.stat" href="#Base.stat"><code>Base.stat</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">stat(file)</code></pre><p>Return a structure whose fields contain information about the file. The fields of the structure are:</p><table><tr><th style="text-align: left">Name</th><th style="text-align: left">Type</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left">desc</td><td style="text-align: left"><code>Union{String, Base.OS_HANDLE}</code></td><td style="text-align: left">The path or OS file descriptor</td></tr><tr><td style="text-align: left">size</td><td style="text-align: left"><code>Int64</code></td><td style="text-align: left">The size (in bytes) of the file</td></tr><tr><td style="text-align: left">device</td><td style="text-align: left"><code>UInt</code></td><td style="text-align: left">ID of the device that contains the file</td></tr><tr><td style="text-align: left">inode</td><td style="text-align: left"><code>UInt</code></td><td style="text-align: left">The inode number of the file</td></tr><tr><td style="text-align: left">mode</td><td style="text-align: left"><code>UInt</code></td><td style="text-align: left">The protection mode of the file</td></tr><tr><td style="text-align: left">nlink</td><td style="text-align: left"><code>Int</code></td><td style="text-align: left">The number of hard links to the file</td></tr><tr><td style="text-align: left">uid</td><td style="text-align: left"><code>UInt</code></td><td style="text-align: left">The user id of the owner of the file</td></tr><tr><td style="text-align: left">gid</td><td style="text-align: left"><code>UInt</code></td><td style="text-align: left">The group id of the file owner</td></tr><tr><td style="text-align: left">rdev</td><td style="text-align: left"><code>UInt</code></td><td style="text-align: left">If this file refers to a device, the ID of the device it refers to</td></tr><tr><td style="text-align: left">blksize</td><td style="text-align: left"><code>Int64</code></td><td style="text-align: left">The file-system preferred block size for the file</td></tr><tr><td style="text-align: left">blocks</td><td style="text-align: left"><code>Int64</code></td><td style="text-align: left">The number of 512-byte blocks allocated</td></tr><tr><td style="text-align: left">mtime</td><td style="text-align: left"><code>Float64</code></td><td style="text-align: left">Unix timestamp of when the file was last modified</td></tr><tr><td style="text-align: left">ctime</td><td style="text-align: left"><code>Float64</code></td><td style="text-align: left">Unix timestamp of when the file&#39;s metadata was changed</td></tr></table></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L194-L215">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.diskstat" href="#Base.Filesystem.diskstat"><code>Base.Filesystem.diskstat</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">diskstat(path=pwd())</code></pre><p>Returns statistics in bytes about the disk that contains the file or directory pointed at by <code>path</code>. If no argument is passed, statistics about the disk that contains the current working directory are returned.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>This method was added in Julia 1.8.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L1341-L1350">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.lstat" href="#Base.Filesystem.lstat"><code>Base.Filesystem.lstat</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lstat(file)</code></pre><p>Like <a href="file.html#Base.stat"><code>stat</code></a>, but for symbolic links gets the info for the link itself rather than the file it refers to. This function must be called on a file path rather than a file object or a file descriptor.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L218-L225">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.ctime" href="#Base.Filesystem.ctime"><code>Base.Filesystem.ctime</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ctime(file)</code></pre><p>Equivalent to <code>stat(file).ctime</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L312-L316">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.mtime" href="#Base.Filesystem.mtime"><code>Base.Filesystem.mtime</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mtime(file)</code></pre><p>Equivalent to <code>stat(file).mtime</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L305-L309">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.filemode" href="#Base.Filesystem.filemode"><code>Base.Filesystem.filemode</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">filemode(file)</code></pre><p>Equivalent to <code>stat(file).mode</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L275-L279">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.filesize" href="#Base.filesize"><code>Base.filesize</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">filesize(path...)</code></pre><p>Equivalent to <code>stat(file).size</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L298-L302">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.uperm" href="#Base.Filesystem.uperm"><code>Base.Filesystem.uperm</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">uperm(file)</code></pre><p>Get the permissions of the owner of the file as a bitfield of</p><table><tr><th style="text-align: left">Value</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left">01</td><td style="text-align: left">Execute Permission</td></tr><tr><td style="text-align: left">02</td><td style="text-align: left">Write Permission</td></tr><tr><td style="text-align: left">04</td><td style="text-align: left">Read Permission</td></tr></table><p>For allowed arguments, see <a href="file.html#Base.stat"><code>stat</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L443-L455">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.gperm" href="#Base.Filesystem.gperm"><code>Base.Filesystem.gperm</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">gperm(file)</code></pre><p>Like <a href="file.html#Base.Filesystem.uperm"><code>uperm</code></a> but gets the permissions of the group owning the file.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L458-L462">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.operm" href="#Base.Filesystem.operm"><code>Base.Filesystem.operm</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">operm(file)</code></pre><p>Like <a href="file.html#Base.Filesystem.uperm"><code>uperm</code></a> but gets the permissions for people who neither own the file nor are a member of the group owning the file</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L465-L470">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.cp" href="#Base.Filesystem.cp"><code>Base.Filesystem.cp</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cp(src::AbstractString, dst::AbstractString; force::Bool=false, follow_symlinks::Bool=false)</code></pre><p>Copy the file, link, or directory from <code>src</code> to <code>dst</code>. <code>force=true</code> will first remove an existing <code>dst</code>.</p><p>If <code>follow_symlinks=false</code>, and <code>src</code> is a symbolic link, <code>dst</code> will be created as a symbolic link. If <code>follow_symlinks=true</code> and <code>src</code> is a symbolic link, <code>dst</code> will be a copy of the file or directory <code>src</code> refers to. Return <code>dst</code>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The <code>cp</code> function is different from the <code>cp</code> command. The <code>cp</code> function always operates on the assumption that <code>dst</code> is a file, while the command does different things depending on whether <code>dst</code> is a directory or a file. Using <code>force=true</code> when <code>dst</code> is a directory will result in loss of all the contents present in the <code>dst</code> directory, and <code>dst</code> will become a file that has the contents of <code>src</code> instead.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L360-L377">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.download" href="#Base.download"><code>Base.download</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">download(url::AbstractString, [path::AbstractString = tempname()]) -&gt; path</code></pre><p>Download a file from the given url, saving it to the location <code>path</code>, or if not specified, a temporary path. Returns the path of the downloaded file.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Since Julia 1.6, this function is deprecated and is just a thin wrapper around <code>Downloads.download</code>. In new code, you should use that function directly instead of calling this.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/download.jl#L8-L18">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.mv" href="#Base.Filesystem.mv"><code>Base.Filesystem.mv</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mv(src::AbstractString, dst::AbstractString; force::Bool=false)</code></pre><p>Move the file, link, or directory from <code>src</code> to <code>dst</code>. <code>force=true</code> will first remove an existing <code>dst</code>. Return <code>dst</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; write(&quot;hello.txt&quot;, &quot;world&quot;);

julia&gt; mv(&quot;hello.txt&quot;, &quot;goodbye.txt&quot;)
&quot;goodbye.txt&quot;

julia&gt; &quot;hello.txt&quot; in readdir()
false

julia&gt; readline(&quot;goodbye.txt&quot;)
&quot;world&quot;

julia&gt; write(&quot;hello.txt&quot;, &quot;world2&quot;);

julia&gt; mv(&quot;hello.txt&quot;, &quot;goodbye.txt&quot;)
ERROR: ArgumentError: &#39;goodbye.txt&#39; exists. `force=true` is required to remove &#39;goodbye.txt&#39; before moving.
Stacktrace:
 [1] #checkfor_mv_cp_cptree#10(::Bool, ::Function, ::String, ::String, ::String) at ./file.jl:293
[...]

julia&gt; mv(&quot;hello.txt&quot;, &quot;goodbye.txt&quot;, force=true)
&quot;goodbye.txt&quot;

julia&gt; rm(&quot;goodbye.txt&quot;);
</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L391-L425">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.rm" href="#Base.Filesystem.rm"><code>Base.Filesystem.rm</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rm(path::AbstractString; force::Bool=false, recursive::Bool=false)</code></pre><p>Delete the file, link, or empty directory at the given path. If <code>force=true</code> is passed, a non-existing path is not treated as error. If <code>recursive=true</code> is passed and the path is a directory, then all contents are removed recursively.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; mkpath(&quot;my/test/dir&quot;);

julia&gt; rm(&quot;my&quot;, recursive=true)

julia&gt; rm(&quot;this_file_does_not_exist&quot;, force=true)

julia&gt; rm(&quot;this_file_does_not_exist&quot;)
ERROR: IOError: unlink(&quot;this_file_does_not_exist&quot;): no such file or directory (ENOENT)
Stacktrace:
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L252-L272">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.touch" href="#Base.Filesystem.touch"><code>Base.Filesystem.touch</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.touch(::Pidfile.LockMonitor)</code></pre><p>Update the <code>mtime</code> on the lock, to indicate it is still fresh.</p><p>See also the <code>refresh</code> keyword in the <a href="../stdlib/FileWatching.html#FileWatching.Pidfile.mkpidlock"><code>mkpidlock</code></a> constructor.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/FileWatching/src/pidfile.jl#L126-L132">source</a></section><section><div><pre><code class="language-julia hljs">touch(path::AbstractString)
touch(fd::File)</code></pre><p>Update the last-modified timestamp on a file to the current time.</p><p>If the file does not exist a new file is created.</p><p>Return <code>path</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; write(&quot;my_little_file&quot;, 2);

julia&gt; mtime(&quot;my_little_file&quot;)
1.5273815391135583e9

julia&gt; touch(&quot;my_little_file&quot;);

julia&gt; mtime(&quot;my_little_file&quot;)
1.527381559163435e9</code></pre><p>We can see the <a href="file.html#Base.Filesystem.mtime"><code>mtime</code></a> has been modified by <code>touch</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L432-L456">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.tempname" href="#Base.Filesystem.tempname"><code>Base.Filesystem.tempname</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">tempname(parent=tempdir(); cleanup=true) -&gt; String</code></pre><p>Generate a temporary file path. This function only returns a path; no file is created. The path is likely to be unique, but this cannot be guaranteed due to the very remote possibility of two simultaneous calls to <code>tempname</code> generating the same file name. The name is guaranteed to differ from all files already existing at the time of the call to <code>tempname</code>.</p><p>When called with no arguments, the temporary name will be an absolute path to a temporary name in the system temporary directory as given by <code>tempdir()</code>. If a <code>parent</code> directory argument is given, the temporary path will be in that directory instead.</p><p>The <code>cleanup</code> option controls whether the process attempts to delete the returned path automatically when the process exits. Note that the <code>tempname</code> function does not create any file or directory at the returned location, so there is nothing to cleanup unless you create a file or directory there. If you do and <code>cleanup</code> is <code>true</code> it will be deleted upon process termination.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.4</header><div class="admonition-body"><p>The <code>parent</code> and <code>cleanup</code> arguments were added in 1.4. Prior to Julia 1.4 the path <code>tempname</code> would never be cleaned up at process termination.</p></div></div><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>This can lead to security holes if another process obtains the same file name and creates the file before you are able to. Open the file with <code>JL_O_EXCL</code> if this is a concern. Using <a href="file.html#Base.Filesystem.mktemp-Tuple{AbstractString}"><code>mktemp()</code></a> is also recommended instead.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L684-L714">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.tempdir" href="#Base.Filesystem.tempdir"><code>Base.Filesystem.tempdir</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">tempdir()</code></pre><p>Gets the path of the temporary directory. On Windows, <code>tempdir()</code> uses the first environment variable found in the ordered list <code>TMP</code>, <code>TEMP</code>, <code>USERPROFILE</code>. On all other operating systems, <code>tempdir()</code> uses the first environment variable found in the ordered list <code>TMPDIR</code>, <code>TMP</code>, <code>TEMP</code>, and <code>TEMPDIR</code>. If none of these are found, the path <code>&quot;/tmp&quot;</code> is used.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L468-L475">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.mktemp-Tuple{AbstractString}" href="#Base.Filesystem.mktemp-Tuple{AbstractString}"><code>Base.Filesystem.mktemp</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mktemp(parent=tempdir(); cleanup=true) -&gt; (path, io)</code></pre><p>Return <code>(path, io)</code>, where <code>path</code> is the path of a new temporary file in <code>parent</code> and <code>io</code> is an open file object for this path. The <code>cleanup</code> option controls whether the temporary file is automatically deleted when the process exits.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p>The <code>cleanup</code> keyword argument was added in Julia 1.3. Relatedly, starting from 1.3, Julia will remove the temporary paths created by <code>mktemp</code> when the Julia process exits, unless <code>cleanup</code> is explicitly set to <code>false</code>.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L717-L728">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.mktemp-Tuple{Function, AbstractString}" href="#Base.Filesystem.mktemp-Tuple{Function, AbstractString}"><code>Base.Filesystem.mktemp</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mktemp(f::Function, parent=tempdir())</code></pre><p>Apply the function <code>f</code> to the result of <a href="file.html#Base.Filesystem.mktemp-Tuple{AbstractString}"><code>mktemp(parent)</code></a> and remove the temporary file upon completion.</p><p>See also: <a href="file.html#Base.Filesystem.mktempdir-Tuple{AbstractString}"><code>mktempdir</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L779-L786">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.mktempdir-Tuple{AbstractString}" href="#Base.Filesystem.mktempdir-Tuple{AbstractString}"><code>Base.Filesystem.mktempdir</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mktempdir(parent=tempdir(); prefix=&quot;jl_&quot;, cleanup=true) -&gt; path</code></pre><p>Create a temporary directory in the <code>parent</code> directory with a name constructed from the given <code>prefix</code> and a random suffix, and return its path. Additionally, on some platforms, any trailing <code>&#39;X&#39;</code> characters in <code>prefix</code> may be replaced with random characters. If <code>parent</code> does not exist, throw an error. The <code>cleanup</code> option controls whether the temporary directory is automatically deleted when the process exits.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p>The <code>prefix</code> keyword argument was added in Julia 1.2.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p>The <code>cleanup</code> keyword argument was added in Julia 1.3. Relatedly, starting from 1.3, Julia will remove the temporary paths created by <code>mktempdir</code> when the Julia process exits, unless <code>cleanup</code> is explicitly set to <code>false</code>.</p></div></div><p>See also: <a href="file.html#Base.Filesystem.mktemp-Tuple{AbstractString}"><code>mktemp</code></a>, <a href="file.html#Base.Filesystem.mkdir"><code>mkdir</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L731-L750">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.mktempdir-Tuple{Function, AbstractString}" href="#Base.Filesystem.mktempdir-Tuple{Function, AbstractString}"><code>Base.Filesystem.mktempdir</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mktempdir(f::Function, parent=tempdir(); prefix=&quot;jl_&quot;)</code></pre><p>Apply the function <code>f</code> to the result of <a href="file.html#Base.Filesystem.mktempdir-Tuple{AbstractString}"><code>mktempdir(parent; prefix)</code></a> and remove the temporary directory all of its contents upon completion.</p><p>See also: <a href="file.html#Base.Filesystem.mktemp-Tuple{AbstractString}"><code>mktemp</code></a>, <a href="file.html#Base.Filesystem.mkdir"><code>mkdir</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p>The <code>prefix</code> keyword argument was added in Julia 1.2.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/file.jl#L804-L814">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.isblockdev" href="#Base.Filesystem.isblockdev"><code>Base.Filesystem.isblockdev</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isblockdev(path) -&gt; Bool</code></pre><p>Return <code>true</code> if <code>path</code> is a block device, <code>false</code> otherwise.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L372-L376">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.ischardev" href="#Base.Filesystem.ischardev"><code>Base.Filesystem.ischardev</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ischardev(path) -&gt; Bool</code></pre><p>Return <code>true</code> if <code>path</code> is a character device, <code>false</code> otherwise.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L347-L351">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.isdir" href="#Base.Filesystem.isdir"><code>Base.Filesystem.isdir</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isdir(path) -&gt; Bool</code></pre><p>Return <code>true</code> if <code>path</code> is a directory, <code>false</code> otherwise.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isdir(homedir())
true

julia&gt; isdir(&quot;not/a/directory&quot;)
false</code></pre><p>See also <a href="file.html#Base.Filesystem.isfile"><code>isfile</code></a> and <a href="file.html#Base.Filesystem.ispath"><code>ispath</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L354-L369">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.isfifo" href="#Base.Filesystem.isfifo"><code>Base.Filesystem.isfifo</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isfifo(path) -&gt; Bool</code></pre><p>Return <code>true</code> if <code>path</code> is a FIFO, <code>false</code> otherwise.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L340-L344">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.isfile" href="#Base.Filesystem.isfile"><code>Base.Filesystem.isfile</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isfile(path) -&gt; Bool</code></pre><p>Return <code>true</code> if <code>path</code> is a regular file, <code>false</code> otherwise.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isfile(homedir())
false

julia&gt; filename = &quot;test_file.txt&quot;;

julia&gt; write(filename, &quot;Hello world!&quot;);

julia&gt; isfile(filename)
true

julia&gt; rm(filename);

julia&gt; isfile(filename)
false</code></pre><p>See also <a href="file.html#Base.Filesystem.isdir"><code>isdir</code></a> and <a href="file.html#Base.Filesystem.ispath"><code>ispath</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L379-L403">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.islink" href="#Base.Filesystem.islink"><code>Base.Filesystem.islink</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">islink(path) -&gt; Bool</code></pre><p>Return <code>true</code> if <code>path</code> is a symbolic link, <code>false</code> otherwise.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L406-L410">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.ismount" href="#Base.Filesystem.ismount"><code>Base.Filesystem.ismount</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ismount(path) -&gt; Bool</code></pre><p>Return <code>true</code> if <code>path</code> is a mount point, <code>false</code> otherwise.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L511-L515">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.ispath" href="#Base.Filesystem.ispath"><code>Base.Filesystem.ispath</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ispath(path) -&gt; Bool</code></pre><p>Return <code>true</code> if a valid filesystem entity exists at <code>path</code>, otherwise returns <code>false</code>. This is the generalization of <a href="file.html#Base.Filesystem.isfile"><code>isfile</code></a>, <a href="file.html#Base.Filesystem.isdir"><code>isdir</code></a> etc.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L321-L327">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.issetgid" href="#Base.Filesystem.issetgid"><code>Base.Filesystem.issetgid</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">issetgid(path) -&gt; Bool</code></pre><p>Return <code>true</code> if <code>path</code> has the setgid flag set, <code>false</code> otherwise.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L429-L433">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.issetuid" href="#Base.Filesystem.issetuid"><code>Base.Filesystem.issetuid</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">issetuid(path) -&gt; Bool</code></pre><p>Return <code>true</code> if <code>path</code> has the setuid flag set, <code>false</code> otherwise.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L422-L426">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.issocket" href="#Base.Filesystem.issocket"><code>Base.Filesystem.issocket</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">issocket(path) -&gt; Bool</code></pre><p>Return <code>true</code> if <code>path</code> is a socket, <code>false</code> otherwise.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L413-L417">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.issticky" href="#Base.Filesystem.issticky"><code>Base.Filesystem.issticky</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">issticky(path) -&gt; Bool</code></pre><p>Return <code>true</code> if <code>path</code> has the sticky bit set, <code>false</code> otherwise.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L436-L440">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.homedir" href="#Base.Filesystem.homedir"><code>Base.Filesystem.homedir</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">homedir() -&gt; String</code></pre><p>Return the current user&#39;s home directory.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p><code>homedir</code> determines the home directory via <code>libuv</code>&#39;s <code>uv_os_homedir</code>. For details (for example on how to specify the home directory via environment variables), see the <a href="http://docs.libuv.org/en/v1.x/misc.html#c.uv_os_homedir"><code>uv_os_homedir</code> documentation</a>.</p></div></div><p>See also <a href="base.html#Base.Sys.username"><code>Sys.username</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/path.jl#L71-L82">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.dirname" href="#Base.Filesystem.dirname"><code>Base.Filesystem.dirname</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">dirname(path::AbstractString) -&gt; String</code></pre><p>Get the directory part of a path. Trailing characters (&#39;/&#39; or &#39;\&#39;) in the path are counted as part of the path.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; dirname(&quot;/home/<USER>
&quot;/home&quot;

julia&gt; dirname(&quot;/home/<USER>/&quot;)
&quot;/home/<USER>/code></pre><p>See also <a href="file.html#Base.Filesystem.basename"><code>basename</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/path.jl#L166-L182">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.basename" href="#Base.Filesystem.basename"><code>Base.Filesystem.basename</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">basename(path::AbstractString) -&gt; String</code></pre><p>Get the file name part of a path.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This function differs slightly from the Unix <code>basename</code> program, where trailing slashes are ignored, i.e. <code>$ basename /foo/bar/</code> returns <code>bar</code>, whereas <code>basename</code> in Julia returns an empty string <code>&quot;&quot;</code>.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; basename(&quot;/home/<USER>/example.jl&quot;)
&quot;example.jl&quot;

julia&gt; basename(&quot;/home/<USER>/&quot;)
&quot;&quot;</code></pre><p>See also <a href="file.html#Base.Filesystem.dirname"><code>dirname</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/path.jl#L185-L204">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.isabspath" href="#Base.Filesystem.isabspath"><code>Base.Filesystem.isabspath</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isabspath(path::AbstractString) -&gt; Bool</code></pre><p>Determine whether a path is absolute (begins at the root directory).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isabspath(&quot;/home&quot;)
true

julia&gt; isabspath(&quot;home&quot;)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/path.jl#L106-L119">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.isdirpath" href="#Base.Filesystem.isdirpath"><code>Base.Filesystem.isdirpath</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isdirpath(path::AbstractString) -&gt; Bool</code></pre><p>Determine whether a path refers to a directory (for example, ends with a path separator).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isdirpath(&quot;/home&quot;)
false

julia&gt; isdirpath(&quot;/home/<USER>
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/path.jl#L122-L135">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.joinpath" href="#Base.Filesystem.joinpath"><code>Base.Filesystem.joinpath</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">joinpath(parts::AbstractString...) -&gt; String
joinpath(parts::Vector{AbstractString}) -&gt; String
joinpath(parts::Tuple{AbstractString}) -&gt; String</code></pre><p>Join path components into a full path. If some argument is an absolute path or (on Windows) has a drive specification that doesn&#39;t match the drive computed for the join of the preceding paths, then prior components are dropped.</p><p>Note on Windows since there is a current directory for each drive, <code>joinpath(&quot;c:&quot;, &quot;foo&quot;)</code> represents a path relative to the current directory on drive &quot;c:&quot; so this is equal to &quot;c:foo&quot;, not &quot;c:\foo&quot;. Furthermore, <code>joinpath</code> treats this as a non-absolute path and ignores the drive letter casing, hence <code>joinpath(&quot;C:\A&quot;,&quot;c:b&quot;) = &quot;C:\A\b&quot;</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; joinpath(&quot;/home/<USER>
&quot;/home/<USER>/example.jl&quot;</code></pre><pre><code class="language-julia-repl hljs">julia&gt; joinpath([&quot;/home/<USER>
&quot;/home/<USER>/example.jl&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/path.jl#L348-L372">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.abspath" href="#Base.Filesystem.abspath"><code>Base.Filesystem.abspath</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">abspath(path::AbstractString) -&gt; String</code></pre><p>Convert a path to an absolute path by adding the current directory if necessary. Also normalizes the path as in <a href="file.html#Base.Filesystem.normpath"><code>normpath</code></a>.</p><p><strong>Examples</strong></p><p>If you are in a directory called <code>JuliaExample</code> and the data you are using is two levels up relative to the <code>JuliaExample</code> directory, you could write:</p><pre><code class="nohighlight hljs">abspath(&quot;../../data&quot;)</code></pre><p>Which gives a path like <code>&quot;/home/<USER>/data/&quot;</code>.</p><p>See also <a href="file.html#Base.Filesystem.joinpath"><code>joinpath</code></a>, <a href="file.html#Base.Filesystem.pwd"><code>pwd</code></a>, <a href="file.html#Base.Filesystem.expanduser"><code>expanduser</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/path.jl#L432-L447">source</a></section><section><div><pre><code class="language-julia hljs">abspath(path::AbstractString, paths::AbstractString...) -&gt; String</code></pre><p>Convert a set of paths to an absolute path by joining them together and adding the current directory if necessary. Equivalent to <code>abspath(joinpath(path, paths...))</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/path.jl#L462-L467">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.normpath" href="#Base.Filesystem.normpath"><code>Base.Filesystem.normpath</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">normpath(path::AbstractString) -&gt; String</code></pre><p>Normalize a path, removing &quot;.&quot; and &quot;..&quot; entries and changing &quot;/&quot; to the canonical path separator for the system.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; normpath(&quot;/home/<USER>/../example.jl&quot;)
&quot;/home/<USER>

julia&gt; normpath(&quot;Documents/Julia&quot;) == joinpath(&quot;Documents&quot;, &quot;Julia&quot;)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/path.jl#L375-L389">source</a></section><section><div><pre><code class="language-julia hljs">normpath(path::AbstractString, paths::AbstractString...) -&gt; String</code></pre><p>Convert a set of paths to a normalized path by joining them together and removing &quot;.&quot; and &quot;..&quot; entries. Equivalent to <code>normpath(joinpath(path, paths...))</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/path.jl#L424-L429">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.realpath" href="#Base.Filesystem.realpath"><code>Base.Filesystem.realpath</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">realpath(path::AbstractString) -&gt; String</code></pre><p>Canonicalize a path by expanding symbolic links and removing &quot;.&quot; and &quot;..&quot; entries. On case-insensitive case-preserving filesystems (typically Mac and Windows), the filesystem&#39;s stored case for the path is returned.</p><p>(This function throws an exception if <code>path</code> does not exist in the filesystem.)</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/path.jl#L489-L497">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.relpath" href="#Base.Filesystem.relpath"><code>Base.Filesystem.relpath</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">relpath(path::AbstractString, startpath::AbstractString = &quot;.&quot;) -&gt; String</code></pre><p>Return a relative filepath to <code>path</code> either from the current directory or from an optional start directory. This is a path computation: the filesystem is not accessed to confirm the existence or nature of <code>path</code> or <code>startpath</code>.</p><p>On Windows, case sensitivity is applied to every part of the path except drive letters. If <code>path</code> and <code>startpath</code> refer to different drives, the absolute path of <code>path</code> is returned.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/path.jl#L563-L572">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.expanduser" href="#Base.Filesystem.expanduser"><code>Base.Filesystem.expanduser</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">expanduser(path::AbstractString) -&gt; AbstractString</code></pre><p>On Unix systems, replace a tilde character at the start of a path with the current user&#39;s home directory.</p><p>See also: <a href="file.html#Base.Filesystem.contractuser"><code>contractuser</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/path.jl#L544-L550">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.contractuser" href="#Base.Filesystem.contractuser"><code>Base.Filesystem.contractuser</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">contractuser(path::AbstractString) -&gt; AbstractString</code></pre><p>On Unix systems, if the path starts with <code>homedir()</code>, replace it with a tilde character.</p><p>See also: <a href="file.html#Base.Filesystem.expanduser"><code>expanduser</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/path.jl#L553-L559">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.samefile" href="#Base.Filesystem.samefile"><code>Base.Filesystem.samefile</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">samefile(path_a::AbstractString, path_b::AbstractString)</code></pre><p>Check if the paths <code>path_a</code> and <code>path_b</code> refer to the same existing file or directory.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stat.jl#L504-L508">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.splitdir" href="#Base.Filesystem.splitdir"><code>Base.Filesystem.splitdir</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">splitdir(path::AbstractString) -&gt; (AbstractString, AbstractString)</code></pre><p>Split a path into a tuple of the directory name and file name.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; splitdir(&quot;/home/<USER>
(&quot;/home&quot;, &quot;myuser&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/path.jl#L138-L148">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.splitdrive" href="#Base.Filesystem.splitdrive"><code>Base.Filesystem.splitdrive</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">splitdrive(path::AbstractString) -&gt; (AbstractString, AbstractString)</code></pre><p>On Windows, split a path into the drive letter part and the path part. On Unix systems, the first component is always the empty string.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/path.jl#L63-L68">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.splitext" href="#Base.Filesystem.splitext"><code>Base.Filesystem.splitext</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">splitext(path::AbstractString) -&gt; (String, String)</code></pre><p>If the last component of a path contains one or more dots, split the path into everything before the last dot and everything including and after the dot. Otherwise, return a tuple of the argument unmodified and the empty string. &quot;splitext&quot; is short for &quot;split extension&quot;.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; splitext(&quot;/home/<USER>/example.jl&quot;)
(&quot;/home/<USER>/example&quot;, &quot;.jl&quot;)

julia&gt; splitext(&quot;/home/<USER>/example.tar.gz&quot;)
(&quot;/home/<USER>/example.tar&quot;, &quot;.gz&quot;)

julia&gt; splitext(&quot;/home/<USER>/example&quot;)
(&quot;/home/<USER>/example&quot;, &quot;&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/path.jl#L207-L225">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Filesystem.splitpath" href="#Base.Filesystem.splitpath"><code>Base.Filesystem.splitpath</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">splitpath(path::AbstractString) -&gt; Vector{String}</code></pre><p>Split a file path into all its path components. This is the opposite of <code>joinpath</code>. Returns an array of substrings, one for each directory or file in the path, including the root directory if present.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p>This function requires at least Julia 1.1.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; splitpath(&quot;/home/<USER>/example.jl&quot;)
4-element Vector{String}:
 &quot;/&quot;
 &quot;home&quot;
 &quot;myuser&quot;
 &quot;example.jl&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/path.jl#L236-L255">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="constants.html">« Constants</a><a class="docs-footer-nextpage" href="io-network.html">I/O and Network »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
