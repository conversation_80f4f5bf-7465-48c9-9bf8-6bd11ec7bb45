<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Unit Testing · The Julia Language</title><meta name="title" content="Unit Testing · The Julia Language"/><meta property="og:title" content="Unit Testing · The Julia Language"/><meta property="twitter:title" content="Unit Testing · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li class="is-active"><a class="tocitem" href="Test.html">Unit Testing</a><ul class="internal"><li><a class="tocitem" href="#Testing-Base-Julia"><span>Testing Base Julia</span></a></li><li><a class="tocitem" href="#Basic-Unit-Tests"><span>Basic Unit Tests</span></a></li><li><a class="tocitem" href="#Working-with-Test-Sets"><span>Working with Test Sets</span></a></li><li><a class="tocitem" href="#Testing-Log-Statements"><span>Testing Log Statements</span></a></li><li><a class="tocitem" href="#Other-Test-Macros"><span>Other Test Macros</span></a></li><li><a class="tocitem" href="#Broken-Tests"><span>Broken Tests</span></a></li><li><a class="tocitem" href="#Test-result-types"><span>Test result types</span></a></li><li><a class="tocitem" href="#Creating-Custom-AbstractTestSet-Types"><span>Creating Custom <code>AbstractTestSet</code> Types</span></a></li><li><a class="tocitem" href="#Test-utilities"><span>Test utilities</span></a></li><li><a class="tocitem" href="#Workflow-for-Testing-Packages"><span>Workflow for Testing Packages</span></a></li></ul></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="Test.html">Unit Testing</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="Test.html">Unit Testing</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/stdlib/Test/docs/src/index.md" title="View source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Unit-Testing"><a class="docs-heading-anchor" href="#Unit-Testing">Unit Testing</a><a id="Unit-Testing-1"></a><a class="docs-heading-anchor-permalink" href="#Unit-Testing" title="Permalink"></a></h1><h2 id="Testing-Base-Julia"><a class="docs-heading-anchor" href="#Testing-Base-Julia">Testing Base Julia</a><a id="Testing-Base-Julia-1"></a><a class="docs-heading-anchor-permalink" href="#Testing-Base-Julia" title="Permalink"></a></h2><p>Julia is under rapid development and has an extensive test suite to verify functionality across multiple platforms. If you build Julia from source, you can run this test suite with <code>make test</code>. In a binary install, you can run the test suite using <code>Base.runtests()</code>.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.runtests" href="#Base.runtests"><code>Base.runtests</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.runtests(tests=[&quot;all&quot;]; ncores=ceil(Int, Sys.CPU_THREADS / 2),
              exit_on_error=false, revise=false, [seed])</code></pre><p>Run the Julia unit tests listed in <code>tests</code>, which can be either a string or an array of strings, using <code>ncores</code> processors. If <code>exit_on_error</code> is <code>false</code>, when one test fails, all remaining tests in other files will still be run; they are otherwise discarded, when <code>exit_on_error == true</code>. If <code>revise</code> is <code>true</code>, the <code>Revise</code> package is used to load any modifications to <code>Base</code> or to the standard libraries before running the tests. If a seed is provided via the keyword argument, it is used to seed the global RNG in the context where the tests are run; otherwise the seed is chosen randomly.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/util.jl#L661-L673">source</a></section></article><h2 id="Basic-Unit-Tests"><a class="docs-heading-anchor" href="#Basic-Unit-Tests">Basic Unit Tests</a><a id="Basic-Unit-Tests-1"></a><a class="docs-heading-anchor-permalink" href="#Basic-Unit-Tests" title="Permalink"></a></h2><p>The <code>Test</code> module provides simple <em>unit testing</em> functionality. Unit testing is a way to see if your code is correct by checking that the results are what you expect. It can be helpful to ensure your code still works after you make changes, and can be used when developing as a way of specifying the behaviors your code should have when complete. You may also want to look at the documentation for <a href="https://pkgdocs.julialang.org/dev/creating-packages/#Adding-tests-to-the-package">adding tests to your Julia Package</a>.</p><p>Simple unit testing can be performed with the <code>@test</code> and <code>@test_throws</code> macros:</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.@test" href="#Test.@test"><code>Test.@test</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@test ex
@test f(args...) key=val ...
@test ex broken=true
@test ex skip=true</code></pre><p>Test that the expression <code>ex</code> evaluates to <code>true</code>. If executed inside a <code>@testset</code>, return a <code>Pass</code> <code>Result</code> if it does, a <code>Fail</code> <code>Result</code> if it is <code>false</code>, and an <code>Error</code> <code>Result</code> if it could not be evaluated. If executed outside a <code>@testset</code>, throw an exception instead of returning <code>Fail</code> or <code>Error</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; @test true
Test Passed

julia&gt; @test [1, 2] + [2, 1] == [3, 3]
Test Passed</code></pre><p>The <code>@test f(args...) key=val...</code> form is equivalent to writing <code>@test f(args..., key=val...)</code> which can be useful when the expression is a call using infix syntax such as approximate comparisons:</p><pre><code class="language-julia-repl hljs">julia&gt; @test π ≈ 3.14 atol=0.01
Test Passed</code></pre><p>This is equivalent to the uglier test <code>@test ≈(π, 3.14, atol=0.01)</code>. It is an error to supply more than one expression unless the first is a call expression and the rest are assignments (<code>k=v</code>).</p><p>You can use any key for the <code>key=val</code> arguments, except for <code>broken</code> and <code>skip</code>, which have special meanings in the context of <code>@test</code>:</p><ul><li><code>broken=cond</code> indicates a test that should pass but currently consistently fails when <code>cond==true</code>.  Tests that the expression <code>ex</code> evaluates to <code>false</code> or causes an exception.  Returns a <code>Broken</code> <code>Result</code> if it does, or an <code>Error</code> <code>Result</code> if the expression evaluates to <code>true</code>.  Regular <code>@test ex</code> is evaluated when <code>cond==false</code>.</li><li><code>skip=cond</code> marks a test that should not be executed but should be included in test summary reporting as <code>Broken</code>, when <code>cond==true</code>.  This can be useful for tests that intermittently fail, or tests of not-yet-implemented functionality. Regular <code>@test ex</code> is evaluated when <code>cond==false</code>.</li></ul><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; @test 2 + 2 ≈ 6 atol=1 broken=true
Test Broken
  Expression: ≈(2 + 2, 6, atol = 1)

julia&gt; @test 2 + 2 ≈ 5 atol=1 broken=false
Test Passed

julia&gt; @test 2 + 2 == 5 skip=true
Test Broken
  Skipped: 2 + 2 == 5

julia&gt; @test 2 + 2 == 4 skip=false
Test Passed</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>The <code>broken</code> and <code>skip</code> keyword arguments require at least Julia 1.7.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L426-L492">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.@test_throws" href="#Test.@test_throws"><code>Test.@test_throws</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@test_throws exception expr</code></pre><p>Tests that the expression <code>expr</code> throws <code>exception</code>. The exception may specify either a type, a string, regular expression, or list of strings occurring in the displayed error message, a matching function, or a value (which will be tested for equality by comparing fields). Note that <code>@test_throws</code> does not support a trailing keyword form.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>The ability to specify anything other than a type or a value as <code>exception</code> requires Julia v1.8 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; @test_throws BoundsError [1, 2, 3][4]
Test Passed
      Thrown: BoundsError

julia&gt; @test_throws DimensionMismatch [1, 2, 3] + [1, 2]
Test Passed
      Thrown: DimensionMismatch

julia&gt; @test_throws &quot;Try sqrt(Complex&quot; sqrt(-1)
Test Passed
     Message: &quot;DomainError with -1.0:\nsqrt was called with a negative real argument but will only return a complex result if called with a complex argument. Try sqrt(Complex(x)).&quot;</code></pre><p>In the final example, instead of matching a single string it could alternatively have been performed with:</p><ul><li><code>[&quot;Try&quot;, &quot;Complex&quot;]</code> (a list of strings)</li><li><code>r&quot;Try sqrt\([Cc]omplex&quot;</code> (a regular expression)</li><li><code>str -&gt; occursin(&quot;complex&quot;, str)</code> (a matching function)</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L734-L767">source</a></section></article><p>For example, suppose we want to check our new function <code>foo(x)</code> works as expected:</p><pre><code class="language-julia-repl hljs">julia&gt; using Test

julia&gt; foo(x) = length(x)^2
foo (generic function with 1 method)</code></pre><p>If the condition is true, a <code>Pass</code> is returned:</p><pre><code class="language-julia-repl hljs">julia&gt; @test foo(&quot;bar&quot;) == 9
Test Passed

julia&gt; @test foo(&quot;fizz&quot;) &gt;= 10
Test Passed</code></pre><p>If the condition is false, then a <code>Fail</code> is returned and an exception is thrown:</p><pre><code class="language-julia-repl hljs">julia&gt; @test foo(&quot;f&quot;) == 20
Test Failed at none:1
  Expression: foo(&quot;f&quot;) == 20
   Evaluated: 1 == 20

ERROR: There was an error during testing</code></pre><p>If the condition could not be evaluated because an exception was thrown, which occurs in this case because <code>length</code> is not defined for symbols, an <code>Error</code> object is returned and an exception is thrown:</p><pre><code class="language-julia-repl hljs">julia&gt; @test foo(:cat) == 1
Error During Test
  Test threw an exception of type MethodError
  Expression: foo(:cat) == 1
  MethodError: no method matching length(::Symbol)
  The function `length` exists, but no method is defined for this combination of argument types.

  Closest candidates are:
    length(::SimpleVector) at essentials.jl:256
    length(::Base.MethodList) at reflection.jl:521
    length(::MethodTable) at reflection.jl:597
    ...
  Stacktrace:
  [...]
ERROR: There was an error during testing</code></pre><p>If we expect that evaluating an expression <em>should</em> throw an exception, then we can use <code>@test_throws</code> to check that this occurs:</p><pre><code class="language-julia-repl hljs">julia&gt; @test_throws MethodError foo(:cat)
Test Passed
      Thrown: MethodError</code></pre><h2 id="Working-with-Test-Sets"><a class="docs-heading-anchor" href="#Working-with-Test-Sets">Working with Test Sets</a><a id="Working-with-Test-Sets-1"></a><a class="docs-heading-anchor-permalink" href="#Working-with-Test-Sets" title="Permalink"></a></h2><p>Typically a large number of tests are used to make sure functions work correctly over a range of inputs. In the event a test fails, the default behavior is to throw an exception immediately. However, it is normally preferable to run the rest of the tests first to get a better picture of how many errors there are in the code being tested.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The <code>@testset</code> will create a local scope of its own when running the tests in it.</p></div></div><p>The <code>@testset</code> macro can be used to group tests into <em>sets</em>. All the tests in a test set will be run, and at the end of the test set a summary will be printed. If any of the tests failed, or could not be evaluated due to an error, the test set will then throw a <code>TestSetException</code>.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.@testset" href="#Test.@testset"><code>Test.@testset</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@testset [CustomTestSet] [options...] [&quot;description&quot;] begin test_ex end
@testset [CustomTestSet] [options...] [&quot;description $v&quot;] for v in itr test_ex end
@testset [CustomTestSet] [options...] [&quot;description $v, $w&quot;] for v in itrv, w in itrw test_ex end
@testset [CustomTestSet] [options...] [&quot;description&quot;] test_func()
@testset let v = v, w = w; test_ex; end</code></pre><p><strong>With begin/end or function call</strong></p><p>When @testset is used, with begin/end or a single function call, the macro starts a new test set in which to evaluate the given expression.</p><p>If no custom testset type is given it defaults to creating a <code>DefaultTestSet</code>. <code>DefaultTestSet</code> records all the results and, if there are any <code>Fail</code>s or <code>Error</code>s, throws an exception at the end of the top-level (non-nested) test set, along with a summary of the test results.</p><p>Any custom testset type (subtype of <code>AbstractTestSet</code>) can be given and it will also be used for any nested <code>@testset</code> invocations. The given options are only applied to the test set where they are given. The default test set type accepts three boolean options:</p><ul><li><code>verbose</code>: if <code>true</code>, the result summary of the nested testsets is shown even when they all pass (the default is <code>false</code>).</li><li><code>showtiming</code>: if <code>true</code>, the duration of each displayed testset is shown (the default is <code>true</code>).</li><li><code>failfast</code>: if <code>true</code>, any test failure or error will cause the testset and any child testsets to return immediately (the default is <code>false</code>). This can also be set globally via the env var <code>JULIA_TEST_FAILFAST</code>.</li></ul><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p><code>@testset test_func()</code> requires at least Julia 1.8.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p><code>failfast</code> requires at least Julia 1.9.</p></div></div><p>The description string accepts interpolation from the loop indices. If no description is provided, one is constructed based on the variables. If a function call is provided, its name will be used. Explicit description strings override this behavior.</p><p>By default the <code>@testset</code> macro will return the testset object itself, though this behavior can be customized in other testset types. If a <code>for</code> loop is used then the macro collects and returns a list of the return values of the <code>finish</code> method, which by default will return a list of the testset objects used in each iteration.</p><p>Before the execution of the body of a <code>@testset</code>, there is an implicit call to <code>Random.seed!(seed)</code> where <code>seed</code> is the current seed of the global RNG. Moreover, after the execution of the body, the state of the global RNG is restored to what it was before the <code>@testset</code>. This is meant to ease reproducibility in case of failure, and to allow seamless re-arrangements of <code>@testset</code>s regardless of their side-effect on the global RNG state.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; @testset &quot;trigonometric identities&quot; begin
           θ = 2/3*π
           @test sin(-θ) ≈ -sin(θ)
           @test cos(-θ) ≈ cos(θ)
           @test sin(2θ) ≈ 2*sin(θ)*cos(θ)
           @test cos(2θ) ≈ cos(θ)^2 - sin(θ)^2
       end;
Test Summary:            | Pass  Total  Time
trigonometric identities |    4      4  0.2s</code></pre><p><strong><code>@testset for</code></strong></p><p>When <code>@testset for</code> is used, the macro starts a new test for each iteration of the provided loop. The semantics of each test set are otherwise identical to that of that <code>begin/end</code> case (as if used for each loop iteration).</p><p><strong><code>@testset let</code></strong></p><p>When <code>@testset let</code> is used, the macro starts a <em>transparent</em> test set with the given object added as a context object to any failing test contained therein. This is useful when performing a set of related tests on one larger object and it is desirable to print this larger object when any of the individual tests fail. Transparent test sets do not introduce additional levels of nesting in the test set hierarchy and are passed through directly to the parent test set (with the context object appended to any failing tests.)</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p><code>@testset let</code> requires at least Julia 1.9.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.10</header><div class="admonition-body"><p>Multiple <code>let</code> assignments are supported since Julia 1.10.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; @testset let logi = log(im)
           @test imag(logi) == π/2
           @test !iszero(real(logi))
       end
Test Failed at none:3
  Expression: !(iszero(real(logi)))
     Context: logi = 0.0 + 1.5707963267948966im

ERROR: There was an error during testing

julia&gt; @testset let logi = log(im), op = !iszero
           @test imag(logi) == π/2
           @test op(real(logi))
       end
Test Failed at none:3
  Expression: op(real(logi))
     Context: logi = 0.0 + 1.5707963267948966im
              op = !iszero

ERROR: There was an error during testing</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L1481-L1593">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.TestSetException" href="#Test.TestSetException"><code>Test.TestSetException</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">TestSetException</code></pre><p>Thrown when a test set finishes and not all tests passed.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L978-L982">source</a></section></article><p>We can put our tests for the <code>foo(x)</code> function in a test set:</p><pre><code class="language-julia-repl hljs">julia&gt; @testset &quot;Foo Tests&quot; begin
           @test foo(&quot;a&quot;)   == 1
           @test foo(&quot;ab&quot;)  == 4
           @test foo(&quot;abc&quot;) == 9
       end;
Test Summary: | Pass  Total  Time
Foo Tests     |    3      3  0.0s</code></pre><p>Test sets can also be nested:</p><pre><code class="language-julia-repl hljs">julia&gt; @testset &quot;Foo Tests&quot; begin
           @testset &quot;Animals&quot; begin
               @test foo(&quot;cat&quot;) == 9
               @test foo(&quot;dog&quot;) == foo(&quot;cat&quot;)
           end
           @testset &quot;Arrays $i&quot; for i in 1:3
               @test foo(zeros(i)) == i^2
               @test foo(fill(1.0, i)) == i^2
           end
       end;
Test Summary: | Pass  Total  Time
Foo Tests     |    8      8  0.0s</code></pre><p>As well as call functions:</p><pre><code class="language-julia-repl hljs">julia&gt; f(x) = @test isone(x)
f (generic function with 1 method)

julia&gt; @testset f(1);
Test Summary: | Pass  Total  Time
f             |    1      1  0.0s</code></pre><p>This can be used to allow for factorization of test sets, making it easier to run individual test sets by running the associated functions instead. Note that in the case of functions, the test set will be given the name of the called function. In the event that a nested test set has no failures, as happened here, it will be hidden in the summary, unless the <code>verbose=true</code> option is passed:</p><pre><code class="language-julia-repl hljs">julia&gt; @testset verbose = true &quot;Foo Tests&quot; begin
           @testset &quot;Animals&quot; begin
               @test foo(&quot;cat&quot;) == 9
               @test foo(&quot;dog&quot;) == foo(&quot;cat&quot;)
           end
           @testset &quot;Arrays $i&quot; for i in 1:3
               @test foo(zeros(i)) == i^2
               @test foo(fill(1.0, i)) == i^2
           end
       end;
Test Summary: | Pass  Total  Time
Foo Tests     |    8      8  0.0s
  Animals     |    2      2  0.0s
  Arrays 1    |    2      2  0.0s
  Arrays 2    |    2      2  0.0s
  Arrays 3    |    2      2  0.0s</code></pre><p>If we do have a test failure, only the details for the failed test sets will be shown:</p><pre><code class="language-julia-repl hljs">julia&gt; @testset &quot;Foo Tests&quot; begin
           @testset &quot;Animals&quot; begin
               @testset &quot;Felines&quot; begin
                   @test foo(&quot;cat&quot;) == 9
               end
               @testset &quot;Canines&quot; begin
                   @test foo(&quot;dog&quot;) == 9
               end
           end
           @testset &quot;Arrays&quot; begin
               @test foo(zeros(2)) == 4
               @test foo(fill(1.0, 4)) == 15
           end
       end

Arrays: Test Failed
  Expression: foo(fill(1.0, 4)) == 15
   Evaluated: 16 == 15
[...]
Test Summary: | Pass  Fail  Total  Time
Foo Tests     |    3     1      4  0.0s
  Animals     |    2            2  0.0s
  Arrays      |    1     1      2  0.0s
ERROR: Some tests did not pass: 3 passed, 1 failed, 0 errored, 0 broken.</code></pre><h2 id="Testing-Log-Statements"><a class="docs-heading-anchor" href="#Testing-Log-Statements">Testing Log Statements</a><a id="Testing-Log-Statements-1"></a><a class="docs-heading-anchor-permalink" href="#Testing-Log-Statements" title="Permalink"></a></h2><p>One can use the <a href="Test.html#Test.@test_logs"><code>@test_logs</code></a> macro to test log statements, or use a <a href="Test.html#Test.TestLogger"><code>TestLogger</code></a>.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.@test_logs" href="#Test.@test_logs"><code>Test.@test_logs</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@test_logs [log_patterns...] [keywords] expression</code></pre><p>Collect a list of log records generated by <code>expression</code> using <code>collect_test_logs</code>, check that they match the sequence <code>log_patterns</code>, and return the value of <code>expression</code>.  The <code>keywords</code> provide some simple filtering of log records: the <code>min_level</code> keyword controls the minimum log level which will be collected for the test, the <code>match_mode</code> keyword defines how matching will be performed (the default <code>:all</code> checks that all logs and patterns match pairwise; use <code>:any</code> to check that the pattern matches at least once somewhere in the sequence.)</p><p>The most useful log pattern is a simple tuple of the form <code>(level,message)</code>. A different number of tuple elements may be used to match other log metadata, corresponding to the arguments to passed to <code>AbstractLogger</code> via the <code>handle_message</code> function: <code>(level,message,module,group,id,file,line)</code>. Elements which are present will be matched pairwise with the log record fields using <code>==</code> by default, with the special cases that <code>Symbol</code>s may be used for the standard log levels, and <code>Regex</code>s in the pattern will match string or Symbol fields using <code>occursin</code>.</p><p><strong>Examples</strong></p><p>Consider a function which logs a warning, and several debug messages:</p><pre><code class="nohighlight hljs">function foo(n)
    @info &quot;Doing foo with n=$n&quot;
    for i=1:n
        @debug &quot;Iteration $i&quot;
    end
    42
end</code></pre><p>We can test the info message using</p><pre><code class="nohighlight hljs">@test_logs (:info,&quot;Doing foo with n=2&quot;) foo(2)</code></pre><p>If we also wanted to test the debug messages, these need to be enabled with the <code>min_level</code> keyword:</p><pre><code class="nohighlight hljs">using Logging
@test_logs (:info,&quot;Doing foo with n=2&quot;) (:debug,&quot;Iteration 1&quot;) (:debug,&quot;Iteration 2&quot;) min_level=Logging.Debug foo(2)</code></pre><p>If you want to test that some particular messages are generated while ignoring the rest, you can set the keyword <code>match_mode=:any</code>:</p><pre><code class="nohighlight hljs">using Logging
@test_logs (:info,) (:debug,&quot;Iteration 42&quot;) min_level=Logging.Debug match_mode=:any foo(100)</code></pre><p>The macro may be chained with <code>@test</code> to also test the returned value:</p><pre><code class="nohighlight hljs">@test (@test_logs (:info,&quot;Doing foo with n=2&quot;) foo(2)) == 42</code></pre><p>If you want to test for the absence of warnings, you can omit specifying log patterns and set the <code>min_level</code> accordingly:</p><pre><code class="nohighlight hljs"># test that the expression logs no messages when the logger level is warn:
@test_logs min_level=Logging.Warn @info(&quot;Some information&quot;) # passes
@test_logs min_level=Logging.Warn @warn(&quot;Some information&quot;) # fails</code></pre><p>If you want to test the absence of warnings (or error messages) in <a href="../base/io-network.html#Base.stderr"><code>stderr</code></a> which are not generated by <code>@warn</code>, see <a href="Test.html#Test.@test_nowarn"><code>@test_nowarn</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/logging.jl#L174-L236">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.TestLogger" href="#Test.TestLogger"><code>Test.TestLogger</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">TestLogger(; min_level=Info, catch_exceptions=false)</code></pre><p>Create a <code>TestLogger</code> which captures logged messages in its <code>logs::Vector{LogRecord}</code> field.</p><p>Set <code>min_level</code> to control the <code>LogLevel</code>, <code>catch_exceptions</code> for whether or not exceptions thrown as part of log event generation should be caught, and <code>respect_maxlog</code> for whether or not to follow the convention of logging messages with <code>maxlog=n</code> for some integer <code>n</code> at most <code>n</code> times.</p><p>See also: <a href="Test.html#Test.LogRecord"><code>LogRecord</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; using Test, Logging

julia&gt; f() = @info &quot;Hi&quot; number=5;

julia&gt; test_logger = TestLogger();

julia&gt; with_logger(test_logger) do
           f()
           @info &quot;Bye!&quot;
       end

julia&gt; @test test_logger.logs[1].message == &quot;Hi&quot;
Test Passed

julia&gt; @test test_logger.logs[1].kwargs[:number] == 5
Test Passed

julia&gt; @test test_logger.logs[2].message == &quot;Bye!&quot;
Test Passed</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/logging.jl#L51-L86">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.LogRecord" href="#Test.LogRecord"><code>Test.LogRecord</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LogRecord</code></pre><p>Stores the results of a single log event. Fields:</p><ul><li><code>level</code>: the <a href="Logging.html#Logging.LogLevel"><code>LogLevel</code></a> of the log message</li><li><code>message</code>: the textual content of the log message</li><li><code>_module</code>: the module of the log event</li><li><code>group</code>: the logging group (by default, the name of the file containing the log event)</li><li><code>id</code>: the ID of the log event</li><li><code>file</code>: the file containing the log event</li><li><code>line</code>: the line within the file of the log event</li><li><code>kwargs</code>: any keyword arguments passed to the log event</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/logging.jl#L8-L21">source</a></section></article><h2 id="Other-Test-Macros"><a class="docs-heading-anchor" href="#Other-Test-Macros">Other Test Macros</a><a id="Other-Test-Macros-1"></a><a class="docs-heading-anchor-permalink" href="#Other-Test-Macros" title="Permalink"></a></h2><p>As calculations on floating-point values can be imprecise, you can perform approximate equality checks using either <code>@test a ≈ b</code> (where <code>≈</code>, typed via tab completion of <code>\approx</code>, is the <a href="../base/math.html#Base.isapprox"><code>isapprox</code></a> function) or use <a href="../base/math.html#Base.isapprox"><code>isapprox</code></a> directly.</p><pre><code class="language-julia-repl hljs">julia&gt; @test 1 ≈ 0.999999999
Test Passed

julia&gt; @test 1 ≈ 0.999999
Test Failed at none:1
  Expression: 1 ≈ 0.999999
   Evaluated: 1 ≈ 0.999999

ERROR: There was an error during testing</code></pre><p>You can specify relative and absolute tolerances by setting the <code>rtol</code> and <code>atol</code> keyword arguments of <code>isapprox</code>, respectively, after the <code>≈</code> comparison:</p><pre><code class="language-julia-repl hljs">julia&gt; @test 1 ≈ 0.999999  rtol=1e-5
Test Passed</code></pre><p>Note that this is not a specific feature of the <code>≈</code> but rather a general feature of the <code>@test</code> macro: <code>@test a &lt;op&gt; b key=val</code> is transformed by the macro into <code>@test op(a, b, key=val)</code>. It is, however, particularly useful for <code>≈</code> tests.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.@inferred" href="#Test.@inferred"><code>Test.@inferred</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@inferred [AllowedType] f(x)</code></pre><p>Tests that the call expression <code>f(x)</code> returns a value of the same type inferred by the compiler. It is useful to check for type stability.</p><p><code>f(x)</code> can be any call expression. Returns the result of <code>f(x)</code> if the types match, and an <code>Error</code> <code>Result</code> if it finds different types.</p><p>Optionally, <code>AllowedType</code> relaxes the test, by making it pass when either the type of <code>f(x)</code> matches the inferred type modulo <code>AllowedType</code>, or when the return type is a subtype of <code>AllowedType</code>. This is useful when testing type stability of functions returning a small union such as <code>Union{Nothing, T}</code> or <code>Union{Missing, T}</code>.</p><pre><code class="language-julia-repl hljs">julia&gt; f(a) = a &gt; 1 ? 1 : 1.0
f (generic function with 1 method)

julia&gt; typeof(f(2))
Int64

julia&gt; @code_warntype f(2)
MethodInstance for f(::Int64)
  from f(a) @ Main none:1
Arguments
  #self#::Core.Const(f)
  a::Int64
Body::UNION{FLOAT64, INT64}
1 ─ %1 = (a &gt; 1)::Bool
└──      goto #3 if not %1
2 ─      return 1
3 ─      return 1.0

julia&gt; @inferred f(2)
ERROR: return type Int64 does not match inferred return type Union{Float64, Int64}
[...]

julia&gt; @inferred max(1, 2)
2

julia&gt; g(a) = a &lt; 10 ? missing : 1.0
g (generic function with 1 method)

julia&gt; @inferred g(20)
ERROR: return type Float64 does not match inferred return type Union{Missing, Float64}
[...]

julia&gt; @inferred Missing g(20)
1.0

julia&gt; h(a) = a &lt; 10 ? missing : f(a)
h (generic function with 1 method)

julia&gt; @inferred Missing h(20)
ERROR: return type Int64 does not match inferred return type Union{Missing, Float64, Int64}
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L1910-L1967">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.@test_deprecated" href="#Test.@test_deprecated"><code>Test.@test_deprecated</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@test_deprecated [pattern] expression</code></pre><p>When <code>--depwarn=yes</code>, test that <code>expression</code> emits a deprecation warning and return the value of <code>expression</code>.  The log message string will be matched against <code>pattern</code> which defaults to <code>r&quot;deprecated&quot;i</code>.</p><p>When <code>--depwarn=no</code>, simply return the result of executing <code>expression</code>.  When <code>--depwarn=error</code>, check that an ErrorException is thrown.</p><p><strong>Examples</strong></p><pre><code class="nohighlight hljs"># Deprecated in julia 0.7
@test_deprecated num2hex(1)

# The returned value can be tested by chaining with @test:
@test (@test_deprecated num2hex(1)) == &quot;0000000000000001&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/logging.jl#L308-L327">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.@test_warn" href="#Test.@test_warn"><code>Test.@test_warn</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@test_warn msg expr</code></pre><p>Test whether evaluating <code>expr</code> results in <a href="../base/io-network.html#Base.stderr"><code>stderr</code></a> output that contains the <code>msg</code> string or matches the <code>msg</code> regular expression.  If <code>msg</code> is a boolean function, tests whether <code>msg(output)</code> returns <code>true</code>.  If <code>msg</code> is a tuple or array, checks that the error output contains/matches each item in <code>msg</code>. Returns the result of evaluating <code>expr</code>.</p><p>See also <a href="Test.html#Test.@test_nowarn"><code>@test_nowarn</code></a> to check for the absence of error output.</p><p>Note: Warnings generated by <code>@warn</code> cannot be tested with this macro. Use <a href="Test.html#Test.@test_logs"><code>@test_logs</code></a> instead.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L872-L885">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.@test_nowarn" href="#Test.@test_nowarn"><code>Test.@test_nowarn</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@test_nowarn expr</code></pre><p>Test whether evaluating <code>expr</code> results in empty <a href="../base/io-network.html#Base.stderr"><code>stderr</code></a> output (no warnings or other messages).  Returns the result of evaluating <code>expr</code>.</p><p>Note: The absence of warnings generated by <code>@warn</code> cannot be tested with this macro. Use <a href="Test.html#Test.@test_logs"><code>@test_logs</code></a> instead.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L904-L912">source</a></section></article><h2 id="Broken-Tests"><a class="docs-heading-anchor" href="#Broken-Tests">Broken Tests</a><a id="Broken-Tests-1"></a><a class="docs-heading-anchor-permalink" href="#Broken-Tests" title="Permalink"></a></h2><p>If a test fails consistently it can be changed to use the <code>@test_broken</code> macro. This will denote the test as <code>Broken</code> if the test continues to fail and alerts the user via an <code>Error</code> if the test succeeds.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.@test_broken" href="#Test.@test_broken"><code>Test.@test_broken</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@test_broken ex
@test_broken f(args...) key=val ...</code></pre><p>Indicates a test that should pass but currently consistently fails. Tests that the expression <code>ex</code> evaluates to <code>false</code> or causes an exception. Returns a <code>Broken</code> <code>Result</code> if it does, or an <code>Error</code> <code>Result</code> if the expression evaluates to <code>true</code>.  This is equivalent to <a href="Test.html#Test.@test"><code>@test ex broken=true</code></a>.</p><p>The <code>@test_broken f(args...) key=val...</code> form works as for the <code>@test</code> macro.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; @test_broken 1 == 2
Test Broken
  Expression: 1 == 2

julia&gt; @test_broken 1 == 2 atol=0.1
Test Broken
  Expression: ==(1, 2, atol = 0.1)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L526-L548">source</a></section></article><p><code>@test_skip</code> is also available to skip a test without evaluation, but counting the skipped test in the test set reporting. The test will not run but gives a <code>Broken</code> <code>Result</code>.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.@test_skip" href="#Test.@test_skip"><code>Test.@test_skip</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@test_skip ex
@test_skip f(args...) key=val ...</code></pre><p>Marks a test that should not be executed but should be included in test summary reporting as <code>Broken</code>. This can be useful for tests that intermittently fail, or tests of not-yet-implemented functionality.  This is equivalent to <a href="Test.html#Test.@test"><code>@test ex skip=true</code></a>.</p><p>The <code>@test_skip f(args...) key=val...</code> form works as for the <code>@test</code> macro.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; @test_skip 1 == 2
Test Broken
  Skipped: 1 == 2

julia&gt; @test_skip 1 == 2 atol=0.1
Test Broken
  Skipped: ==(1, 2, atol = 0.1)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L557-L578">source</a></section></article><h2 id="Test-result-types"><a class="docs-heading-anchor" href="#Test-result-types">Test result types</a><a id="Test-result-types-1"></a><a class="docs-heading-anchor-permalink" href="#Test-result-types" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.Result" href="#Test.Result"><code>Test.Result</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Test.Result</code></pre><p>All tests produce a result object. This object may or may not be stored, depending on whether the test is part of a test set.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L106-L111">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.Pass" href="#Test.Pass"><code>Test.Pass</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Test.Pass &lt;: Test.Result</code></pre><p>The test condition was true, i.e. the expression evaluated to true or the correct exception was thrown.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L114-L119">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.Fail" href="#Test.Fail"><code>Test.Fail</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Test.Fail &lt;: Test.Result</code></pre><p>The test condition was false, i.e. the expression evaluated to false or the correct exception was not thrown.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L144-L149">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.Error" href="#Test.Error"><code>Test.Error</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Test.Error &lt;: Test.Result</code></pre><p>The test condition couldn&#39;t be evaluated due to an exception, or it evaluated to something other than a <a href="../base/numbers.html#Core.Bool"><code>Bool</code></a>. In the case of <code>@test_broken</code> it is used to indicate that an unexpected <code>Pass</code> <code>Result</code> occurred.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L212-L219">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.Broken" href="#Test.Broken"><code>Test.Broken</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Test.Broken &lt;: Test.Result</code></pre><p>The test condition is the expected (failed) result of a broken test, or was explicitly skipped with <code>@test_skip</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L293-L298">source</a></section></article><h2 id="Creating-Custom-AbstractTestSet-Types"><a class="docs-heading-anchor" href="#Creating-Custom-AbstractTestSet-Types">Creating Custom <code>AbstractTestSet</code> Types</a><a id="Creating-Custom-AbstractTestSet-Types-1"></a><a class="docs-heading-anchor-permalink" href="#Creating-Custom-AbstractTestSet-Types" title="Permalink"></a></h2><p>Packages can create their own <code>AbstractTestSet</code> subtypes by implementing the <code>record</code> and <code>finish</code> methods. The subtype should have a one-argument constructor taking a description string, with any options passed in as keyword arguments.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.record" href="#Test.record"><code>Test.record</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">record(ts::AbstractTestSet, res::Result)</code></pre><p>Record a result to a testset. This function is called by the <code>@testset</code> infrastructure each time a contained <code>@test</code> macro completes, and is given the test result (which could be an <code>Error</code>). This will also be called with an <code>Error</code> if an exception is thrown inside the test block but outside of a <code>@test</code> context.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L947-L954">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.finish" href="#Test.finish"><code>Test.finish</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">finish(ts::AbstractTestSet)</code></pre><p>Do any final processing necessary for the given testset. This is called by the <code>@testset</code> infrastructure after a test block executes.</p><p>Custom <code>AbstractTestSet</code> subtypes should call <code>record</code> on their parent (if there is one) to add themselves to the tree of test results. This might be implemented as:</p><pre><code class="language-julia hljs">if get_testset_depth() != 0
    # Attach this test set to the parent test set
    parent_ts = get_testset()
    record(parent_ts, self)
    return self
end</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L957-L975">source</a></section></article><p><code>Test</code> takes responsibility for maintaining a stack of nested testsets as they are executed, but any result accumulation is the responsibility of the <code>AbstractTestSet</code> subtype. You can access this stack with the <code>get_testset</code> and <code>get_testset_depth</code> methods. Note that these functions are not exported.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.get_testset" href="#Test.get_testset"><code>Test.get_testset</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">get_testset()</code></pre><p>Retrieve the active test set from the task&#39;s local storage. If no test set is active, use the fallback default test set.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L1862-L1867">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.get_testset_depth" href="#Test.get_testset_depth"><code>Test.get_testset_depth</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">get_testset_depth()</code></pre><p>Return the number of active test sets, not including the default test set</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L1897-L1901">source</a></section></article><p><code>Test</code> also makes sure that nested <code>@testset</code> invocations use the same <code>AbstractTestSet</code> subtype as their parent unless it is set explicitly. It does not propagate any properties of the testset. Option inheritance behavior can be implemented by packages using the stack infrastructure that <code>Test</code> provides.</p><p>Defining a basic <code>AbstractTestSet</code> subtype might look like:</p><pre><code class="language-julia hljs">import Test: Test, record, finish
using Test: AbstractTestSet, Result, Pass, Fail, Error
using Test: get_testset_depth, get_testset
struct CustomTestSet &lt;: Test.AbstractTestSet
    description::AbstractString
    foo::Int
    results::Vector
    # constructor takes a description string and options keyword arguments
    CustomTestSet(desc; foo=1) = new(desc, foo, [])
end

record(ts::CustomTestSet, child::AbstractTestSet) = push!(ts.results, child)
record(ts::CustomTestSet, res::Result) = push!(ts.results, res)
function finish(ts::CustomTestSet)
    # just record if we&#39;re not the top-level parent
    if get_testset_depth() &gt; 0
        record(get_testset(), ts)
        return ts
    end

    # so the results are printed if we are at the top level
    Test.print_test_results(ts)
    return ts
end</code></pre><p>And using that testset looks like:</p><pre><code class="language-julia hljs">@testset CustomTestSet foo=4 &quot;custom testset inner 2&quot; begin
    # this testset should inherit the type, but not the argument.
    @testset &quot;custom testset inner&quot; begin
        @test true
    end
end</code></pre><p>In order to use a custom testset and have the recorded results printed as part of any outer default testset, also define <code>Test.get_test_counts</code>. This might look like so:</p><pre><code class="language-julia hljs">using Test: AbstractTestSet, Pass, Fail, Error, Broken, get_test_counts, TestCounts, format_duration

function Test.get_test_counts(ts::CustomTestSet)
    passes, fails, errors, broken = 0, 0, 0, 0
    # cumulative results
    c_passes, c_fails, c_errors, c_broken = 0, 0, 0, 0

    for t in ts.results
        # count up results
        isa(t, Pass)   &amp;&amp; (passes += 1)
        isa(t, Fail)   &amp;&amp; (fails  += 1)
        isa(t, Error)  &amp;&amp; (errors += 1)
        isa(t, Broken) &amp;&amp; (broken += 1)
        # handle children
        if isa(t, AbstractTestSet)
            tc = get_test_counts(t)::TestCounts
            c_passes += tc.passes + tc.cumulative_passes
            c_fails  += tc.fails + tc.cumulative_fails
            c_errors += tc.errors + tc.cumulative_errors
            c_broken += tc.broken + tc.cumulative_broken
        end
    end
    # get a duration, if we have one
    duration = format_duration(ts)
    return TestCounts(true, passes, fails, errors, broken, c_passes, c_fails, c_errors, c_broken, duration)
end</code></pre><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.TestCounts" href="#Test.TestCounts"><code>Test.TestCounts</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">TestCounts</code></pre><p>Holds the state for recursively gathering the results of a test set for display purposes.</p><p>Fields:</p><ul><li><code>customized</code>: Whether the function <code>get_test_counts</code> was customized for the <code>AbstractTestSet</code>               this counts object is for. If a custom method was defined, always pass <code>true</code>               to the constructor.</li><li><code>passes</code>: The number of passing <code>@test</code> invocations.</li><li><code>fails</code>: The number of failing <code>@test</code> invocations.</li><li><code>errors</code>: The number of erroring <code>@test</code> invocations.</li><li><code>broken</code>: The number of broken <code>@test</code> invocations.</li><li><code>passes</code>: The cumulative number of passing <code>@test</code> invocations.</li><li><code>fails</code>: The cumulative number of failing <code>@test</code> invocations.</li><li><code>errors</code>: The cumulative number of erroring <code>@test</code> invocations.</li><li><code>broken</code>: The cumulative number of broken <code>@test</code> invocations.</li><li><code>duration</code>: The total duration the <code>AbstractTestSet</code> in question ran for, as a formatted <code>String</code>.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L1297-L1316">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.get_test_counts" href="#Test.get_test_counts"><code>Test.get_test_counts</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>&quot;     get<em>test</em>counts(::AbstractTestSet) -&gt; TestCounts</p><p>Recursive function that counts the number of test results of each type directly in the testset, and totals across the child testsets.</p><p>Custom <code>AbstractTestSet</code> should implement this function to get their totals counted &amp; displayed with <code>DefaultTestSet</code> as well.</p><p>If this is not implemented for a custom <code>TestSet</code>, the printing falls back to reporting <code>x</code> for failures and <code>?s</code> for the duration.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L1330-L1342">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.format_duration" href="#Test.format_duration"><code>Test.format_duration</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">format_duration(::AbstractTestSet)</code></pre><p>Return a formatted string for printing the duration the testset ran for.</p><p>If not defined, falls back to <code>&quot;?s&quot;</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L1367-L1373">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.print_test_results" href="#Test.print_test_results"><code>Test.print_test_results</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">print_test_results(ts::AbstractTestSet, depth_pad=0)</code></pre><p>Print the results of an <code>AbstractTestSet</code> as a formatted table.</p><p><code>depth_pad</code> refers to how much padding should be added in front of all output.</p><p>Called inside of <code>Test.finish</code>, if the <code>finish</code>ed testset is the topmost testset.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L1167-L1176">source</a></section></article><h2 id="Test-utilities"><a class="docs-heading-anchor" href="#Test-utilities">Test utilities</a><a id="Test-utilities-1"></a><a class="docs-heading-anchor-permalink" href="#Test-utilities" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.GenericArray" href="#Test.GenericArray"><code>Test.GenericArray</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>The <code>GenericArray</code> can be used to test generic array APIs that program to the <code>AbstractArray</code> interface, in order to ensure that functions can work with array types besides the standard <code>Array</code> type.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L2237-L2241">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.GenericDict" href="#Test.GenericDict"><code>Test.GenericDict</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>The <code>GenericDict</code> can be used to test generic dict APIs that program to the <code>AbstractDict</code> interface, in order to ensure that functions can work with associative types besides the standard <code>Dict</code> type.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L2213-L2217">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.GenericOrder" href="#Test.GenericOrder"><code>Test.GenericOrder</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>The <code>GenericOrder</code> can be used to test APIs for their support of generic ordered types.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L2249-L2252">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.GenericSet" href="#Test.GenericSet"><code>Test.GenericSet</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>The <code>GenericSet</code> can be used to test generic set APIs that program to the <code>AbstractSet</code> interface, in order to ensure that functions can work with set types besides the standard <code>Set</code> and <code>BitSet</code> types.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L2204-L2208">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.GenericString" href="#Test.GenericString"><code>Test.GenericString</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>The <code>GenericString</code> can be used to test generic string APIs that program to the <code>AbstractString</code> interface, in order to ensure that functions can work with string types besides the standard <code>String</code> type.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L2187-L2191">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.detect_ambiguities" href="#Test.detect_ambiguities"><code>Test.detect_ambiguities</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">detect_ambiguities(mod1, mod2...; recursive=false,
                                  ambiguous_bottom=false,
                                  allowed_undefineds=nothing)</code></pre><p>Return a vector of <code>(Method,Method)</code> pairs of ambiguous methods defined in the specified modules. Use <code>recursive=true</code> to test in all submodules.</p><p><code>ambiguous_bottom</code> controls whether ambiguities triggered only by <code>Union{}</code> type parameters are included; in most cases you probably want to set this to <code>false</code>. See <a href="../base/base.html#Base.isambiguous"><code>Base.isambiguous</code></a>.</p><p>See <a href="Test.html#Test.detect_unbound_args"><code>Test.detect_unbound_args</code></a> for an explanation of <code>allowed_undefineds</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p><code>allowed_undefineds</code> requires at least Julia 1.8.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L2025-L2043">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Test.detect_unbound_args" href="#Test.detect_unbound_args"><code>Test.detect_unbound_args</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">detect_unbound_args(mod1, mod2...; recursive=false, allowed_undefineds=nothing)</code></pre><p>Return a vector of <code>Method</code>s which may have unbound type parameters. Use <code>recursive=true</code> to test in all submodules.</p><p>By default, any undefined symbols trigger a warning. This warning can be suppressed by supplying a collection of <code>GlobalRef</code>s for which the warning can be skipped. For example, setting</p><pre><code class="nohighlight hljs">allowed_undefineds = Set([GlobalRef(Base, :active_repl),
                          GlobalRef(Base, :active_repl_backend)])</code></pre><p>would suppress warnings about <code>Base.active_repl</code> and <code>Base.active_repl_backend</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p><code>allowed_undefineds</code> requires at least Julia 1.8.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Test/src/Test.jl#L2104-L2124">source</a></section></article><h2 id="Workflow-for-Testing-Packages"><a class="docs-heading-anchor" href="#Workflow-for-Testing-Packages">Workflow for Testing Packages</a><a id="Workflow-for-Testing-Packages-1"></a><a class="docs-heading-anchor-permalink" href="#Workflow-for-Testing-Packages" title="Permalink"></a></h2><p>Using the tools available to us in the previous sections, here is a potential workflow of creating a package and adding tests to it.</p><h3 id="Generating-an-Example-Package"><a class="docs-heading-anchor" href="#Generating-an-Example-Package">Generating an Example Package</a><a id="Generating-an-Example-Package-1"></a><a class="docs-heading-anchor-permalink" href="#Generating-an-Example-Package" title="Permalink"></a></h3><p>For this workflow, we will create a package called <code>Example</code>:</p><pre><code class="language-julia hljs">pkg&gt; generate Example
shell&gt; cd Example
shell&gt; mkdir test
pkg&gt; activate .</code></pre><h3 id="Creating-Sample-Functions"><a class="docs-heading-anchor" href="#Creating-Sample-Functions">Creating Sample Functions</a><a id="Creating-Sample-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Creating-Sample-Functions" title="Permalink"></a></h3><p>The number one requirement for testing a package is to have functionality to test. For that, we will add some simple functions to <code>Example</code> that we can test. Add the following to <code>src/Example.jl</code>:</p><pre><code class="language-julia hljs">module Example

function greet()
    &quot;Hello world!&quot;
end

function simple_add(a, b)
    a + b
end

function type_multiply(a::Float64, b::Float64)
    a * b
end

export greet, simple_add, type_multiply

end</code></pre><h3 id="Creating-a-Test-Environment"><a class="docs-heading-anchor" href="#Creating-a-Test-Environment">Creating a Test Environment</a><a id="Creating-a-Test-Environment-1"></a><a class="docs-heading-anchor-permalink" href="#Creating-a-Test-Environment" title="Permalink"></a></h3><p>From within the root of the <code>Example</code> package, navigate to the <code>test</code> directory, activate a new environment there, and add the <code>Test</code> package to the environment:</p><pre><code class="language-julia hljs">shell&gt; cd test
pkg&gt; activate .
(test) pkg&gt; add Test</code></pre><h3 id="Testing-Our-Package"><a class="docs-heading-anchor" href="#Testing-Our-Package">Testing Our Package</a><a id="Testing-Our-Package-1"></a><a class="docs-heading-anchor-permalink" href="#Testing-Our-Package" title="Permalink"></a></h3><p>Now, we are ready to add tests to <code>Example</code>. It is standard practice to create a file within the <code>test</code> directory called <code>runtests.jl</code> which contains the test sets we want to run. Go ahead and create that file within the <code>test</code> directory and add the following code to it:</p><pre><code class="language-julia hljs">using Example
using Test

@testset &quot;Example tests&quot; begin

    @testset &quot;Math tests&quot; begin
        include(&quot;math_tests.jl&quot;)
    end

    @testset &quot;Greeting tests&quot; begin
        include(&quot;greeting_tests.jl&quot;)
    end
end</code></pre><p>We will need to create those two included files, <code>math_tests.jl</code> and <code>greeting_tests.jl</code>, and add some tests to them.</p><blockquote><p><strong>Note:</strong> Notice how we did not have to specify add <code>Example</code> into the <code>test</code> environment&#39;s <code>Project.toml</code>. This is a benefit of Julia&#39;s testing system that you could <a href="https://pkgdocs.julialang.org/dev/creating-packages/">read about more here</a>.</p></blockquote><h4 id="Writing-Tests-for-math_tests.jl"><a class="docs-heading-anchor" href="#Writing-Tests-for-math_tests.jl">Writing Tests for <code>math_tests.jl</code></a><a id="Writing-Tests-for-math_tests.jl-1"></a><a class="docs-heading-anchor-permalink" href="#Writing-Tests-for-math_tests.jl" title="Permalink"></a></h4><p>Using our knowledge of <code>Test.jl</code>, here are some example tests we could add to <code>math_tests.jl</code>:</p><pre><code class="language-julia hljs">@testset &quot;Testset 1&quot; begin
    @test 2 == simple_add(1, 1)
    @test 3.5 == simple_add(1, 2.5)
        @test_throws MethodError simple_add(1, &quot;A&quot;)
        @test_throws MethodError simple_add(1, 2, 3)
end

@testset &quot;Testset 2&quot; begin
    @test 1.0 == type_multiply(1.0, 1.0)
        @test isa(type_multiply(2.0, 2.0), Float64)
    @test_throws MethodError type_multiply(1, 2.5)
end</code></pre><h4 id="Writing-Tests-for-greeting_tests.jl"><a class="docs-heading-anchor" href="#Writing-Tests-for-greeting_tests.jl">Writing Tests for <code>greeting_tests.jl</code></a><a id="Writing-Tests-for-greeting_tests.jl-1"></a><a class="docs-heading-anchor-permalink" href="#Writing-Tests-for-greeting_tests.jl" title="Permalink"></a></h4><p>Using our knowledge of <code>Test.jl</code>, here are some example tests we could add to <code>greeting_tests.jl</code>:</p><pre><code class="language-julia hljs">@testset &quot;Testset 3&quot; begin
    @test &quot;Hello world!&quot; == greet()
    @test_throws MethodError greet(&quot;Antonia&quot;)
end</code></pre><h3 id="Testing-Our-Package-2"><a class="docs-heading-anchor" href="#Testing-Our-Package-2">Testing Our Package</a><a class="docs-heading-anchor-permalink" href="#Testing-Our-Package-2" title="Permalink"></a></h3><p>Now that we have added our tests and our <code>runtests.jl</code> script in <code>test</code>, we can test our <code>Example</code> package by going back to the root of the <code>Example</code> package environment and reactivating the <code>Example</code> environment:</p><pre><code class="language-julia hljs">shell&gt; cd ..
pkg&gt; activate .</code></pre><p>From there, we can finally run our test suite as follows:</p><pre><code class="language-julia hljs">(Example) pkg&gt; test
     Testing Example
      Status `/tmp/jl_Yngpvy/Project.toml`
  [fa318bd2] Example v0.1.0 `/home/<USER>/Projects/tmp/errata/Example`
  [8dfed614] Test `@stdlib/Test`
      Status `/tmp/jl_Yngpvy/Manifest.toml`
  [fa318bd2] Example v0.1.0 `/home/<USER>/Projects/tmp/errata/Example`
  [2a0f44e3] Base64 `@stdlib/Base64`
  [b77e0a4c] InteractiveUtils `@stdlib/InteractiveUtils`
  [56ddb016] Logging `@stdlib/Logging`
  [d6f4376e] Markdown `@stdlib/Markdown`
  [9a3f8284] Random `@stdlib/Random`
  [ea8e919c] SHA `@stdlib/SHA`
  [9e88b42a] Serialization `@stdlib/Serialization`
  [8dfed614] Test `@stdlib/Test`
     Testing Running tests...
Test Summary: | Pass  Total
Example tests |    9      9
     Testing Example tests passed</code></pre><p>And if all went correctly, you should see a similar output as above. Using <code>Test.jl</code>, more complicated tests can be added for packages but this should ideally point developers in the direction of how to get started with testing their own created packages.</p><h3 id="Code-Coverage"><a class="docs-heading-anchor" href="#Code-Coverage">Code Coverage</a><a id="Code-Coverage-1"></a><a class="docs-heading-anchor-permalink" href="#Code-Coverage" title="Permalink"></a></h3><p>Code coverage tracking during tests can be enabled using the <code>pkg&gt; test --coverage</code> flag (or at a lower level using the <a href="../manual/command-line-interface.html#command-line-interface"><code>--code-coverage</code></a> julia arg). This is on by default in the <a href="https://github.com/julia-actions/julia-runtest">julia-runtest</a> GitHub action.</p><p>To evaluate coverage either manually inspect the <code>.cov</code> files that are generated beside the source files locally, or in CI use the <a href="https://github.com/julia-actions/julia-processcoverage">julia-processcoverage</a> GitHub action.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>Since Julia 1.11, coverage is not collected during the package precompilation phase.</p></div></div></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="Tar.html">« Tar</a><a class="docs-footer-nextpage" href="UUIDs.html">UUIDs »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
