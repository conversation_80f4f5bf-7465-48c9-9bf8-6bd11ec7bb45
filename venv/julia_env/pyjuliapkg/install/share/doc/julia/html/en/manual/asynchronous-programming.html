<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Asynchronous Programming · The Julia Language</title><meta name="title" content="Asynchronous Programming · The Julia Language"/><meta property="og:title" content="Asynchronous Programming · The Julia Language"/><meta property="twitter:title" content="Asynchronous Programming · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li class="is-active"><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a><ul class="internal"><li><a class="tocitem" href="#Basic-Task-operations"><span>Basic <code>Task</code> operations</span></a></li><li><a class="tocitem" href="#Communicating-with-Channels"><span>Communicating with Channels</span></a></li><li><a class="tocitem" href="#More-task-operations"><span>More task operations</span></a></li><li><a class="tocitem" href="#Tasks-and-events"><span>Tasks and events</span></a></li></ul></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="asynchronous-programming.html">Asynchronous Programming</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="asynchronous-programming.html">Asynchronous Programming</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/asynchronous-programming.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="man-asynchronous"><a class="docs-heading-anchor" href="#man-asynchronous">Asynchronous Programming</a><a id="man-asynchronous-1"></a><a class="docs-heading-anchor-permalink" href="#man-asynchronous" title="Permalink"></a></h1><p>When a program needs to interact with the outside world, for example communicating with another machine over the internet, operations in the program may need to happen in an unpredictable order. Say your program needs to download a file. We would like to initiate the download operation, perform other operations while we wait for it to complete, and then resume the code that needs the downloaded file when it is available. This sort of scenario falls in the domain of asynchronous programming, sometimes also referred to as concurrent programming (since, conceptually, multiple things are happening at once).</p><p>To address these scenarios, Julia provides <a href="../base/parallel.html#Core.Task"><code>Task</code></a>s (also known by several other names, such as symmetric coroutines, lightweight threads, cooperative multitasking, or one-shot continuations). When a piece of computing work (in practice, executing a particular function) is designated as a <a href="../base/parallel.html#Core.Task"><code>Task</code></a>, it becomes possible to interrupt it by switching to another <a href="../base/parallel.html#Core.Task"><code>Task</code></a>. The original <a href="../base/parallel.html#Core.Task"><code>Task</code></a> can later be resumed, at which point it will pick up right where it left off. At first, this may seem similar to a function call. However there are two key differences. First, switching tasks does not use any space, so any number of task switches can occur without consuming the call stack. Second, switching among tasks can occur in any order, unlike function calls, where the called function must finish executing before control returns to the calling function.</p><h2 id="Basic-Task-operations"><a class="docs-heading-anchor" href="#Basic-Task-operations">Basic <code>Task</code> operations</a><a id="Basic-Task-operations-1"></a><a class="docs-heading-anchor-permalink" href="#Basic-Task-operations" title="Permalink"></a></h2><p>You can think of a <code>Task</code> as a handle to a unit of computational work to be performed. It has a create-start-run-finish lifecycle. Tasks are created by calling the <code>Task</code> constructor on a 0-argument function to run, or using the <a href="../base/parallel.html#Base.@task"><code>@task</code></a> macro:</p><pre><code class="language-julia-repl hljs">julia&gt; t = @task begin; sleep(5); println(&quot;done&quot;); end
Task (runnable) @0x00007f13a40c0eb0</code></pre><p><code>@task x</code> is equivalent to <code>Task(()-&gt;x)</code>.</p><p>This task will wait for five seconds, and then print <code>done</code>. However, it has not started running yet. We can run it whenever we&#39;re ready by calling <a href="../base/parallel.html#Base.schedule"><code>schedule</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; schedule(t);</code></pre><p>If you try this in the REPL, you will see that <code>schedule</code> returns immediately. That is because it simply adds <code>t</code> to an internal queue of tasks to run. Then, the REPL will print the next prompt and wait for more input. Waiting for keyboard input provides an opportunity for other tasks to run, so at that point <code>t</code> will start. <code>t</code> calls <a href="../base/parallel.html#Base.sleep"><code>sleep</code></a>, which sets a timer and stops execution. If other tasks have been scheduled, they could run then. After five seconds, the timer fires and restarts <code>t</code>, and you will see <code>done</code> printed. <code>t</code> is then finished.</p><p>The <a href="../base/parallel.html#Base.wait"><code>wait</code></a> function blocks the calling task until some other task finishes. So for example if you type</p><pre><code class="language-julia-repl hljs">julia&gt; schedule(t); wait(t)</code></pre><p>instead of only calling <code>schedule</code>, you will see a five second pause before the next input prompt appears. That is because the REPL is waiting for <code>t</code> to finish before proceeding.</p><p>It is common to want to create a task and schedule it right away, so the macro <a href="../base/parallel.html#Base.@async"><code>@async</code></a> is provided for that purpose –- <code>@async x</code> is equivalent to <code>schedule(@task x)</code>.</p><h2 id="Communicating-with-Channels"><a class="docs-heading-anchor" href="#Communicating-with-Channels">Communicating with Channels</a><a id="Communicating-with-Channels-1"></a><a class="docs-heading-anchor-permalink" href="#Communicating-with-Channels" title="Permalink"></a></h2><p>In some problems, the various pieces of required work are not naturally related by function calls; there is no obvious &quot;caller&quot; or &quot;callee&quot; among the jobs that need to be done. An example is the producer-consumer problem, where one complex procedure is generating values and another complex procedure is consuming them. The consumer cannot simply call a producer function to get a value, because the producer may have more values to generate and so might not yet be ready to return. With tasks, the producer and consumer can both run as long as they need to, passing values back and forth as necessary.</p><p>Julia provides a <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a> mechanism for solving this problem. A <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a> is a waitable first-in first-out queue which can have multiple tasks reading from and writing to it.</p><p>Let&#39;s define a producer task, which produces values via the <a href="../base/parallel.html#Base.put!-Tuple{Channel, Any}"><code>put!</code></a> call. To consume values, we need to schedule the producer to run in a new task. A special <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a> constructor which accepts a 1-arg function as an argument can be used to run a task bound to a channel. We can then <a href="../base/io-network.html#Base.take!-Tuple{Base.GenericIOBuffer}"><code>take!</code></a> values repeatedly from the channel object:</p><pre><code class="language-julia-repl hljs">julia&gt; function producer(c::Channel)
           put!(c, &quot;start&quot;)
           for n=1:4
               put!(c, 2n)
           end
           put!(c, &quot;stop&quot;)
       end;

julia&gt; chnl = Channel(producer);

julia&gt; take!(chnl)
&quot;start&quot;

julia&gt; take!(chnl)
2

julia&gt; take!(chnl)
4

julia&gt; take!(chnl)
6

julia&gt; take!(chnl)
8

julia&gt; take!(chnl)
&quot;stop&quot;</code></pre><p>One way to think of this behavior is that <code>producer</code> was able to return multiple times. Between calls to <a href="../base/parallel.html#Base.put!-Tuple{Channel, Any}"><code>put!</code></a>, the producer&#39;s execution is suspended and the consumer has control.</p><p>The returned <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a> can be used as an iterable object in a <code>for</code> loop, in which case the loop variable takes on all the produced values. The loop is terminated when the channel is closed.</p><pre><code class="language-julia-repl hljs">julia&gt; for x in Channel(producer)
           println(x)
       end
start
2
4
6
8
stop</code></pre><p>Note that we did not have to explicitly close the channel in the producer. This is because the act of binding a <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a> to a <a href="../base/parallel.html#Core.Task"><code>Task</code></a> associates the open lifetime of a channel with that of the bound task. The channel object is closed automatically when the task terminates. Multiple channels can be bound to a task, and vice-versa.</p><p>While the <a href="../base/parallel.html#Core.Task"><code>Task</code></a> constructor expects a 0-argument function, the <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a> method that creates a task-bound channel expects a function that accepts a single argument of type <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a>. A common pattern is for the producer to be parameterized, in which case a partial function application is needed to create a 0 or 1 argument <a href="functions.html#man-anonymous-functions">anonymous function</a>.</p><p>For <a href="../base/parallel.html#Core.Task"><code>Task</code></a> objects this can be done either directly or by use of a convenience macro:</p><pre><code class="language-julia hljs">function mytask(myarg)
    ...
end

taskHdl = Task(() -&gt; mytask(7))
# or, equivalently
taskHdl = @task mytask(7)</code></pre><p>To orchestrate more advanced work distribution patterns, <a href="../stdlib/Sockets.html#Base.bind"><code>bind</code></a> and <a href="../base/parallel.html#Base.schedule"><code>schedule</code></a> can be used in conjunction with <a href="../base/parallel.html#Core.Task"><code>Task</code></a> and <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a> constructors to explicitly link a set of channels with a set of producer/consumer tasks.</p><h3 id="More-on-Channels"><a class="docs-heading-anchor" href="#More-on-Channels">More on Channels</a><a id="More-on-Channels-1"></a><a class="docs-heading-anchor-permalink" href="#More-on-Channels" title="Permalink"></a></h3><p>A channel can be visualized as a pipe, i.e., it has a write end and a read end :</p><ul><li><p>Multiple writers in different tasks can write to the same channel concurrently via <a href="../base/parallel.html#Base.put!-Tuple{Channel, Any}"><code>put!</code></a> calls.</p></li><li><p>Multiple readers in different tasks can read data concurrently via <a href="../base/io-network.html#Base.take!-Tuple{Base.GenericIOBuffer}"><code>take!</code></a> calls.</p></li><li><p>As an example:</p><pre><code class="language-julia hljs"># Given Channels c1 and c2,
c1 = Channel(32)
c2 = Channel(32)

# and a function `foo` which reads items from c1, processes the item read
# and writes a result to c2,
function foo()
    while true
        data = take!(c1)
        [...]               # process data
        put!(c2, result)    # write out result
    end
end

# we can schedule `n` instances of `foo` to be active concurrently.
for _ in 1:n
    errormonitor(@async foo())
end</code></pre></li><li><p>Channels are created via the <code>Channel{T}(sz)</code> constructor. The channel will only hold objects of type <code>T</code>. If the type is not specified, the channel can hold objects of any type. <code>sz</code> refers to the maximum number of elements that can be held in the channel at any time. For example, <code>Channel(32)</code> creates a channel that can hold a maximum of 32 objects of any type. A <code>Channel{MyType}(64)</code> can hold up to 64 objects of <code>MyType</code> at any time.</p></li><li><p>If a <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a> is empty, readers (on a <a href="../base/io-network.html#Base.take!-Tuple{Base.GenericIOBuffer}"><code>take!</code></a> call) will block until data is available.</p></li><li><p>If a <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a> is full, writers (on a <a href="../base/parallel.html#Base.put!-Tuple{Channel, Any}"><code>put!</code></a> call) will block until space becomes available.</p></li><li><p><a href="../base/parallel.html#Base.isready-Tuple{Channel}"><code>isready</code></a> tests for the presence of any object in the channel, while <a href="../base/parallel.html#Base.wait"><code>wait</code></a> waits for an object to become available.</p></li><li><p>A <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a> is in an open state initially. This means that it can be read from and written to freely via <a href="../base/io-network.html#Base.take!-Tuple{Base.GenericIOBuffer}"><code>take!</code></a> and <a href="../base/parallel.html#Base.put!-Tuple{Channel, Any}"><code>put!</code></a> calls. <a href="../base/io-network.html#Base.close"><code>close</code></a> closes a <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a>. On a closed <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a>, <a href="../base/parallel.html#Base.put!-Tuple{Channel, Any}"><code>put!</code></a> will fail. For example:</p><pre><code class="language-julia-repl hljs">julia&gt; c = Channel(2);

julia&gt; put!(c, 1) # `put!` on an open channel succeeds
1

julia&gt; close(c);

julia&gt; put!(c, 2) # `put!` on a closed channel throws an exception.
ERROR: InvalidStateException: Channel is closed.
Stacktrace:
[...]</code></pre></li><li><p><a href="../base/io-network.html#Base.take!-Tuple{Base.GenericIOBuffer}"><code>take!</code></a> and <a href="../base/parallel.html#Base.fetch-Tuple{Task}"><code>fetch</code></a> (which retrieves but does not remove the value) on a closed channel successfully return any existing values until it is emptied. Continuing the above example:</p><pre><code class="language-julia-repl hljs">julia&gt; fetch(c) # Any number of `fetch` calls succeed.
1

julia&gt; fetch(c)
1

julia&gt; take!(c) # The first `take!` removes the value.
1

julia&gt; take!(c) # No more data available on a closed channel.
ERROR: InvalidStateException: Channel is closed.
Stacktrace:
[...]</code></pre></li></ul><p>Consider a simple example using channels for inter-task communication. We start 4 tasks to process data from a single <code>jobs</code> channel. Jobs, identified by an id (<code>job_id</code>), are written to the channel. Each task in this simulation reads a <code>job_id</code>, waits for a random amount of time and writes back a tuple of <code>job_id</code> and the simulated time to the results channel. Finally all the <code>results</code> are printed out.</p><pre><code class="language-julia-repl hljs">julia&gt; const jobs = Channel{Int}(32);

julia&gt; const results = Channel{Tuple}(32);

julia&gt; function do_work()
           for job_id in jobs
               exec_time = rand()
               sleep(exec_time)                # simulates elapsed time doing actual work
                                               # typically performed externally.
               put!(results, (job_id, exec_time))
           end
       end;

julia&gt; function make_jobs(n)
           for i in 1:n
               put!(jobs, i)
           end
       end;

julia&gt; n = 12;

julia&gt; errormonitor(@async make_jobs(n)); # feed the jobs channel with &quot;n&quot; jobs

julia&gt; for i in 1:4 # start 4 tasks to process requests in parallel
           errormonitor(@async do_work())
       end

julia&gt; @elapsed while n &gt; 0 # print out results
           job_id, exec_time = take!(results)
           println(&quot;$job_id finished in $(round(exec_time; digits=2)) seconds&quot;)
           global n = n - 1
       end
4 finished in 0.22 seconds
3 finished in 0.45 seconds
1 finished in 0.5 seconds
7 finished in 0.14 seconds
2 finished in 0.78 seconds
5 finished in 0.9 seconds
9 finished in 0.36 seconds
6 finished in 0.87 seconds
8 finished in 0.79 seconds
10 finished in 0.64 seconds
12 finished in 0.5 seconds
11 finished in 0.97 seconds
0.029772311</code></pre><p>Instead of <code>errormonitor(t)</code>, a more robust solution may be to use <code>bind(results, t)</code>, as that will not only log any unexpected failures, but also force the associated resources to close and propagate the exception everywhere.</p><h2 id="More-task-operations"><a class="docs-heading-anchor" href="#More-task-operations">More task operations</a><a id="More-task-operations-1"></a><a class="docs-heading-anchor-permalink" href="#More-task-operations" title="Permalink"></a></h2><p>Task operations are built on a low-level primitive called <a href="../base/parallel.html#Base.yieldto"><code>yieldto</code></a>. <code>yieldto(task, value)</code> suspends the current task, switches to the specified <code>task</code>, and causes that task&#39;s last <a href="../base/parallel.html#Base.yieldto"><code>yieldto</code></a> call to return the specified <code>value</code>. Notice that <a href="../base/parallel.html#Base.yieldto"><code>yieldto</code></a> is the only operation required to use task-style control flow; instead of calling and returning we are always just switching to a different task. This is why this feature is also called &quot;symmetric coroutines&quot;; each task is switched to and from using the same mechanism.</p><p><a href="../base/parallel.html#Base.yieldto"><code>yieldto</code></a> is powerful, but most uses of tasks do not invoke it directly. Consider why this might be. If you switch away from the current task, you will probably want to switch back to it at some point, but knowing when to switch back, and knowing which task has the responsibility of switching back, can require considerable coordination. For example, <a href="../base/parallel.html#Base.put!-Tuple{Channel, Any}"><code>put!</code></a> and <a href="../base/io-network.html#Base.take!-Tuple{Base.GenericIOBuffer}"><code>take!</code></a> are blocking operations, which, when used in the context of channels maintain state to remember who the consumers are. Not needing to manually keep track of the consuming task is what makes <a href="../base/parallel.html#Base.put!-Tuple{Channel, Any}"><code>put!</code></a> easier to use than the low-level <a href="../base/parallel.html#Base.yieldto"><code>yieldto</code></a>.</p><p>In addition to <a href="../base/parallel.html#Base.yieldto"><code>yieldto</code></a>, a few other basic functions are needed to use tasks effectively.</p><ul><li><a href="../base/parallel.html#Base.current_task"><code>current_task</code></a> gets a reference to the currently-running task.</li><li><a href="../base/parallel.html#Base.istaskdone"><code>istaskdone</code></a> queries whether a task has exited.</li><li><a href="../base/parallel.html#Base.istaskstarted"><code>istaskstarted</code></a> queries whether a task has run yet.</li><li><a href="../base/parallel.html#Base.task_local_storage-Tuple{Any}"><code>task_local_storage</code></a> manipulates a key-value store specific to the current task.</li></ul><h2 id="Tasks-and-events"><a class="docs-heading-anchor" href="#Tasks-and-events">Tasks and events</a><a id="Tasks-and-events-1"></a><a class="docs-heading-anchor-permalink" href="#Tasks-and-events" title="Permalink"></a></h2><p>Most task switches occur as a result of waiting for events such as I/O requests, and are performed by a scheduler included in Julia Base. The scheduler maintains a queue of runnable tasks, and executes an event loop that restarts tasks based on external events such as message arrival.</p><p>The basic function for waiting for an event is <a href="../base/parallel.html#Base.wait"><code>wait</code></a>. Several objects implement <a href="../base/parallel.html#Base.wait"><code>wait</code></a>; for example, given a <code>Process</code> object, <a href="../base/parallel.html#Base.wait"><code>wait</code></a> will wait for it to exit. <a href="../base/parallel.html#Base.wait"><code>wait</code></a> is often implicit; for example, a <a href="../base/parallel.html#Base.wait"><code>wait</code></a> can happen inside a call to <a href="../base/io-network.html#Base.read"><code>read</code></a> to wait for data to be available.</p><p>In all of these cases, <a href="../base/parallel.html#Base.wait"><code>wait</code></a> ultimately operates on a <a href="../base/parallel.html#Base.Condition"><code>Condition</code></a> object, which is in charge of queueing and restarting tasks. When a task calls <a href="../base/parallel.html#Base.wait"><code>wait</code></a> on a <a href="../base/parallel.html#Base.Condition"><code>Condition</code></a>, the task is marked as non-runnable, added to the condition&#39;s queue, and switches to the scheduler. The scheduler will then pick another task to run, or block waiting for external events. If all goes well, eventually an event handler will call <a href="../base/parallel.html#Base.notify"><code>notify</code></a> on the condition, which causes tasks waiting for that condition to become runnable again.</p><p>A task created explicitly by calling <a href="../base/parallel.html#Core.Task"><code>Task</code></a> is initially not known to the scheduler. This allows you to manage tasks manually using <a href="../base/parallel.html#Base.yieldto"><code>yieldto</code></a> if you wish. However, when such a task waits for an event, it still gets restarted automatically when the event happens, as you would expect.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="parallel-computing.html">« Parallel Computing</a><a class="docs-footer-nextpage" href="multi-threading.html">Multi-Threading »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
