<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Missing Values · The Julia Language</title><meta name="title" content="Missing Values · The Julia Language"/><meta property="og:title" content="Missing Values · The Julia Language"/><meta property="twitter:title" content="Missing Values · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li class="is-active"><a class="tocitem" href="missing.html">Missing Values</a><ul class="internal"><li><a class="tocitem" href="#Propagation-of-Missing-Values"><span>Propagation of Missing Values</span></a></li><li><a class="tocitem" href="#Equality-and-Comparison-Operators"><span>Equality and Comparison Operators</span></a></li><li><a class="tocitem" href="#Logical-operators"><span>Logical operators</span></a></li><li><a class="tocitem" href="#Control-Flow-and-Short-Circuiting-Operators"><span>Control Flow and Short-Circuiting Operators</span></a></li><li><a class="tocitem" href="#Arrays-With-Missing-Values"><span>Arrays With Missing Values</span></a></li><li><a class="tocitem" href="#Skipping-Missing-Values"><span>Skipping Missing Values</span></a></li><li><a class="tocitem" href="#Logical-Operations-on-Arrays"><span>Logical Operations on Arrays</span></a></li></ul></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="missing.html">Missing Values</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="missing.html">Missing Values</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/missing.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="missing"><a class="docs-heading-anchor" href="#missing">Missing Values</a><a id="missing-1"></a><a class="docs-heading-anchor-permalink" href="#missing" title="Permalink"></a></h1><p>Julia provides support for representing missing values in the statistical sense. This is for situations where no value is available for a variable in an observation, but a valid value theoretically exists. Missing values are represented via the <a href="missing.html#missing"><code>missing</code></a> object, which is the singleton instance of the type <a href="../base/base.html#Base.Missing"><code>Missing</code></a>. <code>missing</code> is equivalent to <a href="https://en.wikipedia.org/wiki/NULL_(SQL)"><code>NULL</code> in SQL</a> and <a href="https://cran.r-project.org/doc/manuals/r-release/R-lang.html#NA-handling"><code>NA</code> in R</a>, and behaves like them in most situations.</p><h2 id="Propagation-of-Missing-Values"><a class="docs-heading-anchor" href="#Propagation-of-Missing-Values">Propagation of Missing Values</a><a id="Propagation-of-Missing-Values-1"></a><a class="docs-heading-anchor-permalink" href="#Propagation-of-Missing-Values" title="Permalink"></a></h2><p><code>missing</code> values <em>propagate</em> automatically when passed to standard mathematical operators and functions. For these functions, uncertainty about the value of one of the operands induces uncertainty about the result. In practice, this means a math operation involving a <code>missing</code> value generally returns <code>missing</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; missing + 1
missing

julia&gt; &quot;a&quot; * missing
missing

julia&gt; abs(missing)
missing</code></pre><p>Since <code>missing</code> is a normal Julia object, this propagation rule only works for functions which have opted in to implement this behavior. This can be achieved by:</p><ul><li>adding a specific method defined for arguments of type <code>Missing</code>,</li><li>accepting arguments of this type, and passing them to functions which propagate them (like standard math operators).</li></ul><p>Packages should consider whether it makes sense to propagate missing values when defining new functions, and define methods appropriately if this is the case. Passing a <code>missing</code> value to a function which does not have a method accepting arguments of type <code>Missing</code> throws a <a href="../base/base.html#Core.MethodError"><code>MethodError</code></a>, just like for any other type.</p><p>Functions that do not propagate <code>missing</code> values can be made to do so by wrapping them in the <code>passmissing</code> function provided by the <a href="https://github.com/JuliaData/Missings.jl">Missings.jl</a> package. For example, <code>f(x)</code> becomes <code>passmissing(f)(x)</code>.</p><h2 id="Equality-and-Comparison-Operators"><a class="docs-heading-anchor" href="#Equality-and-Comparison-Operators">Equality and Comparison Operators</a><a id="Equality-and-Comparison-Operators-1"></a><a class="docs-heading-anchor-permalink" href="#Equality-and-Comparison-Operators" title="Permalink"></a></h2><p>Standard equality and comparison operators follow the propagation rule presented above: if any of the operands is <code>missing</code>, the result is <code>missing</code>. Here are a few examples:</p><pre><code class="language-julia-repl hljs">julia&gt; missing == 1
missing

julia&gt; missing == missing
missing

julia&gt; missing &lt; 1
missing

julia&gt; 2 &gt;= missing
missing</code></pre><p>In particular, note that <code>missing == missing</code> returns <code>missing</code>, so <code>==</code> cannot be used to test whether a value is missing. To test whether <code>x</code> is <code>missing</code>, use <a href="../base/base.html#Base.ismissing"><code>ismissing(x)</code></a>.</p><p>Special comparison operators <a href="../base/base.html#Base.isequal"><code>isequal</code></a> and <a href="../base/base.html#Core.:==="><code>===</code></a> are exceptions to the propagation rule. They will always return a <code>Bool</code> value, even in the presence of <code>missing</code> values, considering <code>missing</code> as equal to <code>missing</code> and as different from any other value. They can therefore be used to test whether a value is <code>missing</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; missing === 1
false

julia&gt; isequal(missing, 1)
false

julia&gt; missing === missing
true

julia&gt; isequal(missing, missing)
true</code></pre><p>The <a href="../base/base.html#Base.isless"><code>isless</code></a> operator is another exception: <code>missing</code> is considered as greater than any other value. This operator is used by <a href="../base/sort.html#Base.sort!"><code>sort!</code></a>, which therefore places <code>missing</code> values after all other values:</p><pre><code class="language-julia-repl hljs">julia&gt; isless(1, missing)
true

julia&gt; isless(missing, Inf)
false

julia&gt; isless(missing, missing)
false</code></pre><h2 id="Logical-operators"><a class="docs-heading-anchor" href="#Logical-operators">Logical operators</a><a id="Logical-operators-1"></a><a class="docs-heading-anchor-permalink" href="#Logical-operators" title="Permalink"></a></h2><p>Logical (or boolean) operators <a href="../base/math.html#Base.:|"><code>|</code></a>, <a href="../base/math.html#Base.:&amp;"><code>&amp;</code></a> and <a href="../base/math.html#Base.xor"><code>xor</code></a> are another special case since they only propagate <code>missing</code> values when it is logically required. For these operators, whether or not the result is uncertain, depends on the particular operation. This follows the well-established rules of <a href="https://en.wikipedia.org/wiki/Three-valued_logic"><em>three-valued logic</em></a> which are implemented by e.g. <code>NULL</code> in SQL and <code>NA</code> in R. This abstract definition corresponds to a relatively natural behavior which is best explained via concrete examples.</p><p>Let us illustrate this principle with the logical &quot;or&quot; operator <a href="../base/math.html#Base.:|"><code>|</code></a>. Following the rules of boolean logic, if one of the operands is <code>true</code>, the value of the other operand does not have an influence on the result, which will always be <code>true</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; true | true
true

julia&gt; true | false
true

julia&gt; false | true
true</code></pre><p>Based on this observation, we can conclude if one of the operands is <code>true</code> and the other <code>missing</code>, we know that the result is <code>true</code> in spite of the uncertainty about the actual value of one of the operands. If we had been able to observe the actual value of the second operand, it could only be <code>true</code> or <code>false</code>, and in both cases the result would be <code>true</code>. Therefore, in this particular case, missingness does <em>not</em> propagate:</p><pre><code class="language-julia-repl hljs">julia&gt; true | missing
true

julia&gt; missing | true
true</code></pre><p>On the contrary, if one of the operands is <code>false</code>, the result could be either <code>true</code> or <code>false</code> depending on the value of the other operand. Therefore, if that operand is <code>missing</code>, the result has to be <code>missing</code> too:</p><pre><code class="language-julia-repl hljs">julia&gt; false | true
true

julia&gt; true | false
true

julia&gt; false | false
false

julia&gt; false | missing
missing

julia&gt; missing | false
missing</code></pre><p>The behavior of the logical &quot;and&quot; operator <a href="../base/math.html#Base.:&amp;"><code>&amp;</code></a> is similar to that of the <code>|</code> operator, with the difference that missingness does not propagate when one of the operands is <code>false</code>. For example, when that is the case of the first operand:</p><pre><code class="language-julia-repl hljs">julia&gt; false &amp; false
false

julia&gt; false &amp; true
false

julia&gt; false &amp; missing
false</code></pre><p>On the other hand, missingness propagates when one of the operands is <code>true</code>, for example the first one:</p><pre><code class="language-julia-repl hljs">julia&gt; true &amp; true
true

julia&gt; true &amp; false
false

julia&gt; true &amp; missing
missing</code></pre><p>Finally, the &quot;exclusive or&quot; logical operator <a href="../base/math.html#Base.xor"><code>xor</code></a> always propagates <code>missing</code> values, since both operands always have an effect on the result. Also note that the negation operator <a href="../base/math.html#Base.:!"><code>!</code></a> returns <code>missing</code> when the operand is <code>missing</code>, just like other unary operators.</p><h2 id="Control-Flow-and-Short-Circuiting-Operators"><a class="docs-heading-anchor" href="#Control-Flow-and-Short-Circuiting-Operators">Control Flow and Short-Circuiting Operators</a><a id="Control-Flow-and-Short-Circuiting-Operators-1"></a><a class="docs-heading-anchor-permalink" href="#Control-Flow-and-Short-Circuiting-Operators" title="Permalink"></a></h2><p>Control flow operators including <a href="../base/base.html#if"><code>if</code></a>, <a href="../base/base.html#while"><code>while</code></a> and the <a href="control-flow.html#man-conditional-evaluation">ternary operator</a> <code>x ? y : z</code> do not allow for missing values. This is because of the uncertainty about whether the actual value would be <code>true</code> or <code>false</code> if we could observe it. This implies we do not know how the program should behave. In this case, a <a href="../base/base.html#Core.TypeError"><code>TypeError</code></a> is thrown as soon as a <code>missing</code> value is encountered in this context:</p><pre><code class="language-julia-repl hljs">julia&gt; if missing
           println(&quot;here&quot;)
       end
ERROR: TypeError: non-boolean (Missing) used in boolean context</code></pre><p>For the same reason, contrary to logical operators presented above, the short-circuiting boolean operators <a href="../base/math.html#&amp;&amp;"><code>&amp;&amp;</code></a> and <a href="../base/math.html#||"><code>||</code></a> do not allow for <code>missing</code> values in situations where the value of the operand determines whether the next operand is evaluated or not. For example:</p><pre><code class="language-julia-repl hljs">julia&gt; missing || false
ERROR: TypeError: non-boolean (Missing) used in boolean context

julia&gt; missing &amp;&amp; false
ERROR: TypeError: non-boolean (Missing) used in boolean context

julia&gt; true &amp;&amp; missing &amp;&amp; false
ERROR: TypeError: non-boolean (Missing) used in boolean context</code></pre><p>In contrast, there is no error thrown when the result can be determined without the <code>missing</code> values. This is the case when the code short-circuits before evaluating the <code>missing</code> operand, and when the <code>missing</code> operand is the last one:</p><pre><code class="language-julia-repl hljs">julia&gt; true &amp;&amp; missing
missing

julia&gt; false &amp;&amp; missing
false</code></pre><h2 id="Arrays-With-Missing-Values"><a class="docs-heading-anchor" href="#Arrays-With-Missing-Values">Arrays With Missing Values</a><a id="Arrays-With-Missing-Values-1"></a><a class="docs-heading-anchor-permalink" href="#Arrays-With-Missing-Values" title="Permalink"></a></h2><p>Arrays containing missing values can be created like other arrays:</p><pre><code class="language-julia-repl hljs">julia&gt; [1, missing]
2-element Vector{Union{Missing, Int64}}:
 1
  missing</code></pre><p>As this example shows, the element type of such arrays is <code>Union{Missing, T}</code>, with <code>T</code> the type of the non-missing values. This reflects the fact that array entries can be either of type <code>T</code> (here, <code>Int64</code>) or of type <code>Missing</code>. This kind of array uses an efficient memory storage equivalent to an <code>Array{T}</code> holding the actual values combined with an <code>Array{UInt8}</code> indicating the type of the entry (i.e. whether it is <code>Missing</code> or <code>T</code>).</p><p>Arrays allowing for missing values can be constructed with the standard syntax. Use <code>Array{Union{Missing, T}}(missing, dims)</code> to create arrays filled with missing values:</p><pre><code class="language-julia-repl hljs">julia&gt; Array{Union{Missing, String}}(missing, 2, 3)
2×3 Matrix{Union{Missing, String}}:
 missing  missing  missing
 missing  missing  missing</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Using <code>undef</code> or <code>similar</code> may currently give an array filled with <code>missing</code>, but this is not the correct way to obtain such an array. Use a <code>missing</code> constructor as shown above instead.</p></div></div><p>An array with element type allowing <code>missing</code> entries (e.g. <code>Vector{Union{Missing, T}}</code>) which does not contain any <code>missing</code> entries can be converted to an array type that does not allow for <code>missing</code> entries (e.g. <code>Vector{T}</code>) using <a href="../base/base.html#Base.convert"><code>convert</code></a>. If the array contains <code>missing</code> values, a <code>MethodError</code> is thrown during conversion:</p><pre><code class="language-julia-repl hljs">julia&gt; x = Union{Missing, String}[&quot;a&quot;, &quot;b&quot;]
2-element Vector{Union{Missing, String}}:
 &quot;a&quot;
 &quot;b&quot;

julia&gt; convert(Array{String}, x)
2-element Vector{String}:
 &quot;a&quot;
 &quot;b&quot;

julia&gt; y = Union{Missing, String}[missing, &quot;b&quot;]
2-element Vector{Union{Missing, String}}:
 missing
 &quot;b&quot;

julia&gt; convert(Array{String}, y)
ERROR: MethodError: Cannot `convert` an object of type Missing to an object of type String</code></pre><h2 id="Skipping-Missing-Values"><a class="docs-heading-anchor" href="#Skipping-Missing-Values">Skipping Missing Values</a><a id="Skipping-Missing-Values-1"></a><a class="docs-heading-anchor-permalink" href="#Skipping-Missing-Values" title="Permalink"></a></h2><p>Since <code>missing</code> values propagate with standard mathematical operators, reduction functions return <code>missing</code> when called on arrays which contain missing values:</p><pre><code class="language-julia-repl hljs">julia&gt; sum([1, missing])
missing</code></pre><p>In this situation, use the <a href="../base/base.html#Base.skipmissing"><code>skipmissing</code></a> function to skip missing values:</p><pre><code class="language-julia-repl hljs">julia&gt; sum(skipmissing([1, missing]))
1</code></pre><p>This convenience function returns an iterator which filters out <code>missing</code> values efficiently. It can therefore be used with any function which supports iterators:</p><pre><code class="language-julia-repl hljs">julia&gt; x = skipmissing([3, missing, 2, 1])
skipmissing(Union{Missing, Int64}[3, missing, 2, 1])

julia&gt; maximum(x)
3

julia&gt; sum(x)
6

julia&gt; mapreduce(sqrt, +, x)
4.146264369941973</code></pre><p>Objects created by calling <code>skipmissing</code> on an array can be indexed using indices from the parent array. Indices corresponding to missing values are not valid for these objects, and an error is thrown when trying to use them (they are also skipped by <code>keys</code> and <code>eachindex</code>):</p><pre><code class="language-julia-repl hljs">julia&gt; x[1]
3

julia&gt; x[2]
ERROR: MissingException: the value at index (2,) is missing
[...]</code></pre><p>This allows functions which operate on indices to work in combination with <code>skipmissing</code>. This is notably the case for search and find functions. These functions return indices valid for the object returned by <code>skipmissing</code>, and are also the indices of the matching entries <em>in the parent array</em>:</p><pre><code class="language-julia-repl hljs">julia&gt; findall(==(1), x)
1-element Vector{Int64}:
 4

julia&gt; findfirst(!iszero, x)
1

julia&gt; argmax(x)
1</code></pre><p>Use <a href="../base/collections.html#Base.collect-Tuple{Any}"><code>collect</code></a> to extract non-<code>missing</code> values and store them in an array:</p><pre><code class="language-julia-repl hljs">julia&gt; collect(x)
3-element Vector{Int64}:
 3
 2
 1</code></pre><h2 id="Logical-Operations-on-Arrays"><a class="docs-heading-anchor" href="#Logical-Operations-on-Arrays">Logical Operations on Arrays</a><a id="Logical-Operations-on-Arrays-1"></a><a class="docs-heading-anchor-permalink" href="#Logical-Operations-on-Arrays" title="Permalink"></a></h2><p>The three-valued logic described above for logical operators is also used by logical functions applied to arrays. Thus, array equality tests using the <a href="../base/math.html#Base.:=="><code>==</code></a> operator return <code>missing</code> whenever the result cannot be determined without knowing the actual value of the <code>missing</code> entry. In practice, this means <code>missing</code> is returned if all non-missing values of the compared arrays are equal, but one or both arrays contain missing values (possibly at different positions):</p><pre><code class="language-julia-repl hljs">julia&gt; [1, missing] == [2, missing]
false

julia&gt; [1, missing] == [1, missing]
missing

julia&gt; [1, 2, missing] == [1, missing, 2]
missing</code></pre><p>As for single values, use <a href="../base/base.html#Base.isequal"><code>isequal</code></a> to treat <code>missing</code> values as equal to other <code>missing</code> values, but different from non-missing values:</p><pre><code class="language-julia-repl hljs">julia&gt; isequal([1, missing], [1, missing])
true

julia&gt; isequal([1, 2, missing], [1, missing, 2])
false</code></pre><p>Functions <a href="../base/collections.html#Base.any-Tuple{Any}"><code>any</code></a> and <a href="../base/collections.html#Base.all-Tuple{Any}"><code>all</code></a> also follow the rules of three-valued logic. Thus, returning <code>missing</code> when the result cannot be determined:</p><pre><code class="language-julia-repl hljs">julia&gt; all([true, missing])
missing

julia&gt; all([false, missing])
false

julia&gt; any([true, missing])
true

julia&gt; any([false, missing])
missing</code></pre></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="arrays.html">« Single- and multi-dimensional Arrays</a><a class="docs-footer-nextpage" href="networking-and-streams.html">Networking and Streams »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
