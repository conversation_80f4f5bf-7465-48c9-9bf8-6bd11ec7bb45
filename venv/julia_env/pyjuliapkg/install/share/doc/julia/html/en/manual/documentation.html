<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Documentation · The Julia Language</title><meta name="title" content="Documentation · The Julia Language"/><meta property="og:title" content="Documentation · The Julia Language"/><meta property="twitter:title" content="Documentation · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li class="is-active"><a class="tocitem" href="documentation.html">Documentation</a><ul class="internal"><li><a class="tocitem" href="#Accessing-Documentation"><span>Accessing Documentation</span></a></li><li><a class="tocitem" href="#Writing-Documentation"><span>Writing Documentation</span></a></li><li><a class="tocitem" href="#Functions-and-Methods"><span>Functions &amp; Methods</span></a></li><li><a class="tocitem" href="#Advanced-Usage"><span>Advanced Usage</span></a></li><li><a class="tocitem" href="#Syntax-Guide"><span>Syntax Guide</span></a></li></ul></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="documentation.html">Documentation</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="documentation.html">Documentation</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/documentation.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="man-documentation"><a class="docs-heading-anchor" href="#man-documentation">Documentation</a><a id="man-documentation-1"></a><a class="docs-heading-anchor-permalink" href="#man-documentation" title="Permalink"></a></h1><h2 id="Accessing-Documentation"><a class="docs-heading-anchor" href="#Accessing-Documentation">Accessing Documentation</a><a id="Accessing-Documentation-1"></a><a class="docs-heading-anchor-permalink" href="#Accessing-Documentation" title="Permalink"></a></h2><p>Documentation can be accessed at the REPL or in <a href="https://github.com/JuliaLang/IJulia.jl">IJulia</a> by typing <code>?</code> followed by the name of a function or macro, and pressing <code>Enter</code>. For example,</p><pre><code class="language-julia hljs">?cos
?@time
?r&quot;&quot;</code></pre><p>will show documentation for the relevant function, macro or string macro respectively. Most Julia environments provide a way to access documentation directly:</p><ul><li><a href="https://www.julia-vscode.org/">VS Code</a> shows documentation when you hover over a function name. You can also use the Julia panel in the sidebar to search for documentation.</li><li>In <a href="https://github.com/fonsp/Pluto.jl">Pluto</a>, open the &quot;Live Docs&quot; panel on the bottom right.</li><li>In <a href="https://junolab.org">Juno</a> using <code>Ctrl-J, Ctrl-D</code> will show the documentation for the object under the cursor.</li></ul><p><code>Docs.hasdoc(module, name)::Bool</code> tells whether a name has a docstring. <code>Docs.undocumented_names(module; all)</code> returns the undocumented names in a module.</p><h2 id="Writing-Documentation"><a class="docs-heading-anchor" href="#Writing-Documentation">Writing Documentation</a><a id="Writing-Documentation-1"></a><a class="docs-heading-anchor-permalink" href="#Writing-Documentation" title="Permalink"></a></h2><p>Julia enables package developers and users to document functions, types and other objects easily via a built-in documentation system.</p><p>The basic syntax is simple: any string appearing just before an object (function, macro, type or instance) will be interpreted as documenting it (these are called <em>docstrings</em>). Note that no blank lines or comments may intervene between a docstring and the documented object. Here is a basic example:</p><pre><code class="language-julia hljs">&quot;Tell whether there are too foo items in the array.&quot;
foo(xs::Array) = ...</code></pre><p>Documentation is interpreted as <a href="https://en.wikipedia.org/wiki/Markdown">Markdown</a>, so you can use indentation and code fences to delimit code examples from text. Technically, any object can be associated with any other as metadata; Markdown happens to be the default, but one can construct other string macros and pass them to the <code>@doc</code> macro just as well.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Markdown support is implemented in the <code>Markdown</code> standard library and for a full list of supported syntax see the <a href="../stdlib/Markdown.html#markdown_stdlib">documentation</a>.</p></div></div><p>Here is a more complex example, still using Markdown:</p><pre><code class="language-julia hljs">&quot;&quot;&quot;
    bar(x[, y])

Compute the Bar index between `x` and `y`.

If `y` is unspecified, compute the Bar index between all pairs of columns of `x`.

# Examples
```julia-repl
julia&gt; bar([1, 2], [1, 2])
1
```
&quot;&quot;&quot;
function bar(x, y) ...</code></pre><p>As in the example above, we recommend following some simple conventions when writing documentation:</p><ol><li><p>Always show the signature of a function at the top of the documentation, with a four-space indent so that it is printed as Julia code.</p><p>This can be identical to the signature present in the Julia code (like <code>mean(x::AbstractArray)</code>), or a simplified form. Optional arguments should be represented with their default values (i.e. <code>f(x, y=1)</code>) when possible, following the actual Julia syntax. Optional arguments which do not have a default value should be put in brackets (i.e. <code>f(x[, y])</code> and <code>f(x[, y[, z]])</code>). An alternative solution is to use several lines: one without optional arguments, the other(s) with them. This solution can also be used to document several related methods of a given function. When a function accepts many keyword arguments, only include a <code>&lt;keyword arguments&gt;</code> placeholder in the signature (i.e. <code>f(x; &lt;keyword arguments&gt;)</code>), and give the complete list under an <code># Arguments</code> section (see point 4 below).</p></li><li><p>Include a single one-line sentence describing what the function does or what the object represents after the simplified signature block. If needed, provide more details in a second paragraph, after a blank line.</p><p>The one-line sentence should use the imperative form (&quot;Do this&quot;, &quot;Return that&quot;) instead of the third person (do not write &quot;Returns the length...&quot;) when documenting functions. It should end with a period. If the meaning of a function cannot be summarized easily, splitting it into separate composable parts could be beneficial (this should not be taken as an absolute requirement for every single case though).</p></li><li><p>Do not repeat yourself.</p><p>Since the function name is given by the signature, there is no need to start the documentation with &quot;The function <code>bar</code>...&quot;: go straight to the point. Similarly, if the signature specifies the types of the arguments, mentioning them in the description is redundant.</p></li><li><p>Only provide an argument list when really necessary.</p><p>For simple functions, it is often clearer to mention the role of the arguments directly in the description of the function&#39;s purpose. An argument list would only repeat information already provided elsewhere. However, providing an argument list can be a good idea for complex functions with many arguments (in particular keyword arguments). In that case, insert it after the general description of the function, under an <code># Arguments</code> header, with one <code>-</code> bullet for each argument. The list should mention the types and default values (if any) of the arguments:</p><pre><code class="language-julia hljs">&quot;&quot;&quot;
...
# Arguments
- `n::Integer`: the number of elements to compute.
- `dim::Integer=1`: the dimensions along which to perform the computation.
...
&quot;&quot;&quot;</code></pre></li><li><p>Provide hints to related functions.</p><p>Sometimes there are functions of related functionality. To increase discoverability please provide a short list of these in a <code>See also</code> paragraph.</p><pre><code class="nohighlight hljs">See also [`bar!`](@ref), [`baz`](@ref), [`baaz`](@ref).</code></pre></li><li><p>Include any code examples in an <code># Examples</code> section.</p><p>Examples should, whenever possible, be written as <em>doctests</em>. A <em>doctest</em> is a fenced code block (see <a href="../stdlib/Markdown.html#Code-blocks">Code blocks</a>) starting with <code>```jldoctest</code> and contains any number of <code>julia&gt;</code> prompts together with inputs and expected outputs that mimic the Julia REPL.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Doctests are enabled by <a href="https://github.com/JuliaDocs/Documenter.jl"><code>Documenter.jl</code></a>. For more detailed documentation see Documenter&#39;s <a href="https://juliadocs.github.io/Documenter.jl/">manual</a>.</p></div></div><p>For example in the following docstring a variable <code>a</code> is defined and the expected result, as printed in a Julia REPL, appears afterwards:</p><pre><code class="language-julia hljs">&quot;&quot;&quot;
Some nice documentation here.

# Examples
```jldoctest
julia&gt; a = [1 2; 3 4]
2×2 Array{Int64,2}:
 1  2
 3  4
```
&quot;&quot;&quot;</code></pre><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Calling <code>rand</code> and other RNG-related functions should be avoided in doctests since they will not produce consistent outputs during different Julia sessions. If you would like to show some random number generation related functionality, one option is to explicitly construct and seed your own RNG object (see <a href="../stdlib/Random.html#Random-Numbers"><code>Random</code></a>) and pass it to the functions you are doctesting.</p><p>Operating system word size (<a href="../base/numbers.html#Core.Int32"><code>Int32</code></a> or <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>) as well as path separator differences (<code>/</code> or <code>\</code>) will also affect the reproducibility of some doctests.</p><p>Note that whitespace in your doctest is significant! The doctest will fail if you misalign the output of pretty-printing an array, for example.</p></div></div><p>You can then run <code>make -C doc doctest=true</code> to run all the doctests in the Julia Manual and API documentation, which will ensure that your example works.</p><p>To indicate that the output result is truncated, you may write <code>[...]</code> at the line where checking should stop. This is useful to hide a stacktrace (which contains non-permanent references to lines of julia code) when the doctest shows that an exception is thrown, for example:</p><pre><code class="language-julia hljs">```jldoctest
julia&gt; div(1, 0)
ERROR: DivideError: integer division error
[...]
```</code></pre><p>Examples that are untestable should be written within fenced code blocks starting with <code>```julia</code> so that they are highlighted correctly in the generated documentation.</p><div class="admonition is-success"><header class="admonition-header">Tip</header><div class="admonition-body"><p>Wherever possible examples should be <strong>self-contained</strong> and <strong>runnable</strong> so that readers are able to try them out without having to include any dependencies.</p></div></div></li><li><p>Use backticks to identify code and equations.</p><p>Julia identifiers and code excerpts should always appear between backticks <code>`</code> to enable highlighting. Equations in the LaTeX syntax can be inserted between double backticks <code>``</code>. Use Unicode characters rather than their LaTeX escape sequence, i.e. <code>``α = 1``</code> rather than <code>``\\alpha = 1``</code>.</p></li><li><p>Place the starting and ending <code>&quot;&quot;&quot;</code> characters on lines by themselves.</p><p>That is, write:</p><pre><code class="language-julia hljs">&quot;&quot;&quot;
...

...
&quot;&quot;&quot;
f(x, y) = ...</code></pre><p>rather than:</p><pre><code class="language-julia hljs">&quot;&quot;&quot;...

...&quot;&quot;&quot;
f(x, y) = ...</code></pre><p>This makes it clearer where docstrings start and end.</p></li><li><p>Respect the line length limit used in the surrounding code.</p><p>Docstrings are edited using the same tools as code. Therefore, the same conventions should apply. It is recommended that lines are at most 92 characters wide.</p></li><li><p>Provide information allowing custom types to implement the function in an <code># Implementation</code> section. These implementation details are intended for developers rather than users, explaining e.g. which functions should be overridden and which functions automatically use appropriate fallbacks. Such details are best kept separate from the main description of the function&#39;s behavior.</p></li><li><p>For long docstrings, consider splitting the documentation with an <code># Extended help</code> header. The typical help-mode will show only the material above the header; you can access the full help by adding a &#39;?&#39; at the beginning of the expression (i.e., &quot;??foo&quot; rather than &quot;?foo&quot;).</p></li></ol><h2 id="Functions-and-Methods"><a class="docs-heading-anchor" href="#Functions-and-Methods">Functions &amp; Methods</a><a id="Functions-and-Methods-1"></a><a class="docs-heading-anchor-permalink" href="#Functions-and-Methods" title="Permalink"></a></h2><p>Functions in Julia may have multiple implementations, known as methods. While it&#39;s good practice for generic functions to have a single purpose, Julia allows methods to be documented individually if necessary. In general, only the most generic method should be documented, or even the function itself (i.e. the object created without any methods by <code>function bar end</code>). Specific methods should only be documented if their behaviour differs from the more generic ones. In any case, they should not repeat the information provided elsewhere. For example:</p><pre><code class="language-julia hljs">&quot;&quot;&quot;
    *(x, y, z...)

Multiplication operator. `x * y * z *...` calls this function with multiple
arguments, i.e. `*(x, y, z...)`.
&quot;&quot;&quot;
function *(x, y, z...)
    # ... [implementation sold separately] ...
end

&quot;&quot;&quot;
    *(x::AbstractString, y::AbstractString, z::AbstractString...)

When applied to strings, concatenates them.
&quot;&quot;&quot;
function *(x::AbstractString, y::AbstractString, z::AbstractString...)
    # ... [insert secret sauce here] ...
end

help?&gt; *
search: * .*

  *(x, y, z...)

  Multiplication operator. x * y * z *... calls this function with multiple
  arguments, i.e. *(x,y,z...).

  *(x::AbstractString, y::AbstractString, z::AbstractString...)

  When applied to strings, concatenates them.</code></pre><p>When retrieving documentation for a generic function, the metadata for each method is concatenated with the <code>catdoc</code> function, which can of course be overridden for custom types.</p><h2 id="Advanced-Usage"><a class="docs-heading-anchor" href="#Advanced-Usage">Advanced Usage</a><a id="Advanced-Usage-1"></a><a class="docs-heading-anchor-permalink" href="#Advanced-Usage" title="Permalink"></a></h2><p>The <code>@doc</code> macro associates its first argument with its second in a per-module dictionary called <code>META</code>.</p><p>To make it easier to write documentation, the parser treats the macro name <code>@doc</code> specially: if a call to <code>@doc</code> has one argument, but another expression appears after a single line break, then that additional expression is added as an argument to the macro. Therefore the following syntax is parsed as a 2-argument call to <code>@doc</code>:</p><pre><code class="language-julia hljs">@doc raw&quot;&quot;&quot;
...
&quot;&quot;&quot;
f(x) = x</code></pre><p>This makes it possible to use expressions other than normal string literals (such as the <code>raw&quot;&quot;</code> string macro) as a docstring.</p><p>When used for retrieving documentation, the <code>@doc</code> macro (or equally, the <code>doc</code> function) will search all <code>META</code> dictionaries for metadata relevant to the given object and return it. The returned object (some Markdown content, for example) will by default display itself intelligently. This design also makes it easy to use the doc system in a programmatic way; for example, to re-use documentation between different versions of a function:</p><pre><code class="language-julia hljs">@doc &quot;...&quot; foo!
@doc (@doc foo!) foo</code></pre><p>Or for use with Julia&#39;s metaprogramming functionality:</p><pre><code class="language-julia hljs">for (f, op) in ((:add, :+), (:subtract, :-), (:multiply, :*), (:divide, :/))
    @eval begin
        $f(a, b) = $op(a, b)
    end
end
@doc &quot;`add(a, b)` adds `a` and `b` together&quot; add
@doc &quot;`subtract(a, b)` subtracts `b` from `a`&quot; subtract</code></pre><p>Documentation in non-toplevel blocks, such as <code>begin</code>, <code>if</code>, <code>for</code>, <code>let</code>, and inner constructors, should be added to the documentation system via <code>@doc</code> as well. For example:</p><pre><code class="language-julia hljs">if condition()
    @doc &quot;...&quot;
    f(x) = x
end</code></pre><p>will add documentation to <code>f(x)</code> when <code>condition()</code> is <code>true</code>. Note that even if <code>f(x)</code> goes out of scope at the end of a block, its documentation will remain.</p><p>It is possible to make use of metaprogramming to assist in the creation of documentation. When using string-interpolation within the docstring you will need to use an extra <code>$</code> as shown with <code>$($name)</code>:</p><pre><code class="language-julia hljs">for func in (:day, :dayofmonth)
    name = string(func)
    @eval begin
        @doc &quot;&quot;&quot;
            $($name)(dt::TimeType) -&gt; Int64

        The day of month of a `Date` or `DateTime` as an `Int64`.
        &quot;&quot;&quot; $func(dt::Dates.TimeType)
    end
end</code></pre><h3 id="Dynamic-documentation"><a class="docs-heading-anchor" href="#Dynamic-documentation">Dynamic documentation</a><a id="Dynamic-documentation-1"></a><a class="docs-heading-anchor-permalink" href="#Dynamic-documentation" title="Permalink"></a></h3><p>Sometimes the appropriate documentation for an instance of a type depends on the field values of that instance, rather than just on the type itself. In these cases, you can add a method to <code>Docs.getdoc</code> for your custom type that returns the documentation on a per-instance basis. For instance,</p><pre><code class="language-julia hljs">struct MyType
    value::Int
end

Docs.getdoc(t::MyType) = &quot;Documentation for MyType with value $(t.value)&quot;

x = MyType(1)
y = MyType(2)</code></pre><p><code>?x</code> will display &quot;Documentation for MyType with value 1&quot; while <code>?y</code> will display &quot;Documentation for MyType with value 2&quot;.</p><h2 id="Syntax-Guide"><a class="docs-heading-anchor" href="#Syntax-Guide">Syntax Guide</a><a id="Syntax-Guide-1"></a><a class="docs-heading-anchor-permalink" href="#Syntax-Guide" title="Permalink"></a></h2><p>This guide provides a comprehensive overview of how to attach documentation to all Julia syntax constructs for which providing documentation is possible.</p><p>In the following examples <code>&quot;...&quot;</code> is used to illustrate an arbitrary docstring.</p><h3 id="and-\\-characters"><a class="docs-heading-anchor" href="#and-\\-characters"><code>$</code> and <code>\</code> characters</a><a id="and-\\-characters-1"></a><a class="docs-heading-anchor-permalink" href="#and-\\-characters" title="Permalink"></a></h3><p>The <code>$</code> and <code>\</code> characters are still parsed as string interpolation or start of an escape sequence in docstrings too. The <code>raw&quot;&quot;</code> string macro together with the <code>@doc</code> macro can be used to avoid having to escape them. This is handy when the docstrings include LaTeX or Julia source code examples containing interpolation:</p><pre><code class="language-julia hljs">@doc raw&quot;&quot;&quot;
```math
\LaTeX
```
&quot;&quot;&quot;
function f end</code></pre><h3 id="Functions-and-Methods-2"><a class="docs-heading-anchor" href="#Functions-and-Methods-2">Functions and Methods</a><a class="docs-heading-anchor-permalink" href="#Functions-and-Methods-2" title="Permalink"></a></h3><pre><code class="language-julia hljs">&quot;...&quot;
function f end

&quot;...&quot;
f</code></pre><p>Adds docstring <code>&quot;...&quot;</code> to the function <code>f</code>. The first version is the preferred syntax, however both are equivalent.</p><pre><code class="language-julia hljs">&quot;...&quot;
f(x) = x

&quot;...&quot;
function f(x)
    return x
end

&quot;...&quot;
f(x)</code></pre><p>Adds docstring <code>&quot;...&quot;</code> to the method <code>f(::Any)</code>.</p><pre><code class="language-julia hljs">&quot;...&quot;
f(x, y = 1) = x + y</code></pre><p>Adds docstring <code>&quot;...&quot;</code> to two <code>Method</code>s, namely <code>f(::Any)</code> and <code>f(::Any, ::Any)</code>.</p><h3 id="Macros"><a class="docs-heading-anchor" href="#Macros">Macros</a><a id="Macros-1"></a><a class="docs-heading-anchor-permalink" href="#Macros" title="Permalink"></a></h3><pre><code class="language-julia hljs">&quot;...&quot;
macro m(x) end</code></pre><p>Adds docstring <code>&quot;...&quot;</code> to the <code>@m(::Any)</code> macro definition.</p><pre><code class="language-julia hljs">&quot;...&quot;
:(@m1)

&quot;...&quot;
macro m2 end</code></pre><p>Adds docstring <code>&quot;...&quot;</code> to the macros named <code>@m1</code> and <code>@m2</code>.</p><h3 id="Types"><a class="docs-heading-anchor" href="#Types">Types</a><a id="Types-1"></a><a class="docs-heading-anchor-permalink" href="#Types" title="Permalink"></a></h3><pre><code class="nohighlight hljs">&quot;...&quot;
abstract type T1 end

&quot;...&quot;
mutable struct T2
    ...
end

&quot;...&quot;
struct T3
    ...
end</code></pre><p>Adds the docstring <code>&quot;...&quot;</code> to types <code>T1</code>, <code>T2</code>, and <code>T3</code>.</p><pre><code class="nohighlight hljs">&quot;...&quot;
T1

&quot;...&quot;
T2

&quot;...&quot;
T3</code></pre><p>Adds the docstring <code>&quot;...&quot;</code> to types <code>T1</code>, <code>T2</code>, and <code>T3</code>. The previous version is the preferred syntax, however both are equivalent.</p><pre><code class="language-julia hljs">&quot;...&quot;
struct T
    &quot;x&quot;
    x
    &quot;y&quot;
    y

    @doc &quot;Inner constructor&quot;
    function T()
        new(...)
    end
end</code></pre><p>Adds docstring <code>&quot;...&quot;</code> to type <code>T</code>, <code>&quot;x&quot;</code> to field <code>T.x</code>, <code>&quot;y&quot;</code> to field <code>T.y</code>, and <code>&quot;Inner constructor&quot;</code> to the inner constructor <code>T()</code>. Also applicable to <code>mutable struct</code> types.</p><h3 id="Modules"><a class="docs-heading-anchor" href="#Modules">Modules</a><a id="Modules-1"></a><a class="docs-heading-anchor-permalink" href="#Modules" title="Permalink"></a></h3><pre><code class="language-julia hljs">&quot;...&quot;
module M end

module M

&quot;...&quot;
M

end</code></pre><p>Adds docstring <code>&quot;...&quot;</code> to the <code>Module</code> <code>M</code>. Adding the docstring above the <code>Module</code> is the preferred syntax, however both are equivalent.</p><pre><code class="language-julia hljs">&quot;...&quot;
baremodule M
# ...
end

baremodule M

import Base: @doc

&quot;...&quot;
f(x) = x

end</code></pre><p>Documenting a <code>baremodule</code> by placing a docstring above the expression automatically imports <code>@doc</code> into the module. These imports must be done manually when the module expression is not documented.</p><h3 id="Global-Variables"><a class="docs-heading-anchor" href="#Global-Variables">Global Variables</a><a id="Global-Variables-1"></a><a class="docs-heading-anchor-permalink" href="#Global-Variables" title="Permalink"></a></h3><pre><code class="language-julia hljs">&quot;...&quot;
const a = 1

&quot;...&quot;
b = 2

&quot;...&quot;
global c = 3</code></pre><p>Adds docstring <code>&quot;...&quot;</code> to the <code>Binding</code>s <code>a</code>, <code>b</code>, and <code>c</code>.</p><p><code>Binding</code>s are used to store a reference to a particular <code>Symbol</code> in a <code>Module</code> without storing the referenced value itself.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>When a <code>const</code> definition is only used to define an alias of another definition, such as is the case with the function <code>div</code> and its alias <code>÷</code> in <code>Base</code>, do not document the alias and instead document the actual function.</p><p>If the alias is documented and not the real definition then the docsystem (<code>?</code> mode) will not return the docstring attached to the alias when the real definition is searched for.</p><p>For example you should write</p><pre><code class="language-julia hljs">&quot;...&quot;
f(x) = x + 1
const alias = f</code></pre><p>rather than</p><pre><code class="language-julia hljs">f(x) = x + 1
&quot;...&quot;
const alias = f</code></pre></div></div><pre><code class="language-julia hljs">&quot;...&quot;
sym</code></pre><p>Adds docstring <code>&quot;...&quot;</code> to the value associated with <code>sym</code>. However, it is preferred that <code>sym</code> is documented where it is defined.</p><h3 id="Multiple-Objects"><a class="docs-heading-anchor" href="#Multiple-Objects">Multiple Objects</a><a id="Multiple-Objects-1"></a><a class="docs-heading-anchor-permalink" href="#Multiple-Objects" title="Permalink"></a></h3><pre><code class="language-julia hljs">&quot;...&quot;
a, b</code></pre><p>Adds docstring <code>&quot;...&quot;</code> to <code>a</code> and <code>b</code> each of which should be a documentable expression. This syntax is equivalent to</p><pre><code class="language-julia hljs">&quot;...&quot;
a

&quot;...&quot;
b</code></pre><p>Any number of expressions many be documented together in this way. This syntax can be useful when two functions are related, such as non-mutating and mutating versions <code>f</code> and <code>f!</code>.</p><h3 id="Macro-generated-code"><a class="docs-heading-anchor" href="#Macro-generated-code">Macro-generated code</a><a id="Macro-generated-code-1"></a><a class="docs-heading-anchor-permalink" href="#Macro-generated-code" title="Permalink"></a></h3><pre><code class="language-julia hljs">&quot;...&quot;
@m expression</code></pre><p>Adds docstring <code>&quot;...&quot;</code> to the expression generated by expanding <code>@m expression</code>. This allows for expressions decorated with <code>@inline</code>, <code>@noinline</code>, <code>@generated</code>, or any other macro to be documented in the same way as undecorated expressions.</p><p>Macro authors should take note that only macros that generate a single expression will automatically support docstrings. If a macro returns a block containing multiple subexpressions then the subexpression that should be documented must be marked using the <a href="documentation.html#Core.@__doc__"><code>@__doc__</code></a> macro.</p><p>The <a href="../base/base.html#Base.Enums.@enum"><code>@enum</code></a> macro makes use of <code>@__doc__</code> to allow for documenting <a href="../base/base.html#Base.Enums.Enum"><code>Enum</code></a>s. Examining its definition should serve as an example of how to use <code>@__doc__</code> correctly.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.@__doc__" href="#Core.@__doc__"><code>Core.@__doc__</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@__doc__(ex)</code></pre><p>Low-level macro used to mark expressions returned by a macro that should be documented. If more than one expression is marked then the same docstring is applied to each expression.</p><pre><code class="nohighlight hljs">macro example(f)
    quote
        $(f)() = 0
        @__doc__ $(f)(x) = 1
        $(f)(x, y) = 2
    end |&gt; esc
end</code></pre><p><code>@__doc__</code> has no effect when a macro that uses it is not documented.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/Docs.jl#L434-L449">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="modules.html">« Modules</a><a class="docs-footer-nextpage" href="metaprogramming.html">Metaprogramming »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
