<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Distributed Computing · The Julia Language</title><meta name="title" content="Distributed Computing · The Julia Language"/><meta property="og:title" content="Distributed Computing · The Julia Language"/><meta property="twitter:title" content="Distributed Computing · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li class="is-active"><a class="tocitem" href="Distributed.html">Distributed Computing</a><ul class="internal"><li><a class="tocitem" href="#Cluster-Manager-Interface"><span>Cluster Manager Interface</span></a></li></ul></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="Distributed.html">Distributed Computing</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="Distributed.html">Distributed Computing</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/Distributed.jl/blob/master/docs/src/index.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="man-distributed"><a class="docs-heading-anchor" href="#man-distributed">Distributed Computing</a><a id="man-distributed-1"></a><a class="docs-heading-anchor-permalink" href="#man-distributed" title="Permalink"></a></h1><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed" href="#Distributed"><code>Distributed</code></a> — <span class="docstring-category">Module</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>Tools for distributed parallel processing.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/Distributed.jl#L3-L5">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.addprocs" href="#Distributed.addprocs"><code>Distributed.addprocs</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">addprocs(manager::ClusterManager; kwargs...) -&gt; List of process identifiers</code></pre><p>Launches worker processes via the specified cluster manager.</p><p>For example, Beowulf clusters are supported via a custom cluster manager implemented in the package <code>ClusterManagers.jl</code>.</p><p>The number of seconds a newly launched worker waits for connection establishment from the master can be specified via variable <code>JULIA_WORKER_TIMEOUT</code> in the worker process&#39;s environment. Relevant only when using TCP/IP as transport.</p><p>To launch workers without blocking the REPL, or the containing function if launching workers programmatically, execute <code>addprocs</code> in its own task.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs"># On busy clusters, call `addprocs` asynchronously
t = @async addprocs(...)</code></pre><pre><code class="language-julia hljs"># Utilize workers as and when they come online
if nprocs() &gt; 1   # Ensure at least one new worker is available
   ....   # perform distributed execution
end</code></pre><pre><code class="language-julia hljs"># Retrieve newly launched worker IDs, or any error messages
if istaskdone(t)   # Check if `addprocs` has completed to ensure `fetch` doesn&#39;t block
    if nworkers() == N
        new_pids = fetch(t)
    else
        fetch(t)
    end
end</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L403-L442">source</a></section><section><div><pre><code class="language-julia hljs">addprocs(machines; tunnel=false, sshflags=``, max_parallel=10, kwargs...) -&gt; List of process identifiers</code></pre><p>Add worker processes on remote machines via SSH. Configuration is done with keyword arguments (see below). In particular, the <code>exename</code> keyword can be used to specify the path to the <code>julia</code> binary on the remote machine(s).</p><p><code>machines</code> is a vector of &quot;machine specifications&quot; which are given as strings of the form <code>[user@]host[:port] [bind_addr[:port]]</code>. <code>user</code> defaults to current user and <code>port</code> to the standard SSH port. If <code>[bind_addr[:port]]</code> is specified, other workers will connect to this worker at the specified <code>bind_addr</code> and <code>port</code>.</p><p>It is possible to launch multiple processes on a remote host by using a tuple in the <code>machines</code> vector or the form <code>(machine_spec, count)</code>, where <code>count</code> is the number of workers to be launched on the specified host. Passing <code>:auto</code> as the worker count will launch as many workers as the number of CPU threads on the remote host.</p><p><strong>Examples</strong>:</p><pre><code class="language-julia hljs">addprocs([
    &quot;remote1&quot;,               # one worker on &#39;remote1&#39; logging in with the current username
    &quot;user@remote2&quot;,          # one worker on &#39;remote2&#39; logging in with the &#39;user&#39; username
    &quot;user@remote3:2222&quot;,     # specifying SSH port to &#39;2222&#39; for &#39;remote3&#39;
    (&quot;user@remote4&quot;, 4),     # launch 4 workers on &#39;remote4&#39;
    (&quot;user@remote5&quot;, :auto), # launch as many workers as CPU threads on &#39;remote5&#39;
])</code></pre><p><strong>Keyword arguments</strong>:</p><ul><li><p><code>tunnel</code>: if <code>true</code> then SSH tunneling will be used to connect to the worker from the master process. Default is <code>false</code>.</p></li><li><p><code>multiplex</code>: if <code>true</code> then SSH multiplexing is used for SSH tunneling. Default is <code>false</code>.</p></li><li><p><code>ssh</code>: the name or path of the SSH client executable used to start the workers. Default is <code>&quot;ssh&quot;</code>.</p></li><li><p><code>sshflags</code>: specifies additional ssh options, e.g. <code>sshflags=`-i /home/<USER>/bar.pem`</code></p></li><li><p><code>max_parallel</code>: specifies the maximum number of workers connected to in parallel at a host. Defaults to 10.</p></li><li><p><code>shell</code>: specifies the type of shell to which ssh connects on the workers.</p><ul><li><p><code>shell=:posix</code>: a POSIX-compatible Unix/Linux shell (sh, ksh, bash, dash, zsh, etc.). The default.</p></li><li><p><code>shell=:csh</code>: a Unix C shell (csh, tcsh).</p></li><li><p><code>shell=:wincmd</code>: Microsoft Windows <code>cmd.exe</code>.</p></li></ul></li><li><p><code>dir</code>: specifies the working directory on the workers. Defaults to the host&#39;s current directory (as found by <code>pwd()</code>)</p></li><li><p><code>enable_threaded_blas</code>: if <code>true</code> then  BLAS will run on multiple threads in added processes. Default is <code>false</code>.</p></li><li><p><code>exename</code>: name of the <code>julia</code> executable. Defaults to <code>&quot;$(Sys.BINDIR)/julia&quot;</code> or <code>&quot;$(Sys.BINDIR)/julia-debug&quot;</code> as the case may be. It is recommended that a common Julia version is used on all remote machines because serialization and code distribution might fail otherwise.</p></li><li><p><code>exeflags</code>: additional flags passed to the worker processes.</p></li><li><p><code>topology</code>: Specifies how the workers connect to each other. Sending a message between unconnected workers results in an error.</p><ul><li><p><code>topology=:all_to_all</code>: All processes are connected to each other. The default.</p></li><li><p><code>topology=:master_worker</code>: Only the driver process, i.e. <code>pid</code> 1 connects to the workers. The workers do not connect to each other.</p></li><li><p><code>topology=:custom</code>: The <code>launch</code> method of the cluster manager specifies the connection topology via fields <code>ident</code> and <code>connect_idents</code> in <code>WorkerConfig</code>. A worker with a cluster manager identity <code>ident</code> will connect to all workers specified in <code>connect_idents</code>.</p></li></ul></li><li><p><code>lazy</code>: Applicable only with <code>topology=:all_to_all</code>. If <code>true</code>, worker-worker connections are setup lazily, i.e. they are setup at the first instance of a remote call between workers. Default is true.</p></li><li><p><code>env</code>: provide an array of string pairs such as <code>env=[&quot;JULIA_DEPOT_PATH&quot;=&gt;&quot;/depot&quot;]</code> to request that environment variables are set on the remote machine. By default only the environment variable <code>JULIA_WORKER_TIMEOUT</code> is passed automatically from the local to the remote environment.</p></li><li><p><code>cmdline_cookie</code>: pass the authentication cookie via the <code>--worker</code> commandline  option. The (more secure) default behaviour of passing the cookie via ssh stdio  may hang with Windows workers that use older (pre-ConPTY) Julia or Windows versions,  in which case <code>cmdline_cookie=true</code> offers a work-around.</p></li></ul><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>The keyword arguments <code>ssh</code>, <code>shell</code>, <code>env</code> and <code>cmdline_cookie</code> were added in Julia 1.6.</p></div></div><p>Environment variables:</p><p>If the master process fails to establish a connection with a newly launched worker within 60.0 seconds, the worker treats it as a fatal situation and terminates. This timeout can be controlled via environment variable <code>JULIA_WORKER_TIMEOUT</code>. The value of <code>JULIA_WORKER_TIMEOUT</code> on the master process specifies the number of seconds a newly launched worker waits for connection establishment.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/managers.jl#L51-L155">source</a></section><section><div><pre><code class="language-julia hljs">addprocs(np::Integer=Sys.CPU_THREADS; restrict=true, kwargs...) -&gt; List of process identifiers</code></pre><p>Launch <code>np</code> workers on the local host using the in-built <code>LocalManager</code>.</p><p>Local workers inherit the current package environment (i.e., active project, <a href="../base/constants.html#Base.LOAD_PATH"><code>LOAD_PATH</code></a>, and <a href="../base/constants.html#Base.DEPOT_PATH"><code>DEPOT_PATH</code></a>) from the main process.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Note that workers do not run a <code>~/.julia/config/startup.jl</code> startup script, nor do they synchronize their global state (such as command-line switches, global variables, new method definitions, and loaded modules) with any of the other running processes.</p></div></div><p><strong>Keyword arguments</strong>:</p><ul><li><code>restrict::Bool</code>: if <code>true</code> (default) binding is restricted to <code>127.0.0.1</code>.</li><li><code>dir</code>, <code>exename</code>, <code>exeflags</code>, <code>env</code>, <code>topology</code>, <code>lazy</code>, <code>enable_threaded_blas</code>: same effect as for <code>SSHManager</code>, see documentation for <a href="Distributed.html#Distributed.addprocs"><code>addprocs(machines::AbstractVector)</code></a>.</li></ul><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p>The inheriting of the package environment and the <code>env</code> keyword argument were added in Julia 1.9.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/managers.jl#L445-L466">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.nprocs" href="#Distributed.nprocs"><code>Distributed.nprocs</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">nprocs()</code></pre><p>Get the number of available processes.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; nprocs()
3

julia&gt; workers()
2-element Array{Int64,1}:
 2
 3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L852-L867">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.nworkers" href="#Distributed.nworkers"><code>Distributed.nworkers</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">nworkers()</code></pre><p>Get the number of available worker processes. This is one less than <a href="Distributed.html#Distributed.nprocs"><code>nprocs()</code></a>. Equal to <code>nprocs()</code> if <code>nprocs() == 1</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">$ julia -p 2

julia&gt; nprocs()
3

julia&gt; nworkers()
2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L883-L899">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.procs-Tuple{}" href="#Distributed.procs-Tuple{}"><code>Distributed.procs</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">procs()</code></pre><p>Return a list of all process identifiers, including pid 1 (which is not included by <a href="Distributed.html#Distributed.workers"><code>workers()</code></a>).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">$ julia -p 2

julia&gt; procs()
3-element Array{Int64,1}:
 1
 2
 3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L905-L920">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.procs-Tuple{Integer}" href="#Distributed.procs-Tuple{Integer}"><code>Distributed.procs</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">procs(pid::Integer)</code></pre><p>Return a list of all process identifiers on the same physical node. Specifically all workers bound to the same ip-address as <code>pid</code> are returned.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L947-L952">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.workers" href="#Distributed.workers"><code>Distributed.workers</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">workers()</code></pre><p>Return a list of all worker process identifiers.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">$ julia -p 2

julia&gt; workers()
2-element Array{Int64,1}:
 2
 3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L967-L981">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.rmprocs" href="#Distributed.rmprocs"><code>Distributed.rmprocs</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rmprocs(pids...; waitfor=typemax(Int))</code></pre><p>Remove the specified workers. Note that only process 1 can add or remove workers.</p><p>Argument <code>waitfor</code> specifies how long to wait for the workers to shut down:</p><ul><li>If unspecified, <code>rmprocs</code> will wait until all requested <code>pids</code> are removed.</li><li>An <a href="../base/base.html#Core.ErrorException"><code>ErrorException</code></a> is raised if all workers cannot be terminated before the requested <code>waitfor</code> seconds.</li><li>With a <code>waitfor</code> value of 0, the call returns immediately with the workers scheduled for removal in a different task. The scheduled <a href="../base/parallel.html#Core.Task"><code>Task</code></a> object is returned. The user should call <a href="../base/parallel.html#Base.wait"><code>wait</code></a> on the task before invoking any other parallel calls.</li></ul><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">$ julia -p 5

julia&gt; t = rmprocs(2, 3, waitfor=0)
Task (runnable) @0x0000000107c718d0

julia&gt; wait(t)

julia&gt; workers()
3-element Array{Int64,1}:
 4
 5
 6</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L997-L1027">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.interrupt" href="#Distributed.interrupt"><code>Distributed.interrupt</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">interrupt(pids::Integer...)</code></pre><p>Interrupt the current executing task on the specified workers. This is equivalent to pressing Ctrl-C on the local machine. If no arguments are given, all workers are interrupted.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L1204-L1209">source</a></section><section><div><pre><code class="language-julia hljs">interrupt(pids::AbstractVector=workers())</code></pre><p>Interrupt the current executing task on the specified workers. This is equivalent to pressing Ctrl-C on the local machine. If no arguments are given, all workers are interrupted.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L1212-L1217">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.myid" href="#Distributed.myid"><code>Distributed.myid</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">myid()</code></pre><p>Get the id of the current process.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; myid()
1

julia&gt; remotecall_fetch(() -&gt; myid(), 4)
4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L836-L849">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.pmap" href="#Distributed.pmap"><code>Distributed.pmap</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">pmap(f, [::AbstractWorkerPool], c...; distributed=true, batch_size=1, on_error=nothing, retry_delays=[], retry_check=nothing) -&gt; collection</code></pre><p>Transform collection <code>c</code> by applying <code>f</code> to each element using available workers and tasks.</p><p>For multiple collection arguments, apply <code>f</code> elementwise.</p><p>Note that <code>f</code> must be made available to all worker processes; see <a href="../manual/distributed-computing.html#code-availability">Code Availability and Loading Packages</a> for details.</p><p>If a worker pool is not specified all available workers will be used via a <a href="Distributed.html#Distributed.CachingPool"><code>CachingPool</code></a>.</p><p>By default, <code>pmap</code> distributes the computation over all specified workers. To use only the local process and distribute over tasks, specify <code>distributed=false</code>. This is equivalent to using <a href="../base/parallel.html#Base.asyncmap"><code>asyncmap</code></a>. For example, <code>pmap(f, c; distributed=false)</code> is equivalent to <code>asyncmap(f,c; ntasks=()-&gt;nworkers())</code></p><p><code>pmap</code> can also use a mix of processes and tasks via the <code>batch_size</code> argument. For batch sizes greater than 1, the collection is processed in multiple batches, each of length <code>batch_size</code> or less. A batch is sent as a single request to a free worker, where a local <a href="../base/parallel.html#Base.asyncmap"><code>asyncmap</code></a> processes elements from the batch using multiple concurrent tasks.</p><p>Any error stops <code>pmap</code> from processing the remainder of the collection. To override this behavior you can specify an error handling function via argument <code>on_error</code> which takes in a single argument, i.e., the exception. The function can stop the processing by rethrowing the error, or, to continue, return any value which is then returned inline with the results to the caller.</p><p>Consider the following two examples. The first one returns the exception object inline, the second a 0 in place of any exception:</p><pre><code class="language-julia-repl hljs">julia&gt; pmap(x-&gt;iseven(x) ? error(&quot;foo&quot;) : x, 1:4; on_error=identity)
4-element Array{Any,1}:
 1
  ErrorException(&quot;foo&quot;)
 3
  ErrorException(&quot;foo&quot;)

julia&gt; pmap(x-&gt;iseven(x) ? error(&quot;foo&quot;) : x, 1:4; on_error=ex-&gt;0)
4-element Array{Int64,1}:
 1
 0
 3
 0</code></pre><p>Errors can also be handled by retrying failed computations. Keyword arguments <code>retry_delays</code> and <code>retry_check</code> are passed through to <a href="../base/base.html#Base.retry"><code>retry</code></a> as keyword arguments <code>delays</code> and <code>check</code> respectively. If batching is specified, and an entire batch fails, all items in the batch are retried.</p><p>Note that if both <code>on_error</code> and <code>retry_delays</code> are specified, the <code>on_error</code> hook is called before retrying. If <code>on_error</code> does not throw (or rethrow) an exception, the element will not be retried.</p><p>Example: On errors, retry <code>f</code> on an element a maximum of 3 times without any delay between retries.</p><pre><code class="language-julia hljs">pmap(f, c; retry_delays = zeros(3))</code></pre><p>Example: Retry <code>f</code> only if the exception is not of type <a href="../base/base.html#Core.InexactError"><code>InexactError</code></a>, with exponentially increasing delays up to 3 times. Return a <code>NaN</code> in place for all <code>InexactError</code> occurrences.</p><pre><code class="language-julia hljs">pmap(f, c; on_error = e-&gt;(isa(e, InexactError) ? NaN : rethrow()), retry_delays = ExponentialBackOff(n = 3))</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/pmap.jl#L32-L98">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.RemoteException" href="#Distributed.RemoteException"><code>Distributed.RemoteException</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">RemoteException(captured)</code></pre><p>Exceptions on remote computations are captured and rethrown locally.  A <code>RemoteException</code> wraps the <code>pid</code> of the worker and a captured exception. A <code>CapturedException</code> captures the remote exception and a serializable form of the call stack when the exception was raised.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/process_messages.jl#L54-L60">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.ProcessExitedException" href="#Distributed.ProcessExitedException"><code>Distributed.ProcessExitedException</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ProcessExitedException(worker_id::Int)</code></pre><p>After a client Julia process has exited, further attempts to reference the dead child will throw this exception.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L1077-L1082">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.Future" href="#Distributed.Future"><code>Distributed.Future</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Future(w::Int, rrid::RRID, v::Union{Some, Nothing}=nothing)</code></pre><p>A <code>Future</code> is a placeholder for a single computation of unknown termination status and time. For multiple potential computations, see <code>RemoteChannel</code>. See <code>remoteref_id</code> for identifying an <code>AbstractRemoteRef</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/remotecall.jl#L17-L24">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.RemoteChannel" href="#Distributed.RemoteChannel"><code>Distributed.RemoteChannel</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">RemoteChannel(pid::Integer=myid())</code></pre><p>Make a reference to a <code>Channel{Any}(1)</code> on process <code>pid</code>. The default <code>pid</code> is the current process.</p><pre><code class="nohighlight hljs">RemoteChannel(f::Function, pid::Integer=myid())</code></pre><p>Create references to remote channels of a specific size and type. <code>f</code> is a function that when executed on <code>pid</code> must return an implementation of an <code>AbstractChannel</code>.</p><p>For example, <code>RemoteChannel(()-&gt;Channel{Int}(10), pid)</code>, will return a reference to a channel of type <code>Int</code> and size 10 on <code>pid</code>.</p><p>The default <code>pid</code> is the current process.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/remotecall.jl#L38-L53">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.fetch-Tuple{Distributed.Future}" href="#Base.fetch-Tuple{Distributed.Future}"><code>Base.fetch</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">fetch(x::Future)</code></pre><p>Wait for and get the value of a <a href="Future.html#Future"><code>Future</code></a>. The fetched value is cached locally. Further calls to <code>fetch</code> on the same reference return the cached value. If the remote value is an exception, throws a <a href="Distributed.html#Distributed.RemoteException"><code>RemoteException</code></a> which captures the remote exception and backtrace.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/remotecall.jl#L595-L601">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.fetch-Tuple{RemoteChannel}" href="#Base.fetch-Tuple{RemoteChannel}"><code>Base.fetch</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">fetch(c::RemoteChannel)</code></pre><p>Wait for and get a value from a <a href="Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a>. Exceptions raised are the same as for a <a href="Future.html#Future"><code>Future</code></a>. Does not remove the item fetched.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/remotecall.jl#L650-L655">source</a></section><section><div><pre><code class="language-julia hljs">fetch(x::Any)</code></pre><p>Return <code>x</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/task.jl#L375-L379">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.remotecall-Tuple{Any, Integer, Vararg{Any}}" href="#Distributed.remotecall-Tuple{Any, Integer, Vararg{Any}}"><code>Distributed.remotecall</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">remotecall(f, id::Integer, args...; kwargs...) -&gt; Future</code></pre><p>Call a function <code>f</code> asynchronously on the given arguments on the specified process. Return a <a href="Future.html#Future"><code>Future</code></a>. Keyword arguments, if any, are passed through to <code>f</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/remotecall.jl#L440-L446">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.remotecall_wait-Tuple{Any, Integer, Vararg{Any}}" href="#Distributed.remotecall_wait-Tuple{Any, Integer, Vararg{Any}}"><code>Distributed.remotecall_wait</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">remotecall_wait(f, id::Integer, args...; kwargs...)</code></pre><p>Perform a faster <code>wait(remotecall(...))</code> in one message on the <code>Worker</code> specified by worker id <code>id</code>. Keyword arguments, if any, are passed through to <code>f</code>.</p><p>See also <a href="../base/parallel.html#Base.wait"><code>wait</code></a> and <a href="Distributed.html#Distributed.remotecall-Tuple{Any, Integer, Vararg{Any}}"><code>remotecall</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/remotecall.jl#L511-L518">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.remotecall_fetch-Tuple{Any, Integer, Vararg{Any}}" href="#Distributed.remotecall_fetch-Tuple{Any, Integer, Vararg{Any}}"><code>Distributed.remotecall_fetch</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">remotecall_fetch(f, id::Integer, args...; kwargs...)</code></pre><p>Perform <code>fetch(remotecall(...))</code> in one message. Keyword arguments, if any, are passed through to <code>f</code>. Any remote exceptions are captured in a <a href="Distributed.html#Distributed.RemoteException"><code>RemoteException</code></a> and thrown.</p><p>See also <a href="../base/parallel.html#Base.fetch-Tuple{Task}"><code>fetch</code></a> and <a href="Distributed.html#Distributed.remotecall-Tuple{Any, Integer, Vararg{Any}}"><code>remotecall</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">$ julia -p 2

julia&gt; remotecall_fetch(sqrt, 2, 4)
2.0

julia&gt; remotecall_fetch(sqrt, 2, -4)
ERROR: On worker 2:
DomainError with -4.0:
sqrt was called with a negative real argument but will only return a complex result if called with a complex argument. Try sqrt(Complex(x)).
...</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/remotecall.jl#L468-L491">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.remote_do-Tuple{Any, Integer, Vararg{Any}}" href="#Distributed.remote_do-Tuple{Any, Integer, Vararg{Any}}"><code>Distributed.remote_do</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">remote_do(f, id::Integer, args...; kwargs...) -&gt; nothing</code></pre><p>Executes <code>f</code> on worker <code>id</code> asynchronously. Unlike <a href="Distributed.html#Distributed.remotecall-Tuple{Any, Integer, Vararg{Any}}"><code>remotecall</code></a>, it does not store the result of computation, nor is there a way to wait for its completion.</p><p>A successful invocation indicates that the request has been accepted for execution on the remote node.</p><p>While consecutive <code>remotecall</code>s to the same worker are serialized in the order they are invoked, the order of executions on the remote worker is undetermined. For example, <code>remote_do(f1, 2); remotecall(f2, 2); remote_do(f3, 2)</code> will serialize the call to <code>f1</code>, followed by <code>f2</code> and <code>f3</code> in that order. However, it is not guaranteed that <code>f1</code> is executed before <code>f3</code> on worker 2.</p><p>Any exceptions thrown by <code>f</code> are printed to <a href="../base/io-network.html#Base.stderr"><code>stderr</code></a> on the remote worker.</p><p>Keyword arguments, if any, are passed through to <code>f</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/remotecall.jl#L537-L556">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.put!-Tuple{RemoteChannel, Vararg{Any}}" href="#Base.put!-Tuple{RemoteChannel, Vararg{Any}}"><code>Base.put!</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">put!(rr::RemoteChannel, args...)</code></pre><p>Store a set of values to the <a href="Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a>. If the channel is full, blocks until space is available. Return the first argument.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/remotecall.jl#L715-L721">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.put!-Tuple{Distributed.Future, Any}" href="#Base.put!-Tuple{Distributed.Future, Any}"><code>Base.put!</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">put!(rr::Future, v)</code></pre><p>Store a value to a <a href="Future.html#Future"><code>Future</code></a> <code>rr</code>. <code>Future</code>s are write-once remote references. A <code>put!</code> on an already set <code>Future</code> throws an <code>Exception</code>. All asynchronous remote calls return <code>Future</code>s and set the value to the return value of the call upon completion.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/remotecall.jl#L660-L668">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.take!-Tuple{RemoteChannel, Vararg{Any}}" href="#Base.take!-Tuple{RemoteChannel, Vararg{Any}}"><code>Base.take!</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">take!(rr::RemoteChannel, args...)</code></pre><p>Fetch value(s) from a <a href="Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a> <code>rr</code>, removing the value(s) in the process.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/remotecall.jl#L755-L760">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isready-Tuple{RemoteChannel, Vararg{Any}}" href="#Base.isready-Tuple{RemoteChannel, Vararg{Any}}"><code>Base.isready</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isready(rr::RemoteChannel, args...)</code></pre><p>Determine whether a <a href="Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a> has a value stored to it. Note that this function can cause race conditions, since by the time you receive its result it may no longer be true. However, it can be safely used on a <a href="Future.html#Future"><code>Future</code></a> since they are assigned only once.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/remotecall.jl#L224-L231">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isready-Tuple{Distributed.Future}" href="#Base.isready-Tuple{Distributed.Future}"><code>Base.isready</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isready(rr::Future)</code></pre><p>Determine whether a <a href="Future.html#Future"><code>Future</code></a> has a value stored to it.</p><p>If the argument <code>Future</code> is owned by a different node, this call will block to wait for the answer. It is recommended to wait for <code>rr</code> in a separate task instead or to use a local <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a> as a proxy:</p><pre><code class="language-julia hljs">p = 1
f = Future(p)
errormonitor(@async put!(f, remotecall_fetch(long_computation, p)))
isready(f)  # will not block</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/remotecall.jl#L196-L211">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.AbstractWorkerPool" href="#Distributed.AbstractWorkerPool"><code>Distributed.AbstractWorkerPool</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AbstractWorkerPool</code></pre><p>Supertype for worker pools such as <a href="Distributed.html#Distributed.WorkerPool"><code>WorkerPool</code></a> and <a href="Distributed.html#Distributed.CachingPool"><code>CachingPool</code></a>. An <code>AbstractWorkerPool</code> should implement:</p><ul><li><a href="../base/collections.html#Base.push!"><code>push!</code></a> - add a new worker to the overall pool (available + busy)</li><li><a href="../base/parallel.html#Base.put!-Tuple{Channel, Any}"><code>put!</code></a> - put back a worker to the available pool</li><li><a href="../base/io-network.html#Base.take!-Tuple{Base.GenericIOBuffer}"><code>take!</code></a> - take a worker from the available pool (to be used for remote function execution)</li><li><a href="../base/collections.html#Base.length"><code>length</code></a> - number of workers available in the overall pool</li><li><a href="../base/parallel.html#Base.isready-Tuple{Channel}"><code>isready</code></a> - return false if a <code>take!</code> on the pool would block, else true</li></ul><p>The default implementations of the above (on a <code>AbstractWorkerPool</code>) require fields</p><ul><li><code>channel::Channel{Int}</code></li><li><code>workers::Set{Int}</code></li></ul><p>where <code>channel</code> contains free worker pids and <code>workers</code> is the set of all workers associated with this pool.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/workerpool.jl#L3-L18">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.WorkerPool" href="#Distributed.WorkerPool"><code>Distributed.WorkerPool</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">WorkerPool(workers::Union{Vector{Int},AbstractRange{Int}})</code></pre><p>Create a <code>WorkerPool</code> from a vector or range of worker ids.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">$ julia -p 3

julia&gt; WorkerPool([2, 3])
WorkerPool(Channel{Int64}(sz_max:9223372036854775807,sz_curr:2), Set([2, 3]), RemoteChannel{Channel{Any}}(1, 1, 6))

julia&gt; WorkerPool(2:4)
WorkerPool(Channel{Int64}(sz_max:9223372036854775807,sz_curr:2), Set([4, 2, 3]), RemoteChannel{Channel{Any}}(1, 1, 7))</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/workerpool.jl#L35-L50">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.CachingPool" href="#Distributed.CachingPool"><code>Distributed.CachingPool</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">CachingPool(workers::Vector{Int})</code></pre><p>An implementation of an <code>AbstractWorkerPool</code>. <a href="Distributed.html#Distributed.remote"><code>remote</code></a>, <a href="Distributed.html#Distributed.remotecall_fetch-Tuple{Any, Integer, Vararg{Any}}"><code>remotecall_fetch</code></a>, <a href="Distributed.html#Distributed.pmap"><code>pmap</code></a> (and other remote calls which execute functions remotely) benefit from caching the serialized/deserialized functions on the worker nodes, especially closures (which may capture large amounts of data).</p><p>The remote cache is maintained for the lifetime of the returned <code>CachingPool</code> object. To clear the cache earlier, use <code>clear!(pool)</code>.</p><p>For global variables, only the bindings are captured in a closure, not the data. <code>let</code> blocks can be used to capture global data.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">const foo = rand(10^8);
wp = CachingPool(workers())
let foo = foo
    pmap(i -&gt; sum(foo) + i, wp, 1:100);
end</code></pre><p>The above would transfer <code>foo</code> only once to each worker.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/workerpool.jl#L306-L332">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.default_worker_pool" href="#Distributed.default_worker_pool"><code>Distributed.default_worker_pool</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">default_worker_pool()</code></pre><p><a href="Distributed.html#Distributed.AbstractWorkerPool"><code>AbstractWorkerPool</code></a> containing idle <a href="Distributed.html#Distributed.workers"><code>workers</code></a> - used by <code>remote(f)</code> and <a href="Distributed.html#Distributed.pmap"><code>pmap</code></a> (by default). Unless one is explicitly set via <code>default_worker_pool!(pool)</code>, the default worker pool is initialized to a <a href="Distributed.html#Distributed.WorkerPool"><code>WorkerPool</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">$ julia -p 3

julia&gt; default_worker_pool()
WorkerPool(Channel{Int64}(sz_max:9223372036854775807,sz_curr:3), Set([4, 2, 3]), RemoteChannel{Channel{Any}}(1, 1, 4))</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/workerpool.jl#L244-L258">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.clear!" href="#Distributed.clear!"><code>Distributed.clear!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">clear!(syms, pids=workers(); mod=Main)</code></pre><p>Clears global bindings in modules by initializing them to <code>nothing</code>. <code>syms</code> should be of type <a href="../base/base.html#Core.Symbol"><code>Symbol</code></a> or a collection of <code>Symbol</code>s . <code>pids</code> and <code>mod</code> identify the processes and the module in which global variables are to be reinitialized. Only those names found to be defined under <code>mod</code> are cleared.</p><p>An exception is raised if a global constant is requested to be cleared.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/clusterserialize.jl#L235-L244">source</a></section><section><div><pre><code class="language-julia hljs">clear!(pool::CachingPool) -&gt; pool</code></pre><p>Removes all cached functions from all participating workers.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/workerpool.jl#L341-L345">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.remote" href="#Distributed.remote"><code>Distributed.remote</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">remote([p::AbstractWorkerPool], f) -&gt; Function</code></pre><p>Return an anonymous function that executes function <code>f</code> on an available worker (drawn from <a href="Distributed.html#Distributed.WorkerPool"><code>WorkerPool</code></a> <code>p</code> if provided) using <a href="Distributed.html#Distributed.remotecall_fetch-Tuple{Any, Integer, Vararg{Any}}"><code>remotecall_fetch</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/workerpool.jl#L281-L286">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.remotecall-Tuple{Any, AbstractWorkerPool, Vararg{Any}}" href="#Distributed.remotecall-Tuple{Any, AbstractWorkerPool, Vararg{Any}}"><code>Distributed.remotecall</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">remotecall(f, pool::AbstractWorkerPool, args...; kwargs...) -&gt; Future</code></pre><p><a href="Distributed.html#Distributed.WorkerPool"><code>WorkerPool</code></a> variant of <code>remotecall(f, pid, ....)</code>. Wait for and take a free worker from <code>pool</code> and perform a <code>remotecall</code> on it.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">$ julia -p 3

julia&gt; wp = WorkerPool([2, 3]);

julia&gt; A = rand(3000);

julia&gt; f = remotecall(maximum, wp, A)
Future(2, 1, 6, nothing)</code></pre><p>In this example, the task ran on pid 2, called from pid 1.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/workerpool.jl#L169-L186">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.remotecall_wait-Tuple{Any, AbstractWorkerPool, Vararg{Any}}" href="#Distributed.remotecall_wait-Tuple{Any, AbstractWorkerPool, Vararg{Any}}"><code>Distributed.remotecall_wait</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">remotecall_wait(f, pool::AbstractWorkerPool, args...; kwargs...) -&gt; Future</code></pre><p><a href="Distributed.html#Distributed.WorkerPool"><code>WorkerPool</code></a> variant of <code>remotecall_wait(f, pid, ....)</code>. Wait for and take a free worker from <code>pool</code> and perform a <code>remotecall_wait</code> on it.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">$ julia -p 3

julia&gt; wp = WorkerPool([2, 3]);

julia&gt; A = rand(3000);

julia&gt; f = remotecall_wait(maximum, wp, A)
Future(3, 1, 9, nothing)

julia&gt; fetch(f)
0.9995177101692958</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/workerpool.jl#L190-L210">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.remotecall_fetch-Tuple{Any, AbstractWorkerPool, Vararg{Any}}" href="#Distributed.remotecall_fetch-Tuple{Any, AbstractWorkerPool, Vararg{Any}}"><code>Distributed.remotecall_fetch</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">remotecall_fetch(f, pool::AbstractWorkerPool, args...; kwargs...) -&gt; result</code></pre><p><a href="Distributed.html#Distributed.WorkerPool"><code>WorkerPool</code></a> variant of <code>remotecall_fetch(f, pid, ....)</code>. Waits for and takes a free worker from <code>pool</code> and performs a <code>remotecall_fetch</code> on it.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">$ julia -p 3

julia&gt; wp = WorkerPool([2, 3]);

julia&gt; A = rand(3000);

julia&gt; remotecall_fetch(maximum, wp, A)
0.9995177101692958</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/workerpool.jl#L214-L231">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.remote_do-Tuple{Any, AbstractWorkerPool, Vararg{Any}}" href="#Distributed.remote_do-Tuple{Any, AbstractWorkerPool, Vararg{Any}}"><code>Distributed.remote_do</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">remote_do(f, pool::AbstractWorkerPool, args...; kwargs...) -&gt; nothing</code></pre><p><a href="Distributed.html#Distributed.WorkerPool"><code>WorkerPool</code></a> variant of <code>remote_do(f, pid, ....)</code>. Wait for and take a free worker from <code>pool</code> and perform a <code>remote_do</code> on it.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/workerpool.jl#L234-L239">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.@spawn" href="#Distributed.@spawn"><code>Distributed.@spawn</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@spawn expr</code></pre><p>Create a closure around an expression and run it on an automatically-chosen process, returning a <a href="Future.html#Future"><code>Future</code></a> to the result. This macro is deprecated; <code>@spawnat :any expr</code> should be used instead.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; addprocs(3);

julia&gt; f = @spawn myid()
Future(2, 1, 5, nothing)

julia&gt; fetch(f)
2

julia&gt; f = @spawn myid()
Future(3, 1, 7, nothing)

julia&gt; fetch(f)
3</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p>As of Julia 1.3 this macro is deprecated. Use <code>@spawnat :any</code> instead.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/macros.jl#L15-L41">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.@spawnat" href="#Distributed.@spawnat"><code>Distributed.@spawnat</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@spawnat p expr</code></pre><p>Create a closure around an expression and run the closure asynchronously on process <code>p</code>. Return a <a href="Future.html#Future"><code>Future</code></a> to the result. If <code>p</code> is the quoted literal symbol <code>:any</code>, then the system will pick a processor to use automatically.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; addprocs(3);

julia&gt; f = @spawnat 2 myid()
Future(2, 1, 3, nothing)

julia&gt; fetch(f)
2

julia&gt; f = @spawnat :any myid()
Future(3, 1, 7, nothing)

julia&gt; fetch(f)
3</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p>The <code>:any</code> argument is available as of Julia 1.3.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/macros.jl#L54-L81">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.@fetch" href="#Distributed.@fetch"><code>Distributed.@fetch</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@fetch expr</code></pre><p>Equivalent to <code>fetch(@spawnat :any expr)</code>. See <a href="../base/parallel.html#Base.fetch-Tuple{Task}"><code>fetch</code></a> and <a href="Distributed.html#Distributed.@spawnat"><code>@spawnat</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; addprocs(3);

julia&gt; @fetch myid()
2

julia&gt; @fetch myid()
3

julia&gt; @fetch myid()
4

julia&gt; @fetch myid()
2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/macros.jl#L99-L121">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.@fetchfrom" href="#Distributed.@fetchfrom"><code>Distributed.@fetchfrom</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@fetchfrom</code></pre><p>Equivalent to <code>fetch(@spawnat p expr)</code>. See <a href="../base/parallel.html#Base.fetch-Tuple{Task}"><code>fetch</code></a> and <a href="Distributed.html#Distributed.@spawnat"><code>@spawnat</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; addprocs(3);

julia&gt; @fetchfrom 2 myid()
2

julia&gt; @fetchfrom 4 myid()
4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/macros.jl#L127-L143">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.@distributed" href="#Distributed.@distributed"><code>Distributed.@distributed</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@distributed</code></pre><p>A distributed memory, parallel for loop of the form :</p><pre><code class="nohighlight hljs">@distributed [reducer] for var = range
    body
end</code></pre><p>The specified range is partitioned and locally executed across all workers. In case an optional reducer function is specified, <code>@distributed</code> performs local reductions on each worker with a final reduction on the calling process.</p><p>Note that without a reducer function, <code>@distributed</code> executes asynchronously, i.e. it spawns independent tasks on all available workers and returns immediately without waiting for completion. To wait for completion, prefix the call with <a href="../base/parallel.html#Base.@sync"><code>@sync</code></a>, like :</p><pre><code class="nohighlight hljs">@sync @distributed for var = range
    body
end</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/macros.jl#L309-L329">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.@everywhere" href="#Distributed.@everywhere"><code>Distributed.@everywhere</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@everywhere [procs()] expr</code></pre><p>Execute an expression under <code>Main</code> on all <code>procs</code>. Errors on any of the processes are collected into a <a href="../base/base.html#Base.CompositeException"><code>CompositeException</code></a> and thrown. For example:</p><pre><code class="nohighlight hljs">@everywhere bar = 1</code></pre><p>will define <code>Main.bar</code> on all current processes. Any processes added later (say with <a href="Distributed.html#Distributed.addprocs"><code>addprocs()</code></a>) will not have the expression defined.</p><p>Unlike <a href="Distributed.html#Distributed.@spawnat"><code>@spawnat</code></a>, <code>@everywhere</code> does not capture any local variables. Instead, local variables can be broadcast using interpolation:</p><pre><code class="nohighlight hljs">foo = 1
@everywhere bar = $foo</code></pre><p>The optional argument <code>procs</code> allows specifying a subset of all processes to have execute the expression.</p><p>Similar to calling <code>remotecall_eval(Main, procs, expr)</code>, but with two extra features:</p><pre><code class="nohighlight hljs">- `using` and `import` statements run on the calling process first, to ensure
  packages are precompiled.
- The current source file path used by `include` is propagated to other processes.</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/macros.jl#L165-L191">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.remoteref_id" href="#Distributed.remoteref_id"><code>Distributed.remoteref_id</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">remoteref_id(r::AbstractRemoteRef) -&gt; RRID</code></pre><p><code>Future</code>s and <code>RemoteChannel</code>s are identified by fields:</p><ul><li><p><code>where</code> - refers to the node where the underlying object/storage referred to by the reference actually exists.</p></li><li><p><code>whence</code> - refers to the node the remote reference was created from. Note that this is different from the node where the underlying object referred to actually exists. For example calling <code>RemoteChannel(2)</code> from the master process would result in a <code>where</code> value of 2 and a <code>whence</code> value of 1.</p></li><li><p><code>id</code> is unique across all references created from the worker specified by <code>whence</code>.</p></li></ul><p>Taken together,  <code>whence</code> and <code>id</code> uniquely identify a reference across all workers.</p><p><code>remoteref_id</code> is a low-level API which returns a <code>RRID</code> object that wraps <code>whence</code> and <code>id</code> values of a remote reference.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/remotecall.jl#L142-L162">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.channel_from_id" href="#Distributed.channel_from_id"><code>Distributed.channel_from_id</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">channel_from_id(id) -&gt; c</code></pre><p>A low-level API which returns the backing <code>AbstractChannel</code> for an <code>id</code> returned by <a href="Distributed.html#Distributed.remoteref_id"><code>remoteref_id</code></a>. The call is valid only on the node where the backing channel exists.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/remotecall.jl#L165-L171">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.worker_id_from_socket" href="#Distributed.worker_id_from_socket"><code>Distributed.worker_id_from_socket</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">worker_id_from_socket(s) -&gt; pid</code></pre><p>A low-level API which, given a <code>IO</code> connection or a <code>Worker</code>, returns the <code>pid</code> of the worker it is connected to. This is useful when writing custom <a href="Serialization.html#Serialization.serialize"><code>serialize</code></a> methods for a type, which optimizes the data written out depending on the receiving process id.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L1108-L1115">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.cluster_cookie-Tuple{}" href="#Distributed.cluster_cookie-Tuple{}"><code>Distributed.cluster_cookie</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cluster_cookie() -&gt; cookie</code></pre><p>Return the cluster cookie.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L752-L756">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.cluster_cookie-Tuple{Any}" href="#Distributed.cluster_cookie-Tuple{Any}"><code>Distributed.cluster_cookie</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cluster_cookie(cookie) -&gt; cookie</code></pre><p>Set the passed cookie as the cluster cookie, then returns it.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L759-L763">source</a></section></article><h2 id="Cluster-Manager-Interface"><a class="docs-heading-anchor" href="#Cluster-Manager-Interface">Cluster Manager Interface</a><a id="Cluster-Manager-Interface-1"></a><a class="docs-heading-anchor-permalink" href="#Cluster-Manager-Interface" title="Permalink"></a></h2><p>This interface provides a mechanism to launch and manage Julia workers on different cluster environments. There are two types of managers present in Base: <code>LocalManager</code>, for launching additional workers on the same host, and <code>SSHManager</code>, for launching on remote hosts via <code>ssh</code>. TCP/IP sockets are used to connect and transport messages between processes. It is possible for Cluster Managers to provide a different transport.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.ClusterManager" href="#Distributed.ClusterManager"><code>Distributed.ClusterManager</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ClusterManager</code></pre><p>Supertype for cluster managers, which control workers processes as a cluster. Cluster managers implement how workers can be added, removed and communicated with. <code>SSHManager</code> and <code>LocalManager</code> are subtypes of this.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L3-L9">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.WorkerConfig" href="#Distributed.WorkerConfig"><code>Distributed.WorkerConfig</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">WorkerConfig</code></pre><p>Type used by <a href="Distributed.html#Distributed.ClusterManager"><code>ClusterManager</code></a>s to control workers added to their clusters. Some fields are used by all cluster managers to access a host:</p><ul><li><code>io</code> – the connection used to access the worker (a subtype of <code>IO</code> or <code>Nothing</code>)</li><li><code>host</code> – the host address (either a <code>String</code> or <code>Nothing</code>)</li><li><code>port</code> – the port on the host used to connect to the worker (either an <code>Int</code> or <code>Nothing</code>)</li></ul><p>Some are used by the cluster manager to add workers to an already-initialized host:</p><ul><li><code>count</code> – the number of workers to be launched on the host</li><li><code>exename</code> – the path to the Julia executable on the host, defaults to <code>&quot;$(Sys.BINDIR)/julia&quot;</code> or <code>&quot;$(Sys.BINDIR)/julia-debug&quot;</code></li><li><code>exeflags</code> – flags to use when launching Julia remotely</li></ul><p>The <code>userdata</code> field is used to store information for each worker by external managers.</p><p>Some fields are used by <code>SSHManager</code> and similar managers:</p><ul><li><code>tunnel</code> – <code>true</code> (use tunneling), <code>false</code> (do not use tunneling), or <a href="../base/constants.html#Core.nothing"><code>nothing</code></a> (use default for the manager)</li><li><code>multiplex</code> – <code>true</code> (use SSH multiplexing for tunneling) or <code>false</code></li><li><code>forward</code> – the forwarding option used for <code>-L</code> option of ssh</li><li><code>bind_addr</code> – the address on the remote host to bind to</li><li><code>sshflags</code> – flags to use in establishing the SSH connection</li><li><code>max_parallel</code> – the maximum number of workers to connect to in parallel on the host</li></ul><p>Some fields are used by both <code>LocalManager</code>s and <code>SSHManager</code>s:</p><ul><li><code>connect_at</code> – determines whether this is a worker-to-worker or driver-to-worker setup call</li><li><code>process</code> – the process which will be connected (usually the manager will assign this during <a href="Distributed.html#Distributed.addprocs"><code>addprocs</code></a>)</li><li><code>ospid</code> – the process ID according to the host OS, used to interrupt worker processes</li><li><code>environ</code> – private dictionary used to store temporary information by Local/SSH managers</li><li><code>ident</code> – worker as identified by the <a href="Distributed.html#Distributed.ClusterManager"><code>ClusterManager</code></a></li><li><code>connect_idents</code> – list of worker ids the worker must connect to if using a custom topology</li><li><code>enable_threaded_blas</code> – <code>true</code>, <code>false</code>, or <code>nothing</code>, whether to use threaded BLAS or not on the workers</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L12-L45">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.launch" href="#Distributed.launch"><code>Distributed.launch</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">launch(manager::ClusterManager, params::Dict, launched::Array, launch_ntfy::Condition)</code></pre><p>Implemented by cluster managers. For every Julia worker launched by this function, it should append a <code>WorkerConfig</code> entry to <code>launched</code> and notify <code>launch_ntfy</code>. The function MUST exit once all workers, requested by <code>manager</code> have been launched. <code>params</code> is a dictionary of all keyword arguments <a href="Distributed.html#Distributed.addprocs"><code>addprocs</code></a> was called with.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/managers.jl#L531-L538">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.manage" href="#Distributed.manage"><code>Distributed.manage</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">manage(manager::ClusterManager, id::Integer, config::WorkerConfig. op::Symbol)</code></pre><p>Implemented by cluster managers. It is called on the master process, during a worker&#39;s lifetime, with appropriate <code>op</code> values:</p><ul><li>with <code>:register</code>/<code>:deregister</code> when a worker is added / removed from the Julia worker pool.</li><li>with <code>:interrupt</code> when <code>interrupt(workers)</code> is called. The <code>ClusterManager</code> should signal the appropriate worker with an interrupt signal.</li><li>with <code>:finalize</code> for cleanup purposes.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/managers.jl#L541-L551">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.kill-Tuple{ClusterManager, Int64, WorkerConfig}" href="#Base.kill-Tuple{ClusterManager, Int64, WorkerConfig}"><code>Base.kill</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">kill(manager::ClusterManager, pid::Int, config::WorkerConfig)</code></pre><p>Implemented by cluster managers. It is called on the master process, by <a href="Distributed.html#Distributed.rmprocs"><code>rmprocs</code></a>. It should cause the remote worker specified by <code>pid</code> to exit. <code>kill(manager::ClusterManager.....)</code> executes a remote <code>exit()</code> on <code>pid</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/managers.jl#L721-L729">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.connect-Tuple{ClusterManager, Int64, WorkerConfig}" href="#Sockets.connect-Tuple{ClusterManager, Int64, WorkerConfig}"><code>Sockets.connect</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">connect(manager::ClusterManager, pid::Int, config::WorkerConfig) -&gt; (instrm::IO, outstrm::IO)</code></pre><p>Implemented by cluster managers using custom transports. It should establish a logical connection to worker with id <code>pid</code>, specified by <code>config</code> and return a pair of <code>IO</code> objects. Messages from <code>pid</code> to current process will be read off <code>instrm</code>, while messages to be sent to <code>pid</code> will be written to <code>outstrm</code>. The custom transport implementation must ensure that messages are delivered and received completely and in order. <code>connect(manager::ClusterManager.....)</code> sets up TCP/IP socket connections in-between workers.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/managers.jl#L561-L571">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.init_worker" href="#Distributed.init_worker"><code>Distributed.init_worker</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">init_worker(cookie::AbstractString, manager::ClusterManager=DefaultClusterManager())</code></pre><p>Called by cluster managers implementing custom transports. It initializes a newly launched process as a worker. Command line argument <code>--worker[=&lt;cookie&gt;]</code> has the effect of initializing a process as a worker using TCP/IP sockets for transport. <code>cookie</code> is a <a href="Distributed.html#Distributed.cluster_cookie-Tuple{}"><code>cluster_cookie</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L364-L371">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.start_worker" href="#Distributed.start_worker"><code>Distributed.start_worker</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">start_worker([out::IO=stdout], cookie::AbstractString=readline(stdin); close_stdin::Bool=true, stderr_to_stdout::Bool=true)</code></pre><p><code>start_worker</code> is an internal function which is the default entry point for worker processes connecting via TCP/IP. It sets up the process as a Julia cluster worker.</p><p>host:port information is written to stream <code>out</code> (defaults to stdout).</p><p>The function reads the cookie from stdin if required, and  listens on a free port (or if specified, the port in the <code>--bind-to</code> command line option) and schedules tasks to process incoming TCP connections and requests. It also (optionally) closes stdin and redirects stderr to stdout.</p><p>It does not return.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L215-L230">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.process_messages" href="#Distributed.process_messages"><code>Distributed.process_messages</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">process_messages(r_stream::IO, w_stream::IO, incoming::Bool=true)</code></pre><p>Called by cluster managers using custom transports. It should be called when the custom transport implementation receives the first message from a remote worker. The custom transport must manage a logical connection to the remote worker and provide two <code>IO</code> objects, one for incoming messages and the other for messages addressed to the remote worker. If <code>incoming</code> is <code>true</code>, the remote peer initiated the connection. Whichever of the pair initiates the connection sends the cluster cookie and its Julia version number to perform the authentication handshake.</p><p>See also <a href="Distributed.html#Distributed.cluster_cookie-Tuple{}"><code>cluster_cookie</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/process_messages.jl#L136-L149">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Distributed.default_addprocs_params" href="#Distributed.default_addprocs_params"><code>Distributed.default_addprocs_params</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">default_addprocs_params(mgr::ClusterManager) -&gt; Dict{Symbol, Any}</code></pre><p>Implemented by cluster managers. The default keyword parameters passed when calling <code>addprocs(mgr)</code>. The minimal set of options is available by calling <code>default_addprocs_params()</code></p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/cluster.jl#L526-L532">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="DelimitedFiles.html">« Delimited Files</a><a class="docs-footer-nextpage" href="Downloads.html">Downloads »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
