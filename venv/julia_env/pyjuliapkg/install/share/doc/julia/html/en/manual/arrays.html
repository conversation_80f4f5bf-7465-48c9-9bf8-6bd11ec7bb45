<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Single- and multi-dimensional Arrays · The Julia Language</title><meta name="title" content="Single- and multi-dimensional Arrays · The Julia Language"/><meta property="og:title" content="Single- and multi-dimensional Arrays · The Julia Language"/><meta property="twitter:title" content="Single- and multi-dimensional Arrays · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li class="is-active"><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a><ul class="internal"><li><a class="tocitem" href="#Basic-Functions"><span>Basic Functions</span></a></li><li><a class="tocitem" href="#Construction-and-Initialization"><span>Construction and Initialization</span></a></li><li><a class="tocitem" href="#man-array-literals"><span>Array literals</span></a></li><li><a class="tocitem" href="#man-comprehensions"><span>Comprehensions</span></a></li><li><a class="tocitem" href="#man-generators"><span>Generator Expressions</span></a></li><li><a class="tocitem" href="#man-array-indexing"><span>Indexing</span></a></li><li><a class="tocitem" href="#man-indexed-assignment"><span>Indexed Assignment</span></a></li><li><a class="tocitem" href="#man-supported-index-types"><span>Supported index types</span></a></li><li><a class="tocitem" href="#Iteration"><span>Iteration</span></a></li><li><a class="tocitem" href="#Array-traits"><span>Array traits</span></a></li><li><a class="tocitem" href="#man-array-and-vectorized-operators-and-functions"><span>Array and Vectorized Operators and Functions</span></a></li><li><a class="tocitem" href="#Broadcasting"><span>Broadcasting</span></a></li><li><a class="tocitem" href="#Implementation"><span>Implementation</span></a></li></ul></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="arrays.html">Single- and multi-dimensional Arrays</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="arrays.html">Single- and multi-dimensional Arrays</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/arrays.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="man-multi-dim-arrays"><a class="docs-heading-anchor" href="#man-multi-dim-arrays">Single- and multi-dimensional Arrays</a><a id="man-multi-dim-arrays-1"></a><a class="docs-heading-anchor-permalink" href="#man-multi-dim-arrays" title="Permalink"></a></h1><p>Julia, like most technical computing languages, provides a first-class array implementation. Most technical computing languages pay a lot of attention to their array implementation at the expense of other containers. Julia does not treat arrays in any special way. The array library is implemented almost completely in Julia itself, and derives its performance from the compiler, just like any other code written in Julia. As such, it&#39;s also possible to define custom array types by inheriting from <a href="../base/arrays.html#Core.AbstractArray"><code>AbstractArray</code></a>. See the <a href="interfaces.html#man-interface-array">manual section on the AbstractArray interface</a> for more details on implementing a custom array type.</p><p>An array is a collection of objects stored in a multi-dimensional grid. Zero-dimensional arrays are allowed, see <a href="faq.html#faq-array-0dim">this FAQ entry</a>. In the most general case, an array may contain objects of type <a href="../base/base.html#Core.Any"><code>Any</code></a>. For most computational purposes, arrays should contain objects of a more specific type, such as <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> or <a href="../base/numbers.html#Core.Int32"><code>Int32</code></a>.</p><p>In general, unlike many other technical computing languages, Julia does not expect programs to be written in a vectorized style for performance. Julia&#39;s compiler uses type inference and generates optimized code for scalar array indexing, allowing programs to be written in a style that is convenient and readable, without sacrificing performance, and using less memory at times.</p><p>In Julia, all arguments to functions are <a href="https://en.wikipedia.org/wiki/Evaluation_strategy#Call_by_sharing">passed by sharing</a> (i.e. by pointers). Some technical computing languages pass arrays by value, and while this prevents accidental modification by callees of a value in the caller, it makes avoiding unwanted copying of arrays difficult. By convention, a function name ending with a <code>!</code> indicates that it will mutate or destroy the value of one or more of its arguments (compare, for example, <a href="../base/sort.html#Base.sort"><code>sort</code></a> and <a href="../base/sort.html#Base.sort!"><code>sort!</code></a>). Callees must make explicit copies to ensure that they don&#39;t modify inputs that they don&#39;t intend to change. Many non-mutating functions are implemented by calling a function of the same name with an added <code>!</code> at the end on an explicit copy of the input, and returning that copy.</p><h2 id="Basic-Functions"><a class="docs-heading-anchor" href="#Basic-Functions">Basic Functions</a><a id="Basic-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Basic-Functions" title="Permalink"></a></h2><table><tr><th style="text-align: left">Function</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left"><a href="../base/collections.html#Base.eltype"><code>eltype(A)</code></a></td><td style="text-align: left">the type of the elements contained in <code>A</code></td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.length-Tuple{AbstractArray}"><code>length(A)</code></a></td><td style="text-align: left">the number of elements in <code>A</code></td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.ndims"><code>ndims(A)</code></a></td><td style="text-align: left">the number of dimensions of <code>A</code></td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.size"><code>size(A)</code></a></td><td style="text-align: left">a tuple containing the dimensions of <code>A</code></td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.size"><code>size(A,n)</code></a></td><td style="text-align: left">the size of <code>A</code> along dimension <code>n</code></td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.axes-Tuple{Any}"><code>axes(A)</code></a></td><td style="text-align: left">a tuple containing the valid indices of <code>A</code></td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.axes-Tuple{Any}"><code>axes(A,n)</code></a></td><td style="text-align: left">a range expressing the valid indices along dimension <code>n</code></td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.eachindex"><code>eachindex(A)</code></a></td><td style="text-align: left">an efficient iterator for visiting each position in <code>A</code></td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.stride"><code>stride(A,k)</code></a></td><td style="text-align: left">the stride (linear index distance between adjacent elements) along dimension <code>k</code></td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.strides"><code>strides(A)</code></a></td><td style="text-align: left">a tuple of the strides in each dimension</td></tr></table><h2 id="Construction-and-Initialization"><a class="docs-heading-anchor" href="#Construction-and-Initialization">Construction and Initialization</a><a id="Construction-and-Initialization-1"></a><a class="docs-heading-anchor-permalink" href="#Construction-and-Initialization" title="Permalink"></a></h2><p>Many functions for constructing and initializing arrays are provided. In the following list of such functions, calls with a <code>dims...</code> argument can either take a single tuple of dimension sizes or a series of dimension sizes passed as a variable number of arguments. Most of these functions also accept a first input <code>T</code>, which is the element type of the array. If the type <code>T</code> is omitted it will default to <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a>.</p><table><tr><th style="text-align: left">Function</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Core.Array"><code>Array{T}(undef, dims...)</code></a></td><td style="text-align: left">an uninitialized dense <a href="../base/arrays.html#Core.Array"><code>Array</code></a></td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.zeros"><code>zeros(T, dims...)</code></a></td><td style="text-align: left">an <code>Array</code> of all zeros</td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.ones"><code>ones(T, dims...)</code></a></td><td style="text-align: left">an <code>Array</code> of all ones</td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.trues"><code>trues(dims...)</code></a></td><td style="text-align: left">a <a href="../base/arrays.html#Base.BitArray"><code>BitArray</code></a> with all values <code>true</code></td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.falses"><code>falses(dims...)</code></a></td><td style="text-align: left">a <code>BitArray</code> with all values <code>false</code></td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.reshape"><code>reshape(A, dims...)</code></a></td><td style="text-align: left">an array containing the same data as <code>A</code>, but with different dimensions</td></tr><tr><td style="text-align: left"><a href="../base/base.html#Base.copy"><code>copy(A)</code></a></td><td style="text-align: left">copy <code>A</code></td></tr><tr><td style="text-align: left"><a href="../base/base.html#Base.deepcopy"><code>deepcopy(A)</code></a></td><td style="text-align: left">copy <code>A</code>, recursively copying its elements</td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.similar"><code>similar(A, T, dims...)</code></a></td><td style="text-align: left">an uninitialized array of the same type as <code>A</code> (dense, sparse, etc.), but with the specified element type and dimensions. The second and third arguments are both optional, defaulting to the element type and dimensions of <code>A</code> if omitted.</td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.reinterpret"><code>reinterpret(T, A)</code></a></td><td style="text-align: left">an array with the same binary data as <code>A</code>, but with element type <code>T</code></td></tr><tr><td style="text-align: left"><a href="../stdlib/Random.html#Base.rand"><code>rand(T, dims...)</code></a></td><td style="text-align: left">an <code>Array</code> with random, iid <sup class="footnote-reference"><a id="citeref-1" href="#footnote-1">[1]</a></sup> and uniformly distributed values. For floating point types <code>T</code>, the values lie in the half-open interval <span>$[0, 1)$</span>.</td></tr><tr><td style="text-align: left"><a href="../stdlib/Random.html#Base.randn"><code>randn(T, dims...)</code></a></td><td style="text-align: left">an <code>Array</code> with random, iid and standard normally distributed values</td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.Matrix"><code>Matrix{T}(I, m, n)</code></a></td><td style="text-align: left"><code>m</code>-by-<code>n</code> identity matrix. Requires <code>using LinearAlgebra</code> for <a href="../stdlib/LinearAlgebra.html#LinearAlgebra.I"><code>I</code></a>.</td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.range"><code>range(start, stop, n)</code></a></td><td style="text-align: left">a range of <code>n</code> linearly spaced elements from <code>start</code> to <code>stop</code></td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.fill!"><code>fill!(A, x)</code></a></td><td style="text-align: left">fill the array <code>A</code> with the value <code>x</code></td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.fill"><code>fill(x, dims...)</code></a></td><td style="text-align: left">an <code>Array</code> filled with the value <code>x</code>. In particular, <code>fill(x)</code> constructs a zero-dimensional <code>Array</code> containing <code>x</code>.</td></tr></table><p>To see the various ways we can pass dimensions to these functions, consider the following examples:</p><pre><code class="language-julia-repl hljs">julia&gt; zeros(Int8, 2, 3)
2×3 Matrix{Int8}:
 0  0  0
 0  0  0

julia&gt; zeros(Int8, (2, 3))
2×3 Matrix{Int8}:
 0  0  0
 0  0  0

julia&gt; zeros((2, 3))
2×3 Matrix{Float64}:
 0.0  0.0  0.0
 0.0  0.0  0.0</code></pre><p>Here, <code>(2, 3)</code> is a <a href="../base/base.html#Core.Tuple"><code>Tuple</code></a> and the first argument — the element type — is optional, defaulting to <code>Float64</code>.</p><h2 id="man-array-literals"><a class="docs-heading-anchor" href="#man-array-literals">Array literals</a><a id="man-array-literals-1"></a><a class="docs-heading-anchor-permalink" href="#man-array-literals" title="Permalink"></a></h2><p>Arrays can also be directly constructed with square braces; the syntax <code>[A, B, C, ...]</code> creates a one-dimensional array (i.e., a vector) containing the comma-separated arguments as its elements. The element type (<a href="../base/collections.html#Base.eltype"><code>eltype</code></a>) of the resulting array is automatically determined by the types of the arguments inside the braces. If all the arguments are the same type, then that is its <code>eltype</code>. If they all have a common <a href="conversion-and-promotion.html#conversion-and-promotion">promotion type</a> then they get converted to that type using <a href="../base/base.html#Base.convert"><code>convert</code></a> and that type is the array&#39;s <code>eltype</code>. Otherwise, a heterogeneous array that can hold anything — a <code>Vector{Any}</code> — is constructed; this includes the literal <code>[]</code> where no arguments are given. <a href="arrays.html#man-array-typed-literal">Array literal can be typed</a> with the syntax <code>T[A, B, C, ...]</code> where <code>T</code> is a type.</p><pre><code class="language-julia-repl hljs">julia&gt; [1, 2, 3] # An array of `Int`s
3-element Vector{Int64}:
 1
 2
 3

julia&gt; promote(1, 2.3, 4//5) # This combination of Int, Float64 and Rational promotes to Float64
(1.0, 2.3, 0.8)

julia&gt; [1, 2.3, 4//5] # Thus that&#39;s the element type of this Array
3-element Vector{Float64}:
 1.0
 2.3
 0.8

julia&gt; Float32[1, 2.3, 4//5] # Specify element type manually
3-element Vector{Float32}:
 1.0
 2.3
 0.8

julia&gt; []
Any[]</code></pre><h3 id="man-array-concatenation"><a class="docs-heading-anchor" href="#man-array-concatenation">Concatenation</a><a id="man-array-concatenation-1"></a><a class="docs-heading-anchor-permalink" href="#man-array-concatenation" title="Permalink"></a></h3><p>If the arguments inside the square brackets are separated by single semicolons (<code>;</code>) or newlines instead of commas, then their contents are <em>vertically concatenated</em> together instead of the arguments being used as elements themselves.</p><pre><code class="language-julia-repl hljs">julia&gt; [1:2, 4:5] # Has a comma, so no concatenation occurs. The ranges are themselves the elements
2-element Vector{UnitRange{Int64}}:
 1:2
 4:5

julia&gt; [1:2; 4:5]
4-element Vector{Int64}:
 1
 2
 4
 5

julia&gt; [1:2
        4:5
        6]
5-element Vector{Int64}:
 1
 2
 4
 5
 6</code></pre><p>Similarly, if the arguments are separated by tabs or spaces or double semicolons, then their contents are <em>horizontally concatenated</em> together.</p><pre><code class="language-julia-repl hljs">julia&gt; [1:2  4:5  7:8]
2×3 Matrix{Int64}:
 1  4  7
 2  5  8

julia&gt; [[1,2]  [4,5]  [7,8]]
2×3 Matrix{Int64}:
 1  4  7
 2  5  8

julia&gt; [1 2 3] # Numbers can also be horizontally concatenated
1×3 Matrix{Int64}:
 1  2  3

julia&gt; [1;; 2;; 3;; 4]
1×4 Matrix{Int64}:
 1  2  3  4</code></pre><p>Single semicolons (or newlines) and spaces (or tabs) can be combined to concatenate both horizontally and vertically at the same time.</p><pre><code class="language-julia-repl hljs">julia&gt; [1 2
        3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; [zeros(Int, 2, 2) [1; 2]
        [3 4]            5]
3×3 Matrix{Int64}:
 0  0  1
 0  0  2
 3  4  5

julia&gt; [[1 1]; 2 3; [4 4]]
3×2 Matrix{Int64}:
 1  1
 2  3
 4  4</code></pre><p>Spaces (and tabs) have a higher precedence than semicolons, performing any horizontal concatenations first and then concatenating the result. Using double semicolons for the horizontal concatenation, on the other hand, performs any vertical concatenations before horizontally concatenating the result.</p><pre><code class="language-julia-repl hljs">julia&gt; [zeros(Int, 2, 2) ; [3 4] ;; [1; 2] ; 5]
3×3 Matrix{Int64}:
 0  0  1
 0  0  2
 3  4  5

julia&gt; [1:2; 4;; 1; 3:4]
3×2 Matrix{Int64}:
 1  1
 2  3
 4  4</code></pre><p>Just as <code>;</code> and <code>;;</code> concatenate in the first and second dimension, using more semicolons extends this same general scheme. The number of semicolons in the separator specifies the particular dimension, so <code>;;;</code> concatenates in the third dimension, <code>;;;;</code> in the 4th, and so on. Fewer semicolons take precedence, so the lower dimensions are generally concatenated first.</p><pre><code class="language-julia-repl hljs">julia&gt; [1; 2;; 3; 4;; 5; 6;;;
        7; 8;; 9; 10;; 11; 12]
2×3×2 Array{Int64, 3}:
[:, :, 1] =
 1  3  5
 2  4  6

[:, :, 2] =
 7   9  11
 8  10  12</code></pre><p>Like before, spaces (and tabs) for horizontal concatenation have a higher precedence than any number of semicolons. Thus, higher-dimensional arrays can also be written by specifying their rows first, with their elements textually arranged in a manner similar to their layout:</p><pre><code class="language-julia-repl hljs">julia&gt; [1 3 5
        2 4 6;;;
        7 9 11
        8 10 12]
2×3×2 Array{Int64, 3}:
[:, :, 1] =
 1  3  5
 2  4  6

[:, :, 2] =
 7   9  11
 8  10  12

julia&gt; [1 2;;; 3 4;;;; 5 6;;; 7 8]
1×2×2×2 Array{Int64, 4}:
[:, :, 1, 1] =
 1  2

[:, :, 2, 1] =
 3  4

[:, :, 1, 2] =
 5  6

[:, :, 2, 2] =
 7  8

julia&gt; [[1 2;;; 3 4];;;; [5 6];;; [7 8]]
1×2×2×2 Array{Int64, 4}:
[:, :, 1, 1] =
 1  2

[:, :, 2, 1] =
 3  4

[:, :, 1, 2] =
 5  6

[:, :, 2, 2] =
 7  8</code></pre><p>Although they both mean concatenation in the second dimension, spaces (or tabs) and <code>;;</code> cannot appear in the same array expression unless the double semicolon is simply serving as a &quot;line continuation&quot; character. This allows a single horizontal concatenation to span multiple lines (without the line break being interpreted as a vertical concatenation).</p><pre><code class="language-julia-repl hljs">julia&gt; [1 2 ;;
       3 4]
1×4 Matrix{Int64}:
 1  2  3  4</code></pre><p>Terminating semicolons may also be used to add trailing length 1 dimensions.</p><pre><code class="language-julia-repl hljs">julia&gt; [1;;]
1×1 Matrix{Int64}:
 1

julia&gt; [2; 3;;;]
2×1×1 Array{Int64, 3}:
[:, :, 1] =
 2
 3</code></pre><p>More generally, concatenation can be accomplished through the <a href="../base/arrays.html#Base.cat"><code>cat</code></a> function. These syntaxes are shorthands for function calls that themselves are convenience functions:</p><table><tr><th style="text-align: left">Syntax</th><th style="text-align: left">Function</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left"></td><td style="text-align: left"><a href="../base/arrays.html#Base.cat"><code>cat</code></a></td><td style="text-align: left">concatenate input arrays along dimension(s) <code>k</code></td></tr><tr><td style="text-align: left"><code>[A; B; C; ...]</code></td><td style="text-align: left"><a href="../base/arrays.html#Base.vcat"><code>vcat</code></a></td><td style="text-align: left">shorthand for <code>cat(A...; dims=1)</code></td></tr><tr><td style="text-align: left"><code>[A B C ...]</code></td><td style="text-align: left"><a href="../base/arrays.html#Base.hcat"><code>hcat</code></a></td><td style="text-align: left">shorthand for <code>cat(A...; dims=2)</code></td></tr><tr><td style="text-align: left"><code>[A B; C D; ...]</code></td><td style="text-align: left"><a href="../base/arrays.html#Base.hvcat"><code>hvcat</code></a></td><td style="text-align: left">simultaneous vertical and horizontal concatenation</td></tr><tr><td style="text-align: left"><code>[A; C;; B; D;;; ...]</code></td><td style="text-align: left"><a href="../base/arrays.html#Base.hvncat"><code>hvncat</code></a></td><td style="text-align: left">simultaneous n-dimensional concatenation, where number of semicolons indicate the dimension to concatenate</td></tr></table><h3 id="man-array-typed-literal"><a class="docs-heading-anchor" href="#man-array-typed-literal">Typed array literals</a><a id="man-array-typed-literal-1"></a><a class="docs-heading-anchor-permalink" href="#man-array-typed-literal" title="Permalink"></a></h3><p>An array with a specific element type can be constructed using the syntax <code>T[A, B, C, ...]</code>. This will construct a 1-d array with element type <code>T</code>, initialized to contain elements <code>A</code>, <code>B</code>, <code>C</code>, etc. For example, <code>Any[x, y, z]</code> constructs a heterogeneous array that can contain any values.</p><p>Concatenation syntax can similarly be prefixed with a type to specify the element type of the result.</p><pre><code class="language-julia-repl hljs">julia&gt; [[1 2] [3 4]]
1×4 Matrix{Int64}:
 1  2  3  4

julia&gt; Int8[[1 2] [3 4]]
1×4 Matrix{Int8}:
 1  2  3  4</code></pre><h2 id="man-comprehensions"><a class="docs-heading-anchor" href="#man-comprehensions">Comprehensions</a><a id="man-comprehensions-1"></a><a class="docs-heading-anchor-permalink" href="#man-comprehensions" title="Permalink"></a></h2><p>Comprehensions provide a general and powerful way to construct arrays. Comprehension syntax is similar to set construction notation in mathematics:</p><pre><code class="nohighlight hljs">A = [ F(x, y, ...) for x=rx, y=ry, ... ]</code></pre><p>The meaning of this form is that <code>F(x,y,...)</code> is evaluated with the variables <code>x</code>, <code>y</code>, etc. taking on each value in their given list of values. Values can be specified as any iterable object, but will commonly be ranges like <code>1:n</code> or <code>2:(n-1)</code>, or explicit arrays of values like <code>[1.2, 3.4, 5.7]</code>. The result is an N-d dense array with dimensions that are the concatenation of the dimensions of the variable ranges <code>rx</code>, <code>ry</code>, etc. and each <code>F(x,y,...)</code> evaluation returns a scalar.</p><p>The following example computes a weighted average of the current element and its left and right neighbor along a 1-d grid. :</p><pre><code class="language-julia-repl hljs">julia&gt; x = rand(8)
8-element Array{Float64,1}:
 0.843025
 0.869052
 0.365105
 0.699456
 0.977653
 0.994953
 0.41084
 0.809411

julia&gt; [ 0.25*x[i-1] + 0.5*x[i] + 0.25*x[i+1] for i=2:length(x)-1 ]
6-element Array{Float64,1}:
 0.736559
 0.57468
 0.685417
 0.912429
 0.8446
 0.656511</code></pre><p>The resulting array type depends on the types of the computed elements just like <a href="arrays.html#man-array-literals">array literals</a> do. In order to control the type explicitly, a type can be prepended to the comprehension. For example, we could have requested the result in single precision by writing:</p><pre><code class="language-julia hljs">Float32[ 0.25*x[i-1] + 0.5*x[i] + 0.25*x[i+1] for i=2:length(x)-1 ]</code></pre><h2 id="man-generators"><a class="docs-heading-anchor" href="#man-generators">Generator Expressions</a><a id="man-generators-1"></a><a class="docs-heading-anchor-permalink" href="#man-generators" title="Permalink"></a></h2><p>Comprehensions can also be written without the enclosing square brackets, producing an object known as a generator. This object can be iterated to produce values on demand, instead of allocating an array and storing them in advance (see <a href="arrays.html#Iteration">Iteration</a>). For example, the following expression sums a series without allocating memory:</p><pre><code class="language-julia-repl hljs">julia&gt; sum(1/n^2 for n=1:1000)
1.6439345666815615</code></pre><p>When writing a generator expression with multiple dimensions inside an argument list, parentheses are needed to separate the generator from subsequent arguments:</p><pre><code class="language-julia-repl hljs">julia&gt; map(tuple, 1/(i+j) for i=1:2, j=1:2, [1:4;])
ERROR: syntax: invalid iteration specification</code></pre><p>All comma-separated expressions after <code>for</code> are interpreted as ranges. Adding parentheses lets us add a third argument to <a href="../base/collections.html#Base.map"><code>map</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; map(tuple, (1/(i+j) for i=1:2, j=1:2), [1 3; 2 4])
2×2 Matrix{Tuple{Float64, Int64}}:
 (0.5, 1)       (0.333333, 3)
 (0.333333, 2)  (0.25, 4)</code></pre><p>Generators are implemented via inner functions. Just like inner functions used elsewhere in the language, variables from the enclosing scope can be &quot;captured&quot; in the inner function.  For example, <code>sum(p[i] - q[i] for i=1:n)</code> captures the three variables <code>p</code>, <code>q</code> and <code>n</code> from the enclosing scope. Captured variables can present performance challenges; see <a href="performance-tips.html#man-performance-captured">performance tips</a>.</p><p>Ranges in generators and comprehensions can depend on previous ranges by writing multiple <code>for</code> keywords:</p><pre><code class="language-julia-repl hljs">julia&gt; [(i, j) for i=1:3 for j=1:i]
6-element Vector{Tuple{Int64, Int64}}:
 (1, 1)
 (2, 1)
 (2, 2)
 (3, 1)
 (3, 2)
 (3, 3)</code></pre><p>In such cases, the result is always 1-d.</p><p>Generated values can be filtered using the <code>if</code> keyword:</p><pre><code class="language-julia-repl hljs">julia&gt; [(i, j) for i=1:3 for j=1:i if i+j == 4]
2-element Vector{Tuple{Int64, Int64}}:
 (2, 2)
 (3, 1)</code></pre><h2 id="man-array-indexing"><a class="docs-heading-anchor" href="#man-array-indexing">Indexing</a><a id="man-array-indexing-1"></a><a class="docs-heading-anchor-permalink" href="#man-array-indexing" title="Permalink"></a></h2><p>The general syntax for indexing into an n-dimensional array <code>A</code> is:</p><pre><code class="nohighlight hljs">X = A[I_1, I_2, ..., I_n]</code></pre><p>where each <code>I_k</code> may be a scalar integer, an array of integers, or any other <a href="arrays.html#man-supported-index-types">supported index</a>. This includes <a href="../base/arrays.html#Base.Colon"><code>Colon</code></a> (<code>:</code>) to select all indices within the entire dimension, ranges of the form <code>a:c</code> or <code>a:b:c</code> to select contiguous or strided subsections, and arrays of booleans to select elements at their <code>true</code> indices.</p><p>If all the indices are scalars, then the result <code>X</code> is a single element from the array <code>A</code>. Otherwise, <code>X</code> is an array with the same number of dimensions as the sum of the dimensionalities of all the indices.</p><p>If all indices <code>I_k</code> are vectors, for example, then the shape of <code>X</code> would be <code>(length(I_1), length(I_2), ..., length(I_n))</code>, with location <code>i_1, i_2, ..., i_n</code> of <code>X</code> containing the value <code>A[I_1[i_1], I_2[i_2], ..., I_n[i_n]]</code>.</p><p>Example:</p><pre><code class="language-julia-repl hljs">julia&gt; A = reshape(collect(1:16), (2, 2, 2, 2))
2×2×2×2 Array{Int64, 4}:
[:, :, 1, 1] =
 1  3
 2  4

[:, :, 2, 1] =
 5  7
 6  8

[:, :, 1, 2] =
  9  11
 10  12

[:, :, 2, 2] =
 13  15
 14  16

julia&gt; A[1, 2, 1, 1] # all scalar indices
3

julia&gt; A[[1, 2], [1], [1, 2], [1]] # all vector indices
2×1×2×1 Array{Int64, 4}:
[:, :, 1, 1] =
 1
 2

[:, :, 2, 1] =
 5
 6

julia&gt; A[[1, 2], [1], [1, 2], 1] # a mix of index types
2×1×2 Array{Int64, 3}:
[:, :, 1] =
 1
 2

[:, :, 2] =
 5
 6</code></pre><p>Note how the size of the resulting array is different in the last two cases.</p><p>If <code>I_1</code> is changed to a two-dimensional matrix, then <code>X</code> becomes an <code>n+1</code>-dimensional array of shape <code>(size(I_1, 1), size(I_1, 2), length(I_2), ..., length(I_n))</code>. The matrix adds a dimension.</p><p>Example:</p><pre><code class="language-julia-repl hljs">julia&gt; A = reshape(collect(1:16), (2, 2, 2, 2));

julia&gt; A[[1 2; 1 2]]
2×2 Matrix{Int64}:
 1  2
 1  2

julia&gt; A[[1 2; 1 2], 1, 2, 1]
2×2 Matrix{Int64}:
 5  6
 5  6</code></pre><p>The location <code>i_1, i_2, i_3, ..., i_{n+1}</code> contains the value at <code>A[I_1[i_1, i_2], I_2[i_3], ..., I_n[i_{n+1}]]</code>. All dimensions indexed with scalars are dropped. For example, if <code>J</code> is an array of indices, then the result of <code>A[2, J, 3]</code> is an array with size <code>size(J)</code>. Its <code>j</code>th element is populated by <code>A[2, J[j], 3]</code>.</p><p>As a special part of this syntax, the <code>end</code> keyword may be used to represent the last index of each dimension within the indexing brackets, as determined by the size of the innermost array being indexed. Indexing syntax without the <code>end</code> keyword is equivalent to a call to <a href="../base/collections.html#Base.getindex"><code>getindex</code></a>:</p><pre><code class="nohighlight hljs">X = getindex(A, I_1, I_2, ..., I_n)</code></pre><p>Example:</p><pre><code class="language-julia-repl hljs">julia&gt; x = reshape(1:16, 4, 4)
4×4 reshape(::UnitRange{Int64}, 4, 4) with eltype Int64:
 1  5   9  13
 2  6  10  14
 3  7  11  15
 4  8  12  16

julia&gt; x[2:3, 2:end-1]
2×2 Matrix{Int64}:
 6  10
 7  11

julia&gt; x[1, [2 3; 4 1]]
2×2 Matrix{Int64}:
  5  9
 13  1</code></pre><h2 id="man-indexed-assignment"><a class="docs-heading-anchor" href="#man-indexed-assignment">Indexed Assignment</a><a id="man-indexed-assignment-1"></a><a class="docs-heading-anchor-permalink" href="#man-indexed-assignment" title="Permalink"></a></h2><p>The general syntax for assigning values in an n-dimensional array <code>A</code> is:</p><pre><code class="nohighlight hljs">A[I_1, I_2, ..., I_n] = X</code></pre><p>where each <code>I_k</code> may be a scalar integer, an array of integers, or any other <a href="arrays.html#man-supported-index-types">supported index</a>. This includes <a href="../base/arrays.html#Base.Colon"><code>Colon</code></a> (<code>:</code>) to select all indices within the entire dimension, ranges of the form <code>a:c</code> or <code>a:b:c</code> to select contiguous or strided subsections, and arrays of booleans to select elements at their <code>true</code> indices.</p><p>If all indices <code>I_k</code> are integers, then the value in location <code>I_1, I_2, ..., I_n</code> of <code>A</code> is overwritten with the value of <code>X</code>, <a href="../base/base.html#Base.convert"><code>convert</code></a>ing to the <a href="../base/collections.html#Base.eltype"><code>eltype</code></a> of <code>A</code> if necessary.</p><p>If any index <code>I_k</code> is itself an array, then the right hand side <code>X</code> must also be an array with the same shape as the result of indexing <code>A[I_1, I_2, ..., I_n]</code> or a vector with the same number of elements. The value in location <code>I_1[i_1], I_2[i_2], ..., I_n[i_n]</code> of <code>A</code> is overwritten with the value <code>X[i_1, i_2, ..., i_n]</code>, converting if necessary. The element-wise assignment operator <code>.=</code> may be used to <a href="arrays.html#Broadcasting">broadcast</a> <code>X</code> across the selected locations:</p><pre><code class="nohighlight hljs">A[I_1, I_2, ..., I_n] .= X</code></pre><p>Just as in <a href="arrays.html#man-array-indexing">Indexing</a>, the <code>end</code> keyword may be used to represent the last index of each dimension within the indexing brackets, as determined by the size of the array being assigned into. Indexed assignment syntax without the <code>end</code> keyword is equivalent to a call to <a href="../base/collections.html#Base.setindex!"><code>setindex!</code></a>:</p><pre><code class="nohighlight hljs">setindex!(A, X, I_1, I_2, ..., I_n)</code></pre><p>Example:</p><pre><code class="language-julia-repl hljs">julia&gt; x = collect(reshape(1:9, 3, 3))
3×3 Matrix{Int64}:
 1  4  7
 2  5  8
 3  6  9

julia&gt; x[3, 3] = -9;

julia&gt; x[1:2, 1:2] = [-1 -4; -2 -5];

julia&gt; x
3×3 Matrix{Int64}:
 -1  -4   7
 -2  -5   8
  3   6  -9</code></pre><h2 id="man-supported-index-types"><a class="docs-heading-anchor" href="#man-supported-index-types">Supported index types</a><a id="man-supported-index-types-1"></a><a class="docs-heading-anchor-permalink" href="#man-supported-index-types" title="Permalink"></a></h2><p>In the expression <code>A[I_1, I_2, ..., I_n]</code>, each <code>I_k</code> may be a scalar index, an array of scalar indices, or an object that represents an array of scalar indices and can be converted to such by <a href="../base/arrays.html#Base.to_indices"><code>to_indices</code></a>:</p><ol><li>A scalar index. By default this includes:<ul><li>Non-boolean integers</li><li><a href="../base/arrays.html#Base.IteratorsMD.CartesianIndex"><code>CartesianIndex{N}</code></a>s, which behave like an <code>N</code>-tuple of integers spanning multiple dimensions (see below for more details)</li></ul></li><li>An array of scalar indices. This includes:<ul><li>Vectors and multidimensional arrays of integers</li><li>Empty arrays like <code>[]</code>, which select no elements e.g. <code>A[[]]</code> (not to be confused with <code>A[]</code>)</li><li>Ranges like <code>a:c</code> or <code>a:b:c</code>, which select contiguous or strided subsections from <code>a</code> to <code>c</code> (inclusive)</li><li>Any custom array of scalar indices that is a subtype of <code>AbstractArray</code></li><li>Arrays of <code>CartesianIndex{N}</code> (see below for more details)</li></ul></li><li>An object that represents an array of scalar indices and can be converted to such by <a href="../base/arrays.html#Base.to_indices"><code>to_indices</code></a>. By default this includes:<ul><li><a href="../base/arrays.html#Base.Colon"><code>Colon()</code></a> (<code>:</code>), which represents all indices within an entire dimension or across the entire array</li><li>Arrays of booleans, which select elements at their <code>true</code> indices (see below for more details)</li></ul></li></ol><p>Some examples:</p><pre><code class="language-julia-repl hljs">julia&gt; A = reshape(collect(1:2:18), (3, 3))
3×3 Matrix{Int64}:
 1   7  13
 3   9  15
 5  11  17

julia&gt; A[4]
7

julia&gt; A[[2, 5, 8]]
3-element Vector{Int64}:
  3
  9
 15

julia&gt; A[[1 4; 3 8]]
2×2 Matrix{Int64}:
 1   7
 5  15

julia&gt; A[[]]
Int64[]

julia&gt; A[1:2:5]
3-element Vector{Int64}:
 1
 5
 9

julia&gt; A[2, :]
3-element Vector{Int64}:
  3
  9
 15

julia&gt; A[:, 3]
3-element Vector{Int64}:
 13
 15
 17

julia&gt; A[:, 3:3]
3×1 Matrix{Int64}:
 13
 15
 17</code></pre><h3 id="Cartesian-indices"><a class="docs-heading-anchor" href="#Cartesian-indices">Cartesian indices</a><a id="Cartesian-indices-1"></a><a class="docs-heading-anchor-permalink" href="#Cartesian-indices" title="Permalink"></a></h3><p>The special <code>CartesianIndex{N}</code> object represents a scalar index that behaves like an <code>N</code>-tuple of integers spanning multiple dimensions.  For example:</p><pre><code class="language-julia-repl hljs">julia&gt; A = reshape(1:32, 4, 4, 2);

julia&gt; A[3, 2, 1]
7

julia&gt; A[CartesianIndex(3, 2, 1)] == A[3, 2, 1] == 7
true</code></pre><p>Considered alone, this may seem relatively trivial; <code>CartesianIndex</code> simply gathers multiple integers together into one object that represents a single multidimensional index. When combined with other indexing forms and iterators that yield <code>CartesianIndex</code>es, however, this can produce very elegant and efficient code. See <a href="arrays.html#Iteration">Iteration</a> below, and for some more advanced examples, see <a href="https://julialang.org/blog/2016/02/iteration">this blog post on multidimensional algorithms and iteration</a>.</p><p>Arrays of <code>CartesianIndex{N}</code> are also supported. They represent a collection of scalar indices that each span <code>N</code> dimensions, enabling a form of indexing that is sometimes referred to as pointwise indexing. For example, it enables accessing the diagonal elements from the first &quot;page&quot; of <code>A</code> from above:</p><pre><code class="language-julia-repl hljs">julia&gt; page = A[:, :, 1]
4×4 Matrix{Int64}:
 1  5   9  13
 2  6  10  14
 3  7  11  15
 4  8  12  16

julia&gt; page[[CartesianIndex(1, 1),
             CartesianIndex(2, 2),
             CartesianIndex(3, 3),
             CartesianIndex(4, 4)]]
4-element Vector{Int64}:
  1
  6
 11
 16</code></pre><p>This can be expressed much more simply with <a href="functions.html#man-vectorized">dot broadcasting</a> and by combining it with a normal integer index (instead of extracting the first <code>page</code> from <code>A</code> as a separate step). It can even be combined with a <code>:</code> to extract both diagonals from the two pages at the same time:</p><pre><code class="language-julia-repl hljs">julia&gt; A[CartesianIndex.(axes(A, 1), axes(A, 2)), 1]
4-element Vector{Int64}:
  1
  6
 11
 16

julia&gt; A[CartesianIndex.(axes(A, 1), axes(A, 2)), :]
4×2 Matrix{Int64}:
  1  17
  6  22
 11  27
 16  32</code></pre><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p><code>CartesianIndex</code> and arrays of <code>CartesianIndex</code> are not compatible with the <code>end</code> keyword to represent the last index of a dimension. Do not use <code>end</code> in indexing expressions that may contain either <code>CartesianIndex</code> or arrays thereof.</p></div></div><h3 id="Logical-indexing"><a class="docs-heading-anchor" href="#Logical-indexing">Logical indexing</a><a id="Logical-indexing-1"></a><a class="docs-heading-anchor-permalink" href="#Logical-indexing" title="Permalink"></a></h3><p>Often referred to as logical indexing or indexing with a logical mask, indexing by a boolean array selects elements at the indices where its values are <code>true</code>. Indexing by a boolean vector <code>B</code> is effectively the same as indexing by the vector of integers that is returned by <a href="../base/arrays.html#Base.findall-Tuple{Any}"><code>findall(B)</code></a>. Similarly, indexing by a <code>N</code>-dimensional boolean array is effectively the same as indexing by the vector of <code>CartesianIndex{N}</code>s where its values are <code>true</code>. A logical index must be a array of the same shape as the dimension(s) it indexes into, or it must be the only index provided and match the shape of the one-dimensional reshaped view of the array it indexes into. It is generally more efficient to use boolean arrays as indices directly instead of first calling <a href="../base/arrays.html#Base.findall-Tuple{Any}"><code>findall</code></a>.</p><pre><code class="language-julia-repl hljs">julia&gt; x = reshape(1:12, 2, 3, 2)
2×3×2 reshape(::UnitRange{Int64}, 2, 3, 2) with eltype Int64:
[:, :, 1] =
 1  3  5
 2  4  6

[:, :, 2] =
 7   9  11
 8  10  12

julia&gt; x[:, [true false; false true; true false]]
2×3 Matrix{Int64}:
 1  5   9
 2  6  10

julia&gt; mask = map(ispow2, x)
2×3×2 Array{Bool, 3}:
[:, :, 1] =
 1  0  0
 1  1  0

[:, :, 2] =
 0  0  0
 1  0  0

julia&gt; x[mask]
4-element Vector{Int64}:
 1
 2
 4
 8

julia&gt; x[vec(mask)] == x[mask] # we can also index with a single Boolean vector
true</code></pre><h3 id="Number-of-indices"><a class="docs-heading-anchor" href="#Number-of-indices">Number of indices</a><a id="Number-of-indices-1"></a><a class="docs-heading-anchor-permalink" href="#Number-of-indices" title="Permalink"></a></h3><h4 id="Cartesian-indexing"><a class="docs-heading-anchor" href="#Cartesian-indexing">Cartesian indexing</a><a id="Cartesian-indexing-1"></a><a class="docs-heading-anchor-permalink" href="#Cartesian-indexing" title="Permalink"></a></h4><p>The ordinary way to index into an <code>N</code>-dimensional array is to use exactly <code>N</code> indices; each index selects the position(s) in its particular dimension. For example, in the three-dimensional array <code>A = rand(4, 3, 2)</code>, <code>A[2, 3, 1]</code> will select the number in the second row of the third column in the first &quot;page&quot; of the array. This is often referred to as <em>cartesian indexing</em>.</p><h4 id="Linear-indexing"><a class="docs-heading-anchor" href="#Linear-indexing">Linear indexing</a><a id="Linear-indexing-1"></a><a class="docs-heading-anchor-permalink" href="#Linear-indexing" title="Permalink"></a></h4><p>When exactly one index <code>i</code> is provided, that index no longer represents a location in a particular dimension of the array. Instead, it selects the <code>i</code>th element using the column-major iteration order that linearly spans the entire array. This is known as <em>linear indexing</em>. It essentially treats the array as though it had been reshaped into a one-dimensional vector with <a href="../base/arrays.html#Base.vec"><code>vec</code></a>.</p><pre><code class="language-julia-repl hljs">julia&gt; A = [2 6; 4 7; 3 1]
3×2 Matrix{Int64}:
 2  6
 4  7
 3  1

julia&gt; A[5]
7

julia&gt; vec(A)[5]
7</code></pre><p>A linear index into the array <code>A</code> can be converted to a <code>CartesianIndex</code> for cartesian indexing with <code>CartesianIndices(A)[i]</code> (see <a href="../base/arrays.html#Base.IteratorsMD.CartesianIndices"><code>CartesianIndices</code></a>), and a set of <code>N</code> cartesian indices can be converted to a linear index with <code>LinearIndices(A)[i_1, i_2, ..., i_N]</code> (see <a href="../base/arrays.html#Base.LinearIndices"><code>LinearIndices</code></a>).</p><pre><code class="language-julia-repl hljs">julia&gt; CartesianIndices(A)[5]
CartesianIndex(2, 2)

julia&gt; LinearIndices(A)[2, 2]
5</code></pre><p>It&#39;s important to note that there&#39;s a very large asymmetry in the performance of these conversions. Converting a linear index to a set of cartesian indices requires dividing and taking the remainder, whereas going the other way is just multiplies and adds. In modern processors, integer division can be 10-50 times slower than multiplication. While some arrays — like <a href="../base/arrays.html#Core.Array"><code>Array</code></a> itself — are implemented using a linear chunk of memory and directly use a linear index in their implementations, other arrays — like <a href="../stdlib/LinearAlgebra.html#LinearAlgebra.Diagonal"><code>Diagonal</code></a> — need the full set of cartesian indices to do their lookup (see <a href="../base/arrays.html#Base.IndexStyle"><code>IndexStyle</code></a> to introspect which is which).</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>When iterating over all the indices for an array, it is better to iterate over <a href="../base/arrays.html#Base.eachindex"><code>eachindex(A)</code></a> instead of <code>1:length(A)</code>. Not only will this be faster in cases where <code>A</code> is <code>IndexCartesian</code>, but it will also support arrays with custom indexing, such as <a href="https://github.com/JuliaArrays/OffsetArrays.jl">OffsetArrays</a>. If only the values are needed, then is better to just iterate the array directly, i.e. <code>for a in A</code>.</p></div></div><h4 id="Omitted-and-extra-indices"><a class="docs-heading-anchor" href="#Omitted-and-extra-indices">Omitted and extra indices</a><a id="Omitted-and-extra-indices-1"></a><a class="docs-heading-anchor-permalink" href="#Omitted-and-extra-indices" title="Permalink"></a></h4><p>In addition to linear indexing, an <code>N</code>-dimensional array may be indexed with fewer or more than <code>N</code> indices in certain situations.</p><p>Indices may be omitted if the trailing dimensions that are not indexed into are all length one. In other words, trailing indices can be omitted only if there is only one possible value that those omitted indices could be for an in-bounds indexing expression. For example, a four-dimensional array with size <code>(3, 4, 2, 1)</code> may be indexed with only three indices as the dimension that gets skipped (the fourth dimension) has length one. Note that linear indexing takes precedence over this rule.</p><pre><code class="language-julia-repl hljs">julia&gt; A = reshape(1:24, 3, 4, 2, 1)
3×4×2×1 reshape(::UnitRange{Int64}, 3, 4, 2, 1) with eltype Int64:
[:, :, 1, 1] =
 1  4  7  10
 2  5  8  11
 3  6  9  12

[:, :, 2, 1] =
 13  16  19  22
 14  17  20  23
 15  18  21  24

julia&gt; A[1, 3, 2] # Omits the fourth dimension (length 1)
19

julia&gt; A[1, 3] # Attempts to omit dimensions 3 &amp; 4 (lengths 2 and 1)
ERROR: BoundsError: attempt to access 3×4×2×1 reshape(::UnitRange{Int64}, 3, 4, 2, 1) with eltype Int64 at index [1, 3]

julia&gt; A[19] # Linear indexing
19</code></pre><p>When omitting <em>all</em> indices with <code>A[]</code>, this semantic provides a simple idiom to retrieve the only element in an array and simultaneously ensure that there was only one element.</p><p>Similarly, more than <code>N</code> indices may be provided if all the indices beyond the dimensionality of the array are <code>1</code> (or more generally are the first and only element of <code>axes(A, d)</code> where <code>d</code> is that particular dimension number). This allows vectors to be indexed like one-column matrices, for example:</p><pre><code class="language-julia-repl hljs">julia&gt; A = [8, 6, 7]
3-element Vector{Int64}:
 8
 6
 7

julia&gt; A[2, 1]
6</code></pre><h2 id="Iteration"><a class="docs-heading-anchor" href="#Iteration">Iteration</a><a id="Iteration-1"></a><a class="docs-heading-anchor-permalink" href="#Iteration" title="Permalink"></a></h2><p>The recommended ways to iterate over a whole array are</p><pre><code class="language-julia hljs">for a in A
    # Do something with the element a
end

for i in eachindex(A)
    # Do something with i and/or A[i]
end</code></pre><p>The first construct is used when you need the value, but not index, of each element. In the second construct, <code>i</code> will be an <code>Int</code> if <code>A</code> is an array type with fast linear indexing; otherwise, it will be a <code>CartesianIndex</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; A = rand(4, 3);

julia&gt; B = view(A, 1:3, 2:3);

julia&gt; for i in eachindex(B)
           @show i
       end
i = CartesianIndex(1, 1)
i = CartesianIndex(2, 1)
i = CartesianIndex(3, 1)
i = CartesianIndex(1, 2)
i = CartesianIndex(2, 2)
i = CartesianIndex(3, 2)</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>In contrast with <code>for i = 1:length(A)</code>, iterating with <a href="../base/arrays.html#Base.eachindex"><code>eachindex</code></a> provides an efficient way to iterate over any array type. Besides, this also supports generic arrays with custom indexing such as <a href="https://github.com/JuliaArrays/OffsetArrays.jl">OffsetArrays</a>.</p></div></div><h2 id="Array-traits"><a class="docs-heading-anchor" href="#Array-traits">Array traits</a><a id="Array-traits-1"></a><a class="docs-heading-anchor-permalink" href="#Array-traits" title="Permalink"></a></h2><p>If you write a custom <a href="../base/arrays.html#Core.AbstractArray"><code>AbstractArray</code></a> type, you can specify that it has fast linear indexing using</p><pre><code class="language-julia hljs">Base.IndexStyle(::Type{&lt;:MyArray}) = IndexLinear()</code></pre><p>This setting will cause <code>eachindex</code> iteration over a <code>MyArray</code> to use integers. If you don&#39;t specify this trait, the default value <code>IndexCartesian()</code> is used.</p><h2 id="man-array-and-vectorized-operators-and-functions"><a class="docs-heading-anchor" href="#man-array-and-vectorized-operators-and-functions">Array and Vectorized Operators and Functions</a><a id="man-array-and-vectorized-operators-and-functions-1"></a><a class="docs-heading-anchor-permalink" href="#man-array-and-vectorized-operators-and-functions" title="Permalink"></a></h2><p>The following operators are supported for arrays:</p><ol><li>Unary arithmetic – <code>-</code>, <code>+</code></li><li>Binary arithmetic – <code>-</code>, <code>+</code>, <code>*</code>, <code>/</code>, <code>\</code>, <code>^</code></li><li>Comparison – <code>==</code>, <code>!=</code>, <code>≈</code> (<a href="../base/math.html#Base.isapprox"><code>isapprox</code></a>), <code>≉</code></li></ol><p>To enable convenient vectorization of mathematical and other operations, Julia <a href="functions.html#man-vectorized">provides the dot syntax</a> <code>f.(args...)</code>, e.g. <code>sin.(x)</code> or <code>min.(x, y)</code>, for elementwise operations over arrays or mixtures of arrays and scalars (a <a href="arrays.html#Broadcasting">Broadcasting</a> operation); these have the additional advantage of &quot;fusing&quot; into a single loop when combined with other dot calls, e.g. <code>sin.(cos.(x))</code>.</p><p>Also, <em>every</em> binary operator supports a <a href="mathematical-operations.html#man-dot-operators">dot version</a> that can be applied to arrays (and combinations of arrays and scalars) in such <a href="functions.html#man-vectorized">fused broadcasting operations</a>, e.g. <code>z .== sin.(x .* y)</code>.</p><p>Note that comparisons such as <code>==</code> operate on whole arrays, giving a single boolean answer. Use dot operators like <code>.==</code> for elementwise comparisons. (For comparison operations like <code>&lt;</code>, <em>only</em> the elementwise <code>.&lt;</code> version is applicable to arrays.)</p><p>Also notice the difference between <code>max.(a,b)</code>, which <a href="../base/arrays.html#Base.Broadcast.broadcast"><code>broadcast</code></a>s <a href="../base/math.html#Base.max"><code>max</code></a> elementwise over <code>a</code> and <code>b</code>, and <a href="../base/collections.html#Base.maximum"><code>maximum(a)</code></a>, which finds the largest value within <code>a</code>. The same relationship holds for <code>min.(a, b)</code> and <code>minimum(a)</code>.</p><h2 id="Broadcasting"><a class="docs-heading-anchor" href="#Broadcasting">Broadcasting</a><a id="Broadcasting-1"></a><a class="docs-heading-anchor-permalink" href="#Broadcasting" title="Permalink"></a></h2><p>It is sometimes useful to perform element-by-element binary operations on arrays of different sizes, such as adding a vector to each column of a matrix. An inefficient way to do this would be to replicate the vector to the size of the matrix:</p><pre><code class="language-julia-repl hljs">julia&gt; a = rand(2, 1); A = rand(2, 3);

julia&gt; repeat(a, 1, 3) + A
2×3 Array{Float64,2}:
 1.20813  1.82068  1.25387
 1.56851  1.86401  1.67846</code></pre><p>This is wasteful when dimensions get large, so Julia provides <a href="../base/arrays.html#Base.Broadcast.broadcast"><code>broadcast</code></a>, which expands singleton dimensions in array arguments to match the corresponding dimension in the other array without using extra memory, and applies the given function elementwise:</p><pre><code class="language-julia-repl hljs">julia&gt; broadcast(+, a, A)
2×3 Array{Float64,2}:
 1.20813  1.82068  1.25387
 1.56851  1.86401  1.67846

julia&gt; b = rand(1,2)
1×2 Array{Float64,2}:
 0.867535  0.00457906

julia&gt; broadcast(+, a, b)
2×2 Array{Float64,2}:
 1.71056  0.847604
 1.73659  0.873631</code></pre><p><a href="mathematical-operations.html#man-dot-operators">Dotted operators</a> such as <code>.+</code> and <code>.*</code> are equivalent to <code>broadcast</code> calls (except that they fuse, as <a href="arrays.html#man-array-and-vectorized-operators-and-functions">described above</a>). There is also a <a href="../base/arrays.html#Base.Broadcast.broadcast!"><code>broadcast!</code></a> function to specify an explicit destination (which can also be accessed in a fusing fashion by <code>.=</code> assignment). In fact, <code>f.(args...)</code> is equivalent to <code>broadcast(f, args...)</code>, providing a convenient syntax to broadcast any function (<a href="functions.html#man-vectorized">dot syntax</a>). Nested &quot;dot calls&quot; <code>f.(...)</code> (including calls to <code>.+</code> etcetera) <a href="mathematical-operations.html#man-dot-operators">automatically fuse</a> into a single <code>broadcast</code> call.</p><p>Additionally, <a href="../base/arrays.html#Base.Broadcast.broadcast"><code>broadcast</code></a> is not limited to arrays (see the function documentation); it also handles scalars, tuples and other collections.  By default, only some argument types are considered scalars, including (but not limited to) <code>Number</code>s, <code>String</code>s, <code>Symbol</code>s, <code>Type</code>s, <code>Function</code>s and some common singletons like <code>missing</code> and <code>nothing</code>. All other arguments are iterated over or indexed into elementwise.</p><pre><code class="language-julia-repl hljs">julia&gt; convert.(Float32, [1, 2])
2-element Vector{Float32}:
 1.0
 2.0

julia&gt; ceil.(UInt8, [1.2 3.4; 5.6 6.7])
2×2 Matrix{UInt8}:
 0x02  0x04
 0x06  0x07

julia&gt; string.(1:3, &quot;. &quot;, [&quot;First&quot;, &quot;Second&quot;, &quot;Third&quot;])
3-element Vector{String}:
 &quot;1. First&quot;
 &quot;2. Second&quot;
 &quot;3. Third&quot;</code></pre><p>Sometimes, you want a container (like an array) that would normally participate in broadcast to be &quot;protected&quot; from broadcast&#39;s behavior of iterating over all of its elements. By placing it inside another container (like a single element <a href="../base/base.html#Core.Tuple"><code>Tuple</code></a>) broadcast will treat it as a single value.</p><pre><code class="language-julia-repl hljs">julia&gt; ([1, 2, 3], [4, 5, 6]) .+ ([1, 2, 3],)
([2, 4, 6], [5, 7, 9])

julia&gt; ([1, 2, 3], [4, 5, 6]) .+ tuple([1, 2, 3])
([2, 4, 6], [5, 7, 9])</code></pre><h2 id="Implementation"><a class="docs-heading-anchor" href="#Implementation">Implementation</a><a id="Implementation-1"></a><a class="docs-heading-anchor-permalink" href="#Implementation" title="Permalink"></a></h2><p>The base array type in Julia is the abstract type <a href="../base/arrays.html#Core.AbstractArray"><code>AbstractArray{T,N}</code></a>. It is parameterized by the number of dimensions <code>N</code> and the element type <code>T</code>. <a href="../base/arrays.html#Base.AbstractVector"><code>AbstractVector</code></a> and <a href="../base/arrays.html#Base.AbstractMatrix"><code>AbstractMatrix</code></a> are aliases for the 1-d and 2-d cases. Operations on <code>AbstractArray</code> objects are defined using higher level operators and functions, in a way that is independent of the underlying storage. These operations generally work correctly as a fallback for any specific array implementation.</p><p>The <code>AbstractArray</code> type includes anything vaguely array-like, and implementations of it might be quite different from conventional arrays. For example, elements might be computed on request rather than stored. However, any concrete <code>AbstractArray{T,N}</code> type should generally implement at least <a href="../base/arrays.html#Base.size"><code>size(A)</code></a> (returning an <code>Int</code> tuple), <a href="../base/arrays.html#Base.getindex-Tuple{Type, Vararg{Any}}"><code>getindex(A, i)</code></a> and <a href="../base/collections.html#Base.getindex"><code>getindex(A, i1, ..., iN)</code></a>; mutable arrays should also implement <a href="../base/collections.html#Base.setindex!"><code>setindex!</code></a>. It is recommended that these operations have nearly constant time complexity, as otherwise some array functions may be unexpectedly slow. Concrete types should also typically provide a <a href="../base/arrays.html#Base.similar"><code>similar(A, T=eltype(A), dims=size(A))</code></a> method, which is used to allocate a similar array for <a href="../base/base.html#Base.copy"><code>copy</code></a> and other out-of-place operations. No matter how an <code>AbstractArray{T,N}</code> is represented internally, <code>T</code> is the type of object returned by <em>integer</em> indexing (<code>A[1, ..., 1]</code>, when <code>A</code> is not empty) and <code>N</code> should be the length of the tuple returned by <a href="../base/arrays.html#Base.size"><code>size</code></a>. For more details on defining custom <code>AbstractArray</code> implementations, see the <a href="interfaces.html#man-interface-array">array interface guide in the interfaces chapter</a>.</p><p><code>DenseArray</code> is an abstract subtype of <code>AbstractArray</code> intended to include all arrays where elements are stored contiguously in column-major order (see <a href="performance-tips.html#man-performance-column-major">additional notes in Performance Tips</a>). The <a href="../base/arrays.html#Core.Array"><code>Array</code></a> type is a specific instance of <code>DenseArray</code>;  <a href="../base/arrays.html#Base.Vector"><code>Vector</code></a> and <a href="../base/arrays.html#Base.Matrix"><code>Matrix</code></a> are aliases for the 1-d and 2-d cases. Very few operations are implemented specifically for <code>Array</code> beyond those that are required for all <code>AbstractArray</code>s; much of the array library is implemented in a generic manner that allows all custom arrays to behave similarly.</p><p><code>SubArray</code> is a specialization of <code>AbstractArray</code> that performs indexing by sharing memory with the original array rather than by copying it. A <code>SubArray</code> is created with the <a href="../base/arrays.html#Base.view"><code>view</code></a> function, which is called the same way as <a href="../base/collections.html#Base.getindex"><code>getindex</code></a> (with an array and a series of index arguments). The result of <a href="../base/arrays.html#Base.view"><code>view</code></a> looks the same as the result of <a href="../base/collections.html#Base.getindex"><code>getindex</code></a>, except the data is left in place. <a href="../base/arrays.html#Base.view"><code>view</code></a> stores the input index vectors in a <code>SubArray</code> object, which can later be used to index the original array indirectly.  By putting the <a href="../base/arrays.html#Base.@views"><code>@views</code></a> macro in front of an expression or block of code, any <code>array[...]</code> slice in that expression will be converted to create a <code>SubArray</code> view instead.</p><p><a href="../base/arrays.html#Base.BitArray"><code>BitArray</code></a>s are space-efficient &quot;packed&quot; boolean arrays, which store one bit per boolean value. They can be used similarly to <code>Array{Bool}</code> arrays (which store one byte per boolean value), and can be converted to/from the latter via <code>Array(bitarray)</code> and <code>BitArray(array)</code>, respectively.</p><p>An array is &quot;strided&quot; if it is stored in memory with well-defined spacings (strides) between its elements. A strided array with a supported element type may be passed to an external (non-Julia) library like BLAS or LAPACK by simply passing its <a href="../base/c.html#Base.pointer"><code>pointer</code></a> and the stride for each dimension. The <a href="../base/arrays.html#Base.stride"><code>stride(A, d)</code></a> is the distance between elements along dimension <code>d</code>. For example, the builtin <code>Array</code> returned by <code>rand(5,7,2)</code> has its elements arranged contiguously in column major order. This means that the stride of the first dimension — the spacing between elements in the same column — is <code>1</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; A = rand(5, 7, 2);

julia&gt; stride(A, 1)
1</code></pre><p>The stride of the second dimension is the spacing between elements in the same row, skipping as many elements as there are in a single column (<code>5</code>). Similarly, jumping between the two &quot;pages&quot; (in the third dimension) requires skipping <code>5*7 == 35</code> elements. The <a href="../base/arrays.html#Base.strides"><code>strides</code></a> of this array is the tuple of these three numbers together:</p><pre><code class="language-julia-repl hljs">julia&gt; strides(A)
(1, 5, 35)</code></pre><p>In this particular case, the number of elements skipped <em>in memory</em> matches the number of <em>linear indices</em> skipped. This is only the case for contiguous arrays like <code>Array</code> (and other <code>DenseArray</code> subtypes) and is not true in general. Views with range indices are a good example of <em>non-contiguous</em> strided arrays; consider <code>V = @view A[1:3:4, 2:2:6, 2:-1:1]</code>. This view <code>V</code> refers to the same memory as <code>A</code> but is skipping and re-arranging some of its elements. The stride of the first dimension of <code>V</code> is <code>3</code> because we&#39;re only selecting every third row from our original array:</p><pre><code class="language-julia-repl hljs">julia&gt; V = @view A[1:3:4, 2:2:6, 2:-1:1];

julia&gt; stride(V, 1)
3</code></pre><p>This view is similarly selecting every other column from our original <code>A</code> — and thus it needs to skip the equivalent of two five-element columns when moving between indices in the second dimension:</p><pre><code class="language-julia-repl hljs">julia&gt; stride(V, 2)
10</code></pre><p>The third dimension is interesting because its order is reversed! Thus to get from the first &quot;page&quot; to the second one it must go <em>backwards</em> in memory, and so its stride in this dimension is negative!</p><pre><code class="language-julia-repl hljs">julia&gt; stride(V, 3)
-35</code></pre><p>This means that the <code>pointer</code> for <code>V</code> is actually pointing into the middle of <code>A</code>&#39;s memory block, and it refers to elements both backwards and forwards in memory. See the <a href="interfaces.html#man-interface-strided-arrays">interface guide for strided arrays</a> for more details on defining your own strided arrays. <a href="../base/arrays.html#Base.StridedVector"><code>StridedVector</code></a> and <a href="../base/arrays.html#Base.StridedMatrix"><code>StridedMatrix</code></a> are convenient aliases for many of the builtin array types that are considered strided arrays, allowing them to dispatch to select specialized implementations that call highly tuned and optimized BLAS and LAPACK functions using just the pointer and strides.</p><p>It is worth emphasizing that strides are about offsets in memory rather than indexing. If you are looking to convert between linear (single-index) indexing and cartesian (multi-index) indexing, see <a href="../base/arrays.html#Base.LinearIndices"><code>LinearIndices</code></a> and <a href="../base/arrays.html#Base.IteratorsMD.CartesianIndices"><code>CartesianIndices</code></a>.</p><section class="footnotes is-size-7"><ul><li class="footnote" id="footnote-1"><a class="tag is-link" href="#citeref-1">1</a><em>iid</em>, independently and identically distributed.</li></ul></section></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="metaprogramming.html">« Metaprogramming</a><a class="docs-footer-nextpage" href="missing.html">Missing Values »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
