<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Multi-processing and Distributed Computing · The Julia Language</title><meta name="title" content="Multi-processing and Distributed Computing · The Julia Language"/><meta property="og:title" content="Multi-processing and Distributed Computing · The Julia Language"/><meta property="twitter:title" content="Multi-processing and Distributed Computing · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li class="is-active"><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a><ul class="internal"><li><a class="tocitem" href="#code-availability"><span>Code Availability and Loading Packages</span></a></li><li><a class="tocitem" href="#Starting-and-managing-worker-processes"><span>Starting and managing worker processes</span></a></li><li><a class="tocitem" href="#Data-Movement"><span>Data Movement</span></a></li><li><a class="tocitem" href="#Global-variables"><span>Global variables</span></a></li><li><a class="tocitem" href="#Parallel-Map-and-Loops"><span>Parallel Map and Loops</span></a></li><li><a class="tocitem" href="#Remote-References-and-AbstractChannels"><span>Remote References and AbstractChannels</span></a></li><li><a class="tocitem" href="#Channels-and-RemoteChannels"><span>Channels and RemoteChannels</span></a></li><li><a class="tocitem" href="#Local-invocations"><span>Local invocations</span></a></li><li><a class="tocitem" href="#man-shared-arrays"><span>Shared Arrays</span></a></li><li><a class="tocitem" href="#ClusterManagers"><span>ClusterManagers</span></a></li><li><a class="tocitem" href="#Specifying-Network-Topology-(Experimental)"><span>Specifying Network Topology (Experimental)</span></a></li><li><a class="tocitem" href="#Noteworthy-external-packages"><span>Noteworthy external packages</span></a></li></ul></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="distributed-computing.html">Multi-processing and Distributed Computing</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="distributed-computing.html">Multi-processing and Distributed Computing</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/distributed-computing.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Multi-processing-and-Distributed-Computing"><a class="docs-heading-anchor" href="#Multi-processing-and-Distributed-Computing">Multi-processing and Distributed Computing</a><a id="Multi-processing-and-Distributed-Computing-1"></a><a class="docs-heading-anchor-permalink" href="#Multi-processing-and-Distributed-Computing" title="Permalink"></a></h1><p>An implementation of distributed memory parallel computing is provided by module <a href="../stdlib/Distributed.html#man-distributed"><code>Distributed</code></a> as part of the standard library shipped with Julia.</p><p>Most modern computers possess more than one CPU, and several computers can be combined together in a cluster. Harnessing the power of these multiple CPUs allows many computations to be completed more quickly. There are two major factors that influence performance: the speed of the CPUs themselves, and the speed of their access to memory. In a cluster, it&#39;s fairly obvious that a given CPU will have fastest access to the RAM within the same computer (node). Perhaps more surprisingly, similar issues are relevant on a typical multicore laptop, due to differences in the speed of main memory and the <a href="https://www.akkadia.org/drepper/cpumemory.pdf">cache</a>. Consequently, a good multiprocessing environment should allow control over the &quot;ownership&quot; of a chunk of memory by a particular CPU. Julia provides a multiprocessing environment based on message passing to allow programs to run on multiple processes in separate memory domains at once.</p><p>Julia&#39;s implementation of message passing is different from other environments such as MPI<sup class="footnote-reference"><a id="citeref-1" href="#footnote-1">[1]</a></sup>. Communication in Julia is generally &quot;one-sided&quot;, meaning that the programmer needs to explicitly manage only one process in a two-process operation. Furthermore, these operations typically do not look like &quot;message send&quot; and &quot;message receive&quot; but rather resemble higher-level operations like calls to user functions.</p><p>Distributed programming in Julia is built on two primitives: <em>remote references</em> and <em>remote calls</em>. A remote reference is an object that can be used from any process to refer to an object stored on a particular process. A remote call is a request by one process to call a certain function on certain arguments on another (possibly the same) process.</p><p>Remote references come in two flavors: <a href="../stdlib/Distributed.html#Distributed.Future"><code>Future</code></a> and <a href="../stdlib/Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a>.</p><p>A remote call returns a <a href="../stdlib/Distributed.html#Distributed.Future"><code>Future</code></a> to its result. Remote calls return immediately; the process that made the call proceeds to its next operation while the remote call happens somewhere else. You can wait for a remote call to finish by calling <a href="../base/parallel.html#Base.wait"><code>wait</code></a> on the returned <a href="../stdlib/Distributed.html#Distributed.Future"><code>Future</code></a>, and you can obtain the full value of the result using <a href="../base/parallel.html#Base.fetch-Tuple{Task}"><code>fetch</code></a>.</p><p>On the other hand, <a href="../stdlib/Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a> s are rewritable. For example, multiple processes can coordinate their processing by referencing the same remote <code>Channel</code>.</p><p>Each process has an associated identifier. The process providing the interactive Julia prompt always has an <code>id</code> equal to 1. The processes used by default for parallel operations are referred to as &quot;workers&quot;. When there is only one process, process 1 is considered a worker. Otherwise, workers are considered to be all processes other than process 1. As a result, adding 2 or more processes is required to gain benefits from parallel processing methods like <a href="../stdlib/Distributed.html#Distributed.pmap"><code>pmap</code></a>. Adding a single process is beneficial if you just wish to do other things in the main process while a long computation is running on the worker.</p><p>Let&#39;s try this out. Starting with <code>julia -p n</code> provides <code>n</code> worker processes on the local machine. Generally it makes sense for <code>n</code> to equal the number of CPU threads (logical cores) on the machine. Note that the <code>-p</code> argument implicitly loads module <a href="../stdlib/Distributed.html#man-distributed"><code>Distributed</code></a>.</p><pre><code class="language-julia hljs">$ julia -p 2

julia&gt; r = remotecall(rand, 2, 2, 2)
Future(2, 1, 4, nothing)

julia&gt; s = @spawnat 2 1 .+ fetch(r)
Future(2, 1, 5, nothing)

julia&gt; fetch(s)
2×2 Array{Float64,2}:
 1.18526  1.50912
 1.16296  1.60607</code></pre><p>The first argument to <a href="../stdlib/Distributed.html#Distributed.remotecall-Tuple{Any, Integer, Vararg{Any}}"><code>remotecall</code></a> is the function to call. Most parallel programming in Julia does not reference specific processes or the number of processes available, but <a href="../stdlib/Distributed.html#Distributed.remotecall-Tuple{Any, Integer, Vararg{Any}}"><code>remotecall</code></a> is considered a low-level interface providing finer control. The second argument to <a href="../stdlib/Distributed.html#Distributed.remotecall-Tuple{Any, Integer, Vararg{Any}}"><code>remotecall</code></a> is the <code>id</code> of the process that will do the work, and the remaining arguments will be passed to the function being called.</p><p>As you can see, in the first line we asked process 2 to construct a 2-by-2 random matrix, and in the second line we asked it to add 1 to it. The result of both calculations is available in the two futures, <code>r</code> and <code>s</code>. The <a href="../stdlib/Distributed.html#Distributed.@spawnat"><code>@spawnat</code></a> macro evaluates the expression in the second argument on the process specified by the first argument.</p><p>Occasionally you might want a remotely-computed value immediately. This typically happens when you read from a remote object to obtain data needed by the next local operation. The function <a href="../stdlib/Distributed.html#Distributed.remotecall_fetch-Tuple{Any, Integer, Vararg{Any}}"><code>remotecall_fetch</code></a> exists for this purpose. It is equivalent to <code>fetch(remotecall(...))</code> but is more efficient.</p><pre><code class="language-julia-repl hljs">julia&gt; remotecall_fetch(r-&gt; fetch(r)[1, 1], 2, r)
0.18526337335308085</code></pre><p>This fetches the array on worker 2 and returns the first value. Note, that <code>fetch</code> doesn&#39;t move any data in this case, since it&#39;s executed on the worker that owns the array. One can also write:</p><pre><code class="language-julia-repl hljs">julia&gt; remotecall_fetch(getindex, 2, r, 1, 1)
0.10824216411304866</code></pre><p>Remember that <a href="../base/arrays.html#Base.getindex-Tuple{Type, Vararg{Any}}"><code>getindex(r,1,1)</code></a> is <a href="arrays.html#man-array-indexing">equivalent</a> to <code>r[1,1]</code>, so this call fetches the first element of the future <code>r</code>.</p><p>To make things easier, the symbol <code>:any</code> can be passed to <a href="../stdlib/Distributed.html#Distributed.@spawnat"><code>@spawnat</code></a>, which picks where to do the operation for you:</p><pre><code class="language-julia-repl hljs">julia&gt; r = @spawnat :any rand(2,2)
Future(2, 1, 4, nothing)

julia&gt; s = @spawnat :any 1 .+ fetch(r)
Future(3, 1, 5, nothing)

julia&gt; fetch(s)
2×2 Array{Float64,2}:
 1.38854  1.9098
 1.20939  1.57158</code></pre><p>Note that we used <code>1 .+ fetch(r)</code> instead of <code>1 .+ r</code>. This is because we do not know where the code will run, so in general a <a href="../base/parallel.html#Base.fetch-Tuple{Task}"><code>fetch</code></a> might be required to move <code>r</code> to the process doing the addition. In this case, <a href="../stdlib/Distributed.html#Distributed.@spawnat"><code>@spawnat</code></a> is smart enough to perform the computation on the process that owns <code>r</code>, so the <a href="../base/parallel.html#Base.fetch-Tuple{Task}"><code>fetch</code></a> will be a no-op (no work is done).</p><p>(It is worth noting that <a href="../stdlib/Distributed.html#Distributed.@spawnat"><code>@spawnat</code></a> is not built-in but defined in Julia as a <a href="metaprogramming.html#man-macros">macro</a>. It is possible to define your own such constructs.)</p><p>An important thing to remember is that, once fetched, a <a href="../stdlib/Distributed.html#Distributed.Future"><code>Future</code></a> will cache its value locally. Further <a href="../base/parallel.html#Base.fetch-Tuple{Task}"><code>fetch</code></a> calls do not entail a network hop. Once all referencing <a href="../stdlib/Distributed.html#Distributed.Future"><code>Future</code></a>s have fetched, the remote stored value is deleted.</p><p><a href="../base/parallel.html#Base.@async"><code>@async</code></a> is similar to <a href="../stdlib/Distributed.html#Distributed.@spawnat"><code>@spawnat</code></a>, but only runs tasks on the local process. We use it to create a &quot;feeder&quot; task for each process. Each task picks the next index that needs to be computed, then waits for its process to finish, then repeats until we run out of indices. Note that the feeder tasks do not begin to execute until the main task reaches the end of the <a href="../base/parallel.html#Base.@sync"><code>@sync</code></a> block, at which point it surrenders control and waits for all the local tasks to complete before returning from the function. As for v0.7 and beyond, the feeder tasks are able to share state via <code>nextidx</code> because they all run on the same process. Even if <code>Tasks</code> are scheduled cooperatively, locking may still be required in some contexts, as in <a href="faq.html#faq-async-io">asynchronous I/O</a>. This means context switches only occur at well-defined points: in this case, when <a href="../stdlib/Distributed.html#Distributed.remotecall_fetch-Tuple{Any, Integer, Vararg{Any}}"><code>remotecall_fetch</code></a> is called. This is the current state of implementation and it may change for future Julia versions, as it is intended to make it possible to run up to N <code>Tasks</code> on M <code>Process</code>, aka <a href="https://en.wikipedia.org/wiki/Thread_(computing)#Models">M:N Threading</a>. Then a lock acquiring\releasing model for <code>nextidx</code> will be needed, as it is not safe to let multiple processes read-write a resource at the same time.</p><h2 id="code-availability"><a class="docs-heading-anchor" href="#code-availability">Code Availability and Loading Packages</a><a id="code-availability-1"></a><a class="docs-heading-anchor-permalink" href="#code-availability" title="Permalink"></a></h2><p>Your code must be available on any process that runs it. For example, type the following into the Julia prompt:</p><pre><code class="language-julia-repl hljs">julia&gt; function rand2(dims...)
           return 2*rand(dims...)
       end

julia&gt; rand2(2,2)
2×2 Array{Float64,2}:
 0.153756  0.368514
 1.15119   0.918912

julia&gt; fetch(@spawnat :any rand2(2,2))
ERROR: RemoteException(2, CapturedException(UndefVarError(Symbol(&quot;#rand2&quot;))))
Stacktrace:
[...]</code></pre><p>Process 1 knew about the function <code>rand2</code>, but process 2 did not.</p><p>Most commonly you&#39;ll be loading code from files or packages, and you have a considerable amount of flexibility in controlling which processes load code. Consider a file, <code>DummyModule.jl</code>, containing the following code:</p><pre><code class="language-julia hljs">module DummyModule

export MyType, f

mutable struct MyType
    a::Int
end

f(x) = x^2+1

println(&quot;loaded&quot;)

end</code></pre><p>In order to refer to <code>MyType</code> across all processes, <code>DummyModule.jl</code> needs to be loaded on every process.  Calling <code>include(&quot;DummyModule.jl&quot;)</code> loads it only on a single process.  To load it on every process, use the <a href="../stdlib/Distributed.html#Distributed.@everywhere"><code>@everywhere</code></a> macro (starting Julia with <code>julia -p 2</code>):</p><pre><code class="language-julia-repl hljs">julia&gt; @everywhere include(&quot;DummyModule.jl&quot;)
loaded
      From worker 3:    loaded
      From worker 2:    loaded</code></pre><p>As usual, this does not bring <code>DummyModule</code> into scope on any of the process, which requires <a href="../base/base.html#using"><code>using</code></a> or <a href="../base/base.html#import"><code>import</code></a>.  Moreover, when <code>DummyModule</code> is brought into scope on one process, it is not on any other:</p><pre><code class="language-julia-repl hljs">julia&gt; using .DummyModule

julia&gt; MyType(7)
MyType(7)

julia&gt; fetch(@spawnat 2 MyType(7))
ERROR: On worker 2:
UndefVarError: `MyType` not defined in `Main`
⋮

julia&gt; fetch(@spawnat 2 DummyModule.MyType(7))
MyType(7)</code></pre><p>However, it&#39;s still possible, for instance, to send a <code>MyType</code> to a process which has loaded <code>DummyModule</code> even if it&#39;s not in scope:</p><pre><code class="language-julia-repl hljs">julia&gt; put!(RemoteChannel(2), MyType(7))
RemoteChannel{Channel{Any}}(2, 1, 13)</code></pre><p>A file can also be preloaded on multiple processes at startup with the <code>-L</code> flag, and a driver script can be used to drive the computation:</p><pre><code class="nohighlight hljs">julia -p &lt;n&gt; -L file1.jl -L file2.jl driver.jl</code></pre><p>The Julia process running the driver script in the example above has an <code>id</code> equal to 1, just like a process providing an interactive prompt.</p><p>Finally, if <code>DummyModule.jl</code> is not a standalone file but a package, then <code>using DummyModule</code> will <em>load</em> <code>DummyModule.jl</code> on all processes, but only bring it into scope on the process where <a href="../base/base.html#using"><code>using</code></a> was called.</p><h2 id="Starting-and-managing-worker-processes"><a class="docs-heading-anchor" href="#Starting-and-managing-worker-processes">Starting and managing worker processes</a><a id="Starting-and-managing-worker-processes-1"></a><a class="docs-heading-anchor-permalink" href="#Starting-and-managing-worker-processes" title="Permalink"></a></h2><p>The base Julia installation has in-built support for two types of clusters:</p><ul><li>A local cluster specified with the <code>-p</code> option as shown above.</li><li>A cluster spanning machines using the <code>--machine-file</code> option. This uses a passwordless <code>ssh</code> login to start Julia worker processes (from the same path as the current host) on the specified machines. Each machine definition takes the form <code>[count*][user@]host[:port] [bind_addr[:port]]</code>. <code>user</code> defaults to current user, <code>port</code> to the standard ssh port. <code>count</code> is the number of workers to spawn on the node, and defaults to 1. The optional <code>bind-to bind_addr[:port]</code> specifies the IP address and port that other workers should use to connect to this worker.</li></ul><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>While Julia generally strives for backward compatibility, distribution of code to worker processes relies on <a href="../stdlib/Serialization.html#Serialization.serialize"><code>Serialization.serialize</code></a>. As pointed out in the corresponding documentation, this can not be guaranteed to work across different Julia versions, so it is advised that all workers on all machines use the same version.</p></div></div><p>Functions <a href="../stdlib/Distributed.html#Distributed.addprocs"><code>addprocs</code></a>, <a href="../stdlib/Distributed.html#Distributed.rmprocs"><code>rmprocs</code></a>, <a href="../stdlib/Distributed.html#Distributed.workers"><code>workers</code></a>, and others are available as a programmatic means of adding, removing and querying the processes in a cluster.</p><pre><code class="language-julia-repl hljs">julia&gt; using Distributed

julia&gt; addprocs(2)
2-element Array{Int64,1}:
 2
 3</code></pre><p>Module <a href="../stdlib/Distributed.html#man-distributed"><code>Distributed</code></a> must be explicitly loaded on the master process before invoking <a href="../stdlib/Distributed.html#Distributed.addprocs"><code>addprocs</code></a>. It is automatically made available on the worker processes.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Note that workers do not run a <code>~/.julia/config/startup.jl</code> startup script, nor do they synchronize their global state (such as command-line switches, global variables, new method definitions, and loaded modules) with any of the other running processes. You may use <code>addprocs(exeflags=&quot;--project&quot;)</code> to initialize a worker with a particular environment, and then <code>@everywhere using &lt;modulename&gt;</code> or <code>@everywhere include(&quot;file.jl&quot;)</code>.</p></div></div><p>Other types of clusters can be supported by writing your own custom <code>ClusterManager</code>, as described below in the <a href="distributed-computing.html#ClusterManagers">ClusterManagers</a> section.</p><h2 id="Data-Movement"><a class="docs-heading-anchor" href="#Data-Movement">Data Movement</a><a id="Data-Movement-1"></a><a class="docs-heading-anchor-permalink" href="#Data-Movement" title="Permalink"></a></h2><p>Sending messages and moving data constitute most of the overhead in a distributed program. Reducing the number of messages and the amount of data sent is critical to achieving performance and scalability. To this end, it is important to understand the data movement performed by Julia&#39;s various distributed programming constructs.</p><p><a href="../base/parallel.html#Base.fetch-Tuple{Task}"><code>fetch</code></a> can be considered an explicit data movement operation, since it directly asks that an object be moved to the local machine. <a href="../stdlib/Distributed.html#Distributed.@spawnat"><code>@spawnat</code></a> (and a few related constructs) also moves data, but this is not as obvious, hence it can be called an implicit data movement operation. Consider these two approaches to constructing and squaring a random matrix:</p><p>Method 1:</p><pre><code class="language-julia-repl hljs">julia&gt; A = rand(1000,1000);

julia&gt; Bref = @spawnat :any A^2;

[...]

julia&gt; fetch(Bref);</code></pre><p>Method 2:</p><pre><code class="language-julia-repl hljs">julia&gt; Bref = @spawnat :any rand(1000,1000)^2;

[...]

julia&gt; fetch(Bref);</code></pre><p>The difference seems trivial, but in fact is quite significant due to the behavior of <a href="../stdlib/Distributed.html#Distributed.@spawnat"><code>@spawnat</code></a>. In the first method, a random matrix is constructed locally, then sent to another process where it is squared. In the second method, a random matrix is both constructed and squared on another process. Therefore the second method sends much less data than the first.</p><p>In this toy example, the two methods are easy to distinguish and choose from. However, in a real program designing data movement might require more thought and likely some measurement. For example, if the first process needs matrix <code>A</code> then the first method might be better. Or, if computing <code>A</code> is expensive and only the current process has it, then moving it to another process might be unavoidable. Or, if the current process has very little to do between the <a href="../stdlib/Distributed.html#Distributed.@spawnat"><code>@spawnat</code></a> and <code>fetch(Bref)</code>, it might be better to eliminate the parallelism altogether. Or imagine <code>rand(1000,1000)</code> is replaced with a more expensive operation. Then it might make sense to add another <a href="../stdlib/Distributed.html#Distributed.@spawnat"><code>@spawnat</code></a> statement just for this step.</p><h2 id="Global-variables"><a class="docs-heading-anchor" href="#Global-variables">Global variables</a><a id="Global-variables-1"></a><a class="docs-heading-anchor-permalink" href="#Global-variables" title="Permalink"></a></h2><p>Expressions executed remotely via <a href="../stdlib/Distributed.html#Distributed.@spawnat"><code>@spawnat</code></a>, or closures specified for remote execution using <a href="../stdlib/Distributed.html#Distributed.remotecall-Tuple{Any, Integer, Vararg{Any}}"><code>remotecall</code></a> may refer to global variables. Global bindings under module <code>Main</code> are treated a little differently compared to global bindings in other modules. Consider the following code snippet:</p><pre><code class="language-julia-repl hljs">A = rand(10,10)
remotecall_fetch(()-&gt;sum(A), 2)</code></pre><p>In this case <a href="../base/collections.html#Base.sum"><code>sum</code></a> MUST be defined in the remote process. Note that <code>A</code> is a global variable defined in the local workspace. Worker 2 does not have a variable called <code>A</code> under <code>Main</code>. The act of shipping the closure <code>()-&gt;sum(A)</code> to worker 2 results in <code>Main.A</code> being defined on 2. <code>Main.A</code> continues to exist on worker 2 even after the call <a href="../stdlib/Distributed.html#Distributed.remotecall_fetch-Tuple{Any, Integer, Vararg{Any}}"><code>remotecall_fetch</code></a> returns. Remote calls with embedded global references (under <code>Main</code> module only) manage globals as follows:</p><ul><li><p>New global bindings are created on destination workers if they are referenced as part of a remote call.</p></li><li><p>Global constants are declared as constants on remote nodes too.</p></li><li><p>Globals are re-sent to a destination worker only in the context of a remote call, and then only if its value has changed. Also, the cluster does not synchronize global bindings across nodes. For example:</p><pre><code class="language-julia hljs">A = rand(10,10)
remotecall_fetch(()-&gt;sum(A), 2) # worker 2
A = rand(10,10)
remotecall_fetch(()-&gt;sum(A), 3) # worker 3
A = nothing</code></pre><p>Executing the above snippet results in <code>Main.A</code> on worker 2 having a different value from <code>Main.A</code> on worker 3, while the value of <code>Main.A</code> on node 1 is set to <code>nothing</code>.</p></li></ul><p>As you may have realized, while memory associated with globals may be collected when they are reassigned on the master, no such action is taken on the workers as the bindings continue to be valid. <a href="../stdlib/Distributed.html#Distributed.clear!"><code>clear!</code></a> can be used to manually reassign specific globals on remote nodes to <code>nothing</code> once they are no longer required. This will release any memory associated with them as part of a regular garbage collection cycle.</p><p>Thus programs should be careful referencing globals in remote calls. In fact, it is preferable to avoid them altogether if possible. If you must reference globals, consider using <code>let</code> blocks to localize global variables.</p><p>For example:</p><pre><code class="language-julia-repl hljs">julia&gt; A = rand(10,10);

julia&gt; remotecall_fetch(()-&gt;A, 2);

julia&gt; B = rand(10,10);

julia&gt; let B = B
           remotecall_fetch(()-&gt;B, 2)
       end;

julia&gt; @fetchfrom 2 InteractiveUtils.varinfo()
name           size summary
––––––––– ––––––––– ––––––––––––––––––––––
A         800 bytes 10×10 Array{Float64,2}
Base                Module
Core                Module
Main                Module</code></pre><p>As can be seen, global variable <code>A</code> is defined on worker 2, but <code>B</code> is captured as a local variable and hence a binding for <code>B</code> does not exist on worker 2.</p><h2 id="Parallel-Map-and-Loops"><a class="docs-heading-anchor" href="#Parallel-Map-and-Loops">Parallel Map and Loops</a><a id="Parallel-Map-and-Loops-1"></a><a class="docs-heading-anchor-permalink" href="#Parallel-Map-and-Loops" title="Permalink"></a></h2><p>Fortunately, many useful parallel computations do not require data movement. A common example is a Monte Carlo simulation, where multiple processes can handle independent simulation trials simultaneously. We can use <a href="../stdlib/Distributed.html#Distributed.@spawnat"><code>@spawnat</code></a> to flip coins on two processes. First, write the following function in <code>count_heads.jl</code>:</p><pre><code class="language-julia hljs">function count_heads(n)
    c::Int = 0
    for i = 1:n
        c += rand(Bool)
    end
    c
end</code></pre><p>The function <code>count_heads</code> simply adds together <code>n</code> random bits. Here is how we can perform some trials on two machines, and add together the results:</p><pre><code class="language-julia-repl hljs">julia&gt; @everywhere include_string(Main, $(read(&quot;count_heads.jl&quot;, String)), &quot;count_heads.jl&quot;)

julia&gt; a = @spawnat :any count_heads(100000000)
Future(2, 1, 6, nothing)

julia&gt; b = @spawnat :any count_heads(100000000)
Future(3, 1, 7, nothing)

julia&gt; fetch(a)+fetch(b)
100001564</code></pre><p>This example demonstrates a powerful and often-used parallel programming pattern. Many iterations run independently over several processes, and then their results are combined using some function. The combination process is called a <em>reduction</em>, since it is generally tensor-rank-reducing: a vector of numbers is reduced to a single number, or a matrix is reduced to a single row or column, etc. In code, this typically looks like the pattern <code>x = f(x,v[i])</code>, where <code>x</code> is the accumulator, <code>f</code> is the reduction function, and the <code>v[i]</code> are the elements being reduced. It is desirable for <code>f</code> to be associative, so that it does not matter what order the operations are performed in.</p><p>Notice that our use of this pattern with <code>count_heads</code> can be generalized. We used two explicit <a href="../stdlib/Distributed.html#Distributed.@spawnat"><code>@spawnat</code></a> statements, which limits the parallelism to two processes. To run on any number of processes, we can use a <em>parallel for loop</em>, running in distributed memory, which can be written in Julia using <a href="../stdlib/Distributed.html#Distributed.@distributed"><code>@distributed</code></a> like this:</p><pre><code class="language-julia hljs">nheads = @distributed (+) for i = 1:200000000
    Int(rand(Bool))
end</code></pre><p>This construct implements the pattern of assigning iterations to multiple processes, and combining them with a specified reduction (in this case <code>(+)</code>). The result of each iteration is taken as the value of the last expression inside the loop. The whole parallel loop expression itself evaluates to the final answer.</p><p>Note that although parallel for loops look like serial for loops, their behavior is dramatically different. In particular, the iterations do not happen in a specified order, and writes to variables or arrays will not be globally visible since iterations run on different processes. Any variables used inside the parallel loop will be copied and broadcast to each process.</p><p>For example, the following code will not work as intended:</p><pre><code class="language-julia hljs">a = zeros(100000)
@distributed for i = 1:100000
    a[i] = i
end</code></pre><p>This code will not initialize all of <code>a</code>, since each process will have a separate copy of it. Parallel for loops like these must be avoided. Fortunately, <a href="distributed-computing.html#man-shared-arrays">Shared Arrays</a> can be used to get around this limitation:</p><pre><code class="language-julia hljs">using SharedArrays

a = SharedArray{Float64}(10)
@distributed for i = 1:10
    a[i] = i
end</code></pre><p>Using &quot;outside&quot; variables in parallel loops is perfectly reasonable if the variables are read-only:</p><pre><code class="language-julia hljs">a = randn(1000)
@distributed (+) for i = 1:100000
    f(a[rand(1:end)])
end</code></pre><p>Here each iteration applies <code>f</code> to a randomly-chosen sample from a vector <code>a</code> shared by all processes.</p><p>As you could see, the reduction operator can be omitted if it is not needed. In that case, the loop executes asynchronously, i.e. it spawns independent tasks on all available workers and returns an array of <a href="../stdlib/Distributed.html#Distributed.Future"><code>Future</code></a> immediately without waiting for completion. The caller can wait for the <a href="../stdlib/Distributed.html#Distributed.Future"><code>Future</code></a> completions at a later point by calling <a href="../base/parallel.html#Base.fetch-Tuple{Task}"><code>fetch</code></a> on them, or wait for completion at the end of the loop by prefixing it with <a href="../base/parallel.html#Base.@sync"><code>@sync</code></a>, like <code>@sync @distributed for</code>.</p><p>In some cases no reduction operator is needed, and we merely wish to apply a function to all integers in some range (or, more generally, to all elements in some collection). This is another useful operation called <em>parallel map</em>, implemented in Julia as the <a href="../stdlib/Distributed.html#Distributed.pmap"><code>pmap</code></a> function. For example, we could compute the singular values of several large random matrices in parallel as follows:</p><pre><code class="language-julia-repl hljs">julia&gt; M = Matrix{Float64}[rand(1000,1000) for i = 1:10];

julia&gt; pmap(svdvals, M);</code></pre><p>Julia&#39;s <a href="../stdlib/Distributed.html#Distributed.pmap"><code>pmap</code></a> is designed for the case where each function call does a large amount of work. In contrast, <code>@distributed for</code> can handle situations where each iteration is tiny, perhaps merely summing two numbers. Only worker processes are used by both <a href="../stdlib/Distributed.html#Distributed.pmap"><code>pmap</code></a> and <code>@distributed for</code> for the parallel computation. In case of <code>@distributed for</code>, the final reduction is done on the calling process.</p><h2 id="Remote-References-and-AbstractChannels"><a class="docs-heading-anchor" href="#Remote-References-and-AbstractChannels">Remote References and AbstractChannels</a><a id="Remote-References-and-AbstractChannels-1"></a><a class="docs-heading-anchor-permalink" href="#Remote-References-and-AbstractChannels" title="Permalink"></a></h2><p>Remote references always refer to an implementation of an <code>AbstractChannel</code>.</p><p>A concrete implementation of an <code>AbstractChannel</code> (like <code>Channel</code>), is required to implement <a href="../base/parallel.html#Base.put!-Tuple{Channel, Any}"><code>put!</code></a>, <a href="../base/io-network.html#Base.take!-Tuple{Base.GenericIOBuffer}"><code>take!</code></a>, <a href="../base/parallel.html#Base.fetch-Tuple{Task}"><code>fetch</code></a>, <a href="../base/parallel.html#Base.isready-Tuple{Channel}"><code>isready</code></a> and <a href="../base/parallel.html#Base.wait"><code>wait</code></a>. The remote object referred to by a <a href="../stdlib/Distributed.html#Distributed.Future"><code>Future</code></a> is stored in a <code>Channel{Any}(1)</code>, i.e., a <code>Channel</code> of size 1 capable of holding objects of <code>Any</code> type.</p><p><a href="../stdlib/Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a>, which is rewritable, can point to any type and size of channels, or any other implementation of an <code>AbstractChannel</code>.</p><p>The constructor <code>RemoteChannel(f::Function, pid)()</code> allows us to construct references to channels holding more than one value of a specific type. <code>f</code> is a function executed on <code>pid</code> and it must return an <code>AbstractChannel</code>.</p><p>For example, <code>RemoteChannel(()-&gt;Channel{Int}(10), pid)</code>, will return a reference to a channel of type <code>Int</code> and size 10. The channel exists on worker <code>pid</code>.</p><p>Methods <a href="../base/parallel.html#Base.put!-Tuple{Channel, Any}"><code>put!</code></a>, <a href="../base/io-network.html#Base.take!-Tuple{Base.GenericIOBuffer}"><code>take!</code></a>, <a href="../base/parallel.html#Base.fetch-Tuple{Task}"><code>fetch</code></a>, <a href="../base/parallel.html#Base.isready-Tuple{Channel}"><code>isready</code></a> and <a href="../base/parallel.html#Base.wait"><code>wait</code></a> on a <a href="../stdlib/Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a> are proxied onto the backing store on the remote process.</p><p><a href="../stdlib/Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a> can thus be used to refer to user implemented <code>AbstractChannel</code> objects. A simple example of this is the following <code>DictChannel</code> which uses a dictionary as its remote store:</p><pre><code class="language-julia-repl hljs">julia&gt; struct DictChannel{T} &lt;: AbstractChannel{T}
           d::Dict
           cond_take::Threads.Condition    # waiting for data to become available
           DictChannel{T}() where {T} = new(Dict(), Threads.Condition())
           DictChannel() = DictChannel{Any}()
       end

julia&gt; begin
       function Base.put!(D::DictChannel, k, v)
           @lock D.cond_take begin
               D.d[k] = v
               notify(D.cond_take)
           end
           return D
       end
       function Base.take!(D::DictChannel, k)
           @lock D.cond_take begin
               v = fetch(D, k)
               delete!(D.d, k)
               return v
           end
       end
       Base.isready(D::DictChannel) = @lock D.cond_take !isempty(D.d)
       Base.isready(D::DictChannel, k) = @lock D.cond_take haskey(D.d, k)
       function Base.fetch(D::DictChannel, k)
           @lock D.cond_take begin
               wait(D, k)
               return D.d[k]
           end
       end
       function Base.wait(D::DictChannel, k)
           @lock D.cond_take begin
               while !isready(D, k)
                   wait(D.cond_take)
               end
           end
       end
       end;

julia&gt; d = DictChannel();

julia&gt; isready(d)
false

julia&gt; put!(d, :k, :v);

julia&gt; isready(d, :k)
true

julia&gt; fetch(d, :k)
:v

julia&gt; wait(d, :k)

julia&gt; take!(d, :k)
:v

julia&gt; isready(d, :k)
false</code></pre><h2 id="Channels-and-RemoteChannels"><a class="docs-heading-anchor" href="#Channels-and-RemoteChannels">Channels and RemoteChannels</a><a id="Channels-and-RemoteChannels-1"></a><a class="docs-heading-anchor-permalink" href="#Channels-and-RemoteChannels" title="Permalink"></a></h2><ul><li>A <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a> is local to a process. Worker 2 cannot directly refer to a <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a> on worker 3 and vice-versa. A <a href="../stdlib/Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a>, however, can put and take values across workers.</li><li>A <a href="../stdlib/Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a> can be thought of as a <em>handle</em> to a <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a>.</li><li>The process id, <code>pid</code>, associated with a <a href="../stdlib/Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a> identifies the process where the backing store, i.e., the backing <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a> exists.</li><li>Any process with a reference to a <a href="../stdlib/Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a> can put and take items from the channel. Data is automatically sent to (or retrieved from) the process a <a href="../stdlib/Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a> is associated with.</li><li>Serializing  a <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a> also serializes any data present in the channel. Deserializing it therefore effectively makes a copy of the original object.</li><li>On the other hand, serializing a <a href="../stdlib/Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a> only involves the serialization of an identifier that identifies the location and instance of <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a> referred to by the handle. A deserialized <a href="../stdlib/Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a> object (on any worker), therefore also points to the same backing store as the original.</li></ul><p>The channels example from above can be modified for interprocess communication, as shown below.</p><p>We start 4 workers to process a single <code>jobs</code> remote channel. Jobs, identified by an id (<code>job_id</code>), are written to the channel. Each remotely executing task in this simulation reads a <code>job_id</code>, waits for a random amount of time and writes back a tuple of <code>job_id</code>, time taken and its own <code>pid</code> to the results channel. Finally all the <code>results</code> are printed out on the master process.</p><pre><code class="language-julia-repl hljs">julia&gt; addprocs(4); # add worker processes

julia&gt; const jobs = RemoteChannel(()-&gt;Channel{Int}(32));

julia&gt; const results = RemoteChannel(()-&gt;Channel{Tuple}(32));

julia&gt; @everywhere function do_work(jobs, results) # define work function everywhere
           while true
               job_id = take!(jobs)
               exec_time = rand()
               sleep(exec_time) # simulates elapsed time doing actual work
               put!(results, (job_id, exec_time, myid()))
           end
       end

julia&gt; function make_jobs(n)
           for i in 1:n
               put!(jobs, i)
           end
       end;

julia&gt; n = 12;

julia&gt; errormonitor(@async make_jobs(n)); # feed the jobs channel with &quot;n&quot; jobs

julia&gt; for p in workers() # start tasks on the workers to process requests in parallel
           remote_do(do_work, p, jobs, results)
       end

julia&gt; @elapsed while n &gt; 0 # print out results
           job_id, exec_time, where = take!(results)
           println(&quot;$job_id finished in $(round(exec_time; digits=2)) seconds on worker $where&quot;)
           global n = n - 1
       end
1 finished in 0.18 seconds on worker 4
2 finished in 0.26 seconds on worker 5
6 finished in 0.12 seconds on worker 4
7 finished in 0.18 seconds on worker 4
5 finished in 0.35 seconds on worker 5
4 finished in 0.68 seconds on worker 2
3 finished in 0.73 seconds on worker 3
11 finished in 0.01 seconds on worker 3
12 finished in 0.02 seconds on worker 3
9 finished in 0.26 seconds on worker 5
8 finished in 0.57 seconds on worker 4
10 finished in 0.58 seconds on worker 2
0.055971741</code></pre><h3 id="Remote-References-and-Distributed-Garbage-Collection"><a class="docs-heading-anchor" href="#Remote-References-and-Distributed-Garbage-Collection">Remote References and Distributed Garbage Collection</a><a id="Remote-References-and-Distributed-Garbage-Collection-1"></a><a class="docs-heading-anchor-permalink" href="#Remote-References-and-Distributed-Garbage-Collection" title="Permalink"></a></h3><p>Objects referred to by remote references can be freed only when <em>all</em> held references in the cluster are deleted.</p><p>The node where the value is stored keeps track of which of the workers have a reference to it. Every time a <a href="../stdlib/Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a> or a (unfetched) <a href="../stdlib/Distributed.html#Distributed.Future"><code>Future</code></a> is serialized to a worker, the node pointed to by the reference is notified. And every time a <a href="../stdlib/Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a> or a (unfetched) <a href="../stdlib/Distributed.html#Distributed.Future"><code>Future</code></a> is garbage collected locally, the node owning the value is again notified. This is implemented in an internal cluster aware serializer. Remote references are only valid in the context of a running cluster. Serializing and deserializing references to and from regular <code>IO</code> objects is not supported.</p><p>The notifications are done via sending of &quot;tracking&quot; messages–an &quot;add reference&quot; message when a reference is serialized to a different process and a &quot;delete reference&quot; message when a reference is locally garbage collected.</p><p>Since <a href="../stdlib/Distributed.html#Distributed.Future"><code>Future</code></a>s are write-once and cached locally, the act of <a href="../base/parallel.html#Base.fetch-Tuple{Task}"><code>fetch</code></a>ing a <a href="../stdlib/Distributed.html#Distributed.Future"><code>Future</code></a> also updates reference tracking information on the node owning the value.</p><p>The node which owns the value frees it once all references to it are cleared.</p><p>With <a href="../stdlib/Distributed.html#Distributed.Future"><code>Future</code></a>s, serializing an already fetched <a href="../stdlib/Distributed.html#Distributed.Future"><code>Future</code></a> to a different node also sends the value since the original remote store may have collected the value by this time.</p><p>It is important to note that <em>when</em> an object is locally garbage collected depends on the size of the object and the current memory pressure in the system.</p><p>In case of remote references, the size of the local reference object is quite small, while the value stored on the remote node may be quite large. Since the local object may not be collected immediately, it is a good practice to explicitly call <a href="../base/base.html#Base.finalize"><code>finalize</code></a> on local instances of a <a href="../stdlib/Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a>, or on unfetched <a href="../stdlib/Distributed.html#Distributed.Future"><code>Future</code></a>s. Since calling <a href="../base/parallel.html#Base.fetch-Tuple{Task}"><code>fetch</code></a> on a <a href="../stdlib/Distributed.html#Distributed.Future"><code>Future</code></a> also removes its reference from the remote store, this is not required on fetched <a href="../stdlib/Distributed.html#Distributed.Future"><code>Future</code></a>s. Explicitly calling <a href="../base/base.html#Base.finalize"><code>finalize</code></a> results in an immediate message sent to the remote node to go ahead and remove its reference to the value.</p><p>Once finalized, a reference becomes invalid and cannot be used in any further calls.</p><h2 id="Local-invocations"><a class="docs-heading-anchor" href="#Local-invocations">Local invocations</a><a id="Local-invocations-1"></a><a class="docs-heading-anchor-permalink" href="#Local-invocations" title="Permalink"></a></h2><p>Data is necessarily copied over to the remote node for execution. This is the case for both remotecalls and when data is stored to a <a href="../stdlib/Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a> / <a href="../stdlib/Distributed.html#Distributed.Future"><code>Future</code></a> on a different node. As expected, this results in a copy of the serialized objects on the remote node. However, when the destination node is the local node, i.e. the calling process id is the same as the remote node id, it is executed as a local call. It is usually (not always) executed in a different task - but there is no serialization/deserialization of data. Consequently, the call refers to the same object instances as passed - no copies are created. This behavior is highlighted below:</p><pre><code class="language-julia-repl hljs">julia&gt; using Distributed;

julia&gt; rc = RemoteChannel(()-&gt;Channel(3));   # RemoteChannel created on local node

julia&gt; v = [0];

julia&gt; for i in 1:3
           v[1] = i                          # Reusing `v`
           put!(rc, v)
       end;

julia&gt; result = [take!(rc) for _ in 1:3];

julia&gt; println(result);
Array{Int64,1}[[3], [3], [3]]

julia&gt; println(&quot;Num Unique objects : &quot;, length(unique(map(objectid, result))));
Num Unique objects : 1

julia&gt; addprocs(1);

julia&gt; rc = RemoteChannel(()-&gt;Channel(3), workers()[1]);   # RemoteChannel created on remote node

julia&gt; v = [0];

julia&gt; for i in 1:3
           v[1] = i
           put!(rc, v)
       end;

julia&gt; result = [take!(rc) for _ in 1:3];

julia&gt; println(result);
Array{Int64,1}[[1], [2], [3]]

julia&gt; println(&quot;Num Unique objects : &quot;, length(unique(map(objectid, result))));
Num Unique objects : 3</code></pre><p>As can be seen, <a href="../base/parallel.html#Base.put!-Tuple{Channel, Any}"><code>put!</code></a> on a locally owned <a href="../stdlib/Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a> with the same object <code>v</code> modified between calls results in the same single object instance stored. As opposed to copies of <code>v</code> being created when the node owning <code>rc</code> is a different node.</p><p>It is to be noted that this is generally not an issue. It is something to be factored in only if the object is both being stored locally and modified post the call. In such cases it may be appropriate to store a <code>deepcopy</code> of the object.</p><p>This is also true for remotecalls on the local node as seen in the following example:</p><pre><code class="language-julia-repl hljs">julia&gt; using Distributed; addprocs(1);

julia&gt; v = [0];

julia&gt; v2 = remotecall_fetch(x-&gt;(x[1] = 1; x), myid(), v);     # Executed on local node

julia&gt; println(&quot;v=$v, v2=$v2, &quot;, v === v2);
v=[1], v2=[1], true

julia&gt; v = [0];

julia&gt; v2 = remotecall_fetch(x-&gt;(x[1] = 1; x), workers()[1], v); # Executed on remote node

julia&gt; println(&quot;v=$v, v2=$v2, &quot;, v === v2);
v=[0], v2=[1], false</code></pre><p>As can be seen once again, a remote call onto the local node behaves just like a direct invocation. The call modifies local objects passed as arguments. In the remote invocation, it operates on a copy of the arguments.</p><p>To repeat, in general this is not an issue. If the local node is also being used as a compute node, and the arguments used post the call, this behavior needs to be factored in and if required deep copies of arguments must be passed to the call invoked on the local node. Calls on remote nodes will always operate on copies of arguments.</p><h2 id="man-shared-arrays"><a class="docs-heading-anchor" href="#man-shared-arrays">Shared Arrays</a><a id="man-shared-arrays-1"></a><a class="docs-heading-anchor-permalink" href="#man-shared-arrays" title="Permalink"></a></h2><p>Shared Arrays use system shared memory to map the same array across many processes. A <a href="../stdlib/SharedArrays.html#SharedArrays.SharedArray"><code>SharedArray</code></a> is a good choice when you want to have a large amount of data jointly accessible to two or more processes on the same machine. Shared Array support is available via the module <code>SharedArrays</code>, which must be explicitly loaded on all participating workers.</p><p>A complementary data structure is provided by the external package <a href="https://github.com/JuliaParallel/DistributedArrays.jl"><code>DistributedArrays.jl</code></a> in the form of a <code>DArray</code>. While there are some similarities to a <a href="../stdlib/SharedArrays.html#SharedArrays.SharedArray"><code>SharedArray</code></a>, the behavior of a <a href="https://github.com/JuliaParallel/DistributedArrays.jl"><code>DArray</code></a> is quite different. In a <a href="../stdlib/SharedArrays.html#SharedArrays.SharedArray"><code>SharedArray</code></a>, each &quot;participating&quot; process has access to the entire array; in contrast, in a <a href="https://github.com/JuliaParallel/DistributedArrays.jl"><code>DArray</code></a>, each process has local access to just a chunk of the data, and no two processes share the same chunk.</p><p><a href="../stdlib/SharedArrays.html#SharedArrays.SharedArray"><code>SharedArray</code></a> indexing (assignment and accessing values) works just as with regular arrays, and is efficient because the underlying memory is available to the local process. Therefore, most algorithms work naturally on <a href="../stdlib/SharedArrays.html#SharedArrays.SharedArray"><code>SharedArray</code></a>s, albeit in single-process mode. In cases where an algorithm insists on an <a href="../base/arrays.html#Core.Array"><code>Array</code></a> input, the underlying array can be retrieved from a <a href="../stdlib/SharedArrays.html#SharedArrays.SharedArray"><code>SharedArray</code></a> by calling <a href="../stdlib/SharedArrays.html#SharedArrays.sdata"><code>sdata</code></a>. For other <code>AbstractArray</code> types, <a href="../stdlib/SharedArrays.html#SharedArrays.sdata"><code>sdata</code></a> just returns the object itself, so it&#39;s safe to use <a href="../stdlib/SharedArrays.html#SharedArrays.sdata"><code>sdata</code></a> on any <code>Array</code>-type object.</p><p>The constructor for a shared array is of the form:</p><pre><code class="language-julia hljs">SharedArray{T,N}(dims::NTuple; init=false, pids=Int[])</code></pre><p>which creates an <code>N</code>-dimensional shared array of a bits type <code>T</code> and size <code>dims</code> across the processes specified by <code>pids</code>. Unlike distributed arrays, a shared array is accessible only from those participating workers specified by the <code>pids</code> named argument (and the creating process too, if it is on the same host). Note that only elements that are <a href="../base/base.html#Base.isbits"><code>isbits</code></a> are supported in a SharedArray.</p><p>If an <code>init</code> function, of signature <code>initfn(S::SharedArray)</code>, is specified, it is called on all the participating workers. You can specify that each worker runs the <code>init</code> function on a distinct portion of the array, thereby parallelizing initialization.</p><p>Here&#39;s a brief example:</p><pre><code class="language-julia-repl hljs">julia&gt; using Distributed

julia&gt; addprocs(3)
3-element Array{Int64,1}:
 2
 3
 4

julia&gt; @everywhere using SharedArrays

julia&gt; S = SharedArray{Int,2}((3,4), init = S -&gt; S[localindices(S)] = repeat([myid()], length(localindices(S))))
3×4 SharedArray{Int64,2}:
 2  2  3  4
 2  3  3  4
 2  3  4  4

julia&gt; S[3,2] = 7
7

julia&gt; S
3×4 SharedArray{Int64,2}:
 2  2  3  4
 2  3  3  4
 2  7  4  4</code></pre><p><a href="../stdlib/SharedArrays.html#SharedArrays.localindices"><code>SharedArrays.localindices</code></a> provides disjoint one-dimensional ranges of indices, and is sometimes convenient for splitting up tasks among processes. You can, of course, divide the work any way you wish:</p><pre><code class="language-julia-repl hljs">julia&gt; S = SharedArray{Int,2}((3,4), init = S -&gt; S[indexpids(S):length(procs(S)):length(S)] = repeat([myid()], length( indexpids(S):length(procs(S)):length(S))))
3×4 SharedArray{Int64,2}:
 2  2  2  2
 3  3  3  3
 4  4  4  4</code></pre><p>Since all processes have access to the underlying data, you do have to be careful not to set up conflicts. For example:</p><pre><code class="language-julia hljs">@sync begin
    for p in procs(S)
        @async begin
            remotecall_wait(fill!, p, S, p)
        end
    end
end</code></pre><p>would result in undefined behavior. Because each process fills the <em>entire</em> array with its own <code>pid</code>, whichever process is the last to execute (for any particular element of <code>S</code>) will have its <code>pid</code> retained.</p><p>As a more extended and complex example, consider running the following &quot;kernel&quot; in parallel:</p><pre><code class="language-julia hljs">q[i,j,t+1] = q[i,j,t] + u[i,j,t]</code></pre><p>In this case, if we try to split up the work using a one-dimensional index, we are likely to run into trouble: if <code>q[i,j,t]</code> is near the end of the block assigned to one worker and <code>q[i,j,t+1]</code> is near the beginning of the block assigned to another, it&#39;s very likely that <code>q[i,j,t]</code> will not be ready at the time it&#39;s needed for computing <code>q[i,j,t+1]</code>. In such cases, one is better off chunking the array manually. Let&#39;s split along the second dimension. Define a function that returns the <code>(irange, jrange)</code> indices assigned to this worker:</p><pre><code class="language-julia-repl hljs">julia&gt; @everywhere function myrange(q::SharedArray)
           idx = indexpids(q)
           if idx == 0 # This worker is not assigned a piece
               return 1:0, 1:0
           end
           nchunks = length(procs(q))
           splits = [round(Int, s) for s in range(0, stop=size(q,2), length=nchunks+1)]
           1:size(q,1), splits[idx]+1:splits[idx+1]
       end</code></pre><p>Next, define the kernel:</p><pre><code class="language-julia-repl hljs">julia&gt; @everywhere function advection_chunk!(q, u, irange, jrange, trange)
           @show (irange, jrange, trange)  # display so we can see what&#39;s happening
           for t in trange, j in jrange, i in irange
               q[i,j,t+1] = q[i,j,t] + u[i,j,t]
           end
           q
       end</code></pre><p>We also define a convenience wrapper for a <code>SharedArray</code> implementation</p><pre><code class="language-julia-repl hljs">julia&gt; @everywhere advection_shared_chunk!(q, u) =
           advection_chunk!(q, u, myrange(q)..., 1:size(q,3)-1)</code></pre><p>Now let&#39;s compare three different versions, one that runs in a single process:</p><pre><code class="language-julia-repl hljs">julia&gt; advection_serial!(q, u) = advection_chunk!(q, u, 1:size(q,1), 1:size(q,2), 1:size(q,3)-1);</code></pre><p>one that uses <a href="../stdlib/Distributed.html#Distributed.@distributed"><code>@distributed</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; function advection_parallel!(q, u)
           for t = 1:size(q,3)-1
               @sync @distributed for j = 1:size(q,2)
                   for i = 1:size(q,1)
                       q[i,j,t+1]= q[i,j,t] + u[i,j,t]
                   end
               end
           end
           q
       end;</code></pre><p>and one that delegates in chunks:</p><pre><code class="language-julia-repl hljs">julia&gt; function advection_shared!(q, u)
           @sync begin
               for p in procs(q)
                   @async remotecall_wait(advection_shared_chunk!, p, q, u)
               end
           end
           q
       end;</code></pre><p>If we create <code>SharedArray</code>s and time these functions, we get the following results (with <code>julia -p 4</code>):</p><pre><code class="language-julia-repl hljs">julia&gt; q = SharedArray{Float64,3}((500,500,500));

julia&gt; u = SharedArray{Float64,3}((500,500,500));</code></pre><p>Run the functions once to JIT-compile and <a href="profile.html#@time"><code>@time</code></a> them on the second run:</p><pre><code class="language-julia-repl hljs">julia&gt; @time advection_serial!(q, u);
(irange,jrange,trange) = (1:500,1:500,1:499)
 830.220 milliseconds (216 allocations: 13820 bytes)

julia&gt; @time advection_parallel!(q, u);
   2.495 seconds      (3999 k allocations: 289 MB, 2.09% gc time)

julia&gt; @time advection_shared!(q,u);
        From worker 2:       (irange,jrange,trange) = (1:500,1:125,1:499)
        From worker 4:       (irange,jrange,trange) = (1:500,251:375,1:499)
        From worker 3:       (irange,jrange,trange) = (1:500,126:250,1:499)
        From worker 5:       (irange,jrange,trange) = (1:500,376:500,1:499)
 238.119 milliseconds (2264 allocations: 169 KB)</code></pre><p>The biggest advantage of <code>advection_shared!</code> is that it minimizes traffic among the workers, allowing each to compute for an extended time on the assigned piece.</p><h3 id="Shared-Arrays-and-Distributed-Garbage-Collection"><a class="docs-heading-anchor" href="#Shared-Arrays-and-Distributed-Garbage-Collection">Shared Arrays and Distributed Garbage Collection</a><a id="Shared-Arrays-and-Distributed-Garbage-Collection-1"></a><a class="docs-heading-anchor-permalink" href="#Shared-Arrays-and-Distributed-Garbage-Collection" title="Permalink"></a></h3><p>Like remote references, shared arrays are also dependent on garbage collection on the creating node to release references from all participating workers. Code which creates many short lived shared array objects would benefit from explicitly finalizing these objects as soon as possible. This results in both memory and file handles mapping the shared segment being released sooner.</p><h2 id="ClusterManagers"><a class="docs-heading-anchor" href="#ClusterManagers">ClusterManagers</a><a id="ClusterManagers-1"></a><a class="docs-heading-anchor-permalink" href="#ClusterManagers" title="Permalink"></a></h2><p>The launching, management and networking of Julia processes into a logical cluster is done via cluster managers. A <code>ClusterManager</code> is responsible for</p><ul><li>launching worker processes in a cluster environment</li><li>managing events during the lifetime of each worker</li><li>optionally, providing data transport</li></ul><p>A Julia cluster has the following characteristics:</p><ul><li>The initial Julia process, also called the <code>master</code>, is special and has an <code>id</code> of 1.</li><li>Only the <code>master</code> process can add or remove worker processes.</li><li>All processes can directly communicate with each other.</li></ul><p>Connections between workers (using the in-built TCP/IP transport) is established in the following manner:</p><ul><li><a href="../stdlib/Distributed.html#Distributed.addprocs"><code>addprocs</code></a> is called on the master process with a <code>ClusterManager</code> object.</li><li><a href="../stdlib/Distributed.html#Distributed.addprocs"><code>addprocs</code></a> calls the appropriate <a href="../stdlib/Distributed.html#Distributed.launch"><code>launch</code></a> method which spawns required number of worker processes on appropriate machines.</li><li>Each worker starts listening on a free port and writes out its host and port information to <a href="../base/io-network.html#Base.stdout"><code>stdout</code></a>.</li><li>The cluster manager captures the <a href="../base/io-network.html#Base.stdout"><code>stdout</code></a> of each worker and makes it available to the master process.</li><li>The master process parses this information and sets up TCP/IP connections to each worker.</li><li>Every worker is also notified of other workers in the cluster.</li><li>Each worker connects to all workers whose <code>id</code> is less than the worker&#39;s own <code>id</code>.</li><li>In this way a mesh network is established, wherein every worker is directly connected with every other worker.</li></ul><p>While the default transport layer uses plain <a href="../stdlib/Sockets.html#Sockets.TCPSocket"><code>TCPSocket</code></a>, it is possible for a Julia cluster to provide its own transport.</p><p>Julia provides two in-built cluster managers:</p><ul><li><code>LocalManager</code>, used when <a href="../stdlib/Distributed.html#Distributed.addprocs"><code>addprocs()</code></a> or <a href="../stdlib/Distributed.html#Distributed.addprocs"><code>addprocs(np::Integer)</code></a> are called</li><li><code>SSHManager</code>, used when <a href="../stdlib/Distributed.html#Distributed.addprocs"><code>addprocs(hostnames::Array)</code></a> is called with a list of hostnames</li></ul><p><code>LocalManager</code> is used to launch additional workers on the same host, thereby leveraging multi-core and multi-processor hardware.</p><p>Thus, a minimal cluster manager would need to:</p><ul><li>be a subtype of the abstract <code>ClusterManager</code></li><li>implement <a href="../stdlib/Distributed.html#Distributed.launch"><code>launch</code></a>, a method responsible for launching new workers</li><li>implement <a href="../stdlib/Distributed.html#Distributed.manage"><code>manage</code></a>, which is called at various events during a worker&#39;s lifetime (for example, sending an interrupt signal)</li></ul><p><a href="../stdlib/Distributed.html#Distributed.addprocs"><code>addprocs(manager::FooManager)</code></a> requires <code>FooManager</code> to implement:</p><pre><code class="language-julia hljs">function launch(manager::FooManager, params::Dict, launched::Array, c::Condition)
    [...]
end

function manage(manager::FooManager, id::Integer, config::WorkerConfig, op::Symbol)
    [...]
end</code></pre><p>As an example let us see how the <code>LocalManager</code>, the manager responsible for starting workers on the same host, is implemented:</p><pre><code class="language-julia hljs">struct LocalManager &lt;: ClusterManager
    np::Integer
end

function launch(manager::LocalManager, params::Dict, launched::Array, c::Condition)
    [...]
end

function manage(manager::LocalManager, id::Integer, config::WorkerConfig, op::Symbol)
    [...]
end</code></pre><p>The <a href="../stdlib/Distributed.html#Distributed.launch"><code>launch</code></a> method takes the following arguments:</p><ul><li><code>manager::ClusterManager</code>: the cluster manager that <a href="../stdlib/Distributed.html#Distributed.addprocs"><code>addprocs</code></a> is called with</li><li><code>params::Dict</code>: all the keyword arguments passed to <a href="../stdlib/Distributed.html#Distributed.addprocs"><code>addprocs</code></a></li><li><code>launched::Array</code>: the array to append one or more <code>WorkerConfig</code> objects to</li><li><code>c::Condition</code>: the condition variable to be notified as and when workers are launched</li></ul><p>The <a href="../stdlib/Distributed.html#Distributed.launch"><code>launch</code></a> method is called asynchronously in a separate task. The termination of this task signals that all requested workers have been launched. Hence the <a href="../stdlib/Distributed.html#Distributed.launch"><code>launch</code></a> function MUST exit as soon as all the requested workers have been launched.</p><p>Newly launched workers are connected to each other and the master process in an all-to-all manner. Specifying the command line argument <code>--worker[=&lt;cookie&gt;]</code> results in the launched processes initializing themselves as workers and connections being set up via TCP/IP sockets.</p><p>All workers in a cluster share the same <a href="distributed-computing.html#man-cluster-cookie">cookie</a> as the master. When the cookie is unspecified, i.e, with the <code>--worker</code> option, the worker tries to read it from its standard input.  <code>LocalManager</code> and <code>SSHManager</code> both pass the cookie to newly launched workers via their  standard inputs.</p><p>By default a worker will listen on a free port at the address returned by a call to <a href="../stdlib/Sockets.html#Sockets.getipaddr"><code>getipaddr()</code></a>. A specific address to listen on may be specified by optional argument <code>--bind-to bind_addr[:port]</code>. This is useful for multi-homed hosts.</p><p>As an example of a non-TCP/IP transport, an implementation may choose to use MPI, in which case <code>--worker</code> must NOT be specified. Instead, newly launched workers should call <code>init_worker(cookie)</code> before using any of the parallel constructs.</p><p>For every worker launched, the <a href="../stdlib/Distributed.html#Distributed.launch"><code>launch</code></a> method must add a <code>WorkerConfig</code> object (with appropriate fields initialized) to <code>launched</code></p><pre><code class="language-julia hljs">mutable struct WorkerConfig
    # Common fields relevant to all cluster managers
    io::Union{IO, Nothing}
    host::Union{AbstractString, Nothing}
    port::Union{Integer, Nothing}

    # Used when launching additional workers at a host
    count::Union{Int, Symbol, Nothing}
    exename::Union{AbstractString, Cmd, Nothing}
    exeflags::Union{Cmd, Nothing}

    # External cluster managers can use this to store information at a per-worker level
    # Can be a dict if multiple fields need to be stored.
    userdata::Any

    # SSHManager / SSH tunnel connections to workers
    tunnel::Union{Bool, Nothing}
    bind_addr::Union{AbstractString, Nothing}
    sshflags::Union{Cmd, Nothing}
    max_parallel::Union{Integer, Nothing}

    # Used by Local/SSH managers
    connect_at::Any

    [...]
end</code></pre><p>Most of the fields in <code>WorkerConfig</code> are used by the inbuilt managers. Custom cluster managers would typically specify only <code>io</code> or <code>host</code> / <code>port</code>:</p><ul><li><p>If <code>io</code> is specified, it is used to read host/port information. A Julia worker prints out its bind address and port at startup. This allows Julia workers to listen on any free port available instead of requiring worker ports to be configured manually.</p></li><li><p>If <code>io</code> is not specified, <code>host</code> and <code>port</code> are used to connect.</p></li><li><p><code>count</code>, <code>exename</code> and <code>exeflags</code> are relevant for launching additional workers from a worker. For example, a cluster manager may launch a single worker per node, and use that to launch additional workers.</p><ul><li><code>count</code> with an integer value <code>n</code> will launch a total of <code>n</code> workers.</li><li><code>count</code> with a value of <code>:auto</code> will launch as many workers as the number of CPU threads (logical cores) on that machine.</li><li><code>exename</code> is the name of the <code>julia</code> executable including the full path.</li><li><code>exeflags</code> should be set to the required command line arguments for new workers.</li></ul></li><li><p><code>tunnel</code>, <code>bind_addr</code>, <code>sshflags</code> and <code>max_parallel</code> are used when a ssh tunnel is required to connect to the workers from the master process.</p></li><li><p><code>userdata</code> is provided for custom cluster managers to store their own worker-specific information.</p></li></ul><p><code>manage(manager::FooManager, id::Integer, config::WorkerConfig, op::Symbol)</code> is called at different times during the worker&#39;s lifetime with appropriate <code>op</code> values:</p><ul><li>with <code>:register</code>/<code>:deregister</code> when a worker is added / removed from the Julia worker pool.</li><li>with <code>:interrupt</code> when <code>interrupt(workers)</code> is called. The <code>ClusterManager</code> should signal the appropriate worker with an interrupt signal.</li><li>with <code>:finalize</code> for cleanup purposes.</li></ul><h3 id="Cluster-Managers-with-Custom-Transports"><a class="docs-heading-anchor" href="#Cluster-Managers-with-Custom-Transports">Cluster Managers with Custom Transports</a><a id="Cluster-Managers-with-Custom-Transports-1"></a><a class="docs-heading-anchor-permalink" href="#Cluster-Managers-with-Custom-Transports" title="Permalink"></a></h3><p>Replacing the default TCP/IP all-to-all socket connections with a custom transport layer is a little more involved. Each Julia process has as many communication tasks as the workers it is connected to. For example, consider a Julia cluster of 32 processes in an all-to-all mesh network:</p><ul><li>Each Julia process thus has 31 communication tasks.</li><li>Each task handles all incoming messages from a single remote worker in a message-processing loop.</li><li>The message-processing loop waits on an <code>IO</code> object (for example, a <a href="../stdlib/Sockets.html#Sockets.TCPSocket"><code>TCPSocket</code></a> in the default implementation), reads an entire message, processes it and waits for the next one.</li><li>Sending messages to a process is done directly from any Julia task–not just communication tasks–again, via the appropriate <code>IO</code> object.</li></ul><p>Replacing the default transport requires the new implementation to set up connections to remote workers and to provide appropriate <code>IO</code> objects that the message-processing loops can wait on. The manager-specific callbacks to be implemented are:</p><pre><code class="language-julia hljs">connect(manager::FooManager, pid::Integer, config::WorkerConfig)
kill(manager::FooManager, pid::Int, config::WorkerConfig)</code></pre><p>The default implementation (which uses TCP/IP sockets) is implemented as <code>connect(manager::ClusterManager, pid::Integer, config::WorkerConfig)</code>.</p><p><code>connect</code> should return a pair of <code>IO</code> objects, one for reading data sent from worker <code>pid</code>, and the other to write data that needs to be sent to worker <code>pid</code>. Custom cluster managers can use an in-memory <code>BufferStream</code> as the plumbing to proxy data between the custom, possibly non-<code>IO</code> transport and Julia&#39;s in-built parallel infrastructure.</p><p>A <code>BufferStream</code> is an in-memory <a href="../base/io-network.html#Base.IOBuffer"><code>IOBuffer</code></a> which behaves like an <code>IO</code>–it is a stream which can be handled asynchronously.</p><p>The folder <code>clustermanager/0mq</code> in the <a href="https://github.com/JuliaAttic/Examples">Examples repository</a> contains an example of using ZeroMQ to connect Julia workers in a star topology with a 0MQ broker in the middle. Note: The Julia processes are still all <em>logically</em> connected to each other–any worker can message any other worker directly without any awareness of 0MQ being used as the transport layer.</p><p>When using custom transports:</p><ul><li>Julia workers must NOT be started with <code>--worker</code>. Starting with <code>--worker</code> will result in the newly launched workers defaulting to the TCP/IP socket transport implementation.</li><li>For every incoming logical connection with a worker, <code>Base.process_messages(rd::IO, wr::IO)()</code> must be called. This launches a new task that handles reading and writing of messages from/to the worker represented by the <code>IO</code> objects.</li><li><code>init_worker(cookie, manager::FooManager)</code> <em>must</em> be called as part of worker process initialization.</li><li>Field <code>connect_at::Any</code> in <code>WorkerConfig</code> can be set by the cluster manager when <a href="../stdlib/Distributed.html#Distributed.launch"><code>launch</code></a> is called. The value of this field is passed in all <a href="../stdlib/Distributed.html#Sockets.connect-Tuple{ClusterManager, Int64, WorkerConfig}"><code>connect</code></a> callbacks. Typically, it carries information on <em>how to connect</em> to a worker. For example, the TCP/IP socket transport uses this field to specify the <code>(host, port)</code> tuple at which to connect to a worker.</li></ul><p><code>kill(manager, pid, config)</code> is called to remove a worker from the cluster. On the master process, the corresponding <code>IO</code> objects must be closed by the implementation to ensure proper cleanup. The default implementation simply executes an <code>exit()</code> call on the specified remote worker.</p><p>The Examples folder <code>clustermanager/simple</code> is an example that shows a simple implementation using UNIX domain sockets for cluster setup.</p><h3 id="Network-Requirements-for-LocalManager-and-SSHManager"><a class="docs-heading-anchor" href="#Network-Requirements-for-LocalManager-and-SSHManager">Network Requirements for LocalManager and SSHManager</a><a id="Network-Requirements-for-LocalManager-and-SSHManager-1"></a><a class="docs-heading-anchor-permalink" href="#Network-Requirements-for-LocalManager-and-SSHManager" title="Permalink"></a></h3><p>Julia clusters are designed to be executed on already secured environments on infrastructure such as local laptops, departmental clusters, or even the cloud. This section covers network security requirements for the inbuilt <code>LocalManager</code> and <code>SSHManager</code>:</p><ul><li><p>The master process does not listen on any port. It only connects out to the workers.</p></li><li><p>Each worker binds to only one of the local interfaces and listens on an ephemeral port number assigned by the OS.</p></li><li><p><code>LocalManager</code>, used by <code>addprocs(N)</code>, by default binds only to the loopback interface. This means that workers started later on remote hosts (or by anyone with malicious intentions) are unable to connect to the cluster. An <code>addprocs(4)</code> followed by an <code>addprocs([&quot;remote_host&quot;])</code> will fail. Some users may need to create a cluster comprising their local system and a few remote systems. This can be done by explicitly requesting <code>LocalManager</code> to bind to an external network interface via the <code>restrict</code> keyword argument: <code>addprocs(4; restrict=false)</code>.</p></li><li><p><code>SSHManager</code>, used by <code>addprocs(list_of_remote_hosts)</code>, launches workers on remote hosts via SSH. By default SSH is only used to launch Julia workers. Subsequent master-worker and worker-worker connections use plain, unencrypted TCP/IP sockets. The remote hosts must have passwordless login enabled. Additional SSH flags or credentials may be specified via keyword argument <code>sshflags</code>.</p></li><li><p><code>addprocs(list_of_remote_hosts; tunnel=true, sshflags=&lt;ssh keys and other flags&gt;)</code> is useful when we wish to use SSH connections for master-worker too. A typical scenario for this is a local laptop running the Julia REPL (i.e., the master) with the rest of the cluster on the cloud, say on Amazon EC2. In this case only port 22 needs to be opened at the remote cluster coupled with SSH client authenticated via public key infrastructure (PKI). Authentication credentials can be supplied via <code>sshflags</code>, for example <code>sshflags=`-i &lt;keyfile&gt;`</code>.</p><p>In an all-to-all topology (the default), all workers connect to each other via plain TCP sockets. The security policy on the cluster nodes must thus ensure free connectivity between workers for the ephemeral port range (varies by OS).</p><p>Securing and encrypting all worker-worker traffic (via SSH) or encrypting individual messages can be done via a custom <code>ClusterManager</code>.</p></li><li><p>If you specify <code>multiplex=true</code> as an option to <a href="../stdlib/Distributed.html#Distributed.addprocs"><code>addprocs</code></a>, SSH multiplexing is used to create a tunnel between the master and workers. If you have configured SSH multiplexing on your own and the connection has already been established, SSH multiplexing is used regardless of <code>multiplex</code> option. If multiplexing is enabled, forwarding is set by using the existing connection (<code>-O forward</code> option in ssh). This is beneficial if your servers require password authentication; you can avoid authentication in Julia by logging in to the server ahead of <a href="../stdlib/Distributed.html#Distributed.addprocs"><code>addprocs</code></a>. The control socket will be located at <code>~/.ssh/julia-%r@%h:%p</code> during the session unless the existing multiplexing connection is used. Note that bandwidth may be limited if you create multiple processes on a node and enable multiplexing, because in that case processes share a single multiplexing TCP connection.</p></li></ul><h3 id="man-cluster-cookie"><a class="docs-heading-anchor" href="#man-cluster-cookie">Cluster Cookie</a><a id="man-cluster-cookie-1"></a><a class="docs-heading-anchor-permalink" href="#man-cluster-cookie" title="Permalink"></a></h3><p>All processes in a cluster share the same cookie which, by default, is a randomly generated string on the master process:</p><ul><li><a href="../stdlib/Distributed.html#Distributed.cluster_cookie-Tuple{}"><code>cluster_cookie()</code></a> returns the cookie, while <code>cluster_cookie(cookie)()</code> sets it and returns the new cookie.</li><li>All connections are authenticated on both sides to ensure that only workers started by the master are allowed to connect to each other.</li><li>The cookie may be passed to the workers at startup via argument <code>--worker=&lt;cookie&gt;</code>. If argument <code>--worker</code> is specified without the cookie, the worker tries to read the cookie from its standard input (<a href="../base/io-network.html#Base.stdin"><code>stdin</code></a>). The <code>stdin</code> is closed immediately after the cookie is retrieved.</li><li><code>ClusterManager</code>s can retrieve the cookie on the master by calling <a href="../stdlib/Distributed.html#Distributed.cluster_cookie-Tuple{}"><code>cluster_cookie()</code></a>. Cluster managers not using the default TCP/IP transport (and hence not specifying <code>--worker</code>) must call <code>init_worker(cookie, manager)</code> with the same cookie as on the master.</li></ul><p>Note that environments requiring higher levels of security can implement this via a custom <code>ClusterManager</code>. For example, cookies can be pre-shared and hence not specified as a startup argument.</p><h2 id="Specifying-Network-Topology-(Experimental)"><a class="docs-heading-anchor" href="#Specifying-Network-Topology-(Experimental)">Specifying Network Topology (Experimental)</a><a id="Specifying-Network-Topology-(Experimental)-1"></a><a class="docs-heading-anchor-permalink" href="#Specifying-Network-Topology-(Experimental)" title="Permalink"></a></h2><p>The keyword argument <code>topology</code> passed to <a href="../stdlib/Distributed.html#Distributed.addprocs"><code>addprocs</code></a> is used to specify how the workers must be connected to each other:</p><ul><li><code>:all_to_all</code>, the default: all workers are connected to each other.</li><li><code>:master_worker</code>: only the driver process, i.e. <code>pid</code> 1, has connections to the workers.</li><li><code>:custom</code>: the <code>launch</code> method of the cluster manager specifies the connection topology via the fields <code>ident</code> and <code>connect_idents</code> in <code>WorkerConfig</code>. A worker with a cluster-manager-provided identity <code>ident</code> will connect to all workers specified in <code>connect_idents</code>.</li></ul><p>Keyword argument <code>lazy=true|false</code> only affects <code>topology</code> option <code>:all_to_all</code>. If <code>true</code>, the cluster starts off with the master connected to all workers. Specific worker-worker connections are established at the first remote invocation between two workers. This helps in reducing initial resources allocated for intra-cluster communication. Connections are setup depending on the runtime requirements of a parallel program. Default value for <code>lazy</code> is <code>true</code>.</p><p>Currently, sending a message between unconnected workers results in an error. This behaviour, as with the functionality and interface, should be considered experimental in nature and may change in future releases.</p><h2 id="Noteworthy-external-packages"><a class="docs-heading-anchor" href="#Noteworthy-external-packages">Noteworthy external packages</a><a id="Noteworthy-external-packages-1"></a><a class="docs-heading-anchor-permalink" href="#Noteworthy-external-packages" title="Permalink"></a></h2><p>Outside of Julia parallelism there are plenty of external packages that should be mentioned. For example, <a href="https://github.com/JuliaParallel/MPI.jl"><code>MPI.jl</code></a> is a Julia wrapper for the <code>MPI</code> protocol, <a href="https://github.com/JuliaParallel/Dagger.jl"><code>Dagger.jl</code></a> provides functionality similar to Python&#39;s <a href="https://dask.org/">Dask</a>, and <a href="https://github.com/JuliaParallel/Distributedarrays.jl"><code>DistributedArrays.jl</code></a> provides array operations distributed across workers, as <a href="distributed-computing.html#man-shared-arrays">outlined above</a>.</p><p>A mention must be made of Julia&#39;s GPU programming ecosystem, which includes:</p><ol><li><p><a href="https://github.com/JuliaGPU/CUDA.jl">CUDA.jl</a> wraps the various CUDA libraries and supports compiling Julia kernels for Nvidia GPUs.</p></li><li><p><a href="https://github.com/JuliaGPU/oneAPI.jl">oneAPI.jl</a> wraps the oneAPI unified programming model, and supports executing Julia kernels on supported accelerators. Currently only Linux is supported.</p></li><li><p><a href="https://github.com/JuliaGPU/AMDGPU.jl">AMDGPU.jl</a> wraps the AMD ROCm libraries and supports compiling Julia kernels for AMD GPUs. Currently only Linux is supported.</p></li><li><p>High-level libraries like <a href="https://github.com/JuliaGPU/KernelAbstractions.jl">KernelAbstractions.jl</a>, <a href="https://github.com/mcabbott/Tullio.jl">Tullio.jl</a> and <a href="https://github.com/JuliaComputing/ArrayFire.jl">ArrayFire.jl</a>.</p></li></ol><p>In the following example we will use both <code>DistributedArrays.jl</code> and <code>CUDA.jl</code> to distribute an array across multiple processes by first casting it through <code>distribute()</code> and <code>CuArray()</code>.</p><p>Remember when importing <code>DistributedArrays.jl</code> to import it across all processes using <a href="../stdlib/Distributed.html#Distributed.@everywhere"><code>@everywhere</code></a></p><pre><code class="language-julia-repl hljs">$ ./julia -p 4

julia&gt; addprocs()

julia&gt; @everywhere using DistributedArrays

julia&gt; using CUDA

julia&gt; B = ones(10_000) ./ 2;

julia&gt; A = ones(10_000) .* π;

julia&gt; C = 2 .* A ./ B;

julia&gt; all(C .≈ 4*π)
true

julia&gt; typeof(C)
Array{Float64,1}

julia&gt; dB = distribute(B);

julia&gt; dA = distribute(A);

julia&gt; dC = 2 .* dA ./ dB;

julia&gt; all(dC .≈ 4*π)
true

julia&gt; typeof(dC)
DistributedArrays.DArray{Float64,1,Array{Float64,1}}

julia&gt; cuB = CuArray(B);

julia&gt; cuA = CuArray(A);

julia&gt; cuC = 2 .* cuA ./ cuB;

julia&gt; all(cuC .≈ 4*π);
true

julia&gt; typeof(cuC)
CuArray{Float64,1}</code></pre><p>In the following example we will use both <code>DistributedArrays.jl</code> and <code>CUDA.jl</code> to distribute an array across multiple processes and call a generic function on it.</p><pre><code class="language-julia hljs">function power_method(M, v)
    for i in 1:100
        v = M*v
        v /= norm(v)
    end

    return v, norm(M*v) / norm(v)  # or  (M*v) ./ v
end</code></pre><p><code>power_method</code> repeatedly creates a new vector and normalizes it. We have not specified any type signature in function declaration, let&#39;s see if it works with the aforementioned datatypes:</p><pre><code class="language-julia-repl hljs">julia&gt; M = [2. 1; 1 1];

julia&gt; v = rand(2)
2-element Array{Float64,1}:
0.40395
0.445877

julia&gt; power_method(M,v)
([0.850651, 0.525731], 2.618033988749895)

julia&gt; cuM = CuArray(M);

julia&gt; cuv = CuArray(v);

julia&gt; curesult = power_method(cuM, cuv);

julia&gt; typeof(curesult)
CuArray{Float64,1}

julia&gt; dM = distribute(M);

julia&gt; dv = distribute(v);

julia&gt; dC = power_method(dM, dv);

julia&gt; typeof(dC)
Tuple{DistributedArrays.DArray{Float64,1,Array{Float64,1}},Float64}</code></pre><p>To end this short exposure to external packages, we can consider <code>MPI.jl</code>, a Julia wrapper of the MPI protocol. As it would take too long to consider every inner function, it would be better to simply appreciate the approach used to implement the protocol.</p><p>Consider this toy script which simply calls each subprocess, instantiate its rank and when the master process is reached, performs the ranks&#39; sum</p><pre><code class="language-julia hljs">import MPI

MPI.Init()

comm = MPI.COMM_WORLD
MPI.Barrier(comm)

root = 0
r = MPI.Comm_rank(comm)

sr = MPI.Reduce(r, MPI.SUM, root, comm)

if(MPI.Comm_rank(comm) == root)
   @printf(&quot;sum of ranks: %s\n&quot;, sr)
end

MPI.Finalize()</code></pre><pre><code class="nohighlight hljs">mpirun -np 4 ./julia example.jl</code></pre><section class="footnotes is-size-7"><ul><li class="footnote" id="footnote-1"><a class="tag is-link" href="#citeref-1">1</a>In this context, MPI refers to the MPI-1 standard. Beginning with MPI-2, the MPI standards committee introduced a new set of communication mechanisms, collectively referred to as Remote Memory Access (RMA). The motivation for adding rma to the MPI standard was to facilitate one-sided communication patterns. For additional information on the latest MPI standard, see <a href="https://mpi-forum.org/docs">https://mpi-forum.org/docs</a>.</li></ul></section></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="multi-threading.html">« Multi-Threading</a><a class="docs-footer-nextpage" href="running-external-programs.html">Running External Programs »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
