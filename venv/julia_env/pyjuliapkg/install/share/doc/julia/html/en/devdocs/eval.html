<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Eval of Julia code · The Julia Language</title><meta name="title" content="Eval of Julia code · The Julia Language"/><meta property="og:title" content="Eval of Julia code · The Julia Language"/><meta property="twitter:title" content="Eval of Julia code · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox" checked/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="ast.html">Julia ASTs</a></li><li><a class="tocitem" href="types.html">More about types</a></li><li><a class="tocitem" href="object.html">Memory layout of Julia Objects</a></li><li class="is-active"><a class="tocitem" href="eval.html">Eval of Julia code</a><ul class="internal"><li><a class="tocitem" href="#Julia-Execution"><span>Julia Execution</span></a></li><li><a class="tocitem" href="#dev-parsing"><span>Parsing</span></a></li><li><a class="tocitem" href="#dev-macro-expansion"><span>Macro Expansion</span></a></li><li><a class="tocitem" href="#dev-type-inference"><span>Type Inference</span></a></li><li><a class="tocitem" href="#dev-codegen"><span>JIT Code Generation</span></a></li><li><a class="tocitem" href="#dev-sysimg"><span>System Image</span></a></li></ul></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="functions.html">Julia Functions</a></li><li><a class="tocitem" href="cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="subarrays.html">SubArrays</a></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Documentation of Julia&#39;s Internals</a></li><li class="is-active"><a href="eval.html">Eval of Julia code</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="eval.html">Eval of Julia code</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/eval.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Eval-of-Julia-code"><a class="docs-heading-anchor" href="#Eval-of-Julia-code">Eval of Julia code</a><a id="Eval-of-Julia-code-1"></a><a class="docs-heading-anchor-permalink" href="#Eval-of-Julia-code" title="Permalink"></a></h1><p>One of the hardest parts about learning how the Julia Language runs code is learning how all of the pieces work together to execute a block of code.</p><p>Each chunk of code typically makes a trip through many steps with potentially unfamiliar names, such as (in no particular order): flisp, AST, C++, LLVM, <code>eval</code>, <code>typeinf</code>, <code>macroexpand</code>, sysimg (or system image), bootstrapping, compile, parse, execute, JIT, interpret, box, unbox, intrinsic function, and primitive function, before turning into the desired result (hopefully).</p><div class="admonition is-category-sidebar"><header class="admonition-header">Definitions</header><div class="admonition-body"><ul><li><p>REPL</p><p>REPL stands for Read-Eval-Print Loop. It&#39;s just what we call the command line environment for short.</p></li><li><p>AST</p><p>Abstract Syntax Tree The AST is the digital representation of the code structure. In this form the code has been tokenized for meaning so that it is more suitable for manipulation and execution.</p></li></ul></div></div><p><img src="img/compiler_diagram.png" alt="Diagram of the compiler flow"/></p><h2 id="Julia-Execution"><a class="docs-heading-anchor" href="#Julia-Execution">Julia Execution</a><a id="Julia-Execution-1"></a><a class="docs-heading-anchor-permalink" href="#Julia-Execution" title="Permalink"></a></h2><p>The 10,000 foot view of the whole process is as follows:</p><ol><li>The user starts <code>julia</code>.</li><li>The C function <code>main()</code> from <code>cli/loader_exe.c</code> gets called. This function processes the command line arguments, filling in the <code>jl_options</code> struct and setting the variable <code>ARGS</code>. It then initializes Julia (by calling <a href="https://github.com/JuliaLang/julia/blob/master/src/init.c"><code>julia_init</code> in <code>init.c</code></a>, which may load a previously compiled <a href="eval.html#dev-sysimg">sysimg</a>). Finally, it passes off control to Julia by calling <a href="https://github.com/JuliaLang/julia/blob/master/base/client.jl"><code>Base._start()</code></a>.</li><li>When <code>_start()</code> takes over control, the subsequent sequence of commands depends on the command line arguments given. For example, if a filename was supplied, it will proceed to execute that file. Otherwise, it will start an interactive REPL.</li><li>Skipping the details about how the REPL interacts with the user, let&#39;s just say the program ends up with a block of code that it wants to run.</li><li>If the block of code to run is in a file, <a href="https://github.com/JuliaLang/julia/blob/master/src/toplevel.c"><code>jl_load(char *filename)</code></a> gets invoked to load the file and <a href="eval.html#dev-parsing">parse</a> it. Each fragment of code is then passed to <code>eval</code> to execute.</li><li>Each fragment of code (or AST), is handed off to <a href="../base/base.html#eval"><code>eval()</code></a> to turn into results.</li><li><a href="../base/base.html#eval"><code>eval()</code></a> takes each code fragment and tries to run it in <a href="https://github.com/JuliaLang/julia/blob/master/src/toplevel.c"><code>jl_toplevel_eval_flex()</code></a>.</li><li><code>jl_toplevel_eval_flex()</code> decides whether the code is a &quot;toplevel&quot; action (such as <code>using</code> or <code>module</code>), which would be invalid inside a function. If so, it passes off the code to the toplevel interpreter.</li><li><code>jl_toplevel_eval_flex()</code> then <a href="eval.html#dev-macro-expansion">expands</a> the code to eliminate any macros and to &quot;lower&quot; the AST to make it simpler to execute.</li><li><code>jl_toplevel_eval_flex()</code> then uses some simple heuristics to decide whether to JIT compile the  AST or to interpret it directly.</li><li>The bulk of the work to interpret code is handled by <a href="https://github.com/JuliaLang/julia/blob/master/src/interpreter.c"><code>eval</code> in <code>interpreter.c</code></a>.</li><li>If instead, the code is compiled, the bulk of the work is handled by <code>codegen.cpp</code>. Whenever a  Julia function is called for the first time with a given set of argument types, <a href="eval.html#dev-type-inference">type inference</a>  will be run on that function. This information is used by the <a href="eval.html#dev-codegen">codegen</a> step to generate  faster code.</li><li>Eventually, the user quits the REPL, or the end of the program is reached, and the <code>_start()</code>  method returns.</li><li>Just before exiting, <code>main()</code> calls <a href="https://github.com/JuliaLang/julia/blob/master/src/init.c"><code>jl_atexit_hook(exit_code)</code></a>.  This calls <code>Base._atexit()</code> (which calls any functions registered to <a href="../base/base.html#Base.atexit"><code>atexit()</code></a> inside  Julia). Then it calls <a href="https://github.com/JuliaLang/julia/blob/master/src/gc.c"><code>jl_gc_run_all_finalizers()</code></a>.  Finally, it gracefully cleans up all <code>libuv</code> handles and waits for them to flush and close.</li></ol><h2 id="dev-parsing"><a class="docs-heading-anchor" href="#dev-parsing">Parsing</a><a id="dev-parsing-1"></a><a class="docs-heading-anchor-permalink" href="#dev-parsing" title="Permalink"></a></h2><p>The Julia parser is a small lisp program written in femtolisp, the source-code for which is distributed inside Julia in <a href="https://github.com/JuliaLang/julia/tree/master/src/flisp">src/flisp</a>.</p><p>The interface functions for this are primarily defined in <a href="https://github.com/JuliaLang/julia/blob/master/src/jlfrontend.scm"><code>jlfrontend.scm</code></a>. The code in <a href="https://github.com/JuliaLang/julia/blob/master/src/ast.c"><code>ast.c</code></a> handles this handoff on the Julia side.</p><p>The other relevant files at this stage are <a href="https://github.com/JuliaLang/julia/blob/master/src/julia-parser.scm"><code>julia-parser.scm</code></a>, which handles tokenizing Julia code and turning it into an AST, and <a href="https://github.com/JuliaLang/julia/blob/master/src/julia-syntax.scm"><code>julia-syntax.scm</code></a>, which handles transforming complex AST representations into simpler, &quot;lowered&quot; AST representations which are more suitable for analysis and execution.</p><p>If you want to test the parser without re-building Julia in its entirety, you can run the frontend on its own as follows:</p><pre><code class="nohighlight hljs">$ cd src
$ flisp/flisp
&gt; (load &quot;jlfrontend.scm&quot;)
&gt; (jl-parse-file &quot;&lt;filename&gt;&quot;)</code></pre><h2 id="dev-macro-expansion"><a class="docs-heading-anchor" href="#dev-macro-expansion">Macro Expansion</a><a id="dev-macro-expansion-1"></a><a class="docs-heading-anchor-permalink" href="#dev-macro-expansion" title="Permalink"></a></h2><p>When <a href="../base/base.html#eval"><code>eval()</code></a> encounters a macro, it expands that AST node before attempting to evaluate the expression. Macro expansion involves a handoff from <a href="../base/base.html#eval"><code>eval()</code></a> (in Julia), to the parser function <code>jl_macroexpand()</code> (written in <code>flisp</code>) to the Julia macro itself (written in - what else - Julia) via <code>fl_invoke_julia_macro()</code>, and back.</p><p>Typically, macro expansion is invoked as a first step during a call to <a href="../base/base.html#Base.Meta.lower"><code>Meta.lower()</code></a>/<code>jl_expand()</code>, although it can also be invoked directly by a call to <a href="../base/base.html#Base.macroexpand"><code>macroexpand()</code></a>/<code>jl_macroexpand()</code>.</p><h2 id="dev-type-inference"><a class="docs-heading-anchor" href="#dev-type-inference">Type Inference</a><a id="dev-type-inference-1"></a><a class="docs-heading-anchor-permalink" href="#dev-type-inference" title="Permalink"></a></h2><p>Type inference is implemented in Julia by <a href="https://github.com/JuliaLang/julia/blob/master/base/compiler/typeinfer.jl"><code>typeinf()</code> in <code>compiler/typeinfer.jl</code></a>. Type inference is the process of examining a Julia function and determining bounds for the types of each of its variables, as well as bounds on the type of the return value from the function. This enables many future optimizations, such as unboxing of known immutable values, and compile-time hoisting of various run-time operations such as computing field offsets and function pointers. Type inference may also include other steps such as constant propagation and inlining.</p><div class="admonition is-category-sidebar"><header class="admonition-header">More Definitions</header><div class="admonition-body"><ul><li><p>JIT</p><p>Just-In-Time Compilation The process of generating native-machine code into memory right when it is needed.</p></li><li><p>LLVM</p><p>Low-Level Virtual Machine (a compiler) The Julia JIT compiler is a program/library called libLLVM. Codegen in Julia refers both to the process of taking a Julia AST and turning it into LLVM instructions, and the process of LLVM optimizing that and turning it into native assembly instructions.</p></li><li><p>C++</p><p>The programming language that LLVM is implemented in, which means that codegen is also implemented in this language. The rest of Julia&#39;s library is implemented in C, in part because its smaller feature set makes it more usable as a cross-language interface layer.</p></li><li><p>box</p><p>This term is used to describe the process of taking a value and allocating a wrapper around the data that is tracked by the garbage collector (gc) and is tagged with the object&#39;s type.</p></li><li><p>unbox</p><p>The reverse of boxing a value. This operation enables more efficient manipulation of data when the type of that data is fully known at compile-time (through type inference).</p></li><li><p>generic function</p><p>A Julia function composed of multiple &quot;methods&quot; that are selected for dynamic dispatch based on the argument type-signature</p></li><li><p>anonymous function or &quot;method&quot;</p><p>A Julia function without a name and without type-dispatch capabilities</p></li><li><p>primitive function</p><p>A function implemented in C but exposed in Julia as a named function &quot;method&quot; (albeit without generic function dispatch capabilities, similar to a anonymous function)</p></li><li><p>intrinsic function</p><p>A low-level operation exposed as a function in Julia. These pseudo-functions implement operations on raw bits such as add and sign extend that cannot be expressed directly in any other way. Since they operate on bits directly, they must be compiled into a function and surrounded by a call to <code>Core.Intrinsics.box(T, ...)</code> to reassign type information to the value.</p></li></ul></div></div><h2 id="dev-codegen"><a class="docs-heading-anchor" href="#dev-codegen">JIT Code Generation</a><a id="dev-codegen-1"></a><a class="docs-heading-anchor-permalink" href="#dev-codegen" title="Permalink"></a></h2><p>Codegen is the process of turning a Julia AST into native machine code.</p><p>The JIT environment is initialized by an early call to <a href="https://github.com/JuliaLang/julia/blob/master/src/codegen.cpp"><code>jl_init_codegen</code> in <code>codegen.cpp</code></a>.</p><p>On demand, a Julia method is converted into a native function by the function <code>emit_function(jl_method_instance_t*)</code>. (note, when using the MCJIT (in LLVM v3.4+), each function must be JIT into a new module.) This function recursively calls <code>emit_expr()</code> until the entire function has been emitted.</p><p>Much of the remaining bulk of this file is devoted to various manual optimizations of specific code patterns. For example, <code>emit_known_call()</code> knows how to inline many of the primitive functions (defined in <a href="https://github.com/JuliaLang/julia/blob/master/src/builtins.c"><code>builtins.c</code></a>) for various combinations of argument types.</p><p>Other parts of codegen are handled by various helper files:</p><ul><li><p><a href="https://github.com/JuliaLang/julia/blob/master/src/debuginfo.cpp"><code>debuginfo.cpp</code></a></p><p>Handles backtraces for JIT functions</p></li><li><p><a href="https://github.com/JuliaLang/julia/blob/master/src/ccall.cpp"><code>ccall.cpp</code></a></p><p>Handles the ccall and llvmcall FFI, along with various <code>abi_*.cpp</code> files</p></li><li><p><a href="https://github.com/JuliaLang/julia/blob/master/src/intrinsics.cpp"><code>intrinsics.cpp</code></a></p><p>Handles the emission of various low-level intrinsic functions</p></li></ul><div class="admonition is-category-sidebar"><header class="admonition-header">Bootstrapping</header><div class="admonition-body"><p>The process of creating a new system image is called &quot;bootstrapping&quot;.</p><p>The etymology of this word comes from the phrase &quot;pulling oneself up by the bootstraps&quot;, and refers to the idea of starting from a very limited set of available functions and definitions and ending with the creation of a full-featured environment.</p></div></div><h2 id="dev-sysimg"><a class="docs-heading-anchor" href="#dev-sysimg">System Image</a><a id="dev-sysimg-1"></a><a class="docs-heading-anchor-permalink" href="#dev-sysimg" title="Permalink"></a></h2><p>The system image is a precompiled archive of a set of Julia files. The <code>sys.ji</code> file distributed with Julia is one such system image, generated by executing the file <a href="https://github.com/JuliaLang/julia/blob/master/base/sysimg.jl"><code>sysimg.jl</code></a>, and serializing the resulting environment (including Types, Functions, Modules, and all other defined values) into a file. Therefore, it contains a frozen version of the <code>Main</code>, <code>Core</code>, and <code>Base</code> modules (and whatever else was in the environment at the end of bootstrapping). This serializer/deserializer is implemented by <a href="https://github.com/JuliaLang/julia/blob/master/src/staticdata.c"><code>jl_save_system_image</code>/<code>jl_restore_system_image</code> in <code>staticdata.c</code></a>.</p><p>If there is no sysimg file (<code>jl_options.image_file == NULL</code>), this also implies that <code>--build</code> was given on the command line, so the final result should be a new sysimg file. During Julia initialization, minimal <code>Core</code> and <code>Main</code> modules are created. Then a file named <code>boot.jl</code> is evaluated from the current directory. Julia then evaluates any file given as a command line argument until it reaches the end. Finally, it saves the resulting environment to a &quot;sysimg&quot; file for use as a starting point for a future Julia run.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="object.html">« Memory layout of Julia Objects</a><a class="docs-footer-nextpage" href="callconv.html">Calling Conventions »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
