<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Code Loading · The Julia Language</title><meta name="title" content="Code Loading · The Julia Language"/><meta property="og:title" content="Code Loading · The Julia Language"/><meta property="twitter:title" content="Code Loading · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li class="is-active"><a class="tocitem" href="code-loading.html">Code Loading</a><ul class="internal"><li><a class="tocitem" href="#Definitions"><span>Definitions</span></a></li><li><a class="tocitem" href="#Federation-of-packages"><span>Federation of packages</span></a></li><li><a class="tocitem" href="#Environments"><span>Environments</span></a></li><li><a class="tocitem" href="#Conclusion"><span>Conclusion</span></a></li></ul></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="code-loading.html">Code Loading</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="code-loading.html">Code Loading</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/code-loading.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="code-loading"><a class="docs-heading-anchor" href="#code-loading">Code Loading</a><a id="code-loading-1"></a><a class="docs-heading-anchor-permalink" href="#code-loading" title="Permalink"></a></h1><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This chapter covers the technical details of package loading. To install packages, use <a href="../stdlib/Pkg.html#Pkg"><code>Pkg</code></a>, Julia&#39;s built-in package manager, to add packages to your active environment. To use packages already in your active environment, write <code>import X</code> or <code>using X</code>, as described in the <a href="modules.html#modules">Modules documentation</a>.</p></div></div><h2 id="Definitions"><a class="docs-heading-anchor" href="#Definitions">Definitions</a><a id="Definitions-1"></a><a class="docs-heading-anchor-permalink" href="#Definitions" title="Permalink"></a></h2><p>Julia has two mechanisms for loading code:</p><ol><li><strong>Code inclusion:</strong> e.g. <code>include(&quot;source.jl&quot;)</code>. Inclusion allows you to split a single program across multiple source files. The expression <code>include(&quot;source.jl&quot;)</code> causes the contents of the file <code>source.jl</code> to be evaluated in the global scope of the module where the <code>include</code> call occurs. If <code>include(&quot;source.jl&quot;)</code> is called multiple times, <code>source.jl</code> is evaluated multiple times. The included path, <code>source.jl</code>, is interpreted relative to the file where the <code>include</code> call occurs. This makes it simple to relocate a subtree of source files. In the REPL, included paths are interpreted relative to the current working directory, <a href="../base/file.html#Base.Filesystem.pwd"><code>pwd()</code></a>.</li><li><strong>Package loading:</strong> e.g. <code>import X</code> or <code>using X</code>. The import mechanism allows you to load a package—i.e. an independent, reusable collection of Julia code, wrapped in a module—and makes the resulting module available by the name <code>X</code> inside of the importing module. If the same <code>X</code> package is imported multiple times in the same Julia session, it is only loaded the first time—on subsequent imports, the importing module gets a reference to the same module. Note though, that <code>import X</code> can load different packages in different contexts: <code>X</code> can refer to one package named <code>X</code> in the main project but potentially to different packages also named <code>X</code> in each dependency. More on this below.</li></ol><p>Code inclusion is quite straightforward and simple: it evaluates the given source file in the context of the caller. Package loading is built on top of code inclusion and serves a <a href="modules.html#modules">different purpose</a>. The rest of this chapter focuses on the behavior and mechanics of package loading.</p><p>A <em>package</em> is a source tree with a standard layout providing functionality that can be reused by other Julia projects. A package is loaded by <code>import X</code> or  <code>using X</code> statements. These statements also make the module named <code>X</code>—which results from loading the package code—available within the module where the import statement occurs. The meaning of <code>X</code> in <code>import X</code> is context-dependent: which <code>X</code> package is loaded depends on what code the statement occurs in. Thus, handling of <code>import X</code> happens in two stages: first, it determines <strong>what</strong> package is defined to be <code>X</code> in this context; second, it determines <strong>where</strong> that particular <code>X</code> package is found.</p><p>These questions are answered by searching through the project environments listed in <a href="../base/constants.html#Base.LOAD_PATH"><code>LOAD_PATH</code></a> for project files (<code>Project.toml</code> or <code>JuliaProject.toml</code>), manifest files (<code>Manifest.toml</code> or <code>JuliaManifest.toml</code>, or the same names suffixed by <code>-v{major}.{minor}.toml</code> for specific versions), or folders of source files.</p><h2 id="Federation-of-packages"><a class="docs-heading-anchor" href="#Federation-of-packages">Federation of packages</a><a id="Federation-of-packages-1"></a><a class="docs-heading-anchor-permalink" href="#Federation-of-packages" title="Permalink"></a></h2><p>Most of the time, a package is uniquely identifiable simply from its name. However, sometimes a project might encounter a situation where it needs to use two different packages that share the same name. While you might be able fix this by renaming one of the packages, being forced to do so can be highly disruptive in a large, shared code base. Instead, Julia&#39;s code loading mechanism allows the same package name to refer to different packages in different components of an application.</p><p>Julia supports federated package management, which means that multiple independent parties can maintain both public and private packages and registries of packages, and that projects can depend on a mix of public and private packages from different registries. Packages from various registries are installed and managed using a common set of tools and workflows. The <code>Pkg</code> package manager that ships with Julia lets you install and manage your projects&#39; dependencies. It assists in creating and manipulating project files (which describe what other projects that your project depends on), and manifest files (which snapshot exact versions of your project&#39;s complete dependency graph).</p><p>One consequence of federation is that there cannot be a central authority for package naming. Different entities may use the same name to refer to unrelated packages. This possibility is unavoidable since these entities do not coordinate and may not even know about each other. Because of the lack of a central naming authority, a single project may end up depending on different packages that have the same name. Julia&#39;s package loading mechanism does not require package names to be globally unique, even within the dependency graph of a single project. Instead, packages are identified by <a href="https://en.wikipedia.org/wiki/Universally_unique_identifier">universally unique identifiers</a> (UUIDs), which get assigned when each package is created. Usually you won&#39;t have to work directly with these somewhat cumbersome 128-bit identifiers since <code>Pkg</code> will take care of generating and tracking them for you. However, these UUIDs provide the definitive answer to the question of <em>&quot;what package does <code>X</code> refer to?&quot;</em></p><p>Since the decentralized naming problem is somewhat abstract, it may help to walk through a concrete scenario to understand the issue. Suppose you&#39;re developing an application called <code>App</code>, which uses two packages: <code>Pub</code> and  <code>Priv</code>. <code>Priv</code> is a private package that you created, whereas <code>Pub</code> is a public package that you use but don&#39;t control. When you created <code>Priv</code>, there was no public package by the name <code>Priv</code>. Subsequently, however, an unrelated package also named <code>Priv</code> has been published and become popular. In fact, the <code>Pub</code> package has started to use it. Therefore, when you next upgrade <code>Pub</code> to get the latest bug fixes and features, <code>App</code> will end up depending on two different packages named <code>Priv</code>—through no action of yours other than upgrading. <code>App</code> has a direct dependency on your private <code>Priv</code> package, and an indirect dependency, through <code>Pub</code>, on the new public <code>Priv</code> package. Since these two <code>Priv</code> packages are different but are both required for <code>App</code> to continue working correctly, the expression <code>import Priv</code> must refer to different <code>Priv</code> packages depending on whether it occurs in <code>App</code>&#39;s code or in <code>Pub</code>&#39;s code. To handle this, Julia&#39;s package loading mechanism distinguishes the two <code>Priv</code> packages by their UUID and picks the correct one based on its context (the module that called <code>import</code>). How this distinction works is determined by environments, as explained in the following sections.</p><h2 id="Environments"><a class="docs-heading-anchor" href="#Environments">Environments</a><a id="Environments-1"></a><a class="docs-heading-anchor-permalink" href="#Environments" title="Permalink"></a></h2><p>An <em>environment</em> determines what <code>import X</code> and <code>using X</code> mean in various code contexts and what files these statements cause to be loaded. Julia understands two kinds of environments:</p><ol><li><strong>A project environment</strong> is a directory with a project file and an optional manifest file, and forms an <em>explicit environment</em>. The project file determines what the names and identities of the direct dependencies of a project are. The manifest file, if present, gives a complete dependency graph, including all direct and indirect dependencies, exact versions of each dependency, and sufficient information to locate and load the correct version.</li><li><strong>A package directory</strong> is a directory containing the source trees of a set of packages as subdirectories, and forms an <em>implicit environment</em>. If <code>X</code> is a subdirectory of a package directory and <code>X/src/X.jl</code> exists, then the package <code>X</code> is available in the package directory environment and <code>X/src/X.jl</code> is the source file by which it is loaded.</li></ol><p>These can be intermixed to create <strong>a stacked environment</strong>: an ordered set of project environments and package directories, overlaid to make a single composite environment. The precedence and visibility rules then combine to determine which packages are available and where they get loaded from. Julia&#39;s load path forms a stacked environment, for example.</p><p>These environment each serve a different purpose:</p><ul><li>Project environments provide <strong>reproducibility</strong>. By checking a project environment into version control—e.g. a git repository—along with the rest of the project&#39;s source code, you can reproduce the exact state of the project and all of its dependencies. The manifest file, in particular, captures the exact version of every dependency, identified by a cryptographic hash of its source tree, which makes it possible for <code>Pkg</code> to retrieve the correct versions and be sure that you are running the exact code that was recorded for all dependencies.</li><li>Package directories provide <strong>convenience</strong> when a full carefully-tracked project environment is unnecessary. They are useful when you want to put a set of packages somewhere and be able to directly use them, without needing to create a project environment for them.</li><li>Stacked environments allow for <strong>adding</strong> tools to the primary environment. You can push an environment of development tools onto the end of the stack to make them available from the REPL and scripts, but not from inside packages.</li></ul><p>At a high-level, each environment conceptually defines three maps: roots, graph and paths. When resolving the meaning of <code>import X</code>, the roots and graph maps are used to determine the identity of <code>X</code>, while the paths map is used to locate the source code of <code>X</code>. The specific roles of the three maps are:</p><ul><li><p><strong>roots:</strong> <code>name::Symbol</code> ⟶ <code>uuid::UUID</code></p><p>An environment&#39;s roots map assigns package names to UUIDs for all the top-level dependencies that the environment makes available to the main project (i.e. the ones that can be loaded in <code>Main</code>). When Julia encounters <code>import X</code> in the main project, it looks up the identity of <code>X</code> as <code>roots[:X]</code>.</p></li><li><p><strong>graph:</strong> <code>context::UUID</code> ⟶ <code>name::Symbol</code> ⟶ <code>uuid::UUID</code></p><p>An environment&#39;s graph is a multilevel map which assigns, for each <code>context</code> UUID, a map from names to UUIDs, similar to the roots map but specific to that <code>context</code>. When Julia sees <code>import X</code> in the code of the package whose UUID is <code>context</code>, it looks up the identity of <code>X</code> as <code>graph[context][:X]</code>. In particular, this means that <code>import X</code> can refer to different packages depending on <code>context</code>.</p></li><li><p><strong>paths:</strong> <code>uuid::UUID</code> × <code>name::Symbol</code> ⟶ <code>path::String</code></p><p>The paths map assigns to each package UUID-name pair, the location of that package&#39;s entry-point source file. After the identity of <code>X</code> in <code>import X</code> has been resolved to a UUID via roots or graph (depending on whether it is loaded from the main project or a dependency), Julia determines what file to load to acquire <code>X</code> by looking up <code>paths[uuid,:X]</code> in the environment. Including this file should define a module named <code>X</code>. Once this package is loaded, any subsequent import resolving to the same <code>uuid</code> will create a new binding to the already-loaded package module.</p></li></ul><p>Each kind of environment defines these three maps differently, as detailed in the following sections.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>For ease of understanding, the examples throughout this chapter show full data structures for roots, graph and paths. However, Julia&#39;s package loading code does not explicitly create these. Instead, it lazily computes only as much of each structure as it needs to load a given package.</p></div></div><h3 id="Project-environments"><a class="docs-heading-anchor" href="#Project-environments">Project environments</a><a id="Project-environments-1"></a><a class="docs-heading-anchor-permalink" href="#Project-environments" title="Permalink"></a></h3><p>A project environment is determined by a directory containing a project file called <code>Project.toml</code>, and optionally a manifest file called <code>Manifest.toml</code>. These files may also be called <code>JuliaProject.toml</code> and <code>JuliaManifest.toml</code>, in which case <code>Project.toml</code> and <code>Manifest.toml</code> are ignored. This allows for coexistence with other tools that might consider files called <code>Project.toml</code> and <code>Manifest.toml</code> significant. For pure Julia projects, however, the names <code>Project.toml</code> and <code>Manifest.toml</code> are preferred. However, from Julia v1.10.8 onwards, <code>(Julia)Manifest-v{major}.{minor}.toml</code> is recognized as a format to make a given julia version use a specific manifest file i.e. in the same folder, a <code>Manifest-v1.11.toml</code> would be used by v1.11 and <code>Manifest.toml</code> by any other julia version.</p><p>The roots, graph and paths maps of a project environment are defined as follows:</p><p><strong>The roots map</strong> of the environment is determined by the contents of the project file, specifically, its top-level <code>name</code> and <code>uuid</code> entries and its <code>[deps]</code> section (all optional). Consider the following example project file for the hypothetical application, <code>App</code>, as described earlier:</p><pre><code class="language-toml hljs">name = &quot;App&quot;
uuid = &quot;8f986787-14fe-4607-ba5d-fbff2944afa9&quot;

[deps]
Priv = &quot;ba13f791-ae1d-465a-978b-69c3ad90f72b&quot;
Pub  = &quot;c07ecb7d-0dc9-4db7-8803-fadaaeaf08e1&quot;</code></pre><p>This project file implies the following roots map, if it was represented by a Julia dictionary:</p><pre><code class="language-julia hljs">roots = Dict(
    :App  =&gt; UUID(&quot;8f986787-14fe-4607-ba5d-fbff2944afa9&quot;),
    :Priv =&gt; UUID(&quot;ba13f791-ae1d-465a-978b-69c3ad90f72b&quot;),
    :Pub  =&gt; UUID(&quot;c07ecb7d-0dc9-4db7-8803-fadaaeaf08e1&quot;),
)</code></pre><p>Given this roots map, in <code>App</code>&#39;s code the statement <code>import Priv</code> will cause Julia to look up <code>roots[:Priv]</code>, which yields <code>ba13f791-ae1d-465a-978b-69c3ad90f72b</code>, the UUID of the <code>Priv</code> package that is to be loaded in that context. This UUID identifies which <code>Priv</code> package to load and use when the main application evaluates <code>import Priv</code>.</p><p><strong>The dependency graph</strong> of a project environment is determined by the contents of the manifest file, if present. If there is no manifest file, graph is empty. A manifest file contains a stanza for each of a project&#39;s direct or indirect dependencies. For each dependency, the file lists the package&#39;s UUID and a source tree hash or an explicit path to the source code. Consider the following example manifest file for <code>App</code>:</p><pre><code class="language-toml hljs">[[Priv]] # the private one
deps = [&quot;Pub&quot;, &quot;Zebra&quot;]
uuid = &quot;ba13f791-ae1d-465a-978b-69c3ad90f72b&quot;
path = &quot;deps/Priv&quot;

[[Priv]] # the public one
uuid = &quot;2d15fe94-a1f7-436c-a4d8-07a9a496e01c&quot;
git-tree-sha1 = &quot;1bf63d3be994fe83456a03b874b409cfd59a6373&quot;
version = &quot;0.1.5&quot;

[[Pub]]
uuid = &quot;c07ecb7d-0dc9-4db7-8803-fadaaeaf08e1&quot;
git-tree-sha1 = &quot;9ebd50e2b0dd1e110e842df3b433cb5869b0dd38&quot;
version = &quot;2.1.4&quot;

  [Pub.deps]
  Priv = &quot;2d15fe94-a1f7-436c-a4d8-07a9a496e01c&quot;
  Zebra = &quot;f7a24cb4-21fc-4002-ac70-f0e3a0dd3f62&quot;

[[Zebra]]
uuid = &quot;f7a24cb4-21fc-4002-ac70-f0e3a0dd3f62&quot;
git-tree-sha1 = &quot;e808e36a5d7173974b90a15a353b564f3494092f&quot;
version = &quot;3.4.2&quot;</code></pre><p>This manifest file describes a possible complete dependency graph for the <code>App</code> project:</p><ul><li>There are two different packages named <code>Priv</code> that the application uses. It uses a private package, which is a root dependency, and a public one, which is an indirect dependency through <code>Pub</code>. These are differentiated by their distinct UUIDs, and they have different deps:<ul><li>The private <code>Priv</code> depends on the <code>Pub</code> and <code>Zebra</code> packages.</li><li>The public <code>Priv</code> has no dependencies.</li></ul></li><li>The application also depends on the <code>Pub</code> package, which in turn depends on the public <code>Priv</code> and the same <code>Zebra</code> package that the private <code>Priv</code> package depends on.</li></ul><p>This dependency graph represented as a dictionary, looks like this:</p><pre><code class="language-julia hljs">graph = Dict(
    # Priv – the private one:
    UUID(&quot;ba13f791-ae1d-465a-978b-69c3ad90f72b&quot;) =&gt; Dict(
        :Pub   =&gt; UUID(&quot;c07ecb7d-0dc9-4db7-8803-fadaaeaf08e1&quot;),
        :Zebra =&gt; UUID(&quot;f7a24cb4-21fc-4002-ac70-f0e3a0dd3f62&quot;),
    ),
    # Priv – the public one:
    UUID(&quot;2d15fe94-a1f7-436c-a4d8-07a9a496e01c&quot;) =&gt; Dict(),
    # Pub:
    UUID(&quot;c07ecb7d-0dc9-4db7-8803-fadaaeaf08e1&quot;) =&gt; Dict(
        :Priv  =&gt; UUID(&quot;2d15fe94-a1f7-436c-a4d8-07a9a496e01c&quot;),
        :Zebra =&gt; UUID(&quot;f7a24cb4-21fc-4002-ac70-f0e3a0dd3f62&quot;),
    ),
    # Zebra:
    UUID(&quot;f7a24cb4-21fc-4002-ac70-f0e3a0dd3f62&quot;) =&gt; Dict(),
)</code></pre><p>Given this dependency <code>graph</code>, when Julia sees <code>import Priv</code> in the <code>Pub</code> package—which has UUID <code>c07ecb7d-0dc9-4db7-8803-fadaaeaf08e1</code>—it looks up:</p><pre><code class="language-julia hljs">graph[UUID(&quot;c07ecb7d-0dc9-4db7-8803-fadaaeaf08e1&quot;)][:Priv]</code></pre><p>and gets <code>2d15fe94-a1f7-436c-a4d8-07a9a496e01c</code>, which indicates that in the context of the <code>Pub</code> package, <code>import Priv</code> refers to the public <code>Priv</code> package, rather than the private one which the app depends on directly. This is how the name <code>Priv</code> can refer to different packages in the main project than it does in one of its package&#39;s dependencies, which allows for duplicate names in the package ecosystem.</p><p>What happens if <code>import Zebra</code> is evaluated in the main <code>App</code> code base? Since <code>Zebra</code> does not appear in the project file, the import will fail even though <code>Zebra</code> <em>does</em> appear in the manifest file. Moreover, if <code>import Zebra</code> occurs in the public <code>Priv</code> package—the one with UUID <code>2d15fe94-a1f7-436c-a4d8-07a9a496e01c</code>—then that would also fail since that <code>Priv</code> package has no declared dependencies in the manifest file and therefore cannot load any packages. The <code>Zebra</code> package can only be loaded by packages for which it appear as an explicit dependency in the manifest file: the  <code>Pub</code> package and one of the <code>Priv</code> packages.</p><p><strong>The paths map</strong> of a project environment is extracted from the manifest file. The path of a package <code>uuid</code> named <code>X</code> is determined by these rules (in order):</p><ol><li>If the project file in the directory matches <code>uuid</code> and name <code>X</code>, then either:<ul><li>It has a toplevel <code>path</code> entry, then <code>uuid</code> will be mapped to that path, interpreted relative to the directory containing the project file.</li><li>Otherwise, <code>uuid</code> is mapped to  <code>src/X.jl</code> relative to the directory containing the project file.</li></ul></li><li>If the above is not the case and the project file has a corresponding manifest file and the manifest contains a stanza matching <code>uuid</code> then:<ul><li>If it has a <code>path</code> entry, use that path (relative to the directory containing the manifest file).</li><li>If it has a <code>git-tree-sha1</code> entry, compute a deterministic hash function of <code>uuid</code> and <code>git-tree-sha1</code>—call it <code>slug</code>—and look for a directory named <code>packages/X/$slug</code> in each directory in the Julia <code>DEPOT_PATH</code> global array. Use the first such directory that exists.</li></ul></li></ol><p>If any of these result in success, the path to the source code entry point will be either that result, the relative path from that result plus <code>src/X.jl</code>; otherwise, there is no path mapping for <code>uuid</code>. When loading <code>X</code>, if no source code path is found, the lookup will fail, and the user may be prompted to install the appropriate package version or to take other corrective action (e.g. declaring <code>X</code> as a dependency).</p><p>In the example manifest file above, to find the path of the first <code>Priv</code> package—the one with UUID <code>ba13f791-ae1d-465a-978b-69c3ad90f72b</code>—Julia looks for its stanza in the manifest file, sees that it has a <code>path</code> entry, looks at <code>deps/Priv</code> relative to the <code>App</code> project directory—let&#39;s suppose the <code>App</code> code lives in <code>/home/<USER>/projects/App</code>—sees that <code>/home/<USER>/projects/App/deps/Priv</code> exists and therefore loads <code>Priv</code> from there.</p><p>If, on the other hand, Julia was loading the <em>other</em> <code>Priv</code> package—the one with UUID <code>2d15fe94-a1f7-436c-a4d8-07a9a496e01c</code>—it finds its stanza in the manifest, see that it does <em>not</em> have a <code>path</code> entry, but that it does have a <code>git-tree-sha1</code> entry. It then computes the <code>slug</code> for this UUID/SHA-1 pair, which is <code>HDkrT</code> (the exact details of this computation aren&#39;t important, but it is consistent and deterministic). This means that the path to this <code>Priv</code> package will be <code>packages/Priv/HDkrT/src/Priv.jl</code> in one of the package depots. Suppose the contents of <code>DEPOT_PATH</code> is <code>[&quot;/home/<USER>/.julia&quot;, &quot;/usr/local/julia&quot;]</code>, then Julia will look at the following paths to see if they exist:</p><ol><li><code>/home/<USER>/.julia/packages/Priv/HDkrT</code></li><li><code>/usr/local/julia/packages/Priv/HDkrT</code></li></ol><p>Julia uses the first of these that exists to try to load the public <code>Priv</code> package from the file <code>packages/Priv/HDKrT/src/Priv.jl</code> in the depot where it was found.</p><p>Here is a representation of a possible paths map for our example <code>App</code> project environment, as provided in the Manifest given above for the dependency graph, after searching the local file system:</p><pre><code class="language-julia hljs">paths = Dict(
    # Priv – the private one:
    (UUID(&quot;ba13f791-ae1d-465a-978b-69c3ad90f72b&quot;), :Priv) =&gt;
        # relative entry-point inside `App` repo:
        &quot;/home/<USER>/projects/App/deps/Priv/src/Priv.jl&quot;,
    # Priv – the public one:
    (UUID(&quot;2d15fe94-a1f7-436c-a4d8-07a9a496e01c&quot;), :Priv) =&gt;
        # package installed in the system depot:
        &quot;/usr/local/julia/packages/Priv/HDkr/src/Priv.jl&quot;,
    # Pub:
    (UUID(&quot;c07ecb7d-0dc9-4db7-8803-fadaaeaf08e1&quot;), :Pub) =&gt;
        # package installed in the user depot:
        &quot;/home/<USER>/.julia/packages/Pub/oKpw/src/Pub.jl&quot;,
    # Zebra:
    (UUID(&quot;f7a24cb4-21fc-4002-ac70-f0e3a0dd3f62&quot;), :Zebra) =&gt;
        # package installed in the system depot:
        &quot;/usr/local/julia/packages/Zebra/me9k/src/Zebra.jl&quot;,
)</code></pre><p>This example map includes three different kinds of package locations (the first and third are part of the default load path):</p><ol><li>The private <code>Priv</code> package is &quot;<a href="https://stackoverflow.com/a/35109534">vendored</a>&quot; inside the <code>App</code> repository.</li><li>The public <code>Priv</code> and <code>Zebra</code> packages are in the system depot, where packages installed and managed by the system administrator live. These are available to all users on the system.</li><li>The <code>Pub</code> package is in the user depot, where packages installed by the user live. These are only available to the user who installed them.</li></ol><h3 id="Package-directories"><a class="docs-heading-anchor" href="#Package-directories">Package directories</a><a id="Package-directories-1"></a><a class="docs-heading-anchor-permalink" href="#Package-directories" title="Permalink"></a></h3><p>Package directories provide a simpler kind of environment without the ability to handle name collisions. In a package directory, the set of top-level packages is the set of subdirectories that &quot;look like&quot; packages. A package <code>X</code> exists in a package directory if the directory contains one of the following &quot;entry point&quot; files:</p><ul><li><code>X.jl</code></li><li><code>X/src/X.jl</code></li><li><code>X.jl/src/X.jl</code></li></ul><p>Which dependencies a package in a package directory can import depends on whether the package contains a project file:</p><ul><li>If it has a project file, it can only import those packages which are identified in the <code>[deps]</code> section of the project file.</li><li>If it does not have a project file, it can import any top-level package—i.e. the same packages that can be loaded in <code>Main</code> or the REPL.</li></ul><p><strong>The roots map</strong> is determined by examining the contents of the package directory to generate a list of all packages that exist. Additionally, a UUID will be assigned to each entry as follows: For a given package found inside the folder <code>X</code>...</p><ol><li>If <code>X/Project.toml</code> exists and has a <code>uuid</code> entry, then <code>uuid</code> is that value.</li><li>If <code>X/Project.toml</code> exists and but does <em>not</em> have a top-level UUID entry, <code>uuid</code> is a dummy UUID generated by hashing the canonical (real) path to <code>X/Project.toml</code>.</li><li>Otherwise (if <code>Project.toml</code> does not exist), then <code>uuid</code> is the all-zero <a href="https://en.wikipedia.org/wiki/Universally_unique_identifier#Nil_UUID">nil UUID</a>.</li></ol><p><strong>The dependency graph</strong> of a project directory is determined by the presence and contents of project files in the subdirectory of each package. The rules are:</p><ul><li>If a package subdirectory has no project file, then it is omitted from graph and import statements in its code are treated as top-level, the same as the main project and REPL.</li><li>If a package subdirectory has a project file, then the graph entry for its UUID is the <code>[deps]</code> map of the project file, which is considered to be empty if the section is absent.</li></ul><p>As an example, suppose a package directory has the following structure and content:</p><pre><code class="nohighlight hljs">Aardvark/
    src/Aardvark.jl:
        import Bobcat
        import Cobra

Bobcat/
    Project.toml:
        [deps]
        Cobra = &quot;4725e24d-f727-424b-bca0-c4307a3456fa&quot;
        Dingo = &quot;7a7925be-828c-4418-bbeb-bac8dfc843bc&quot;

    src/Bobcat.jl:
        import Cobra
        import Dingo

Cobra/
    Project.toml:
        uuid = &quot;4725e24d-f727-424b-bca0-c4307a3456fa&quot;
        [deps]
        Dingo = &quot;7a7925be-828c-4418-bbeb-bac8dfc843bc&quot;

    src/Cobra.jl:
        import Dingo

Dingo/
    Project.toml:
        uuid = &quot;7a7925be-828c-4418-bbeb-bac8dfc843bc&quot;

    src/Dingo.jl:
        # no imports</code></pre><p>Here is a corresponding roots structure, represented as a dictionary:</p><pre><code class="language-julia hljs">roots = Dict(
    :Aardvark =&gt; UUID(&quot;00000000-0000-0000-0000-000000000000&quot;), # no project file, nil UUID
    :Bobcat   =&gt; UUID(&quot;85ad11c7-31f6-5d08-84db-0a4914d4cadf&quot;), # dummy UUID based on path
    :Cobra    =&gt; UUID(&quot;4725e24d-f727-424b-bca0-c4307a3456fa&quot;), # UUID from project file
    :Dingo    =&gt; UUID(&quot;7a7925be-828c-4418-bbeb-bac8dfc843bc&quot;), # UUID from project file
)</code></pre><p>Here is the corresponding graph structure, represented as a dictionary:</p><pre><code class="language-julia hljs">graph = Dict(
    # Bobcat:
    UUID(&quot;85ad11c7-31f6-5d08-84db-0a4914d4cadf&quot;) =&gt; Dict(
        :Cobra =&gt; UUID(&quot;4725e24d-f727-424b-bca0-c4307a3456fa&quot;),
        :Dingo =&gt; UUID(&quot;7a7925be-828c-4418-bbeb-bac8dfc843bc&quot;),
    ),
    # Cobra:
    UUID(&quot;4725e24d-f727-424b-bca0-c4307a3456fa&quot;) =&gt; Dict(
        :Dingo =&gt; UUID(&quot;7a7925be-828c-4418-bbeb-bac8dfc843bc&quot;),
    ),
    # Dingo:
    UUID(&quot;7a7925be-828c-4418-bbeb-bac8dfc843bc&quot;) =&gt; Dict(),
)</code></pre><p>A few general rules to note:</p><ol><li>A package without a project file can depend on any top-level dependency, and since every package in a package directory is available at the top-level, it can import all packages in the environment.</li><li>A package with a project file cannot depend on one without a project file since packages with project files can only load packages in <code>graph</code> and packages without project files do not appear in <code>graph</code>.</li><li>A package with a project file but no explicit UUID can only be depended on by packages without project files since dummy UUIDs assigned to these packages are strictly internal.</li></ol><p>Observe the following specific instances of these rules in our example:</p><ul><li><code>Aardvark</code> can import on any of <code>Bobcat</code>, <code>Cobra</code> or <code>Dingo</code>; it does import <code>Bobcat</code> and <code>Cobra</code>.</li><li><code>Bobcat</code> can and does import both <code>Cobra</code> and <code>Dingo</code>, which both have project files with UUIDs and are declared as dependencies in <code>Bobcat</code>&#39;s <code>[deps]</code> section.</li><li><code>Bobcat</code> cannot depend on <code>Aardvark</code> since <code>Aardvark</code> does not have a project file.</li><li><code>Cobra</code> can and does import <code>Dingo</code>, which has a project file and UUID, and is declared as a dependency in <code>Cobra</code>&#39;s  <code>[deps]</code> section.</li><li><code>Cobra</code> cannot depend on <code>Aardvark</code> or <code>Bobcat</code> since neither have real UUIDs.</li><li><code>Dingo</code> cannot import anything because it has a project file without a <code>[deps]</code> section.</li></ul><p><strong>The paths map</strong> in a package directory is simple: it maps subdirectory names to their corresponding entry-point paths. In other words, if the path to our example project directory is <code>/home/<USER>/animals</code> then the <code>paths</code> map could be represented by this dictionary:</p><pre><code class="language-julia hljs">paths = Dict(
    (UUID(&quot;00000000-0000-0000-0000-000000000000&quot;), :Aardvark) =&gt;
        &quot;/home/<USER>/AnimalPackages/Aardvark/src/Aardvark.jl&quot;,
    (UUID(&quot;85ad11c7-31f6-5d08-84db-0a4914d4cadf&quot;), :Bobcat) =&gt;
        &quot;/home/<USER>/AnimalPackages/Bobcat/src/Bobcat.jl&quot;,
    (UUID(&quot;4725e24d-f727-424b-bca0-c4307a3456fa&quot;), :Cobra) =&gt;
        &quot;/home/<USER>/AnimalPackages/Cobra/src/Cobra.jl&quot;,
    (UUID(&quot;7a7925be-828c-4418-bbeb-bac8dfc843bc&quot;), :Dingo) =&gt;
        &quot;/home/<USER>/AnimalPackages/Dingo/src/Dingo.jl&quot;,
)</code></pre><p>Since all packages in a package directory environment are, by definition, subdirectories with the expected entry-point files, their <code>paths</code> map entries always have this form.</p><h3 id="Environment-stacks"><a class="docs-heading-anchor" href="#Environment-stacks">Environment stacks</a><a id="Environment-stacks-1"></a><a class="docs-heading-anchor-permalink" href="#Environment-stacks" title="Permalink"></a></h3><p>The third and final kind of environment is one that combines other environments by overlaying several of them, making the packages in each available in a single composite environment. These composite environments are called <em>environment stacks</em>. The Julia <code>LOAD_PATH</code> global defines an environment stack—the environment in which the Julia process operates. If you want your Julia process to have access only to the packages in one project or package directory, make it the only entry in <code>LOAD_PATH</code>. It is often quite useful, however, to have access to some of your favorite tools—standard libraries, profilers, debuggers, personal utilities, etc.—even if they are not dependencies of the project you&#39;re working on. By adding an environment containing these tools to the load path, you immediately have access to them in top-level code without needing to add them to your project.</p><p>The mechanism for combining the roots, graph and paths data structures of the components of an environment stack is simple: they are merged as dictionaries, favoring earlier entries over later ones in the case of key collisions. In other words, if we have <code>stack = [env₁, env₂, …]</code> then we have:</p><pre><code class="language-julia hljs">roots = reduce(merge, reverse([roots₁, roots₂, …]))
graph = reduce(merge, reverse([graph₁, graph₂, …]))
paths = reduce(merge, reverse([paths₁, paths₂, …]))</code></pre><p>The subscripted <code>rootsᵢ</code>, <code>graphᵢ</code> and <code>pathsᵢ</code> variables correspond to the subscripted environments, <code>envᵢ</code>, contained in <code>stack</code>. The <code>reverse</code> is present because <code>merge</code> favors the last argument rather than first when there are collisions between keys in its argument dictionaries. There are a couple of noteworthy features of this design:</p><ol><li>The <em>primary environment</em>—i.e. the first environment in a stack—is faithfully embedded in a stacked environment. The full dependency graph of the first environment in a stack is guaranteed to be included intact in the stacked environment including the same versions of all dependencies.</li><li>Packages in non-primary environments can end up using incompatible versions of their dependencies even if their own environments are entirely compatible. This can happen when one of their dependencies is shadowed by a version in an earlier environment in the stack (either by graph or path, or both).</li></ol><p>Since the primary environment is typically the environment of a project you&#39;re working on, while environments later in the stack contain additional tools, this is the right trade-off: it&#39;s better to break your development tools but keep the project working. When such incompatibilities occur, you&#39;ll typically want to upgrade your dev tools to versions that are compatible with the main project.</p><h3 id="man-extensions"><a class="docs-heading-anchor" href="#man-extensions">Package Extensions</a><a id="man-extensions-1"></a><a class="docs-heading-anchor-permalink" href="#man-extensions" title="Permalink"></a></h3><p>A package &quot;extension&quot; is a module that is automatically loaded when a specified set of other packages (its &quot;triggers&quot;) are loaded in the current Julia session. Extensions are defined under the <code>[extensions]</code> section in the project file. The triggers of an extension are a subset of those packages listed under the <code>[weakdeps]</code> (and possibly, but uncommonly the <code>[deps]</code>) section of the project file. Those packages can have compat entries like other packages.</p><pre><code class="language-toml hljs">name = &quot;MyPackage&quot;

[compat]
ExtDep = &quot;1.0&quot;
OtherExtDep = &quot;1.0&quot;

[weakdeps]
ExtDep = &quot;c9a23...&quot; # uuid
OtherExtDep = &quot;862e...&quot; # uuid

[extensions]
BarExt = [&quot;ExtDep&quot;, &quot;OtherExtDep&quot;]
FooExt = &quot;ExtDep&quot;
...</code></pre><p>The keys under <code>extensions</code> are the names of the extensions. They are loaded when all the packages on the right hand side (the triggers) of that extension are loaded. If an extension only has one trigger the list of triggers can be written as just a string for brevity. The location for the entry point of the extension is either in <code>ext/FooExt.jl</code> or <code>ext/FooExt/FooExt.jl</code> for extension <code>FooExt</code>. The content of an extension is often structured as:</p><pre><code class="nohighlight hljs">module FooExt

# Load main package and triggers
using MyPackage, ExtDep

# Extend functionality in main package with types from the triggers
MyPackage.func(x::ExtDep.SomeStruct) = ...

end</code></pre><p>When a package with extensions is added to an environment, the <code>weakdeps</code> and <code>extensions</code> sections are stored in the manifest file in the section for that package. The dependency lookup rules for a package are the same as for its &quot;parent&quot; except that the listed triggers are also considered as dependencies.</p><h3 id="preferences"><a class="docs-heading-anchor" href="#preferences">Package/Environment Preferences</a><a id="preferences-1"></a><a class="docs-heading-anchor-permalink" href="#preferences" title="Permalink"></a></h3><p>Preferences are dictionaries of metadata that influence package behavior within an environment. The preferences system supports reading preferences at compile-time, which means that at code-loading time, we must ensure that the precompilation files selected by Julia were built with the same preferences as the current environment before loading them. The public API for modifying Preferences is contained within the <a href="https://github.com/JuliaPackaging/Preferences.jl">Preferences.jl</a> package. Preferences are stored as TOML dictionaries within a <code>(Julia)LocalPreferences.toml</code> file next to the currently-active project. If a preference is &quot;exported&quot;, it is instead stored within the <code>(Julia)Project.toml</code> instead. The intention is to allow shared projects to contain shared preferences, while allowing for users themselves to override those preferences with their own settings in the LocalPreferences.toml file, which should be .gitignored as the name implies.</p><p>Preferences that are accessed during compilation are automatically marked as compile-time preferences, and any change recorded to these preferences will cause the Julia compiler to recompile any cached precompilation file(s) (<code>.ji</code> and corresponding <code>.so</code>, <code>.dll</code>, or <code>.dylib</code> files) for that module. This is done by serializing the hash of all compile-time preferences during compilation, then checking that hash against the current environment when searching for the proper file(s) to load.</p><p>Preferences can be set with depot-wide defaults; if package Foo is installed within your global environment and it has preferences set, these preferences will apply as long as your global environment is part of your <code>LOAD_PATH</code>. Preferences in environments higher up in the environment stack get overridden by the more proximal entries in the load path, ending with the currently active project. This allows depot-wide preference defaults to exist, with active projects able to merge or even completely overwrite these inherited preferences. See the docstring for <code>Preferences.set_preferences!()</code> for the full details of how to set preferences to allow or disallow merging.</p><h2 id="Conclusion"><a class="docs-heading-anchor" href="#Conclusion">Conclusion</a><a id="Conclusion-1"></a><a class="docs-heading-anchor-permalink" href="#Conclusion" title="Permalink"></a></h2><p>Federated package management and precise software reproducibility are difficult but worthy goals in a package system. In combination, these goals lead to a more complex package loading mechanism than most dynamic languages have, but it also yields scalability and reproducibility that is more commonly associated with static languages. Typically, Julia users should be able to use the built-in package manager to manage their projects without needing a precise understanding of these interactions. A call to <code>Pkg.add(&quot;X&quot;)</code> will add to the appropriate project and manifest files, selected via <code>Pkg.activate(&quot;Y&quot;)</code>, so that a future call to <code>import X</code> will load <code>X</code> without further thought.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="embedding.html">« Embedding Julia</a><a class="docs-footer-nextpage" href="profile.html">Profiling »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
