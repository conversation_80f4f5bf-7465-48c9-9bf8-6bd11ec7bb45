<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>gdb debugging tips · The Julia Language</title><meta name="title" content="gdb debugging tips · The Julia Language"/><meta property="og:title" content="gdb debugging tips · The Julia Language"/><meta property="twitter:title" content="gdb debugging tips · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="ast.html">Julia ASTs</a></li><li><a class="tocitem" href="types.html">More about types</a></li><li><a class="tocitem" href="object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="functions.html">Julia Functions</a></li><li><a class="tocitem" href="cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="subarrays.html">SubArrays</a></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox" checked/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li class="is-active"><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a><ul class="internal"><li><a class="tocitem" href="#Displaying-Julia-variables"><span>Displaying Julia variables</span></a></li><li><a class="tocitem" href="#Useful-Julia-variables-for-Inspecting"><span>Useful Julia variables for Inspecting</span></a></li><li><a class="tocitem" href="#Useful-Julia-functions-for-Inspecting-those-variables"><span>Useful Julia functions for Inspecting those variables</span></a></li><li><a class="tocitem" href="#Inserting-breakpoints-for-inspection-from-gdb"><span>Inserting breakpoints for inspection from gdb</span></a></li><li><a class="tocitem" href="#Inserting-breakpoints-upon-certain-conditions"><span>Inserting breakpoints upon certain conditions</span></a></li><li><a class="tocitem" href="#Dealing-with-signals"><span>Dealing with signals</span></a></li><li><a class="tocitem" href="#Debugging-during-Julia&#39;s-build-process-(bootstrap)"><span>Debugging during Julia&#39;s build process (bootstrap)</span></a></li><li><a class="tocitem" href="#Debugging-precompilation-errors"><span>Debugging precompilation errors</span></a></li><li><a class="tocitem" href="#Mozilla&#39;s-Record-and-Replay-Framework-(rr)"><span>Mozilla&#39;s Record and Replay Framework (rr)</span></a></li></ul></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Developing/debugging Julia&#39;s C code</a></li><li class="is-active"><a href="debuggingtips.html">gdb debugging tips</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="debuggingtips.html">gdb debugging tips</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/debuggingtips.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="gdb-debugging-tips"><a class="docs-heading-anchor" href="#gdb-debugging-tips">gdb debugging tips</a><a id="gdb-debugging-tips-1"></a><a class="docs-heading-anchor-permalink" href="#gdb-debugging-tips" title="Permalink"></a></h1><h2 id="Displaying-Julia-variables"><a class="docs-heading-anchor" href="#Displaying-Julia-variables">Displaying Julia variables</a><a id="Displaying-Julia-variables-1"></a><a class="docs-heading-anchor-permalink" href="#Displaying-Julia-variables" title="Permalink"></a></h2><p>Within <code>gdb</code>, any <code>jl_value_t*</code> object <code>obj</code> can be displayed using</p><pre><code class="nohighlight hljs">(gdb) call jl_(obj)</code></pre><p>The object will be displayed in the <code>julia</code> session, not in the gdb session. This is a useful way to discover the types and values of objects being manipulated by Julia&#39;s C code.</p><p>Similarly, if you&#39;re debugging some of Julia&#39;s internals (e.g., <code>compiler.jl</code>), you can print <code>obj</code> using</p><pre><code class="language-julia hljs">ccall(:jl_, Cvoid, (Any,), obj)</code></pre><p>This is a good way to circumvent problems that arise from the order in which julia&#39;s output streams are initialized.</p><p>Julia&#39;s flisp interpreter uses <code>value_t</code> objects; these can be displayed with <code>call fl_print(fl_ctx, ios_stdout, obj)</code>.</p><h2 id="Useful-Julia-variables-for-Inspecting"><a class="docs-heading-anchor" href="#Useful-Julia-variables-for-Inspecting">Useful Julia variables for Inspecting</a><a id="Useful-Julia-variables-for-Inspecting-1"></a><a class="docs-heading-anchor-permalink" href="#Useful-Julia-variables-for-Inspecting" title="Permalink"></a></h2><p>While the addresses of many variables, like singletons, can be useful to print for many failures, there are a number of additional variables (see <code>julia.h</code> for a complete list) that are even more useful.</p><ul><li>(when in <code>jl_apply_generic</code>) <code>mfunc</code> and <code>jl_uncompress_ast(mfunc-&gt;def, mfunc-&gt;code)</code> :: for figuring out a bit about the call-stack</li><li><code>jl_lineno</code> and <code>jl_filename</code> :: for figuring out what line in a test to go start debugging from (or figure out how far into a file has been parsed)</li><li><code>$1</code> :: not really a variable, but still a useful shorthand for referring to the result of the last gdb command (such as <code>print</code>)</li><li><code>jl_options</code> :: sometimes useful, since it lists all of the command line options that were successfully parsed</li><li><code>jl_uv_stderr</code> :: because who doesn&#39;t like to be able to interact with stdio</li></ul><h2 id="Useful-Julia-functions-for-Inspecting-those-variables"><a class="docs-heading-anchor" href="#Useful-Julia-functions-for-Inspecting-those-variables">Useful Julia functions for Inspecting those variables</a><a id="Useful-Julia-functions-for-Inspecting-those-variables-1"></a><a class="docs-heading-anchor-permalink" href="#Useful-Julia-functions-for-Inspecting-those-variables" title="Permalink"></a></h2><ul><li><code>jl_print_task_backtraces(0)</code> :: Similar to gdb&#39;s <code>thread apply all bt</code> or lldb&#39;s <code>thread backtrace all</code>. Runs all threads while printing backtraces for all existing tasks.</li><li><code>jl_gdblookup($pc)</code> :: For looking up the current function and line.</li><li><code>jl_gdblookupinfo($pc)</code> :: For looking up the current method instance object.</li><li><code>jl_gdbdumpcode(mi)</code> :: For dumping all of <code>code_typed/code_llvm/code_asm</code> when the REPL is not working right.</li><li><code>jlbacktrace()</code> :: For dumping the current Julia backtrace stack to stderr. Only usable after <code>record_backtrace()</code> has been called.</li><li><code>jl_dump_llvm_value(Value*)</code> :: For invoking <code>Value-&gt;dump()</code> in gdb, where it doesn&#39;t work natively. For example, <code>f-&gt;linfo-&gt;functionObject</code>, <code>f-&gt;linfo-&gt;specFunctionObject</code>, and <code>to_function(f-&gt;linfo)</code>.</li><li><code>jl_dump_llvm_module(Module*)</code> :: For invoking <code>Module-&gt;dump()</code> in gdb, where it doesn&#39;t work natively.</li><li><code>Type-&gt;dump()</code> :: only works in lldb. Note: add something like <code>;1</code> to prevent lldb from printing its prompt over the output</li><li><code>jl_eval_string(&quot;expr&quot;)</code> :: for invoking side-effects to modify the current state or to lookup symbols</li><li><code>jl_typeof(jl_value_t*)</code> :: for extracting the type tag of a Julia value (in gdb, call <code>macro define jl_typeof jl_typeof</code> first, or pick something short like <code>ty</code> for the first arg to define a shorthand)</li></ul><h2 id="Inserting-breakpoints-for-inspection-from-gdb"><a class="docs-heading-anchor" href="#Inserting-breakpoints-for-inspection-from-gdb">Inserting breakpoints for inspection from gdb</a><a id="Inserting-breakpoints-for-inspection-from-gdb-1"></a><a class="docs-heading-anchor-permalink" href="#Inserting-breakpoints-for-inspection-from-gdb" title="Permalink"></a></h2><p>In your <code>gdb</code> session, set a breakpoint in <code>jl_breakpoint</code> like so:</p><pre><code class="nohighlight hljs">(gdb) break jl_breakpoint</code></pre><p>Then within your Julia code, insert a call to <code>jl_breakpoint</code> by adding</p><pre><code class="language-julia hljs">ccall(:jl_breakpoint, Cvoid, (Any,), obj)</code></pre><p>where <code>obj</code> can be any variable or tuple you want to be accessible in the breakpoint.</p><p>It&#39;s particularly helpful to back up to the <code>jl_apply</code> frame, from which you can display the arguments to a function using, e.g.,</p><pre><code class="nohighlight hljs">(gdb) call jl_(args[0])</code></pre><p>Another useful frame is <code>to_function(jl_method_instance_t *li, bool cstyle)</code>. The <code>jl_method_instance_t*</code> argument is a struct with a reference to the final AST sent into the compiler. However, the AST at this point will usually be compressed; to view the AST, call <code>jl_uncompress_ast</code> and then pass the result to <code>jl_</code>:</p><pre><code class="nohighlight hljs">#2  0x00007ffff7928bf7 in to_function (li=0x2812060, cstyle=false) at codegen.cpp:584
584          abort();
(gdb) p jl_(jl_uncompress_ast(li, li-&gt;ast))</code></pre><h2 id="Inserting-breakpoints-upon-certain-conditions"><a class="docs-heading-anchor" href="#Inserting-breakpoints-upon-certain-conditions">Inserting breakpoints upon certain conditions</a><a id="Inserting-breakpoints-upon-certain-conditions-1"></a><a class="docs-heading-anchor-permalink" href="#Inserting-breakpoints-upon-certain-conditions" title="Permalink"></a></h2><h3 id="Loading-a-particular-file"><a class="docs-heading-anchor" href="#Loading-a-particular-file">Loading a particular file</a><a id="Loading-a-particular-file-1"></a><a class="docs-heading-anchor-permalink" href="#Loading-a-particular-file" title="Permalink"></a></h3><p>Let&#39;s say the file is <code>sysimg.jl</code>:</p><pre><code class="nohighlight hljs">(gdb) break jl_load if strcmp(fname, &quot;sysimg.jl&quot;)==0</code></pre><h3 id="Calling-a-particular-method"><a class="docs-heading-anchor" href="#Calling-a-particular-method">Calling a particular method</a><a id="Calling-a-particular-method-1"></a><a class="docs-heading-anchor-permalink" href="#Calling-a-particular-method" title="Permalink"></a></h3><pre><code class="nohighlight hljs">(gdb) break jl_apply_generic if strcmp((char*)(jl_symbol_name)(jl_gf_mtable(F)-&gt;name), &quot;method_to_break&quot;)==0</code></pre><p>Since this function is used for every call, you will make everything 1000x slower if you do this.</p><h2 id="Dealing-with-signals"><a class="docs-heading-anchor" href="#Dealing-with-signals">Dealing with signals</a><a id="Dealing-with-signals-1"></a><a class="docs-heading-anchor-permalink" href="#Dealing-with-signals" title="Permalink"></a></h2><p>Julia requires a few signals to function properly. The profiler uses <code>SIGUSR2</code> for sampling and the garbage collector uses <code>SIGSEGV</code> for threads synchronization. If you are debugging some code that uses the profiler or multiple threads, you may want to let the debugger ignore these signals since they can be triggered very often during normal operations. The command to do this in GDB is (replace <code>SIGSEGV</code> with <code>SIGUSR2</code> or other signals you want to ignore):</p><pre><code class="nohighlight hljs">(gdb) handle SIGSEGV noprint nostop pass</code></pre><p>The corresponding LLDB command is (after the process is started):</p><pre><code class="nohighlight hljs">(lldb) pro hand -p true -s false -n false SIGSEGV</code></pre><p>If you are debugging a segfault with threaded code, you can set a breakpoint on <code>jl_critical_error</code> (<code>sigdie_handler</code> should also work on Linux and BSD) in order to only catch the actual segfault rather than the GC synchronization points.</p><h2 id="Debugging-during-Julia&#39;s-build-process-(bootstrap)"><a class="docs-heading-anchor" href="#Debugging-during-Julia&#39;s-build-process-(bootstrap)">Debugging during Julia&#39;s build process (bootstrap)</a><a id="Debugging-during-Julia&#39;s-build-process-(bootstrap)-1"></a><a class="docs-heading-anchor-permalink" href="#Debugging-during-Julia&#39;s-build-process-(bootstrap)" title="Permalink"></a></h2><p>Errors that occur during <code>make</code> need special handling. Julia is built in two stages, constructing <code>sys0</code> and <code>sys.ji</code>. To see what commands are running at the time of failure, use <code>make VERBOSE=1</code>.</p><p>At the time of this writing, you can debug build errors during the <code>sys0</code> phase from the <code>base</code> directory using:</p><pre><code class="nohighlight hljs">julia/base$ gdb --args ../usr/bin/julia-debug -C native --build ../usr/lib/julia/sys0 sysimg.jl</code></pre><p>You might need to delete all the files in <code>usr/lib/julia/</code> to get this to work.</p><p>You can debug the <code>sys.ji</code> phase using:</p><pre><code class="nohighlight hljs">julia/base$ gdb --args ../usr/bin/julia-debug -C native --build ../usr/lib/julia/sys -J ../usr/lib/julia/sys0.ji sysimg.jl</code></pre><p>By default, any errors will cause Julia to exit, even under gdb. To catch an error &quot;in the act&quot;, set a breakpoint in <code>jl_error</code> (there are several other useful spots, for specific kinds of failures, including: <code>jl_too_few_args</code>, <code>jl_too_many_args</code>, and <code>jl_throw</code>).</p><p>Once an error is caught, a useful technique is to walk up the stack and examine the function by inspecting the related call to <code>jl_apply</code>. To take a real-world example:</p><pre><code class="nohighlight hljs">Breakpoint 1, jl_throw (e=0x7ffdf42de400) at task.c:802
802 {
(gdb) p jl_(e)
ErrorException(&quot;auto_unbox: unable to determine argument type&quot;)
$2 = void
(gdb) bt 10
#0  jl_throw (e=0x7ffdf42de400) at task.c:802
#1  0x00007ffff65412fe in jl_error (str=0x7ffde56be000 &lt;_j_str267&gt; &quot;auto_unbox:
   unable to determine argument type&quot;)
   at builtins.c:39
#2  0x00007ffde56bd01a in julia_convert_16886 ()
#3  0x00007ffff6541154 in jl_apply (f=0x7ffdf367f630, args=0x7fffffffc2b0, nargs=2) at julia.h:1281
...</code></pre><p>The most recent <code>jl_apply</code> is at frame #3, so we can go back there and look at the AST for the function <code>julia_convert_16886</code>. This is the uniqued name for some method of <code>convert</code>. <code>f</code> in this frame is a <code>jl_function_t*</code>, so we can look at the type signature, if any, from the <code>specTypes</code> field:</p><pre><code class="nohighlight hljs">(gdb) f 3
#3  0x00007ffff6541154 in jl_apply (f=0x7ffdf367f630, args=0x7fffffffc2b0, nargs=2) at julia.h:1281
1281            return f-&gt;fptr((jl_value_t*)f, args, nargs);
(gdb) p f-&gt;linfo-&gt;specTypes
$4 = (jl_tupletype_t *) 0x7ffdf39b1030
(gdb) p jl_( f-&gt;linfo-&gt;specTypes )
Tuple{Type{Float32}, Float64}           # &lt;-- type signature for julia_convert_16886</code></pre><p>Then, we can look at the AST for this function:</p><pre><code class="nohighlight hljs">(gdb) p jl_( jl_uncompress_ast(f-&gt;linfo, f-&gt;linfo-&gt;ast) )
Expr(:lambda, Array{Any, 1}[:#s29, :x], Array{Any, 1}[Array{Any, 1}[], Array{Any, 1}[Array{Any, 1}[:#s29, :Any, 0], Array{Any, 1}[:x, :Any, 0]], Array{Any, 1}[], 0], Expr(:body,
Expr(:line, 90, :float.jl)::Any,
Expr(:return, Expr(:call, :box, :Float32, Expr(:call, :fptrunc, :Float32, :x)::Any)::Any)::Any)::Any)::Any</code></pre><p>Finally, and perhaps most usefully, we can force the function to be recompiled in order to step through the codegen process. To do this, clear the cached <code>functionObject</code> from the <code>jl_lamdbda_info_t*</code>:</p><pre><code class="nohighlight hljs">(gdb) p f-&gt;linfo-&gt;functionObject
$8 = (void *) 0x1289d070
(gdb) set f-&gt;linfo-&gt;functionObject = NULL</code></pre><p>Then, set a breakpoint somewhere useful (e.g. <code>emit_function</code>, <code>emit_expr</code>, <code>emit_call</code>, etc.), and run codegen:</p><pre><code class="nohighlight hljs">(gdb) p jl_compile(f)
... # your breakpoint here</code></pre><h2 id="Debugging-precompilation-errors"><a class="docs-heading-anchor" href="#Debugging-precompilation-errors">Debugging precompilation errors</a><a id="Debugging-precompilation-errors-1"></a><a class="docs-heading-anchor-permalink" href="#Debugging-precompilation-errors" title="Permalink"></a></h2><p>Module precompilation spawns a separate Julia process to precompile each module. Setting a breakpoint or catching failures in a precompile worker requires attaching a debugger to the worker. The easiest approach is to set the debugger watch for new process launches matching a given name. For example:</p><pre><code class="nohighlight hljs">(gdb) attach -w -n julia-debug</code></pre><p>or:</p><pre><code class="nohighlight hljs">(lldb) process attach -w -n julia-debug</code></pre><p>Then run a script/command to start precompilation. As described earlier, use conditional breakpoints in the parent process to catch specific file-loading events and narrow the debugging window. (some operating systems may require alternative approaches, such as following each <code>fork</code> from the parent process)</p><h2 id="Mozilla&#39;s-Record-and-Replay-Framework-(rr)"><a class="docs-heading-anchor" href="#Mozilla&#39;s-Record-and-Replay-Framework-(rr)">Mozilla&#39;s Record and Replay Framework (rr)</a><a id="Mozilla&#39;s-Record-and-Replay-Framework-(rr)-1"></a><a class="docs-heading-anchor-permalink" href="#Mozilla&#39;s-Record-and-Replay-Framework-(rr)" title="Permalink"></a></h2><p>Julia now works out of the box with <a href="https://rr-project.org/">rr</a>, the lightweight recording and deterministic debugging framework from Mozilla. This allows you to replay the trace of an execution deterministically.  The replayed execution&#39;s address spaces, register contents, syscall data etc are exactly the same in every run.</p><p>A recent version of rr (3.1.0 or higher) is required.</p><h3 id="Reproducing-concurrency-bugs-with-rr"><a class="docs-heading-anchor" href="#Reproducing-concurrency-bugs-with-rr">Reproducing concurrency bugs with rr</a><a id="Reproducing-concurrency-bugs-with-rr-1"></a><a class="docs-heading-anchor-permalink" href="#Reproducing-concurrency-bugs-with-rr" title="Permalink"></a></h3><p>rr simulates a single-threaded machine by default. In order to debug concurrent code you can use <code>rr record --chaos</code> which will cause rr to simulate between one to eight cores, chosen randomly. You might therefore want to set <code>JULIA_NUM_THREADS=8</code> and rerun your code under rr until you have caught your bug.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="backtraces.html">« Reporting and analyzing crashes (segfaults)</a><a class="docs-footer-nextpage" href="valgrind.html">Using Valgrind with Julia »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
