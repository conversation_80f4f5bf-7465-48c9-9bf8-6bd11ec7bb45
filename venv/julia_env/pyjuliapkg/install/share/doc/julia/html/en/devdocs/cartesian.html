<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Base.Cartesian · The Julia Language</title><meta name="title" content="Base.Cartesian · The Julia Language"/><meta property="og:title" content="Base.Cartesian · The Julia Language"/><meta property="twitter:title" content="Base.Cartesian · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox" checked/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="ast.html">Julia ASTs</a></li><li><a class="tocitem" href="types.html">More about types</a></li><li><a class="tocitem" href="object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="functions.html">Julia Functions</a></li><li class="is-active"><a class="tocitem" href="cartesian.html">Base.Cartesian</a><ul class="internal"><li><a class="tocitem" href="#Principles-of-usage"><span>Principles of usage</span></a></li><li><a class="tocitem" href="#Basic-syntax"><span>Basic syntax</span></a></li></ul></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="subarrays.html">SubArrays</a></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Documentation of Julia&#39;s Internals</a></li><li class="is-active"><a href="cartesian.html">Base.Cartesian</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="cartesian.html">Base.Cartesian</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/cartesian.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Base.Cartesian"><a class="docs-heading-anchor" href="#Base.Cartesian">Base.Cartesian</a><a id="Base.Cartesian-1"></a><a class="docs-heading-anchor-permalink" href="#Base.Cartesian" title="Permalink"></a></h1><p>The (non-exported) Cartesian module provides macros that facilitate writing multidimensional algorithms. Most often you can write such algorithms with <a href="https://julialang.org/blog/2016/02/iteration">straightforward techniques</a>; however, there are a few cases where <code>Base.Cartesian</code> is still useful or necessary.</p><h2 id="Principles-of-usage"><a class="docs-heading-anchor" href="#Principles-of-usage">Principles of usage</a><a id="Principles-of-usage-1"></a><a class="docs-heading-anchor-permalink" href="#Principles-of-usage" title="Permalink"></a></h2><p>A simple example of usage is:</p><pre><code class="language-julia hljs">@nloops 3 i A begin
    s += @nref 3 A i
end</code></pre><p>which generates the following code:</p><pre><code class="language-julia hljs">for i_3 = axes(A, 3)
    for i_2 = axes(A, 2)
        for i_1 = axes(A, 1)
            s += A[i_1, i_2, i_3]
        end
    end
end</code></pre><p>In general, Cartesian allows you to write generic code that contains repetitive elements, like the nested loops in this example.  Other applications include repeated expressions (e.g., loop unwinding) or creating function calls with variable numbers of arguments without using the &quot;splat&quot; construct (<code>i...</code>).</p><h2 id="Basic-syntax"><a class="docs-heading-anchor" href="#Basic-syntax">Basic syntax</a><a id="Basic-syntax-1"></a><a class="docs-heading-anchor-permalink" href="#Basic-syntax" title="Permalink"></a></h2><p>The (basic) syntax of <code>@nloops</code> is as follows:</p><ul><li>The first argument must be an integer (<em>not</em> a variable) specifying the number of loops.</li><li>The second argument is the symbol-prefix used for the iterator variable. Here we used <code>i</code>, and variables <code>i_1, i_2, i_3</code> were generated.</li><li>The third argument specifies the range for each iterator variable. If you use a variable (symbol) here, it&#39;s taken as <code>axes(A, dim)</code>. More flexibly, you can use the anonymous-function expression syntax described below.</li><li>The last argument is the body of the loop. Here, that&#39;s what appears between the <code>begin...end</code>.</li></ul><p>There are some additional features of <code>@nloops</code> described in the <a href="cartesian.html#dev-cartesian-reference">reference section</a>.</p><p><code>@nref</code> follows a similar pattern, generating <code>A[i_1,i_2,i_3]</code> from <code>@nref 3 A i</code>. The general practice is to read from left to right, which is why <code>@nloops</code> is <code>@nloops 3 i A expr</code> (as in <code>for i_2 = axes(A, 2)</code>, where <code>i_2</code> is to the left and the range is to the right) whereas <code>@nref</code> is <code>@nref 3 A i</code> (as in <code>A[i_1,i_2,i_3]</code>, where the array comes first).</p><p>If you&#39;re developing code with Cartesian, you may find that debugging is easier when you examine the generated code, using <code>@macroexpand</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; @macroexpand @nref 2 A i
:(A[i_1, i_2])</code></pre><h3 id="Supplying-the-number-of-expressions"><a class="docs-heading-anchor" href="#Supplying-the-number-of-expressions">Supplying the number of expressions</a><a id="Supplying-the-number-of-expressions-1"></a><a class="docs-heading-anchor-permalink" href="#Supplying-the-number-of-expressions" title="Permalink"></a></h3><p>The first argument to both of these macros is the number of expressions, which must be an integer. When you&#39;re writing a function that you intend to work in multiple dimensions, this may not be something you want to hard-code. The recommended approach is to use a <code>@generated function</code>.  Here&#39;s an example:</p><pre><code class="language-julia hljs">@generated function mysum(A::Array{T,N}) where {T,N}
    quote
        s = zero(T)
        @nloops $N i A begin
            s += @nref $N A i
        end
        s
    end
end</code></pre><p>Naturally, you can also prepare expressions or perform calculations before the <code>quote</code> block.</p><h3 id="Anonymous-function-expressions-as-macro-arguments"><a class="docs-heading-anchor" href="#Anonymous-function-expressions-as-macro-arguments">Anonymous-function expressions as macro arguments</a><a id="Anonymous-function-expressions-as-macro-arguments-1"></a><a class="docs-heading-anchor-permalink" href="#Anonymous-function-expressions-as-macro-arguments" title="Permalink"></a></h3><p>Perhaps the single most powerful feature in <code>Cartesian</code> is the ability to supply anonymous-function expressions that get evaluated at parsing time.  Let&#39;s consider a simple example:</p><pre><code class="language-julia hljs">@nexprs 2 j-&gt;(i_j = 1)</code></pre><p><code>@nexprs</code> generates <code>n</code> expressions that follow a pattern. This code would generate the following statements:</p><pre><code class="language-julia hljs">i_1 = 1
i_2 = 1</code></pre><p>In each generated statement, an &quot;isolated&quot; <code>j</code> (the variable of the anonymous function) gets replaced by values in the range <code>1:2</code>. Generally speaking, Cartesian employs a LaTeX-like syntax.  This allows you to do math on the index <code>j</code>.  Here&#39;s an example computing the strides of an array:</p><pre><code class="language-julia hljs">s_1 = 1
@nexprs 3 j-&gt;(s_{j+1} = s_j * size(A, j))</code></pre><p>would generate expressions</p><pre><code class="language-julia hljs">s_1 = 1
s_2 = s_1 * size(A, 1)
s_3 = s_2 * size(A, 2)
s_4 = s_3 * size(A, 3)</code></pre><p>Anonymous-function expressions have many uses in practice.</p><h4 id="dev-cartesian-reference"><a class="docs-heading-anchor" href="#dev-cartesian-reference">Macro reference</a><a id="dev-cartesian-reference-1"></a><a class="docs-heading-anchor-permalink" href="#dev-cartesian-reference" title="Permalink"></a></h4><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cartesian.@nloops" href="#Base.Cartesian.@nloops"><code>Base.Cartesian.@nloops</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@nloops N itersym rangeexpr bodyexpr
@nloops N itersym rangeexpr preexpr bodyexpr
@nloops N itersym rangeexpr preexpr postexpr bodyexpr</code></pre><p>Generate <code>N</code> nested loops, using <code>itersym</code> as the prefix for the iteration variables. <code>rangeexpr</code> may be an anonymous-function expression, or a simple symbol <code>var</code> in which case the range is <code>axes(var, d)</code> for dimension <code>d</code>.</p><p>Optionally, you can provide &quot;pre&quot; and &quot;post&quot; expressions. These get executed first and last, respectively, in the body of each loop. For example:</p><pre><code class="nohighlight hljs">@nloops 2 i A d -&gt; j_d = min(i_d, 5) begin
    s += @nref 2 A j
end</code></pre><p>would generate:</p><pre><code class="nohighlight hljs">for i_2 = axes(A, 2)
    j_2 = min(i_2, 5)
    for i_1 = axes(A, 1)
        j_1 = min(i_1, 5)
        s += A[j_1, j_2]
    end
end</code></pre><p>If you want just a post-expression, supply <a href="../base/constants.html#Core.nothing"><code>nothing</code></a> for the pre-expression. Using parentheses and semicolons, you can supply multi-statement expressions.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/cartesian.jl#L9-L37">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cartesian.@nref" href="#Base.Cartesian.@nref"><code>Base.Cartesian.@nref</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@nref N A indexexpr</code></pre><p>Generate expressions like <code>A[i_1, i_2, ...]</code>. <code>indexexpr</code> can either be an iteration-symbol prefix, or an anonymous-function expression.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; @macroexpand Base.Cartesian.@nref 3 A i
:(A[i_1, i_2, i_3])</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/cartesian.jl#L72-L83">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cartesian.@nextract" href="#Base.Cartesian.@nextract"><code>Base.Cartesian.@nextract</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@nextract N esym isym</code></pre><p>Generate <code>N</code> variables <code>esym_1</code>, <code>esym_2</code>, ..., <code>esym_N</code> to extract values from <code>isym</code>. <code>isym</code> can be either a <code>Symbol</code> or anonymous-function expression.</p><p><code>@nextract 2 x y</code> would generate</p><pre><code class="nohighlight hljs">x_1 = y[1]
x_2 = y[2]</code></pre><p>while <code>@nextract 3 x d-&gt;y[2d-1]</code> yields</p><pre><code class="nohighlight hljs">x_1 = y[1]
x_2 = y[3]
x_3 = y[5]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/cartesian.jl#L160-L177">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cartesian.@nexprs" href="#Base.Cartesian.@nexprs"><code>Base.Cartesian.@nexprs</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@nexprs N expr</code></pre><p>Generate <code>N</code> expressions. <code>expr</code> should be an anonymous-function expression.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; @macroexpand Base.Cartesian.@nexprs 4 i -&gt; y[i] = A[i+j]
quote
    y[1] = A[1 + j]
    y[2] = A[2 + j]
    y[3] = A[3 + j]
    y[4] = A[4 + j]
end</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/cartesian.jl#L139-L154">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cartesian.@ncall" href="#Base.Cartesian.@ncall"><code>Base.Cartesian.@ncall</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@ncall N f sym...</code></pre><p>Generate a function call expression. <code>sym</code> represents any number of function arguments, the last of which may be an anonymous-function expression and is expanded into <code>N</code> arguments.</p><p>For example, <code>@ncall 3 func a</code> generates</p><pre><code class="nohighlight hljs">func(a_1, a_2, a_3)</code></pre><p>while <code>@ncall 2 func a b i-&gt;c[i]</code> yields</p><pre><code class="nohighlight hljs">func(a, b, c[1], c[2])</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/cartesian.jl#L89-L103">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cartesian.@ncallkw" href="#Base.Cartesian.@ncallkw"><code>Base.Cartesian.@ncallkw</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@ncallkw N f kw sym...</code></pre><p>Generate a function call expression with keyword arguments <code>kw...</code>. As in the case of <a href="cartesian.html#Base.Cartesian.@ncall"><code>@ncall</code></a>, <code>sym</code> represents any number of function arguments, the last of which may be an anonymous-function expression and is expanded into <code>N</code> arguments.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; using Base.Cartesian

julia&gt; f(x...; a, b = 1, c = 2, d = 3) = +(x..., a, b, c, d);

julia&gt; x_1, x_2 = (-1, -2); b = 0; kw = (c = 0, d = 0);

julia&gt; @ncallkw 2 f (; a = 0, b, kw...) x
-3
</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/cartesian.jl#L111-L130">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cartesian.@ntuple" href="#Base.Cartesian.@ntuple"><code>Base.Cartesian.@ntuple</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@ntuple N expr</code></pre><p>Generates an <code>N</code>-tuple. <code>@ntuple 2 i</code> would generate <code>(i_1, i_2)</code>, and <code>@ntuple 2 k-&gt;k+1</code> would generate <code>(2,3)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/cartesian.jl#L221-L226">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cartesian.@nall" href="#Base.Cartesian.@nall"><code>Base.Cartesian.@nall</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@nall N expr</code></pre><p>Check whether all of the expressions generated by the anonymous-function expression <code>expr</code> evaluate to <code>true</code>.</p><p><code>@nall 3 d-&gt;(i_d &gt; 1)</code> would generate the expression <code>(i_1 &gt; 1 &amp;&amp; i_2 &gt; 1 &amp;&amp; i_3 &gt; 1)</code>. This can be convenient for bounds-checking.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/cartesian.jl#L188-L196">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cartesian.@nany" href="#Base.Cartesian.@nany"><code>Base.Cartesian.@nany</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@nany N expr</code></pre><p>Check whether any of the expressions generated by the anonymous-function expression <code>expr</code> evaluate to <code>true</code>.</p><p><code>@nany 3 d-&gt;(i_d &gt; 1)</code> would generate the expression <code>(i_1 &gt; 1 || i_2 &gt; 1 || i_3 &gt; 1)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/cartesian.jl#L205-L212">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Cartesian.@nif" href="#Base.Cartesian.@nif"><code>Base.Cartesian.@nif</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@nif N conditionexpr expr
@nif N conditionexpr expr elseexpr</code></pre><p>Generates a sequence of <code>if ... elseif ... else ... end</code> statements. For example:</p><pre><code class="nohighlight hljs">@nif 3 d-&gt;(i_d &gt;= size(A,d)) d-&gt;(error(&quot;Dimension &quot;, d, &quot; too big&quot;)) d-&gt;println(&quot;All OK&quot;)</code></pre><p>would generate:</p><pre><code class="nohighlight hljs">if i_1 &gt; size(A, 1)
    error(&quot;Dimension &quot;, 1, &quot; too big&quot;)
elseif i_2 &gt; size(A, 2)
    error(&quot;Dimension &quot;, 2, &quot; too big&quot;)
else
    println(&quot;All OK&quot;)
end</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/cartesian.jl#L232-L249">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="functions.html">« Julia Functions</a><a class="docs-footer-nextpage" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism) »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
