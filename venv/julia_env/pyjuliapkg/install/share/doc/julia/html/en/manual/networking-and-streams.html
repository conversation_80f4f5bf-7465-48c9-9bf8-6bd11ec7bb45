<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Networking and Streams · The Julia Language</title><meta name="title" content="Networking and Streams · The Julia Language"/><meta property="og:title" content="Networking and Streams · The Julia Language"/><meta property="twitter:title" content="Networking and Streams · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li class="is-active"><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a><ul class="internal"><li><a class="tocitem" href="#Basic-Stream-I/O"><span>Basic Stream I/O</span></a></li><li><a class="tocitem" href="#Text-I/O"><span>Text I/O</span></a></li><li><a class="tocitem" href="#IO-Output-Contextual-Properties"><span>IO Output Contextual Properties</span></a></li><li><a class="tocitem" href="#Working-with-Files"><span>Working with Files</span></a></li><li><a class="tocitem" href="#A-simple-TCP-example"><span>A simple TCP example</span></a></li><li><a class="tocitem" href="#Resolving-IP-Addresses"><span>Resolving IP Addresses</span></a></li><li><a class="tocitem" href="#Asynchronous-I/O"><span>Asynchronous I/O</span></a></li><li><a class="tocitem" href="#Multicast"><span>Multicast</span></a></li></ul></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="networking-and-streams.html">Networking and Streams</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="networking-and-streams.html">Networking and Streams</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/networking-and-streams.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Networking-and-Streams"><a class="docs-heading-anchor" href="#Networking-and-Streams">Networking and Streams</a><a id="Networking-and-Streams-1"></a><a class="docs-heading-anchor-permalink" href="#Networking-and-Streams" title="Permalink"></a></h1><p>Julia provides a rich interface to deal with streaming I/O objects such as terminals, pipes and TCP sockets. These objects allow data to be sent and received in a stream-like fashion, which means that data is processed sequentially as it becomes available. This interface, though asynchronous at the system level, is presented in a synchronous manner to the programmer. This is achieved by making heavy use of Julia cooperative threading (<a href="control-flow.html#man-tasks">coroutine</a>) functionality.</p><h2 id="Basic-Stream-I/O"><a class="docs-heading-anchor" href="#Basic-Stream-I/O">Basic Stream I/O</a><a id="Basic-Stream-I/O-1"></a><a class="docs-heading-anchor-permalink" href="#Basic-Stream-I/O" title="Permalink"></a></h2><p>All Julia streams expose at least a <a href="../base/io-network.html#Base.read"><code>read</code></a> and a <a href="../base/io-network.html#Base.write"><code>write</code></a> method, taking the stream as their first argument, e.g.:</p><pre><code class="language-julia-repl hljs">julia&gt; write(stdout, &quot;Hello World&quot;);  # suppress return value 11 with ;
Hello World
julia&gt; read(stdin, Char)

&#39;\n&#39;: ASCII/Unicode U+000a (category Cc: Other, control)</code></pre><p>Note that <a href="../base/io-network.html#Base.write"><code>write</code></a> returns 11, the number of bytes (in <code>&quot;Hello World&quot;</code>) written to <a href="../base/io-network.html#Base.stdout"><code>stdout</code></a>, but this return value is suppressed with the <code>;</code>.</p><p>Here Enter was pressed again so that Julia would read the newline. Now, as you can see from this example, <a href="../base/io-network.html#Base.write"><code>write</code></a> takes the data to write as its second argument, while <a href="../base/io-network.html#Base.read"><code>read</code></a> takes the type of the data to be read as the second argument.</p><p>For example, to read a simple byte array, we could do:</p><pre><code class="language-julia-repl hljs">julia&gt; x = zeros(UInt8, 4)
4-element Array{UInt8,1}:
 0x00
 0x00
 0x00
 0x00

julia&gt; read!(stdin, x)
abcd
4-element Array{UInt8,1}:
 0x61
 0x62
 0x63
 0x64</code></pre><p>However, since this is slightly cumbersome, there are several convenience methods provided. For example, we could have written the above as:</p><pre><code class="language-julia-repl hljs">julia&gt; read(stdin, 4)
abcd
4-element Array{UInt8,1}:
 0x61
 0x62
 0x63
 0x64</code></pre><p>or if we had wanted to read the entire line instead:</p><pre><code class="language-julia-repl hljs">julia&gt; readline(stdin)
abcd
&quot;abcd&quot;</code></pre><p>Note that depending on your terminal settings, your TTY (&quot;teletype terminal&quot;) may be line buffered and might thus require an additional enter before <code>stdin</code> data is sent to Julia. When running Julia from the command line in a TTY, output is sent to the console by default, and standard input is read from the keyboard.</p><p>To read every line from <a href="../base/io-network.html#Base.stdin"><code>stdin</code></a> you can use <a href="../base/io-network.html#Base.eachline"><code>eachline</code></a>:</p><pre><code class="language-julia hljs">for line in eachline(stdin)
    print(&quot;Found $line&quot;)
end</code></pre><p>or <a href="../base/io-network.html#Base.read"><code>read</code></a> if you wanted to read by character instead:</p><pre><code class="language-julia hljs">while !eof(stdin)
    x = read(stdin, Char)
    println(&quot;Found: $x&quot;)
end</code></pre><h2 id="Text-I/O"><a class="docs-heading-anchor" href="#Text-I/O">Text I/O</a><a id="Text-I/O-1"></a><a class="docs-heading-anchor-permalink" href="#Text-I/O" title="Permalink"></a></h2><p>Note that the <a href="../base/io-network.html#Base.write"><code>write</code></a> method mentioned above operates on binary streams. In particular, values do not get converted to any canonical text representation but are written out as is:</p><pre><code class="language-julia-repl hljs">julia&gt; write(stdout, 0x61);  # suppress return value 1 with ;
a</code></pre><p>Note that <code>a</code> is written to <a href="../base/io-network.html#Base.stdout"><code>stdout</code></a> by the <a href="../base/io-network.html#Base.write"><code>write</code></a> function and that the returned value is <code>1</code> (since <code>0x61</code> is one byte).</p><p>For text I/O, use the <a href="../base/io-network.html#Base.print"><code>print</code></a> or <a href="../base/io-network.html#Base.show-Tuple{IO, Any}"><code>show</code></a> methods, depending on your needs (see the documentation for these two methods for a detailed discussion of the difference between them):</p><pre><code class="language-julia-repl hljs">julia&gt; print(stdout, 0x61)
97</code></pre><p>See <a href="types.html#man-custom-pretty-printing">Custom pretty-printing</a> for more information on how to implement display methods for custom types.</p><h2 id="IO-Output-Contextual-Properties"><a class="docs-heading-anchor" href="#IO-Output-Contextual-Properties">IO Output Contextual Properties</a><a id="IO-Output-Contextual-Properties-1"></a><a class="docs-heading-anchor-permalink" href="#IO-Output-Contextual-Properties" title="Permalink"></a></h2><p>Sometimes IO output can benefit from the ability to pass contextual information into show methods. The <a href="../base/io-network.html#Base.IOContext"><code>IOContext</code></a> object provides this framework for associating arbitrary metadata with an IO object. For example, <code>:compact =&gt; true</code> adds a hinting parameter to the IO object that the invoked show method should print a shorter output (if applicable). See the <a href="../base/io-network.html#Base.IOContext"><code>IOContext</code></a> documentation for a list of common properties.</p><h2 id="Working-with-Files"><a class="docs-heading-anchor" href="#Working-with-Files">Working with Files</a><a id="Working-with-Files-1"></a><a class="docs-heading-anchor-permalink" href="#Working-with-Files" title="Permalink"></a></h2><p>You can write content to a file with the <code>write(filename::String, content)</code> method:</p><pre><code class="language-julia-repl hljs">julia&gt; write(&quot;hello.txt&quot;, &quot;Hello, World!&quot;)
13</code></pre><p><em>(<code>13</code> is the number of bytes written.)</em></p><p>You can read the contents of a file with the <code>read(filename::String)</code> method, or <code>read(filename::String, String)</code> to the contents as a string:</p><pre><code class="language-julia-repl hljs">julia&gt; read(&quot;hello.txt&quot;, String)
&quot;Hello, World!&quot;</code></pre><h3 id="Advanced:-streaming-files"><a class="docs-heading-anchor" href="#Advanced:-streaming-files">Advanced: streaming files</a><a id="Advanced:-streaming-files-1"></a><a class="docs-heading-anchor-permalink" href="#Advanced:-streaming-files" title="Permalink"></a></h3><p>The <code>read</code> and <code>write</code> methods above allow you to read and write file contents. Like many other environments, Julia also has an <a href="../base/io-network.html#Base.open"><code>open</code></a> function, which takes a filename and returns an <a href="../base/io-network.html#Base.IOStream"><code>IOStream</code></a> object that you can use to read and write things from the file. For example, if we have a file, <code>hello.txt</code>, whose contents are <code>Hello, World!</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; f = open(&quot;hello.txt&quot;)
IOStream(&lt;file hello.txt&gt;)

julia&gt; readlines(f)
1-element Array{String,1}:
 &quot;Hello, World!&quot;</code></pre><p>If you want to write to a file, you can open it with the write (<code>&quot;w&quot;</code>) flag:</p><pre><code class="language-julia-repl hljs">julia&gt; f = open(&quot;hello.txt&quot;,&quot;w&quot;)
IOStream(&lt;file hello.txt&gt;)

julia&gt; write(f,&quot;Hello again.&quot;)
12</code></pre><p>If you examine the contents of <code>hello.txt</code> at this point, you will notice that it is empty; nothing has actually been written to disk yet. This is because the <code>IOStream</code> must be closed before the write is actually flushed to disk:</p><pre><code class="language-julia-repl hljs">julia&gt; close(f)</code></pre><p>Examining <code>hello.txt</code> again will show its contents have been changed.</p><p>Opening a file, doing something to its contents, and closing it again is a very common pattern. To make this easier, there exists another invocation of <a href="../base/io-network.html#Base.open"><code>open</code></a> which takes a function as its first argument and filename as its second, opens the file, calls the function with the file as an argument, and then closes it again. For example, given a function:</p><pre><code class="language-julia hljs">function read_and_capitalize(f::IOStream)
    return uppercase(read(f, String))
end</code></pre><p>You can call:</p><pre><code class="language-julia-repl hljs">julia&gt; open(read_and_capitalize, &quot;hello.txt&quot;)
&quot;HELLO AGAIN.&quot;</code></pre><p>to open <code>hello.txt</code>, call <code>read_and_capitalize</code> on it, close <code>hello.txt</code> and return the capitalized contents.</p><p>To avoid even having to define a named function, you can use the <code>do</code> syntax, which creates an anonymous function on the fly:</p><pre><code class="language-julia-repl hljs">julia&gt; open(&quot;hello.txt&quot;) do f
           uppercase(read(f, String))
       end
&quot;HELLO AGAIN.&quot;</code></pre><p>If you want to redirect stdout to a file</p><pre><code class="language- hljs">out_file = open(&quot;output.txt&quot;, &quot;w&quot;)

# Redirect stdout to file
redirect_stdout(out_file) do
    # Your code here
    println(&quot;This output goes to `out_file` via the `stdout` variable.&quot;)
end

# Close file
close(out_file)
</code></pre><p>Redirecting stdout to a file can help you save and analyze program output, automate processes, and meet compliance requirements.</p><h2 id="A-simple-TCP-example"><a class="docs-heading-anchor" href="#A-simple-TCP-example">A simple TCP example</a><a id="A-simple-TCP-example-1"></a><a class="docs-heading-anchor-permalink" href="#A-simple-TCP-example" title="Permalink"></a></h2><p>Let&#39;s jump right in with a simple example involving TCP sockets. This functionality is in a standard library package called <code>Sockets</code>. Let&#39;s first create a simple server:</p><pre><code class="language-julia-repl hljs">julia&gt; using Sockets

julia&gt; errormonitor(@async begin
           server = listen(2000)
           while true
               sock = accept(server)
               println(&quot;Hello World\n&quot;)
           end
       end)
Task (runnable) @0x00007fd31dc11ae0</code></pre><p>To those familiar with the Unix socket API, the method names will feel familiar, though their usage is somewhat simpler than the raw Unix socket API. The first call to <a href="../stdlib/Sockets.html#Sockets.listen-Tuple{Any}"><code>listen</code></a> will create a server waiting for incoming connections on the specified port (2000) in this case. The same function may also be used to create various other kinds of servers:</p><pre><code class="language-julia-repl hljs">julia&gt; listen(2000) # Listens on localhost:2000 (IPv4)
Sockets.TCPServer(active)

julia&gt; listen(ip&quot;127.0.0.1&quot;,2000) # Equivalent to the first
Sockets.TCPServer(active)

julia&gt; listen(ip&quot;::1&quot;,2000) # Listens on localhost:2000 (IPv6)
Sockets.TCPServer(active)

julia&gt; listen(IPv4(0),2001) # Listens on port 2001 on all IPv4 interfaces
Sockets.TCPServer(active)

julia&gt; listen(IPv6(0),2001) # Listens on port 2001 on all IPv6 interfaces
Sockets.TCPServer(active)

julia&gt; listen(&quot;testsocket&quot;) # Listens on a UNIX domain socket
Sockets.PipeServer(active)

julia&gt; listen(&quot;\\\\.\\pipe\\testsocket&quot;) # Listens on a Windows named pipe
Sockets.PipeServer(active)</code></pre><p>Note that the return type of the last invocation is different. This is because this server does not listen on TCP, but rather on a named pipe (Windows) or UNIX domain socket. Also note that Windows named pipe format has to be a specific pattern such that the name prefix (<code>\\.\pipe\</code>) uniquely identifies the <a href="https://docs.microsoft.com/windows/desktop/ipc/pipe-names">file type</a>. The difference between TCP and named pipes or UNIX domain sockets is subtle and has to do with the <a href="../stdlib/Sockets.html#Sockets.accept"><code>accept</code></a> and <a href="../stdlib/Distributed.html#Sockets.connect-Tuple{ClusterManager, Int64, WorkerConfig}"><code>connect</code></a> methods. The <a href="../stdlib/Sockets.html#Sockets.accept"><code>accept</code></a> method retrieves a connection to the client that is connecting on the server we just created, while the <a href="../stdlib/Distributed.html#Sockets.connect-Tuple{ClusterManager, Int64, WorkerConfig}"><code>connect</code></a> function connects to a server using the specified method. The <a href="../stdlib/Distributed.html#Sockets.connect-Tuple{ClusterManager, Int64, WorkerConfig}"><code>connect</code></a> function takes the same arguments as <a href="../stdlib/Sockets.html#Sockets.listen-Tuple{Any}"><code>listen</code></a>, so, assuming the environment (i.e. host, cwd, etc.) is the same you should be able to pass the same arguments to <a href="../stdlib/Distributed.html#Sockets.connect-Tuple{ClusterManager, Int64, WorkerConfig}"><code>connect</code></a> as you did to listen to establish the connection. So let&#39;s try that out (after having created the server above):</p><pre><code class="language-julia-repl hljs">julia&gt; connect(2000)
TCPSocket(open, 0 bytes waiting)

julia&gt; Hello World</code></pre><p>As expected we saw &quot;Hello World&quot; printed. So, let&#39;s actually analyze what happened behind the scenes. When we called <a href="../stdlib/Distributed.html#Sockets.connect-Tuple{ClusterManager, Int64, WorkerConfig}"><code>connect</code></a>, we connect to the server we had just created. Meanwhile, the accept function returns a server-side connection to the newly created socket and prints &quot;Hello World&quot; to indicate that the connection was successful.</p><p>A great strength of Julia is that since the API is exposed synchronously even though the I/O is actually happening asynchronously, we didn&#39;t have to worry about callbacks or even making sure that the server gets to run. When we called <a href="../stdlib/Distributed.html#Sockets.connect-Tuple{ClusterManager, Int64, WorkerConfig}"><code>connect</code></a> the current task waited for the connection to be established and only continued executing after that was done. In this pause, the server task resumed execution (because a connection request was now available), accepted the connection, printed the message and waited for the next client. Reading and writing works in the same way. To see this, consider the following simple echo server:</p><pre><code class="language-julia-repl hljs">julia&gt; errormonitor(@async begin
           server = listen(2001)
           while true
               sock = accept(server)
               @async while isopen(sock)
                   write(sock, readline(sock, keep=true))
               end
           end
       end)
Task (runnable) @0x00007fd31dc12e60

julia&gt; clientside = connect(2001)
TCPSocket(RawFD(28) open, 0 bytes waiting)

julia&gt; errormonitor(@async while isopen(clientside)
           write(stdout, readline(clientside, keep=true))
       end)
Task (runnable) @0x00007fd31dc11870

julia&gt; println(clientside,&quot;Hello World from the Echo Server&quot;)
Hello World from the Echo Server</code></pre><p>As with other streams, use <a href="../base/io-network.html#Base.close"><code>close</code></a> to disconnect the socket:</p><pre><code class="language-julia-repl hljs">julia&gt; close(clientside)</code></pre><h2 id="Resolving-IP-Addresses"><a class="docs-heading-anchor" href="#Resolving-IP-Addresses">Resolving IP Addresses</a><a id="Resolving-IP-Addresses-1"></a><a class="docs-heading-anchor-permalink" href="#Resolving-IP-Addresses" title="Permalink"></a></h2><p>One of the <a href="../stdlib/Distributed.html#Sockets.connect-Tuple{ClusterManager, Int64, WorkerConfig}"><code>connect</code></a> methods that does not follow the <a href="../stdlib/Sockets.html#Sockets.listen-Tuple{Any}"><code>listen</code></a> methods is <code>connect(host::String,port)</code>, which will attempt to connect to the host given by the <code>host</code> parameter on the port given by the <code>port</code> parameter. It allows you to do things like:</p><pre><code class="language-julia-repl hljs">julia&gt; connect(&quot;google.com&quot;, 80)
TCPSocket(RawFD(30) open, 0 bytes waiting)</code></pre><p>At the base of this functionality is <a href="../stdlib/Sockets.html#Sockets.getaddrinfo"><code>getaddrinfo</code></a>, which will do the appropriate address resolution:</p><pre><code class="language-julia-repl hljs">julia&gt; getaddrinfo(&quot;google.com&quot;)
ip&quot;**************&quot;</code></pre><h2 id="Asynchronous-I/O"><a class="docs-heading-anchor" href="#Asynchronous-I/O">Asynchronous I/O</a><a id="Asynchronous-I/O-1"></a><a class="docs-heading-anchor-permalink" href="#Asynchronous-I/O" title="Permalink"></a></h2><p>All I/O operations exposed by <a href="../base/io-network.html#Base.read"><code>Base.read</code></a> and <a href="../base/io-network.html#Base.write"><code>Base.write</code></a> can be performed asynchronously through the use of <a href="control-flow.html#man-tasks">coroutines</a>. You can create a new coroutine to read from or write to a stream using the <a href="../base/parallel.html#Base.@async"><code>@async</code></a> macro:</p><pre><code class="language-julia-repl hljs">julia&gt; task = @async open(&quot;foo.txt&quot;, &quot;w&quot;) do io
           write(io, &quot;Hello, World!&quot;)
       end;

julia&gt; wait(task)

julia&gt; readlines(&quot;foo.txt&quot;)
1-element Array{String,1}:
 &quot;Hello, World!&quot;</code></pre><p>It&#39;s common to run into situations where you want to perform multiple asynchronous operations concurrently and wait until they&#39;ve all completed. You can use the <a href="../base/parallel.html#Base.@sync"><code>@sync</code></a> macro to cause your program to block until all of the coroutines it wraps around have exited:</p><pre><code class="language-julia-repl hljs">julia&gt; using Sockets

julia&gt; @sync for hostname in (&quot;google.com&quot;, &quot;github.com&quot;, &quot;julialang.org&quot;)
           @async begin
               conn = connect(hostname, 80)
               write(conn, &quot;GET / HTTP/1.1\r\nHost:$(hostname)\r\n\r\n&quot;)
               readline(conn, keep=true)
               println(&quot;Finished connection to $(hostname)&quot;)
           end
       end
Finished connection to google.com
Finished connection to julialang.org
Finished connection to github.com</code></pre><h2 id="Multicast"><a class="docs-heading-anchor" href="#Multicast">Multicast</a><a id="Multicast-1"></a><a class="docs-heading-anchor-permalink" href="#Multicast" title="Permalink"></a></h2><p>Julia supports <a href="https://datatracker.ietf.org/doc/html/rfc1112">multicast</a> over IPv4 and IPv6 using the User Datagram Protocol (<a href="https://datatracker.ietf.org/doc/html/rfc768">UDP</a>) as transport.</p><p>Unlike the Transmission Control Protocol (<a href="https://datatracker.ietf.org/doc/html/rfc793">TCP</a>), UDP makes almost no assumptions about the needs of the application. TCP provides flow control (it accelerates and decelerates to maximize throughput), reliability (lost or corrupt packets are automatically retransmitted), sequencing (packets are ordered by the operating system before they are given to the application), segment size, and session setup and teardown. UDP provides no such features.</p><p>A common use for UDP is in multicast applications. TCP is a stateful protocol for communication between exactly two devices. UDP can use special multicast addresses to allow simultaneous communication between many devices.</p><h3 id="Receiving-IP-Multicast-Packets"><a class="docs-heading-anchor" href="#Receiving-IP-Multicast-Packets">Receiving IP Multicast Packets</a><a id="Receiving-IP-Multicast-Packets-1"></a><a class="docs-heading-anchor-permalink" href="#Receiving-IP-Multicast-Packets" title="Permalink"></a></h3><p>To transmit data over UDP multicast, simply <code>recv</code> on the socket, and the first packet received will be returned. Note that it may not be the first packet that you sent however!</p><pre><code class="language-julia hljs">using Sockets
group = ip&quot;*********&quot;
socket = Sockets.UDPSocket()
bind(socket, ip&quot;0.0.0.0&quot;, 6789)
join_multicast_group(socket, group)
println(String(recv(socket)))
leave_multicast_group(socket, group)
close(socket)</code></pre><h3 id="Sending-IP-Multicast-Packets"><a class="docs-heading-anchor" href="#Sending-IP-Multicast-Packets">Sending IP Multicast Packets</a><a id="Sending-IP-Multicast-Packets-1"></a><a class="docs-heading-anchor-permalink" href="#Sending-IP-Multicast-Packets" title="Permalink"></a></h3><p>To transmit data over UDP multicast, simply <code>send</code> to the socket. Notice that it is not necessary for a sender to join the multicast group.</p><pre><code class="language-julia hljs">using Sockets
group = ip&quot;*********&quot;
socket = Sockets.UDPSocket()
send(socket, group, 6789, &quot;Hello over IPv4&quot;)
close(socket)</code></pre><h3 id="IPv6-Example"><a class="docs-heading-anchor" href="#IPv6-Example">IPv6 Example</a><a id="IPv6-Example-1"></a><a class="docs-heading-anchor-permalink" href="#IPv6-Example" title="Permalink"></a></h3><p>This example gives the same functionality as the previous program, but uses IPv6 as the network-layer protocol.</p><p>Listener:</p><pre><code class="language-julia hljs">using Sockets
group = Sockets.IPv6(&quot;ff05::5:6:7&quot;)
socket = Sockets.UDPSocket()
bind(socket, Sockets.IPv6(&quot;::&quot;), 6789)
join_multicast_group(socket, group)
println(String(recv(socket)))
leave_multicast_group(socket, group)
close(socket)</code></pre><p>Sender:</p><pre><code class="language-julia hljs">using Sockets
group = Sockets.IPv6(&quot;ff05::5:6:7&quot;)
socket = Sockets.UDPSocket()
send(socket, group, 6789, &quot;Hello over IPv6&quot;)
close(socket)</code></pre></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="missing.html">« Missing Values</a><a class="docs-footer-nextpage" href="parallel-computing.html">Parallel Computing »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
