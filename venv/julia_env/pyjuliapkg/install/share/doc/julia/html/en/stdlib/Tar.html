<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Tar · The Julia Language</title><meta name="title" content="Tar · The Julia Language"/><meta property="og:title" content="Tar · The Julia Language"/><meta property="twitter:title" content="Tar · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li class="is-active"><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="Tar.html">Tar</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="Tar.html">Tar</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaIO/Tar.jl/blob/master/docs/src/index.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Tar"><a class="docs-heading-anchor" href="#Tar">Tar</a><a id="Tar-1"></a><a class="docs-heading-anchor-permalink" href="#Tar" title="Permalink"></a></h1><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Tar.create" href="#Tar.create"><code>Tar.create</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">create(
    [ predicate, ] dir, [ tarball ];
    [ skeleton, ] [ portable = false ]
) -&gt; tarball

    predicate :: String --&gt; Bool
    dir       :: AbstractString
    tarball   :: Union{AbstractString, AbstractCmd, IO}
    skeleton  :: Union{AbstractString, AbstractCmd, IO}
    portable  :: Bool</code></pre><p>Create a tar archive (&quot;tarball&quot;) of the directory <code>dir</code>. The resulting archive is written to the path <code>tarball</code> or if no path is specified, a temporary path is created and returned by the function call. If <code>tarball</code> is an IO object then the tarball content is written to that handle instead (the handle is left open).</p><p>If a <code>predicate</code> function is passed, it is called on each system path that is encountered while recursively searching <code>dir</code> and <code>path</code> is only included in the tarball if <code>predicate(path)</code> is true. If <code>predicate(path)</code> returns false for a directory, then the directory is excluded entirely: nothing under that directory will be included in the archive.</p><p>If the <code>skeleton</code> keyword is passed then the file or IO handle given is used as a &quot;skeleton&quot; to generate the tarball. You create a skeleton file by passing the <code>skeleton</code> keyword to the <code>extract</code> command. If <code>create</code> is called with that skeleton file and the extracted files haven&#39;t changed, an identical tarball is recreated. The <code>skeleton</code> and <code>predicate</code> arguments cannot be used together.</p><p>If the <code>portable</code> flag is true then path names are checked for validity on Windows, which ensures that they don&#39;t contain illegal characters or have names that are reserved. See https://stackoverflow.com/a/31976060/659248 for details.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaIO/Tar.jl/blob/81888a33704b233a2ad6f82f84456a1dd82c87f0/src/Tar.jl#L57-L89">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Tar.extract" href="#Tar.extract"><code>Tar.extract</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">extract(
    [ predicate, ] tarball, [ dir ];
    [ skeleton = &lt;none&gt;, ]
    [ copy_symlinks = &lt;auto&gt;, ]
    [ set_permissions = true, ]
) -&gt; dir

    predicate       :: Header --&gt; Bool
    tarball         :: Union{AbstractString, AbstractCmd, IO}
    dir             :: AbstractString
    skeleton        :: Union{AbstractString, AbstractCmd, IO}
    copy_symlinks   :: Bool
    set_permissions :: Bool</code></pre><p>Extract a tar archive (&quot;tarball&quot;) located at the path <code>tarball</code> into the directory <code>dir</code>. If <code>tarball</code> is an IO object instead of a path, then the archive contents will be read from that IO stream. The archive is extracted to <code>dir</code> which must either be an existing empty directory or a non-existent path which can be created as a new directory. If <code>dir</code> is not specified, the archive is extracted into a temporary directory which is returned by <code>extract</code>.</p><p>If a <code>predicate</code> function is passed, it is called on each <code>Header</code> object that is encountered while extracting <code>tarball</code> and the entry is only extracted if the <code>predicate(hdr)</code> is true. This can be used to selectively extract only parts of an archive, to skip entries that cause <code>extract</code> to throw an error, or to record what is extracted during the extraction process.</p><p>Before it is passed to the predicate function, the <code>Header</code> object is somewhat modified from the raw header in the tarball: the <code>path</code> field is normalized to remove <code>.</code> entries and replace multiple consecutive slashes with a single slash. If the entry has type <code>:hardlink</code>, the link target path is normalized the same way so that it will match the path of the target entry; the size field is set to the size of the target path (which must be an already-seen file).</p><p>If the <code>skeleton</code> keyword is passed then a &quot;skeleton&quot; of the extracted tarball is written to the file or IO handle given. This skeleton file can be used to recreate an identical tarball by passing the <code>skeleton</code> keyword to the <code>create</code> function. The <code>skeleton</code> and <code>predicate</code> arguments cannot be used together.</p><p>If <code>copy_symlinks</code> is <code>true</code> then instead of extracting symbolic links as such, they will be extracted as copies of what they link to if they are internal to the tarball and if it is possible to do so. Non-internal symlinks, such as a link to <code>/etc/passwd</code> will not be copied. Symlinks which are in any way cyclic will also not be copied and will instead be skipped. By default, <code>extract</code> will detect whether symlinks can be created in <code>dir</code> or not and will automatically copy symlinks if they cannot be created.</p><p>If <code>set_permissions</code> is <code>false</code>, no permissions are set on the extracted files.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaIO/Tar.jl/blob/81888a33704b233a2ad6f82f84456a1dd82c87f0/src/Tar.jl#L179-L228">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Tar.list" href="#Tar.list"><code>Tar.list</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">list(tarball; [ strict = true ]) -&gt; Vector{Header}
list(callback, tarball; [ strict = true ])

    callback  :: Header, [ &lt;data&gt; ] --&gt; Any
    tarball   :: Union{AbstractString, AbstractCmd, IO}
    strict    :: Bool</code></pre><p>List the contents of a tar archive (&quot;tarball&quot;) located at the path <code>tarball</code>. If <code>tarball</code> is an IO handle, read the tar contents from that stream. Returns a vector of <code>Header</code> structs. See <a href="Tar.html#Tar.Header"><code>Header</code></a> for details.</p><p>If a <code>callback</code> is provided then instead of returning a vector of headers, the callback is called on each <code>Header</code>. This can be useful if the number of items in the tarball is large or if you want examine items prior to an error in the tarball. If the <code>callback</code> function can accept a second argument of either type <code>Vector{UInt8}</code> or <code>Vector{Pair{Symbol, String}}</code> then it will be called with a representation of the raw header data either as a single byte vector or as a vector of pairs mapping field names to the raw data for that field (if these fields are concatenated together, the result is the raw data of the header).</p><p>By default <code>list</code> will error if it encounters any tarball contents which the <code>extract</code> function would refuse to extract. With <code>strict=false</code> it will skip these checks and list all the the contents of the tar file whether <code>extract</code> would extract them or not. Beware that malicious tarballs can do all sorts of crafty and unexpected things to try to trick you into doing something bad.</p><p>If the <code>tarball</code> argument is a skeleton file (see <code>extract</code> and <code>create</code>) then <code>list</code> will detect that from the file header and appropriately list or iterate the headers of the skeleton file.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaIO/Tar.jl/blob/81888a33704b233a2ad6f82f84456a1dd82c87f0/src/Tar.jl#L123-L153">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Tar.rewrite" href="#Tar.rewrite"><code>Tar.rewrite</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rewrite(
    [ predicate, ] old_tarball, [ new_tarball ];
    [ portable = false, ]
) -&gt; new_tarball

    predicate   :: Header --&gt; Bool
    old_tarball :: Union{AbstractString, AbstractCmd, IO}
    new_tarball :: Union{AbstractString, AbstractCmd, IO}
    portable    :: Bool</code></pre><p>Rewrite <code>old_tarball</code> to the standard format that <code>create</code> generates, while also checking that it doesn&#39;t contain anything that would cause <code>extract</code> to raise an error. This is functionally equivalent to doing</p><pre><code class="nohighlight hljs">Tar.create(Tar.extract(predicate, old_tarball), new_tarball)</code></pre><p>However, it never extracts anything to disk and instead uses the <code>seek</code> function to navigate the old tarball&#39;s data. If no <code>new_tarball</code> argument is passed, the new tarball is written to a temporary file whose path is returned.</p><p>If a <code>predicate</code> function is passed, it is called on each <code>Header</code> object that is encountered while extracting <code>old_tarball</code> and the entry is skipped unless <code>predicate(hdr)</code> is true. This can be used to selectively rewrite only parts of an archive, to skip entries that would cause <code>extract</code> to throw an error, or to record what content is encountered during the rewrite process.</p><p>Before it is passed to the predicate function, the <code>Header</code> object is somewhat modified from the raw header in the tarball: the <code>path</code> field is normalized to remove <code>.</code> entries and replace multiple consecutive slashes with a single slash. If the entry has type <code>:hardlink</code>, the link target path is normalized the same way so that it will match the path of the target entry; the size field is set to the size of the target path (which must be an already-seen file).</p><p>If the <code>portable</code> flag is true then path names are checked for validity on Windows, which ensures that they don&#39;t contain illegal characters or have names that are reserved. See https://stackoverflow.com/a/31976060/659248 for details.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaIO/Tar.jl/blob/81888a33704b233a2ad6f82f84456a1dd82c87f0/src/Tar.jl#L276-L313">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Tar.tree_hash" href="#Tar.tree_hash"><code>Tar.tree_hash</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">tree_hash([ predicate, ] tarball;
          [ algorithm = &quot;git-sha1&quot;, ]
          [ skip_empty = false ]) -&gt; hash::String

    predicate  :: Header --&gt; Bool
    tarball    :: Union{AbstractString, AbstractCmd, IO}
    algorithm  :: AbstractString
    skip_empty :: Bool</code></pre><p>Compute a tree hash value for the file tree that the tarball contains. By default, this uses git&#39;s tree hashing algorithm with the SHA1 secure hash function (like current versions of git). This means that for any tarball whose file tree git can represent—i.e. one with only files, symlinks and non-empty directories—the hash value computed by this function will be the same as the hash value git would compute for that file tree. Note that tarballs can represent file trees with empty directories, which git cannot store, and this function can generate hashes for those, which will, by default (see <code>skip_empty</code> below for how to change this behavior), differ from the hash of a tarball which omits those empty directories. In short, the hash function agrees with git on all trees which git can represent, but extends (in a consistent way) the domain of hashable trees to other trees which git cannot represent.</p><p>If a <code>predicate</code> function is passed, it is called on each <code>Header</code> object that is encountered while processing <code>tarball</code> and an entry is only hashed if <code>predicate(hdr)</code> is true. This can be used to selectively hash only parts of an archive, to skip entries that cause <code>extract</code> to throw an error, or to record what is extracted during the hashing process.</p><p>Before it is passed to the predicate function, the <code>Header</code> object is somewhat modified from the raw header in the tarball: the <code>path</code> field is normalized to remove <code>.</code> entries and replace multiple consecutive slashes with a single slash. If the entry has type <code>:hardlink</code>, the link target path is normalized the same way so that it will match the path of the target entry; the size field is set to the size of the target path (which must be an already-seen file).</p><p>Currently supported values for <code>algorithm</code> are <code>git-sha1</code> (the default) and <code>git-sha256</code>, which uses the same basic algorithm as <code>git-sha1</code> but replaces the SHA1 hash function with SHA2-256, the hash function that git will transition to using in the future (due to known attacks on SHA1). Support for other file tree hashing algorithms may be added in the future.</p><p>The <code>skip_empty</code> option controls whether directories in the tarball which recursively contain no files or symlinks are included in the hash or ignored. In general, if you are hashing the content of a tarball or a file tree, you care about all directories, not just non-empty ones, so including these in the computed hash is the default. So why does this function even provide the option to skip empty directories? Because git refuses to store empty directories and will ignore them if you try to add them to a repo. So if you compute a reference tree hash by by adding files to a git repo and then asking git for the tree hash, the hash value that you get will match the hash value computed by <code>tree_hash</code> with <code>skip_empty=true</code>. In other words, this option allows <code>tree_hash</code> to emulate how git would hash a tree with empty directories. If you are hashing trees that may contain empty directories (i.e. do not come from a git repo), however, it is recommended that you hash them using a tool (such as this one) that does not ignore empty directories.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaIO/Tar.jl/blob/81888a33704b233a2ad6f82f84456a1dd82c87f0/src/Tar.jl#L336-L392">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Tar.Header" href="#Tar.Header"><code>Tar.Header</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>The <code>Header</code> type is a struct representing the essential metadata for a single record in a tar file with this definition:</p><pre><code class="nohighlight hljs">struct Header
    path :: String # path relative to the root
    type :: Symbol # type indicator (see below)
    mode :: UInt16 # mode/permissions (best viewed in octal)
    size :: Int64  # size of record data in bytes
    link :: String # target path of a symlink
end</code></pre><p>Types are represented with the following symbols: <code>file</code>, <code>hardlink</code>, <code>symlink</code>, <code>chardev</code>, <code>blockdev</code>, <code>directory</code>, <code>fifo</code>, or for unknown types, the typeflag character as a symbol. Note that <a href="Tar.html#Tar.extract"><code>extract</code></a> refuses to extract records types other than <code>file</code>, <code>symlink</code> and <code>directory</code>; <a href="Tar.html#Tar.list"><code>list</code></a> will only list other kinds of records if called with <code>strict=false</code>.</p><p>The tar format includes various other metadata about records, including user and group IDs, user and group names, and timestamps. The <code>Tar</code> package, by design, completely ignores these. When creating tar files, these fields are always set to zero/empty. When reading tar files, these fields are ignored aside from verifying header checksums for each header record for all fields.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaIO/Tar.jl/blob/81888a33704b233a2ad6f82f84456a1dd82c87f0/src/header.jl#L1-L24">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="TOML.html">« TOML</a><a class="docs-footer-nextpage" href="Test.html">Unit Testing »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
