<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Integers and Floating-Point Numbers · The Julia Language</title><meta name="title" content="Integers and Floating-Point Numbers · The Julia Language"/><meta property="og:title" content="Integers and Floating-Point Numbers · The Julia Language"/><meta property="twitter:title" content="Integers and Floating-Point Numbers · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li class="is-active"><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a><ul class="internal"><li><a class="tocitem" href="#Integers"><span>Integers</span></a></li><li><a class="tocitem" href="#Floating-Point-Numbers"><span>Floating-Point Numbers</span></a></li><li><a class="tocitem" href="#Arbitrary-Precision-Arithmetic"><span>Arbitrary Precision Arithmetic</span></a></li><li><a class="tocitem" href="#man-numeric-literal-coefficients"><span>Numeric Literal Coefficients</span></a></li><li><a class="tocitem" href="#Literal-zero-and-one"><span>Literal zero and one</span></a></li></ul></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/integers-and-floating-point-numbers.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Integers-and-Floating-Point-Numbers"><a class="docs-heading-anchor" href="#Integers-and-Floating-Point-Numbers">Integers and Floating-Point Numbers</a><a id="Integers-and-Floating-Point-Numbers-1"></a><a class="docs-heading-anchor-permalink" href="#Integers-and-Floating-Point-Numbers" title="Permalink"></a></h1><p>Integers and floating-point values are the basic building blocks of arithmetic and computation. Built-in representations of such values are called numeric primitives, while representations of integers and floating-point numbers as immediate values in code are known as numeric literals. For example, <code>1</code> is an integer literal, while <code>1.0</code> is a floating-point literal; their binary in-memory representations as objects are numeric primitives.</p><p>Julia provides a broad range of primitive numeric types, and a full complement of arithmetic and bitwise operators as well as standard mathematical functions are defined over them. These map directly onto numeric types and operations that are natively supported on modern computers, thus allowing Julia to take full advantage of computational resources. Additionally, Julia provides software support for <a href="integers-and-floating-point-numbers.html#Arbitrary-Precision-Arithmetic">Arbitrary Precision Arithmetic</a>, which can handle operations on numeric values that cannot be represented effectively in native hardware representations, but at the cost of relatively slower performance.</p><p>The following are Julia&#39;s primitive numeric types:</p><ul><li><strong>Integer types:</strong></li></ul><table><tr><th style="text-align: left">Type</th><th style="text-align: left">Signed?</th><th style="text-align: left">Number of bits</th><th style="text-align: left">Smallest value</th><th style="text-align: left">Largest value</th></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Core.Int8"><code>Int8</code></a></td><td style="text-align: left">✓</td><td style="text-align: left">8</td><td style="text-align: left">-2^7</td><td style="text-align: left">2^7 - 1</td></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Core.UInt8"><code>UInt8</code></a></td><td style="text-align: left"></td><td style="text-align: left">8</td><td style="text-align: left">0</td><td style="text-align: left">2^8 - 1</td></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Core.Int16"><code>Int16</code></a></td><td style="text-align: left">✓</td><td style="text-align: left">16</td><td style="text-align: left">-2^15</td><td style="text-align: left">2^15 - 1</td></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Core.UInt16"><code>UInt16</code></a></td><td style="text-align: left"></td><td style="text-align: left">16</td><td style="text-align: left">0</td><td style="text-align: left">2^16 - 1</td></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Core.Int32"><code>Int32</code></a></td><td style="text-align: left">✓</td><td style="text-align: left">32</td><td style="text-align: left">-2^31</td><td style="text-align: left">2^31 - 1</td></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Core.UInt32"><code>UInt32</code></a></td><td style="text-align: left"></td><td style="text-align: left">32</td><td style="text-align: left">0</td><td style="text-align: left">2^32 - 1</td></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Core.Int64"><code>Int64</code></a></td><td style="text-align: left">✓</td><td style="text-align: left">64</td><td style="text-align: left">-2^63</td><td style="text-align: left">2^63 - 1</td></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Core.UInt64"><code>UInt64</code></a></td><td style="text-align: left"></td><td style="text-align: left">64</td><td style="text-align: left">0</td><td style="text-align: left">2^64 - 1</td></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Core.Int128"><code>Int128</code></a></td><td style="text-align: left">✓</td><td style="text-align: left">128</td><td style="text-align: left">-2^127</td><td style="text-align: left">2^127 - 1</td></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Core.UInt128"><code>UInt128</code></a></td><td style="text-align: left"></td><td style="text-align: left">128</td><td style="text-align: left">0</td><td style="text-align: left">2^128 - 1</td></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Core.Bool"><code>Bool</code></a></td><td style="text-align: left">N/A</td><td style="text-align: left">8</td><td style="text-align: left"><code>false</code> (0)</td><td style="text-align: left"><code>true</code> (1)</td></tr></table><ul><li><strong>Floating-point types:</strong></li></ul><table><tr><th style="text-align: left">Type</th><th style="text-align: left">Precision</th><th style="text-align: left">Number of bits</th></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Core.Float16"><code>Float16</code></a></td><td style="text-align: left"><a href="https://en.wikipedia.org/wiki/Half-precision_floating-point_format">half</a></td><td style="text-align: left">16</td></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Core.Float32"><code>Float32</code></a></td><td style="text-align: left"><a href="https://en.wikipedia.org/wiki/Single_precision_floating-point_format">single</a></td><td style="text-align: left">32</td></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Core.Float64"><code>Float64</code></a></td><td style="text-align: left"><a href="https://en.wikipedia.org/wiki/Double_precision_floating-point_format">double</a></td><td style="text-align: left">64</td></tr></table><p>Additionally, full support for <a href="complex-and-rational-numbers.html#Complex-and-Rational-Numbers">Complex and Rational Numbers</a> is built on top of these primitive numeric types. All numeric types interoperate naturally without explicit casting, thanks to a flexible, user-extensible <a href="conversion-and-promotion.html#conversion-and-promotion">type promotion system</a>.</p><h2 id="Integers"><a class="docs-heading-anchor" href="#Integers">Integers</a><a id="Integers-1"></a><a class="docs-heading-anchor-permalink" href="#Integers" title="Permalink"></a></h2><p>Literal integers are represented in the standard manner:</p><pre><code class="language-julia-repl hljs">julia&gt; 1
1

julia&gt; 1234
1234</code></pre><p>The default type for an integer literal depends on whether the target system has a 32-bit architecture or a 64-bit architecture:</p><pre><code class="language-julia-repl hljs"># 32-bit system:
julia&gt; typeof(1)
Int32

# 64-bit system:
julia&gt; typeof(1)
Int64</code></pre><p>The Julia internal variable <a href="../base/constants.html#Base.Sys.WORD_SIZE"><code>Sys.WORD_SIZE</code></a> indicates whether the target system is 32-bit or 64-bit:</p><pre><code class="language-julia-repl hljs"># 32-bit system:
julia&gt; Sys.WORD_SIZE
32

# 64-bit system:
julia&gt; Sys.WORD_SIZE
64</code></pre><p>Julia also defines the types <code>Int</code> and <code>UInt</code>, which are aliases for the system&#39;s signed and unsigned native integer types respectively:</p><pre><code class="language-julia-repl hljs"># 32-bit system:
julia&gt; Int
Int32
julia&gt; UInt
UInt32

# 64-bit system:
julia&gt; Int
Int64
julia&gt; UInt
UInt64</code></pre><p>Larger integer literals that cannot be represented using only 32 bits but can be represented in 64 bits always create 64-bit integers, regardless of the system type:</p><pre><code class="language-julia-repl hljs"># 32-bit or 64-bit system:
julia&gt; typeof(3000000000)
Int64</code></pre><p>Unsigned integers are input and output using the <code>0x</code> prefix and hexadecimal (base 16) digits <code>0-9a-f</code> (the capitalized digits <code>A-F</code> also work for input). The size of the unsigned value is determined by the number of hex digits used:</p><pre><code class="language-julia-repl hljs">julia&gt; x = 0x1
0x01

julia&gt; typeof(x)
UInt8

julia&gt; x = 0x123
0x0123

julia&gt; typeof(x)
UInt16

julia&gt; x = 0x1234567
0x01234567

julia&gt; typeof(x)
UInt32

julia&gt; x = 0x123456789abcdef
0x0123456789abcdef

julia&gt; typeof(x)
UInt64

julia&gt; x = 0x11112222333344445555666677778888
0x11112222333344445555666677778888

julia&gt; typeof(x)
UInt128</code></pre><p>This behavior is based on the observation that when one uses unsigned hex literals for integer values, one typically is using them to represent a fixed numeric byte sequence, rather than just an integer value.</p><p>Binary and octal literals are also supported:</p><pre><code class="language-julia-repl hljs">julia&gt; x = 0b10
0x02

julia&gt; typeof(x)
UInt8

julia&gt; x = 0o010
0x08

julia&gt; typeof(x)
UInt8

julia&gt; x = 0x00000000000000001111222233334444
0x00000000000000001111222233334444

julia&gt; typeof(x)
UInt128</code></pre><p>As for hexadecimal literals, binary and octal literals produce unsigned integer types. The size of the binary data item is the minimal needed size, if the leading digit of the literal is not <code>0</code>. In the case of leading zeros, the size is determined by the minimal needed size for a literal, which has the same length but leading digit <code>1</code>. It means that:</p><ul><li><code>0x1</code> and <code>0x12</code> are <code>UInt8</code> literals,</li><li><code>0x123</code> and <code>0x1234</code> are <code>UInt16</code> literals,</li><li><code>0x12345</code> and <code>0x12345678</code> are <code>UInt32</code> literals,</li><li><code>0x123456789</code> and <code>0x1234567890adcdef</code> are <code>UInt64</code> literals, etc.</li></ul><p>Even if there are leading zero digits which don’t contribute to the value, they count for determining storage size of a literal. So <code>0x01</code> is a <code>UInt8</code> while <code>0x0001</code> is a <code>UInt16</code>.</p><p>That allows the user to control the size.</p><p>Unsigned literals (starting with <code>0x</code>) that encode integers too large to be represented as <code>UInt128</code> values will construct <code>BigInt</code> values instead. This is not an unsigned type but it is the only built-in type big enough to represent such large integer values.</p><p>Binary, octal, and hexadecimal literals may be signed by a <code>-</code> immediately preceding the unsigned literal. They produce an unsigned integer of the same size as the unsigned literal would do, with the two&#39;s complement of the value:</p><pre><code class="language-julia-repl hljs">julia&gt; -0x2
0xfe

julia&gt; -0x0002
0xfffe</code></pre><p>The minimum and maximum representable values of primitive numeric types such as integers are given by the <a href="../base/base.html#Base.typemin"><code>typemin</code></a> and <a href="../base/base.html#Base.typemax"><code>typemax</code></a> functions:</p><pre><code class="language-julia-repl hljs">julia&gt; (typemin(Int32), typemax(Int32))
(-2147483648, 2147483647)

julia&gt; for T in [Int8,Int16,Int32,Int64,Int128,UInt8,UInt16,UInt32,UInt64,UInt128]
           println(&quot;$(lpad(T,7)): [$(typemin(T)),$(typemax(T))]&quot;)
       end
   Int8: [-128,127]
  Int16: [-32768,32767]
  Int32: [-2147483648,2147483647]
  Int64: [-9223372036854775808,9223372036854775807]
 Int128: [-170141183460469231731687303715884105728,170141183460469231731687303715884105727]
  UInt8: [0,255]
 UInt16: [0,65535]
 UInt32: [0,4294967295]
 UInt64: [0,18446744073709551615]
UInt128: [0,340282366920938463463374607431768211455]</code></pre><p>The values returned by <a href="../base/base.html#Base.typemin"><code>typemin</code></a> and <a href="../base/base.html#Base.typemax"><code>typemax</code></a> are always of the given argument type. (The above expression uses several features that have yet to be introduced, including <a href="control-flow.html#man-loops">for loops</a>, <a href="strings.html#man-strings">Strings</a>, and <a href="strings.html#string-interpolation">Interpolation</a>, but should be easy enough to understand for users with some existing programming experience.)</p><h3 id="Overflow-behavior"><a class="docs-heading-anchor" href="#Overflow-behavior">Overflow behavior</a><a id="Overflow-behavior-1"></a><a class="docs-heading-anchor-permalink" href="#Overflow-behavior" title="Permalink"></a></h3><p>In Julia, exceeding the maximum representable value of a given type results in a wraparound behavior:</p><pre><code class="language-julia-repl hljs">julia&gt; x = typemax(Int64)
9223372036854775807

julia&gt; x + 1
-9223372036854775808

julia&gt; x + 1 == typemin(Int64)
true</code></pre><p>Arithmetic operations with Julia&#39;s integer types inherently perform <a href="https://en.wikipedia.org/wiki/Modular_arithmetic">modular arithmetic</a>, mirroring the characteristics of integer arithmetic on modern computer hardware. In scenarios where overflow is a possibility, it is crucial to explicitly check for wraparound effects that can result from such overflows. The <a href="../base/math.html#Base.Checked"><code>Base.Checked</code></a> module provides a suite of arithmetic operations equipped with overflow checks, which trigger errors if an overflow occurs. For use cases where overflow cannot be tolerated under any circumstances, utilizing the <a href="../base/numbers.html#Base.GMP.BigInt"><code>BigInt</code></a> type, as detailed in <a href="integers-and-floating-point-numbers.html#Arbitrary-Precision-Arithmetic">Arbitrary Precision Arithmetic</a>, is advisable.</p><p>An example of overflow behavior and how to potentially resolve it is as follows:</p><pre><code class="language-julia-repl hljs">julia&gt; 10^19
-8446744073709551616

julia&gt; big(10)^19
10000000000000000000</code></pre><h3 id="Division-errors"><a class="docs-heading-anchor" href="#Division-errors">Division errors</a><a id="Division-errors-1"></a><a class="docs-heading-anchor-permalink" href="#Division-errors" title="Permalink"></a></h3><p>Integer division (the <code>div</code> function) has two exceptional cases: dividing by zero, and dividing the lowest negative number (<a href="../base/base.html#Base.typemin"><code>typemin</code></a>) by -1. Both of these cases throw a <a href="../base/base.html#Core.DivideError"><code>DivideError</code></a>. The remainder and modulus functions (<code>rem</code> and <code>mod</code>) throw a <a href="../base/base.html#Core.DivideError"><code>DivideError</code></a> when their second argument is zero.</p><h2 id="Floating-Point-Numbers"><a class="docs-heading-anchor" href="#Floating-Point-Numbers">Floating-Point Numbers</a><a id="Floating-Point-Numbers-1"></a><a class="docs-heading-anchor-permalink" href="#Floating-Point-Numbers" title="Permalink"></a></h2><p>Literal floating-point numbers are represented in the standard formats, using <a href="https://en.wikipedia.org/wiki/Scientific_notation#E_notation">E-notation</a> when necessary:</p><pre><code class="language-julia-repl hljs">julia&gt; 1.0
1.0

julia&gt; 1.
1.0

julia&gt; 0.5
0.5

julia&gt; .5
0.5

julia&gt; -1.23
-1.23

julia&gt; 1e10
1.0e10

julia&gt; 2.5e-4
0.00025</code></pre><p>The above results are all <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> values. Literal <a href="../base/numbers.html#Core.Float32"><code>Float32</code></a> values can be entered by writing an <code>f</code> in place of <code>e</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; x = 0.5f0
0.5f0

julia&gt; typeof(x)
Float32

julia&gt; 2.5f-4
0.00025f0</code></pre><p>Values can be converted to <a href="../base/numbers.html#Core.Float32"><code>Float32</code></a> easily:</p><pre><code class="language-julia-repl hljs">julia&gt; x = Float32(-1.5)
-1.5f0

julia&gt; typeof(x)
Float32</code></pre><p>Hexadecimal floating-point literals are also valid, but only as <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> values, with <code>p</code> preceding the base-2 exponent:</p><pre><code class="language-julia-repl hljs">julia&gt; 0x1p0
1.0

julia&gt; 0x1.8p3
12.0

julia&gt; x = 0x.4p-1
0.125

julia&gt; typeof(x)
Float64</code></pre><p>Half-precision floating-point numbers are also supported (<a href="../base/numbers.html#Core.Float16"><code>Float16</code></a>) on all platforms, with native instructions used on hardware which supports this number format. Otherwise, operations are implemented in software, and use <a href="../base/numbers.html#Core.Float32"><code>Float32</code></a> for intermediate calculations. As an internal implementation detail, this is achieved under the hood by using LLVM&#39;s <a href="https://llvm.org/docs/LangRef.html#half-precision-floating-point-intrinsics"><code>half</code></a> type, which behaves similarly to what the GCC <a href="https://gcc.gnu.org/onlinedocs/gcc/Optimize-Options.html#index-fexcess-precision"><code>-fexcess-precision=16</code></a> flag does for C/C++ code.</p><pre><code class="language-julia-repl hljs">julia&gt; sizeof(Float16(4.))
2

julia&gt; 2*Float16(4.)
Float16(8.0)</code></pre><p>The underscore <code>_</code> can be used as digit separator:</p><pre><code class="language-julia-repl hljs">julia&gt; 10_000, 0.000_000_005, 0xdead_beef, 0b1011_0010
(10000, 5.0e-9, 0xdeadbeef, 0xb2)</code></pre><h3 id="Floating-point-zero"><a class="docs-heading-anchor" href="#Floating-point-zero">Floating-point zero</a><a id="Floating-point-zero-1"></a><a class="docs-heading-anchor-permalink" href="#Floating-point-zero" title="Permalink"></a></h3><p>Floating-point numbers have <a href="https://en.wikipedia.org/wiki/Signed_zero">two zeros</a>, positive zero and negative zero. They are equal to each other but have different binary representations, as can be seen using the <a href="../base/numbers.html#Base.bitstring"><code>bitstring</code></a> function:</p><pre><code class="language-julia-repl hljs">julia&gt; 0.0 == -0.0
true

julia&gt; bitstring(0.0)
&quot;0000000000000000000000000000000000000000000000000000000000000000&quot;

julia&gt; bitstring(-0.0)
&quot;1000000000000000000000000000000000000000000000000000000000000000&quot;</code></pre><h3 id="Special-floating-point-values"><a class="docs-heading-anchor" href="#Special-floating-point-values">Special floating-point values</a><a id="Special-floating-point-values-1"></a><a class="docs-heading-anchor-permalink" href="#Special-floating-point-values" title="Permalink"></a></h3><p>There are three specified standard floating-point values that do not correspond to any point on the real number line:</p><table><tr><th style="text-align: left"><code>Float16</code></th><th style="text-align: left"><code>Float32</code></th><th style="text-align: left"><code>Float64</code></th><th style="text-align: left">Name</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left"><code>Inf16</code></td><td style="text-align: left"><code>Inf32</code></td><td style="text-align: left"><code>Inf</code></td><td style="text-align: left">positive infinity</td><td style="text-align: left">a value greater than all finite floating-point values</td></tr><tr><td style="text-align: left"><code>-Inf16</code></td><td style="text-align: left"><code>-Inf32</code></td><td style="text-align: left"><code>-Inf</code></td><td style="text-align: left">negative infinity</td><td style="text-align: left">a value less than all finite floating-point values</td></tr><tr><td style="text-align: left"><code>NaN16</code></td><td style="text-align: left"><code>NaN32</code></td><td style="text-align: left"><code>NaN</code></td><td style="text-align: left">not a number</td><td style="text-align: left">a value not <code>==</code> to any floating-point value (including itself)</td></tr></table><p>For further discussion of how these non-finite floating-point values are ordered with respect to each other and other floats, see <a href="mathematical-operations.html#Numeric-Comparisons">Numeric Comparisons</a>. By the <a href="https://en.wikipedia.org/wiki/IEEE_754-2008">IEEE 754 standard</a>, these floating-point values are the results of certain arithmetic operations:</p><pre><code class="language-julia-repl hljs">julia&gt; 1/Inf
0.0

julia&gt; 1/0
Inf

julia&gt; -5/0
-Inf

julia&gt; 0.000001/0
Inf

julia&gt; 0/0
NaN

julia&gt; 500 + Inf
Inf

julia&gt; 500 - Inf
-Inf

julia&gt; Inf + Inf
Inf

julia&gt; Inf - Inf
NaN

julia&gt; Inf * Inf
Inf

julia&gt; Inf / Inf
NaN

julia&gt; 0 * Inf
NaN

julia&gt; NaN == NaN
false

julia&gt; NaN != NaN
true

julia&gt; NaN &lt; NaN
false

julia&gt; NaN &gt; NaN
false</code></pre><p>The <a href="../base/base.html#Base.typemin"><code>typemin</code></a> and <a href="../base/base.html#Base.typemax"><code>typemax</code></a> functions also apply to floating-point types:</p><pre><code class="language-julia-repl hljs">julia&gt; (typemin(Float16),typemax(Float16))
(-Inf16, Inf16)

julia&gt; (typemin(Float32),typemax(Float32))
(-Inf32, Inf32)

julia&gt; (typemin(Float64),typemax(Float64))
(-Inf, Inf)</code></pre><h3 id="Machine-epsilon"><a class="docs-heading-anchor" href="#Machine-epsilon">Machine epsilon</a><a id="Machine-epsilon-1"></a><a class="docs-heading-anchor-permalink" href="#Machine-epsilon" title="Permalink"></a></h3><p>Most real numbers cannot be represented exactly with floating-point numbers, and so for many purposes it is important to know the distance between two adjacent representable floating-point numbers, which is often known as <a href="https://en.wikipedia.org/wiki/Machine_epsilon">machine epsilon</a>.</p><p>Julia provides <a href="../base/base.html#Base.eps-Tuple{Type{&lt;:AbstractFloat}}"><code>eps</code></a>, which gives the distance between <code>1.0</code> and the next larger representable floating-point value:</p><pre><code class="language-julia-repl hljs">julia&gt; eps(Float32)
1.1920929f-7

julia&gt; eps(Float64)
2.220446049250313e-16

julia&gt; eps() # same as eps(Float64)
2.220446049250313e-16</code></pre><p>These values are <code>2.0^-23</code> and <code>2.0^-52</code> as <a href="../base/numbers.html#Core.Float32"><code>Float32</code></a> and <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> values, respectively. The <a href="../base/base.html#Base.eps-Tuple{Type{&lt;:AbstractFloat}}"><code>eps</code></a> function can also take a floating-point value as an argument, and gives the absolute difference between that value and the next representable floating point value. That is, <code>eps(x)</code> yields a value of the same type as <code>x</code> such that <code>x + eps(x)</code> is the next representable floating-point value larger than <code>x</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; eps(1.0)
2.220446049250313e-16

julia&gt; eps(1000.)
1.1368683772161603e-13

julia&gt; eps(1e-27)
1.793662034335766e-43

julia&gt; eps(0.0)
5.0e-324</code></pre><p>The distance between two adjacent representable floating-point numbers is not constant, but is smaller for smaller values and larger for larger values. In other words, the representable floating-point numbers are densest in the real number line near zero, and grow sparser exponentially as one moves farther away from zero. By definition, <code>eps(1.0)</code> is the same as <code>eps(Float64)</code> since <code>1.0</code> is a 64-bit floating-point value.</p><p>Julia also provides the <a href="../base/numbers.html#Base.nextfloat"><code>nextfloat</code></a> and <a href="../base/numbers.html#Base.prevfloat"><code>prevfloat</code></a> functions which return the next largest or smallest representable floating-point number to the argument respectively:</p><pre><code class="language-julia-repl hljs">julia&gt; x = 1.25f0
1.25f0

julia&gt; nextfloat(x)
1.2500001f0

julia&gt; prevfloat(x)
1.2499999f0

julia&gt; bitstring(prevfloat(x))
&quot;00111111100111111111111111111111&quot;

julia&gt; bitstring(x)
&quot;00111111101000000000000000000000&quot;

julia&gt; bitstring(nextfloat(x))
&quot;00111111101000000000000000000001&quot;</code></pre><p>This example highlights the general principle that the adjacent representable floating-point numbers also have adjacent binary integer representations.</p><h3 id="Rounding-modes"><a class="docs-heading-anchor" href="#Rounding-modes">Rounding modes</a><a id="Rounding-modes-1"></a><a class="docs-heading-anchor-permalink" href="#Rounding-modes" title="Permalink"></a></h3><p>If a number doesn&#39;t have an exact floating-point representation, it must be rounded to an appropriate representable value. However, the manner in which this rounding is done can be changed if required according to the rounding modes presented in the <a href="https://en.wikipedia.org/wiki/IEEE_754-2008">IEEE 754 standard</a>.</p><p>The default mode used is always <a href="../base/math.html#Base.Rounding.RoundNearest"><code>RoundNearest</code></a>, which rounds to the nearest representable value, with ties rounded towards the nearest value with an even least significant bit.</p><h3 id="Background-and-References"><a class="docs-heading-anchor" href="#Background-and-References">Background and References</a><a id="Background-and-References-1"></a><a class="docs-heading-anchor-permalink" href="#Background-and-References" title="Permalink"></a></h3><p>Floating-point arithmetic entails many subtleties which can be surprising to users who are unfamiliar with the low-level implementation details. However, these subtleties are described in detail in most books on scientific computation, and also in the following references:</p><ul><li>The definitive guide to floating point arithmetic is the <a href="https://standards.ieee.org/standard/754-2008.html">IEEE 754-2008 Standard</a>; however, it is not available for free online.</li><li>For a brief but lucid presentation of how floating-point numbers are represented, see John D. Cook&#39;s <a href="https://www.johndcook.com/blog/2009/04/06/anatomy-of-a-floating-point-number/">article</a> on the subject as well as his <a href="https://www.johndcook.com/blog/2009/04/06/numbers-are-a-leaky-abstraction/">introduction</a> to some of the issues arising from how this representation differs in behavior from the idealized abstraction of real numbers.</li><li>Also recommended is Bruce Dawson&#39;s <a href="https://randomascii.wordpress.com/2012/05/20/thats-not-normalthe-performance-of-odd-floats/">series of blog posts on floating-point numbers</a>.</li><li>For an excellent, in-depth discussion of floating-point numbers and issues of numerical accuracy encountered when computing with them, see David Goldberg&#39;s paper <a href="https://citeseerx.ist.psu.edu/viewdoc/download?doi=*********.6768&amp;rep=rep1&amp;type=pdf">What Every Computer Scientist Should Know About Floating-Point Arithmetic</a>.</li><li>For even more extensive documentation of the history of, rationale for, and issues with floating-point numbers, as well as discussion of many other topics in numerical computing, see the <a href="https://people.eecs.berkeley.edu/~wkahan/">collected writings</a> of <a href="https://en.wikipedia.org/wiki/William_Kahan">William Kahan</a>, commonly known as the &quot;Father of Floating-Point&quot;. Of particular interest may be <a href="https://people.eecs.berkeley.edu/~wkahan/ieee754status/754story.html">An Interview with the Old Man of Floating-Point</a>.</li></ul><h2 id="Arbitrary-Precision-Arithmetic"><a class="docs-heading-anchor" href="#Arbitrary-Precision-Arithmetic">Arbitrary Precision Arithmetic</a><a id="Arbitrary-Precision-Arithmetic-1"></a><a class="docs-heading-anchor-permalink" href="#Arbitrary-Precision-Arithmetic" title="Permalink"></a></h2><p>To allow computations with arbitrary-precision integers and floating point numbers, Julia wraps the <a href="https://gmplib.org">GNU Multiple Precision Arithmetic Library (GMP)</a> and the <a href="https://www.mpfr.org">GNU MPFR Library</a>, respectively. The <a href="../base/numbers.html#Base.GMP.BigInt"><code>BigInt</code></a> and <a href="../base/numbers.html#Base.MPFR.BigFloat"><code>BigFloat</code></a> types are available in Julia for arbitrary precision integer and floating point numbers respectively.</p><p>Constructors exist to create these types from primitive numerical types, and the <a href="strings.html#non-standard-string-literals">string literal</a> <a href="../base/numbers.html#Core.@big_str"><code>@big_str</code></a> or <a href="../base/numbers.html#Base.parse"><code>parse</code></a> can be used to construct them from <code>AbstractString</code>s. <code>BigInt</code>s can also be input as integer literals when they are too big for other built-in integer types. Note that as there is no unsigned arbitrary-precision integer type in <code>Base</code> (<code>BigInt</code> is sufficient in most cases), hexadecimal, octal and binary literals can be used (in addition to decimal literals).</p><p>Once created, they participate in arithmetic with all other numeric types thanks to Julia&#39;s <a href="conversion-and-promotion.html#conversion-and-promotion">type promotion and conversion mechanism</a>:</p><pre><code class="language-julia-repl hljs">julia&gt; BigInt(typemax(Int64)) + 1
9223372036854775808

julia&gt; big&quot;123456789012345678901234567890&quot; + 1
123456789012345678901234567891

julia&gt; parse(BigInt, &quot;123456789012345678901234567890&quot;) + 1
123456789012345678901234567891

julia&gt; string(big&quot;2&quot;^200, base=16)
&quot;100000000000000000000000000000000000000000000000000&quot;

julia&gt; 0x100000000000000000000000000000000-1 == typemax(UInt128)
true

julia&gt; 0x000000000000000000000000000000000
0

julia&gt; typeof(ans)
BigInt

julia&gt; big&quot;1.23456789012345678901&quot;
1.234567890123456789010000000000000000000000000000000000000000000000000000000004

julia&gt; parse(BigFloat, &quot;1.23456789012345678901&quot;)
1.234567890123456789010000000000000000000000000000000000000000000000000000000004

julia&gt; BigFloat(2.0^66) / 3
2.459565876494606882133333333333333333333333333333333333333333333333333333333344e+19

julia&gt; factorial(BigInt(40))
815915283247897734345611269596115894272000000000</code></pre><p>However, type promotion between the primitive types above and <a href="../base/numbers.html#Base.GMP.BigInt"><code>BigInt</code></a>/<a href="../base/numbers.html#Base.MPFR.BigFloat"><code>BigFloat</code></a> is not automatic and must be explicitly stated.</p><pre><code class="language-julia-repl hljs">julia&gt; x = typemin(Int64)
-9223372036854775808

julia&gt; x = x - 1
9223372036854775807

julia&gt; typeof(x)
Int64

julia&gt; y = BigInt(typemin(Int64))
-9223372036854775808

julia&gt; y = y - 1
-922****************

julia&gt; typeof(y)
BigInt</code></pre><p>The default precision (in number of bits of the significand) and rounding mode of <a href="../base/numbers.html#Base.MPFR.BigFloat"><code>BigFloat</code></a> operations can be changed globally by calling <a href="../base/numbers.html#Base.MPFR.setprecision"><code>setprecision</code></a> and <a href="../base/numbers.html#Base.Rounding.setrounding-Tuple{Type, Any}"><code>setrounding</code></a>, and all further calculations will take these changes in account.  Alternatively, the precision or the rounding can be changed only within the execution of a particular block of code by using the same functions with a <code>do</code> block:</p><pre><code class="language-julia-repl hljs">julia&gt; setrounding(BigFloat, RoundUp) do
           BigFloat(1) + parse(BigFloat, &quot;0.1&quot;)
       end
1.100000000000000000000000000000000000000000000000000000000000000000000000000003

julia&gt; setrounding(BigFloat, RoundDown) do
           BigFloat(1) + parse(BigFloat, &quot;0.1&quot;)
       end
1.099999999999999999999999999999999999999999999999999999999999999999999999999986

julia&gt; setprecision(40) do
           BigFloat(1) + parse(BigFloat, &quot;0.1&quot;)
       end
1.*************</code></pre><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>The relation between <a href="../base/numbers.html#Base.MPFR.setprecision"><code>setprecision</code></a> or <a href="../base/numbers.html#Base.Rounding.setrounding-Tuple{Type, Any}"><code>setrounding</code></a> and <a href="../base/numbers.html#Core.@big_str"><code>@big_str</code></a>, the macro used for <code>big</code> string literals (such as <code>big&quot;0.3&quot;</code>), might not be intuitive, as a consequence of the fact that <code>@big_str</code> is a macro. See the <a href="../base/numbers.html#Core.@big_str"><code>@big_str</code></a> documentation for details.</p></div></div><h2 id="man-numeric-literal-coefficients"><a class="docs-heading-anchor" href="#man-numeric-literal-coefficients">Numeric Literal Coefficients</a><a id="man-numeric-literal-coefficients-1"></a><a class="docs-heading-anchor-permalink" href="#man-numeric-literal-coefficients" title="Permalink"></a></h2><p>To make common numeric formulae and expressions clearer, Julia allows variables to be immediately preceded by a numeric literal, implying multiplication. This makes writing polynomial expressions much cleaner:</p><pre><code class="language-julia-repl hljs">julia&gt; x = 3
3

julia&gt; 2x^2 - 3x + 1
10

julia&gt; 1.5x^2 - .5x + 1
13.0</code></pre><p>It also makes writing exponential functions more elegant:</p><pre><code class="language-julia-repl hljs">julia&gt; 2^2x
64</code></pre><p>The precedence of numeric literal coefficients is slightly lower than that of unary operators such as negation. So <code>-2x</code> is parsed as <code>(-2) * x</code> and <code>√2x</code> is parsed as <code>(√2) * x</code>. However, numeric literal coefficients parse similarly to unary operators when combined with exponentiation. For example <code>2^3x</code> is parsed as <code>2^(3x)</code>, and <code>2x^3</code> is parsed as <code>2*(x^3)</code>.</p><p>Numeric literals also work as coefficients to parenthesized expressions:</p><pre><code class="language-julia-repl hljs">julia&gt; 2(x-1)^2 - 3(x-1) + 1
3</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The precedence of numeric literal coefficients used for implicit multiplication is higher than other binary operators such as multiplication (<code>*</code>), and division (<code>/</code>, <code>\</code>, and <code>//</code>).  This means, for example, that <code>1 / 2im</code> equals <code>-0.5im</code> and <code>6 // 2(2 + 1)</code> equals <code>1 // 1</code>.</p></div></div><p>Additionally, parenthesized expressions can be used as coefficients to variables, implying multiplication of the expression by the variable:</p><pre><code class="language-julia-repl hljs">julia&gt; (x-1)x
6</code></pre><p>Neither juxtaposition of two parenthesized expressions, nor placing a variable before a parenthesized expression, however, can be used to imply multiplication:</p><pre><code class="language-julia-repl hljs">julia&gt; (x-1)(x+1)
ERROR: MethodError: objects of type Int64 are not callable

julia&gt; x(x+1)
ERROR: MethodError: objects of type Int64 are not callable</code></pre><p>Both expressions are interpreted as function application: any expression that is not a numeric literal, when immediately followed by a parenthetical, is interpreted as a function applied to the values in parentheses (see <a href="faq.html#Functions">Functions</a> for more about functions). Thus, in both of these cases, an error occurs since the left-hand value is not a function.</p><p>The above syntactic enhancements significantly reduce the visual noise incurred when writing common mathematical formulae. Note that no whitespace may come between a numeric literal coefficient and the identifier or parenthesized expression which it multiplies.</p><h3 id="Syntax-Conflicts"><a class="docs-heading-anchor" href="#Syntax-Conflicts">Syntax Conflicts</a><a id="Syntax-Conflicts-1"></a><a class="docs-heading-anchor-permalink" href="#Syntax-Conflicts" title="Permalink"></a></h3><p>Juxtaposed literal coefficient syntax may conflict with some numeric literal syntaxes: hexadecimal, octal and binary integer literals and engineering notation for floating-point literals. Here are some situations where syntactic conflicts arise:</p><ul><li>The hexadecimal integer literal expression <code>0xff</code> could be interpreted as the numeric literal <code>0</code> multiplied by the variable <code>xff</code>. Similar ambiguities arise with octal and binary literals like <code>0o777</code> or <code>0b01001010</code>.</li><li>The floating-point literal expression <code>1e10</code> could be interpreted as the numeric literal <code>1</code> multiplied by the variable <code>e10</code>, and similarly with the equivalent <code>E</code> form.</li><li>The 32-bit floating-point literal expression <code>1.5f22</code> could be interpreted as the numeric literal <code>1.5</code> multiplied by the variable <code>f22</code>.</li></ul><p>In all cases the ambiguity is resolved in favor of interpretation as numeric literals:</p><ul><li>Expressions starting with <code>0x</code>/<code>0o</code>/<code>0b</code> are always hexadecimal/octal/binary literals.</li><li>Expressions starting with a numeric literal followed by <code>e</code> or <code>E</code> are always floating-point literals.</li><li>Expressions starting with a numeric literal followed by <code>f</code> are always 32-bit floating-point literals.</li></ul><p>Unlike <code>E</code>, which is equivalent to <code>e</code> in numeric literals for historical reasons, <code>F</code> is just another letter and does not behave like <code>f</code> in numeric literals. Hence, expressions starting with a numeric literal followed by <code>F</code> are interpreted as the numerical literal multiplied by a variable, which means that, for example, <code>1.5F22</code> is equal to <code>1.5 * F22</code>.</p><h2 id="Literal-zero-and-one"><a class="docs-heading-anchor" href="#Literal-zero-and-one">Literal zero and one</a><a id="Literal-zero-and-one-1"></a><a class="docs-heading-anchor-permalink" href="#Literal-zero-and-one" title="Permalink"></a></h2><p>Julia provides functions which return literal 0 and 1 corresponding to a specified type or the type of a given variable.</p><table><tr><th style="text-align: left">Function</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Base.zero"><code>zero(x)</code></a></td><td style="text-align: left">Literal zero of type <code>x</code> or type of variable <code>x</code></td></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Base.one"><code>one(x)</code></a></td><td style="text-align: left">Literal one of type <code>x</code> or type of variable <code>x</code></td></tr></table><p>These functions are useful in <a href="mathematical-operations.html#Numeric-Comparisons">Numeric Comparisons</a> to avoid overhead from unnecessary <a href="conversion-and-promotion.html#conversion-and-promotion">type conversion</a>.</p><p>Examples:</p><pre><code class="language-julia-repl hljs">julia&gt; zero(Float32)
0.0f0

julia&gt; zero(1.0)
0.0

julia&gt; one(Int32)
1

julia&gt; one(BigFloat)
1.0</code></pre></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="variables.html">« Variables</a><a class="docs-footer-nextpage" href="mathematical-operations.html">Mathematical Operations and Elementary Functions »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
