<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Random Numbers · The Julia Language</title><meta name="title" content="Random Numbers · The Julia Language"/><meta property="og:title" content="Random Numbers · The Julia Language"/><meta property="twitter:title" content="Random Numbers · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li class="is-active"><a class="tocitem" href="Random.html">Random Numbers</a><ul class="internal"><li><a class="tocitem" href="#Random-numbers-module"><span>Random numbers module</span></a></li><li><a class="tocitem" href="#Random-generation-functions"><span>Random generation functions</span></a></li><li><a class="tocitem" href="#Subsequences,-permutations-and-shuffling"><span>Subsequences, permutations and shuffling</span></a></li><li><a class="tocitem" href="#Generators-(creation-and-seeding)"><span>Generators (creation and seeding)</span></a></li><li><a class="tocitem" href="#rand-api-hook"><span>Hooking into the <code>Random</code> API</span></a></li><li class="toplevel"><a class="tocitem" href="#Reproducibility"><span>Reproducibility</span></a></li></ul></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="Random.html">Random Numbers</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="Random.html">Random Numbers</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/stdlib/Random/docs/src/index.md" title="View source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Random-Numbers"><a class="docs-heading-anchor" href="#Random-Numbers">Random Numbers</a><a id="Random-Numbers-1"></a><a class="docs-heading-anchor-permalink" href="#Random-Numbers" title="Permalink"></a></h1><p>Random number generation in Julia uses the <a href="https://prng.di.unimi.it/">Xoshiro256++</a> algorithm by default, with per-<code>Task</code> state. Other RNG types can be plugged in by inheriting the <code>AbstractRNG</code> type; they can then be used to obtain multiple streams of random numbers.</p><p>The PRNGs (pseudorandom number generators) exported by the <code>Random</code> package are:</p><ul><li><code>TaskLocalRNG</code>: a token that represents use of the currently active Task-local stream, deterministically seeded from the parent task, or by <code>RandomDevice</code> (with system randomness) at program start</li><li><code>Xoshiro</code>: generates a high-quality stream of random numbers with a small state vector and high performance using the Xoshiro256++ algorithm</li><li><code>RandomDevice</code>: for OS-provided entropy. This may be used for cryptographically secure random numbers (CS(P)RNG).</li><li><code>MersenneTwister</code>: an alternate high-quality PRNG which was the default in older versions of Julia, and is also quite fast, but requires much more space to store the state vector and generate a random sequence.</li></ul><p>Most functions related to random generation accept an optional <code>AbstractRNG</code> object as first argument. Some also accept dimension specifications <code>dims...</code> (which can also be given as a tuple) to generate arrays of random values. In a multi-threaded program, you should generally use different RNG objects from different threads or tasks in order to be thread-safe. However, the default RNG is thread-safe as of Julia 1.3 (using a per-thread RNG up to version 1.6, and per-task thereafter).</p><p>The provided RNGs can generate uniform random numbers of the following types: <a href="../base/numbers.html#Core.Float16"><code>Float16</code></a>, <a href="../base/numbers.html#Core.Float32"><code>Float32</code></a>, <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a>, <a href="../base/numbers.html#Base.MPFR.BigFloat"><code>BigFloat</code></a>, <a href="../base/numbers.html#Core.Bool"><code>Bool</code></a>, <a href="../base/numbers.html#Core.Int8"><code>Int8</code></a>, <a href="../base/numbers.html#Core.UInt8"><code>UInt8</code></a>, <a href="../base/numbers.html#Core.Int16"><code>Int16</code></a>, <a href="../base/numbers.html#Core.UInt16"><code>UInt16</code></a>, <a href="../base/numbers.html#Core.Int32"><code>Int32</code></a>, <a href="../base/numbers.html#Core.UInt32"><code>UInt32</code></a>, <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>, <a href="../base/numbers.html#Core.UInt64"><code>UInt64</code></a>, <a href="../base/numbers.html#Core.Int128"><code>Int128</code></a>, <a href="../base/numbers.html#Core.UInt128"><code>UInt128</code></a>, <a href="../base/numbers.html#Base.GMP.BigInt"><code>BigInt</code></a> (or complex numbers of those types). Random floating point numbers are generated uniformly in <span>$[0, 1)$</span>. As <code>BigInt</code> represents unbounded integers, the interval must be specified (e.g. <code>rand(big.(1:6))</code>).</p><p>Additionally, normal and exponential distributions are implemented for some <code>AbstractFloat</code> and <code>Complex</code> types, see <a href="Random.html#Base.randn"><code>randn</code></a> and <a href="Random.html#Random.randexp"><code>randexp</code></a> for details.</p><p>To generate random numbers from other distributions, see the <a href="https://juliastats.org/Distributions.jl/stable/">Distributions.jl</a> package.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Because the precise way in which random numbers are generated is considered an implementation detail, bug fixes and speed improvements may change the stream of numbers that are generated after a version change. Relying on a specific seed or generated stream of numbers during unit testing is thus discouraged - consider testing properties of the methods in question instead.</p></div></div><h2 id="Random-numbers-module"><a class="docs-heading-anchor" href="#Random-numbers-module">Random numbers module</a><a id="Random-numbers-module-1"></a><a class="docs-heading-anchor-permalink" href="#Random-numbers-module" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.Random" href="#Random.Random"><code>Random.Random</code></a> — <span class="docstring-category">Module</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Random</code></pre><p>Support for generating random numbers. Provides <a href="Random.html#Base.rand"><code>rand</code></a>, <a href="Random.html#Base.randn"><code>randn</code></a>, <a href="Random.html#Random.AbstractRNG"><code>AbstractRNG</code></a>, <a href="Random.html#Random.MersenneTwister"><code>MersenneTwister</code></a>, and <a href="Random.html#Random.RandomDevice"><code>RandomDevice</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/Random.jl#L3-L8">source</a></section></article><h2 id="Random-generation-functions"><a class="docs-heading-anchor" href="#Random-generation-functions">Random generation functions</a><a id="Random-generation-functions-1"></a><a class="docs-heading-anchor-permalink" href="#Random-generation-functions" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.rand" href="#Base.rand"><code>Base.rand</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rand([rng=default_rng()], [S], [dims...])</code></pre><p>Pick a random element or array of random elements from the set of values specified by <code>S</code>; <code>S</code> can be</p><ul><li><p>an indexable collection (for example <code>1:9</code> or <code>(&#39;x&#39;, &quot;y&quot;, :z)</code>)</p></li><li><p>an <code>AbstractDict</code> or <code>AbstractSet</code> object</p></li><li><p>a string (considered as a collection of characters), or</p></li><li><p>a type from the list below, corresponding to the specified set of values</p><ul><li><p>concrete integer types sample from <code>typemin(S):typemax(S)</code> (excepting <a href="../base/numbers.html#Base.GMP.BigInt"><code>BigInt</code></a> which is not supported)</p></li><li><p>concrete floating point types sample from <code>[0, 1)</code></p></li><li><p>concrete complex types <code>Complex{T}</code> if <code>T</code> is a sampleable type take their real and imaginary components independently from the set of values corresponding to <code>T</code>, but are not supported if <code>T</code> is not sampleable.</p></li><li><p>all <code>&lt;:AbstractChar</code> types sample from the set of valid Unicode scalars</p></li><li><p>a user-defined type and set of values; for implementation guidance please see <a href="Random.html#rand-api-hook">Hooking into the <code>Random</code> API</a></p></li><li><p>a tuple type of known size and where each parameter of <code>S</code> is itself a sampleable type; return a value of type <code>S</code>. Note that tuple types such as <code>Tuple{Vararg{T}}</code> (unknown size) and <code>Tuple{1:2}</code> (parameterized with a value) are not supported</p></li><li><p>a <code>Pair</code> type, e.g. <code>Pair{X, Y}</code> such that <code>rand</code> is defined for <code>X</code> and <code>Y</code>, in which case random pairs are produced.</p></li></ul></li></ul><p><code>S</code> defaults to <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a>. When only one argument is passed besides the optional <code>rng</code> and is a <code>Tuple</code>, it is interpreted as a collection of values (<code>S</code>) and not as <code>dims</code>.</p><p>See also <a href="Random.html#Base.randn"><code>randn</code></a> for normally distributed numbers, and <a href="Random.html#Random.rand!"><code>rand!</code></a> and <a href="Random.html#Random.randn!"><code>randn!</code></a> for the in-place equivalents.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p>Support for <code>S</code> as a tuple requires at least Julia 1.1.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>Support for <code>S</code> as a <code>Tuple</code> type requires at least Julia 1.11.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; rand(Int, 2)
2-element Array{Int64,1}:
 1339893410598768192
 1575814717733606317

julia&gt; using Random

julia&gt; rand(Xoshiro(0), Dict(1=&gt;2, 3=&gt;4))
3 =&gt; 4

julia&gt; rand((2, 3))
3

julia&gt; rand(Float64, (2, 3))
2×3 Array{Float64,2}:
 0.999717  0.0143835  0.540787
 0.696556  0.783855   0.938235</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The complexity of <code>rand(rng, s::Union{AbstractDict,AbstractSet})</code> is linear in the length of <code>s</code>, unless an optimized method with constant complexity is available, which is the case for <code>Dict</code>, <code>Set</code> and dense <code>BitSet</code>s. For more than a few calls, use <code>rand(rng, collect(s))</code> instead, or either <code>rand(rng, Dict(s))</code> or <code>rand(rng, Set(s))</code> as appropriate.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/Random.jl#L310-L383">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.rand!" href="#Random.rand!"><code>Random.rand!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rand!([rng=default_rng()], A, [S=eltype(A)])</code></pre><p>Populate the array <code>A</code> with random values. If <code>S</code> is specified (<code>S</code> can be a type or a collection, cf. <a href="Random.html#Base.rand"><code>rand</code></a> for details), the values are picked randomly from <code>S</code>. This is equivalent to <code>copyto!(A, rand(rng, S, size(A)))</code> but without allocating a new array.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; rand!(Xoshiro(123), zeros(5))
5-element Vector{Float64}:
 0.521213795535383
 0.5868067574533484
 0.8908786980927811
 0.19090669902576285
 0.5256623915420473</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/Random.jl#L386-L405">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.bitrand" href="#Random.bitrand"><code>Random.bitrand</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">bitrand([rng=default_rng()], [dims...])</code></pre><p>Generate a <code>BitArray</code> of random boolean values.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; bitrand(Xoshiro(123), 10)
10-element BitVector:
 0
 1
 0
 1
 0
 1
 0
 0
 1
 1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/misc.jl#L13-L33">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.randn" href="#Base.randn"><code>Base.randn</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">randn([rng=default_rng()], [T=Float64], [dims...])</code></pre><p>Generate a normally-distributed random number of type <code>T</code> with mean 0 and standard deviation 1. Given the optional <code>dims</code> argument(s), generate an array of size <code>dims</code> of such numbers. Julia&#39;s standard library supports <code>randn</code> for any floating-point type that implements <a href="Random.html#Base.rand"><code>rand</code></a>, e.g. the <code>Base</code> types <a href="../base/numbers.html#Core.Float16"><code>Float16</code></a>, <a href="../base/numbers.html#Core.Float32"><code>Float32</code></a>, <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> (the default), and <a href="../base/numbers.html#Base.MPFR.BigFloat"><code>BigFloat</code></a>, along with their <a href="../base/numbers.html#Base.Complex"><code>Complex</code></a> counterparts.</p><p>(When <code>T</code> is complex, the values are drawn from the circularly symmetric complex normal distribution of variance 1, corresponding to real and imaginary parts having independent normal distribution with mean zero and variance <code>1/2</code>).</p><p>See also <a href="Random.html#Random.randn!"><code>randn!</code></a> to act in-place.</p><p><strong>Examples</strong></p><p>Generating a single random number (with the default <code>Float64</code> type):</p><pre><code class="language-julia-repl hljs">julia&gt; randn()
-0.942481877315864</code></pre><p>Generating a matrix of normal random numbers (with the default <code>Float64</code> type):</p><pre><code class="language-julia-repl hljs">julia&gt; randn(2,3)
2×3 Matrix{Float64}:
  1.18786   -0.678616   1.49463
 -0.342792  -0.134299  -1.45005</code></pre><p>Setting up of the random number generator <code>rng</code> with a user-defined seed (for reproducible numbers) and using it to generate a random <code>Float32</code> number or a matrix of <code>ComplexF32</code> random numbers:</p><pre><code class="language-julia-repl hljs">julia&gt; using Random

julia&gt; rng = Xoshiro(123);

julia&gt; randn(rng, Float32)
-0.6457307f0

julia&gt; randn(rng, ComplexF32, (2, 3))
2×3 Matrix{ComplexF32}:
  -1.03467-1.14806im  0.693657+0.056538im   0.291442+0.419454im
 -0.153912+0.34807im    1.0954-0.948661im  -0.543347-0.0538589im</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/normal.jl#L12-L63">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.randn!" href="#Random.randn!"><code>Random.randn!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">randn!([rng=default_rng()], A::AbstractArray) -&gt; A</code></pre><p>Fill the array <code>A</code> with normally-distributed (mean 0, standard deviation 1) random numbers. Also see the <a href="Random.html#Base.rand"><code>rand</code></a> function.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; randn!(Xoshiro(123), zeros(5))
5-element Vector{Float64}:
 -0.6457306721039767
 -1.4632513788889214
 -1.6236037455860806
 -0.21766510678354617
  0.4922456865251828</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/normal.jl#L181-L197">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.randexp" href="#Random.randexp"><code>Random.randexp</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">randexp([rng=default_rng()], [T=Float64], [dims...])</code></pre><p>Generate a random number of type <code>T</code> according to the exponential distribution with scale 1. Optionally generate an array of such random numbers. The <code>Base</code> module currently provides an implementation for the types <a href="../base/numbers.html#Core.Float16"><code>Float16</code></a>, <a href="../base/numbers.html#Core.Float32"><code>Float32</code></a>, and <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> (the default).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; rng = Xoshiro(123);

julia&gt; randexp(rng, Float32)
1.1757717f0

julia&gt; randexp(rng, 3, 3)
3×3 Matrix{Float64}:
 1.37766  0.456653  0.236418
 3.40007  0.229917  0.0684921
 0.48096  0.577481  0.71835</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/normal.jl#L130-L152">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.randexp!" href="#Random.randexp!"><code>Random.randexp!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">randexp!([rng=default_rng()], A::AbstractArray) -&gt; A</code></pre><p>Fill the array <code>A</code> with random numbers following the exponential distribution (with scale 1).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; randexp!(Xoshiro(123), zeros(5))
5-element Vector{Float64}:
 1.1757716836348473
 1.758884569451514
 1.0083623637301151
 0.3510644315565272
 0.6348266443720407</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/normal.jl#L200-L216">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.randstring" href="#Random.randstring"><code>Random.randstring</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">randstring([rng=default_rng()], [chars], [len=8])</code></pre><p>Create a random string of length <code>len</code>, consisting of characters from <code>chars</code>, which defaults to the set of upper- and lower-case letters and the digits 0-9. The optional <code>rng</code> argument specifies a random number generator, see <a href="Random.html#Random-Numbers">Random Numbers</a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Random.seed!(3); randstring()
&quot;Lxz5hUwn&quot;

julia&gt; randstring(Xoshiro(3), &#39;a&#39;:&#39;z&#39;, 6)
&quot;iyzcsm&quot;

julia&gt; randstring(&quot;ACGT&quot;)
&quot;TGCTCCTC&quot;</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p><code>chars</code> can be any collection of characters, of type <code>Char</code> or <code>UInt8</code> (more efficient), provided <a href="Random.html#Base.rand"><code>rand</code></a> can randomly pick characters from it.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/misc.jl#L43-L67">source</a></section></article><h2 id="Subsequences,-permutations-and-shuffling"><a class="docs-heading-anchor" href="#Subsequences,-permutations-and-shuffling">Subsequences, permutations and shuffling</a><a id="Subsequences,-permutations-and-shuffling-1"></a><a class="docs-heading-anchor-permalink" href="#Subsequences,-permutations-and-shuffling" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.randsubseq" href="#Random.randsubseq"><code>Random.randsubseq</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">randsubseq([rng=default_rng(),] A, p) -&gt; Vector</code></pre><p>Return a vector consisting of a random subsequence of the given array <code>A</code>, where each element of <code>A</code> is included (in order) with independent probability <code>p</code>. (Complexity is linear in <code>p*length(A)</code>, so this function is efficient even if <code>p</code> is small and <code>A</code> is large.) Technically, this process is known as &quot;Bernoulli sampling&quot; of <code>A</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; randsubseq(Xoshiro(123), 1:8, 0.3)
2-element Vector{Int64}:
 4
 7</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/misc.jl#L160-L175">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.randsubseq!" href="#Random.randsubseq!"><code>Random.randsubseq!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">randsubseq!([rng=default_rng(),] S, A, p)</code></pre><p>Like <a href="Random.html#Random.randsubseq"><code>randsubseq</code></a>, but the results are stored in <code>S</code> (which is resized as needed).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; S = Int64[];

julia&gt; randsubseq!(Xoshiro(123), S, 1:8, 0.3)
2-element Vector{Int64}:
 4
 7

julia&gt; S
2-element Vector{Int64}:
 4
 7</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/misc.jl#L134-L154">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.randperm" href="#Random.randperm"><code>Random.randperm</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">randperm([rng=default_rng(),] n::Integer)</code></pre><p>Construct a random permutation of length <code>n</code>. The optional <code>rng</code> argument specifies a random number generator (see <a href="Random.html#Random-Numbers">Random Numbers</a>). The element type of the result is the same as the type of <code>n</code>.</p><p>To randomly permute an arbitrary vector, see <a href="Random.html#Random.shuffle"><code>shuffle</code></a> or <a href="Random.html#Random.shuffle!"><code>shuffle!</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p>In Julia 1.1 <code>randperm</code> returns a vector <code>v</code> with <code>eltype(v) == typeof(n)</code> while in Julia 1.0 <code>eltype(v) == Int</code>.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; randperm(Xoshiro(123), 4)
4-element Vector{Int64}:
 1
 4
 2
 3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/misc.jl#L271-L295">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.randperm!" href="#Random.randperm!"><code>Random.randperm!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">randperm!([rng=default_rng(),] A::Array{&lt;:Integer})</code></pre><p>Construct in <code>A</code> a random permutation of length <code>length(A)</code>. The optional <code>rng</code> argument specifies a random number generator (see <a href="Random.html#Random-Numbers">Random Numbers</a>). To randomly permute an arbitrary vector, see <a href="Random.html#Random.shuffle"><code>shuffle</code></a> or <a href="Random.html#Random.shuffle!"><code>shuffle!</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; randperm!(Xoshiro(123), Vector{Int}(undef, 4))
4-element Vector{Int64}:
 1
 4
 2
 3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/misc.jl#L299-L316">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.randcycle" href="#Random.randcycle"><code>Random.randcycle</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">randcycle([rng=default_rng(),] n::Integer)</code></pre><p>Construct a random cyclic permutation of length <code>n</code>. The optional <code>rng</code> argument specifies a random number generator, see <a href="Random.html#Random-Numbers">Random Numbers</a>. The element type of the result is the same as the type of <code>n</code>.</p><p>Here, a &quot;cyclic permutation&quot; means that all of the elements lie within a single cycle.  If <code>n &gt; 0</code>, there are <span>$(n-1)!$</span> possible cyclic permutations, which are sampled uniformly.  If <code>n == 0</code>, <code>randcycle</code> returns an empty vector.</p><p><a href="Random.html#Random.randcycle!"><code>randcycle!</code></a> is an in-place variant of this function.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p>In Julia 1.1 and above, <code>randcycle</code> returns a vector <code>v</code> with <code>eltype(v) == typeof(n)</code> while in Julia 1.0 <code>eltype(v) == Int</code>.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; randcycle(Xoshiro(123), 6)
6-element Vector{Int64}:
 5
 4
 2
 6
 3
 1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/misc.jl#L340-L368">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.randcycle!" href="#Random.randcycle!"><code>Random.randcycle!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">randcycle!([rng=default_rng(),] A::Array{&lt;:Integer})</code></pre><p>Construct in <code>A</code> a random cyclic permutation of length <code>n = length(A)</code>. The optional <code>rng</code> argument specifies a random number generator, see <a href="Random.html#Random-Numbers">Random Numbers</a>.</p><p>Here, a &quot;cyclic permutation&quot; means that all of the elements lie within a single cycle. If <code>A</code> is nonempty (<code>n &gt; 0</code>), there are <span>$(n-1)!$</span> possible cyclic permutations, which are sampled uniformly.  If <code>A</code> is empty, <code>randcycle!</code> leaves it unchanged.</p><p><a href="Random.html#Random.randcycle"><code>randcycle</code></a> is a variant of this function that allocates a new vector.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; randcycle!(Xoshiro(123), Vector{Int}(undef, 6))
6-element Vector{Int64}:
 5
 4
 2
 6
 3
 1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/misc.jl#L372-L396">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.shuffle" href="#Random.shuffle"><code>Random.shuffle</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">shuffle([rng=default_rng(),] v::AbstractArray)</code></pre><p>Return a randomly permuted copy of <code>v</code>. The optional <code>rng</code> argument specifies a random number generator (see <a href="Random.html#Random-Numbers">Random Numbers</a>). To permute <code>v</code> in-place, see <a href="Random.html#Random.shuffle!"><code>shuffle!</code></a>. To obtain randomly permuted indices, see <a href="Random.html#Random.randperm"><code>randperm</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; shuffle(Xoshiro(123), Vector(1:10))
10-element Vector{Int64}:
  5
  4
  2
  3
  6
 10
  8
  1
  9
  7</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/misc.jl#L240-L263">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.shuffle!" href="#Random.shuffle!"><code>Random.shuffle!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">shuffle!([rng=default_rng(),] v::AbstractArray)</code></pre><p>In-place version of <a href="Random.html#Random.shuffle"><code>shuffle</code></a>: randomly permute <code>v</code> in-place, optionally supplying the random-number generator <code>rng</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; shuffle!(Xoshiro(123), Vector(1:10))
10-element Vector{Int64}:
  5
  4
  2
  3
  6
 10
  8
  1
  9
  7</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/misc.jl#L186-L207">source</a></section></article><h2 id="Generators-(creation-and-seeding)"><a class="docs-heading-anchor" href="#Generators-(creation-and-seeding)">Generators (creation and seeding)</a><a id="Generators-(creation-and-seeding)-1"></a><a class="docs-heading-anchor-permalink" href="#Generators-(creation-and-seeding)" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.default_rng" href="#Random.default_rng"><code>Random.default_rng</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Random.default_rng() -&gt; rng</code></pre><p>Return the default global random number generator (RNG), which is used by <code>rand</code>-related functions when no explicit RNG is provided.</p><p>When the <code>Random</code> module is loaded, the default RNG is <em>randomly</em> seeded, via <a href="Random.html#Random.seed!"><code>Random.seed!()</code></a>: this means that each time a new julia session is started, the first call to <code>rand()</code> produces a different result, unless <code>seed!(seed)</code> is called first.</p><p>It is thread-safe: distinct threads can safely call <code>rand</code>-related functions on <code>default_rng()</code> concurrently, e.g. <code>rand(default_rng())</code>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The type of the default RNG is an implementation detail. Across different versions of Julia, you should not expect the default RNG to always have the same type, nor that it will produce the same stream of random numbers for a given seed.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p>This function was introduced in Julia 1.3.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/RNGs.jl#L383-L403">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.seed!" href="#Random.seed!"><code>Random.seed!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">seed!([rng=default_rng()], seed) -&gt; rng
seed!([rng=default_rng()]) -&gt; rng</code></pre><p>Reseed the random number generator: <code>rng</code> will give a reproducible sequence of numbers if and only if a <code>seed</code> is provided. Some RNGs don&#39;t accept a seed, like <code>RandomDevice</code>. After the call to <code>seed!</code>, <code>rng</code> is equivalent to a newly created object initialized with the same seed. The types of accepted seeds depend on the type of <code>rng</code>, but in general, integer seeds should work.</p><p>If <code>rng</code> is not specified, it defaults to seeding the state of the shared task-local generator.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Random.seed!(1234);

julia&gt; x1 = rand(2)
2-element Vector{Float64}:
 0.32597672886359486
 0.****************

julia&gt; Random.seed!(1234);

julia&gt; x2 = rand(2)
2-element Vector{Float64}:
 0.32597672886359486
 0.****************

julia&gt; x1 == x2
true

julia&gt; rng = Xoshiro(1234); rand(rng, 2) == x1
true

julia&gt; Xoshiro(1) == Random.seed!(rng, 1)
true

julia&gt; rand(Random.seed!(rng), Bool) # not reproducible
true

julia&gt; rand(Random.seed!(rng), Bool) # not reproducible either
false

julia&gt; rand(Xoshiro(), Bool) # not reproducible either
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/Random.jl#L408-L457">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.AbstractRNG" href="#Random.AbstractRNG"><code>Random.AbstractRNG</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AbstractRNG</code></pre><p>Supertype for random number generators such as <a href="Random.html#Random.MersenneTwister"><code>MersenneTwister</code></a> and <a href="Random.html#Random.RandomDevice"><code>RandomDevice</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/Random.jl#L36-L40">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.TaskLocalRNG" href="#Random.TaskLocalRNG"><code>Random.TaskLocalRNG</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">TaskLocalRNG</code></pre><p>The <code>TaskLocalRNG</code> has state that is local to its task, not its thread. It is seeded upon task creation, from the state of its parent task, but without advancing the state of the parent&#39;s RNG.</p><p>As an upside, the <code>TaskLocalRNG</code> is pretty fast, and permits reproducible multithreaded simulations (barring race conditions), independent of scheduler decisions. As long as the number of threads is not used to make decisions on task creation, simulation results are also independent of the number of available threads / CPUs. The random stream should not depend on hardware specifics, up to endianness and possibly word size.</p><p>Using or seeding the RNG of any other task than the one returned by <code>current_task()</code> is undefined behavior: it will work most of the time, and may sometimes fail silently.</p><p>When seeding <code>TaskLocalRNG()</code> with <a href="Random.html#Random.seed!"><code>seed!</code></a>, the passed seed, if any, may be any integer.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>Seeding <code>TaskLocalRNG()</code> with a negative integer seed requires at least Julia 1.11.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.10</header><div class="admonition-body"><p>Task creation no longer advances the parent task&#39;s RNG state as of Julia 1.10.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/Xoshiro.jl#L184-L209">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.Xoshiro" href="#Random.Xoshiro"><code>Random.Xoshiro</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Xoshiro(seed::Union{Integer, AbstractString})
Xoshiro()</code></pre><p>Xoshiro256++ is a fast pseudorandom number generator described by David Blackman and Sebastiano Vigna in &quot;Scrambled Linear Pseudorandom Number Generators&quot;, ACM Trans. Math. Softw., 2021. Reference implementation is available at https://prng.di.unimi.it</p><p>Apart from the high speed, Xoshiro has a small memory footprint, making it suitable for applications where many different random states need to be held for long time.</p><p>Julia&#39;s Xoshiro implementation has a bulk-generation mode; this seeds new virtual PRNGs from the parent, and uses SIMD to generate in parallel (i.e. the bulk stream consists of multiple interleaved xoshiro instances). The virtual PRNGs are discarded once the bulk request has been serviced (and should cause no heap allocations).</p><p>If no seed is provided, a randomly generated one is created (using entropy from the system). See the <a href="Random.html#Random.seed!"><code>seed!</code></a> function for reseeding an already existing <code>Xoshiro</code> object.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>Passing a negative integer seed requires at least Julia 1.11.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; using Random

julia&gt; rng = Xoshiro(1234);

julia&gt; x1 = rand(rng, 2)
2-element Vector{Float64}:
 0.32597672886359486
 0.****************

julia&gt; rng = Xoshiro(1234);

julia&gt; x2 = rand(rng, 2)
2-element Vector{Float64}:
 0.32597672886359486
 0.****************

julia&gt; x1 == x2
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/Xoshiro.jl#L6-L51">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.MersenneTwister" href="#Random.MersenneTwister"><code>Random.MersenneTwister</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">MersenneTwister(seed)
MersenneTwister()</code></pre><p>Create a <code>MersenneTwister</code> RNG object. Different RNG objects can have their own seeds, which may be useful for generating different streams of random numbers. The <code>seed</code> may be an integer, a string, or a vector of <code>UInt32</code> integers. If no seed is provided, a randomly generated one is created (using entropy from the system). See the <a href="Random.html#Random.seed!"><code>seed!</code></a> function for reseeding an already existing <code>MersenneTwister</code> object.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>Passing a negative integer seed requires at least Julia 1.11.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; rng = MersenneTwister(123);

julia&gt; x1 = rand(rng, 2)
2-element Vector{Float64}:
 0.37453777969575874
 0.8735343642013971

julia&gt; x2 = rand(MersenneTwister(123), 2)
2-element Vector{Float64}:
 0.37453777969575874
 0.8735343642013971

julia&gt; x1 == x2
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/RNGs.jl#L79-L110">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.RandomDevice" href="#Random.RandomDevice"><code>Random.RandomDevice</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">RandomDevice()</code></pre><p>Create a <code>RandomDevice</code> RNG object. Two such objects will always generate different streams of random numbers. The entropy is obtained from the operating system.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/RNGs.jl#L6-L12">source</a></section></article><h2 id="rand-api-hook"><a class="docs-heading-anchor" href="#rand-api-hook">Hooking into the <code>Random</code> API</a><a id="rand-api-hook-1"></a><a class="docs-heading-anchor-permalink" href="#rand-api-hook" title="Permalink"></a></h2><p>There are two mostly orthogonal ways to extend <code>Random</code> functionalities:</p><ol><li>generating random values of custom types</li><li>creating new generators</li></ol><p>The API for 1) is quite functional, but is relatively recent so it may still have to evolve in subsequent releases of the <code>Random</code> module. For example, it&#39;s typically sufficient to implement one <code>rand</code> method in order to have all other usual methods work automatically.</p><p>The API for 2) is still rudimentary, and may require more work than strictly necessary from the implementor, in order to support usual types of generated values.</p><h3 id="Generating-random-values-of-custom-types"><a class="docs-heading-anchor" href="#Generating-random-values-of-custom-types">Generating random values of custom types</a><a id="Generating-random-values-of-custom-types-1"></a><a class="docs-heading-anchor-permalink" href="#Generating-random-values-of-custom-types" title="Permalink"></a></h3><p>Generating random values for some distributions may involve various trade-offs. <em>Pre-computed</em> values, such as an <a href="https://en.wikipedia.org/wiki/Alias_method">alias table</a> for discrete distributions, or <a href="https://en.wikipedia.org/wiki/Rejection_sampling">“squeezing” functions</a> for univariate distributions, can speed up sampling considerably. How much information should be pre-computed can depend on the number of values we plan to draw from a distribution. Also, some random number generators can have certain properties that various algorithms may want to exploit.</p><p>The <code>Random</code> module defines a customizable framework for obtaining random values that can address these issues. Each invocation of <code>rand</code> generates a <em>sampler</em> which can be customized with the above trade-offs in mind, by adding methods to <code>Sampler</code>, which in turn can dispatch on the random number generator, the object that characterizes the distribution, and a suggestion for the number of repetitions. Currently, for the latter, <code>Val{1}</code> (for a single sample) and <code>Val{Inf}</code> (for an arbitrary number) are used, with <code>Random.Repetition</code> an alias for both.</p><p>The object returned by <code>Sampler</code> is then used to generate the random values. When implementing the random generation interface for a value <code>X</code> that can be sampled from, the implementor should define the method</p><pre><code class="language-julia hljs">rand(rng, sampler)</code></pre><p>for the particular <code>sampler</code> returned by <code>Sampler(rng, X, repetition)</code>.</p><p>Samplers can be arbitrary values that implement <code>rand(rng, sampler)</code>, but for most applications the following predefined samplers may be sufficient:</p><ol><li><p><code>SamplerType{T}()</code> can be used for implementing samplers that draw from type <code>T</code> (e.g. <code>rand(Int)</code>). This is the default returned by <code>Sampler</code> for <em>types</em>.</p></li><li><p><code>SamplerTrivial(self)</code> is a simple wrapper for <code>self</code>, which can be accessed with <code>[]</code>. This is the recommended sampler when no pre-computed information is needed (e.g. <code>rand(1:3)</code>), and is the default returned by <code>Sampler</code> for <em>values</em>.</p></li><li><p><code>SamplerSimple(self, data)</code> also contains the additional <code>data</code> field, which can be used to store arbitrary pre-computed values, which should be computed in a <em>custom method</em> of <code>Sampler</code>.</p></li></ol><p>We provide examples for each of these. We assume here that the choice of algorithm is independent of the RNG, so we use <code>AbstractRNG</code> in our signatures.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.Sampler" href="#Random.Sampler"><code>Random.Sampler</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Sampler(rng, x, repetition = Val(Inf))</code></pre><p>Return a sampler object that can be used to generate random values from <code>rng</code> for <code>x</code>.</p><p>When <code>sp = Sampler(rng, x, repetition)</code>, <code>rand(rng, sp)</code> will be used to draw random values, and should be defined accordingly.</p><p><code>repetition</code> can be <code>Val(1)</code> or <code>Val(Inf)</code>, and should be used as a suggestion for deciding the amount of precomputation, if applicable.</p><p><a href="Random.html#Random.SamplerType"><code>Random.SamplerType</code></a> and <a href="Random.html#Random.SamplerTrivial"><code>Random.SamplerTrivial</code></a> are default fallbacks for <em>types</em> and <em>values</em>, respectively. <a href="Random.html#Random.SamplerSimple"><code>Random.SamplerSimple</code></a> can be used to store pre-computed values without defining extra types for only this purpose.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/Random.jl#L126-L140">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.SamplerType" href="#Random.SamplerType"><code>Random.SamplerType</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SamplerType{T}()</code></pre><p>A sampler for types, containing no other information. The default fallback for <code>Sampler</code> when called with types.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/Random.jl#L156-L161">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.SamplerTrivial" href="#Random.SamplerTrivial"><code>Random.SamplerTrivial</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SamplerTrivial(x)</code></pre><p>Create a sampler that just wraps the given value <code>x</code>. This is the default fall-back for values. The <code>eltype</code> of this sampler is equal to <code>eltype(x)</code>.</p><p>The recommended use case is sampling from values without precomputed data.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/Random.jl#L172-L180">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Random.SamplerSimple" href="#Random.SamplerSimple"><code>Random.SamplerSimple</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SamplerSimple(x, data)</code></pre><p>Create a sampler that wraps the given value <code>x</code> and the <code>data</code>. The <code>eltype</code> of this sampler is equal to <code>eltype(x)</code>.</p><p>The recommended use case is sampling from values with precomputed data.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Random/src/Random.jl#L193-L200">source</a></section></article><p>Decoupling pre-computation from actually generating the values is part of the API, and is also available to the user. As an example, assume that <code>rand(rng, 1:20)</code> has to be called repeatedly in a loop: the way to take advantage of this decoupling is as follows:</p><pre><code class="language-julia hljs">rng = Xoshiro()
sp = Random.Sampler(rng, 1:20) # or Random.Sampler(Xoshiro, 1:20)
for x in X
    n = rand(rng, sp) # similar to n = rand(rng, 1:20)
    # use n
end</code></pre><p>This is the mechanism that is also used in the standard library, e.g. by the default implementation of random array generation (like in <code>rand(1:20, 10)</code>).</p><h4 id="Generating-values-from-a-type"><a class="docs-heading-anchor" href="#Generating-values-from-a-type">Generating values from a type</a><a id="Generating-values-from-a-type-1"></a><a class="docs-heading-anchor-permalink" href="#Generating-values-from-a-type" title="Permalink"></a></h4><p>Given a type <code>T</code>, it&#39;s currently assumed that if <code>rand(T)</code> is defined, an object of type <code>T</code> will be produced. <code>SamplerType</code> is the <em>default sampler for types</em>. In order to define random generation of values of type <code>T</code>, the <code>rand(rng::AbstractRNG, ::Random.SamplerType{T})</code> method should be defined, and should return values what <code>rand(rng, T)</code> is expected to return.</p><p>Let&#39;s take the following example: we implement a <code>Die</code> type, with a variable number <code>n</code> of sides, numbered from <code>1</code> to <code>n</code>. We want <code>rand(Die)</code> to produce a <code>Die</code> with a random number of up to 20 sides (and at least 4):</p><pre><code class="language-julia hljs">struct Die
    nsides::Int # number of sides
end

Random.rand(rng::AbstractRNG, ::Random.SamplerType{Die}) = Die(rand(rng, 4:20))

# output
</code></pre><p>Scalar and array methods for <code>Die</code> now work as expected:</p><pre><code class="language-julia-repl hljs">julia&gt; rand(Die)
Die(5)

julia&gt; rand(Xoshiro(0), Die)
Die(10)

julia&gt; rand(Die, 3)
3-element Vector{Die}:
 Die(9)
 Die(15)
 Die(14)

julia&gt; a = Vector{Die}(undef, 3); rand!(a)
3-element Vector{Die}:
 Die(19)
 Die(7)
 Die(17)</code></pre><h4 id="A-simple-sampler-without-pre-computed-data"><a class="docs-heading-anchor" href="#A-simple-sampler-without-pre-computed-data">A simple sampler without pre-computed data</a><a id="A-simple-sampler-without-pre-computed-data-1"></a><a class="docs-heading-anchor-permalink" href="#A-simple-sampler-without-pre-computed-data" title="Permalink"></a></h4><p>Here we define a sampler for a collection. If no pre-computed data is required, it can be implemented with a <code>SamplerTrivial</code> sampler, which is in fact the <em>default fallback for values</em>.</p><p>In order to define random generation out of objects of type <code>S</code>, the following method should be defined: <code>rand(rng::AbstractRNG, sp::Random.SamplerTrivial{S})</code>. Here, <code>sp</code> simply wraps an object of type <code>S</code>, which can be accessed via <code>sp[]</code>. Continuing the <code>Die</code> example, we want now to define <code>rand(d::Die)</code> to produce an <code>Int</code> corresponding to one of <code>d</code>&#39;s sides:</p><pre><code class="language-julia-repl hljs">julia&gt; Random.rand(rng::AbstractRNG, d::Random.SamplerTrivial{Die}) = rand(rng, 1:d[].nsides);

julia&gt; rand(Die(4))
1

julia&gt; rand(Die(4), 3)
3-element Vector{Any}:
 2
 3
 3</code></pre><p>Given a collection type <code>S</code>, it&#39;s currently assumed that if <code>rand(::S)</code> is defined, an object of type <code>eltype(S)</code> will be produced. In the last example, a <code>Vector{Any}</code> is produced; the reason is that <code>eltype(Die) == Any</code>. The remedy is to define <code>Base.eltype(::Type{Die}) = Int</code>.</p><h4 id="Generating-values-for-an-AbstractFloat-type"><a class="docs-heading-anchor" href="#Generating-values-for-an-AbstractFloat-type">Generating values for an <code>AbstractFloat</code> type</a><a id="Generating-values-for-an-AbstractFloat-type-1"></a><a class="docs-heading-anchor-permalink" href="#Generating-values-for-an-AbstractFloat-type" title="Permalink"></a></h4><p><code>AbstractFloat</code> types are special-cased, because by default random values are not produced in the whole type domain, but rather in <code>[0,1)</code>. The following method should be implemented for <code>T &lt;: AbstractFloat</code>: <code>Random.rand(::AbstractRNG, ::Random.SamplerTrivial{Random.CloseOpen01{T}})</code></p><h4 id="An-optimized-sampler-with-pre-computed-data"><a class="docs-heading-anchor" href="#An-optimized-sampler-with-pre-computed-data">An optimized sampler with pre-computed data</a><a id="An-optimized-sampler-with-pre-computed-data-1"></a><a class="docs-heading-anchor-permalink" href="#An-optimized-sampler-with-pre-computed-data" title="Permalink"></a></h4><p>Consider a discrete distribution, where numbers <code>1:n</code> are drawn with given probabilities that sum to one. When many values are needed from this distribution, the fastest method is using an <a href="https://en.wikipedia.org/wiki/Alias_method">alias table</a>. We don&#39;t provide the algorithm for building such a table here, but suppose it is available in <code>make_alias_table(probabilities)</code> instead, and <code>draw_number(rng, alias_table)</code> can be used to draw a random number from it.</p><p>Suppose that the distribution is described by</p><pre><code class="language-julia hljs">struct DiscreteDistribution{V &lt;: AbstractVector}
    probabilities::V
end</code></pre><p>and that we <em>always</em> want to build an alias table, regardless of the number of values needed (we learn how to customize this below). The methods</p><pre><code class="language-julia hljs">Random.eltype(::Type{&lt;:DiscreteDistribution}) = Int

function Random.Sampler(::Type{&lt;:AbstractRNG}, distribution::DiscreteDistribution, ::Repetition)
    SamplerSimple(distribution, make_alias_table(distribution.probabilities))
end</code></pre><p>should be defined to return a sampler with pre-computed data, then</p><pre><code class="language-julia hljs">function rand(rng::AbstractRNG, sp::SamplerSimple{&lt;:DiscreteDistribution})
    draw_number(rng, sp.data)
end</code></pre><p>will be used to draw the values.</p><h4 id="Custom-sampler-types"><a class="docs-heading-anchor" href="#Custom-sampler-types">Custom sampler types</a><a id="Custom-sampler-types-1"></a><a class="docs-heading-anchor-permalink" href="#Custom-sampler-types" title="Permalink"></a></h4><p>The <code>SamplerSimple</code> type is sufficient for most use cases with precomputed data. However, in order to demonstrate how to use custom sampler types, here we implement something similar to <code>SamplerSimple</code>.</p><p>Going back to our <code>Die</code> example: <code>rand(::Die)</code> uses random generation from a range, so there is an opportunity for this optimization. We call our custom sampler <code>SamplerDie</code>.</p><pre><code class="language-julia hljs">import Random: Sampler, rand

struct SamplerDie &lt;: Sampler{Int} # generates values of type Int
    die::Die
    sp::Sampler{Int} # this is an abstract type, so this could be improved
end

Sampler(RNG::Type{&lt;:AbstractRNG}, die::Die, r::Random.Repetition) =
    SamplerDie(die, Sampler(RNG, 1:die.nsides, r))
# the `r` parameter will be explained later on

rand(rng::AbstractRNG, sp::SamplerDie) = rand(rng, sp.sp)</code></pre><p>It&#39;s now possible to get a sampler with <code>sp = Sampler(rng, die)</code>, and use <code>sp</code> instead of <code>die</code> in any <code>rand</code> call involving <code>rng</code>. In the simplistic example above, <code>die</code> doesn&#39;t need to be stored in <code>SamplerDie</code> but this is often the case in practice.</p><p>Of course, this pattern is so frequent that the helper type used above, namely <code>Random.SamplerSimple</code>, is available, saving us the definition of <code>SamplerDie</code>: we could have implemented our decoupling with:</p><pre><code class="language-julia hljs">Sampler(RNG::Type{&lt;:AbstractRNG}, die::Die, r::Random.Repetition) =
    SamplerSimple(die, Sampler(RNG, 1:die.nsides, r))

rand(rng::AbstractRNG, sp::SamplerSimple{Die}) = rand(rng, sp.data)</code></pre><p>Here, <code>sp.data</code> refers to the second parameter in the call to the <code>SamplerSimple</code> constructor (in this case equal to <code>Sampler(rng, 1:die.nsides, r)</code>), while the <code>Die</code> object can be accessed via <code>sp[]</code>.</p><p>Like <code>SamplerDie</code>, any custom sampler must be a subtype of <code>Sampler{T}</code> where <code>T</code> is the type of the generated values. Note that <code>SamplerSimple(x, data) isa Sampler{eltype(x)}</code>, so this constrains what the first argument to <code>SamplerSimple</code> can be (it&#39;s recommended to use <code>SamplerSimple</code> like in the <code>Die</code> example, where <code>x</code> is simply forwarded while defining a <code>Sampler</code> method). Similarly, <code>SamplerTrivial(x) isa Sampler{eltype(x)}</code>.</p><p>Another helper type is currently available for other cases, <code>Random.SamplerTag</code>, but is considered as internal API, and can break at any time without proper deprecations.</p><h4 id="Using-distinct-algorithms-for-scalar-or-array-generation"><a class="docs-heading-anchor" href="#Using-distinct-algorithms-for-scalar-or-array-generation">Using distinct algorithms for scalar or array generation</a><a id="Using-distinct-algorithms-for-scalar-or-array-generation-1"></a><a class="docs-heading-anchor-permalink" href="#Using-distinct-algorithms-for-scalar-or-array-generation" title="Permalink"></a></h4><p>In some cases, whether one wants to generate only a handful of values or a large number of values will have an impact on the choice of algorithm. This is handled with the third parameter of the <code>Sampler</code> constructor. Let&#39;s assume we defined two helper types for <code>Die</code>, say <code>SamplerDie1</code> which should be used to generate only few random values, and <code>SamplerDieMany</code> for many values. We can use those types as follows:</p><pre><code class="language-julia hljs">Sampler(RNG::Type{&lt;:AbstractRNG}, die::Die, ::Val{1}) = SamplerDie1(...)
Sampler(RNG::Type{&lt;:AbstractRNG}, die::Die, ::Val{Inf}) = SamplerDieMany(...)</code></pre><p>Of course, <code>rand</code> must also be defined on those types (i.e. <code>rand(::AbstractRNG, ::SamplerDie1)</code> and <code>rand(::AbstractRNG, ::SamplerDieMany)</code>). Note that, as usual, <code>SamplerTrivial</code> and <code>SamplerSimple</code> can be used if custom types are not necessary.</p><p>Note: <code>Sampler(rng, x)</code> is simply a shorthand for <code>Sampler(rng, x, Val(Inf))</code>, and <code>Random.Repetition</code> is an alias for <code>Union{Val{1}, Val{Inf}}</code>.</p><h3 id="Creating-new-generators"><a class="docs-heading-anchor" href="#Creating-new-generators">Creating new generators</a><a id="Creating-new-generators-1"></a><a class="docs-heading-anchor-permalink" href="#Creating-new-generators" title="Permalink"></a></h3><p>The API is not clearly defined yet, but as a rule of thumb:</p><ol><li>any <code>rand</code> method producing &quot;basic&quot; types (<code>isbitstype</code> integer and floating types in <code>Base</code>) should be defined for this specific RNG, if they are needed;</li><li>other documented <code>rand</code> methods accepting an <code>AbstractRNG</code> should work out of the box, (provided the methods from 1) what are relied on are implemented), but can of course be specialized for this RNG if there is room for optimization;</li><li><code>copy</code> for pseudo-RNGs should return an independent copy that generates the exact same random sequence as the original from that point when called in the same way. When this is not feasible (e.g. hardware-based RNGs), <code>copy</code> must not be implemented.</li></ol><p>Concerning 1), a <code>rand</code> method may happen to work automatically, but it&#39;s not officially supported and may break without warnings in a subsequent release.</p><p>To define a new <code>rand</code> method for an hypothetical <code>MyRNG</code> generator, and a value specification <code>s</code> (e.g. <code>s == Int</code>, or <code>s == 1:10</code>) of type <code>S==typeof(s)</code> or <code>S==Type{s}</code> if <code>s</code> is a type, the same two methods as we saw before must be defined:</p><ol><li><code>Sampler(::Type{MyRNG}, ::S, ::Repetition)</code>, which returns an object of type say <code>SamplerS</code></li><li><code>rand(rng::MyRNG, sp::SamplerS)</code></li></ol><p>It can happen that <code>Sampler(rng::AbstractRNG, ::S, ::Repetition)</code> is already defined in the <code>Random</code> module. It would then be possible to skip step 1) in practice (if one wants to specialize generation for this particular RNG type), but the corresponding <code>SamplerS</code> type is considered as internal detail, and may be changed without warning.</p><h4 id="Specializing-array-generation"><a class="docs-heading-anchor" href="#Specializing-array-generation">Specializing array generation</a><a id="Specializing-array-generation-1"></a><a class="docs-heading-anchor-permalink" href="#Specializing-array-generation" title="Permalink"></a></h4><p>In some cases, for a given RNG type, generating an array of random values can be more efficient with a specialized method than by merely using the decoupling technique explained before. This is for example the case for <code>MersenneTwister</code>, which natively writes random values in an array.</p><p>To implement this specialization for <code>MyRNG</code> and for a specification <code>s</code>, producing elements of type <code>S</code>, the following method can be defined: <code>rand!(rng::MyRNG, a::AbstractArray{S}, ::SamplerS)</code>, where <code>SamplerS</code> is the type of the sampler returned by <code>Sampler(MyRNG, s, Val(Inf))</code>. Instead of <code>AbstractArray</code>, it&#39;s possible to implement the functionality only for a subtype, e.g. <code>Array{S}</code>. The non-mutating array method of <code>rand</code> will automatically call this specialization internally.</p><h1 id="Reproducibility"><a class="docs-heading-anchor" href="#Reproducibility">Reproducibility</a><a id="Reproducibility-1"></a><a class="docs-heading-anchor-permalink" href="#Reproducibility" title="Permalink"></a></h1><p>By using an RNG parameter initialized with a given seed, you can reproduce the same pseudorandom number sequence when running your program multiple times. However, a minor release of Julia (e.g. 1.3 to 1.4) <em>may change</em> the sequence of pseudorandom numbers generated from a specific seed, in particular if <code>MersenneTwister</code> is used. (Even if the sequence produced by a low-level function like <a href="Random.html#Base.rand"><code>rand</code></a> does not change, the output of higher-level functions like <a href="Random.html#Random.randsubseq"><code>randsubseq</code></a> may change due to algorithm updates.) Rationale: guaranteeing that pseudorandom streams never change prohibits many algorithmic improvements.</p><p>If you need to guarantee exact reproducibility of random data, it is advisable to simply <em>save the data</em> (e.g. as a supplementary attachment in a scientific publication). (You can also, of course, specify a particular Julia version and package manifest, especially if you require bit reproducibility.)</p><p>Software tests that rely on <em>specific</em> &quot;random&quot; data should also generally either save the data, embed it into the test code, or use third-party packages like <a href="https://github.com/JuliaRandom/StableRNGs.jl">StableRNGs.jl</a>. On the other hand, tests that should pass for <em>most</em> random data (e.g. testing <code>A \ (A*x) ≈ x</code> for a random matrix <code>A = randn(n,n)</code>) can use an RNG with a fixed seed to ensure that simply running the test many times does not encounter a failure due to very improbable data (e.g. an extremely ill-conditioned matrix).</p><p>The statistical <em>distribution</em> from which random samples are drawn <em>is</em> guaranteed to be the same across any minor Julia releases.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="REPL.html">« The Julia REPL</a><a class="docs-footer-nextpage" href="SHA.html">SHA »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
