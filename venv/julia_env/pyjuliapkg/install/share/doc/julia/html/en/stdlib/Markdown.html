<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Markdown · The Julia Language</title><meta name="title" content="Markdown · The Julia Language"/><meta property="og:title" content="Markdown · The Julia Language"/><meta property="twitter:title" content="Markdown · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li class="is-active"><a class="tocitem" href="Markdown.html">Markdown</a><ul class="internal"><li><a class="tocitem" href="#Inline-elements"><span>Inline elements</span></a></li><li><a class="tocitem" href="#Toplevel-elements"><span>Toplevel elements</span></a></li><li><a class="tocitem" href="#stdlib-markdown-literals"><span>Markdown String Literals</span></a></li><li><a class="tocitem" href="#Markdown-Syntax-Extensions"><span>Markdown Syntax Extensions</span></a></li><li><a class="tocitem" href="#stdlib-markdown-api"><span>API reference</span></a></li></ul></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="Markdown.html">Markdown</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="Markdown.html">Markdown</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/stdlib/Markdown/docs/src/index.md" title="View source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="markdown_stdlib"><a class="docs-heading-anchor" href="#markdown_stdlib">Markdown</a><a id="markdown_stdlib-1"></a><a class="docs-heading-anchor-permalink" href="#markdown_stdlib" title="Permalink"></a></h1><p>This section describes Julia&#39;s markdown syntax, which is enabled by the Markdown standard library. The following Markdown elements are supported:</p><h2 id="Inline-elements"><a class="docs-heading-anchor" href="#Inline-elements">Inline elements</a><a id="Inline-elements-1"></a><a class="docs-heading-anchor-permalink" href="#Inline-elements" title="Permalink"></a></h2><p>Here &quot;inline&quot; refers to elements that can be found within blocks of text, i.e. paragraphs. These include the following elements.</p><h3 id="Bold"><a class="docs-heading-anchor" href="#Bold">Bold</a><a id="Bold-1"></a><a class="docs-heading-anchor-permalink" href="#Bold" title="Permalink"></a></h3><p>Surround words with two asterisks, <code>**</code>, to display the enclosed text in boldface.</p><pre><code class="nohighlight hljs">A paragraph containing a **bold** word.</code></pre><h3 id="Italics"><a class="docs-heading-anchor" href="#Italics">Italics</a><a id="Italics-1"></a><a class="docs-heading-anchor-permalink" href="#Italics" title="Permalink"></a></h3><p>Surround words with one asterisk, <code>*</code>, to display the enclosed text in italics.</p><pre><code class="nohighlight hljs">A paragraph containing an *italicized* word.</code></pre><h3 id="Literals"><a class="docs-heading-anchor" href="#Literals">Literals</a><a id="Literals-1"></a><a class="docs-heading-anchor-permalink" href="#Literals" title="Permalink"></a></h3><p>Surround text that should be displayed exactly as written with single backticks, <code>`</code> .</p><pre><code class="nohighlight hljs">A paragraph containing a `literal` word.</code></pre><p>Literals should be used when writing text that refers to names of variables, functions, or other parts of a Julia program.</p><div class="admonition is-success"><header class="admonition-header">Tip</header><div class="admonition-body"><p>To include a backtick character within literal text use three backticks rather than one to enclose the text.</p><pre><code class="nohighlight hljs">A paragraph containing ``` `backtick` characters ```.</code></pre><p>By extension any odd number of backticks may be used to enclose a lesser number of backticks.</p></div></div><h3 id="\\LaTeX"><a class="docs-heading-anchor" href="#\\LaTeX"><span>$\LaTeX$</span></a><a id="\\LaTeX-1"></a><a class="docs-heading-anchor-permalink" href="#\\LaTeX" title="Permalink"></a></h3><p>Surround text that should be displayed as mathematics using <span>$\LaTeX$</span> syntax with double backticks, <code>``</code> .</p><pre><code class="nohighlight hljs">A paragraph containing some ``\LaTeX`` markup.</code></pre><div class="admonition is-success"><header class="admonition-header">Tip</header><div class="admonition-body"><p>As with literals in the previous section, if literal backticks need to be written within double backticks use an even number greater than two. Note that if a single literal backtick needs to be included within <span>$\LaTeX$</span> markup then two enclosing backticks is sufficient.</p></div></div><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The <code>\</code> character should be escaped appropriately if the text is embedded in a Julia source code, for example, <code>&quot;``\\LaTeX`` syntax in a docstring.&quot;</code>, since it is interpreted as a string literal. Alternatively, in order to avoid escaping, it is possible to use the <code>raw</code> string macro together with the <code>@doc</code> macro:</p><pre><code class="nohighlight hljs">@doc raw&quot;``\LaTeX`` syntax in a docstring.&quot; functionname</code></pre></div></div><h3 id="Links"><a class="docs-heading-anchor" href="#Links">Links</a><a id="Links-1"></a><a class="docs-heading-anchor-permalink" href="#Links" title="Permalink"></a></h3><p>Links to either external or internal targets can be written using the following syntax, where the text enclosed in square brackets, <code>[ ]</code>, is the name of the link and the text enclosed in parentheses, <code>( )</code>, is the URL.</p><pre><code class="nohighlight hljs">A paragraph containing a link to [Julia](https://www.julialang.org).</code></pre><p>It&#39;s also possible to add cross-references to other documented functions/methods/variables within the Julia documentation itself. For example:</p><pre><code class="language-julia hljs">&quot;&quot;&quot;
    tryparse(type, str; base)

Like [`parse`](@ref), but returns either a value of the requested type,
or [`nothing`](@ref) if the string does not contain a valid number.
&quot;&quot;&quot;</code></pre><p>This will create a link in the generated docs to the <a href="../base/numbers.html#Base.parse"><code>parse</code></a> documentation (which has more information about what this function actually does), and to the <a href="../base/constants.html#Core.nothing"><code>nothing</code></a> documentation. It&#39;s good to include cross references to mutating/non-mutating versions of a function, or to highlight a difference between two similar-seeming functions.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The above cross referencing is <em>not</em> a Markdown feature, and relies on <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a>, which is used to build base Julia&#39;s documentation.</p></div></div><h3 id="Footnote-references"><a class="docs-heading-anchor" href="#Footnote-references">Footnote references</a><a id="Footnote-references-1"></a><a class="docs-heading-anchor-permalink" href="#Footnote-references" title="Permalink"></a></h3><p>Named and numbered footnote references can be written using the following syntax. A footnote name must be a single alphanumeric word containing no punctuation.</p><pre><code class="nohighlight hljs">A paragraph containing a numbered footnote [^1] and a named one [^named].</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The text associated with a footnote can be written anywhere within the same page as the footnote reference. The syntax used to define the footnote text is discussed in the <a href="Markdown.html#Footnotes">Footnotes</a> section below.</p></div></div><h2 id="Toplevel-elements"><a class="docs-heading-anchor" href="#Toplevel-elements">Toplevel elements</a><a id="Toplevel-elements-1"></a><a class="docs-heading-anchor-permalink" href="#Toplevel-elements" title="Permalink"></a></h2><p>The following elements can be written either at the &quot;toplevel&quot; of a document or within another &quot;toplevel&quot; element.</p><h3 id="Paragraphs"><a class="docs-heading-anchor" href="#Paragraphs">Paragraphs</a><a id="Paragraphs-1"></a><a class="docs-heading-anchor-permalink" href="#Paragraphs" title="Permalink"></a></h3><p>A paragraph is a block of plain text, possibly containing any number of inline elements defined in the <a href="Markdown.html#Inline-elements">Inline elements</a> section above, with one or more blank lines above and below it.</p><pre><code class="nohighlight hljs">This is a paragraph.

And this is *another* paragraph containing some emphasized text.
A new line, but still part of the same paragraph.</code></pre><h3 id="Headers"><a class="docs-heading-anchor" href="#Headers">Headers</a><a id="Headers-1"></a><a class="docs-heading-anchor-permalink" href="#Headers" title="Permalink"></a></h3><p>A document can be split up into different sections using headers. Headers use the following syntax:</p><pre><code class="language-julia hljs"># Level One
## Level Two
### Level Three
#### Level Four
##### Level Five
###### Level Six</code></pre><p>A header line can contain any inline syntax in the same way as a paragraph can.</p><div class="admonition is-success"><header class="admonition-header">Tip</header><div class="admonition-body"><p>Try to avoid using too many levels of header within a single document. A heavily nested document may be indicative of a need to restructure it or split it into several pages covering separate topics.</p></div></div><h3 id="Code-blocks"><a class="docs-heading-anchor" href="#Code-blocks">Code blocks</a><a id="Code-blocks-1"></a><a class="docs-heading-anchor-permalink" href="#Code-blocks" title="Permalink"></a></h3><p>Source code can be displayed as a literal block using an indent of four spaces or one tab as shown in the following example.</p><pre><code class="nohighlight hljs">This is a paragraph.

    function func(x)
        # ...
    end

Another paragraph.</code></pre><p>Additionally, code blocks can be enclosed using triple backticks with an optional &quot;language&quot; to specify how a block of code should be highlighted.</p><pre><code class="nohighlight hljs">A code block without a &quot;language&quot;:

```
function func(x)
    # ...
end
```

and another one with the &quot;language&quot; specified as `julia`:

```julia
function func(x)
    # ...
end
```</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>&quot;Fenced&quot; code blocks, as shown in the last example, should be preferred over indented code blocks since there is no way to specify what language an indented code block is written in.</p></div></div><h3 id="Block-quotes"><a class="docs-heading-anchor" href="#Block-quotes">Block quotes</a><a id="Block-quotes-1"></a><a class="docs-heading-anchor-permalink" href="#Block-quotes" title="Permalink"></a></h3><p>Text from external sources, such as quotations from books or websites, can be quoted using <code>&gt;</code> characters prepended to each line of the quote as follows.</p><pre><code class="nohighlight hljs">Here&#39;s a quote:

&gt; Julia is a high-level, high-performance dynamic programming language for
&gt; technical computing, with syntax that is familiar to users of other
&gt; technical computing environments.</code></pre><p>Note that a single space must appear after the <code>&gt;</code> character on each line. Quoted blocks may themselves contain other toplevel or inline elements.</p><h3 id="Images"><a class="docs-heading-anchor" href="#Images">Images</a><a id="Images-1"></a><a class="docs-heading-anchor-permalink" href="#Images" title="Permalink"></a></h3><p>The syntax for images is similar to the link syntax mentioned above. Prepending a <code>!</code> character to a link will display an image from the specified URL rather than a link to it.</p><pre><code class="language-julia hljs">![alternative text](link/to/image.png)</code></pre><h3 id="Lists"><a class="docs-heading-anchor" href="#Lists">Lists</a><a id="Lists-1"></a><a class="docs-heading-anchor-permalink" href="#Lists" title="Permalink"></a></h3><p>Unordered lists can be written by prepending each item in a list with either <code>*</code>, <code>+</code>, or <code>-</code>.</p><pre><code class="nohighlight hljs">A list of items:

  * item one
  * item two
  * item three</code></pre><p>Note the two spaces before each <code>*</code> and the single space after each one.</p><p>Lists can contain other nested toplevel elements such as lists, code blocks, or quoteblocks. A blank line should be left between each list item when including any toplevel elements within a list.</p><pre><code class="nohighlight hljs">Another list:

  * item one

  * item two

    ```
    f(x) = x
    ```

  * And a sublist:

      + sub-item one
      + sub-item two</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The contents of each item in the list must line up with the first line of the item. In the above example the fenced code block must be indented by four spaces to align with the <code>i</code> in <code>item two</code>.</p></div></div><p>Ordered lists are written by replacing the &quot;bullet&quot; character, either <code>*</code>, <code>+</code>, or <code>-</code>, with a positive integer followed by either <code>.</code> or <code>)</code>.</p><pre><code class="nohighlight hljs">Two ordered lists:

 1. item one
 2. item two
 3. item three

 5) item five
 6) item six
 7) item seven</code></pre><p>An ordered list may start from a number other than one, as in the second list of the above example, where it is numbered from five. As with unordered lists, ordered lists can contain nested toplevel elements.</p><h3 id="Display-equations"><a class="docs-heading-anchor" href="#Display-equations">Display equations</a><a id="Display-equations-1"></a><a class="docs-heading-anchor-permalink" href="#Display-equations" title="Permalink"></a></h3><p>Large <span>$\LaTeX$</span> equations that do not fit inline within a paragraph may be written as display equations using a fenced code block with the &quot;language&quot; <code>math</code> as in the example below.</p><pre><code class="language-julia hljs">```math
f(a) = \frac{1}{2\pi}\int_{0}^{2\pi} (\alpha+R\cos(\theta))d\theta
```</code></pre><h3 id="Footnotes"><a class="docs-heading-anchor" href="#Footnotes">Footnotes</a><a id="Footnotes-1"></a><a class="docs-heading-anchor-permalink" href="#Footnotes" title="Permalink"></a></h3><p>This syntax is paired with the inline syntax for <a href="Markdown.html#Footnote-references">Footnote references</a>. Make sure to read that section as well.</p><p>Footnote text is defined using the following syntax, which is similar to footnote reference syntax, aside from the <code>:</code> character that is appended to the footnote label.</p><pre><code class="nohighlight hljs">[^1]: Numbered footnote text.

[^note]:

    Named footnote text containing several toplevel elements
    indented by 4 spaces or one tab.

      * item one
      * item two
      * item three

    ```julia
    function func(x)
        # ...
    end
    ```</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>No checks are done during parsing to make sure that all footnote references have matching footnotes.</p></div></div><h3 id="Horizontal-rules"><a class="docs-heading-anchor" href="#Horizontal-rules">Horizontal rules</a><a id="Horizontal-rules-1"></a><a class="docs-heading-anchor-permalink" href="#Horizontal-rules" title="Permalink"></a></h3><p>The equivalent of an <code>&lt;hr&gt;</code> HTML tag can be achieved using three hyphens (<code>---</code>). For example:</p><pre><code class="nohighlight hljs">Text above the line.

---

And text below the line.</code></pre><h3 id="Tables"><a class="docs-heading-anchor" href="#Tables">Tables</a><a id="Tables-1"></a><a class="docs-heading-anchor-permalink" href="#Tables" title="Permalink"></a></h3><p>Basic tables can be written using the syntax described below. Note that markdown tables have limited features and cannot contain nested toplevel elements unlike other elements discussed above – only inline elements are allowed. Tables must always contain a header row with column names. Cells cannot span multiple rows or columns of the table.</p><pre><code class="nohighlight hljs">| Column One | Column Two | Column Three |
|:---------- | ---------- |:------------:|
| Row `1`    | Column `2` |              |
| *Row* 2    | **Row** 2  | Column ``3`` |</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>As illustrated in the above example each column of <code>|</code> characters must be aligned vertically.</p><p>A <code>:</code> character on either end of a column&#39;s header separator (the row containing <code>-</code> characters) specifies whether the row is left-aligned, right-aligned, or (when <code>:</code> appears on both ends) center-aligned. Providing no <code>:</code> characters will default to right-aligning the column.</p></div></div><h3 id="Admonitions"><a class="docs-heading-anchor" href="#Admonitions">Admonitions</a><a id="Admonitions-1"></a><a class="docs-heading-anchor-permalink" href="#Admonitions" title="Permalink"></a></h3><p>Specially formatted blocks, known as admonitions, can be used to highlight particular remarks. They can be defined using the following <code>!!!</code> syntax:</p><pre><code class="nohighlight hljs">!!! note

    This is the content of the note.
    It is indented by 4 spaces. A tab would work as well.

!!! warning &quot;Beware!&quot;

    And this is another one.

    This warning admonition has a custom title: `&quot;Beware!&quot;`.</code></pre><p>The first word after <code>!!!</code> declares the type of the admonition. There are standard admonition types that should produce special styling. Namely (in order of decreasing severity): <code>danger</code>, <code>warning</code>, <code>info</code>/<code>note</code>, and <code>tip</code>.</p><p>You can also use your own admonition types, as long as the type name only contains lowercase Latin characters (a-z). For example, you could have a <code>terminology</code> block like this:</p><pre><code class="nohighlight hljs">!!! terminology &quot;julia vs Julia&quot;

    Strictly speaking, &quot;Julia&quot; refers to the language,
    and &quot;julia&quot; to the standard implementation.</code></pre><p>However, unless the code rendering the Markdown special-cases that particular admonition type, it will get the default styling.</p><p>A custom title for the box can be provided as a string (in double quotes) after the admonition type. If no title text is specified after the admonition type, then the type name will be used as the title (e.g. <code>&quot;Note&quot;</code> for the <code>note</code> admonition).</p><p>Admonitions, like most other toplevel elements, can contain other toplevel elements (e.g. lists, images).</p><h2 id="stdlib-markdown-literals"><a class="docs-heading-anchor" href="#stdlib-markdown-literals">Markdown String Literals</a><a id="stdlib-markdown-literals-1"></a><a class="docs-heading-anchor-permalink" href="#stdlib-markdown-literals" title="Permalink"></a></h2><p>The <code>md&quot;&quot;</code> macro allows you to embed Markdown strings directly into your Julia code. This macro is designed to simplify the inclusion of Markdown-formatted text within your Julia source files.</p><h3 id="Usage"><a class="docs-heading-anchor" href="#Usage">Usage</a><a id="Usage-1"></a><a class="docs-heading-anchor-permalink" href="#Usage" title="Permalink"></a></h3><pre><code class="language-julia hljs">result = md&quot;This is a **custom** Markdown string with [a link](http://example.com).&quot;</code></pre><h2 id="Markdown-Syntax-Extensions"><a class="docs-heading-anchor" href="#Markdown-Syntax-Extensions">Markdown Syntax Extensions</a><a id="Markdown-Syntax-Extensions-1"></a><a class="docs-heading-anchor-permalink" href="#Markdown-Syntax-Extensions" title="Permalink"></a></h2><p>Julia&#39;s markdown supports interpolation in a very similar way to basic string literals, with the difference that it will store the object itself in the Markdown tree (as opposed to converting it to a string). When the Markdown content is rendered the usual <code>show</code> methods will be called, and these can be overridden as usual. This design allows the Markdown to be extended with arbitrarily complex features (such as references) without cluttering the basic syntax.</p><p>In principle, the Markdown parser itself can also be arbitrarily extended by packages, or an entirely custom flavour of Markdown can be used, but this should generally be unnecessary.</p><h2 id="stdlib-markdown-api"><a class="docs-heading-anchor" href="#stdlib-markdown-api">API reference</a><a id="stdlib-markdown-api-1"></a><a class="docs-heading-anchor-permalink" href="#stdlib-markdown-api" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Markdown.MD" href="#Markdown.MD"><code>Markdown.MD</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">MD</code></pre><p><code>MD</code> represents a Markdown document. Note that the <code>MD</code> constructor should not generally be used directly, since it constructs the internal data structures. Instead, you can construct <code>MD</code> objects using the exported macros <a href="Markdown.html#Markdown.@md_str"><code>@md_str</code></a> and <a href="Markdown.html#Markdown.@doc_str"><code>@doc_str</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Markdown/src/parse/parse.jl#L3-L9">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Markdown.@md_str" href="#Markdown.@md_str"><code>Markdown.@md_str</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@md_str -&gt; MD</code></pre><p>Parse the given string as Markdown text and return a corresponding <a href="Markdown.html#Markdown.MD"><code>MD</code></a> object.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; s = md&quot;# Hello, world!&quot;
  Hello, world!
  ≡≡≡≡≡≡≡≡≡≡≡≡≡

julia&gt; typeof(s)
Markdown.MD
</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Markdown/src/Markdown.jl#L47-L62">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Markdown.@doc_str" href="#Markdown.@doc_str"><code>Markdown.@doc_str</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@doc_str -&gt; MD</code></pre><p>Parse the given string as Markdown text, add line and module information and return a corresponding <a href="Markdown.html#Markdown.MD"><code>MD</code></a> object.</p><p><code>@doc_str</code> can be used in conjunction with the <a href="../base/base.html#Base.Docs"><code>Base.Docs</code></a> module. Please also refer to the manual section on <a href="../manual/documentation.html#man-documentation">documentation</a> for more information.</p><p><strong>Examples</strong></p><pre><code class="nohighlight hljs">julia&gt; s = doc&quot;f(x) = 2*x&quot;
  f(x) = 2*x

julia&gt; typeof(s)
Markdown.MD
</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Markdown/src/Markdown.jl#L74-L92">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Markdown.html" href="#Markdown.html"><code>Markdown.html</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">html([io::IO], md)</code></pre><p>Output the contents of the Markdown object <code>md</code> in HTML format, either writing to an (optional) <code>io</code> stream or returning a string.</p><p>One can alternatively use <code>show(io, &quot;text/html&quot;, md)</code> or <code>repr(&quot;text/html&quot;, md)</code>, which differ in that they wrap the output in a <code>&lt;div class=&quot;markdown&quot;&gt; ... &lt;/div&gt;</code> element.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; html(md&quot;hello _world_&quot;)
&quot;&lt;p&gt;hello &lt;em&gt;world&lt;/em&gt;&lt;/p&gt;\n&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Markdown/src/render/html.jl#L185-L199">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Markdown.latex" href="#Markdown.latex"><code>Markdown.latex</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">latex([io::IO], md)</code></pre><p>Output the contents of the Markdown object <code>md</code> in LaTeX format, either writing to an (optional) <code>io</code> stream or returning a string.</p><p>One can alternatively use <code>show(io, &quot;text/latex&quot;, md)</code> or <code>repr(&quot;text/latex&quot;, md)</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; latex(md&quot;hello _world_&quot;)
&quot;hello \\emph{world}\n\n&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Markdown/src/render/latex.jl#L170-L183">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="Logging.html">« Logging</a><a class="docs-footer-nextpage" href="Mmap.html">Memory-mapped I/O »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
