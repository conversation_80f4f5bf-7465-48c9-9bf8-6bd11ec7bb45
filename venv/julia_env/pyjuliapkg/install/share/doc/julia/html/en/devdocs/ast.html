<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Julia ASTs · The Julia Language</title><meta name="title" content="Julia ASTs · The Julia Language"/><meta property="og:title" content="Julia ASTs · The Julia Language"/><meta property="twitter:title" content="Julia ASTs · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox" checked/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="init.html">Initialization of the Julia runtime</a></li><li class="is-active"><a class="tocitem" href="ast.html">Julia ASTs</a><ul class="internal"><li><a class="tocitem" href="#Surface-syntax-AST"><span>Surface syntax AST</span></a></li><li><a class="tocitem" href="#Lowered-form"><span>Lowered form</span></a></li></ul></li><li><a class="tocitem" href="types.html">More about types</a></li><li><a class="tocitem" href="object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="functions.html">Julia Functions</a></li><li><a class="tocitem" href="cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="subarrays.html">SubArrays</a></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Documentation of Julia&#39;s Internals</a></li><li class="is-active"><a href="ast.html">Julia ASTs</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="ast.html">Julia ASTs</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/ast.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Julia-ASTs"><a class="docs-heading-anchor" href="#Julia-ASTs">Julia ASTs</a><a id="Julia-ASTs-1"></a><a class="docs-heading-anchor-permalink" href="#Julia-ASTs" title="Permalink"></a></h1><p>Julia has two representations of code. First there is a surface syntax AST returned by the parser (e.g. the <a href="../base/base.html#Base.Meta.parse-Tuple{AbstractString, Int64}"><code>Meta.parse</code></a> function), and manipulated by macros. It is a structured representation of code as it is written, constructed by <code>julia-parser.scm</code> from a character stream. Next there is a lowered form, or IR (intermediate representation), which is used by type inference and code generation. In the lowered form there are fewer types of nodes, all macros are expanded, and all control flow is converted to explicit branches and sequences of statements. The lowered form is constructed by <code>julia-syntax.scm</code>.</p><p>First we will focus on the AST, since it is needed to write macros.</p><h2 id="Surface-syntax-AST"><a class="docs-heading-anchor" href="#Surface-syntax-AST">Surface syntax AST</a><a id="Surface-syntax-AST-1"></a><a class="docs-heading-anchor-permalink" href="#Surface-syntax-AST" title="Permalink"></a></h2><p>Front end ASTs consist almost entirely of <a href="../base/base.html#Core.Expr"><code>Expr</code></a>s and atoms (e.g. symbols, numbers). There is generally a different expression head for each visually distinct syntactic form. Examples will be given in s-expression syntax. Each parenthesized list corresponds to an Expr, where the first element is the head. For example <code>(call f x)</code> corresponds to <code>Expr(:call, :f, :x)</code> in Julia.</p><h3 id="Calls"><a class="docs-heading-anchor" href="#Calls">Calls</a><a id="Calls-1"></a><a class="docs-heading-anchor-permalink" href="#Calls" title="Permalink"></a></h3><table><tr><th style="text-align: left">Input</th><th style="text-align: left">AST</th></tr><tr><td style="text-align: left"><code>f(x)</code></td><td style="text-align: left"><code>(call f x)</code></td></tr><tr><td style="text-align: left"><code>f(x, y=1, z=2)</code></td><td style="text-align: left"><code>(call f x (kw y 1) (kw z 2))</code></td></tr><tr><td style="text-align: left"><code>f(x; y=1)</code></td><td style="text-align: left"><code>(call f (parameters (kw y 1)) x)</code></td></tr><tr><td style="text-align: left"><code>f(x...)</code></td><td style="text-align: left"><code>(call f (... x))</code></td></tr></table><p><code>do</code> syntax:</p><pre><code class="language-julia hljs">f(x) do a,b
    body
end</code></pre><p>parses as <code>(do (call f x) (-&gt; (tuple a b) (block body)))</code>.</p><h3 id="Operators"><a class="docs-heading-anchor" href="#Operators">Operators</a><a id="Operators-1"></a><a class="docs-heading-anchor-permalink" href="#Operators" title="Permalink"></a></h3><p>Most uses of operators are just function calls, so they are parsed with the head <code>call</code>. However some operators are special forms (not necessarily function calls), and in those cases the operator itself is the expression head. In julia-parser.scm these are referred to as &quot;syntactic operators&quot;. Some operators (<code>+</code> and <code>*</code>) use N-ary parsing; chained calls are parsed as a single N-argument call. Finally, chains of comparisons have their own special expression structure.</p><table><tr><th style="text-align: left">Input</th><th style="text-align: left">AST</th></tr><tr><td style="text-align: left"><code>x+y</code></td><td style="text-align: left"><code>(call + x y)</code></td></tr><tr><td style="text-align: left"><code>a+b+c+d</code></td><td style="text-align: left"><code>(call + a b c d)</code></td></tr><tr><td style="text-align: left"><code>2x</code></td><td style="text-align: left"><code>(call * 2 x)</code></td></tr><tr><td style="text-align: left"><code>a&amp;&amp;b</code></td><td style="text-align: left"><code>(&amp;&amp; a b)</code></td></tr><tr><td style="text-align: left"><code>x += 1</code></td><td style="text-align: left"><code>(+= x 1)</code></td></tr><tr><td style="text-align: left"><code>a ? 1 : 2</code></td><td style="text-align: left"><code>(if a 1 2)</code></td></tr><tr><td style="text-align: left"><code>a,b</code></td><td style="text-align: left"><code>(tuple a b)</code></td></tr><tr><td style="text-align: left"><code>a==b</code></td><td style="text-align: left"><code>(call == a b)</code></td></tr><tr><td style="text-align: left"><code>1&lt;i&lt;=n</code></td><td style="text-align: left"><code>(comparison 1 &lt; i &lt;= n)</code></td></tr><tr><td style="text-align: left"><code>a.b</code></td><td style="text-align: left"><code>(. a (quote b))</code></td></tr><tr><td style="text-align: left"><code>a.(b)</code></td><td style="text-align: left"><code>(. a (tuple b))</code></td></tr></table><h3 id="Bracketed-forms"><a class="docs-heading-anchor" href="#Bracketed-forms">Bracketed forms</a><a id="Bracketed-forms-1"></a><a class="docs-heading-anchor-permalink" href="#Bracketed-forms" title="Permalink"></a></h3><table><tr><th style="text-align: left">Input</th><th style="text-align: left">AST</th></tr><tr><td style="text-align: left"><code>a[i]</code></td><td style="text-align: left"><code>(ref a i)</code></td></tr><tr><td style="text-align: left"><code>t[i;j]</code></td><td style="text-align: left"><code>(typed_vcat t i j)</code></td></tr><tr><td style="text-align: left"><code>t[i j]</code></td><td style="text-align: left"><code>(typed_hcat t i j)</code></td></tr><tr><td style="text-align: left"><code>t[a b; c d]</code></td><td style="text-align: left"><code>(typed_vcat t (row a b) (row c d))</code></td></tr><tr><td style="text-align: left"><code>t[a b;;; c d]</code></td><td style="text-align: left"><code>(typed_ncat t 3 (row a b) (row c d))</code></td></tr><tr><td style="text-align: left"><code>a{b}</code></td><td style="text-align: left"><code>(curly a b)</code></td></tr><tr><td style="text-align: left"><code>a{b;c}</code></td><td style="text-align: left"><code>(curly a (parameters c) b)</code></td></tr><tr><td style="text-align: left"><code>[x]</code></td><td style="text-align: left"><code>(vect x)</code></td></tr><tr><td style="text-align: left"><code>[x,y]</code></td><td style="text-align: left"><code>(vect x y)</code></td></tr><tr><td style="text-align: left"><code>[x;y]</code></td><td style="text-align: left"><code>(vcat x y)</code></td></tr><tr><td style="text-align: left"><code>[x y]</code></td><td style="text-align: left"><code>(hcat x y)</code></td></tr><tr><td style="text-align: left"><code>[x y; z t]</code></td><td style="text-align: left"><code>(vcat (row x y) (row z t))</code></td></tr><tr><td style="text-align: left"><code>[x;y;; z;t;;;]</code></td><td style="text-align: left"><code>(ncat 3 (nrow 2 (nrow 1 x y) (nrow 1 z t)))</code></td></tr><tr><td style="text-align: left"><code>[x for y in z, a in b]</code></td><td style="text-align: left"><code>(comprehension (generator x (= y z) (= a b)))</code></td></tr><tr><td style="text-align: left"><code>T[x for y in z]</code></td><td style="text-align: left"><code>(typed_comprehension T (generator x (= y z)))</code></td></tr><tr><td style="text-align: left"><code>(a, b, c)</code></td><td style="text-align: left"><code>(tuple a b c)</code></td></tr><tr><td style="text-align: left"><code>(a; b; c)</code></td><td style="text-align: left"><code>(block a b c)</code></td></tr></table><h3 id="Macros"><a class="docs-heading-anchor" href="#Macros">Macros</a><a id="Macros-1"></a><a class="docs-heading-anchor-permalink" href="#Macros" title="Permalink"></a></h3><table><tr><th style="text-align: left">Input</th><th style="text-align: left">AST</th></tr><tr><td style="text-align: left"><code>@m x y</code></td><td style="text-align: left"><code>(macrocall @m (line) x y)</code></td></tr><tr><td style="text-align: left"><code>Base.@m x y</code></td><td style="text-align: left"><code>(macrocall (. Base (quote @m)) (line) x y)</code></td></tr><tr><td style="text-align: left"><code>@Base.m x y</code></td><td style="text-align: left"><code>(macrocall (. Base (quote @m)) (line) x y)</code></td></tr></table><h3 id="Strings"><a class="docs-heading-anchor" href="#Strings">Strings</a><a id="Strings-1"></a><a class="docs-heading-anchor-permalink" href="#Strings" title="Permalink"></a></h3><table><tr><th style="text-align: left">Input</th><th style="text-align: left">AST</th></tr><tr><td style="text-align: left"><code>&quot;a&quot;</code></td><td style="text-align: left"><code>&quot;a&quot;</code></td></tr><tr><td style="text-align: left"><code>x&quot;y&quot;</code></td><td style="text-align: left"><code>(macrocall @x_str (line) &quot;y&quot;)</code></td></tr><tr><td style="text-align: left"><code>x&quot;y&quot;z</code></td><td style="text-align: left"><code>(macrocall @x_str (line) &quot;y&quot; &quot;z&quot;)</code></td></tr><tr><td style="text-align: left"><code>&quot;x = $x&quot;</code></td><td style="text-align: left"><code>(string &quot;x = &quot; x)</code></td></tr><tr><td style="text-align: left"><code>`a b c`</code></td><td style="text-align: left"><code>(macrocall @cmd (line) &quot;a b c&quot;)</code></td></tr></table><p>Doc string syntax:</p><pre><code class="language-julia hljs">&quot;some docs&quot;
f(x) = x</code></pre><p>parses as <code>(macrocall (|.| Core &#39;@doc) (line) &quot;some docs&quot; (= (call f x) (block x)))</code>.</p><h3 id="Imports-and-such"><a class="docs-heading-anchor" href="#Imports-and-such">Imports and such</a><a id="Imports-and-such-1"></a><a class="docs-heading-anchor-permalink" href="#Imports-and-such" title="Permalink"></a></h3><table><tr><th style="text-align: left">Input</th><th style="text-align: left">AST</th></tr><tr><td style="text-align: left"><code>import a</code></td><td style="text-align: left"><code>(import (. a))</code></td></tr><tr><td style="text-align: left"><code>import a.b.c</code></td><td style="text-align: left"><code>(import (. a b c))</code></td></tr><tr><td style="text-align: left"><code>import ...a</code></td><td style="text-align: left"><code>(import (. . . . a))</code></td></tr><tr><td style="text-align: left"><code>import a.b, c.d</code></td><td style="text-align: left"><code>(import (. a b) (. c d))</code></td></tr><tr><td style="text-align: left"><code>import Base: x</code></td><td style="text-align: left"><code>(import (: (. Base) (. x)))</code></td></tr><tr><td style="text-align: left"><code>import Base: x, y</code></td><td style="text-align: left"><code>(import (: (. Base) (. x) (. y)))</code></td></tr><tr><td style="text-align: left"><code>export a, b</code></td><td style="text-align: left"><code>(export a b)</code></td></tr></table><p><code>using</code> has the same representation as <code>import</code>, but with expression head <code>:using</code> instead of <code>:import</code>.</p><h3 id="Numbers"><a class="docs-heading-anchor" href="#Numbers">Numbers</a><a id="Numbers-1"></a><a class="docs-heading-anchor-permalink" href="#Numbers" title="Permalink"></a></h3><p>Julia supports more number types than many scheme implementations, so not all numbers are represented directly as scheme numbers in the AST.</p><table><tr><th style="text-align: left">Input</th><th style="text-align: left">AST</th></tr><tr><td style="text-align: left"><code>11111111111111111111</code></td><td style="text-align: left"><code>(macrocall @int128_str nothing &quot;11111111111111111111&quot;)</code></td></tr><tr><td style="text-align: left"><code>0xfffffffffffffffff</code></td><td style="text-align: left"><code>(macrocall @uint128_str nothing &quot;0xfffffffffffffffff&quot;)</code></td></tr><tr><td style="text-align: left"><code>1111...many digits...</code></td><td style="text-align: left"><code>(macrocall @big_str nothing &quot;1111....&quot;)</code></td></tr></table><h3 id="Block-forms"><a class="docs-heading-anchor" href="#Block-forms">Block forms</a><a id="Block-forms-1"></a><a class="docs-heading-anchor-permalink" href="#Block-forms" title="Permalink"></a></h3><p>A block of statements is parsed as <code>(block stmt1 stmt2 ...)</code>.</p><p>If statement:</p><pre><code class="language-julia hljs">if a
    b
elseif c
    d
else
    e
end</code></pre><p>parses as:</p><pre><code class="nohighlight hljs">(if a (block (line 2) b)
    (elseif (block (line 3) c) (block (line 4) d)
            (block (line 6 e))))</code></pre><p>A <code>while</code> loop parses as <code>(while condition body)</code>.</p><p>A <code>for</code> loop parses as <code>(for (= var iter) body)</code>. If there is more than one iteration specification, they are parsed as a block: <code>(for (block (= v1 iter1) (= v2 iter2)) body)</code>.</p><p><code>break</code> and <code>continue</code> are parsed as 0-argument expressions <code>(break)</code> and <code>(continue)</code>.</p><p><code>let</code> is parsed as <code>(let (= var val) body)</code> or <code>(let (block (= var1 val1) (= var2 val2) ...) body)</code>, like <code>for</code> loops.</p><p>A basic function definition is parsed as <code>(function (call f x) body)</code>. A more complex example:</p><pre><code class="language-julia hljs">function f(x::T; k = 1) where T
    return x+1
end</code></pre><p>parses as:</p><pre><code class="nohighlight hljs">(function (where (call f (parameters (kw k 1))
                       (:: x T))
                 T)
          (block (line 2) (return (call + x 1))))</code></pre><p>Type definition:</p><pre><code class="language-julia hljs">mutable struct Foo{T&lt;:S}
    x::T
end</code></pre><p>parses as:</p><pre><code class="nohighlight hljs">(struct true (curly Foo (&lt;: T S))
        (block (line 2) (:: x T)))</code></pre><p>The first argument is a boolean telling whether the type is mutable.</p><p><code>try</code> blocks parse as <code>(try try_block var catch_block finally_block)</code>. If no variable is present after <code>catch</code>, <code>var</code> is <code>#f</code>. If there is no <code>finally</code> clause, then the last argument is not present.</p><h3 id="Quote-expressions"><a class="docs-heading-anchor" href="#Quote-expressions">Quote expressions</a><a id="Quote-expressions-1"></a><a class="docs-heading-anchor-permalink" href="#Quote-expressions" title="Permalink"></a></h3><p>Julia source syntax forms for code quoting (<code>quote</code> and <code>:( )</code>) support interpolation with <code>$</code>. In Lisp terminology, this means they are actually &quot;backquote&quot; or &quot;quasiquote&quot; forms. Internally, there is also a need for code quoting without interpolation. In Julia&#39;s scheme code, non-interpolating quote is represented with the expression head <code>inert</code>.</p><p><code>inert</code> expressions are converted to Julia <code>QuoteNode</code> objects. These objects wrap a single value of any type, and when evaluated simply return that value.</p><p>A <code>quote</code> expression whose argument is an atom also gets converted to a <code>QuoteNode</code>.</p><h3 id="Line-numbers"><a class="docs-heading-anchor" href="#Line-numbers">Line numbers</a><a id="Line-numbers-1"></a><a class="docs-heading-anchor-permalink" href="#Line-numbers" title="Permalink"></a></h3><p>Source location information is represented as <code>(line line_num file_name)</code> where the third component is optional (and omitted when the current line number, but not file name, changes).</p><p>These expressions are represented as <code>LineNumberNode</code>s in Julia.</p><h3 id="Macros-2"><a class="docs-heading-anchor" href="#Macros-2">Macros</a><a class="docs-heading-anchor-permalink" href="#Macros-2" title="Permalink"></a></h3><p>Macro hygiene is represented through the expression head pair <code>escape</code> and <code>hygienic-scope</code>. The result of a macro expansion is automatically wrapped in <code>(hygienic-scope block module)</code>, to represent the result of the new scope. The user can insert <code>(escape block)</code> inside to interpolate code from the caller.</p><h2 id="Lowered-form"><a class="docs-heading-anchor" href="#Lowered-form">Lowered form</a><a id="Lowered-form-1"></a><a class="docs-heading-anchor-permalink" href="#Lowered-form" title="Permalink"></a></h2><p>Lowered form (IR) is more important to the compiler, since it is used for type inference, optimizations like inlining, and code generation. It is also less obvious to the human, since it results from a significant rearrangement of the input syntax.</p><p>In addition to <code>Symbol</code>s and some number types, the following data types exist in lowered form:</p><ul><li><p><code>Expr</code></p><p>Has a node type indicated by the <code>head</code> field, and an <code>args</code> field which is a <code>Vector{Any}</code> of subexpressions. While almost every part of a surface AST is represented by an <code>Expr</code>, the IR uses only a limited number of <code>Expr</code>s, mostly for calls and some top-level-only forms.</p></li><li><p><code>SlotNumber</code></p><p>Identifies arguments and local variables by consecutive numbering. It has an integer-valued <code>id</code> field giving the slot index. The types of these slots can be found in the <code>slottypes</code> field of their <code>CodeInfo</code> object.</p></li><li><p><code>Argument</code></p><p>The same as <code>SlotNumber</code>, but appears only post-optimization. Indicates that the referenced slot is an argument of the enclosing function.</p></li><li><p><code>CodeInfo</code></p><p>Wraps the IR of a group of statements. Its <code>code</code> field is an array of expressions to execute.</p></li><li><p><code>GotoNode</code></p><p>Unconditional branch. The argument is the branch target, represented as an index in the code array to jump to.</p></li><li><p><code>GotoIfNot</code></p><p>Conditional branch. If the <code>cond</code> field evaluates to false, goes to the index identified by the <code>dest</code> field.</p></li><li><p><code>ReturnNode</code></p><p>Returns its argument (the <code>val</code> field) as the value of the enclosing function. If the <code>val</code> field is undefined, then this represents an unreachable statement.</p></li><li><p><code>QuoteNode</code></p><p>Wraps an arbitrary value to reference as data. For example, the function <code>f() = :a</code> contains a <code>QuoteNode</code> whose <code>value</code> field is the symbol <code>a</code>, in order to return the symbol itself instead of evaluating it.</p></li><li><p><code>GlobalRef</code></p><p>Refers to global variable <code>name</code> in module <code>mod</code>.</p></li><li><p><code>SSAValue</code></p><p>Refers to a consecutively-numbered (starting at 1) static single assignment (SSA) variable inserted by the compiler. The number (<code>id</code>) of an <code>SSAValue</code> is the code array index of the expression whose value it represents.</p></li><li><p><code>NewvarNode</code></p><p>Marks a point where a variable (slot) is created. This has the effect of resetting a variable to undefined.</p></li></ul><h3 id="Expr-types"><a class="docs-heading-anchor" href="#Expr-types"><code>Expr</code> types</a><a id="Expr-types-1"></a><a class="docs-heading-anchor-permalink" href="#Expr-types" title="Permalink"></a></h3><p>These symbols appear in the <code>head</code> field of <a href="../base/base.html#Core.Expr"><code>Expr</code></a>s in lowered form.</p><ul><li><p><code>call</code></p><p>Function call (dynamic dispatch). <code>args[1]</code> is the function to call, <code>args[2:end]</code> are the arguments.</p></li><li><p><code>invoke</code></p><p>Function call (static dispatch). <code>args[1]</code> is the MethodInstance to call, <code>args[2:end]</code> are the arguments (including the function that is being called, at <code>args[2]</code>).</p></li><li><p><code>static_parameter</code></p><p>Reference a static parameter by index.</p></li><li><p><code>=</code></p><p>Assignment. In the IR, the first argument is always a <code>SlotNumber</code> or a <code>GlobalRef</code>.</p></li><li><p><code>method</code></p><p>Adds a method to a generic function and assigns the result if necessary.</p><p>Has a 1-argument form and a 3-argument form. The 1-argument form arises from the syntax <code>function foo end</code>. In the 1-argument form, the argument is a symbol. If this symbol already names a function in the current scope, nothing happens. If the symbol is undefined, a new function is created and assigned to the identifier specified by the symbol. If the symbol is defined but names a non-function, an error is raised. The definition of &quot;names a function&quot; is that the binding is constant, and refers to an object of singleton type. The rationale for this is that an instance of a singleton type uniquely identifies the type to add the method to. When the type has fields, it wouldn&#39;t be clear whether the method was being added to the instance or its type.</p><p>The 3-argument form has the following arguments:</p><ul><li><p><code>args[1]</code></p><p>A function name, or <code>nothing</code> if unknown or unneeded. If a symbol, then the expression first behaves like the 1-argument form above. This argument is ignored from then on. It can be <code>nothing</code> when methods are added strictly by type, <code>(::T)(x) = x</code>, or when a method is being added to an existing function, <code>MyModule.f(x) = x</code>.</p></li><li><p><code>args[2]</code></p><p>A <code>SimpleVector</code> of argument type data. <code>args[2][1]</code> is a <code>SimpleVector</code> of the argument types, and <code>args[2][2]</code> is a <code>SimpleVector</code> of type variables corresponding to the method&#39;s static parameters.</p></li><li><p><code>args[3]</code></p><p>A <code>CodeInfo</code> of the method itself. For &quot;out of scope&quot; method definitions (adding a method to a function that also has methods defined in different scopes) this is an expression that evaluates to a <code>:lambda</code> expression.</p></li></ul></li><li><p><code>struct_type</code></p><p>A 7-argument expression that defines a new <code>struct</code>:</p><ul><li><p><code>args[1]</code></p><p>The name of the <code>struct</code></p></li><li><p><code>args[2]</code></p><p>A <code>call</code> expression that creates a <code>SimpleVector</code> specifying its parameters</p></li><li><p><code>args[3]</code></p><p>A <code>call</code> expression that creates a <code>SimpleVector</code> specifying its fieldnames</p></li><li><p><code>args[4]</code></p><p>A <code>Symbol</code>, <code>GlobalRef</code>, or <code>Expr</code> specifying the supertype (e.g., <code>:Integer</code>, <code>GlobalRef(Core, :Any)</code>, or <code>:(Core.apply_type(AbstractArray, T, N))</code>)</p></li><li><p><code>args[5]</code></p><p>A <code>call</code> expression that creates a <code>SimpleVector</code> specifying its fieldtypes</p></li><li><p><code>args[6]</code></p><p>A Bool, true if <code>mutable</code></p></li><li><p><code>args[7]</code></p><p>The number of arguments to initialize. This will be the number of fields, or the minimum number of fields called by an inner constructor&#39;s <code>new</code> statement.</p></li></ul></li><li><p><code>abstract_type</code></p><p>A 3-argument expression that defines a new abstract type. The arguments are the same as arguments 1, 2, and 4 of <code>struct_type</code> expressions.</p></li><li><p><code>primitive_type</code></p><p>A 4-argument expression that defines a new primitive type. Arguments 1, 2, and 4 are the same as <code>struct_type</code>. Argument 3 is the number of bits.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p><code>struct_type</code>, <code>abstract_type</code>, and <code>primitive_type</code> were removed in Julia 1.5 and replaced by calls to new builtins.</p></div></div></li><li><p><code>global</code></p><p>Declares a global binding.</p></li><li><p><code>const</code></p><p>Declares a (global) variable as constant.</p></li><li><p><code>new</code></p><p>Allocates a new struct-like object. First argument is the type. The <a href="../base/base.html#new"><code>new</code></a> pseudo-function is lowered to this, and the type is always inserted by the compiler.  This is very much an internal-only feature, and does no checking. Evaluating arbitrary <code>new</code> expressions can easily segfault.</p></li><li><p><code>splatnew</code></p><p>Similar to <code>new</code>, except field values are passed as a single tuple. Works similarly to <code>splat(new)</code> if <code>new</code> were a first-class function, hence the name.</p></li><li><p><code>isdefined</code></p><p><code>Expr(:isdefined, :x)</code> returns a Bool indicating whether <code>x</code> has already been defined in the current scope.</p></li><li><p><code>the_exception</code></p><p>Yields the caught exception inside a <code>catch</code> block, as returned by <code>jl_current_exception(ct)</code>.</p></li><li><p><code>enter</code></p><p>Enters an exception handler (<code>setjmp</code>). <code>args[1]</code> is the label of the catch block to jump to on error.  Yields a token which is consumed by <code>pop_exception</code>.</p></li><li><p><code>leave</code></p><p>Pop exception handlers. <code>args[1]</code> is the number of handlers to pop.</p></li><li><p><code>pop_exception</code></p><p>Pop the stack of current exceptions back to the state at the associated <code>enter</code> when leaving a catch block. <code>args[1]</code> contains the token from the associated <code>enter</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p><code>pop_exception</code> is new in Julia 1.1.</p></div></div></li><li><p><code>inbounds</code></p><p>Controls turning bounds checks on or off. A stack is maintained; if the first argument of this expression is true or false (<code>true</code> means bounds checks are disabled), it is pushed onto the stack. If the first argument is <code>:pop</code>, the stack is popped.</p></li><li><p><code>boundscheck</code></p><p>Has the value <code>false</code> if inlined into a section of code marked with <code>@inbounds</code>, otherwise has the value <code>true</code>.</p></li><li><p><code>loopinfo</code></p><p>Marks the end of the a loop. Contains metadata that is passed to <code>LowerSimdLoop</code> to either mark the inner loop of <code>@simd</code> expression, or to propagate information to LLVM loop passes.</p></li><li><p><code>copyast</code></p><p>Part of the implementation of quasi-quote. The argument is a surface syntax AST that is simply copied recursively and returned at run time.</p></li><li><p><code>meta</code></p><p>Metadata. <code>args[1]</code> is typically a symbol specifying the kind of metadata, and the rest of the arguments are free-form. The following kinds of metadata are commonly used:</p><ul><li><code>:inline</code> and <code>:noinline</code>: Inlining hints.</li></ul></li><li><p><code>foreigncall</code></p><p>Statically-computed container for <code>ccall</code> information. The fields are:</p><ul><li><p><code>args[1]</code> : name</p><p>The expression that&#39;ll be parsed for the foreign function.</p></li><li><p><code>args[2]::Type</code> : RT</p><p>The (literal) return type, computed statically when the containing method was defined.</p></li><li><p><code>args[3]::SimpleVector</code> (of Types) : AT</p><p>The (literal) vector of argument types, computed statically when the containing method was defined.</p></li><li><p><code>args[4]::Int</code> : nreq</p><p>The number of required arguments for a varargs function definition.</p></li><li><p><code>args[5]::QuoteNode{Symbol}</code> : calling convention</p><p>The calling convention for the call.</p></li><li><p><code>args[6:5+length(args[3])]</code> : arguments</p><p>The values for all the arguments (with types of each given in args[3]).</p></li><li><p><code>args[6+length(args[3])+1:end]</code> : gc-roots</p><p>The additional objects that may need to be gc-rooted for the duration of the call. See <a href="llvm.html#Working-with-LLVM">Working with LLVM</a> for where these are derived from and how they get handled.</p></li></ul></li><li><p><code>new_opaque_closure</code></p><p>Constructs a new opaque closure. The fields are:</p><ul><li><p><code>args[1]</code> : signature</p><p>The function signature of the opaque closure. Opaque closures don&#39;t participate in dispatch, but the input types can be restricted.</p></li><li><p><code>args[2]</code> : isva</p><p>Indicates whether the closure accepts varargs.</p></li><li><p><code>args[3]</code> : lb</p><p>Lower bound on the output type. (Defaults to <code>Union{}</code>)</p></li><li><p><code>args[4]</code> : ub</p><p>Upper bound on the output type. (Defaults to <code>Any</code>)</p></li><li><p><code>args[5]</code> : method</p><p>The actual method as an <code>opaque_closure_method</code> expression.</p></li><li><p><code>args[6:end]</code> : captures</p><p>The values captured by the opaque closure.</p></li></ul><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Opaque closures were added in Julia 1.7</p></div></div></li></ul><h3 id="ast-lowered-method"><a class="docs-heading-anchor" href="#ast-lowered-method">Method</a><a id="ast-lowered-method-1"></a><a class="docs-heading-anchor-permalink" href="#ast-lowered-method" title="Permalink"></a></h3><p>A unique&#39;d container describing the shared metadata for a single method.</p><ul><li><p><code>name</code>, <code>module</code>, <code>file</code>, <code>line</code>, <code>sig</code></p><p>Metadata to uniquely identify the method for the computer and the human.</p></li><li><p><code>ambig</code></p><p>Cache of other methods that may be ambiguous with this one.</p></li><li><p><code>specializations</code></p><p>Cache of all MethodInstance ever created for this Method, used to ensure uniqueness. Uniqueness is required for efficiency, especially for incremental precompile and tracking of method invalidation.</p></li><li><p><code>source</code></p><p>The original source code (if available, usually compressed).</p></li><li><p><code>generator</code></p><p>A callable object which can be executed to get specialized source for a specific method signature.</p></li><li><p><code>roots</code></p><p>Pointers to non-AST things that have been interpolated into the AST, required by compression of the AST, type-inference, or the generation of native code.</p></li><li><p><code>nargs</code>, <code>isva</code>, <code>called</code>, <code>is_for_opaque_closure</code>,</p><p>Descriptive bit-fields for the source code of this Method.</p></li><li><p><code>primary_world</code></p><p>The world age that &quot;owns&quot; this Method.</p></li></ul><h3 id="MethodInstance"><a class="docs-heading-anchor" href="#MethodInstance">MethodInstance</a><a id="MethodInstance-1"></a><a class="docs-heading-anchor-permalink" href="#MethodInstance" title="Permalink"></a></h3><p>A unique&#39;d container describing a single callable signature for a Method. See especially <a href="locks.html#Proper-maintenance-and-care-of-multi-threading-locks">Proper maintenance and care of multi-threading locks</a> for important details on how to modify these fields safely.</p><ul><li><p><code>specTypes</code></p><p>The primary key for this MethodInstance. Uniqueness is guaranteed through a <code>def.specializations</code> lookup.</p></li><li><p><code>def</code></p><p>The <code>Method</code> that this function describes a specialization of. Or a <code>Module</code>, if this is a top-level Lambda expanded in Module, and which is not part of a Method.</p></li><li><p><code>sparam_vals</code></p><p>The values of the static parameters in <code>specTypes</code>. For the <code>MethodInstance</code> at <code>Method.unspecialized</code>, this is the empty <code>SimpleVector</code>. But for a runtime <code>MethodInstance</code> from the <code>MethodTable</code> cache, this will always be defined and indexable.</p></li><li><p><code>uninferred</code></p><p>The uncompressed source code for a toplevel thunk. Additionally, for a generated function, this is one of many places that the source code might be found.</p></li><li><p><code>backedges</code></p><p>We store the reverse-list of cache dependencies for efficient tracking of incremental reanalysis/recompilation work that may be needed after a new method definitions. This works by keeping a list of the other <code>MethodInstance</code> that have been inferred or optimized to contain a possible call to this <code>MethodInstance</code>. Those optimization results might be stored somewhere in the <code>cache</code>, or it might have been the result of something we didn&#39;t want to cache, such as constant propagation. Thus we merge all of those backedges to various cache entries here (there&#39;s almost always only the one applicable cache entry with a sentinel value for max_world anyways).</p></li><li><p><code>cache</code></p><p>Cache of <code>CodeInstance</code> objects that share this template instantiation.</p></li></ul><h3 id="CodeInstance"><a class="docs-heading-anchor" href="#CodeInstance">CodeInstance</a><a id="CodeInstance-1"></a><a class="docs-heading-anchor-permalink" href="#CodeInstance" title="Permalink"></a></h3><ul><li><p><code>def</code></p><p>The <code>MethodInstance</code> that this cache entry is derived from.</p></li><li><p><code>owner</code></p><p>A token that represents the owner of this <code>CodeInstance</code>. Will use <code>jl_egal</code> to match.</p></li></ul><ul><li><p><code>rettype</code>/<code>rettype_const</code></p><p>The inferred return type for the <code>specFunctionObject</code> field, which (in most cases) is also the computed return type for the function in general.</p></li><li><p><code>inferred</code></p><p>May contain a cache of the inferred source for this function, or it could be set to <code>nothing</code> to just indicate <code>rettype</code> is inferred.</p></li><li><p><code>ftpr</code></p><p>The generic jlcall entry point.</p></li><li><p><code>jlcall_api</code></p><p>The ABI to use when calling <code>fptr</code>. Some significant ones include:</p><ul><li>0 - Not compiled yet</li><li>1 - <code>JL_CALLABLE</code> <code>jl_value_t *(*)(jl_function_t *f, jl_value_t *args[nargs], uint32_t nargs)</code></li><li>2 - Constant (value stored in <code>rettype_const</code>)</li><li>3 - With Static-parameters forwarded <code>jl_value_t *(*)(jl_svec_t *sparams, jl_function_t *f, jl_value_t *args[nargs], uint32_t nargs)</code></li><li>4 - Run in interpreter <code>jl_value_t *(*)(jl_method_instance_t *meth, jl_function_t *f, jl_value_t *args[nargs], uint32_t nargs)</code></li></ul></li><li><p><code>min_world</code> / <code>max_world</code></p><p>The range of world ages for which this method instance is valid to be called. If max_world is the special token value <code>-1</code>, the value is not yet known. It may continue to be used until we encounter a backedge that requires us to reconsider.</p></li></ul><h3 id="CodeInfo"><a class="docs-heading-anchor" href="#CodeInfo">CodeInfo</a><a id="CodeInfo-1"></a><a class="docs-heading-anchor-permalink" href="#CodeInfo" title="Permalink"></a></h3><p>A (usually temporary) container for holding lowered source code.</p><ul><li><p><code>code</code></p><p>An <code>Any</code> array of statements</p></li><li><p><code>slotnames</code></p><p>An array of symbols giving names for each slot (argument or local variable).</p></li><li><p><code>slotflags</code></p><p>A <code>UInt8</code> array of slot properties, represented as bit flags:</p><ul><li>0x02 - assigned (only false if there are <em>no</em> assignment statements with this var on the left)</li><li>0x08 - used (if there is any read or write of the slot)</li><li>0x10 - statically assigned once</li><li>0x20 - might be used before assigned. This flag is only valid after type inference.</li></ul></li><li><p><code>ssavaluetypes</code></p><p>Either an array or an <code>Int</code>.</p><p>If an <code>Int</code>, it gives the number of compiler-inserted temporary locations in the function (the length of <code>code</code> array). If an array, specifies a type for each location.</p></li><li><p><code>ssaflags</code></p><p>Statement-level 32 bits flags for each expression in the function. See the definition of <code>jl_code_info_t</code> in julia.h for more details.</p></li><li><p><code>linetable</code></p><p>An array of source location objects</p></li><li><p><code>codelocs</code></p><p>An array of integer indices into the <code>linetable</code>, giving the location associated with each statement.</p></li></ul><p>Optional Fields:</p><ul><li><p><code>slottypes</code></p><p>An array of types for the slots.</p></li><li><p><code>rettype</code></p><p>The inferred return type of the lowered form (IR). Default value is <code>Any</code>.</p></li><li><p><code>method_for_inference_limit_heuristics</code></p><p>The <code>method_for_inference_heuristics</code> will expand the given method&#39;s generator if necessary during inference.</p></li><li><p><code>parent</code></p><p>The <code>MethodInstance</code> that &quot;owns&quot; this object (if applicable).</p></li><li><p><code>edges</code></p><p>Forward edges to method instances that must be invalidated.</p></li><li><p><code>min_world</code>/<code>max_world</code></p><p>The range of world ages for which this code was valid at the time when it had been inferred.</p></li></ul><p>Boolean properties:</p><ul><li><p><code>inferred</code></p><p>Whether this has been produced by type inference.</p></li><li><p><code>inlineable</code></p><p>Whether this should be eligible for inlining.</p></li><li><p><code>propagate_inbounds</code></p><p>Whether this should propagate <code>@inbounds</code> when inlined for the purpose of eliding <code>@boundscheck</code> blocks.</p></li></ul><p><code>UInt8</code> settings:</p><ul><li><p><code>constprop</code></p><ul><li>0 = use heuristic</li><li>1 = aggressive</li><li>2 = none</li></ul></li><li><p><code>purity</code> Constructed from 5 bit flags:</p><ul><li>0x01 &lt;&lt; 0 = this method is guaranteed to return or terminate consistently (<code>:consistent</code>)</li><li>0x01 &lt;&lt; 1 = this method is free from externally semantically visible side effects (<code>:effect_free</code>)</li><li>0x01 &lt;&lt; 2 = this method is guaranteed to not throw an exception (<code>:nothrow</code>)</li><li>0x01 &lt;&lt; 3 = this method is guaranteed to terminate (<code>:terminates_globally</code>)</li><li>0x01 &lt;&lt; 4 = the syntactic control flow within this method is guaranteed to terminate (<code>:terminates_locally</code>)</li></ul><p>See the documentation of <code>Base.@assume_effects</code> for more details.</p></li></ul></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="init.html">« Initialization of the Julia runtime</a><a class="docs-footer-nextpage" href="types.html">More about types »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
