<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Fixing precompilation hangs due to open tasks or IO · The Julia Language</title><meta name="title" content="Fixing precompilation hangs due to open tasks or IO · The Julia Language"/><meta property="og:title" content="Fixing precompilation hangs due to open tasks or IO · The Julia Language"/><meta property="twitter:title" content="Fixing precompilation hangs due to open tasks or IO · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox" checked/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="ast.html">Julia ASTs</a></li><li><a class="tocitem" href="types.html">More about types</a></li><li><a class="tocitem" href="object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="functions.html">Julia Functions</a></li><li><a class="tocitem" href="cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="subarrays.html">SubArrays</a></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li class="is-active"><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a><ul class="internal"><li><a class="tocitem" href="#pchang_deps"><span>Diagnosing and fixing hangs due to a package dependency</span></a></li><li><a class="tocitem" href="#pchang_fix"><span>Fixing package code to avoid hangs</span></a></li></ul></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Documentation of Julia&#39;s Internals</a></li><li class="is-active"><a href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/precompile_hang.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Fixing-precompilation-hangs-due-to-open-tasks-or-IO"><a class="docs-heading-anchor" href="#Fixing-precompilation-hangs-due-to-open-tasks-or-IO">Fixing precompilation hangs due to open tasks or IO</a><a id="Fixing-precompilation-hangs-due-to-open-tasks-or-IO-1"></a><a class="docs-heading-anchor-permalink" href="#Fixing-precompilation-hangs-due-to-open-tasks-or-IO" title="Permalink"></a></h1><p>On Julia 1.10 or higher, you might see the following message:</p><p><img src="img/precompilation_hang.png" alt="Screenshot of precompilation hang"/></p><p>This may repeat. If it continues to repeat with no hints that it will resolve itself, you may have a &quot;precompilation hang&quot; that requires fixing. Even if it&#39;s transient, you might prefer to resolve it so that users will not be bothered by this warning.  This page walks you through how to analyze and fix such issues.</p><p>If you follow the advice and hit <code>Ctrl-C</code>, you might see</p><pre><code class="nohighlight hljs">^C Interrupted: Exiting precompilation...

  1 dependency had warnings during precompilation:
┌ Test1 [ac89d554-e2ba-40bc-bc5c-de68b658c982]
│  [pid 2745] waiting for IO to finish:
│   Handle type        uv_handle_t-&gt;data
│   timer              0x55580decd1e0-&gt;0x7f94c3a4c340</code></pre><p>This message conveys two key pieces of information:</p><ul><li>the hang is occurring during precompilation of <code>Test1</code>, a dependency of <code>Test2</code> (the package we were trying to load with <code>using Test2</code>)</li><li>during precompilation of <code>Test1</code>, Julia created a <code>Timer</code> object (use <code>?Timer</code> if you&#39;re unfamiliar with Timers) which is still open; until that closes, the process is hung</li></ul><p>If this is enough of a hint for you to figure out how <code>timer = Timer(args...)</code> is being created, one good solution is to add <code>wait(timer)</code> if <code>timer</code> eventually finishes on its own, or <code>close(timer)</code> if you need to force-close it, before the final <code>end</code> of the module.</p><p>However, there are cases that may not be that straightforward. Usually the best option is to start by determining whether the hang is due to code in Test1 or whether it is due to one of Test1&#39;s dependencies:</p><ul><li>Option 1: <code>Pkg.add(&quot;Aqua&quot;)</code> and use <a href="https://juliatesting.github.io/Aqua.jl/dev/#Aqua.test_persistent_tasks-Tuple{Base.PkgId}"><code>Aqua.test_persistent_tasks</code></a>. This should help you identify which package is causing the problem, after which the instructions <a href="precompile_hang.html#pchang_fix">below</a> should be followed. If needed, you can create a <code>PkgId</code> as <code>Base.PkgId(UUID(&quot;...&quot;), &quot;Test1&quot;)</code>, where <code>...</code> comes from the <code>uuid</code> entry in <code>Test1/Project.toml</code>.</li><li>Option 2: manually diagnose the source of the hang.</li></ul><p>To manually diagnose:</p><ol><li><code>Pkg.develop(&quot;Test1&quot;)</code></li><li>Comment out all the code <code>include</code>d or defined in <code>Test1</code>, <em>except</em> the <code>using/import</code> statements.</li><li>Try <code>using Test2</code> (or even <code>using Test1</code> assuming that hangs too) again</li></ol><p>Now we arrive at a fork in the road: either</p><ul><li>the hang persists, indicating it is <a href="precompile_hang.html#pchang_deps">due to one of your dependencies</a></li><li>the hang disappears, indicating that it is <a href="precompile_hang.html#pchang_fix">due to something in your code</a>.</li></ul><h2 id="pchang_deps"><a class="docs-heading-anchor" href="#pchang_deps">Diagnosing and fixing hangs due to a package dependency</a><a id="pchang_deps-1"></a><a class="docs-heading-anchor-permalink" href="#pchang_deps" title="Permalink"></a></h2><p>Use a binary search to identify the problematic dependency: start by commenting out half your dependencies, then when you isolate which half is responsible comment out half of that half, etc. (You don&#39;t have to remove them from the project, just comment out the <code>using</code>/<code>import</code> statements.)</p><p>Once you&#39;ve identified a suspect (here we&#39;ll call it <code>ThePackageYouThinkIsCausingTheProblem</code>), first try precompiling that package. If it also hangs during precompilation, continue chasing the problem backwards.</p><p>However, most likely <code>ThePackageYouThinkIsCausingTheProblem</code> will precompile fine. This suggests it&#39;s in the function <code>ThePackageYouThinkIsCausingTheProblem.__init__</code>, which does not run during precompilation of <code>ThePackageYouThinkIsCausingTheProblem</code> but <em>does</em> in any package that loads <code>ThePackageYouThinkIsCausingTheProblem</code>.  To test this theory, set up a minimal working example (MWE), something like</p><pre><code class="language-julia hljs">(@v1.10) pkg&gt; generate MWE
  Generating  project MWE:
    MWE\Project.toml
    MWE\src\MWE.jl</code></pre><p>where the source code of <code>MWE.jl</code> is</p><pre><code class="language-julia hljs">module MWE
using ThePackageYouThinkIsCausingTheProblem
end</code></pre><p>and you&#39;ve added <code>ThePackageYouThinkIsCausingTheProblem</code> to MWE&#39;s dependencies.</p><p>If that MWE reproduces the hang, you&#39;ve found your culprit: <code>ThePackageYouThinkIsCausingTheProblem.__init__</code> must be creating the <code>Timer</code> object. If the timer object can be safely <code>close</code>d, that&#39;s a good option. Otherwise, the most common solution is to avoid creating the timer while <em>any</em> package is being precompiled: add</p><pre><code class="language-julia hljs">ccall(:jl_generating_output, Cint, ()) == 1 &amp;&amp; return nothing</code></pre><p>as the first line of <code>ThePackageYouThinkIsCausingTheProblem.__init__</code>, and it will avoid doing any initialization in any Julia process whose purpose is to precompile packages.</p><h2 id="pchang_fix"><a class="docs-heading-anchor" href="#pchang_fix">Fixing package code to avoid hangs</a><a id="pchang_fix-1"></a><a class="docs-heading-anchor-permalink" href="#pchang_fix" title="Permalink"></a></h2><p>Search your package for suggestive words (here like &quot;Timer&quot;) and see if you can identify where the problem is being created. Note that a method <em>definition</em> like</p><pre><code class="language-julia hljs">maketimer() = Timer(timer -&gt; println(&quot;hi&quot;), 0; interval=1)</code></pre><p>is not problematic in and of itself: it can cause this problem only if <code>maketimer</code> gets called while the module is being defined. This might be happening from a top-level statement such as</p><pre><code class="language-julia hljs">const GLOBAL_TIMER = maketimer()</code></pre><p>or it might conceivably occur in a <a href="https://github.com/JuliaLang/PrecompileTools.jl">precompile workload</a>.</p><p>If you struggle to identify the causative lines, then consider doing a binary search: comment out sections of your package (or <code>include</code> lines to omit entire files) until you&#39;ve reduced the problem in scope.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="builtins.html">« Core.Builtins</a><a class="docs-footer-nextpage" href="backtraces.html">Reporting and analyzing crashes (segfaults) »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
