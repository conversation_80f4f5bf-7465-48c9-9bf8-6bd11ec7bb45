<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>The Julia REPL · The Julia Language</title><meta name="title" content="The Julia REPL · The Julia Language"/><meta property="og:title" content="The Julia REPL · The Julia Language"/><meta property="twitter:title" content="The Julia REPL · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li class="is-active"><a class="tocitem" href="REPL.html">The Julia REPL</a><ul class="internal"><li><a class="tocitem" href="#The-different-prompt-modes"><span>The different prompt modes</span></a></li><li><a class="tocitem" href="#Key-bindings"><span>Key bindings</span></a></li><li><a class="tocitem" href="#Tab-completion"><span>Tab completion</span></a></li><li><a class="tocitem" href="#Customizing-Colors"><span>Customizing Colors</span></a></li><li><a class="tocitem" href="#Changing-the-contextual-module-which-is-active-at-the-REPL"><span>Changing the contextual module which is active at the REPL</span></a></li><li><a class="tocitem" href="#Numbered-prompt"><span>Numbered prompt</span></a></li><li><a class="tocitem" href="#TerminalMenus"><span>TerminalMenus</span></a></li><li><a class="tocitem" href="#References"><span>References</span></a></li></ul></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="REPL.html">The Julia REPL</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="REPL.html">The Julia REPL</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/stdlib/REPL/docs/src/index.md" title="View source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="The-Julia-REPL"><a class="docs-heading-anchor" href="#The-Julia-REPL">The Julia REPL</a><a id="The-Julia-REPL-1"></a><a class="docs-heading-anchor-permalink" href="#The-Julia-REPL" title="Permalink"></a></h1><p>Julia comes with a full-featured interactive command-line REPL (read-eval-print loop) built into the <code>julia</code> executable. In addition to allowing quick and easy evaluation of Julia statements, it has a searchable history, tab-completion, many helpful keybindings, and dedicated help and shell modes. The REPL can be started by simply calling <code>julia</code> with no arguments or double-clicking on the executable:</p><pre><code class="nohighlight hljs">$ julia

               _
   _       _ _(_)_     |  Documentation: https://docs.julialang.org
  (_)     | (_) (_)    |
   _ _   _| |_  __ _   |  Type &quot;?&quot; for help, &quot;]?&quot; for Pkg help.
  | | | | | | |/ _` |  |
  | | |_| | | | (_| |  |  Version 1.11.5 (2025-04-14)
 _/ |\__&#39;_|_|_|\__&#39;_|  |  Official https://julialang.org/ release
|__/                   |


julia&gt;</code></pre><p>To exit the interactive session, type <code>^D</code> – the control key together with the <code>d</code> key on a blank line – or type <code>exit()</code> followed by the return or enter key. The REPL greets you with a banner and a <code>julia&gt;</code> prompt.</p><h2 id="The-different-prompt-modes"><a class="docs-heading-anchor" href="#The-different-prompt-modes">The different prompt modes</a><a id="The-different-prompt-modes-1"></a><a class="docs-heading-anchor-permalink" href="#The-different-prompt-modes" title="Permalink"></a></h2><h3 id="The-Julian-mode"><a class="docs-heading-anchor" href="#The-Julian-mode">The Julian mode</a><a id="The-Julian-mode-1"></a><a class="docs-heading-anchor-permalink" href="#The-Julian-mode" title="Permalink"></a></h3><p>The REPL has five main modes of operation. The first and most common is the Julian prompt. It is the default mode of operation; each new line initially starts with <code>julia&gt;</code>. It is here that you can enter Julia expressions. Hitting return or enter after a complete expression has been entered will evaluate the entry and show the result of the last expression.</p><pre><code class="language-julia-repl hljs">julia&gt; string(1 + 2)
&quot;3&quot;</code></pre><p>There are a number of useful features unique to interactive work. In addition to showing the result, the REPL also binds the result to the variable <code>ans</code>. A trailing semicolon on the line can be used as a flag to suppress showing the result.</p><pre><code class="language-julia-repl hljs">julia&gt; string(3 * 4);

julia&gt; ans
&quot;12&quot;</code></pre><p>In Julia mode, the REPL supports something called <em>prompt pasting</em>. This activates when pasting text that starts with <code>julia&gt;</code> into the REPL. In that case, only expressions starting with <code>julia&gt;</code> (as well as the other REPL mode prompts: <code>shell&gt;</code>, <code>help?&gt;</code>, <code>pkg&gt;</code> ) are parsed, but others are removed. This makes it possible to paste a chunk of text that has been copied from a REPL session without having to scrub away prompts and outputs. This feature is enabled by default but can be disabled or enabled at will with <code>REPL.enable_promptpaste(::Bool)</code>. If it is enabled, you can try it out by pasting the code block above this paragraph straight into the REPL. This feature does not work on the standard Windows command prompt due to its limitation at detecting when a paste occurs.</p><p>Objects are printed at the REPL using the <a href="../base/io-network.html#Base.show-Tuple{IO, Any}"><code>show</code></a> function with a specific <a href="../base/io-network.html#Base.IOContext"><code>IOContext</code></a>. In particular, the <code>:limit</code> attribute is set to <code>true</code>. Other attributes can receive in certain <code>show</code> methods a default value if it&#39;s not already set, like <code>:compact</code>. It&#39;s possible, as an experimental feature, to specify the attributes used by the REPL via the <code>Base.active_repl.options.iocontext</code> dictionary (associating values to attributes). For example:</p><pre><code class="language-julia-repl hljs">julia&gt; rand(2, 2)
2×2 Array{Float64,2}:
 0.8833    0.329197
 0.719708  0.59114

julia&gt; show(IOContext(stdout, :compact =&gt; false), &quot;text/plain&quot;, rand(2, 2))
 0.43540323669187075  0.15759787870609387
 0.2540832269192739   0.4597637838786053
julia&gt; Base.active_repl.options.iocontext[:compact] = false;

julia&gt; rand(2, 2)
2×2 Array{Float64,2}:
 0.2083967319174056  0.13330606013126012
 0.6244375177790158  0.9777957560761545</code></pre><p>In order to define automatically the values of this dictionary at startup time, one can use the <a href="REPL.html#Base.atreplinit"><code>atreplinit</code></a> function in the <code>~/.julia/config/startup.jl</code> file, for example:</p><pre><code class="language-julia hljs">atreplinit() do repl
    repl.options.iocontext[:compact] = false
end</code></pre><h3 id="Help-mode"><a class="docs-heading-anchor" href="#Help-mode">Help mode</a><a id="Help-mode-1"></a><a class="docs-heading-anchor-permalink" href="#Help-mode" title="Permalink"></a></h3><p>When the cursor is at the beginning of the line, the prompt can be changed to a help mode by typing <code>?</code>. Julia will attempt to print help or documentation for anything entered in help mode:</p><pre><code class="language-julia-repl hljs">julia&gt; ? # upon typing ?, the prompt changes (in place) to: help?&gt;

help?&gt; string
search: string String Cstring Cwstring RevString randstring bytestring SubString

  string(xs...)

  Create a string from any values using the print function.</code></pre><p>Macros, types and variables can also be queried:</p><pre><code class="nohighlight hljs">help?&gt; @time
  @time

  A macro to execute an expression, printing the time it took to execute, the number of allocations,
  and the total number of bytes its execution caused to be allocated, before returning the value of the
  expression.

  See also @timev, @timed, @elapsed, and @allocated.

help?&gt; Int32
search: Int32 UInt32

  Int32 &lt;: Signed

  32-bit signed integer type.</code></pre><p>A string or regex literal searches all docstrings using <a href="InteractiveUtils.html#Base.Docs.apropos"><code>apropos</code></a>:</p><pre><code class="nohighlight hljs">help?&gt; &quot;aprop&quot;
REPL.stripmd
Base.Docs.apropos

help?&gt; r&quot;ap..p&quot;
Base.:∘
Base.shell_escape_posixly
Distributed.CachingPool
REPL.stripmd
Base.Docs.apropos</code></pre><p>Another feature of help mode is the ability to access extended docstrings. You can do this by typing something like <code>??Print</code> rather than <code>?Print</code> which will display the <code># Extended help</code> section from the source codes documentation.</p><p>Help mode can be exited by pressing backspace at the beginning of the line.</p><h3 id="man-shell-mode"><a class="docs-heading-anchor" href="#man-shell-mode">Shell mode</a><a id="man-shell-mode-1"></a><a class="docs-heading-anchor-permalink" href="#man-shell-mode" title="Permalink"></a></h3><p>Just as help mode is useful for quick access to documentation, another common task is to use the system shell to execute system commands. Just as <code>?</code> entered help mode when at the beginning of the line, a semicolon (<code>;</code>) will enter the shell mode. And it can be exited by pressing backspace at the beginning of the line.</p><pre><code class="language-julia-repl hljs">julia&gt; ; # upon typing ;, the prompt changes (in place) to: shell&gt;

shell&gt; echo hello
hello</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>For Windows users, Julia&#39;s shell mode does not expose windows shell commands. Hence, this will fail:</p></div></div><pre><code class="language-julia-repl hljs">julia&gt; ; # upon typing ;, the prompt changes (in place) to: shell&gt;

shell&gt; dir
ERROR: IOError: could not spawn `dir`: no such file or directory (ENOENT)
Stacktrace!
.......</code></pre><p>However, you can get access to <code>PowerShell</code> like this:</p><pre><code class="language-julia-repl hljs">julia&gt; ; # upon typing ;, the prompt changes (in place) to: shell&gt;

shell&gt; powershell
Windows PowerShell
Copyright (C) Microsoft Corporation. All rights reserved.
PS C:\Users\<USER>\Users\elm&gt;dir
 Volume in drive C has no label
 Volume Serial Number is 1643-0CD7
  Directory of C:\Users\<USER>\\M-x&quot;</code> or <code>&quot;\ex&quot;</code>, and Control plus <code>x</code> can be written <code>&quot;\\C-x&quot;</code> or <code>&quot;^x&quot;</code>. The values of the custom keymap must be <code>nothing</code> (indicating that the input should be ignored) or functions that accept the signature <code>(PromptState, AbstractREPL, Char)</code>. The <code>REPL.setup_interface</code> function must be called before the REPL is initialized, by registering the operation with <a href="REPL.html#Base.atreplinit"><code>atreplinit</code></a> . For example, to bind the up and down arrow keys to move through history without prefix search, one could put the following code in <code>~/.julia/config/startup.jl</code>:</p><pre><code class="language-julia hljs">import REPL
import REPL.LineEdit

const mykeys = Dict{Any,Any}(
    # Up Arrow
    &quot;\e[A&quot; =&gt; (s,o...)-&gt;(LineEdit.edit_move_up(s) || LineEdit.history_prev(s, LineEdit.mode(s).hist)),
    # Down Arrow
    &quot;\e[B&quot; =&gt; (s,o...)-&gt;(LineEdit.edit_move_down(s) || LineEdit.history_next(s, LineEdit.mode(s).hist))
)

function customize_keys(repl)
    repl.interface = REPL.setup_interface(repl; extra_repl_keymap = mykeys)
end

atreplinit(customize_keys)</code></pre><p>Users should refer to <code>LineEdit.jl</code> to discover the available actions on key input.</p><h2 id="Tab-completion"><a class="docs-heading-anchor" href="#Tab-completion">Tab completion</a><a id="Tab-completion-1"></a><a class="docs-heading-anchor-permalink" href="#Tab-completion" title="Permalink"></a></h2><p>In the Julian, pkg and help modes of the REPL, one can enter the first few characters of a function or type and then press the tab key to get a list all matches:</p><pre><code class="language-julia-repl hljs">julia&gt; x[TAB]
julia&gt; xor</code></pre><p>In some cases it only completes part of the name, up to the next ambiguity:</p><pre><code class="language-julia-repl hljs">julia&gt; mapf[TAB]
julia&gt; mapfold</code></pre><p>If you hit tab again, then you get the list of things that might complete this:</p><pre><code class="language-julia-repl hljs">julia&gt; mapfold[TAB]
mapfoldl mapfoldr</code></pre><p>When a single complete tab-complete result is available at the end of an input line and 2 or more characters have been typed, a hint of the completion will show in a lighter color. This can be disabled via <code>Base.active_repl.options.hint_tab_completes = false</code> or by adding</p><pre><code class="nohighlight hljs">atreplinit() do repl
    if VERSION &gt;= v&quot;1.11.0-0&quot;
        repl.options.hint_tab_completes = false
    end
end</code></pre><p>to your <code>~/.julia/config/startup.jl</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>Tab-complete hinting was added in Julia 1.11</p></div></div><p>Like other components of the REPL, the search is case-sensitive:</p><pre><code class="language-julia-repl hljs">julia&gt; stri[TAB]
stride     strides     string      strip

julia&gt; Stri[TAB]
StridedArray    StridedMatrix    StridedVecOrMat  StridedVector    String</code></pre><p>The tab key can also be used to substitute LaTeX math symbols with their Unicode equivalents, and get a list of LaTeX matches as well:</p><pre><code class="language-julia-repl hljs">julia&gt; \pi[TAB]
julia&gt; π
π = 3.1415926535897...

julia&gt; e\_1[TAB] = [1,0]
julia&gt; e₁ = [1,0]
2-element Array{Int64,1}:
 1
 0

julia&gt; e\^1[TAB] = [1 0]
julia&gt; e¹ = [1 0]
1×2 Array{Int64,2}:
 1  0

julia&gt; \sqrt[TAB]2     # √ is equivalent to the sqrt function
julia&gt; √2
1.4142135623730951

julia&gt; \hbar[TAB](h) = h / 2\pi[TAB]
julia&gt; ħ(h) = h / 2π
ħ (generic function with 1 method)

julia&gt; \h[TAB]
\hat              \hermitconjmatrix  \hkswarow          \hrectangle
\hatapprox        \hexagon           \hookleftarrow     \hrectangleblack
\hbar             \hexagonblack      \hookrightarrow    \hslash
\heartsuit        \hksearow          \house             \hspace

julia&gt; α=&quot;\alpha[TAB]&quot;   # LaTeX completion also works in strings
julia&gt; α=&quot;α&quot;</code></pre><p>A full list of tab-completions can be found in the <a href="../manual/unicode-input.html#Unicode-Input">Unicode Input</a> section of the manual.</p><p>Completion of paths works for strings and julia&#39;s shell mode:</p><pre><code class="language-julia-repl hljs">julia&gt; path=&quot;/[TAB]&quot;
.dockerenv  .juliabox/   boot/        etc/         lib/         media/       opt/         root/        sbin/        sys/         usr/
.dockerinit bin/         dev/         home/        lib64/       mnt/         proc/        run/         srv/         tmp/         var/
shell&gt; /[TAB]
.dockerenv  .juliabox/   boot/        etc/         lib/         media/       opt/         root/        sbin/        sys/         usr/
.dockerinit bin/         dev/         home/        lib64/       mnt/         proc/        run/         srv/         tmp/         var/</code></pre><p>Dictionary keys can also be tab completed:</p><pre><code class="language-julia-repl hljs">julia&gt; foo = Dict(&quot;qwer1&quot;=&gt;1, &quot;qwer2&quot;=&gt;2, &quot;asdf&quot;=&gt;3)
Dict{String,Int64} with 3 entries:
  &quot;qwer2&quot; =&gt; 2
  &quot;asdf&quot;  =&gt; 3
  &quot;qwer1&quot; =&gt; 1

julia&gt; foo[&quot;q[TAB]

&quot;qwer1&quot; &quot;qwer2&quot;
julia&gt; foo[&quot;qwer</code></pre><p>Tab completion can also help completing fields:</p><pre><code class="language-julia-repl hljs">julia&gt; x = 3 + 4im;

julia&gt; x.[TAB][TAB]
im re

julia&gt; import UUIDs

julia&gt; UUIDs.uuid[TAB][TAB]
uuid1        uuid4         uuid5        uuid_version</code></pre><p>Fields for output from functions can also be completed:</p><pre><code class="language-julia-repl hljs">julia&gt; split(&quot;&quot;,&quot;&quot;)[1].[TAB]
lastindex  offset  string</code></pre><p>The completion of fields for output from functions uses type inference, and it can only suggest fields if the function is type stable.</p><p>Tab completion can help with investigation of the available methods matching the input arguments:</p><pre><code class="language-julia-repl hljs">julia&gt; max([TAB] # All methods are displayed, not shown here due to size of the list

julia&gt; max([1, 2], [TAB] # All methods where `Vector{Int}` matches as first argument
max(x, y) in Base at operators.jl:215
max(a, b, c, xs...) in Base at operators.jl:281

julia&gt; max([1, 2], max(1, 2), [TAB] # All methods matching the arguments.
max(x, y) in Base at operators.jl:215
max(a, b, c, xs...) in Base at operators.jl:281</code></pre><p>Keywords are also displayed in the suggested methods after <code>;</code>, see below line where <code>limit</code> and <code>keepempty</code> are keyword arguments:</p><pre><code class="language-julia-repl hljs">julia&gt; split(&quot;1 1 1&quot;, [TAB]
split(str::AbstractString; limit, keepempty) in Base at strings/util.jl:302
split(str::T, splitter; limit, keepempty) where T&lt;:AbstractString in Base at strings/util.jl:277</code></pre><p>The completion of the methods uses type inference and can therefore see if the arguments match even if the arguments are output from functions. The function needs to be type stable for the completion to be able to remove non-matching methods.</p><p>If you wonder which methods can be used with particular argument types, use <code>?</code> as the function name. This shows an example of looking for functions in InteractiveUtils that accept a single string:</p><pre><code class="language-julia-repl hljs">julia&gt; InteractiveUtils.?(&quot;somefile&quot;)[TAB]
edit(path::AbstractString) in InteractiveUtils at InteractiveUtils/src/editless.jl:197
less(file::AbstractString) in InteractiveUtils at InteractiveUtils/src/editless.jl:266</code></pre><p>This listed methods in the <code>InteractiveUtils</code> module that can be called on a string. By default, this excludes methods where all arguments are typed as <code>Any</code>, but you can see those too by holding down SHIFT-TAB instead of TAB:</p><pre><code class="language-julia-repl hljs">julia&gt; InteractiveUtils.?(&quot;somefile&quot;)[SHIFT-TAB]
apropos(string) in REPL at REPL/src/docview.jl:796
clipboard(x) in InteractiveUtils at InteractiveUtils/src/clipboard.jl:64
code_llvm(f) in InteractiveUtils at InteractiveUtils/src/codeview.jl:221
code_native(f) in InteractiveUtils at InteractiveUtils/src/codeview.jl:243
edit(path::AbstractString) in InteractiveUtils at InteractiveUtils/src/editless.jl:197
edit(f) in InteractiveUtils at InteractiveUtils/src/editless.jl:225
eval(x) in InteractiveUtils at InteractiveUtils/src/InteractiveUtils.jl:3
include(x) in InteractiveUtils at InteractiveUtils/src/InteractiveUtils.jl:3
less(file::AbstractString) in InteractiveUtils at InteractiveUtils/src/editless.jl:266
less(f) in InteractiveUtils at InteractiveUtils/src/editless.jl:274
report_bug(kind) in InteractiveUtils at InteractiveUtils/src/InteractiveUtils.jl:391
separate_kwargs(args...; kwargs...) in InteractiveUtils at InteractiveUtils/src/macros.jl:7</code></pre><p>You can also use <code>?(&quot;somefile&quot;)[TAB]</code>  and look across all modules, but the method lists can be long.</p><p>By omitting the closing parenthesis, you can include functions that might require additional arguments:</p><pre><code class="language-julia-repl hljs">julia&gt; using Mmap

help?&gt; Mmap.?(&quot;file&quot;,[TAB]
Mmap.Anonymous(name::String, readonly::Bool, create::Bool) in Mmap at Mmap/src/Mmap.jl:16
mmap(file::AbstractString) in Mmap at Mmap/src/Mmap.jl:245
mmap(file::AbstractString, ::Type{T}) where T&lt;:Array in Mmap at Mmap/src/Mmap.jl:245
mmap(file::AbstractString, ::Type{T}, dims::Tuple{Vararg{Integer, N}}) where {T&lt;:Array, N} in Mmap at Mmap/src/Mmap.jl:245
mmap(file::AbstractString, ::Type{T}, dims::Tuple{Vararg{Integer, N}}, offset::Integer; grow, shared) where {T&lt;:Array, N} in Mmap at Mmap/src/Mmap.jl:245
mmap(file::AbstractString, ::Type{T}, len::Integer) where T&lt;:Array in Mmap at Mmap/src/Mmap.jl:251
mmap(file::AbstractString, ::Type{T}, len::Integer, offset::Integer; grow, shared) where T&lt;:Array in Mmap at Mmap/src/Mmap.jl:251
mmap(file::AbstractString, ::Type{T}, dims::Tuple{Vararg{Integer, N}}) where {T&lt;:BitArray, N} in Mmap at Mmap/src/Mmap.jl:316
mmap(file::AbstractString, ::Type{T}, dims::Tuple{Vararg{Integer, N}}, offset::Integer; grow, shared) where {T&lt;:BitArray, N} in Mmap at Mmap/src/Mmap.jl:316
mmap(file::AbstractString, ::Type{T}, len::Integer) where T&lt;:BitArray in Mmap at Mmap/src/Mmap.jl:322
mmap(file::AbstractString, ::Type{T}, len::Integer, offset::Integer; grow, shared) where T&lt;:BitArray in Mmap at Mmap/src/Mmap.jl:322</code></pre><h2 id="Customizing-Colors"><a class="docs-heading-anchor" href="#Customizing-Colors">Customizing Colors</a><a id="Customizing-Colors-1"></a><a class="docs-heading-anchor-permalink" href="#Customizing-Colors" title="Permalink"></a></h2><p>The colors used by Julia and the REPL can be customized, as well. To change the color of the Julia prompt you can add something like the following to your <code>~/.julia/config/startup.jl</code> file, which is to be placed inside your home directory:</p><pre><code class="language-julia hljs">function customize_colors(repl)
    repl.prompt_color = Base.text_colors[:cyan]
end

atreplinit(customize_colors)</code></pre><p>The available color keys can be seen by typing <code>Base.text_colors</code> in the help mode of the REPL. In addition, the integers 0 to 255 can be used as color keys for terminals with 256 color support.</p><p>You can also change the colors for the help and shell prompts and input and answer text by setting the appropriate field of <code>repl</code> in the <code>customize_colors</code> function above (respectively, <code>help_color</code>, <code>shell_color</code>, <code>input_color</code>, and <code>answer_color</code>). For the latter two, be sure that the <code>envcolors</code> field is also set to false.</p><p>It is also possible to apply boldface formatting by using <code>Base.text_colors[:bold]</code> as a color. For instance, to print answers in boldface font, one can use the following as a <code>~/.julia/config/startup.jl</code>:</p><pre><code class="language-julia hljs">function customize_colors(repl)
    repl.envcolors = false
    repl.answer_color = Base.text_colors[:bold]
end

atreplinit(customize_colors)</code></pre><p>You can also customize the color used to render warning and informational messages by setting the appropriate environment variables. For instance, to render error, warning, and informational messages respectively in magenta, yellow, and cyan you can add the following to your <code>~/.julia/config/startup.jl</code> file:</p><pre><code class="language-julia hljs">ENV[&quot;JULIA_ERROR_COLOR&quot;] = :magenta
ENV[&quot;JULIA_WARN_COLOR&quot;] = :yellow
ENV[&quot;JULIA_INFO_COLOR&quot;] = :cyan</code></pre><h2 id="Changing-the-contextual-module-which-is-active-at-the-REPL"><a class="docs-heading-anchor" href="#Changing-the-contextual-module-which-is-active-at-the-REPL">Changing the contextual module which is active at the REPL</a><a id="Changing-the-contextual-module-which-is-active-at-the-REPL-1"></a><a class="docs-heading-anchor-permalink" href="#Changing-the-contextual-module-which-is-active-at-the-REPL" title="Permalink"></a></h2><p>When entering expressions at the REPL, they are by default evaluated in the <code>Main</code> module;</p><pre><code class="language-julia-repl hljs">julia&gt; @__MODULE__
Main</code></pre><p>It is possible to change this contextual module via the function <code>REPL.activate(m)</code> where <code>m</code> is a <code>Module</code> or by typing the module in the REPL and pressing the keybinding Alt-m with the cursor on the module name (Esc-m on MacOS). Pressing the keybinding on an empty prompt toggles the context between the previously active non-<code>Main</code> module and <code>Main</code>. The active module is shown in the prompt (unless it is <code>Main</code>):</p><pre><code class="language-julia-repl hljs">julia&gt; using REPL

julia&gt; REPL.activate(Base)

(Base) julia&gt; @__MODULE__
Base

(Base) julia&gt; using REPL # Need to load REPL into Base module to use it

(Base) julia&gt; REPL.activate(Main)

julia&gt;

julia&gt; Core&lt;Alt-m&gt; # using the keybinding to change module

(Core) julia&gt;

(Core) julia&gt; &lt;Alt-m&gt; # going back to Main via keybinding

julia&gt;

julia&gt; &lt;Alt-m&gt; # going back to previously-active Core via keybinding

(Core) julia&gt;</code></pre><p>Functions that take an optional module argument often defaults to the REPL context module. As an example, calling <code>varinfo()</code> will show the variables of the current active module:</p><pre><code class="language-julia-repl hljs">julia&gt; module CustomMod
           export var, f
           var = 1
           f(x) = x^2
       end;

julia&gt; REPL.activate(CustomMod)

(Main.CustomMod) julia&gt; varinfo()
  name         size summary
  ––––––––– ––––––– ––––––––––––––––––––––––––––––––––
  CustomMod         Module
  f         0 bytes f (generic function with 1 method)
  var       8 bytes Int64</code></pre><h2 id="Numbered-prompt"><a class="docs-heading-anchor" href="#Numbered-prompt">Numbered prompt</a><a id="Numbered-prompt-1"></a><a class="docs-heading-anchor-permalink" href="#Numbered-prompt" title="Permalink"></a></h2><p>It is possible to get an interface which is similar to the IPython REPL and the Mathematica notebook with numbered input prompts and output prefixes. This is done by calling <code>REPL.numbered_prompt!()</code>. If you want to have this enabled on startup, add</p><pre><code class="language-julia hljs">atreplinit() do repl
    @eval import REPL
    if !isdefined(repl, :interface)
        repl.interface = REPL.setup_interface(repl)
    end
    REPL.numbered_prompt!(repl)
end</code></pre><p>to your <code>startup.jl</code> file. In numbered prompt the variable <code>Out[n]</code> (where <code>n</code> is an integer) can be used to refer to earlier results:</p><pre><code class="language-julia-repl hljs">In [1]: 5 + 3
Out[1]: 8

In [2]: Out[1] + 5
Out[2]: 13

In [3]: Out
Out[3]: Dict{Int64, Any} with 2 entries:
  2 =&gt; 13
  1 =&gt; 8</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Since all outputs from previous REPL evaluations are saved in the <code>Out</code> variable, one should be careful if they are returning many large in-memory objects like arrays, since they will be protected from garbage collection so long as a reference to them remains in <code>Out</code>. If you need to remove references to objects in <code>Out</code>, you can clear the entire history it stores with <code>empty!(Out)</code>, or clear an individual entry with <code>Out[n] = nothing</code>.</p></div></div><h2 id="TerminalMenus"><a class="docs-heading-anchor" href="#TerminalMenus">TerminalMenus</a><a id="TerminalMenus-1"></a><a class="docs-heading-anchor-permalink" href="#TerminalMenus" title="Permalink"></a></h2><p>TerminalMenus is a submodule of the Julia REPL and enables small, low-profile interactive menus in the terminal.</p><h3 id="Examples"><a class="docs-heading-anchor" href="#Examples">Examples</a><a id="Examples-1"></a><a class="docs-heading-anchor-permalink" href="#Examples" title="Permalink"></a></h3><pre><code class="language-julia hljs">import REPL
using REPL.TerminalMenus

options = [&quot;apple&quot;, &quot;orange&quot;, &quot;grape&quot;, &quot;strawberry&quot;,
            &quot;blueberry&quot;, &quot;peach&quot;, &quot;lemon&quot;, &quot;lime&quot;]
</code></pre><h4 id="RadioMenu"><a class="docs-heading-anchor" href="#RadioMenu">RadioMenu</a><a id="RadioMenu-1"></a><a class="docs-heading-anchor-permalink" href="#RadioMenu" title="Permalink"></a></h4><p>The RadioMenu allows the user to select one option from the list. The <code>request</code> function displays the interactive menu and returns the index of the selected choice. If a user presses &#39;q&#39; or <code>ctrl-c</code>, <code>request</code> will return a <code>-1</code>.</p><pre><code class="language-julia hljs"># `pagesize` is the number of items to be displayed at a time.
#  The UI will scroll if the number of options is greater
#   than the `pagesize`
menu = RadioMenu(options, pagesize=4)

# `request` displays the menu and returns the index after the
#   user has selected a choice
choice = request(&quot;Choose your favorite fruit:&quot;, menu)

if choice != -1
    println(&quot;Your favorite fruit is &quot;, options[choice], &quot;!&quot;)
else
    println(&quot;Menu canceled.&quot;)
end
</code></pre><p>Output:</p><pre><code class="nohighlight hljs">Choose your favorite fruit:
^  grape
   strawberry
 &gt; blueberry
v  peach
Your favorite fruit is blueberry!</code></pre><h4 id="MultiSelectMenu"><a class="docs-heading-anchor" href="#MultiSelectMenu">MultiSelectMenu</a><a id="MultiSelectMenu-1"></a><a class="docs-heading-anchor-permalink" href="#MultiSelectMenu" title="Permalink"></a></h4><p>The MultiSelectMenu allows users to select many choices from a list.</p><pre><code class="language-julia hljs"># here we use the default `pagesize` 10
menu = MultiSelectMenu(options)

# `request` returns a `Set` of selected indices
# if the menu us canceled (ctrl-c or q), return an empty set
choices = request(&quot;Select the fruits you like:&quot;, menu)

if length(choices) &gt; 0
    println(&quot;You like the following fruits:&quot;)
    for i in choices
        println(&quot;  - &quot;, options[i])
    end
else
    println(&quot;Menu canceled.&quot;)
end</code></pre><p>Output:</p><pre><code class="nohighlight hljs">Select the fruits you like:
[press: Enter=toggle, a=all, n=none, d=done, q=abort]
   [ ] apple
 &gt; [X] orange
   [X] grape
   [ ] strawberry
   [ ] blueberry
   [X] peach
   [ ] lemon
   [ ] lime
You like the following fruits:
  - orange
  - grape
  - peach</code></pre><h3 id="Customization-/-Configuration"><a class="docs-heading-anchor" href="#Customization-/-Configuration">Customization / Configuration</a><a id="Customization-/-Configuration-1"></a><a class="docs-heading-anchor-permalink" href="#Customization-/-Configuration" title="Permalink"></a></h3><h4 id="ConfiguredMenu-subtypes"><a class="docs-heading-anchor" href="#ConfiguredMenu-subtypes">ConfiguredMenu subtypes</a><a id="ConfiguredMenu-subtypes-1"></a><a class="docs-heading-anchor-permalink" href="#ConfiguredMenu-subtypes" title="Permalink"></a></h4><p>Starting with Julia 1.6, the recommended way to configure menus is via the constructor. For instance, the default multiple-selection menu</p><pre><code class="nohighlight hljs">julia&gt; menu = MultiSelectMenu(options, pagesize=5);

julia&gt; request(menu) # ASCII is used by default
[press: Enter=toggle, a=all, n=none, d=done, q=abort]
   [ ] apple
   [X] orange
   [ ] grape
 &gt; [X] strawberry
v  [ ] blueberry</code></pre><p>can instead be rendered with Unicode selection and navigation characters with</p><pre><code class="language-julia-repl hljs">julia&gt; menu = MultiSelectMenu(options, pagesize=5, charset=:unicode);

julia&gt; request(menu)
[press: Enter=toggle, a=all, n=none, d=done, q=abort]
   ⬚ apple
   ✓ orange
   ⬚ grape
 → ✓ strawberry
↓  ⬚ blueberry</code></pre><p>More fine-grained configuration is also possible:</p><pre><code class="language-julia-repl hljs">julia&gt; menu = MultiSelectMenu(options, pagesize=5, charset=:unicode, checked=&quot;YEP!&quot;, unchecked=&quot;NOPE&quot;, cursor=&#39;⧐&#39;);

julia&gt; request(menu)
julia&gt; request(menu)
[press: Enter=toggle, a=all, n=none, d=done, q=abort]
   NOPE apple
   YEP! orange
   NOPE grape
 ⧐ YEP! strawberry
↓  NOPE blueberry</code></pre><p>Aside from the overall <code>charset</code> option, for <code>RadioMenu</code> the configurable options are:</p><ul><li><code>cursor::Char=&#39;&gt;&#39;|&#39;→&#39;</code>: character to use for cursor</li><li><code>up_arrow::Char=&#39;^&#39;|&#39;↑&#39;</code>: character to use for up arrow</li><li><code>down_arrow::Char=&#39;v&#39;|&#39;↓&#39;</code>: character to use for down arrow</li><li><code>updown_arrow::Char=&#39;I&#39;|&#39;↕&#39;</code>: character to use for up/down arrow in one-line page</li><li><code>scroll_wrap::Bool=false</code>: optionally wrap-around at the beginning/end of a menu</li><li><code>ctrl_c_interrupt::Bool=true</code>: If <code>false</code>, return empty on ^C, if <code>true</code> throw InterruptException() on ^C</li></ul><p><code>MultiSelectMenu</code> adds:</p><ul><li><code>checked::String=&quot;[X]&quot;|&quot;✓&quot;</code>: string to use for checked</li><li><code>unchecked::String=&quot;[ ]&quot;|&quot;⬚&quot;)</code>: string to use for unchecked</li></ul><p>You can create new menu types of your own. Types that are derived from <code>TerminalMenus.ConfiguredMenu</code> configure the menu options at construction time.</p><h4 id="Legacy-interface"><a class="docs-heading-anchor" href="#Legacy-interface">Legacy interface</a><a id="Legacy-interface-1"></a><a class="docs-heading-anchor-permalink" href="#Legacy-interface" title="Permalink"></a></h4><p>Prior to Julia 1.6, and still supported throughout Julia 1.x, one can also configure menus by calling <code>TerminalMenus.config()</code>.</p><h2 id="References"><a class="docs-heading-anchor" href="#References">References</a><a id="References-1"></a><a class="docs-heading-anchor-permalink" href="#References" title="Permalink"></a></h2><h3 id="REPL"><a class="docs-heading-anchor" href="#REPL">REPL</a><a id="REPL-1"></a><a class="docs-heading-anchor-permalink" href="#REPL" title="Permalink"></a></h3><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.atreplinit" href="#Base.atreplinit"><code>Base.atreplinit</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">atreplinit(f)</code></pre><p>Register a one-argument function to be called before the REPL interface is initialized in interactive sessions; this is useful to customize the interface. The argument of <code>f</code> is the REPL object. This function should be called from within the <code>.julia/config/startup.jl</code> initialization file.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/client.jl#L368-L375">source</a></section></article><h3 id="TerminalMenus-2"><a class="docs-heading-anchor" href="#TerminalMenus-2">TerminalMenus</a><a class="docs-heading-anchor-permalink" href="#TerminalMenus-2" title="Permalink"></a></h3><h3 id="Menus"><a class="docs-heading-anchor" href="#Menus">Menus</a><a id="Menus-1"></a><a class="docs-heading-anchor-permalink" href="#Menus" title="Permalink"></a></h3><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="REPL.TerminalMenus.RadioMenu" href="#REPL.TerminalMenus.RadioMenu"><code>REPL.TerminalMenus.RadioMenu</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">RadioMenu</code></pre><p>A menu that allows a user to select a single option from a list.</p><p><strong>Sample Output</strong></p><pre><code class="language-julia-repl hljs">julia&gt; request(RadioMenu(options, pagesize=4))
Choose your favorite fruit:
^  grape
   strawberry
 &gt; blueberry
v  peach
Your favorite fruit is blueberry!</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/REPL/src/TerminalMenus/RadioMenu.jl#L3-L21">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="REPL.TerminalMenus.MultiSelectMenu" href="#REPL.TerminalMenus.MultiSelectMenu"><code>REPL.TerminalMenus.MultiSelectMenu</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">MultiSelectMenu</code></pre><p>A menu that allows a user to select a multiple options from a list.</p><p><strong>Sample Output</strong></p><pre><code class="language-julia-repl hljs">julia&gt; request(MultiSelectMenu(options))
Select the fruits you like:
[press: Enter=toggle, a=all, n=none, d=done, q=abort]
   [ ] apple
 &gt; [X] orange
   [X] grape
   [ ] strawberry
   [ ] blueberry
   [X] peach
   [ ] lemon
   [ ] lime
You like the following fruits:
  - orange
  - grape
  - peach</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/REPL/src/TerminalMenus/MultiSelectMenu.jl#L3-L29">source</a></section></article><h4 id="Configuration"><a class="docs-heading-anchor" href="#Configuration">Configuration</a><a id="Configuration-1"></a><a class="docs-heading-anchor-permalink" href="#Configuration" title="Permalink"></a></h4><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="REPL.TerminalMenus.Config" href="#REPL.TerminalMenus.Config"><code>REPL.TerminalMenus.Config</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Config(; scroll_wrap=false, ctrl_c_interrupt=true, charset=:ascii, cursor::Char, up_arrow::Char, down_arrow::Char)</code></pre><p>Configure behavior for selection menus via keyword arguments:</p><ul><li><code>scroll_wrap</code>, if <code>true</code>, causes the menu to wrap around when scrolling above the first or below the last entry</li><li><code>ctrl_c_interrupt</code>, if <code>true</code>, throws an <code>InterruptException</code> if the user hits Ctrl-C during menu selection. If <code>false</code>, <a href="REPL.html#REPL.TerminalMenus.request"><code>TerminalMenus.request</code></a> will return the default result from <a href="REPL.html#REPL.TerminalMenus.selected"><code>TerminalMenus.selected</code></a>.</li><li><code>charset</code> affects the default values for <code>cursor</code>, <code>up_arrow</code>, and <code>down_arrow</code>, and can be <code>:ascii</code> or <code>:unicode</code></li><li><code>cursor</code> is the character printed to indicate the option that will be chosen by hitting &quot;Enter.&quot; Defaults are &#39;&gt;&#39; or &#39;→&#39;, depending on <code>charset</code>.</li><li><code>up_arrow</code> is the character printed when the display does not include the first entry. Defaults are &#39;^&#39; or &#39;↑&#39;, depending on <code>charset</code>.</li><li><code>down_arrow</code> is the character printed when the display does not include the last entry. Defaults are &#39;v&#39; or &#39;↓&#39;, depending on <code>charset</code>.</li></ul><p>Subtypes of <code>ConfiguredMenu</code> will print <code>cursor</code>, <code>up_arrow</code>, and <code>down_arrow</code> automatically as needed, your <code>writeline</code> method should not print them.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p><code>Config</code> is available as of Julia 1.6. On older releases use the global <code>CONFIG</code>.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/REPL/src/TerminalMenus/config.jl#L20-L44">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="REPL.TerminalMenus.MultiSelectConfig" href="#REPL.TerminalMenus.MultiSelectConfig"><code>REPL.TerminalMenus.MultiSelectConfig</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">MultiSelectConfig(; charset=:ascii, checked::String, unchecked::String, kwargs...)</code></pre><p>Configure behavior for a multiple-selection menu via keyword arguments:</p><ul><li><code>checked</code> is the string to print when an option has been selected. Defaults are &quot;[X]&quot; or &quot;✓&quot;, depending on <code>charset</code>.</li><li><code>unchecked</code> is the string to print when an option has not been selected. Defaults are &quot;[ ]&quot; or &quot;⬚&quot;, depending on <code>charset</code>.</li></ul><p>All other keyword arguments are as described for <a href="REPL.html#REPL.TerminalMenus.Config"><code>TerminalMenus.Config</code></a>. <code>checked</code> and <code>unchecked</code> are not printed automatically, and should be printed by your <code>writeline</code> method.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p><code>MultiSelectConfig</code> is available as of Julia 1.6. On older releases use the global <code>CONFIG</code>.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/REPL/src/TerminalMenus/config.jl#L70-L86">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="REPL.TerminalMenus.config" href="#REPL.TerminalMenus.config"><code>REPL.TerminalMenus.config</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">config( &lt;see arguments&gt; )</code></pre><p>Keyword-only function to configure global menu parameters</p><p><strong>Arguments</strong></p><ul><li><code>charset::Symbol=:na</code>: ui characters to use (<code>:ascii</code> or <code>:unicode</code>); overridden by other arguments</li><li><code>cursor::Char=&#39;&gt;&#39;|&#39;→&#39;</code>: character to use for cursor</li><li><code>up_arrow::Char=&#39;^&#39;|&#39;↑&#39;</code>: character to use for up arrow</li><li><code>down_arrow::Char=&#39;v&#39;|&#39;↓&#39;</code>: character to use for down arrow</li><li><code>checked::String=&quot;[X]&quot;|&quot;✓&quot;</code>: string to use for checked</li><li><code>unchecked::String=&quot;[ ]&quot;|&quot;⬚&quot;)</code>: string to use for unchecked</li><li><code>scroll::Symbol=:nowrap</code>: If <code>:wrap</code> wrap cursor around top and bottom, if :<code>nowrap</code> do not wrap cursor</li><li><code>supress_output::Bool=false</code>: Ignored legacy argument, pass <code>suppress_output</code> as a keyword argument to <code>request</code> instead.</li><li><code>ctrl_c_interrupt::Bool=true</code>: If <code>false</code>, return empty on ^C, if <code>true</code> throw InterruptException() on ^C</li></ul><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>As of Julia 1.6, <code>config</code> is deprecated. Use <code>Config</code> or <code>MultiSelectConfig</code> instead.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/REPL/src/TerminalMenus/config.jl#L117-L135">source</a></section></article><h4 id="User-interaction"><a class="docs-heading-anchor" href="#User-interaction">User interaction</a><a id="User-interaction-1"></a><a class="docs-heading-anchor-permalink" href="#User-interaction" title="Permalink"></a></h4><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="REPL.TerminalMenus.request" href="#REPL.TerminalMenus.request"><code>REPL.TerminalMenus.request</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">request(m::AbstractMenu; cursor=1)</code></pre><p>Display the menu and enter interactive mode. <code>cursor</code> indicates the item number used for the initial cursor position. <code>cursor</code> can be either an <code>Int</code> or a <code>RefValue{Int}</code>. The latter is useful for observation and control of the cursor position from the outside.</p><p>Returns <code>selected(m)</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>The <code>cursor</code> argument requires Julia 1.6 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/REPL/src/TerminalMenus/AbstractMenu.jl#L166-L178">source</a></section><section><div><pre><code class="language-julia hljs">request([term,] msg::AbstractString, m::AbstractMenu)</code></pre><p>Shorthand for <code>println(msg); request(m)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/REPL/src/TerminalMenus/AbstractMenu.jl#L250-L254">source</a></section></article><h4 id="AbstractMenu-extension-interface"><a class="docs-heading-anchor" href="#AbstractMenu-extension-interface">AbstractMenu extension interface</a><a id="AbstractMenu-extension-interface-1"></a><a class="docs-heading-anchor-permalink" href="#AbstractMenu-extension-interface" title="Permalink"></a></h4><p>Any subtype of <code>AbstractMenu</code> must be mutable, and must contain the fields <code>pagesize::Int</code> and <code>pageoffset::Int</code>. Any subtype must also implement the following functions:</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="REPL.TerminalMenus.pick" href="#REPL.TerminalMenus.pick"><code>REPL.TerminalMenus.pick</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">pick(m::AbstractMenu, cursor::Int)</code></pre><p>Defines what happens when a user presses the Enter key while the menu is open. If <code>true</code> is returned, <code>request()</code> will exit. <code>cursor</code> indexes the position of the selection.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/REPL/src/TerminalMenus/AbstractMenu.jl#L76-L82">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="REPL.TerminalMenus.cancel" href="#REPL.TerminalMenus.cancel"><code>REPL.TerminalMenus.cancel</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cancel(m::AbstractMenu)</code></pre><p>Define what happens when a user cancels (&#39;q&#39; or ctrl-c) a menu. <code>request()</code> will always exit after calling this function.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/REPL/src/TerminalMenus/AbstractMenu.jl#L85-L90">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="REPL.TerminalMenus.writeline" href="#REPL.TerminalMenus.writeline"><code>REPL.TerminalMenus.writeline</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">writeline(buf::IO, m::AbstractMenu, idx::Int, iscursor::Bool)</code></pre><p>Write the option at index <code>idx</code> to <code>buf</code>. <code>iscursor</code>, if <code>true</code>, indicates that this item is at the current cursor position (the one that will be selected by hitting &quot;Enter&quot;).</p><p>If <code>m</code> is a <code>ConfiguredMenu</code>, <code>TerminalMenus</code> will print the cursor indicator. Otherwise the callee is expected to handle such printing.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p><code>writeline</code> requires Julia 1.6 or higher.</p><p>On older versions of Julia, this was     <code>writeLine(buf::IO, m::AbstractMenu, idx, iscursor::Bool)</code> and <code>m</code> is assumed to be unconfigured. The selection and cursor indicators can be obtained from <code>TerminalMenus.CONFIG</code>.</p><p>This older function is supported on all Julia 1.x versions but will be dropped in Julia 2.0.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/REPL/src/TerminalMenus/AbstractMenu.jl#L102-L120">source</a></section></article><p>It must also implement either <code>options</code> or <code>numoptions</code>:</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="REPL.TerminalMenus.options" href="#REPL.TerminalMenus.options"><code>REPL.TerminalMenus.options</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">options(m::AbstractMenu)</code></pre><p>Return a list of strings to be displayed as options in the current page.</p><p>Alternatively, implement <code>numoptions</code>, in which case <code>options</code> is not needed.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/REPL/src/TerminalMenus/AbstractMenu.jl#L93-L99">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="REPL.TerminalMenus.numoptions" href="#REPL.TerminalMenus.numoptions"><code>REPL.TerminalMenus.numoptions</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">numoptions(m::AbstractMenu) -&gt; Int</code></pre><p>Return the number of options in menu <code>m</code>. Defaults to <code>length(options(m))</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>This function requires Julia 1.6 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/REPL/src/TerminalMenus/AbstractMenu.jl#L148-L155">source</a></section></article><p>If the subtype does not have a field named <code>selected</code>, it must also implement</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="REPL.TerminalMenus.selected" href="#REPL.TerminalMenus.selected"><code>REPL.TerminalMenus.selected</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">selected(m::AbstractMenu)</code></pre><p>Return information about the user-selected option. By default it returns <code>m.selected</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/REPL/src/TerminalMenus/AbstractMenu.jl#L158-L163">source</a></section></article><p>The following are optional but can allow additional customization:</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="REPL.TerminalMenus.header" href="#REPL.TerminalMenus.header"><code>REPL.TerminalMenus.header</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">header(m::AbstractMenu) -&gt; String</code></pre><p>Return a header string to be printed above the menu. Defaults to &quot;&quot;.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/REPL/src/TerminalMenus/AbstractMenu.jl#L131-L136">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="REPL.TerminalMenus.keypress" href="#REPL.TerminalMenus.keypress"><code>REPL.TerminalMenus.keypress</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">keypress(m::AbstractMenu, i::UInt32) -&gt; Bool</code></pre><p>Handle any non-standard keypress event. If <code>true</code> is returned, <a href="REPL.html#REPL.TerminalMenus.request"><code>TerminalMenus.request</code></a> will exit. Defaults to <code>false</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/REPL/src/TerminalMenus/AbstractMenu.jl#L139-L145">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="Profile.html">« Profiling</a><a class="docs-footer-nextpage" href="Random.html">Random Numbers »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
