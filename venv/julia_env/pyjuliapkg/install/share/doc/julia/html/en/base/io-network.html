<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>I/O and Network · The Julia Language</title><meta name="title" content="I/O and Network · The Julia Language"/><meta property="og:title" content="I/O and Network · The Julia Language"/><meta property="twitter:title" content="I/O and Network · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox" checked/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="base.html">Essentials</a></li><li><a class="tocitem" href="collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="math.html">Mathematics</a></li><li><a class="tocitem" href="numbers.html">Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="arrays.html">Arrays</a></li><li><a class="tocitem" href="parallel.html">Tasks</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="constants.html">Constants</a></li><li><a class="tocitem" href="file.html">Filesystem</a></li><li class="is-active"><a class="tocitem" href="io-network.html">I/O and Network</a><ul class="internal"><li><a class="tocitem" href="#General-I/O"><span>General I/O</span></a></li><li><a class="tocitem" href="#Text-I/O"><span>Text I/O</span></a></li><li><a class="tocitem" href="#Multimedia-I/O"><span>Multimedia I/O</span></a></li><li><a class="tocitem" href="#Network-I/O"><span>Network I/O</span></a></li></ul></li><li><a class="tocitem" href="punctuation.html">Punctuation</a></li><li><a class="tocitem" href="sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="c.html">C Interface</a></li><li><a class="tocitem" href="libc.html">C Standard Library</a></li><li><a class="tocitem" href="stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Base</a></li><li class="is-active"><a href="io-network.html">I/O and Network</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="io-network.html">I/O and Network</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/base/io-network.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="I/O-and-Network"><a class="docs-heading-anchor" href="#I/O-and-Network">I/O and Network</a><a id="I/O-and-Network-1"></a><a class="docs-heading-anchor-permalink" href="#I/O-and-Network" title="Permalink"></a></h1><h2 id="General-I/O"><a class="docs-heading-anchor" href="#General-I/O">General I/O</a><a id="General-I/O-1"></a><a class="docs-heading-anchor-permalink" href="#General-I/O" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.stdout" href="#Base.stdout"><code>Base.stdout</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">stdout::IO</code></pre><p>Global variable referring to the standard out stream.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/libuv.jl#L160-L164">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.stderr" href="#Base.stderr"><code>Base.stderr</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">stderr::IO</code></pre><p>Global variable referring to the standard error stream.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/libuv.jl#L167-L171">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.stdin" href="#Base.stdin"><code>Base.stdin</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">stdin::IO</code></pre><p>Global variable referring to the standard input stream.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/libuv.jl#L153-L157">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.read-Tuple{AbstractString}" href="#Base.read-Tuple{AbstractString}"><code>Base.read</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">read(filename::AbstractString)</code></pre><p>Read the entire contents of a file as a <code>Vector{UInt8}</code>.</p><pre><code class="nohighlight hljs">read(filename::AbstractString, String)</code></pre><p>Read the entire contents of a file as a string.</p><pre><code class="nohighlight hljs">read(filename::AbstractString, args...)</code></pre><p>Open a file and read its contents. <code>args</code> is passed to <code>read</code>: this is equivalent to <code>open(io-&gt;read(io, args...), filename)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L491-L504">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.write-Tuple{AbstractString, Any}" href="#Base.write-Tuple{AbstractString, Any}"><code>Base.write</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">write(filename::AbstractString, content)</code></pre><p>Write the canonical binary representation of <code>content</code> to a file, which will be created if it does not exist yet or overwritten if it does exist.</p><p>Return the number of bytes written into the file.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L482-L488">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.open" href="#Base.open"><code>Base.open</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">open(f::Function, args...; kwargs...)</code></pre><p>Apply the function <code>f</code> to the result of <code>open(args...; kwargs...)</code> and close the resulting file descriptor upon completion.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; write(&quot;myfile.txt&quot;, &quot;Hello world!&quot;);

julia&gt; open(io-&gt;read(io, String), &quot;myfile.txt&quot;)
&quot;Hello world!&quot;

julia&gt; rm(&quot;myfile.txt&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L391-L406">source</a></section><section><div><pre><code class="language-julia hljs">open(filename::AbstractString; lock = true, keywords...) -&gt; IOStream</code></pre><p>Open a file in a mode specified by five boolean keyword arguments:</p><table><tr><th style="text-align: left">Keyword</th><th style="text-align: left">Description</th><th style="text-align: left">Default</th></tr><tr><td style="text-align: left"><code>read</code></td><td style="text-align: left">open for reading</td><td style="text-align: left"><code>!write</code></td></tr><tr><td style="text-align: left"><code>write</code></td><td style="text-align: left">open for writing</td><td style="text-align: left"><code>truncate | append</code></td></tr><tr><td style="text-align: left"><code>create</code></td><td style="text-align: left">create if non-existent</td><td style="text-align: left"><code>!read &amp; write | truncate | append</code></td></tr><tr><td style="text-align: left"><code>truncate</code></td><td style="text-align: left">truncate to zero size</td><td style="text-align: left"><code>!read &amp; write</code></td></tr><tr><td style="text-align: left"><code>append</code></td><td style="text-align: left">seek to end</td><td style="text-align: left"><code>false</code></td></tr></table><p>The default when no keywords are passed is to open files for reading only. Returns a stream for accessing the opened file.</p><p>The <code>lock</code> keyword argument controls whether operations will be locked for safe multi-threaded access.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p>The <code>lock</code> argument is available as of Julia 1.5.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/iostream.jl#L255-L276">source</a></section><section><div><pre><code class="language-julia hljs">open(filename::AbstractString, [mode::AbstractString]; lock = true) -&gt; IOStream</code></pre><p>Alternate syntax for open, where a string-based mode specifier is used instead of the five booleans. The values of <code>mode</code> correspond to those from <code>fopen(3)</code> or Perl <code>open</code>, and are equivalent to setting the following boolean groups:</p><table><tr><th style="text-align: left">Mode</th><th style="text-align: left">Description</th><th style="text-align: left">Keywords</th></tr><tr><td style="text-align: left"><code>r</code></td><td style="text-align: left">read</td><td style="text-align: left">none</td></tr><tr><td style="text-align: left"><code>w</code></td><td style="text-align: left">write, create, truncate</td><td style="text-align: left"><code>write = true</code></td></tr><tr><td style="text-align: left"><code>a</code></td><td style="text-align: left">write, create, append</td><td style="text-align: left"><code>append = true</code></td></tr><tr><td style="text-align: left"><code>r+</code></td><td style="text-align: left">read, write</td><td style="text-align: left"><code>read = true, write = true</code></td></tr><tr><td style="text-align: left"><code>w+</code></td><td style="text-align: left">read, write, create, truncate</td><td style="text-align: left"><code>truncate = true, read = true</code></td></tr><tr><td style="text-align: left"><code>a+</code></td><td style="text-align: left">read, write, create, append</td><td style="text-align: left"><code>append = true, read = true</code></td></tr></table><p>The <code>lock</code> keyword argument controls whether operations will be locked for safe multi-threaded access.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; io = open(&quot;myfile.txt&quot;, &quot;w&quot;);

julia&gt; write(io, &quot;Hello world!&quot;);

julia&gt; close(io);

julia&gt; io = open(&quot;myfile.txt&quot;, &quot;r&quot;);

julia&gt; read(io, String)
&quot;Hello world!&quot;

julia&gt; write(io, &quot;This file is read only&quot;)
ERROR: ArgumentError: write failed, IOStream is not writeable
[...]

julia&gt; close(io)

julia&gt; io = open(&quot;myfile.txt&quot;, &quot;a&quot;);

julia&gt; write(io, &quot;This stream is not read only&quot;)
28

julia&gt; close(io)

julia&gt; rm(&quot;myfile.txt&quot;)</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p>The <code>lock</code> argument is available as of Julia 1.5.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/iostream.jl#L306-L356">source</a></section><section><div><pre><code class="language-julia hljs">open(fd::OS_HANDLE) -&gt; IO</code></pre><p>Take a raw file descriptor wrap it in a Julia-aware IO type, and take ownership of the fd handle. Call <code>open(Libc.dup(fd))</code> to avoid the ownership capture of the original handle.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Do not call this on a handle that&#39;s already owned by some other part of the system.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stream.jl#L318-L329">source</a></section><section><div><pre><code class="language-julia hljs">open(command, mode::AbstractString, stdio=devnull)</code></pre><p>Run <code>command</code> asynchronously. Like <code>open(command, stdio; read, write)</code> except specifying the read and write flags via a mode string instead of keyword arguments. Possible mode strings are:</p><table><tr><th style="text-align: left">Mode</th><th style="text-align: left">Description</th><th style="text-align: left">Keywords</th></tr><tr><td style="text-align: left"><code>r</code></td><td style="text-align: left">read</td><td style="text-align: left">none</td></tr><tr><td style="text-align: left"><code>w</code></td><td style="text-align: left">write</td><td style="text-align: left"><code>write = true</code></td></tr><tr><td style="text-align: left"><code>r+</code></td><td style="text-align: left">read, write</td><td style="text-align: left"><code>read = true, write = true</code></td></tr><tr><td style="text-align: left"><code>w+</code></td><td style="text-align: left">read, write</td><td style="text-align: left"><code>read = true, write = true</code></td></tr></table></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/process.jl#L360-L373">source</a></section><section><div><pre><code class="language-julia hljs">open(command, stdio=devnull; write::Bool = false, read::Bool = !write)</code></pre><p>Start running <code>command</code> asynchronously, and return a <code>process::IO</code> object.  If <code>read</code> is true, then reads from the process come from the process&#39;s standard output and <code>stdio</code> optionally specifies the process&#39;s standard input stream.  If <code>write</code> is true, then writes go to the process&#39;s standard input and <code>stdio</code> optionally specifies the process&#39;s standard output stream. The process&#39;s standard error stream is connected to the current global <code>stderr</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/process.jl#L387-L396">source</a></section><section><div><pre><code class="language-julia hljs">open(f::Function, command, args...; kwargs...)</code></pre><p>Similar to <code>open(command, args...; kwargs...)</code>, but calls <code>f(stream)</code> on the resulting process stream, then closes the input stream and waits for the process to complete. Return the value returned by <code>f</code> on success. Throw an error if the process failed, or if the process attempts to print anything to stdout.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/process.jl#L420-L427">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.IOStream" href="#Base.IOStream"><code>Base.IOStream</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">IOStream</code></pre><p>A buffered IO stream wrapping an OS file descriptor. Mostly used to represent files returned by <a href="io-network.html#Base.open"><code>open</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/iostream.jl#L7-L12">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.IOBuffer" href="#Base.IOBuffer"><code>Base.IOBuffer</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">IOBuffer([data::AbstractVector{UInt8}]; keywords...) -&gt; IOBuffer</code></pre><p>Create an in-memory I/O stream, which may optionally operate on a pre-existing array.</p><p>It may take optional keyword arguments:</p><ul><li><code>read</code>, <code>write</code>, <code>append</code>: restricts operations to the buffer; see <code>open</code> for details.</li><li><code>truncate</code>: truncates the buffer size to zero length.</li><li><code>maxsize</code>: specifies a size beyond which the buffer may not be grown.</li><li><code>sizehint</code>: suggests a capacity of the buffer (<code>data</code> must implement <code>sizehint!(data, size)</code>).</li></ul><p>When <code>data</code> is not given, the buffer will be both readable and writable by default.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; io = IOBuffer();

julia&gt; write(io, &quot;JuliaLang is a GitHub organization.&quot;, &quot; It has many members.&quot;)
56

julia&gt; String(take!(io))
&quot;JuliaLang is a GitHub organization. It has many members.&quot;

julia&gt; io = IOBuffer(b&quot;JuliaLang is a GitHub organization.&quot;)
IOBuffer(data=UInt8[...], readable=true, writable=false, seekable=true, append=false, size=35, maxsize=Inf, ptr=1, mark=-1)

julia&gt; read(io, String)
&quot;JuliaLang is a GitHub organization.&quot;

julia&gt; write(io, &quot;This isn&#39;t writable.&quot;)
ERROR: ArgumentError: ensureroom failed, IOBuffer is not writeable

julia&gt; io = IOBuffer(UInt8[], read=true, write=true, maxsize=34)
IOBuffer(data=UInt8[...], readable=true, writable=true, seekable=true, append=false, size=0, maxsize=34, ptr=1, mark=-1)

julia&gt; write(io, &quot;JuliaLang is a GitHub organization.&quot;)
34

julia&gt; String(take!(io))
&quot;JuliaLang is a GitHub organization&quot;

julia&gt; length(read(IOBuffer(b&quot;data&quot;, read=true, truncate=false)))
4

julia&gt; length(read(IOBuffer(b&quot;data&quot;, read=true, truncate=true)))
0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/iobuffer.jl#L49-L96">source</a></section><section><div><pre><code class="language-julia hljs">IOBuffer(string::String)</code></pre><p>Create a read-only <code>IOBuffer</code> on the data underlying the given string.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; io = IOBuffer(&quot;Haho&quot;);

julia&gt; String(take!(io))
&quot;Haho&quot;

julia&gt; String(take!(io))
&quot;Haho&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/io.jl#L292-L307">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.take!-Tuple{Base.GenericIOBuffer}" href="#Base.take!-Tuple{Base.GenericIOBuffer}"><code>Base.take!</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">take!(b::IOBuffer)</code></pre><p>Obtain the contents of an <code>IOBuffer</code> as an array. Afterwards, the <code>IOBuffer</code> is reset to its initial state.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; io = IOBuffer();

julia&gt; write(io, &quot;JuliaLang is a GitHub organization.&quot;, &quot; It has many members.&quot;)
56

julia&gt; String(take!(io))
&quot;JuliaLang is a GitHub organization. It has many members.&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/iobuffer.jl#L430-L445">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Pipe" href="#Base.Pipe"><code>Base.Pipe</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Pipe()</code></pre><p>Construct an uninitialized Pipe object, especially for IO communication between multiple processes.</p><p>The appropriate end of the pipe will be automatically initialized if the object is used in process spawning. This can be useful to easily obtain references in process pipelines, e.g.:</p><pre><code class="nohighlight hljs">julia&gt; err = Pipe()

# After this `err` will be initialized and you may read `foo`&#39;s
# stderr from the `err` pipe, or pass `err` to other pipelines.
julia&gt; run(pipeline(pipeline(`foo`, stderr=err), `cat`), wait=false)

# Now destroy the write half of the pipe, so that the read half will get EOF
julia&gt; closewrite(err)

julia&gt; read(err, String)
&quot;stderr messages&quot;</code></pre><p>See also <a href="io-network.html#Base.link_pipe!"><code>Base.link_pipe!</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stream.jl#L754-L777">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.link_pipe!" href="#Base.link_pipe!"><code>Base.link_pipe!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">link_pipe!(pipe; reader_supports_async=false, writer_supports_async=false)</code></pre><p>Initialize <code>pipe</code> and link the <code>in</code> endpoint to the <code>out</code> endpoint. The keyword arguments <code>reader_supports_async</code>/<code>writer_supports_async</code> correspond to <code>OVERLAPPED</code> on Windows and <code>O_NONBLOCK</code> on POSIX systems. They should be <code>true</code> unless they&#39;ll be used by an external program (e.g. the output of a command executed with <a href="base.html#Base.run"><code>run</code></a>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stream.jl#L782-L790">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.fdio" href="#Base.fdio"><code>Base.fdio</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">fdio([name::AbstractString, ]fd::Integer[, own::Bool=false]) -&gt; IOStream</code></pre><p>Create an <a href="io-network.html#Base.IOStream"><code>IOStream</code></a> object from an integer file descriptor. If <code>own</code> is <code>true</code>, closing this object will close the underlying descriptor. By default, an <code>IOStream</code> is closed when it is garbage collected. <code>name</code> allows you to associate the descriptor with a named file.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/iostream.jl#L240-L246">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.flush" href="#Base.flush"><code>Base.flush</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">flush(stream)</code></pre><p>Commit all currently buffered writes to the given stream.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L99-L103">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.close" href="#Base.close"><code>Base.close</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">close(stream)</code></pre><p>Close an I/O stream. Performs a <a href="io-network.html#Base.flush"><code>flush</code></a> first.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L65-L69">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.closewrite" href="#Base.closewrite"><code>Base.closewrite</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">closewrite(stream)</code></pre><p>Shutdown the write half of a full-duplex I/O stream. Performs a <a href="io-network.html#Base.flush"><code>flush</code></a> first. Notify the other end that no more data will be written to the underlying file. This is not supported by all IO types.</p><p>If implemented, <code>closewrite</code> causes subsequent <code>read</code> or <code>eof</code> calls that would block to instead throw EOF or return true, respectively. If the stream is already closed, this is idempotent.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; io = Base.BufferStream(); # this never blocks, so we can read and write on the same Task

julia&gt; write(io, &quot;request&quot;);

julia&gt; # calling `read(io)` here would block forever

julia&gt; closewrite(io);

julia&gt; read(io, String)
&quot;request&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L72-L96">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.write" href="#Base.write"><code>Base.write</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">write(io::IO, x)</code></pre><p>Write the canonical binary representation of a value to the given I/O stream or file. Return the number of bytes written into the stream. See also <a href="io-network.html#Base.print"><code>print</code></a> to write a text representation (with an encoding that may depend upon <code>io</code>).</p><p>The endianness of the written value depends on the endianness of the host system. Convert to/from a fixed endianness when writing/reading (e.g. using  <a href="io-network.html#Base.htol"><code>htol</code></a> and <a href="io-network.html#Base.ltoh"><code>ltoh</code></a>) to get results that are consistent across platforms.</p><p>You can write multiple values with the same <code>write</code> call. i.e. the following are equivalent:</p><pre><code class="nohighlight hljs">write(io, x, y...)
write(io, x) + write(io, y...)</code></pre><p><strong>Examples</strong></p><p>Consistent serialization:</p><pre><code class="language-julia-repl hljs">julia&gt; fname = tempname(); # random temporary filename

julia&gt; open(fname,&quot;w&quot;) do f
           # Make sure we write 64bit integer in little-endian byte order
           write(f,htol(Int64(42)))
       end
8

julia&gt; open(fname,&quot;r&quot;) do f
           # Convert back to host byte order and host integer type
           Int(ltoh(read(f,Int64)))
       end
42</code></pre><p>Merging write calls:</p><pre><code class="language-julia-repl hljs">julia&gt; io = IOBuffer();

julia&gt; write(io, &quot;JuliaLang is a GitHub organization.&quot;, &quot; It has many members.&quot;)
56

julia&gt; String(take!(io))
&quot;JuliaLang is a GitHub organization. It has many members.&quot;

julia&gt; write(io, &quot;Sometimes those members&quot;) + write(io, &quot; write documentation.&quot;)
44

julia&gt; String(take!(io))
&quot;Sometimes those members write documentation.&quot;</code></pre><p>User-defined plain-data types without <code>write</code> methods can be written when wrapped in a <code>Ref</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; struct MyStruct; x::Float64; end

julia&gt; io = IOBuffer()
IOBuffer(data=UInt8[...], readable=true, writable=true, seekable=true, append=false, size=0, maxsize=Inf, ptr=1, mark=-1)

julia&gt; write(io, Ref(MyStruct(42.0)))
8

julia&gt; seekstart(io); read!(io, Ref(MyStruct(NaN)))
Base.RefValue{MyStruct}(MyStruct(42.0))</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L239-L302">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.read" href="#Base.read"><code>Base.read</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">read(io::IO, T)</code></pre><p>Read a single value of type <code>T</code> from <code>io</code>, in canonical binary representation.</p><p>Note that Julia does not convert the endianness for you. Use <a href="io-network.html#Base.ntoh"><code>ntoh</code></a> or <a href="io-network.html#Base.ltoh"><code>ltoh</code></a> for this purpose.</p><pre><code class="nohighlight hljs">read(io::IO, String)</code></pre><p>Read the entirety of <code>io</code>, as a <code>String</code> (see also <a href="io-network.html#Base.readchomp"><code>readchomp</code></a>).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; io = IOBuffer(&quot;JuliaLang is a GitHub organization&quot;);

julia&gt; read(io, Char)
&#39;J&#39;: ASCII/Unicode U+004A (category Lu: Letter, uppercase)

julia&gt; io = IOBuffer(&quot;JuliaLang is a GitHub organization&quot;);

julia&gt; read(io, String)
&quot;JuliaLang is a GitHub organization&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L210-L234">source</a></section><section><div><pre><code class="language-julia hljs">read(filename::AbstractString)</code></pre><p>Read the entire contents of a file as a <code>Vector{UInt8}</code>.</p><pre><code class="nohighlight hljs">read(filename::AbstractString, String)</code></pre><p>Read the entire contents of a file as a string.</p><pre><code class="nohighlight hljs">read(filename::AbstractString, args...)</code></pre><p>Open a file and read its contents. <code>args</code> is passed to <code>read</code>: this is equivalent to <code>open(io-&gt;read(io, args...), filename)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L491-L504">source</a></section><section><div><pre><code class="language-julia hljs">read(s::IO, nb=typemax(Int))</code></pre><p>Read at most <code>nb</code> bytes from <code>s</code>, returning a <code>Vector{UInt8}</code> of the bytes read.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L1166-L1170">source</a></section><section><div><pre><code class="language-julia hljs">read(s::IOStream, nb::Integer; all=true)</code></pre><p>Read at most <code>nb</code> bytes from <code>s</code>, returning a <code>Vector{UInt8}</code> of the bytes read.</p><p>If <code>all</code> is <code>true</code> (the default), this function will block repeatedly trying to read all requested bytes, until an error or end-of-file occurs. If <code>all</code> is <code>false</code>, at most one <code>read</code> call is performed, and the amount of data returned is device-dependent. Note that not all stream types support the <code>all</code> option.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/iostream.jl#L585-L594">source</a></section><section><div><pre><code class="language-julia hljs">read(command::Cmd)</code></pre><p>Run <code>command</code> and return the resulting output as an array of bytes.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/process.jl#L474-L478">source</a></section><section><div><pre><code class="language-julia hljs">read(command::Cmd, String)</code></pre><p>Run <code>command</code> and return the resulting output as a <code>String</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/process.jl#L486-L490">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.read!" href="#Base.read!"><code>Base.read!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">read!(stream::IO, array::AbstractArray)
read!(filename::AbstractString, array::AbstractArray)</code></pre><p>Read binary data from an I/O stream or file, filling in <code>array</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L509-L514">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.readbytes!" href="#Base.readbytes!"><code>Base.readbytes!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">readbytes!(stream::IO, b::AbstractVector{UInt8}, nb=length(b))</code></pre><p>Read at most <code>nb</code> bytes from <code>stream</code> into <code>b</code>, returning the number of bytes read. The size of <code>b</code> will be increased if needed (i.e. if <code>nb</code> is greater than <code>length(b)</code> and enough bytes could be read), but it will never be decreased.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L1140-L1146">source</a></section><section><div><pre><code class="language-julia hljs">readbytes!(stream::IOStream, b::AbstractVector{UInt8}, nb=length(b); all::Bool=true)</code></pre><p>Read at most <code>nb</code> bytes from <code>stream</code> into <code>b</code>, returning the number of bytes read. The size of <code>b</code> will be increased if needed (i.e. if <code>nb</code> is greater than <code>length(b)</code> and enough bytes could be read), but it will never be decreased.</p><p>If <code>all</code> is <code>true</code> (the default), this function will block repeatedly trying to read all requested bytes, until an error or end-of-file occurs. If <code>all</code> is <code>false</code>, at most one <code>read</code> call is performed, and the amount of data returned is device-dependent. Note that not all stream types support the <code>all</code> option.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/iostream.jl#L535-L546">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unsafe_read" href="#Base.unsafe_read"><code>Base.unsafe_read</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unsafe_read(io::IO, ref, nbytes::UInt)</code></pre><p>Copy <code>nbytes</code> from the <code>IO</code> stream object into <code>ref</code> (converted to a pointer).</p><p>It is recommended that subtypes <code>T&lt;:IO</code> override the following method signature to provide more efficient implementations: <code>unsafe_read(s::T, p::Ptr{UInt8}, n::UInt)</code></p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L325-L333">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unsafe_write" href="#Base.unsafe_write"><code>Base.unsafe_write</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unsafe_write(io::IO, ref, nbytes::UInt)</code></pre><p>Copy <code>nbytes</code> from <code>ref</code> (converted to a pointer) into the <code>IO</code> object.</p><p>It is recommended that subtypes <code>T&lt;:IO</code> override the following method signature to provide more efficient implementations: <code>unsafe_write(s::T, p::Ptr{UInt8}, n::UInt)</code></p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L308-L316">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.readeach" href="#Base.readeach"><code>Base.readeach</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">readeach(io::IO, T)</code></pre><p>Return an iterable object yielding <a href="file.html#Base.read-Tuple{String}"><code>read(io, T)</code></a>.</p><p>See also <a href="io-network.html#Base.skipchars"><code>skipchars</code></a>, <a href="io-network.html#Base.eachline"><code>eachline</code></a>, <a href="io-network.html#Base.readuntil"><code>readuntil</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p><code>readeach</code> requires Julia 1.6 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; io = IOBuffer(&quot;JuliaLang is a GitHub organization.\n It has many members.\n&quot;);

julia&gt; for c in readeach(io, Char)
           c == &#39;\n&#39; &amp;&amp; break
           print(c)
       end
JuliaLang is a GitHub organization.</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L1358-L1378">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.peek" href="#Base.peek"><code>Base.peek</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">peek(stream[, T=UInt8])</code></pre><p>Read and return a value of type <code>T</code> from a stream without advancing the current position in the stream.   See also <a href="strings.html#Base.startswith"><code>startswith(stream, char_or_string)</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; b = IOBuffer(&quot;julia&quot;);

julia&gt; peek(b)
0x6a

julia&gt; position(b)
0

julia&gt; peek(b, Char)
&#39;j&#39;: ASCII/Unicode U+006A (category Ll: Letter, lowercase)</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p>The method which accepts a type requires Julia 1.5 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/essentials.jl#L1178-L1201">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.position" href="#Base.position"><code>Base.position</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">position(l::Lexer)</code></pre><p>Returns the current position.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base//Users/<USER>/.julia/scratchspaces/a66863c6-20e8-4ff4-8a62-49f30b1f605e/agent-cache/default-grannysmith-C07ZQ07RJYVY.0/build/default-grannysmith-C07ZQ07RJYVY-0/julialang/julia-release-1-dot-11/base/JuliaSyntax/src/tokenize.jl#L354-L358">source</a></section><section><div><pre><code class="language-julia hljs">position(s)</code></pre><p>Get the current position of a stream.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; io = IOBuffer(&quot;JuliaLang is a GitHub organization.&quot;);

julia&gt; seek(io, 5);

julia&gt; position(io)
5

julia&gt; skip(io, 10);

julia&gt; position(io)
15

julia&gt; seekend(io);

julia&gt; position(io)
35</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/iostream.jl#L193-L217">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.seek" href="#Base.seek"><code>Base.seek</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">seek(s, pos)</code></pre><p>Seek a stream to the given position.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; io = IOBuffer(&quot;JuliaLang is a GitHub organization.&quot;);

julia&gt; seek(io, 5);

julia&gt; read(io, Char)
&#39;L&#39;: ASCII/Unicode U+004C (category Lu: Letter, uppercase)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/iostream.jl#L114-L128">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.seekstart" href="#Base.seekstart"><code>Base.seekstart</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">seekstart(s)</code></pre><p>Seek a stream to its beginning.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; io = IOBuffer(&quot;JuliaLang is a GitHub organization.&quot;);

julia&gt; seek(io, 5);

julia&gt; read(io, Char)
&#39;L&#39;: ASCII/Unicode U+004C (category Lu: Letter, uppercase)

julia&gt; seekstart(io);

julia&gt; read(io, Char)
&#39;J&#39;: ASCII/Unicode U+004A (category Lu: Letter, uppercase)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/iostream.jl#L136-L155">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.seekend" href="#Base.seekend"><code>Base.seekend</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">seekend(s)</code></pre><p>Seek a stream to its end.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/iostream.jl#L158-L162">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.skip" href="#Base.skip"><code>Base.skip</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">skip(s, offset)</code></pre><p>Seek a stream relative to the current position.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; io = IOBuffer(&quot;JuliaLang is a GitHub organization.&quot;);

julia&gt; seek(io, 5);

julia&gt; skip(io, 10);

julia&gt; read(io, Char)
&#39;G&#39;: ASCII/Unicode U+0047 (category Lu: Letter, uppercase)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/iostream.jl#L169-L185">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.mark" href="#Base.mark"><code>Base.mark</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mark(s::IO)</code></pre><p>Add a mark at the current position of stream <code>s</code>. Return the marked position.</p><p>See also <a href="io-network.html#Base.unmark"><code>unmark</code></a>, <a href="io-network.html#Base.reset-Tuple{IO}"><code>reset</code></a>, <a href="io-network.html#Base.ismarked"><code>ismarked</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L1395-L1401">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unmark" href="#Base.unmark"><code>Base.unmark</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unmark(s::IO)</code></pre><p>Remove a mark from stream <code>s</code>. Return <code>true</code> if the stream was marked, <code>false</code> otherwise.</p><p>See also <a href="io-network.html#Base.mark"><code>mark</code></a>, <a href="io-network.html#Base.reset-Tuple{IO}"><code>reset</code></a>, <a href="io-network.html#Base.ismarked"><code>ismarked</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L1406-L1412">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.reset-Tuple{IO}" href="#Base.reset-Tuple{IO}"><code>Base.reset</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">reset(s::IO)</code></pre><p>Reset a stream <code>s</code> to a previously marked position, and remove the mark. Return the previously marked position. Throw an error if the stream is not marked.</p><p>See also <a href="io-network.html#Base.mark"><code>mark</code></a>, <a href="io-network.html#Base.unmark"><code>unmark</code></a>, <a href="io-network.html#Base.ismarked"><code>ismarked</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L1419-L1426">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.ismarked" href="#Base.ismarked"><code>Base.ismarked</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ismarked(s::IO)</code></pre><p>Return <code>true</code> if stream <code>s</code> is marked.</p><p>See also <a href="io-network.html#Base.mark"><code>mark</code></a>, <a href="io-network.html#Base.unmark"><code>unmark</code></a>, <a href="io-network.html#Base.reset-Tuple{IO}"><code>reset</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L1435-L1441">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.eof" href="#Base.eof"><code>Base.eof</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">eof(stream) -&gt; Bool</code></pre><p>Test whether an I/O stream is at end-of-file. If the stream is not yet exhausted, this function will block to wait for more data if necessary, and then return <code>false</code>. Therefore it is always safe to read one byte after seeing <code>eof</code> return <code>false</code>. <code>eof</code> will return <code>false</code> as long as buffered data is still available, even if the remote end of a connection is closed.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; b = IOBuffer(&quot;my buffer&quot;);

julia&gt; eof(b)
false

julia&gt; seekend(b);

julia&gt; eof(b)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L182-L203">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isreadonly" href="#Base.isreadonly"><code>Base.isreadonly</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isreadonly(io) -&gt; Bool</code></pre><p>Determine whether a stream is read-only.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; io = IOBuffer(&quot;JuliaLang is a GitHub organization&quot;);

julia&gt; isreadonly(io)
true

julia&gt; io = IOBuffer();

julia&gt; isreadonly(io)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L770-L787">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.iswritable" href="#Base.iswritable"><code>Base.iswritable</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">iswritable(path::String)</code></pre><p>Return <code>true</code> if the access permissions for the given <code>path</code> permitted writing by the current user.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This permission may change before the user calls <code>open</code>, so it is recommended to just call <code>open</code> alone and handle the error if that fails, rather than calling <code>iswritable</code> first.</p></div></div><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Currently this function does not correctly interrogate filesystem ACLs on Windows, therefore it can return wrong results.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>This function requires at least Julia 1.11.</p></div></div><p>See also <a href="file.html#Base.Filesystem.ispath"><code>ispath</code></a>, <a href="io-network.html#Base.isexecutable"><code>isexecutable</code></a>, <a href="io-network.html#Base.isreadable"><code>isreadable</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/filesystem.jl#L422-L440">source</a></section><section><div><pre><code class="language-julia hljs">iswritable(io) -&gt; Bool</code></pre><p>Return <code>false</code> if the specified IO object is not writable.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; open(&quot;myfile.txt&quot;, &quot;w&quot;) do io
           print(io, &quot;Hello world!&quot;);
           iswritable(io)
       end
true

julia&gt; open(&quot;myfile.txt&quot;, &quot;r&quot;) do io
           iswritable(io)
       end
false

julia&gt; rm(&quot;myfile.txt&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L159-L179">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isreadable" href="#Base.isreadable"><code>Base.isreadable</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isreadable(path::String)</code></pre><p>Return <code>true</code> if the access permissions for the given <code>path</code> permitted reading by the current user.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This permission may change before the user calls <code>open</code>, so it is recommended to just call <code>open</code> alone and handle the error if that fails, rather than calling <code>isreadable</code> first.</p></div></div><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Currently this function does not correctly interrogate filesystem ACLs on Windows, therefore it can return wrong results.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>This function requires at least Julia 1.11.</p></div></div><p>See also <a href="file.html#Base.Filesystem.ispath"><code>ispath</code></a>, <a href="io-network.html#Base.isexecutable"><code>isexecutable</code></a>, <a href="io-network.html#Base.iswritable"><code>iswritable</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/filesystem.jl#L395-L413">source</a></section><section><div><pre><code class="language-julia hljs">isreadable(io) -&gt; Bool</code></pre><p>Return <code>false</code> if the specified IO object is not readable.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; open(&quot;myfile.txt&quot;, &quot;w&quot;) do io
           print(io, &quot;Hello world!&quot;);
           isreadable(io)
       end
false

julia&gt; open(&quot;myfile.txt&quot;, &quot;r&quot;) do io
           isreadable(io)
       end
true

julia&gt; rm(&quot;myfile.txt&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L136-L156">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isexecutable" href="#Base.isexecutable"><code>Base.isexecutable</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isexecutable(path::String)</code></pre><p>Return <code>true</code> if the given <code>path</code> has executable permissions.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This permission may change before the user executes <code>path</code>, so it is recommended to execute the file and handle the error if that fails, rather than calling <code>isexecutable</code> first.</p></div></div><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Prior to Julia 1.6, this did not correctly interrogate filesystem ACLs on Windows, therefore it would return <code>true</code> for any file.  From Julia 1.6 on, it correctly determines whether the file is marked as executable or not.</p></div></div><p>See also <a href="file.html#Base.Filesystem.ispath"><code>ispath</code></a>, <a href="io-network.html#Base.isreadable"><code>isreadable</code></a>, <a href="io-network.html#Base.iswritable"><code>iswritable</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/filesystem.jl#L369-L386">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isopen" href="#Base.isopen"><code>Base.isopen</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isopen(object) -&gt; Bool</code></pre><p>Determine whether an object - such as a stream or timer – is not yet closed. Once an object is closed, it will never produce a new event. However, since a closed stream may still have data to read in its buffer, use <a href="io-network.html#Base.eof"><code>eof</code></a> to check for the ability to read data. Use the <code>FileWatching</code> package to be notified when a stream might be writable or readable.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; io = open(&quot;my_file.txt&quot;, &quot;w+&quot;);

julia&gt; isopen(io)
true

julia&gt; close(io)

julia&gt; isopen(io)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L41-L62">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.fd" href="#Base.fd"><code>Base.fd</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">fd(stream)</code></pre><p>Return the file descriptor backing the stream or file. Note that this function only applies to synchronous <code>File</code>&#39;s and <code>IOStream</code>&#39;s not to any of the asynchronous streams.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/iostream.jl#L49-L54">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.redirect_stdio" href="#Base.redirect_stdio"><code>Base.redirect_stdio</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">redirect_stdio(;stdin=stdin, stderr=stderr, stdout=stdout)</code></pre><p>Redirect a subset of the streams <code>stdin</code>, <code>stderr</code>, <code>stdout</code>. Each argument must be an <code>IOStream</code>, <code>TTY</code>, <a href="io-network.html#Base.Pipe"><code>Pipe</code></a>, socket, or <code>devnull</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p><code>redirect_stdio</code> requires Julia 1.7 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stream.jl#L1343-L1352">source</a></section><section><div><pre><code class="language-julia hljs">redirect_stdio(f; stdin=nothing, stderr=nothing, stdout=nothing)</code></pre><p>Redirect a subset of the streams <code>stdin</code>, <code>stderr</code>, <code>stdout</code>, call <code>f()</code> and restore each stream.</p><p>Possible values for each stream are:</p><ul><li><code>nothing</code> indicating the stream should not be redirected.</li><li><code>path::AbstractString</code> redirecting the stream to the file at <code>path</code>.</li><li><code>io</code> an <code>IOStream</code>, <code>TTY</code>, <a href="io-network.html#Base.Pipe"><code>Pipe</code></a>, socket, or <code>devnull</code>.</li></ul><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; redirect_stdio(stdout=&quot;stdout.txt&quot;, stderr=&quot;stderr.txt&quot;) do
           print(&quot;hello stdout&quot;)
           print(stderr, &quot;hello stderr&quot;)
       end

julia&gt; read(&quot;stdout.txt&quot;, String)
&quot;hello stdout&quot;

julia&gt; read(&quot;stderr.txt&quot;, String)
&quot;hello stderr&quot;</code></pre><p><strong>Edge cases</strong></p><p>It is possible to pass the same argument to <code>stdout</code> and <code>stderr</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; redirect_stdio(stdout=&quot;log.txt&quot;, stderr=&quot;log.txt&quot;, stdin=devnull) do
    ...
end</code></pre><p>However it is not supported to pass two distinct descriptors of the same file.</p><pre><code class="language-julia-repl hljs">julia&gt; io1 = open(&quot;same/path&quot;, &quot;w&quot;)

julia&gt; io2 = open(&quot;same/path&quot;, &quot;w&quot;)

julia&gt; redirect_stdio(f, stdout=io1, stderr=io2) # not supported</code></pre><p>Also the <code>stdin</code> argument may not be the same descriptor as <code>stdout</code> or <code>stderr</code>.</p><pre><code class="language-julia-repl hljs">julia&gt; io = open(...)

julia&gt; redirect_stdio(f, stdout=io, stdin=io) # not supported</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p><code>redirect_stdio</code> requires Julia 1.7 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stream.jl#L1359-L1410">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.redirect_stdout" href="#Base.redirect_stdout"><code>Base.redirect_stdout</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">redirect_stdout([stream]) -&gt; stream</code></pre><p>Create a pipe to which all C and Julia level <a href="io-network.html#Base.stdout"><code>stdout</code></a> output will be redirected. Return a stream representing the pipe ends. Data written to <a href="io-network.html#Base.stdout"><code>stdout</code></a> may now be read from the <code>rd</code> end of the pipe.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p><code>stream</code> must be a compatible objects, such as an <code>IOStream</code>, <code>TTY</code>, <a href="io-network.html#Base.Pipe"><code>Pipe</code></a>, socket, or <code>devnull</code>.</p></div></div><p>See also <a href="io-network.html#Base.redirect_stdio"><code>redirect_stdio</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stream.jl#L1300-L1313">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.redirect_stdout-Tuple{Function, Any}" href="#Base.redirect_stdout-Tuple{Function, Any}"><code>Base.redirect_stdout</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">redirect_stdout(f::Function, stream)</code></pre><p>Run the function <code>f</code> while redirecting <a href="io-network.html#Base.stdout"><code>stdout</code></a> to <code>stream</code>. Upon completion, <a href="io-network.html#Base.stdout"><code>stdout</code></a> is restored to its prior setting.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stream.jl#L1471-L1476">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.redirect_stderr" href="#Base.redirect_stderr"><code>Base.redirect_stderr</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">redirect_stderr([stream]) -&gt; stream</code></pre><p>Like <a href="io-network.html#Base.redirect_stdout"><code>redirect_stdout</code></a>, but for <a href="io-network.html#Base.stderr"><code>stderr</code></a>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p><code>stream</code> must be a compatible objects, such as an <code>IOStream</code>, <code>TTY</code>, <a href="io-network.html#Base.Pipe"><code>Pipe</code></a>, socket, or <code>devnull</code>.</p></div></div><p>See also <a href="io-network.html#Base.redirect_stdio"><code>redirect_stdio</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stream.jl#L1316-L1326">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.redirect_stderr-Tuple{Function, Any}" href="#Base.redirect_stderr-Tuple{Function, Any}"><code>Base.redirect_stderr</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">redirect_stderr(f::Function, stream)</code></pre><p>Run the function <code>f</code> while redirecting <a href="io-network.html#Base.stderr"><code>stderr</code></a> to <code>stream</code>. Upon completion, <a href="io-network.html#Base.stderr"><code>stderr</code></a> is restored to its prior setting.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stream.jl#L1479-L1484">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.redirect_stdin" href="#Base.redirect_stdin"><code>Base.redirect_stdin</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">redirect_stdin([stream]) -&gt; stream</code></pre><p>Like <a href="io-network.html#Base.redirect_stdout"><code>redirect_stdout</code></a>, but for <a href="io-network.html#Base.stdin"><code>stdin</code></a>. Note that the direction of the stream is reversed.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p><code>stream</code> must be a compatible objects, such as an <code>IOStream</code>, <code>TTY</code>, <a href="io-network.html#Base.Pipe"><code>Pipe</code></a>, socket, or <code>devnull</code>.</p></div></div><p>See also <a href="io-network.html#Base.redirect_stdio"><code>redirect_stdio</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stream.jl#L1329-L1340">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.redirect_stdin-Tuple{Function, Any}" href="#Base.redirect_stdin-Tuple{Function, Any}"><code>Base.redirect_stdin</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">redirect_stdin(f::Function, stream)</code></pre><p>Run the function <code>f</code> while redirecting <a href="io-network.html#Base.stdin"><code>stdin</code></a> to <code>stream</code>. Upon completion, <a href="io-network.html#Base.stdin"><code>stdin</code></a> is restored to its prior setting.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stream.jl#L1487-L1492">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.readchomp" href="#Base.readchomp"><code>Base.readchomp</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">readchomp(x)</code></pre><p>Read the entirety of <code>x</code> as a string and remove a single trailing newline if there is one. Equivalent to <code>chomp(read(x, String))</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; write(&quot;my_file.txt&quot;, &quot;JuliaLang is a GitHub organization.\nIt has many members.\n&quot;);

julia&gt; readchomp(&quot;my_file.txt&quot;)
&quot;JuliaLang is a GitHub organization.\nIt has many members.&quot;

julia&gt; rm(&quot;my_file.txt&quot;);</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L1120-L1135">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.truncate" href="#Base.truncate"><code>Base.truncate</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">truncate(file, n)</code></pre><p>Resize the file or buffer given by the first argument to exactly <code>n</code> bytes, filling previously unallocated space with &#39;\0&#39; if the file or buffer is grown.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; io = IOBuffer();

julia&gt; write(io, &quot;JuliaLang is a GitHub organization.&quot;)
35

julia&gt; truncate(io, 15)
IOBuffer(data=UInt8[...], readable=true, writable=true, seekable=true, append=false, size=15, maxsize=Inf, ptr=16, mark=-1)

julia&gt; String(take!(io))
&quot;JuliaLang is a &quot;

julia&gt; io = IOBuffer();

julia&gt; write(io, &quot;JuliaLang is a GitHub organization.&quot;);

julia&gt; truncate(io, 40);

julia&gt; String(take!(io))
&quot;JuliaLang is a GitHub organization.\0\0\0\0\0&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/iostream.jl#L79-L107">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.skipchars" href="#Base.skipchars"><code>Base.skipchars</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">skipchars(predicate, io::IO; linecomment=nothing)</code></pre><p>Advance the stream <code>io</code> such that the next-read character will be the first remaining for which <code>predicate</code> returns <code>false</code>. If the keyword argument <code>linecomment</code> is specified, all characters from that character until the start of the next line are ignored.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; buf = IOBuffer(&quot;    text&quot;)
IOBuffer(data=UInt8[...], readable=true, writable=false, seekable=true, append=false, size=8, maxsize=Inf, ptr=1, mark=-1)

julia&gt; skipchars(isspace, buf)
IOBuffer(data=UInt8[...], readable=true, writable=false, seekable=true, append=false, size=8, maxsize=Inf, ptr=5, mark=-1)

julia&gt; String(readavailable(buf))
&quot;text&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L1449-L1467">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.countlines" href="#Base.countlines"><code>Base.countlines</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">countlines(io::IO; eol::AbstractChar = &#39;\n&#39;)
countlines(filename::AbstractString; eol::AbstractChar = &#39;\n&#39;)</code></pre><p>Read <code>io</code> until the end of the stream/file and count the number of lines. To specify a file pass the filename as the first argument. EOL markers other than <code>&#39;\n&#39;</code> are supported by passing them as the second argument.  The last non-empty line of <code>io</code> is counted even if it does not end with the EOL, matching the length returned by <a href="io-network.html#Base.eachline"><code>eachline</code></a> and <a href="io-network.html#Base.readlines"><code>readlines</code></a>.</p><p>To count lines of a <code>String</code>, <code>countlines(IOBuffer(str))</code> can be used.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; io = IOBuffer(&quot;JuliaLang is a GitHub organization.\n&quot;);

julia&gt; countlines(io)
1

julia&gt; io = IOBuffer(&quot;JuliaLang is a GitHub organization.&quot;);

julia&gt; countlines(io)
1

julia&gt; eof(io) # counting lines moves the file pointer
true

julia&gt; io = IOBuffer(&quot;JuliaLang is a GitHub organization.&quot;);

julia&gt; countlines(io, eol = &#39;.&#39;)
1</code></pre><pre><code class="language-julia-repl hljs">julia&gt; write(&quot;my_file.txt&quot;, &quot;JuliaLang is a GitHub organization.\n&quot;)
36

julia&gt; countlines(&quot;my_file.txt&quot;)
1

julia&gt; countlines(&quot;my_file.txt&quot;, eol = &#39;n&#39;)
4

julia&gt; rm(&quot;my_file.txt&quot;)
</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L1480-L1524">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.PipeBuffer" href="#Base.PipeBuffer"><code>Base.PipeBuffer</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">PipeBuffer(data::AbstractVector{UInt8}=UInt8[]; maxsize::Integer = typemax(Int))</code></pre><p>An <a href="io-network.html#Base.IOBuffer"><code>IOBuffer</code></a> that allows reading and performs writes by appending. Seeking and truncating are not supported. See <a href="io-network.html#Base.IOBuffer"><code>IOBuffer</code></a> for the available constructors. If <code>data</code> is given, creates a <code>PipeBuffer</code> to operate on a data vector, optionally specifying a size beyond which the underlying <code>Array</code> may not be grown.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/iobuffer.jl#L142-L150">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.readavailable" href="#Base.readavailable"><code>Base.readavailable</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">readavailable(stream)</code></pre><p>Read available buffered data from a stream. Actual I/O is performed only if no data has already been buffered. The result is a <code>Vector{UInt8}</code>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>The amount of data returned is implementation-dependent; for example it can depend on the internal choice of buffer size. Other functions such as <a href="io-network.html#Base.read"><code>read</code></a> should generally be used instead.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L121-L131">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.IOContext" href="#Base.IOContext"><code>Base.IOContext</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">IOContext</code></pre><p><code>IOContext</code> provides a mechanism for passing output configuration settings among <a href="io-network.html#Base.show-Tuple{IO, Any}"><code>show</code></a> methods.</p><p>In short, it is an immutable dictionary that is a subclass of <code>IO</code>. It supports standard dictionary operations such as <a href="collections.html#Base.getindex"><code>getindex</code></a>, and can also be used as an I/O stream.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/show.jl#L291-L298">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.IOContext-Tuple{IO, Pair}" href="#Base.IOContext-Tuple{IO, Pair}"><code>Base.IOContext</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">IOContext(io::IO, KV::Pair...)</code></pre><p>Create an <code>IOContext</code> that wraps a given stream, adding the specified <code>key=&gt;value</code> pairs to the properties of that stream (note that <code>io</code> can itself be an <code>IOContext</code>).</p><ul><li>use <code>(key =&gt; value) in io</code> to see if this particular combination is in the properties set</li><li>use <code>get(io, key, default)</code> to retrieve the most recent value for a particular key</li></ul><p>The following properties are in common use:</p><ul><li><code>:compact</code>: Boolean specifying that values should be printed more compactly, e.g. that numbers should be printed with fewer digits. This is set when printing array elements. <code>:compact</code> output should not contain line breaks.</li><li><code>:limit</code>: Boolean specifying that containers should be truncated, e.g. showing <code>…</code> in place of most elements.</li><li><code>:displaysize</code>: A <code>Tuple{Int,Int}</code> giving the size in rows and columns to use for text output. This can be used to override the display size for called functions, but to get the size of the screen use the <code>displaysize</code> function.</li><li><code>:typeinfo</code>: a <code>Type</code> characterizing the information already printed concerning the type of the object about to be displayed. This is mainly useful when displaying a collection of objects of the same type, so that redundant type information can be avoided (e.g. <code>[Float16(0)]</code> can be shown as &quot;Float16[0.0]&quot; instead of &quot;Float16[Float16(0.0)]&quot; : while displaying the elements of the array, the <code>:typeinfo</code> property will be set to <code>Float16</code>).</li><li><code>:color</code>: Boolean specifying whether ANSI color/escape codes are supported/expected. By default, this is determined by whether <code>io</code> is a compatible terminal and by any <code>--color</code> command-line flag when <code>julia</code> was launched.</li></ul><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; io = IOBuffer();

julia&gt; printstyled(IOContext(io, :color =&gt; true), &quot;string&quot;, color=:red)

julia&gt; String(take!(io))
&quot;\e[31mstring\e[39m&quot;

julia&gt; printstyled(io, &quot;string&quot;, color=:red)

julia&gt; String(take!(io))
&quot;string&quot;</code></pre><pre><code class="language-julia-repl hljs">julia&gt; print(IOContext(stdout, :compact =&gt; false), 1.12341234)
1.12341234
julia&gt; print(IOContext(stdout, :compact =&gt; true), 1.12341234)
1.12341</code></pre><pre><code class="language-julia-repl hljs">julia&gt; function f(io::IO)
           if get(io, :short, false)
               print(io, &quot;short&quot;)
           else
               print(io, &quot;loooooong&quot;)
           end
       end
f (generic function with 1 method)

julia&gt; f(stdout)
loooooong
julia&gt; f(IOContext(stdout, :short =&gt; true))
short</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/show.jl#L345-L412">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.IOContext-Tuple{IO, IOContext}" href="#Base.IOContext-Tuple{IO, IOContext}"><code>Base.IOContext</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">IOContext(io::IO, context::IOContext)</code></pre><p>Create an <code>IOContext</code> that wraps an alternate <code>IO</code> but inherits the properties of <code>context</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/show.jl#L338-L342">source</a></section></article><h2 id="Text-I/O"><a class="docs-heading-anchor" href="#Text-I/O">Text I/O</a><a id="Text-I/O-1"></a><a class="docs-heading-anchor-permalink" href="#Text-I/O" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.show-Tuple{IO, Any}" href="#Base.show-Tuple{IO, Any}"><code>Base.show</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">show([io::IO = stdout], x)</code></pre><p>Write a text representation of a value <code>x</code> to the output stream <code>io</code>. New types <code>T</code> should overload <code>show(io::IO, x::T)</code>. The representation used by <code>show</code> generally includes Julia-specific formatting and type information, and should be parseable Julia code when possible.</p><p><a href="io-network.html#Base.repr-Tuple{MIME, Any}"><code>repr</code></a> returns the output of <code>show</code> as a string.</p><p>For a more verbose human-readable text output for objects of type <code>T</code>, define <code>show(io::IO, ::MIME&quot;text/plain&quot;, ::T)</code> in addition. Checking the <code>:compact</code> <a href="io-network.html#Base.IOContext"><code>IOContext</code></a> key (often checked as <code>get(io, :compact, false)::Bool</code>) of <code>io</code> in such methods is recommended, since some containers show their elements by calling this method with <code>:compact =&gt; true</code>.</p><p>See also <a href="io-network.html#Base.print"><code>print</code></a>, which writes un-decorated representations.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; show(&quot;Hello World!&quot;)
&quot;Hello World!&quot;
julia&gt; print(&quot;Hello World!&quot;)
Hello World!</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/show.jl#L450-L476">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.summary" href="#Base.summary"><code>Base.summary</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">summary(io::IO, x)
str = summary(x)</code></pre><p>Print to a stream <code>io</code>, or return a string <code>str</code>, giving a brief description of a value. By default returns <code>string(typeof(x))</code>, e.g. <a href="numbers.html#Core.Int64"><code>Int64</code></a>.</p><p>For arrays, returns a string of size and type info, e.g. <code>10-element Array{Int64,1}</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; summary(1)
&quot;Int64&quot;

julia&gt; summary(zeros(2))
&quot;2-element Vector{Float64}&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/show.jl#L3115-L3133">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.print" href="#Base.print"><code>Base.print</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">print([io::IO], xs...)</code></pre><p>Write to <code>io</code> (or to the default output stream <a href="io-network.html#Base.stdout"><code>stdout</code></a> if <code>io</code> is not given) a canonical (un-decorated) text representation. The representation used by <code>print</code> includes minimal formatting and tries to avoid Julia-specific details.</p><p><code>print</code> falls back to calling <code>show</code>, so most types should just define <code>show</code>. Define <code>print</code> if your type has a separate &quot;plain&quot; representation. For example, <code>show</code> displays strings with quotes, and <code>print</code> displays strings without quotes.</p><p>See also <a href="io-network.html#Base.println"><code>println</code></a>, <a href="strings.html#Base.string"><code>string</code></a>, <a href="io-network.html#Base.printstyled"><code>printstyled</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; print(&quot;Hello World!&quot;)
Hello World!
julia&gt; io = IOBuffer();

julia&gt; print(io, &quot;Hello&quot;, &#39; &#39;, :World!)

julia&gt; String(take!(io))
&quot;Hello World!&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/io.jl#L5-L31">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.println" href="#Base.println"><code>Base.println</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">println([io::IO], xs...)</code></pre><p>Print (using <a href="io-network.html#Base.print"><code>print</code></a>) <code>xs</code> to <code>io</code> followed by a newline. If <code>io</code> is not supplied, prints to the default output stream <a href="io-network.html#Base.stdout"><code>stdout</code></a>.</p><p>See also <a href="io-network.html#Base.printstyled"><code>printstyled</code></a> to add colors etc.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; println(&quot;Hello, world&quot;)
Hello, world

julia&gt; io = IOBuffer();

julia&gt; println(io, &quot;Hello&quot;, &#39;,&#39;, &quot; world.&quot;)

julia&gt; String(take!(io))
&quot;Hello, world.\n&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/io.jl#L54-L74">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.printstyled" href="#Base.printstyled"><code>Base.printstyled</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">printstyled([io], xs...; bold::Bool=false, italic::Bool=false, underline::Bool=false, blink::Bool=false, reverse::Bool=false, hidden::Bool=false, color::Union{Symbol,Int}=:normal)</code></pre><p>Print <code>xs</code> in a color specified as a symbol or integer, optionally in bold.</p><p>Keyword <code>color</code> may take any of the values <code>:normal</code>, <code>:italic</code>, <code>:default</code>, <code>:bold</code>, <code>:black</code>, <code>:blink</code>, <code>:blue</code>, <code>:cyan</code>, <code>:green</code>, <code>:hidden</code>, <code>:light_black</code>, <code>:light_blue</code>, <code>:light_cyan</code>, <code>:light_green</code>, <code>:light_magenta</code>, <code>:light_red</code>, <code>:light_white</code>, <code>:light_yellow</code>, <code>:magenta</code>, <code>:nothing</code>, <code>:red</code>, <code>:reverse</code>, <code>:underline</code>, <code>:white</code>, or  <code>:yellow</code> or an integer between 0 and 255 inclusive. Note that not all terminals support 256 colors.</p><p>Keywords <code>bold=true</code>, <code>italic=true</code>, <code>underline=true</code>, <code>blink=true</code> are self-explanatory. Keyword <code>reverse=true</code> prints with foreground and background colors exchanged, and <code>hidden=true</code> should be invisible in the terminal but can still be copied. These properties can be used in any combination.</p><p>See also <a href="io-network.html#Base.print"><code>print</code></a>, <a href="io-network.html#Base.println"><code>println</code></a>, <a href="io-network.html#Base.show-Tuple{IO, Any}"><code>show</code></a>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Not all terminals support italic output. Some terminals interpret italic as reverse or blink.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Keywords except <code>color</code> and <code>bold</code> were added in Julia 1.7.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.10</header><div class="admonition-body"><p>Support for italic output was added in Julia 1.10.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/util.jl#L117-L140">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.sprint" href="#Base.sprint"><code>Base.sprint</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sprint(f::Function, args...; context=nothing, sizehint=0)</code></pre><p>Call the given function with an I/O stream and the supplied extra arguments. Everything written to this I/O stream is returned as a string. <code>context</code> can be an <a href="io-network.html#Base.IOContext"><code>IOContext</code></a> whose properties will be used, a <code>Pair</code> specifying a property and its value, or a tuple of <code>Pair</code> specifying multiple properties and their values. <code>sizehint</code> suggests the capacity of the buffer (in bytes).</p><p>The optional keyword argument <code>context</code> can be set to a <code>:key=&gt;value</code> pair, a tuple of <code>:key=&gt;value</code> pairs, or an <code>IO</code> or <a href="io-network.html#Base.IOContext"><code>IOContext</code></a> object whose attributes are used for the I/O stream passed to <code>f</code>.  The optional <code>sizehint</code> is a suggested size (in bytes) to allocate for the buffer used to write the string.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Passing a tuple to keyword <code>context</code> requires Julia 1.7 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; sprint(show, 66.66666; context=:compact =&gt; true)
&quot;66.6667&quot;

julia&gt; sprint(showerror, BoundsError([1], 100))
&quot;BoundsError: attempt to access 1-element Vector{Int64} at index [100]&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/io.jl#L79-L106">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.showerror" href="#Base.showerror"><code>Base.showerror</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">showerror(io, e)</code></pre><p>Show a descriptive representation of an exception object <code>e</code>. This method is used to display the exception after a call to <a href="base.html#Core.throw"><code>throw</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; struct MyException &lt;: Exception
           msg::String
       end

julia&gt; function Base.showerror(io::IO, err::MyException)
           print(io, &quot;MyException: &quot;)
           print(io, err.msg)
       end

julia&gt; err = MyException(&quot;test exception&quot;)
MyException(&quot;test exception&quot;)

julia&gt; sprint(showerror, err)
&quot;MyException: test exception&quot;

julia&gt; throw(MyException(&quot;test exception&quot;))
ERROR: MyException: test exception</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/errorshow.jl#L3-L29">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.dump" href="#Base.dump"><code>Base.dump</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">dump(x; maxdepth=8)</code></pre><p>Show every part of the representation of a value. The depth of the output is truncated at <code>maxdepth</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; struct MyStruct
           x
           y
       end

julia&gt; x = MyStruct(1, (2,3));

julia&gt; dump(x)
MyStruct
  x: Int64 1
  y: Tuple{Int64, Int64}
    1: Int64 2
    2: Int64 3

julia&gt; dump(x; maxdepth = 1)
MyStruct
  x: Int64 1
  y: Tuple{Int64, Int64}</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/show.jl#L3022-L3049">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Meta.@dump" href="#Base.Meta.@dump"><code>Base.Meta.@dump</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@dump expr</code></pre><p>Show every part of the representation of the given expression. Equivalent to <a href="io-network.html#Base.dump"><code>dump(:(expr))</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/meta.jl#L144-L149">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.readline" href="#Base.readline"><code>Base.readline</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">readline(io::IO=stdin; keep::Bool=false)
readline(filename::AbstractString; keep::Bool=false)</code></pre><p>Read a single line of text from the given I/O stream or file (defaults to <code>stdin</code>). When reading from a file, the text is assumed to be encoded in UTF-8. Lines in the input end with <code>&#39;\n&#39;</code> or <code>&quot;\r\n&quot;</code> or the end of an input stream. When <code>keep</code> is false (as it is by default), these trailing newline characters are removed from the line before it is returned. When <code>keep</code> is true, they are returned as part of the line.</p><p>Return a <code>String</code>.   See also <a href="io-network.html#Base.copyline"><code>copyline</code></a> to instead write in-place to another stream (which can be a preallocated <a href="io-network.html#Base.IOBuffer"><code>IOBuffer</code></a>).</p><p>See also <a href="io-network.html#Base.readuntil"><code>readuntil</code></a> for reading until more general delimiters.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; write(&quot;my_file.txt&quot;, &quot;JuliaLang is a GitHub organization.\nIt has many members.\n&quot;);

julia&gt; readline(&quot;my_file.txt&quot;)
&quot;JuliaLang is a GitHub organization.&quot;

julia&gt; readline(&quot;my_file.txt&quot;, keep=true)
&quot;JuliaLang is a GitHub organization.\n&quot;

julia&gt; rm(&quot;my_file.txt&quot;)</code></pre><pre><code class="language-julia-repl hljs">julia&gt; print(&quot;Enter your name: &quot;)
Enter your name:

julia&gt; your_name = readline()
Logan
&quot;Logan&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L580-L616">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.readuntil" href="#Base.readuntil"><code>Base.readuntil</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">readuntil(stream::IO, delim; keep::Bool = false)
readuntil(filename::AbstractString, delim; keep::Bool = false)</code></pre><p>Read a string from an I/O <code>stream</code> or a file, up to the given delimiter. The delimiter can be a <code>UInt8</code>, <code>AbstractChar</code>, string, or vector. Keyword argument <code>keep</code> controls whether the delimiter is included in the result. The text is assumed to be encoded in UTF-8.</p><p>Return a <code>String</code> if <code>delim</code> is an <code>AbstractChar</code> or a string or otherwise return a <code>Vector{typeof(delim)}</code>.   See also <a href="io-network.html#Base.copyuntil"><code>copyuntil</code></a> to instead write in-place to another stream (which can be a preallocated <a href="io-network.html#Base.IOBuffer"><code>IOBuffer</code></a>).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; write(&quot;my_file.txt&quot;, &quot;JuliaLang is a GitHub organization.\nIt has many members.\n&quot;);

julia&gt; readuntil(&quot;my_file.txt&quot;, &#39;L&#39;)
&quot;Julia&quot;

julia&gt; readuntil(&quot;my_file.txt&quot;, &#39;.&#39;, keep = true)
&quot;JuliaLang is a GitHub organization.&quot;

julia&gt; rm(&quot;my_file.txt&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L519-L544">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.readlines" href="#Base.readlines"><code>Base.readlines</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">readlines(io::IO=stdin; keep::Bool=false)
readlines(filename::AbstractString; keep::Bool=false)</code></pre><p>Read all lines of an I/O stream or a file as a vector of strings. Behavior is equivalent to saving the result of reading <a href="io-network.html#Base.readline"><code>readline</code></a> repeatedly with the same arguments and saving the resulting lines as a vector of strings.  See also <a href="io-network.html#Base.eachline"><code>eachline</code></a> to iterate over the lines without reading them all at once.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; write(&quot;my_file.txt&quot;, &quot;JuliaLang is a GitHub organization.\nIt has many members.\n&quot;);

julia&gt; readlines(&quot;my_file.txt&quot;)
2-element Vector{String}:
 &quot;JuliaLang is a GitHub organization.&quot;
 &quot;It has many members.&quot;

julia&gt; readlines(&quot;my_file.txt&quot;, keep=true)
2-element Vector{String}:
 &quot;JuliaLang is a GitHub organization.\n&quot;
 &quot;It has many members.\n&quot;

julia&gt; rm(&quot;my_file.txt&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L677-L702">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.eachline" href="#Base.eachline"><code>Base.eachline</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">eachline(io::IO=stdin; keep::Bool=false)
eachline(filename::AbstractString; keep::Bool=false)</code></pre><p>Create an iterable <code>EachLine</code> object that will yield each line from an I/O stream or a file. Iteration calls <a href="io-network.html#Base.readline"><code>readline</code></a> on the stream argument repeatedly with <code>keep</code> passed through, determining whether trailing end-of-line characters are retained. When called with a file name, the file is opened once at the beginning of iteration and closed at the end. If iteration is interrupted, the file will be closed when the <code>EachLine</code> object is garbage collected.</p><p>To iterate over each line of a <code>String</code>, <code>eachline(IOBuffer(str))</code> can be used.</p><p><a href="iterators.html#Base.Iterators.reverse"><code>Iterators.reverse</code></a> can be used on an <code>EachLine</code> object to read the lines in reverse order (for files, buffers, and other I/O streams supporting <a href="io-network.html#Base.seek"><code>seek</code></a>), and <a href="collections.html#Base.first"><code>first</code></a> or <a href="collections.html#Base.last"><code>last</code></a> can be used to extract the initial or final lines, respectively.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; write(&quot;my_file.txt&quot;, &quot;JuliaLang is a GitHub organization.\n It has many members.\n&quot;);

julia&gt; for line in eachline(&quot;my_file.txt&quot;)
           print(line)
       end
JuliaLang is a GitHub organization. It has many members.

julia&gt; rm(&quot;my_file.txt&quot;);</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>Julia 1.8 is required to use <code>Iterators.reverse</code> or <code>last</code> with <code>eachline</code> iterators.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L1192-L1224">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.copyline" href="#Base.copyline"><code>Base.copyline</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">copyline(out::IO, io::IO=stdin; keep::Bool=false)
copyline(out::IO, filename::AbstractString; keep::Bool=false)</code></pre><p>Copy a single line of text from an I/O <code>stream</code> or a file to the <code>out</code> stream, returning <code>out</code>.</p><p>When reading from a file, the text is assumed to be encoded in UTF-8. Lines in the input end with <code>&#39;\n&#39;</code> or <code>&quot;\r\n&quot;</code> or the end of an input stream. When <code>keep</code> is false (as it is by default), these trailing newline characters are removed from the line before it is returned. When <code>keep</code> is true, they are returned as part of the line.</p><p>Similar to <a href="io-network.html#Base.readline"><code>readline</code></a>, which returns a <code>String</code>; in contrast, <code>copyline</code> writes directly to <code>out</code>, without allocating a string. (This can be used, for example, to read data into a pre-allocated <a href="io-network.html#Base.IOBuffer"><code>IOBuffer</code></a>.)</p><p>See also <a href="io-network.html#Base.copyuntil"><code>copyuntil</code></a> for reading until more general delimiters.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; write(&quot;my_file.txt&quot;, &quot;JuliaLang is a GitHub organization.\nIt has many members.\n&quot;);

julia&gt; String(take!(copyline(IOBuffer(), &quot;my_file.txt&quot;)))
&quot;JuliaLang is a GitHub organization.&quot;

julia&gt; String(take!(copyline(IOBuffer(), &quot;my_file.txt&quot;, keep=true)))
&quot;JuliaLang is a GitHub organization.\n&quot;

julia&gt; rm(&quot;my_file.txt&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L622-L653">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.copyuntil" href="#Base.copyuntil"><code>Base.copyuntil</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">copyuntil(out::IO, stream::IO, delim; keep::Bool = false)
copyuntil(out::IO, filename::AbstractString, delim; keep::Bool = false)</code></pre><p>Copy a string from an I/O <code>stream</code> or a file, up to the given delimiter, to the <code>out</code> stream, returning <code>out</code>. The delimiter can be a <code>UInt8</code>, <code>AbstractChar</code>, string, or vector. Keyword argument <code>keep</code> controls whether the delimiter is included in the result. The text is assumed to be encoded in UTF-8.</p><p>Similar to <a href="io-network.html#Base.readuntil"><code>readuntil</code></a>, which returns a <code>String</code>; in contrast, <code>copyuntil</code> writes directly to <code>out</code>, without allocating a string. (This can be used, for example, to read data into a pre-allocated <a href="io-network.html#Base.IOBuffer"><code>IOBuffer</code></a>.)</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; write(&quot;my_file.txt&quot;, &quot;JuliaLang is a GitHub organization.\nIt has many members.\n&quot;);

julia&gt; String(take!(copyuntil(IOBuffer(), &quot;my_file.txt&quot;, &#39;L&#39;)))
&quot;Julia&quot;

julia&gt; String(take!(copyuntil(IOBuffer(), &quot;my_file.txt&quot;, &#39;.&#39;, keep = true)))
&quot;JuliaLang is a GitHub organization.&quot;

julia&gt; rm(&quot;my_file.txt&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L551-L577">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.displaysize" href="#Base.displaysize"><code>Base.displaysize</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">displaysize([io::IO]) -&gt; (lines, columns)</code></pre><p>Return the nominal size of the screen that may be used for rendering output to this <code>IO</code> object. If no input is provided, the environment variables <code>LINES</code> and <code>COLUMNS</code> are read. If those are not set, a default size of <code>(24, 80)</code> is returned.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; withenv(&quot;LINES&quot; =&gt; 30, &quot;COLUMNS&quot; =&gt; 100) do
           displaysize()
       end
(30, 100)</code></pre><p>To get your TTY size,</p><pre><code class="language-julia-repl hljs">julia&gt; displaysize(stdout)
(34, 147)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/stream.jl#L545-L567">source</a></section></article><h2 id="Multimedia-I/O"><a class="docs-heading-anchor" href="#Multimedia-I/O">Multimedia I/O</a><a id="Multimedia-I/O-1"></a><a class="docs-heading-anchor-permalink" href="#Multimedia-I/O" title="Permalink"></a></h2><p>Just as text output is performed by <a href="io-network.html#Base.print"><code>print</code></a> and user-defined types can indicate their textual representation by overloading <a href="io-network.html#Base.show-Tuple{IO, Any}"><code>show</code></a>, Julia provides a standardized mechanism for rich multimedia output (such as images, formatted text, or even audio and video), consisting of three parts:</p><ul><li>A function <a href="io-network.html#Base.Multimedia.display"><code>display(x)</code></a> to request the richest available multimedia display of a Julia object <code>x</code> (with a plain-text fallback).</li><li>Overloading <a href="io-network.html#Base.show-Tuple{IO, Any}"><code>show</code></a> allows one to indicate arbitrary multimedia representations (keyed by standard MIME types) of user-defined types.</li><li>Multimedia-capable display backends may be registered by subclassing a generic <a href="io-network.html#Base.Multimedia.AbstractDisplay"><code>AbstractDisplay</code></a> type and pushing them onto a stack of display backends via <a href="io-network.html#Base.Multimedia.pushdisplay"><code>pushdisplay</code></a>.</li></ul><p>The base Julia runtime provides only plain-text display, but richer displays may be enabled by loading external modules or by using graphical Julia environments (such as the IPython-based IJulia notebook).</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Multimedia.AbstractDisplay" href="#Base.Multimedia.AbstractDisplay"><code>Base.Multimedia.AbstractDisplay</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AbstractDisplay</code></pre><p>Abstract supertype for rich display output devices. <a href="io-network.html#Base.Multimedia.TextDisplay"><code>TextDisplay</code></a> is a subtype of this.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multimedia.jl#L219-L224">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Multimedia.display" href="#Base.Multimedia.display"><code>Base.Multimedia.display</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">display(x)
display(d::AbstractDisplay, x)
display(mime, x)
display(d::AbstractDisplay, mime, x)</code></pre><p>Display <code>x</code> using the topmost applicable display in the display stack, typically using the richest supported multimedia output for <code>x</code>, with plain-text <a href="io-network.html#Base.stdout"><code>stdout</code></a> output as a fallback. The <code>display(d, x)</code> variant attempts to display <code>x</code> on the given display <code>d</code> only, throwing a <a href="base.html#Core.MethodError"><code>MethodError</code></a> if <code>d</code> cannot display objects of this type.</p><p>In general, you cannot assume that <code>display</code> output goes to <code>stdout</code> (unlike <a href="io-network.html#Base.print"><code>print(x)</code></a> or <a href="io-network.html#Base.show-Tuple{IO, Any}"><code>show(x)</code></a>).  For example, <code>display(x)</code> may open up a separate window with an image. <code>display(x)</code> means &quot;show <code>x</code> in the best way you can for the current output device(s).&quot; If you want REPL-like text output that is guaranteed to go to <code>stdout</code>, use <a href="io-network.html#Base.show-Tuple{IO, Any}"><code>show(stdout, &quot;text/plain&quot;, x)</code></a> instead.</p><p>There are also two variants with a <code>mime</code> argument (a MIME type string, such as <code>&quot;image/png&quot;</code>), which attempt to display <code>x</code> using the requested MIME type <em>only</em>, throwing a <code>MethodError</code> if this type is not supported by either the display(s) or by <code>x</code>. With these variants, one can also supply the &quot;raw&quot; data in the requested MIME type by passing <code>x::AbstractString</code> (for MIME types with text-based storage, such as text/html or application/postscript) or <code>x::Vector{UInt8}</code> (for binary MIME types).</p><p>To customize how instances of a type are displayed, overload <a href="io-network.html#Base.show-Tuple{IO, Any}"><code>show</code></a> rather than <code>display</code>, as explained in the manual section on <a href="../manual/types.html#man-custom-pretty-printing">custom pretty-printing</a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multimedia.jl#L309-L335">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Multimedia.redisplay" href="#Base.Multimedia.redisplay"><code>Base.Multimedia.redisplay</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">redisplay(x)
redisplay(d::AbstractDisplay, x)
redisplay(mime, x)
redisplay(d::AbstractDisplay, mime, x)</code></pre><p>By default, the <code>redisplay</code> functions simply call <a href="io-network.html#Base.Multimedia.display"><code>display</code></a>. However, some display backends may override <code>redisplay</code> to modify an existing display of <code>x</code> (if any). Using <code>redisplay</code> is also a hint to the backend that <code>x</code> may be redisplayed several times, and the backend may choose to defer the display until (for example) the next interactive prompt.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multimedia.jl#L382-L394">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Multimedia.displayable" href="#Base.Multimedia.displayable"><code>Base.Multimedia.displayable</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">displayable(mime) -&gt; Bool
displayable(d::AbstractDisplay, mime) -&gt; Bool</code></pre><p>Return a boolean value indicating whether the given <code>mime</code> type (string) is displayable by any of the displays in the current display stack, or specifically by the display <code>d</code> in the second variant.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multimedia.jl#L231-L238">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.show-Tuple{IO, Any, Any}" href="#Base.show-Tuple{IO, Any, Any}"><code>Base.show</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">show(io::IO, mime, x)</code></pre><p>The <a href="io-network.html#Base.Multimedia.display"><code>display</code></a> functions ultimately call <code>show</code> in order to write an object <code>x</code> as a given <code>mime</code> type to a given I/O stream <code>io</code> (usually a memory buffer), if possible. In order to provide a rich multimedia representation of a user-defined type <code>T</code>, it is only necessary to define a new <code>show</code> method for <code>T</code>, via: <code>show(io, ::MIME&quot;mime&quot;, x::T) = ...</code>, where <code>mime</code> is a MIME-type string and the function body calls <a href="io-network.html#Base.write"><code>write</code></a> (or similar) to write that representation of <code>x</code> to <code>io</code>. (Note that the <code>MIME&quot;&quot;</code> notation only supports literal strings; to construct <code>MIME</code> types in a more flexible manner use <code>MIME{Symbol(&quot;&quot;)}</code>.)</p><p>For example, if you define a <code>MyImage</code> type and know how to write it to a PNG file, you could define a function <code>show(io, ::MIME&quot;image/png&quot;, x::MyImage) = ...</code> to allow your images to be displayed on any PNG-capable <code>AbstractDisplay</code> (such as IJulia). As usual, be sure to <code>import Base.show</code> in order to add new methods to the built-in Julia function <code>show</code>.</p><p>Technically, the <code>MIME&quot;mime&quot;</code> macro defines a singleton type for the given <code>mime</code> string, which allows us to exploit Julia&#39;s dispatch mechanisms in determining how to display objects of any given type.</p><p>The default MIME type is <code>MIME&quot;text/plain&quot;</code>. There is a fallback definition for <code>text/plain</code> output that calls <code>show</code> with 2 arguments, so it is not always necessary to add a method for that case. If a type benefits from custom human-readable output though, <code>show(::IO, ::MIME&quot;text/plain&quot;, ::T)</code> should be defined. For example, the <code>Day</code> type uses <code>1 day</code> as the output for the <code>text/plain</code> MIME type, and <code>Day(1)</code> as the output of 2-argument <code>show</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; struct Day
           n::Int
       end

julia&gt; Base.show(io::IO, ::MIME&quot;text/plain&quot;, d::Day) = print(io, d.n, &quot; day&quot;)

julia&gt; Day(1)
1 day</code></pre><p>Container types generally implement 3-argument <code>show</code> by calling <code>show(io, MIME&quot;text/plain&quot;(), x)</code> for elements <code>x</code>, with <code>:compact =&gt; true</code> set in an <a href="io-network.html#Base.IOContext"><code>IOContext</code></a> passed as the first argument.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multimedia.jl#L79-L121">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Multimedia.showable" href="#Base.Multimedia.showable"><code>Base.Multimedia.showable</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">showable(mime, x)</code></pre><p>Return a boolean value indicating whether or not the object <code>x</code> can be written as the given <code>mime</code> type.</p><p>(By default, this is determined automatically by the existence of the corresponding <a href="io-network.html#Base.show-Tuple{IO, Any}"><code>show</code></a> method for <code>typeof(x)</code>.  Some types provide custom <code>showable</code> methods; for example, if the available MIME formats depend on the <em>value</em> of <code>x</code>.)</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; showable(MIME(&quot;text/plain&quot;), rand(5))
true

julia&gt; showable(&quot;image/png&quot;, rand(5))
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multimedia.jl#L57-L75">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.repr-Tuple{MIME, Any}" href="#Base.repr-Tuple{MIME, Any}"><code>Base.repr</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">repr(mime, x; context=nothing)</code></pre><p>Return an <code>AbstractString</code> or <code>Vector{UInt8}</code> containing the representation of <code>x</code> in the requested <code>mime</code> type, as written by <a href="io-network.html#Base.show-Tuple{IO, Any}"><code>show(io, mime, x)</code></a> (throwing a <a href="base.html#Core.MethodError"><code>MethodError</code></a> if no appropriate <code>show</code> is available). An <code>AbstractString</code> is returned for MIME types with textual representations (such as <code>&quot;text/html&quot;</code> or <code>&quot;application/postscript&quot;</code>), whereas binary data is returned as <code>Vector{UInt8}</code>. (The function <code>istextmime(mime)</code> returns whether or not Julia treats a given <code>mime</code> type as text.)</p><p>The optional keyword argument <code>context</code> can be set to <code>:key=&gt;value</code> pair or an <code>IO</code> or <a href="io-network.html#Base.IOContext"><code>IOContext</code></a> object whose attributes are used for the I/O stream passed to <code>show</code>.</p><p>As a special case, if <code>x</code> is an <code>AbstractString</code> (for textual MIME types) or a <code>Vector{UInt8}</code> (for binary MIME types), the <code>repr</code> function assumes that <code>x</code> is already in the requested <code>mime</code> format and simply returns <code>x</code>. This special case does not apply to the <code>&quot;text/plain&quot;</code> MIME type. This is useful so that raw data can be passed to <code>display(m::MIME, x)</code>.</p><p>In particular, <code>repr(&quot;text/plain&quot;, x)</code> is typically a &quot;pretty-printed&quot; version of <code>x</code> designed for human consumption.  See also <a href="strings.html#Base.repr-Tuple{Any}"><code>repr(x)</code></a> to instead return a string corresponding to <a href="io-network.html#Base.show-Tuple{IO, Any}"><code>show(x)</code></a> that may be closer to how the value of <code>x</code> would be entered in Julia.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4];

julia&gt; repr(&quot;text/plain&quot;, A)
&quot;2×2 Matrix{Int64}:\n 1  2\n 3  4&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multimedia.jl#L125-L158">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Multimedia.MIME" href="#Base.Multimedia.MIME"><code>Base.Multimedia.MIME</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">MIME</code></pre><p>A type representing a standard internet data format. &quot;MIME&quot; stands for &quot;Multipurpose Internet Mail Extensions&quot;, since the standard was originally used to describe multimedia attachments to email messages.</p><p>A <code>MIME</code> object can be passed as the second argument to <a href="io-network.html#Base.show-Tuple{IO, Any}"><code>show</code></a> to request output in that format.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; show(stdout, MIME(&quot;text/plain&quot;), &quot;hi&quot;)
&quot;hi&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multimedia.jl#L16-L31">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Multimedia.@MIME_str" href="#Base.Multimedia.@MIME_str"><code>Base.Multimedia.@MIME_str</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@MIME_str</code></pre><p>A convenience macro for writing <a href="io-network.html#Base.Multimedia.MIME"><code>MIME</code></a> types, typically used when adding methods to <a href="io-network.html#Base.show-Tuple{IO, Any}"><code>show</code></a>. For example the syntax <code>show(io::IO, ::MIME&quot;text/html&quot;, x::MyType) = ...</code> could be used to define how to write an HTML representation of <code>MyType</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multimedia.jl#L34-L41">source</a></section></article><p>As mentioned above, one can also define new display backends. For example, a module that can display PNG images in a window can register this capability with Julia, so that calling <a href="io-network.html#Base.Multimedia.display"><code>display(x)</code></a> on types with PNG representations will automatically display the image using the module&#39;s window.</p><p>In order to define a new display backend, one should first create a subtype <code>D</code> of the abstract class <a href="io-network.html#Base.Multimedia.AbstractDisplay"><code>AbstractDisplay</code></a>.  Then, for each MIME type (<code>mime</code> string) that can be displayed on <code>D</code>, one should define a function <code>display(d::D, ::MIME&quot;mime&quot;, x) = ...</code> that displays <code>x</code> as that MIME type, usually by calling <a href="io-network.html#Base.show-Tuple{IO, Any}"><code>show(io, mime, x)</code></a> or <a href="io-network.html#Base.repr-Tuple{MIME, Any}"><code>repr(io, mime, x)</code></a>. A <a href="base.html#Core.MethodError"><code>MethodError</code></a> should be thrown if <code>x</code> cannot be displayed as that MIME type; this is automatic if one calls <code>show</code> or <code>repr</code>. Finally, one should define a function <code>display(d::D, x)</code> that queries <a href="io-network.html#Base.Multimedia.showable"><code>showable(mime, x)</code></a> for the <code>mime</code> types supported by <code>D</code> and displays the &quot;best&quot; one; a <code>MethodError</code> should be thrown if no supported MIME types are found for <code>x</code>.  Similarly, some subtypes may wish to override <a href="io-network.html#Base.Multimedia.redisplay"><code>redisplay(d::D, ...)</code></a>. (Again, one should <code>import Base.display</code> to add new methods to <code>display</code>.) The return values of these functions are up to the implementation (since in some cases it may be useful to return a display &quot;handle&quot; of some type).  The display functions for <code>D</code> can then be called directly, but they can also be invoked automatically from <a href="io-network.html#Base.Multimedia.display"><code>display(x)</code></a> simply by pushing a new display onto the display-backend stack with:</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Multimedia.pushdisplay" href="#Base.Multimedia.pushdisplay"><code>Base.Multimedia.pushdisplay</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">pushdisplay(d::AbstractDisplay)</code></pre><p>Pushes a new display <code>d</code> on top of the global display-backend stack. Calling <code>display(x)</code> or <code>display(mime, x)</code> will display <code>x</code> on the topmost compatible backend in the stack (i.e., the topmost backend that does not throw a <a href="base.html#Core.MethodError"><code>MethodError</code></a>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multimedia.jl#L274-L280">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Multimedia.popdisplay" href="#Base.Multimedia.popdisplay"><code>Base.Multimedia.popdisplay</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">popdisplay()
popdisplay(d::AbstractDisplay)</code></pre><p>Pop the topmost backend off of the display-backend stack, or the topmost copy of <code>d</code> in the second variant.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multimedia.jl#L286-L292">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Multimedia.TextDisplay" href="#Base.Multimedia.TextDisplay"><code>Base.Multimedia.TextDisplay</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">TextDisplay(io::IO)</code></pre><p>Return a <code>TextDisplay &lt;: AbstractDisplay</code>, which displays any object as the text/plain MIME type (by default), writing the text representation to the given I/O stream. (This is how objects are printed in the Julia REPL.)</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multimedia.jl#L244-L250">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Multimedia.istextmime" href="#Base.Multimedia.istextmime"><code>Base.Multimedia.istextmime</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">istextmime(m::MIME)</code></pre><p>Determine whether a MIME type is text data. MIME types are assumed to be binary data except for a set of types known to be text data (possibly Unicode).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; istextmime(MIME(&quot;text/plain&quot;))
true

julia&gt; istextmime(MIME(&quot;image/png&quot;))
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multimedia.jl#L180-L194">source</a></section></article><h2 id="Network-I/O"><a class="docs-heading-anchor" href="#Network-I/O">Network I/O</a><a id="Network-I/O-1"></a><a class="docs-heading-anchor-permalink" href="#Network-I/O" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.bytesavailable" href="#Base.bytesavailable"><code>Base.bytesavailable</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">bytesavailable(io)</code></pre><p>Return the number of bytes available for reading before a read from this stream or buffer will block.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; io = IOBuffer(&quot;JuliaLang is a GitHub organization&quot;);

julia&gt; bytesavailable(io)
34</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L106-L118">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.ntoh" href="#Base.ntoh"><code>Base.ntoh</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ntoh(x)</code></pre><p>Convert the endianness of a value from Network byte order (big-endian) to that used by the Host.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L741-L745">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.hton" href="#Base.hton"><code>Base.hton</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hton(x)</code></pre><p>Convert the endianness of a value from that used by the Host to Network byte order (big-endian).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L748-L752">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.ltoh" href="#Base.ltoh"><code>Base.ltoh</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ltoh(x)</code></pre><p>Convert the endianness of a value from Little-endian to that used by the Host.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L755-L759">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.htol" href="#Base.htol"><code>Base.htol</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">htol(x)</code></pre><p>Convert the endianness of a value from that used by the Host to Little-endian.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L762-L766">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.ENDIAN_BOM" href="#Base.ENDIAN_BOM"><code>Base.ENDIAN_BOM</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ENDIAN_BOM</code></pre><p>The 32-bit byte-order-mark indicates the native byte order of the host machine. Little-endian machines will contain the value <code>0x04030201</code>. Big-endian machines will contain the value <code>0x01020304</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/io.jl#L732-L738">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="file.html">« Filesystem</a><a class="docs-footer-nextpage" href="punctuation.html">Punctuation »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
