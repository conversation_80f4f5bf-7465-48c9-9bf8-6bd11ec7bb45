<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Frequently Asked Questions · The Julia Language</title><meta name="title" content="Frequently Asked Questions · The Julia Language"/><meta property="og:title" content="Frequently Asked Questions · The Julia Language"/><meta property="twitter:title" content="Frequently Asked Questions · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li class="is-active"><a class="tocitem" href="faq.html">Frequently Asked Questions</a><ul class="internal"><li><a class="tocitem" href="#General"><span>General</span></a></li><li><a class="tocitem" href="#man-api"><span>Public API</span></a></li><li><a class="tocitem" href="#Sessions-and-the-REPL"><span>Sessions and the REPL</span></a></li><li><a class="tocitem" href="#man-scripting"><span>Scripting</span></a></li><li><a class="tocitem" href="#Variables-and-Assignments"><span>Variables and Assignments</span></a></li><li><a class="tocitem" href="#Functions"><span>Functions</span></a></li><li><a class="tocitem" href="#Types,-type-declarations,-and-constructors"><span>Types, type declarations, and constructors</span></a></li><li><a class="tocitem" href="#Troubleshooting-&quot;method-not-matched&quot;:-parametric-type-invariance-and-MethodErrors"><span>Troubleshooting &quot;method not matched&quot;: parametric type invariance and <code>MethodError</code>s</span></a></li><li><a class="tocitem" href="#Packages-and-Modules"><span>Packages and Modules</span></a></li><li><a class="tocitem" href="#Nothingness-and-missing-values"><span>Nothingness and missing values</span></a></li><li><a class="tocitem" href="#Memory"><span>Memory</span></a></li><li><a class="tocitem" href="#faq-async-io"><span>Asynchronous IO and concurrent synchronous writes</span></a></li><li><a class="tocitem" href="#Arrays"><span>Arrays</span></a></li><li><a class="tocitem" href="#Computing-cluster"><span>Computing cluster</span></a></li><li><a class="tocitem" href="#Julia-Releases"><span>Julia Releases</span></a></li></ul></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="faq.html">Frequently Asked Questions</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="faq.html">Frequently Asked Questions</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/faq.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Frequently-Asked-Questions"><a class="docs-heading-anchor" href="#Frequently-Asked-Questions">Frequently Asked Questions</a><a id="Frequently-Asked-Questions-1"></a><a class="docs-heading-anchor-permalink" href="#Frequently-Asked-Questions" title="Permalink"></a></h1><h2 id="General"><a class="docs-heading-anchor" href="#General">General</a><a id="General-1"></a><a class="docs-heading-anchor-permalink" href="#General" title="Permalink"></a></h2><h3 id="Is-Julia-named-after-someone-or-something?"><a class="docs-heading-anchor" href="#Is-Julia-named-after-someone-or-something?">Is Julia named after someone or something?</a><a id="Is-Julia-named-after-someone-or-something?-1"></a><a class="docs-heading-anchor-permalink" href="#Is-Julia-named-after-someone-or-something?" title="Permalink"></a></h3><p>No.</p><h3 id="Why-don&#39;t-you-compile-Matlab/Python/R/…-code-to-Julia?"><a class="docs-heading-anchor" href="#Why-don&#39;t-you-compile-Matlab/Python/R/…-code-to-Julia?">Why don&#39;t you compile Matlab/Python/R/… code to Julia?</a><a id="Why-don&#39;t-you-compile-Matlab/Python/R/…-code-to-Julia?-1"></a><a class="docs-heading-anchor-permalink" href="#Why-don&#39;t-you-compile-Matlab/Python/R/…-code-to-Julia?" title="Permalink"></a></h3><p>Since many people are familiar with the syntax of other dynamic languages, and lots of code has already been written in those languages, it is natural to wonder why we didn&#39;t just plug a Matlab or Python front-end into a Julia back-end (or “transpile” code to Julia) in order to get all the performance benefits of Julia without requiring programmers to learn a new language.  Simple, right?</p><p>The basic issue is that there is <em>nothing special about Julia&#39;s compiler</em>: we use a commonplace compiler (LLVM) with no “secret sauce” that other language developers don&#39;t know about.  Indeed, Julia&#39;s compiler is in many ways much simpler than those of other dynamic languages (e.g. PyPy or LuaJIT).   Julia&#39;s performance advantage derives almost entirely from its front-end: its language semantics allow a <a href="performance-tips.html#man-performance-tips">well-written Julia program</a> to <em>give more opportunities to the compiler</em> to generate efficient code and memory layouts.  If you tried to compile Matlab or Python code to Julia, our compiler would be limited by the semantics of Matlab or Python to producing code no better than that of existing compilers for those languages (and probably worse).  The key role of semantics is also why several existing Python compilers (like Numba and Pythran) only attempt to optimize a small subset of the language (e.g. operations on Numpy arrays and scalars), and for this subset they are already doing at least as well as we could for the same semantics.  The people working on those projects are incredibly smart and have accomplished amazing things, but retrofitting a compiler onto a language that was designed to be interpreted is a very difficult problem.</p><p>Julia&#39;s advantage is that good performance is not limited to a small subset of “built-in” types and operations, and one can write high-level type-generic code that works on arbitrary user-defined types while remaining fast and memory-efficient.  Types in languages like Python simply don&#39;t provide enough information to the compiler for similar capabilities, so as soon as you used those languages as a Julia front-end you would be stuck.</p><p>For similar reasons, automated translation to Julia would also typically generate unreadable, slow, non-idiomatic code that would not be a good starting point for a native Julia port from another language.</p><p>On the other hand, language <em>interoperability</em> is extremely useful: we want to exploit existing high-quality code in other languages from Julia (and vice versa)!  The best way to enable this is not a transpiler, but rather via easy inter-language calling facilities.  We have worked hard on this, from the built-in <code>ccall</code> intrinsic (to call C and Fortran libraries) to <a href="https://github.com/JuliaInterop">JuliaInterop</a> packages that connect Julia to Python, Matlab, C++, and more.</p><h2 id="man-api"><a class="docs-heading-anchor" href="#man-api">Public API</a><a id="man-api-1"></a><a class="docs-heading-anchor-permalink" href="#man-api" title="Permalink"></a></h2><h3 id="How-does-Julia-define-its-public-API?"><a class="docs-heading-anchor" href="#How-does-Julia-define-its-public-API?">How does Julia define its public API?</a><a id="How-does-Julia-define-its-public-API?-1"></a><a class="docs-heading-anchor-permalink" href="#How-does-Julia-define-its-public-API?" title="Permalink"></a></h3><p>Julia&#39;s public <a href="https://en.wikipedia.org/wiki/API">API</a> is the behavior described in documentation of public symbols from <code>Base</code> and the standard libraries. Functions, types, and constants are not part of the public API if they are not public, even if they have docstrings or are described in the documentation. Further, only the documented behavior of public symbols is part of the public API. Undocumented behavior of public symbols is internal.</p><p>Public symbols are those marked with either <code>public foo</code> or <code>export foo</code>.</p><p>In other words:</p><ul><li>Documented behavior of public symbols is part of the public API.</li><li>Undocumented behavior of public symbols is not part of the public API.</li><li>Documented behavior of private symbols is not part of the public API.</li><li>Undocumented behavior of private symbols is not part of the public API.</li></ul><p>You can get a complete list of the public symbols from a module with <code>names(MyModule)</code>.</p><p>Package authors are encouraged to define their public API similarly.</p><p>Anything in Julia&#39;s Public API is covered by <a href="https://semver.org/">SemVer</a> and therefore will not be removed or receive meaningful breaking changes before Julia 2.0.</p><h3 id="There-is-a-useful-undocumented-function/type/constant.-Can-I-use-it?"><a class="docs-heading-anchor" href="#There-is-a-useful-undocumented-function/type/constant.-Can-I-use-it?">There is a useful undocumented function/type/constant. Can I use it?</a><a id="There-is-a-useful-undocumented-function/type/constant.-Can-I-use-it?-1"></a><a class="docs-heading-anchor-permalink" href="#There-is-a-useful-undocumented-function/type/constant.-Can-I-use-it?" title="Permalink"></a></h3><p>Updating Julia may break your code if you use non-public API.  If the code is self-contained, it may be a good idea to copy it into your project.  If you want to rely on a complex non-public API, especially when using it from a stable package, it is a good idea to open an <a href="https://github.com/JuliaLang/julia/issues">issue</a> or <a href="https://github.com/JuliaLang/julia/pulls">pull request</a> to start a discussion for turning it into a public API.  However, we do not discourage the attempt to create packages that expose stable public interfaces while relying on non-public implementation details of Julia and buffering the differences across different Julia versions.</p><h3 id="The-documentation-is-not-accurate-enough.-Can-I-rely-on-the-existing-behavior?"><a class="docs-heading-anchor" href="#The-documentation-is-not-accurate-enough.-Can-I-rely-on-the-existing-behavior?">The documentation is not accurate enough. Can I rely on the existing behavior?</a><a id="The-documentation-is-not-accurate-enough.-Can-I-rely-on-the-existing-behavior?-1"></a><a class="docs-heading-anchor-permalink" href="#The-documentation-is-not-accurate-enough.-Can-I-rely-on-the-existing-behavior?" title="Permalink"></a></h3><p>Please open an <a href="https://github.com/JuliaLang/julia/issues">issue</a> or <a href="https://github.com/JuliaLang/julia/pulls">pull request</a> to start a discussion for turning the existing behavior into a public API.</p><h2 id="Sessions-and-the-REPL"><a class="docs-heading-anchor" href="#Sessions-and-the-REPL">Sessions and the REPL</a><a id="Sessions-and-the-REPL-1"></a><a class="docs-heading-anchor-permalink" href="#Sessions-and-the-REPL" title="Permalink"></a></h2><h3 id="How-do-I-delete-an-object-in-memory?"><a class="docs-heading-anchor" href="#How-do-I-delete-an-object-in-memory?">How do I delete an object in memory?</a><a id="How-do-I-delete-an-object-in-memory?-1"></a><a class="docs-heading-anchor-permalink" href="#How-do-I-delete-an-object-in-memory?" title="Permalink"></a></h3><p>Julia does not have an analog of MATLAB&#39;s <code>clear</code> function; once a name is defined in a Julia session (technically, in module <code>Main</code>), it is always present.</p><p>If memory usage is your concern, you can always replace objects with ones that consume less memory.  For example, if <code>A</code> is a gigabyte-sized array that you no longer need, you can free the memory with <code>A = nothing</code>.  The memory will be released the next time the garbage collector runs; you can force this to happen with <a href="../base/base.html#Base.GC.gc"><code>GC.gc()</code></a>. Moreover, an attempt to use <code>A</code> will likely result in an error, because most methods are not defined on type <code>Nothing</code>.</p><h3 id="How-can-I-modify-the-declaration-of-a-type-in-my-session?"><a class="docs-heading-anchor" href="#How-can-I-modify-the-declaration-of-a-type-in-my-session?">How can I modify the declaration of a type in my session?</a><a id="How-can-I-modify-the-declaration-of-a-type-in-my-session?-1"></a><a class="docs-heading-anchor-permalink" href="#How-can-I-modify-the-declaration-of-a-type-in-my-session?" title="Permalink"></a></h3><p>Perhaps you&#39;ve defined a type and then realize you need to add a new field.  If you try this at the REPL, you get the error:</p><pre><code class="nohighlight hljs">ERROR: invalid redefinition of constant MyType</code></pre><p>Types in module <code>Main</code> cannot be redefined.</p><p>While this can be inconvenient when you are developing new code, there&#39;s an excellent workaround.  Modules can be replaced by redefining them, and so if you wrap all your new code inside a module you can redefine types and constants.  You can&#39;t import the type names into <code>Main</code> and then expect to be able to redefine them there, but you can use the module name to resolve the scope.  In other words, while developing you might use a workflow something like this:</p><pre><code class="language-julia hljs">include(&quot;mynewcode.jl&quot;)              # this defines a module MyModule
obj1 = MyModule.ObjConstructor(a, b)
obj2 = MyModule.somefunction(obj1)
# Got an error. Change something in &quot;mynewcode.jl&quot;
include(&quot;mynewcode.jl&quot;)              # reload the module
obj1 = MyModule.ObjConstructor(a, b) # old objects are no longer valid, must reconstruct
obj2 = MyModule.somefunction(obj1)   # this time it worked!
obj3 = MyModule.someotherfunction(obj2, c)
...</code></pre><h2 id="man-scripting"><a class="docs-heading-anchor" href="#man-scripting">Scripting</a><a id="man-scripting-1"></a><a class="docs-heading-anchor-permalink" href="#man-scripting" title="Permalink"></a></h2><h3 id="How-do-I-check-if-the-current-file-is-being-run-as-the-main-script?"><a class="docs-heading-anchor" href="#How-do-I-check-if-the-current-file-is-being-run-as-the-main-script?">How do I check if the current file is being run as the main script?</a><a id="How-do-I-check-if-the-current-file-is-being-run-as-the-main-script?-1"></a><a class="docs-heading-anchor-permalink" href="#How-do-I-check-if-the-current-file-is-being-run-as-the-main-script?" title="Permalink"></a></h3><p>When a file is run as the main script using <code>julia file.jl</code> one might want to activate extra functionality like command line argument handling. A way to determine that a file is run in this fashion is to check if <code>abspath(PROGRAM_FILE) == @__FILE__</code> is <code>true</code>.</p><p>However, it is recommended to not write files that double as a script and as an importable library. If one needs functionality both available as a library and a script, it is better to write is as a library, then import the functionality into a distinct script.</p><h3 id="catch-ctrl-c"><a class="docs-heading-anchor" href="#catch-ctrl-c">How do I catch CTRL-C in a script?</a><a id="catch-ctrl-c-1"></a><a class="docs-heading-anchor-permalink" href="#catch-ctrl-c" title="Permalink"></a></h3><p>Running a Julia script using <code>julia file.jl</code> does not throw <a href="../base/base.html#Core.InterruptException"><code>InterruptException</code></a> when you try to terminate it with CTRL-C (SIGINT).  To run a certain code before terminating a Julia script, which may or may not be caused by CTRL-C, use <a href="../base/base.html#Base.atexit"><code>atexit</code></a>. Alternatively, you can use <code>julia -e &#39;include(popfirst!(ARGS))&#39; file.jl</code> to execute a script while being able to catch <code>InterruptException</code> in the <a href="../base/base.html#try"><code>try</code></a> block. Note that with this strategy <a href="../base/constants.html#Base.PROGRAM_FILE"><code>PROGRAM_FILE</code></a> will not be set.</p><h3 id="How-do-I-pass-options-to-julia-using-#!/usr/bin/env?"><a class="docs-heading-anchor" href="#How-do-I-pass-options-to-julia-using-#!/usr/bin/env?">How do I pass options to <code>julia</code> using <code>#!/usr/bin/env</code>?</a><a id="How-do-I-pass-options-to-julia-using-#!/usr/bin/env?-1"></a><a class="docs-heading-anchor-permalink" href="#How-do-I-pass-options-to-julia-using-#!/usr/bin/env?" title="Permalink"></a></h3><p>Passing options to <code>julia</code> in a so-called shebang line, as in <code>#!/usr/bin/env julia --startup-file=no</code>, will not work on many platforms (BSD, macOS, Linux) where the kernel, unlike the shell, does not split arguments at space characters. The option <code>env -S</code>, which splits a single argument string into multiple arguments at spaces, similar to a shell, offers a simple workaround:</p><pre><code class="language-julia hljs">#!/usr/bin/env -S julia --color=yes --startup-file=no
@show ARGS  # put any Julia code here</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Option <code>env -S</code> appeared in FreeBSD 6.0 (2005), macOS Sierra (2016) and GNU/Linux coreutils 8.30 (2018).</p></div></div><h3 id="Why-doesn&#39;t-run-support-*-or-pipes-for-scripting-external-programs?"><a class="docs-heading-anchor" href="#Why-doesn&#39;t-run-support-*-or-pipes-for-scripting-external-programs?">Why doesn&#39;t <code>run</code> support <code>*</code> or pipes for scripting external programs?</a><a id="Why-doesn&#39;t-run-support-*-or-pipes-for-scripting-external-programs?-1"></a><a class="docs-heading-anchor-permalink" href="#Why-doesn&#39;t-run-support-*-or-pipes-for-scripting-external-programs?" title="Permalink"></a></h3><p>Julia&#39;s <a href="../base/base.html#Base.run"><code>run</code></a> function launches external programs <em>directly</em>, without invoking an <a href="https://en.wikipedia.org/wiki/Shell_(computing)">operating-system shell</a> (unlike the <code>system(&quot;...&quot;)</code> function in other languages like Python, R, or C). That means that <code>run</code> does not perform wildcard expansion of <code>*</code> (<a href="https://en.wikipedia.org/wiki/Glob_(programming)">&quot;globbing&quot;</a>), nor does it interpret <a href="https://en.wikipedia.org/wiki/Pipeline_(Unix)">shell pipelines</a> like <code>|</code> or <code>&gt;</code>.</p><p>You can still do globbing and pipelines using Julia features, however.  For example, the built-in <a href="../base/base.html#Base.pipeline-Tuple{Any, Any, Any, Vararg{Any}}"><code>pipeline</code></a> function allows you to chain external programs and files, similar to shell pipes, and the <a href="https://github.com/vtjnash/Glob.jl">Glob.jl package</a> implements POSIX-compatible globbing.</p><p>You can, of course, run programs through the shell by explicitly passing a shell and a command string to <code>run</code>, e.g. <code>run(`sh -c &quot;ls &gt; files.txt&quot;`)</code> to use the Unix <a href="https://en.wikipedia.org/wiki/Bourne_shell">Bourne shell</a>, but you should generally prefer pure-Julia scripting like <code>run(pipeline(`ls`, &quot;files.txt&quot;))</code>. The reason why we avoid the shell by default is that <a href="https://julialang.org/blog/2012/03/shelling-out-sucks/">shelling out sucks</a>: launching processes via the shell is slow, fragile to quoting of special characters,  has poor error handling, and is problematic for portability.  (The Python developers came to a <a href="https://www.python.org/dev/peps/pep-0324/#motivation">similar conclusion</a>.)</p><h2 id="Variables-and-Assignments"><a class="docs-heading-anchor" href="#Variables-and-Assignments">Variables and Assignments</a><a id="Variables-and-Assignments-1"></a><a class="docs-heading-anchor-permalink" href="#Variables-and-Assignments" title="Permalink"></a></h2><h3 id="Why-am-I-getting-UndefVarError-from-a-simple-loop?"><a class="docs-heading-anchor" href="#Why-am-I-getting-UndefVarError-from-a-simple-loop?">Why am I getting <code>UndefVarError</code> from a simple loop?</a><a id="Why-am-I-getting-UndefVarError-from-a-simple-loop?-1"></a><a class="docs-heading-anchor-permalink" href="#Why-am-I-getting-UndefVarError-from-a-simple-loop?" title="Permalink"></a></h3><p>You might have something like:</p><pre><code class="nohighlight hljs">x = 0
while x &lt; 10
    x += 1
end</code></pre><p>and notice that it works fine in an interactive environment (like the Julia REPL), but gives <code>UndefVarError: `x` not defined</code> when you try to run it in script or other file.   What is going on is that Julia generally requires you to <strong>be explicit about assigning to global variables in a local scope</strong>.</p><p>Here, <code>x</code> is a global variable, <code>while</code> defines a <a href="variables-and-scoping.html#scope-of-variables">local scope</a>, and <code>x += 1</code> is an assignment to a global in that local scope.</p><p>As mentioned above, Julia (version 1.5 or later) allows you to omit the <code>global</code> keyword for code in the REPL (and many other interactive environments), to simplify exploration (e.g. copy-pasting code from a function to run interactively). However, once you move to code in files, Julia requires a more disciplined approach to global variables.  You have least three options:</p><ol><li>Put the code into a function (so that <code>x</code> is a <em>local</em> variable in a function). In general, it is good software engineering to use functions rather than global scripts (search online for &quot;why global variables bad&quot; to see many explanations). In Julia, global variables are also <a href="performance-tips.html#man-performance-tips">slow</a>.</li><li>Wrap the code in a <a href="../base/base.html#let"><code>let</code></a> block.  (This makes <code>x</code> a local variable within the <code>let ... end</code> statement, again eliminating the need for <code>global</code>).</li><li>Explicitly mark <code>x</code> as <code>global</code> inside the local scope before assigning to it, e.g. write <code>global x += 1</code>.</li></ol><p>More explanation can be found in the manual section <a href="variables-and-scoping.html#on-soft-scope">on soft scope</a>.</p><h2 id="Functions"><a class="docs-heading-anchor" href="#Functions">Functions</a><a id="Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Functions" title="Permalink"></a></h2><h3 id="I-passed-an-argument-x-to-a-function,-modified-it-inside-that-function,-but-on-the-outside,-the-variable-x-is-still-unchanged.-Why?"><a class="docs-heading-anchor" href="#I-passed-an-argument-x-to-a-function,-modified-it-inside-that-function,-but-on-the-outside,-the-variable-x-is-still-unchanged.-Why?">I passed an argument <code>x</code> to a function, modified it inside that function, but on the outside, the variable <code>x</code> is still unchanged. Why?</a><a id="I-passed-an-argument-x-to-a-function,-modified-it-inside-that-function,-but-on-the-outside,-the-variable-x-is-still-unchanged.-Why?-1"></a><a class="docs-heading-anchor-permalink" href="#I-passed-an-argument-x-to-a-function,-modified-it-inside-that-function,-but-on-the-outside,-the-variable-x-is-still-unchanged.-Why?" title="Permalink"></a></h3><p>Suppose you call a function like this:</p><pre><code class="language-julia-repl hljs">julia&gt; x = 10
10

julia&gt; function change_value!(y)
           y = 17
       end
change_value! (generic function with 1 method)

julia&gt; change_value!(x)
17

julia&gt; x # x is unchanged!
10</code></pre><p>In Julia, the binding of a variable <code>x</code> cannot be changed by passing <code>x</code> as an argument to a function. When calling <code>change_value!(x)</code> in the above example, <code>y</code> is a newly created variable, bound initially to the value of <code>x</code>, i.e. <code>10</code>; then <code>y</code> is rebound to the constant <code>17</code>, while the variable <code>x</code> of the outer scope is left untouched.</p><p>However, if <code>x</code> is bound to an object of type <code>Array</code> (or any other <em>mutable</em> type). From within the function, you cannot &quot;unbind&quot; <code>x</code> from this Array, but you <em>can</em> change its content. For example:</p><pre><code class="language-julia-repl hljs">julia&gt; x = [1,2,3]
3-element Vector{Int64}:
 1
 2
 3

julia&gt; function change_array!(A)
           A[1] = 5
       end
change_array! (generic function with 1 method)

julia&gt; change_array!(x)
5

julia&gt; x
3-element Vector{Int64}:
 5
 2
 3</code></pre><p>Here we created a function <code>change_array!</code>, that assigns <code>5</code> to the first element of the passed array (bound to <code>x</code> at the call site, and bound to <code>A</code> within the function). Notice that, after the function call, <code>x</code> is still bound to the same array, but the content of that array changed: the variables <code>A</code> and <code>x</code> were distinct bindings referring to the same mutable <code>Array</code> object.</p><h3 id="Can-I-use-using-or-import-inside-a-function?"><a class="docs-heading-anchor" href="#Can-I-use-using-or-import-inside-a-function?">Can I use <code>using</code> or <code>import</code> inside a function?</a><a id="Can-I-use-using-or-import-inside-a-function?-1"></a><a class="docs-heading-anchor-permalink" href="#Can-I-use-using-or-import-inside-a-function?" title="Permalink"></a></h3><p>No, you are not allowed to have a <code>using</code> or <code>import</code> statement inside a function.  If you want to import a module but only use its symbols inside a specific function or set of functions, you have two options:</p><ol><li><p>Use <code>import</code>:</p><pre><code class="language-julia hljs">import Foo
function bar(...)
    # ... refer to Foo symbols via Foo.baz ...
end</code></pre><p>This loads the module <code>Foo</code> and defines a variable <code>Foo</code> that refers to the module, but does not import any of the other symbols from the module into the current namespace.  You refer to the <code>Foo</code> symbols by their qualified names <code>Foo.bar</code> etc.</p></li><li><p>Wrap your function in a module:</p><pre><code class="language-julia hljs">module Bar
export bar
using Foo
function bar(...)
    # ... refer to Foo.baz as simply baz ....
end
end
using Bar</code></pre><p>This imports all the symbols from <code>Foo</code>, but only inside the module <code>Bar</code>.</p></li></ol><h3 id="What-does-the-...-operator-do?"><a class="docs-heading-anchor" href="#What-does-the-...-operator-do?">What does the <code>...</code> operator do?</a><a id="What-does-the-...-operator-do?-1"></a><a class="docs-heading-anchor-permalink" href="#What-does-the-...-operator-do?" title="Permalink"></a></h3><h4 id="The-two-uses-of-the-...-operator:-slurping-and-splatting"><a class="docs-heading-anchor" href="#The-two-uses-of-the-...-operator:-slurping-and-splatting">The two uses of the <code>...</code> operator: slurping and splatting</a><a id="The-two-uses-of-the-...-operator:-slurping-and-splatting-1"></a><a class="docs-heading-anchor-permalink" href="#The-two-uses-of-the-...-operator:-slurping-and-splatting" title="Permalink"></a></h4><p>Many newcomers to Julia find the use of <code>...</code> operator confusing. Part of what makes the <code>...</code> operator confusing is that it means two different things depending on context.</p><h4 id="...-combines-many-arguments-into-one-argument-in-function-definitions"><a class="docs-heading-anchor" href="#...-combines-many-arguments-into-one-argument-in-function-definitions"><code>...</code> combines many arguments into one argument in function definitions</a><a id="...-combines-many-arguments-into-one-argument-in-function-definitions-1"></a><a class="docs-heading-anchor-permalink" href="#...-combines-many-arguments-into-one-argument-in-function-definitions" title="Permalink"></a></h4><p>In the context of function definitions, the <code>...</code> operator is used to combine many different arguments into a single argument. This use of <code>...</code> for combining many different arguments into a single argument is called slurping:</p><pre><code class="language-julia-repl hljs">julia&gt; function printargs(args...)
           println(typeof(args))
           for (i, arg) in enumerate(args)
               println(&quot;Arg #$i = $arg&quot;)
           end
       end
printargs (generic function with 1 method)

julia&gt; printargs(1, 2, 3)
Tuple{Int64, Int64, Int64}
Arg #1 = 1
Arg #2 = 2
Arg #3 = 3</code></pre><p>If Julia were a language that made more liberal use of ASCII characters, the slurping operator might have been written as <code>&lt;-...</code> instead of <code>...</code>.</p><h4 id="...-splits-one-argument-into-many-different-arguments-in-function-calls"><a class="docs-heading-anchor" href="#...-splits-one-argument-into-many-different-arguments-in-function-calls"><code>...</code> splits one argument into many different arguments in function calls</a><a id="...-splits-one-argument-into-many-different-arguments-in-function-calls-1"></a><a class="docs-heading-anchor-permalink" href="#...-splits-one-argument-into-many-different-arguments-in-function-calls" title="Permalink"></a></h4><p>In contrast to the use of the <code>...</code> operator to denote slurping many different arguments into one argument when defining a function, the <code>...</code> operator is also used to cause a single function argument to be split apart into many different arguments when used in the context of a function call. This use of <code>...</code> is called splatting:</p><pre><code class="language-julia-repl hljs">julia&gt; function threeargs(a, b, c)
           println(&quot;a = $a::$(typeof(a))&quot;)
           println(&quot;b = $b::$(typeof(b))&quot;)
           println(&quot;c = $c::$(typeof(c))&quot;)
       end
threeargs (generic function with 1 method)

julia&gt; x = [1, 2, 3]
3-element Vector{Int64}:
 1
 2
 3

julia&gt; threeargs(x...)
a = 1::Int64
b = 2::Int64
c = 3::Int64</code></pre><p>If Julia were a language that made more liberal use of ASCII characters, the splatting operator might have been written as <code>...-&gt;</code> instead of <code>...</code>.</p><h3 id="What-is-the-return-value-of-an-assignment?"><a class="docs-heading-anchor" href="#What-is-the-return-value-of-an-assignment?">What is the return value of an assignment?</a><a id="What-is-the-return-value-of-an-assignment?-1"></a><a class="docs-heading-anchor-permalink" href="#What-is-the-return-value-of-an-assignment?" title="Permalink"></a></h3><p>The operator <code>=</code> always returns the right-hand side, therefore:</p><pre><code class="language-julia-repl hljs">julia&gt; function threeint()
           x::Int = 3.0
           x # returns variable x
       end
threeint (generic function with 1 method)

julia&gt; function threefloat()
           x::Int = 3.0 # returns 3.0
       end
threefloat (generic function with 1 method)

julia&gt; threeint()
3

julia&gt; threefloat()
3.0</code></pre><p>and similarly:</p><pre><code class="language-julia-repl hljs">julia&gt; function twothreetup()
           x, y = [2, 3] # assigns 2 to x and 3 to y
           x, y # returns a tuple
       end
twothreetup (generic function with 1 method)

julia&gt; function twothreearr()
           x, y = [2, 3] # returns an array
       end
twothreearr (generic function with 1 method)

julia&gt; twothreetup()
(2, 3)

julia&gt; twothreearr()
2-element Vector{Int64}:
 2
 3</code></pre><h2 id="Types,-type-declarations,-and-constructors"><a class="docs-heading-anchor" href="#Types,-type-declarations,-and-constructors">Types, type declarations, and constructors</a><a id="Types,-type-declarations,-and-constructors-1"></a><a class="docs-heading-anchor-permalink" href="#Types,-type-declarations,-and-constructors" title="Permalink"></a></h2><h3 id="man-type-stability"><a class="docs-heading-anchor" href="#man-type-stability">What does &quot;type-stable&quot; mean?</a><a id="man-type-stability-1"></a><a class="docs-heading-anchor-permalink" href="#man-type-stability" title="Permalink"></a></h3><p>It means that the type of the output is predictable from the types of the inputs.  In particular, it means that the type of the output cannot vary depending on the <em>values</em> of the inputs. The following code is <em>not</em> type-stable:</p><pre><code class="language-julia-repl hljs">julia&gt; function unstable(flag::Bool)
           if flag
               return 1
           else
               return 1.0
           end
       end
unstable (generic function with 1 method)</code></pre><p>It returns either an <code>Int</code> or a <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> depending on the value of its argument. Since Julia can&#39;t predict the return type of this function at compile-time, any computation that uses it must be able to cope with values of both types, which makes it hard to produce fast machine code.</p><h3 id="faq-domain-errors"><a class="docs-heading-anchor" href="#faq-domain-errors">Why does Julia give a <code>DomainError</code> for certain seemingly-sensible operations?</a><a id="faq-domain-errors-1"></a><a class="docs-heading-anchor-permalink" href="#faq-domain-errors" title="Permalink"></a></h3><p>Certain operations make mathematical sense but result in errors:</p><pre><code class="language-julia-repl hljs">julia&gt; sqrt(-2.0)
ERROR: DomainError with -2.0:
sqrt was called with a negative real argument but will only return a complex result if called with a complex argument. Try sqrt(Complex(x)).
Stacktrace:
[...]</code></pre><p>This behavior is an inconvenient consequence of the requirement for type-stability.  In the case of <a href="../base/math.html#Base.sqrt-Tuple{Number}"><code>sqrt</code></a>, most users want <code>sqrt(2.0)</code> to give a real number, and would be unhappy if it produced the complex number <code>1.4142135623730951 + 0.0im</code>.  One could write the <a href="../base/math.html#Base.sqrt-Tuple{Number}"><code>sqrt</code></a> function to switch to a complex-valued output only when passed a negative number (which is what <a href="../base/math.html#Base.sqrt-Tuple{Number}"><code>sqrt</code></a> does in some other languages), but then the result would not be <a href="faq.html#man-type-stability">type-stable</a> and the <a href="../base/math.html#Base.sqrt-Tuple{Number}"><code>sqrt</code></a> function would have poor performance.</p><p>In these and other cases, you can get the result you want by choosing an <em>input type</em> that conveys your willingness to accept an <em>output type</em> in which the result can be represented:</p><pre><code class="language-julia-repl hljs">julia&gt; sqrt(-2.0+0im)
0.0 + 1.4142135623730951im</code></pre><h3 id="How-can-I-constrain-or-compute-type-parameters?"><a class="docs-heading-anchor" href="#How-can-I-constrain-or-compute-type-parameters?">How can I constrain or compute type parameters?</a><a id="How-can-I-constrain-or-compute-type-parameters?-1"></a><a class="docs-heading-anchor-permalink" href="#How-can-I-constrain-or-compute-type-parameters?" title="Permalink"></a></h3><p>The parameters of a <a href="types.html#Parametric-Types">parametric type</a> can hold either types or bits values, and the type itself chooses how it makes use of these parameters. For example, <code>Array{Float64, 2}</code> is parameterized by the type <code>Float64</code> to express its element type and the integer value <code>2</code> to express its number of dimensions.  When defining your own parametric type, you can use subtype constraints to declare that a certain parameter must be a subtype (<a href="../base/base.html#Core.:&lt;:"><code>&lt;:</code></a>) of some abstract type or a previous type parameter.  There is not, however, a dedicated syntax to declare that a parameter must be a <em>value</em> of a given type — that is, you cannot directly declare that a dimensionality-like parameter <a href="../base/base.html#Core.isa"><code>isa</code></a> <code>Int</code> within the <code>struct</code> definition, for example.  Similarly, you cannot do computations (including simple things like addition or subtraction) on type parameters.  Instead, these sorts of constraints and relationships may be expressed through additional type parameters that are computed and enforced within the type&#39;s <a href="constructors.html#man-constructors">constructors</a>.</p><p>As an example, consider</p><pre><code class="language-julia hljs">struct ConstrainedType{T,N,N+1} # NOTE: INVALID SYNTAX
    A::Array{T,N}
    B::Array{T,N+1}
end</code></pre><p>where the user would like to enforce that the third type parameter is always the second plus one. This can be implemented with an explicit type parameter that is checked by an <a href="constructors.html#man-inner-constructor-methods">inner constructor method</a> (where it can be combined with other checks):</p><pre><code class="language-julia hljs">struct ConstrainedType{T,N,M}
    A::Array{T,N}
    B::Array{T,M}
    function ConstrainedType(A::Array{T,N}, B::Array{T,M}) where {T,N,M}
        N + 1 == M || throw(ArgumentError(&quot;second argument should have one more axis&quot; ))
        new{T,N,M}(A, B)
    end
end</code></pre><p>This check is usually <em>costless</em>, as the compiler can elide the check for valid concrete types. If the second argument is also computed, it may be advantageous to provide an <a href="constructors.html#man-outer-constructor-methods">outer constructor method</a> that performs this calculation:</p><pre><code class="language-julia hljs">ConstrainedType(A) = ConstrainedType(A, compute_B(A))</code></pre><h3 id="faq-integer-arithmetic"><a class="docs-heading-anchor" href="#faq-integer-arithmetic">Why does Julia use native machine integer arithmetic?</a><a id="faq-integer-arithmetic-1"></a><a class="docs-heading-anchor-permalink" href="#faq-integer-arithmetic" title="Permalink"></a></h3><p>Julia uses machine arithmetic for integer computations. This means that the range of <code>Int</code> values is bounded and wraps around at either end so that adding, subtracting and multiplying integers can overflow or underflow, leading to some results that can be unsettling at first:</p><pre><code class="language-julia-repl hljs">julia&gt; x = typemax(Int)
9223372036854775807

julia&gt; y = x+1
-9223372036854775808

julia&gt; z = -y
-9223372036854775808

julia&gt; 2*z
0</code></pre><p>Clearly, this is far from the way mathematical integers behave, and you might think it less than ideal for a high-level programming language to expose this to the user. For numerical work where efficiency and transparency are at a premium, however, the alternatives are worse.</p><p>One alternative to consider would be to check each integer operation for overflow and promote results to bigger integer types such as <a href="../base/numbers.html#Core.Int128"><code>Int128</code></a> or <a href="../base/numbers.html#Base.GMP.BigInt"><code>BigInt</code></a> in the case of overflow. Unfortunately, this introduces major overhead on every integer operation (think incrementing a loop counter) – it requires emitting code to perform run-time overflow checks after arithmetic instructions and branches to handle potential overflows. Worse still, this would cause every computation involving integers to be type-unstable. As we mentioned above, <a href="faq.html#man-type-stability">type-stability is crucial</a> for effective generation of efficient code. If you can&#39;t count on the results of integer operations being integers, it&#39;s impossible to generate fast, simple code the way C and Fortran compilers do.</p><p>A variation on this approach, which avoids the appearance of type instability is to merge the <code>Int</code> and <a href="../base/numbers.html#Base.GMP.BigInt"><code>BigInt</code></a> types into a single hybrid integer type, that internally changes representation when a result no longer fits into the size of a machine integer. While this superficially avoids type-instability at the level of Julia code, it just sweeps the problem under the rug by foisting all of the same difficulties onto the C code implementing this hybrid integer type. This approach <em>can</em> be made to work and can even be made quite fast in many cases, but has several drawbacks. One problem is that the in-memory representation of integers and arrays of integers no longer match the natural representation used by C, Fortran and other languages with native machine integers. Thus, to interoperate with those languages, we would ultimately need to introduce native integer types anyway. Any unbounded representation of integers cannot have a fixed number of bits, and thus cannot be stored inline in an array with fixed-size slots – large integer values will always require separate heap-allocated storage. And of course, no matter how clever a hybrid integer implementation one uses, there are always performance traps – situations where performance degrades unexpectedly. Complex representation, lack of interoperability with C and Fortran, the inability to represent integer arrays without additional heap storage, and unpredictable performance characteristics make even the cleverest hybrid integer implementations a poor choice for high-performance numerical work.</p><p>An alternative to using hybrid integers or promoting to BigInts is to use saturating integer arithmetic, where adding to the largest integer value leaves it unchanged and likewise for subtracting from the smallest integer value. This is precisely what Matlab™ does:</p><pre><code class="nohighlight hljs">&gt;&gt; int64(9223372036854775807)

ans =

  9223372036854775807

&gt;&gt; int64(9223372036854775807) + 1

ans =

  9223372036854775807

&gt;&gt; int64(-9223372036854775808)

ans =

 -9223372036854775808

&gt;&gt; int64(-9223372036854775808) - 1

ans =

 -9223372036854775808</code></pre><p>At first blush, this seems reasonable enough since 9223372036854775807 is much closer to 9223372036854775808 than -9223372036854775808 is and integers are still represented with a fixed size in a natural way that is compatible with C and Fortran. Saturated integer arithmetic, however, is deeply problematic. The first and most obvious issue is that this is not the way machine integer arithmetic works, so implementing saturated operations requires emitting instructions after each machine integer operation to check for underflow or overflow and replace the result with <a href="../base/base.html#Base.typemin"><code>typemin(Int)</code></a> or <a href="../base/base.html#Base.typemax"><code>typemax(Int)</code></a> as appropriate. This alone expands each integer operation from a single, fast instruction into half a dozen instructions, probably including branches. Ouch. But it gets worse – saturating integer arithmetic isn&#39;t associative. Consider this Matlab computation:</p><pre><code class="nohighlight hljs">&gt;&gt; n = int64(2)^62
4611686018427387904

&gt;&gt; n + (n - 1)
9223372036854775807

&gt;&gt; (n + n) - 1
9223372036854775806</code></pre><p>This makes it hard to write many basic integer algorithms since a lot of common techniques depend on the fact that machine addition with overflow <em>is</em> associative. Consider finding the midpoint between integer values <code>lo</code> and <code>hi</code> in Julia using the expression <code>(lo + hi) &gt;&gt;&gt; 1</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; n = 2^62
4611686018427387904

julia&gt; (n + 2n) &gt;&gt;&gt; 1
6917529027641081856</code></pre><p>See? No problem. That&#39;s the correct midpoint between 2^62 and 2^63, despite the fact that <code>n + 2n</code> is -4611686018427387904. Now try it in Matlab:</p><pre><code class="nohighlight hljs">&gt;&gt; (n + 2*n)/2

ans =

  4611686018427387904</code></pre><p>Oops. Adding a <code>&gt;&gt;&gt;</code> operator to Matlab wouldn&#39;t help, because saturation that occurs when adding <code>n</code> and <code>2n</code> has already destroyed the information necessary to compute the correct midpoint.</p><p>Not only is lack of associativity unfortunate for programmers who cannot rely it for techniques like this, but it also defeats almost anything compilers might want to do to optimize integer arithmetic. For example, since Julia integers use normal machine integer arithmetic, LLVM is free to aggressively optimize simple little functions like <code>f(k) = 5k-1</code>. The machine code for this function is just this:</p><pre><code class="language-julia-repl hljs">julia&gt; code_native(f, Tuple{Int})
  .text
Filename: none
  pushq %rbp
  movq  %rsp, %rbp
Source line: 1
  leaq  -1(%rdi,%rdi,4), %rax
  popq  %rbp
  retq
  nopl  (%rax,%rax)</code></pre><p>The actual body of the function is a single <code>leaq</code> instruction, which computes the integer multiply and add at once. This is even more beneficial when <code>f</code> gets inlined into another function:</p><pre><code class="language-julia-repl hljs">julia&gt; function g(k, n)
           for i = 1:n
               k = f(k)
           end
           return k
       end
g (generic function with 1 methods)

julia&gt; code_native(g, Tuple{Int,Int})
  .text
Filename: none
  pushq %rbp
  movq  %rsp, %rbp
Source line: 2
  testq %rsi, %rsi
  jle L26
  nopl  (%rax)
Source line: 3
L16:
  leaq  -1(%rdi,%rdi,4), %rdi
Source line: 2
  decq  %rsi
  jne L16
Source line: 5
L26:
  movq  %rdi, %rax
  popq  %rbp
  retq
  nop</code></pre><p>Since the call to <code>f</code> gets inlined, the loop body ends up being just a single <code>leaq</code> instruction. Next, consider what happens if we make the number of loop iterations fixed:</p><pre><code class="language-julia-repl hljs">julia&gt; function g(k)
           for i = 1:10
               k = f(k)
           end
           return k
       end
g (generic function with 2 methods)

julia&gt; code_native(g,(Int,))
  .text
Filename: none
  pushq %rbp
  movq  %rsp, %rbp
Source line: 3
  imulq $9765625, %rdi, %rax    # imm = 0x9502F9
  addq  $-2441406, %rax         # imm = 0xFFDABF42
Source line: 5
  popq  %rbp
  retq
  nopw  %cs:(%rax,%rax)</code></pre><p>Because the compiler knows that integer addition and multiplication are associative and that multiplication distributes over addition – neither of which is true of saturating arithmetic – it can optimize the entire loop down to just a multiply and an add. Saturated arithmetic completely defeats this kind of optimization since associativity and distributivity can fail at each loop iteration, causing different outcomes depending on which iteration the failure occurs in. The compiler can unroll the loop, but it cannot algebraically reduce multiple operations into fewer equivalent operations.</p><p>The most reasonable alternative to having integer arithmetic silently overflow is to do checked arithmetic everywhere, raising errors when adds, subtracts, and multiplies overflow, producing values that are not value-correct. In this <a href="https://danluu.com/integer-overflow/">blog post</a>, Dan Luu analyzes this and finds that rather than the trivial cost that this approach should in theory have, it ends up having a substantial cost due to compilers (LLVM and GCC) not gracefully optimizing around the added overflow checks. If this improves in the future, we could consider defaulting to checked integer arithmetic in Julia, but for now, we have to live with the possibility of overflow.</p><p>In the meantime, overflow-safe integer operations can be achieved through the use of external libraries such as <a href="https://github.com/JeffreySarnoff/SaferIntegers.jl">SaferIntegers.jl</a>. Note that, as stated previously, the use of these libraries significantly increases the execution time of code using the checked integer types. However, for limited usage, this is far less of an issue than if it were used for all integer operations. You can follow the status of the discussion <a href="https://github.com/JuliaLang/julia/issues/855">here</a>.</p><h3 id="What-are-the-possible-causes-of-an-UndefVarError-during-remote-execution?"><a class="docs-heading-anchor" href="#What-are-the-possible-causes-of-an-UndefVarError-during-remote-execution?">What are the possible causes of an <code>UndefVarError</code> during remote execution?</a><a id="What-are-the-possible-causes-of-an-UndefVarError-during-remote-execution?-1"></a><a class="docs-heading-anchor-permalink" href="#What-are-the-possible-causes-of-an-UndefVarError-during-remote-execution?" title="Permalink"></a></h3><p>As the error states, an immediate cause of an <code>UndefVarError</code> on a remote node is that a binding by that name does not exist. Let us explore some of the possible causes.</p><pre><code class="language-julia-repl hljs">julia&gt; module Foo
           foo() = remotecall_fetch(x-&gt;x, 2, &quot;Hello&quot;)
       end

julia&gt; Foo.foo()
ERROR: On worker 2:
UndefVarError: `Foo` not defined in `Main`
Stacktrace:
[...]</code></pre><p>The closure <code>x-&gt;x</code> carries a reference to <code>Foo</code>, and since <code>Foo</code> is unavailable on node 2, an <code>UndefVarError</code> is thrown.</p><p>Globals under modules other than <code>Main</code> are not serialized by value to the remote node. Only a reference is sent. Functions which create global bindings (except under <code>Main</code>) may cause an <code>UndefVarError</code> to be thrown later.</p><pre><code class="language-julia-repl hljs">julia&gt; @everywhere module Foo
           function foo()
               global gvar = &quot;Hello&quot;
               remotecall_fetch(()-&gt;gvar, 2)
           end
       end

julia&gt; Foo.foo()
ERROR: On worker 2:
UndefVarError: `gvar` not defined in `Main.Foo`
Stacktrace:
[...]</code></pre><p>In the above example, <code>@everywhere module Foo</code> defined <code>Foo</code> on all nodes. However the call to <code>Foo.foo()</code> created a new global binding <code>gvar</code> on the local node, but this was not found on node 2 resulting in an <code>UndefVarError</code> error.</p><p>Note that this does not apply to globals created under module <code>Main</code>. Globals under module <code>Main</code> are serialized and new bindings created under <code>Main</code> on the remote node.</p><pre><code class="language-julia-repl hljs">julia&gt; gvar_self = &quot;Node1&quot;
&quot;Node1&quot;

julia&gt; remotecall_fetch(()-&gt;gvar_self, 2)
&quot;Node1&quot;

julia&gt; remotecall_fetch(varinfo, 2)
name          size summary
––––––––– –––––––– –––––––
Base               Module
Core               Module
Main               Module
gvar_self 13 bytes String</code></pre><p>This does not apply to <code>function</code> or <code>struct</code> declarations. However, anonymous functions bound to global variables are serialized as can be seen below.</p><pre><code class="language-julia-repl hljs">julia&gt; bar() = 1
bar (generic function with 1 method)

julia&gt; remotecall_fetch(bar, 2)
ERROR: On worker 2:
UndefVarError: `#bar` not defined in `Main`
[...]

julia&gt; anon_bar  = ()-&gt;1
(::#21) (generic function with 1 method)

julia&gt; remotecall_fetch(anon_bar, 2)
1</code></pre><h2 id="Troubleshooting-&quot;method-not-matched&quot;:-parametric-type-invariance-and-MethodErrors"><a class="docs-heading-anchor" href="#Troubleshooting-&quot;method-not-matched&quot;:-parametric-type-invariance-and-MethodErrors">Troubleshooting &quot;method not matched&quot;: parametric type invariance and <code>MethodError</code>s</a><a id="Troubleshooting-&quot;method-not-matched&quot;:-parametric-type-invariance-and-MethodErrors-1"></a><a class="docs-heading-anchor-permalink" href="#Troubleshooting-&quot;method-not-matched&quot;:-parametric-type-invariance-and-MethodErrors" title="Permalink"></a></h2><h3 id="Why-doesn&#39;t-it-work-to-declare-foo(bar::Vector{Real})-42-and-then-call-foo([1])?"><a class="docs-heading-anchor" href="#Why-doesn&#39;t-it-work-to-declare-foo(bar::Vector{Real})-42-and-then-call-foo([1])?">Why doesn&#39;t it work to declare <code>foo(bar::Vector{Real}) = 42</code> and then call <code>foo([1])</code>?</a><a id="Why-doesn&#39;t-it-work-to-declare-foo(bar::Vector{Real})-42-and-then-call-foo([1])?-1"></a><a class="docs-heading-anchor-permalink" href="#Why-doesn&#39;t-it-work-to-declare-foo(bar::Vector{Real})-42-and-then-call-foo([1])?" title="Permalink"></a></h3><p>As you&#39;ll see if you try this, the result is a <code>MethodError</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; foo(x::Vector{Real}) = 42
foo (generic function with 1 method)

julia&gt; foo([1])
ERROR: MethodError: no method matching foo(::Vector{Int64})
The function `foo` exists, but no method is defined for this combination of argument types.

Closest candidates are:
  foo(!Matched::Vector{Real})
   @ Main none:1

Stacktrace:
[...]</code></pre><p>This is because <code>Vector{Real}</code> is not a supertype of <code>Vector{Int}</code>! You can solve this problem with something like <code>foo(bar::Vector{T}) where {T&lt;:Real}</code> (or the short form <code>foo(bar::Vector{&lt;:Real})</code> if the static parameter <code>T</code> is not needed in the body of the function). The <code>T</code> is a wild card: you first specify that it must be a subtype of Real, then specify the function takes a Vector of with elements of that type.</p><p>This same issue goes for any composite type <code>Comp</code>, not just <code>Vector</code>. If <code>Comp</code> has a parameter declared of type <code>Y</code>, then another type <code>Comp2</code> with a parameter of type <code>X&lt;:Y</code> is not a subtype of <code>Comp</code>. This is type-invariance (by contrast, Tuple is type-covariant in its parameters). See <a href="types.html#man-parametric-composite-types">Parametric Composite Types</a> for more explanation of these.</p><h3 id="Why-does-Julia-use-*-for-string-concatenation?-Why-not-or-something-else?"><a class="docs-heading-anchor" href="#Why-does-Julia-use-*-for-string-concatenation?-Why-not-or-something-else?">Why does Julia use <code>*</code> for string concatenation? Why not <code>+</code> or something else?</a><a id="Why-does-Julia-use-*-for-string-concatenation?-Why-not-or-something-else?-1"></a><a class="docs-heading-anchor-permalink" href="#Why-does-Julia-use-*-for-string-concatenation?-Why-not-or-something-else?" title="Permalink"></a></h3><p>The <a href="strings.html#man-concatenation">main argument</a> against <code>+</code> is that string concatenation is not commutative, while <code>+</code> is generally used as a commutative operator. While the Julia community recognizes that other languages use different operators and <code>*</code> may be unfamiliar for some users, it communicates certain algebraic properties.</p><p>Note that you can also use <code>string(...)</code> to concatenate strings (and other values converted to strings); similarly, <code>repeat</code> can be used instead of <code>^</code> to repeat strings. The <a href="strings.html#string-interpolation">interpolation syntax</a> is also useful for constructing strings.</p><h2 id="Packages-and-Modules"><a class="docs-heading-anchor" href="#Packages-and-Modules">Packages and Modules</a><a id="Packages-and-Modules-1"></a><a class="docs-heading-anchor-permalink" href="#Packages-and-Modules" title="Permalink"></a></h2><h3 id="What-is-the-difference-between-&quot;using&quot;-and-&quot;import&quot;?"><a class="docs-heading-anchor" href="#What-is-the-difference-between-&quot;using&quot;-and-&quot;import&quot;?">What is the difference between &quot;using&quot; and &quot;import&quot;?</a><a id="What-is-the-difference-between-&quot;using&quot;-and-&quot;import&quot;?-1"></a><a class="docs-heading-anchor-permalink" href="#What-is-the-difference-between-&quot;using&quot;-and-&quot;import&quot;?" title="Permalink"></a></h3><p>There are several differences between <code>using</code> and <code>import</code> (see the <a href="https://docs.julialang.org/en/v1/manual/modules/#modules">Modules section</a>), but there is an important difference that may not seem intuitive at first glance, and on the surface (i.e. syntax-wise) it may seem very minor. When loading modules with <code>using</code>, you need to say <code>function Foo.bar(...</code> to extend module <code>Foo</code>&#39;s function <code>bar</code> with a new method, but with <code>import Foo.bar</code>, you only need to say <code>function bar(...</code> and it automatically extends module <code>Foo</code>&#39;s function <code>bar</code>.</p><p>The reason this is important enough to have been given separate syntax is that you don&#39;t want to accidentally extend a function that you didn&#39;t know existed, because that could easily cause a bug. This is most likely to happen with a method that takes a common type like a string or integer, because both you and the other module could define a method to handle such a common type. If you use <code>import</code>, then you&#39;ll replace the other module&#39;s implementation of <code>bar(s::AbstractString)</code> with your new implementation, which could easily do something completely different (and break all/many future usages of the other functions in module Foo that depend on calling bar).</p><h2 id="Nothingness-and-missing-values"><a class="docs-heading-anchor" href="#Nothingness-and-missing-values">Nothingness and missing values</a><a id="Nothingness-and-missing-values-1"></a><a class="docs-heading-anchor-permalink" href="#Nothingness-and-missing-values" title="Permalink"></a></h2><h3 id="faq-nothing"><a class="docs-heading-anchor" href="#faq-nothing">How does &quot;null&quot;, &quot;nothingness&quot; or &quot;missingness&quot; work in Julia?</a><a id="faq-nothing-1"></a><a class="docs-heading-anchor-permalink" href="#faq-nothing" title="Permalink"></a></h3><p>Unlike many languages (for example, C and Java), Julia objects cannot be &quot;null&quot; by default. When a reference (variable, object field, or array element) is uninitialized, accessing it will immediately throw an error. This situation can be detected using the <a href="../base/base.html#Core.isdefined"><code>isdefined</code></a> or <a href="../base/arrays.html#Base.isassigned"><code>isassigned</code></a> functions.</p><p>Some functions are used only for their side effects, and do not need to return a value. In these cases, the convention is to return the value <code>nothing</code>, which is just a singleton object of type <code>Nothing</code>. This is an ordinary type with no fields; there is nothing special about it except for this convention, and that the REPL does not print anything for it. Some language constructs that would not otherwise have a value also yield <code>nothing</code>, for example <code>if false; end</code>.</p><p>For situations where a value <code>x</code> of type <code>T</code> exists only sometimes, the <code>Union{T, Nothing}</code> type can be used for function arguments, object fields and array element types as the equivalent of <a href="https://en.wikipedia.org/wiki/Nullable_type"><code>Nullable</code>, <code>Option</code> or <code>Maybe</code></a> in other languages. If the value itself can be <code>nothing</code> (notably, when <code>T</code> is <code>Any</code>), the <code>Union{Some{T}, Nothing}</code> type is more appropriate since <code>x == nothing</code> then indicates the absence of a value, and <code>x == Some(nothing)</code> indicates the presence of a value equal to <code>nothing</code>. The <a href="../base/base.html#Base.something"><code>something</code></a> function allows unwrapping <code>Some</code> objects and using a default value instead of <code>nothing</code> arguments. Note that the compiler is able to generate efficient code when working with <code>Union{T, Nothing}</code> arguments or fields.</p><p>To represent missing data in the statistical sense (<code>NA</code> in R or <code>NULL</code> in SQL), use the <a href="missing.html#missing"><code>missing</code></a> object. See the <a href="missing.html#missing"><code>Missing Values</code></a> section for more details.</p><p>In some languages, the empty tuple (<code>()</code>) is considered the canonical form of nothingness. However, in julia it is best thought of as just a regular tuple that happens to contain zero values.</p><p>The empty (or &quot;bottom&quot;) type, written as <code>Union{}</code> (an empty union type), is a type with no values and no subtypes (except itself). You will generally not need to use this type.</p><h2 id="Memory"><a class="docs-heading-anchor" href="#Memory">Memory</a><a id="Memory-1"></a><a class="docs-heading-anchor-permalink" href="#Memory" title="Permalink"></a></h2><h3 id="Why-does-x-y-allocate-memory-when-x-and-y-are-arrays?"><a class="docs-heading-anchor" href="#Why-does-x-y-allocate-memory-when-x-and-y-are-arrays?">Why does <code>x += y</code> allocate memory when <code>x</code> and <code>y</code> are arrays?</a><a id="Why-does-x-y-allocate-memory-when-x-and-y-are-arrays?-1"></a><a class="docs-heading-anchor-permalink" href="#Why-does-x-y-allocate-memory-when-x-and-y-are-arrays?" title="Permalink"></a></h3><p>In Julia, <code>x += y</code> gets replaced during lowering by <code>x = x + y</code>. For arrays, this has the consequence that, rather than storing the result in the same location in memory as <code>x</code>, it allocates a new array to store the result. If you prefer to mutate <code>x</code>, use <code>x .+= y</code> to update each element individually.</p><p>While this behavior might surprise some, the choice is deliberate. The main reason is the presence of immutable objects within Julia, which cannot change their value once created.  Indeed, a number is an immutable object; the statements <code>x = 5; x += 1</code> do not modify the meaning of <code>5</code>, they modify the value bound to <code>x</code>. For an immutable, the only way to change the value is to reassign it.</p><p>To amplify a bit further, consider the following function:</p><pre><code class="language-julia hljs">function power_by_squaring(x, n::Int)
    ispow2(n) || error(&quot;This implementation only works for powers of 2&quot;)
    while n &gt;= 2
        x *= x
        n &gt;&gt;= 1
    end
    x
end</code></pre><p>After a call like <code>x = 5; y = power_by_squaring(x, 4)</code>, you would get the expected result: <code>x == 5 &amp;&amp; y == 625</code>.  However, now suppose that <code>*=</code>, when used with matrices, instead mutated the left hand side.  There would be two problems:</p><ul><li>For general square matrices, <code>A = A*B</code> cannot be implemented without temporary storage: <code>A[1,1]</code> gets computed and stored on the left hand side before you&#39;re done using it on the right hand side.</li><li>Suppose you were willing to allocate a temporary for the computation (which would eliminate most of the point of making <code>*=</code> work in-place); if you took advantage of the mutability of <code>x</code>, then this function would behave differently for mutable vs. immutable inputs. In particular, for immutable <code>x</code>, after the call you&#39;d have (in general) <code>y != x</code>, but for mutable <code>x</code> you&#39;d have <code>y == x</code>.</li></ul><p>Because supporting generic programming is deemed more important than potential performance optimizations that can be achieved by other means (e.g., using broadcasting or explicit loops), operators like <code>+=</code> and <code>*=</code> work by rebinding new values.</p><h2 id="faq-async-io"><a class="docs-heading-anchor" href="#faq-async-io">Asynchronous IO and concurrent synchronous writes</a><a id="faq-async-io-1"></a><a class="docs-heading-anchor-permalink" href="#faq-async-io" title="Permalink"></a></h2><h3 id="Why-do-concurrent-writes-to-the-same-stream-result-in-inter-mixed-output?"><a class="docs-heading-anchor" href="#Why-do-concurrent-writes-to-the-same-stream-result-in-inter-mixed-output?">Why do concurrent writes to the same stream result in inter-mixed output?</a><a id="Why-do-concurrent-writes-to-the-same-stream-result-in-inter-mixed-output?-1"></a><a class="docs-heading-anchor-permalink" href="#Why-do-concurrent-writes-to-the-same-stream-result-in-inter-mixed-output?" title="Permalink"></a></h3><p>While the streaming I/O API is synchronous, the underlying implementation is fully asynchronous.</p><p>Consider the printed output from the following:</p><pre><code class="language-julia-repl hljs">julia&gt; @sync for i in 1:3
           @async write(stdout, string(i), &quot; Foo &quot;, &quot; Bar &quot;)
       end
123 Foo  Foo  Foo  Bar  Bar  Bar</code></pre><p>This is happening because, while the <code>write</code> call is synchronous, the writing of each argument yields to other tasks while waiting for that part of the I/O to complete.</p><p><code>print</code> and <code>println</code> &quot;lock&quot; the stream during a call. Consequently changing <code>write</code> to <code>println</code> in the above example results in:</p><pre><code class="language-julia-repl hljs">julia&gt; @sync for i in 1:3
           @async println(stdout, string(i), &quot; Foo &quot;, &quot; Bar &quot;)
       end
1 Foo  Bar
2 Foo  Bar
3 Foo  Bar</code></pre><p>You can lock your writes with a <code>ReentrantLock</code> like this:</p><pre><code class="language-julia-repl hljs">julia&gt; l = ReentrantLock();

julia&gt; @sync for i in 1:3
           @async begin
               lock(l)
               try
                   write(stdout, string(i), &quot; Foo &quot;, &quot; Bar &quot;)
               finally
                   unlock(l)
               end
           end
       end
1 Foo  Bar 2 Foo  Bar 3 Foo  Bar</code></pre><h2 id="Arrays"><a class="docs-heading-anchor" href="#Arrays">Arrays</a><a id="Arrays-1"></a><a class="docs-heading-anchor-permalink" href="#Arrays" title="Permalink"></a></h2><h3 id="faq-array-0dim"><a class="docs-heading-anchor" href="#faq-array-0dim">What are the differences between zero-dimensional arrays and scalars?</a><a id="faq-array-0dim-1"></a><a class="docs-heading-anchor-permalink" href="#faq-array-0dim" title="Permalink"></a></h3><p>Zero-dimensional arrays are arrays of the form <code>Array{T,0}</code>. They behave similar to scalars, but there are important differences. They deserve a special mention because they are a special case which makes logical sense given the generic definition of arrays, but might be a bit unintuitive at first. The following line defines a zero-dimensional array:</p><pre><code class="nohighlight hljs">julia&gt; A = zeros()
0-dimensional Array{Float64,0}:
0.0</code></pre><p>In this example, <code>A</code> is a mutable container that contains one element, which can be set by <code>A[] = 1.0</code> and retrieved with <code>A[]</code>. All zero-dimensional arrays have the same size (<code>size(A) == ()</code>), and length (<code>length(A) == 1</code>). In particular, zero-dimensional arrays are not empty. If you find this unintuitive, here are some ideas that might help to understand Julia&#39;s definition.</p><ul><li>Zero-dimensional arrays are the &quot;point&quot; to vector&#39;s &quot;line&quot; and matrix&#39;s &quot;plane&quot;. Just as a line has no area (but still represents a set of things), a point has no length or any dimensions at all (but still represents a thing).</li><li>We define <code>prod(())</code> to be 1, and the total number of elements in an array is the product of the size. The size of a zero-dimensional array is <code>()</code>, and therefore its length is <code>1</code>.</li><li>Zero-dimensional arrays don&#39;t natively have any dimensions into which you index – they’re just <code>A[]</code>. We can apply the same &quot;trailing one&quot; rule for them as for all other array dimensionalities, so you can indeed index them as <code>A[1]</code>, <code>A[1,1]</code>, etc; see <a href="arrays.html#Omitted-and-extra-indices">Omitted and extra indices</a>.</li></ul><p>It is also important to understand the differences to ordinary scalars. Scalars are not mutable containers (even though they are iterable and define things like <code>length</code>, <code>getindex</code>, <em>e.g.</em> <code>1[] == 1</code>). In particular, if <code>x = 0.0</code> is defined as a scalar, it is an error to attempt to change its value via <code>x[] = 1.0</code>. A scalar <code>x</code> can be converted into a zero-dimensional array containing it via <code>fill(x)</code>, and conversely, a zero-dimensional array <code>a</code> can be converted to the contained scalar via <code>a[]</code>. Another difference is that a scalar can participate in linear algebra operations such as <code>2 * rand(2,2)</code>, but the analogous operation with a zero-dimensional array <code>fill(2) * rand(2,2)</code> is an error.</p><h3 id="Why-are-my-Julia-benchmarks-for-linear-algebra-operations-different-from-other-languages?"><a class="docs-heading-anchor" href="#Why-are-my-Julia-benchmarks-for-linear-algebra-operations-different-from-other-languages?">Why are my Julia benchmarks for linear algebra operations different from other languages?</a><a id="Why-are-my-Julia-benchmarks-for-linear-algebra-operations-different-from-other-languages?-1"></a><a class="docs-heading-anchor-permalink" href="#Why-are-my-Julia-benchmarks-for-linear-algebra-operations-different-from-other-languages?" title="Permalink"></a></h3><p>You may find that simple benchmarks of linear algebra building blocks like</p><pre><code class="language-julia hljs">using BenchmarkTools
A = randn(1000, 1000)
B = randn(1000, 1000)
@btime $A \ $B
@btime $A * $B</code></pre><p>can be different when compared to other languages like Matlab or R.</p><p>Since operations like this are very thin wrappers over the relevant BLAS functions, the reason for the discrepancy is very likely to be</p><ol><li><p>the BLAS library each language is using,</p></li><li><p>the number of concurrent threads.</p></li></ol><p>Julia compiles and uses its own copy of OpenBLAS, with threads currently capped at <code>8</code> (or the number of your cores).</p><p>Modifying OpenBLAS settings or compiling Julia with a different BLAS library, eg <a href="https://software.intel.com/en-us/mkl">Intel MKL</a>, may provide performance improvements. You can use <a href="https://github.com/JuliaComputing/MKL.jl">MKL.jl</a>, a package that makes Julia&#39;s linear algebra use Intel MKL BLAS and LAPACK instead of OpenBLAS, or search the discussion forum for suggestions on how to set this up manually. Note that Intel MKL cannot be bundled with Julia, as it is not open source.</p><h2 id="Computing-cluster"><a class="docs-heading-anchor" href="#Computing-cluster">Computing cluster</a><a id="Computing-cluster-1"></a><a class="docs-heading-anchor-permalink" href="#Computing-cluster" title="Permalink"></a></h2><h3 id="How-do-I-manage-precompilation-caches-in-distributed-file-systems?"><a class="docs-heading-anchor" href="#How-do-I-manage-precompilation-caches-in-distributed-file-systems?">How do I manage precompilation caches in distributed file systems?</a><a id="How-do-I-manage-precompilation-caches-in-distributed-file-systems?-1"></a><a class="docs-heading-anchor-permalink" href="#How-do-I-manage-precompilation-caches-in-distributed-file-systems?" title="Permalink"></a></h3><p>When using Julia in high-performance computing (HPC) facilities with shared filesystems, it is recommended to use a shared depot (via the <a href="environment-variables.html#JULIA_DEPOT_PATH"><code>JULIA_DEPOT_PATH</code></a> environment variable). Since Julia v1.10, multiple Julia processes on functionally similar workers and using the same depot will coordinate via pidfile locks to only spend effort precompiling on one process while the others wait. The precompilation process will indicate when the process is precompiling or waiting for another that is precompiling. If non-interactive the messages are via <code>@debug</code>.</p><p>However, due to caching of binary code, the cache rejection since v1.9 is more strict and users may need to set the <a href="environment-variables.html#JULIA_CPU_TARGET"><code>JULIA_CPU_TARGET</code></a> environment variable appropriately to get a single cache that is usable throughout the HPC environment.</p><h2 id="Julia-Releases"><a class="docs-heading-anchor" href="#Julia-Releases">Julia Releases</a><a id="Julia-Releases-1"></a><a class="docs-heading-anchor-permalink" href="#Julia-Releases" title="Permalink"></a></h2><h3 id="Do-I-want-to-use-the-Stable,-LTS,-or-nightly-version-of-Julia?"><a class="docs-heading-anchor" href="#Do-I-want-to-use-the-Stable,-LTS,-or-nightly-version-of-Julia?">Do I want to use the Stable, LTS, or nightly version of Julia?</a><a id="Do-I-want-to-use-the-Stable,-LTS,-or-nightly-version-of-Julia?-1"></a><a class="docs-heading-anchor-permalink" href="#Do-I-want-to-use-the-Stable,-LTS,-or-nightly-version-of-Julia?" title="Permalink"></a></h3><p>The Stable version of Julia is the latest released version of Julia, this is the version most people will want to run. It has the latest features, including improved performance. The Stable version of Julia is versioned according to <a href="https://semver.org/">SemVer</a> as v1.x.y. A new minor release of Julia corresponding to a new Stable version is made approximately every 4-5 months after a few weeks of testing as a release candidate. Unlike the LTS version the Stable version will not normally receive bugfixes after another Stable version of Julia has been released. However, upgrading to the next Stable release will always be possible as each release of Julia v1.x will continue to run code written for earlier versions.</p><p>You may prefer the LTS (Long Term Support) version of Julia if you are looking for a very stable code base. The current LTS version of Julia is versioned according to SemVer as v1.6.x; this branch will continue to receive bugfixes until a new LTS branch is chosen, at which point the v1.6.x series will no longer received regular bug fixes and all but the most conservative users will be advised to upgrade to the new LTS version series. As a package developer, you may prefer to develop for the LTS version, to maximize the number of users who can use your package. As per SemVer, code written for v1.0 will continue to work for all future LTS and Stable versions. In general, even if targeting the LTS, one can develop and run code in the latest Stable version, to take advantage of the improved performance; so long as one avoids using new features (such as added library functions or new methods).</p><p>You may prefer the nightly version of Julia if you want to take advantage of the latest updates to the language, and don&#39;t mind if the version available today occasionally doesn&#39;t actually work. As the name implies, releases to the nightly version are made roughly every night (depending on build infrastructure stability). In general nightly released are fairly safe to use—your code will not catch on fire. However, they may be occasional regressions and or issues that will not be found until more thorough pre-release testing. You may wish to test against the nightly version to ensure that such regressions that affect your use case are caught before a release is made.</p><p>Finally, you may also consider building Julia from source for yourself. This option is mainly for those individuals who are comfortable at the command line, or interested in learning. If this describes you, you may also be interested in reading our <a href="https://github.com/JuliaLang/julia/blob/master/CONTRIBUTING.md">guidelines for contributing</a>.</p><p>Links to each of these download types can be found on the download page at <a href="https://julialang.org/downloads/">https://julialang.org/downloads/</a>. Note that not all versions of Julia are available for all platforms.</p><h3 id="How-can-I-transfer-the-list-of-installed-packages-after-updating-my-version-of-Julia?"><a class="docs-heading-anchor" href="#How-can-I-transfer-the-list-of-installed-packages-after-updating-my-version-of-Julia?">How can I transfer the list of installed packages after updating my version of Julia?</a><a id="How-can-I-transfer-the-list-of-installed-packages-after-updating-my-version-of-Julia?-1"></a><a class="docs-heading-anchor-permalink" href="#How-can-I-transfer-the-list-of-installed-packages-after-updating-my-version-of-Julia?" title="Permalink"></a></h3><p>Each minor version of julia has its own default <a href="https://docs.julialang.org/en/v1/manual/code-loading/#Environments-1">environment</a>. As a result, upon installing a new minor version of Julia, the packages you added using the previous minor version will not be available by default. The environment for a given julia version is defined by the files <code>Project.toml</code> and <code>Manifest.toml</code> in a folder matching the version number in <code>.julia/environments/</code>, for instance, <code>.julia/environments/v1.3</code>.</p><p>If you install a new minor version of Julia, say <code>1.4</code>, and want to use in its default environment the same packages as in a previous version (e.g. <code>1.3</code>), you can copy the contents of the file <code>Project.toml</code> from the <code>1.3</code> folder to <code>1.4</code>. Then, in a session of the new Julia version, enter the &quot;package management mode&quot; by typing the key <code>]</code>, and run the command <a href="https://julialang.github.io/Pkg.jl/v1/api/#Pkg.instantiate"><code>instantiate</code></a>.</p><p>This operation will resolve a set of feasible packages from the copied file that are compatible with the target Julia version, and will install or update them if suitable. If you want to reproduce not only the set of packages, but also the versions you were using in the previous Julia version, you should also copy the <code>Manifest.toml</code> file before running the Pkg command <code>instantiate</code>. However, note that packages may define compatibility constraints that may be affected by changing the version of Julia, so the exact set of versions you had in <code>1.3</code> may not work for <code>1.4</code>.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="style-guide.html">« Style Guide</a><a class="docs-footer-nextpage" href="noteworthy-differences.html">Noteworthy Differences from other Languages »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
