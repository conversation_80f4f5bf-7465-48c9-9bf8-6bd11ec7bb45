<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Arrays · The Julia Language</title><meta name="title" content="Arrays · The Julia Language"/><meta property="og:title" content="Arrays · The Julia Language"/><meta property="twitter:title" content="Arrays · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox" checked/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="base.html">Essentials</a></li><li><a class="tocitem" href="collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="math.html">Mathematics</a></li><li><a class="tocitem" href="numbers.html">Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li class="is-active"><a class="tocitem" href="arrays.html">Arrays</a><ul class="internal"><li><a class="tocitem" href="#Constructors-and-Types"><span>Constructors and Types</span></a></li><li><a class="tocitem" href="#Basic-functions"><span>Basic functions</span></a></li><li><a class="tocitem" href="#Broadcast-and-vectorization"><span>Broadcast and vectorization</span></a></li><li><a class="tocitem" href="#Indexing-and-assignment"><span>Indexing and assignment</span></a></li><li><a class="tocitem" href="#Views-(SubArrays-and-other-view-types)"><span>Views (SubArrays and other view types)</span></a></li><li><a class="tocitem" href="#Concatenation-and-permutation"><span>Concatenation and permutation</span></a></li><li><a class="tocitem" href="#Array-functions"><span>Array functions</span></a></li><li><a class="tocitem" href="#Combinatorics"><span>Combinatorics</span></a></li></ul></li><li><a class="tocitem" href="parallel.html">Tasks</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="constants.html">Constants</a></li><li><a class="tocitem" href="file.html">Filesystem</a></li><li><a class="tocitem" href="io-network.html">I/O and Network</a></li><li><a class="tocitem" href="punctuation.html">Punctuation</a></li><li><a class="tocitem" href="sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="c.html">C Interface</a></li><li><a class="tocitem" href="libc.html">C Standard Library</a></li><li><a class="tocitem" href="stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Base</a></li><li class="is-active"><a href="arrays.html">Arrays</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="arrays.html">Arrays</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/base/arrays.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="lib-arrays"><a class="docs-heading-anchor" href="#lib-arrays">Arrays</a><a id="lib-arrays-1"></a><a class="docs-heading-anchor-permalink" href="#lib-arrays" title="Permalink"></a></h1><h2 id="Constructors-and-Types"><a class="docs-heading-anchor" href="#Constructors-and-Types">Constructors and Types</a><a id="Constructors-and-Types-1"></a><a class="docs-heading-anchor-permalink" href="#Constructors-and-Types" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.AbstractArray" href="#Core.AbstractArray"><code>Core.AbstractArray</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AbstractArray{T,N}</code></pre><p>Supertype for <code>N</code>-dimensional arrays (or array-like types) with elements of type <code>T</code>. <a href="arrays.html#Core.Array"><code>Array</code></a> and other types are subtypes of this. See the manual section on the <a href="../manual/interfaces.html#man-interface-array"><code>AbstractArray</code> interface</a>.</p><p>See also: <a href="arrays.html#Base.AbstractVector"><code>AbstractVector</code></a>, <a href="arrays.html#Base.AbstractMatrix"><code>AbstractMatrix</code></a>, <a href="collections.html#Base.eltype"><code>eltype</code></a>, <a href="arrays.html#Base.ndims"><code>ndims</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L5-L13">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.AbstractVector" href="#Base.AbstractVector"><code>Base.AbstractVector</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AbstractVector{T}</code></pre><p>Supertype for one-dimensional arrays (or array-like types) with elements of type <code>T</code>. Alias for <a href="arrays.html#Core.AbstractArray"><code>AbstractArray{T,1}</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L17-L22">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.AbstractMatrix" href="#Base.AbstractMatrix"><code>Base.AbstractMatrix</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AbstractMatrix{T}</code></pre><p>Supertype for two-dimensional arrays (or array-like types) with elements of type <code>T</code>. Alias for <a href="arrays.html#Core.AbstractArray"><code>AbstractArray{T,2}</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L25-L30">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.AbstractVecOrMat" href="#Base.AbstractVecOrMat"><code>Base.AbstractVecOrMat</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AbstractVecOrMat{T}</code></pre><p>Union type of <a href="arrays.html#Base.AbstractVector"><code>AbstractVector{T}</code></a> and <a href="arrays.html#Base.AbstractMatrix"><code>AbstractMatrix{T}</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L33-L37">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Array" href="#Core.Array"><code>Core.Array</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Array{T,N} &lt;: AbstractArray{T,N}</code></pre><p><code>N</code>-dimensional dense array with elements of type <code>T</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L45-L49">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Array-Tuple{UndefInitializer, Any}" href="#Core.Array-Tuple{UndefInitializer, Any}"><code>Core.Array</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Array{T}(undef, dims)
Array{T,N}(undef, dims)</code></pre><p>Construct an uninitialized <code>N</code>-dimensional <a href="arrays.html#Core.Array"><code>Array</code></a> containing elements of type <code>T</code>. <code>N</code> can either be supplied explicitly, as in <code>Array{T,N}(undef, dims)</code>, or be determined by the length or number of <code>dims</code>. <code>dims</code> may be a tuple or a series of integer arguments corresponding to the lengths in each dimension. If the rank <code>N</code> is supplied explicitly, then it must match the length or number of <code>dims</code>. Here <a href="arrays.html#Core.undef"><code>undef</code></a> is the <a href="arrays.html#Core.UndefInitializer"><code>UndefInitializer</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = Array{Float64, 2}(undef, 2, 3) # N given explicitly
2×3 Matrix{Float64}:
 6.90198e-310  6.90198e-310  6.90198e-310
 6.90198e-310  6.90198e-310  0.0

julia&gt; B = Array{Float64}(undef, 4) # N determined by the input
4-element Vector{Float64}:
   2.360075077e-314
 NaN
   2.2671131793e-314
   2.299821756e-314

julia&gt; similar(B, 2, 4, 1) # use typeof(B), and the given size
2×4×1 Array{Float64, 3}:
[:, :, 1] =
 2.26703e-314  2.26708e-314  0.0           2.80997e-314
 0.0           2.26703e-314  2.26708e-314  0.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2821-L2853">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Array-Tuple{Nothing, Any}" href="#Core.Array-Tuple{Nothing, Any}"><code>Core.Array</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Array{T}(nothing, dims)
Array{T,N}(nothing, dims)</code></pre><p>Construct an <code>N</code>-dimensional <a href="arrays.html#Core.Array"><code>Array</code></a> containing elements of type <code>T</code>, initialized with <a href="constants.html#Core.nothing"><code>nothing</code></a> entries. Element type <code>T</code> must be able to hold these values, i.e. <code>Nothing &lt;: T</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Array{Union{Nothing, String}}(nothing, 2)
2-element Vector{Union{Nothing, String}}:
 nothing
 nothing

julia&gt; Array{Union{Nothing, Int}}(nothing, 2, 3)
2×3 Matrix{Union{Nothing, Int64}}:
 nothing  nothing  nothing
 nothing  nothing  nothing</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2856-L2876">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Array-Tuple{Missing, Any}" href="#Core.Array-Tuple{Missing, Any}"><code>Core.Array</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Array{T}(missing, dims)
Array{T,N}(missing, dims)</code></pre><p>Construct an <code>N</code>-dimensional <a href="arrays.html#Core.Array"><code>Array</code></a> containing elements of type <code>T</code>, initialized with <a href="../manual/missing.html#missing"><code>missing</code></a> entries. Element type <code>T</code> must be able to hold these values, i.e. <code>Missing &lt;: T</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Array{Union{Missing, String}}(missing, 2)
2-element Vector{Union{Missing, String}}:
 missing
 missing

julia&gt; Array{Union{Missing, Int}}(missing, 2, 3)
2×3 Matrix{Union{Missing, Int64}}:
 missing  missing  missing
 missing  missing  missing</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2880-L2900">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.UndefInitializer" href="#Core.UndefInitializer"><code>Core.UndefInitializer</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">UndefInitializer</code></pre><p>Singleton type used in array initialization, indicating the array-constructor-caller would like an uninitialized array. See also <a href="arrays.html#Core.undef"><code>undef</code></a>, an alias for <code>UndefInitializer()</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Array{Float64, 1}(UndefInitializer(), 3)
3-element Array{Float64, 1}:
 2.2752528595e-314
 2.202942107e-314
 2.275252907e-314</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2903-L2918">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.undef" href="#Core.undef"><code>Core.undef</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">undef</code></pre><p>Alias for <code>UndefInitializer()</code>, which constructs an instance of the singleton type <a href="arrays.html#Core.UndefInitializer"><code>UndefInitializer</code></a>, used in array initialization to indicate the array-constructor-caller would like an uninitialized array.</p><p>See also: <a href="../manual/missing.html#missing"><code>missing</code></a>, <a href="arrays.html#Base.similar"><code>similar</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Array{Float64, 1}(undef, 3)
3-element Vector{Float64}:
 2.2752528595e-314
 2.202942107e-314
 2.275252907e-314</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2921-L2938">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Vector" href="#Base.Vector"><code>Base.Vector</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Vector{T} &lt;: AbstractVector{T}</code></pre><p>One-dimensional dense array with elements of type <code>T</code>, often used to represent a mathematical vector. Alias for <a href="arrays.html#Core.Array"><code>Array{T,1}</code></a>.</p><p>See also <a href="arrays.html#Base.empty"><code>empty</code></a>, <a href="arrays.html#Base.similar"><code>similar</code></a> and <a href="numbers.html#Base.zero"><code>zero</code></a> for creating vectors.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L52-L59">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Vector-Tuple{UndefInitializer, Any}" href="#Base.Vector-Tuple{UndefInitializer, Any}"><code>Base.Vector</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Vector{T}(undef, n)</code></pre><p>Construct an uninitialized <a href="arrays.html#Base.Vector"><code>Vector{T}</code></a> of length <code>n</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Vector{Float64}(undef, 3)
3-element Array{Float64, 1}:
 6.90966e-310
 6.90966e-310
 6.90966e-310</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2717-L2730">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Vector-Tuple{Nothing, Any}" href="#Base.Vector-Tuple{Nothing, Any}"><code>Base.Vector</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Vector{T}(nothing, m)</code></pre><p>Construct a <a href="arrays.html#Base.Vector"><code>Vector{T}</code></a> of length <code>m</code>, initialized with <a href="constants.html#Core.nothing"><code>nothing</code></a> entries. Element type <code>T</code> must be able to hold these values, i.e. <code>Nothing &lt;: T</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Vector{Union{Nothing, String}}(nothing, 2)
2-element Vector{Union{Nothing, String}}:
 nothing
 nothing</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2733-L2747">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Vector-Tuple{Missing, Any}" href="#Base.Vector-Tuple{Missing, Any}"><code>Base.Vector</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Vector{T}(missing, m)</code></pre><p>Construct a <a href="arrays.html#Base.Vector"><code>Vector{T}</code></a> of length <code>m</code>, initialized with <a href="../manual/missing.html#missing"><code>missing</code></a> entries. Element type <code>T</code> must be able to hold these values, i.e. <code>Missing &lt;: T</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Vector{Union{Missing, String}}(missing, 2)
2-element Vector{Union{Missing, String}}:
 missing
 missing</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2750-L2764">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Matrix" href="#Base.Matrix"><code>Base.Matrix</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Matrix{T} &lt;: AbstractMatrix{T}</code></pre><p>Two-dimensional dense array with elements of type <code>T</code>, often used to represent a mathematical matrix. Alias for <a href="arrays.html#Core.Array"><code>Array{T,2}</code></a>.</p><p>See also <a href="arrays.html#Base.fill"><code>fill</code></a>, <a href="arrays.html#Base.zeros"><code>zeros</code></a>, <a href="arrays.html#Core.undef"><code>undef</code></a> and <a href="arrays.html#Base.similar"><code>similar</code></a> for creating matrices.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L62-L70">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Matrix-Tuple{UndefInitializer, Any, Any}" href="#Base.Matrix-Tuple{UndefInitializer, Any, Any}"><code>Base.Matrix</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Matrix{T}(undef, m, n)</code></pre><p>Construct an uninitialized <a href="arrays.html#Base.Matrix"><code>Matrix{T}</code></a> of size <code>m</code>×<code>n</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Matrix{Float64}(undef, 2, 3)
2×3 Array{Float64, 2}:
 2.36365e-314  2.28473e-314    5.0e-324
 2.26704e-314  2.26711e-314  NaN

julia&gt; similar(ans, Int32, 2, 2)
2×2 Matrix{Int32}:
 490537216  1277177453
         1  1936748399</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2767-L2784">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Matrix-Tuple{Nothing, Any, Any}" href="#Base.Matrix-Tuple{Nothing, Any, Any}"><code>Base.Matrix</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Matrix{T}(nothing, m, n)</code></pre><p>Construct a <a href="arrays.html#Base.Matrix"><code>Matrix{T}</code></a> of size <code>m</code>×<code>n</code>, initialized with <a href="constants.html#Core.nothing"><code>nothing</code></a> entries. Element type <code>T</code> must be able to hold these values, i.e. <code>Nothing &lt;: T</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Matrix{Union{Nothing, String}}(nothing, 2, 3)
2×3 Matrix{Union{Nothing, String}}:
 nothing  nothing  nothing
 nothing  nothing  nothing</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2787-L2801">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Matrix-Tuple{Missing, Any, Any}" href="#Base.Matrix-Tuple{Missing, Any, Any}"><code>Base.Matrix</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Matrix{T}(missing, m, n)</code></pre><p>Construct a <a href="arrays.html#Base.Matrix"><code>Matrix{T}</code></a> of size <code>m</code>×<code>n</code>, initialized with <a href="../manual/missing.html#missing"><code>missing</code></a> entries. Element type <code>T</code> must be able to hold these values, i.e. <code>Missing &lt;: T</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Matrix{Union{Missing, String}}(missing, 2, 3)
2×3 Matrix{Union{Missing, String}}:
 missing  missing  missing
 missing  missing  missing</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2804-L2818">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.VecOrMat" href="#Base.VecOrMat"><code>Base.VecOrMat</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">VecOrMat{T}</code></pre><p>Union type of <a href="arrays.html#Base.Vector"><code>Vector{T}</code></a> and <a href="arrays.html#Base.Matrix"><code>Matrix{T}</code></a> which allows functions to accept either a Matrix or a Vector.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Vector{Float64} &lt;: VecOrMat{Float64}
true

julia&gt; Matrix{Float64} &lt;: VecOrMat{Float64}
true

julia&gt; Array{Float64, 3} &lt;: VecOrMat{Float64}
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L73-L89">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.DenseArray" href="#Core.DenseArray"><code>Core.DenseArray</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">DenseArray{T, N} &lt;: AbstractArray{T,N}</code></pre><p><code>N</code>-dimensional dense array with elements of type <code>T</code>. The elements of a dense array are stored contiguously in memory.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L92-L97">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.DenseVector" href="#Base.DenseVector"><code>Base.DenseVector</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">DenseVector{T}</code></pre><p>One-dimensional <a href="arrays.html#Core.DenseArray"><code>DenseArray</code></a> with elements of type <code>T</code>. Alias for <code>DenseArray{T,1}</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L100-L104">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.DenseMatrix" href="#Base.DenseMatrix"><code>Base.DenseMatrix</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">DenseMatrix{T}</code></pre><p>Two-dimensional <a href="arrays.html#Core.DenseArray"><code>DenseArray</code></a> with elements of type <code>T</code>. Alias for <code>DenseArray{T,2}</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L107-L111">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.DenseVecOrMat" href="#Base.DenseVecOrMat"><code>Base.DenseVecOrMat</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">DenseVecOrMat{T}</code></pre><p>Union type of <a href="arrays.html#Base.DenseVector"><code>DenseVector{T}</code></a> and <a href="arrays.html#Base.DenseMatrix"><code>DenseMatrix{T}</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L114-L118">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.StridedArray" href="#Base.StridedArray"><code>Base.StridedArray</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">StridedArray{T, N}</code></pre><p>A hard-coded <a href="base.html#Core.Union"><code>Union</code></a> of common array types that follow the <a href="../manual/interfaces.html#man-interface-strided-arrays">strided array interface</a>, with elements of type <code>T</code> and <code>N</code> dimensions.</p><p>If <code>A</code> is a <code>StridedArray</code>, then its elements are stored in memory with offsets, which may vary between dimensions but are constant within a dimension. For example, <code>A</code> could have stride 2 in dimension 1, and stride 3 in dimension 2. Incrementing <code>A</code> along dimension <code>d</code> jumps in memory by [<code>stride(A, d)</code>] slots. Strided arrays are particularly important and useful because they can sometimes be passed directly as pointers to foreign language libraries like BLAS.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L3489-L3501">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.StridedVector" href="#Base.StridedVector"><code>Base.StridedVector</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">StridedVector{T}</code></pre><p>One dimensional <a href="arrays.html#Base.StridedArray"><code>StridedArray</code></a> with elements of type <code>T</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L3504-L3508">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.StridedMatrix" href="#Base.StridedMatrix"><code>Base.StridedMatrix</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">StridedMatrix{T}</code></pre><p>Two dimensional <a href="arrays.html#Base.StridedArray"><code>StridedArray</code></a> with elements of type <code>T</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L3511-L3515">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.StridedVecOrMat" href="#Base.StridedVecOrMat"><code>Base.StridedVecOrMat</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">StridedVecOrMat{T}</code></pre><p>Union type of <a href="arrays.html#Base.StridedVector"><code>StridedVector</code></a> and <a href="arrays.html#Base.StridedMatrix"><code>StridedMatrix</code></a> with elements of type <code>T</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L3518-L3522">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.GenericMemory" href="#Core.GenericMemory"><code>Core.GenericMemory</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">GenericMemory{kind::Symbol, T, addrspace=Core.CPU} &lt;: DenseVector{T}</code></pre><p>Fixed-size <a href="arrays.html#Base.DenseVector"><code>DenseVector{T}</code></a>.</p><p><code>kind</code> can currently be either <code>:not_atomic</code> or <code>:atomic</code>. For details on what <code>:atomic</code> implies, see <a href="multi-threading.html#Core.AtomicMemory"><code>AtomicMemory</code></a></p><p><code>addrspace</code> can currently only be set to Core.CPU. It is designed to  to permit extension by other systems such as GPUs, which might define values such as:</p><pre><code class="nohighlight hljs">module CUDA
const Generic = bitcast(Core.AddrSpace{CUDA}, 0)
const Global = bitcast(Core.AddrSpace{CUDA}, 1)
end</code></pre><p>The exact semantics of these other addrspaces is defined by the specific backend, but will error if the user is attempting to access these on the CPU.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>This type requires Julia 1.11 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/genericmemory.jl#L5-L24">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Memory" href="#Core.Memory"><code>Core.Memory</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Memory{T} == GenericMemory{:not_atomic, T, Core.CPU}</code></pre><p>Fixed-size <a href="arrays.html#Base.DenseVector"><code>DenseVector{T}</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>This type requires Julia 1.11 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/genericmemory.jl#L27-L34">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.memoryref" href="#Core.memoryref"><code>Core.memoryref</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">`memoryref(::GenericMemory)`</code></pre><p>Construct a <code>GenericMemoryRef</code> from a memory object. This does not fail, but the resulting memory will point out-of-bounds if and only if the memory is empty.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2698-L2703">source</a></section><section><div><pre><code class="language-julia hljs">memoryref(::GenericMemory, index::Integer)
memoryref(::GenericMemoryRef, index::Integer)</code></pre><p>Construct a <code>GenericMemoryRef</code> from a memory object and an offset index (1-based) which can also be negative. This always returns an inbounds object, and will throw an error if that is not possible (because the index would result in a shift out-of-bounds of the underlying memory).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2706-L2714">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Slices" href="#Base.Slices"><code>Base.Slices</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Slices{P,SM,AX,S,N} &lt;: AbstractSlices{S,N}</code></pre><p>An <code>AbstractArray</code> of slices into a parent array over specified dimension(s), returning views that select all the data from the other dimension(s).</p><p>These should typically be constructed by <a href="arrays.html#Base.eachslice"><code>eachslice</code></a>, <a href="arrays.html#Base.eachcol"><code>eachcol</code></a> or <a href="arrays.html#Base.eachrow"><code>eachrow</code></a>.</p><p><a href="arrays.html#Base.parent"><code>parent(s::Slices)</code></a> will return the parent array.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/slicearray.jl#L11-L21">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.RowSlices" href="#Base.RowSlices"><code>Base.RowSlices</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">RowSlices{M,AX,S}</code></pre><p>A special case of <a href="arrays.html#Base.Slices"><code>Slices</code></a> that is a vector of row slices of a matrix, as constructed by <a href="arrays.html#Base.eachrow"><code>eachrow</code></a>.</p><p><a href="arrays.html#Base.parent"><code>parent</code></a> can be used to get the underlying matrix.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/slicearray.jl#L207-L214">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.ColumnSlices" href="#Base.ColumnSlices"><code>Base.ColumnSlices</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ColumnSlices{M,AX,S}</code></pre><p>A special case of <a href="arrays.html#Base.Slices"><code>Slices</code></a> that is a vector of column slices of a matrix, as constructed by <a href="arrays.html#Base.eachcol"><code>eachcol</code></a>.</p><p><a href="arrays.html#Base.parent"><code>parent</code></a> can be used to get the underlying matrix.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/slicearray.jl#L217-L224">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.getindex-Tuple{Type, Vararg{Any}}" href="#Base.getindex-Tuple{Type, Vararg{Any}}"><code>Base.getindex</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">getindex(type[, elements...])</code></pre><p>Construct a 1-d array of the specified type. This is usually called with the syntax <code>Type[]</code>. Element values can be specified using <code>Type[a,b,c,...]</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Int8[1, 2, 3]
3-element Vector{Int8}:
 1
 2
 3

julia&gt; getindex(Int8, 1, 2, 3)
3-element Vector{Int8}:
 1
 2
 3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L376-L396">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.zeros" href="#Base.zeros"><code>Base.zeros</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">zeros([T=Float64,] dims::Tuple)
zeros([T=Float64,] dims...)</code></pre><p>Create an <code>Array</code>, with element type <code>T</code>, of all zeros with size specified by <code>dims</code>. See also <a href="arrays.html#Base.fill"><code>fill</code></a>, <a href="arrays.html#Base.ones"><code>ones</code></a>, <a href="numbers.html#Base.zero"><code>zero</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; zeros(1)
1-element Vector{Float64}:
 0.0

julia&gt; zeros(Int8, 2, 3)
2×3 Matrix{Int8}:
 0  0  0
 0  0  0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L540-L558">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.ones" href="#Base.ones"><code>Base.ones</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ones([T=Float64,] dims::Tuple)
ones([T=Float64,] dims...)</code></pre><p>Create an <code>Array</code>, with element type <code>T</code>, of all ones with size specified by <code>dims</code>. See also <a href="arrays.html#Base.fill"><code>fill</code></a>, <a href="arrays.html#Base.zeros"><code>zeros</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; ones(1,2)
1×2 Matrix{Float64}:
 1.0  1.0

julia&gt; ones(ComplexF64, 2, 3)
2×3 Matrix{ComplexF64}:
 1.0+0.0im  1.0+0.0im  1.0+0.0im
 1.0+0.0im  1.0+0.0im  1.0+0.0im</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L561-L579">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.BitArray" href="#Base.BitArray"><code>Base.BitArray</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">BitArray{N} &lt;: AbstractArray{Bool, N}</code></pre><p>Space-efficient <code>N</code>-dimensional boolean array, using just one bit for each boolean value.</p><p><code>BitArray</code>s pack up to 64 values into every 8 bytes, resulting in an 8x space efficiency over <code>Array{Bool, N}</code> and allowing some operations to work on 64 values at once.</p><p>By default, Julia returns <code>BitArrays</code> from <a href="../manual/arrays.html#Broadcasting">broadcasting</a> operations that generate boolean elements (including dotted-comparisons like <code>.==</code>) as well as from the functions <a href="arrays.html#Base.trues"><code>trues</code></a> and <a href="arrays.html#Base.falses"><code>falses</code></a>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Due to its packed storage format, concurrent access to the elements of a <code>BitArray</code> where at least one of them is a write is not thread-safe.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/bitarray.jl#L7-L23">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.BitArray-Tuple{UndefInitializer, Vararg{Integer}}" href="#Base.BitArray-Tuple{UndefInitializer, Vararg{Integer}}"><code>Base.BitArray</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">BitArray(undef, dims::Integer...)
BitArray{N}(undef, dims::NTuple{N,Int})</code></pre><p>Construct an undef <a href="arrays.html#Base.BitArray"><code>BitArray</code></a> with the given dimensions. Behaves identically to the <a href="arrays.html#Core.Array"><code>Array</code></a> constructor. See <a href="arrays.html#Core.undef"><code>undef</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; BitArray(undef, 2, 2)
2×2 BitMatrix:
 0  0
 0  0

julia&gt; BitArray(undef, (3, 1))
3×1 BitMatrix:
 0
 0
 0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/bitarray.jl#L48-L68">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.BitArray-Tuple{Any}" href="#Base.BitArray-Tuple{Any}"><code>Base.BitArray</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">BitArray(itr)</code></pre><p>Construct a <a href="arrays.html#Base.BitArray"><code>BitArray</code></a> generated by the given iterable object. The shape is inferred from the <code>itr</code> object.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; BitArray([1 0; 0 1])
2×2 BitMatrix:
 1  0
 0  1

julia&gt; BitArray(x+y == 3 for x = 1:2, y = 1:3)
2×3 BitMatrix:
 0  1  0
 1  0  0

julia&gt; BitArray(x+y == 3 for x = 1:2 for y = 1:3)
6-element BitVector:
 0
 1
 0
 1
 0
 0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/bitarray.jl#L549-L576">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.trues" href="#Base.trues"><code>Base.trues</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">trues(dims)</code></pre><p>Create a <code>BitArray</code> with all values set to <code>true</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; trues(2,3)
2×3 BitMatrix:
 1  1  1
 1  1  1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/bitarray.jl#L408-L420">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.falses" href="#Base.falses"><code>Base.falses</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">falses(dims)</code></pre><p>Create a <code>BitArray</code> with all values set to <code>false</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; falses(2,3)
2×3 BitMatrix:
 0  0  0
 0  0  0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/bitarray.jl#L390-L402">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.fill" href="#Base.fill"><code>Base.fill</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">fill(value, dims::Tuple)
fill(value, dims...)</code></pre><p>Create an array of size <code>dims</code> with every location set to <code>value</code>.</p><p>For example, <code>fill(1.0, (5,5))</code> returns a 5×5 array of floats, with <code>1.0</code> in every location of the array.</p><p>The dimension lengths <code>dims</code> may be specified as either a tuple or a sequence of arguments. An <code>N</code>-length tuple or <code>N</code> arguments following the <code>value</code> specify an <code>N</code>-dimensional array. Thus, a common idiom for creating a zero-dimensional array with its only location set to <code>x</code> is <code>fill(x)</code>.</p><p>Every location of the returned array is set to (and is thus <a href="base.html#Core.:==="><code>===</code></a> to) the <code>value</code> that was passed; this means that if the <code>value</code> is itself modified, all elements of the <code>fill</code>ed array will reflect that modification because they&#39;re <em>still</em> that very <code>value</code>. This is of no concern with <code>fill(1.0, (5,5))</code> as the <code>value</code> <code>1.0</code> is immutable and cannot itself be modified, but can be unexpected with mutable values like — most commonly — arrays.  For example, <code>fill([], 3)</code> places <em>the very same</em> empty array in all three locations of the returned vector:</p><pre><code class="language-julia-repl hljs">julia&gt; v = fill([], 3)
3-element Vector{Vector{Any}}:
 []
 []
 []

julia&gt; v[1] === v[2] === v[3]
true

julia&gt; value = v[1]
Any[]

julia&gt; push!(value, 867_5309)
1-element Vector{Any}:
 8675309

julia&gt; v
3-element Vector{Vector{Any}}:
 [8675309]
 [8675309]
 [8675309]</code></pre><p>To create an array of many independent inner arrays, use a <a href="../manual/arrays.html#man-comprehensions">comprehension</a> instead. This creates a new and distinct array on each iteration of the loop:</p><pre><code class="language-julia-repl hljs">julia&gt; v2 = [[] for _ in 1:3]
3-element Vector{Vector{Any}}:
 []
 []
 []

julia&gt; v2[1] === v2[2] === v2[3]
false

julia&gt; push!(v2[1], 8675309)
1-element Vector{Any}:
 8675309

julia&gt; v2
3-element Vector{Vector{Any}}:
 [8675309]
 []
 []</code></pre><p>See also: <a href="arrays.html#Base.fill!"><code>fill!</code></a>, <a href="arrays.html#Base.zeros"><code>zeros</code></a>, <a href="arrays.html#Base.ones"><code>ones</code></a>, <a href="arrays.html#Base.similar"><code>similar</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; fill(1.0, (2,3))
2×3 Matrix{Float64}:
 1.0  1.0  1.0
 1.0  1.0  1.0

julia&gt; fill(42)
0-dimensional Array{Int64, 0}:
42

julia&gt; A = fill(zeros(2), 2) # sets both elements to the same [0.0, 0.0] vector
2-element Vector{Vector{Float64}}:
 [0.0, 0.0]
 [0.0, 0.0]

julia&gt; A[1][1] = 42; # modifies the filled value to be [42.0, 0.0]

julia&gt; A # both A[1] and A[2] are the very same vector
2-element Vector{Vector{Float64}}:
 [42.0, 0.0]
 [42.0, 0.0]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L437-L532">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.fill!" href="#Base.fill!"><code>Base.fill!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">fill!(A, x)</code></pre><p>Fill array <code>A</code> with the value <code>x</code>. If <code>x</code> is an object reference, all elements will refer to the same object. <code>fill!(A, Foo())</code> will return <code>A</code> filled with the result of evaluating <code>Foo()</code> once.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = zeros(2,3)
2×3 Matrix{Float64}:
 0.0  0.0  0.0
 0.0  0.0  0.0

julia&gt; fill!(A, 2.)
2×3 Matrix{Float64}:
 2.0  2.0  2.0
 2.0  2.0  2.0

julia&gt; a = [1, 1, 1]; A = fill!(Vector{Vector{Int}}(undef, 3), a); a[1] = 2; A
3-element Vector{Vector{Int64}}:
 [2, 1, 1]
 [2, 1, 1]
 [2, 1, 1]

julia&gt; x = 0; f() = (global x += 1; x); fill!(Vector{Int}(undef, 3), f())
3-element Vector{Int64}:
 1
 1
 1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multidimensional.jl#L1109-L1140">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.empty" href="#Base.empty"><code>Base.empty</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">empty(x::Tuple)</code></pre><p>Return an empty tuple, <code>()</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/tuple.jl#L685-L689">source</a></section><section><div><pre><code class="language-julia hljs">empty(v::AbstractVector, [eltype])</code></pre><p>Create an empty vector similar to <code>v</code>, optionally changing the <code>eltype</code>.</p><p>See also: <a href="collections.html#Base.empty!"><code>empty!</code></a>, <a href="collections.html#Base.isempty"><code>isempty</code></a>, <a href="arrays.html#Base.isassigned"><code>isassigned</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; empty([1.0, 2.0, 3.0])
Float64[]

julia&gt; empty([1.0, 2.0, 3.0], String)
String[]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L870-L886">source</a></section><section><div><pre><code class="language-julia hljs">empty(a::AbstractDict, [index_type=keytype(a)], [value_type=valtype(a)])</code></pre><p>Create an empty <code>AbstractDict</code> container which can accept indices of type <code>index_type</code> and values of type <code>value_type</code>. The second and third arguments are optional and default to the input&#39;s <code>keytype</code> and <code>valtype</code>, respectively. (If only one of the two types is specified, it is assumed to be the <code>value_type</code>, and the <code>index_type</code> we default to <code>keytype(a)</code>).</p><p>Custom <code>AbstractDict</code> subtypes may choose which specific dictionary type is best suited to return for the given index and value types, by specializing on the three-argument signature. The default is to return an empty <code>Dict</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractdict.jl#L182-L193">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.similar" href="#Base.similar"><code>Base.similar</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">similar(A::AbstractSparseMatrixCSC{Tv,Ti}, [::Type{TvNew}, ::Type{TiNew}, m::Integer, n::Integer]) where {Tv,Ti}</code></pre><p>Create an uninitialized mutable array with the given element type, index type, and size, based upon the given source <code>SparseMatrixCSC</code>. The new sparse matrix maintains the structure of the original sparse matrix, except in the case where dimensions of the output matrix are different from the output.</p><p>The output matrix has zeros in the same locations as the input, but uninitialized values for the nonzero locations.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L708-L719">source</a></section><section><div><pre><code class="language-julia hljs">similar(array, [element_type=eltype(array)], [dims=size(array)])</code></pre><p>Create an uninitialized mutable array with the given element type and size, based upon the given source array. The second and third arguments are both optional, defaulting to the given array&#39;s <code>eltype</code> and <code>size</code>. The dimensions may be specified either as a single tuple argument or as a series of integer arguments.</p><p>Custom AbstractArray subtypes may choose which specific array type is best-suited to return for the given element type and dimensionality. If they do not specialize this method, the default is an <code>Array{element_type}(undef, dims...)</code>.</p><p>For example, <code>similar(1:10, 1, 4)</code> returns an uninitialized <code>Array{Int,2}</code> since ranges are neither mutable nor support 2 dimensions:</p><pre><code class="language-julia-repl hljs">julia&gt; similar(1:10, 1, 4)
1×4 Matrix{Int64}:
 4419743872  4374413872  4419743888  0</code></pre><p>Conversely, <code>similar(trues(10,10), 2)</code> returns an uninitialized <code>BitVector</code> with two elements since <code>BitArray</code>s are both mutable and can support 1-dimensional arrays:</p><pre><code class="language-julia-repl hljs">julia&gt; similar(trues(10,10), 2)
2-element BitVector:
 0
 0</code></pre><p>Since <code>BitArray</code>s can only store elements of type <a href="numbers.html#Core.Bool"><code>Bool</code></a>, however, if you request a different element type it will create a regular <code>Array</code> instead:</p><pre><code class="language-julia-repl hljs">julia&gt; similar(falses(10), Float64, 2, 4)
2×4 Matrix{Float64}:
 2.18425e-314  2.18425e-314  2.18425e-314  2.18425e-314
 2.18425e-314  2.18425e-314  2.18425e-314  2.18425e-314</code></pre><p>See also: <a href="arrays.html#Core.undef"><code>undef</code></a>, <a href="arrays.html#Base.isassigned"><code>isassigned</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L777-L819">source</a></section><section><div><pre><code class="language-julia hljs">similar(storagetype, axes)</code></pre><p>Create an uninitialized mutable array analogous to that specified by <code>storagetype</code>, but with <code>axes</code> specified by the last argument.</p><p><strong>Examples</strong>:</p><pre><code class="nohighlight hljs">similar(Array{Int}, axes(A))</code></pre><p>creates an array that &quot;acts like&quot; an <code>Array{Int}</code> (and might indeed be backed by one), but which is indexed identically to <code>A</code>. If <code>A</code> has conventional indexing, this will be identical to <code>Array{Int}(undef, size(A))</code>, but if <code>A</code> has unconventional indexing then the indices of the result will match <code>A</code>.</p><pre><code class="nohighlight hljs">similar(BitArray, (axes(A, 2),))</code></pre><p>would create a 1-dimensional logical array whose indices match those of the columns of <code>A</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L844-L865">source</a></section></article><h2 id="Basic-functions"><a class="docs-heading-anchor" href="#Basic-functions">Basic functions</a><a id="Basic-functions-1"></a><a class="docs-heading-anchor-permalink" href="#Basic-functions" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.ndims" href="#Base.ndims"><code>Base.ndims</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ndims(A::AbstractArray) -&gt; Integer</code></pre><p>Return the number of dimensions of <code>A</code>.</p><p>See also: <a href="arrays.html#Base.size"><code>size</code></a>, <a href="arrays.html#Base.axes-Tuple{Any}"><code>axes</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = fill(1, (3,4,5));

julia&gt; ndims(A)
3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L259-L273">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.size" href="#Base.size"><code>Base.size</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">size(A::AbstractArray, [dim])</code></pre><p>Return a tuple containing the dimensions of <code>A</code>. Optionally you can specify a dimension to just get the length of that dimension.</p><p>Note that <code>size</code> may not be defined for arrays with non-standard indices, in which case <a href="arrays.html#Base.axes-Tuple{Any}"><code>axes</code></a> may be useful. See the manual chapter on <a href="../devdocs/offset-arrays.html#man-custom-indices">arrays with custom indices</a>.</p><p>See also: <a href="collections.html#Base.length"><code>length</code></a>, <a href="arrays.html#Base.ndims"><code>ndims</code></a>, <a href="arrays.html#Base.eachindex"><code>eachindex</code></a>, <a href="base.html#Base.sizeof-Tuple{Type}"><code>sizeof</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = fill(1, (2,3,4));

julia&gt; size(A)
(2, 3, 4)

julia&gt; size(A, 2)
3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L20-L41">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.axes-Tuple{Any}" href="#Base.axes-Tuple{Any}"><code>Base.axes</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">axes(A)</code></pre><p>Return the tuple of valid indices for array <code>A</code>.</p><p>See also: <a href="arrays.html#Base.size"><code>size</code></a>, <a href="collections.html#Base.keys"><code>keys</code></a>, <a href="arrays.html#Base.eachindex"><code>eachindex</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = fill(1, (5,6,7));

julia&gt; axes(A)
(Base.OneTo(5), Base.OneTo(6), Base.OneTo(7))</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L80-L95">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.axes-Tuple{AbstractArray, Any}" href="#Base.axes-Tuple{AbstractArray, Any}"><code>Base.axes</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">axes(A, d)</code></pre><p>Return the valid range of indices for array <code>A</code> along dimension <code>d</code>.</p><p>See also <a href="arrays.html#Base.size"><code>size</code></a>, and the manual chapter on <a href="../devdocs/offset-arrays.html#man-custom-indices">arrays with custom indices</a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = fill(1, (5,6,7));

julia&gt; axes(A, 2)
Base.OneTo(6)

julia&gt; axes(A, 4) == 1:1  # all dimensions d &gt; ndims(A) have size 1
true</code></pre><p><strong>Usage note</strong></p><p>Each of the indices has to be an <code>AbstractUnitRange{&lt;:Integer}</code>, but at the same time can be a type that uses custom indices. So, for example, if you need a subset, use generalized indexing constructs like <code>begin</code>/<code>end</code> or <a href="collections.html#Base.firstindex"><code>firstindex</code></a>/<a href="collections.html#Base.lastindex"><code>lastindex</code></a>:</p><pre><code class="language-julia hljs">ix = axes(v, 1)
ix[2:end]          # will work for eg Vector, but may fail in general
ix[(begin+1):end]  # works for generalized indexes</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L44-L74">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.length-Tuple{AbstractArray}" href="#Base.length-Tuple{AbstractArray}"><code>Base.length</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">length(A::AbstractArray)</code></pre><p>Return the number of elements in the array, defaults to <code>prod(size(A))</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; length([1, 2, 3, 4])
4

julia&gt; length([1 2; 3 4])
4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L301-L314">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.keys-Tuple{AbstractArray}" href="#Base.keys-Tuple{AbstractArray}"><code>Base.keys</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">keys(a::AbstractArray)</code></pre><p>Return an efficient array describing all valid indices for <code>a</code> arranged in the shape of <code>a</code> itself.</p><p>The keys of 1-dimensional arrays (vectors) are integers, whereas all other N-dimensional arrays use <a href="arrays.html#Base.IteratorsMD.CartesianIndex"><code>CartesianIndex</code></a> to describe their locations.  Often the special array types <a href="arrays.html#Base.LinearIndices"><code>LinearIndices</code></a> and <a href="arrays.html#Base.IteratorsMD.CartesianIndices"><code>CartesianIndices</code></a> are used to efficiently represent these arrays of integers and <code>CartesianIndex</code>es, respectively.</p><p>Note that the <code>keys</code> of an array might not be the most efficient index type; for maximum performance use  <a href="arrays.html#Base.eachindex"><code>eachindex</code></a> instead.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; keys([4, 5, 6])
3-element LinearIndices{1, Tuple{Base.OneTo{Int64}}}:
 1
 2
 3

julia&gt; keys([4 5; 6 7])
CartesianIndices((2, 2))</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L140-L164">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.eachindex" href="#Base.eachindex"><code>Base.eachindex</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">eachindex(A...)
eachindex(::IndexStyle, A::AbstractArray...)</code></pre><p>Create an iterable object for visiting each index of an <code>AbstractArray</code> <code>A</code> in an efficient manner. For array types that have opted into fast linear indexing (like <code>Array</code>), this is simply the range <code>1:length(A)</code> if they use 1-based indexing. For array types that have not opted into fast linear indexing, a specialized Cartesian range is typically returned to efficiently index into the array with indices specified for every dimension.</p><p>In general <code>eachindex</code> accepts arbitrary iterables, including strings and dictionaries, and returns an iterator object supporting arbitrary index types (e.g. unevenly spaced or non-integer indices).</p><p>If <code>A</code> is <code>AbstractArray</code> it is possible to explicitly specify the style of the indices that should be returned by <code>eachindex</code> by passing a value having <code>IndexStyle</code> type as its first argument (typically <code>IndexLinear()</code> if linear indices are required or <code>IndexCartesian()</code> if Cartesian range is wanted).</p><p>If you supply more than one <code>AbstractArray</code> argument, <code>eachindex</code> will create an iterable object that is fast for all arguments (typically a <a href="collections.html#Base.UnitRange"><code>UnitRange</code></a> if all inputs have fast linear indexing, a <a href="arrays.html#Base.IteratorsMD.CartesianIndices"><code>CartesianIndices</code></a> otherwise). If the arrays have different sizes and/or dimensionalities, a <code>DimensionMismatch</code> exception will be thrown.</p><p>See also <a href="collections.html#Base.pairs"><code>pairs</code></a><code>(A)</code> to iterate over indices and values together, and <a href="arrays.html#Base.axes-Tuple{Any}"><code>axes</code></a><code>(A, 2)</code> for valid indices along one dimension.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [10 20; 30 40];

julia&gt; for i in eachindex(A) # linear indexing
           println(&quot;A[&quot;, i, &quot;] == &quot;, A[i])
       end
A[1] == 10
A[2] == 30
A[3] == 20
A[4] == 40

julia&gt; for i in eachindex(view(A, 1:2, 1:1)) # Cartesian indexing
           println(i)
       end
CartesianIndex(1, 1)
CartesianIndex(2, 1)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L331-L377">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.IndexStyle" href="#Base.IndexStyle"><code>Base.IndexStyle</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">IndexStyle(A)
IndexStyle(typeof(A))</code></pre><p><code>IndexStyle</code> specifies the &quot;native indexing style&quot; for array <code>A</code>. When you define a new <a href="arrays.html#Core.AbstractArray"><code>AbstractArray</code></a> type, you can choose to implement either linear indexing (with <a href="arrays.html#Base.IndexLinear"><code>IndexLinear</code></a>) or cartesian indexing. If you decide to only implement linear indexing, then you must set this trait for your array type:</p><pre><code class="nohighlight hljs">Base.IndexStyle(::Type{&lt;:MyArray}) = IndexLinear()</code></pre><p>The default is <a href="arrays.html#Base.IndexCartesian"><code>IndexCartesian()</code></a>.</p><p>Julia&#39;s internal indexing machinery will automatically (and invisibly) recompute all indexing operations into the preferred style. This allows users to access elements of your array using any indexing style, even when explicit methods have not been provided.</p><p>If you define both styles of indexing for your <code>AbstractArray</code>, this trait can be used to select the most performant indexing style. Some methods check this trait on their inputs, and dispatch to different algorithms depending on the most efficient access pattern. In particular, <a href="arrays.html#Base.eachindex"><code>eachindex</code></a> creates an iterator whose type depends on the setting of this trait.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/indices.jl#L68-L93">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.IndexLinear" href="#Base.IndexLinear"><code>Base.IndexLinear</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">IndexLinear()</code></pre><p>Subtype of <a href="arrays.html#Base.IndexStyle"><code>IndexStyle</code></a> used to describe arrays which are optimally indexed by one linear index.</p><p>A linear indexing style uses one integer index to describe the position in the array (even if it&#39;s a multidimensional array) and column-major ordering is used to efficiently access the elements. This means that requesting <a href="arrays.html#Base.eachindex"><code>eachindex</code></a> from an array that is <code>IndexLinear</code> will return a simple one-dimensional range, even if it is multidimensional.</p><p>A custom array that reports its <code>IndexStyle</code> as <code>IndexLinear</code> only needs to implement indexing (and indexed assignment) with a single <code>Int</code> index; all other indexing expressions — including multidimensional accesses — will be recomputed to the linear index.  For example, if <code>A</code> were a <code>2×3</code> custom matrix with linear indexing, and we referenced <code>A[1, 3]</code>, this would be recomputed to the equivalent linear index and call <code>A[5]</code> since <code>1 + 2*(3 - 1) = 5</code>.</p><p>See also <a href="arrays.html#Base.IndexCartesian"><code>IndexCartesian</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/indices.jl#L16-L36">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.IndexCartesian" href="#Base.IndexCartesian"><code>Base.IndexCartesian</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">IndexCartesian()</code></pre><p>Subtype of <a href="arrays.html#Base.IndexStyle"><code>IndexStyle</code></a> used to describe arrays which are optimally indexed by a Cartesian index. This is the default for new custom <a href="arrays.html#Core.AbstractArray"><code>AbstractArray</code></a> subtypes.</p><p>A Cartesian indexing style uses multiple integer indices to describe the position in a multidimensional array, with exactly one index per dimension. This means that requesting <a href="arrays.html#Base.eachindex"><code>eachindex</code></a> from an array that is <code>IndexCartesian</code> will return a range of <a href="arrays.html#Base.IteratorsMD.CartesianIndices"><code>CartesianIndices</code></a>.</p><p>A <code>N</code>-dimensional custom array that reports its <code>IndexStyle</code> as <code>IndexCartesian</code> needs to implement indexing (and indexed assignment) with exactly <code>N</code> <code>Int</code> indices; all other indexing expressions — including linear indexing — will be recomputed to the equivalent Cartesian location.  For example, if <code>A</code> were a <code>2×3</code> custom matrix with cartesian indexing, and we referenced <code>A[5]</code>, this would be recomputed to the equivalent Cartesian index and call <code>A[1, 3]</code> since <code>5 = 1 + 2*(3 - 1)</code>.</p><p>It is significantly more expensive to compute Cartesian indices from a linear index than it is to go the other way.  The former operation requires division — a very costly operation — whereas the latter only uses multiplication and addition and is essentially free. This asymmetry means it is far more costly to use linear indexing with an <code>IndexCartesian</code> array than it is to use Cartesian indexing with an <code>IndexLinear</code> array.</p><p>See also <a href="arrays.html#Base.IndexLinear"><code>IndexLinear</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/indices.jl#L39-L65">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.conj!" href="#Base.conj!"><code>Base.conj!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">conj!(A)</code></pre><p>Transform an array to its complex conjugate in-place.</p><p>See also <a href="math.html#Base.conj"><code>conj</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1+im 2-im; 2+2im 3+im]
2×2 Matrix{Complex{Int64}}:
 1+1im  2-1im
 2+2im  3+1im

julia&gt; conj!(A);

julia&gt; A
2×2 Matrix{Complex{Int64}}:
 1-1im  2+1im
 2-2im  3-1im</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarraymath.jl#L98-L119">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.stride" href="#Base.stride"><code>Base.stride</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">stride(A, k::Integer)</code></pre><p>Return the distance in memory (in number of elements) between adjacent elements in dimension <code>k</code>.</p><p>See also: <a href="arrays.html#Base.strides"><code>strides</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = fill(1, (3,4,5));

julia&gt; stride(A,2)
3

julia&gt; stride(A,3)
12</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L576-L593">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.strides" href="#Base.strides"><code>Base.strides</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">strides(A)</code></pre><p>Return a tuple of the memory strides in each dimension.</p><p>See also: <a href="arrays.html#Base.stride"><code>stride</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = fill(1, (3,4,5));

julia&gt; strides(A)
(1, 3, 12)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L559-L573">source</a></section></article><h2 id="Broadcast-and-vectorization"><a class="docs-heading-anchor" href="#Broadcast-and-vectorization">Broadcast and vectorization</a><a id="Broadcast-and-vectorization-1"></a><a class="docs-heading-anchor-permalink" href="#Broadcast-and-vectorization" title="Permalink"></a></h2><p>See also the <a href="../manual/functions.html#man-vectorized">dot syntax for vectorizing functions</a>; for example, <code>f.(args...)</code> implicitly calls <code>broadcast(f, args...)</code>. Rather than relying on &quot;vectorized&quot; methods of functions like <code>sin</code> to operate on arrays, you should use <code>sin.(a)</code> to vectorize via <code>broadcast</code>.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Broadcast.broadcast" href="#Base.Broadcast.broadcast"><code>Base.Broadcast.broadcast</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">broadcast(f, As...)</code></pre><p>Broadcast the function <code>f</code> over the arrays, tuples, collections, <a href="c.html#Core.Ref"><code>Ref</code></a>s and/or scalars <code>As</code>.</p><p>Broadcasting applies the function <code>f</code> over the elements of the container arguments and the scalars themselves in <code>As</code>. Singleton and missing dimensions are expanded to match the extents of the other arguments by virtually repeating the value. By default, only a limited number of types are considered scalars, including <code>Number</code>s, <code>String</code>s, <code>Symbol</code>s, <code>Type</code>s, <code>Function</code>s and some common singletons like <a href="../manual/missing.html#missing"><code>missing</code></a> and <a href="constants.html#Core.nothing"><code>nothing</code></a>. All other arguments are iterated over or indexed into elementwise.</p><p>The resulting container type is established by the following rules:</p><ul><li>If all the arguments are scalars or zero-dimensional arrays, it returns an unwrapped scalar.</li><li>If at least one argument is a tuple and all others are scalars or zero-dimensional arrays, it returns a tuple.</li><li>All other combinations of arguments default to returning an <code>Array</code>, but custom container types can define their own implementation and promotion-like rules to customize the result when they appear as arguments.</li></ul><p>A special syntax exists for broadcasting: <code>f.(args...)</code> is equivalent to <code>broadcast(f, args...)</code>, and nested <code>f.(g.(args...))</code> calls are fused into a single broadcast loop.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1, 2, 3, 4, 5]
5-element Vector{Int64}:
 1
 2
 3
 4
 5

julia&gt; B = [1 2; 3 4; 5 6; 7 8; 9 10]
5×2 Matrix{Int64}:
 1   2
 3   4
 5   6
 7   8
 9  10

julia&gt; broadcast(+, A, B)
5×2 Matrix{Int64}:
  2   3
  5   6
  8   9
 11  12
 14  15

julia&gt; parse.(Int, [&quot;1&quot;, &quot;2&quot;])
2-element Vector{Int64}:
 1
 2

julia&gt; abs.((1, -2))
(1, 2)

julia&gt; broadcast(+, 1.0, (0, -2.0))
(1.0, -1.0)

julia&gt; (+).([[0,2], [1,3]], Ref{Vector{Int}}([1,-1]))
2-element Vector{Vector{Int64}}:
 [1, 1]
 [2, 2]

julia&gt; string.((&quot;one&quot;,&quot;two&quot;,&quot;three&quot;,&quot;four&quot;), &quot;: &quot;, 1:4)
4-element Vector{String}:
 &quot;one: 1&quot;
 &quot;two: 2&quot;
 &quot;three: 3&quot;
 &quot;four: 4&quot;
</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/broadcast.jl#L734-L809">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Broadcast.broadcast!" href="#Base.Broadcast.broadcast!"><code>Base.Broadcast.broadcast!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">broadcast!(f, dest, As...)</code></pre><p>Like <a href="arrays.html#Base.Broadcast.broadcast"><code>broadcast</code></a>, but store the result of <code>broadcast(f, As...)</code> in the <code>dest</code> array. Note that <code>dest</code> is only used to store the result, and does not supply arguments to <code>f</code> unless it is also listed in the <code>As</code>, as in <code>broadcast!(f, A, A, B)</code> to perform <code>A[:] = broadcast(f, A, B)</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1.0; 0.0]; B = [0.0; 0.0];

julia&gt; broadcast!(+, B, A, (0, -2.0));

julia&gt; B
2-element Vector{Float64}:
  1.0
 -2.0

julia&gt; A
2-element Vector{Float64}:
 1.0
 0.0

julia&gt; broadcast!(+, A, A, (0, -2.0));

julia&gt; A
2-element Vector{Float64}:
  1.0
 -2.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/broadcast.jl#L816-L848">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Broadcast.@__dot__" href="#Base.Broadcast.@__dot__"><code>Base.Broadcast.@__dot__</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@. expr</code></pre><p>Convert every function call or operator in <code>expr</code> into a &quot;dot call&quot; (e.g. convert <code>f(x)</code> to <code>f.(x)</code>), and convert every assignment in <code>expr</code> to a &quot;dot assignment&quot; (e.g. convert <code>+=</code> to <code>.+=</code>).</p><p>If you want to <em>avoid</em> adding dots for selected function calls in <code>expr</code>, splice those function calls in with <code>$</code>.  For example, <code>@. sqrt(abs($sort(x)))</code> is equivalent to <code>sqrt.(abs.(sort(x)))</code> (no dot for <code>sort</code>).</p><p>(<code>@.</code> is equivalent to a call to <code>@__dot__</code>.)</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = 1.0:3.0; y = similar(x);

julia&gt; @. y = x + 3 * sin(x)
3-element Vector{Float64}:
 3.5244129544236893
 4.727892280477045
 3.4233600241796016</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/broadcast.jl#L1278-L1302">source</a></section></article><p>For specializing broadcast on custom types, see</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Broadcast.BroadcastStyle" href="#Base.Broadcast.BroadcastStyle"><code>Base.Broadcast.BroadcastStyle</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p><code>BroadcastStyle</code> is an abstract type and trait-function used to determine behavior of objects under broadcasting. <code>BroadcastStyle(typeof(x))</code> returns the style associated with <code>x</code>. To customize the broadcasting behavior of a type, one can declare a style by defining a type/method pair</p><pre><code class="nohighlight hljs">struct MyContainerStyle &lt;: BroadcastStyle end
Base.BroadcastStyle(::Type{&lt;:MyContainer}) = MyContainerStyle()</code></pre><p>One then writes method(s) (at least <a href="arrays.html#Base.similar"><code>similar</code></a>) operating on <code>Broadcasted{MyContainerStyle}</code>. There are also several pre-defined subtypes of <code>BroadcastStyle</code> that you may be able to leverage; see the <a href="../manual/interfaces.html#man-interfaces-broadcasting">Interfaces chapter</a> for more information.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/broadcast.jl#L21-L34">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Broadcast.AbstractArrayStyle" href="#Base.Broadcast.AbstractArrayStyle"><code>Base.Broadcast.AbstractArrayStyle</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p><code>Broadcast.AbstractArrayStyle{N} &lt;: BroadcastStyle</code> is the abstract supertype for any style associated with an <code>AbstractArray</code> type. The <code>N</code> parameter is the dimensionality, which can be handy for AbstractArray types that only support specific dimensionalities:</p><pre><code class="nohighlight hljs">struct SparseMatrixStyle &lt;: Broadcast.AbstractArrayStyle{2} end
Base.BroadcastStyle(::Type{&lt;:SparseMatrixCSC}) = SparseMatrixStyle()</code></pre><p>For <code>AbstractArray</code> types that support arbitrary dimensionality, <code>N</code> can be set to <code>Any</code>:</p><pre><code class="nohighlight hljs">struct MyArrayStyle &lt;: Broadcast.AbstractArrayStyle{Any} end
Base.BroadcastStyle(::Type{&lt;:MyArray}) = MyArrayStyle()</code></pre><p>In cases where you want to be able to mix multiple <code>AbstractArrayStyle</code>s and keep track of dimensionality, your style needs to support a <a href="base.html#Base.Val"><code>Val</code></a> constructor:</p><pre><code class="nohighlight hljs">struct MyArrayStyleDim{N} &lt;: Broadcast.AbstractArrayStyle{N} end
(::Type{&lt;:MyArrayStyleDim})(::Val{N}) where N = MyArrayStyleDim{N}()</code></pre><p>Note that if two or more <code>AbstractArrayStyle</code> subtypes conflict, broadcasting machinery will fall back to producing <code>Array</code>s. If this is undesirable, you may need to define binary <a href="arrays.html#Base.Broadcast.BroadcastStyle"><code>BroadcastStyle</code></a> rules to control the output type.</p><p>See also <a href="arrays.html#Base.Broadcast.DefaultArrayStyle"><code>Broadcast.DefaultArrayStyle</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/broadcast.jl#L51-L76">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Broadcast.ArrayStyle" href="#Base.Broadcast.ArrayStyle"><code>Base.Broadcast.ArrayStyle</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p><code>Broadcast.ArrayStyle{MyArrayType}()</code> is a <a href="arrays.html#Base.Broadcast.BroadcastStyle"><code>BroadcastStyle</code></a> indicating that an object behaves as an array for broadcasting. It presents a simple way to construct <a href="arrays.html#Base.Broadcast.AbstractArrayStyle"><code>Broadcast.AbstractArrayStyle</code></a>s for specific <code>AbstractArray</code> container types. Broadcast styles created this way lose track of dimensionality; if keeping track is important for your type, you should create your own custom <a href="arrays.html#Base.Broadcast.AbstractArrayStyle"><code>Broadcast.AbstractArrayStyle</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/broadcast.jl#L79-L85">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Broadcast.DefaultArrayStyle" href="#Base.Broadcast.DefaultArrayStyle"><code>Base.Broadcast.DefaultArrayStyle</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p><code>Broadcast.DefaultArrayStyle{N}()</code> is a <a href="arrays.html#Base.Broadcast.BroadcastStyle"><code>BroadcastStyle</code></a> indicating that an object behaves as an <code>N</code>-dimensional array for broadcasting. Specifically, <code>DefaultArrayStyle</code> is used for any <code>AbstractArray</code> type that hasn&#39;t defined a specialized style, and in the absence of overrides from other <code>broadcast</code> arguments the resulting output type is <code>Array</code>. When there are multiple inputs to <code>broadcast</code>, <code>DefaultArrayStyle</code> &quot;loses&quot; to any other <a href="arrays.html#Base.Broadcast.ArrayStyle"><code>Broadcast.ArrayStyle</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/broadcast.jl#L89-L96">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Broadcast.broadcastable" href="#Base.Broadcast.broadcastable"><code>Base.Broadcast.broadcastable</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Broadcast.broadcastable(x)</code></pre><p>Return either <code>x</code> or an object like <code>x</code> such that it supports <a href="arrays.html#Base.axes-Tuple{Any}"><code>axes</code></a>, indexing, and its type supports <a href="arrays.html#Base.ndims"><code>ndims</code></a>.</p><p>If <code>x</code> supports iteration, the returned value should have the same <code>axes</code> and indexing behaviors as <a href="collections.html#Base.collect-Tuple{Any}"><code>collect(x)</code></a>.</p><p>If <code>x</code> is not an <code>AbstractArray</code> but it supports <code>axes</code>, indexing, and its type supports <code>ndims</code>, then <code>broadcastable(::typeof(x))</code> may be implemented to just return itself. Further, if <code>x</code> defines its own <a href="arrays.html#Base.Broadcast.BroadcastStyle"><code>BroadcastStyle</code></a>, then it must define its <code>broadcastable</code> method to return itself for the custom style to have any effect.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Broadcast.broadcastable([1,2,3]) # like `identity` since arrays already support axes and indexing
3-element Vector{Int64}:
 1
 2
 3

julia&gt; Broadcast.broadcastable(Int) # Types don&#39;t support axes, indexing, or iteration but are commonly used as scalars
Base.RefValue{Type{Int64}}(Int64)

julia&gt; Broadcast.broadcastable(&quot;hello&quot;) # Strings break convention of matching iteration and act like a scalar instead
Base.RefValue{String}(&quot;hello&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/broadcast.jl#L680-L707">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Broadcast.combine_axes" href="#Base.Broadcast.combine_axes"><code>Base.Broadcast.combine_axes</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">combine_axes(As...) -&gt; Tuple</code></pre><p>Determine the result axes for broadcasting across all values in <code>As</code>.</p><pre><code class="language-julia-repl hljs">julia&gt; Broadcast.combine_axes([1], [1 2; 3 4; 5 6])
(Base.OneTo(3), Base.OneTo(2))

julia&gt; Broadcast.combine_axes(1, 1, 1)
()</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/broadcast.jl#L483-L495">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Broadcast.combine_styles" href="#Base.Broadcast.combine_styles"><code>Base.Broadcast.combine_styles</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">combine_styles(cs...) -&gt; BroadcastStyle</code></pre><p>Decides which <code>BroadcastStyle</code> to use for any number of value arguments. Uses <a href="arrays.html#Base.Broadcast.BroadcastStyle"><code>BroadcastStyle</code></a> to get the style for each argument, and uses <a href="arrays.html#Base.Broadcast.result_style"><code>result_style</code></a> to combine styles.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Broadcast.combine_styles([1], [1 2; 3 4])
Base.Broadcast.DefaultArrayStyle{2}()</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/broadcast.jl#L409-L422">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Broadcast.result_style" href="#Base.Broadcast.result_style"><code>Base.Broadcast.result_style</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">result_style(s1::BroadcastStyle[, s2::BroadcastStyle]) -&gt; BroadcastStyle</code></pre><p>Takes one or two <code>BroadcastStyle</code>s and combines them using <a href="arrays.html#Base.Broadcast.BroadcastStyle"><code>BroadcastStyle</code></a> to determine a common <code>BroadcastStyle</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Broadcast.result_style(Broadcast.DefaultArrayStyle{0}(), Broadcast.DefaultArrayStyle{3}())
Base.Broadcast.DefaultArrayStyle{3}()

julia&gt; Broadcast.result_style(Broadcast.Unknown(), Broadcast.DefaultArrayStyle{1}())
Base.Broadcast.DefaultArrayStyle{1}()</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/broadcast.jl#L434-L449">source</a></section></article><h2 id="Indexing-and-assignment"><a class="docs-heading-anchor" href="#Indexing-and-assignment">Indexing and assignment</a><a id="Indexing-and-assignment-1"></a><a class="docs-heading-anchor-permalink" href="#Indexing-and-assignment" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.getindex-Tuple{AbstractArray, Vararg{Any}}" href="#Base.getindex-Tuple{AbstractArray, Vararg{Any}}"><code>Base.getindex</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">getindex(A, inds...)</code></pre><p>Return a subset of array <code>A</code> as selected by the indices <code>inds</code>.</p><p>Each index may be any <a href="../manual/arrays.html#man-supported-index-types">supported index type</a>, such as an <a href="numbers.html#Core.Integer"><code>Integer</code></a>, <a href="arrays.html#Base.IteratorsMD.CartesianIndex"><code>CartesianIndex</code></a>, <a href="collections.html#Base.AbstractRange">range</a>, or <a href="../manual/arrays.html#man-multi-dim-arrays">array</a> of supported indices. A <a href="arrays.html#Base.Colon">:</a> may be used to select all elements along a specific dimension, and a boolean array (e.g. an <code>Array{Bool}</code> or a <a href="arrays.html#Base.BitArray"><code>BitArray</code></a>) may be used to filter for elements where the corresponding index is <code>true</code>.</p><p>When <code>inds</code> selects multiple elements, this function returns a newly allocated array. To index multiple elements without making a copy, use <a href="arrays.html#Base.view"><code>view</code></a> instead.</p><p>See the manual section on <a href="../manual/arrays.html#man-array-indexing">array indexing</a> for details.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; getindex(A, 1)
1

julia&gt; getindex(A, [2, 1])
2-element Vector{Int64}:
 3
 1

julia&gt; getindex(A, 2:4)
3-element Vector{Int64}:
 3
 2
 4

julia&gt; getindex(A, 2, 1)
3

julia&gt; getindex(A, CartesianIndex(2, 1))
3

julia&gt; getindex(A, :, 2)
2-element Vector{Int64}:
 2
 4

julia&gt; getindex(A, 2, :)
2-element Vector{Int64}:
 3
 4

julia&gt; getindex(A, A .&gt; 2)
2-element Vector{Int64}:
 3
 4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L1251-L1308">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.setindex!-Tuple{AbstractArray, Any, Vararg{Any}}" href="#Base.setindex!-Tuple{AbstractArray, Any, Vararg{Any}}"><code>Base.setindex!</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">setindex!(A, X, inds...)
A[inds...] = X</code></pre><p>Store values from array <code>X</code> within some subset of <code>A</code> as specified by <code>inds</code>. The syntax <code>A[inds...] = X</code> is equivalent to <code>(setindex!(A, X, inds...); X)</code>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = zeros(2,2);

julia&gt; setindex!(A, [10, 20], [1, 2]);

julia&gt; A[[3, 4]] = [30, 40];

julia&gt; A
2×2 Matrix{Float64}:
 10.0  30.0
 20.0  40.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L1387-L1409">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.nextind" href="#Base.nextind"><code>Base.nextind</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">nextind(A, i)</code></pre><p>Return the index after <code>i</code> in <code>A</code>. The returned index is often equivalent to <code>i + 1</code> for an integer <code>i</code>. This function can be useful for generic code.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>The returned index might be out of bounds. Consider using <a href="arrays.html#Base.checkbounds"><code>checkbounds</code></a>.</p></div></div><p>See also: <a href="arrays.html#Base.prevind"><code>prevind</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; nextind(x, 1) # valid result
2

julia&gt; nextind(x, 4) # invalid result
5

julia&gt; nextind(x, CartesianIndex(1, 1)) # valid result
CartesianIndex(2, 1)

julia&gt; nextind(x, CartesianIndex(2, 2)) # invalid result
CartesianIndex(1, 3)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/tuple.jl#L110-L141">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.prevind" href="#Base.prevind"><code>Base.prevind</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">prevind(A, i)</code></pre><p>Return the index before <code>i</code> in <code>A</code>. The returned index is often equivalent to <code>i - 1</code> for an integer <code>i</code>. This function can be useful for generic code.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>The returned index might be out of bounds. Consider using <a href="arrays.html#Base.checkbounds"><code>checkbounds</code></a>.</p></div></div><p>See also: <a href="arrays.html#Base.nextind"><code>nextind</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; prevind(x, 4) # valid result
3

julia&gt; prevind(x, 1) # invalid result
0

julia&gt; prevind(x, CartesianIndex(2, 2)) # valid result
CartesianIndex(1, 2)

julia&gt; prevind(x, CartesianIndex(1, 1)) # invalid result
CartesianIndex(2, 0)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/tuple.jl#L76-L107">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.copyto!-Tuple{AbstractArray, CartesianIndices, AbstractArray, CartesianIndices}" href="#Base.copyto!-Tuple{AbstractArray, CartesianIndices, AbstractArray, CartesianIndices}"><code>Base.copyto!</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">copyto!(dest, Rdest::CartesianIndices, src, Rsrc::CartesianIndices) -&gt; dest</code></pre><p>Copy the block of <code>src</code> in the range of <code>Rsrc</code> to the block of <code>dest</code> in the range of <code>Rdest</code>. The sizes of the two regions must match.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = zeros(5, 5);

julia&gt; B = [1 2; 3 4];

julia&gt; Ainds = CartesianIndices((2:3, 2:3));

julia&gt; Binds = CartesianIndices(B);

julia&gt; copyto!(A, Ainds, B, Binds)
5×5 Matrix{Float64}:
 0.0  0.0  0.0  0.0  0.0
 0.0  1.0  2.0  0.0  0.0
 0.0  3.0  4.0  0.0  0.0
 0.0  0.0  0.0  0.0  0.0
 0.0  0.0  0.0  0.0  0.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multidimensional.jl#L1177-L1201">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.copy!" href="#Base.copy!"><code>Base.copy!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">copy!(dst, src) -&gt; dst</code></pre><p>In-place <a href="base.html#Base.copy"><code>copy</code></a> of <code>src</code> into <code>dst</code>, discarding any pre-existing elements in <code>dst</code>. If <code>dst</code> and <code>src</code> are of the same type, <code>dst == src</code> should hold after the call. If <code>dst</code> and <code>src</code> are multidimensional arrays, they must have equal <a href="arrays.html#Base.axes-Tuple{Any}"><code>axes</code></a>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p>See also <a href="c.html#Base.copyto!"><code>copyto!</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p>This method requires at least Julia 1.1. In Julia 1.0 this method is available from the <code>Future</code> standard library as <code>Future.copy!</code>.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L893-L909">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isassigned" href="#Base.isassigned"><code>Base.isassigned</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isassigned(array, i) -&gt; Bool</code></pre><p>Test whether the given array has a value associated with index <code>i</code>. Return <code>false</code> if the index is out of bounds, or has an undefined reference.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isassigned(rand(3, 3), 5)
true

julia&gt; isassigned(rand(3, 3), 3 * 3 + 1)
false

julia&gt; mutable struct Foo end

julia&gt; v = similar(rand(3), Foo)
3-element Vector{Foo}:
 #undef
 #undef
 #undef

julia&gt; isassigned(v, 1)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/essentials.jl#L963-L988">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Colon" href="#Base.Colon"><code>Base.Colon</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Colon()</code></pre><p>Colons (:) are used to signify indexing entire objects or dimensions at once.</p><p>Very few operations are defined on Colons directly; instead they are converted by <a href="arrays.html#Base.to_indices"><code>to_indices</code></a> to an internal vector type (<code>Base.Slice</code>) to represent the collection of indices they span before being used.</p><p>The singleton instance of <code>Colon</code> is also a function used to construct ranges; see <a href="math.html#Base.::"><code>:</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/essentials.jl#L997-L1008">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.IteratorsMD.CartesianIndex" href="#Base.IteratorsMD.CartesianIndex"><code>Base.IteratorsMD.CartesianIndex</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">CartesianIndex(i, j, k...)   -&gt; I
CartesianIndex((i, j, k...)) -&gt; I</code></pre><p>Create a multidimensional index <code>I</code>, which can be used for indexing a multidimensional array <code>A</code>.  In particular, <code>A[I]</code> is equivalent to <code>A[i,j,k...]</code>.  One can freely mix integer and <code>CartesianIndex</code> indices; for example, <code>A[Ipre, i, Ipost]</code> (where <code>Ipre</code> and <code>Ipost</code> are <code>CartesianIndex</code> indices and <code>i</code> is an <code>Int</code>) can be a useful expression when writing algorithms that work along a single dimension of an array of arbitrary dimensionality.</p><p>A <code>CartesianIndex</code> is sometimes produced by <a href="arrays.html#Base.eachindex"><code>eachindex</code></a>, and always when iterating with an explicit <a href="arrays.html#Base.IteratorsMD.CartesianIndices"><code>CartesianIndices</code></a>.</p><p>An <code>I::CartesianIndex</code> is treated as a &quot;scalar&quot; (not a container) for <code>broadcast</code>.   In order to iterate over the components of a <code>CartesianIndex</code>, convert it to a tuple with <code>Tuple(I)</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = reshape(Vector(1:16), (2, 2, 2, 2))
2×2×2×2 Array{Int64, 4}:
[:, :, 1, 1] =
 1  3
 2  4

[:, :, 2, 1] =
 5  7
 6  8

[:, :, 1, 2] =
  9  11
 10  12

[:, :, 2, 2] =
 13  15
 14  16

julia&gt; A[CartesianIndex((1, 1, 1, 1))]
1

julia&gt; A[CartesianIndex((1, 1, 1, 2))]
9

julia&gt; A[CartesianIndex((1, 1, 2, 1))]
5</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.10</header><div class="admonition-body"><p>Using a <code>CartesianIndex</code> as a &quot;scalar&quot; for <code>broadcast</code> requires Julia 1.10; in previous releases, use <code>Ref(I)</code>.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multidimensional.jl#L19-L72">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.IteratorsMD.CartesianIndices" href="#Base.IteratorsMD.CartesianIndices"><code>Base.IteratorsMD.CartesianIndices</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">CartesianIndices(sz::Dims) -&gt; R
CartesianIndices((istart:[istep:]istop, jstart:[jstep:]jstop, ...)) -&gt; R</code></pre><p>Define a region <code>R</code> spanning a multidimensional rectangular range of integer indices. These are most commonly encountered in the context of iteration, where <code>for I in R ... end</code> will return <a href="arrays.html#Base.IteratorsMD.CartesianIndex"><code>CartesianIndex</code></a> indices <code>I</code> equivalent to the nested loops</p><pre><code class="nohighlight hljs">for j = jstart:jstep:jstop
    for i = istart:istep:istop
        ...
    end
end</code></pre><p>Consequently these can be useful for writing algorithms that work in arbitrary dimensions.</p><pre><code class="nohighlight hljs">CartesianIndices(A::AbstractArray) -&gt; R</code></pre><p>As a convenience, constructing a <code>CartesianIndices</code> from an array makes a range of its indices.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>The step range method <code>CartesianIndices((istart:istep:istop, jstart:[jstep:]jstop, ...))</code> requires at least Julia 1.6.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; foreach(println, CartesianIndices((2, 2, 2)))
CartesianIndex(1, 1, 1)
CartesianIndex(2, 1, 1)
CartesianIndex(1, 2, 1)
CartesianIndex(2, 2, 1)
CartesianIndex(1, 1, 2)
CartesianIndex(2, 1, 2)
CartesianIndex(1, 2, 2)
CartesianIndex(2, 2, 2)

julia&gt; CartesianIndices(fill(1, (2,3)))
CartesianIndices((2, 3))</code></pre><p><strong>Conversion between linear and cartesian indices</strong></p><p>Linear index to cartesian index conversion exploits the fact that a <code>CartesianIndices</code> is an <code>AbstractArray</code> and can be indexed linearly:</p><pre><code class="language-julia-repl hljs">julia&gt; cartesian = CartesianIndices((1:3, 1:2))
CartesianIndices((1:3, 1:2))

julia&gt; cartesian[4]
CartesianIndex(1, 2)

julia&gt; cartesian = CartesianIndices((1:2:5, 1:2))
CartesianIndices((1:2:5, 1:2))

julia&gt; cartesian[2, 2]
CartesianIndex(3, 2)</code></pre><p><strong>Broadcasting</strong></p><p><code>CartesianIndices</code> support broadcasting arithmetic (+ and -) with a <code>CartesianIndex</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p>Broadcasting of CartesianIndices requires at least Julia 1.1.</p></div></div><pre><code class="language-julia-repl hljs">julia&gt; CIs = CartesianIndices((2:3, 5:6))
CartesianIndices((2:3, 5:6))

julia&gt; CI = CartesianIndex(3, 4)
CartesianIndex(3, 4)

julia&gt; CIs .+ CI
CartesianIndices((5:6, 9:10))</code></pre><p>For cartesian to linear index conversion, see <a href="arrays.html#Base.LinearIndices"><code>LinearIndices</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multidimensional.jl#L185-L266">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Dims" href="#Base.Dims"><code>Base.Dims</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Dims{N}</code></pre><p>An <code>NTuple</code> of <code>N</code> <code>Int</code>s used to represent the dimensions of an <a href="arrays.html#Core.AbstractArray"><code>AbstractArray</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/indices.jl#L3-L8">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.LinearIndices" href="#Base.LinearIndices"><code>Base.LinearIndices</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LinearIndices(A::AbstractArray)</code></pre><p>Return a <code>LinearIndices</code> array with the same shape and <a href="arrays.html#Base.axes-Tuple{Any}"><code>axes</code></a> as <code>A</code>, holding the linear index of each entry in <code>A</code>. Indexing this array with cartesian indices allows mapping them to linear indices.</p><p>For arrays with conventional indexing (indices start at 1), or any multidimensional array, linear indices range from 1 to <code>length(A)</code>. However, for <code>AbstractVector</code>s linear indices are <code>axes(A, 1)</code>, and therefore do not start at 1 for vectors with unconventional indexing.</p><p>Calling this function is the &quot;safe&quot; way to write algorithms that exploit linear indexing.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = fill(1, (5,6,7));

julia&gt; b = LinearIndices(A);

julia&gt; extrema(b)
(1, 210)</code></pre><pre><code class="nohighlight hljs">LinearIndices(inds::CartesianIndices) -&gt; R
LinearIndices(sz::Dims) -&gt; R
LinearIndices((istart:istop, jstart:jstop, ...)) -&gt; R</code></pre><p>Return a <code>LinearIndices</code> array with the specified shape or <a href="arrays.html#Base.axes-Tuple{Any}"><code>axes</code></a>.</p><p><strong>Examples</strong></p><p>The main purpose of this constructor is intuitive conversion from cartesian to linear indexing:</p><pre><code class="language-julia-repl hljs">julia&gt; linear = LinearIndices((1:3, 1:2))
3×2 LinearIndices{2, Tuple{UnitRange{Int64}, UnitRange{Int64}}}:
 1  4
 2  5
 3  6

julia&gt; linear[1,2]
4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/indices.jl#L436-L482">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.to_indices" href="#Base.to_indices"><code>Base.to_indices</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">to_indices(A, I::Tuple)</code></pre><p>Convert the tuple <code>I</code> to a tuple of indices for use in indexing into array <code>A</code>.</p><p>The returned tuple must only contain either <code>Int</code>s or <code>AbstractArray</code>s of scalar indices that are supported by array <code>A</code>. It will error upon encountering a novel index type that it does not know how to process.</p><p>For simple index types, it defers to the unexported <code>Base.to_index(A, i)</code> to process each index <code>i</code>. While this internal function is not intended to be called directly, <code>Base.to_index</code> may be extended by custom array or index types to provide custom indexing behaviors.</p><p>More complicated index types may require more context about the dimension into which they index. To support those cases, <code>to_indices(A, I)</code> calls <code>to_indices(A, axes(A), I)</code>, which then recursively walks through both the given tuple of indices and the dimensional indices of <code>A</code> in tandem. As such, not all index types are guaranteed to propagate to <code>Base.to_index</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = zeros(1,2,3,4);

julia&gt; to_indices(A, (1,1,2,2))
(1, 1, 2, 2)

julia&gt; to_indices(A, (1,1,2,20)) # no bounds checking
(1, 1, 2, 20)

julia&gt; to_indices(A, (CartesianIndex((1,)), 2, CartesianIndex((3,4)))) # exotic index
(1, 2, 3, 4)

julia&gt; to_indices(A, ([1,1], 1:2, 3, 4))
([1, 1], 1:2, 3, 4)

julia&gt; to_indices(A, (1,2)) # no shape checking
(1, 2)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/indices.jl#L319-L358">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.checkbounds" href="#Base.checkbounds"><code>Base.checkbounds</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">checkbounds(Bool, A, I...)</code></pre><p>Return <code>true</code> if the specified indices <code>I</code> are in bounds for the given array <code>A</code>. Subtypes of <code>AbstractArray</code> should specialize this method if they need to provide custom bounds checking behaviors; however, in many cases one can rely on <code>A</code>&#39;s indices and <a href="arrays.html#Base.checkindex"><code>checkindex</code></a>.</p><p>See also <a href="arrays.html#Base.checkindex"><code>checkindex</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = rand(3, 3);

julia&gt; checkbounds(Bool, A, 2)
true

julia&gt; checkbounds(Bool, A, 3, 4)
false

julia&gt; checkbounds(Bool, A, 1:3)
true

julia&gt; checkbounds(Bool, A, 1:3, 2:4)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L652-L678">source</a></section><section><div><pre><code class="language-julia hljs">checkbounds(A, I...)</code></pre><p>Throw an error if the specified indices <code>I</code> are not in bounds for the given array <code>A</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L692-L696">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.checkindex" href="#Base.checkindex"><code>Base.checkindex</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">checkindex(Bool, inds::AbstractUnitRange, index)</code></pre><p>Return <code>true</code> if the given <code>index</code> is within the bounds of <code>inds</code>. Custom types that would like to behave as indices for all arrays can extend this method in order to provide a specialized bounds checking implementation.</p><p>See also <a href="arrays.html#Base.checkbounds"><code>checkbounds</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; checkindex(Bool, 1:20, 8)
true

julia&gt; checkindex(Bool, 1:20, 21)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L732-L750">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.elsize" href="#Base.elsize"><code>Base.elsize</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">elsize(type)</code></pre><p>Compute the memory stride in bytes between consecutive elements of <a href="collections.html#Base.eltype"><code>eltype</code></a> stored inside the given <code>type</code>, if the array elements are stored densely with a uniform linear stride.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Base.elsize(rand(Float32, 10))
4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L244-L256">source</a></section></article><h2 id="Views-(SubArrays-and-other-view-types)"><a class="docs-heading-anchor" href="#Views-(SubArrays-and-other-view-types)">Views (SubArrays and other view types)</a><a id="Views-(SubArrays-and-other-view-types)-1"></a><a class="docs-heading-anchor-permalink" href="#Views-(SubArrays-and-other-view-types)" title="Permalink"></a></h2><p>A “view” is a data structure that acts like an array (it is a subtype of <code>AbstractArray</code>), but the underlying data is actually part of another array.</p><p>For example, if <code>x</code> is an array and <code>v = @view x[1:10]</code>, then <code>v</code> acts like a 10-element array, but its data is actually accessing the first 10 elements of <code>x</code>. Writing to a view, e.g. <code>v[3] = 2</code>, writes directly to the underlying array <code>x</code> (in this case modifying <code>x[3]</code>).</p><p>Slicing operations like <code>x[1:10]</code> create a copy by default in Julia. <code>@view x[1:10]</code> changes it to make a view. The <code>@views</code> macro can be used on a whole block of code (e.g. <code>@views function foo() .... end</code> or <code>@views begin ... end</code>) to change all the slicing operations in that block to use views.  Sometimes making a copy of the data is faster and sometimes using a view is faster, as described in the <a href="../manual/performance-tips.html#man-performance-views">performance tips</a>.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.view" href="#Base.view"><code>Base.view</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">view(A, inds...)</code></pre><p>Like <a href="collections.html#Base.getindex"><code>getindex</code></a>, but returns a lightweight array that lazily references (or is effectively a <em>view</em> into) the parent array <code>A</code> at the given index or indices <code>inds</code> instead of eagerly extracting elements or constructing a copied subset. Calling <a href="collections.html#Base.getindex"><code>getindex</code></a> or <a href="collections.html#Base.setindex!"><code>setindex!</code></a> on the returned value (often a <a href="arrays.html#Base.SubArray"><code>SubArray</code></a>) computes the indices to access or modify the parent array on the fly.  The behavior is undefined if the shape of the parent array is changed after <code>view</code> is called because there is no bound check for the parent array; e.g., it may cause a segmentation fault.</p><p>Some immutable parent arrays (like ranges) may choose to simply recompute a new array in some circumstances instead of returning a <code>SubArray</code> if doing so is efficient and provides compatible semantics.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>In Julia 1.6 or later, <code>view</code> can be called on an <code>AbstractString</code>, returning a <code>SubString</code>.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; b = view(A, :, 1)
2-element view(::Matrix{Int64}, :, 1) with eltype Int64:
 1
 3

julia&gt; fill!(b, 0)
2-element view(::Matrix{Int64}, :, 1) with eltype Int64:
 0
 0

julia&gt; A # Note A has changed even though we modified b
2×2 Matrix{Int64}:
 0  2
 0  4

julia&gt; view(2:5, 2:3) # returns a range as type is immutable
3:4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/subarray.jl#L165-L210">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@view" href="#Base.@view"><code>Base.@view</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@view A[inds...]</code></pre><p>Transform the indexing expression <code>A[inds...]</code> into the equivalent <a href="arrays.html#Base.view"><code>view</code></a> call.</p><p>This can only be applied directly to a single indexing expression and is particularly helpful for expressions that include the special <code>begin</code> or <code>end</code> indexing syntaxes like <code>A[begin, 2:end-1]</code> (as those are not supported by the normal <a href="arrays.html#Base.view"><code>view</code></a> function).</p><p>Note that <code>@view</code> cannot be used as the target of a regular assignment (e.g., <code>@view(A[1, 2:end]) = ...</code>), nor would the un-decorated <a href="../manual/arrays.html#man-indexed-assignment">indexed assignment</a> (<code>A[1, 2:end] = ...</code>) or broadcasted indexed assignment (<code>A[1, 2:end] .= ...</code>) make a copy.  It can be useful, however, for <em>updating</em> broadcasted assignments like <code>@view(A[1, 2:end]) .+= 1</code> because this is a simple syntax for <code>@view(A[1, 2:end]) .= @view(A[1, 2:end]) + 1</code>, and the indexing expression on the right-hand side would otherwise make a copy without the <code>@view</code>.</p><p>See also <a href="arrays.html#Base.@views"><code>@views</code></a> to switch an entire block of code to use views for non-scalar indexing.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p>Using <code>begin</code> in an indexing expression to refer to the first index requires at least Julia 1.5.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; b = @view A[:, 1]
2-element view(::Matrix{Int64}, :, 1) with eltype Int64:
 1
 3

julia&gt; fill!(b, 0)
2-element view(::Matrix{Int64}, :, 1) with eltype Int64:
 0
 0

julia&gt; A
2×2 Matrix{Int64}:
 0  2
 0  4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/views.jl#L77-L124">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@views" href="#Base.@views"><code>Base.@views</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@views expression</code></pre><p>Convert every array-slicing operation in the given expression (which may be a <code>begin</code>/<code>end</code> block, loop, function, etc.) to return a view. Scalar indices, non-array types, and explicit <a href="collections.html#Base.getindex"><code>getindex</code></a> calls (as opposed to <code>array[...]</code>) are unaffected.</p><p>Similarly, <code>@views</code> converts string slices into <a href="strings.html#Base.SubString"><code>SubString</code></a> views.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The <code>@views</code> macro only affects <code>array[...]</code> expressions that appear explicitly in the given <code>expression</code>, not array slicing that occurs in functions called by that code.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p>Using <code>begin</code> in an indexing expression to refer to the first index was implemented in Julia 1.4, but was only supported by <code>@views</code> starting in Julia 1.5.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = zeros(3, 3);

julia&gt; @views for row in 1:3
           b = A[row, :] # b is a view, not a copy
           b .= row      # assign every element to the row index
       end

julia&gt; A
3×3 Matrix{Float64}:
 1.0  1.0  1.0
 2.0  2.0  2.0
 3.0  3.0  3.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/views.jl#L211-L246">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.parent" href="#Base.parent"><code>Base.parent</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">parent(A)</code></pre><p>Return the underlying parent object of the view. This parent of objects of types <code>SubArray</code>, <code>SubString</code>, <code>ReshapedArray</code> or <code>LinearAlgebra.Transpose</code> is what was passed as an argument to <code>view</code>, <code>reshape</code>, <code>transpose</code>, etc. during object creation. If the input is not a wrapped object, return the input itself. If the input is wrapped multiple times, only the outermost wrapper will be removed.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; V = view(A, 1:2, :)
2×2 view(::Matrix{Int64}, 1:2, :) with eltype Int64:
 1  2
 3  4

julia&gt; parent(V)
2×2 Matrix{Int64}:
 1  2
 3  4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L1454-L1479">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.parentindices" href="#Base.parentindices"><code>Base.parentindices</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">parentindices(A)</code></pre><p>Return the indices in the <a href="arrays.html#Base.parent"><code>parent</code></a> which correspond to the view <code>A</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4];

julia&gt; V = view(A, 1, :)
2-element view(::Matrix{Int64}, 1, :) with eltype Int64:
 1
 2

julia&gt; parentindices(V)
(1, Base.Slice(Base.OneTo(2)))</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/subarray.jl#L83-L100">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.selectdim" href="#Base.selectdim"><code>Base.selectdim</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">selectdim(A, d::Integer, i)</code></pre><p>Return a view of all the data of <code>A</code> where the index for dimension <code>d</code> equals <code>i</code>.</p><p>Equivalent to <code>view(A,:,:,...,i,:,:,...)</code> where <code>i</code> is in position <code>d</code>.</p><p>See also: <a href="arrays.html#Base.eachslice"><code>eachslice</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2 3 4; 5 6 7 8]
2×4 Matrix{Int64}:
 1  2  3  4
 5  6  7  8

julia&gt; selectdim(A, 2, 3)
2-element view(::Matrix{Int64}, :, 3) with eltype Int64:
 3
 7

julia&gt; selectdim(A, 2, 3:4)
2×2 view(::Matrix{Int64}, :, 3:4) with eltype Int64:
 3  4
 7  8</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarraymath.jl#L226-L252">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.reinterpret" href="#Base.reinterpret"><code>Base.reinterpret</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">reinterpret(::Type{Out}, x::In)</code></pre><p>Change the type-interpretation of the binary data in the isbits value <code>x</code> to that of the isbits type <code>Out</code>. The size (ignoring padding) of <code>Out</code> has to be the same as that of the type of <code>x</code>. For example, <code>reinterpret(Float32, UInt32(7))</code> interprets the 4 bytes corresponding to <code>UInt32(7)</code> as a <a href="numbers.html#Core.Float32"><code>Float32</code></a>. Note that <code>reinterpret(In, reinterpret(Out, x)) === x</code></p><pre><code class="language-julia-repl hljs">julia&gt; reinterpret(Float32, UInt32(7))
1.0f-44

julia&gt; reinterpret(NTuple{2, UInt8}, 0x1234)
(0x34, 0x12)

julia&gt; reinterpret(UInt16, (0x34, 0x12))
0x1234

julia&gt; reinterpret(Tuple{UInt16, UInt8}, (0x01, 0x0203))
(0x0301, 0x02)</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The treatment of padding differs from reinterpret(::DataType, ::AbstractArray).</p></div></div><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Use caution if some combinations of bits in <code>Out</code> are not considered valid and would otherwise be prevented by the type&#39;s constructors and methods. Unexpected behavior may result without additional validation.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/essentials.jl#L694-L727">source</a></section><section><div><pre><code class="language-julia hljs">reinterpret(T::DataType, A::AbstractArray)</code></pre><p>Construct a view of the array with the same binary data as the given array, but with <code>T</code> as element type.</p><p>This function also works on &quot;lazy&quot; array whose elements are not computed until they are explicitly retrieved. For instance, <code>reinterpret</code> on the range <code>1:6</code> works similarly as on the dense vector <code>collect(1:6)</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; reinterpret(Float32, UInt32[1 2 3 4 5])
1×5 reinterpret(Float32, ::Matrix{UInt32}):
 1.0f-45  3.0f-45  4.0f-45  6.0f-45  7.0f-45

julia&gt; reinterpret(Complex{Int}, 1:6)
3-element reinterpret(Complex{Int64}, ::UnitRange{Int64}):
 1 + 2im
 3 + 4im
 5 + 6im</code></pre><p>If the location of padding bits does not line up between <code>T</code> and <code>eltype(A)</code>, the resulting array will be read-only or write-only, to prevent invalid bits from being written to or read from, respectively.</p><pre><code class="language-julia-repl hljs">julia&gt; a = reinterpret(Tuple{UInt8, UInt32}, UInt32[1, 2])
1-element reinterpret(Tuple{UInt8, UInt32}, ::Vector{UInt32}):
 (0x01, 0x00000002)

julia&gt; a[1] = 3
ERROR: Padding of type Tuple{UInt8, UInt32} is not compatible with type UInt32.

julia&gt; b = reinterpret(UInt32, Tuple{UInt8, UInt32}[(0x01, 0x00000002)]); # showing will error

julia&gt; b[1]
ERROR: Padding of type UInt32 is not compatible with type Tuple{UInt8, UInt32}.</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reinterpretarray.jl#L30-L67">source</a></section><section><div><pre><code class="language-julia hljs">reinterpret(reshape, T, A::AbstractArray{S}) -&gt; B</code></pre><p>Change the type-interpretation of <code>A</code> while consuming or adding a &quot;channel dimension.&quot;</p><p>If <code>sizeof(T) = n*sizeof(S)</code> for <code>n&gt;1</code>, <code>A</code>&#39;s first dimension must be of size <code>n</code> and <code>B</code> lacks <code>A</code>&#39;s first dimension. Conversely, if <code>sizeof(S) = n*sizeof(T)</code> for <code>n&gt;1</code>, <code>B</code> gets a new first dimension of size <code>n</code>. The dimensionality is unchanged if <code>sizeof(T) == sizeof(S)</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>This method requires at least Julia 1.6.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; reinterpret(reshape, Complex{Int}, A)    # the result is a vector
2-element reinterpret(reshape, Complex{Int64}, ::Matrix{Int64}) with eltype Complex{Int64}:
 1 + 3im
 2 + 4im

julia&gt; a = [(1,2,3), (4,5,6)]
2-element Vector{Tuple{Int64, Int64, Int64}}:
 (1, 2, 3)
 (4, 5, 6)

julia&gt; reinterpret(reshape, Int, a)             # the result is a matrix
3×2 reinterpret(reshape, Int64, ::Vector{Tuple{Int64, Int64, Int64}}) with eltype Int64:
 1  4
 2  5
 3  6</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reinterpretarray.jl#L142-L178">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.reshape" href="#Base.reshape"><code>Base.reshape</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">reshape(A, dims...) -&gt; AbstractArray
reshape(A, dims) -&gt; AbstractArray</code></pre><p>Return an array with the same data as <code>A</code>, but with different dimension sizes or number of dimensions. The two arrays share the same underlying data, so that the result is mutable if and only if <code>A</code> is mutable, and setting elements of one alters the values of the other.</p><p>The new dimensions may be specified either as a list of arguments or as a shape tuple. At most one dimension may be specified with a <code>:</code>, in which case its length is computed such that its product with all the specified dimensions is equal to the length of the original array <code>A</code>. The total number of elements must not change.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = Vector(1:16)
16-element Vector{Int64}:
  1
  2
  3
  4
  5
  6
  7
  8
  9
 10
 11
 12
 13
 14
 15
 16

julia&gt; reshape(A, (4, 4))
4×4 Matrix{Int64}:
 1  5   9  13
 2  6  10  14
 3  7  11  15
 4  8  12  16

julia&gt; reshape(A, 2, :)
2×8 Matrix{Int64}:
 1  3  5  7   9  11  13  15
 2  4  6  8  10  12  14  16

julia&gt; reshape(1:6, 2, 3)
2×3 reshape(::UnitRange{Int64}, 2, 3) with eltype Int64:
 1  3  5
 2  4  6</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reshapedarray.jl#L64-L117">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.dropdims" href="#Base.dropdims"><code>Base.dropdims</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">dropdims(A; dims)</code></pre><p>Return an array with the same data as <code>A</code>, but with the dimensions specified by <code>dims</code> removed. <code>size(A,d)</code> must equal 1 for every <code>d</code> in <code>dims</code>, and repeated dimensions or numbers outside <code>1:ndims(A)</code> are forbidden.</p><p>The result shares the same underlying data as <code>A</code>, such that the result is mutable if and only if <code>A</code> is mutable, and setting elements of one alters the values of the other.</p><p>See also: <a href="arrays.html#Base.reshape"><code>reshape</code></a>, <a href="arrays.html#Base.vec"><code>vec</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = reshape(Vector(1:4),(2,2,1,1))
2×2×1×1 Array{Int64, 4}:
[:, :, 1, 1] =
 1  3
 2  4

julia&gt; b = dropdims(a; dims=3)
2×2×1 Array{Int64, 3}:
[:, :, 1] =
 1  3
 2  4

julia&gt; b[1,1,1] = 5; a
2×2×1×1 Array{Int64, 4}:
[:, :, 1, 1] =
 5  3
 2  4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarraymath.jl#L48-L81">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.vec" href="#Base.vec"><code>Base.vec</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">vec(a::AbstractArray) -&gt; AbstractVector</code></pre><p>Reshape the array <code>a</code> as a one-dimensional column vector. Return <code>a</code> if it is already an <code>AbstractVector</code>. The resulting array shares the same underlying data as <code>a</code>, so it will only be mutable if <code>a</code> is mutable, in which case modifying one will also modify the other.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [1 2 3; 4 5 6]
2×3 Matrix{Int64}:
 1  2  3
 4  5  6

julia&gt; vec(a)
6-element Vector{Int64}:
 1
 4
 2
 5
 3
 6

julia&gt; vec(1:3)
1:3</code></pre><p>See also <a href="arrays.html#Base.reshape"><code>reshape</code></a>, <a href="arrays.html#Base.dropdims"><code>dropdims</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarraymath.jl#L11-L40">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.SubArray" href="#Base.SubArray"><code>Base.SubArray</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SubArray{T,N,P,I,L} &lt;: AbstractArray{T,N}</code></pre><p><code>N</code>-dimensional view into a parent array (of type <code>P</code>) with an element type <code>T</code>, restricted by a tuple of indices (of type <code>I</code>). <code>L</code> is true for types that support fast linear indexing, and <code>false</code> otherwise.</p><p>Construct <code>SubArray</code>s using the <a href="arrays.html#Base.view"><code>view</code></a> function.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/subarray.jl#L7-L13">source</a></section></article><h2 id="Concatenation-and-permutation"><a class="docs-heading-anchor" href="#Concatenation-and-permutation">Concatenation and permutation</a><a id="Concatenation-and-permutation-1"></a><a class="docs-heading-anchor-permalink" href="#Concatenation-and-permutation" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.cat" href="#Base.cat"><code>Base.cat</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cat(A...; dims)</code></pre><p>Concatenate the input arrays along the dimensions specified in <code>dims</code>.</p><p>Along a dimension <code>d in dims</code>, the size of the output array is <code>sum(size(a,d) for a in A)</code>. Along other dimensions, all input arrays should have the same size, which will also be the size of the output array along those dimensions.</p><p>If <code>dims</code> is a single number, the different arrays are tightly packed along that dimension. If <code>dims</code> is an iterable containing several dimensions, the positions along these dimensions are increased simultaneously for each input array, filling with zero elsewhere. This allows one to construct block-diagonal matrices as <code>cat(matrices...; dims=(1,2))</code>, and their higher-dimensional analogues.</p><p>The special case <code>dims=1</code> is <a href="arrays.html#Base.vcat"><code>vcat</code></a>, and <code>dims=2</code> is <a href="arrays.html#Base.hcat"><code>hcat</code></a>. See also <a href="arrays.html#Base.hvcat"><code>hvcat</code></a>, <a href="arrays.html#Base.hvncat"><code>hvncat</code></a>, <a href="arrays.html#Base.stack"><code>stack</code></a>, <a href="arrays.html#Base.repeat"><code>repeat</code></a>.</p><p>The keyword also accepts <code>Val(dims)</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>For multiple dimensions <code>dims = Val(::Tuple)</code> was added in Julia 1.8.</p></div></div><p><strong>Examples</strong></p><p>Concatenate two arrays in different dimensions:</p><pre><code class="language-julia-repl hljs">julia&gt; a = [1 2 3]
1×3 Matrix{Int64}:
 1  2  3

julia&gt; b = [4 5 6]
1×3 Matrix{Int64}:
 4  5  6

julia&gt; cat(a, b; dims=1)
2×3 Matrix{Int64}:
 1  2  3
 4  5  6

julia&gt; cat(a, b; dims=2)
1×6 Matrix{Int64}:
 1  2  3  4  5  6

julia&gt; cat(a, b; dims=(1, 2))
2×6 Matrix{Int64}:
 1  2  3  0  0  0
 0  0  0  4  5  6</code></pre><p><strong>Extended Help</strong></p><p>Concatenate 3D arrays:</p><pre><code class="language-julia-repl hljs">julia&gt; a = ones(2, 2, 3);

julia&gt; b = ones(2, 2, 4);

julia&gt; c = cat(a, b; dims=3);

julia&gt; size(c) == (2, 2, 7)
true</code></pre><p>Concatenate arrays of different sizes:</p><pre><code class="language-julia-repl hljs">julia&gt; cat([1 2; 3 4], [pi, pi], fill(10, 2,3,1); dims=2)  # same as hcat
2×6×1 Array{Float64, 3}:
[:, :, 1] =
 1.0  2.0  3.14159  10.0  10.0  10.0
 3.0  4.0  3.14159  10.0  10.0  10.0</code></pre><p>Construct a block diagonal matrix:</p><pre><code class="nohighlight hljs">julia&gt; cat(true, trues(2,2), trues(4)&#39;, dims=(1,2))  # block-diagonal
4×7 Matrix{Bool}:
 1  0  0  0  0  0  0
 0  1  1  0  0  0  0
 0  1  1  0  0  0  0
 0  0  0  1  1  1  1</code></pre><pre><code class="nohighlight hljs">julia&gt; cat(1, [2], [3;;]; dims=Val(2))
1×3 Matrix{Int64}:
 1  2  3</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p><code>cat</code> does not join two strings, you may want to use <code>*</code>.</p></div></div><pre><code class="language-julia-repl hljs">julia&gt; a = &quot;aaa&quot;;

julia&gt; b = &quot;bbb&quot;;

julia&gt; cat(a, b; dims=1)
2-element Vector{String}:
 &quot;aaa&quot;
 &quot;bbb&quot;

julia&gt; cat(a, b; dims=2)
1×2 Matrix{String}:
 &quot;aaa&quot;  &quot;bbb&quot;

julia&gt; a * b
&quot;aaabbb&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L1973-L2083">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.vcat" href="#Base.vcat"><code>Base.vcat</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">vcat(A...)</code></pre><p>Concatenate arrays or numbers vertically. Equivalent to <a href="arrays.html#Base.cat"><code>cat</code></a><code>(A...; dims=1)</code>, and to the syntax <code>[a; b; c]</code>.</p><p>To concatenate a large vector of arrays, <code>reduce(vcat, A)</code> calls an efficient method when <code>A isa AbstractVector{&lt;:AbstractVecOrMat}</code>, rather than working pairwise.</p><p>See also <a href="arrays.html#Base.hcat"><code>hcat</code></a>, <a href="iterators.html#Base.Iterators.flatten"><code>Iterators.flatten</code></a>, <a href="arrays.html#Base.stack"><code>stack</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; v = vcat([1,2], [3,4])
4-element Vector{Int64}:
 1
 2
 3
 4

julia&gt; v == vcat(1, 2, [3,4])  # accepts numbers
true

julia&gt; v == [1; 2; [3,4]]  # syntax for the same operation
true

julia&gt; summary(ComplexF64[1; 2; [3,4]])  # syntax for supplying the element type
&quot;4-element Vector{ComplexF64}&quot;

julia&gt; vcat(range(1, 2, length=3))  # collects lazy ranges
3-element Vector{Float64}:
 1.0
 1.5
 2.0

julia&gt; two = ([10, 20, 30]&#39;, Float64[4 5 6; 7 8 9])  # row vector and a matrix
([10 20 30], [4.0 5.0 6.0; 7.0 8.0 9.0])

julia&gt; vcat(two...)
3×3 Matrix{Float64}:
 10.0  20.0  30.0
  4.0   5.0   6.0
  7.0   8.0   9.0

julia&gt; vs = [[1, 2], [3, 4], [5, 6]];

julia&gt; reduce(vcat, vs)  # more efficient than vcat(vs...)
6-element Vector{Int64}:
 1
 2
 3
 4
 5
 6

julia&gt; ans == collect(Iterators.flatten(vs))
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L1859-L1917">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.hcat" href="#Base.hcat"><code>Base.hcat</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hcat(A...)</code></pre><p>Concatenate arrays or numbers horizontally. Equivalent to <a href="arrays.html#Base.cat"><code>cat</code></a><code>(A...; dims=2)</code>, and to the syntax <code>[a b c]</code> or <code>[a;; b;; c]</code>.</p><p>For a large vector of arrays, <code>reduce(hcat, A)</code> calls an efficient method when <code>A isa AbstractVector{&lt;:AbstractVecOrMat}</code>. For a vector of vectors, this can also be written <a href="arrays.html#Base.stack"><code>stack</code></a><code>(A)</code>.</p><p>See also <a href="arrays.html#Base.vcat"><code>vcat</code></a>, <a href="arrays.html#Base.hvcat"><code>hvcat</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; hcat([1,2], [3,4], [5,6])
2×3 Matrix{Int64}:
 1  3  5
 2  4  6

julia&gt; hcat(1, 2, [30 40], [5, 6, 7]&#39;)  # accepts numbers
1×7 Matrix{Int64}:
 1  2  30  40  5  6  7

julia&gt; ans == [1 2 [30 40] [5, 6, 7]&#39;]  # syntax for the same operation
true

julia&gt; Float32[1 2 [30 40] [5, 6, 7]&#39;]  # syntax for supplying the eltype
1×7 Matrix{Float32}:
 1.0  2.0  30.0  40.0  5.0  6.0  7.0

julia&gt; ms = [zeros(2,2), [1 2; 3 4], [50 60; 70 80]];

julia&gt; reduce(hcat, ms)  # more efficient than hcat(ms...)
2×6 Matrix{Float64}:
 0.0  0.0  1.0  2.0  50.0  60.0
 0.0  0.0  3.0  4.0  70.0  80.0

julia&gt; stack(ms) |&gt; summary  # disagrees on a vector of matrices
&quot;2×2×3 Array{Float64, 3}&quot;

julia&gt; hcat(Int[], Int[], Int[])  # empty vectors, each of size (0,)
0×3 Matrix{Int64}

julia&gt; hcat([1.1, 9.9], Matrix(undef, 2, 0))  # hcat with empty 2×0 Matrix
2×1 Matrix{Any}:
 1.1
 9.9</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L1919-L1967">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.hvcat" href="#Base.hvcat"><code>Base.hvcat</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hvcat(blocks_per_row::Union{Tuple{Vararg{Int}}, Int}, values...)</code></pre><p>Horizontal and vertical concatenation in one call. This function is called for block matrix syntax. The first argument specifies the number of arguments to concatenate in each block row. If the first argument is a single integer <code>n</code>, then all block rows are assumed to have <code>n</code> block columns.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a, b, c, d, e, f = 1, 2, 3, 4, 5, 6
(1, 2, 3, 4, 5, 6)

julia&gt; [a b c; d e f]
2×3 Matrix{Int64}:
 1  2  3
 4  5  6

julia&gt; hvcat((3,3), a,b,c,d,e,f)
2×3 Matrix{Int64}:
 1  2  3
 4  5  6

julia&gt; [a b; c d; e f]
3×2 Matrix{Int64}:
 1  2
 3  4
 5  6

julia&gt; hvcat((2,2,2), a,b,c,d,e,f)
3×2 Matrix{Int64}:
 1  2
 3  4
 5  6
julia&gt; hvcat((2,2,2), a,b,c,d,e,f) == hvcat(2, a,b,c,d,e,f)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L2121-L2158">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.hvncat" href="#Base.hvncat"><code>Base.hvncat</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hvncat(dim::Int, row_first, values...)
hvncat(dims::Tuple{Vararg{Int}}, row_first, values...)
hvncat(shape::Tuple{Vararg{Tuple}}, row_first, values...)</code></pre><p>Horizontal, vertical, and n-dimensional concatenation of many <code>values</code> in one call.</p><p>This function is called for block matrix syntax. The first argument either specifies the shape of the concatenation, similar to <code>hvcat</code>, as a tuple of tuples, or the dimensions that specify the key number of elements along each axis, and is used to determine the output dimensions. The <code>dims</code> form is more performant, and is used by default when the concatenation operation has the same number of elements along each axis (e.g., [a b; c d;;; e f ; g h]). The <code>shape</code> form is used when the number of elements along each axis is unbalanced (e.g., [a b ; c]). Unbalanced syntax needs additional validation overhead. The <code>dim</code> form is an optimization for concatenation along just one dimension. <code>row_first</code> indicates how <code>values</code> are ordered. The meaning of the first and second elements of <code>shape</code> are also swapped based on <code>row_first</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a, b, c, d, e, f = 1, 2, 3, 4, 5, 6
(1, 2, 3, 4, 5, 6)

julia&gt; [a b c;;; d e f]
1×3×2 Array{Int64, 3}:
[:, :, 1] =
 1  2  3

[:, :, 2] =
 4  5  6

julia&gt; hvncat((2,1,3), false, a,b,c,d,e,f)
2×1×3 Array{Int64, 3}:
[:, :, 1] =
 1
 2

[:, :, 2] =
 3
 4

[:, :, 3] =
 5
 6

julia&gt; [a b;;; c d;;; e f]
1×2×3 Array{Int64, 3}:
[:, :, 1] =
 1  2

[:, :, 2] =
 3  4

[:, :, 3] =
 5  6

julia&gt; hvncat(((3, 3), (3, 3), (6,)), true, a, b, c, d, e, f)
1×3×2 Array{Int64, 3}:
[:, :, 1] =
 1  2  3

[:, :, 2] =
 4  5  6</code></pre><p><strong>Examples for construction of the arguments</strong></p><pre><code class="nohighlight hljs">[a b c ; d e f ;;;
 g h i ; j k l ;;;
 m n o ; p q r ;;;
 s t u ; v w x]
⇒ dims = (2, 3, 4)

[a b ; c ;;; d ;;;;]
 ___   _     _
 2     1     1 = elements in each row (2, 1, 1)
 _______     _
 3           1 = elements in each column (3, 1)
 _____________
 4             = elements in each 3d slice (4,)
 _____________
 4             = elements in each 4d slice (4,)
⇒ shape = ((2, 1, 1), (3, 1), (4,), (4,)) with `row_first` = true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L2274-L2358">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.stack" href="#Base.stack"><code>Base.stack</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">stack(iter; [dims])</code></pre><p>Combine a collection of arrays (or other iterable objects) of equal size into one larger array, by arranging them along one or more new dimensions.</p><p>By default the axes of the elements are placed first, giving <code>size(result) = (size(first(iter))..., size(iter)...)</code>. This has the same order of elements as <a href="iterators.html#Base.Iterators.flatten"><code>Iterators.flatten</code></a><code>(iter)</code>.</p><p>With keyword <code>dims::Integer</code>, instead the <code>i</code>th element of <code>iter</code> becomes the slice <a href="arrays.html#Base.selectdim"><code>selectdim</code></a><code>(result, dims, i)</code>, so that <code>size(result, dims) == length(iter)</code>. In this case <code>stack</code> reverses the action of <a href="arrays.html#Base.eachslice"><code>eachslice</code></a> with the same <code>dims</code>.</p><p>The various <a href="arrays.html#Base.cat"><code>cat</code></a> functions also combine arrays. However, these all extend the arrays&#39; existing (possibly trivial) dimensions, rather than placing the arrays along new dimensions. They also accept arrays as separate arguments, rather than a single collection.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p>This function requires at least Julia 1.9.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; vecs = (1:2, [30, 40], Float32[500, 600]);

julia&gt; mat = stack(vecs)
2×3 Matrix{Float32}:
 1.0  30.0  500.0
 2.0  40.0  600.0

julia&gt; mat == hcat(vecs...) == reduce(hcat, collect(vecs))
true

julia&gt; vec(mat) == vcat(vecs...) == reduce(vcat, collect(vecs))
true

julia&gt; stack(zip(1:4, 10:99))  # accepts any iterators of iterators
2×4 Matrix{Int64}:
  1   2   3   4
 10  11  12  13

julia&gt; vec(ans) == collect(Iterators.flatten(zip(1:4, 10:99)))
true

julia&gt; stack(vecs; dims=1)  # unlike any cat function, 1st axis of vecs[1] is 2nd axis of result
3×2 Matrix{Float32}:
   1.0    2.0
  30.0   40.0
 500.0  600.0

julia&gt; x = rand(3,4);

julia&gt; x == stack(eachcol(x)) == stack(eachrow(x), dims=1)  # inverse of eachslice
true</code></pre><p>Higher-dimensional examples:</p><pre><code class="language-julia-repl hljs">julia&gt; A = rand(5, 7, 11);

julia&gt; E = eachslice(A, dims=2);  # a vector of matrices

julia&gt; (element = size(first(E)), container = size(E))
(element = (5, 11), container = (7,))

julia&gt; stack(E) |&gt; size
(5, 11, 7)

julia&gt; stack(E) == stack(E; dims=3) == cat(E...; dims=3)
true

julia&gt; A == stack(E; dims=2)
true

julia&gt; M = (fill(10i+j, 2, 3) for i in 1:5, j in 1:7);

julia&gt; (element = size(first(M)), container = size(M))
(element = (2, 3), container = (5, 7))

julia&gt; stack(M) |&gt; size  # keeps all dimensions
(2, 3, 5, 7)

julia&gt; stack(M; dims=1) |&gt; size  # vec(container) along dims=1
(35, 2, 3)

julia&gt; hvcat(5, M...) |&gt; size  # hvcat puts matrices next to each other
(14, 15)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L2767-L2857">source</a></section><section><div><pre><code class="language-julia hljs">stack(f, args...; [dims])</code></pre><p>Apply a function to each element of a collection, and <code>stack</code> the result. Or to several collections, <a href="iterators.html#Base.Iterators.zip"><code>zip</code></a>ped together.</p><p>The function should return arrays (or tuples, or other iterators) all of the same size. These become slices of the result, each separated along <code>dims</code> (if given) or by default along the last dimensions.</p><p>See also <a href="arrays.html#Base.mapslices"><code>mapslices</code></a>, <a href="arrays.html#Base.eachcol"><code>eachcol</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; stack(c -&gt; (c, c-32), &quot;julia&quot;)
2×5 Matrix{Char}:
 &#39;j&#39;  &#39;u&#39;  &#39;l&#39;  &#39;i&#39;  &#39;a&#39;
 &#39;J&#39;  &#39;U&#39;  &#39;L&#39;  &#39;I&#39;  &#39;A&#39;

julia&gt; stack(eachrow([1 2 3; 4 5 6]), (10, 100); dims=1) do row, n
         vcat(row, row .* n, row ./ n)
       end
2×9 Matrix{Float64}:
 1.0  2.0  3.0   10.0   20.0   30.0  0.1   0.2   0.3
 4.0  5.0  6.0  400.0  500.0  600.0  0.04  0.05  0.06</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L2860-L2886">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.vect" href="#Base.vect"><code>Base.vect</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">vect(X...)</code></pre><p>Create a <a href="arrays.html#Base.Vector"><code>Vector</code></a> with element type computed from the <code>promote_typeof</code> of the argument, containing the argument list.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = Base.vect(UInt8(1), 2.5, 1//2)
3-element Vector{Float64}:
 1.0
 2.5
 0.5</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L168-L182">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.circshift" href="#Base.circshift"><code>Base.circshift</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">circshift(A, shifts)</code></pre><p>Circularly shift, i.e. rotate, the data in an array. The second argument is a tuple or vector giving the amount to shift in each dimension, or an integer to shift only in the first dimension.</p><p>See also: <a href="arrays.html#Base.circshift!"><code>circshift!</code></a>, <a href="arrays.html#Base.circcopy!"><code>circcopy!</code></a>, <a href="math.html#Base.bitrotate"><code>bitrotate</code></a>, <a href="math.html#Base.:&lt;&lt;"><code>&lt;&lt;</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; b = reshape(Vector(1:16), (4,4))
4×4 Matrix{Int64}:
 1  5   9  13
 2  6  10  14
 3  7  11  15
 4  8  12  16

julia&gt; circshift(b, (0,2))
4×4 Matrix{Int64}:
  9  13  1  5
 10  14  2  6
 11  15  3  7
 12  16  4  8

julia&gt; circshift(b, (-1,0))
4×4 Matrix{Int64}:
 2  6  10  14
 3  7  11  15
 4  8  12  16
 1  5   9  13

julia&gt; a = BitArray([true, true, false, false, true])
5-element BitVector:
 1
 1
 0
 0
 1

julia&gt; circshift(a, 1)
5-element BitVector:
 1
 1
 1
 0
 0

julia&gt; circshift(a, -1)
5-element BitVector:
 1
 0
 0
 1
 1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarraymath.jl#L265-L321">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.circshift!" href="#Base.circshift!"><code>Base.circshift!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">circshift!(a::AbstractVector, shift::Integer)</code></pre><p>Circularly shift, or rotate, the data in vector <code>a</code> by <code>shift</code> positions.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; circshift!([1, 2, 3, 4, 5], 2)
5-element Vector{Int64}:
 4
 5
 1
 2
 3

julia&gt; circshift!([1, 2, 3, 4, 5], -2)
5-element Vector{Int64}:
 3
 4
 5
 1
 2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L3657-L3681">source</a></section><section><div><pre><code class="language-julia hljs">circshift!(dest, src, shifts)</code></pre><p>Circularly shift, i.e. rotate, the data in <code>src</code>, storing the result in <code>dest</code>. <code>shifts</code> specifies the amount to shift in each dimension.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p>See also <a href="arrays.html#Base.circshift"><code>circshift</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multidimensional.jl#L1206-L1215">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.circcopy!" href="#Base.circcopy!"><code>Base.circcopy!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">circcopy!(dest, src)</code></pre><p>Copy <code>src</code> to <code>dest</code>, indexing each dimension modulo its length. <code>src</code> and <code>dest</code> must have the same size, but can be offset in their indices; any offset results in a (circular) wraparound. If the arrays have overlapping indices, then on the domain of the overlap <code>dest</code> agrees with <code>src</code>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p>See also: <a href="arrays.html#Base.circshift"><code>circshift</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; src = reshape(Vector(1:16), (4,4))
4×4 Array{Int64,2}:
 1  5   9  13
 2  6  10  14
 3  7  11  15
 4  8  12  16

julia&gt; dest = OffsetArray{Int}(undef, (0:3,2:5))

julia&gt; circcopy!(dest, src)
OffsetArrays.OffsetArray{Int64,2,Array{Int64,2}} with indices 0:3×2:5:
 8  12  16  4
 5   9  13  1
 6  10  14  2
 7  11  15  3

julia&gt; dest[1:3,2:4] == src[1:3,2:4]
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multidimensional.jl#L1261-L1295">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findall-Tuple{Any}" href="#Base.findall-Tuple{Any}"><code>Base.findall</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findall(A)</code></pre><p>Return a vector <code>I</code> of the <code>true</code> indices or keys of <code>A</code>. If there are no such elements of <code>A</code>, return an empty array. To search for other kinds of values, pass a predicate as the first argument.</p><p>Indices or keys are of the same type as those returned by <a href="arrays.html#Base.keys-Tuple{AbstractArray}"><code>keys(A)</code></a> and <a href="collections.html#Base.pairs"><code>pairs(A)</code></a>.</p><p>See also: <a href="arrays.html#Base.findfirst-Tuple{Any}"><code>findfirst</code></a>, <a href="sort.html#Base.Sort.searchsorted"><code>searchsorted</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [true, false, false, true]
4-element Vector{Bool}:
 1
 0
 0
 1

julia&gt; findall(A)
2-element Vector{Int64}:
 1
 4

julia&gt; A = [true false; false true]
2×2 Matrix{Bool}:
 1  0
 0  1

julia&gt; findall(A)
2-element Vector{CartesianIndex{2}}:
 CartesianIndex(1, 1)
 CartesianIndex(2, 2)

julia&gt; findall(falses(3))
Int64[]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L2651-L2690">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findall-Tuple{Function, Any}" href="#Base.findall-Tuple{Function, Any}"><code>Base.findall</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findall(f::Function, A)</code></pre><p>Return a vector <code>I</code> of the indices or keys of <code>A</code> where <code>f(A[I])</code> returns <code>true</code>. If there are no such elements of <code>A</code>, return an empty array.</p><p>Indices or keys are of the same type as those returned by <a href="arrays.html#Base.keys-Tuple{AbstractArray}"><code>keys(A)</code></a> and <a href="collections.html#Base.pairs"><code>pairs(A)</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = [1, 3, 4]
3-element Vector{Int64}:
 1
 3
 4

julia&gt; findall(isodd, x)
2-element Vector{Int64}:
 1
 2

julia&gt; A = [1 2 0; 3 4 0]
2×3 Matrix{Int64}:
 1  2  0
 3  4  0
julia&gt; findall(isodd, A)
2-element Vector{CartesianIndex{2}}:
 CartesianIndex(1, 1)
 CartesianIndex(2, 1)

julia&gt; findall(!iszero, A)
4-element Vector{CartesianIndex{2}}:
 CartesianIndex(1, 1)
 CartesianIndex(2, 1)
 CartesianIndex(1, 2)
 CartesianIndex(2, 2)

julia&gt; d = Dict(:A =&gt; 10, :B =&gt; -1, :C =&gt; 0)
Dict{Symbol, Int64} with 3 entries:
  :A =&gt; 10
  :B =&gt; -1
  :C =&gt; 0

julia&gt; findall(x -&gt; x &gt;= 0, d)
2-element Vector{Symbol}:
 :A
 :C
</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L2591-L2641">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findfirst-Tuple{Any}" href="#Base.findfirst-Tuple{Any}"><code>Base.findfirst</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findfirst(A)</code></pre><p>Return the index or key of the first <code>true</code> value in <code>A</code>. Return <code>nothing</code> if no such value is found. To search for other kinds of values, pass a predicate as the first argument.</p><p>Indices or keys are of the same type as those returned by <a href="arrays.html#Base.keys-Tuple{AbstractArray}"><code>keys(A)</code></a> and <a href="collections.html#Base.pairs"><code>pairs(A)</code></a>.</p><p>See also: <a href="arrays.html#Base.findall-Tuple{Any}"><code>findall</code></a>, <a href="arrays.html#Base.findnext-Tuple{Any, Integer}"><code>findnext</code></a>, <a href="arrays.html#Base.findlast-Tuple{Any}"><code>findlast</code></a>, <a href="sort.html#Base.Sort.searchsortedfirst"><code>searchsortedfirst</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [false, false, true, false]
4-element Vector{Bool}:
 0
 0
 1
 0

julia&gt; findfirst(A)
3

julia&gt; findfirst(falses(3)) # returns nothing, but not printed in the REPL

julia&gt; A = [false false; true false]
2×2 Matrix{Bool}:
 0  0
 1  0

julia&gt; findfirst(A)
CartesianIndex(2, 1)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L2279-L2313">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findfirst-Tuple{Function, Any}" href="#Base.findfirst-Tuple{Function, Any}"><code>Base.findfirst</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findfirst(predicate::Function, A)</code></pre><p>Return the index or key of the first element of <code>A</code> for which <code>predicate</code> returns <code>true</code>. Return <code>nothing</code> if there is no such element.</p><p>Indices or keys are of the same type as those returned by <a href="arrays.html#Base.keys-Tuple{AbstractArray}"><code>keys(A)</code></a> and <a href="collections.html#Base.pairs"><code>pairs(A)</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1, 4, 2, 2]
4-element Vector{Int64}:
 1
 4
 2
 2

julia&gt; findfirst(iseven, A)
2

julia&gt; findfirst(x -&gt; x&gt;10, A) # returns nothing, but not printed in the REPL

julia&gt; findfirst(isequal(4), A)
2

julia&gt; A = [1 4; 2 2]
2×2 Matrix{Int64}:
 1  4
 2  2

julia&gt; findfirst(iseven, A)
CartesianIndex(2, 1)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L2361-L2395">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findlast-Tuple{Any}" href="#Base.findlast-Tuple{Any}"><code>Base.findlast</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findlast(A)</code></pre><p>Return the index or key of the last <code>true</code> value in <code>A</code>. Return <code>nothing</code> if there is no <code>true</code> value in <code>A</code>.</p><p>Indices or keys are of the same type as those returned by <a href="arrays.html#Base.keys-Tuple{AbstractArray}"><code>keys(A)</code></a> and <a href="collections.html#Base.pairs"><code>pairs(A)</code></a>.</p><p>See also: <a href="arrays.html#Base.findfirst-Tuple{Any}"><code>findfirst</code></a>, <a href="arrays.html#Base.findprev-Tuple{Any, Integer}"><code>findprev</code></a>, <a href="arrays.html#Base.findall-Tuple{Any}"><code>findall</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [true, false, true, false]
4-element Vector{Bool}:
 1
 0
 1
 0

julia&gt; findlast(A)
3

julia&gt; A = falses(2,2);

julia&gt; findlast(A) # returns nothing, but not printed in the REPL

julia&gt; A = [true false; true false]
2×2 Matrix{Bool}:
 1  0
 1  0

julia&gt; findlast(A)
CartesianIndex(2, 1)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L2457-L2492">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findlast-Tuple{Function, Any}" href="#Base.findlast-Tuple{Function, Any}"><code>Base.findlast</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findlast(predicate::Function, A)</code></pre><p>Return the index or key of the last element of <code>A</code> for which <code>predicate</code> returns <code>true</code>. Return <code>nothing</code> if there is no such element.</p><p>Indices or keys are of the same type as those returned by <a href="arrays.html#Base.keys-Tuple{AbstractArray}"><code>keys(A)</code></a> and <a href="collections.html#Base.pairs"><code>pairs(A)</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1, 2, 3, 4]
4-element Vector{Int64}:
 1
 2
 3
 4

julia&gt; findlast(isodd, A)
3

julia&gt; findlast(x -&gt; x &gt; 5, A) # returns nothing, but not printed in the REPL

julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; findlast(isodd, A)
CartesianIndex(2, 1)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L2548-L2579">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findnext-Tuple{Any, Integer}" href="#Base.findnext-Tuple{Any, Integer}"><code>Base.findnext</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findnext(A, i)</code></pre><p>Find the next index after or including <code>i</code> of a <code>true</code> element of <code>A</code>, or <code>nothing</code> if not found.</p><p>Indices are of the same type as those returned by <a href="arrays.html#Base.keys-Tuple{AbstractArray}"><code>keys(A)</code></a> and <a href="collections.html#Base.pairs"><code>pairs(A)</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [false, false, true, false]
4-element Vector{Bool}:
 0
 0
 1
 0

julia&gt; findnext(A, 1)
3

julia&gt; findnext(A, 4) # returns nothing, but not printed in the REPL

julia&gt; A = [false false; true false]
2×2 Matrix{Bool}:
 0  0
 1  0

julia&gt; findnext(A, CartesianIndex(1, 1))
CartesianIndex(2, 1)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L2245-L2276">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findnext-Tuple{Function, Any, Integer}" href="#Base.findnext-Tuple{Function, Any, Integer}"><code>Base.findnext</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findnext(predicate::Function, A, i)</code></pre><p>Find the next index after or including <code>i</code> of an element of <code>A</code> for which <code>predicate</code> returns <code>true</code>, or <code>nothing</code> if not found. This works for Arrays, Strings, and most other collections that support <a href="collections.html#Base.getindex"><code>getindex</code></a>, <a href="arrays.html#Base.keys-Tuple{AbstractArray}"><code>keys(A)</code></a>, and <a href="arrays.html#Base.nextind"><code>nextind</code></a>.</p><p>Indices are of the same type as those returned by <a href="arrays.html#Base.keys-Tuple{AbstractArray}"><code>keys(A)</code></a> and <a href="collections.html#Base.pairs"><code>pairs(A)</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1, 4, 2, 2];

julia&gt; findnext(isodd, A, 1)
1

julia&gt; findnext(isodd, A, 2) # returns nothing, but not printed in the REPL

julia&gt; A = [1 4; 2 2];

julia&gt; findnext(isodd, A, CartesianIndex(1, 1))
CartesianIndex(1, 1)

julia&gt; findnext(isspace, &quot;a b c&quot;, 3)
4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L2319-L2347">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findprev-Tuple{Any, Integer}" href="#Base.findprev-Tuple{Any, Integer}"><code>Base.findprev</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findprev(A, i)</code></pre><p>Find the previous index before or including <code>i</code> of a <code>true</code> element of <code>A</code>, or <code>nothing</code> if not found.</p><p>Indices are of the same type as those returned by <a href="arrays.html#Base.keys-Tuple{AbstractArray}"><code>keys(A)</code></a> and <a href="collections.html#Base.pairs"><code>pairs(A)</code></a>.</p><p>See also: <a href="arrays.html#Base.findnext-Tuple{Any, Integer}"><code>findnext</code></a>, <a href="arrays.html#Base.findfirst-Tuple{Any}"><code>findfirst</code></a>, <a href="arrays.html#Base.findall-Tuple{Any}"><code>findall</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [false, false, true, true]
4-element Vector{Bool}:
 0
 0
 1
 1

julia&gt; findprev(A, 3)
3

julia&gt; findprev(A, 1) # returns nothing, but not printed in the REPL

julia&gt; A = [false false; true true]
2×2 Matrix{Bool}:
 0  0
 1  1

julia&gt; findprev(A, CartesianIndex(2, 1))
CartesianIndex(2, 1)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L2421-L2454">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findprev-Tuple{Function, Any, Integer}" href="#Base.findprev-Tuple{Function, Any, Integer}"><code>Base.findprev</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findprev(predicate::Function, A, i)</code></pre><p>Find the previous index before or including <code>i</code> of an element of <code>A</code> for which <code>predicate</code> returns <code>true</code>, or <code>nothing</code> if not found. This works for Arrays, Strings, and most other collections that support <a href="collections.html#Base.getindex"><code>getindex</code></a>, <a href="arrays.html#Base.keys-Tuple{AbstractArray}"><code>keys(A)</code></a>, and <a href="arrays.html#Base.nextind"><code>nextind</code></a>.</p><p>Indices are of the same type as those returned by <a href="arrays.html#Base.keys-Tuple{AbstractArray}"><code>keys(A)</code></a> and <a href="collections.html#Base.pairs"><code>pairs(A)</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [4, 6, 1, 2]
4-element Vector{Int64}:
 4
 6
 1
 2

julia&gt; findprev(isodd, A, 1) # returns nothing, but not printed in the REPL

julia&gt; findprev(isodd, A, 3)
3

julia&gt; A = [4 6; 1 2]
2×2 Matrix{Int64}:
 4  6
 1  2

julia&gt; findprev(isodd, A, CartesianIndex(1, 2))
CartesianIndex(2, 1)

julia&gt; findprev(isspace, &quot;a b c&quot;, 3)
2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L2498-L2534">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.permutedims" href="#Base.permutedims"><code>Base.permutedims</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">permutedims(A::AbstractArray, perm)
permutedims(A::AbstractMatrix)</code></pre><p>Permute the dimensions (axes) of array <code>A</code>. <code>perm</code> is a tuple or vector of <code>ndims(A)</code> integers specifying the permutation.</p><p>If <code>A</code> is a 2d array (<a href="arrays.html#Base.AbstractMatrix"><code>AbstractMatrix</code></a>), then <code>perm</code> defaults to <code>(2,1)</code>, swapping the two axes of <code>A</code> (the rows and columns of the matrix).   This differs from <a href="../stdlib/LinearAlgebra.html#Base.transpose"><code>transpose</code></a> in that the operation is not recursive, which is especially useful for arrays of non-numeric values (where the recursive <code>transpose</code> would throw an error) and/or 2d arrays that do not represent linear operators.</p><p>For 1d arrays, see <a href="arrays.html#Base.permutedims"><code>permutedims(v::AbstractVector)</code></a>, which returns a 1-row “matrix”.</p><p>See also <a href="arrays.html#Base.permutedims!"><code>permutedims!</code></a>, <a href="arrays.html#Base.PermutedDimsArrays.PermutedDimsArray"><code>PermutedDimsArray</code></a>, <a href="../stdlib/LinearAlgebra.html#Base.transpose"><code>transpose</code></a>, <a href="arrays.html#Base.invperm"><code>invperm</code></a>.</p><p><strong>Examples</strong></p><p><strong>2d arrays:</strong></p><p>Unlike <code>transpose</code>, <code>permutedims</code> can be used to swap rows and columns of 2d arrays of arbitrary non-numeric elements, such as strings:</p><pre><code class="language-julia-repl hljs">julia&gt; A = [&quot;a&quot; &quot;b&quot; &quot;c&quot;
            &quot;d&quot; &quot;e&quot; &quot;f&quot;]
2×3 Matrix{String}:
 &quot;a&quot;  &quot;b&quot;  &quot;c&quot;
 &quot;d&quot;  &quot;e&quot;  &quot;f&quot;

julia&gt; permutedims(A)
3×2 Matrix{String}:
 &quot;a&quot;  &quot;d&quot;
 &quot;b&quot;  &quot;e&quot;
 &quot;c&quot;  &quot;f&quot;</code></pre><p>And <code>permutedims</code> produces results that differ from <code>transpose</code> for matrices whose elements are themselves numeric matrices:</p><pre><code class="language-julia-repl hljs">julia&gt; a = [1 2; 3 4];

julia&gt; b = [5 6; 7 8];

julia&gt; c = [9 10; 11 12];

julia&gt; d = [13 14; 15 16];

julia&gt; X = [[a] [b]; [c] [d]]
2×2 Matrix{Matrix{Int64}}:
 [1 2; 3 4]     [5 6; 7 8]
 [9 10; 11 12]  [13 14; 15 16]

julia&gt; permutedims(X)
2×2 Matrix{Matrix{Int64}}:
 [1 2; 3 4]  [9 10; 11 12]
 [5 6; 7 8]  [13 14; 15 16]

julia&gt; transpose(X)
2×2 transpose(::Matrix{Matrix{Int64}}) with eltype Transpose{Int64, Matrix{Int64}}:
 [1 3; 2 4]  [9 11; 10 12]
 [5 7; 6 8]  [13 15; 14 16]</code></pre><p><strong>Multi-dimensional arrays</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = reshape(Vector(1:8), (2,2,2))
2×2×2 Array{Int64, 3}:
[:, :, 1] =
 1  3
 2  4

[:, :, 2] =
 5  7
 6  8

julia&gt; perm = (3, 1, 2); # put the last dimension first

julia&gt; B = permutedims(A, perm)
2×2×2 Array{Int64, 3}:
[:, :, 1] =
 1  2
 5  6

[:, :, 2] =
 3  4
 7  8

julia&gt; A == permutedims(B, invperm(perm)) # the inverse permutation
true</code></pre><p>For each dimension <code>i</code> of <code>B = permutedims(A, perm)</code>, its corresponding dimension of <code>A</code> will be <code>perm[i]</code>. This means the equality <code>size(B, i) == size(A, perm[i])</code> holds.</p><pre><code class="language-julia-repl hljs">julia&gt; A = randn(5, 7, 11, 13);

julia&gt; perm = [4, 1, 3, 2];

julia&gt; B = permutedims(A, perm);

julia&gt; size(B)
(13, 5, 11, 7)

julia&gt; size(A)[perm] == ans
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/permuteddimsarray.jl#L88-L195">source</a></section><section><div><pre><code class="language-julia hljs">permutedims(v::AbstractVector)</code></pre><p>Reshape vector <code>v</code> into a <code>1 × length(v)</code> row matrix. Differs from <a href="../stdlib/LinearAlgebra.html#Base.transpose"><code>transpose</code></a> in that the operation is not recursive, which is especially useful for arrays of non-numeric values (where the recursive <code>transpose</code> might throw an error).</p><p><strong>Examples</strong></p><p>Unlike <code>transpose</code>, <code>permutedims</code> can be used on vectors of arbitrary non-numeric elements, such as strings:</p><pre><code class="language-julia-repl hljs">julia&gt; permutedims([&quot;a&quot;, &quot;b&quot;, &quot;c&quot;])
1×3 Matrix{String}:
 &quot;a&quot;  &quot;b&quot;  &quot;c&quot;</code></pre><p>For vectors of numbers, <code>permutedims(v)</code> works much like <code>transpose(v)</code> except that the return type differs (it uses <a href="arrays.html#Base.reshape"><code>reshape</code></a> rather than a <code>LinearAlgebra.Transpose</code> view, though both share memory with the original array <code>v</code>):</p><pre><code class="language-julia-repl hljs">julia&gt; v = [1, 2, 3, 4]
4-element Vector{Int64}:
 1
 2
 3
 4

julia&gt; p = permutedims(v)
1×4 Matrix{Int64}:
 1  2  3  4

julia&gt; r = transpose(v)
1×4 transpose(::Vector{Int64}) with eltype Int64:
 1  2  3  4

julia&gt; p == r
true

julia&gt; typeof(r)
Transpose{Int64, Vector{Int64}}

julia&gt; p[1] = 5; r[2] = 6; # mutating p or r also changes v

julia&gt; v # shares memory with both p and r
4-element Vector{Int64}:
 5
 6
 3
 4</code></pre><p>However, <code>permutedims</code> produces results that differ from <code>transpose</code> for vectors whose elements are themselves numeric matrices:</p><pre><code class="language-julia-repl hljs">julia&gt; V = [[[1 2; 3 4]]; [[5 6; 7 8]]]
2-element Vector{Matrix{Int64}}:
 [1 2; 3 4]
 [5 6; 7 8]

julia&gt; permutedims(V)
1×2 Matrix{Matrix{Int64}}:
 [1 2; 3 4]  [5 6; 7 8]

julia&gt; transpose(V)
1×2 transpose(::Vector{Matrix{Int64}}) with eltype Transpose{Int64, Matrix{Int64}}:
 [1 3; 2 4]  [5 7; 6 8]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/permuteddimsarray.jl#L203-L270">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.permutedims!" href="#Base.permutedims!"><code>Base.permutedims!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">permutedims!(dest, src, perm)</code></pre><p>Permute the dimensions of array <code>src</code> and store the result in the array <code>dest</code>. <code>perm</code> is a vector specifying a permutation of length <code>ndims(src)</code>. The preallocated array <code>dest</code> should have <code>size(dest) == size(src)[perm]</code> and is completely overwritten. No in-place permutation is supported and unexpected results will happen if <code>src</code> and <code>dest</code> have overlapping memory regions.</p><p>See also <a href="arrays.html#Base.permutedims"><code>permutedims</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/permuteddimsarray.jl#L273-L283">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.PermutedDimsArrays.PermutedDimsArray" href="#Base.PermutedDimsArrays.PermutedDimsArray"><code>Base.PermutedDimsArrays.PermutedDimsArray</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">PermutedDimsArray(A, perm) -&gt; B</code></pre><p>Given an AbstractArray <code>A</code>, create a view <code>B</code> such that the dimensions appear to be permuted. Similar to <code>permutedims</code>, except that no copying occurs (<code>B</code> shares storage with <code>A</code>).</p><p>See also <a href="arrays.html#Base.permutedims"><code>permutedims</code></a>, <a href="arrays.html#Base.invperm"><code>invperm</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = rand(3,5,4);

julia&gt; B = PermutedDimsArray(A, (3,1,2));

julia&gt; size(B)
(4, 3, 5)

julia&gt; B[3,1,2] == A[1,2,3]
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/permuteddimsarray.jl#L20-L41">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.promote_shape" href="#Base.promote_shape"><code>Base.promote_shape</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">promote_shape(s1, s2)</code></pre><p>Check two array shapes for compatibility, allowing trailing singleton dimensions, and return whichever shape has more dimensions.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = fill(1, (3,4,1,1,1));

julia&gt; b = fill(1, (3,4));

julia&gt; promote_shape(a,b)
(Base.OneTo(3), Base.OneTo(4), Base.OneTo(1), Base.OneTo(1), Base.OneTo(1))

julia&gt; promote_shape((2,3,1,4), (2, 3, 1, 4, 1))
(2, 3, 1, 4, 1)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/indices.jl#L155-L173">source</a></section></article><h2 id="Array-functions"><a class="docs-heading-anchor" href="#Array-functions">Array functions</a><a id="Array-functions-1"></a><a class="docs-heading-anchor-permalink" href="#Array-functions" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.accumulate" href="#Base.accumulate"><code>Base.accumulate</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">accumulate(op, A; dims::Integer, [init])</code></pre><p>Cumulative operation <code>op</code> along the dimension <code>dims</code> of <code>A</code> (providing <code>dims</code> is optional for vectors). An initial value <code>init</code> may optionally be provided by a keyword argument. See also <a href="arrays.html#Base.accumulate!"><code>accumulate!</code></a> to use a preallocated output array, both for performance and to control the precision of the output (e.g. to avoid overflow).</p><p>For common operations there are specialized variants of <code>accumulate</code>, see <a href="arrays.html#Base.cumsum"><code>cumsum</code></a>, <a href="arrays.html#Base.cumprod"><code>cumprod</code></a>. For a lazy version, see <a href="iterators.html#Base.Iterators.accumulate"><code>Iterators.accumulate</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p><code>accumulate</code> on a non-array iterator requires at least Julia 1.5.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; accumulate(+, [1,2,3])
3-element Vector{Int64}:
 1
 3
 6

julia&gt; accumulate(min, (1, -2, 3, -4, 5), init=0)
(0, -2, -2, -4, -4)

julia&gt; accumulate(/, (2, 4, Inf), init=100)
(50.0, 12.5, 0.0)

julia&gt; accumulate(=&gt;, i^2 for i in 1:3)
3-element Vector{Any}:
          1
        1 =&gt; 4
 (1 =&gt; 4) =&gt; 9

julia&gt; accumulate(+, fill(1, 3, 4))
3×4 Matrix{Int64}:
 1  4  7  10
 2  5  8  11
 3  6  9  12

julia&gt; accumulate(+, fill(1, 2, 5), dims=2, init=100.0)
2×5 Matrix{Float64}:
 101.0  102.0  103.0  104.0  105.0
 101.0  102.0  103.0  104.0  105.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/accumulate.jl#L231-L277">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.accumulate!" href="#Base.accumulate!"><code>Base.accumulate!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">accumulate!(op, B, A; [dims], [init])</code></pre><p>Cumulative operation <code>op</code> on <code>A</code> along the dimension <code>dims</code>, storing the result in <code>B</code>. Providing <code>dims</code> is optional for vectors.  If the keyword argument <code>init</code> is given, its value is used to instantiate the accumulation.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p>See also <a href="arrays.html#Base.accumulate"><code>accumulate</code></a>, <a href="arrays.html#Base.cumsum!"><code>cumsum!</code></a>, <a href="arrays.html#Base.cumprod!"><code>cumprod!</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = [1, 0, 2, 0, 3];

julia&gt; y = rand(5);

julia&gt; accumulate!(+, y, x);

julia&gt; y
5-element Vector{Float64}:
 1.0
 1.0
 3.0
 3.0
 6.0

julia&gt; A = [1 2 3; 4 5 6];

julia&gt; B = similar(A);

julia&gt; accumulate!(-, B, A, dims=1)
2×3 Matrix{Int64}:
  1   2   3
 -3  -3  -3

julia&gt; accumulate!(*, B, A, dims=2, init=10)
2×3 Matrix{Int64}:
 10   20    60
 40  200  1200</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/accumulate.jl#L303-L344">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.cumprod" href="#Base.cumprod"><code>Base.cumprod</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cumprod(A; dims::Integer)</code></pre><p>Cumulative product along the dimension <code>dim</code>. See also <a href="arrays.html#Base.cumprod!"><code>cumprod!</code></a> to use a preallocated output array, both for performance and to control the precision of the output (e.g. to avoid overflow).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = Int8[1 2 3; 4 5 6];

julia&gt; cumprod(a, dims=1)
2×3 Matrix{Int64}:
 1   2   3
 4  10  18

julia&gt; cumprod(a, dims=2)
2×3 Matrix{Int64}:
 1   2    6
 4  20  120</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/accumulate.jl#L171-L192">source</a></section><section><div><pre><code class="language-julia hljs">cumprod(itr)</code></pre><p>Cumulative product of an iterator.</p><p>See also <a href="arrays.html#Base.cumprod!"><code>cumprod!</code></a>, <a href="arrays.html#Base.accumulate"><code>accumulate</code></a>, <a href="arrays.html#Base.cumsum"><code>cumsum</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p><code>cumprod</code> on a non-array iterator requires at least Julia 1.5.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; cumprod(fill(1//2, 3))
3-element Vector{Rational{Int64}}:
 1//2
 1//4
 1//8

julia&gt; cumprod((1, 2, 1, 3, 1))
(1, 2, 2, 6, 6)

julia&gt; cumprod(&quot;julia&quot;)
5-element Vector{String}:
 &quot;j&quot;
 &quot;ju&quot;
 &quot;jul&quot;
 &quot;juli&quot;
 &quot;julia&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/accumulate.jl#L197-L226">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.cumprod!" href="#Base.cumprod!"><code>Base.cumprod!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cumprod!(B, A; dims::Integer)</code></pre><p>Cumulative product of <code>A</code> along the dimension <code>dims</code>, storing the result in <code>B</code>. See also <a href="arrays.html#Base.cumprod"><code>cumprod</code></a>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/accumulate.jl#L150-L157">source</a></section><section><div><pre><code class="language-julia hljs">cumprod!(y::AbstractVector, x::AbstractVector)</code></pre><p>Cumulative product of a vector <code>x</code>, storing the result in <code>y</code>. See also <a href="arrays.html#Base.cumprod"><code>cumprod</code></a>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/accumulate.jl#L161-L168">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.cumsum" href="#Base.cumsum"><code>Base.cumsum</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cumsum(A; dims::Integer)</code></pre><p>Cumulative sum along the dimension <code>dims</code>. See also <a href="arrays.html#Base.cumsum!"><code>cumsum!</code></a> to use a preallocated output array, both for performance and to control the precision of the output (e.g. to avoid overflow).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [1 2 3; 4 5 6]
2×3 Matrix{Int64}:
 1  2  3
 4  5  6

julia&gt; cumsum(a, dims=1)
2×3 Matrix{Int64}:
 1  2  3
 5  7  9

julia&gt; cumsum(a, dims=2)
2×3 Matrix{Int64}:
 1  3   6
 4  9  15</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The return array&#39;s <code>eltype</code> is <code>Int</code> for signed integers of less than system word size  and <code>UInt</code> for unsigned integers of less than system word size. To preserve <code>eltype</code> of arrays with small signed or unsigned integer <code>accumulate(+, A)</code> should be used.</p><pre><code class="language-julia-repl hljs">julia&gt; cumsum(Int8[100, 28])
2-element Vector{Int64}:
 100
 128

julia&gt; accumulate(+,Int8[100, 28])
2-element Vector{Int8}:
  100
 -128</code></pre><p>In the former case, the integers are widened to system word size and therefore the result is <code>Int64[100, 128]</code>. In the latter case, no such widening happens and integer overflow results in <code>Int8[100, -128]</code>.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/accumulate.jl#L66-L112">source</a></section><section><div><pre><code class="language-julia hljs">cumsum(itr)</code></pre><p>Cumulative sum of an iterator.</p><p>See also <a href="arrays.html#Base.accumulate"><code>accumulate</code></a> to apply functions other than <code>+</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p><code>cumsum</code> on a non-array iterator requires at least Julia 1.5.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; cumsum(1:3)
3-element Vector{Int64}:
 1
 3
 6

julia&gt; cumsum((true, false, true, false, true))
(1, 1, 2, 2, 3)

julia&gt; cumsum(fill(1, 2) for i in 1:3)
3-element Vector{Vector{Int64}}:
 [1, 1]
 [2, 2]
 [3, 3]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/accumulate.jl#L118-L145">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.cumsum!" href="#Base.cumsum!"><code>Base.cumsum!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cumsum!(B, A; dims::Integer)</code></pre><p>Cumulative sum of <code>A</code> along the dimension <code>dims</code>, storing the result in <code>B</code>. See also <a href="arrays.html#Base.cumsum"><code>cumsum</code></a>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/accumulate.jl#L41-L47">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.diff" href="#Base.diff"><code>Base.diff</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">diff(A::AbstractVector)
diff(A::AbstractArray; dims::Integer)</code></pre><p>Finite difference operator on a vector or a multidimensional array <code>A</code>. In the latter case the dimension to operate on needs to be specified with the <code>dims</code> keyword argument.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p><code>diff</code> for arrays with dimension higher than 2 requires at least Julia 1.1.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [2 4; 6 16]
2×2 Matrix{Int64}:
 2   4
 6  16

julia&gt; diff(a, dims=2)
2×1 Matrix{Int64}:
  2
 10

julia&gt; diff(vec(a))
3-element Vector{Int64}:
  4
 -2
 12</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multidimensional.jl#L1002-L1031">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.repeat" href="#Base.repeat"><code>Base.repeat</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">repeat(A::AbstractArray, counts::Integer...)</code></pre><p>Construct an array by repeating array <code>A</code> a given number of times in each dimension, specified by <code>counts</code>.</p><p>See also: <a href="arrays.html#Base.fill"><code>fill</code></a>, <a href="iterators.html#Base.Iterators.repeated"><code>Iterators.repeated</code></a>, <a href="iterators.html#Base.Iterators.cycle"><code>Iterators.cycle</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; repeat([1, 2, 3], 2)
6-element Vector{Int64}:
 1
 2
 3
 1
 2
 3

julia&gt; repeat([1, 2, 3], 2, 3)
6×3 Matrix{Int64}:
 1  1  1
 2  2  2
 3  3  3
 1  1  1
 2  2  2
 3  3  3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarraymath.jl#L328-L355">source</a></section><section><div><pre><code class="language-julia hljs">repeat(A::AbstractArray; inner=ntuple(Returns(1), ndims(A)), outer=ntuple(Returns(1), ndims(A)))</code></pre><p>Construct an array by repeating the entries of <code>A</code>. The i-th element of <code>inner</code> specifies the number of times that the individual entries of the i-th dimension of <code>A</code> should be repeated. The i-th element of <code>outer</code> specifies the number of times that a slice along the i-th dimension of <code>A</code> should be repeated. If <code>inner</code> or <code>outer</code> are omitted, no repetition is performed.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; repeat(1:2, inner=2)
4-element Vector{Int64}:
 1
 1
 2
 2

julia&gt; repeat(1:2, outer=2)
4-element Vector{Int64}:
 1
 2
 1
 2

julia&gt; repeat([1 2; 3 4], inner=(2, 1), outer=(1, 3))
4×6 Matrix{Int64}:
 1  2  1  2  1  2
 1  2  1  2  1  2
 3  4  3  4  3  4
 3  4  3  4  3  4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarraymath.jl#L360-L392">source</a></section><section><div><pre><code class="language-julia hljs">repeat(s::AbstractString, r::Integer)</code></pre><p>Repeat a string <code>r</code> times. This can be written as <code>s^r</code>.</p><p>See also <a href="strings.html#Base.:^-Tuple{Union{AbstractChar, AbstractString}, Integer}"><code>^</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; repeat(&quot;ha&quot;, 3)
&quot;hahaha&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L751-L763">source</a></section><section><div><pre><code class="language-julia hljs">repeat(c::AbstractChar, r::Integer) -&gt; String</code></pre><p>Repeat a character <code>r</code> times. This can equivalently be accomplished by calling <a href="strings.html#Base.:^-Tuple{Union{AbstractChar, AbstractString}, Integer}"><code>c^r</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; repeat(&#39;A&#39;, 3)
&quot;AAA&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/string.jl#L560-L571">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.rot180" href="#Base.rot180"><code>Base.rot180</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rot180(A)</code></pre><p>Rotate matrix <code>A</code> 180 degrees.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; rot180(a)
2×2 Matrix{Int64}:
 4  3
 2  1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/arraymath.jl#L158-L175">source</a></section><section><div><pre><code class="language-julia hljs">rot180(A, k)</code></pre><p>Rotate matrix <code>A</code> 180 degrees an integer <code>k</code> number of times. If <code>k</code> is even, this is equivalent to a <code>copy</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; rot180(a,1)
2×2 Matrix{Int64}:
 4  3
 2  1

julia&gt; rot180(a,2)
2×2 Matrix{Int64}:
 1  2
 3  4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/arraymath.jl#L260-L283">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.rotl90" href="#Base.rotl90"><code>Base.rotl90</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rotl90(A)</code></pre><p>Rotate matrix <code>A</code> left 90 degrees.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; rotl90(a)
2×2 Matrix{Int64}:
 2  4
 1  3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/arraymath.jl#L103-L120">source</a></section><section><div><pre><code class="language-julia hljs">rotl90(A, k)</code></pre><p>Left-rotate matrix <code>A</code> 90 degrees counterclockwise an integer <code>k</code> number of times. If <code>k</code> is a multiple of four (including zero), this is equivalent to a <code>copy</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; rotl90(a,1)
2×2 Matrix{Int64}:
 2  4
 1  3

julia&gt; rotl90(a,2)
2×2 Matrix{Int64}:
 4  3
 2  1

julia&gt; rotl90(a,3)
2×2 Matrix{Int64}:
 3  1
 4  2

julia&gt; rotl90(a,4)
2×2 Matrix{Int64}:
 1  2
 3  4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/arraymath.jl#L185-L218">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.rotr90" href="#Base.rotr90"><code>Base.rotr90</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rotr90(A)</code></pre><p>Rotate matrix <code>A</code> right 90 degrees.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; rotr90(a)
2×2 Matrix{Int64}:
 3  1
 4  2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/arraymath.jl#L131-L148">source</a></section><section><div><pre><code class="language-julia hljs">rotr90(A, k)</code></pre><p>Right-rotate matrix <code>A</code> 90 degrees clockwise an integer <code>k</code> number of times. If <code>k</code> is a multiple of four (including zero), this is equivalent to a <code>copy</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; rotr90(a,1)
2×2 Matrix{Int64}:
 3  1
 4  2

julia&gt; rotr90(a,2)
2×2 Matrix{Int64}:
 4  3
 2  1

julia&gt; rotr90(a,3)
2×2 Matrix{Int64}:
 2  4
 1  3

julia&gt; rotr90(a,4)
2×2 Matrix{Int64}:
 1  2
 3  4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/arraymath.jl#L225-L258">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.mapslices" href="#Base.mapslices"><code>Base.mapslices</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mapslices(f, A; dims)</code></pre><p>Transform the given dimensions of array <code>A</code> by applying a function <code>f</code> on each slice of the form <code>A[..., :, ..., :, ...]</code>, with a colon at each <code>d</code> in <code>dims</code>. The results are concatenated along the remaining dimensions.</p><p>For example, if <code>dims = [1,2]</code> and <code>A</code> is 4-dimensional, then <code>f</code> is called on <code>x = A[:,:,i,j]</code> for all <code>i</code> and <code>j</code>, and <code>f(x)</code> becomes <code>R[:,:,i,j]</code> in the result <code>R</code>.</p><p>See also <a href="arrays.html#Base.eachcol"><code>eachcol</code></a> or <a href="arrays.html#Base.eachslice"><code>eachslice</code></a>, used with <a href="collections.html#Base.map"><code>map</code></a> or <a href="arrays.html#Base.stack"><code>stack</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = reshape(1:30,(2,5,3))
2×5×3 reshape(::UnitRange{Int64}, 2, 5, 3) with eltype Int64:
[:, :, 1] =
 1  3  5  7   9
 2  4  6  8  10

[:, :, 2] =
 11  13  15  17  19
 12  14  16  18  20

[:, :, 3] =
 21  23  25  27  29
 22  24  26  28  30

julia&gt; f(x::Matrix) = fill(x[1,1], 1,4);  # returns a 1×4 matrix

julia&gt; B = mapslices(f, A, dims=(1,2))
1×4×3 Array{Int64, 3}:
[:, :, 1] =
 1  1  1  1

[:, :, 2] =
 11  11  11  11

[:, :, 3] =
 21  21  21  21

julia&gt; f2(x::AbstractMatrix) = fill(x[1,1], 1,4);

julia&gt; B == stack(f2, eachslice(A, dims=3))
true

julia&gt; g(x) = x[begin] // x[end-1];  # returns a number

julia&gt; mapslices(g, A, dims=[1,3])
1×5×1 Array{Rational{Int64}, 3}:
[:, :, 1] =
 1//21  3//23  1//5  7//27  9//29

julia&gt; map(g, eachslice(A, dims=2))
5-element Vector{Rational{Int64}}:
 1//21
 3//23
 1//5
 7//27
 9//29

julia&gt; mapslices(sum, A; dims=(1,3)) == sum(A; dims=(1,3))
true</code></pre><p>Notice that in <code>eachslice(A; dims=2)</code>, the specified dimension is the one <em>without</em> a colon in the slice. This is <code>view(A,:,i,:)</code>, whereas <code>mapslices(f, A; dims=(1,3))</code> uses <code>A[:,i,:]</code>. The function <code>f</code> may mutate values in the slice without affecting <code>A</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L3195-L3264">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.eachrow" href="#Base.eachrow"><code>Base.eachrow</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">eachrow(A::AbstractVecOrMat) &lt;: AbstractVector</code></pre><p>Create a <a href="arrays.html#Base.RowSlices"><code>RowSlices</code></a> object that is a vector of rows of matrix or vector <code>A</code>. Row slices are returned as <code>AbstractVector</code> views of <code>A</code>.</p><p>For the inverse, see <a href="arrays.html#Base.stack"><code>stack</code></a><code>(rows; dims=1)</code>.</p><p>See also <a href="arrays.html#Base.eachcol"><code>eachcol</code></a>, <a href="arrays.html#Base.eachslice"><code>eachslice</code></a> and <a href="arrays.html#Base.mapslices"><code>mapslices</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p>This function requires at least Julia 1.1.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p>Prior to Julia 1.9, this returned an iterator.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; s = eachrow(a)
2-element RowSlices{Matrix{Int64}, Tuple{Base.OneTo{Int64}}, SubArray{Int64, 1, Matrix{Int64}, Tuple{Int64, Base.Slice{Base.OneTo{Int64}}}, true}}:
 [1, 2]
 [3, 4]

julia&gt; s[1]
2-element view(::Matrix{Int64}, 1, :) with eltype Int64:
 1
 2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/slicearray.jl#L131-L165">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.eachcol" href="#Base.eachcol"><code>Base.eachcol</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">eachcol(A::AbstractVecOrMat) &lt;: AbstractVector</code></pre><p>Create a <a href="arrays.html#Base.ColumnSlices"><code>ColumnSlices</code></a> object that is a vector of columns of matrix or vector <code>A</code>. Column slices are returned as <code>AbstractVector</code> views of <code>A</code>.</p><p>For the inverse, see <a href="arrays.html#Base.stack"><code>stack</code></a><code>(cols)</code> or <code>reduce(</code><a href="arrays.html#Base.hcat"><code>hcat</code></a><code>, cols)</code>.</p><p>See also <a href="arrays.html#Base.eachrow"><code>eachrow</code></a>, <a href="arrays.html#Base.eachslice"><code>eachslice</code></a> and <a href="arrays.html#Base.mapslices"><code>mapslices</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p>This function requires at least Julia 1.1.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p>Prior to Julia 1.9, this returned an iterator.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; s = eachcol(a)
2-element ColumnSlices{Matrix{Int64}, Tuple{Base.OneTo{Int64}}, SubArray{Int64, 1, Matrix{Int64}, Tuple{Base.Slice{Base.OneTo{Int64}}, Int64}, true}}:
 [1, 3]
 [2, 4]

julia&gt; s[1]
2-element view(::Matrix{Int64}, :, 1) with eltype Int64:
 1
 3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/slicearray.jl#L169-L203">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.eachslice" href="#Base.eachslice"><code>Base.eachslice</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">eachslice(A::AbstractArray; dims, drop=true)</code></pre><p>Create a <a href="arrays.html#Base.Slices"><code>Slices</code></a> object that is an array of slices over dimensions <code>dims</code> of <code>A</code>, returning views that select all the data from the other dimensions in <code>A</code>. <code>dims</code> can either be an integer or a tuple of integers.</p><p>If <code>drop = true</code> (the default), the outer <code>Slices</code> will drop the inner dimensions, and the ordering of the dimensions will match those in <code>dims</code>. If <code>drop = false</code>, then the <code>Slices</code> will have the same dimensionality as the underlying array, with inner dimensions having size 1.</p><p>See <a href="arrays.html#Base.stack"><code>stack</code></a><code>(slices; dims)</code> for the inverse of <code>eachslice(A; dims::Integer)</code>.</p><p>See also <a href="arrays.html#Base.eachrow"><code>eachrow</code></a>, <a href="arrays.html#Base.eachcol"><code>eachcol</code></a>, <a href="arrays.html#Base.mapslices"><code>mapslices</code></a> and <a href="arrays.html#Base.selectdim"><code>selectdim</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p>This function requires at least Julia 1.1.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p>Prior to Julia 1.9, this returned an iterator, and only a single dimension <code>dims</code> was supported.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; m = [1 2 3; 4 5 6; 7 8 9]
3×3 Matrix{Int64}:
 1  2  3
 4  5  6
 7  8  9

julia&gt; s = eachslice(m, dims=1)
3-element RowSlices{Matrix{Int64}, Tuple{Base.OneTo{Int64}}, SubArray{Int64, 1, Matrix{Int64}, Tuple{Int64, Base.Slice{Base.OneTo{Int64}}}, true}}:
 [1, 2, 3]
 [4, 5, 6]
 [7, 8, 9]

julia&gt; s[1]
3-element view(::Matrix{Int64}, 1, :) with eltype Int64:
 1
 2
 3

julia&gt; eachslice(m, dims=1, drop=false)
3×1 Slices{Matrix{Int64}, Tuple{Int64, Colon}, Tuple{Base.OneTo{Int64}, Base.OneTo{Int64}}, SubArray{Int64, 1, Matrix{Int64}, Tuple{Int64, Base.Slice{Base.OneTo{Int64}}}, true}, 2}:
 [1, 2, 3]
 [4, 5, 6]
 [7, 8, 9]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/slicearray.jl#L77-L126">source</a></section></article><h2 id="Combinatorics"><a class="docs-heading-anchor" href="#Combinatorics">Combinatorics</a><a id="Combinatorics-1"></a><a class="docs-heading-anchor-permalink" href="#Combinatorics" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.invperm" href="#Base.invperm"><code>Base.invperm</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">invperm(v)</code></pre><p>Return the inverse permutation of <code>v</code>. If <code>B = A[v]</code>, then <code>A == B[invperm(v)]</code>.</p><p>See also <a href="sort.html#Base.sortperm"><code>sortperm</code></a>, <a href="arrays.html#Base.invpermute!"><code>invpermute!</code></a>, <a href="arrays.html#Base.isperm"><code>isperm</code></a>, <a href="arrays.html#Base.permutedims"><code>permutedims</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; p = (2, 3, 1);

julia&gt; invperm(p)
(3, 1, 2)

julia&gt; v = [2; 4; 3; 1];

julia&gt; invperm(v)
4-element Vector{Int64}:
 4
 1
 3
 2

julia&gt; A = [&#39;a&#39;,&#39;b&#39;,&#39;c&#39;,&#39;d&#39;];

julia&gt; B = A[v]
4-element Vector{Char}:
 &#39;b&#39;: ASCII/Unicode U+0062 (category Ll: Letter, lowercase)
 &#39;d&#39;: ASCII/Unicode U+0064 (category Ll: Letter, lowercase)
 &#39;c&#39;: ASCII/Unicode U+0063 (category Ll: Letter, lowercase)
 &#39;a&#39;: ASCII/Unicode U+0061 (category Ll: Letter, lowercase)

julia&gt; B[invperm(v)]
4-element Vector{Char}:
 &#39;a&#39;: ASCII/Unicode U+0061 (category Ll: Letter, lowercase)
 &#39;b&#39;: ASCII/Unicode U+0062 (category Ll: Letter, lowercase)
 &#39;c&#39;: ASCII/Unicode U+0063 (category Ll: Letter, lowercase)
 &#39;d&#39;: ASCII/Unicode U+0064 (category Ll: Letter, lowercase)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/combinatorics.jl#L239-L279">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isperm" href="#Base.isperm"><code>Base.isperm</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isperm(v) -&gt; Bool</code></pre><p>Return <code>true</code> if <code>v</code> is a valid permutation.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isperm([1; 2])
true

julia&gt; isperm([1; 3])
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/combinatorics.jl#L55-L68">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.permute!-Tuple{Any, AbstractVector}" href="#Base.permute!-Tuple{Any, AbstractVector}"><code>Base.permute!</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">permute!(v, p)</code></pre><p>Permute vector <code>v</code> in-place, according to permutation <code>p</code>. No checking is done to verify that <code>p</code> is a permutation.</p><p>To return a new permutation, use <code>v[p]</code>. This is generally faster than <code>permute!(v, p)</code>; it is even faster to write into a pre-allocated output array with <code>u .= @view v[p]</code>. (Even though <code>permute!</code> overwrites <code>v</code> in-place, it internally requires some allocation to keep track of which elements have been moved.)</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p>See also <a href="arrays.html#Base.invpermute!"><code>invpermute!</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1, 1, 3, 4];

julia&gt; perm = [2, 4, 3, 1];

julia&gt; permute!(A, perm);

julia&gt; A
4-element Vector{Int64}:
 1
 4
 3
 1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/combinatorics.jl#L177-L207">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.invpermute!" href="#Base.invpermute!"><code>Base.invpermute!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">invpermute!(v, p)</code></pre><p>Like <a href="arrays.html#Base.permute!-Tuple{Any, AbstractVector}"><code>permute!</code></a>, but the inverse of the given permutation is applied.</p><p>Note that if you have a pre-allocated output array (e.g. <code>u = similar(v)</code>), it is quicker to instead employ <code>u[p] = v</code>.  (<code>invpermute!</code> internally allocates a copy of the data.)</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1, 1, 3, 4];

julia&gt; perm = [2, 4, 3, 1];

julia&gt; invpermute!(A, perm);

julia&gt; A
4-element Vector{Int64}:
 4
 1
 3
 1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/combinatorics.jl#L210-L236">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.reverse-Tuple{AbstractVector}" href="#Base.reverse-Tuple{AbstractVector}"><code>Base.reverse</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">reverse(A; dims=:)</code></pre><p>Reverse <code>A</code> along dimension <code>dims</code>, which can be an integer (a single dimension), a tuple of integers (a tuple of dimensions) or <code>:</code> (reverse along all the dimensions, the default).  See also <a href="arrays.html#Base.reverse!"><code>reverse!</code></a> for in-place reversal.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; b = Int64[1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; reverse(b, dims=2)
2×2 Matrix{Int64}:
 2  1
 4  3

julia&gt; reverse(b)
2×2 Matrix{Int64}:
 4  3
 2  1</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>Prior to Julia 1.6, only single-integer <code>dims</code> are supported in <code>reverse</code>.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/arraymath.jl#L30-L58">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.reverseind" href="#Base.reverseind"><code>Base.reverseind</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">reverseind(v, i)</code></pre><p>Given an index <code>i</code> in <a href="arrays.html#Base.reverse-Tuple{AbstractVector}"><code>reverse(v)</code></a>, return the corresponding index in <code>v</code> so that <code>v[reverseind(v,i)] == reverse(v)[i]</code>. (This can be nontrivial in cases where <code>v</code> contains non-ASCII characters.)</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; s = &quot;Julia🚀&quot;
&quot;Julia🚀&quot;

julia&gt; r = reverse(s)
&quot;🚀ailuJ&quot;

julia&gt; for i in eachindex(s)
           print(r[reverseind(r, i)])
       end
Julia🚀</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L728-L748">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.reverse!" href="#Base.reverse!"><code>Base.reverse!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">reverse!(v [, start=firstindex(v) [, stop=lastindex(v) ]]) -&gt; v</code></pre><p>In-place version of <a href="arrays.html#Base.reverse-Tuple{AbstractVector}"><code>reverse</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = Vector(1:5)
5-element Vector{Int64}:
 1
 2
 3
 4
 5

julia&gt; reverse!(A);

julia&gt; A
5-element Vector{Int64}:
 5
 4
 3
 2
 1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L2164-L2189">source</a></section><section><div><pre><code class="language-julia hljs">reverse!(A; dims=:)</code></pre><p>Like <a href="arrays.html#Base.reverse-Tuple{AbstractVector}"><code>reverse</code></a>, but operates in-place in <code>A</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>Multidimensional <code>reverse!</code> requires Julia 1.6.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/arraymath.jl#L62-L69">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="strings.html">« Strings</a><a class="docs-footer-nextpage" href="parallel.html">Tasks »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
