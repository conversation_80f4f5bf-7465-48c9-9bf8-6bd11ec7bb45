<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Ahead of Time Compilation · The Julia Language</title><meta name="title" content="Ahead of Time Compilation · The Julia Language"/><meta property="og:title" content="Ahead of Time Compilation · The Julia Language"/><meta property="twitter:title" content="Ahead of Time Compilation · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox" checked/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="ast.html">Julia ASTs</a></li><li><a class="tocitem" href="types.html">More about types</a></li><li><a class="tocitem" href="object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="functions.html">Julia Functions</a></li><li><a class="tocitem" href="cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="subarrays.html">SubArrays</a></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li class="is-active"><a class="tocitem" href="aot.html">Ahead of Time Compilation</a><ul class="internal"><li><a class="tocitem" href="#Introduction"><span>Introduction</span></a></li><li><a class="tocitem" href="#High-Level-Overview"><span>High-Level Overview</span></a></li><li><a class="tocitem" href="#Compiling-Methods"><span>Compiling Methods</span></a></li><li><a class="tocitem" href="#Loading-Code-Images-2"><span>Loading Code Images</span></a></li></ul></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Documentation of Julia&#39;s Internals</a></li><li class="is-active"><a href="aot.html">Ahead of Time Compilation</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="aot.html">Ahead of Time Compilation</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/aot.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Ahead-of-Time-Compilation"><a class="docs-heading-anchor" href="#Ahead-of-Time-Compilation">Ahead of Time Compilation</a><a id="Ahead-of-Time-Compilation-1"></a><a class="docs-heading-anchor-permalink" href="#Ahead-of-Time-Compilation" title="Permalink"></a></h1><p>This document describes the design and structure of the ahead-of-time (AOT) compilation system in Julia. This system is used when generating system images and package images. Much of the implementation described here is located in <code>aotcompile.cpp</code>, <code>staticdata.c</code>, and <code>processor.cpp</code></p><h2 id="Introduction"><a class="docs-heading-anchor" href="#Introduction">Introduction</a><a id="Introduction-1"></a><a class="docs-heading-anchor-permalink" href="#Introduction" title="Permalink"></a></h2><p>Though Julia normally compiles code just-in-time (JIT), it is possible to compile code ahead of time and save the resulting code to a file. This can be useful for a number of reasons:</p><ol><li>To reduce the time it takes to start a Julia process.</li><li>To reduce the time spent in the JIT compiler instead of executing code (time to first execution, TTFX).</li><li>To reduce the amount of memory used by the JIT compiler.</li></ol><h2 id="High-Level-Overview"><a class="docs-heading-anchor" href="#High-Level-Overview">High-Level Overview</a><a id="High-Level-Overview-1"></a><a class="docs-heading-anchor-permalink" href="#High-Level-Overview" title="Permalink"></a></h2><p>The following descriptions are a snapshot of the current implementation details of the end-to-end pipeline that happens internally when the user compiles a new AOT module, such as occurs when they type <code>using Foo</code>. These details are likely to change over time as we implement better ways to handle them, so current implementations may not exactly match the dataflow and functions described below.</p><h3 id="Compiling-Code-Images"><a class="docs-heading-anchor" href="#Compiling-Code-Images">Compiling Code Images</a><a id="Compiling-Code-Images-1"></a><a class="docs-heading-anchor-permalink" href="#Compiling-Code-Images" title="Permalink"></a></h3><p>Firstly, the methods that need to be compiled to native code must be identified. This can only be done by actually executing the code to be compiled, as the set of methods that need to be compiled depends on the types of the arguments passed to the methods, and method invocations with certain combinations of types may not be known until runtime. During this process, the exact methods that the compiler sees are tracked for later compilation, producing a compilation trace.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Currently when compiling images, Julia runs the trace generation in a different process than the process performing the AOT compilation. This can have impacts when attempting to use a debugger during precompilation. The best way to debug precompilation with a debugger is to use the rr debugger, record the entire process tree, use <code>rr ps</code> to identify the relevant failing process, and then use <code>rr replay -p PID</code> to replay just the failing process.</p></div></div><p>Once the methods to be compiled have been identified, they are passed to the <code>jl_create_system_image</code> function. This function sets up a number of data structures that will be used when serializing native code to a file, and then calls <code>jl_create_native</code> with the array of methods. <code>jl_create_native</code> runs codegen on the methods produces one or more LLVM modules. <code>jl_create_system_image</code> then records some useful information about what codegen produced from the module(s).</p><p>The module(s) are then passed to <code>jl_dump_native</code>, along with the information recorded by <code>jl_create_system_image</code>. <code>jl_dump_native</code> contains the code necessary to serialize the module(s) to bitcode, object, or assembly files depending on the command-line options passed to Julia. The serialized code and information are then written to a file as an archive.</p><p>The final step is to run a system linker on the object files in the archive produced by <code>jl_dump_native</code>. Once this step is complete, a shared library containing the compiled code is produced.</p><h3 id="Loading-Code-Images"><a class="docs-heading-anchor" href="#Loading-Code-Images">Loading Code Images</a><a id="Loading-Code-Images-1"></a><a class="docs-heading-anchor-permalink" href="#Loading-Code-Images" title="Permalink"></a></h3><p>When loading a code image, the shared library produced by the linker is loaded into memory. The system image data is then loaded from the shared library. This data contains information about the types, methods, and code instances that were compiled into the shared library. This data is used to restore the state of the runtime to what it was when the code image was compiled.</p><p>If the code image was compiled with multiversioning, the loader will pick the appropriate version of each function to use based on the CPU features available on the current machine.</p><p>For system images, since no other code has been loaded, the state of the runtime is now the same as it was when the code image was compiled. For package images, the environment may have changed compared to when the code was compiled, so each method must be checked against the global method table to determine if it is still valid code.</p><h2 id="Compiling-Methods"><a class="docs-heading-anchor" href="#Compiling-Methods">Compiling Methods</a><a id="Compiling-Methods-1"></a><a class="docs-heading-anchor-permalink" href="#Compiling-Methods" title="Permalink"></a></h2><h3 id="Tracing-Compiled-Methods"><a class="docs-heading-anchor" href="#Tracing-Compiled-Methods">Tracing Compiled Methods</a><a id="Tracing-Compiled-Methods-1"></a><a class="docs-heading-anchor-permalink" href="#Tracing-Compiled-Methods" title="Permalink"></a></h3><p>Julia has a command-line flag to record all of the methods that are compiled by the JIT compiler, <code>--trace-compile=filename</code>. When a function is compiled and this flag has a filename, Julia will print out a precompile statement to that file with the method and argument types it was called with. This therefore generates a precompile script that can be used later in the AOT compilation process. The <a href="https://julialang.github.io/PrecompileTools.jl/stable/">PrecompileTools</a> package has tooling that can make taking advantage of this functionality easier for package developers.</p><h3 id="jl_create_system_image"><a class="docs-heading-anchor" href="#jl_create_system_image"><code>jl_create_system_image</code></a><a id="jl_create_system_image-1"></a><a class="docs-heading-anchor-permalink" href="#jl_create_system_image" title="Permalink"></a></h3><p><code>jl_create_system_image</code> saves all of the Julia-specific metadata necessary to later restore the state of the runtime. This includes data such as code instances, method instances, method tables, and type information. This function also sets up the data structures necessary to serialize the native code to a file. Finally, it calls <code>jl_create_native</code> to create one or more LLVM modules containing the native code for the methods passed to it. <code>jl_create_native</code> is responsible for running codegen on the methods passed to it.</p><h3 id="jl_dump_native"><a class="docs-heading-anchor" href="#jl_dump_native"><code>jl_dump_native</code></a><a id="jl_dump_native-1"></a><a class="docs-heading-anchor-permalink" href="#jl_dump_native" title="Permalink"></a></h3><p><code>jl_dump_native</code> is responsible for serializing the LLVM module containing the native code to a file. In addition to the module, the system image data produced by <code>jl_create_system_image</code> is compiled as a global variable. The output of this method is bitcode, object, and/or assembly archives containing the code and system image data.</p><p><code>jl_dump_native</code> is typically one of the larger time sinks when emitting native code, with much of the time spent in optimizing LLVM IR and emitting machine code. Therefore, this function is capable of multithreading the optimization and machine code emission steps. This multithreading is parameterized on the size of the module, but can be explicitly overridden by setting the <a href="../manual/environment-variables.html#JULIA_IMAGE_THREADS"><code>JULIA_IMAGE_THREADS</code></a> environment variable. The default maximum number of threads is half the number of available threads, but setting it to be lower can reduce peak memory usage during compilation.</p><p><code>jl_dump_native</code> can also produce native code optimized for multiple architectures, when integrated with the Julia loader. This is triggered by setting the <a href="../manual/environment-variables.html#JULIA_CPU_TARGET"><code>JULIA_CPU_TARGET</code></a> environment variable and mediated by the multiversioning pass in the optimization pipeline. To make this work with multithreading, an annotation step is added before the module is split into submodules that are emitted on their own threads, and this annotation step uses information available throughout the entire module to decide what functions are cloned for different architectures. Once the annotation has happened, individual threads can emit code for different architectures in parallel, knowing that a different submodule is guaranteed to produce the necessary functions that will be called by a cloned function.</p><p>Some other metadata about how the module was serialized is also stored in the archive, such as the number of threads used to serialize the module and the number of functions that were compiled.</p><h3 id="Static-Linking"><a class="docs-heading-anchor" href="#Static-Linking">Static Linking</a><a id="Static-Linking-1"></a><a class="docs-heading-anchor-permalink" href="#Static-Linking" title="Permalink"></a></h3><p>The final step in the AOT compilation process is to run a linker on the object files in the archive produced by <code>jl_dump_native</code>. This produces a shared library containing the compiled code. This shared library can then be loaded by Julia to restore the state of the runtime. When compiling a system image, the native linker used by a C compiler is used to produce the final shared library. For package images, the LLVM linker LLD is used to provide a more consistent linking interface.</p><h2 id="Loading-Code-Images-2"><a class="docs-heading-anchor" href="#Loading-Code-Images-2">Loading Code Images</a><a class="docs-heading-anchor-permalink" href="#Loading-Code-Images-2" title="Permalink"></a></h2><h3 id="Loading-the-Shared-Library"><a class="docs-heading-anchor" href="#Loading-the-Shared-Library">Loading the Shared Library</a><a id="Loading-the-Shared-Library-1"></a><a class="docs-heading-anchor-permalink" href="#Loading-the-Shared-Library" title="Permalink"></a></h3><p>The first step in loading a code image is to load the shared library produced by the linker. This is done by calling <code>jl_dlopen</code> on the path to the shared library. This function is responsible for loading the shared library and resolving all of the symbols in the library.</p><h3 id="Loading-Native-Code"><a class="docs-heading-anchor" href="#Loading-Native-Code">Loading Native Code</a><a id="Loading-Native-Code-1"></a><a class="docs-heading-anchor-permalink" href="#Loading-Native-Code" title="Permalink"></a></h3><p>The loader first needs to identify whether the native code that was compiled is valid for the architecture that the loader is running on. This is necessary to avoid executing instructions that older CPUs do not recognize. This is done by checking the CPU features available on the current machine against the CPU features that the code was compiled for. When multiversioning is enabled, the loader will pick the appropriate version of each function to use based on the CPU features available on the current machine. If none of the feature sets that were multiversioned, the loader will throw an error.</p><p>Part of the multiversioning pass creates a number of global arrays of all of the functions in the module. When this process is multithreaded, an array of arrays is created, which the loader reorganizes into one large array with all of the functions that were compiled for this architecture. A similar process occurs for the global variables in the module.</p><h3 id="Setting-Up-Julia-State"><a class="docs-heading-anchor" href="#Setting-Up-Julia-State">Setting Up Julia State</a><a id="Setting-Up-Julia-State-1"></a><a class="docs-heading-anchor-permalink" href="#Setting-Up-Julia-State" title="Permalink"></a></h3><p>The loader then uses the global variables and functions produced from loading native code to set up Julia runtime core data structures in the current process. This setup involves adding types and methods to the Julia runtime, and making the cached native code available for use by other Julia functions and the interpreter. For package images, each method must be validated, in that the global method table&#39;s state must match the state that the package image was compiled for. In particular, if a different set of methods exists at the load time compared to compile time of the package image, the method must be invalidated and recompiled on first use. This is necessary to ensure that execution semantics remain the same regardless of if a package was precompiled or if the code was directly executed. System images do not need to perform this validation, since the global method table is empty at load time. Thus, system images have faster load times than package images.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="EscapeAnalysis.html">« <code>EscapeAnalysis</code></a><a class="docs-footer-nextpage" href="gc-sa.html">Static analyzer annotations for GC correctness in C code »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
