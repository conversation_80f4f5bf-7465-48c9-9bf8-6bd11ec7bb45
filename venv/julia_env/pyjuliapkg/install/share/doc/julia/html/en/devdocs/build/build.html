<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Building Julia (Detailed) · The Julia Language</title><meta name="title" content="Building Julia (Detailed) · The Julia Language"/><meta property="og:title" content="Building Julia (Detailed) · The Julia Language"/><meta property="twitter:title" content="Building Julia (Detailed) · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL="../.."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../../assets/documenter.js"></script><script src="../../search_index.js"></script><script src="../../siteinfo.js"></script><script src="../../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../../assets/themeswap.js"></script><link href="../../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../../index.html"><img class="docs-light-only" src="../../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../../manual/types.html">Types</a></li><li><a class="tocitem" href="../../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../../base/base.html">Essentials</a></li><li><a class="tocitem" href="../../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../../base/strings.html">Strings</a></li><li><a class="tocitem" href="../../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../../base/constants.html">Constants</a></li><li><a class="tocitem" href="../../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../../base/c.html">C Interface</a></li><li><a class="tocitem" href="../../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../types.html">More about types</a></li><li><a class="tocitem" href="../object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../functions.html">Julia Functions</a></li><li><a class="tocitem" href="../cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../require.html">Module loading</a></li><li><a class="tocitem" href="../inference.html">Inference</a></li><li><a class="tocitem" href="../ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox" checked/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li class="is-active"><a class="tocitem" href="build.html">Building Julia (Detailed)</a><ul class="internal"><li><a class="tocitem" href="#Downloading-the-Julia-source-code"><span>Downloading the Julia source code</span></a></li><li><a class="tocitem" href="#Building-Julia"><span>Building Julia</span></a></li><li><a class="tocitem" href="#Updating-an-existing-source-tree"><span>Updating an existing source tree</span></a></li><li><a class="tocitem" href="#General-troubleshooting"><span>General troubleshooting</span></a></li><li><a class="tocitem" href="#Platform-Specific-Notes"><span>Platform-Specific Notes</span></a></li><li><a class="tocitem" href="#Required-Build-Tools-and-External-Libraries"><span>Required Build Tools and External Libraries</span></a></li><li><a class="tocitem" href="#Build-dependencies"><span>Build dependencies</span></a></li><li><a class="tocitem" href="#Source-distributions-of-releases"><span>Source distributions of releases</span></a></li><li><a class="tocitem" href="#Building-Julia-from-source-with-a-Git-checkout-of-a-stdlib"><span>Building Julia from source with a Git checkout of a stdlib</span></a></li><li><a class="tocitem" href="#Building-an-&quot;assert-build&quot;-of-Julia"><span>Building an &quot;assert build&quot; of Julia</span></a></li><li><a class="tocitem" href="#Building-32-bit-Julia-on-a-64-bit-machine"><span>Building 32-bit Julia on a 64-bit machine</span></a></li><li><a class="tocitem" href="#Update-the-version-number-of-a-dependency"><span>Update the version number of a dependency</span></a></li></ul></li><li><a class="tocitem" href="linux.html">Linux</a></li><li><a class="tocitem" href="macos.html">macOS</a></li><li><a class="tocitem" href="windows.html">Windows</a></li><li><a class="tocitem" href="freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Building Julia</a></li><li class="is-active"><a href="build.html">Building Julia (Detailed)</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="build.html">Building Julia (Detailed)</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/build/build.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Building-Julia-(Detailed)"><a class="docs-heading-anchor" href="#Building-Julia-(Detailed)">Building Julia (Detailed)</a><a id="Building-Julia-(Detailed)-1"></a><a class="docs-heading-anchor-permalink" href="#Building-Julia-(Detailed)" title="Permalink"></a></h1><h2 id="Downloading-the-Julia-source-code"><a class="docs-heading-anchor" href="#Downloading-the-Julia-source-code">Downloading the Julia source code</a><a id="Downloading-the-Julia-source-code-1"></a><a class="docs-heading-anchor-permalink" href="#Downloading-the-Julia-source-code" title="Permalink"></a></h2><p>If you are behind a firewall, you may need to use the <code>https</code> protocol instead of the <code>git</code> protocol:</p><pre><code class="language-sh hljs">git config --global url.&quot;https://&quot;.insteadOf git://</code></pre><p>Be sure to also configure your system to use the appropriate proxy settings, e.g. by setting the <code>https_proxy</code> and <code>http_proxy</code> variables.</p><h2 id="Building-Julia"><a class="docs-heading-anchor" href="#Building-Julia">Building Julia</a><a id="Building-Julia-1"></a><a class="docs-heading-anchor-permalink" href="#Building-Julia" title="Permalink"></a></h2><p>When compiled the first time, the build will automatically download pre-built <a href="build.html#Required-Build-Tools-and-External-Libraries">external dependencies</a>. If you prefer to build all the dependencies on your own, or are building on a system that cannot access the network during the build process, add the following in <code>Make.user</code>:</p><pre><code class="nohighlight hljs">USE_BINARYBUILDER=0</code></pre><p>Building Julia requires 5GiB if building all dependencies and approximately 4GiB of virtual memory.</p><p>To perform a parallel build, use <code>make -j N</code> and supply the maximum number of concurrent processes. If the defaults in the build do not work for you, and you need to set specific make parameters, you can save them in <code>Make.user</code>, and place the file in the root of your Julia source. The build will automatically check for the existence of <code>Make.user</code> and use it if it exists.</p><p>You can create out-of-tree builds of Julia by specifying <code>make O=&lt;build-directory&gt; configure</code> on the command line. This will create a directory mirror, with all of the necessary Makefiles to build Julia, in the specified directory. These builds will share the source files in Julia and <code>deps/srccache</code>. Each out-of-tree build directory can have its own <code>Make.user</code> file to override the global <code>Make.user</code> file in the top-level folder.</p><p>If everything works correctly, you will see a Julia banner and an interactive prompt into which you can enter expressions for evaluation. (Errors related to libraries might be caused by old, incompatible libraries sitting around in your PATH. In this case, try moving the <code>julia</code> directory earlier in the PATH). Note that most of the instructions above apply to unix systems.</p><p>To run julia from anywhere you can:</p><ul><li><p>add an alias (in <code>bash</code>: <code>echo &quot;alias julia=&#39;/path/to/install/folder/bin/julia&#39;&quot; &gt;&gt; ~/.bashrc &amp;&amp; source ~/.bashrc</code>), or</p></li><li><p>add a soft link to the <code>julia</code> executable in the <code>julia</code> directory to <code>/usr/local/bin</code> (or any suitable directory already in your path), or</p></li><li><p>add the <code>julia</code> directory to your executable path for this shell session (in <code>bash</code>: <code>export PATH=&quot;$(pwd):$PATH&quot;</code> ; in <code>csh</code> or <code>tcsh</code>:</p></li></ul><p><code>set path= ( $path $cwd )</code> ), or</p><ul><li><p>add the <code>julia</code> directory to your executable path permanently (e.g. in <code>.bash_profile</code>), or</p></li><li><p>write <code>prefix=/path/to/install/folder</code> into <code>Make.user</code> and then run <code>make install</code>. If there is a version of Julia already installed in this folder, you should delete it before running <code>make install</code>.</p></li></ul><p>Some of the options you can set to control the build of Julia are listed and documented at the beginning of the file <code>Make.inc</code>, but you should never edit it for this purpose, use <code>Make.user</code> instead.</p><p>Julia&#39;s Makefiles define convenient automatic rules called <code>print-&lt;VARNAME&gt;</code> for printing the value of variables, replacing <code>&lt;VARNAME&gt;</code> with the name of the variable to print the value of. For example</p><pre><code class="language-console hljs">$ make print-JULIA_PRECOMPILE
JULIA_PRECOMPILE=1</code></pre><p>These rules are useful for debugging purposes.</p><p>Now you should be able to run Julia like this:</p><pre><code class="nohighlight hljs">julia</code></pre><p>If you are building a Julia package for distribution on Linux, macOS, or Windows, take a look at the detailed notes in <a href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/build/distributing.md">distributing.md</a>.</p><h2 id="Updating-an-existing-source-tree"><a class="docs-heading-anchor" href="#Updating-an-existing-source-tree">Updating an existing source tree</a><a id="Updating-an-existing-source-tree-1"></a><a class="docs-heading-anchor-permalink" href="#Updating-an-existing-source-tree" title="Permalink"></a></h2><p>If you have previously downloaded <code>julia</code> using <code>git clone</code>, you can update the existing source tree using <code>git pull</code> rather than starting anew:</p><pre><code class="language-sh hljs">cd julia
git pull &amp;&amp; make</code></pre><p>Assuming that you had made no changes to the source tree that will conflict with upstream updates, these commands will trigger a build to update to the latest version.</p><h2 id="General-troubleshooting"><a class="docs-heading-anchor" href="#General-troubleshooting">General troubleshooting</a><a id="General-troubleshooting-1"></a><a class="docs-heading-anchor-permalink" href="#General-troubleshooting" title="Permalink"></a></h2><ol><li><p>Over time, the base library may accumulate enough changes such that the bootstrapping process in building the system image will fail. If this happens, the build may fail with an error like</p><pre><code class="language-sh hljs"> *** This error is usually fixed by running &#39;make clean&#39;. If the error persists, try &#39;make cleanall&#39; ***</code></pre><p>As described, running <code>make clean &amp;&amp; make</code> is usually sufficient. Occasionally, the stronger cleanup done by <code>make cleanall</code> is needed.</p></li><li><p>New versions of external dependencies may be introduced which may occasionally cause conflicts with existing builds of older versions.</p><p>a. Special <code>make</code> targets exist to help wipe the existing build of a    dependency. For example, <code>make -C deps clean-llvm</code> will clean out the    existing build of <code>llvm</code> so that <code>llvm</code> will be rebuilt from the    downloaded source distribution the next time <code>make</code> is called.    <code>make -C deps distclean-llvm</code> is a stronger wipe which will also delete    the downloaded source distribution, ensuring that a fresh copy of the    source distribution will be downloaded and that any new patches will be    applied the next time <code>make</code> is called.</p><p>b. To delete existing binaries of <code>julia</code> and all its dependencies,    delete the <code>./usr</code> directory <em>in the source tree</em>.</p></li><li><p>If you&#39;ve updated macOS recently, be sure to run <code>xcode-select --install</code> to update the command line tools. Otherwise, you could run into errors for missing headers and libraries, such as <code>ld: library not found for -lcrt1.10.6.o</code>.</p></li><li><p>If you&#39;ve moved the source directory, you might get errors such as  <code>CMake Error: The current CMakeCache.txt directory ... is different than the directory ... where     CMakeCache.txt was created.</code>, in which case you may delete the offending dependency under <code>deps</code></p></li><li><p>In extreme cases, you may wish to reset the source tree to a pristine state. The following git commands may be helpful:</p><pre><code class="language-sh hljs"> git reset --hard #Forcibly remove any changes to any files under version control
 git clean -x -f -d #Forcibly remove any file or directory not under version control</code></pre><p><em>To avoid losing work, make sure you know what these commands do before you run them. <code>git</code> will not be able to undo these changes!</em></p></li></ol><h2 id="Platform-Specific-Notes"><a class="docs-heading-anchor" href="#Platform-Specific-Notes">Platform-Specific Notes</a><a id="Platform-Specific-Notes-1"></a><a class="docs-heading-anchor-permalink" href="#Platform-Specific-Notes" title="Permalink"></a></h2><p>Notes for various operating systems:</p><ul><li><a href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/build/linux.md">Linux</a></li><li><a href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/build/macos.md">macOS</a></li><li><a href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/build/windows.md">Windows</a></li><li><a href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/build/freebsd.md">FreeBSD</a></li></ul><p>Notes for various architectures:</p><ul><li><a href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/build/arm.md">ARM</a></li></ul><h2 id="Required-Build-Tools-and-External-Libraries"><a class="docs-heading-anchor" href="#Required-Build-Tools-and-External-Libraries">Required Build Tools and External Libraries</a><a id="Required-Build-Tools-and-External-Libraries-1"></a><a class="docs-heading-anchor-permalink" href="#Required-Build-Tools-and-External-Libraries" title="Permalink"></a></h2><p>Building Julia requires that the following software be installed:</p><ul><li><strong>[GNU make]</strong>                — building dependencies.</li><li><strong>[gcc &amp; g++][gcc]</strong> (&gt;= 7.1) or <strong>[Clang][clang]</strong> (&gt;= 5.0, &gt;= 9.3 for Apple Clang) — compiling and linking C, C++.</li><li><strong>[libatomic][gcc]</strong>          — provided by <strong>[gcc]</strong> and needed to support atomic operations.</li><li><strong>[python]</strong> (&gt;=2.7)          — needed to build LLVM.</li><li><strong>[gfortran]</strong>                — compiling and linking Fortran libraries.</li><li><strong>[perl]</strong>                    — preprocessing of header files of libraries.</li><li><strong>[wget]</strong>, <strong>[curl]</strong>, or <strong>[fetch]</strong> (FreeBSD) — to automatically download external libraries.</li><li><strong>[m4]</strong>                      — needed to build GMP.</li><li><strong>[awk]</strong>                     — helper tool for Makefiles.</li><li><strong>[patch]</strong>                   — for modifying source code.</li><li><strong>[cmake]</strong> (&gt;= 3.4.3)        — needed to build <code>libgit2</code>.</li><li><strong>[pkg-config]</strong>              — needed to build <code>libgit2</code> correctly, especially for proxy support.</li><li><strong>[powershell]</strong> (&gt;= 3.0)     — necessary only on Windows.</li><li><strong>[which]</strong>                   — needed for checking build dependencies.</li></ul><p>On Debian-based distributions (e.g. Ubuntu), you can easily install them with <code>apt-get</code>:</p><pre><code class="nohighlight hljs">sudo apt-get install build-essential libatomic1 python gfortran perl wget m4 cmake pkg-config curl</code></pre><p>Julia uses the following external libraries, which are automatically downloaded (or in a few cases, included in the Julia source repository) and then compiled from source the first time you run <code>make</code>. The specific version numbers of these libraries that Julia uses are listed in <a href="https://github.com/JuliaLang/julia/blob/master/deps/"><code>deps/$(libname).version</code></a>:</p><ul><li><strong>[LLVM]</strong> (15.0 + <a href="https://github.com/JuliaLang/llvm-project/tree/julia-release/15.x">patches</a>) — compiler infrastructure (see <a href="build.html#llvm">note below</a>).</li><li><strong>[FemtoLisp]</strong>            — packaged with Julia source, and used to implement the compiler front-end.</li><li><strong>[libuv]</strong>  (custom fork) — portable, high-performance event-based I/O library.</li><li><strong>[OpenLibm]</strong>             — portable libm library containing elementary math functions.</li><li><strong>[DSFMT]</strong>                — fast Mersenne Twister pseudorandom number generator library.</li><li><strong>[OpenBLAS]</strong>             — fast, open, and maintained [basic linear algebra subprograms (BLAS)]</li><li><strong>[LAPACK]</strong>               — library of linear algebra routines for solving systems of simultaneous linear equations, least-squares solutions of linear systems of equations, eigenvalue problems, and singular value problems.</li><li><strong>[MKL]</strong> (optional)       – OpenBLAS and LAPACK may be replaced by Intel&#39;s MKL library.</li><li><strong>[SuiteSparse]</strong>          — library of linear algebra routines for sparse matrices.</li><li><strong>[PCRE]</strong>                 — Perl-compatible regular expressions library.</li><li><strong>[GMP]</strong>                  — GNU multiple precision arithmetic library, needed for <code>BigInt</code> support.</li><li><strong>[MPFR]</strong>                 — GNU multiple precision floating point library, needed for arbitrary precision floating point (<code>BigFloat</code>) support.</li><li><strong>[libgit2]</strong>              — Git linkable library, used by Julia&#39;s package manager.</li><li><strong>[curl]</strong>                 — libcurl provides download and proxy support.</li><li><strong>[libssh2]</strong>              — library for SSH transport, used by libgit2 for packages with SSH remotes.</li><li><strong>[mbedtls]</strong>              — library used for cryptography and transport layer security, used by libssh2</li><li><strong>[utf8proc]</strong>             — a library for processing UTF-8 encoded Unicode strings.</li><li><strong>[LLVM libunwind]</strong>       — LLVM&#39;s fork of [libunwind], a library that determines the call-chain of a program.</li><li><strong>[ITTAPI]</strong>               — Intel&#39;s Instrumentation and Tracing Technology and Just-In-Time API.</li></ul><p>[GNU make]:     https://www.gnu.org/software/make [patch]:        https://www.gnu.org/software/patch [wget]:         https://www.gnu.org/software/wget [m4]:           https://www.gnu.org/software/m4 [awk]:          https://www.gnu.org/software/gawk [gcc]:          https://gcc.gnu.org [clang]:        https://clang.llvm.org [python]:       https://www.python.org/ [gfortran]:     https://gcc.gnu.org/fortran/ [curl]:         https://curl.haxx.se [fetch]:        https://www.freebsd.org/cgi/man.cgi?fetch(1) [perl]:         https://www.perl.org [cmake]:        https://www.cmake.org [OpenLibm]:     https://github.com/JuliaLang/openlibm [DSFMT]:        https://github.com/MersenneTwister-Lab/dSFMT [OpenBLAS]:     https://github.com/xianyi/OpenBLAS [LAPACK]:       https://www.netlib.org/lapack [MKL]:          https://software.intel.com/en-us/articles/intel-mkl [SuiteSparse]:  https://people.engr.tamu.edu/davis/suitesparse.html [PCRE]:         https://www.pcre.org [LLVM]:         https://www.llvm.org [LLVM libunwind]: https://github.com/llvm/llvm-project/tree/main/libunwind [FemtoLisp]:    https://github.com/JeffBezanson/femtolisp [GMP]:          https://gmplib.org [MPFR]:         https://www.mpfr.org [libuv]:        https://github.com/JuliaLang/libuv [libgit2]:      https://libgit2.org/ [utf8proc]:     https://julialang.org/utf8proc/ [libunwind]:    https://www.nongnu.org/libunwind [libssh2]:      https://www.libssh2.org [mbedtls]:      https://tls.mbed.org/ [pkg-config]:   https://www.freedesktop.org/wiki/Software/pkg-config/ [powershell]:   https://docs.microsoft.com/en-us/powershell/scripting/wmf/overview [which]:        https://carlowood.github.io/which/ [ITTAPI]:       https://github.com/intel/ittapi</p><h2 id="Build-dependencies"><a class="docs-heading-anchor" href="#Build-dependencies">Build dependencies</a><a id="Build-dependencies-1"></a><a class="docs-heading-anchor-permalink" href="#Build-dependencies" title="Permalink"></a></h2><p>If you already have one or more of these packages installed on your system, you can prevent Julia from compiling duplicates of these libraries by passing <code>USE_SYSTEM_...=1</code> to <code>make</code> or adding the line to <code>Make.user</code>. The complete list of possible flags can be found in <code>Make.inc</code>.</p><p>Please be aware that this procedure is not officially supported, as it introduces additional variability into the installation and versioning of the dependencies, and is recommended only for system package maintainers. Unexpected compile errors may result, as the build system will do no further checking to ensure the proper packages are installed.</p><h3 id="LLVM"><a class="docs-heading-anchor" href="#LLVM">LLVM</a><a id="LLVM-1"></a><a class="docs-heading-anchor-permalink" href="#LLVM" title="Permalink"></a></h3><p>The most complicated dependency is LLVM, for which we require additional patches from upstream (LLVM is not backward compatible).</p><p>For packaging Julia with LLVM, we recommend either:</p><ul><li>bundling a Julia-only LLVM library inside the Julia package, or</li><li>adding the patches to the LLVM package of the distribution.<ul><li>A complete list of patches is available in on <a href="https://github.com/JuliaLang/llvm-project">Github</a> see the <code>julia-release/15.x</code> branch.</li><li>The only Julia-specific patch is the lib renaming (<code>llvm7-symver-jlprefix.patch</code>), which should <em>not</em> be applied to a system LLVM.</li><li>The remaining patches are all upstream bug fixes, and have been contributed into upstream LLVM.</li></ul></li></ul><p>Using an unpatched or different version of LLVM will result in errors and/or poor performance. You can build a different version of LLVM from a remote Git repository with the following options in the <code>Make.user</code> file:</p><pre><code class="language-make hljs"># Force source build of LLVM
USE_BINARYBUILDER_LLVM = 0
# Use Git for fetching LLVM source code
# this is either `1` to get all of them
DEPS_GIT = 1
# or a space-separated list of specific dependencies to download with git
DEPS_GIT = llvm

# Other useful options:
#URL of the Git repository you want to obtain LLVM from:
#  LLVM_GIT_URL = ...
#Name of the alternate branch to clone from git
#  LLVM_BRANCH = julia-16.0.6-0
#SHA hash of the alterate commit to check out automatically
#  LLVM_SHA1 = $(LLVM_BRANCH)
#List of LLVM targets to build.  It is strongly recommended to keep at least all the
#default targets listed in `deps/llvm.mk`, even if you don&#39;t necessarily need all of them.
#  LLVM_TARGETS = ...
#Use ccache for faster recompilation in case you need to restart a build.
#  USECCACHE = 1
#  CMAKE_GENERATOR=Ninja
#  LLVM_ASSERTIONS=1
#  LLVM_DEBUG=Symbols</code></pre><p>The various build phases are controlled by specific files:</p><ul><li><code>deps/llvm.version</code> : touch or change to checkout a new version, <code>make get-llvm check-llvm</code></li><li><code>deps/srccache/llvm/source-extracted</code> : result of <code>make extract-llvm</code></li><li><code>deps/llvm/build_Release*/build-configured</code> : result of <code>make configure-llvm</code></li><li><code>deps/llvm/build_Release*/build-configured</code> : result of <code>make compile-llvm</code></li><li><code>usr-staging/llvm/build_Release*.tgz</code> : result of <code>make stage-llvm</code> (regenerate with <code>make reinstall-llvm</code>)</li><li><code>usr/manifest/llvm</code> : result of <code>make install-llvm</code> (regenerate with <code>make uninstall-llvm</code>)</li><li><code>make version-check-llvm</code> : runs every time to warn the user if there are local modifications</li></ul><p>Though Julia can be built with newer LLVM versions, support for this should be regarded as experimental and not suitable for packaging.</p><h3 id="libuv"><a class="docs-heading-anchor" href="#libuv">libuv</a><a id="libuv-1"></a><a class="docs-heading-anchor-permalink" href="#libuv" title="Permalink"></a></h3><p>Julia uses a custom fork of libuv. It is a small dependency, and can be safely bundled in the same package as Julia, and will not conflict with the system library. Julia builds should <em>not</em> try to use the system libuv.</p><h3 id="BLAS-and-LAPACK"><a class="docs-heading-anchor" href="#BLAS-and-LAPACK">BLAS and LAPACK</a><a id="BLAS-and-LAPACK-1"></a><a class="docs-heading-anchor-permalink" href="#BLAS-and-LAPACK" title="Permalink"></a></h3><p>As a high-performance numerical language, Julia should be linked to a multi-threaded BLAS and LAPACK, such as OpenBLAS or ATLAS, which will provide much better performance than the reference <code>libblas</code> implementations which may be default on some systems.</p><h2 id="Source-distributions-of-releases"><a class="docs-heading-anchor" href="#Source-distributions-of-releases">Source distributions of releases</a><a id="Source-distributions-of-releases-1"></a><a class="docs-heading-anchor-permalink" href="#Source-distributions-of-releases" title="Permalink"></a></h2><p>Each pre-release and release of Julia has a &quot;full&quot; source distribution and a &quot;light&quot; source distribution.</p><p>The full source distribution contains the source code for Julia and all dependencies so that it can be built from source without an internet connection. The light source distribution does not include the source code of dependencies.</p><p>For example, <code>julia-1.0.0.tar.gz</code> is the light source distribution for the <code>v1.0.0</code> release of Julia, while <code>julia-1.0.0-full.tar.gz</code> is the full source distribution.</p><h2 id="Building-Julia-from-source-with-a-Git-checkout-of-a-stdlib"><a class="docs-heading-anchor" href="#Building-Julia-from-source-with-a-Git-checkout-of-a-stdlib">Building Julia from source with a Git checkout of a stdlib</a><a id="Building-Julia-from-source-with-a-Git-checkout-of-a-stdlib-1"></a><a class="docs-heading-anchor-permalink" href="#Building-Julia-from-source-with-a-Git-checkout-of-a-stdlib" title="Permalink"></a></h2><p>If you need to build Julia from source with a Git checkout of a stdlib, then use <code>make DEPS_GIT=NAME_OF_STDLIB</code> when building Julia.</p><p>For example, if you need to build Julia from source with a Git checkout of Pkg, then use <code>make DEPS_GIT=Pkg</code> when building Julia. The <code>Pkg</code> repo is in <code>stdlib/Pkg</code>, and created initially with a detached <code>HEAD</code>. If you&#39;re doing this from a pre-existing Julia repository, you may need to <code>make clean</code> beforehand.</p><p>If you need to build Julia from source with Git checkouts of more than one stdlib, then <code>DEPS_GIT</code> should be a space-separated list of the stdlib names. For example, if you need to build Julia from source with a Git checkout of Pkg, Tar, and Downloads, then use <code>make DEPS_GIT=&#39;Pkg Tar Downloads&#39;</code> when building Julia.</p><h2 id="Building-an-&quot;assert-build&quot;-of-Julia"><a class="docs-heading-anchor" href="#Building-an-&quot;assert-build&quot;-of-Julia">Building an &quot;assert build&quot; of Julia</a><a id="Building-an-&quot;assert-build&quot;-of-Julia-1"></a><a class="docs-heading-anchor-permalink" href="#Building-an-&quot;assert-build&quot;-of-Julia" title="Permalink"></a></h2><p>An &quot;assert build&quot; of Julia is a build that was built with both <code>FORCE_ASSERTIONS=1</code> and <code>LLVM_ASSERTIONS=1</code>. To build an assert build, define both of the following variables in your <code>Make.user</code> file:</p><pre><code class="nohighlight hljs">FORCE_ASSERTIONS=1
LLVM_ASSERTIONS=1</code></pre><p>Please note that assert builds of Julia will be slower than regular (non-assert) builds.</p><h2 id="Building-32-bit-Julia-on-a-64-bit-machine"><a class="docs-heading-anchor" href="#Building-32-bit-Julia-on-a-64-bit-machine">Building 32-bit Julia on a 64-bit machine</a><a id="Building-32-bit-Julia-on-a-64-bit-machine-1"></a><a class="docs-heading-anchor-permalink" href="#Building-32-bit-Julia-on-a-64-bit-machine" title="Permalink"></a></h2><p>Occasionally, bugs specific to 32-bit architectures may arise, and when this happens it is useful to be able to debug the problem on your local machine.  Since most modern 64-bit systems support running programs built for 32-bit ones, if you don&#39;t have to recompile Julia from source (e.g. you mainly need to inspect the behavior of a 32-bit Julia without having to touch the C code), you can likely use a 32-bit build of Julia for your system that you can obtain from the <a href="https://julialang.org/downloads/">official downloads page</a>. However, if you do need to recompile Julia from source one option is to use a Docker container of a 32-bit system.  At least for now, building a 32-bit version of Julia is relatively straightforward using <a href="https://hub.docker.com/r/i386/ubuntu">ubuntu 32-bit docker images</a>. In brief, after setting up <code>docker</code> here are the required steps:</p><pre><code class="language-sh hljs">$ docker pull i386/ubuntu
$ docker run --platform i386 -i -t i386/ubuntu /bin/bash</code></pre><p>At this point you should be in a 32-bit machine console (note that <code>uname</code> reports the host architecture, so will still say 64-bit, but this will not affect the Julia build). You can add packages and compile code; when you <code>exit</code>, all the changes will be lost, so be sure to finish your analysis in a single session or set up a copy/pastable script you can use to set up your environment.</p><p>From this point, you should</p><pre><code class="language-sh hljs"># apt update</code></pre><p>(Note that <code>sudo</code> isn&#39;t installed, but neither is it necessary since you are running as <code>root</code>, so you can omit <code>sudo</code> from all commands.)</p><p>Then add all the <a href="build.html#required-build-tools-and-external-libraries">build dependencies</a>, a console-based editor of your choice, <code>git</code>, and anything else you&#39;ll need (e.g., <code>gdb</code>, <code>rr</code>, etc). Pick a directory to work in and <code>git clone</code> Julia, check out the branch you wish to debug, and build Julia as usual.</p><h2 id="Update-the-version-number-of-a-dependency"><a class="docs-heading-anchor" href="#Update-the-version-number-of-a-dependency">Update the version number of a dependency</a><a id="Update-the-version-number-of-a-dependency-1"></a><a class="docs-heading-anchor-permalink" href="#Update-the-version-number-of-a-dependency" title="Permalink"></a></h2><p>There are two types of builds</p><ol><li>Build everything (<code>deps/</code> and <code>src/</code>) from source code.  (Add <code>USE_BINARYBUILDER=0</code> to <code>Make.user</code>, see <a href="build.html#building-julia">Building Julia</a>)</li><li>Build from source (<code>src/</code>) with pre-compiled dependencies (default)</li></ol><p>When you want to update the version number of a dependency in <code>deps/</code>, you may want to use the following checklist:</p><pre><code class="language-md hljs">### Check list

Version numbers:
- [ ] `deps/$(libname).version`: `LIBNAME_VER`, `LIBNAME_BRANCH`, `LIBNAME_SHA1` and `LIBNAME_JLL_VER`
- [ ] `stdlib/$(LIBNAME_JLL_NAME)_jll/Project.toml`: `version`

Checksum:
- [ ] `deps/checksums/$(libname)`
- [ ] `deps/checksums/$(LIBNAME_JLL_NAME)-*/`: `md5` and `sha512`

Patches:
- [ ] `deps/$(libname).mk`
- [ ] `deps/patches/$(libname)-*.patch`</code></pre><p>Note:</p><ul><li>For specific dependencies, some items in the checklist may not exist.</li><li>For checksum file, it may be <strong>a single file</strong> without a suffix, or <strong>a folder</strong> containing two files.</li></ul><h3 id="Example:-OpenLibm"><a class="docs-heading-anchor" href="#Example:-OpenLibm">Example: <code>OpenLibm</code></a><a id="Example:-OpenLibm-1"></a><a class="docs-heading-anchor-permalink" href="#Example:-OpenLibm" title="Permalink"></a></h3><ol><li>Update Version numbers in <code>deps/openlibm.version</code><ul><li><code>OPENLIBM_VER := 0.X.Y</code></li><li><code>OPENLIBM_BRANCH = v0.X.Y</code></li><li><code>OPENLIBM_SHA1 = new-sha1-hash</code></li></ul></li><li>Update Version number in <code>stdlib/OpenLibm_jll/Project.toml</code><ul><li><code>version = &quot;0.X.Y+0&quot;</code></li></ul></li><li>Update checksums in <code>deps/checksums/openlibm</code><ul><li><code>make -f contrib/refresh_checksums.mk openlibm</code></li></ul></li><li>Check if the patch files <code>deps/patches/openlibm-*.patch</code> exist<ul><li>if patches don&#39;t exist, skip.</li><li>if patches exist, check if they have been merged into the new version and need to be removed.   When deleting a patch, remember to modify the corresponding Makefile file (<code>deps/openlibm.mk</code>).</li></ul></li></ol></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="../probes.html">« Instrumenting Julia with DTrace, and bpftrace</a><a class="docs-footer-nextpage" href="linux.html">Linux »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
