<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>More about types · The Julia Language</title><meta name="title" content="More about types · The Julia Language"/><meta property="og:title" content="More about types · The Julia Language"/><meta property="twitter:title" content="More about types · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox" checked/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="ast.html">Julia ASTs</a></li><li class="is-active"><a class="tocitem" href="types.html">More about types</a><ul class="internal"><li><a class="tocitem" href="#Types-and-sets-(and-Any-and-Union{}/Bottom)"><span>Types and sets (and <code>Any</code> and <code>Union{}</code>/<code>Bottom</code>)</span></a></li><li><a class="tocitem" href="#UnionAll-types"><span>UnionAll types</span></a></li><li><a class="tocitem" href="#Free-variables"><span>Free variables</span></a></li><li><a class="tocitem" href="#TypeNames"><span>TypeNames</span></a></li><li><a class="tocitem" href="#Tuple-types"><span>Tuple types</span></a></li><li><a class="tocitem" href="#Diagonal-types"><span>Diagonal types</span></a></li><li><a class="tocitem" href="#Subtyping-diagonal-variables"><span>Subtyping diagonal variables</span></a></li><li><a class="tocitem" href="#Introduction-to-the-internal-machinery"><span>Introduction to the internal machinery</span></a></li><li><a class="tocitem" href="#Subtyping-and-method-sorting"><span>Subtyping and method sorting</span></a></li></ul></li><li><a class="tocitem" href="object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="functions.html">Julia Functions</a></li><li><a class="tocitem" href="cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="subarrays.html">SubArrays</a></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Documentation of Julia&#39;s Internals</a></li><li class="is-active"><a href="types.html">More about types</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="types.html">More about types</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/types.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="More-about-types"><a class="docs-heading-anchor" href="#More-about-types">More about types</a><a id="More-about-types-1"></a><a class="docs-heading-anchor-permalink" href="#More-about-types" title="Permalink"></a></h1><p>If you&#39;ve used Julia for a while, you understand the fundamental role that types play.  Here we try to get under the hood, focusing particularly on <a href="../manual/types.html#Parametric-Types">Parametric Types</a>.</p><h2 id="Types-and-sets-(and-Any-and-Union{}/Bottom)"><a class="docs-heading-anchor" href="#Types-and-sets-(and-Any-and-Union{}/Bottom)">Types and sets (and <code>Any</code> and <code>Union{}</code>/<code>Bottom</code>)</a><a id="Types-and-sets-(and-Any-and-Union{}/Bottom)-1"></a><a class="docs-heading-anchor-permalink" href="#Types-and-sets-(and-Any-and-Union{}/Bottom)" title="Permalink"></a></h2><p>It&#39;s perhaps easiest to conceive of Julia&#39;s type system in terms of sets. While programs manipulate individual values, a type refers to a set of values. This is not the same thing as a collection; for example a <a href="../base/collections.html#Base.Set"><code>Set</code></a> of values is itself a single <code>Set</code> value. Rather, a type describes a set of <em>possible</em> values, expressing uncertainty about which value we have.</p><p>A <em>concrete</em> type <code>T</code> describes the set of values whose direct tag, as returned by the <a href="../base/base.html#Core.typeof"><code>typeof</code></a> function, is <code>T</code>. An <em>abstract</em> type describes some possibly-larger set of values.</p><p><a href="../base/base.html#Core.Any"><code>Any</code></a> describes the entire universe of possible values. <a href="../base/numbers.html#Core.Integer"><code>Integer</code></a> is a subset of <code>Any</code> that includes <code>Int</code>, <a href="../base/numbers.html#Core.Int8"><code>Int8</code></a>, and other concrete types. Internally, Julia also makes heavy use of another type known as <code>Bottom</code>, which can also be written as <code>Union{}</code>. This corresponds to the empty set.</p><p>Julia&#39;s types support the standard operations of set theory: you can ask whether <code>T1</code> is a &quot;subset&quot; (subtype) of <code>T2</code> with <code>T1 &lt;: T2</code>. Likewise, you intersect two types using <a href="../base/base.html#Base.typeintersect"><code>typeintersect</code></a>, take their union with <a href="../base/base.html#Core.Union"><code>Union</code></a>, and compute a type that contains their union with <a href="../base/base.html#Base.typejoin"><code>typejoin</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; typeintersect(Int, Float64)
Union{}

julia&gt; Union{Int, Float64}
Union{Float64, Int64}

julia&gt; typejoin(Int, Float64)
Real

julia&gt; typeintersect(Signed, Union{UInt8, Int8})
Int8

julia&gt; Union{Signed, Union{UInt8, Int8}}
Union{UInt8, Signed}

julia&gt; typejoin(Signed, Union{UInt8, Int8})
Integer

julia&gt; typeintersect(Tuple{Integer, Float64}, Tuple{Int, Real})
Tuple{Int64, Float64}

julia&gt; Union{Tuple{Integer, Float64}, Tuple{Int, Real}}
Union{Tuple{Int64, Real}, Tuple{Integer, Float64}}

julia&gt; typejoin(Tuple{Integer, Float64}, Tuple{Int, Real})
Tuple{Integer, Real}</code></pre><p>While these operations may seem abstract, they lie at the heart of Julia.  For example, method dispatch is implemented by stepping through the items in a method list until reaching one for which the type of the argument tuple is a subtype of the method signature. For this algorithm to work, it&#39;s important that methods be sorted by their specificity, and that the search begins with the most specific methods. Consequently, Julia also implements a partial order on types; this is achieved by functionality that is similar to <code>&lt;:</code>, but with differences that will be discussed below.</p><h2 id="UnionAll-types"><a class="docs-heading-anchor" href="#UnionAll-types">UnionAll types</a><a id="UnionAll-types-1"></a><a class="docs-heading-anchor-permalink" href="#UnionAll-types" title="Permalink"></a></h2><p>Julia&#39;s type system can also express an <em>iterated union</em> of types: a union of types over all values of some variable. This is needed to describe parametric types where the values of some parameters are not known.</p><p>For example, <a href="../base/arrays.html#Core.Array"><code>Array</code></a> has two parameters as in <code>Array{Int,2}</code>. If we did not know the element type, we could write <code>Array{T,2} where T</code>, which is the union of <code>Array{T,2}</code> for all values of <code>T</code>: <code>Union{Array{Int8,2}, Array{Int16,2}, ...}</code>.</p><p>Such a type is represented by a <code>UnionAll</code> object, which contains a variable (<code>T</code> in this example, of type <code>TypeVar</code>), and a wrapped type (<code>Array{T,2}</code> in this example).</p><p>Consider the following methods:</p><pre><code class="language-julia hljs">f1(A::Array) = 1
f2(A::Array{Int}) = 2
f3(A::Array{T}) where {T&lt;:Any} = 3
f4(A::Array{Any}) = 4</code></pre><p>The signature - as described in <a href="functions.html#Function-calls">Function calls</a> - of <code>f3</code> is a <code>UnionAll</code> type wrapping a tuple type: <code>Tuple{typeof(f3), Array{T}} where T</code>. All but <code>f4</code> can be called with <code>a = [1,2]</code>; all but <code>f2</code> can be called with <code>b = Any[1,2]</code>.</p><p>Let&#39;s look at these types a little more closely:</p><pre><code class="language-julia-repl hljs">julia&gt; dump(Array)
UnionAll
  var: TypeVar
    name: Symbol T
    lb: Union{}
    ub: Any
  body: UnionAll
    var: TypeVar
      name: Symbol N
      lb: Union{}
      ub: Any
    body: Array{T, N} &lt;: DenseArray{T, N}
      ref::MemoryRef{T}
      size::NTuple{N, Int64}</code></pre><p>This indicates that <code>Array</code> actually names a <code>UnionAll</code> type. There is one <code>UnionAll</code> type for each parameter, nested. The syntax <code>Array{Int,2}</code> is equivalent to <code>Array{Int}{2}</code>; internally each <code>UnionAll</code> is instantiated with a particular variable value, one at a time, outermost-first. This gives a natural meaning to the omission of trailing type parameters; <code>Array{Int}</code> gives a type equivalent to <code>Array{Int,N} where N</code>.</p><p>A <code>TypeVar</code> is not itself a type, but rather should be considered part of the structure of a <code>UnionAll</code> type. Type variables have lower and upper bounds on their values (in the fields <code>lb</code> and <code>ub</code>). The symbol <code>name</code> is purely cosmetic. Internally, <code>TypeVar</code>s are compared by address, so they are defined as mutable types to ensure that &quot;different&quot; type variables can be distinguished. However, by convention they should not be mutated.</p><p>One can construct <code>TypeVar</code>s manually:</p><pre><code class="language-julia-repl hljs">julia&gt; TypeVar(:V, Signed, Real)
Signed&lt;:V&lt;:Real</code></pre><p>There are convenience versions that allow you to omit any of these arguments except the <code>name</code> symbol.</p><p>The syntax <code>Array{T} where T&lt;:Integer</code> is lowered to</p><pre><code class="language-julia hljs">let T = TypeVar(:T,Integer)
    UnionAll(T, Array{T})
end</code></pre><p>so it is seldom necessary to construct a <code>TypeVar</code> manually (indeed, this is to be avoided).</p><h2 id="Free-variables"><a class="docs-heading-anchor" href="#Free-variables">Free variables</a><a id="Free-variables-1"></a><a class="docs-heading-anchor-permalink" href="#Free-variables" title="Permalink"></a></h2><p>The concept of a <em>free</em> type variable is extremely important in the type system. We say that a variable <code>V</code> is free in type <code>T</code> if <code>T</code> does not contain the <code>UnionAll</code> that introduces variable <code>V</code>. For example, the type <code>Array{Array{V} where V&lt;:Integer}</code> has no free variables, but the <code>Array{V}</code> part inside of it does have a free variable, <code>V</code>.</p><p>A type with free variables is, in some sense, not really a type at all. Consider the type <code>Array{Array{T}} where T</code>, which refers to all homogeneous arrays of arrays. The inner type <code>Array{T}</code>, seen by itself, might seem to refer to any kind of array. However, every element of the outer array must have the <em>same</em> array type, so <code>Array{T}</code> cannot refer to just any old array. One could say that <code>Array{T}</code> effectively &quot;occurs&quot; multiple times, and <code>T</code> must have the same value each &quot;time&quot;.</p><p>For this reason, the function <code>jl_has_free_typevars</code> in the C API is very important. Types for which it returns true will not give meaningful answers in subtyping and other type functions.</p><h2 id="TypeNames"><a class="docs-heading-anchor" href="#TypeNames">TypeNames</a><a id="TypeNames-1"></a><a class="docs-heading-anchor-permalink" href="#TypeNames" title="Permalink"></a></h2><p>The following two <a href="../base/arrays.html#Core.Array"><code>Array</code></a> types are functionally equivalent, yet print differently:</p><pre><code class="language-julia-repl hljs">julia&gt; TV, NV = TypeVar(:T), TypeVar(:N)
(T, N)

julia&gt; Array
Array

julia&gt; Array{TV, NV}
Array{T, N}</code></pre><p>These can be distinguished by examining the <code>name</code> field of the type, which is an object of type <code>TypeName</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; dump(Array{Int,1}.name)
TypeName
  name: Symbol Array
  module: Module Core
  names: empty SimpleVector
  wrapper: UnionAll
    var: TypeVar
      name: Symbol T
      lb: Union{}
      ub: Any
    body: UnionAll
      var: TypeVar
        name: Symbol N
        lb: Union{}
        ub: Any
      body: Array{T, N} &lt;: DenseArray{T, N}
  cache: SimpleVector
    ...

  linearcache: SimpleVector
    ...

  hash: Int64 -7900426068641098781
  mt: MethodTable
    name: Symbol Array
    defs: Nothing nothing
    cache: Nothing nothing
    max_args: Int64 0
    module: Module Core
    : Int64 0
    : Int64 0</code></pre><p>In this case, the relevant field is <code>wrapper</code>, which holds a reference to the top-level type used to make new <code>Array</code> types.</p><pre><code class="language-julia-repl hljs">julia&gt; pointer_from_objref(Array)
Ptr{Cvoid} @0x00007fcc7de64850

julia&gt; pointer_from_objref(Array.body.body.name.wrapper)
Ptr{Cvoid} @0x00007fcc7de64850

julia&gt; pointer_from_objref(Array{TV,NV})
Ptr{Cvoid} @0x00007fcc80c4d930

julia&gt; pointer_from_objref(Array{TV,NV}.name.wrapper)
Ptr{Cvoid} @0x00007fcc7de64850</code></pre><p>The <code>wrapper</code> field of <a href="../base/arrays.html#Core.Array"><code>Array</code></a> points to itself, but for <code>Array{TV,NV}</code> it points back to the original definition of the type.</p><p>What about the other fields? <code>hash</code> assigns an integer to each type.  To examine the <code>cache</code> field, it&#39;s helpful to pick a type that is less heavily used than Array. Let&#39;s first create our own type:</p><pre><code class="language-julia-repl hljs">julia&gt; struct MyType{T,N} end

julia&gt; MyType{Int,2}
MyType{Int64, 2}

julia&gt; MyType{Float32, 5}
MyType{Float32, 5}</code></pre><p>When you instantiate a parametric type, each concrete type gets saved in a type cache (<code>MyType.body.body.name.cache</code>). However, instances containing free type variables are not cached.</p><h2 id="Tuple-types"><a class="docs-heading-anchor" href="#Tuple-types">Tuple types</a><a id="Tuple-types-1"></a><a class="docs-heading-anchor-permalink" href="#Tuple-types" title="Permalink"></a></h2><p>Tuple types constitute an interesting special case.  For dispatch to work on declarations like <code>x::Tuple</code>, the type has to be able to accommodate any tuple.  Let&#39;s check the parameters:</p><pre><code class="language-julia-repl hljs">julia&gt; Tuple
Tuple

julia&gt; Tuple.parameters
svec(Vararg{Any})</code></pre><p>Unlike other types, tuple types are covariant in their parameters, so this definition permits <code>Tuple</code> to match any type of tuple:</p><pre><code class="language-julia-repl hljs">julia&gt; typeintersect(Tuple, Tuple{Int,Float64})
Tuple{Int64, Float64}

julia&gt; typeintersect(Tuple{Vararg{Any}}, Tuple{Int,Float64})
Tuple{Int64, Float64}</code></pre><p>However, if a variadic (<code>Vararg</code>) tuple type has free variables it can describe different kinds of tuples:</p><pre><code class="language-julia-repl hljs">julia&gt; typeintersect(Tuple{Vararg{T} where T}, Tuple{Int,Float64})
Tuple{Int64, Float64}

julia&gt; typeintersect(Tuple{Vararg{T}} where T, Tuple{Int,Float64})
Union{}</code></pre><p>Notice that when <code>T</code> is free with respect to the <code>Tuple</code> type (i.e. its binding <code>UnionAll</code> type is outside the <code>Tuple</code> type), only one <code>T</code> value must work over the whole type. Therefore a heterogeneous tuple does not match.</p><p>Finally, it&#39;s worth noting that <code>Tuple{}</code> is distinct:</p><pre><code class="language-julia-repl hljs">julia&gt; Tuple{}
Tuple{}

julia&gt; Tuple{}.parameters
svec()

julia&gt; typeintersect(Tuple{}, Tuple{Int})
Union{}</code></pre><p>What is the &quot;primary&quot; tuple-type?</p><pre><code class="language-julia-repl hljs">julia&gt; pointer_from_objref(Tuple)
Ptr{Cvoid} @0x00007f5998a04370

julia&gt; pointer_from_objref(Tuple{})
Ptr{Cvoid} @0x00007f5998a570d0

julia&gt; pointer_from_objref(Tuple.name.wrapper)
Ptr{Cvoid} @0x00007f5998a04370

julia&gt; pointer_from_objref(Tuple{}.name.wrapper)
Ptr{Cvoid} @0x00007f5998a04370</code></pre><p>so <code>Tuple == Tuple{Vararg{Any}}</code> is indeed the primary type.</p><h2 id="Diagonal-types"><a class="docs-heading-anchor" href="#Diagonal-types">Diagonal types</a><a id="Diagonal-types-1"></a><a class="docs-heading-anchor-permalink" href="#Diagonal-types" title="Permalink"></a></h2><p>Consider the type <code>Tuple{T,T} where T</code>. A method with this signature would look like:</p><pre><code class="language-julia hljs">f(x::T, y::T) where {T} = ...</code></pre><p>According to the usual interpretation of a <code>UnionAll</code> type, this <code>T</code> ranges over all types, including <code>Any</code>, so this type should be equivalent to <code>Tuple{Any,Any}</code>. However, this interpretation causes some practical problems.</p><p>First, a value of <code>T</code> needs to be available inside the method definition. For a call like <code>f(1, 1.0)</code>, it&#39;s not clear what <code>T</code> should be. It could be <code>Union{Int,Float64}</code>, or perhaps <a href="../base/numbers.html#Core.Real"><code>Real</code></a>. Intuitively, we expect the declaration <code>x::T</code> to mean <code>T === typeof(x)</code>. To make sure that invariant holds, we need <code>typeof(x) === typeof(y) === T</code> in this method. That implies the method should only be called for arguments of the exact same type.</p><p>It turns out that being able to dispatch on whether two values have the same type is very useful (this is used by the promotion system for example), so we have multiple reasons to want a different interpretation of <code>Tuple{T,T} where T</code>. To make this work we add the following rule to subtyping: if a variable occurs more than once in covariant position, it is restricted to ranging over only concrete types. (&quot;Covariant position&quot; means that only <code>Tuple</code> and <code>Union</code> types occur between an occurrence of a variable and the <code>UnionAll</code> type that introduces it.) Such variables are called &quot;diagonal variables&quot; or &quot;concrete variables&quot;.</p><p>So for example, <code>Tuple{T,T} where T</code> can be seen as <code>Union{Tuple{Int8,Int8}, Tuple{Int16,Int16}, ...}</code>, where <code>T</code> ranges over all concrete types. This gives rise to some interesting subtyping results. For example <code>Tuple{Real,Real}</code> is not a subtype of <code>Tuple{T,T} where T</code>, because it includes some types like <code>Tuple{Int8,Int16}</code> where the two elements have different types. <code>Tuple{Real,Real}</code> and <code>Tuple{T,T} where T</code> have the non-trivial intersection <code>Tuple{T,T} where T&lt;:Real</code>. However, <code>Tuple{Real}</code> <em>is</em> a subtype of <code>Tuple{T} where T</code>, because in that case <code>T</code> occurs only once and so is not diagonal.</p><p>Next consider a signature like the following:</p><pre><code class="language-julia hljs">f(a::Array{T}, x::T, y::T) where {T} = ...</code></pre><p>In this case, <code>T</code> occurs in invariant position inside <code>Array{T}</code>. That means whatever type of array is passed unambiguously determines the value of <code>T</code> – we say <code>T</code> has an <em>equality constraint</em> on it. Therefore in this case the diagonal rule is not really necessary, since the array determines <code>T</code> and we can then allow <code>x</code> and <code>y</code> to be of any subtypes of <code>T</code>. So variables that occur in invariant position are never considered diagonal. This choice of behavior is slightly controversial – some feel this definition should be written as</p><pre><code class="language-julia hljs">f(a::Array{T}, x::S, y::S) where {T, S&lt;:T} = ...</code></pre><p>to clarify whether <code>x</code> and <code>y</code> need to have the same type. In this version of the signature they would, or we could introduce a third variable for the type of <code>y</code> if <code>x</code> and <code>y</code> can have different types.</p><p>The next complication is the interaction of unions and diagonal variables, e.g.</p><pre><code class="language-julia hljs">f(x::Union{Nothing,T}, y::T) where {T} = ...</code></pre><p>Consider what this declaration means. <code>y</code> has type <code>T</code>. <code>x</code> then can have either the same type <code>T</code>, or else be of type <a href="../base/base.html#Core.Nothing"><code>Nothing</code></a>. So all of the following calls should match:</p><pre><code class="language-julia hljs">f(1, 1)
f(&quot;&quot;, &quot;&quot;)
f(2.0, 2.0)
f(nothing, 1)
f(nothing, &quot;&quot;)
f(nothing, 2.0)</code></pre><p>These examples are telling us something: when <code>x</code> is <code>nothing::Nothing</code>, there are no extra constraints on <code>y</code>. It is as if the method signature had <code>y::Any</code>. Indeed, we have the following type equivalence:</p><pre><code class="language-julia hljs">(Tuple{Union{Nothing,T},T} where T) == Union{Tuple{Nothing,Any}, Tuple{T,T} where T}</code></pre><p>The general rule is: a concrete variable in covariant position acts like it&#39;s not concrete if the subtyping algorithm only <em>uses</em> it once. When <code>x</code> has type <code>Nothing</code>, we don&#39;t need to use the <code>T</code> in <code>Union{Nothing,T}</code>; we only use it in the second slot. This arises naturally from the observation that in <code>Tuple{T} where T</code> restricting <code>T</code> to concrete types makes no difference; the type is equal to <code>Tuple{Any}</code> either way.</p><p>However, appearing in <em>invariant</em> position disqualifies a variable from being concrete whether that appearance of the variable is used or not. Otherwise types can behave differently depending on which other types they are compared to, making subtyping not transitive. For example, consider</p><pre><code class="language-julia hljs">Tuple{Int,Int8,Vector{Integer}} &lt;: Tuple{T,T,Vector{Union{Integer,T}}} where T</code></pre><p>If the <code>T</code> inside the <code>Union</code> is ignored, then <code>T</code> is concrete and the answer is &quot;false&quot; since the first two types aren&#39;t the same. But consider instead</p><pre><code class="language-julia hljs">Tuple{Int,Int8,Vector{Any}} &lt;: Tuple{T,T,Vector{Union{Integer,T}}} where T</code></pre><p>Now we cannot ignore the <code>T</code> in the <code>Union</code> (we must have <code>T == Any</code>), so <code>T</code> is not concrete and the answer is &quot;true&quot;. That would make the concreteness of <code>T</code> depend on the other type, which is not acceptable since a type must have a clear meaning on its own. Therefore the appearance of <code>T</code> inside <code>Vector</code> is considered in both cases.</p><h2 id="Subtyping-diagonal-variables"><a class="docs-heading-anchor" href="#Subtyping-diagonal-variables">Subtyping diagonal variables</a><a id="Subtyping-diagonal-variables-1"></a><a class="docs-heading-anchor-permalink" href="#Subtyping-diagonal-variables" title="Permalink"></a></h2><p>The subtyping algorithm for diagonal variables has two components: (1) identifying variable occurrences, and (2) ensuring that diagonal variables range over concrete types only.</p><p>The first task is accomplished by keeping counters <code>occurs_inv</code> and <code>occurs_cov</code> (in <code>src/subtype.c</code>) for each variable in the environment, tracking the number of invariant and covariant occurrences, respectively. A variable is diagonal when <code>occurs_inv == 0 &amp;&amp; occurs_cov &gt; 1</code>.</p><p>The second task is accomplished by imposing a condition on a variable&#39;s lower bound. As the subtyping algorithm runs, it narrows the bounds of each variable (raising lower bounds and lowering upper bounds) to keep track of the range of variable values for which the subtype relation would hold. When we are done evaluating the body of a <code>UnionAll</code> type whose variable is diagonal, we look at the final values of the bounds. Since the variable must be concrete, a contradiction occurs if its lower bound could not be a subtype of a concrete type. For example, an abstract type like <a href="../base/arrays.html#Core.AbstractArray"><code>AbstractArray</code></a> cannot be a subtype of a concrete type, but a concrete type like <code>Int</code> can be, and the empty type <code>Bottom</code> can be as well. If a lower bound fails this test the algorithm stops with the answer <code>false</code>.</p><p>For example, in the problem <code>Tuple{Int,String} &lt;: Tuple{T,T} where T</code>, we derive that this would be true if <code>T</code> were a supertype of <code>Union{Int,String}</code>. However, <code>Union{Int,String}</code> is an abstract type, so the relation does not hold.</p><p>This concreteness test is done by the function <code>is_leaf_bound</code>. Note that this test is slightly different from <code>jl_is_leaf_type</code>, since it also returns <code>true</code> for <code>Bottom</code>. Currently this function is heuristic, and does not catch all possible concrete types. The difficulty is that whether a lower bound is concrete might depend on the values of other type variable bounds. For example, <code>Vector{T}</code> is equivalent to the concrete type <code>Vector{Int}</code> only if both the upper and lower bounds of <code>T</code> equal <code>Int</code>. We have not yet worked out a complete algorithm for this.</p><h2 id="Introduction-to-the-internal-machinery"><a class="docs-heading-anchor" href="#Introduction-to-the-internal-machinery">Introduction to the internal machinery</a><a id="Introduction-to-the-internal-machinery-1"></a><a class="docs-heading-anchor-permalink" href="#Introduction-to-the-internal-machinery" title="Permalink"></a></h2><p>Most operations for dealing with types are found in the files <code>jltypes.c</code> and <code>subtype.c</code>. A good way to start is to watch subtyping in action. Build Julia with <code>make debug</code> and fire up Julia within a debugger. <a href="debuggingtips.html#gdb-debugging-tips">gdb debugging tips</a> has some tips which may be useful.</p><p>Because the subtyping code is used heavily in the REPL itself – and hence breakpoints in this code get triggered often – it will be easiest if you make the following definition:</p><pre><code class="language-julia-repl hljs">julia&gt; function mysubtype(a,b)
           ccall(:jl_breakpoint, Cvoid, (Any,), nothing)
           a &lt;: b
       end</code></pre><p>and then set a breakpoint in <code>jl_breakpoint</code>.  Once this breakpoint gets triggered, you can set breakpoints in other functions.</p><p>As a warm-up, try the following:</p><pre><code class="language-julia hljs">mysubtype(Tuple{Int, Float64}, Tuple{Integer, Real})</code></pre><p>We can make it more interesting by trying a more complex case:</p><pre><code class="language-julia hljs">mysubtype(Tuple{Array{Int,2}, Int8}, Tuple{Array{T}, T} where T)</code></pre><h2 id="Subtyping-and-method-sorting"><a class="docs-heading-anchor" href="#Subtyping-and-method-sorting">Subtyping and method sorting</a><a id="Subtyping-and-method-sorting-1"></a><a class="docs-heading-anchor-permalink" href="#Subtyping-and-method-sorting" title="Permalink"></a></h2><p>The <code>type_morespecific</code> functions are used for imposing a partial order on functions in method tables (from most-to-least specific). Specificity is strict; if <code>a</code> is more specific than <code>b</code>, then <code>a</code> does not equal <code>b</code> and <code>b</code> is not more specific than <code>a</code>.</p><p>If <code>a</code> is a strict subtype of <code>b</code>, then it is automatically considered more specific. From there, <code>type_morespecific</code> employs some less formal rules. For example, <code>subtype</code> is sensitive to the number of arguments, but <code>type_morespecific</code> may not be. In particular, <code>Tuple{Int,AbstractFloat}</code> is more specific than <code>Tuple{Integer}</code>, even though it is not a subtype.  (Of <code>Tuple{Int,AbstractFloat}</code> and <code>Tuple{Integer,Float64}</code>, neither is more specific than the other.)  Likewise, <code>Tuple{Int,Vararg{Int}}</code> is not a subtype of <code>Tuple{Integer}</code>, but it is considered more specific. However, <code>morespecific</code> does get a bonus for length: in particular, <code>Tuple{Int,Int}</code> is more specific than <code>Tuple{Int,Vararg{Int}}</code>.</p><p>If you&#39;re debugging how methods get sorted, it can be convenient to define the function:</p><pre><code class="language-julia hljs">type_morespecific(a, b) = ccall(:jl_type_morespecific, Cint, (Any,Any), a, b)</code></pre><p>which allows you to test whether tuple type <code>a</code> is more specific than tuple type <code>b</code>.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="ast.html">« Julia ASTs</a><a class="docs-footer-nextpage" href="object.html">Memory layout of Julia Objects »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
