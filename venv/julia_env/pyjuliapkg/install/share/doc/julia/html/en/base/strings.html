<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Strings · The Julia Language</title><meta name="title" content="Strings · The Julia Language"/><meta property="og:title" content="Strings · The Julia Language"/><meta property="twitter:title" content="Strings · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox" checked/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="base.html">Essentials</a></li><li><a class="tocitem" href="collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="math.html">Mathematics</a></li><li><a class="tocitem" href="numbers.html">Numbers</a></li><li class="is-active"><a class="tocitem" href="strings.html">Strings</a><ul class="internal"><li><a class="tocitem" href="#AnnotatedStrings"><span><code>AnnotatedString</code>s</span></a></li></ul></li><li><a class="tocitem" href="arrays.html">Arrays</a></li><li><a class="tocitem" href="parallel.html">Tasks</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="constants.html">Constants</a></li><li><a class="tocitem" href="file.html">Filesystem</a></li><li><a class="tocitem" href="io-network.html">I/O and Network</a></li><li><a class="tocitem" href="punctuation.html">Punctuation</a></li><li><a class="tocitem" href="sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="c.html">C Interface</a></li><li><a class="tocitem" href="libc.html">C Standard Library</a></li><li><a class="tocitem" href="stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Base</a></li><li class="is-active"><a href="strings.html">Strings</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="strings.html">Strings</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/base/strings.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="lib-strings"><a class="docs-heading-anchor" href="#lib-strings">Strings</a><a id="lib-strings-1"></a><a class="docs-heading-anchor-permalink" href="#lib-strings" title="Permalink"></a></h1><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.AbstractString" href="#Core.AbstractString"><code>Core.AbstractString</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>The <code>AbstractString</code> type is the supertype of all string implementations in Julia. Strings are encodings of sequences of <a href="https://unicode.org/">Unicode</a> code points as represented by the <code>AbstractChar</code> type. Julia makes a few assumptions about strings:</p><ul><li>Strings are encoded in terms of fixed-size &quot;code units&quot;<ul><li>Code units can be extracted with <code>codeunit(s, i)</code></li><li>The first code unit has index <code>1</code></li><li>The last code unit has index <code>ncodeunits(s)</code></li><li>Any index <code>i</code> such that <code>1 ≤ i ≤ ncodeunits(s)</code> is in bounds</li></ul></li><li>String indexing is done in terms of these code units:<ul><li>Characters are extracted by <code>s[i]</code> with a valid string index <code>i</code></li><li>Each <code>AbstractChar</code> in a string is encoded by one or more code units</li><li>Only the index of the first code unit of an <code>AbstractChar</code> is a valid index</li><li>The encoding of an <code>AbstractChar</code> is independent of what precedes or follows it</li><li>String encodings are <a href="https://en.wikipedia.org/wiki/Self-synchronizing_code">self-synchronizing</a> – i.e. <code>isvalid(s, i)</code> is O(1)</li></ul></li></ul><p>Some string functions that extract code units, characters or substrings from strings error if you pass them out-of-bounds or invalid string indices. This includes <code>codeunit(s, i)</code> and <code>s[i]</code>. Functions that do string index arithmetic take a more relaxed approach to indexing and give you the closest valid string index when in-bounds, or when out-of-bounds, behave as if there were an infinite number of characters padding each side of the string. Usually these imaginary padding characters have code unit length <code>1</code> but string types may choose different &quot;imaginary&quot; character sizes as makes sense for their implementations (e.g. substrings may pass index arithmetic through to the underlying string they provide a view into). Relaxed indexing functions include those intended for index arithmetic: <code>thisind</code>, <code>nextind</code> and <code>prevind</code>. This model allows index arithmetic to work with out-of-bounds indices as intermediate values so long as one never uses them to retrieve a character, which often helps avoid needing to code around edge cases.</p><p>See also <a href="strings.html#Base.codeunit"><code>codeunit</code></a>, <a href="strings.html#Base.ncodeunits-Tuple{AbstractString}"><code>ncodeunits</code></a>, <a href="strings.html#Base.thisind"><code>thisind</code></a>, <a href="arrays.html#Base.nextind"><code>nextind</code></a>, <a href="arrays.html#Base.prevind"><code>prevind</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L3-L38">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.AbstractChar" href="#Core.AbstractChar"><code>Core.AbstractChar</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>The <code>AbstractChar</code> type is the supertype of all character implementations in Julia. A character represents a Unicode code point, and can be converted to an integer via the <a href="strings.html#Base.codepoint"><code>codepoint</code></a> function in order to obtain the numerical value of the code point, or constructed from the same integer. These numerical values determine how characters are compared with <code>&lt;</code> and <code>==</code>, for example.  New <code>T &lt;: AbstractChar</code> types should define a <code>codepoint(::T)</code> method and a <code>T(::UInt32)</code> constructor, at minimum.</p><p>A given <code>AbstractChar</code> subtype may be capable of representing only a subset of Unicode, in which case conversion from an unsupported <code>UInt32</code> value may throw an error. Conversely, the built-in <a href="strings.html#Core.Char"><code>Char</code></a> type represents a <em>superset</em> of Unicode (in order to losslessly encode invalid byte streams), in which case conversion of a non-Unicode value <em>to</em> <code>UInt32</code> throws an error. The <a href="strings.html#Base.isvalid-Tuple{Any}"><code>isvalid</code></a> function can be used to check which codepoints are representable in a given <code>AbstractChar</code> type.</p><p>Internally, an <code>AbstractChar</code> type may use a variety of encodings.  Conversion via <code>codepoint(char)</code> will not reveal this encoding because it always returns the Unicode value of the character. <code>print(io, c)</code> of any <code>c::AbstractChar</code> produces an encoding determined by <code>io</code> (UTF-8 for all built-in <code>IO</code> types), via conversion to <code>Char</code> if necessary.</p><p><code>write(io, c)</code>, in contrast, may emit an encoding depending on <code>typeof(c)</code>, and <code>read(io, typeof(c))</code> should read the same encoding as <code>write</code>. New <code>AbstractChar</code> types must provide their own implementations of <code>write</code> and <code>read</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/char.jl#L3-L30">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Char" href="#Core.Char"><code>Core.Char</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Char(c::Union{Number,AbstractChar})</code></pre><p><code>Char</code> is a 32-bit <a href="strings.html#Core.AbstractChar"><code>AbstractChar</code></a> type that is the default representation of characters in Julia. <code>Char</code> is the type used for character literals like <code>&#39;x&#39;</code> and it is also the element type of <a href="strings.html#Core.String-Tuple{AbstractString}"><code>String</code></a>.</p><p>In order to losslessly represent arbitrary byte streams stored in a <code>String</code>, a <code>Char</code> value may store information that cannot be converted to a Unicode codepoint — converting such a <code>Char</code> to <code>UInt32</code> will throw an error. The <a href="strings.html#Base.isvalid-Tuple{Any}"><code>isvalid(c::Char)</code></a> function can be used to query whether <code>c</code> represents a valid Unicode character.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/char.jl#L33-L45">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.codepoint" href="#Base.codepoint"><code>Base.codepoint</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">codepoint(c::AbstractChar) -&gt; Integer</code></pre><p>Return the Unicode codepoint (an unsigned integer) corresponding to the character <code>c</code> (or throw an exception if <code>c</code> does not represent a valid character). For <code>Char</code>, this is a <code>UInt32</code> value, but <code>AbstractChar</code> types that represent only a subset of Unicode may return a different-sized integer (e.g. <code>UInt8</code>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/char.jl#L67-L75">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.length-Tuple{AbstractString}" href="#Base.length-Tuple{AbstractString}"><code>Base.length</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">length(s::AbstractString) -&gt; Int
length(s::AbstractString, i::Integer, j::Integer) -&gt; Int</code></pre><p>Return the number of characters in string <code>s</code> from indices <code>i</code> through <code>j</code>.</p><p>This is computed as the number of code unit indices from <code>i</code> to <code>j</code> which are valid character indices. With only a single string argument, this computes the number of characters in the entire string. With <code>i</code> and <code>j</code> arguments it computes the number of indices between <code>i</code> and <code>j</code> inclusive that are valid indices in the string <code>s</code>. In addition to in-bounds values, <code>i</code> may take the out-of-bounds value <code>ncodeunits(s) + 1</code> and <code>j</code> may take the out-of-bounds value <code>0</code>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The time complexity of this operation is linear in general. That is, it will take the time proportional to the number of bytes or characters in the string because it counts the value on the fly. This is in contrast to the method for arrays, which is a constant-time operation.</p></div></div><p>See also <a href="strings.html#Base.isvalid-Tuple{Any}"><code>isvalid</code></a>, <a href="strings.html#Base.ncodeunits-Tuple{AbstractString}"><code>ncodeunits</code></a>, <a href="collections.html#Base.lastindex"><code>lastindex</code></a>, <a href="strings.html#Base.thisind"><code>thisind</code></a>, <a href="arrays.html#Base.nextind"><code>nextind</code></a>, <a href="arrays.html#Base.prevind"><code>prevind</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; length(&quot;jμΛIα&quot;)
5</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L375-L403">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.sizeof-Tuple{AbstractString}" href="#Base.sizeof-Tuple{AbstractString}"><code>Base.sizeof</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sizeof(str::AbstractString)</code></pre><p>Size, in bytes, of the string <code>str</code>. Equal to the number of code units in <code>str</code> multiplied by the size, in bytes, of one code unit in <code>str</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; sizeof(&quot;&quot;)
0

julia&gt; sizeof(&quot;∀&quot;)
3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L162-L176">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:*-Tuple{Union{AbstractChar, AbstractString}, Vararg{Union{AbstractChar, AbstractString}}}" href="#Base.:*-Tuple{Union{AbstractChar, AbstractString}, Vararg{Union{AbstractChar, AbstractString}}}"><code>Base.:*</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">*(s::Union{AbstractString, AbstractChar}, t::Union{AbstractString, AbstractChar}...) -&gt; AbstractString</code></pre><p>Concatenate strings and/or characters, producing a <a href="strings.html#Core.String-Tuple{AbstractString}"><code>String</code></a> or <a href="strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a> (as appropriate). This is equivalent to calling the <a href="strings.html#Base.string"><code>string</code></a> or <a href="strings.html#Base.annotatedstring"><code>annotatedstring</code></a> function on the arguments. Concatenation of built-in string types always produces a value of type <code>String</code> but other string types may choose to return a string of a different type as appropriate.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; &quot;Hello &quot; * &quot;world&quot;
&quot;Hello world&quot;

julia&gt; &#39;j&#39; * &quot;ulia&quot;
&quot;julia&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L243-L260">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:^-Tuple{Union{AbstractChar, AbstractString}, Integer}" href="#Base.:^-Tuple{Union{AbstractChar, AbstractString}, Integer}"><code>Base.:^</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">^(s::Union{AbstractString,AbstractChar}, n::Integer) -&gt; AbstractString</code></pre><p>Repeat a string or character <code>n</code> times. This can also be written as <code>repeat(s, n)</code>.</p><p>See also <a href="arrays.html#Base.repeat"><code>repeat</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; &quot;Test &quot;^3
&quot;Test Test Test &quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L766-L778">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.string" href="#Base.string"><code>Base.string</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">string(n::Integer; base::Integer = 10, pad::Integer = 1)</code></pre><p>Convert an integer <code>n</code> to a string in the given <code>base</code>, optionally specifying a number of digits to pad to.</p><p>See also <a href="numbers.html#Base.digits"><code>digits</code></a>, <a href="numbers.html#Base.bitstring"><code>bitstring</code></a>, <a href="numbers.html#Base.count_zeros"><code>count_zeros</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; string(5, base = 13, pad = 4)
&quot;0005&quot;

julia&gt; string(-13, base = 5, pad = 4)
&quot;-0023&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L904-L920">source</a></section><section><div><pre><code class="language-julia hljs">string(xs...)</code></pre><p>Create a string from any values using the <a href="io-network.html#Base.print"><code>print</code></a> function.</p><p><code>string</code> should usually not be defined directly. Instead, define a method <code>print(io::IO, x::MyType)</code>. If <code>string(x)</code> for a certain type needs to be highly efficient, then it may make sense to add a method to <code>string</code> and define <code>print(io::IO, x::MyType) = print(io, string(x))</code> to ensure the functions are consistent.</p><p>See also: <a href="strings.html#Core.String-Tuple{AbstractString}"><code>String</code></a>, <a href="io-network.html#Base.repr-Tuple{MIME, Any}"><code>repr</code></a>, <a href="io-network.html#Base.sprint"><code>sprint</code></a>, <a href="base.html#Base.@show"><code>show</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; string(&quot;a&quot;, 1, true)
&quot;a1true&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/io.jl#L170-L188">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.repeat-Tuple{AbstractString, Integer}" href="#Base.repeat-Tuple{AbstractString, Integer}"><code>Base.repeat</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">repeat(s::AbstractString, r::Integer)</code></pre><p>Repeat a string <code>r</code> times. This can be written as <code>s^r</code>.</p><p>See also <a href="strings.html#Base.:^-Tuple{Union{AbstractChar, AbstractString}, Integer}"><code>^</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; repeat(&quot;ha&quot;, 3)
&quot;hahaha&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L751-L763">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.repeat-Tuple{AbstractChar, Integer}" href="#Base.repeat-Tuple{AbstractChar, Integer}"><code>Base.repeat</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">repeat(c::AbstractChar, r::Integer) -&gt; String</code></pre><p>Repeat a character <code>r</code> times. This can equivalently be accomplished by calling <a href="strings.html#Base.:^-Tuple{Union{AbstractChar, AbstractString}, Integer}"><code>c^r</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; repeat(&#39;A&#39;, 3)
&quot;AAA&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/string.jl#L560-L571">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.repr-Tuple{Any}" href="#Base.repr-Tuple{Any}"><code>Base.repr</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">repr(x; context=nothing)</code></pre><p>Create a string from any value using the <a href="io-network.html#Base.show-Tuple{IO, Any}"><code>show</code></a> function. You should not add methods to <code>repr</code>; define a <code>show</code> method instead.</p><p>The optional keyword argument <code>context</code> can be set to a <code>:key=&gt;value</code> pair, a tuple of <code>:key=&gt;value</code> pairs, or an <code>IO</code> or <a href="io-network.html#Base.IOContext"><code>IOContext</code></a> object whose attributes are used for the I/O stream passed to <code>show</code>.</p><p>Note that <code>repr(x)</code> is usually similar to how the value of <code>x</code> would be entered in Julia.  See also <a href="io-network.html#Base.repr-Tuple{MIME, Any}"><code>repr(MIME(&quot;text/plain&quot;), x)</code></a> to instead return a &quot;pretty-printed&quot; version of <code>x</code> designed more for human consumption, equivalent to the REPL display of <code>x</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Passing a tuple to keyword <code>context</code> requires Julia 1.7 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; repr(1)
&quot;1&quot;

julia&gt; repr(zeros(3))
&quot;[0.0, 0.0, 0.0]&quot;

julia&gt; repr(big(1/3))
&quot;0.333333333333333314829616256247390992939472198486328125&quot;

julia&gt; repr(big(1/3), context=:compact =&gt; true)
&quot;0.333333&quot;
</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/io.jl#L252-L285">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.String-Tuple{AbstractString}" href="#Core.String-Tuple{AbstractString}"><code>Core.String</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">String(s::AbstractString)</code></pre><p>Create a new <code>String</code> from an existing <code>AbstractString</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/string.jl#L111-L115">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.SubString" href="#Base.SubString"><code>Base.SubString</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SubString(s::AbstractString, i::Integer, j::Integer=lastindex(s))
SubString(s::AbstractString, r::UnitRange{&lt;:Integer})</code></pre><p>Like <a href="collections.html#Base.getindex"><code>getindex</code></a>, but returns a view into the parent string <code>s</code> within range <code>i:j</code> or <code>r</code> respectively instead of making a copy.</p><p>The <a href="arrays.html#Base.@views"><code>@views</code></a> macro converts any string slices <code>s[i:j]</code> into substrings <code>SubString(s, i, j)</code> in a block of code.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; SubString(&quot;abc&quot;, 1, 2)
&quot;ab&quot;

julia&gt; SubString(&quot;abc&quot;, 1:2)
&quot;ab&quot;

julia&gt; SubString(&quot;abc&quot;, 2)
&quot;bc&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/substring.jl#L3-L24">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.LazyString" href="#Base.LazyString"><code>Base.LazyString</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LazyString &lt;: AbstractString</code></pre><p>A lazy representation of string interpolation. This is useful when a string needs to be constructed in a context where performing the actual interpolation and string construction is unnecessary or undesirable (e.g. in error paths of functions).</p><p>This type is designed to be cheap to construct at runtime, trying to offload as much work as possible to either the macro or later printing operations.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; n = 5; str = LazyString(&quot;n is &quot;, n)
&quot;n is 5&quot;</code></pre><p>See also <a href="strings.html#Base.@lazy_str"><code>@lazy_str</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p><code>LazyString</code> requires Julia 1.8 or later.</p></div></div><p><strong>Extended help</strong></p><p><strong>Safety properties for concurrent programs</strong></p><p>A lazy string itself does not introduce any concurrency problems even if it is printed in multiple Julia tasks.  However, if <code>print</code> methods on a captured value can have a concurrency issue when invoked without synchronizations, printing the lazy string may cause an issue.  Furthermore, the <code>print</code> methods on the captured values may be invoked multiple times, though only exactly one result will be returned.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p><code>LazyString</code> is safe in the above sense in Julia 1.9 and later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/lazy.jl#L1-L35">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@lazy_str" href="#Base.@lazy_str"><code>Base.@lazy_str</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lazy&quot;str&quot;</code></pre><p>Create a <a href="strings.html#Base.LazyString"><code>LazyString</code></a> using regular string interpolation syntax. Note that interpolations are <em>evaluated</em> at LazyString construction time, but <em>printing</em> is delayed until the first access to the string.</p><p>See <a href="strings.html#Base.LazyString"><code>LazyString</code></a> documentation for the safety properties for concurrent programs.</p><p><strong>Examples</strong></p><pre><code class="nohighlight hljs">julia&gt; n = 5; str = lazy&quot;n is $n&quot;
&quot;n is 5&quot;

julia&gt; typeof(str)
LazyString</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p><code>lazy&quot;str&quot;</code> requires Julia 1.8 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/lazy.jl#L44-L65">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.transcode" href="#Base.transcode"><code>Base.transcode</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">transcode(T, src)</code></pre><p>Convert string data between Unicode encodings. <code>src</code> is either a <code>String</code> or a <code>Vector{UIntXX}</code> of UTF-XX code units, where <code>XX</code> is 8, 16, or 32. <code>T</code> indicates the encoding of the return value: <code>String</code> to return a (UTF-8 encoded) <code>String</code> or <code>UIntXX</code> to return a <code>Vector{UIntXX}</code> of UTF-<code>XX</code> data. (The alias <a href="c.html#Base.Cwchar_t"><code>Cwchar_t</code></a> can also be used as the integer type, for converting <code>wchar_t*</code> strings used by external C libraries.)</p><p>The <code>transcode</code> function succeeds as long as the input data can be reasonably represented in the target encoding; it always succeeds for conversions between UTF-XX encodings, even for invalid Unicode data.</p><p>Only conversion to/from UTF-8 is currently supported.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; str = &quot;αβγ&quot;
&quot;αβγ&quot;

julia&gt; transcode(UInt16, str)
3-element Vector{UInt16}:
 0x03b1
 0x03b2
 0x03b3

julia&gt; transcode(String, transcode(UInt16, str))
&quot;αβγ&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/cstring.jl#L129-L160">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unsafe_string" href="#Base.unsafe_string"><code>Base.unsafe_string</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unsafe_string(p::Ptr{UInt8}, [length::Integer])</code></pre><p>Copy a string from the address of a C-style (NUL-terminated) string encoded as UTF-8. (The pointer can be safely freed afterwards.) If <code>length</code> is specified (the length of the data in bytes), the string does not have to be NUL-terminated.</p><p>This function is labeled &quot;unsafe&quot; because it will crash if <code>p</code> is not a valid memory address to data of the requested length.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/string.jl#L88-L97">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.ncodeunits-Tuple{AbstractString}" href="#Base.ncodeunits-Tuple{AbstractString}"><code>Base.ncodeunits</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ncodeunits(s::AbstractString) -&gt; Int</code></pre><p>Return the number of code units in a string. Indices that are in bounds to access this string must satisfy <code>1 ≤ i ≤ ncodeunits(s)</code>. Not all such indices are valid – they may not be the start of a character, but they will return a code unit value when calling <code>codeunit(s,i)</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; ncodeunits(&quot;The Julia Language&quot;)
18

julia&gt; ncodeunits(&quot;∫eˣ&quot;)
6

julia&gt; ncodeunits(&#39;∫&#39;), ncodeunits(&#39;e&#39;), ncodeunits(&#39;ˣ&#39;)
(3, 1, 2)</code></pre><p>See also <a href="strings.html#Base.codeunit"><code>codeunit</code></a>, <a href="arrays.html#Base.checkbounds"><code>checkbounds</code></a>, <a href="base.html#Base.sizeof-Tuple{Type}"><code>sizeof</code></a>, <a href="collections.html#Base.length"><code>length</code></a>, <a href="collections.html#Base.lastindex"><code>lastindex</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L43-L65">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.codeunit" href="#Base.codeunit"><code>Base.codeunit</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">codeunit(s::AbstractString) -&gt; Type{&lt;:Union{UInt8, UInt16, UInt32}}</code></pre><p>Return the code unit type of the given string object. For ASCII, Latin-1, or UTF-8 encoded strings, this would be <code>UInt8</code>; for UCS-2 and UTF-16 it would be <code>UInt16</code>; for UTF-32 it would be <code>UInt32</code>. The code unit type need not be limited to these three types, but it&#39;s hard to think of widely used string encodings that don&#39;t use one of these units. <code>codeunit(s)</code> is the same as <code>typeof(codeunit(s,1))</code> when <code>s</code> is a non-empty string.</p><p>See also <a href="strings.html#Base.ncodeunits-Tuple{AbstractString}"><code>ncodeunits</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L68-L79">source</a></section><section><div><pre><code class="language-julia hljs">codeunit(s::AbstractString, i::Integer) -&gt; Union{UInt8, UInt16, UInt32}</code></pre><p>Return the code unit value in the string <code>s</code> at index <code>i</code>. Note that</p><pre><code class="nohighlight hljs">codeunit(s, i) :: codeunit(s)</code></pre><p>I.e. the value returned by <code>codeunit(s, i)</code> is of the type returned by <code>codeunit(s)</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = codeunit(&quot;Hello&quot;, 2)
0x65

julia&gt; typeof(a)
UInt8</code></pre><p>See also <a href="strings.html#Base.ncodeunits-Tuple{AbstractString}"><code>ncodeunits</code></a>, <a href="arrays.html#Base.checkbounds"><code>checkbounds</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L84-L104">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.codeunits" href="#Base.codeunits"><code>Base.codeunits</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">codeunits(s::AbstractString)</code></pre><p>Obtain a vector-like object containing the code units of a string. Returns a <code>CodeUnits</code> wrapper by default, but <code>codeunits</code> may optionally be defined for new string types if necessary.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; codeunits(&quot;Juλia&quot;)
6-element Base.CodeUnits{UInt8, String}:
 0x4a
 0x75
 0xce
 0xbb
 0x69
 0x61</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L812-L830">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.ascii" href="#Base.ascii"><code>Base.ascii</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ascii(s::AbstractString)</code></pre><p>Convert a string to <code>String</code> type and check that it contains only ASCII data, otherwise throwing an <code>ArgumentError</code> indicating the position of the first non-ASCII byte.</p><p>See also the <a href="strings.html#Base.isascii"><code>isascii</code></a> predicate to filter or replace non-ASCII characters.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; ascii(&quot;abcdeγfgh&quot;)
ERROR: ArgumentError: invalid ASCII at index 6 in &quot;abcdeγfgh&quot;
Stacktrace:
[...]

julia&gt; ascii(&quot;abcdefgh&quot;)
&quot;abcdefgh&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L1088-L1106">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Regex" href="#Base.Regex"><code>Base.Regex</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Regex(pattern[, flags]) &lt;: AbstractPattern</code></pre><p>A type representing a regular expression. <code>Regex</code> objects can be used to match strings with <a href="strings.html#Base.match"><code>match</code></a>.</p><p><code>Regex</code> objects can be created using the <a href="strings.html#Base.@r_str"><code>@r_str</code></a> string macro. The <code>Regex(pattern[, flags])</code> constructor is usually used if the <code>pattern</code> string needs to be interpolated. See the documentation of the string macro for details on flags.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>To escape interpolated variables use <code>\Q</code> and <code>\E</code> (e.g. <code>Regex(&quot;\\Q$x\\E&quot;)</code>)</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/regex.jl#L10-L22">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@r_str" href="#Base.@r_str"><code>Base.@r_str</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@r_str -&gt; Regex</code></pre><p>Construct a regex, such as <code>r&quot;^[a-z]*$&quot;</code>, without interpolation and unescaping (except for quotation mark <code>&quot;</code> which still has to be escaped). The regex also accepts one or more flags, listed after the ending quote, to change its behaviour:</p><ul><li><code>i</code> enables case-insensitive matching</li><li><code>m</code> treats the <code>^</code> and <code>$</code> tokens as matching the start and end of individual lines, as opposed to the whole string.</li><li><code>s</code> allows the <code>.</code> modifier to match newlines.</li><li><code>x</code> enables &quot;free-spacing mode&quot;: whitespace between regex tokens is ignored except when escaped with <code>\</code>,  and <code>#</code> in the regex is treated as starting a comment (which is ignored to the line ending).</li><li><code>a</code> enables ASCII mode (disables <code>UTF</code> and <code>UCP</code> modes). By default <code>\B</code>, <code>\b</code>, <code>\D</code>, <code>\d</code>, <code>\S</code>, <code>\s</code>, <code>\W</code>, <code>\w</code>, etc. match based on Unicode character properties. With this option, these sequences only match ASCII characters. This includes <code>\u</code> also, which will emit the specified character value directly as a single byte, and not attempt to encode it into UTF-8. Importantly, this option allows matching against invalid UTF-8 strings, by treating both matcher and target as simple bytes (as if they were ISO/IEC 8859-1 / Latin-1 bytes) instead of as character encodings. In this case, this option is often combined with <code>s</code>. This option can be further refined by starting the pattern with (<em>UCP) or (</em>UTF).</li></ul><p>See <a href="strings.html#Base.Regex"><code>Regex</code></a> if interpolation is needed.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; match(r&quot;a+.*b+.*?d$&quot;ism, &quot;Goodbye,\nOh, angry,\nBad world\n&quot;)
RegexMatch(&quot;angry,\nBad world&quot;)</code></pre><p>This regex has the first three flags enabled.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/regex.jl#L97-L128">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.SubstitutionString" href="#Base.SubstitutionString"><code>Base.SubstitutionString</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SubstitutionString(substr) &lt;: AbstractString</code></pre><p>Stores the given string <code>substr</code> as a <code>SubstitutionString</code>, for use in regular expression substitutions. Most commonly constructed using the <a href="strings.html#Base.@s_str"><code>@s_str</code></a> macro.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; SubstitutionString(&quot;Hello \\g&lt;name&gt;, it&#39;s \\1&quot;)
s&quot;Hello \g&lt;name&gt;, it&#39;s \1&quot;

julia&gt; subst = s&quot;Hello \g&lt;name&gt;, it&#39;s \1&quot;
s&quot;Hello \g&lt;name&gt;, it&#39;s \1&quot;

julia&gt; typeof(subst)
SubstitutionString{String}</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/regex.jl#L564-L581">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@s_str" href="#Base.@s_str"><code>Base.@s_str</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@s_str -&gt; SubstitutionString</code></pre><p>Construct a substitution string, used for regular expression substitutions.  Within the string, sequences of the form <code>\N</code> refer to the Nth capture group in the regex, and <code>\g&lt;groupname&gt;</code> refers to a named capture group with name <code>groupname</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; msg = &quot;#Hello# from Julia&quot;;

julia&gt; replace(msg, r&quot;#(.+)# from (?&lt;from&gt;\w+)&quot; =&gt; s&quot;FROM: \g&lt;from&gt;; MESSAGE: \1&quot;)
&quot;FROM: Julia; MESSAGE: Hello&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/regex.jl#L598-L612">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@raw_str" href="#Base.@raw_str"><code>Base.@raw_str</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@raw_str -&gt; String</code></pre><p>Create a raw string without interpolation and unescaping. The exception is that quotation marks still must be escaped. Backslashes escape both quotation marks and other backslashes, but only when a sequence of backslashes precedes a quote character. Thus, 2n backslashes followed by a quote encodes n backslashes and the end of the literal while 2n+1 backslashes followed by a quote encodes n backslashes followed by a quote character.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; println(raw&quot;\ $x&quot;)
\ $x

julia&gt; println(raw&quot;\&quot;&quot;)
&quot;

julia&gt; println(raw&quot;\\\&quot;&quot;)
\&quot;

julia&gt; println(raw&quot;\\x \\\&quot;&quot;)
\\x \&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/io.jl#L585-L609">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@b_str" href="#Base.@b_str"><code>Base.@b_str</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@b_str</code></pre><p>Create an immutable byte (<code>UInt8</code>) vector using string syntax.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; v = b&quot;12\x01\x02&quot;
4-element Base.CodeUnits{UInt8, String}:
 0x31
 0x32
 0x01
 0x02

julia&gt; v[2]
0x32</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/io.jl#L562-L579">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Docs.@html_str" href="#Base.Docs.@html_str"><code>Base.Docs.@html_str</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@html_str -&gt; Docs.HTML</code></pre><p>Create an <code>HTML</code> object from a literal string.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; html&quot;Julia&quot;
HTML{String}(&quot;Julia&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/utils.jl#L44-L54">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Docs.@text_str" href="#Base.Docs.@text_str"><code>Base.Docs.@text_str</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@text_str -&gt; Docs.Text</code></pre><p>Create a <code>Text</code> object from a literal string.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; text&quot;Julia&quot;
Julia</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/utils.jl#L98-L108">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isvalid-Tuple{Any}" href="#Base.isvalid-Tuple{Any}"><code>Base.isvalid</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isvalid(value) -&gt; Bool</code></pre><p>Return <code>true</code> if the given value is valid for its type, which currently can be either <code>AbstractChar</code> or <code>String</code> or <code>SubString{String}</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isvalid(Char(0xd800))
false

julia&gt; isvalid(SubString(String(UInt8[0xfe,0x80,0x80,0x80,0x80,0x80]),1,2))
false

julia&gt; isvalid(Char(0xd799))
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L13-L30">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isvalid-Tuple{Any, Any}" href="#Base.isvalid-Tuple{Any, Any}"><code>Base.isvalid</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isvalid(T, value) -&gt; Bool</code></pre><p>Return <code>true</code> if the given value is valid for that type. Types currently can be either <code>AbstractChar</code> or <code>String</code>. Values for <code>AbstractChar</code> can be of type <code>AbstractChar</code> or <a href="numbers.html#Core.UInt32"><code>UInt32</code></a>. Values for <code>String</code> can be of that type, <code>SubString{String}</code>, <code>Vector{UInt8}</code>, or a contiguous subarray thereof.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isvalid(Char, 0xd800)
false

julia&gt; isvalid(String, SubString(&quot;thisisvalid&quot;,1,5))
true

julia&gt; isvalid(Char, 0xd799)
true</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>Support for subarray values was added in Julia 1.6.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L33-L55">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isvalid-Tuple{AbstractString, Integer}" href="#Base.isvalid-Tuple{AbstractString, Integer}"><code>Base.isvalid</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isvalid(s::AbstractString, i::Integer) -&gt; Bool</code></pre><p>Predicate indicating whether the given index is the start of the encoding of a character in <code>s</code> or not. If <code>isvalid(s, i)</code> is true then <code>s[i]</code> will return the character whose encoding starts at that index, if it&#39;s false, then <code>s[i]</code> will raise an invalid index error or a bounds error depending on if <code>i</code> is in bounds. In order for <code>isvalid(s, i)</code> to be an O(1) function, the encoding of <code>s</code> must be <a href="https://en.wikipedia.org/wiki/Self-synchronizing_code">self-synchronizing</a>. This is a basic assumption of Julia&#39;s generic string support.</p><p>See also <a href="collections.html#Base.getindex"><code>getindex</code></a>, <a href="collections.html#Base.iterate"><code>iterate</code></a>, <a href="strings.html#Base.thisind"><code>thisind</code></a>, <a href="arrays.html#Base.nextind"><code>nextind</code></a>, <a href="arrays.html#Base.prevind"><code>prevind</code></a>, <a href="collections.html#Base.length"><code>length</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; str = &quot;αβγdef&quot;;

julia&gt; isvalid(str, 1)
true

julia&gt; str[1]
&#39;α&#39;: Unicode U+03B1 (category Ll: Letter, lowercase)

julia&gt; isvalid(str, 2)
false

julia&gt; str[2]
ERROR: StringIndexError: invalid index [2], valid nearby indices [1]=&gt;&#39;α&#39;, [3]=&gt;&#39;β&#39;
Stacktrace:
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L108-L140">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.match" href="#Base.match"><code>Base.match</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">match(r::Regex, s::AbstractString[, idx::Integer[, addopts]])</code></pre><p>Search for the first match of the regular expression <code>r</code> in <code>s</code> and return a <a href="strings.html#Base.RegexMatch"><code>RegexMatch</code></a> object containing the match, or nothing if the match failed. The matching substring can be retrieved by accessing <code>m.match</code> and the captured sequences can be retrieved by accessing <code>m.captures</code> The optional <code>idx</code> argument specifies an index at which to start the search.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; rx = r&quot;a(.)a&quot;
r&quot;a(.)a&quot;

julia&gt; m = match(rx, &quot;cabac&quot;)
RegexMatch(&quot;aba&quot;, 1=&quot;b&quot;)

julia&gt; m.captures
1-element Vector{Union{Nothing, SubString{String}}}:
 &quot;b&quot;

julia&gt; m.match
&quot;aba&quot;

julia&gt; match(rx, &quot;cabac&quot;, 3) === nothing
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/regex.jl#L386-L412">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.eachmatch" href="#Base.eachmatch"><code>Base.eachmatch</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">eachmatch(r::Regex, s::AbstractString; overlap::Bool=false)</code></pre><p>Search for all matches of the regular expression <code>r</code> in <code>s</code> and return an iterator over the matches. If <code>overlap</code> is <code>true</code>, the matching sequences are allowed to overlap indices in the original string, otherwise they must be from distinct character ranges.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; rx = r&quot;a.a&quot;
r&quot;a.a&quot;

julia&gt; m = eachmatch(rx, &quot;a1a2a3a&quot;)
Base.RegexMatchIterator{String}(r&quot;a.a&quot;, &quot;a1a2a3a&quot;, false)

julia&gt; collect(m)
2-element Vector{RegexMatch}:
 RegexMatch(&quot;a1a&quot;)
 RegexMatch(&quot;a3a&quot;)

julia&gt; collect(eachmatch(rx, &quot;a1a2a3a&quot;, overlap = true))
3-element Vector{RegexMatch}:
 RegexMatch(&quot;a1a&quot;)
 RegexMatch(&quot;a2a&quot;)
 RegexMatch(&quot;a3a&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/regex.jl#L754-L780">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.RegexMatch" href="#Base.RegexMatch"><code>Base.RegexMatch</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">RegexMatch &lt;: AbstractMatch</code></pre><p>A type representing a single match to a <a href="strings.html#Base.Regex"><code>Regex</code></a> found in a string. Typically created from the <a href="strings.html#Base.match"><code>match</code></a> function.</p><p>The <code>match</code> field stores the substring of the entire matched string. The <code>captures</code> field stores the substrings for each capture group, indexed by number. To index by capture group name, the entire match object should be indexed instead, as shown in the examples. The location of the start of the match is stored in the <code>offset</code> field. The <code>offsets</code> field stores the locations of the start of each capture group, with 0 denoting a group that was not captured.</p><p>This type can be used as an iterator over the capture groups of the <code>Regex</code>, yielding the substrings captured in each group. Because of this, the captures of a match can be destructured. If a group was not captured, <code>nothing</code> will be yielded instead of a substring.</p><p>Methods that accept a <code>RegexMatch</code> object are defined for <a href="collections.html#Base.iterate"><code>iterate</code></a>, <a href="collections.html#Base.length"><code>length</code></a>, <a href="collections.html#Base.eltype"><code>eltype</code></a>, <a href="strings.html#Base.keys-Tuple{RegexMatch}"><code>keys</code></a>, <a href="collections.html#Base.haskey"><code>haskey</code></a>, and <a href="collections.html#Base.getindex"><code>getindex</code></a>, where keys are the the names or numbers of a capture group. See <a href="strings.html#Base.keys-Tuple{RegexMatch}"><code>keys</code></a> for more information.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; m = match(r&quot;(?&lt;hour&gt;\d+):(?&lt;minute&gt;\d+)(am|pm)?&quot;, &quot;11:30 in the morning&quot;)
RegexMatch(&quot;11:30&quot;, hour=&quot;11&quot;, minute=&quot;30&quot;, 3=nothing)

julia&gt; m.match
&quot;11:30&quot;

julia&gt; m.captures
3-element Vector{Union{Nothing, SubString{String}}}:
 &quot;11&quot;
 &quot;30&quot;
 nothing


julia&gt; m[&quot;minute&quot;]
&quot;30&quot;

julia&gt; hr, min, ampm = m; # destructure capture groups by iteration

julia&gt; hr
&quot;11&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/regex.jl#L173-L220">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.keys-Tuple{RegexMatch}" href="#Base.keys-Tuple{RegexMatch}"><code>Base.keys</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">keys(m::RegexMatch) -&gt; Vector</code></pre><p>Return a vector of keys for all capture groups of the underlying regex. A key is included even if the capture group fails to match. That is, <code>idx</code> will be in the return value even if <code>m[idx] == nothing</code>.</p><p>Unnamed capture groups will have integer keys corresponding to their index. Named capture groups will have string keys.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>This method was added in Julia 1.7</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; keys(match(r&quot;(?&lt;hour&gt;\d+):(?&lt;minute&gt;\d+)(am|pm)?&quot;, &quot;11:30&quot;))
3-element Vector{Any}:
  &quot;hour&quot;
  &quot;minute&quot;
 3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/regex.jl#L233-L254">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isless-Tuple{AbstractString, AbstractString}" href="#Base.isless-Tuple{AbstractString, AbstractString}"><code>Base.isless</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isless(a::AbstractString, b::AbstractString) -&gt; Bool</code></pre><p>Test whether string <code>a</code> comes before string <code>b</code> in alphabetical order (technically, in lexicographical order by Unicode code points).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isless(&quot;a&quot;, &quot;b&quot;)
true

julia&gt; isless(&quot;β&quot;, &quot;α&quot;)
false

julia&gt; isless(&quot;a&quot;, &quot;a&quot;)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L341-L358">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:==-Tuple{AbstractString, AbstractString}" href="#Base.:==-Tuple{AbstractString, AbstractString}"><code>Base.:==</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">==(a::AbstractString, b::AbstractString) -&gt; Bool</code></pre><p>Test whether two strings are equal character by character (technically, Unicode code point by code point). Should either string be a <a href="strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a> the string properties must match too.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; &quot;abc&quot; == &quot;abc&quot;
true

julia&gt; &quot;abc&quot; == &quot;αβγ&quot;
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L323-L338">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.cmp-Tuple{AbstractString, AbstractString}" href="#Base.cmp-Tuple{AbstractString, AbstractString}"><code>Base.cmp</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cmp(a::AbstractString, b::AbstractString) -&gt; Int</code></pre><p>Compare two strings. Return <code>0</code> if both strings have the same length and the character at each index is the same in both strings. Return <code>-1</code> if <code>a</code> is a prefix of <code>b</code>, or if <code>a</code> comes before <code>b</code> in alphabetical order. Return <code>1</code> if <code>b</code> is a prefix of <code>a</code>, or if <code>b</code> comes before <code>a</code> in alphabetical order (technically, lexicographical order by Unicode code points).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; cmp(&quot;abc&quot;, &quot;abc&quot;)
0

julia&gt; cmp(&quot;ab&quot;, &quot;abc&quot;)
-1

julia&gt; cmp(&quot;abc&quot;, &quot;ab&quot;)
1

julia&gt; cmp(&quot;ab&quot;, &quot;ac&quot;)
-1

julia&gt; cmp(&quot;ac&quot;, &quot;ab&quot;)
1

julia&gt; cmp(&quot;α&quot;, &quot;a&quot;)
1

julia&gt; cmp(&quot;b&quot;, &quot;β&quot;)
-1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L279-L311">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.lpad" href="#Base.lpad"><code>Base.lpad</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lpad(s, n::Integer, p::Union{AbstractChar,AbstractString}=&#39; &#39;) -&gt; String</code></pre><p>Stringify <code>s</code> and pad the resulting string on the left with <code>p</code> to make it <code>n</code> characters (in <a href="strings.html#Base.Unicode.textwidth"><code>textwidth</code></a>) long. If <code>s</code> is already <code>n</code> characters long, an equal string is returned. Pad with spaces by default.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; lpad(&quot;March&quot;, 10)
&quot;     March&quot;</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>In Julia 1.7, this function was changed to use <code>textwidth</code> rather than a raw character (codepoint) count.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L452-L466">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.rpad" href="#Base.rpad"><code>Base.rpad</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rpad(s, n::Integer, p::Union{AbstractChar,AbstractString}=&#39; &#39;) -&gt; String</code></pre><p>Stringify <code>s</code> and pad the resulting string on the right with <code>p</code> to make it <code>n</code> characters (in <a href="strings.html#Base.Unicode.textwidth"><code>textwidth</code></a>) long. If <code>s</code> is already <code>n</code> characters long, an equal string is returned. Pad with spaces by default.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; rpad(&quot;March&quot;, 20)
&quot;March               &quot;</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>In Julia 1.7, this function was changed to use <code>textwidth</code> rather than a raw character (codepoint) count.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L484-L498">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findfirst-Tuple{AbstractString, AbstractString}" href="#Base.findfirst-Tuple{AbstractString, AbstractString}"><code>Base.findfirst</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findfirst(pattern::AbstractString, string::AbstractString)
findfirst(pattern::AbstractPattern, string::String)</code></pre><p>Find the first occurrence of <code>pattern</code> in <code>string</code>. Equivalent to <a href="arrays.html#Base.findnext-Tuple{Any, Integer}"><code>findnext(pattern, string, firstindex(s))</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; findfirst(&quot;z&quot;, &quot;Hello to the world&quot;) # returns nothing, but not printed in the REPL

julia&gt; findfirst(&quot;Julia&quot;, &quot;JuliaLang&quot;)
1:5</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/search.jl#L105-L119">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findnext-Tuple{AbstractString, AbstractString, Integer}" href="#Base.findnext-Tuple{AbstractString, AbstractString, Integer}"><code>Base.findnext</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findnext(pattern::AbstractString, string::AbstractString, start::Integer)
findnext(pattern::AbstractPattern, string::String, start::Integer)</code></pre><p>Find the next occurrence of <code>pattern</code> in <code>string</code> starting at position <code>start</code>. <code>pattern</code> can be either a string, or a regular expression, in which case <code>string</code> must be of type <code>String</code>.</p><p>The return value is a range of indices where the matching sequence is found, such that <code>s[findnext(x, s, i)] == x</code>:</p><p><code>findnext(&quot;substring&quot;, string, i)</code> == <code>start:stop</code> such that <code>string[start:stop] == &quot;substring&quot;</code> and <code>i &lt;= start</code>, or <code>nothing</code> if unmatched.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; findnext(&quot;z&quot;, &quot;Hello to the world&quot;, 1) === nothing
true

julia&gt; findnext(&quot;o&quot;, &quot;Hello to the world&quot;, 6)
8:8

julia&gt; findnext(&quot;Lang&quot;, &quot;JuliaLang&quot;, 2)
6:9</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/search.jl#L293-L318">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findnext-Tuple{AbstractChar, AbstractString, Integer}" href="#Base.findnext-Tuple{AbstractChar, AbstractString, Integer}"><code>Base.findnext</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findnext(ch::AbstractChar, string::AbstractString, start::Integer)</code></pre><p>Find the next occurrence of character <code>ch</code> in <code>string</code> starting at position <code>start</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p>This method requires at least Julia 1.3.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; findnext(&#39;z&#39;, &quot;Hello to the world&quot;, 1) === nothing
true

julia&gt; findnext(&#39;o&#39;, &quot;Hello to the world&quot;, 6)
8</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/search.jl#L321-L337">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findlast-Tuple{AbstractString, AbstractString}" href="#Base.findlast-Tuple{AbstractString, AbstractString}"><code>Base.findlast</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findlast(pattern::AbstractString, string::AbstractString)</code></pre><p>Find the last occurrence of <code>pattern</code> in <code>string</code>. Equivalent to <a href="arrays.html#Base.findprev-Tuple{Any, Integer}"><code>findprev(pattern, string, lastindex(string))</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; findlast(&quot;o&quot;, &quot;Hello to the world&quot;)
15:15

julia&gt; findfirst(&quot;Julia&quot;, &quot;JuliaLang&quot;)
1:5</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/search.jl#L365-L379">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findlast-Tuple{AbstractChar, AbstractString}" href="#Base.findlast-Tuple{AbstractChar, AbstractString}"><code>Base.findlast</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findlast(ch::AbstractChar, string::AbstractString)</code></pre><p>Find the last occurrence of character <code>ch</code> in <code>string</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p>This method requires at least Julia 1.3.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; findlast(&#39;p&#39;, &quot;happy&quot;)
4

julia&gt; findlast(&#39;z&#39;, &quot;happy&quot;) === nothing
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/search.jl#L400-L416">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findprev-Tuple{AbstractString, AbstractString, Integer}" href="#Base.findprev-Tuple{AbstractString, AbstractString, Integer}"><code>Base.findprev</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findprev(pattern::AbstractString, string::AbstractString, start::Integer)</code></pre><p>Find the previous occurrence of <code>pattern</code> in <code>string</code> starting at position <code>start</code>.</p><p>The return value is a range of indices where the matching sequence is found, such that <code>s[findprev(x, s, i)] == x</code>:</p><p><code>findprev(&quot;substring&quot;, string, i)</code> == <code>start:stop</code> such that <code>string[start:stop] == &quot;substring&quot;</code> and <code>stop &lt;= i</code>, or <code>nothing</code> if unmatched.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; findprev(&quot;z&quot;, &quot;Hello to the world&quot;, 18) === nothing
true

julia&gt; findprev(&quot;o&quot;, &quot;Hello to the world&quot;, 18)
15:15

julia&gt; findprev(&quot;Julia&quot;, &quot;JuliaLang&quot;, 6)
1:5</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/search.jl#L611-L633">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.occursin" href="#Base.occursin"><code>Base.occursin</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">occursin(needle::Union{AbstractString,AbstractPattern,AbstractChar}, haystack::AbstractString)</code></pre><p>Determine whether the first argument is a substring of the second. If <code>needle</code> is a regular expression, checks whether <code>haystack</code> contains a match.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; occursin(&quot;Julia&quot;, &quot;JuliaLang is pretty cool!&quot;)
true

julia&gt; occursin(&#39;a&#39;, &quot;JuliaLang is pretty cool!&quot;)
true

julia&gt; occursin(r&quot;a.a&quot;, &quot;aba&quot;)
true

julia&gt; occursin(r&quot;a.a&quot;, &quot;abba&quot;)
false</code></pre><p>See also <a href="strings.html#Base.contains"><code>contains</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/search.jl#L676-L698">source</a></section><section><div><pre><code class="language-julia hljs">occursin(haystack)</code></pre><p>Create a function that checks whether its argument occurs in <code>haystack</code>, i.e. a function equivalent to <code>needle -&gt; occursin(needle, haystack)</code>.</p><p>The returned function is of type <code>Base.Fix2{typeof(occursin)}</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>This method requires Julia 1.6 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; search_f = occursin(&quot;JuliaLang is a programming language&quot;);

julia&gt; search_f(&quot;JuliaLang&quot;)
true

julia&gt; search_f(&quot;Python&quot;)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/search.jl#L702-L723">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.reverse-Tuple{Union{SubString{String}, String}}" href="#Base.reverse-Tuple{Union{SubString{String}, String}}"><code>Base.reverse</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">reverse(s::AbstractString) -&gt; AbstractString</code></pre><p>Reverses a string. Technically, this function reverses the codepoints in a string and its main utility is for reversed-order string processing, especially for reversed regular-expression searches. See also <a href="arrays.html#Base.reverseind"><code>reverseind</code></a> to convert indices in <code>s</code> to indices in <code>reverse(s)</code> and vice-versa, and <code>graphemes</code> from module <code>Unicode</code> to operate on user-visible &quot;characters&quot; (graphemes) rather than codepoints. See also <a href="iterators.html#Base.Iterators.reverse"><code>Iterators.reverse</code></a> for reverse-order iteration without making a copy. Custom string types must implement the <code>reverse</code> function themselves and should typically return a string with the same type and encoding. If they return a string with a different encoding, they must also override <code>reverseind</code> for that string type to satisfy <code>s[reverseind(s,i)] == reverse(s)[i]</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; reverse(&quot;JuliaLang&quot;)
&quot;gnaLailuJ&quot;</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The examples below may be rendered differently on different systems. The comments indicate how they&#39;re supposed to be rendered</p></div></div><p>Combining characters can lead to surprising results:</p><pre><code class="language-julia-repl hljs">julia&gt; reverse(&quot;ax̂e&quot;) # hat is above x in the input, above e in the output
&quot;êxa&quot;

julia&gt; using Unicode

julia&gt; join(reverse(collect(graphemes(&quot;ax̂e&quot;)))) # reverses graphemes; hat is above x in both in- and output
&quot;ex̂a&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/substring.jl#L145-L180">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.replace-Tuple{IO, AbstractString, Vararg{Pair}}" href="#Base.replace-Tuple{IO, AbstractString, Vararg{Pair}}"><code>Base.replace</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">replace([io::IO], s::AbstractString, pat=&gt;r, [pat2=&gt;r2, ...]; [count::Integer])</code></pre><p>Search for the given pattern <code>pat</code> in <code>s</code>, and replace each occurrence with <code>r</code>. If <code>count</code> is provided, replace at most <code>count</code> occurrences. <code>pat</code> may be a single character, a vector or a set of characters, a string, or a regular expression. If <code>r</code> is a function, each occurrence is replaced with <code>r(s)</code> where <code>s</code> is the matched substring (when <code>pat</code> is a <code>AbstractPattern</code> or <code>AbstractString</code>) or character (when <code>pat</code> is an <code>AbstractChar</code> or a collection of <code>AbstractChar</code>). If <code>pat</code> is a regular expression and <code>r</code> is a <a href="strings.html#Base.SubstitutionString"><code>SubstitutionString</code></a>, then capture group references in <code>r</code> are replaced with the corresponding matched text. To remove instances of <code>pat</code> from <code>string</code>, set <code>r</code> to the empty <code>String</code> (<code>&quot;&quot;</code>).</p><p>The return value is a new string after the replacements.  If the <code>io::IO</code> argument is supplied, the transformed string is instead written to <code>io</code> (returning <code>io</code>). (For example, this can be used in conjunction with an <a href="io-network.html#Base.IOBuffer"><code>IOBuffer</code></a> to re-use a pre-allocated buffer array in-place.)</p><p>Multiple patterns can be specified, and they will be applied left-to-right simultaneously, so only one pattern will be applied to any character, and the patterns will only be applied to the input text, not the replacements.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Support for multiple patterns requires version 1.7.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.10</header><div class="admonition-body"><p>The <code>io::IO</code> argument requires version 1.10.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; replace(&quot;Python is a programming language.&quot;, &quot;Python&quot; =&gt; &quot;Julia&quot;)
&quot;Julia is a programming language.&quot;

julia&gt; replace(&quot;The quick foxes run quickly.&quot;, &quot;quick&quot; =&gt; &quot;slow&quot;, count=1)
&quot;The slow foxes run quickly.&quot;

julia&gt; replace(&quot;The quick foxes run quickly.&quot;, &quot;quick&quot; =&gt; &quot;&quot;, count=1)
&quot;The  foxes run quickly.&quot;

julia&gt; replace(&quot;The quick foxes run quickly.&quot;, r&quot;fox(es)?&quot; =&gt; s&quot;bus\1&quot;)
&quot;The quick buses run quickly.&quot;

julia&gt; replace(&quot;abcabc&quot;, &quot;a&quot; =&gt; &quot;b&quot;, &quot;b&quot; =&gt; &quot;c&quot;, r&quot;.+&quot; =&gt; &quot;a&quot;)
&quot;bca&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L886-L932">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.eachsplit" href="#Base.eachsplit"><code>Base.eachsplit</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">eachsplit(str::AbstractString, dlm; limit::Integer=0, keepempty::Bool=true)
eachsplit(str::AbstractString; limit::Integer=0, keepempty::Bool=false)</code></pre><p>Split <code>str</code> on occurrences of the delimiter(s) <code>dlm</code> and return an iterator over the substrings.  <code>dlm</code> can be any of the formats allowed by <a href="arrays.html#Base.findnext-Tuple{Any, Integer}"><code>findnext</code></a>&#39;s first argument (i.e. as a string, regular expression or a function), or as a single character or collection of characters.</p><p>If <code>dlm</code> is omitted, it defaults to <a href="strings.html#Base.Unicode.isspace"><code>isspace</code></a>.</p><p>The optional keyword arguments are:</p><ul><li><code>limit</code>: the maximum size of the result. <code>limit=0</code> implies no maximum (default)</li><li><code>keepempty</code>: whether empty fields should be kept in the result. Default is <code>false</code> without a <code>dlm</code> argument, <code>true</code> with a <code>dlm</code> argument.</li></ul><p>See also <a href="strings.html#Base.split"><code>split</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>The <code>eachsplit</code> function requires at least Julia 1.8.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = &quot;Ma.rch&quot;
&quot;Ma.rch&quot;

julia&gt; b = eachsplit(a, &quot;.&quot;)
Base.SplitIterator{String, String}(&quot;Ma.rch&quot;, &quot;.&quot;, 0, true)

julia&gt; collect(b)
2-element Vector{SubString{String}}:
 &quot;Ma&quot;
 &quot;rch&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L516-L550">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.eachrsplit" href="#Base.eachrsplit"><code>Base.eachrsplit</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">eachrsplit(str::AbstractString, dlm; limit::Integer=0, keepempty::Bool=true)
eachrsplit(str::AbstractString; limit::Integer=0, keepempty::Bool=false)</code></pre><p>Return an iterator over <code>SubString</code>s of <code>str</code>, produced when splitting on the delimiter(s) <code>dlm</code>, and yielded in reverse order (from right to left). <code>dlm</code> can be any of the formats allowed by <a href="arrays.html#Base.findprev-Tuple{Any, Integer}"><code>findprev</code></a>&#39;s first argument (i.e. a string, a single character or a function), or a collection of characters.</p><p>If <code>dlm</code> is omitted, it defaults to <a href="strings.html#Base.Unicode.isspace"><code>isspace</code></a>, and <code>keepempty</code> default to <code>false</code>.</p><p>The optional keyword arguments are:</p><ul><li>If <code>limit &gt; 0</code>, the iterator will split at most <code>limit - 1</code> times before returning the rest of the string unsplit. <code>limit &lt; 1</code> implies no cap to splits (default).</li><li><code>keepempty</code>: whether empty fields should be returned when iterating Default is <code>false</code> without a <code>dlm</code> argument, <code>true</code> with a <code>dlm</code> argument.</li></ul><p>Note that unlike <a href="strings.html#Base.split"><code>split</code></a>, <a href="strings.html#Base.rsplit"><code>rsplit</code></a> and <a href="strings.html#Base.eachsplit"><code>eachsplit</code></a>, this function iterates the substrings right to left as they occur in the input.</p><p>See also <a href="strings.html#Base.eachsplit"><code>eachsplit</code></a>, <a href="strings.html#Base.rsplit"><code>rsplit</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>This function requires Julia 1.11 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = &quot;Ma.r.ch&quot;;

julia&gt; collect(eachrsplit(a, &quot;.&quot;)) == [&quot;ch&quot;, &quot;r&quot;, &quot;Ma&quot;]
true

julia&gt; collect(eachrsplit(a, &quot;.&quot;; limit=2)) == [&quot;ch&quot;, &quot;Ma.r&quot;]
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L613-L648">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.split" href="#Base.split"><code>Base.split</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">split(str::AbstractString, dlm; limit::Integer=0, keepempty::Bool=true)
split(str::AbstractString; limit::Integer=0, keepempty::Bool=false)</code></pre><p>Split <code>str</code> into an array of substrings on occurrences of the delimiter(s) <code>dlm</code>.  <code>dlm</code> can be any of the formats allowed by <a href="arrays.html#Base.findnext-Tuple{Any, Integer}"><code>findnext</code></a>&#39;s first argument (i.e. as a string, regular expression or a function), or as a single character or collection of characters.</p><p>If <code>dlm</code> is omitted, it defaults to <a href="strings.html#Base.Unicode.isspace"><code>isspace</code></a>.</p><p>The optional keyword arguments are:</p><ul><li><code>limit</code>: the maximum size of the result. <code>limit=0</code> implies no maximum (default)</li><li><code>keepempty</code>: whether empty fields should be kept in the result. Default is <code>false</code> without a <code>dlm</code> argument, <code>true</code> with a <code>dlm</code> argument.</li></ul><p>See also <a href="strings.html#Base.rsplit"><code>rsplit</code></a>, <a href="strings.html#Base.eachsplit"><code>eachsplit</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = &quot;Ma.rch&quot;
&quot;Ma.rch&quot;

julia&gt; split(a, &quot;.&quot;)
2-element Vector{SubString{String}}:
 &quot;Ma&quot;
 &quot;rch&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L708-L736">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.rsplit" href="#Base.rsplit"><code>Base.rsplit</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rsplit(s::AbstractString; limit::Integer=0, keepempty::Bool=false)
rsplit(s::AbstractString, chars; limit::Integer=0, keepempty::Bool=true)</code></pre><p>Similar to <a href="strings.html#Base.split"><code>split</code></a>, but starting from the end of the string.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = &quot;M.a.r.c.h&quot;
&quot;M.a.r.c.h&quot;

julia&gt; rsplit(a, &quot;.&quot;)
5-element Vector{SubString{String}}:
 &quot;M&quot;
 &quot;a&quot;
 &quot;r&quot;
 &quot;c&quot;
 &quot;h&quot;

julia&gt; rsplit(a, &quot;.&quot;; limit=1)
1-element Vector{SubString{String}}:
 &quot;M.a.r.c.h&quot;

julia&gt; rsplit(a, &quot;.&quot;; limit=2)
2-element Vector{SubString{String}}:
 &quot;M.a.r.c&quot;
 &quot;h&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L747-L775">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.strip" href="#Base.strip"><code>Base.strip</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">strip([pred=isspace,] str::AbstractString) -&gt; SubString
strip(str::AbstractString, chars) -&gt; SubString</code></pre><p>Remove leading and trailing characters from <code>str</code>, either those specified by <code>chars</code> or those for which the function <code>pred</code> returns <code>true</code>.</p><p>The default behaviour is to remove leading and trailing whitespace and delimiters: see <a href="strings.html#Base.Unicode.isspace"><code>isspace</code></a> for precise details.</p><p>The optional <code>chars</code> argument specifies which characters to remove: it can be a single character, vector or set of characters.</p><p>See also <a href="strings.html#Base.lstrip"><code>lstrip</code></a> and <a href="strings.html#Base.rstrip"><code>rstrip</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p>The method which accepts a predicate function requires Julia 1.2 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; strip(&quot;{3, 5}\n&quot;, [&#39;{&#39;, &#39;}&#39;, &#39;\n&#39;])
&quot;3, 5&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L421-L444">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.lstrip" href="#Base.lstrip"><code>Base.lstrip</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lstrip([pred=isspace,] str::AbstractString) -&gt; SubString
lstrip(str::AbstractString, chars) -&gt; SubString</code></pre><p>Remove leading characters from <code>str</code>, either those specified by <code>chars</code> or those for which the function <code>pred</code> returns <code>true</code>.</p><p>The default behaviour is to remove leading whitespace and delimiters: see <a href="strings.html#Base.Unicode.isspace"><code>isspace</code></a> for precise details.</p><p>The optional <code>chars</code> argument specifies which characters to remove: it can be a single character, or a vector or set of characters.</p><p>See also <a href="strings.html#Base.strip"><code>strip</code></a> and <a href="strings.html#Base.rstrip"><code>rstrip</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = lpad(&quot;March&quot;, 20)
&quot;               March&quot;

julia&gt; lstrip(a)
&quot;March&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L351-L374">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.rstrip" href="#Base.rstrip"><code>Base.rstrip</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rstrip([pred=isspace,] str::AbstractString) -&gt; SubString
rstrip(str::AbstractString, chars) -&gt; SubString</code></pre><p>Remove trailing characters from <code>str</code>, either those specified by <code>chars</code> or those for which the function <code>pred</code> returns <code>true</code>.</p><p>The default behaviour is to remove trailing whitespace and delimiters: see <a href="strings.html#Base.Unicode.isspace"><code>isspace</code></a> for precise details.</p><p>The optional <code>chars</code> argument specifies which characters to remove: it can be a single character, or a vector or set of characters.</p><p>See also <a href="strings.html#Base.strip"><code>strip</code></a> and <a href="strings.html#Base.lstrip"><code>lstrip</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = rpad(&quot;March&quot;, 20)
&quot;March               &quot;

julia&gt; rstrip(a)
&quot;March&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L386-L409">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.startswith" href="#Base.startswith"><code>Base.startswith</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">startswith(s::AbstractString, prefix::Union{AbstractString,Base.Chars})</code></pre><p>Return <code>true</code> if <code>s</code> starts with <code>prefix</code>, which can be a string, a character, or a tuple/vector/set of characters. If <code>prefix</code> is a tuple/vector/set of characters, test whether the first character of <code>s</code> belongs to that set.</p><p>See also <a href="strings.html#Base.endswith"><code>endswith</code></a>, <a href="strings.html#Base.contains"><code>contains</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; startswith(&quot;JuliaLang&quot;, &quot;Julia&quot;)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L16-L30">source</a></section><section><div><pre><code class="language-julia hljs">startswith(io::IO, prefix::Union{AbstractString,Base.Chars})</code></pre><p>Check if an <code>IO</code> object starts with a prefix, which can be either a string, a character, or a tuple/vector/set of characters.  See also <a href="io-network.html#Base.peek"><code>peek</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L81-L86">source</a></section><section><div><pre><code class="language-julia hljs">startswith(prefix)</code></pre><p>Create a function that checks whether its argument starts with <code>prefix</code>, i.e. a function equivalent to <code>y -&gt; startswith(y, prefix)</code>.</p><p>The returned function is of type <code>Base.Fix2{typeof(startswith)}</code>, which can be used to implement specialized methods.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p>The single argument <code>startswith(prefix)</code> requires at least Julia 1.5.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; startswith(&quot;Julia&quot;)(&quot;JuliaLang&quot;)
true

julia&gt; startswith(&quot;Julia&quot;)(&quot;Ends with Julia&quot;)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L166-L186">source</a></section><section><div><pre><code class="language-julia hljs">startswith(s::AbstractString, prefix::Regex)</code></pre><p>Return <code>true</code> if <code>s</code> starts with the regex pattern, <code>prefix</code>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p><code>startswith</code> does not compile the anchoring into the regular expression, but instead passes the anchoring as <code>match_option</code> to PCRE. If compile time is amortized, <code>occursin(r&quot;^...&quot;, s)</code> is faster than <code>startswith(s, r&quot;...&quot;)</code>.</p></div></div><p>See also <a href="strings.html#Base.occursin"><code>occursin</code></a> and <a href="strings.html#Base.endswith"><code>endswith</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p>This method requires at least Julia 1.2.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; startswith(&quot;JuliaLang&quot;, r&quot;Julia|Romeo&quot;)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/regex.jl#L308-L329">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.endswith" href="#Base.endswith"><code>Base.endswith</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">endswith(s::AbstractString, suffix::Union{AbstractString,Base.Chars})</code></pre><p>Return <code>true</code> if <code>s</code> ends with <code>suffix</code>, which can be a string, a character, or a tuple/vector/set of characters. If <code>suffix</code> is a tuple/vector/set of characters, test whether the last character of <code>s</code> belongs to that set.</p><p>See also <a href="strings.html#Base.startswith"><code>startswith</code></a>, <a href="strings.html#Base.contains"><code>contains</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; endswith(&quot;Sunday&quot;, &quot;day&quot;)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L42-L56">source</a></section><section><div><pre><code class="language-julia hljs">endswith(suffix)</code></pre><p>Create a function that checks whether its argument ends with <code>suffix</code>, i.e. a function equivalent to <code>y -&gt; endswith(y, suffix)</code>.</p><p>The returned function is of type <code>Base.Fix2{typeof(endswith)}</code>, which can be used to implement specialized methods.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p>The single argument <code>endswith(suffix)</code> requires at least Julia 1.5.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; endswith(&quot;Julia&quot;)(&quot;Ends with Julia&quot;)
true

julia&gt; endswith(&quot;Julia&quot;)(&quot;JuliaLang&quot;)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L143-L163">source</a></section><section><div><pre><code class="language-julia hljs">endswith(s::AbstractString, suffix::Regex)</code></pre><p>Return <code>true</code> if <code>s</code> ends with the regex pattern, <code>suffix</code>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p><code>endswith</code> does not compile the anchoring into the regular expression, but instead passes the anchoring as <code>match_option</code> to PCRE. If compile time is amortized, <code>occursin(r&quot;...$&quot;, s)</code> is faster than <code>endswith(s, r&quot;...&quot;)</code>.</p></div></div><p>See also <a href="strings.html#Base.occursin"><code>occursin</code></a> and <a href="strings.html#Base.startswith"><code>startswith</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p>This method requires at least Julia 1.2.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; endswith(&quot;JuliaLang&quot;, r&quot;Lang|Roberts&quot;)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/regex.jl#L340-L361">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.contains" href="#Base.contains"><code>Base.contains</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">contains(haystack::AbstractString, needle)</code></pre><p>Return <code>true</code> if <code>haystack</code> contains <code>needle</code>. This is the same as <code>occursin(needle, haystack)</code>, but is provided for consistency with <code>startswith(haystack, needle)</code> and <code>endswith(haystack, needle)</code>.</p><p>See also <a href="strings.html#Base.occursin"><code>occursin</code></a>, <a href="collections.html#Base.in"><code>in</code></a>, <a href="collections.html#Base.issubset"><code>issubset</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; contains(&quot;JuliaLang is pretty cool!&quot;, &quot;Julia&quot;)
true

julia&gt; contains(&quot;JuliaLang is pretty cool!&quot;, &#39;a&#39;)
true

julia&gt; contains(&quot;aba&quot;, r&quot;a.a&quot;)
true

julia&gt; contains(&quot;abba&quot;, r&quot;a.a&quot;)
false</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p>The <code>contains</code> function requires at least Julia 1.5.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L114-L140">source</a></section><section><div><pre><code class="language-julia hljs">contains(needle)</code></pre><p>Create a function that checks whether its argument contains <code>needle</code>, i.e. a function equivalent to <code>haystack -&gt; contains(haystack, needle)</code>.</p><p>The returned function is of type <code>Base.Fix2{typeof(contains)}</code>, which can be used to implement specialized methods.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L189-L197">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.first-Tuple{AbstractString, Integer}" href="#Base.first-Tuple{AbstractString, Integer}"><code>Base.first</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">first(s::AbstractString, n::Integer)</code></pre><p>Get a string consisting of the first <code>n</code> characters of <code>s</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; first(&quot;∀ϵ≠0: ϵ²&gt;0&quot;, 0)
&quot;&quot;

julia&gt; first(&quot;∀ϵ≠0: ϵ²&gt;0&quot;, 1)
&quot;∀&quot;

julia&gt; first(&quot;∀ϵ≠0: ϵ²&gt;0&quot;, 3)
&quot;∀ϵ≠&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L690-L706">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.last-Tuple{AbstractString, Integer}" href="#Base.last-Tuple{AbstractString, Integer}"><code>Base.last</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">last(s::AbstractString, n::Integer)</code></pre><p>Get a string consisting of the last <code>n</code> characters of <code>s</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; last(&quot;∀ϵ≠0: ϵ²&gt;0&quot;, 0)
&quot;&quot;

julia&gt; last(&quot;∀ϵ≠0: ϵ²&gt;0&quot;, 1)
&quot;0&quot;

julia&gt; last(&quot;∀ϵ≠0: ϵ²&gt;0&quot;, 3)
&quot;²&gt;0&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L709-L725">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Unicode.uppercase" href="#Base.Unicode.uppercase"><code>Base.Unicode.uppercase</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">uppercase(c::AbstractChar)</code></pre><p>Convert <code>c</code> to uppercase.</p><p>See also <a href="strings.html#Base.Unicode.lowercase"><code>lowercase</code></a>, <a href="strings.html#Base.Unicode.titlecase"><code>titlecase</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; uppercase(&#39;a&#39;)
&#39;A&#39;: ASCII/Unicode U+0041 (category Lu: Letter, uppercase)

julia&gt; uppercase(&#39;ê&#39;)
&#39;Ê&#39;: Unicode U+00CA (category Lu: Letter, uppercase)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L299-L314">source</a></section><section><div><pre><code class="language-julia hljs">uppercase(s::AbstractString)</code></pre><p>Return <code>s</code> with all characters converted to uppercase.</p><p>See also <a href="strings.html#Base.Unicode.lowercase"><code>lowercase</code></a>, <a href="strings.html#Base.Unicode.titlecase"><code>titlecase</code></a>, <a href="strings.html#Base.Unicode.uppercasefirst"><code>uppercasefirst</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; uppercase(&quot;Julia&quot;)
&quot;JULIA&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L605-L617">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Unicode.lowercase" href="#Base.Unicode.lowercase"><code>Base.Unicode.lowercase</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lowercase(c::AbstractChar)</code></pre><p>Convert <code>c</code> to lowercase.</p><p>See also <a href="strings.html#Base.Unicode.uppercase"><code>uppercase</code></a>, <a href="strings.html#Base.Unicode.titlecase"><code>titlecase</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; lowercase(&#39;A&#39;)
&#39;a&#39;: ASCII/Unicode U+0061 (category Ll: Letter, lowercase)

julia&gt; lowercase(&#39;Ö&#39;)
&#39;ö&#39;: Unicode U+00F6 (category Ll: Letter, lowercase)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L278-L293">source</a></section><section><div><pre><code class="language-julia hljs">lowercase(s::AbstractString)</code></pre><p>Return <code>s</code> with all characters converted to lowercase.</p><p>See also <a href="strings.html#Base.Unicode.uppercase"><code>uppercase</code></a>, <a href="strings.html#Base.Unicode.titlecase"><code>titlecase</code></a>, <a href="strings.html#Base.Unicode.lowercasefirst"><code>lowercasefirst</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; lowercase(&quot;STRINGS AND THINGS&quot;)
&quot;strings and things&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L621-L633">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Unicode.titlecase" href="#Base.Unicode.titlecase"><code>Base.Unicode.titlecase</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">titlecase(c::AbstractChar)</code></pre><p>Convert <code>c</code> to titlecase. This may differ from uppercase for digraphs, compare the example below.</p><p>See also <a href="strings.html#Base.Unicode.uppercase"><code>uppercase</code></a>, <a href="strings.html#Base.Unicode.lowercase"><code>lowercase</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; titlecase(&#39;a&#39;)
&#39;A&#39;: ASCII/Unicode U+0041 (category Lu: Letter, uppercase)

julia&gt; titlecase(&#39;ǆ&#39;)
&#39;ǅ&#39;: Unicode U+01C5 (category Lt: Letter, titlecase)

julia&gt; uppercase(&#39;ǆ&#39;)
&#39;Ǆ&#39;: Unicode U+01C4 (category Lu: Letter, uppercase)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L320-L339">source</a></section><section><div><pre><code class="language-julia hljs">titlecase(s::AbstractString; [wordsep::Function], strict::Bool=true) -&gt; String</code></pre><p>Capitalize the first character of each word in <code>s</code>; if <code>strict</code> is true, every other character is converted to lowercase, otherwise they are left unchanged. By default, all non-letters beginning a new grapheme are considered as word separators; a predicate can be passed as the <code>wordsep</code> keyword to determine which characters should be considered as word separators. See also <a href="strings.html#Base.Unicode.uppercasefirst"><code>uppercasefirst</code></a> to capitalize only the first character in <code>s</code>.</p><p>See also <a href="strings.html#Base.Unicode.uppercase"><code>uppercase</code></a>, <a href="strings.html#Base.Unicode.lowercase"><code>lowercase</code></a>, <a href="strings.html#Base.Unicode.uppercasefirst"><code>uppercasefirst</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; titlecase(&quot;the JULIA programming language&quot;)
&quot;The Julia Programming Language&quot;

julia&gt; titlecase(&quot;ISS - international space station&quot;, strict=false)
&quot;ISS - International Space Station&quot;

julia&gt; titlecase(&quot;a-a b-b&quot;, wordsep = c-&gt;c==&#39; &#39;)
&quot;A-a B-b&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L637-L662">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Unicode.uppercasefirst" href="#Base.Unicode.uppercasefirst"><code>Base.Unicode.uppercasefirst</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">uppercasefirst(s::AbstractString) -&gt; String</code></pre><p>Return <code>s</code> with the first character converted to uppercase (technically &quot;title case&quot; for Unicode). See also <a href="strings.html#Base.Unicode.titlecase"><code>titlecase</code></a> to capitalize the first character of every word in <code>s</code>.</p><p>See also <a href="strings.html#Base.Unicode.lowercasefirst"><code>lowercasefirst</code></a>, <a href="strings.html#Base.Unicode.uppercase"><code>uppercase</code></a>, <a href="strings.html#Base.Unicode.lowercase"><code>lowercase</code></a>, <a href="strings.html#Base.Unicode.titlecase"><code>titlecase</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; uppercasefirst(&quot;python&quot;)
&quot;Python&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L701-L716">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Unicode.lowercasefirst" href="#Base.Unicode.lowercasefirst"><code>Base.Unicode.lowercasefirst</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lowercasefirst(s::AbstractString)</code></pre><p>Return <code>s</code> with the first character converted to lowercase.</p><p>See also <a href="strings.html#Base.Unicode.uppercasefirst"><code>uppercasefirst</code></a>, <a href="strings.html#Base.Unicode.uppercase"><code>uppercase</code></a>, <a href="strings.html#Base.Unicode.lowercase"><code>lowercase</code></a>, <a href="strings.html#Base.Unicode.titlecase"><code>titlecase</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; lowercasefirst(&quot;Julia&quot;)
&quot;julia&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L736-L749">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.join" href="#Base.join"><code>Base.join</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">join([io::IO,] iterator [, delim [, last]])</code></pre><p>Join any <code>iterator</code> into a single string, inserting the given delimiter (if any) between adjacent items.  If <code>last</code> is given, it will be used instead of <code>delim</code> between the last two items.  Each item of <code>iterator</code> is converted to a string via <code>print(io::IOBuffer, x)</code>. If <code>io</code> is given, the result is written to <code>io</code> rather than returned as a <code>String</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; join([&quot;apples&quot;, &quot;bananas&quot;, &quot;pineapples&quot;], &quot;, &quot;, &quot; and &quot;)
&quot;apples, bananas and pineapples&quot;

julia&gt; join([1,2,3,4,5])
&quot;12345&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/io.jl#L313-L329">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.chop" href="#Base.chop"><code>Base.chop</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">chop(s::AbstractString; head::Integer = 0, tail::Integer = 1)</code></pre><p>Remove the first <code>head</code> and the last <code>tail</code> characters from <code>s</code>. The call <code>chop(s)</code> removes the last character from <code>s</code>. If it is requested to remove more characters than <code>length(s)</code> then an empty string is returned.</p><p>See also <a href="strings.html#Base.chomp"><code>chomp</code></a>, <a href="strings.html#Base.startswith"><code>startswith</code></a>, <a href="collections.html#Base.first"><code>first</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = &quot;March&quot;
&quot;March&quot;

julia&gt; chop(a)
&quot;Marc&quot;

julia&gt; chop(a, head = 1, tail = 2)
&quot;ar&quot;

julia&gt; chop(a, head = 5, tail = 5)
&quot;&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L200-L224">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.chopprefix" href="#Base.chopprefix"><code>Base.chopprefix</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">chopprefix(s::AbstractString, prefix::Union{AbstractString,Regex}) -&gt; SubString</code></pre><p>Remove the prefix <code>prefix</code> from <code>s</code>. If <code>s</code> does not start with <code>prefix</code>, a string equal to <code>s</code> is returned.</p><p>See also <a href="strings.html#Base.chopsuffix"><code>chopsuffix</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>This function is available as of Julia 1.8.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; chopprefix(&quot;Hamburger&quot;, &quot;Ham&quot;)
&quot;burger&quot;

julia&gt; chopprefix(&quot;Hamburger&quot;, &quot;hotdog&quot;)
&quot;Hamburger&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L235-L253">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.chopsuffix" href="#Base.chopsuffix"><code>Base.chopsuffix</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">chopsuffix(s::AbstractString, suffix::Union{AbstractString,Regex}) -&gt; SubString</code></pre><p>Remove the suffix <code>suffix</code> from <code>s</code>. If <code>s</code> does not end with <code>suffix</code>, a string equal to <code>s</code> is returned.</p><p>See also <a href="strings.html#Base.chopprefix"><code>chopprefix</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>This function is available as of Julia 1.8.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; chopsuffix(&quot;Hamburger&quot;, &quot;er&quot;)
&quot;Hamburg&quot;

julia&gt; chopsuffix(&quot;Hamburger&quot;, &quot;hotdog&quot;)
&quot;Hamburger&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L276-L294">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.chomp" href="#Base.chomp"><code>Base.chomp</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">chomp(s::AbstractString) -&gt; SubString</code></pre><p>Remove a single trailing newline from a string.</p><p>See also <a href="strings.html#Base.chop"><code>chop</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; chomp(&quot;Hello\n&quot;)
&quot;Hello&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L320-L332">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.thisind" href="#Base.thisind"><code>Base.thisind</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">thisind(s::AbstractString, i::Integer) -&gt; Int</code></pre><p>If <code>i</code> is in bounds in <code>s</code> return the index of the start of the character whose encoding code unit <code>i</code> is part of. In other words, if <code>i</code> is the start of a character, return <code>i</code>; if <code>i</code> is not the start of a character, rewind until the start of a character and return that index. If <code>i</code> is equal to 0 or <code>ncodeunits(s)+1</code> return <code>i</code>. In all other cases throw <code>BoundsError</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; thisind(&quot;α&quot;, 0)
0

julia&gt; thisind(&quot;α&quot;, 1)
1

julia&gt; thisind(&quot;α&quot;, 2)
1

julia&gt; thisind(&quot;α&quot;, 3)
3

julia&gt; thisind(&quot;α&quot;, 4)
ERROR: BoundsError: attempt to access 2-codeunit String at index [4]
[...]

julia&gt; thisind(&quot;α&quot;, -1)
ERROR: BoundsError: attempt to access 2-codeunit String at index [-1]
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L421-L452">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.nextind-Tuple{AbstractString, Integer, Integer}" href="#Base.nextind-Tuple{AbstractString, Integer, Integer}"><code>Base.nextind</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">nextind(str::AbstractString, i::Integer, n::Integer=1) -&gt; Int</code></pre><ul><li><p>Case <code>n == 1</code></p><p>If <code>i</code> is in bounds in <code>s</code> return the index of the start of the character whose encoding starts after index <code>i</code>. In other words, if <code>i</code> is the start of a character, return the start of the next character; if <code>i</code> is not the start of a character, move forward until the start of a character and return that index. If <code>i</code> is equal to <code>0</code> return <code>1</code>. If <code>i</code> is in bounds but greater or equal to <code>lastindex(str)</code> return <code>ncodeunits(str)+1</code>. Otherwise throw <code>BoundsError</code>.</p></li><li><p>Case <code>n &gt; 1</code></p><p>Behaves like applying <code>n</code> times <code>nextind</code> for <code>n==1</code>. The only difference is that if <code>n</code> is so large that applying <code>nextind</code> would reach <code>ncodeunits(str)+1</code> then each remaining iteration increases the returned value by <code>1</code>. This means that in this case <code>nextind</code> can return a value greater than <code>ncodeunits(str)+1</code>.</p></li><li><p>Case <code>n == 0</code></p><p>Return <code>i</code> only if <code>i</code> is a valid index in <code>s</code> or is equal to <code>0</code>. Otherwise <code>StringIndexError</code> or <code>BoundsError</code> is thrown.</p></li></ul><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; nextind(&quot;α&quot;, 0)
1

julia&gt; nextind(&quot;α&quot;, 1)
3

julia&gt; nextind(&quot;α&quot;, 3)
ERROR: BoundsError: attempt to access 2-codeunit String at index [3]
[...]

julia&gt; nextind(&quot;α&quot;, 0, 2)
3

julia&gt; nextind(&quot;α&quot;, 1, 2)
4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L524-L567">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.prevind-Tuple{AbstractString, Integer, Integer}" href="#Base.prevind-Tuple{AbstractString, Integer, Integer}"><code>Base.prevind</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">prevind(str::AbstractString, i::Integer, n::Integer=1) -&gt; Int</code></pre><ul><li><p>Case <code>n == 1</code></p><p>If <code>i</code> is in bounds in <code>s</code> return the index of the start of the character whose encoding starts before index <code>i</code>. In other words, if <code>i</code> is the start of a character, return the start of the previous character; if <code>i</code> is not the start of a character, rewind until the start of a character and return that index. If <code>i</code> is equal to <code>1</code> return <code>0</code>. If <code>i</code> is equal to <code>ncodeunits(str)+1</code> return <code>lastindex(str)</code>. Otherwise throw <code>BoundsError</code>.</p></li><li><p>Case <code>n &gt; 1</code></p><p>Behaves like applying <code>n</code> times <code>prevind</code> for <code>n==1</code>. The only difference is that if <code>n</code> is so large that applying <code>prevind</code> would reach <code>0</code> then each remaining iteration decreases the returned value by <code>1</code>. This means that in this case <code>prevind</code> can return a negative value.</p></li><li><p>Case <code>n == 0</code></p><p>Return <code>i</code> only if <code>i</code> is a valid index in <code>str</code> or is equal to <code>ncodeunits(str)+1</code>. Otherwise <code>StringIndexError</code> or <code>BoundsError</code> is thrown.</p></li></ul><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; prevind(&quot;α&quot;, 3)
1

julia&gt; prevind(&quot;α&quot;, 1)
0

julia&gt; prevind(&quot;α&quot;, 0)
ERROR: BoundsError: attempt to access 2-codeunit String at index [0]
[...]

julia&gt; prevind(&quot;α&quot;, 2, 2)
0

julia&gt; prevind(&quot;α&quot;, 2, 3)
-1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L465-L508">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Unicode.textwidth" href="#Base.Unicode.textwidth"><code>Base.Unicode.textwidth</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">textwidth(c)</code></pre><p>Give the number of columns needed to print a character.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; textwidth(&#39;α&#39;)
1

julia&gt; textwidth(&#39;⛵&#39;)
2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L244-L257">source</a></section><section><div><pre><code class="language-julia hljs">textwidth(s::AbstractString)</code></pre><p>Give the number of columns needed to print a string.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; textwidth(&quot;March&quot;)
5</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L263-L273">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isascii" href="#Base.isascii"><code>Base.isascii</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isascii(c::Union{AbstractChar,AbstractString}) -&gt; Bool</code></pre><p>Test whether a character belongs to the ASCII character set, or whether this is true for all elements of a string.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isascii(&#39;a&#39;)
true

julia&gt; isascii(&#39;α&#39;)
false

julia&gt; isascii(&quot;abc&quot;)
true

julia&gt; isascii(&quot;αβγ&quot;)
false</code></pre><p>For example, <code>isascii</code> can be used as a predicate function for <a href="collections.html#Base.filter"><code>filter</code></a> or <a href="collections.html#Base.replace-Tuple{Any, Vararg{Pair}}"><code>replace</code></a> to remove or replace non-ASCII characters, respectively:</p><pre><code class="language-julia-repl hljs">julia&gt; filter(isascii, &quot;abcdeγfgh&quot;) # discard non-ASCII chars
&quot;abcdefgh&quot;

julia&gt; replace(&quot;abcdeγfgh&quot;, !isascii=&gt;&#39; &#39;) # replace non-ASCII chars with spaces
&quot;abcde fgh&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L596-L625">source</a></section><section><div><pre><code class="language-julia hljs">isascii(cu::AbstractVector{CU}) where {CU &lt;: Integer} -&gt; Bool</code></pre><p>Test whether all values in the vector belong to the ASCII character set (0x00 to 0x7f). This function is intended to be used by other string implementations that need a fast ASCII check.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L647-L652">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Unicode.iscntrl" href="#Base.Unicode.iscntrl"><code>Base.Unicode.iscntrl</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">iscntrl(c::AbstractChar) -&gt; Bool</code></pre><p>Tests whether a character is a control character. Control characters are the non-printing characters of the Latin-1 subset of Unicode.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; iscntrl(&#39;\x01&#39;)
true

julia&gt; iscntrl(&#39;a&#39;)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L503-L517">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Unicode.isdigit" href="#Base.Unicode.isdigit"><code>Base.Unicode.isdigit</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isdigit(c::AbstractChar) -&gt; Bool</code></pre><p>Tests whether a character is a decimal digit (0-9).</p><p>See also: <a href="strings.html#Base.Unicode.isletter"><code>isletter</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isdigit(&#39;❤&#39;)
false

julia&gt; isdigit(&#39;9&#39;)
true

julia&gt; isdigit(&#39;α&#39;)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L430-L448">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Unicode.isletter" href="#Base.Unicode.isletter"><code>Base.Unicode.isletter</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isletter(c::AbstractChar) -&gt; Bool</code></pre><p>Test whether a character is a letter. A character is classified as a letter if it belongs to the Unicode general category Letter, i.e. a character whose category code begins with &#39;L&#39;.</p><p>See also: <a href="strings.html#Base.Unicode.isdigit"><code>isdigit</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isletter(&#39;❤&#39;)
false

julia&gt; isletter(&#39;α&#39;)
true

julia&gt; isletter(&#39;9&#39;)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L451-L471">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Unicode.islowercase" href="#Base.Unicode.islowercase"><code>Base.Unicode.islowercase</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">islowercase(c::AbstractChar) -&gt; Bool</code></pre><p>Tests whether a character is a lowercase letter (according to the Unicode standard&#39;s <code>Lowercase</code> derived property).</p><p>See also <a href="strings.html#Base.Unicode.isuppercase"><code>isuppercase</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; islowercase(&#39;α&#39;)
true

julia&gt; islowercase(&#39;Γ&#39;)
false

julia&gt; islowercase(&#39;❤&#39;)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L369-L388">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Unicode.isnumeric" href="#Base.Unicode.isnumeric"><code>Base.Unicode.isnumeric</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isnumeric(c::AbstractChar) -&gt; Bool</code></pre><p>Tests whether a character is numeric. A character is classified as numeric if it belongs to the Unicode general category Number, i.e. a character whose category code begins with &#39;N&#39;.</p><p>Note that this broad category includes characters such as ¾ and ௰. Use <a href="strings.html#Base.Unicode.isdigit"><code>isdigit</code></a> to check whether a character is a decimal digit between 0 and 9.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isnumeric(&#39;௰&#39;)
true

julia&gt; isnumeric(&#39;9&#39;)
true

julia&gt; isnumeric(&#39;α&#39;)
false

julia&gt; isnumeric(&#39;❤&#39;)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L474-L498">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Unicode.isprint" href="#Base.Unicode.isprint"><code>Base.Unicode.isprint</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isprint(c::AbstractChar) -&gt; Bool</code></pre><p>Tests whether a character is printable, including spaces, but not a control character.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isprint(&#39;\x01&#39;)
false

julia&gt; isprint(&#39;A&#39;)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L568-L581">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Unicode.ispunct" href="#Base.Unicode.ispunct"><code>Base.Unicode.ispunct</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ispunct(c::AbstractChar) -&gt; Bool</code></pre><p>Tests whether a character belongs to the Unicode general category Punctuation, i.e. a character whose category code begins with &#39;P&#39;.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; ispunct(&#39;α&#39;)
false

julia&gt; ispunct(&#39;/&#39;)
true

julia&gt; ispunct(&#39;;&#39;)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L520-L537">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Unicode.isspace" href="#Base.Unicode.isspace"><code>Base.Unicode.isspace</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isspace(c::AbstractChar) -&gt; Bool</code></pre><p>Tests whether a character is any whitespace character. Includes ASCII characters &#39;\t&#39;, &#39;\n&#39;, &#39;\v&#39;, &#39;\f&#39;, &#39;\r&#39;, and &#39; &#39;, Latin-1 character U+0085, and characters in Unicode category Zs.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isspace(&#39;\n&#39;)
true

julia&gt; isspace(&#39;\r&#39;)
true

julia&gt; isspace(&#39; &#39;)
true

julia&gt; isspace(&#39;\x20&#39;)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L542-L563">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Unicode.isuppercase" href="#Base.Unicode.isuppercase"><code>Base.Unicode.isuppercase</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isuppercase(c::AbstractChar) -&gt; Bool</code></pre><p>Tests whether a character is an uppercase letter (according to the Unicode standard&#39;s <code>Uppercase</code> derived property).</p><p>See also <a href="strings.html#Base.Unicode.islowercase"><code>islowercase</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isuppercase(&#39;γ&#39;)
false

julia&gt; isuppercase(&#39;Γ&#39;)
true

julia&gt; isuppercase(&#39;❤&#39;)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L393-L412">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Unicode.isxdigit" href="#Base.Unicode.isxdigit"><code>Base.Unicode.isxdigit</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isxdigit(c::AbstractChar) -&gt; Bool</code></pre><p>Test whether a character is a valid hexadecimal digit. Note that this does not include <code>x</code> (as in the standard <code>0x</code> prefix).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isxdigit(&#39;a&#39;)
true

julia&gt; isxdigit(&#39;x&#39;)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/unicode.jl#L586-L600">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.escape_string" href="#Base.escape_string"><code>Base.escape_string</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">escape_string(str::AbstractString[, esc]; keep = ())::AbstractString
escape_string(io, str::AbstractString[, esc]; keep = ())::Nothing</code></pre><p>General escaping of traditional C and Unicode escape sequences. The first form returns the escaped string, the second prints the result to <code>io</code>.</p><p>Backslashes (<code>\</code>) are escaped with a double-backslash (<code>&quot;\\&quot;</code>). Non-printable characters are escaped either with their standard C escape codes, <code>&quot;\0&quot;</code> for NUL (if unambiguous), unicode code point (<code>&quot;\u&quot;</code> prefix) or hex (<code>&quot;\x&quot;</code> prefix).</p><p>The optional <code>esc</code> argument specifies any additional characters that should also be escaped by a prepending backslash (<code>&quot;</code> is also escaped by default in the first form).</p><p>The argument <code>keep</code> specifies a collection of characters which are to be kept as they are. Notice that <code>esc</code> has precedence here.</p><p>See also <a href="strings.html#Base.unescape_string"><code>unescape_string</code></a> for the reverse operation.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>The <code>keep</code> argument is available as of Julia 1.7.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; escape_string(&quot;aaa\nbbb&quot;)
&quot;aaa\\nbbb&quot;

julia&gt; escape_string(&quot;aaa\nbbb&quot;; keep = &#39;\n&#39;)
&quot;aaa\nbbb&quot;

julia&gt; escape_string(&quot;\xfe\xff&quot;) # invalid utf-8
&quot;\\xfe\\xff&quot;

julia&gt; escape_string(string(&#39;\u2135&#39;,&#39;\0&#39;)) # unambiguous
&quot;ℵ\\0&quot;

julia&gt; escape_string(string(&#39;\u2135&#39;,&#39;\0&#39;,&#39;0&#39;)) # \0 would be ambiguous
&quot;ℵ\\x000&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/io.jl#L386-L425">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.escape_raw_string" href="#Base.escape_raw_string"><code>Base.escape_raw_string</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">escape_raw_string(s::AbstractString, delim=&#39;&quot;&#39;) -&gt; AbstractString
escape_raw_string(io, s::AbstractString, delim=&#39;&quot;&#39;)</code></pre><p>Escape a string in the manner used for parsing raw string literals. For each double-quote (<code>&quot;</code>) character in input string <code>s</code> (or <code>delim</code> if specified), this function counts the number <em>n</em> of preceding backslash (<code>\</code>) characters, and then increases there the number of backslashes from <em>n</em> to 2<em>n</em>+1 (even for <em>n</em> = 0). It also doubles a sequence of backslashes at the end of the string.</p><p>This escaping convention is used in raw strings and other non-standard string literals. (It also happens to be the escaping convention expected by the Microsoft C/C++ compiler runtime when it parses a command-line string into the argv[] array.)</p><p>See also <a href="strings.html#Base.escape_string"><code>escape_string</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/io.jl#L612-L629">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unescape_string" href="#Base.unescape_string"><code>Base.unescape_string</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unescape_string(str::AbstractString, keep = ())::AbstractString
unescape_string(io, s::AbstractString, keep = ())::Nothing</code></pre><p>General unescaping of traditional C and Unicode escape sequences. The first form returns the escaped string, the second prints the result to <code>io</code>. The argument <code>keep</code> specifies a collection of characters which (along with backlashes) are to be kept as they are.</p><p>The following escape sequences are recognised:</p><ul><li>Escaped backslash (<code>\\</code>)</li><li>Escaped double-quote (<code>\&quot;</code>)</li><li>Standard C escape sequences (<code>\a</code>, <code>\b</code>, <code>\t</code>, <code>\n</code>, <code>\v</code>, <code>\f</code>, <code>\r</code>, <code>\e</code>)</li><li>Unicode BMP code points (<code>\u</code> with 1-4 trailing hex digits)</li><li>All Unicode code points (<code>\U</code> with 1-8 trailing hex digits; max value = 0010ffff)</li><li>Hex bytes (<code>\x</code> with 1-2 trailing hex digits)</li><li>Octal bytes (<code>\</code> with 1-3 trailing octal digits)</li></ul><p>See also <a href="strings.html#Base.escape_string"><code>escape_string</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; unescape_string(&quot;aaa\\nbbb&quot;) # C escape sequence
&quot;aaa\nbbb&quot;

julia&gt; unescape_string(&quot;\\u03c0&quot;) # unicode
&quot;π&quot;

julia&gt; unescape_string(&quot;\\101&quot;) # octal
&quot;A&quot;

julia&gt; unescape_string(&quot;aaa \\g \\n&quot;, [&#39;g&#39;]) # using `keep` argument
&quot;aaa \\g \n&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/io.jl#L467-L501">source</a></section></article><h2 id="AnnotatedStrings"><a class="docs-heading-anchor" href="#AnnotatedStrings"><code>AnnotatedString</code>s</a><a id="AnnotatedStrings-1"></a><a class="docs-heading-anchor-permalink" href="#AnnotatedStrings" title="Permalink"></a></h2><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The API for AnnotatedStrings is considered experimental and is subject to change between Julia versions.</p></div></div><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.AnnotatedString" href="#Base.AnnotatedString"><code>Base.AnnotatedString</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AnnotatedString{S &lt;: AbstractString} &lt;: AbstractString</code></pre><p>A string with metadata, in the form of annotated regions.</p><p>More specifically, this is a simple wrapper around any other <a href="strings.html#Core.AbstractString"><code>AbstractString</code></a> that allows for regions of the wrapped string to be annotated with labeled values.</p><pre><code class="language-text hljs">                           C
                    ┌──────┸─────────┐
  &quot;this is an example annotated string&quot;
  └──┰────────┼─────┘         │
     A        └─────┰─────────┘
                    B</code></pre><p>The above diagram represents a <code>AnnotatedString</code> where three ranges have been annotated (labeled <code>A</code>, <code>B</code>, and <code>C</code>). Each annotation holds a label (<code>Symbol</code>) and a value (<code>Any</code>). These three pieces of information are held as a <code>@NamedTuple{region::UnitRange{Int64}, label::Symbol, value}</code>.</p><p>Labels do not need to be unique, the same region can hold multiple annotations with the same label.</p><p>Code written for <code>AnnotatedString</code>s in general should conserve the following properties:</p><ul><li>Which characters an annotation is applied to</li><li>The order in which annotations are applied to each character</li></ul><p>Additional semantics may be introduced by specific uses of <code>AnnotatedString</code>s.</p><p>A corollary of these rules is that adjacent, consecutively placed, annotations with identical labels and values are equivalent to a single annotation spanning the combined range.</p><p>See also <a href="strings.html#Base.AnnotatedChar"><code>AnnotatedChar</code></a>, <a href="strings.html#Base.annotatedstring"><code>annotatedstring</code></a>, <a href="strings.html#Base.annotations"><code>annotations</code></a>, and <a href="strings.html#Base.annotate!"><code>annotate!</code></a>.</p><p><strong>Constructors</strong></p><pre><code class="language-julia hljs">AnnotatedString(s::S&lt;:AbstractString) -&gt; AnnotatedString{S}
AnnotatedString(s::S&lt;:AbstractString, annotations::Vector{@NamedTuple{region::UnitRange{Int64}, label::Symbol, value}})</code></pre><p>A AnnotatedString can also be created with <a href="strings.html#Base.annotatedstring"><code>annotatedstring</code></a>, which acts much like <a href="strings.html#Base.string"><code>string</code></a> but preserves any annotations present in the arguments.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; AnnotatedString(&quot;this is an example annotated string&quot;,
                    [(1:18, :A =&gt; 1), (12:28, :B =&gt; 2), (18:35, :C =&gt; 3)])
&quot;this is an example annotated string&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/annotated.jl#L6-L63">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.AnnotatedChar" href="#Base.AnnotatedChar"><code>Base.AnnotatedChar</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AnnotatedChar{S &lt;: AbstractChar} &lt;: AbstractChar</code></pre><p>A Char with annotations.</p><p>More specifically, this is a simple wrapper around any other <a href="strings.html#Core.AbstractChar"><code>AbstractChar</code></a>, which holds a list of arbitrary labelled annotations (<code>@NamedTuple{label::Symbol, value}</code>) with the wrapped character.</p><p>See also: <a href="strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a>, <a href="strings.html#Base.annotatedstring"><code>annotatedstring</code></a>, <code>annotations</code>, and <code>annotate!</code>.</p><p><strong>Constructors</strong></p><pre><code class="language-julia hljs">AnnotatedChar(s::S) -&gt; AnnotatedChar{S}
AnnotatedChar(s::S, annotations::Vector{@NamedTuple{label::Symbol, value}})</code></pre><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; AnnotatedChar(&#39;j&#39;, :label =&gt; 1)
&#39;j&#39;: ASCII/Unicode U+006A (category Ll: Letter, lowercase)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/annotated.jl#L69-L94">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.annotatedstring" href="#Base.annotatedstring"><code>Base.annotatedstring</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">annotatedstring(values...)</code></pre><p>Create a <code>AnnotatedString</code> from any number of <code>values</code> using their <a href="io-network.html#Base.print"><code>print</code></a>ed representation.</p><p>This acts like <a href="strings.html#Base.string"><code>string</code></a>, but takes care to preserve any annotations present (in the form of <a href="strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a> or <a href="strings.html#Base.AnnotatedChar"><code>AnnotatedChar</code></a> values).</p><p>See also <a href="strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a> and <a href="strings.html#Base.AnnotatedChar"><code>AnnotatedChar</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; annotatedstring(&quot;now a AnnotatedString&quot;)
&quot;now a AnnotatedString&quot;

julia&gt; annotatedstring(AnnotatedString(&quot;annotated&quot;, [(1:9, :label =&gt; 1)]), &quot;, and unannotated&quot;)
&quot;annotated, and unannotated&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/annotated.jl#L222-L242">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.annotations" href="#Base.annotations"><code>Base.annotations</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">annotations(str::Union{AnnotatedString, SubString{AnnotatedString}},
            [position::Union{Integer, UnitRange}]) -&gt;
    Vector{@NamedTuple{region::UnitRange{Int64}, label::Symbol, value}}</code></pre><p>Get all annotations that apply to <code>str</code>. Should <code>position</code> be provided, only annotations that overlap with <code>position</code> will be returned.</p><p>Annotations are provided together with the regions they apply to, in the form of a vector of region–annotation tuples.</p><p>In accordance with the semantics documented in <a href="strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a>, the order of annotations returned matches the order in which they were applied.</p><p>See also: <a href="strings.html#Base.annotate!"><code>annotate!</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/annotated.jl#L373-L388">source</a></section><section><div><pre><code class="language-julia hljs">annotations(chr::AnnotatedChar) -&gt; Vector{@NamedTuple{label::Symbol, value}}</code></pre><p>Get all annotations of <code>chr</code>, in the form of a vector of annotation pairs.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/annotated.jl#L412-L416">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.annotate!" href="#Base.annotate!"><code>Base.annotate!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">annotate!(str::AnnotatedString, [range::UnitRange{Int}], label::Symbol, value)
annotate!(str::SubString{AnnotatedString}, [range::UnitRange{Int}], label::Symbol, value)</code></pre><p>Annotate a <code>range</code> of <code>str</code> (or the entire string) with a labeled value (<code>label</code> =&gt; <code>value</code>). To remove existing <code>label</code> annotations, use a value of <code>nothing</code>.</p><p>The order in which annotations are applied to <code>str</code> is semantically meaningful, as described in <a href="strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/annotated.jl#L343-L352">source</a></section><section><div><pre><code class="language-julia hljs">annotate!(char::AnnotatedChar, label::Symbol, value::Any)</code></pre><p>Annotate <code>char</code> with the pair <code>label =&gt; value</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/annotated.jl#L365-L369">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="numbers.html">« Numbers</a><a class="docs-footer-nextpage" href="arrays.html">Arrays »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
