<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Reporting and analyzing crashes (segfaults) · The Julia Language</title><meta name="title" content="Reporting and analyzing crashes (segfaults) · The Julia Language"/><meta property="og:title" content="Reporting and analyzing crashes (segfaults) · The Julia Language"/><meta property="twitter:title" content="Reporting and analyzing crashes (segfaults) · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="ast.html">Julia ASTs</a></li><li><a class="tocitem" href="types.html">More about types</a></li><li><a class="tocitem" href="object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="functions.html">Julia Functions</a></li><li><a class="tocitem" href="cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="subarrays.html">SubArrays</a></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox" checked/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li class="is-active"><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a><ul class="internal"><li><a class="tocitem" href="#dev-version-info"><span>Version/Environment info</span></a></li><li><a class="tocitem" href="#Segfaults-during-bootstrap-(sysimg.jl)"><span>Segfaults during bootstrap (<code>sysimg.jl</code>)</span></a></li><li><a class="tocitem" href="#Segfaults-when-running-a-script"><span>Segfaults when running a script</span></a></li><li><a class="tocitem" href="#Errors-during-Julia-startup"><span>Errors during Julia startup</span></a></li><li><a class="tocitem" href="#Other-generic-segfaults-or-unreachables-reached"><span>Other generic segfaults or unreachables reached</span></a></li><li><a class="tocitem" href="#Glossary"><span>Glossary</span></a></li></ul></li><li><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Developing/debugging Julia&#39;s C code</a></li><li class="is-active"><a href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/backtraces.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Reporting-and-analyzing-crashes-(segfaults)"><a class="docs-heading-anchor" href="#Reporting-and-analyzing-crashes-(segfaults)">Reporting and analyzing crashes (segfaults)</a><a id="Reporting-and-analyzing-crashes-(segfaults)-1"></a><a class="docs-heading-anchor-permalink" href="#Reporting-and-analyzing-crashes-(segfaults)" title="Permalink"></a></h1><p>So you managed to break Julia.  Congratulations!  Collected here are some general procedures you can undergo for common symptoms encountered when something goes awry.  Including the information from these debugging steps can greatly help the maintainers when tracking down a segfault or trying to figure out why your script is running slower than expected.</p><p>If you&#39;ve been directed to this page, find the symptom that best matches what you&#39;re experiencing and follow the instructions to generate the debugging information requested.  Table of symptoms:</p><ul><li><a href="backtraces.html#Segfaults-during-bootstrap-(sysimg.jl)">Segfaults during bootstrap (<code>sysimg.jl</code>)</a></li><li><a href="backtraces.html#Segfaults-when-running-a-script">Segfaults when running a script</a></li><li><a href="backtraces.html#Errors-during-Julia-startup">Errors during Julia startup</a></li><li><a href="backtraces.html#Other-generic-segfaults-or-unreachables-reached">Other generic segfaults or unreachables reached</a></li></ul><h2 id="dev-version-info"><a class="docs-heading-anchor" href="#dev-version-info">Version/Environment info</a><a id="dev-version-info-1"></a><a class="docs-heading-anchor-permalink" href="#dev-version-info" title="Permalink"></a></h2><p>No matter the error, we will always need to know what version of Julia you are running. When Julia first starts up, a header is printed out with a version number and date. Please also include the output of <code>versioninfo()</code> (exported from the <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.versioninfo"><code>InteractiveUtils</code></a> standard library) in any report you create:</p><pre><code class="language-julia-repl hljs" style="display:block;">julia&gt; using InteractiveUtils</code><code class="nohighlight hljs ansi" style="display:block;"></code><br/><code class="language-julia-repl hljs" style="display:block;">julia&gt; versioninfo()</code><code class="nohighlight hljs ansi" style="display:block;">Julia Version 1.11.5
Commit 760b2e5b739 (2025-04-14 06:53 UTC)
Build Info:
  Official https://julialang.org/ release
Platform Info:
  OS: macOS (x86_64-apple-darwin24.0.0)
  CPU: 4 × Intel(R) Core(TM) i3-8100B CPU @ 3.60GHz
  WORD_SIZE: 64
  LLVM: libLLVM-16.0.6 (ORCJIT, skylake)
Threads: 1 default, 0 interactive, 1 GC (on 4 virtual cores)
Environment:
  JULIA_CPU_THREADS = 4
  JULIA_EXECUTABLE = /Users/<USER>/.julia/scratchspaces/a66863c6-20e8-4ff4-8a62-49f30b1f605e/agent-cache/default-grannysmith-C07ZQ07RJYVY.0/build/default-grannysmith-C07ZQ07RJYVY-0/julialang/julia-release-1-dot-11/usr/bin/julia --startup-file=no
  JULIA_CPU_TARGET = generic;sandybridge,-xsaveopt,clone_all;haswell,-rdrnd,base(1);x86-64-v4,-rdrnd,base(1)
  JULIA_BINARYDIST_FILENAME = julia-1.11.5-mac64
  JULIA_IMAGE_THREADS = 4
  JULIA_VERSION = 1.11.5
  JULIA_INSTALL_DIR = julia-1.11.5</code></pre><h2 id="Segfaults-during-bootstrap-(sysimg.jl)"><a class="docs-heading-anchor" href="#Segfaults-during-bootstrap-(sysimg.jl)">Segfaults during bootstrap (<code>sysimg.jl</code>)</a><a id="Segfaults-during-bootstrap-(sysimg.jl)-1"></a><a class="docs-heading-anchor-permalink" href="#Segfaults-during-bootstrap-(sysimg.jl)" title="Permalink"></a></h2><p>Segfaults toward the end of the <code>make</code> process of building Julia are a common symptom of something going wrong while Julia is preparsing the corpus of code in the <code>base/</code> folder.  Many factors can contribute toward this process dying unexpectedly, however it is as often as not due to an error in the C-code portion of Julia, and as such must typically be debugged with a debug build inside of <code>gdb</code>.  Explicitly:</p><p>Create a debug build of Julia:</p><pre><code class="nohighlight hljs">$ cd &lt;julia_root&gt;
$ make debug</code></pre><p>Note that this process will likely fail with the same error as a normal <code>make</code> incantation, however this will create a debug executable that will offer <code>gdb</code> the debugging symbols needed to get accurate backtraces.  Next, manually run the bootstrap process inside of <code>gdb</code>:</p><pre><code class="nohighlight hljs">$ cd base/
$ gdb -x ../contrib/debug_bootstrap.gdb</code></pre><p>This will start <code>gdb</code>, attempt to run the bootstrap process using the debug build of Julia, and print out a backtrace if (when) it segfaults.  You may need to hit <code>&lt;enter&gt;</code> a few times to get the full backtrace.  Create a <a href="https://gist.github.com">gist</a> with the backtrace, the <a href="backtraces.html#dev-version-info">version info</a>, and any other pertinent information you can think of and open a new <a href="https://github.com/JuliaLang/julia/issues?q=is%3Aopen">issue</a> on Github with a link to the gist.</p><h2 id="Segfaults-when-running-a-script"><a class="docs-heading-anchor" href="#Segfaults-when-running-a-script">Segfaults when running a script</a><a id="Segfaults-when-running-a-script-1"></a><a class="docs-heading-anchor-permalink" href="#Segfaults-when-running-a-script" title="Permalink"></a></h2><p>The procedure is very similar to <a href="backtraces.html#Segfaults-during-bootstrap-(sysimg.jl)">Segfaults during bootstrap (<code>sysimg.jl</code>)</a>.  Create a debug build of Julia, and run your script inside of a debugged Julia process:</p><pre><code class="nohighlight hljs">$ cd &lt;julia_root&gt;
$ make debug
$ gdb --args usr/bin/julia-debug &lt;path_to_your_script&gt;</code></pre><p>Note that <code>gdb</code> will sit there, waiting for instructions.  Type <code>r</code> to run the process, and <code>bt</code> to generate a backtrace once it segfaults:</p><pre><code class="nohighlight hljs">(gdb) r
Starting program: /home/<USER>/src/julia/usr/bin/julia-debug ./test.jl
...
(gdb) bt</code></pre><p>Create a <a href="https://gist.github.com">gist</a> with the backtrace, the <a href="backtraces.html#dev-version-info">version info</a>, and any other pertinent information you can think of and open a new <a href="https://github.com/JuliaLang/julia/issues?q=is%3Aopen">issue</a> on Github with a link to the gist.</p><h2 id="Errors-during-Julia-startup"><a class="docs-heading-anchor" href="#Errors-during-Julia-startup">Errors during Julia startup</a><a id="Errors-during-Julia-startup-1"></a><a class="docs-heading-anchor-permalink" href="#Errors-during-Julia-startup" title="Permalink"></a></h2><p>Occasionally errors occur during Julia&#39;s startup process (especially when using binary distributions, as opposed to compiling from source) such as the following:</p><pre><code class="language-julia hljs">$ julia
exec: error -5</code></pre><p>These errors typically indicate something is not getting loaded properly very early on in the bootup phase, and our best bet in determining what&#39;s going wrong is to use external tools to audit the disk activity of the <code>julia</code> process:</p><ul><li><p>On Linux, use <code>strace</code>:</p><pre><code class="nohighlight hljs">$ strace julia</code></pre></li><li><p>On OSX, use <code>dtruss</code>:</p><pre><code class="nohighlight hljs">$ dtruss -f julia</code></pre></li></ul><p>Create a <a href="https://gist.github.com">gist</a> with the <code>strace</code>/ <code>dtruss</code> output, the <a href="backtraces.html#dev-version-info">version info</a>, and any other pertinent information and open a new <a href="https://github.com/JuliaLang/julia/issues?q=is%3Aopen">issue</a> on Github with a link to the gist.</p><h2 id="Other-generic-segfaults-or-unreachables-reached"><a class="docs-heading-anchor" href="#Other-generic-segfaults-or-unreachables-reached">Other generic segfaults or unreachables reached</a><a id="Other-generic-segfaults-or-unreachables-reached-1"></a><a class="docs-heading-anchor-permalink" href="#Other-generic-segfaults-or-unreachables-reached" title="Permalink"></a></h2><p>As mentioned elsewhere, <code>julia</code> has good integration with <code>rr</code> for generating traces; this includes, on Linux, the ability to automatically run <code>julia</code> under <code>rr</code> and share the trace after a crash. This can be immensely helpful when debugging such crashes and is strongly encouraged when reporting crash issues to the JuliaLang/julia repo. To run <code>julia</code> under <code>rr</code> automatically, do:</p><pre><code class="language-julia hljs">julia --bug-report=rr</code></pre><p>To generate the <code>rr</code> trace locally, but not share, you can do:</p><pre><code class="language-julia hljs">julia --bug-report=rr-local</code></pre><p>Note that this is only works on Linux. The blog post on <a href="https://julialang.org/blog/2020/05/rr/">Time Travelling Bug Reporting</a> has many more details.</p><h2 id="Glossary"><a class="docs-heading-anchor" href="#Glossary">Glossary</a><a id="Glossary-1"></a><a class="docs-heading-anchor-permalink" href="#Glossary" title="Permalink"></a></h2><p>A few terms have been used as shorthand in this guide:</p><ul><li><code>&lt;julia_root&gt;</code> refers to the root directory of the Julia source tree; e.g. it should contain folders such as <code>base</code>, <code>deps</code>, <code>src</code>, <code>test</code>, etc.....</li></ul></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="precompile_hang.html">« Fixing precompilation hangs due to open tasks or IO</a><a class="docs-footer-nextpage" href="debuggingtips.html">gdb debugging tips »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
