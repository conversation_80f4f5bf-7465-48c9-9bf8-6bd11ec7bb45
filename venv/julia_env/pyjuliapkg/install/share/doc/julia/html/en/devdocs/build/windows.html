<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Windows · The Julia Language</title><meta name="title" content="Windows · The Julia Language"/><meta property="og:title" content="Windows · The Julia Language"/><meta property="twitter:title" content="Windows · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL="../.."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../../assets/documenter.js"></script><script src="../../search_index.js"></script><script src="../../siteinfo.js"></script><script src="../../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../../assets/themeswap.js"></script><link href="../../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../../index.html"><img class="docs-light-only" src="../../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../../manual/types.html">Types</a></li><li><a class="tocitem" href="../../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../../base/base.html">Essentials</a></li><li><a class="tocitem" href="../../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../../base/strings.html">Strings</a></li><li><a class="tocitem" href="../../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../../base/constants.html">Constants</a></li><li><a class="tocitem" href="../../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../../base/c.html">C Interface</a></li><li><a class="tocitem" href="../../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../types.html">More about types</a></li><li><a class="tocitem" href="../object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../functions.html">Julia Functions</a></li><li><a class="tocitem" href="../cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../require.html">Module loading</a></li><li><a class="tocitem" href="../inference.html">Inference</a></li><li><a class="tocitem" href="../ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox" checked/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="linux.html">Linux</a></li><li><a class="tocitem" href="macos.html">macOS</a></li><li class="is-active"><a class="tocitem" href="windows.html">Windows</a><ul class="internal"><li><a class="tocitem" href="#General-Information-for-Windows"><span>General Information for Windows</span></a></li><li><a class="tocitem" href="#Binary-distribution"><span>Binary distribution</span></a></li><li><a class="tocitem" href="#Source-distribution"><span>Source distribution</span></a></li><li><a class="tocitem" href="#Debugging-a-cross-compiled-build-under-wine"><span>Debugging a cross-compiled build under wine</span></a></li><li><a class="tocitem" href="#After-compiling"><span>After compiling</span></a></li><li><a class="tocitem" href="#Windows-Build-Debugging"><span>Windows Build Debugging</span></a></li></ul></li><li><a class="tocitem" href="freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Building Julia</a></li><li class="is-active"><a href="windows.html">Windows</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="windows.html">Windows</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/build/windows.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Windows"><a class="docs-heading-anchor" href="#Windows">Windows</a><a id="Windows-1"></a><a class="docs-heading-anchor-permalink" href="#Windows" title="Permalink"></a></h1><p>This file describes how to install, or build, and use Julia on Windows.</p><p>For more general information about Julia, please see the <a href="https://github.com/JuliaLang/julia/blob/master/README.md">main README</a> or the <a href="https://docs.julialang.org">documentation</a>.</p><h2 id="General-Information-for-Windows"><a class="docs-heading-anchor" href="#General-Information-for-Windows">General Information for Windows</a><a id="General-Information-for-Windows-1"></a><a class="docs-heading-anchor-permalink" href="#General-Information-for-Windows" title="Permalink"></a></h2><p>We highly recommend running Julia using a modern terminal application, in particular Windows Terminal, which can be installed from the <a href="https://aka.ms/terminal">Microsoft Store</a>.</p><h3 id="Line-endings"><a class="docs-heading-anchor" href="#Line-endings">Line endings</a><a id="Line-endings-1"></a><a class="docs-heading-anchor-permalink" href="#Line-endings" title="Permalink"></a></h3><p>Julia uses binary-mode files exclusively. Unlike many other Windows programs, if you write <code>\n</code> to a file, you get a <code>\n</code> in the file, not some other bit pattern. This matches the behavior exhibited by other operating systems. If you have installed Git for Windows, it is suggested, but not required, that you configure your system Git to use the same convention:</p><pre><code class="language-sh hljs">git config --global core.eol lf
git config --global core.autocrlf input</code></pre><p>or edit <code>%USERPROFILE%\.gitconfig</code> and add/edit the lines:</p><pre><code class="nohighlight hljs">[core]
    eol = lf
    autocrlf = input</code></pre><h2 id="Binary-distribution"><a class="docs-heading-anchor" href="#Binary-distribution">Binary distribution</a><a id="Binary-distribution-1"></a><a class="docs-heading-anchor-permalink" href="#Binary-distribution" title="Permalink"></a></h2><p>For the binary distribution installation notes on Windows please see the instructions at <a href="https://julialang.org/downloads/platform/#windows">https://julialang.org/downloads/platform/#windows</a>.</p><h2 id="Source-distribution"><a class="docs-heading-anchor" href="#Source-distribution">Source distribution</a><a id="Source-distribution-1"></a><a class="docs-heading-anchor-permalink" href="#Source-distribution" title="Permalink"></a></h2><h3 id="Cygwin-to-MinGW-cross-compiling"><a class="docs-heading-anchor" href="#Cygwin-to-MinGW-cross-compiling">Cygwin-to-MinGW cross-compiling</a><a id="Cygwin-to-MinGW-cross-compiling-1"></a><a class="docs-heading-anchor-permalink" href="#Cygwin-to-MinGW-cross-compiling" title="Permalink"></a></h3><p>The recommended way of compiling Julia from source on Windows is by cross compiling from <a href="https://www.cygwin.com">Cygwin</a>, using versions of the MinGW-w64 compilers available through Cygwin&#39;s package manager.</p><ol><li><p>Download and run Cygwin setup for <a href="https://cygwin.com/setup-x86.exe">32 bit</a> or <a href="https://cygwin.com/setup-x86_64.exe">64 bit</a>. Note, that you can compile either 32 or 64 bit Julia from either 32 or 64 bit Cygwin. 64 bit Cygwin has a slightly smaller but often more up-to-date selection of packages.</p><p><em>Advanced</em>: you may skip steps 2-4 by running:</p><pre><code class="language-sh hljs">setup-x86_64.exe -s &lt;url&gt; -q -P cmake,gcc-g++,git,make,patch,curl,m4,python3,p7zip,mingw64-i686-gcc-g++,mingw64-i686-gcc-fortran,mingw64-x86_64-gcc-g++,mingw64-x86_64-gcc-fortran</code></pre><p>replacing <code>&lt;url&gt;</code> with a site from <a href="https://cygwin.com/mirrors.html">https://cygwin.com/mirrors.html</a> or run setup manually first and select a mirror.</p></li><li><p>Select installation location and a mirror to download from.</p></li><li><p>At the <em>Select Packages</em> step, select the following:</p><ol><li>From the <em>Devel</em> category: <code>cmake</code>, <code>gcc-g++</code>, <code>git</code>, <code>make</code>, <code>patch</code></li><li>From the <em>Net</em> category: <code>curl</code></li><li>From <em>Interpreters</em> (or <em>Python</em>) category: <code>m4</code>, <code>python3</code></li><li>From the <em>Archive</em> category: <code>p7zip</code></li><li>For 32 bit Julia, and also from the <em>Devel</em> category:  <code>mingw64-i686-gcc-g++</code> and <code>mingw64-i686-gcc-fortran</code></li><li>For 64 bit Julia, and also from the <em>Devel</em> category:  <code>mingw64-x86_64-gcc-g++</code> and <code>mingw64-x86_64-gcc-fortran</code></li></ol></li><li><p>Allow Cygwin installation to finish, then start from the installed shortcut <em>&#39;Cygwin Terminal&#39;</em>, or <em>&#39;Cygwin64 Terminal&#39;</em>, respectively.</p></li><li><p>Build Julia and its dependencies from source:</p><ol><li><p>Get the Julia sources</p><pre><code class="language-sh hljs">git clone https://github.com/JuliaLang/julia.git
cd julia</code></pre><p>Tip: If you get an <code>error: cannot fork() for fetch-pack: Resource temporarily unavailable</code> from git, add <code>alias git=&quot;env PATH=/usr/bin git&quot;</code> to <code>~/.bashrc</code> and restart Cygwin.</p></li><li><p>Set the <code>XC_HOST</code> variable in <code>Make.user</code> to indicate MinGW-w64 cross compilation</p><pre><code class="language-sh hljs">echo &#39;XC_HOST = i686-w64-mingw32&#39; &gt; Make.user     # for 32 bit Julia
# or
echo &#39;XC_HOST = x86_64-w64-mingw32&#39; &gt; Make.user   # for 64 bit Julia</code></pre></li><li><p>Start the build</p><pre><code class="language-sh hljs">make -j 4       # Adjust the number of threads (4) to match your build environment.
make -j 4 debug # This builds julia-debug.exe</code></pre></li></ol></li><li><p>Run Julia using the Julia executables directly</p><pre><code class="language-sh hljs">usr/bin/julia.exe
usr/bin/julia-debug.exe</code></pre></li></ol><div class="admonition is-info"><header class="admonition-header">Pro tip: build both!</header><div class="admonition-body"><pre><code class="language-sh hljs">make O=julia-win32 configure
make O=julia-win64 configure
echo &#39;XC_HOST = i686-w64-mingw32&#39; &gt; julia-win32/Make.user
echo &#39;XC_HOST = x86_64-w64-mingw32&#39; &gt; julia-win64/Make.user
echo &#39;ifeq ($(BUILDROOT),$(JULIAHOME))
        $(error &quot;in-tree build disabled&quot;)
      endif&#39; &gt;&gt; Make.user
make -C julia-win32  # build for Windows x86 in julia-win32 folder
make -C julia-win64  # build for Windows x86-64 in julia-win64 folder</code></pre></div></div><h3 id="Compiling-with-MinGW/MSYS2"><a class="docs-heading-anchor" href="#Compiling-with-MinGW/MSYS2">Compiling with MinGW/MSYS2</a><a id="Compiling-with-MinGW/MSYS2-1"></a><a class="docs-heading-anchor-permalink" href="#Compiling-with-MinGW/MSYS2" title="Permalink"></a></h3><p><a href="https://www.msys2.org/">MSYS2</a> is a software distribution and build environment for Windows.</p><p>Note: MSYS2 requires <strong>64 bit</strong> Windows 7 or newer.</p><ol><li><p>Install and configure MSYS2.</p><ol><li><p>Download and run the latest installer for the  <a href="https://github.com/msys2/msys2-installer/releases/latest">64-bit</a> distribution.  The installer will have a name like <code>msys2-x86_64-yyyymmdd.exe</code>.</p></li><li><p>Open the MSYS2 shell. Update the package database and base packages:</p><p><code>pacman -Syu</code></p></li><li><p>Exit and restart MSYS2. Update the rest of the base packages:</p><p><code>pacman -Syu</code></p></li><li><p>Then install tools required to build julia:</p><p><code>pacman -S cmake diffutils git m4 make patch tar p7zip curl python</code></p><p>For 64 bit Julia, install the x86_64 version:</p><p><code>pacman -S mingw-w64-x86_64-gcc</code></p><p>For 32 bit Julia, install the i686 version:</p><p><code>pacman -S mingw-w64-i686-gcc</code></p></li><li><p>Configuration of MSYS2 is complete. Now <code>exit</code> the MSYS2 shell.</p></li></ol></li><li><p>Build Julia and its dependencies with pre-build dependencies.</p><ol><li><p>Open a new <a href="https://www.msys2.org/docs/environments/#overview"><strong>MINGW64/MINGW32 shell</strong></a>.  Currently we can&#39;t use both mingw32 and mingw64,  so if you want to build the x86_64 and i686 versions,  you&#39;ll need to build them in each environment separately.</p></li><li><p>Clone the Julia sources:</p><p><code>git clone https://github.com/JuliaLang/julia.git  cd julia</code></p></li><li><p>Start the build</p><p><code>make -j$(nproc)</code></p></li></ol></li></ol><div class="admonition is-info"><header class="admonition-header">Pro tip: build in dir</header><div class="admonition-body"><pre><code class="language-sh hljs">make O=julia-mingw-w64 configure
echo &#39;ifeq ($(BUILDROOT),$(JULIAHOME))
        $(error &quot;in-tree build disabled&quot;)
      endif&#39; &gt;&gt; Make.user
make -C julia-mingw-w64</code></pre></div></div><h3 id="Cross-compiling-from-Unix-(Linux/Mac/WSL)"><a class="docs-heading-anchor" href="#Cross-compiling-from-Unix-(Linux/Mac/WSL)">Cross-compiling from Unix (Linux/Mac/WSL)</a><a id="Cross-compiling-from-Unix-(Linux/Mac/WSL)-1"></a><a class="docs-heading-anchor-permalink" href="#Cross-compiling-from-Unix-(Linux/Mac/WSL)" title="Permalink"></a></h3><p>You can also use MinGW-w64 cross compilers to build a Windows version of Julia from Linux, Mac, or the Windows Subsystem for Linux (WSL).</p><p>First, you will need to ensure your system has the required dependencies. We need wine (&gt;=1.7.5), a system compiler, and some downloaders. Note: a Cygwin install might interfere with this method if using WSL.</p><p><strong>On Ubuntu</strong> (on other Linux systems the dependency names are likely to be similar):</p><pre><code class="language-sh hljs">apt-get install wine-stable gcc wget p7zip-full winbind mingw-w64 gfortran-mingw-w64
dpkg --add-architecture i386 &amp;&amp; apt-get update &amp;&amp; apt-get install wine32 # add sudo to each if needed
# switch all of the following to their &quot;-posix&quot; variants (interactively):
for pkg in i686-w64-mingw32-g++ i686-w64-mingw32-gcc i686-w64-mingw32-gfortran x86_64-w64-mingw32-g++ x86_64-w64-mingw32-gcc x86_64-w64-mingw32-gfortran; do
    sudo update-alternatives --config $pkg
done</code></pre><p><strong>On Mac</strong>: Install XCode, XCode command line tools, X11 (now <a href="https://www.xquartz.org/">XQuartz</a>), and <a href="https://www.macports.org/install.php">MacPorts</a> or <a href="https://brew.sh/">Homebrew</a>.  Then run <code>port install wine wget mingw-w64</code>, or <code>brew install wine wget mingw-w64</code>, as appropriate.</p><p><strong>Then run the build:</strong></p><ol><li><code>git clone https://github.com/JuliaLang/julia.git julia-win32</code></li><li><code>cd julia-win32</code></li><li><code>echo override XC_HOST = i686-w64-mingw32 &gt;&gt; Make.user</code></li><li><code>make</code></li><li><code>make win-extras</code> (Necessary before running <code>make binary-dist</code>)</li><li><code>make binary-dist</code> then <code>make exe</code> to create the Windows installer.</li><li>move the <code>julia-*.exe</code> installer to the target machine</li></ol><p>If you are building for 64-bit Windows, the steps are essentially the same. Just replace <code>i686</code> in <code>XC_HOST</code> with <code>x86_64</code>. (Note: on Mac, wine only runs in 32-bit mode).</p><h2 id="Debugging-a-cross-compiled-build-under-wine"><a class="docs-heading-anchor" href="#Debugging-a-cross-compiled-build-under-wine">Debugging a cross-compiled build under wine</a><a id="Debugging-a-cross-compiled-build-under-wine-1"></a><a class="docs-heading-anchor-permalink" href="#Debugging-a-cross-compiled-build-under-wine" title="Permalink"></a></h2><p>The most effective way to debug a cross-compiled version of Julia on the cross-compilation host is to install a Windows version of GDB and run it under wine as usual. The pre-built packages available <a href="https://packages.msys2.org/">as part of the MSYS2 project</a> are known to work. Apart from the GDB package you may also need the python and termcap packages. Finally, GDB&#39;s prompt may not work when launched from the command line. This can be worked around by prepending <code>wineconsole</code> to the regular GDB invocation.</p><h2 id="After-compiling"><a class="docs-heading-anchor" href="#After-compiling">After compiling</a><a id="After-compiling-1"></a><a class="docs-heading-anchor-permalink" href="#After-compiling" title="Permalink"></a></h2><p>Compiling using one of the options above creates a basic Julia build, but not some extra components that are included if you run the full Julia binary installer. If you need these components, the easiest way to get them is to build the installer yourself using <code>make win-extras</code> followed by <code>make binary-dist</code> and <code>make exe</code>. Then run the resulting installer.</p><h2 id="Windows-Build-Debugging"><a class="docs-heading-anchor" href="#Windows-Build-Debugging">Windows Build Debugging</a><a id="Windows-Build-Debugging-1"></a><a class="docs-heading-anchor-permalink" href="#Windows-Build-Debugging" title="Permalink"></a></h2><h3 id="GDB-hangs-with-Cygwin-mintty"><a class="docs-heading-anchor" href="#GDB-hangs-with-Cygwin-mintty">GDB hangs with Cygwin mintty</a><a id="GDB-hangs-with-Cygwin-mintty-1"></a><a class="docs-heading-anchor-permalink" href="#GDB-hangs-with-Cygwin-mintty" title="Permalink"></a></h3><ul><li>Run GDB under the Windows console (cmd) instead. GDB <a href="https://www.cygwin.com/ml/cygwin/2009-02/msg00531.html">may not function properly</a> under mintty with non- Cygwin applications. You can use <code>cmd /c start</code> to start the Windows console from mintty if necessary.</li></ul><h3 id="GDB-not-attaching-to-the-right-process"><a class="docs-heading-anchor" href="#GDB-not-attaching-to-the-right-process">GDB not attaching to the right process</a><a id="GDB-not-attaching-to-the-right-process-1"></a><a class="docs-heading-anchor-permalink" href="#GDB-not-attaching-to-the-right-process" title="Permalink"></a></h3><ul><li>Use the PID from the Windows task manager or <code>WINPID</code> from the <code>ps</code> command instead of the PID from unix-style command line tools (e.g. <code>pgrep</code>).  You may need to add the PID column if it is not shown by default in the Windows task manager.</li></ul><h3 id="GDB-not-showing-the-right-backtrace"><a class="docs-heading-anchor" href="#GDB-not-showing-the-right-backtrace">GDB not showing the right backtrace</a><a id="GDB-not-showing-the-right-backtrace-1"></a><a class="docs-heading-anchor-permalink" href="#GDB-not-showing-the-right-backtrace" title="Permalink"></a></h3><ul><li>When attaching to the julia process, GDB may not be attaching to the right thread.  Use <code>info threads</code> command to show all the threads and <code>thread &lt;threadno&gt;</code> to switch threads.</li><li>Be sure to use a 32 bit version of GDB to debug a 32 bit build of Julia, or a 64 bit version of GDB to debug a 64 bit build of Julia.</li></ul><h3 id="Build-process-is-slow/eats-memory/hangs-my-computer"><a class="docs-heading-anchor" href="#Build-process-is-slow/eats-memory/hangs-my-computer">Build process is slow/eats memory/hangs my computer</a><a id="Build-process-is-slow/eats-memory/hangs-my-computer-1"></a><a class="docs-heading-anchor-permalink" href="#Build-process-is-slow/eats-memory/hangs-my-computer" title="Permalink"></a></h3><ul><li><p>Disable the Windows <a href="https://en.wikipedia.org/wiki/Windows_Vista_I/O_technologies#SuperFetch">Superfetch</a> and <a href="https://blogs.msdn.com/b/cjacks/archive/2011/11/22/managing-the-windows-7-program-compatibility-assistant-pca.aspx">Program Compatibility Assistant</a> services, as they are known to have <a href="https://cygwin.com/ml/cygwin/2011-12/msg00058.html">spurious interactions</a> with MinGW/Cygwin.</p><p>As mentioned in the link above: excessive memory use by <code>svchost</code> specifically may be investigated in the Task Manager by clicking on the high-memory <code>svchost.exe</code> process and selecting <code>Go to Services</code>. Disable child services one-by-one until a culprit is found.</p></li><li><p>Beware of <a href="https://cygwin.com/faq/faq.html#faq.using.bloda">BLODA</a>. The <a href="https://technet.microsoft.com/en-us/sysinternals/dd535533.aspx">vmmap</a> tool is indispensable for identifying such software conflicts. Use vmmap to inspect the list of loaded DLLs for bash, mintty, or another persistent process used to drive the build. Essentially <em>any</em> DLL outside of the Windows System directory is potential BLODA.</p></li></ul></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="macos.html">« macOS</a><a class="docs-footer-nextpage" href="freebsd.html">FreeBSD »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
