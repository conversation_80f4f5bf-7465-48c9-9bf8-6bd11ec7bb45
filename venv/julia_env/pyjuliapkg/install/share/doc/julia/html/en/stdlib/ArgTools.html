<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>ArgTools · The Julia Language</title><meta name="title" content="ArgTools · The Julia Language"/><meta property="og:title" content="ArgTools · The Julia Language"/><meta property="twitter:title" content="ArgTools · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li class="is-active"><a class="tocitem" href="ArgTools.html">ArgTools</a><ul class="internal"><li><a class="tocitem" href="#Argument-Handling"><span>Argument Handling</span></a></li><li><a class="tocitem" href="#Function-Testing"><span>Function Testing</span></a></li></ul></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="ArgTools.html">ArgTools</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="ArgTools.html">ArgTools</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaIO/ArgTools.jl/blob/master/docs/src/index.md" title="View source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="ArgTools"><a class="docs-heading-anchor" href="#ArgTools">ArgTools</a><a id="ArgTools-1"></a><a class="docs-heading-anchor-permalink" href="#ArgTools" title="Permalink"></a></h1><h2 id="Argument-Handling"><a class="docs-heading-anchor" href="#Argument-Handling">Argument Handling</a><a id="Argument-Handling-1"></a><a class="docs-heading-anchor-permalink" href="#Argument-Handling" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="ArgTools.ArgRead" href="#ArgTools.ArgRead"><code>ArgTools.ArgRead</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ArgRead = Union{AbstractString, AbstractCmd, IO}</code></pre><p>The <code>ArgRead</code> types is a union of the types that the <code>arg_read</code> function knows how to convert into readable IO handles. See <a href="ArgTools.html#ArgTools.arg_read"><code>arg_read</code></a> for details.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaIO/ArgTools.jl/blob/997089b9cd56404b40ff766759662e16dc1aab4b/src/ArgTools.jl#L42-L47">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="ArgTools.ArgWrite" href="#ArgTools.ArgWrite"><code>ArgTools.ArgWrite</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ArgWrite = Union{AbstractString, AbstractCmd, IO}</code></pre><p>The <code>ArgWrite</code> types is a union of the types that the <code>arg_write</code> function knows how to convert into writeable IO handles, except for <code>Nothing</code> which <code>arg_write</code> handles by generating a temporary file. See <a href="ArgTools.html#ArgTools.arg_write"><code>arg_write</code></a> for details.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaIO/ArgTools.jl/blob/997089b9cd56404b40ff766759662e16dc1aab4b/src/ArgTools.jl#L50-L56">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="ArgTools.arg_read" href="#ArgTools.arg_read"><code>ArgTools.arg_read</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">arg_read(f::Function, arg::ArgRead) -&gt; f(arg_io)</code></pre><p>The <code>arg_read</code> function accepts an argument <code>arg</code> that can be any of these:</p><ul><li><code>AbstractString</code>: a file path to be opened for reading</li><li><code>AbstractCmd</code>: a command to be run, reading from its standard output</li><li><code>IO</code>: an open IO handle to be read from</li></ul><p>Whether the body returns normally or throws an error, a path which is opened will be closed before returning from <code>arg_read</code> and an <code>IO</code> handle will be flushed but not closed before returning from <code>arg_read</code>.</p><p>Note: when opening a file, ArgTools will pass <code>lock = false</code> to the file <code>open(...)</code> call. Therefore, the object returned by this function should not be used from multiple threads. This restriction may be relaxed in the future, which would not break any working code.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaIO/ArgTools.jl/blob/997089b9cd56404b40ff766759662e16dc1aab4b/src/ArgTools.jl#L59-L73">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="ArgTools.arg_write" href="#ArgTools.arg_write"><code>ArgTools.arg_write</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">arg_write(f::Function, arg::ArgWrite) -&gt; arg
arg_write(f::Function, arg::Nothing) -&gt; tempname()</code></pre><p>The <code>arg_read</code> function accepts an argument <code>arg</code> that can be any of these:</p><ul><li><code>AbstractString</code>: a file path to be opened for writing</li><li><code>AbstractCmd</code>: a command to be run, writing to its standard input</li><li><code>IO</code>: an open IO handle to be written to</li><li><code>Nothing</code>: a temporary path should be written to</li></ul><p>If the body returns normally, a path that is opened will be closed upon completion; an IO handle argument is left open but flushed before return. If the argument is <code>nothing</code> then a temporary path is opened for writing and closed open completion and the path is returned from <code>arg_write</code>. In all other cases, <code>arg</code> itself is returned. This is a useful pattern since you can consistently return whatever was written, whether an argument was passed or not.</p><p>If there is an error during the evaluation of the body, a path that is opened by <code>arg_write</code> for writing will be deleted, whether it&#39;s passed in as a string or a temporary path generated when <code>arg</code> is <code>nothing</code>.</p><p>Note: when opening a file, ArgTools will pass <code>lock = false</code> to the file <code>open(...)</code> call. Therefore, the object returned by this function should not be used from multiple threads. This restriction may be relaxed in the future, which would not break any working code.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaIO/ArgTools.jl/blob/997089b9cd56404b40ff766759662e16dc1aab4b/src/ArgTools.jl#L78-L101">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="ArgTools.arg_isdir" href="#ArgTools.arg_isdir"><code>ArgTools.arg_isdir</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">arg_isdir(f::Function, arg::AbstractString) -&gt; f(arg)</code></pre><p>The <code>arg_isdir</code> function takes <code>arg</code> which must be the path to an existing directory (an error is raised otherwise) and passes that path to <code>f</code> finally returning the result of <code>f(arg)</code>. This is definitely the least useful tool offered by <code>ArgTools</code> and mostly exists for symmetry with <code>arg_mkdir</code> and to give consistent error messages.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaIO/ArgTools.jl/blob/997089b9cd56404b40ff766759662e16dc1aab4b/src/ArgTools.jl#L141-L149">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="ArgTools.arg_mkdir" href="#ArgTools.arg_mkdir"><code>ArgTools.arg_mkdir</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">arg_mkdir(f::Function, arg::AbstractString) -&gt; arg
arg_mkdir(f::Function, arg::Nothing) -&gt; mktempdir()</code></pre><p>The <code>arg_mkdir</code> function takes <code>arg</code> which must either be one of:</p><ul><li>a path to an already existing empty directory,</li><li>a non-existent path which can be created as a directory, or</li><li><code>nothing</code> in which case a temporary directory is created.</li></ul><p>In all cases the path to the directory is returned. If an error occurs during <code>f(arg)</code>, the directory is returned to its original state: if it already existed but was empty, it will be emptied; if it did not exist it will be deleted.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaIO/ArgTools.jl/blob/997089b9cd56404b40ff766759662e16dc1aab4b/src/ArgTools.jl#L155-L168">source</a></section></article><h2 id="Function-Testing"><a class="docs-heading-anchor" href="#Function-Testing">Function Testing</a><a id="Function-Testing-1"></a><a class="docs-heading-anchor-permalink" href="#Function-Testing" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="ArgTools.arg_readers" href="#ArgTools.arg_readers"><code>ArgTools.arg_readers</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">arg_readers(arg :: AbstractString, [ type = ArgRead ]) do arg::Function
    ## pre-test setup ##
    @arg_test arg begin
        arg :: ArgRead
        ## test using `arg` ##
    end
    ## post-test cleanup ##
end</code></pre><p>The <code>arg_readers</code> function takes a path to be read and a single-argument do block, which is invoked once for each test reader type that <code>arg_read</code> can handle. If the optional <code>type</code> argument is given then the do block is only invoked for readers that produce arguments of that type.</p><p>The <code>arg</code> passed to the do block is not the argument value itself, because some of test argument types need to be initialized and finalized for each test case. Consider an open file handle argument: once you&#39;ve used it for one test, you can&#39;t use it again; you need to close it and open the file again for the next test. This function <code>arg</code> can be converted into an <code>ArgRead</code> instance using <code>@arg_test arg begin ... end</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaIO/ArgTools.jl/blob/997089b9cd56404b40ff766759662e16dc1aab4b/src/ArgTools.jl#L223-L244">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="ArgTools.arg_writers" href="#ArgTools.arg_writers"><code>ArgTools.arg_writers</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">arg_writers([ type = ArgWrite ]) do path::String, arg::Function
    ## pre-test setup ##
    @arg_test arg begin
        arg :: ArgWrite
        ## test using `arg` ##
    end
    ## post-test cleanup ##
end</code></pre><p>The <code>arg_writers</code> function takes a do block, which is invoked once for each test writer type that <code>arg_write</code> can handle with a temporary (non-existent) <code>path</code> and <code>arg</code> which can be converted into various writable argument types which write to <code>path</code>. If the optional <code>type</code> argument is given then the do block is only invoked for writers that produce arguments of that type.</p><p>The <code>arg</code> passed to the do block is not the argument value itself, because some of test argument types need to be initialized and finalized for each test case. Consider an open file handle argument: once you&#39;ve used it for one test, you can&#39;t use it again; you need to close it and open the file again for the next test. This function <code>arg</code> can be converted into an <code>ArgWrite</code> instance using <code>@arg_test arg begin ... end</code>.</p><p>There is also an <code>arg_writers</code> method that takes a path name like <code>arg_readers</code>:</p><pre><code class="nohighlight hljs">arg_writers(path::AbstractString, [ type = ArgWrite ]) do arg::Function
    ## pre-test setup ##
    @arg_test arg begin
        # here `arg :: ArgWrite`
        ## test using `arg` ##
    end
    ## post-test cleanup ##
end</code></pre><p>This method is useful if you need to specify <code>path</code> instead of using path name generated by <code>tempname()</code>. Since <code>path</code> is passed from outside of <code>arg_writers</code>, the path is not an argument to the do block in this form.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaIO/ArgTools.jl/blob/997089b9cd56404b40ff766759662e16dc1aab4b/src/ArgTools.jl#L256-L293">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="ArgTools.@arg_test" href="#ArgTools.@arg_test"><code>ArgTools.@arg_test</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@arg_test arg1 arg2 ... body</code></pre><p>The <code>@arg_test</code> macro is used to convert <code>arg</code> functions provided by <code>arg_readers</code> and <code>arg_writers</code> into actual argument values. When you write <code>@arg_test arg body</code> it is equivalent to <code>arg(arg -&gt; body)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaIO/ArgTools.jl/blob/997089b9cd56404b40ff766759662e16dc1aab4b/src/ArgTools.jl#L319-L325">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="../base/simd-types.html">« SIMD Support</a><a class="docs-footer-nextpage" href="Artifacts.html">Artifacts »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
