<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Custom LLVM Passes · The Julia Language</title><meta name="title" content="Custom LLVM Passes · The Julia Language"/><meta property="og:title" content="Custom LLVM Passes · The Julia Language"/><meta property="twitter:title" content="Custom LLVM Passes · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox" checked/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="ast.html">Julia ASTs</a></li><li><a class="tocitem" href="types.html">More about types</a></li><li><a class="tocitem" href="object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="functions.html">Julia Functions</a></li><li><a class="tocitem" href="cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="subarrays.html">SubArrays</a></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li class="is-active"><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a><ul class="internal"><li><a class="tocitem" href="#Semantic-Passes"><span>Semantic Passes</span></a></li><li><a class="tocitem" href="#Optimization-Passes"><span>Optimization Passes</span></a></li></ul></li><li><a class="tocitem" href="llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Documentation of Julia&#39;s Internals</a></li><li class="is-active"><a href="llvm-passes.html">Custom LLVM Passes</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="llvm-passes.html">Custom LLVM Passes</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/llvm-passes.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Custom-LLVM-Passes"><a class="docs-heading-anchor" href="#Custom-LLVM-Passes">Custom LLVM Passes</a><a id="Custom-LLVM-Passes-1"></a><a class="docs-heading-anchor-permalink" href="#Custom-LLVM-Passes" title="Permalink"></a></h1><p>Julia has a number of custom LLVM passes. Broadly, they can be classified into passes that are required to be run to maintain Julia semantics, and passes that take advantage of Julia semantics to optimize LLVM IR.</p><h2 id="Semantic-Passes"><a class="docs-heading-anchor" href="#Semantic-Passes">Semantic Passes</a><a id="Semantic-Passes-1"></a><a class="docs-heading-anchor-permalink" href="#Semantic-Passes" title="Permalink"></a></h2><p>These passes are used to transform LLVM IR into code that is legal to be run on a CPU. Their main purpose is to enable simpler IR to be emitted by codegen, which then enables other LLVM passes to optimize common patterns.</p><h3 id="CPUFeatures"><a class="docs-heading-anchor" href="#CPUFeatures">CPUFeatures</a><a id="CPUFeatures-1"></a><a class="docs-heading-anchor-permalink" href="#CPUFeatures" title="Permalink"></a></h3><ul><li>Filename: <code>llvm-cpufeatures.cpp</code></li><li>Class Name: <code>CPUFeaturesPass</code></li><li>Opt Name: <code>module(CPUFeatures)</code></li></ul><p>This pass lowers the <code>julia.cpu.have_fma.(f32|f64)</code> intrinsic to either true or false, depending on the target architecture and target features present on the function. This intrinsic is often used to determine if using algorithms dependent on fast <a href="https://en.wikipedia.org/wiki/Multiply%E2%80%93accumulate_operation#Fused_multiply%E2%80%93add">fused multiply-add</a> operations is better than using standard algorithms not dependent on such instructions.</p><h3 id="DemoteFloat16"><a class="docs-heading-anchor" href="#DemoteFloat16">DemoteFloat16</a><a id="DemoteFloat16-1"></a><a class="docs-heading-anchor-permalink" href="#DemoteFloat16" title="Permalink"></a></h3><ul><li>Filename: <code>llvm-demote-float16.cpp</code></li><li>ClassName: <code>DemoteFloat16Pass</code></li><li>Opt Name <code>function(DemoteFloat16)</code></li></ul><p>This pass replaces <a href="https://en.wikipedia.org/wiki/Half-precision_floating-point_format">float16</a> operations with float32 operations on architectures that do not natively support float16 operations. This is done by inserting <code>fpext</code> and <code>fptrunc</code> instructions around any float16 operation. On architectures that do support native float16 operations, this pass is a no-op.</p><h3 id="LateGCLowering"><a class="docs-heading-anchor" href="#LateGCLowering">LateGCLowering</a><a id="LateGCLowering-1"></a><a class="docs-heading-anchor-permalink" href="#LateGCLowering" title="Permalink"></a></h3><ul><li>Filename: <code>llvm-late-gc-lowering.cpp</code></li><li>Class Name: <code>LateLowerGCPass</code></li><li>Opt Name: <code>function(LateLowerGCFrame)</code></li></ul><p>This pass performs most of the GC rooting work required to track pointers between GC safepoints. It also lowers several intrinsics to their corresponding instruction translation, and is permitted to violate the non-integral invariants previously established (<code>pointer_from_objref</code> is lowered to a <code>ptrtoint</code> instruction here). This pass typically occupies the most time out of all the custom Julia passes, due to its dataflow algorithm to minimize the number of objects live at any safepoint.</p><h3 id="FinalGCLowering"><a class="docs-heading-anchor" href="#FinalGCLowering">FinalGCLowering</a><a id="FinalGCLowering-1"></a><a class="docs-heading-anchor-permalink" href="#FinalGCLowering" title="Permalink"></a></h3><ul><li>Filename: <code>llvm-final-gc-lowering.cpp</code></li><li>Class Name: <code>FinalLowerGCPass</code></li><li>Opt Name: <code>module(FinalLowerGC)</code></li></ul><p>This pass lowers a few last intrinsics to their final form targeting functions in the <code>libjulia</code> library. Separating this from <code>LateGCLowering</code> enables other backends (GPU compilation) to supply their own custom lowerings for these intrinsics, enabling the Julia pipeline to be used on those backends as well.</p><h3 id="LowerHandlers"><a class="docs-heading-anchor" href="#LowerHandlers">LowerHandlers</a><a id="LowerHandlers-1"></a><a class="docs-heading-anchor-permalink" href="#LowerHandlers" title="Permalink"></a></h3><ul><li>Filename: <code>llvm-lower-handlers.cpp</code></li><li>Class Name: <code>LowerExcHandlersPass</code></li><li>Opt Name: <code>function(LowerExcHandlers)</code></li></ul><p>This pass lowers exception handling intrinsics into calls to runtime functions that are actually called when handling exceptions.</p><h3 id="RemoveNI"><a class="docs-heading-anchor" href="#RemoveNI">RemoveNI</a><a id="RemoveNI-1"></a><a class="docs-heading-anchor-permalink" href="#RemoveNI" title="Permalink"></a></h3><ul><li>Filename: <code>llvm-remove-ni.cpp</code></li><li>Class Name: <code>RemoveNIPass</code></li><li>Opt Name: <code>module(RemoveNI)</code></li></ul><p>This pass removes the non-integral address spaces from the module&#39;s datalayout string. This enables the backend to lower Julia&#39;s custom address spaces directly to machine code, without a costly rewrite of every pointer operation to address space 0.</p><h3 id="SIMDLoop"><a class="docs-heading-anchor" href="#SIMDLoop">SIMDLoop</a><a id="SIMDLoop-1"></a><a class="docs-heading-anchor-permalink" href="#SIMDLoop" title="Permalink"></a></h3><ul><li>Filename: <code>llvm-simdloop.cpp</code></li><li>Class Name: <code>LowerSIMDLoopPass</code></li><li>Opt Name: <code>loop(LowerSIMDLoop)</code></li></ul><p>This pass acts as the main driver of the <code>@simd</code> annotation. Codegen inserts a <code>!llvm.loopid</code> marker at the back branch of a loop, which this pass uses to identify loops that were originally marked with <code>@simd</code>. Then, this pass looks for a chain of floating point operations that form a reduce and adds the <code>contract</code> and <code>reassoc</code> fast math flags to allow reassociation (and thus vectorization). This pass does not preserve either loop information nor inference correctness, so it may violate Julia semantics in surprising ways. If the loop was annotated with <code>ivdep</code> as well, then the pass marks the loop as having no loop-carried dependencies (the resulting behavior is undefined if the user annotation was incorrect or gets applied to the wrong loop).</p><h3 id="LowerPTLS"><a class="docs-heading-anchor" href="#LowerPTLS">LowerPTLS</a><a id="LowerPTLS-1"></a><a class="docs-heading-anchor-permalink" href="#LowerPTLS" title="Permalink"></a></h3><ul><li>Filename: <code>llvm-ptls.cpp</code></li><li>Class Name: <code>LowerPTLSPass</code></li><li>Opt Name: <code>module(LowerPTLSPass)</code></li></ul><p>This pass lowers thread-local Julia intrinsics to assembly instructions. Julia relies on thread-local storage for garbage collection and multithreading task scheduling. When compiling code for system images and package images, this pass replaces calls to intrinsics with loads from global variables that are initialized at load time.</p><p>If codegen produces a function with a <code>swiftself</code> argument and calling convention, this pass assumes the <code>swiftself</code> argument is the pgcstack and will replace the intrinsics with that argument. Doing so provides speedups on architectures that have slow thread local storage accesses.</p><h3 id="RemoveAddrspaces"><a class="docs-heading-anchor" href="#RemoveAddrspaces">RemoveAddrspaces</a><a id="RemoveAddrspaces-1"></a><a class="docs-heading-anchor-permalink" href="#RemoveAddrspaces" title="Permalink"></a></h3><ul><li>Filename: <code>llvm-remove-addrspaces.cpp</code></li><li>Class Name: <code>RemoveAddrspacesPass</code></li><li>Opt Name: <code>module(RemoveAddrspaces)</code></li></ul><p>This pass renames pointers in one address space to another address space. This is used to remove Julia-specific address spaces from LLVM IR.</p><h3 id="RemoveJuliaAddrspaces"><a class="docs-heading-anchor" href="#RemoveJuliaAddrspaces">RemoveJuliaAddrspaces</a><a id="RemoveJuliaAddrspaces-1"></a><a class="docs-heading-anchor-permalink" href="#RemoveJuliaAddrspaces" title="Permalink"></a></h3><ul><li>Filename: <code>llvm-remove-addrspaces.cpp</code></li><li>Class Name: <code>RemoveJuliaAddrspacesPass</code></li><li>Opt Name: <code>module(RemoveJuliaAddrspaces)</code></li></ul><p>This pass removes Julia-specific address spaces from LLVM IR. It is mostly used for displaying LLVM IR in a less cluttered format. Internally, it is implemented off the RemoveAddrspaces pass.</p><h3 id="Multiversioning"><a class="docs-heading-anchor" href="#Multiversioning">Multiversioning</a><a id="Multiversioning-1"></a><a class="docs-heading-anchor-permalink" href="#Multiversioning" title="Permalink"></a></h3><ul><li>Filename: <code>llvm-multiversioning.cpp</code></li><li>Class Name: <code>MultiVersioningPass</code></li><li>Opt Name: <code>module(JuliaMultiVersioning)</code></li></ul><p>This pass performs modifications to a module to create functions that are optimized for running on different architectures (see sysimg.md and pkgimg.md for more details). Implementation-wise, it clones functions and applies different target-specific attributes to them to allow the optimizer to use advanced features such as vectorization and instruction scheduling for that platform. It also creates some infrastructure to enable the Julia image loader to select the appropriate version of the function to call based on the architecture the loader is running on. The target-specific attributes are controlled by the <code>julia.mv.specs</code> module flag, which during compilation is derived from the <a href="../manual/environment-variables.html#JULIA_CPU_TARGET"><code>JULIA_CPU_TARGET</code></a> environment variable. The pass must also be enabled by providing a <code>julia.mv.enable</code> module flag with a value of 1.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Use of <code>llvmcall</code> with multiversioning is dangerous. <code>llvmcall</code> enables access to features not typically exposed by the Julia APIs, and are therefore usually not available on all architectures. If multiversioning is enabled and code generation is requested for a target architecture that does not support the feature required by an <code>llvmcall</code> expression, LLVM will probably error out, likely with an abort and the message <code>LLVM ERROR: Do not know how to split the result of this operator!</code>.</p></div></div><h3 id="GCInvariantVerifier"><a class="docs-heading-anchor" href="#GCInvariantVerifier">GCInvariantVerifier</a><a id="GCInvariantVerifier-1"></a><a class="docs-heading-anchor-permalink" href="#GCInvariantVerifier" title="Permalink"></a></h3><ul><li>Filename: <code>llvm-gc-invariant-verifier.cpp</code></li><li>Class Name: <code>GCInvariantVerifierPass</code></li><li>Opt Name: <code>module(GCInvariantVerifier)</code></li></ul><p>This pass is used to verify Julia&#39;s invariants about LLVM IR. This includes things such as the nonexistence of <code>ptrtoint</code> in Julia&#39;s <a href="https://llvm.org/docs/LangRef.html#non-integral-pointer-type">non-integral address spaces</a> <sup class="footnote-reference"><a id="citeref-nislides" href="#footnote-nislides">[nislides]</a></sup> and the existence of only blessed <code>addrspacecast</code> instructions (Tracked -&gt; Derived, 0 -&gt; Tracked, etc). It performs no transformations on IR.</p><h2 id="Optimization-Passes"><a class="docs-heading-anchor" href="#Optimization-Passes">Optimization Passes</a><a id="Optimization-Passes-1"></a><a class="docs-heading-anchor-permalink" href="#Optimization-Passes" title="Permalink"></a></h2><p>These passes are used to perform transformations on LLVM IR that LLVM will not perform itself, e.g. fast math flag propagation, escape analysis, and optimizations on Julia-specific internal functions. They use knowledge about Julia&#39;s semantics to perform these optimizations.</p><h3 id="CombineMulAdd"><a class="docs-heading-anchor" href="#CombineMulAdd">CombineMulAdd</a><a id="CombineMulAdd-1"></a><a class="docs-heading-anchor-permalink" href="#CombineMulAdd" title="Permalink"></a></h3><ul><li>Filename: <code>llvm-muladd.cpp</code></li><li>Class Name: <code>CombineMulAddPass</code></li><li>Opt Name: <code>function(CombineMulAdd)</code></li></ul><p>This pass serves to optimize the particular combination of a regular <code>fmul</code> with a fast <code>fadd</code> into a contract <code>fmul</code> with a fast <code>fadd</code>. This is later optimized by the backend to a <a href="https://en.wikipedia.org/wiki/Multiply%E2%80%93accumulate_operation#Fused_multiply%E2%80%93add">fused multiply-add</a> instruction, which can provide significantly faster operations at the cost of more <a href="https://simonbyrne.github.io/notes/fastmath/">unpredictable semantics</a>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This optimization only occurs when the <code>fmul</code> has a single use, which is the fast <code>fadd</code>.</p></div></div><h3 id="AllocOpt"><a class="docs-heading-anchor" href="#AllocOpt">AllocOpt</a><a id="AllocOpt-1"></a><a class="docs-heading-anchor-permalink" href="#AllocOpt" title="Permalink"></a></h3><ul><li>Filename: <code>llvm-alloc-opt.cpp</code></li><li>Class Name: <code>AllocOptPass</code></li><li>Opt Name: <code>function(AllocOpt)</code></li></ul><p>Julia does not have the concept of a program stack as a place to allocate mutable objects. However, allocating objects on the stack reduces GC pressure and is critical for GPU compilation. Thus, <code>AllocOpt</code> performs heap to stack conversion of objects that it can prove do not <a href="https://en.wikipedia.org/wiki/Escape_analysis">escape</a> the current function. It also performs a number of other optimizations on allocations, such as removing allocations that are never used, optimizing typeof calls to freshly allocated objects, and removing stores to allocations that are immediately overwritten. The escape analysis implementation is located in <code>llvm-alloc-helpers.cpp</code>. Currently, this pass does not use information from <code>EscapeAnalysis.jl</code>, though that may change in the future.</p><h3 id="PropagateJuliaAddrspaces"><a class="docs-heading-anchor" href="#PropagateJuliaAddrspaces">PropagateJuliaAddrspaces</a><a id="PropagateJuliaAddrspaces-1"></a><a class="docs-heading-anchor-permalink" href="#PropagateJuliaAddrspaces" title="Permalink"></a></h3><ul><li>Filename: <code>llvm-propagate-addrspaces.cpp</code></li><li>Class Name: <code>PropagateJuliaAddrspacesPass</code></li><li>Opt Name: <code>function(PropagateJuliaAddrspaces)</code></li></ul><p>This pass is used to propagate Julia-specific address spaces through operations on pointers. LLVM is not allowed to introduce or remove addrspacecast instructions by optimizations, so this pass acts to eliminate redundant addrspace casts by replacing operations with their equivalent in a Julia address space. For more information on Julia&#39;s address spaces, see (TODO link to llvm.md).</p><h3 id="JuliaLICM"><a class="docs-heading-anchor" href="#JuliaLICM">JuliaLICM</a><a id="JuliaLICM-1"></a><a class="docs-heading-anchor-permalink" href="#JuliaLICM" title="Permalink"></a></h3><ul><li>Filename: <code>llvm-julia-licm.cpp</code></li><li>Class Name: <code>JuliaLICMPass</code></li><li>Opt Name: <code>loop(JuliaLICM)</code></li></ul><p>This pass is used to hoist Julia-specific intrinsics out of loops. Specifically, it performs the following transformations:</p><ol><li>Hoist <code>gc_preserve_begin</code> and sink <code>gc_preserve_end</code> out of loops when the preserved objects are loop-invariant.<ol><li>Since objects preserved within a loop are likely preserved for the duration of the loop, this transformation can reduce the number of <code>gc_preserve_begin</code>/<code>gc_preserve_end</code> pairs in the IR. This makes it easier for the <code>LateLowerGCPass</code> to identify where particular objects are preserved.</li></ol></li><li>Hoist write barriers with invariant objects<ol><li>Here we assume that there are only two generations that an object can be a part of. Given that, a write barrier needs to only execute once for any pair of the same object. Thus, we can hoist write barriers out of loops when the object being written to is loop-invariant.</li></ol></li><li>Hoist allocations out of loops when they do not escape the loop<ol><li>We use a very conservative definition of escape here, the same as the one used in <code>AllocOptPass</code>. This transformation can reduce the number of allocations in the IR, even when an allocation escapes the function altogether.</li></ol></li></ol><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This pass is required to preserve LLVM&#39;s <a href="https://llvm.org/docs/MemorySSA.html">MemorySSA</a> (<a href="https://www.youtube.com/watch?v=bdxWmryoHak">Short Video</a>, <a href="https://www.youtube.com/watch?v=1e5y6WDbXCQ">Longer Video</a>) and <a href="https://baziotis.cs.illinois.edu/compilers/introduction-to-scalar-evolution.html">ScalarEvolution</a> (<a href="https://llvm.org/devmtg/2018-04/slides/Absar-ScalarEvolution.pdf">Newer Slides</a> <a href="https://llvm.org/devmtg/2009-10/ScalarEvolutionAndLoopOptimization.pdf">Older Slides</a>) analyses.</p></div></div><section class="footnotes is-size-7"><ul><li class="footnote" id="footnote-nislides"><a class="tag is-link" href="#citeref-nislides">nislides</a>https://llvm.org/devmtg/2015-02/slides/chisnall-pointers-not-int.pdf</li></ul></section></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="pkgimg.html">« Package Images</a><a class="docs-footer-nextpage" href="llvm.html">Working with LLVM »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
