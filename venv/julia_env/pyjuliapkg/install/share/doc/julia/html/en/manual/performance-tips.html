<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Performance Tips · The Julia Language</title><meta name="title" content="Performance Tips · The Julia Language"/><meta property="og:title" content="Performance Tips · The Julia Language"/><meta property="twitter:title" content="Performance Tips · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li class="is-active"><a class="tocitem" href="performance-tips.html">Performance Tips</a><ul class="internal"><li><a class="tocitem" href="#Performance-critical-code-should-be-inside-a-function"><span>Performance critical code should be inside a function</span></a></li><li><a class="tocitem" href="#Avoid-untyped-global-variables"><span>Avoid untyped global variables</span></a></li><li><a class="tocitem" href="#Measure-performance-with-[@time](@ref)-and-pay-attention-to-memory-allocation"><span>Measure performance with <code>@time</code> and pay attention to memory allocation</span></a></li><li><a class="tocitem" href="#tools"><span>Tools</span></a></li><li><a class="tocitem" href="#man-performance-abstract-container"><span>Avoid containers with abstract type parameters</span></a></li><li><a class="tocitem" href="#Type-declarations"><span>Type declarations</span></a></li><li><a class="tocitem" href="#Break-functions-into-multiple-definitions"><span>Break functions into multiple definitions</span></a></li><li><a class="tocitem" href="#Write-&quot;type-stable&quot;-functions"><span>Write &quot;type-stable&quot; functions</span></a></li><li><a class="tocitem" href="#Avoid-changing-the-type-of-a-variable"><span>Avoid changing the type of a variable</span></a></li><li><a class="tocitem" href="#kernel-functions"><span>Separate kernel functions (aka, function barriers)</span></a></li><li><a class="tocitem" href="#man-performance-value-type"><span>Types with values-as-parameters</span></a></li><li><a class="tocitem" href="#The-dangers-of-abusing-multiple-dispatch-(aka,-more-on-types-with-values-as-parameters)"><span>The dangers of abusing multiple dispatch (aka, more on types with values-as-parameters)</span></a></li><li><a class="tocitem" href="#man-performance-column-major"><span>Access arrays in memory order, along columns</span></a></li><li><a class="tocitem" href="#Pre-allocating-outputs"><span>Pre-allocating outputs</span></a></li><li><a class="tocitem" href="#man-perftips-mutablearithmetics"><span>Use <code>MutableArithmetics</code> for more control over allocation for mutable arithmetic types</span></a></li><li><a class="tocitem" href="#More-dots:-Fuse-vectorized-operations"><span>More dots: Fuse vectorized operations</span></a></li><li><a class="tocitem" href="#man-performance-unfuse"><span>Fewer dots: Unfuse certain intermediate broadcasts</span></a></li><li><a class="tocitem" href="#man-performance-views"><span>Consider using views for slices</span></a></li><li><a class="tocitem" href="#Copying-data-is-not-always-bad"><span>Copying data is not always bad</span></a></li><li><a class="tocitem" href="#Consider-StaticArrays.jl-for-small-fixed-size-vector/matrix-operations"><span>Consider StaticArrays.jl for small fixed-size vector/matrix operations</span></a></li><li><a class="tocitem" href="#Avoid-string-interpolation-for-I/O"><span>Avoid string interpolation for I/O</span></a></li><li><a class="tocitem" href="#Optimize-network-I/O-during-parallel-execution"><span>Optimize network I/O during parallel execution</span></a></li><li><a class="tocitem" href="#Fix-deprecation-warnings"><span>Fix deprecation warnings</span></a></li><li><a class="tocitem" href="#Tweaks"><span>Tweaks</span></a></li><li><a class="tocitem" href="#man-performance-annotations"><span>Performance Annotations</span></a></li><li><a class="tocitem" href="#Treat-Subnormal-Numbers-as-Zeros"><span>Treat Subnormal Numbers as Zeros</span></a></li><li><a class="tocitem" href="#man-code-warntype"><span><code>@code_warntype</code></span></a></li><li><a class="tocitem" href="#man-performance-captured"><span>Performance of captured variable</span></a></li><li><a class="tocitem" href="#man-multithreading-linear-algebra"><span>Multithreading and linear algebra</span></a></li><li><a class="tocitem" href="#man-backends-linear-algebra"><span>Alternative linear algebra backends</span></a></li><li><a class="tocitem" href="#Execution-latency,-package-loading-and-package-precompiling-time"><span>Execution latency, package loading and package precompiling time</span></a></li></ul></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="performance-tips.html">Performance Tips</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="performance-tips.html">Performance Tips</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/performance-tips.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="man-performance-tips"><a class="docs-heading-anchor" href="#man-performance-tips">Performance Tips</a><a id="man-performance-tips-1"></a><a class="docs-heading-anchor-permalink" href="#man-performance-tips" title="Permalink"></a></h1><p>In the following sections, we briefly go through a few techniques that can help make your Julia code run as fast as possible.</p><h2 id="Performance-critical-code-should-be-inside-a-function"><a class="docs-heading-anchor" href="#Performance-critical-code-should-be-inside-a-function">Performance critical code should be inside a function</a><a id="Performance-critical-code-should-be-inside-a-function-1"></a><a class="docs-heading-anchor-permalink" href="#Performance-critical-code-should-be-inside-a-function" title="Permalink"></a></h2><p>Any code that is performance critical should be inside a function. Code inside functions tends to run much faster than top level code, due to how Julia&#39;s compiler works.</p><p>The use of functions is not only important for performance: functions are more reusable and testable, and clarify what steps are being done and what their inputs and outputs are, <a href="style-guide.html#Write-functions,-not-just-scripts">Write functions, not just scripts</a> is also a recommendation of Julia&#39;s Styleguide.</p><p>The functions should take arguments, instead of operating directly on global variables, see the next point.</p><h2 id="Avoid-untyped-global-variables"><a class="docs-heading-anchor" href="#Avoid-untyped-global-variables">Avoid untyped global variables</a><a id="Avoid-untyped-global-variables-1"></a><a class="docs-heading-anchor-permalink" href="#Avoid-untyped-global-variables" title="Permalink"></a></h2><p>The value of an untyped global variable might change at any point, possibly leading to a change of its type. This makes it difficult for the compiler to optimize code using global variables. This also applies to type-valued variables, i.e. type aliases on the global level. Variables should be local, or passed as arguments to functions, whenever possible.</p><p>We find that global names are frequently constants, and declaring them as such greatly improves performance:</p><pre><code class="language-julia hljs">const DEFAULT_VAL = 0</code></pre><p>If a global is known to always be of the same type, <a href="variables-and-scoping.html#man-typed-globals">the type should be annotated</a>.</p><p>Uses of untyped globals can be optimized by annotating their types at the point of use:</p><pre><code class="language-julia hljs">global x = rand(1000)

function loop_over_global()
    s = 0.0
    for i in x::Vector{Float64}
        s += i
    end
    return s
end</code></pre><p>Passing arguments to functions is better style. It leads to more reusable code and clarifies what the inputs and outputs are.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>All code in the REPL is evaluated in global scope, so a variable defined and assigned at top level will be a <strong>global</strong> variable. Variables defined at top level scope inside modules are also global.</p></div></div><p>In the following REPL session:</p><pre><code class="language-julia-repl hljs">julia&gt; x = 1.0</code></pre><p>is equivalent to:</p><pre><code class="language-julia-repl hljs">julia&gt; global x = 1.0</code></pre><p>so all the performance issues discussed previously apply.</p><h2 id="Measure-performance-with-[@time](@ref)-and-pay-attention-to-memory-allocation"><a class="docs-heading-anchor" href="#Measure-performance-with-[@time](@ref)-and-pay-attention-to-memory-allocation">Measure performance with <a href="profile.html#@time"><code>@time</code></a> and pay attention to memory allocation</a><a id="Measure-performance-with-[@time](@ref)-and-pay-attention-to-memory-allocation-1"></a><a class="docs-heading-anchor-permalink" href="#Measure-performance-with-[@time](@ref)-and-pay-attention-to-memory-allocation" title="Permalink"></a></h2><p>A useful tool for measuring performance is the <a href="profile.html#@time"><code>@time</code></a> macro. We here repeat the example with the global variable above, but this time with the type annotation removed:</p><pre><code class="language-julia-repl hljs">julia&gt; x = rand(1000);

julia&gt; function sum_global()
           s = 0.0
           for i in x
               s += i
           end
           return s
       end;

julia&gt; @time sum_global()
  0.011539 seconds (9.08 k allocations: 373.386 KiB, 98.69% compilation time)
523.0007221951678

julia&gt; @time sum_global()
  0.000091 seconds (3.49 k allocations: 70.156 KiB)
523.0007221951678</code></pre><p>On the first call (<code>@time sum_global()</code>) the function gets compiled. (If you&#39;ve not yet used <a href="profile.html#@time"><code>@time</code></a> in this session, it will also compile functions needed for timing.)  You should not take the results of this run seriously. For the second run, note that in addition to reporting the time, it also indicated that a significant amount of memory was allocated. We are here just computing a sum over all elements in a vector of 64-bit floats so there should be no need to allocate (heap) memory.</p><p>We should clarify that what <code>@time</code> reports is specifically <em>heap</em> allocations, which are typically needed for either mutable objects or for creating/growing variable-sized containers (such as <code>Array</code> or <code>Dict</code>, strings, or &quot;type-unstable&quot; objects whose type is only known at runtime).  Allocating (or deallocating) such blocks of memory may require an expensive function call to libc (e.g. via <code>malloc</code> in C), and they must be tracked for garbage collection.  In contrast, immutable values like numbers (except bignums), tuples, and immutable <code>struct</code>s can be stored much more cheaply, e.g. in stack or CPU-register memory, so one doesn’t typically worry about the performance cost of &quot;allocating&quot; them.</p><p>Unexpected memory allocation is almost always a sign of some problem with your code, usually a problem with type-stability or creating many small temporary arrays. Consequently, in addition to the allocation itself, it&#39;s very likely that the code generated for your function is far from optimal. Take such indications seriously and follow the advice below.</p><p>In this particular case, the memory allocation is due to the usage of a type-unstable global variable <code>x</code>, so if we instead pass <code>x</code> as an argument to the function it no longer allocates memory (the remaining allocation reported below is due to running the <code>@time</code> macro in global scope) and is significantly faster after the first call:</p><pre><code class="language-julia-repl hljs">julia&gt; x = rand(1000);

julia&gt; function sum_arg(x)
           s = 0.0
           for i in x
               s += i
           end
           return s
       end;

julia&gt; @time sum_arg(x)
  0.007551 seconds (3.98 k allocations: 200.548 KiB, 99.77% compilation time)
523.0007221951678

julia&gt; @time sum_arg(x)
  0.000006 seconds (1 allocation: 16 bytes)
523.0007221951678</code></pre><p>The 1 allocation seen is from running the <code>@time</code> macro itself in global scope. If we instead run the timing in a function, we can see that indeed no allocations are performed:</p><pre><code class="language-julia-repl hljs">julia&gt; time_sum(x) = @time sum_arg(x);

julia&gt; time_sum(x)
  0.000002 seconds
523.0007221951678</code></pre><p>In some situations, your function may need to allocate memory as part of its operation, and this can complicate the simple picture above. In such cases, consider using one of the <a href="performance-tips.html#tools">tools</a> below to diagnose problems, or write a version of your function that separates allocation from its algorithmic aspects (see <a href="performance-tips.html#Pre-allocating-outputs">Pre-allocating outputs</a>).</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>For more serious benchmarking, consider the <a href="https://github.com/JuliaCI/BenchmarkTools.jl">BenchmarkTools.jl</a> package which among other things evaluates the function multiple times in order to reduce noise.</p></div></div><h2 id="tools"><a class="docs-heading-anchor" href="#tools">Tools</a><a id="tools-1"></a><a class="docs-heading-anchor-permalink" href="#tools" title="Permalink"></a></h2><p>Julia and its package ecosystem includes tools that may help you diagnose problems and improve the performance of your code:</p><ul><li><a href="profile.html#Profiling">Profiling</a> allows you to measure the performance of your running code and identify lines that serve as bottlenecks. For complex projects, the <a href="https://github.com/timholy/ProfileView.jl">ProfileView</a> package can help you visualize your profiling results.</li><li>The <a href="https://github.com/aviatesk/JET.jl">JET</a> package can help you find common performance problems in your code.</li><li>Unexpectedly-large memory allocations–as reported by <a href="profile.html#@time"><code>@time</code></a>, <a href="../base/base.html#Base.@allocated"><code>@allocated</code></a>, or the profiler (through calls to the garbage-collection routines)–hint that there might be issues with your code. If you don&#39;t see another reason for the allocations, suspect a type problem.  You can also start Julia with the <code>--track-allocation=user</code> option and examine the resulting <code>*.mem</code> files to see information about where those allocations occur. See <a href="profile.html#Memory-allocation-analysis">Memory allocation analysis</a>.</li><li><code>@code_warntype</code> generates a representation of your code that can be helpful in finding expressions that result in type uncertainty. See <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.@code_warntype"><code>@code_warntype</code></a> below.</li></ul><h2 id="man-performance-abstract-container"><a class="docs-heading-anchor" href="#man-performance-abstract-container">Avoid containers with abstract type parameters</a><a id="man-performance-abstract-container-1"></a><a class="docs-heading-anchor-permalink" href="#man-performance-abstract-container" title="Permalink"></a></h2><p>When working with parameterized types, including arrays, it is best to avoid parameterizing with abstract types where possible.</p><p>Consider the following:</p><pre><code class="language-julia-repl hljs">julia&gt; a = Real[]
Real[]

julia&gt; push!(a, 1); push!(a, 2.0); push!(a, π)
3-element Vector{Real}:
 1
 2.0
 π = 3.1415926535897...</code></pre><p>Because <code>a</code> is an array of abstract type <a href="../base/numbers.html#Core.Real"><code>Real</code></a>, it must be able to hold any <code>Real</code> value. Since <code>Real</code> objects can be of arbitrary size and structure, <code>a</code> must be represented as an array of pointers to individually allocated <code>Real</code> objects. However, if we instead only allow numbers of the same type, e.g. <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a>, to be stored in <code>a</code> these can be stored more efficiently:</p><pre><code class="language-julia-repl hljs">julia&gt; a = Float64[]
Float64[]

julia&gt; push!(a, 1); push!(a, 2.0); push!(a,  π)
3-element Vector{Float64}:
 1.0
 2.0
 3.141592653589793</code></pre><p>Assigning numbers into <code>a</code> will now convert them to <code>Float64</code> and <code>a</code> will be stored as a contiguous block of 64-bit floating-point values that can be manipulated efficiently.</p><p>If you cannot avoid containers with abstract value types, it is sometimes better to parametrize with <code>Any</code> to avoid runtime type checking. E.g. <code>IdDict{Any, Any}</code> performs better than <code>IdDict{Type, Vector}</code></p><p>See also the discussion under <a href="types.html#Parametric-Types">Parametric Types</a>.</p><h2 id="Type-declarations"><a class="docs-heading-anchor" href="#Type-declarations">Type declarations</a><a id="Type-declarations-1"></a><a class="docs-heading-anchor-permalink" href="#Type-declarations" title="Permalink"></a></h2><p>In many languages with optional type declarations, adding declarations is the principal way to make code run faster. This is <em>not</em> the case in Julia. In Julia, the compiler generally knows the types of all function arguments, local variables, and expressions. However, there are a few specific instances where declarations are helpful.</p><h3 id="Avoid-fields-with-abstract-type"><a class="docs-heading-anchor" href="#Avoid-fields-with-abstract-type">Avoid fields with abstract type</a><a id="Avoid-fields-with-abstract-type-1"></a><a class="docs-heading-anchor-permalink" href="#Avoid-fields-with-abstract-type" title="Permalink"></a></h3><p>Types can be declared without specifying the types of their fields:</p><pre><code class="language-julia-repl hljs">julia&gt; struct MyAmbiguousType
           a
       end</code></pre><p>This allows <code>a</code> to be of any type. This can often be useful, but it does have a downside: for objects of type <code>MyAmbiguousType</code>, the compiler will not be able to generate high-performance code. The reason is that the compiler uses the types of objects, not their values, to determine how to build code. Unfortunately, very little can be inferred about an object of type <code>MyAmbiguousType</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; b = MyAmbiguousType(&quot;Hello&quot;)
MyAmbiguousType(&quot;Hello&quot;)

julia&gt; c = MyAmbiguousType(17)
MyAmbiguousType(17)

julia&gt; typeof(b)
MyAmbiguousType

julia&gt; typeof(c)
MyAmbiguousType</code></pre><p>The values of <code>b</code> and <code>c</code> have the same type, yet their underlying representation of data in memory is very different. Even if you stored just numeric values in field <code>a</code>, the fact that the memory representation of a <a href="../base/numbers.html#Core.UInt8"><code>UInt8</code></a> differs from a <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> also means that the CPU needs to handle them using two different kinds of instructions. Since the required information is not available in the type, such decisions have to be made at run-time. This slows performance.</p><p>You can do better by declaring the type of <code>a</code>. Here, we are focused on the case where <code>a</code> might be any one of several types, in which case the natural solution is to use parameters. For example:</p><pre><code class="language-julia-repl hljs">julia&gt; mutable struct MyType{T&lt;:AbstractFloat}
           a::T
       end</code></pre><p>This is a better choice than</p><pre><code class="language-julia-repl hljs">julia&gt; mutable struct MyStillAmbiguousType
           a::AbstractFloat
       end</code></pre><p>because the first version specifies the type of <code>a</code> from the type of the wrapper object. For example:</p><pre><code class="language-julia-repl hljs">julia&gt; m = MyType(3.2)
MyType{Float64}(3.2)

julia&gt; t = MyStillAmbiguousType(3.2)
MyStillAmbiguousType(3.2)

julia&gt; typeof(m)
MyType{Float64}

julia&gt; typeof(t)
MyStillAmbiguousType</code></pre><p>The type of field <code>a</code> can be readily determined from the type of <code>m</code>, but not from the type of <code>t</code>. Indeed, in <code>t</code> it&#39;s possible to change the type of the field <code>a</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; typeof(t.a)
Float64

julia&gt; t.a = 4.5f0
4.5f0

julia&gt; typeof(t.a)
Float32</code></pre><p>In contrast, once <code>m</code> is constructed, the type of <code>m.a</code> cannot change:</p><pre><code class="language-julia-repl hljs">julia&gt; m.a = 4.5f0
4.5f0

julia&gt; typeof(m.a)
Float64</code></pre><p>The fact that the type of <code>m.a</code> is known from <code>m</code>&#39;s type—coupled with the fact that its type cannot change mid-function—allows the compiler to generate highly-optimized code for objects like <code>m</code> but not for objects like <code>t</code>.</p><p>Of course, all of this is true only if we construct <code>m</code> with a concrete type. We can break this by explicitly constructing it with an abstract type:</p><pre><code class="language-julia-repl hljs">julia&gt; m = MyType{AbstractFloat}(3.2)
MyType{AbstractFloat}(3.2)

julia&gt; typeof(m.a)
Float64

julia&gt; m.a = 4.5f0
4.5f0

julia&gt; typeof(m.a)
Float32</code></pre><p>For all practical purposes, such objects behave identically to those of <code>MyStillAmbiguousType</code>.</p><p>It&#39;s quite instructive to compare the sheer amount of code generated for a simple function</p><pre><code class="language-julia hljs">func(m::MyType) = m.a+1</code></pre><p>using</p><pre><code class="language-julia hljs">code_llvm(func, Tuple{MyType{Float64}})
code_llvm(func, Tuple{MyType{AbstractFloat}})</code></pre><p>For reasons of length the results are not shown here, but you may wish to try this yourself. Because the type is fully-specified in the first case, the compiler doesn&#39;t need to generate any code to resolve the type at run-time. This results in shorter and faster code.</p><p>One should also keep in mind that not-fully-parameterized types behave like abstract types. For example, even though a fully specified <code>Array{T,n}</code> is concrete, <code>Array</code> itself with no parameters given is not concrete:</p><pre><code class="language-julia-repl hljs">julia&gt; !isconcretetype(Array), !isabstracttype(Array), isstructtype(Array), !isconcretetype(Array{Int}), isconcretetype(Array{Int,1})
(true, true, true, true, true)</code></pre><p>In this case, it would be better to avoid declaring <code>MyType</code> with a field <code>a::Array</code> and instead declare the field as <code>a::Array{T,N}</code> or as <code>a::A</code>, where <code>{T,N}</code> or <code>A</code> are parameters of <code>MyType</code>.</p><p>The previous advice is especially useful when the fields of a struct are meant to be functions, or more generally callable objects. It is very tempting to define a struct as follows:</p><pre><code class="language-julia hljs">struct MyCallableWrapper
    f::Function
end</code></pre><p>But since <code>Function</code> is an abstract type, every call to <code>wrapper.f</code> will require dynamic dispatch, due to the type instability of accessing the field <code>f</code>. Instead, you should write something like:</p><pre><code class="language-julia hljs">struct MyCallableWrapper{F}
    f::F
end</code></pre><p>which has nearly identical behavior but will be much faster (because the type instability is eliminated). Note that we do not impose <code>F&lt;:Function</code>: this means callable objects which do not subtype <code>Function</code> are also allowed for the field <code>f</code>.</p><h3 id="Avoid-fields-with-abstract-containers"><a class="docs-heading-anchor" href="#Avoid-fields-with-abstract-containers">Avoid fields with abstract containers</a><a id="Avoid-fields-with-abstract-containers-1"></a><a class="docs-heading-anchor-permalink" href="#Avoid-fields-with-abstract-containers" title="Permalink"></a></h3><p>The same best practices also work for container types:</p><pre><code class="language-julia-repl hljs">julia&gt; struct MySimpleContainer{A&lt;:AbstractVector}
           a::A
       end

julia&gt; struct MyAmbiguousContainer{T}
           a::AbstractVector{T}
       end

julia&gt; struct MyAlsoAmbiguousContainer
           a::Array
       end</code></pre><p>For example:</p><pre><code class="language-julia-repl hljs">julia&gt; c = MySimpleContainer(1:3);

julia&gt; typeof(c)
MySimpleContainer{UnitRange{Int64}}

julia&gt; c = MySimpleContainer([1:3;]);

julia&gt; typeof(c)
MySimpleContainer{Vector{Int64}}

julia&gt; b = MyAmbiguousContainer(1:3);

julia&gt; typeof(b)
MyAmbiguousContainer{Int64}

julia&gt; b = MyAmbiguousContainer([1:3;]);

julia&gt; typeof(b)
MyAmbiguousContainer{Int64}

julia&gt; d = MyAlsoAmbiguousContainer(1:3);

julia&gt; typeof(d), typeof(d.a)
(MyAlsoAmbiguousContainer, Vector{Int64})

julia&gt; d = MyAlsoAmbiguousContainer(1:1.0:3);

julia&gt; typeof(d), typeof(d.a)
(MyAlsoAmbiguousContainer, Vector{Float64})
</code></pre><p>For <code>MySimpleContainer</code>, the object is fully-specified by its type and parameters, so the compiler can generate optimized functions. In most instances, this will probably suffice.</p><p>While the compiler can now do its job perfectly well, there are cases where <em>you</em> might wish that your code could do different things depending on the <em>element type</em> of <code>a</code>. Usually the best way to achieve this is to wrap your specific operation (here, <code>foo</code>) in a separate function:</p><pre><code class="language-julia-repl hljs">julia&gt; function sumfoo(c::MySimpleContainer)
           s = 0
           for x in c.a
               s += foo(x)
           end
           s
       end
sumfoo (generic function with 1 method)

julia&gt; foo(x::Integer) = x
foo (generic function with 1 method)

julia&gt; foo(x::AbstractFloat) = round(x)
foo (generic function with 2 methods)</code></pre><p>This keeps things simple, while allowing the compiler to generate optimized code in all cases.</p><p>However, there are cases where you may need to declare different versions of the outer function for different element types or types of the <code>AbstractVector</code> of the field <code>a</code> in <code>MySimpleContainer</code>. You could do it like this:</p><pre><code class="language-julia-repl hljs">julia&gt; function myfunc(c::MySimpleContainer{&lt;:AbstractArray{&lt;:Integer}})
           return c.a[1]+1
       end
myfunc (generic function with 1 method)

julia&gt; function myfunc(c::MySimpleContainer{&lt;:AbstractArray{&lt;:AbstractFloat}})
           return c.a[1]+2
       end
myfunc (generic function with 2 methods)

julia&gt; function myfunc(c::MySimpleContainer{Vector{T}}) where T &lt;: Integer
           return c.a[1]+3
       end
myfunc (generic function with 3 methods)</code></pre><pre><code class="language-julia-repl hljs">julia&gt; myfunc(MySimpleContainer(1:3))
2

julia&gt; myfunc(MySimpleContainer(1.0:3))
3.0

julia&gt; myfunc(MySimpleContainer([1:3;]))
4</code></pre><h3 id="Annotate-values-taken-from-untyped-locations"><a class="docs-heading-anchor" href="#Annotate-values-taken-from-untyped-locations">Annotate values taken from untyped locations</a><a id="Annotate-values-taken-from-untyped-locations-1"></a><a class="docs-heading-anchor-permalink" href="#Annotate-values-taken-from-untyped-locations" title="Permalink"></a></h3><p>It is often convenient to work with data structures that may contain values of any type (arrays of type <code>Array{Any}</code>). But, if you&#39;re using one of these structures and happen to know the type of an element, it helps to share this knowledge with the compiler:</p><pre><code class="language-julia hljs">function foo(a::Array{Any,1})
    x = a[1]::Int32
    b = x+1
    ...
end</code></pre><p>Here, we happened to know that the first element of <code>a</code> would be an <a href="../base/numbers.html#Core.Int32"><code>Int32</code></a>. Making an annotation like this has the added benefit that it will raise a run-time error if the value is not of the expected type, potentially catching certain bugs earlier.</p><p>In the case that the type of <code>a[1]</code> is not known precisely, <code>x</code> can be declared via <code>x = convert(Int32, a[1])::Int32</code>. The use of the <a href="../base/base.html#Base.convert"><code>convert</code></a> function allows <code>a[1]</code> to be any object convertible to an <code>Int32</code> (such as <code>UInt8</code>), thus increasing the genericity of the code by loosening the type requirement. Notice that <code>convert</code> itself needs a type annotation in this context in order to achieve type stability. This is because the compiler cannot deduce the type of the return value of a function, even <code>convert</code>, unless the types of all the function&#39;s arguments are known.</p><p>Type annotation will not enhance (and can actually hinder) performance if the type is abstract, or constructed at run-time. This is because the compiler cannot use the annotation to specialize the subsequent code, and the type-check itself takes time. For example, in the code:</p><pre><code class="language-julia hljs">function nr(a, prec)
    ctype = prec == 32 ? Float32 : Float64
    b = Complex{ctype}(a)
    c = (b + 1.0f0)::Complex{ctype}
    abs(c)
end</code></pre><p>the annotation of <code>c</code> harms performance. To write performant code involving types constructed at run-time, use the <a href="performance-tips.html#kernel-functions">function-barrier technique</a> discussed below, and ensure that the constructed type appears among the argument types of the kernel function so that the kernel operations are properly specialized by the compiler. For example, in the above snippet, as soon as <code>b</code> is constructed, it can be passed to another function <code>k</code>, the kernel. If, for example, function <code>k</code> declares <code>b</code> as an argument of type <code>Complex{T}</code>, where <code>T</code> is a type parameter, then a type annotation appearing in an assignment statement within <code>k</code> of the form:</p><pre><code class="language-julia hljs">c = (b + 1.0f0)::Complex{T}</code></pre><p>does not hinder performance (but does not help either) since the compiler can determine the type of <code>c</code> at the time <code>k</code> is compiled.</p><h3 id="Be-aware-of-when-Julia-avoids-specializing"><a class="docs-heading-anchor" href="#Be-aware-of-when-Julia-avoids-specializing">Be aware of when Julia avoids specializing</a><a id="Be-aware-of-when-Julia-avoids-specializing-1"></a><a class="docs-heading-anchor-permalink" href="#Be-aware-of-when-Julia-avoids-specializing" title="Permalink"></a></h3><p>As a heuristic, Julia avoids automatically <a href="methods.html#man-method-specializations">specializing</a> on argument type parameters in three specific cases: <code>Type</code>, <code>Function</code>, and <code>Vararg</code>. Julia will always specialize when the argument is used within the method, but not if the argument is just passed through to another function. This usually has no performance impact at runtime and <a href="../devdocs/functions.html#compiler-efficiency-issues">improves compiler performance</a>. If you find it does have a performance impact at runtime in your case, you can trigger specialization by adding a type parameter to the method declaration. Here are some examples:</p><p>This will not specialize:</p><pre><code class="language-julia hljs">function f_type(t)  # or t::Type
    x = ones(t, 10)
    return sum(map(sin, x))
end</code></pre><p>but this will:</p><pre><code class="language-julia hljs">function g_type(t::Type{T}) where T
    x = ones(T, 10)
    return sum(map(sin, x))
end</code></pre><p>These will not specialize:</p><pre><code class="language-julia hljs">f_func(f, num) = ntuple(f, div(num, 2))
g_func(g::Function, num) = ntuple(g, div(num, 2))</code></pre><p>but this will:</p><pre><code class="language-julia hljs">h_func(h::H, num) where {H} = ntuple(h, div(num, 2))</code></pre><p>This will not specialize:</p><pre><code class="language-julia hljs">f_vararg(x::Int...) = tuple(x...)</code></pre><p>but this will:</p><pre><code class="language-julia hljs">g_vararg(x::Vararg{Int, N}) where {N} = tuple(x...)</code></pre><p>One only needs to introduce a single type parameter to force specialization, even if the other types are unconstrained. For example, this will also specialize, and is useful when the arguments are not all of the same type:</p><pre><code class="language-julia hljs">h_vararg(x::Vararg{Any, N}) where {N} = tuple(x...)</code></pre><p>Note that <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.@code_typed"><code>@code_typed</code></a> and friends will always show you specialized code, even if Julia would not normally specialize that method call. You need to check the <a href="../devdocs/ast.html#ast-lowered-method">method internals</a> if you want to see whether specializations are generated when argument types are changed, i.e., if <code>Base.specializations(@which f(...))</code> contains specializations for the argument in question.</p><h2 id="Break-functions-into-multiple-definitions"><a class="docs-heading-anchor" href="#Break-functions-into-multiple-definitions">Break functions into multiple definitions</a><a id="Break-functions-into-multiple-definitions-1"></a><a class="docs-heading-anchor-permalink" href="#Break-functions-into-multiple-definitions" title="Permalink"></a></h2><p>Writing a function as many small definitions allows the compiler to directly call the most applicable code, or even inline it.</p><p>Here is an example of a &quot;compound function&quot; that should really be written as multiple definitions:</p><pre><code class="language-julia hljs">using LinearAlgebra

function mynorm(A)
    if isa(A, Vector)
        return sqrt(real(dot(A,A)))
    elseif isa(A, Matrix)
        return maximum(svdvals(A))
    else
        error(&quot;mynorm: invalid argument&quot;)
    end
end</code></pre><p>This can be written more concisely and efficiently as:</p><pre><code class="language-julia hljs">mynorm(x::Vector) = sqrt(real(dot(x, x)))
mynorm(A::Matrix) = maximum(svdvals(A))</code></pre><p>It should however be noted that the compiler is quite efficient at optimizing away the dead branches in code written as the <code>mynorm</code> example.</p><h2 id="Write-&quot;type-stable&quot;-functions"><a class="docs-heading-anchor" href="#Write-&quot;type-stable&quot;-functions">Write &quot;type-stable&quot; functions</a><a id="Write-&quot;type-stable&quot;-functions-1"></a><a class="docs-heading-anchor-permalink" href="#Write-&quot;type-stable&quot;-functions" title="Permalink"></a></h2><p>When possible, it helps to ensure that a function always returns a value of the same type. Consider the following definition:</p><pre><code class="language-julia hljs">pos(x) = x &lt; 0 ? 0 : x</code></pre><p>Although this seems innocent enough, the problem is that <code>0</code> is an integer (of type <code>Int</code>) and <code>x</code> might be of any type. Thus, depending on the value of <code>x</code>, this function might return a value of either of two types. This behavior is allowed, and may be desirable in some cases. But it can easily be fixed as follows:</p><pre><code class="language-julia hljs">pos(x) = x &lt; 0 ? zero(x) : x</code></pre><p>There is also a <a href="../base/numbers.html#Base.oneunit"><code>oneunit</code></a> function, and a more general <a href="../base/base.html#Base.oftype"><code>oftype(x, y)</code></a> function, which returns <code>y</code> converted to the type of <code>x</code>.</p><h2 id="Avoid-changing-the-type-of-a-variable"><a class="docs-heading-anchor" href="#Avoid-changing-the-type-of-a-variable">Avoid changing the type of a variable</a><a id="Avoid-changing-the-type-of-a-variable-1"></a><a class="docs-heading-anchor-permalink" href="#Avoid-changing-the-type-of-a-variable" title="Permalink"></a></h2><p>An analogous &quot;type-stability&quot; problem exists for variables used repeatedly within a function:</p><pre><code class="language-julia hljs">function foo()
    x = 1
    for i = 1:10
        x /= rand()
    end
    return x
end</code></pre><p>Local variable <code>x</code> starts as an integer, and after one loop iteration becomes a floating-point number (the result of <a href="../base/math.html#Base.:/"><code>/</code></a> operator). This makes it more difficult for the compiler to optimize the body of the loop. There are several possible fixes:</p><ul><li>Initialize <code>x</code> with <code>x = 1.0</code></li><li>Declare the type of <code>x</code> explicitly as <code>x::Float64 = 1</code></li><li>Use an explicit conversion by <code>x = oneunit(Float64)</code></li><li>Initialize with the first loop iteration, to <code>x = 1 / rand()</code>, then loop <code>for i = 2:10</code></li></ul><h2 id="kernel-functions"><a class="docs-heading-anchor" href="#kernel-functions">Separate kernel functions (aka, function barriers)</a><a id="kernel-functions-1"></a><a class="docs-heading-anchor-permalink" href="#kernel-functions" title="Permalink"></a></h2><p>Many functions follow a pattern of performing some set-up work, and then running many iterations to perform a core computation. Where possible, it is a good idea to put these core computations in separate functions. For example, the following contrived function returns an array of a randomly-chosen type:</p><pre><code class="language-julia-repl hljs">julia&gt; function strange_twos(n)
           a = Vector{rand(Bool) ? Int64 : Float64}(undef, n)
           for i = 1:n
               a[i] = 2
           end
           return a
       end;

julia&gt; strange_twos(3)
3-element Vector{Int64}:
 2
 2
 2</code></pre><p>This should be written as:</p><pre><code class="language-julia-repl hljs">julia&gt; function fill_twos!(a)
           for i = eachindex(a)
               a[i] = 2
           end
       end;

julia&gt; function strange_twos(n)
           a = Vector{rand(Bool) ? Int64 : Float64}(undef, n)
           fill_twos!(a)
           return a
       end;

julia&gt; strange_twos(3)
3-element Vector{Int64}:
 2
 2
 2</code></pre><p>Julia&#39;s compiler specializes code for argument types at function boundaries, so in the original implementation it does not know the type of <code>a</code> during the loop (since it is chosen randomly). Therefore the second version is generally faster since the inner loop can be recompiled as part of <code>fill_twos!</code> for different types of <code>a</code>.</p><p>The second form is also often better style and can lead to more code reuse.</p><p>This pattern is used in several places in Julia Base. For example, see <code>vcat</code> and <code>hcat</code> in <a href="https://github.com/JuliaLang/julia/blob/40fe264f4ffaa29b749bcf42239a89abdcbba846/base/abstractarray.jl#L1205-L1206"><code>abstractarray.jl</code></a>, or the <a href="../base/arrays.html#Base.fill!"><code>fill!</code></a> function, which we could have used instead of writing our own <code>fill_twos!</code>.</p><p>Functions like <code>strange_twos</code> occur when dealing with data of uncertain type, for example data loaded from an input file that might contain either integers, floats, strings, or something else.</p><h2 id="man-performance-value-type"><a class="docs-heading-anchor" href="#man-performance-value-type">Types with values-as-parameters</a><a id="man-performance-value-type-1"></a><a class="docs-heading-anchor-permalink" href="#man-performance-value-type" title="Permalink"></a></h2><p>Let&#39;s say you want to create an <code>N</code>-dimensional array that has size 3 along each axis. Such arrays can be created like this:</p><pre><code class="language-julia-repl hljs">julia&gt; A = fill(5.0, (3, 3))
3×3 Matrix{Float64}:
 5.0  5.0  5.0
 5.0  5.0  5.0
 5.0  5.0  5.0</code></pre><p>This approach works very well: the compiler can figure out that <code>A</code> is an <code>Array{Float64,2}</code> because it knows the type of the fill value (<code>5.0::Float64</code>) and the dimensionality (<code>(3, 3)::NTuple{2,Int}</code>). This implies that the compiler can generate very efficient code for any future usage of <code>A</code> in the same function.</p><p>But now let&#39;s say you want to write a function that creates a 3×3×... array in arbitrary dimensions; you might be tempted to write a function</p><pre><code class="language-julia-repl hljs">julia&gt; function array3(fillval, N)
           fill(fillval, ntuple(d-&gt;3, N))
       end
array3 (generic function with 1 method)

julia&gt; array3(5.0, 2)
3×3 Matrix{Float64}:
 5.0  5.0  5.0
 5.0  5.0  5.0
 5.0  5.0  5.0</code></pre><p>This works, but (as you can verify for yourself using <code>@code_warntype array3(5.0, 2)</code>) the problem is that the output type cannot be inferred: the argument <code>N</code> is a <em>value</em> of type <code>Int</code>, and type-inference does not (and cannot) predict its value in advance. This means that code using the output of this function has to be conservative, checking the type on each access of <code>A</code>; such code will be very slow.</p><p>Now, one very good way to solve such problems is by using the <a href="performance-tips.html#kernel-functions">function-barrier technique</a>. However, in some cases you might want to eliminate the type-instability altogether. In such cases, one approach is to pass the dimensionality as a parameter, for example through <code>Val{T}()</code> (see <a href="types.html#&quot;Value-types&quot;">&quot;Value types&quot;</a>):</p><pre><code class="language-julia-repl hljs">julia&gt; function array3(fillval, ::Val{N}) where N
           fill(fillval, ntuple(d-&gt;3, Val(N)))
       end
array3 (generic function with 1 method)

julia&gt; array3(5.0, Val(2))
3×3 Matrix{Float64}:
 5.0  5.0  5.0
 5.0  5.0  5.0
 5.0  5.0  5.0</code></pre><p>Julia has a specialized version of <code>ntuple</code> that accepts a <code>Val{::Int}</code> instance as the second parameter; by passing <code>N</code> as a type-parameter, you make its &quot;value&quot; known to the compiler. Consequently, this version of <code>array3</code> allows the compiler to predict the return type.</p><p>However, making use of such techniques can be surprisingly subtle. For example, it would be of no help if you called <code>array3</code> from a function like this:</p><pre><code class="language-julia hljs">function call_array3(fillval, n)
    A = array3(fillval, Val(n))
end</code></pre><p>Here, you&#39;ve created the same problem all over again: the compiler can&#39;t guess what <code>n</code> is, so it doesn&#39;t know the <em>type</em> of <code>Val(n)</code>. Attempting to use <code>Val</code>, but doing so incorrectly, can easily make performance <em>worse</em> in many situations. (Only in situations where you&#39;re effectively combining <code>Val</code> with the function-barrier trick, to make the kernel function more efficient, should code like the above be used.)</p><p>An example of correct usage of <code>Val</code> would be:</p><pre><code class="language-julia hljs">function filter3(A::AbstractArray{T,N}) where {T,N}
    kernel = array3(1, Val(N))
    filter(A, kernel)
end</code></pre><p>In this example, <code>N</code> is passed as a parameter, so its &quot;value&quot; is known to the compiler. Essentially, <code>Val(T)</code> works only when <code>T</code> is either hard-coded/literal (<code>Val(3)</code>) or already specified in the type-domain.</p><h2 id="The-dangers-of-abusing-multiple-dispatch-(aka,-more-on-types-with-values-as-parameters)"><a class="docs-heading-anchor" href="#The-dangers-of-abusing-multiple-dispatch-(aka,-more-on-types-with-values-as-parameters)">The dangers of abusing multiple dispatch (aka, more on types with values-as-parameters)</a><a id="The-dangers-of-abusing-multiple-dispatch-(aka,-more-on-types-with-values-as-parameters)-1"></a><a class="docs-heading-anchor-permalink" href="#The-dangers-of-abusing-multiple-dispatch-(aka,-more-on-types-with-values-as-parameters)" title="Permalink"></a></h2><p>Once one learns to appreciate multiple dispatch, there&#39;s an understandable tendency to go overboard and try to use it for everything. For example, you might imagine using it to store information, e.g.</p><pre><code class="nohighlight hljs">struct Car{Make, Model}
    year::Int
    ...more fields...
end</code></pre><p>and then dispatch on objects like <code>Car{:Honda,:Accord}(year, args...)</code>.</p><p>This might be worthwhile when either of the following are true:</p><ul><li>You require CPU-intensive processing on each <code>Car</code>, and it becomes vastly more efficient if you know the <code>Make</code> and <code>Model</code> at compile time and the total number of different <code>Make</code> or <code>Model</code> that will be used is not too large.</li><li>You have homogeneous lists of the same type of <code>Car</code> to process, so that you can store them all in an <code>Array{Car{:Honda,:Accord},N}</code>.</li></ul><p>When the latter holds, a function processing such a homogeneous array can be productively specialized: Julia knows the type of each element in advance (all objects in the container have the same concrete type), so Julia can &quot;look up&quot; the correct method calls when the function is being compiled (obviating the need to check at run-time) and thereby emit efficient code for processing the whole list.</p><p>When these do not hold, then it&#39;s likely that you&#39;ll get no benefit; worse, the resulting &quot;combinatorial explosion of types&quot; will be counterproductive. If <code>items[i+1]</code> has a different type than <code>item[i]</code>, Julia has to look up the type at run-time, search for the appropriate method in method tables, decide (via type intersection) which one matches, determine whether it has been JIT-compiled yet (and do so if not), and then make the call. In essence, you&#39;re asking the full type- system and JIT-compilation machinery to basically execute the equivalent of a switch statement or dictionary lookup in your own code.</p><p>Some run-time benchmarks comparing (1) type dispatch, (2) dictionary lookup, and (3) a &quot;switch&quot; statement can be found <a href="https://groups.google.com/forum/#!msg/julia-users/jUMu9A3QKQQ/qjgVWr7vAwAJ">on the mailing list</a>.</p><p>Perhaps even worse than the run-time impact is the compile-time impact: Julia will compile specialized functions for each different <code>Car{Make, Model}</code>; if you have hundreds or thousands of such types, then every function that accepts such an object as a parameter (from a custom <code>get_year</code> function you might write yourself, to the generic <code>push!</code> function in Julia Base) will have hundreds or thousands of variants compiled for it. Each of these increases the size of the cache of compiled code, the length of internal lists of methods, etc. Excess enthusiasm for values-as-parameters can easily waste enormous resources.</p><h2 id="man-performance-column-major"><a class="docs-heading-anchor" href="#man-performance-column-major">Access arrays in memory order, along columns</a><a id="man-performance-column-major-1"></a><a class="docs-heading-anchor-permalink" href="#man-performance-column-major" title="Permalink"></a></h2><p>Multidimensional arrays in Julia are stored in column-major order. This means that arrays are stacked one column at a time. This can be verified using the <code>vec</code> function or the syntax <code>[:]</code> as shown below (notice that the array is ordered <code>[1 3 2 4]</code>, not <code>[1 2 3 4]</code>):</p><pre><code class="language-julia-repl hljs">julia&gt; x = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; x[:]
4-element Vector{Int64}:
 1
 3
 2
 4</code></pre><p>This convention for ordering arrays is common in many languages like Fortran, Matlab, and R (to name a few). The alternative to column-major ordering is row-major ordering, which is the convention adopted by C and Python (<code>numpy</code>) among other languages. Remembering the ordering of arrays can have significant performance effects when looping over arrays. A rule of thumb to keep in mind is that with column-major arrays, the first index changes most rapidly. Essentially this means that looping will be faster if the inner-most loop index is the first to appear in a slice expression. Keep in mind that indexing an array with <code>:</code> is an implicit loop that iteratively accesses all elements within a particular dimension; it can be faster to extract columns than rows, for example.</p><p>Consider the following contrived example. Imagine we wanted to write a function that accepts a <a href="../base/arrays.html#Base.Vector"><code>Vector</code></a> and returns a square <a href="../base/arrays.html#Base.Matrix"><code>Matrix</code></a> with either the rows or the columns filled with copies of the input vector. Assume that it is not important whether rows or columns are filled with these copies (perhaps the rest of the code can be easily adapted accordingly). We could conceivably do this in at least four ways (in addition to the recommended call to the built-in <a href="../base/arrays.html#Base.repeat"><code>repeat</code></a>):</p><pre><code class="language-julia hljs">function copy_cols(x::Vector{T}) where T
    inds = axes(x, 1)
    out = similar(Array{T}, inds, inds)
    for i = inds
        out[:, i] = x
    end
    return out
end

function copy_rows(x::Vector{T}) where T
    inds = axes(x, 1)
    out = similar(Array{T}, inds, inds)
    for i = inds
        out[i, :] = x
    end
    return out
end

function copy_col_row(x::Vector{T}) where T
    inds = axes(x, 1)
    out = similar(Array{T}, inds, inds)
    for col = inds, row = inds
        out[row, col] = x[row]
    end
    return out
end

function copy_row_col(x::Vector{T}) where T
    inds = axes(x, 1)
    out = similar(Array{T}, inds, inds)
    for row = inds, col = inds
        out[row, col] = x[col]
    end
    return out
end</code></pre><p>Now we will time each of these functions using the same random <code>10000</code> by <code>1</code> input vector:</p><pre><code class="language-julia-repl hljs">julia&gt; x = randn(10000);

julia&gt; fmt(f) = println(rpad(string(f)*&quot;: &quot;, 14, &#39; &#39;), @elapsed f(x))

julia&gt; map(fmt, [copy_cols, copy_rows, copy_col_row, copy_row_col]);
copy_cols:    0.331706323
copy_rows:    1.799009911
copy_col_row: 0.415630047
copy_row_col: 1.721531501</code></pre><p>Notice that <code>copy_cols</code> is much faster than <code>copy_rows</code>. This is expected because <code>copy_cols</code> respects the column-based memory layout of the <code>Matrix</code> and fills it one column at a time. Additionally, <code>copy_col_row</code> is much faster than <code>copy_row_col</code> because it follows our rule of thumb that the first element to appear in a slice expression should be coupled with the inner-most loop.</p><h2 id="Pre-allocating-outputs"><a class="docs-heading-anchor" href="#Pre-allocating-outputs">Pre-allocating outputs</a><a id="Pre-allocating-outputs-1"></a><a class="docs-heading-anchor-permalink" href="#Pre-allocating-outputs" title="Permalink"></a></h2><p>If your function returns an <code>Array</code> or some other complex type, it may have to allocate memory. Unfortunately, oftentimes allocation and its converse, garbage collection, are substantial bottlenecks.</p><p>Sometimes you can circumvent the need to allocate memory on each function call by preallocating the output. As a trivial example, compare</p><pre><code class="language-julia-repl hljs">julia&gt; function xinc(x)
           return [x, x+1, x+2]
       end;

julia&gt; function loopinc()
           y = 0
           for i = 1:10^7
               ret = xinc(i)
               y += ret[2]
           end
           return y
       end;</code></pre><p>with</p><pre><code class="language-julia-repl hljs">julia&gt; function xinc!(ret::AbstractVector{T}, x::T) where T
           ret[1] = x
           ret[2] = x+1
           ret[3] = x+2
           nothing
       end;

julia&gt; function loopinc_prealloc()
           ret = Vector{Int}(undef, 3)
           y = 0
           for i = 1:10^7
               xinc!(ret, i)
               y += ret[2]
           end
           return y
       end;</code></pre><p>Timing results:</p><pre><code class="language-julia-repl hljs">julia&gt; @time loopinc()
  0.529894 seconds (40.00 M allocations: 1.490 GiB, 12.14% gc time)
50000015000000

julia&gt; @time loopinc_prealloc()
  0.030850 seconds (6 allocations: 288 bytes)
50000015000000</code></pre><p>Preallocation has other advantages, for example by allowing the caller to control the &quot;output&quot; type from an algorithm. In the example above, we could have passed a <code>SubArray</code> rather than an <a href="../base/arrays.html#Core.Array"><code>Array</code></a>, had we so desired.</p><p>Taken to its extreme, pre-allocation can make your code uglier, so performance measurements and some judgment may be required. However, for &quot;vectorized&quot; (element-wise) functions, the convenient syntax <code>x .= f.(y)</code> can be used for in-place operations with fused loops and no temporary arrays (see the <a href="functions.html#man-vectorized">dot syntax for vectorizing functions</a>).</p><h2 id="man-perftips-mutablearithmetics"><a class="docs-heading-anchor" href="#man-perftips-mutablearithmetics">Use <code>MutableArithmetics</code> for more control over allocation for mutable arithmetic types</a><a id="man-perftips-mutablearithmetics-1"></a><a class="docs-heading-anchor-permalink" href="#man-perftips-mutablearithmetics" title="Permalink"></a></h2><p>Some <a href="../base/numbers.html#Core.Number"><code>Number</code></a> subtypes, such as <a href="../base/numbers.html#Base.GMP.BigInt"><code>BigInt</code></a> or <a href="../base/numbers.html#Base.MPFR.BigFloat"><code>BigFloat</code></a>, may be implemented as <a href="../base/base.html#mutable struct"><code>mutable struct</code></a> types, or they may have mutable components. The arithmetic interfaces in Julia <code>Base</code> usually opt for convenience over efficiency in such cases, so using them in a naive manner may result in suboptimal performance. The abstractions of the <a href="https://juliahub.com/ui/Packages/General/MutableArithmetics"><code>MutableArithmetics</code></a> package, on the other hand, make it possible to exploit the mutability of such types for writing fast code that allocates only as much as necessary. <code>MutableArithmetics</code> also makes it possible to copy values of mutable arithmetic types explicitly when necessary. <code>MutableArithmetics</code> is a user package and is not affiliated with the Julia project.</p><h2 id="More-dots:-Fuse-vectorized-operations"><a class="docs-heading-anchor" href="#More-dots:-Fuse-vectorized-operations">More dots: Fuse vectorized operations</a><a id="More-dots:-Fuse-vectorized-operations-1"></a><a class="docs-heading-anchor-permalink" href="#More-dots:-Fuse-vectorized-operations" title="Permalink"></a></h2><p>Julia has a special <a href="functions.html#man-vectorized">dot syntax</a> that converts any scalar function into a &quot;vectorized&quot; function call, and any operator into a &quot;vectorized&quot; operator, with the special property that nested &quot;dot calls&quot; are <em>fusing</em>: they are combined at the syntax level into a single loop, without allocating temporary arrays. If you use <code>.=</code> and similar assignment operators, the result can also be stored in-place in a pre-allocated array (see above).</p><p>In a linear-algebra context, this means that even though operations like <code>vector + vector</code> and <code>vector * scalar</code> are defined, it can be advantageous to instead use <code>vector .+ vector</code> and <code>vector .* scalar</code> because the resulting loops can be fused with surrounding computations. For example, consider the two functions:</p><pre><code class="language-julia-repl hljs">julia&gt; f(x) = 3x.^2 + 4x + 7x.^3;

julia&gt; fdot(x) = @. 3x^2 + 4x + 7x^3; # equivalent to 3 .* x.^2 .+ 4 .* x .+ 7 .* x.^3</code></pre><p>Both <code>f</code> and <code>fdot</code> compute the same thing. However, <code>fdot</code> (defined with the help of the <a href="../base/arrays.html#Base.Broadcast.@__dot__"><code>@.</code></a> macro) is significantly faster when applied to an array:</p><pre><code class="language-julia-repl hljs">julia&gt; x = rand(10^6);

julia&gt; @time f(x);
  0.019049 seconds (16 allocations: 45.777 MiB, 18.59% gc time)

julia&gt; @time fdot(x);
  0.002790 seconds (6 allocations: 7.630 MiB)

julia&gt; @time f.(x);
  0.002626 seconds (8 allocations: 7.630 MiB)</code></pre><p>That is, <code>fdot(x)</code> is ten times faster and allocates 1/6 the memory of <code>f(x)</code>, because each <code>*</code> and <code>+</code> operation in <code>f(x)</code> allocates a new temporary array and executes in a separate loop. In this example <code>f.(x)</code> is as fast as <code>fdot(x)</code> but in many contexts it is more convenient to sprinkle some dots in your expressions than to define a separate function for each vectorized operation.</p><h2 id="man-performance-unfuse"><a class="docs-heading-anchor" href="#man-performance-unfuse">Fewer dots: Unfuse certain intermediate broadcasts</a><a id="man-performance-unfuse-1"></a><a class="docs-heading-anchor-permalink" href="#man-performance-unfuse" title="Permalink"></a></h2><p>The dot loop fusion mentioned above enables concise and idiomatic code to express highly performant operations. However, it is important to remember that the fused operation will be computed at every iteration of the broadcast. This means that in some situations, particularly in the presence of composed or multidimensional broadcasts, an expression with dot calls may be computing a function more times than intended. As an example, say we want to build a random matrix whose rows have Euclidean norm one. We might write something like the following:</p><pre><code class="nohighlight hljs">julia&gt; x = rand(1000, 1000);

julia&gt; d = sum(abs2, x; dims=2);

julia&gt; @time x ./= sqrt.(d);
  0.002049 seconds (4 allocations: 96 bytes)</code></pre><p>This will work. However, this expression will actually recompute <code>sqrt(d[i])</code> for <em>every</em> element in the row <code>x[i, :]</code>, meaning that many more square roots are computed than necessary. To see precisely over which indices the broadcast will iterate, we can call <code>Broadcast.combine_axes</code> on the arguments of the fused expression. This will return a tuple of ranges whose entries correspond to the axes of iteration; the product of lengths of these ranges will be the total number of calls to the fused operation.</p><p>It follows that when some components of the broadcast expression are constant along an axis—like the <code>sqrt</code> along the second dimension in the preceding example—there is potential for a performance improvement by forcibly &quot;unfusing&quot; those components, i.e. allocating the result of the broadcasted operation in advance and reusing the cached value along its constant axis. Some such potential approaches are to use temporary variables, wrap components of a dot expression in <code>identity</code>, or use an equivalent intrinsically vectorized (but non-fused) function.</p><pre><code class="nohighlight hljs">julia&gt; @time let s = sqrt.(d); x ./= s end;
  0.000809 seconds (5 allocations: 8.031 KiB)

julia&gt; @time x ./= identity(sqrt.(d));
  0.000608 seconds (5 allocations: 8.031 KiB)

julia&gt; @time x ./= map(sqrt, d);
  0.000611 seconds (4 allocations: 8.016 KiB)</code></pre><p>Any of these options yields approximately a three-fold speedup at the cost of an allocation; for large broadcastables this speedup can be asymptotically very large.</p><h2 id="man-performance-views"><a class="docs-heading-anchor" href="#man-performance-views">Consider using views for slices</a><a id="man-performance-views-1"></a><a class="docs-heading-anchor-permalink" href="#man-performance-views" title="Permalink"></a></h2><p>In Julia, an array &quot;slice&quot; expression like <code>array[1:5, :]</code> creates a copy of that data (except on the left-hand side of an assignment, where <code>array[1:5, :] = ...</code> assigns in-place to that portion of <code>array</code>). If you are doing many operations on the slice, this can be good for performance because it is more efficient to work with a smaller contiguous copy than it would be to index into the original array. On the other hand, if you are just doing a few simple operations on the slice, the cost of the allocation and copy operations can be substantial.</p><p>An alternative is to create a &quot;view&quot; of the array, which is an array object (a <code>SubArray</code>) that actually references the data of the original array in-place, without making a copy. (If you write to a view, it modifies the original array&#39;s data as well.) This can be done for individual slices by calling <a href="../base/arrays.html#Base.view"><code>view</code></a>, or more simply for a whole expression or block of code by putting <a href="../base/arrays.html#Base.@views"><code>@views</code></a> in front of that expression. For example:</p><pre><code class="language-julia-repl hljs">julia&gt; fcopy(x) = sum(x[2:end-1]);

julia&gt; @views fview(x) = sum(x[2:end-1]);

julia&gt; x = rand(10^6);

julia&gt; @time fcopy(x);
  0.003051 seconds (3 allocations: 7.629 MB)

julia&gt; @time fview(x);
  0.001020 seconds (1 allocation: 16 bytes)</code></pre><p>Notice both the 3× speedup and the decreased memory allocation of the <code>fview</code> version of the function.</p><h2 id="Copying-data-is-not-always-bad"><a class="docs-heading-anchor" href="#Copying-data-is-not-always-bad">Copying data is not always bad</a><a id="Copying-data-is-not-always-bad-1"></a><a class="docs-heading-anchor-permalink" href="#Copying-data-is-not-always-bad" title="Permalink"></a></h2><p>Arrays are stored contiguously in memory, lending themselves to CPU vectorization and fewer memory accesses due to caching. These are the same reasons that it is recommended to access arrays in column-major order (see above). Irregular access patterns and non-contiguous views can drastically slow down computations on arrays because of non-sequential memory access.</p><p>Copying irregularly-accessed data into a contiguous array before repeated access it can result in a large speedup, such as in the example below. Here, a matrix is being accessed at randomly-shuffled indices before being multiplied. Copying into plain arrays speeds up the multiplication even with the added cost of copying and allocation.</p><pre><code class="language-julia-repl hljs">julia&gt; using Random

julia&gt; A = randn(3000, 3000);

julia&gt; x = randn(2000);

julia&gt; inds = shuffle(1:3000)[1:2000];

julia&gt; function iterated_neural_network(A, x, depth)
           for _ in 1:depth
               x .= max.(0, A * x)
           end
           argmax(x)
       end

julia&gt; @time iterated_neural_network(view(A, inds, inds), x, 10)
  0.324903 seconds (12 allocations: 157.562 KiB)
1569

julia&gt; @time iterated_neural_network(A[inds, inds], x, 10)
  0.054576 seconds (13 allocations: 30.671 MiB, 13.33% gc time)
1569</code></pre><p>Provided there is enough memory, the cost of copying the view to an array is outweighed by the speed boost from doing the repeated matrix multiplications on a contiguous array.</p><h2 id="Consider-StaticArrays.jl-for-small-fixed-size-vector/matrix-operations"><a class="docs-heading-anchor" href="#Consider-StaticArrays.jl-for-small-fixed-size-vector/matrix-operations">Consider StaticArrays.jl for small fixed-size vector/matrix operations</a><a id="Consider-StaticArrays.jl-for-small-fixed-size-vector/matrix-operations-1"></a><a class="docs-heading-anchor-permalink" href="#Consider-StaticArrays.jl-for-small-fixed-size-vector/matrix-operations" title="Permalink"></a></h2><p>If your application involves many small (<code>&lt; 100</code> element) arrays of fixed sizes (i.e. the size is known prior to execution), then you might want to consider using the <a href="https://github.com/JuliaArrays/StaticArrays.jl">StaticArrays.jl package</a>. This package allows you to represent such arrays in a way that avoids unnecessary heap allocations and allows the compiler to specialize code for the <em>size</em> of the array, e.g. by completely unrolling vector operations (eliminating the loops) and storing elements in CPU registers.</p><p>For example, if you are doing computations with 2d geometries, you might have many computations with 2-component vectors.  By using the <code>SVector</code> type from StaticArrays.jl, you can use convenient vector notation and operations like <code>norm(3v - w)</code> on vectors <code>v</code> and <code>w</code>, while allowing the compiler to unroll the code to a minimal computation equivalent to <code>@inbounds hypot(3v[1]-w[1], 3v[2]-w[2])</code>.</p><h2 id="Avoid-string-interpolation-for-I/O"><a class="docs-heading-anchor" href="#Avoid-string-interpolation-for-I/O">Avoid string interpolation for I/O</a><a id="Avoid-string-interpolation-for-I/O-1"></a><a class="docs-heading-anchor-permalink" href="#Avoid-string-interpolation-for-I/O" title="Permalink"></a></h2><p>When writing data to a file (or other I/O device), forming extra intermediate strings is a source of overhead. Instead of:</p><pre><code class="language-julia hljs">println(file, &quot;$a $b&quot;)</code></pre><p>use:</p><pre><code class="language-julia hljs">println(file, a, &quot; &quot;, b)</code></pre><p>The first version of the code forms a string, then writes it to the file, while the second version writes values directly to the file. Also notice that in some cases string interpolation can be harder to read. Consider:</p><pre><code class="language-julia hljs">println(file, &quot;$(f(a))$(f(b))&quot;)</code></pre><p>versus:</p><pre><code class="language-julia hljs">println(file, f(a), f(b))</code></pre><h2 id="Optimize-network-I/O-during-parallel-execution"><a class="docs-heading-anchor" href="#Optimize-network-I/O-during-parallel-execution">Optimize network I/O during parallel execution</a><a id="Optimize-network-I/O-during-parallel-execution-1"></a><a class="docs-heading-anchor-permalink" href="#Optimize-network-I/O-during-parallel-execution" title="Permalink"></a></h2><p>When executing a remote function in parallel:</p><pre><code class="language-julia hljs">using Distributed

responses = Vector{Any}(undef, nworkers())
@sync begin
    for (idx, pid) in enumerate(workers())
        @async responses[idx] = remotecall_fetch(foo, pid, args...)
    end
end</code></pre><p>is faster than:</p><pre><code class="language-julia hljs">using Distributed

refs = Vector{Any}(undef, nworkers())
for (idx, pid) in enumerate(workers())
    refs[idx] = @spawnat pid foo(args...)
end
responses = [fetch(r) for r in refs]</code></pre><p>The former results in a single network round-trip to every worker, while the latter results in two network calls - first by the <a href="../stdlib/Distributed.html#Distributed.@spawnat"><code>@spawnat</code></a> and the second due to the <a href="../base/parallel.html#Base.fetch-Tuple{Task}"><code>fetch</code></a> (or even a <a href="../base/parallel.html#Base.wait"><code>wait</code></a>). The <a href="../base/parallel.html#Base.fetch-Tuple{Task}"><code>fetch</code></a>/<a href="../base/parallel.html#Base.wait"><code>wait</code></a> is also being executed serially resulting in an overall poorer performance.</p><h2 id="Fix-deprecation-warnings"><a class="docs-heading-anchor" href="#Fix-deprecation-warnings">Fix deprecation warnings</a><a id="Fix-deprecation-warnings-1"></a><a class="docs-heading-anchor-permalink" href="#Fix-deprecation-warnings" title="Permalink"></a></h2><p>A deprecated function internally performs a lookup in order to print a relevant warning only once. This extra lookup can cause a significant slowdown, so all uses of deprecated functions should be modified as suggested by the warnings.</p><h2 id="Tweaks"><a class="docs-heading-anchor" href="#Tweaks">Tweaks</a><a id="Tweaks-1"></a><a class="docs-heading-anchor-permalink" href="#Tweaks" title="Permalink"></a></h2><p>These are some minor points that might help in tight inner loops.</p><ul><li>Avoid unnecessary arrays. For example, instead of <a href="../base/collections.html#Base.sum"><code>sum([x,y,z])</code></a> use <code>x+y+z</code>.</li><li>Use <a href="../base/math.html#Base.abs2"><code>abs2(z)</code></a> instead of <a href="../base/math.html#Base.:^-Tuple{Number, Number}"><code>abs(z)^2</code></a> for complex <code>z</code>. In general, try to rewrite code to use <a href="../base/math.html#Base.abs2"><code>abs2</code></a> instead of <a href="../base/math.html#Base.abs"><code>abs</code></a> for complex arguments.</li><li>Use <a href="../base/math.html#Base.div"><code>div(x,y)</code></a> for truncating division of integers instead of <a href="../base/math.html#Base.trunc"><code>trunc(x/y)</code></a>, <a href="../base/math.html#Base.fld"><code>fld(x,y)</code></a> instead of <a href="../base/math.html#Base.floor"><code>floor(x/y)</code></a>, and <a href="../base/math.html#Base.cld"><code>cld(x,y)</code></a> instead of <a href="../base/math.html#Base.ceil"><code>ceil(x/y)</code></a>.</li></ul><h2 id="man-performance-annotations"><a class="docs-heading-anchor" href="#man-performance-annotations">Performance Annotations</a><a id="man-performance-annotations-1"></a><a class="docs-heading-anchor-permalink" href="#man-performance-annotations" title="Permalink"></a></h2><p>Sometimes you can enable better optimization by promising certain program properties.</p><ul><li>Use <a href="../base/base.html#Base.@inbounds"><code>@inbounds</code></a> to eliminate array bounds checking within expressions. Be certain before doing this. If the subscripts are ever out of bounds, you may suffer crashes or silent corruption.</li><li>Use <a href="../base/math.html#Base.FastMath.@fastmath"><code>@fastmath</code></a> to allow floating point optimizations that are correct for real numbers, but lead to differences for IEEE numbers. Be careful when doing this, as this may change numerical results. This corresponds to the <code>-ffast-math</code> option of clang.</li><li>Write <a href="../base/base.html#Base.SimdLoop.@simd"><code>@simd</code></a> in front of <code>for</code> loops to promise that the iterations are independent and may be reordered.  Note that in many cases, Julia can automatically vectorize code without the <code>@simd</code> macro; it is only beneficial in cases where such a transformation would otherwise be illegal, including cases like allowing floating-point re-associativity and ignoring dependent memory accesses (<code>@simd ivdep</code>). Again, be very careful when asserting <code>@simd</code> as erroneously annotating a loop with dependent iterations may result in unexpected results. In particular, note that <code>setindex!</code> on some <code>AbstractArray</code> subtypes is inherently dependent upon iteration order. <strong>This feature is experimental</strong> and could change or disappear in future versions of Julia.</li></ul><p>The common idiom of using 1:n to index into an AbstractArray is not safe if the Array uses unconventional indexing, and may cause a segmentation fault if bounds checking is turned off. Use <code>LinearIndices(x)</code> or <code>eachindex(x)</code> instead (see also <a href="../devdocs/offset-arrays.html#man-custom-indices">Arrays with custom indices</a>).</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>While <code>@simd</code> needs to be placed directly in front of an innermost <code>for</code> loop, both <code>@inbounds</code> and <code>@fastmath</code> can be applied to either single expressions or all the expressions that appear within nested blocks of code, e.g., using <code>@inbounds begin</code> or <code>@inbounds for ...</code>.</p></div></div><p>Here is an example with both <code>@inbounds</code> and <code>@simd</code> markup (we here use <code>@noinline</code> to prevent the optimizer from trying to be too clever and defeat our benchmark):</p><pre><code class="language-julia hljs">@noinline function inner(x, y)
    s = zero(eltype(x))
    for i=eachindex(x)
        @inbounds s += x[i]*y[i]
    end
    return s
end

@noinline function innersimd(x, y)
    s = zero(eltype(x))
    @simd for i = eachindex(x)
        @inbounds s += x[i] * y[i]
    end
    return s
end

function timeit(n, reps)
    x = rand(Float32, n)
    y = rand(Float32, n)
    s = zero(Float64)
    time = @elapsed for j in 1:reps
        s += inner(x, y)
    end
    println(&quot;GFlop/sec        = &quot;, 2n*reps / time*1E-9)
    time = @elapsed for j in 1:reps
        s += innersimd(x, y)
    end
    println(&quot;GFlop/sec (SIMD) = &quot;, 2n*reps / time*1E-9)
end

timeit(1000, 1000)</code></pre><p>On a computer with a 2.4GHz Intel Core i5 processor, this produces:</p><pre><code class="nohighlight hljs">GFlop/sec        = 1.9467069505224963
GFlop/sec (SIMD) = 17.578554163920018</code></pre><p>(<code>GFlop/sec</code> measures the performance, and larger numbers are better.)</p><p>Here is an example with all three kinds of markup. This program first calculates the finite difference of a one-dimensional array, and then evaluates the L2-norm of the result:</p><pre><code class="language-julia hljs">function init!(u::Vector)
    n = length(u)
    dx = 1.0 / (n-1)
    @fastmath @inbounds @simd for i in 1:n #by asserting that `u` is a `Vector` we can assume it has 1-based indexing
        u[i] = sin(2pi*dx*i)
    end
end

function deriv!(u::Vector, du)
    n = length(u)
    dx = 1.0 / (n-1)
    @fastmath @inbounds du[1] = (u[2] - u[1]) / dx
    @fastmath @inbounds @simd for i in 2:n-1
        du[i] = (u[i+1] - u[i-1]) / (2*dx)
    end
    @fastmath @inbounds du[n] = (u[n] - u[n-1]) / dx
end

function mynorm(u::Vector)
    n = length(u)
    T = eltype(u)
    s = zero(T)
    @fastmath @inbounds @simd for i in 1:n
        s += u[i]^2
    end
    @fastmath @inbounds return sqrt(s)
end

function main()
    n = 2000
    u = Vector{Float64}(undef, n)
    init!(u)
    du = similar(u)

    deriv!(u, du)
    nu = mynorm(du)

    @time for i in 1:10^6
        deriv!(u, du)
        nu = mynorm(du)
    end

    println(nu)
end

main()</code></pre><p>On a computer with a 2.7 GHz Intel Core i7 processor, this produces:</p><pre><code class="nohighlight hljs">$ julia wave.jl;
  1.207814709 seconds
4.443986180758249

$ julia --math-mode=ieee wave.jl;
  4.487083643 seconds
4.443986180758249</code></pre><p>Here, the option <code>--math-mode=ieee</code> disables the <code>@fastmath</code> macro, so that we can compare results.</p><p>In this case, the speedup due to <code>@fastmath</code> is a factor of about 3.7. This is unusually large – in general, the speedup will be smaller. (In this particular example, the working set of the benchmark is small enough to fit into the L1 cache of the processor, so that memory access latency does not play a role, and computing time is dominated by CPU usage. In many real world programs this is not the case.) Also, in this case this optimization does not change the result – in general, the result will be slightly different. In some cases, especially for numerically unstable algorithms, the result can be very different.</p><p>The annotation <code>@fastmath</code> re-arranges floating point expressions, e.g. changing the order of evaluation, or assuming that certain special cases (inf, nan) cannot occur. In this case (and on this particular computer), the main difference is that the expression <code>1 / (2*dx)</code> in the function <code>deriv</code> is hoisted out of the loop (i.e. calculated outside the loop), as if one had written <code>idx = 1 / (2*dx)</code>. In the loop, the expression <code>... / (2*dx)</code> then becomes <code>... * idx</code>, which is much faster to evaluate. Of course, both the actual optimization that is applied by the compiler as well as the resulting speedup depend very much on the hardware. You can examine the change in generated code by using Julia&#39;s <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.code_native"><code>code_native</code></a> function.</p><p>Note that <code>@fastmath</code> also assumes that <code>NaN</code>s will not occur during the computation, which can lead to surprising behavior:</p><pre><code class="language-julia-repl hljs">julia&gt; f(x) = isnan(x);

julia&gt; f(NaN)
true

julia&gt; f_fast(x) = @fastmath isnan(x);

julia&gt; f_fast(NaN)
false</code></pre><h2 id="Treat-Subnormal-Numbers-as-Zeros"><a class="docs-heading-anchor" href="#Treat-Subnormal-Numbers-as-Zeros">Treat Subnormal Numbers as Zeros</a><a id="Treat-Subnormal-Numbers-as-Zeros-1"></a><a class="docs-heading-anchor-permalink" href="#Treat-Subnormal-Numbers-as-Zeros" title="Permalink"></a></h2><p>Subnormal numbers, formerly called <a href="https://en.wikipedia.org/wiki/Denormal_number">denormal numbers</a>, are useful in many contexts, but incur a performance penalty on some hardware. A call <a href="../base/numbers.html#Base.Rounding.set_zero_subnormals"><code>set_zero_subnormals(true)</code></a> grants permission for floating-point operations to treat subnormal inputs or outputs as zeros, which may improve performance on some hardware. A call <a href="../base/numbers.html#Base.Rounding.set_zero_subnormals"><code>set_zero_subnormals(false)</code></a> enforces strict IEEE behavior for subnormal numbers.</p><p>Below is an example where subnormals noticeably impact performance on some hardware:</p><pre><code class="language-julia hljs">function timestep(b::Vector{T}, a::Vector{T}, Δt::T) where T
    @assert length(a)==length(b)
    n = length(b)
    b[1] = 1                            # Boundary condition
    for i=2:n-1
        b[i] = a[i] + (a[i-1] - T(2)*a[i] + a[i+1]) * Δt
    end
    b[n] = 0                            # Boundary condition
end

function heatflow(a::Vector{T}, nstep::Integer) where T
    b = similar(a)
    for t=1:div(nstep,2)                # Assume nstep is even
        timestep(b,a,T(0.1))
        timestep(a,b,T(0.1))
    end
end

heatflow(zeros(Float32,10),2)           # Force compilation
for trial=1:6
    a = zeros(Float32,1000)
    set_zero_subnormals(iseven(trial))  # Odd trials use strict IEEE arithmetic
    @time heatflow(a,1000)
end</code></pre><p>This gives an output similar to</p><pre><code class="nohighlight hljs">  0.002202 seconds (1 allocation: 4.063 KiB)
  0.001502 seconds (1 allocation: 4.063 KiB)
  0.002139 seconds (1 allocation: 4.063 KiB)
  0.001454 seconds (1 allocation: 4.063 KiB)
  0.002115 seconds (1 allocation: 4.063 KiB)
  0.001455 seconds (1 allocation: 4.063 KiB)</code></pre><p>Note how each even iteration is significantly faster.</p><p>This example generates many subnormal numbers because the values in <code>a</code> become an exponentially decreasing curve, which slowly flattens out over time.</p><p>Treating subnormals as zeros should be used with caution, because doing so breaks some identities, such as <code>x-y == 0</code> implies <code>x == y</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; x = 3f-38; y = 2f-38;

julia&gt; set_zero_subnormals(true); (x - y, x == y)
(0.0f0, false)

julia&gt; set_zero_subnormals(false); (x - y, x == y)
(1.0000001f-38, false)</code></pre><p>In some applications, an alternative to zeroing subnormal numbers is to inject a tiny bit of noise.  For example, instead of initializing <code>a</code> with zeros, initialize it with:</p><pre><code class="language-julia hljs">a = rand(Float32,1000) * 1.f-9</code></pre><h2 id="man-code-warntype"><a class="docs-heading-anchor" href="#man-code-warntype"><a href="../stdlib/InteractiveUtils.html#InteractiveUtils.@code_warntype"><code>@code_warntype</code></a></a><a id="man-code-warntype-1"></a><a class="docs-heading-anchor-permalink" href="#man-code-warntype" title="Permalink"></a></h2><p>The macro <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.@code_warntype"><code>@code_warntype</code></a> (or its function variant <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.code_warntype"><code>code_warntype</code></a>) can sometimes be helpful in diagnosing type-related problems. Here&#39;s an example:</p><pre><code class="language-julia-repl hljs">julia&gt; @noinline pos(x) = x &lt; 0 ? 0 : x;

julia&gt; function f(x)
           y = pos(x)
           return sin(y*x + 1)
       end;

julia&gt; @code_warntype f(3.2)
MethodInstance for f(::Float64)
  from f(x) @ Main REPL[9]:1
Arguments
  #self#::Core.Const(f)
  x::Float64
Locals
  y::Union{Float64, Int64}
Body::Float64
1 ─      (y = Main.pos(x))
│   %2 = (y * x)::Float64
│   %3 = (%2 + 1)::Float64
│   %4 = Main.sin(%3)::Float64
└──      return %4</code></pre><p>Interpreting the output of <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.@code_warntype"><code>@code_warntype</code></a>, like that of its cousins <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.@code_lowered"><code>@code_lowered</code></a>, <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.@code_typed"><code>@code_typed</code></a>, <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.@code_llvm"><code>@code_llvm</code></a>, and <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.@code_native"><code>@code_native</code></a>, takes a little practice. Your code is being presented in form that has been heavily digested on its way to generating compiled machine code. Most of the expressions are annotated by a type, indicated by the <code>::T</code> (where <code>T</code> might be <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a>, for example). The most important characteristic of <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.@code_warntype"><code>@code_warntype</code></a> is that non-concrete types are displayed in red; since this document is written in Markdown, which has no color, in this document, red text is denoted by uppercase.</p><p>At the top, the inferred return type of the function is shown as <code>Body::Float64</code>. The next lines represent the body of <code>f</code> in Julia&#39;s SSA IR form. The numbered boxes are labels and represent targets for jumps (via <code>goto</code>) in your code. Looking at the body, you can see that the first thing that happens is that <code>pos</code> is called and the return value has been inferred as the <code>Union</code> type <code>Union{Float64, Int64}</code> shown in uppercase since it is a non-concrete type. This means that we cannot know the exact return type of <code>pos</code> based on the input types. However, the result of <code>y*x</code>is a <code>Float64</code> no matter if <code>y</code> is a <code>Float64</code> or <code>Int64</code> The net result is that <code>f(x::Float64)</code> will not be type-unstable in its output, even if some of the intermediate computations are type-unstable.</p><p>How you use this information is up to you. Obviously, it would be far and away best to fix <code>pos</code> to be type-stable: if you did so, all of the variables in <code>f</code> would be concrete, and its performance would be optimal. However, there are circumstances where this kind of <em>ephemeral</em> type instability might not matter too much: for example, if <code>pos</code> is never used in isolation, the fact that <code>f</code>&#39;s output is type-stable (for <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> inputs) will shield later code from the propagating effects of type instability. This is particularly relevant in cases where fixing the type instability is difficult or impossible. In such cases, the tips above (e.g., adding type annotations and/or breaking up functions) are your best tools to contain the &quot;damage&quot; from type instability. Also, note that even Julia Base has functions that are type unstable. For example, the function <a href="../base/arrays.html#Base.findfirst-Tuple{Any}"><code>findfirst</code></a> returns the index into an array where a key is found, or <code>nothing</code> if it is not found, a clear type instability. In order to make it easier to find the type instabilities that are likely to be important, <code>Union</code>s containing either <code>missing</code> or <code>nothing</code> are color highlighted in yellow, instead of red.</p><p>The following examples may help you interpret expressions marked as containing non-leaf types:</p><ul><li><p>Function body starting with <code>Body::Union{T1,T2})</code></p><ul><li>Interpretation: function with unstable return type</li><li>Suggestion: make the return value type-stable, even if you have to annotate it</li></ul></li><li><p><code>invoke Main.g(%%x::Int64)::Union{Float64, Int64}</code></p><ul><li>Interpretation: call to a type-unstable function <code>g</code>.</li><li>Suggestion: fix the function, or if necessary annotate the return value</li></ul></li><li><p><code>invoke Base.getindex(%%x::Array{Any,1}, 1::Int64)::Any</code></p><ul><li>Interpretation: accessing elements of poorly-typed arrays</li><li>Suggestion: use arrays with better-defined types, or if necessary annotate the type of individual element accesses</li></ul></li><li><p><code>Base.getfield(%%x, :(:data))::Array{Float64,N} where N</code></p><ul><li>Interpretation: getting a field that is of non-leaf type. In this case, the type of <code>x</code>, say <code>ArrayContainer</code>, had a field <code>data::Array{T}</code>. But <code>Array</code> needs the dimension <code>N</code>, too, to be a concrete type.</li><li>Suggestion: use concrete types like <code>Array{T,3}</code> or <code>Array{T,N}</code>, where <code>N</code> is now a parameter of <code>ArrayContainer</code></li></ul></li></ul><h2 id="man-performance-captured"><a class="docs-heading-anchor" href="#man-performance-captured">Performance of captured variable</a><a id="man-performance-captured-1"></a><a class="docs-heading-anchor-permalink" href="#man-performance-captured" title="Permalink"></a></h2><p>Consider the following example that defines an inner function:</p><pre><code class="language-julia hljs">function abmult(r::Int)
    if r &lt; 0
        r = -r
    end
    f = x -&gt; x * r
    return f
end</code></pre><p>Function <code>abmult</code> returns a function <code>f</code> that multiplies its argument by the absolute value of <code>r</code>. The inner function assigned to <code>f</code> is called a &quot;closure&quot;. Inner functions are also used by the language for <code>do</code>-blocks and for generator expressions.</p><p>This style of code presents performance challenges for the language. The parser, when translating it into lower-level instructions, substantially reorganizes the above code by extracting the inner function to a separate code block.  &quot;Captured&quot; variables such as <code>r</code> that are shared by inner functions and their enclosing scope are also extracted into a heap-allocated &quot;box&quot; accessible to both inner and outer functions because the language specifies that <code>r</code> in the inner scope must be identical to <code>r</code> in the outer scope even after the outer scope (or another inner function) modifies <code>r</code>.</p><p>The discussion in the preceding paragraph referred to the &quot;parser&quot;, that is, the phase of compilation that takes place when the module containing <code>abmult</code> is first loaded, as opposed to the later phase when it is first invoked. The parser does not &quot;know&quot; that <code>Int</code> is a fixed type, or that the statement <code>r = -r</code> transforms an <code>Int</code> to another <code>Int</code>. The magic of type inference takes place in the later phase of compilation.</p><p>Thus, the parser does not know that <code>r</code> has a fixed type (<code>Int</code>). nor that <code>r</code> does not change value once the inner function is created (so that the box is unneeded).  Therefore, the parser emits code for box that holds an object with an abstract type such as <code>Any</code>, which requires run-time type dispatch for each occurrence of <code>r</code>.  This can be verified by applying <code>@code_warntype</code> to the above function.  Both the boxing and the run-time type dispatch can cause loss of performance.</p><p>If captured variables are used in a performance-critical section of the code, then the following tips help ensure that their use is performant. First, if it is known that a captured variable does not change its type, then this can be declared explicitly with a type annotation (on the variable, not the right-hand side):</p><pre><code class="language-julia hljs">function abmult2(r0::Int)
    r::Int = r0
    if r &lt; 0
        r = -r
    end
    f = x -&gt; x * r
    return f
end</code></pre><p>The type annotation partially recovers lost performance due to capturing because the parser can associate a concrete type to the object in the box. Going further, if the captured variable does not need to be boxed at all (because it will not be reassigned after the closure is created), this can be indicated with <code>let</code> blocks as follows.</p><pre><code class="language-julia hljs">function abmult3(r::Int)
    if r &lt; 0
        r = -r
    end
    f = let r = r
            x -&gt; x * r
    end
    return f
end</code></pre><p>The <code>let</code> block creates a new variable <code>r</code> whose scope is only the inner function. The second technique recovers full language performance in the presence of captured variables. Note that this is a rapidly evolving aspect of the compiler, and it is likely that future releases will not require this degree of programmer annotation to attain performance. In the mean time, some user-contributed packages like <a href="https://github.com/c42f/FastClosures.jl">FastClosures</a> automate the insertion of <code>let</code> statements as in <code>abmult3</code>.</p><h2 id="man-multithreading-linear-algebra"><a class="docs-heading-anchor" href="#man-multithreading-linear-algebra">Multithreading and linear algebra</a><a id="man-multithreading-linear-algebra-1"></a><a class="docs-heading-anchor-permalink" href="#man-multithreading-linear-algebra" title="Permalink"></a></h2><p>This section applies to multithreaded Julia code which, in each thread, performs linear algebra operations. Indeed, these linear algebra operations involve BLAS / LAPACK calls, which are themselves multithreaded. In this case, one must ensure that cores aren&#39;t oversubscribed due to the two different types of multithreading.</p><p>Julia compiles and uses its own copy of OpenBLAS for linear algebra, whose number of threads is controlled by the environment variable <code>OPENBLAS_NUM_THREADS</code>. It can either be set as a command line option when launching Julia, or modified during the Julia session with <code>BLAS.set_num_threads(N)</code> (the submodule <code>BLAS</code> is exported by <code>using LinearAlgebra</code>). Its current value can be accessed with <code>BLAS.get_num_threads()</code>.</p><p>When the user does not specify anything, Julia tries to choose a reasonable value for the number of OpenBLAS threads (e.g. based on the platform, the Julia version, etc.). However, it is generally recommended to check and set the value manually. The OpenBLAS behavior is as follows:</p><ul><li>If <code>OPENBLAS_NUM_THREADS=1</code>, OpenBLAS uses the calling Julia thread(s), i.e. it &quot;lives in&quot; the Julia thread that runs the computation.</li><li>If <code>OPENBLAS_NUM_THREADS=N&gt;1</code>, OpenBLAS creates and manages its own pool of threads (<code>N</code> in total). There is just one OpenBLAS thread pool shared among all Julia threads.</li></ul><p>When you start Julia in multithreaded mode with <code>JULIA_NUM_THREADS=X</code>, it is generally recommended to set <code>OPENBLAS_NUM_THREADS=1</code>. Given the behavior described above, increasing the number of BLAS threads to <code>N&gt;1</code> can very easily lead to worse performance, in particular when <code>N&lt;&lt;X</code>. However this is just a rule of thumb, and the best way to set each number of threads is to experiment on your specific application.</p><h2 id="man-backends-linear-algebra"><a class="docs-heading-anchor" href="#man-backends-linear-algebra">Alternative linear algebra backends</a><a id="man-backends-linear-algebra-1"></a><a class="docs-heading-anchor-permalink" href="#man-backends-linear-algebra" title="Permalink"></a></h2><p>As an alternative to OpenBLAS, there exist several other backends that can help with linear algebra performance. Prominent examples include <a href="https://github.com/JuliaLinearAlgebra/MKL.jl">MKL.jl</a> and <a href="https://github.com/JuliaMath/AppleAccelerate.jl">AppleAccelerate.jl</a>.</p><p>These are external packages, so we will not discuss them in detail here. Please refer to their respective documentations (especially because they have different behaviors than OpenBLAS with respect to multithreading).</p><h2 id="Execution-latency,-package-loading-and-package-precompiling-time"><a class="docs-heading-anchor" href="#Execution-latency,-package-loading-and-package-precompiling-time">Execution latency, package loading and package precompiling time</a><a id="Execution-latency,-package-loading-and-package-precompiling-time-1"></a><a class="docs-heading-anchor-permalink" href="#Execution-latency,-package-loading-and-package-precompiling-time" title="Permalink"></a></h2><h3 id="Reducing-time-to-first-plot-etc."><a class="docs-heading-anchor" href="#Reducing-time-to-first-plot-etc.">Reducing time to first plot etc.</a><a id="Reducing-time-to-first-plot-etc.-1"></a><a class="docs-heading-anchor-permalink" href="#Reducing-time-to-first-plot-etc." title="Permalink"></a></h3><p>The first time a julia method is called it (and any methods it calls, or ones that can be statically determined) will be compiled. The <a href="profile.html#@time"><code>@time</code></a> macro family illustrates this.</p><pre><code class="nohighlight hljs">julia&gt; foo() = rand(2,2) * rand(2,2)
foo (generic function with 1 method)

julia&gt; @time @eval foo();
  0.252395 seconds (1.12 M allocations: 56.178 MiB, 2.93% gc time, 98.12% compilation time)

julia&gt; @time @eval foo();
  0.000156 seconds (63 allocations: 2.453 KiB)</code></pre><p>Note that <code>@time @eval</code> is better for measuring compilation time because without <a href="../base/base.html#Base.@eval"><code>@eval</code></a>, some compilation may already be done before timing starts.</p><p>When developing a package, you may be able to improve the experience of your users with <em>precompilation</em> so that when they use the package, the code they use is already compiled. To precompile package code effectively, it&#39;s recommended to use <a href="https://julialang.github.io/PrecompileTools.jl/stable/"><code>PrecompileTools.jl</code></a> to run a &quot;precompile workload&quot; during precompilation time that is representative of typical package usage, which will cache the native compiled code into the package <code>pkgimage</code> cache, greatly reducing &quot;time to first execution&quot; (often referred to as TTFX) for such usage.</p><p>Note that <a href="https://julialang.github.io/PrecompileTools.jl/stable/"><code>PrecompileTools.jl</code></a> workloads can be disabled and sometimes configured via Preferences if you do not want to spend the extra time precompiling, which may be the case during development of a package.</p><h3 id="Reducing-package-loading-time"><a class="docs-heading-anchor" href="#Reducing-package-loading-time">Reducing package loading time</a><a id="Reducing-package-loading-time-1"></a><a class="docs-heading-anchor-permalink" href="#Reducing-package-loading-time" title="Permalink"></a></h3><p>Keeping the time taken to load the package down is usually helpful. General good practice for package developers includes:</p><ol><li>Reduce your dependencies to those you really need. Consider using <a href="../base/math.html#Base.:--Tuple{Any, Any}">package extensions</a> to support interoperability with other packages without bloating your essential dependencies.</li><li>Avoid use of <a href="../base/base.html#__init__"><code>__init__()</code></a> functions unless there is no alternative, especially those which might trigger a lot of compilation, or just take a long time to execute.</li><li>Where possible, fix <a href="https://julialang.org/blog/2020/08/invalidations/">invalidations</a> among your dependencies and from your package code.</li></ol><p>The tool <a href="../stdlib/InteractiveUtils.html#Base.@time_imports"><code>@time_imports</code></a> can be useful in the REPL to review the above factors.</p><pre><code class="language-julia-repl hljs">julia&gt; @time @time_imports using Plots
      0.5 ms  Printf
     16.4 ms  Dates
      0.7 ms  Statistics
               ┌ 23.8 ms SuiteSparse_jll.__init__() 86.11% compilation time (100% recompilation)
     90.1 ms  SuiteSparse_jll 91.57% compilation time (82% recompilation)
      0.9 ms  Serialization
               ┌ 39.8 ms SparseArrays.CHOLMOD.__init__() 99.47% compilation time (100% recompilation)
    166.9 ms  SparseArrays 23.74% compilation time (100% recompilation)
      0.4 ms  Statistics → SparseArraysExt
      0.5 ms  TOML
      8.0 ms  Preferences
      0.3 ms  PrecompileTools
      0.2 ms  Reexport
... many deps omitted for example ...
      1.4 ms  Tar
               ┌ 73.8 ms p7zip_jll.__init__() 99.93% compilation time (100% recompilation)
     79.4 ms  p7zip_jll 92.91% compilation time (100% recompilation)
               ┌ 27.7 ms GR.GRPreferences.__init__() 99.77% compilation time (100% recompilation)
     43.0 ms  GR 64.26% compilation time (100% recompilation)
               ┌ 2.1 ms Plots.__init__() 91.80% compilation time (100% recompilation)
    300.9 ms  Plots 0.65% compilation time (100% recompilation)
  1.795602 seconds (3.33 M allocations: 190.153 MiB, 7.91% gc time, 39.45% compilation time: 97% of which was recompilation)
</code></pre><p>Notice that in this example there are multiple packages loaded, some with <code>__init__()</code> functions, some of which cause compilation of which some is recompilation. Recompilation is caused by earlier packages invalidating methods, then in these cases when the following packages run their <code>__init__()</code> function some hit recompilation before the code can be run.</p><p>Further, note the <code>Statistics</code> extension <code>SparseArraysExt</code> has been activated because <code>SparseArrays</code> is in the dependency tree. i.e. see <code>0.4 ms  Statistics → SparseArraysExt</code>.</p><p>This report gives a good opportunity to review whether the cost of dependency load time is worth the functionality it brings. Also the <code>Pkg</code> utility <code>why</code> can be used to report why a an indirect dependency exists.</p><pre><code class="nohighlight hljs">(CustomPackage) pkg&gt; why FFMPEG_jll
  Plots → FFMPEG → FFMPEG_jll
  Plots → GR → GR_jll → FFMPEG_jll</code></pre><p>or to see the indirect dependencies that a package brings in, you can <code>pkg&gt; rm</code> the package, see the deps that are removed from the manifest, then revert the change with <code>pkg&gt; undo</code>.</p><p>If loading time is dominated by slow <code>__init__()</code> methods having compilation, one verbose way to identify what is being compiled is to use the julia args <code>--trace-compile=stderr</code> which will report a <a href="../base/base.html#Base.precompile"><code>precompile</code></a> statement each time a method is compiled. For instance, the full setup would be:</p><pre><code class="nohighlight hljs">$ julia --startup-file=no --trace-compile=stderr
julia&gt; @time @time_imports using CustomPackage
...</code></pre><p>Note the <code>--startup-file=no</code> which helps isolate the test from packages you may have in your <code>startup.jl</code>.</p><p>More analysis of the reasons for recompilation can be achieved with the <a href="https://github.com/timholy/SnoopCompile.jl"><code>SnoopCompile</code></a> package.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="stacktraces.html">« Stack Traces</a><a class="docs-footer-nextpage" href="workflow-tips.html">Workflow Tips »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
