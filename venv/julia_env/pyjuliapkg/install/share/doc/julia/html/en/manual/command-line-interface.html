<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Command-line Interface · The Julia Language</title><meta name="title" content="Command-line Interface · The Julia Language"/><meta property="og:title" content="Command-line Interface · The Julia Language"/><meta property="twitter:title" content="Command-line Interface · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li class="is-active"><a class="tocitem" href="command-line-interface.html">Command-line Interface</a><ul class="internal"><li><a class="tocitem" href="#Using-arguments-inside-scripts"><span>Using arguments inside scripts</span></a></li><li><a class="tocitem" href="#The-Main.main-entry-point"><span>The <code>Main.main</code> entry point</span></a></li><li><a class="tocitem" href="#Parallel-mode"><span>Parallel mode</span></a></li><li><a class="tocitem" href="#Startup-file"><span>Startup file</span></a></li><li><a class="tocitem" href="#command-line-interface"><span>Command-line switches for Julia</span></a></li></ul></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="command-line-interface.html">Command-line Interface</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="command-line-interface.html">Command-line Interface</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/command-line-interface.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="cli"><a class="docs-heading-anchor" href="#cli">Command-line Interface</a><a id="cli-1"></a><a class="docs-heading-anchor-permalink" href="#cli" title="Permalink"></a></h1><h2 id="Using-arguments-inside-scripts"><a class="docs-heading-anchor" href="#Using-arguments-inside-scripts">Using arguments inside scripts</a><a id="Using-arguments-inside-scripts-1"></a><a class="docs-heading-anchor-permalink" href="#Using-arguments-inside-scripts" title="Permalink"></a></h2><p>When running a script using <code>julia</code>, you can pass additional arguments to your script:</p><pre><code class="nohighlight hljs">$ julia script.jl arg1 arg2...</code></pre><p>These additional command-line arguments are passed in the global constant <code>ARGS</code>. The name of the script itself is passed in as the global <code>PROGRAM_FILE</code>. Note that <code>ARGS</code> is also set when a Julia expression is given using the <code>-e</code> option on the command line (see the <code>julia</code> help output below) but <code>PROGRAM_FILE</code> will be empty. For example, to just print the arguments given to a script, you could do this:</p><pre><code class="nohighlight hljs">$ julia -e &#39;println(PROGRAM_FILE); for x in ARGS; println(x); end&#39; foo bar

foo
bar</code></pre><p>Or you could put that code into a script and run it:</p><pre><code class="nohighlight hljs">$ echo &#39;println(PROGRAM_FILE); for x in ARGS; println(x); end&#39; &gt; script.jl
$ julia script.jl foo bar
script.jl
foo
bar</code></pre><p>The <code>--</code> delimiter can be used to separate command-line arguments intended for the script file from arguments intended for Julia:</p><pre><code class="nohighlight hljs">$ julia --color=yes -O -- script.jl arg1 arg2..</code></pre><p>See also <a href="faq.html#man-scripting">Scripting</a> for more information on writing Julia scripts.</p><h2 id="The-Main.main-entry-point"><a class="docs-heading-anchor" href="#The-Main.main-entry-point">The <code>Main.main</code> entry point</a><a id="The-Main.main-entry-point-1"></a><a class="docs-heading-anchor-permalink" href="#The-Main.main-entry-point" title="Permalink"></a></h2><p>As of Julia, 1.11, <code>Base</code> exports the macro <code>@main</code>. This macro expands to the symbol <code>main</code>, but at the conclusion of executing a script or expression, <code>julia</code> will attempt to execute the function <code>Main.main(ARGS)</code> if such a function has been defined and this behavior was opted into by using the <code>@main</code> macro.</p><p>This feature is intended to aid in the unification of compiled and interactive workflows. In compiled workflows, loading the code that defines the <code>main</code> function may be spatially and temporally separated from the invocation. However, for interactive workflows, the behavior is equivalent to explicitly calling <code>exit(main(ARGS))</code> at the end of the evaluated script or expression.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>The special entry point <code>Main.main</code> was added in Julia 1.11. For compatibility with prior julia versions, add an explicit <code>@isdefined(var&quot;@main&quot;) ? (@main) : exit(main(ARGS))</code> at the end of your scripts.</p></div></div><p>To see this feature in action, consider the following definition, which will execute the print function despite there being no explicit call to <code>main</code>:</p><pre><code class="nohighlight hljs">$ julia -e &#39;(@main)(args) = println(&quot;Hello World!&quot;)&#39;
Hello World!
$</code></pre><p>Only the <code>main</code> binding in the <code>Main</code> module has this behavior and only if the macro <code>@main</code> was used within the defining module.</p><p>For example, using <code>hello</code> instead of <code>main</code> will not result in the <code>hello</code> function executing:</p><pre><code class="nohighlight hljs">$ julia -e &#39;hello(ARGS) = println(&quot;Hello World!&quot;)&#39;
$</code></pre><p>and neither will a plain definition of <code>main</code>:</p><pre><code class="nohighlight hljs">$ julia -e &#39;main(ARGS) = println(&quot;Hello World!&quot;)&#39;
$</code></pre><p>However, the opt-in need not occur at definition time:</p><pre><code class="nohighlight hljs">$ julia -e &#39;main(ARGS) = println(&quot;Hello World!&quot;); @main&#39;
Hello World!
$</code></pre><p>The <code>main</code> binding may be imported from a package. A <em>hello world</em> package defined as</p><pre><code class="nohighlight hljs">module Hello

export main
(@main)(args) = println(&quot;Hello from the package!&quot;)

end</code></pre><p>may be used as:</p><pre><code class="nohighlight hljs">$ julia -e &#39;using Hello&#39;
Hello from the package!
$ julia -e &#39;import Hello&#39; # N.B.: Execution depends on the binding not whether the package is loaded
$</code></pre><p>However, note that the current best practice recommendation is to not mix application and reusable library code in the same package. Helper applications may be distributed as separate packages or as scripts with separate <code>main</code> entry points in a package&#39;s <code>bin</code> folder.</p><h2 id="Parallel-mode"><a class="docs-heading-anchor" href="#Parallel-mode">Parallel mode</a><a id="Parallel-mode-1"></a><a class="docs-heading-anchor-permalink" href="#Parallel-mode" title="Permalink"></a></h2><p>Julia can be started in parallel mode with either the <code>-p</code> or the <code>--machine-file</code> options. <code>-p n</code> will launch an additional <code>n</code> worker processes, while <code>--machine-file file</code> will launch a worker for each line in file <code>file</code>. The machines defined in <code>file</code> must be accessible via a password-less <code>ssh</code> login, with Julia installed at the same location as the current host. Each machine definition takes the form <code>[count*][user@]host[:port] [bind_addr[:port]]</code>. <code>user</code> defaults to current user, <code>port</code> to the standard ssh port. <code>count</code> is the number of workers to spawn on the node, and defaults to 1. The optional <code>bind-to bind_addr[:port]</code> specifies the IP address and port that other workers should use to connect to this worker.</p><h2 id="Startup-file"><a class="docs-heading-anchor" href="#Startup-file">Startup file</a><a id="Startup-file-1"></a><a class="docs-heading-anchor-permalink" href="#Startup-file" title="Permalink"></a></h2><p>If you have code that you want executed whenever Julia is run, you can put it in <code>~/.julia/config/startup.jl</code>:</p><pre><code class="nohighlight hljs">$ echo &#39;println(&quot;Greetings! 你好! 안녕하세요?&quot;)&#39; &gt; ~/.julia/config/startup.jl
$ julia
Greetings! 你好! 안녕하세요?

...</code></pre><p>Note that although you should have a <code>~/.julia</code> directory once you&#39;ve run Julia for the first time, you may need to create the <code>~/.julia/config</code> folder and the <code>~/.julia/config/startup.jl</code> file if you use it.</p><p>To have startup code run only in <a href="../stdlib/REPL.html#The-Julia-REPL">The Julia REPL</a> (and not when <code>julia</code> is <em>e.g.</em> run on a script), use <a href="../stdlib/REPL.html#Base.atreplinit"><code>atreplinit</code></a> in <code>startup.jl</code>:</p><pre><code class="language-julia hljs">atreplinit() do repl
    # ...
end</code></pre><h2 id="command-line-interface"><a class="docs-heading-anchor" href="#command-line-interface">Command-line switches for Julia</a><a id="command-line-interface-1"></a><a class="docs-heading-anchor-permalink" href="#command-line-interface" title="Permalink"></a></h2><p>There are various ways to run Julia code and provide options, similar to those available for the <code>perl</code> and <code>ruby</code> programs:</p><pre><code class="nohighlight hljs">julia [switches] -- [programfile] [args...]</code></pre><p>The following is a complete list of command-line switches available when launching julia (a &#39;*&#39; marks the default value, if applicable; settings marked &#39;($)&#39; may trigger package precompilation):</p><table><tr><th style="text-align: left">Switch</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left"><code>-v</code>, <code>--version</code></td><td style="text-align: left">Display version information</td></tr><tr><td style="text-align: left"><code>-h</code>, <code>--help</code></td><td style="text-align: left">Print command-line options (this message)</td></tr><tr><td style="text-align: left"><code>--help-hidden</code></td><td style="text-align: left">Print uncommon options not shown by <code>-h</code></td></tr><tr><td style="text-align: left"><code>--project[={&lt;dir&gt;|@.}]</code></td><td style="text-align: left">Set <code>&lt;dir&gt;</code> as the active project/environment. The default <code>@.</code> option will search through parent directories until a <code>Project.toml</code> or <code>JuliaProject.toml</code> file is found.</td></tr><tr><td style="text-align: left"><code>-J</code>, <code>--sysimage &lt;file&gt;</code></td><td style="text-align: left">Start up with the given system image file</td></tr><tr><td style="text-align: left"><code>-H</code>, <code>--home &lt;dir&gt;</code></td><td style="text-align: left">Set location of <code>julia</code> executable</td></tr><tr><td style="text-align: left"><code>--startup-file={yes*|no}</code></td><td style="text-align: left">Load <code>JULIA_DEPOT_PATH/config/startup.jl</code>; if <a href="environment-variables.html#JULIA_DEPOT_PATH"><code>JULIA_DEPOT_PATH</code></a> environment variable is unset, load <code>~/.julia/config/startup.jl</code></td></tr><tr><td style="text-align: left"><code>--handle-signals={yes*|no}</code></td><td style="text-align: left">Enable or disable Julia&#39;s default signal handlers</td></tr><tr><td style="text-align: left"><code>--sysimage-native-code={yes*|no}</code></td><td style="text-align: left">Use native code from system image if available</td></tr><tr><td style="text-align: left"><code>--compiled-modules={yes*|no|existing|strict}</code></td><td style="text-align: left">Enable or disable incremental precompilation of modules. The <code>existing</code> option allows use of existing compiled modules that were previously precompiled, but disallows creation of new precompile files. The <code>strict</code> option is similar, but will error if no precompile file is found.</td></tr><tr><td style="text-align: left"><code>--pkgimages={yes*|no|existing}</code></td><td style="text-align: left">Enable or disable usage of native code caching in the form of pkgimages. The <code>existing</code> option allows use of existing pkgimages but disallows creation of new ones</td></tr><tr><td style="text-align: left"><code>-e</code>, <code>--eval &lt;expr&gt;</code></td><td style="text-align: left">Evaluate <code>&lt;expr&gt;</code></td></tr><tr><td style="text-align: left"><code>-E</code>, <code>--print &lt;expr&gt;</code></td><td style="text-align: left">Evaluate <code>&lt;expr&gt;</code> and display the result</td></tr><tr><td style="text-align: left"><code>-m</code>, <code>--module &lt;Package&gt; [args]</code></td><td style="text-align: left">Run entry point of <code>Package</code> (<code>@main</code> function) with `args&#39;</td></tr><tr><td style="text-align: left"><code>-L</code>, <code>--load &lt;file&gt;</code></td><td style="text-align: left">Load <code>&lt;file&gt;</code> immediately on all processors</td></tr><tr><td style="text-align: left"><code>-t</code>, <code>--threads {auto|N[,auto|M]}</code></td><td style="text-align: left">Enable N[+M] threads; N threads are assigned to the <code>default</code> threadpool, and if M is specified, M threads are assigned to the <code>interactive</code> threadpool; <code>auto</code> tries to infer a useful default number of threads to use but the exact behavior might change in the future. Currently sets N to the number of CPUs assigned to this Julia process based on the OS-specific affinity assignment interface if supported (Linux and Windows) or to the number of CPU threads if not supported (MacOS) or if process affinity is not configured, and sets M to 1.</td></tr><tr><td style="text-align: left"><code>--gcthreads=N[,M]</code></td><td style="text-align: left">Use N threads for the mark phase of GC and M (0 or 1) threads for the concurrent sweeping phase of GC. N is set to half of the number of compute threads and M is set to 0 if unspecified.</td></tr><tr><td style="text-align: left"><code>-p</code>, <code>--procs {N|auto}</code></td><td style="text-align: left">Integer value N launches N additional local worker processes; <code>auto</code> launches as many workers as the number of local CPU threads (logical cores)</td></tr><tr><td style="text-align: left"><code>--machine-file &lt;file&gt;</code></td><td style="text-align: left">Run processes on hosts listed in <code>&lt;file&gt;</code></td></tr><tr><td style="text-align: left"><code>-i</code>, <code>--interactive</code></td><td style="text-align: left">Interactive mode; REPL runs and <code>isinteractive()</code> is true</td></tr><tr><td style="text-align: left"><code>-q</code>, <code>--quiet</code></td><td style="text-align: left">Quiet startup: no banner, suppress REPL warnings</td></tr><tr><td style="text-align: left"><code>--banner={yes|no|short|auto*}</code></td><td style="text-align: left">Enable or disable startup banner</td></tr><tr><td style="text-align: left"><code>--color={yes|no|auto*}</code></td><td style="text-align: left">Enable or disable color text</td></tr><tr><td style="text-align: left"><code>--history-file={yes*|no}</code></td><td style="text-align: left">Load or save history</td></tr><tr><td style="text-align: left"><code>--depwarn={yes|no*|error}</code></td><td style="text-align: left">Enable or disable syntax and method deprecation warnings (<code>error</code> turns warnings into errors)</td></tr><tr><td style="text-align: left"><code>--warn-overwrite={yes|no*}</code></td><td style="text-align: left">Enable or disable method overwrite warnings</td></tr><tr><td style="text-align: left"><code>--warn-scope={yes*|no}</code></td><td style="text-align: left">Enable or disable warning for ambiguous top-level scope</td></tr><tr><td style="text-align: left"><code>-C</code>, <code>--cpu-target &lt;target&gt;</code></td><td style="text-align: left">Limit usage of CPU features up to <code>&lt;target&gt;</code>; set to <code>help</code> to see the available options</td></tr><tr><td style="text-align: left"><code>-O</code>, <code>--optimize={0|1|2*|3}</code></td><td style="text-align: left">Set the optimization level (level is 3 if <code>-O</code> is used without a level) ($)</td></tr><tr><td style="text-align: left"><code>--min-optlevel={0*|1|2|3}</code></td><td style="text-align: left">Set the lower bound on per-module optimization</td></tr><tr><td style="text-align: left"><code>-g</code>, <code>--debug-info={0|1*|2}</code></td><td style="text-align: left">Set the level of debug info generation (level is 2 if <code>-g</code> is used without a level) ($)</td></tr><tr><td style="text-align: left"><code>--inline={yes|no}</code></td><td style="text-align: left">Control whether inlining is permitted, including overriding <code>@inline</code> declarations</td></tr><tr><td style="text-align: left"><code>--check-bounds={yes|no|auto*}</code></td><td style="text-align: left">Emit bounds checks always, never, or respect <code>@inbounds</code> declarations ($)</td></tr><tr><td style="text-align: left"><code>--math-mode={ieee,fast}</code></td><td style="text-align: left">Disallow or enable unsafe floating point optimizations (overrides <code>@fastmath</code> declaration)</td></tr><tr><td style="text-align: left"><code>--polly={yes*|no}</code></td><td style="text-align: left">Enable or disable the polyhedral optimizer Polly (overrides @polly declaration)</td></tr><tr><td style="text-align: left"><code>--code-coverage[={none*|user|all}]</code></td><td style="text-align: left">Count executions of source lines (omitting setting is equivalent to <code>user</code>)</td></tr><tr><td style="text-align: left"><code>--code-coverage=@&lt;path&gt;</code></td><td style="text-align: left">Count executions but only in files that fall under the given file path/directory. The <code>@</code> prefix is required to select this option. A <code>@</code> with no path will track the current directory.</td></tr><tr><td style="text-align: left"><code>--code-coverage=tracefile.info</code></td><td style="text-align: left">Append coverage information to the LCOV tracefile (filename supports format tokens).</td></tr><tr><td style="text-align: left"><code>--track-allocation[={none*|user|all}]</code></td><td style="text-align: left">Count bytes allocated by each source line (omitting setting is equivalent to &quot;user&quot;)</td></tr><tr><td style="text-align: left"><code>--track-allocation=@&lt;path&gt;</code></td><td style="text-align: left">Count bytes but only in files that fall under the given file path/directory. The <code>@</code> prefix is required to select this option. A <code>@</code> with no path will track the current directory.</td></tr><tr><td style="text-align: left"><code>--bug-report=KIND</code></td><td style="text-align: left">Launch a bug report session. It can be used to start a REPL, run a script, or evaluate expressions. It first tries to use BugReporting.jl installed in current environment and falls back to the latest compatible BugReporting.jl if not. For more information, see <code>--bug-report=help</code>.</td></tr><tr><td style="text-align: left"><code>--heap-size-hint=&lt;size&gt;</code></td><td style="text-align: left">Forces garbage collection if memory usage is higher than the given value. The value may be specified as a number of bytes, optionally in units of KB, MB, GB, or TB, or as a percentage of physical memory with %.</td></tr><tr><td style="text-align: left"><code>--compile={yes*|no|all|min}</code></td><td style="text-align: left">Enable or disable JIT compiler, or request exhaustive or minimal compilation</td></tr><tr><td style="text-align: left"><code>--output-o &lt;name&gt;</code></td><td style="text-align: left">Generate an object file (including system image data)</td></tr><tr><td style="text-align: left"><code>--output-ji &lt;name&gt;</code></td><td style="text-align: left">Generate a system image data file (.ji)</td></tr><tr><td style="text-align: left"><code>--strip-metadata</code></td><td style="text-align: left">Remove docstrings and source location info from system image</td></tr><tr><td style="text-align: left"><code>--strip-ir</code></td><td style="text-align: left">Remove IR (intermediate representation) of compiled functions</td></tr><tr><td style="text-align: left"><code>--output-unopt-bc &lt;name&gt;</code></td><td style="text-align: left">Generate unoptimized LLVM bitcode (.bc)</td></tr><tr><td style="text-align: left"><code>--output-bc &lt;name&gt;</code></td><td style="text-align: left">Generate LLVM bitcode (.bc)</td></tr><tr><td style="text-align: left"><code>--output-asm &lt;name&gt;</code></td><td style="text-align: left">Generate an assembly file (.s)</td></tr><tr><td style="text-align: left"><code>--output-incremental={yes|no*}</code></td><td style="text-align: left">Generate an incremental output file (rather than complete)</td></tr><tr><td style="text-align: left"><code>--trace-compile={stderr|name}</code></td><td style="text-align: left">Print precompile statements for methods compiled during execution or save to a path</td></tr><tr><td style="text-align: left"><code>--image-codegen</code></td><td style="text-align: left">Force generate code in imaging mode</td></tr><tr><td style="text-align: left"><code>--permalloc-pkgimg={yes|no*}</code></td><td style="text-align: left">Copy the data section of package images into memory</td></tr></table><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p>In Julia 1.0, the default <code>--project=@.</code> option did not search up from the root directory of a Git repository for the <code>Project.toml</code> file. From Julia 1.1 forward, it does.</p></div></div></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="unicode-input.html">« Unicode Input</a><a class="docs-footer-nextpage" href="../base/base.html">Essentials »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
