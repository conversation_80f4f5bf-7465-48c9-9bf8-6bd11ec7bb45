<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Strings · The Julia Language</title><meta name="title" content="Strings · The Julia Language"/><meta property="og:title" content="Strings · The Julia Language"/><meta property="twitter:title" content="Strings · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li class="is-active"><a class="tocitem" href="strings.html">Strings</a><ul class="internal"><li><a class="tocitem" href="#man-characters"><span>Characters</span></a></li><li><a class="tocitem" href="#String-Basics"><span>String Basics</span></a></li><li><a class="tocitem" href="#Unicode-and-UTF-8"><span>Unicode and UTF-8</span></a></li><li><a class="tocitem" href="#man-concatenation"><span>Concatenation</span></a></li><li><a class="tocitem" href="#string-interpolation"><span>Interpolation</span></a></li><li><a class="tocitem" href="#Triple-Quoted-String-Literals"><span>Triple-Quoted String Literals</span></a></li><li><a class="tocitem" href="#Common-Operations"><span>Common Operations</span></a></li><li><a class="tocitem" href="#non-standard-string-literals"><span>Non-Standard String Literals</span></a></li><li><a class="tocitem" href="#man-regex-literals"><span>Regular Expressions</span></a></li><li><a class="tocitem" href="#man-byte-array-literals"><span>Byte Array Literals</span></a></li><li><a class="tocitem" href="#man-version-number-literals"><span>Version Number Literals</span></a></li><li><a class="tocitem" href="#man-raw-string-literals"><span>Raw String Literals</span></a></li><li><a class="tocitem" href="#man-annotated-strings"><span>Annotated Strings</span></a></li></ul></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="strings.html">Strings</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="strings.html">Strings</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/strings.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="man-strings"><a class="docs-heading-anchor" href="#man-strings">Strings</a><a id="man-strings-1"></a><a class="docs-heading-anchor-permalink" href="#man-strings" title="Permalink"></a></h1><p>Strings are finite sequences of characters. Of course, the real trouble comes when one asks what a character is. The characters that English speakers are familiar with are the letters <code>A</code>, <code>B</code>, <code>C</code>, etc., together with numerals and common punctuation symbols. These characters are standardized together with a mapping to integer values between 0 and 127 by the <a href="https://en.wikipedia.org/wiki/ASCII">ASCII</a> standard. There are, of course, many other characters used in non-English languages, including variants of the ASCII characters with accents and other modifications, related scripts such as Cyrillic and Greek, and scripts completely unrelated to ASCII and English, including Arabic, Chinese, Hebrew, Hindi, Japanese, and Korean. The <a href="https://en.wikipedia.org/wiki/Unicode">Unicode</a> standard tackles the complexities of what exactly a character is, and is generally accepted as the definitive standard addressing this problem. Depending on your needs, you can either ignore these complexities entirely and just pretend that only ASCII characters exist, or you can write code that can handle any of the characters or encodings that one may encounter when handling non-ASCII text. Julia makes dealing with plain ASCII text simple and efficient, and handling Unicode is as simple and efficient as possible. In particular, you can write C-style string code to process ASCII strings, and they will work as expected, both in terms of performance and semantics. If such code encounters non-ASCII text, it will gracefully fail with a clear error message, rather than silently introducing corrupt results. When this happens, modifying the code to handle non-ASCII data is straightforward.</p><p>There are a few noteworthy high-level features about Julia&#39;s strings:</p><ul><li>The built-in concrete type used for strings (and string literals) in Julia is <a href="../base/strings.html#Core.String-Tuple{AbstractString}"><code>String</code></a>. This supports the full range of <a href="https://en.wikipedia.org/wiki/Unicode">Unicode</a> characters via the <a href="https://en.wikipedia.org/wiki/UTF-8">UTF-8</a> encoding. (A <a href="../base/strings.html#Base.transcode"><code>transcode</code></a> function is provided to convert to/from other Unicode encodings.)</li><li>All string types are subtypes of the abstract type <code>AbstractString</code>, and external packages define additional <code>AbstractString</code> subtypes (e.g. for other encodings).  If you define a function expecting a string argument, you should declare the type as <code>AbstractString</code> in order to accept any string type.</li><li>Like C and Java, but unlike most dynamic languages, Julia has a first-class type for representing a single character, called <a href="../base/strings.html#Core.AbstractChar"><code>AbstractChar</code></a>. The built-in <a href="../base/strings.html#Core.Char"><code>Char</code></a> subtype of <code>AbstractChar</code> is a 32-bit primitive type that can represent any Unicode character (and which is based on the UTF-8 encoding).</li><li>As in Java, strings are immutable: the value of an <code>AbstractString</code> object cannot be changed. To construct a different string value, you construct a new string from parts of other strings.</li><li>Conceptually, a string is a <em>partial function</em> from indices to characters: for some index values, no character value is returned, and instead an exception is thrown. This allows for efficient indexing into strings by the byte index of an encoded representation rather than by a character index, which cannot be implemented both efficiently and simply for variable-width encodings of Unicode strings.</li></ul><h2 id="man-characters"><a class="docs-heading-anchor" href="#man-characters">Characters</a><a id="man-characters-1"></a><a class="docs-heading-anchor-permalink" href="#man-characters" title="Permalink"></a></h2><p>A <code>Char</code> value represents a single character: it is just a 32-bit primitive type with a special literal representation and appropriate arithmetic behaviors, and which can be converted to a numeric value representing a <a href="https://en.wikipedia.org/wiki/Code_point">Unicode code point</a>.  (Julia packages may define other subtypes of <code>AbstractChar</code>, e.g. to optimize operations for other <a href="https://en.wikipedia.org/wiki/Character_encoding">text encodings</a>.) Here is how <code>Char</code> values are input and shown (note that character literals are delimited with single quotes, not double quotes):</p><pre><code class="language-julia-repl hljs">julia&gt; c = &#39;x&#39;
&#39;x&#39;: ASCII/Unicode U+0078 (category Ll: Letter, lowercase)

julia&gt; typeof(c)
Char</code></pre><p>You can easily convert a <code>Char</code> to its integer value, i.e. code point:</p><pre><code class="language-julia-repl hljs">julia&gt; c = Int(&#39;x&#39;)
120

julia&gt; typeof(c)
Int64</code></pre><p>On 32-bit architectures, <a href="../base/base.html#Core.typeof"><code>typeof(c)</code></a> will be <a href="../base/numbers.html#Core.Int32"><code>Int32</code></a>. You can convert an integer value back to a <code>Char</code> just as easily:</p><pre><code class="language-julia-repl hljs">julia&gt; Char(120)
&#39;x&#39;: ASCII/Unicode U+0078 (category Ll: Letter, lowercase)</code></pre><p>Not all integer values are valid Unicode code points, but for performance, the <code>Char</code> conversion does not check that every character value is valid. If you want to check that each converted value is a valid code point, use the <a href="../base/strings.html#Base.isvalid-Tuple{Any}"><code>isvalid</code></a> function:</p><pre><code class="language-julia-repl hljs">julia&gt; Char(0x110000)
&#39;\U110000&#39;: Unicode U+110000 (category In: Invalid, too high)

julia&gt; isvalid(Char, 0x110000)
false</code></pre><p>As of this writing, the valid Unicode code points are <code>U+0000</code> through <code>U+D7FF</code> and <code>U+E000</code> through <code>U+10FFFF</code>. These have not all been assigned intelligible meanings yet, nor are they necessarily interpretable by applications, but all of these values are considered to be valid Unicode characters.</p><p>You can input any Unicode character in single quotes using <code>\u</code> followed by up to four hexadecimal digits or <code>\U</code> followed by up to eight hexadecimal digits (the longest valid value only requires six):</p><pre><code class="language-julia-repl hljs">julia&gt; &#39;\u0&#39;
&#39;\0&#39;: ASCII/Unicode U+0000 (category Cc: Other, control)

julia&gt; &#39;\u78&#39;
&#39;x&#39;: ASCII/Unicode U+0078 (category Ll: Letter, lowercase)

julia&gt; &#39;\u2200&#39;
&#39;∀&#39;: Unicode U+2200 (category Sm: Symbol, math)

julia&gt; &#39;\U10ffff&#39;
&#39;\U10ffff&#39;: Unicode U+10FFFF (category Cn: Other, not assigned)</code></pre><p>Julia uses your system&#39;s locale and language settings to determine which characters can be printed as-is and which must be output using the generic, escaped <code>\u</code> or <code>\U</code> input forms. In addition to these Unicode escape forms, all of <a href="https://en.wikipedia.org/wiki/C_syntax#Backslash_escapes">C&#39;s traditional escaped input forms</a> can also be used:</p><pre><code class="language-julia-repl hljs">julia&gt; Int(&#39;\0&#39;)
0

julia&gt; Int(&#39;\t&#39;)
9

julia&gt; Int(&#39;\n&#39;)
10

julia&gt; Int(&#39;\e&#39;)
27

julia&gt; Int(&#39;\x7f&#39;)
127

julia&gt; Int(&#39;\177&#39;)
127</code></pre><p>You can do comparisons and a limited amount of arithmetic with <code>Char</code> values:</p><pre><code class="language-julia-repl hljs">julia&gt; &#39;A&#39; &lt; &#39;a&#39;
true

julia&gt; &#39;A&#39; &lt;= &#39;a&#39; &lt;= &#39;Z&#39;
false

julia&gt; &#39;A&#39; &lt;= &#39;X&#39; &lt;= &#39;Z&#39;
true

julia&gt; &#39;x&#39; - &#39;a&#39;
23

julia&gt; &#39;A&#39; + 1
&#39;B&#39;: ASCII/Unicode U+0042 (category Lu: Letter, uppercase)</code></pre><h2 id="String-Basics"><a class="docs-heading-anchor" href="#String-Basics">String Basics</a><a id="String-Basics-1"></a><a class="docs-heading-anchor-permalink" href="#String-Basics" title="Permalink"></a></h2><p>String literals are delimited by double quotes or triple double quotes (not single quotes):</p><pre><code class="language-julia-repl hljs">julia&gt; str = &quot;Hello, world.\n&quot;
&quot;Hello, world.\n&quot;

julia&gt; &quot;&quot;&quot;Contains &quot;quote&quot; characters&quot;&quot;&quot;
&quot;Contains \&quot;quote\&quot; characters&quot;</code></pre><p>Long lines in strings can be broken up by preceding the newline with a backslash (<code>\</code>):</p><pre><code class="language-julia-repl hljs">julia&gt; &quot;This is a long \
       line&quot;
&quot;This is a long line&quot;</code></pre><p>If you want to extract a character from a string, you index into it:</p><pre><code class="language-julia-repl hljs">julia&gt; str[begin]
&#39;H&#39;: ASCII/Unicode U+0048 (category Lu: Letter, uppercase)

julia&gt; str[1]
&#39;H&#39;: ASCII/Unicode U+0048 (category Lu: Letter, uppercase)

julia&gt; str[6]
&#39;,&#39;: ASCII/Unicode U+002C (category Po: Punctuation, other)

julia&gt; str[end]
&#39;\n&#39;: ASCII/Unicode U+000A (category Cc: Other, control)</code></pre><p>Many Julia objects, including strings, can be indexed with integers. The index of the first element (the first character of a string) is returned by <a href="../base/collections.html#Base.firstindex"><code>firstindex(str)</code></a>, and the index of the last element (character) with <a href="../base/collections.html#Base.lastindex"><code>lastindex(str)</code></a>. The keywords <code>begin</code> and <code>end</code> can be used inside an indexing operation as shorthand for the first and last indices, respectively, along the given dimension. String indexing, like most indexing in Julia, is 1-based: <code>firstindex</code> always returns <code>1</code> for any <code>AbstractString</code>. As we will see below, however, <code>lastindex(str)</code> is <em>not</em> in general the same as <code>length(str)</code> for a string, because some Unicode characters can occupy multiple &quot;code units&quot;.</p><p>You can perform arithmetic and other operations with <a href="../base/base.html#end"><code>end</code></a>, just like a normal value:</p><pre><code class="language-julia-repl hljs">julia&gt; str[end-1]
&#39;.&#39;: ASCII/Unicode U+002E (category Po: Punctuation, other)

julia&gt; str[end÷2]
&#39; &#39;: ASCII/Unicode U+0020 (category Zs: Separator, space)</code></pre><p>Using an index less than <code>begin</code> (<code>1</code>) or greater than <code>end</code> raises an error:</p><pre><code class="language-julia-repl hljs">julia&gt; str[begin-1]
ERROR: BoundsError: attempt to access 14-codeunit String at index [0]
[...]

julia&gt; str[end+1]
ERROR: BoundsError: attempt to access 14-codeunit String at index [15]
[...]</code></pre><p>You can also extract a substring using range indexing:</p><pre><code class="language-julia-repl hljs">julia&gt; str[4:9]
&quot;lo, wo&quot;</code></pre><p>Notice that the expressions <code>str[k]</code> and <code>str[k:k]</code> do not give the same result:</p><pre><code class="language-julia-repl hljs">julia&gt; str[6]
&#39;,&#39;: ASCII/Unicode U+002C (category Po: Punctuation, other)

julia&gt; str[6:6]
&quot;,&quot;</code></pre><p>The former is a single character value of type <code>Char</code>, while the latter is a string value that happens to contain only a single character. In Julia these are very different things.</p><p>Range indexing makes a copy of the selected part of the original string. Alternatively, it is possible to create a view into a string using the type <a href="../base/strings.html#Base.SubString"><code>SubString</code></a>. More simply, using the <a href="../base/arrays.html#Base.@views"><code>@views</code></a> macro on a block of code converts all string slices into substrings.  For example:</p><pre><code class="language-julia-repl hljs">julia&gt; str = &quot;long string&quot;
&quot;long string&quot;

julia&gt; substr = SubString(str, 1, 4)
&quot;long&quot;

julia&gt; typeof(substr)
SubString{String}

julia&gt; @views typeof(str[1:4]) # @views converts slices to SubStrings
SubString{String}</code></pre><p>Several standard functions like <a href="../base/strings.html#Base.chop"><code>chop</code></a>, <a href="../base/strings.html#Base.chomp"><code>chomp</code></a> or <a href="../base/strings.html#Base.strip"><code>strip</code></a> return a <a href="../base/strings.html#Base.SubString"><code>SubString</code></a>.</p><h2 id="Unicode-and-UTF-8"><a class="docs-heading-anchor" href="#Unicode-and-UTF-8">Unicode and UTF-8</a><a id="Unicode-and-UTF-8-1"></a><a class="docs-heading-anchor-permalink" href="#Unicode-and-UTF-8" title="Permalink"></a></h2><p>Julia fully supports Unicode characters and strings. As <a href="strings.html#man-characters">discussed above</a>, in character literals, Unicode code points can be represented using Unicode <code>\u</code> and <code>\U</code> escape sequences, as well as all the standard C escape sequences. These can likewise be used to write string literals:</p><pre><code class="language-julia-repl hljs">julia&gt; s = &quot;\u2200 x \u2203 y&quot;
&quot;∀ x ∃ y&quot;</code></pre><p>Whether these Unicode characters are displayed as escapes or shown as special characters depends on your terminal&#39;s locale settings and its support for Unicode. String literals are encoded using the UTF-8 encoding. UTF-8 is a variable-width encoding, meaning that not all characters are encoded in the same number of bytes (&quot;code units&quot;). In UTF-8, ASCII characters — i.e. those with code points less than 0x80 (128) – are encoded as they are in ASCII, using a single byte, while code points 0x80 and above are encoded using multiple bytes — up to four per character.</p><p>String indices in Julia refer to code units (= bytes for UTF-8), the fixed-width building blocks that are used to encode arbitrary characters (code points). This means that not every index into a <code>String</code> is necessarily a valid index for a character. If you index into a string at such an invalid byte index, an error is thrown:</p><pre><code class="language-julia-repl hljs">julia&gt; s[1]
&#39;∀&#39;: Unicode U+2200 (category Sm: Symbol, math)

julia&gt; s[2]
ERROR: StringIndexError: invalid index [2], valid nearby indices [1]=&gt;&#39;∀&#39;, [4]=&gt;&#39; &#39;
Stacktrace:
[...]

julia&gt; s[3]
ERROR: StringIndexError: invalid index [3], valid nearby indices [1]=&gt;&#39;∀&#39;, [4]=&gt;&#39; &#39;
Stacktrace:
[...]

julia&gt; s[4]
&#39; &#39;: ASCII/Unicode U+0020 (category Zs: Separator, space)</code></pre><p>In this case, the character <code>∀</code> is a three-byte character, so the indices 2 and 3 are invalid and the next character&#39;s index is 4; this next valid index can be computed by <a href="../base/arrays.html#Base.nextind"><code>nextind(s,1)</code></a>, and the next index after that by <code>nextind(s,4)</code> and so on.</p><p>Since <code>end</code> is always the last valid index into a collection, <code>end-1</code> references an invalid byte index if the second-to-last character is multibyte.</p><pre><code class="language-julia-repl hljs">julia&gt; s[end-1]
&#39; &#39;: ASCII/Unicode U+0020 (category Zs: Separator, space)

julia&gt; s[end-2]
ERROR: StringIndexError: invalid index [9], valid nearby indices [7]=&gt;&#39;∃&#39;, [10]=&gt;&#39; &#39;
Stacktrace:
[...]

julia&gt; s[prevind(s, end, 2)]
&#39;∃&#39;: Unicode U+2203 (category Sm: Symbol, math)</code></pre><p>The first case works, because the last character <code>y</code> and the space are one-byte characters, whereas <code>end-2</code> indexes into the middle of the <code>∃</code> multibyte representation. The correct way for this case is using <code>prevind(s, lastindex(s), 2)</code> or, if you&#39;re using that value to index into <code>s</code> you can write <code>s[prevind(s, end, 2)]</code> and <code>end</code> expands to <code>lastindex(s)</code>.</p><p>Extraction of a substring using range indexing also expects valid byte indices or an error is thrown:</p><pre><code class="language-julia-repl hljs">julia&gt; s[1:1]
&quot;∀&quot;

julia&gt; s[1:2]
ERROR: StringIndexError: invalid index [2], valid nearby indices [1]=&gt;&#39;∀&#39;, [4]=&gt;&#39; &#39;
Stacktrace:
[...]

julia&gt; s[1:4]
&quot;∀ &quot;</code></pre><p>Because of variable-length encodings, the number of characters in a string (given by <a href="../base/arrays.html#Base.length-Tuple{AbstractArray}"><code>length(s)</code></a>) is not always the same as the last index. If you iterate through the indices 1 through <a href="../base/collections.html#Base.lastindex"><code>lastindex(s)</code></a> and index into <code>s</code>, the sequence of characters returned when errors aren&#39;t thrown is the sequence of characters comprising the string <code>s</code>. Thus <code>length(s) &lt;= lastindex(s)</code>, since each character in a string must have its own index. The following is an inefficient and verbose way to iterate through the characters of <code>s</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; for i = firstindex(s):lastindex(s)
           try
               println(s[i])
           catch
               # ignore the index error
           end
       end
∀

x

∃

y</code></pre><p>The blank lines actually have spaces on them. Fortunately, the above awkward idiom is unnecessary for iterating through the characters in a string, since you can just use the string as an iterable object, no exception handling required:</p><pre><code class="language-julia-repl hljs">julia&gt; for c in s
           println(c)
       end
∀

x

∃

y</code></pre><p>If you need to obtain valid indices for a string, you can use the <a href="../base/arrays.html#Base.nextind"><code>nextind</code></a> and <a href="../base/arrays.html#Base.prevind"><code>prevind</code></a> functions to increment/decrement to the next/previous valid index, as mentioned above. You can also use the <a href="../base/arrays.html#Base.eachindex"><code>eachindex</code></a> function to iterate over the valid character indices:</p><pre><code class="language-julia-repl hljs">julia&gt; collect(eachindex(s))
7-element Vector{Int64}:
  1
  4
  5
  6
  7
 10
 11</code></pre><p>To access the raw code units (bytes for UTF-8) of the encoding, you can use the <a href="../base/strings.html#Base.codeunit"><code>codeunit(s,i)</code></a> function, where the index <code>i</code> runs consecutively from <code>1</code> to <a href="../base/strings.html#Base.ncodeunits-Tuple{AbstractString}"><code>ncodeunits(s)</code></a>.  The <a href="../base/strings.html#Base.codeunits"><code>codeunits(s)</code></a> function returns an <code>AbstractVector{UInt8}</code> wrapper that lets you access these raw codeunits (bytes) as an array.</p><p>Strings in Julia can contain invalid UTF-8 code unit sequences. This convention allows to treat any byte sequence as a <code>String</code>. In such situations a rule is that when parsing a sequence of code units from left to right characters are formed by the longest sequence of 8-bit code units that matches the start of one of the following bit patterns (each <code>x</code> can be <code>0</code> or <code>1</code>):</p><ul><li><code>0xxxxxxx</code>;</li><li><code>110xxxxx</code> <code>10xxxxxx</code>;</li><li><code>1110xxxx</code> <code>10xxxxxx</code> <code>10xxxxxx</code>;</li><li><code>11110xxx</code> <code>10xxxxxx</code> <code>10xxxxxx</code> <code>10xxxxxx</code>;</li><li><code>10xxxxxx</code>;</li><li><code>11111xxx</code>.</li></ul><p>In particular this means that overlong and too-high code unit sequences and prefixes thereof are treated as a single invalid character rather than multiple invalid characters. This rule may be best explained with an example:</p><pre><code class="language-julia-repl hljs">julia&gt; s = &quot;\xc0\xa0\xe2\x88\xe2|&quot;
&quot;\xc0\xa0\xe2\x88\xe2|&quot;

julia&gt; foreach(display, s)
&#39;\xc0\xa0&#39;: [overlong] ASCII/Unicode U+0020 (category Zs: Separator, space)
&#39;\xe2\x88&#39;: Malformed UTF-8 (category Ma: Malformed, bad data)
&#39;\xe2&#39;: Malformed UTF-8 (category Ma: Malformed, bad data)
&#39;|&#39;: ASCII/Unicode U+007C (category Sm: Symbol, math)

julia&gt; isvalid.(collect(s))
4-element BitArray{1}:
 0
 0
 0
 1

julia&gt; s2 = &quot;\xf7\xbf\xbf\xbf&quot;
&quot;\U1fffff&quot;

julia&gt; foreach(display, s2)
&#39;\U1fffff&#39;: Unicode U+1FFFFF (category In: Invalid, too high)</code></pre><p>We can see that the first two code units in the string <code>s</code> form an overlong encoding of space character. It is invalid, but is accepted in a string as a single character. The next two code units form a valid start of a three-byte UTF-8 sequence. However, the fifth code unit <code>\xe2</code> is not its valid continuation. Therefore code units 3 and 4 are also interpreted as malformed characters in this string. Similarly code unit 5 forms a malformed character because <code>|</code> is not a valid continuation to it. Finally the string <code>s2</code> contains one too high code point.</p><p>Julia uses the UTF-8 encoding by default, and support for new encodings can be added by packages. For example, the <a href="https://github.com/JuliaStrings/LegacyStrings.jl">LegacyStrings.jl</a> package implements <code>UTF16String</code> and <code>UTF32String</code> types. Additional discussion of other encodings and how to implement support for them is beyond the scope of this document for the time being. For further discussion of UTF-8 encoding issues, see the section below on <a href="strings.html#man-byte-array-literals">byte array literals</a>. The <a href="../base/strings.html#Base.transcode"><code>transcode</code></a> function is provided to convert data between the various UTF-xx encodings, primarily for working with external data and libraries.</p><h2 id="man-concatenation"><a class="docs-heading-anchor" href="#man-concatenation">Concatenation</a><a id="man-concatenation-1"></a><a class="docs-heading-anchor-permalink" href="#man-concatenation" title="Permalink"></a></h2><p>One of the most common and useful string operations is concatenation:</p><pre><code class="language-julia-repl hljs">julia&gt; greet = &quot;Hello&quot;
&quot;Hello&quot;

julia&gt; whom = &quot;world&quot;
&quot;world&quot;

julia&gt; string(greet, &quot;, &quot;, whom, &quot;.\n&quot;)
&quot;Hello, world.\n&quot;</code></pre><p>It&#39;s important to be aware of potentially dangerous situations such as concatenation of invalid UTF-8 strings. The resulting string may contain different characters than the input strings, and its number of characters may be lower than sum of numbers of characters of the concatenated strings, e.g.:</p><pre><code class="language-julia-repl hljs">julia&gt; a, b = &quot;\xe2\x88&quot;, &quot;\x80&quot;
(&quot;\xe2\x88&quot;, &quot;\x80&quot;)

julia&gt; c = string(a, b)
&quot;∀&quot;

julia&gt; collect.([a, b, c])
3-element Vector{Vector{Char}}:
 [&#39;\xe2\x88&#39;]
 [&#39;\x80&#39;]
 [&#39;∀&#39;]

julia&gt; length.([a, b, c])
3-element Vector{Int64}:
 1
 1
 1</code></pre><p>This situation can happen only for invalid UTF-8 strings. For valid UTF-8 strings concatenation preserves all characters in strings and additivity of string lengths.</p><p>Julia also provides <a href="../base/math.html#Base.:*-Tuple{Any, Vararg{Any}}"><code>*</code></a> for string concatenation:</p><pre><code class="language-julia-repl hljs">julia&gt; greet * &quot;, &quot; * whom * &quot;.\n&quot;
&quot;Hello, world.\n&quot;</code></pre><p>While <code>*</code> may seem like a surprising choice to users of languages that provide <code>+</code> for string concatenation, this use of <code>*</code> has precedent in mathematics, particularly in abstract algebra.</p><p>In mathematics, <code>+</code> usually denotes a <em>commutative</em> operation, where the order of the operands does not matter. An example of this is matrix addition, where <code>A + B == B + A</code> for any matrices <code>A</code> and <code>B</code> that have the same shape. In contrast, <code>*</code> typically denotes a <em>noncommutative</em> operation, where the order of the operands <em>does</em> matter. An example of this is matrix multiplication, where in general <code>A * B != B * A</code>. As with matrix multiplication, string concatenation is noncommutative: <code>greet * whom != whom * greet</code>. As such, <code>*</code> is a more natural choice for an infix string concatenation operator, consistent with common mathematical use.</p><p>More precisely, the set of all finite-length strings <em>S</em> together with the string concatenation operator <code>*</code> forms a <a href="https://en.wikipedia.org/wiki/Free_monoid">free monoid</a> (<em>S</em>, <code>*</code>). The identity element of this set is the empty string, <code>&quot;&quot;</code>. Whenever a free monoid is not commutative, the operation is typically represented as <code>\cdot</code>, <code>*</code>, or a similar symbol, rather than <code>+</code>, which as stated usually implies commutativity.</p><h2 id="string-interpolation"><a class="docs-heading-anchor" href="#string-interpolation">Interpolation</a><a id="string-interpolation-1"></a><a class="docs-heading-anchor-permalink" href="#string-interpolation" title="Permalink"></a></h2><p>Constructing strings using concatenation can become a bit cumbersome, however. To reduce the need for these verbose calls to <a href="../base/strings.html#Base.string"><code>string</code></a> or repeated multiplications, Julia allows interpolation into string literals using <code>$</code>, as in Perl:</p><pre><code class="language-julia-repl hljs">julia&gt; greet = &quot;Hello&quot;; whom = &quot;world&quot;;

julia&gt; &quot;$greet, $whom.\n&quot;
&quot;Hello, world.\n&quot;</code></pre><p>This is more readable and convenient and equivalent to the above string concatenation – the system rewrites this apparent single string literal into the call <code>string(greet, &quot;, &quot;, whom, &quot;.\n&quot;)</code>.</p><p>The shortest complete expression after the <code>$</code> is taken as the expression whose value is to be interpolated into the string. Thus, you can interpolate any expression into a string using parentheses:</p><pre><code class="language-julia-repl hljs">julia&gt; &quot;1 + 2 = $(1 + 2)&quot;
&quot;1 + 2 = 3&quot;</code></pre><p>Both concatenation and string interpolation call <a href="../base/strings.html#Base.string"><code>string</code></a> to convert objects into string form. However, <code>string</code> actually just returns the output of <a href="../base/io-network.html#Base.print"><code>print</code></a>, so new types should add methods to <a href="../base/io-network.html#Base.print"><code>print</code></a> or <a href="../base/io-network.html#Base.show-Tuple{IO, Any}"><code>show</code></a> instead of <code>string</code>.</p><p>Most non-<code>AbstractString</code> objects are converted to strings closely corresponding to how they are entered as literal expressions:</p><pre><code class="language-julia-repl hljs">julia&gt; v = [1,2,3]
3-element Vector{Int64}:
 1
 2
 3

julia&gt; &quot;v: $v&quot;
&quot;v: [1, 2, 3]&quot;</code></pre><p><a href="../base/strings.html#Base.string"><code>string</code></a> is the identity for <code>AbstractString</code> and <code>AbstractChar</code> values, so these are interpolated into strings as themselves, unquoted and unescaped:</p><pre><code class="language-julia-repl hljs">julia&gt; c = &#39;x&#39;
&#39;x&#39;: ASCII/Unicode U+0078 (category Ll: Letter, lowercase)

julia&gt; &quot;hi, $c&quot;
&quot;hi, x&quot;</code></pre><p>To include a literal <code>$</code> in a string literal, escape it with a backslash:</p><pre><code class="language-julia-repl hljs">julia&gt; print(&quot;I have \$100 in my account.\n&quot;)
I have $100 in my account.</code></pre><h2 id="Triple-Quoted-String-Literals"><a class="docs-heading-anchor" href="#Triple-Quoted-String-Literals">Triple-Quoted String Literals</a><a id="Triple-Quoted-String-Literals-1"></a><a class="docs-heading-anchor-permalink" href="#Triple-Quoted-String-Literals" title="Permalink"></a></h2><p>When strings are created using triple-quotes (<code>&quot;&quot;&quot;...&quot;&quot;&quot;</code>) they have some special behavior that can be useful for creating longer blocks of text.</p><p>First, triple-quoted strings are also dedented to the level of the least-indented line. This is useful for defining strings within code that is indented. For example:</p><pre><code class="language-julia-repl hljs">julia&gt; str = &quot;&quot;&quot;
           Hello,
           world.
         &quot;&quot;&quot;
&quot;  Hello,\n  world.\n&quot;</code></pre><p>In this case the final (empty) line before the closing <code>&quot;&quot;&quot;</code> sets the indentation level.</p><p>The dedentation level is determined as the longest common starting sequence of spaces or tabs in all lines, excluding the line following the opening <code>&quot;&quot;&quot;</code> and lines containing only spaces or tabs (the line containing the closing <code>&quot;&quot;&quot;</code> is always included). Then for all lines, excluding the text following the opening <code>&quot;&quot;&quot;</code>, the common starting sequence is removed (including lines containing only spaces and tabs if they start with this sequence), e.g.:</p><pre><code class="language-julia-repl hljs">julia&gt; &quot;&quot;&quot;    This
         is
           a test&quot;&quot;&quot;
&quot;    This\nis\n  a test&quot;</code></pre><p>Next, if the opening <code>&quot;&quot;&quot;</code> is followed by a newline, the newline is stripped from the resulting string.</p><pre><code class="language-julia hljs">&quot;&quot;&quot;hello&quot;&quot;&quot;</code></pre><p>is equivalent to</p><pre><code class="language-julia hljs">&quot;&quot;&quot;
hello&quot;&quot;&quot;</code></pre><p>but</p><pre><code class="language-julia hljs">&quot;&quot;&quot;

hello&quot;&quot;&quot;</code></pre><p>will contain a literal newline at the beginning.</p><p>Stripping of the newline is performed after the dedentation. For example:</p><pre><code class="language-julia-repl hljs">julia&gt; &quot;&quot;&quot;
         Hello,
         world.&quot;&quot;&quot;
&quot;Hello,\nworld.&quot;</code></pre><p>If the newline is removed using a backslash, dedentation will be respected as well:</p><pre><code class="language-julia-repl hljs">julia&gt; &quot;&quot;&quot;
         Averylong\
         word&quot;&quot;&quot;
&quot;Averylongword&quot;</code></pre><p>Trailing whitespace is left unaltered.</p><p>Triple-quoted string literals can contain <code>&quot;</code> characters without escaping.</p><p>Note that line breaks in literal strings, whether single- or triple-quoted, result in a newline (LF) character <code>\n</code> in the string, even if your editor uses a carriage return <code>\r</code> (CR) or CRLF combination to end lines. To include a CR in a string, use an explicit escape <code>\r</code>; for example, you can enter the literal string <code>&quot;a CRLF line ending\r\n&quot;</code>.</p><h2 id="Common-Operations"><a class="docs-heading-anchor" href="#Common-Operations">Common Operations</a><a id="Common-Operations-1"></a><a class="docs-heading-anchor-permalink" href="#Common-Operations" title="Permalink"></a></h2><p>You can lexicographically compare strings using the standard comparison operators:</p><pre><code class="language-julia-repl hljs">julia&gt; &quot;abracadabra&quot; &lt; &quot;xylophone&quot;
true

julia&gt; &quot;abracadabra&quot; == &quot;xylophone&quot;
false

julia&gt; &quot;Hello, world.&quot; != &quot;Goodbye, world.&quot;
true

julia&gt; &quot;1 + 2 = 3&quot; == &quot;1 + 2 = $(1 + 2)&quot;
true</code></pre><p>You can search for the index of a particular character using the <a href="../base/arrays.html#Base.findfirst-Tuple{Any}"><code>findfirst</code></a> and <a href="../base/arrays.html#Base.findlast-Tuple{Any}"><code>findlast</code></a> functions:</p><pre><code class="language-julia-repl hljs">julia&gt; findfirst(&#39;o&#39;, &quot;xylophone&quot;)
4

julia&gt; findlast(&#39;o&#39;, &quot;xylophone&quot;)
7

julia&gt; findfirst(&#39;z&#39;, &quot;xylophone&quot;)</code></pre><p>You can start the search for a character at a given offset by using the functions <a href="../base/arrays.html#Base.findnext-Tuple{Any, Integer}"><code>findnext</code></a> and <a href="../base/arrays.html#Base.findprev-Tuple{Any, Integer}"><code>findprev</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; findnext(&#39;o&#39;, &quot;xylophone&quot;, 1)
4

julia&gt; findnext(&#39;o&#39;, &quot;xylophone&quot;, 5)
7

julia&gt; findprev(&#39;o&#39;, &quot;xylophone&quot;, 5)
4

julia&gt; findnext(&#39;o&#39;, &quot;xylophone&quot;, 8)</code></pre><p>You can use the <a href="../base/strings.html#Base.occursin"><code>occursin</code></a> function to check if a substring is found within a string:</p><pre><code class="language-julia-repl hljs">julia&gt; occursin(&quot;world&quot;, &quot;Hello, world.&quot;)
true

julia&gt; occursin(&quot;o&quot;, &quot;Xylophon&quot;)
true

julia&gt; occursin(&quot;a&quot;, &quot;Xylophon&quot;)
false

julia&gt; occursin(&#39;o&#39;, &quot;Xylophon&quot;)
true</code></pre><p>The last example shows that <a href="../base/strings.html#Base.occursin"><code>occursin</code></a> can also look for a character literal.</p><p>Two other handy string functions are <a href="../base/arrays.html#Base.repeat"><code>repeat</code></a> and <a href="../base/strings.html#Base.join"><code>join</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; repeat(&quot;.:Z:.&quot;, 10)
&quot;.:Z:..:Z:..:Z:..:Z:..:Z:..:Z:..:Z:..:Z:..:Z:..:Z:.&quot;

julia&gt; join([&quot;apples&quot;, &quot;bananas&quot;, &quot;pineapples&quot;], &quot;, &quot;, &quot; and &quot;)
&quot;apples, bananas and pineapples&quot;</code></pre><p>Some other useful functions include:</p><ul><li><a href="../base/collections.html#Base.firstindex"><code>firstindex(str)</code></a> gives the minimal (byte) index that can be used to index into <code>str</code> (always 1 for strings, not necessarily true for other containers).</li><li><a href="../base/collections.html#Base.lastindex"><code>lastindex(str)</code></a> gives the maximal (byte) index that can be used to index into <code>str</code>.</li><li><a href="../base/arrays.html#Base.length-Tuple{AbstractArray}"><code>length(str)</code></a> the number of characters in <code>str</code>.</li><li><a href="../base/arrays.html#Base.length-Tuple{AbstractArray}"><code>length(str, i, j)</code></a> the number of valid character indices in <code>str</code> from <code>i</code> to <code>j</code>.</li><li><a href="../base/strings.html#Base.ncodeunits-Tuple{AbstractString}"><code>ncodeunits(str)</code></a> number of <a href="https://en.wikipedia.org/wiki/Character_encoding#Terminology">code units</a> in a string.</li><li><a href="../base/strings.html#Base.codeunit"><code>codeunit(str, i)</code></a> gives the code unit value in the string <code>str</code> at index <code>i</code>.</li><li><a href="../base/strings.html#Base.thisind"><code>thisind(str, i)</code></a> given an arbitrary index into a string find the first index of the character into which the index points.</li><li><a href="../base/arrays.html#Base.nextind"><code>nextind(str, i, n=1)</code></a> find the start of the <code>n</code>th character starting after index <code>i</code>.</li><li><a href="../base/arrays.html#Base.prevind"><code>prevind(str, i, n=1)</code></a> find the start of the <code>n</code>th character starting before index <code>i</code>.</li></ul><h2 id="non-standard-string-literals"><a class="docs-heading-anchor" href="#non-standard-string-literals">Non-Standard String Literals</a><a id="non-standard-string-literals-1"></a><a class="docs-heading-anchor-permalink" href="#non-standard-string-literals" title="Permalink"></a></h2><p>There are situations when you want to construct a string or use string semantics, but the behavior of the standard string construct is not quite what is needed. For these kinds of situations, Julia provides non-standard string literals. A non-standard string literal looks like a regular double-quoted string literal, but is immediately prefixed by an identifier, and may behave differently from a normal string literal.</p><p><a href="strings.html#man-regex-literals">Regular expressions</a>, <a href="strings.html#man-byte-array-literals">byte array literals</a>, and <a href="strings.html#man-version-number-literals">version number literals</a>, as described below, are some examples of non-standard string literals. Users and packages may also define new non-standard string literals. Further documentation is given in the <a href="metaprogramming.html#meta-non-standard-string-literals">Metaprogramming</a> section.</p><h2 id="man-regex-literals"><a class="docs-heading-anchor" href="#man-regex-literals">Regular Expressions</a><a id="man-regex-literals-1"></a><a class="docs-heading-anchor-permalink" href="#man-regex-literals" title="Permalink"></a></h2><p>Sometimes you are not looking for an exact string, but a particular <em>pattern</em>. For example, suppose you are trying to extract a single date from a large text file. You don’t know what that date is (that’s why you are searching for it), but you do know it will look something like <code>YYYY-MM-DD</code>. Regular expressions allow you to specify these patterns and search for them.</p><p>Julia uses version 2 of Perl-compatible regular expressions (regexes), as provided by the <a href="https://www.pcre.org/">PCRE</a> library (see the <a href="https://www.pcre.org/current/doc/html/pcre2syntax.html">PCRE2 syntax description</a> for more details). Regular expressions are related to strings in two ways: the obvious connection is that regular expressions are used to find regular patterns in strings; the other connection is that regular expressions are themselves input as strings, which are parsed into a state machine that can be used to efficiently search for patterns in strings. In Julia, regular expressions are input using non-standard string literals prefixed with various identifiers beginning with <code>r</code>. The most basic regular expression literal without any options turned on just uses <code>r&quot;...&quot;</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; re = r&quot;^\s*(?:#|$)&quot;
r&quot;^\s*(?:#|$)&quot;

julia&gt; typeof(re)
Regex</code></pre><p>To check if a regex matches a string, use <a href="../base/strings.html#Base.occursin"><code>occursin</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; occursin(r&quot;^\s*(?:#|$)&quot;, &quot;not a comment&quot;)
false

julia&gt; occursin(r&quot;^\s*(?:#|$)&quot;, &quot;# a comment&quot;)
true</code></pre><p>As one can see here, <a href="../base/strings.html#Base.occursin"><code>occursin</code></a> simply returns true or false, indicating whether a match for the given regex occurs in the string. Commonly, however, one wants to know not just whether a string matched, but also <em>how</em> it matched. To capture this information about a match, use the <a href="../base/strings.html#Base.match"><code>match</code></a> function instead:</p><pre><code class="language-julia-repl hljs">julia&gt; match(r&quot;^\s*(?:#|$)&quot;, &quot;not a comment&quot;)

julia&gt; match(r&quot;^\s*(?:#|$)&quot;, &quot;# a comment&quot;)
RegexMatch(&quot;#&quot;)</code></pre><p>If the regular expression does not match the given string, <a href="../base/strings.html#Base.match"><code>match</code></a> returns <a href="../base/constants.html#Core.nothing"><code>nothing</code></a> – a special value that does not print anything at the interactive prompt. Other than not printing, it is a completely normal value and you can test for it programmatically:</p><pre><code class="language-julia hljs">m = match(r&quot;^\s*(?:#|$)&quot;, line)
if m === nothing
    println(&quot;not a comment&quot;)
else
    println(&quot;blank or comment&quot;)
end</code></pre><p>If a regular expression does match, the value returned by <a href="../base/strings.html#Base.match"><code>match</code></a> is a <a href="../base/strings.html#Base.RegexMatch"><code>RegexMatch</code></a> object. These objects record how the expression matches, including the substring that the pattern matches and any captured substrings, if there are any. This example only captures the portion of the substring that matches, but perhaps we want to capture any non-blank text after the comment character. We could do the following:</p><pre><code class="language-julia-repl hljs">julia&gt; m = match(r&quot;^\s*(?:#\s*(.*?)\s*$)&quot;, &quot;# a comment &quot;)
RegexMatch(&quot;# a comment &quot;, 1=&quot;a comment&quot;)</code></pre><p>When calling <a href="../base/strings.html#Base.match"><code>match</code></a>, you have the option to specify an index at which to start the search. For example:</p><pre><code class="language-julia-repl hljs">julia&gt; m = match(r&quot;[0-9]&quot;,&quot;aaaa1aaaa2aaaa3&quot;,1)
RegexMatch(&quot;1&quot;)

julia&gt; m = match(r&quot;[0-9]&quot;,&quot;aaaa1aaaa2aaaa3&quot;,6)
RegexMatch(&quot;2&quot;)

julia&gt; m = match(r&quot;[0-9]&quot;,&quot;aaaa1aaaa2aaaa3&quot;,11)
RegexMatch(&quot;3&quot;)</code></pre><p>You can extract the following info from a <code>RegexMatch</code> object:</p><ul><li>the entire substring matched: <code>m.match</code></li><li>the captured substrings as an array of strings: <code>m.captures</code></li><li>the offset at which the whole match begins: <code>m.offset</code></li><li>the offsets of the captured substrings as a vector: <code>m.offsets</code></li></ul><p>For when a capture doesn&#39;t match, instead of a substring, <code>m.captures</code> contains <code>nothing</code> in that position, and <code>m.offsets</code> has a zero offset (recall that indices in Julia are 1-based, so a zero offset into a string is invalid). Here is a pair of somewhat contrived examples:</p><pre><code class="language-julia-repl hljs">julia&gt; m = match(r&quot;(a|b)(c)?(d)&quot;, &quot;acd&quot;)
RegexMatch(&quot;acd&quot;, 1=&quot;a&quot;, 2=&quot;c&quot;, 3=&quot;d&quot;)

julia&gt; m.match
&quot;acd&quot;

julia&gt; m.captures
3-element Vector{Union{Nothing, SubString{String}}}:
 &quot;a&quot;
 &quot;c&quot;
 &quot;d&quot;

julia&gt; m.offset
1

julia&gt; m.offsets
3-element Vector{Int64}:
 1
 2
 3

julia&gt; m = match(r&quot;(a|b)(c)?(d)&quot;, &quot;ad&quot;)
RegexMatch(&quot;ad&quot;, 1=&quot;a&quot;, 2=nothing, 3=&quot;d&quot;)

julia&gt; m.match
&quot;ad&quot;

julia&gt; m.captures
3-element Vector{Union{Nothing, SubString{String}}}:
 &quot;a&quot;
 nothing
 &quot;d&quot;

julia&gt; m.offset
1

julia&gt; m.offsets
3-element Vector{Int64}:
 1
 0
 2</code></pre><p>It is convenient to have captures returned as an array so that one can use destructuring syntax to bind them to local variables. As a convenience, the <code>RegexMatch</code> object implements iterator methods that pass through to the <code>captures</code> field, so you can destructure the match object directly:</p><pre><code class="language-julia-repl hljs">julia&gt; first, second, third = m; first
&quot;a&quot;</code></pre><p>Captures can also be accessed by indexing the <code>RegexMatch</code> object with the number or name of the capture group:</p><pre><code class="language-julia-repl hljs">julia&gt; m=match(r&quot;(?&lt;hour&gt;\d+):(?&lt;minute&gt;\d+)&quot;,&quot;12:45&quot;)
RegexMatch(&quot;12:45&quot;, hour=&quot;12&quot;, minute=&quot;45&quot;)

julia&gt; m[:minute]
&quot;45&quot;

julia&gt; m[2]
&quot;45&quot;</code></pre><p>Captures can be referenced in a substitution string when using <a href="../base/collections.html#Base.replace-Tuple{Any, Vararg{Pair}}"><code>replace</code></a> by using <code>\n</code> to refer to the nth capture group and prefixing the substitution string with <code>s</code>. Capture group 0 refers to the entire match object. Named capture groups can be referenced in the substitution with <code>\g&lt;groupname&gt;</code>. For example:</p><pre><code class="language-julia-repl hljs">julia&gt; replace(&quot;first second&quot;, r&quot;(\w+) (?&lt;agroup&gt;\w+)&quot; =&gt; s&quot;\g&lt;agroup&gt; \1&quot;)
&quot;second first&quot;</code></pre><p>Numbered capture groups can also be referenced as <code>\g&lt;n&gt;</code> for disambiguation, as in:</p><pre><code class="language-julia-repl hljs">julia&gt; replace(&quot;a&quot;, r&quot;.&quot; =&gt; s&quot;\g&lt;0&gt;1&quot;)
&quot;a1&quot;</code></pre><p>You can modify the behavior of regular expressions by some combination of the flags <code>i</code>, <code>m</code>, <code>s</code>, and <code>x</code> after the closing double quote mark. These flags have the same meaning as they do in Perl, as explained in this excerpt from the <a href="https://perldoc.perl.org/perlre#Modifiers">perlre manpage</a>:</p><pre><code class="nohighlight hljs">i   Do case-insensitive pattern matching.

    If locale matching rules are in effect, the case map is taken
    from the current locale for code points less than 255, and
    from Unicode rules for larger code points. However, matches
    that would cross the Unicode rules/non-Unicode rules boundary
    (ords 255/256) will not succeed.

m   Treat string as multiple lines.  That is, change &quot;^&quot; and &quot;$&quot;
    from matching the start or end of the string to matching the
    start or end of any line anywhere within the string.

s   Treat string as single line.  That is, change &quot;.&quot; to match any
    character whatsoever, even a newline, which normally it would
    not match.

    Used together, as r&quot;&quot;ms, they let the &quot;.&quot; match any character
    whatsoever, while still allowing &quot;^&quot; and &quot;$&quot; to match,
    respectively, just after and just before newlines within the
    string.

x   Tells the regular expression parser to ignore most whitespace
    that is neither backslashed nor within a character class. You
    can use this to break up your regular expression into
    (slightly) more readable parts. The &#39;#&#39; character is also
    treated as a metacharacter introducing a comment, just as in
    ordinary code.</code></pre><p>For example, the following regex has all three flags turned on:</p><pre><code class="language-julia-repl hljs">julia&gt; r&quot;a+.*b+.*d$&quot;ism
r&quot;a+.*b+.*d$&quot;ims

julia&gt; match(r&quot;a+.*b+.*d$&quot;ism, &quot;Goodbye,\nOh, angry,\nBad world\n&quot;)
RegexMatch(&quot;angry,\nBad world&quot;)</code></pre><p>The <code>r&quot;...&quot;</code> literal is constructed without interpolation and unescaping (except for quotation mark <code>&quot;</code> which still has to be escaped). Here is an example showing the difference from standard string literals:</p><pre><code class="language-julia-repl hljs">julia&gt; x = 10
10

julia&gt; r&quot;$x&quot;
r&quot;$x&quot;

julia&gt; &quot;$x&quot;
&quot;10&quot;

julia&gt; r&quot;\x&quot;
r&quot;\x&quot;

julia&gt; &quot;\x&quot;
ERROR: syntax: invalid escape sequence</code></pre><p>Triple-quoted regex strings, of the form <code>r&quot;&quot;&quot;...&quot;&quot;&quot;</code>, are also supported (and may be convenient for regular expressions containing quotation marks or newlines).</p><p>The <code>Regex()</code> constructor may be used to create a valid regex string programmatically.  This permits using the contents of string variables and other string operations when constructing the regex string. Any of the regex codes above can be used within the single string argument to <code>Regex()</code>. Here are some examples:</p><pre><code class="language-julia-repl hljs">julia&gt; using Dates

julia&gt; d = Date(1962,7,10)
1962-07-10

julia&gt; regex_d = Regex(&quot;Day &quot; * string(day(d)))
r&quot;Day 10&quot;

julia&gt; match(regex_d, &quot;It happened on Day 10&quot;)
RegexMatch(&quot;Day 10&quot;)

julia&gt; name = &quot;Jon&quot;
&quot;Jon&quot;

julia&gt; regex_name = Regex(&quot;[\&quot;( ]\\Q$name\\E[\&quot;) ]&quot;)  # interpolate value of name
r&quot;[\&quot;( ]\QJon\E[\&quot;) ]&quot;

julia&gt; match(regex_name, &quot; Jon &quot;)
RegexMatch(&quot; Jon &quot;)

julia&gt; match(regex_name, &quot;[Jon]&quot;) === nothing
true</code></pre><p>Note the use of the <code>\Q...\E</code> escape sequence. All characters between the <code>\Q</code> and the <code>\E</code> are interpreted as literal characters. This is convenient for matching characters that would otherwise be regex metacharacters. However, caution is needed when using this feature together with string interpolation, since the interpolated string might itself contain the <code>\E</code> sequence, unexpectedly terminating literal matching. User inputs need to be sanitized before inclusion in a regex.</p><h2 id="man-byte-array-literals"><a class="docs-heading-anchor" href="#man-byte-array-literals">Byte Array Literals</a><a id="man-byte-array-literals-1"></a><a class="docs-heading-anchor-permalink" href="#man-byte-array-literals" title="Permalink"></a></h2><p>Another useful non-standard string literal is the byte-array string literal: <code>b&quot;...&quot;</code>. This form lets you use string notation to express read only literal byte arrays – i.e. arrays of <a href="../base/numbers.html#Core.UInt8"><code>UInt8</code></a> values. The type of those objects is <code>CodeUnits{UInt8, String}</code>. The rules for byte array literals are the following:</p><ul><li>ASCII characters and ASCII escapes produce a single byte.</li><li><code>\x</code> and octal escape sequences produce the <em>byte</em> corresponding to the escape value.</li><li>Unicode escape sequences produce a sequence of bytes encoding that code point in UTF-8.</li></ul><p>There is some overlap between these rules since the behavior of <code>\x</code> and octal escapes less than 0x80 (128) are covered by both of the first two rules, but here these rules agree. Together, these rules allow one to easily use ASCII characters, arbitrary byte values, and UTF-8 sequences to produce arrays of bytes. Here is an example using all three:</p><pre><code class="language-julia-repl hljs">julia&gt; b&quot;DATA\xff\u2200&quot;
8-element Base.CodeUnits{UInt8, String}:
 0x44
 0x41
 0x54
 0x41
 0xff
 0xe2
 0x88
 0x80</code></pre><p>The ASCII string &quot;DATA&quot; corresponds to the bytes 68, 65, 84, 65. <code>\xff</code> produces the single byte 255. The Unicode escape <code>\u2200</code> is encoded in UTF-8 as the three bytes 226, 136, 128. Note that the resulting byte array does not correspond to a valid UTF-8 string:</p><pre><code class="language-julia-repl hljs">julia&gt; isvalid(&quot;DATA\xff\u2200&quot;)
false</code></pre><p>As it was mentioned <code>CodeUnits{UInt8, String}</code> type behaves like read only array of <code>UInt8</code> and if you need a standard vector you can convert it using <code>Vector{UInt8}</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; x = b&quot;123&quot;
3-element Base.CodeUnits{UInt8, String}:
 0x31
 0x32
 0x33

julia&gt; x[1]
0x31

julia&gt; x[1] = 0x32
ERROR: CanonicalIndexError: setindex! not defined for Base.CodeUnits{UInt8, String}
[...]

julia&gt; Vector{UInt8}(x)
3-element Vector{UInt8}:
 0x31
 0x32
 0x33</code></pre><p>Also observe the significant distinction between <code>\xff</code> and <code>\uff</code>: the former escape sequence encodes the <em>byte 255</em>, whereas the latter escape sequence represents the <em>code point 255</em>, which is encoded as two bytes in UTF-8:</p><pre><code class="language-julia-repl hljs">julia&gt; b&quot;\xff&quot;
1-element Base.CodeUnits{UInt8, String}:
 0xff

julia&gt; b&quot;\uff&quot;
2-element Base.CodeUnits{UInt8, String}:
 0xc3
 0xbf</code></pre><p>Character literals use the same behavior.</p><p>For code points less than <code>\u80</code>, it happens that the UTF-8 encoding of each code point is just the single byte produced by the corresponding <code>\x</code> escape, so the distinction can safely be ignored. For the escapes <code>\x80</code> through <code>\xff</code> as compared to <code>\u80</code> through <code>\uff</code>, however, there is a major difference: the former escapes all encode single bytes, which – unless followed by very specific continuation bytes – do not form valid UTF-8 data, whereas the latter escapes all represent Unicode code points with two-byte encodings.</p><p>If this is all extremely confusing, try reading <a href="https://www.joelonsoftware.com/2003/10/08/the-absolute-minimum-every-software-developer-absolutely-positively-must-know-about-unicode-and-character-sets-no-excuses/">&quot;The Absolute Minimum Every Software Developer Absolutely, Positively Must Know About Unicode and Character Sets&quot;</a>. It&#39;s an excellent introduction to Unicode and UTF-8, and may help alleviate some confusion regarding the matter.</p><h2 id="man-version-number-literals"><a class="docs-heading-anchor" href="#man-version-number-literals">Version Number Literals</a><a id="man-version-number-literals-1"></a><a class="docs-heading-anchor-permalink" href="#man-version-number-literals" title="Permalink"></a></h2><p>Version numbers can easily be expressed with non-standard string literals of the form <a href="../base/base.html#Base.@v_str"><code>v&quot;...&quot;</code></a>. Version number literals create <a href="../base/base.html#Base.VersionNumber"><code>VersionNumber</code></a> objects which follow the specifications of <a href="https://semver.org/">semantic versioning</a>, and therefore are composed of major, minor and patch numeric values, followed by pre-release and build alphanumeric annotations. For example, <code>v&quot;0.2.1-rc1+win64&quot;</code> is broken into major version <code>0</code>, minor version <code>2</code>, patch version <code>1</code>, pre-release <code>rc1</code> and build <code>win64</code>. When entering a version literal, everything except the major version number is optional, therefore e.g.  <code>v&quot;0.2&quot;</code> is equivalent to <code>v&quot;0.2.0&quot;</code> (with empty pre-release/build annotations), <code>v&quot;2&quot;</code> is equivalent to <code>v&quot;2.0.0&quot;</code>, and so on.</p><p><code>VersionNumber</code> objects are mostly useful to easily and correctly compare two (or more) versions. For example, the constant <a href="../base/constants.html#Base.VERSION"><code>VERSION</code></a> holds Julia version number as a <code>VersionNumber</code> object, and therefore one can define some version-specific behavior using simple statements as:</p><pre><code class="language-julia hljs">if v&quot;0.2&quot; &lt;= VERSION &lt; v&quot;0.3-&quot;
    # do something specific to 0.2 release series
end</code></pre><p>Note that in the above example the non-standard version number <code>v&quot;0.3-&quot;</code> is used, with a trailing <code>-</code>: this notation is a Julia extension of the standard, and it&#39;s used to indicate a version which is lower than any <code>0.3</code> release, including all of its pre-releases. So in the above example the code would only run with stable <code>0.2</code> versions, and exclude such versions as <code>v&quot;0.3.0-rc1&quot;</code>. In order to also allow for unstable (i.e. pre-release) <code>0.2</code> versions, the lower bound check should be modified like this: <code>v&quot;0.2-&quot; &lt;= VERSION</code>.</p><p>Another non-standard version specification extension allows one to use a trailing <code>+</code> to express an upper limit on build versions, e.g.  <code>VERSION &gt; v&quot;0.2-rc1+&quot;</code> can be used to mean any version above <code>0.2-rc1</code> and any of its builds: it will return <code>false</code> for version <code>v&quot;0.2-rc1+win64&quot;</code> and <code>true</code> for <code>v&quot;0.2-rc2&quot;</code>.</p><p>It is good practice to use such special versions in comparisons (particularly, the trailing <code>-</code> should always be used on upper bounds unless there&#39;s a good reason not to), but they must not be used as the actual version number of anything, as they are invalid in the semantic versioning scheme.</p><p>Besides being used for the <a href="../base/constants.html#Base.VERSION"><code>VERSION</code></a> constant, <code>VersionNumber</code> objects are widely used in the <code>Pkg</code> module, to specify packages versions and their dependencies.</p><h2 id="man-raw-string-literals"><a class="docs-heading-anchor" href="#man-raw-string-literals">Raw String Literals</a><a id="man-raw-string-literals-1"></a><a class="docs-heading-anchor-permalink" href="#man-raw-string-literals" title="Permalink"></a></h2><p>Raw strings without interpolation or unescaping can be expressed with non-standard string literals of the form <code>raw&quot;...&quot;</code>. Raw string literals create ordinary <code>String</code> objects which contain the enclosed contents exactly as entered with no interpolation or unescaping. This is useful for strings which contain code or markup in other languages which use <code>$</code> or <code>\</code> as special characters.</p><p>The exception is that quotation marks still must be escaped, e.g. <code>raw&quot;\&quot;&quot;</code> is equivalent to <code>&quot;\&quot;&quot;</code>. To make it possible to express all strings, backslashes then also must be escaped, but only when appearing right before a quote character:</p><pre><code class="language-julia-repl hljs">julia&gt; println(raw&quot;\\ \\\&quot;&quot;)
\\ \&quot;</code></pre><p>Notice that the first two backslashes appear verbatim in the output, since they do not precede a quote character. However, the next backslash character escapes the backslash that follows it, and the last backslash escapes a quote, since these backslashes appear before a quote.</p><h2 id="man-annotated-strings"><a class="docs-heading-anchor" href="#man-annotated-strings">Annotated Strings</a><a id="man-annotated-strings-1"></a><a class="docs-heading-anchor-permalink" href="#man-annotated-strings" title="Permalink"></a></h2><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The API for AnnotatedStrings is considered experimental and is subject to change between Julia versions.</p></div></div><p>It is sometimes useful to be able to hold metadata relating to regions of a string. A <a href="../base/strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a> wraps another string and allows for regions of it to be annotated with labelled values (<code>:label =&gt; value</code>). All generic string operations are applied to the underlying string. However, when possible, styling information is preserved. This means you can manipulate a <a href="../base/strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a> —taking substrings, padding them, concatenating them with other strings— and the metadata annotations will &quot;come along for the ride&quot;.</p><p>This string type is fundamental to the <a href="../stdlib/StyledStrings.html#stdlib-styledstrings">StyledStrings stdlib</a>, which uses <code>:face</code>-labelled annotations to hold styling information.</p><p>When concatenating a <a href="../base/strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a>, take care to use <a href="../base/strings.html#Base.annotatedstring"><code>annotatedstring</code></a> instead of <a href="../base/strings.html#Base.string"><code>string</code></a> if you want to keep the string annotations.</p><pre><code class="language-julia-repl hljs">julia&gt; str = Base.AnnotatedString(&quot;hello there&quot;,
               [(1:5, :word, :greeting), (7:11, :label, 1)])
&quot;hello there&quot;

julia&gt; length(str)
11

julia&gt; lpad(str, 14)
&quot;   hello there&quot;

julia&gt; typeof(lpad(str, 7))
Base.AnnotatedString{String}

julia&gt; str2 = Base.AnnotatedString(&quot; julia&quot;, [(2:6, :face, :magenta)])
&quot; julia&quot;

julia&gt; Base.annotatedstring(str, str2)
&quot;hello there julia&quot;

julia&gt; str * str2 == Base.annotatedstring(str, str2) # *-concatenation still works
true</code></pre><p>The annotations of a <a href="../base/strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a> can be accessed and modified via the <a href="../base/strings.html#Base.annotations"><code>annotations</code></a> and <a href="../base/strings.html#Base.annotate!"><code>annotate!</code></a> functions.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="complex-and-rational-numbers.html">« Complex and Rational Numbers</a><a class="docs-footer-nextpage" href="functions.html">Functions »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
