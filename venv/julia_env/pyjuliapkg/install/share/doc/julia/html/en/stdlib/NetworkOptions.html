<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Network Options · The Julia Language</title><meta name="title" content="Network Options · The Julia Language"/><meta property="og:title" content="Network Options · The Julia Language"/><meta property="twitter:title" content="Network Options · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li class="is-active"><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="NetworkOptions.html">Network Options</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="NetworkOptions.html">Network Options</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/NetworkOptions.jl/blob/master/docs/src/index.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Network-Options"><a class="docs-heading-anchor" href="#Network-Options">Network Options</a><a id="Network-Options-1"></a><a class="docs-heading-anchor-permalink" href="#Network-Options" title="Permalink"></a></h1><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="NetworkOptions.ca_roots" href="#NetworkOptions.ca_roots"><code>NetworkOptions.ca_roots</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ca_roots() :: Union{Nothing, String}</code></pre><p>The <code>ca_roots()</code> function tells the caller where, if anywhere, to find a file or directory of PEM-encoded certificate authority roots. By default, on systems like Windows and macOS where the built-in TLS engines know how to verify hosts using the system&#39;s built-in certificate verification mechanism, this function will return <code>nothing</code>. On classic UNIX systems (excluding macOS), root certificates are typically stored in a file in <code>/etc</code>: the common places for the current UNIX system will be searched and if one of these paths exists, it will be returned; if none of these typical root certificate paths exist, then the path to the set of root certificates that are bundled with Julia is returned.</p><p>The default value returned by <code>ca_roots()</code> may be overridden by setting the <code>JULIA_SSL_CA_ROOTS_PATH</code>, <code>SSL_CERT_DIR</code>, or <code>SSL_CERT_FILE</code> environment variables, in which case this function will always return the value of the first of these variables that is set (whether the path exists or not). If <code>JULIA_SSL_CA_ROOTS_PATH</code> is set to the empty string, then the other variables are ignored (as if unset); if the other variables are set to the empty string, they behave is if they are not set.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/NetworkOptions.jl/blob/aab83e5dd900c874826d430e25158dff43559d78/src/ca_roots.jl#L3-L23">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="NetworkOptions.ca_roots_path" href="#NetworkOptions.ca_roots_path"><code>NetworkOptions.ca_roots_path</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ca_roots_path() :: String</code></pre><p>The <code>ca_roots_path()</code> function is similar to the <code>ca_roots()</code> function except that it always returns a path to a file or directory of PEM-encoded certificate authority roots. When called on a system like Windows or macOS, where system root certificates are not stored in the file system, it will currently return the path to the set of root certificates that are bundled with Julia. (In the future, this function may instead extract the root certificates from the system and save them to a file whose path would be returned.)</p><p>If it is possible to configure a library that uses TLS to use the system certificates that is generally preferable: i.e. it is better to use <code>ca_roots()</code> which returns <code>nothing</code> to indicate that the system certs should be used. The <code>ca_roots_path()</code> function should only be used when configuring libraries which <em>require</em> a path to a file or directory for root certificates.</p><p>The default value returned by <code>ca_roots_path()</code> may be overridden by setting the <code>JULIA_SSL_CA_ROOTS_PATH</code>, <code>SSL_CERT_DIR</code>, or <code>SSL_CERT_FILE</code> environment variables, in which case this function will always return the value of the first of these variables that is set (whether the path exists or not). If <code>JULIA_SSL_CA_ROOTS_PATH</code> is set to the empty string, then the other variables are ignored (as if unset); if the other variables are set to the empty string, they behave is if they are not set.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/NetworkOptions.jl/blob/aab83e5dd900c874826d430e25158dff43559d78/src/ca_roots.jl#L26-L50">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="NetworkOptions.ssh_dir" href="#NetworkOptions.ssh_dir"><code>NetworkOptions.ssh_dir</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ssh_dir() :: String</code></pre><p>The <code>ssh_dir()</code> function returns the location of the directory where the <code>ssh</code> program keeps/looks for configuration files. By default this is <code>~/.ssh</code> but this can be overridden by setting the environment variable <code>SSH_DIR</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/NetworkOptions.jl/blob/aab83e5dd900c874826d430e25158dff43559d78/src/ssh_options.jl#L10-L16">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="NetworkOptions.ssh_key_pass" href="#NetworkOptions.ssh_key_pass"><code>NetworkOptions.ssh_key_pass</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ssh_key_pass() :: String</code></pre><p>The <code>ssh_key_pass()</code> function returns the value of the environment variable <code>SSH_KEY_PASS</code> if it is set or <code>nothing</code> if it is not set. In the future, this may be able to find a password by other means, such as secure system storage, so packages that need a password to decrypt an SSH private key should use this API instead of directly checking the environment variable so that they gain such capabilities automatically when they are added.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/NetworkOptions.jl/blob/aab83e5dd900c874826d430e25158dff43559d78/src/ssh_options.jl#L19-L28">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="NetworkOptions.ssh_key_name" href="#NetworkOptions.ssh_key_name"><code>NetworkOptions.ssh_key_name</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ssh_key_name() :: String</code></pre><p>The <code>ssh_key_name()</code> function returns the base name of key files that SSH should use for when establishing a connection. There is usually no reason that this function should be called directly and libraries should generally use the <code>ssh_key_path</code> and <code>ssh_pub_key_path</code> functions to get full paths. If the environment variable <code>SSH_KEY_NAME</code> is set then this function returns that; otherwise it returns <code>id_rsa</code> by default.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/NetworkOptions.jl/blob/aab83e5dd900c874826d430e25158dff43559d78/src/ssh_options.jl#L31-L40">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="NetworkOptions.ssh_key_path" href="#NetworkOptions.ssh_key_path"><code>NetworkOptions.ssh_key_path</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ssh_key_path() :: String</code></pre><p>The <code>ssh_key_path()</code> function returns the path of the SSH private key file that should be used for SSH connections. If the <code>SSH_KEY_PATH</code> environment variable is set then it will return that value. Otherwise it defaults to returning</p><pre><code class="nohighlight hljs">joinpath(ssh_dir(), ssh_key_name())</code></pre><p>This default value in turn depends on the <code>SSH_DIR</code> and <code>SSH_KEY_NAME</code> environment variables.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/NetworkOptions.jl/blob/aab83e5dd900c874826d430e25158dff43559d78/src/ssh_options.jl#L43-L54">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="NetworkOptions.ssh_pub_key_path" href="#NetworkOptions.ssh_pub_key_path"><code>NetworkOptions.ssh_pub_key_path</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ssh_pub_key_path() :: String</code></pre><p>The <code>ssh_pub_key_path()</code> function returns the path of the SSH public key file that should be used for SSH connections. If the <code>SSH_PUB_KEY_PATH</code> environment variable is set then it will return that value. If that isn&#39;t set but <code>SSH_KEY_PATH</code> is set, it will return that path with the <code>.pub</code> suffix appended. If neither is set, it defaults to returning</p><pre><code class="nohighlight hljs">joinpath(ssh_dir(), ssh_key_name() * &quot;.pub&quot;)</code></pre><p>This default value in turn depends on the <code>SSH_DIR</code> and <code>SSH_KEY_NAME</code> environment variables.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/NetworkOptions.jl/blob/aab83e5dd900c874826d430e25158dff43559d78/src/ssh_options.jl#L61-L74">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="NetworkOptions.ssh_known_hosts_files" href="#NetworkOptions.ssh_known_hosts_files"><code>NetworkOptions.ssh_known_hosts_files</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ssh_known_hosts_files() :: Vector{String}</code></pre><p>The <code>ssh_known_hosts_files()</code> function returns a vector of paths of SSH known hosts files that should be used when establishing the identities of remote servers for SSH connections. By default this function returns</p><pre><code class="nohighlight hljs">[joinpath(ssh_dir(), &quot;known_hosts&quot;), bundled_known_hosts]</code></pre><p>where <code>bundled_known_hosts</code> is the path of a copy of a known hosts file that is bundled with this package (containing known hosts keys for <code>github.com</code> and <code>gitlab.com</code>). If the environment variable <code>SSH_KNOWN_HOSTS_FILES</code> is set, however, then its value is split into paths on the <code>:</code> character (or on <code>;</code> on Windows) and this vector of paths is returned instead. If any component of this vector is empty, it is expanded to the default known hosts paths.</p><p>Packages that use <code>ssh_known_hosts_files()</code> should ideally look for matching entries by comparing the host name and key types, considering the first entry in any of the files which matches to be the definitive identity of the host. If the caller cannot compare the key type (e.g. because it has been hashes) then it must approximate the above algorithm by looking for all matching entries for a host in each file: if a file has any entries for a host then one of them must match; the caller should only continue to search further known hosts files if there are no entries for the host in question in an earlier file.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/NetworkOptions.jl/blob/aab83e5dd900c874826d430e25158dff43559d78/src/ssh_options.jl#L83-L107">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="NetworkOptions.ssh_known_hosts_file" href="#NetworkOptions.ssh_known_hosts_file"><code>NetworkOptions.ssh_known_hosts_file</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ssh_known_hosts_file() :: String</code></pre><p>The <code>ssh_known_hosts_file()</code> function returns a single path of an SSH known hosts file that should be used when establishing the identities of remote servers for SSH connections. It returns the first path returned by <code>ssh_known_hosts_files</code> that actually exists. Callers who can look in more than one known hosts file should use <code>ssh_known_hosts_files</code> instead and look for host matches in all the files returned as described in that function&#39;s docs.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/NetworkOptions.jl/blob/aab83e5dd900c874826d430e25158dff43559d78/src/ssh_options.jl#L126-L135">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="NetworkOptions.verify_host" href="#NetworkOptions.verify_host"><code>NetworkOptions.verify_host</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">verify_host(url::AbstractString, [transport::AbstractString]) :: Bool</code></pre><p>The <code>verify_host</code> function tells the caller whether the identity of a host should be verified when communicating over secure transports like TLS or SSH. The <code>url</code> argument may be:</p><ol><li>a proper URL staring with <code>proto://</code></li><li>an <code>ssh</code>-style bare host name or host name prefixed with <code>user@</code></li><li>an <code>scp</code>-style host as above, followed by <code>:</code> and a path location</li></ol><p>In each case the host name part is parsed out and the decision about whether to verify or not is made based solely on the host name, not anything else about the input URL. In particular, the protocol of the URL does not matter (more below).</p><p>The <code>transport</code> argument indicates the kind of transport that the query is about. The currently known values are <code>SSL</code>/<code>ssl</code> (alias <code>TLS</code>/<code>tls</code>) and <code>SSH</code>/<code>ssh</code>. If the transport is omitted, the query will return <code>true</code> only if the host name should not be verified regardless of transport.</p><p>The host name is matched against the host patterns in the relevant environment variables depending on whether <code>transport</code> is supplied and what its value is:</p><ul><li><code>JULIA_NO_VERIFY_HOSTS</code> — hosts that should not be verified for any transport</li><li><code>JULIA_SSL_NO_VERIFY_HOSTS</code> — hosts that should not be verified for SSL/TLS</li><li><code>JULIA_SSH_NO_VERIFY_HOSTS</code> — hosts that should not be verified for SSH</li><li><code>JULIA_ALWAYS_VERIFY_HOSTS</code> — hosts that should always be verified</li></ul><p>The values of each of these variables is a comma-separated list of host name patterns with the following syntax — each pattern is split on <code>.</code> into parts and each part must one of:</p><ol><li>A literal domain name component consisting of one or more ASCII letter, digit, hyphen or underscore (technically not part of a legal host name, but sometimes used). A literal domain name component matches only itself.</li><li>A <code>**</code>, which matches zero or more domain name components.</li><li>A <code>*</code>, which match any one domain name component.</li></ol><p>When matching a host name against a pattern list in one of these variables, the host name is split on <code>.</code> into components and that sequence of words is matched against the pattern: a literal pattern matches exactly one host name component with that value; a <code>*</code> pattern matches exactly one host name component with any value; a <code>**</code> pattern matches any number of host name components. For example:</p><ul><li><code>**</code> matches any host name</li><li><code>**.org</code> matches any host name in the <code>.org</code> top-level domain</li><li><code>example.com</code> matches only the exact host name <code>example.com</code></li><li><code>*.example.com</code> matches <code>api.example.com</code> but not <code>example.com</code> or <code>v1.api.example.com</code></li><li><code>**.example.com</code> matches any domain under <code>example.com</code>, including <code>example.com</code> itself, <code>api.example.com</code> and <code>v1.api.example.com</code></li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/NetworkOptions.jl/blob/aab83e5dd900c874826d430e25158dff43559d78/src/verify_host.jl#L3-L54">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="Mmap.html">« Memory-mapped I/O</a><a class="docs-footer-nextpage" href="Pkg.html">Pkg »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
