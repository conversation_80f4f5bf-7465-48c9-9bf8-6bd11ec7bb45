<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Collections and Data Structures · The Julia Language</title><meta name="title" content="Collections and Data Structures · The Julia Language"/><meta property="og:title" content="Collections and Data Structures · The Julia Language"/><meta property="twitter:title" content="Collections and Data Structures · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox" checked/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="base.html">Essentials</a></li><li class="is-active"><a class="tocitem" href="collections.html">Collections and Data Structures</a><ul class="internal"><li><a class="tocitem" href="#lib-collections-iteration"><span>Iteration</span></a></li><li><a class="tocitem" href="#Constructors-and-Types"><span>Constructors and Types</span></a></li><li><a class="tocitem" href="#General-Collections"><span>General Collections</span></a></li><li><a class="tocitem" href="#Iterable-Collections"><span>Iterable Collections</span></a></li><li><a class="tocitem" href="#Indexable-Collections"><span>Indexable Collections</span></a></li><li><a class="tocitem" href="#Dictionaries"><span>Dictionaries</span></a></li><li><a class="tocitem" href="#Set-Like-Collections"><span>Set-Like Collections</span></a></li><li><a class="tocitem" href="#Dequeues"><span>Dequeues</span></a></li><li><a class="tocitem" href="#Utility-Collections"><span>Utility Collections</span></a></li></ul></li><li><a class="tocitem" href="math.html">Mathematics</a></li><li><a class="tocitem" href="numbers.html">Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="arrays.html">Arrays</a></li><li><a class="tocitem" href="parallel.html">Tasks</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="constants.html">Constants</a></li><li><a class="tocitem" href="file.html">Filesystem</a></li><li><a class="tocitem" href="io-network.html">I/O and Network</a></li><li><a class="tocitem" href="punctuation.html">Punctuation</a></li><li><a class="tocitem" href="sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="c.html">C Interface</a></li><li><a class="tocitem" href="libc.html">C Standard Library</a></li><li><a class="tocitem" href="stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Base</a></li><li class="is-active"><a href="collections.html">Collections and Data Structures</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="collections.html">Collections and Data Structures</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/base/collections.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Collections-and-Data-Structures"><a class="docs-heading-anchor" href="#Collections-and-Data-Structures">Collections and Data Structures</a><a id="Collections-and-Data-Structures-1"></a><a class="docs-heading-anchor-permalink" href="#Collections-and-Data-Structures" title="Permalink"></a></h1><h2 id="lib-collections-iteration"><a class="docs-heading-anchor" href="#lib-collections-iteration">Iteration</a><a id="lib-collections-iteration-1"></a><a class="docs-heading-anchor-permalink" href="#lib-collections-iteration" title="Permalink"></a></h2><p>Sequential iteration is implemented by the <a href="collections.html#Base.iterate"><code>iterate</code></a> function. The general <code>for</code> loop:</p><pre><code class="language-julia hljs">for i in iter   # or  &quot;for i = iter&quot;
    # body
end</code></pre><p>is translated into:</p><pre><code class="language-julia hljs">next = iterate(iter)
while next !== nothing
    (i, state) = next
    # body
    next = iterate(iter, state)
end</code></pre><p>The <code>state</code> object may be anything, and should be chosen appropriately for each iterable type. See the <a href="../manual/interfaces.html#man-interface-iteration">manual section on the iteration interface</a> for more details about defining a custom iterable type.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.iterate" href="#Base.iterate"><code>Base.iterate</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">iterate(iter [, state]) -&gt; Union{Nothing, Tuple{Any, Any}}</code></pre><p>Advance the iterator to obtain the next element. If no elements remain, <code>nothing</code> should be returned. Otherwise, a 2-tuple of the next element and the new iteration state should be returned.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/essentials.jl#L1234-L1240">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.IteratorSize" href="#Base.IteratorSize"><code>Base.IteratorSize</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">IteratorSize(itertype::Type) -&gt; IteratorSize</code></pre><p>Given the type of an iterator, return one of the following values:</p><ul><li><code>SizeUnknown()</code> if the length (number of elements) cannot be determined in advance.</li><li><code>HasLength()</code> if there is a fixed, finite length.</li><li><code>HasShape{N}()</code> if there is a known length plus a notion of multidimensional shape (as for an array).  In this case <code>N</code> should give the number of dimensions, and the <a href="arrays.html#Base.axes-Tuple{Any}"><code>axes</code></a> function is valid  for the iterator.</li><li><code>IsInfinite()</code> if the iterator yields values forever.</li></ul><p>The default value (for iterators that do not define this function) is <code>HasLength()</code>. This means that most iterators are assumed to implement <a href="collections.html#Base.length"><code>length</code></a>.</p><p>This trait is generally used to select between algorithms that pre-allocate space for their result, and algorithms that resize their result incrementally.</p><pre><code class="language-julia-repl hljs">julia&gt; Base.IteratorSize(1:5)
Base.HasShape{1}()

julia&gt; Base.IteratorSize((2,3))
Base.HasLength()</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/generator.jl#L68-L93">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.IteratorEltype" href="#Base.IteratorEltype"><code>Base.IteratorEltype</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">IteratorEltype(itertype::Type) -&gt; IteratorEltype</code></pre><p>Given the type of an iterator, return one of the following values:</p><ul><li><code>EltypeUnknown()</code> if the type of elements yielded by the iterator is not known in advance.</li><li><code>HasEltype()</code> if the element type is known, and <a href="collections.html#Base.eltype"><code>eltype</code></a> would return a meaningful value.</li></ul><p><code>HasEltype()</code> is the default, since iterators are assumed to implement <a href="collections.html#Base.eltype"><code>eltype</code></a>.</p><p>This trait is generally used to select between algorithms that pre-allocate a specific type of result, and algorithms that pick a result type based on the types of yielded values.</p><pre><code class="language-julia-repl hljs">julia&gt; Base.IteratorEltype(1:5)
Base.HasEltype()</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/generator.jl#L109-L127">source</a></section></article><p>Fully implemented by:</p><ul><li><a href="collections.html#Base.AbstractRange"><code>AbstractRange</code></a></li><li><a href="collections.html#Base.UnitRange"><code>UnitRange</code></a></li><li><a href="base.html#Core.Tuple"><code>Tuple</code></a></li><li><a href="numbers.html#Core.Number"><code>Number</code></a></li><li><a href="arrays.html#Core.AbstractArray"><code>AbstractArray</code></a></li><li><a href="collections.html#Base.BitSet"><code>BitSet</code></a></li><li><a href="collections.html#Base.IdDict"><code>IdDict</code></a></li><li><a href="collections.html#Base.Dict"><code>Dict</code></a></li><li><a href="collections.html#Base.WeakKeyDict"><code>WeakKeyDict</code></a></li><li><code>EachLine</code></li><li><a href="strings.html#Core.AbstractString"><code>AbstractString</code></a></li><li><a href="collections.html#Base.Set"><code>Set</code></a></li><li><a href="collections.html#Core.Pair"><code>Pair</code></a></li><li><a href="base.html#Core.NamedTuple"><code>NamedTuple</code></a></li></ul><h2 id="Constructors-and-Types"><a class="docs-heading-anchor" href="#Constructors-and-Types">Constructors and Types</a><a id="Constructors-and-Types-1"></a><a class="docs-heading-anchor-permalink" href="#Constructors-and-Types" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.AbstractRange" href="#Base.AbstractRange"><code>Base.AbstractRange</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AbstractRange{T} &lt;: AbstractVector{T}</code></pre><p>Supertype for linear ranges with elements of type <code>T</code>. <a href="collections.html#Base.UnitRange"><code>UnitRange</code></a>, <a href="collections.html#Base.LinRange"><code>LinRange</code></a> and other types are subtypes of this.</p><p>All subtypes must define <a href="collections.html#Base.step"><code>step</code></a>. Thus <a href="math.html#Base.LogRange"><code>LogRange</code></a> is not a subtype of <code>AbstractRange</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/range.jl#L255-L263">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.OrdinalRange" href="#Base.OrdinalRange"><code>Base.OrdinalRange</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">OrdinalRange{T, S} &lt;: AbstractRange{T}</code></pre><p>Supertype for ordinal ranges with elements of type <code>T</code> with spacing(s) of type <code>S</code>. The steps should be always-exact multiples of <a href="numbers.html#Base.oneunit"><code>oneunit</code></a>, and <code>T</code> should be a &quot;discrete&quot; type, which cannot have values smaller than <code>oneunit</code>. For example, <code>Integer</code> or <code>Date</code> types would qualify, whereas <code>Float64</code> would not (since this type can represent values smaller than <code>oneunit(Float64)</code>. <a href="collections.html#Base.UnitRange"><code>UnitRange</code></a>, <a href="collections.html#Base.StepRange"><code>StepRange</code></a>, and other types are subtypes of this.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/range.jl#L273-L283">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.AbstractUnitRange" href="#Base.AbstractUnitRange"><code>Base.AbstractUnitRange</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AbstractUnitRange{T} &lt;: OrdinalRange{T, T}</code></pre><p>Supertype for ranges with a step size of <a href="numbers.html#Base.oneunit"><code>oneunit(T)</code></a> with elements of type <code>T</code>. <a href="collections.html#Base.UnitRange"><code>UnitRange</code></a> and other types are subtypes of this.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/range.jl#L286-L291">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.StepRange" href="#Base.StepRange"><code>Base.StepRange</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">StepRange{T, S} &lt;: OrdinalRange{T, S}</code></pre><p>Ranges with elements of type <code>T</code> with spacing of type <code>S</code>. The step between each element is constant, and the range is defined in terms of a <code>start</code> and <code>stop</code> of type <code>T</code> and a <code>step</code> of type <code>S</code>. Neither <code>T</code> nor <code>S</code> should be floating point types. The syntax <code>a:b:c</code> with <code>b != 0</code> and <code>a</code>, <code>b</code>, and <code>c</code> all integers creates a <code>StepRange</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; collect(StepRange(1, Int8(2), 10))
5-element Vector{Int64}:
 1
 3
 5
 7
 9

julia&gt; typeof(StepRange(1, Int8(2), 10))
StepRange{Int64, Int8}

julia&gt; typeof(1:3:6)
StepRange{Int64, Int64}</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/range.jl#L294-L319">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.UnitRange" href="#Base.UnitRange"><code>Base.UnitRange</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">UnitRange{T&lt;:Real}</code></pre><p>A range parameterized by a <code>start</code> and <code>stop</code> of type <code>T</code>, filled with elements spaced by <code>1</code> from <code>start</code> until <code>stop</code> is exceeded. The syntax <code>a:b</code> with <code>a</code> and <code>b</code> both <code>Integer</code>s creates a <code>UnitRange</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; collect(UnitRange(2.3, 5.2))
3-element Vector{Float64}:
 2.3
 3.3
 4.3

julia&gt; typeof(1:10)
UnitRange{Int64}</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/range.jl#L386-L404">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.LinRange" href="#Base.LinRange"><code>Base.LinRange</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LinRange{T,L}</code></pre><p>A range with <code>len</code> linearly spaced elements between its <code>start</code> and <code>stop</code>. The size of the spacing is controlled by <code>len</code>, which must be an <code>Integer</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; LinRange(1.5, 5.5, 9)
9-element LinRange{Float64, Int64}:
 1.5, 2.0, 2.5, 3.0, 3.5, 4.0, 4.5, 5.0, 5.5</code></pre><p>Compared to using <a href="math.html#Base.range"><code>range</code></a>, directly constructing a <code>LinRange</code> should have less overhead but won&#39;t try to correct for floating point errors:</p><pre><code class="language-julia-repl hljs">julia&gt; collect(range(-0.1, 0.3, length=5))
5-element Vector{Float64}:
 -0.1
  0.0
  0.1
  0.2
  0.3

julia&gt; collect(LinRange(-0.1, 0.3, 5))
5-element Vector{Float64}:
 -0.1
 -1.3877787807814457e-17
  0.09999999999999999
  0.19999999999999998
  0.3</code></pre><p>See also <a href="math.html#Base.LogRange"><code>Logrange</code></a> for logarithmically spaced points.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/range.jl#L524-L559">source</a></section></article><h2 id="General-Collections"><a class="docs-heading-anchor" href="#General-Collections">General Collections</a><a id="General-Collections-1"></a><a class="docs-heading-anchor-permalink" href="#General-Collections" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isempty" href="#Base.isempty"><code>Base.isempty</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isempty(collection) -&gt; Bool</code></pre><p>Determine whether a collection is empty (has no elements).</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p><code>isempty(itr)</code> may consume the next element of a stateful iterator <code>itr</code> unless an appropriate <a href="collections.html#Base.isdone"><code>Base.isdone(itr)</code></a> method is defined. Stateful iterators <em>should</em> implement <code>isdone</code>, but you may want to avoid using <code>isempty</code> when writing generic code which should support any iterator type.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isempty([])
true

julia&gt; isempty([1 2 3])
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/essentials.jl#L1096-L1117">source</a></section><section><div><pre><code class="language-julia hljs">isempty(condition)</code></pre><p>Return <code>true</code> if no tasks are waiting on the condition, <code>false</code> otherwise.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/condition.jl#L163-L167">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isdone" href="#Base.isdone"><code>Base.isdone</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isdone(itr, [state]) -&gt; Union{Bool, Missing}</code></pre><p>This function provides a fast-path hint for iterator completion. This is useful for stateful iterators that want to avoid having elements consumed if they are not going to be exposed to the user (e.g. when checking for done-ness in <code>isempty</code> or <code>zip</code>).</p><p>Stateful iterators that want to opt into this feature should define an <code>isdone</code> method that returns true/false depending on whether the iterator is done or not. Stateless iterators need not implement this function.</p><p>If the result is <code>missing</code>, callers may go ahead and compute <code>iterate(x, state) === nothing</code> to compute a definite answer.</p><p>See also <a href="collections.html#Base.iterate"><code>iterate</code></a>, <a href="collections.html#Base.isempty"><code>isempty</code></a></p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/essentials.jl#L1215-L1231">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.empty!" href="#Base.empty!"><code>Base.empty!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">empty!(collection) -&gt; collection</code></pre><p>Remove all elements from a <code>collection</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = Dict(&quot;a&quot; =&gt; 1, &quot;b&quot; =&gt; 2)
Dict{String, Int64} with 2 entries:
  &quot;b&quot; =&gt; 2
  &quot;a&quot; =&gt; 1

julia&gt; empty!(A);

julia&gt; A
Dict{String, Int64}()</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/dict.jl#L202-L219">source</a></section><section><div><pre><code class="language-julia hljs">empty!(c::Channel)</code></pre><p>Empty a Channel <code>c</code> by calling <code>empty!</code> on the internal buffer. Return the empty channel.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/channels.jl#L221-L226">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.length" href="#Base.length"><code>Base.length</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">length(collection) -&gt; Integer</code></pre><p>Return the number of elements in the collection.</p><p>Use <a href="collections.html#Base.lastindex"><code>lastindex</code></a> to get the last valid index of an indexable collection.</p><p>See also: <a href="arrays.html#Base.size"><code>size</code></a>, <a href="arrays.html#Base.ndims"><code>ndims</code></a>, <a href="arrays.html#Base.eachindex"><code>eachindex</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; length(1:5)
5

julia&gt; length([1, 2, 3, 4])
4

julia&gt; length([1 2; 3 4])
4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L278-L298">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.checked_length" href="#Base.checked_length"><code>Base.checked_length</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.checked_length(r)</code></pre><p>Calculates <code>length(r)</code>, but may check for overflow errors where applicable when the result doesn&#39;t fit into <code>Union{Integer(eltype(r)),Int}</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/checked.jl#L374-L379">source</a></section></article><p>Fully implemented by:</p><ul><li><a href="collections.html#Base.AbstractRange"><code>AbstractRange</code></a></li><li><a href="collections.html#Base.UnitRange"><code>UnitRange</code></a></li><li><a href="base.html#Core.Tuple"><code>Tuple</code></a></li><li><a href="numbers.html#Core.Number"><code>Number</code></a></li><li><a href="arrays.html#Core.AbstractArray"><code>AbstractArray</code></a></li><li><a href="collections.html#Base.BitSet"><code>BitSet</code></a></li><li><a href="collections.html#Base.IdDict"><code>IdDict</code></a></li><li><a href="collections.html#Base.Dict"><code>Dict</code></a></li><li><a href="collections.html#Base.WeakKeyDict"><code>WeakKeyDict</code></a></li><li><a href="strings.html#Core.AbstractString"><code>AbstractString</code></a></li><li><a href="collections.html#Base.Set"><code>Set</code></a></li><li><a href="base.html#Core.NamedTuple"><code>NamedTuple</code></a></li></ul><h2 id="Iterable-Collections"><a class="docs-heading-anchor" href="#Iterable-Collections">Iterable Collections</a><a id="Iterable-Collections-1"></a><a class="docs-heading-anchor-permalink" href="#Iterable-Collections" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.in" href="#Base.in"><code>Base.in</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">in(item, collection) -&gt; Bool
∈(item, collection) -&gt; Bool</code></pre><p>Determine whether an item is in the given collection, in the sense that it is <a href="math.html#Base.:=="><code>==</code></a> to one of the values generated by iterating over the collection. Return a <code>Bool</code> value, except if <code>item</code> is <a href="../manual/missing.html#missing"><code>missing</code></a> or <code>collection</code> contains <code>missing</code> but not <code>item</code>, in which case <code>missing</code> is returned (<a href="https://en.wikipedia.org/wiki/Three-valued_logic">three-valued logic</a>, matching the behavior of <a href="collections.html#Base.any-Tuple{Any}"><code>any</code></a> and <a href="math.html#Base.:=="><code>==</code></a>).</p><p>Some collections follow a slightly different definition. For example, <a href="collections.html#Base.Set"><code>Set</code></a>s check whether the item <a href="base.html#Base.isequal"><code>isequal</code></a> to one of the elements; <a href="collections.html#Base.Dict"><code>Dict</code></a>s look for <code>key=&gt;value</code> pairs, and the <code>key</code> is compared using <a href="base.html#Base.isequal"><code>isequal</code></a>.</p><p>To test for the presence of a key in a dictionary, use <a href="collections.html#Base.haskey"><code>haskey</code></a> or <code>k in keys(dict)</code>. For the collections mentioned above, the result is always a <code>Bool</code>.</p><p>When broadcasting with <code>in.(items, collection)</code> or <code>items .∈ collection</code>, both <code>item</code> and <code>collection</code> are broadcasted over, which is often not what is intended. For example, if both arguments are vectors (and the dimensions match), the result is a vector indicating whether each value in collection <code>items</code> is <code>in</code> the value at the corresponding position in <code>collection</code>. To get a vector indicating whether each value in <code>items</code> is in <code>collection</code>, wrap <code>collection</code> in a tuple or a <code>Ref</code> like this: <code>in.(items, Ref(collection))</code> or <code>items .∈ Ref(collection)</code>.</p><p>See also: <a href="collections.html#Base.:∉"><code>∉</code></a>, <a href="sort.html#Base.Sort.insorted"><code>insorted</code></a>, <a href="strings.html#Base.contains"><code>contains</code></a>, <a href="strings.html#Base.occursin"><code>occursin</code></a>, <a href="collections.html#Base.issubset"><code>issubset</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = 1:3:20
1:3:19

julia&gt; 4 in a
true

julia&gt; 5 in a
false

julia&gt; missing in [1, 2]
missing

julia&gt; 1 in [2, missing]
missing

julia&gt; 1 in [1, missing]
true

julia&gt; missing in Set([1, 2])
false

julia&gt; (1=&gt;missing) in Dict(1=&gt;10, 2=&gt;20)
missing

julia&gt; [1, 2] .∈ [2, 3]
2-element BitVector:
 0
 0

julia&gt; [1, 2] .∈ ([2, 3],)
2-element BitVector:
 0
 1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L1343-L1409">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:∉" href="#Base.:∉"><code>Base.:∉</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">∉(item, collection) -&gt; Bool
∌(collection, item) -&gt; Bool</code></pre><p>Negation of <code>∈</code> and <code>∋</code>, i.e. checks that <code>item</code> is not in <code>collection</code>.</p><p>When broadcasting with <code>items .∉ collection</code>, both <code>item</code> and <code>collection</code> are broadcasted over, which is often not what is intended. For example, if both arguments are vectors (and the dimensions match), the result is a vector indicating whether each value in collection <code>items</code> is not in the value at the corresponding position in <code>collection</code>. To get a vector indicating whether each value in <code>items</code> is not in <code>collection</code>, wrap <code>collection</code> in a tuple or a <code>Ref</code> like this: <code>items .∉ Ref(collection)</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; 1 ∉ 2:4
true

julia&gt; 1 ∉ 1:3
false

julia&gt; [1, 2] .∉ [2, 3]
2-element BitVector:
 1
 1

julia&gt; [1, 2] .∉ ([2, 3],)
2-element BitVector:
 1
 0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L1412-L1444">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.hasfastin" href="#Base.hasfastin"><code>Base.hasfastin</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.hasfastin(T)</code></pre><p>Determine whether the computation <code>x ∈ collection</code> where <code>collection::T</code> can be considered as a &quot;fast&quot; operation (typically constant or logarithmic complexity). The definition <code>hasfastin(x) = hasfastin(typeof(x))</code> is provided for convenience so that instances can be passed instead of types. However the form that accepts a type argument should be defined for new types.</p><p>The default for <code>hasfastin(T)</code> is <code>true</code> for subtypes of <a href="collections.html#Base.AbstractSet"><code>AbstractSet</code></a>, <a href="collections.html#Base.AbstractDict"><code>AbstractDict</code></a> and <a href="collections.html#Base.AbstractRange"><code>AbstractRange</code></a> and <code>false</code> otherwise.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractset.jl#L348-L360">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.eltype" href="#Base.eltype"><code>Base.eltype</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">eltype(type)</code></pre><p>Determine the type of the elements generated by iterating a collection of the given <code>type</code>. For dictionary types, this will be a <code>Pair{KeyType,ValType}</code>. The definition <code>eltype(x) = eltype(typeof(x))</code> is provided for convenience so that instances can be passed instead of types. However the form that accepts a type argument should be defined for new types.</p><p>See also: <a href="collections.html#Base.keytype"><code>keytype</code></a>, <a href="base.html#Core.typeof"><code>typeof</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; eltype(fill(1f0, (2,2)))
Float32

julia&gt; eltype(fill(0x1, (2,2)))
UInt8</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L219-L238">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.indexin" href="#Base.indexin"><code>Base.indexin</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">indexin(a, b)</code></pre><p>Return an array containing the first index in <code>b</code> for each value in <code>a</code> that is a member of <code>b</code>. The output array contains <code>nothing</code> wherever <code>a</code> is not a member of <code>b</code>.</p><p>See also: <a href="sort.html#Base.sortperm"><code>sortperm</code></a>, <a href="arrays.html#Base.findfirst-Tuple{Any}"><code>findfirst</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [&#39;a&#39;, &#39;b&#39;, &#39;c&#39;, &#39;b&#39;, &#39;d&#39;, &#39;a&#39;];

julia&gt; b = [&#39;a&#39;, &#39;b&#39;, &#39;c&#39;];

julia&gt; indexin(a, b)
6-element Vector{Union{Nothing, Int64}}:
 1
 2
 3
 2
  nothing
 1

julia&gt; indexin(b, a)
3-element Vector{Union{Nothing, Int64}}:
 1
 2
 3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L2714-L2744">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unique" href="#Base.unique"><code>Base.unique</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unique(itr)</code></pre><p>Return an array containing only the unique elements of collection <code>itr</code>, as determined by <a href="base.html#Base.isequal"><code>isequal</code></a> and <a href="base.html#Base.hash"><code>hash</code></a>, in the order that the first of each set of equivalent elements originally appears. The element type of the input is preserved.</p><p>See also: <a href="collections.html#Base.unique!"><code>unique!</code></a>, <a href="collections.html#Base.allunique"><code>allunique</code></a>, <a href="collections.html#Base.allequal"><code>allequal</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; unique([1, 2, 6, 2])
3-element Vector{Int64}:
 1
 2
 6

julia&gt; unique(Real[1, 1.0, 2])
2-element Vector{Real}:
 1
 2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/set.jl#L200-L223">source</a></section><section><div><pre><code class="language-julia hljs">unique(f, itr)</code></pre><p>Return an array containing one value from <code>itr</code> for each unique value produced by <code>f</code> applied to elements of <code>itr</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; unique(x -&gt; x^2, [1, -1, 3, -3, 4])
3-element Vector{Int64}:
 1
 3
 4</code></pre><p>This functionality can also be used to extract the <em>indices</em> of the first occurrences of unique elements in an array:</p><pre><code class="language-julia-repl hljs">julia&gt; a = [3.1, 4.2, 5.3, 3.1, 3.1, 3.1, 4.2, 1.7];

julia&gt; i = unique(i -&gt; a[i], eachindex(a))
4-element Vector{Int64}:
 1
 2
 3
 8

julia&gt; a[i]
4-element Vector{Float64}:
 3.1
 4.2
 5.3
 1.7

julia&gt; a[i] == unique(a)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/set.jl#L264-L300">source</a></section><section><div><pre><code class="language-julia hljs">unique(A::AbstractArray; dims::Int)</code></pre><p>Return unique regions of <code>A</code> along dimension <code>dims</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = map(isodd, reshape(Vector(1:8), (2,2,2)))
2×2×2 Array{Bool, 3}:
[:, :, 1] =
 1  1
 0  0

[:, :, 2] =
 1  1
 0  0

julia&gt; unique(A)
2-element Vector{Bool}:
 1
 0

julia&gt; unique(A, dims=2)
2×1×2 Array{Bool, 3}:
[:, :, 1] =
 1
 0

[:, :, 2] =
 1
 0

julia&gt; unique(A, dims=3)
2×2×1 Array{Bool, 3}:
[:, :, 1] =
 1  1
 0  0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multidimensional.jl#L1685-L1723">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unique!" href="#Base.unique!"><code>Base.unique!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unique!(f, A::AbstractVector)</code></pre><p>Selects one value from <code>A</code> for each unique value produced by <code>f</code> applied to elements of <code>A</code>, then return the modified A.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p>This method is available as of Julia 1.1.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; unique!(x -&gt; x^2, [1, -1, 3, -3, 4])
3-element Vector{Int64}:
 1
 3
 4

julia&gt; unique!(n -&gt; n%3, [5, 1, 8, 9, 3, 4, 10, 7, 2, 6])
3-element Vector{Int64}:
 5
 1
 9

julia&gt; unique!(iseven, [2, 3, 5, 7, 9])
2-element Vector{Int64}:
 2
 3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/set.jl#L344-L372">source</a></section><section><div><pre><code class="language-julia hljs">unique!(A::AbstractVector)</code></pre><p>Remove duplicate items as determined by <a href="base.html#Base.isequal"><code>isequal</code></a> and <a href="base.html#Base.hash"><code>hash</code></a>, then return the modified <code>A</code>. <code>unique!</code> will return the elements of <code>A</code> in the order that they occur. If you do not care about the order of the returned data, then calling <code>(sort!(A); unique!(A))</code> will be much more efficient as long as the elements of <code>A</code> can be sorted.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; unique!([1, 1, 1])
1-element Vector{Int64}:
 1

julia&gt; A = [7, 3, 2, 3, 7, 5];

julia&gt; unique!(A)
4-element Vector{Int64}:
 7
 3
 2
 5

julia&gt; B = [7, 6, 42, 6, 7, 42];

julia&gt; sort!(B);  # unique! is able to process sorted data much more efficiently.

julia&gt; unique!(B)
3-element Vector{Int64}:
  6
  7
 42</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/set.jl#L436-L469">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.allunique" href="#Base.allunique"><code>Base.allunique</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">allunique(itr) -&gt; Bool
allunique(f, itr) -&gt; Bool</code></pre><p>Return <code>true</code> if all values from <code>itr</code> are distinct when compared with <a href="base.html#Base.isequal"><code>isequal</code></a>. Or if all of <code>[f(x) for x in itr]</code> are distinct, for the second method.</p><p>Note that <code>allunique(f, itr)</code> may call <code>f</code> fewer than <code>length(itr)</code> times. The precise number of calls is regarded as an implementation detail.</p><p><code>allunique</code> may use a specialized implementation when the input is sorted.</p><p>See also: <a href="collections.html#Base.unique"><code>unique</code></a>, <a href="sort.html#Base.issorted"><code>issorted</code></a>, <a href="collections.html#Base.allequal"><code>allequal</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>The method <code>allunique(f, itr)</code> requires at least Julia 1.11.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; allunique([1, 2, 3])
true

julia&gt; allunique([1, 2, 1, 2])
false

julia&gt; allunique(Real[1, 1.0, 2])
false

julia&gt; allunique([NaN, 2.0, NaN, 4.0])
false

julia&gt; allunique(abs, [1, -1, 2])
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/set.jl#L480-L514">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.allequal" href="#Base.allequal"><code>Base.allequal</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">allequal(itr) -&gt; Bool
allequal(f, itr) -&gt; Bool</code></pre><p>Return <code>true</code> if all values from <code>itr</code> are equal when compared with <a href="base.html#Base.isequal"><code>isequal</code></a>. Or if all of <code>[f(x) for x in itr]</code> are equal, for the second method.</p><p>Note that <code>allequal(f, itr)</code> may call <code>f</code> fewer than <code>length(itr)</code> times. The precise number of calls is regarded as an implementation detail.</p><p>See also: <a href="collections.html#Base.unique"><code>unique</code></a>, <a href="collections.html#Base.allunique"><code>allunique</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>The <code>allequal</code> function requires at least Julia 1.8.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>The method <code>allequal(f, itr)</code> requires at least Julia 1.11.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; allequal([])
true

julia&gt; allequal([1])
true

julia&gt; allequal([1, 1])
true

julia&gt; allequal([1, 2])
false

julia&gt; allequal(Dict(:a =&gt; 1, :b =&gt; 1))
false

julia&gt; allequal(abs2, [1, -1])
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/set.jl#L604-L642">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.reduce-Tuple{Any, Any}" href="#Base.reduce-Tuple{Any, Any}"><code>Base.reduce</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">reduce(op, itr; [init])</code></pre><p>Reduce the given collection <code>itr</code> with the given binary operator <code>op</code>. If provided, the initial value <code>init</code> must be a neutral element for <code>op</code> that will be returned for empty collections. It is unspecified whether <code>init</code> is used for non-empty collections.</p><p>For empty collections, providing <code>init</code> will be necessary, except for some special cases (e.g. when <code>op</code> is one of <code>+</code>, <code>*</code>, <code>max</code>, <code>min</code>, <code>&amp;</code>, <code>|</code>) when Julia can determine the neutral element of <code>op</code>.</p><p>Reductions for certain commonly-used operators may have special implementations, and should be used instead: <a href="collections.html#Base.maximum"><code>maximum</code></a><code>(itr)</code>, <a href="collections.html#Base.minimum"><code>minimum</code></a><code>(itr)</code>, <a href="collections.html#Base.sum"><code>sum</code></a><code>(itr)</code>, <a href="collections.html#Base.prod"><code>prod</code></a><code>(itr)</code>, <a href="collections.html#Base.any-Tuple{Any}"><code>any</code></a><code>(itr)</code>, <a href="collections.html#Base.all-Tuple{Any}"><code>all</code></a><code>(itr)</code>. There are efficient methods for concatenating certain arrays of arrays by calling <code>reduce(</code><a href="arrays.html#Base.vcat"><code>vcat</code></a><code>, arr)</code> or <code>reduce(</code><a href="arrays.html#Base.hcat"><code>hcat</code></a><code>, arr)</code>.</p><p>The associativity of the reduction is implementation dependent. This means that you can&#39;t use non-associative operations like <code>-</code> because it is undefined whether <code>reduce(-,[1,2,3])</code> should be evaluated as <code>(1-2)-3</code> or <code>1-(2-3)</code>. Use <a href="collections.html#Base.foldl-Tuple{Any, Any}"><code>foldl</code></a> or <a href="collections.html#Base.foldr-Tuple{Any, Any}"><code>foldr</code></a> instead for guaranteed left or right associativity.</p><p>Some operations accumulate error. Parallelism will be easier if the reduction can be executed in groups. Future versions of Julia might change the algorithm. Note that the elements are not reordered if you use an ordered collection.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; reduce(*, [2; 3; 4])
24

julia&gt; reduce(*, [2; 3; 4]; init=-1)
-24</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L452-L486">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.reduce-Tuple{Any, AbstractArray}" href="#Base.reduce-Tuple{Any, AbstractArray}"><code>Base.reduce</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">reduce(f, A::AbstractArray; dims=:, [init])</code></pre><p>Reduce 2-argument function <code>f</code> along dimensions of <code>A</code>. <code>dims</code> is a vector specifying the dimensions to reduce, and the keyword argument <code>init</code> is the initial value to use in the reductions. For <code>+</code>, <code>*</code>, <code>max</code> and <code>min</code> the <code>init</code> argument is optional.</p><p>The associativity of the reduction is implementation-dependent; if you need a particular associativity, e.g. left-to-right, you should write your own loop or consider using <a href="collections.html#Base.foldl-Tuple{Any, Any}"><code>foldl</code></a> or <a href="collections.html#Base.foldr-Tuple{Any, Any}"><code>foldr</code></a>. See documentation for <a href="collections.html#Base.reduce-Tuple{Any, Any}"><code>reduce</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = reshape(Vector(1:16), (4,4))
4×4 Matrix{Int64}:
 1  5   9  13
 2  6  10  14
 3  7  11  15
 4  8  12  16

julia&gt; reduce(max, a, dims=2)
4×1 Matrix{Int64}:
 13
 14
 15
 16

julia&gt; reduce(max, a, dims=1)
1×4 Matrix{Int64}:
 4  8  12  16</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L346-L377">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.foldl-Tuple{Any, Any}" href="#Base.foldl-Tuple{Any, Any}"><code>Base.foldl</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">foldl(op, itr; [init])</code></pre><p>Like <a href="collections.html#Base.reduce-Tuple{Any, Any}"><code>reduce</code></a>, but with guaranteed left associativity. If provided, the keyword argument <code>init</code> will be used exactly once. In general, it will be necessary to provide <code>init</code> to work with empty collections.</p><p>See also <a href="collections.html#Base.mapfoldl-Tuple{Any, Any, Any}"><code>mapfoldl</code></a>, <a href="collections.html#Base.foldr-Tuple{Any, Any}"><code>foldr</code></a>, <a href="arrays.html#Base.accumulate"><code>accumulate</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; foldl(=&gt;, 1:4)
((1 =&gt; 2) =&gt; 3) =&gt; 4

julia&gt; foldl(=&gt;, 1:4; init=0)
(((0 =&gt; 1) =&gt; 2) =&gt; 3) =&gt; 4

julia&gt; accumulate(=&gt;, (1,2,3,4))
(1, 1 =&gt; 2, (1 =&gt; 2) =&gt; 3, ((1 =&gt; 2) =&gt; 3) =&gt; 4)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L177-L197">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.foldr-Tuple{Any, Any}" href="#Base.foldr-Tuple{Any, Any}"><code>Base.foldr</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">foldr(op, itr; [init])</code></pre><p>Like <a href="collections.html#Base.reduce-Tuple{Any, Any}"><code>reduce</code></a>, but with guaranteed right associativity. If provided, the keyword argument <code>init</code> will be used exactly once. In general, it will be necessary to provide <code>init</code> to work with empty collections.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; foldr(=&gt;, 1:4)
1 =&gt; (2 =&gt; (3 =&gt; 4))

julia&gt; foldr(=&gt;, 1:4; init=0)
1 =&gt; (2 =&gt; (3 =&gt; (4 =&gt; 0)))</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L226-L241">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.maximum" href="#Base.maximum"><code>Base.maximum</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">maximum(f, itr; [init])</code></pre><p>Return the largest result of calling function <code>f</code> on each element of <code>itr</code>.</p><p>The value returned for empty <code>itr</code> can be specified by <code>init</code>. It must be a neutral element for <code>max</code> (i.e. which is less than or equal to any other element) as it is unspecified whether <code>init</code> is used for non-empty collections.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>Keyword argument <code>init</code> requires Julia 1.6 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; maximum(length, [&quot;Julion&quot;, &quot;Julia&quot;, &quot;Jule&quot;])
6

julia&gt; maximum(length, []; init=-1)
-1

julia&gt; maximum(sin, Real[]; init=-1.0)  # good, since output of sin is &gt;= -1
-1.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L677-L701">source</a></section><section><div><pre><code class="language-julia hljs">maximum(itr; [init])</code></pre><p>Return the largest element in a collection.</p><p>The value returned for empty <code>itr</code> can be specified by <code>init</code>. It must be a neutral element for <code>max</code> (i.e. which is less than or equal to any other element) as it is unspecified whether <code>init</code> is used for non-empty collections.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>Keyword argument <code>init</code> requires Julia 1.6 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; maximum(-20.5:10)
9.5

julia&gt; maximum([1,2,3])
3

julia&gt; maximum(())
ERROR: ArgumentError: reducing over an empty collection is not allowed; consider supplying `init` to the reducer
Stacktrace:
[...]

julia&gt; maximum((); init=-Inf)
-Inf</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L731-L760">source</a></section><section><div><pre><code class="language-julia hljs">maximum(A::AbstractArray; dims)</code></pre><p>Compute the maximum value of an array over the given dimensions. See also the <a href="math.html#Base.max"><code>max(a,b)</code></a> function to take the maximum of two or more arguments, which can be applied elementwise to arrays via <code>max.(a,b)</code>.</p><p>See also: <a href="collections.html#Base.maximum!"><code>maximum!</code></a>, <a href="collections.html#Base.extrema"><code>extrema</code></a>, <a href="collections.html#Base.findmax"><code>findmax</code></a>, <a href="collections.html#Base.argmax"><code>argmax</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; maximum(A, dims=1)
1×2 Matrix{Int64}:
 3  4

julia&gt; maximum(A, dims=2)
2×1 Matrix{Int64}:
 2
 4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L599-L624">source</a></section><section><div><pre><code class="language-julia hljs">maximum(f, A::AbstractArray; dims)</code></pre><p>Compute the maximum value by calling the function <code>f</code> on each element of an array over the given dimensions.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; maximum(abs2, A, dims=1)
1×2 Matrix{Int64}:
 9  16

julia&gt; maximum(abs2, A, dims=2)
2×1 Matrix{Int64}:
  4
 16</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L627-L649">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.maximum!" href="#Base.maximum!"><code>Base.maximum!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">maximum!(r, A)</code></pre><p>Compute the maximum value of <code>A</code> over the singleton dimensions of <code>r</code>, and write results to <code>r</code>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; maximum!([1; 1], A)
2-element Vector{Int64}:
 2
 4

julia&gt; maximum!([1 1], A)
1×2 Matrix{Int64}:
 3  4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L652-L675">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.minimum" href="#Base.minimum"><code>Base.minimum</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">minimum(f, itr; [init])</code></pre><p>Return the smallest result of calling function <code>f</code> on each element of <code>itr</code>.</p><p>The value returned for empty <code>itr</code> can be specified by <code>init</code>. It must be a neutral element for <code>min</code> (i.e. which is greater than or equal to any other element) as it is unspecified whether <code>init</code> is used for non-empty collections.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>Keyword argument <code>init</code> requires Julia 1.6 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; minimum(length, [&quot;Julion&quot;, &quot;Julia&quot;, &quot;Jule&quot;])
4

julia&gt; minimum(length, []; init=typemax(Int64))
9223372036854775807

julia&gt; minimum(sin, Real[]; init=1.0)  # good, since output of sin is &lt;= 1
1.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L704-L728">source</a></section><section><div><pre><code class="language-julia hljs">minimum(itr; [init])</code></pre><p>Return the smallest element in a collection.</p><p>The value returned for empty <code>itr</code> can be specified by <code>init</code>. It must be a neutral element for <code>min</code> (i.e. which is greater than or equal to any other element) as it is unspecified whether <code>init</code> is used for non-empty collections.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>Keyword argument <code>init</code> requires Julia 1.6 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; minimum(-20.5:10)
-20.5

julia&gt; minimum([1,2,3])
1

julia&gt; minimum([])
ERROR: ArgumentError: reducing over an empty collection is not allowed; consider supplying `init` to the reducer
Stacktrace:
[...]

julia&gt; minimum([]; init=Inf)
Inf</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L763-L792">source</a></section><section><div><pre><code class="language-julia hljs">minimum(A::AbstractArray; dims)</code></pre><p>Compute the minimum value of an array over the given dimensions. See also the <a href="math.html#Base.min"><code>min(a,b)</code></a> function to take the minimum of two or more arguments, which can be applied elementwise to arrays via <code>min.(a,b)</code>.</p><p>See also: <a href="collections.html#Base.minimum!"><code>minimum!</code></a>, <a href="collections.html#Base.extrema"><code>extrema</code></a>, <a href="collections.html#Base.findmin"><code>findmin</code></a>, <a href="collections.html#Base.argmin"><code>argmin</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; minimum(A, dims=1)
1×2 Matrix{Int64}:
 1  2

julia&gt; minimum(A, dims=2)
2×1 Matrix{Int64}:
 1
 3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L678-L703">source</a></section><section><div><pre><code class="language-julia hljs">minimum(f, A::AbstractArray; dims)</code></pre><p>Compute the minimum value by calling the function <code>f</code> on each element of an array over the given dimensions.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; minimum(abs2, A, dims=1)
1×2 Matrix{Int64}:
 1  4

julia&gt; minimum(abs2, A, dims=2)
2×1 Matrix{Int64}:
 1
 9</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L706-L728">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.minimum!" href="#Base.minimum!"><code>Base.minimum!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">minimum!(r, A)</code></pre><p>Compute the minimum value of <code>A</code> over the singleton dimensions of <code>r</code>, and write results to <code>r</code>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; minimum!([1; 1], A)
2-element Vector{Int64}:
 1
 3

julia&gt; minimum!([1 1], A)
1×2 Matrix{Int64}:
 1  2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L731-L754">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.extrema" href="#Base.extrema"><code>Base.extrema</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">extrema(itr; [init]) -&gt; (mn, mx)</code></pre><p>Compute both the minimum <code>mn</code> and maximum <code>mx</code> element in a single pass, and return them as a 2-tuple.</p><p>The value returned for empty <code>itr</code> can be specified by <code>init</code>. It must be a 2-tuple whose first and second elements are neutral elements for <code>min</code> and <code>max</code> respectively (i.e. which are greater/less than or equal to any other element). As a consequence, when <code>itr</code> is empty the returned <code>(mn, mx)</code> tuple will satisfy <code>mn ≥ mx</code>. When <code>init</code> is specified it may be used even for non-empty <code>itr</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>Keyword argument <code>init</code> requires Julia 1.8 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; extrema(2:10)
(2, 10)

julia&gt; extrema([9,pi,4.5])
(3.141592653589793, 9.0)

julia&gt; extrema([]; init = (Inf, -Inf))
(Inf, -Inf)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L795-L821">source</a></section><section><div><pre><code class="language-julia hljs">extrema(f, itr; [init]) -&gt; (mn, mx)</code></pre><p>Compute both the minimum <code>mn</code> and maximum <code>mx</code> of <code>f</code> applied to each element in <code>itr</code> and return them as a 2-tuple. Only one pass is made over <code>itr</code>.</p><p>The value returned for empty <code>itr</code> can be specified by <code>init</code>. It must be a 2-tuple whose first and second elements are neutral elements for <code>min</code> and <code>max</code> respectively (i.e. which are greater/less than or equal to any other element). It is used for non-empty collections. Note: it implies that, for empty <code>itr</code>, the returned value <code>(mn, mx)</code> satisfies <code>mn ≥ mx</code> even though for non-empty <code>itr</code> it  satisfies <code>mn ≤ mx</code>.  This is a &quot;paradoxical&quot; but yet expected result.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p>This method requires Julia 1.2 or later.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>Keyword argument <code>init</code> requires Julia 1.8 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; extrema(sin, 0:π)
(0.0, 0.9092974268256817)

julia&gt; extrema(sin, Real[]; init = (1.0, -1.0))  # good, since -1 ≤ sin(::Real) ≤ 1
(1.0, -1.0)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L824-L851">source</a></section><section><div><pre><code class="language-julia hljs">extrema(A::AbstractArray; dims) -&gt; Array{Tuple}</code></pre><p>Compute the minimum and maximum elements of an array over the given dimensions.</p><p>See also: <a href="collections.html#Base.minimum"><code>minimum</code></a>, <a href="collections.html#Base.maximum"><code>maximum</code></a>, <a href="collections.html#Base.extrema!"><code>extrema!</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = reshape(Vector(1:2:16), (2,2,2))
2×2×2 Array{Int64, 3}:
[:, :, 1] =
 1  5
 3  7

[:, :, 2] =
  9  13
 11  15

julia&gt; extrema(A, dims = (1,2))
1×1×2 Array{Tuple{Int64, Int64}, 3}:
[:, :, 1] =
 (1, 7)

[:, :, 2] =
 (9, 15)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L757-L784">source</a></section><section><div><pre><code class="language-julia hljs">extrema(f, A::AbstractArray; dims) -&gt; Array{Tuple}</code></pre><p>Compute the minimum and maximum of <code>f</code> applied to each element in the given dimensions of <code>A</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p>This method requires Julia 1.2 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L787-L795">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.extrema!" href="#Base.extrema!"><code>Base.extrema!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">extrema!(r, A)</code></pre><p>Compute the minimum and maximum value of <code>A</code> over the singleton dimensions of <code>r</code>, and write results to <code>r</code>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>This method requires Julia 1.8 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; extrema!([(1, 1); (1, 1)], A)
2-element Vector{Tuple{Int64, Int64}}:
 (1, 2)
 (3, 4)

julia&gt; extrema!([(1, 1);; (1, 1)], A)
1×2 Matrix{Tuple{Int64, Int64}}:
 (1, 3)  (2, 4)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L798-L824">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.argmax" href="#Base.argmax"><code>Base.argmax</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">argmax(r::AbstractRange)</code></pre><p>Ranges can have multiple maximal elements. In that case <code>argmax</code> will return a maximal index, but not necessarily the first one.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/range.jl#L872-L878">source</a></section><section><div><pre><code class="language-julia hljs">argmax(f, domain)</code></pre><p>Return a value <code>x</code> from <code>domain</code> for which <code>f(x)</code> is maximised. If there are multiple maximal values for <code>f(x)</code> then the first one will be found.</p><p><code>domain</code> must be a non-empty iterable.</p><p>Values are compared with <code>isless</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>This method requires Julia 1.7 or later.</p></div></div><p>See also <a href="collections.html#Base.argmin"><code>argmin</code></a>, <a href="collections.html#Base.findmax"><code>findmax</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; argmax(abs, -10:5)
-10

julia&gt; argmax(cos, 0:π/2:2π)
0.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L1003-L1026">source</a></section><section><div><pre><code class="language-julia hljs">argmax(itr)</code></pre><p>Return the index or key of the maximal element in a collection. If there are multiple maximal elements, then the first one will be returned.</p><p>The collection must not be empty.</p><p>Indices are of the same type as those returned by <a href="arrays.html#Base.keys-Tuple{AbstractArray}"><code>keys(itr)</code></a> and <a href="collections.html#Base.pairs"><code>pairs(itr)</code></a>.</p><p>Values are compared with <code>isless</code>.</p><p>See also: <a href="collections.html#Base.argmin"><code>argmin</code></a>, <a href="collections.html#Base.findmax"><code>findmax</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; argmax([8, 0.1, -9, pi])
1

julia&gt; argmax([1, 7, 7, 6])
2

julia&gt; argmax([1, 7, 7, NaN])
4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L1029-L1055">source</a></section><section><div><pre><code class="language-julia hljs">argmax(A; dims) -&gt; indices</code></pre><p>For an array input, return the indices of the maximum elements over the given dimensions. <code>NaN</code> is treated as greater than all other values except <code>missing</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1.0 2; 3 4]
2×2 Matrix{Float64}:
 1.0  2.0
 3.0  4.0

julia&gt; argmax(A, dims=1)
1×2 Matrix{CartesianIndex{2}}:
 CartesianIndex(2, 1)  CartesianIndex(2, 2)

julia&gt; argmax(A, dims=2)
2×1 Matrix{CartesianIndex{2}}:
 CartesianIndex(1, 2)
 CartesianIndex(2, 2)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L1251-L1273">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.argmin" href="#Base.argmin"><code>Base.argmin</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">argmin(r::AbstractRange)</code></pre><p>Ranges can have multiple minimal elements. In that case <code>argmin</code> will return a minimal index, but not necessarily the first one.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/range.jl#L855-L861">source</a></section><section><div><pre><code class="language-julia hljs">argmin(f, domain)</code></pre><p>Return a value <code>x</code> from <code>domain</code> for which <code>f(x)</code> is minimised. If there are multiple minimal values for <code>f(x)</code> then the first one will be found.</p><p><code>domain</code> must be a non-empty iterable.</p><p><code>NaN</code> is treated as less than all other values except <code>missing</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>This method requires Julia 1.7 or later.</p></div></div><p>See also <a href="collections.html#Base.argmax"><code>argmax</code></a>, <a href="collections.html#Base.findmin"><code>findmin</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; argmin(sign, -10:5)
-10

julia&gt; argmin(x -&gt; -x^3 + x^2 - 10, -5:5)
5

julia&gt; argmin(acos, 0:0.1:1)
1.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L1058-L1084">source</a></section><section><div><pre><code class="language-julia hljs">argmin(itr)</code></pre><p>Return the index or key of the minimal element in a collection. If there are multiple minimal elements, then the first one will be returned.</p><p>The collection must not be empty.</p><p>Indices are of the same type as those returned by <a href="arrays.html#Base.keys-Tuple{AbstractArray}"><code>keys(itr)</code></a> and <a href="collections.html#Base.pairs"><code>pairs(itr)</code></a>.</p><p><code>NaN</code> is treated as less than all other values except <code>missing</code>.</p><p>See also: <a href="collections.html#Base.argmax"><code>argmax</code></a>, <a href="collections.html#Base.findmin"><code>findmin</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; argmin([8, 0.1, -9, pi])
3

julia&gt; argmin([7, 1, 1, 6])
2

julia&gt; argmin([7, 1, 1, NaN])
4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L1087-L1113">source</a></section><section><div><pre><code class="language-julia hljs">argmin(A; dims) -&gt; indices</code></pre><p>For an array input, return the indices of the minimum elements over the given dimensions. <code>NaN</code> is treated as less than all other values except <code>missing</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1.0 2; 3 4]
2×2 Matrix{Float64}:
 1.0  2.0
 3.0  4.0

julia&gt; argmin(A, dims=1)
1×2 Matrix{CartesianIndex{2}}:
 CartesianIndex(1, 1)  CartesianIndex(1, 2)

julia&gt; argmin(A, dims=2)
2×1 Matrix{CartesianIndex{2}}:
 CartesianIndex(1, 1)
 CartesianIndex(2, 1)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L1226-L1248">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findmax" href="#Base.findmax"><code>Base.findmax</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findmax(f, domain) -&gt; (f(x), index)</code></pre><p>Return a pair of a value in the codomain (outputs of <code>f</code>) and the index or key of the corresponding value in the <code>domain</code> (inputs to <code>f</code>) such that <code>f(x)</code> is maximised. If there are multiple maximal points, then the first one will be returned.</p><p><code>domain</code> must be a non-empty iterable supporting <a href="collections.html#Base.keys"><code>keys</code></a>. Indices are of the same type as those returned by <a href="arrays.html#Base.keys-Tuple{AbstractArray}"><code>keys(domain)</code></a>.</p><p>Values are compared with <code>isless</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>This method requires Julia 1.7 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; findmax(identity, 5:9)
(9, 5)

julia&gt; findmax(-, 1:10)
(-1, 1)

julia&gt; findmax(first, [(1, :a), (3, :b), (3, :c)])
(3, 2)

julia&gt; findmax(cos, 0:π/2:2π)
(1.0, 1)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L874-L904">source</a></section><section><div><pre><code class="language-julia hljs">findmax(itr) -&gt; (x, index)</code></pre><p>Return the maximal element of the collection <code>itr</code> and its index or key. If there are multiple maximal elements, then the first one will be returned. Values are compared with <code>isless</code>.</p><p>Indices are of the same type as those returned by <a href="arrays.html#Base.keys-Tuple{AbstractArray}"><code>keys(itr)</code></a> and <a href="collections.html#Base.pairs"><code>pairs(itr)</code></a>.</p><p>See also: <a href="collections.html#Base.findmin"><code>findmin</code></a>, <a href="collections.html#Base.argmax"><code>argmax</code></a>, <a href="collections.html#Base.maximum"><code>maximum</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; findmax([8, 0.1, -9, pi])
(8.0, 1)

julia&gt; findmax([1, 7, 7, 6])
(7, 2)

julia&gt; findmax([1, 7, 7, NaN])
(NaN, 4)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L909-L933">source</a></section><section><div><pre><code class="language-julia hljs">findmax(A; dims) -&gt; (maxval, index)</code></pre><p>For an array input, returns the value and index of the maximum over the given dimensions. <code>NaN</code> is treated as greater than all other values except <code>missing</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1.0 2; 3 4]
2×2 Matrix{Float64}:
 1.0  2.0
 3.0  4.0

julia&gt; findmax(A, dims=1)
([3.0 4.0], CartesianIndex{2}[CartesianIndex(2, 1) CartesianIndex(2, 2)])

julia&gt; findmax(A, dims=2)
([2.0; 4.0;;], CartesianIndex{2}[CartesianIndex(1, 2); CartesianIndex(2, 2);;])</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L1155-L1174">source</a></section><section><div><pre><code class="language-julia hljs">findmax(f, A; dims) -&gt; (f(x), index)</code></pre><p>For an array input, returns the value in the codomain and index of the corresponding value which maximize <code>f</code> over the given dimensions.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [-1.0 1; -0.5 2]
2×2 Matrix{Float64}:
 -1.0  1.0
 -0.5  2.0

julia&gt; findmax(abs2, A, dims=1)
([1.0 4.0], CartesianIndex{2}[CartesianIndex(1, 1) CartesianIndex(2, 2)])

julia&gt; findmax(abs2, A, dims=2)
([1.0; 4.0;;], CartesianIndex{2}[CartesianIndex(1, 1); CartesianIndex(2, 2);;])</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L1178-L1197">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findmin" href="#Base.findmin"><code>Base.findmin</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findmin(f, domain) -&gt; (f(x), index)</code></pre><p>Return a pair of a value in the codomain (outputs of <code>f</code>) and the index or key of the corresponding value in the <code>domain</code> (inputs to <code>f</code>) such that <code>f(x)</code> is minimised. If there are multiple minimal points, then the first one will be returned.</p><p><code>domain</code> must be a non-empty iterable.</p><p>Indices are of the same type as those returned by <a href="arrays.html#Base.keys-Tuple{AbstractArray}"><code>keys(domain)</code></a> and <a href="collections.html#Base.pairs"><code>pairs(domain)</code></a>.</p><p><code>NaN</code> is treated as less than all other values except <code>missing</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>This method requires Julia 1.7 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; findmin(identity, 5:9)
(5, 1)

julia&gt; findmin(-, 1:10)
(-10, 10)

julia&gt; findmin(first, [(2, :a), (2, :b), (3, :c)])
(2, 1)

julia&gt; findmin(cos, 0:π/2:2π)
(-1.0, 3)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L937-L970">source</a></section><section><div><pre><code class="language-julia hljs">findmin(itr) -&gt; (x, index)</code></pre><p>Return the minimal element of the collection <code>itr</code> and its index or key. If there are multiple minimal elements, then the first one will be returned. <code>NaN</code> is treated as less than all other values except <code>missing</code>.</p><p>Indices are of the same type as those returned by <a href="arrays.html#Base.keys-Tuple{AbstractArray}"><code>keys(itr)</code></a> and <a href="collections.html#Base.pairs"><code>pairs(itr)</code></a>.</p><p>See also: <a href="collections.html#Base.findmax"><code>findmax</code></a>, <a href="collections.html#Base.argmin"><code>argmin</code></a>, <a href="collections.html#Base.minimum"><code>minimum</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; findmin([8, 0.1, -9, pi])
(-9.0, 3)

julia&gt; findmin([1, 7, 7, 6])
(1, 1)

julia&gt; findmin([1, 7, 7, NaN])
(NaN, 4)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L975-L999">source</a></section><section><div><pre><code class="language-julia hljs">findmin(A; dims) -&gt; (minval, index)</code></pre><p>For an array input, returns the value and index of the minimum over the given dimensions. <code>NaN</code> is treated as less than all other values except <code>missing</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1.0 2; 3 4]
2×2 Matrix{Float64}:
 1.0  2.0
 3.0  4.0

julia&gt; findmin(A, dims=1)
([1.0 2.0], CartesianIndex{2}[CartesianIndex(1, 1) CartesianIndex(1, 2)])

julia&gt; findmin(A, dims=2)
([1.0; 3.0;;], CartesianIndex{2}[CartesianIndex(1, 1); CartesianIndex(2, 1);;])</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L1082-L1101">source</a></section><section><div><pre><code class="language-julia hljs">findmin(f, A; dims) -&gt; (f(x), index)</code></pre><p>For an array input, returns the value in the codomain and index of the corresponding value which minimize <code>f</code> over the given dimensions.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [-1.0 1; -0.5 2]
2×2 Matrix{Float64}:
 -1.0  1.0
 -0.5  2.0

julia&gt; findmin(abs2, A, dims=1)
([0.25 1.0], CartesianIndex{2}[CartesianIndex(2, 1) CartesianIndex(1, 2)])

julia&gt; findmin(abs2, A, dims=2)
([1.0; 0.25;;], CartesianIndex{2}[CartesianIndex(1, 1); CartesianIndex(2, 1);;])</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L1105-L1124">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findmax!" href="#Base.findmax!"><code>Base.findmax!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findmax!(rval, rind, A) -&gt; (maxval, index)</code></pre><p>Find the maximum of <code>A</code> and the corresponding linear index along singleton dimensions of <code>rval</code> and <code>rind</code>, and store the results in <code>rval</code> and <code>rind</code>. <code>NaN</code> is treated as greater than all other values except <code>missing</code>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L1141-L1149">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.findmin!" href="#Base.findmin!"><code>Base.findmin!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findmin!(rval, rind, A) -&gt; (minval, index)</code></pre><p>Find the minimum of <code>A</code> and the corresponding linear index along singleton dimensions of <code>rval</code> and <code>rind</code>, and store the results in <code>rval</code> and <code>rind</code>. <code>NaN</code> is treated as less than all other values except <code>missing</code>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L1068-L1076">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.sum" href="#Base.sum"><code>Base.sum</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sum(f, itr; [init])</code></pre><p>Sum the results of calling function <code>f</code> on each element of <code>itr</code>.</p><p>The return type is <code>Int</code> for signed integers of less than system word size, and <code>UInt</code> for unsigned integers of less than system word size.  For all other arguments, a common return type is found to which all arguments are promoted.</p><p>The value returned for empty <code>itr</code> can be specified by <code>init</code>. It must be the additive identity (i.e. zero) as it is unspecified whether <code>init</code> is used for non-empty collections.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>Keyword argument <code>init</code> requires Julia 1.6 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; sum(abs2, [2; 3; 4])
29</code></pre><p>Note the important difference between <code>sum(A)</code> and <code>reduce(+, A)</code> for arrays with small integer eltype:</p><pre><code class="language-julia-repl hljs">julia&gt; sum(Int8[100, 28])
128

julia&gt; reduce(+, Int8[100, 28])
-128</code></pre><p>In the former case, the integers are widened to system word size and therefore the result is 128. In the latter case, no such widening happens and integer overflow results in -128.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L495-L531">source</a></section><section><div><pre><code class="language-julia hljs">sum(itr; [init])</code></pre><p>Return the sum of all elements in a collection.</p><p>The return type is <code>Int</code> for signed integers of less than system word size, and <code>UInt</code> for unsigned integers of less than system word size.  For all other arguments, a common return type is found to which all arguments are promoted.</p><p>The value returned for empty <code>itr</code> can be specified by <code>init</code>. It must be the additive identity (i.e. zero) as it is unspecified whether <code>init</code> is used for non-empty collections.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>Keyword argument <code>init</code> requires Julia 1.6 or later.</p></div></div><p>See also: <a href="collections.html#Base.reduce-Tuple{Any, Any}"><code>reduce</code></a>, <a href="collections.html#Base.mapreduce-Tuple{Any, Any, Any}"><code>mapreduce</code></a>, <a href="collections.html#Base.count"><code>count</code></a>, <a href="collections.html#Base.union"><code>union</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; sum(1:20)
210

julia&gt; sum(1:20; init = 0.0)
210.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L534-L560">source</a></section><section><div><pre><code class="language-julia hljs">sum(A::AbstractArray; dims)</code></pre><p>Sum elements of an array over the given dimensions.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; sum(A, dims=1)
1×2 Matrix{Int64}:
 4  6

julia&gt; sum(A, dims=2)
2×1 Matrix{Int64}:
 3
 7</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L449-L470">source</a></section><section><div><pre><code class="language-julia hljs">sum(f, A::AbstractArray; dims)</code></pre><p>Sum the results of calling function <code>f</code> on each element of an array over the given dimensions.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; sum(abs2, A, dims=1)
1×2 Matrix{Int64}:
 10  20

julia&gt; sum(abs2, A, dims=2)
2×1 Matrix{Int64}:
  5
 25</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L473-L495">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.sum!" href="#Base.sum!"><code>Base.sum!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sum!(r, A)</code></pre><p>Sum elements of <code>A</code> over the singleton dimensions of <code>r</code>, and write results to <code>r</code>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; sum!([1; 1], A)
2-element Vector{Int64}:
 3
 7

julia&gt; sum!([1 1], A)
1×2 Matrix{Int64}:
 4  6</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L498-L521">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.prod" href="#Base.prod"><code>Base.prod</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">prod(f, itr; [init])</code></pre><p>Return the product of <code>f</code> applied to each element of <code>itr</code>.</p><p>The return type is <code>Int</code> for signed integers of less than system word size, and <code>UInt</code> for unsigned integers of less than system word size.  For all other arguments, a common return type is found to which all arguments are promoted.</p><p>The value returned for empty <code>itr</code> can be specified by <code>init</code>. It must be the multiplicative identity (i.e. one) as it is unspecified whether <code>init</code> is used for non-empty collections.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>Keyword argument <code>init</code> requires Julia 1.6 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; prod(abs2, [2; 3; 4])
576</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L566-L587">source</a></section><section><div><pre><code class="language-julia hljs">prod(itr; [init])</code></pre><p>Return the product of all elements of a collection.</p><p>The return type is <code>Int</code> for signed integers of less than system word size, and <code>UInt</code> for unsigned integers of less than system word size.  For all other arguments, a common return type is found to which all arguments are promoted.</p><p>The value returned for empty <code>itr</code> can be specified by <code>init</code>. It must be the multiplicative identity (i.e. one) as it is unspecified whether <code>init</code> is used for non-empty collections.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>Keyword argument <code>init</code> requires Julia 1.6 or later.</p></div></div><p>See also: <a href="collections.html#Base.reduce-Tuple{Any, Any}"><code>reduce</code></a>, <a href="arrays.html#Base.cumprod"><code>cumprod</code></a>, <a href="collections.html#Base.any-Tuple{Any}"><code>any</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; prod(1:5)
120

julia&gt; prod(1:5; init = 1.0)
120.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L590-L616">source</a></section><section><div><pre><code class="language-julia hljs">prod(A::AbstractArray; dims)</code></pre><p>Multiply elements of an array over the given dimensions.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; prod(A, dims=1)
1×2 Matrix{Int64}:
 3  8

julia&gt; prod(A, dims=2)
2×1 Matrix{Int64}:
  2
 12</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L524-L545">source</a></section><section><div><pre><code class="language-julia hljs">prod(f, A::AbstractArray; dims)</code></pre><p>Multiply the results of calling the function <code>f</code> on each element of an array over the given dimensions.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; prod(abs2, A, dims=1)
1×2 Matrix{Int64}:
 9  64

julia&gt; prod(abs2, A, dims=2)
2×1 Matrix{Int64}:
   4
 144</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L548-L570">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.prod!" href="#Base.prod!"><code>Base.prod!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">prod!(r, A)</code></pre><p>Multiply elements of <code>A</code> over the singleton dimensions of <code>r</code>, and write results to <code>r</code>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; prod!([1; 1], A)
2-element Vector{Int64}:
  2
 12

julia&gt; prod!([1 1], A)
1×2 Matrix{Int64}:
 3  8</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L573-L596">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.any-Tuple{Any}" href="#Base.any-Tuple{Any}"><code>Base.any</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">any(itr) -&gt; Bool</code></pre><p>Test whether any elements of a boolean collection are <code>true</code>, returning <code>true</code> as soon as the first <code>true</code> value in <code>itr</code> is encountered (short-circuiting). To short-circuit on <code>false</code>, use <a href="collections.html#Base.all-Tuple{Any}"><code>all</code></a>.</p><p>If the input contains <a href="../manual/missing.html#missing"><code>missing</code></a> values, return <code>missing</code> if all non-missing values are <code>false</code> (or equivalently, if the input contains no <code>true</code> value), following <a href="https://en.wikipedia.org/wiki/Three-valued_logic">three-valued logic</a>.</p><p>See also: <a href="collections.html#Base.all-Tuple{Any}"><code>all</code></a>, <a href="collections.html#Base.count"><code>count</code></a>, <a href="collections.html#Base.sum"><code>sum</code></a>, <a href="math.html#Base.:|"><code>|</code></a>, , <a href="math.html#||"><code>||</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [true,false,false,true]
4-element Vector{Bool}:
 1
 0
 0
 1

julia&gt; any(a)
true

julia&gt; any((println(i); v) for (i, v) in enumerate(a))
1
true

julia&gt; any([missing, true])
true

julia&gt; any([false, missing])
missing</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L1118-L1153">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.any-Tuple{AbstractArray, Any}" href="#Base.any-Tuple{AbstractArray, Any}"><code>Base.any</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">any(p, itr) -&gt; Bool</code></pre><p>Determine whether predicate <code>p</code> returns <code>true</code> for any elements of <code>itr</code>, returning <code>true</code> as soon as the first item in <code>itr</code> for which <code>p</code> returns <code>true</code> is encountered (short-circuiting). To short-circuit on <code>false</code>, use <a href="collections.html#Base.all-Tuple{Any}"><code>all</code></a>.</p><p>If the input contains <a href="../manual/missing.html#missing"><code>missing</code></a> values, return <code>missing</code> if all non-missing values are <code>false</code> (or equivalently, if the input contains no <code>true</code> value), following <a href="https://en.wikipedia.org/wiki/Three-valued_logic">three-valued logic</a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; any(i-&gt;(4&lt;=i&lt;=6), [3,5,7])
true

julia&gt; any(i -&gt; (println(i); i &gt; 3), 1:10)
1
2
3
4
true

julia&gt; any(i -&gt; i &gt; 0, [1, missing])
true

julia&gt; any(i -&gt; i &gt; 0, [-1, missing])
missing

julia&gt; any(i -&gt; i &gt; 0, [-1, 0])
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L1195-L1227">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.any!" href="#Base.any!"><code>Base.any!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">any!(r, A)</code></pre><p>Test whether any values in <code>A</code> along the singleton dimensions of <code>r</code> are <code>true</code>, and write results to <code>r</code>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [true false; true false]
2×2 Matrix{Bool}:
 1  0
 1  0

julia&gt; any!([1; 1], A)
2-element Vector{Int64}:
 1
 1

julia&gt; any!([1 1], A)
1×2 Matrix{Int64}:
 1  0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L949-L973">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.all-Tuple{Any}" href="#Base.all-Tuple{Any}"><code>Base.all</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">all(itr) -&gt; Bool</code></pre><p>Test whether all elements of a boolean collection are <code>true</code>, returning <code>false</code> as soon as the first <code>false</code> value in <code>itr</code> is encountered (short-circuiting). To short-circuit on <code>true</code>, use <a href="collections.html#Base.any-Tuple{Any}"><code>any</code></a>.</p><p>If the input contains <a href="../manual/missing.html#missing"><code>missing</code></a> values, return <code>missing</code> if all non-missing values are <code>true</code> (or equivalently, if the input contains no <code>false</code> value), following <a href="https://en.wikipedia.org/wiki/Three-valued_logic">three-valued logic</a>.</p><p>See also: <a href="collections.html#Base.all!"><code>all!</code></a>, <a href="collections.html#Base.any-Tuple{Any}"><code>any</code></a>, <a href="collections.html#Base.count"><code>count</code></a>, <a href="math.html#Base.:&amp;"><code>&amp;</code></a>, , <a href="math.html#&amp;&amp;"><code>&amp;&amp;</code></a>, <a href="collections.html#Base.allunique"><code>allunique</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [true,false,false,true]
4-element Vector{Bool}:
 1
 0
 0
 1

julia&gt; all(a)
false

julia&gt; all((println(i); v) for (i, v) in enumerate(a))
1
2
false

julia&gt; all([missing, false])
false

julia&gt; all([true, missing])
missing</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L1156-L1192">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.all-Tuple{AbstractArray, Any}" href="#Base.all-Tuple{AbstractArray, Any}"><code>Base.all</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">all(p, itr) -&gt; Bool</code></pre><p>Determine whether predicate <code>p</code> returns <code>true</code> for all elements of <code>itr</code>, returning <code>false</code> as soon as the first item in <code>itr</code> for which <code>p</code> returns <code>false</code> is encountered (short-circuiting). To short-circuit on <code>true</code>, use <a href="collections.html#Base.any-Tuple{Any}"><code>any</code></a>.</p><p>If the input contains <a href="../manual/missing.html#missing"><code>missing</code></a> values, return <code>missing</code> if all non-missing values are <code>true</code> (or equivalently, if the input contains no <code>false</code> value), following <a href="https://en.wikipedia.org/wiki/Three-valued_logic">three-valued logic</a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; all(i-&gt;(4&lt;=i&lt;=6), [4,5,6])
true

julia&gt; all(i -&gt; (println(i); i &lt; 3), 1:10)
1
2
3
false

julia&gt; all(i -&gt; i &gt; 0, [1, missing])
missing

julia&gt; all(i -&gt; i &gt; 0, [-1, missing])
false

julia&gt; all(i -&gt; i &gt; 0, [1, 2])
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L1269-L1300">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.all!" href="#Base.all!"><code>Base.all!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">all!(r, A)</code></pre><p>Test whether all values in <code>A</code> along the singleton dimensions of <code>r</code> are <code>true</code>, and write results to <code>r</code>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [true false; true false]
2×2 Matrix{Bool}:
 1  0
 1  0

julia&gt; all!([1; 1], A)
2-element Vector{Int64}:
 0
 0

julia&gt; all!([1 1], A)
1×2 Matrix{Int64}:
 1  0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L875-L898">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.count" href="#Base.count"><code>Base.count</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">count([f=identity,] itr; init=0) -&gt; Integer</code></pre><p>Count the number of elements in <code>itr</code> for which the function <code>f</code> returns <code>true</code>. If <code>f</code> is omitted, count the number of <code>true</code> elements in <code>itr</code> (which should be a collection of boolean values). <code>init</code> optionally specifies the value to start counting from and therefore also determines the output type.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p><code>init</code> keyword was added in Julia 1.6.</p></div></div><p>See also: <a href="collections.html#Base.any-Tuple{Any}"><code>any</code></a>, <a href="collections.html#Base.sum"><code>sum</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; count(i-&gt;(4&lt;=i&lt;=6), [2,3,4,5,6])
3

julia&gt; count([true, false, true, true])
3

julia&gt; count(&gt;(3), 1:7, init=0x03)
0x07</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L1348-L1372">source</a></section><section><div><pre><code class="language-julia hljs">count(
    pattern::Union{AbstractChar,AbstractString,AbstractPattern},
    string::AbstractString;
    overlap::Bool = false,
)</code></pre><p>Return the number of matches for <code>pattern</code> in <code>string</code>. This is equivalent to calling <code>length(findall(pattern, string))</code> but more efficient.</p><p>If <code>overlap=true</code>, the matching sequences are allowed to overlap indices in the original string, otherwise they must be from disjoint character ranges.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p>This method requires at least Julia 1.3.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Using a character as the pattern requires at least Julia 1.7.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; count(&#39;a&#39;, &quot;JuliaLang&quot;)
2

julia&gt; count(r&quot;a(.)a&quot;, &quot;cabacabac&quot;, overlap=true)
3

julia&gt; count(r&quot;a(.)a&quot;, &quot;cabacabac&quot;)
2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/regex.jl#L519-L549">source</a></section><section><div><pre><code class="language-julia hljs">count([f=identity,] A::AbstractArray; dims=:)</code></pre><p>Count the number of elements in <code>A</code> for which <code>f</code> returns <code>true</code> over the given dimensions.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p><code>dims</code> keyword was added in Julia 1.5.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p><code>init</code> keyword was added in Julia 1.6.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; count(&lt;=(2), A, dims=1)
1×2 Matrix{Int64}:
 1  1

julia&gt; count(&lt;=(2), A, dims=2)
2×1 Matrix{Int64}:
 2
 0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reducedim.jl#L382-L410">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.foreach" href="#Base.foreach"><code>Base.foreach</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">foreach(f, c...) -&gt; Nothing</code></pre><p>Call function <code>f</code> on each element of iterable <code>c</code>. For multiple iterable arguments, <code>f</code> is called elementwise, and iteration stops when any iterator is finished.</p><p><code>foreach</code> should be used instead of <a href="collections.html#Base.map"><code>map</code></a> when the results of <code>f</code> are not needed, for example in <code>foreach(println, array)</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; tri = 1:3:7; res = Int[];

julia&gt; foreach(x -&gt; push!(res, x^2), tri)

julia&gt; res
3-element Vector{Int64}:
  1
 16
 49

julia&gt; foreach((x, y) -&gt; println(x, &quot; with &quot;, y), tri, &#39;a&#39;:&#39;z&#39;)
1 with a
4 with b
7 with c</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L3159-L3186">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.map" href="#Base.map"><code>Base.map</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">map(f, c...) -&gt; collection</code></pre><p>Transform collection <code>c</code> by applying <code>f</code> to each element. For multiple collection arguments, apply <code>f</code> elementwise, and stop when any of them is exhausted.</p><p>See also <a href="collections.html#Base.map!"><code>map!</code></a>, <a href="collections.html#Base.foreach"><code>foreach</code></a>, <a href="collections.html#Base.mapreduce-Tuple{Any, Any, Any}"><code>mapreduce</code></a>, <a href="arrays.html#Base.mapslices"><code>mapslices</code></a>, <a href="iterators.html#Base.Iterators.zip"><code>zip</code></a>, <a href="iterators.html#Base.Iterators.map"><code>Iterators.map</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; map(x -&gt; x * 2, [1, 2, 3])
3-element Vector{Int64}:
 2
 4
 6

julia&gt; map(+, [1, 2, 3], [10, 20, 30, 400, 5000])
3-element Vector{Int64}:
 11
 22
 33</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L3376-L3398">source</a></section><section><div><pre><code class="language-julia hljs">map(f, A::AbstractArray...) -&gt; N-array</code></pre><p>When acting on multi-dimensional arrays of the same <a href="arrays.html#Base.ndims"><code>ndims</code></a>, they must all have the same <a href="arrays.html#Base.axes-Tuple{Any}"><code>axes</code></a>, and the answer will too.</p><p>See also <a href="arrays.html#Base.Broadcast.broadcast"><code>broadcast</code></a>, which allows mismatched sizes.</p><p><strong>Examples</strong></p><pre><code class="nohighlight hljs">julia&gt; map(//, [1 2; 3 4], [4 3; 2 1])
2×2 Matrix{Rational{Int64}}:
 1//4  2//3
 3//2  4//1

julia&gt; map(+, [1 2; 3 4], zeros(2,1))
ERROR: DimensionMismatch

julia&gt; map(+, [1 2; 3 4], [1,10,100,1000], zeros(3,1))  # iterates until 3rd is exhausted
3-element Vector{Float64}:
   2.0
  13.0
 102.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L3470-L3494">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.map!" href="#Base.map!"><code>Base.map!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">map!(function, destination, collection...)</code></pre><p>Like <a href="collections.html#Base.map"><code>map</code></a>, but stores the result in <code>destination</code> rather than a new collection. <code>destination</code> must be at least as large as the smallest collection.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p>See also: <a href="collections.html#Base.map"><code>map</code></a>, <a href="collections.html#Base.foreach"><code>foreach</code></a>, <a href="iterators.html#Base.Iterators.zip"><code>zip</code></a>, <a href="c.html#Base.copyto!"><code>copyto!</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = zeros(3);

julia&gt; map!(x -&gt; x * 2, a, [1, 2, 3]);

julia&gt; a
3-element Vector{Float64}:
 2.0
 4.0
 6.0

julia&gt; map!(+, zeros(Int, 5), 100:999, 1:3)
5-element Vector{Int64}:
 101
 103
 105
   0
   0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L3433-L3463">source</a></section><section><div><pre><code class="language-julia hljs">map!(f, values(dict::AbstractDict))</code></pre><p>Modifies <code>dict</code> by transforming each value from <code>val</code> to <code>f(val)</code>. Note that the type of <code>dict</code> cannot be changed: if <code>f(val)</code> is not an instance of the value type of <code>dict</code> then it will be converted to the value type if possible and otherwise raise an error.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p><code>map!(f, values(dict::AbstractDict))</code> requires Julia 1.2 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; d = Dict(:a =&gt; 1, :b =&gt; 2)
Dict{Symbol, Int64} with 2 entries:
  :a =&gt; 1
  :b =&gt; 2

julia&gt; map!(v -&gt; v-1, values(d))
ValueIterator for a Dict{Symbol, Int64} with 2 entries. Values:
  0
  1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractdict.jl#L648-L670">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.mapreduce-Tuple{Any, Any, Any}" href="#Base.mapreduce-Tuple{Any, Any, Any}"><code>Base.mapreduce</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mapreduce(f, op, itrs...; [init])</code></pre><p>Apply function <code>f</code> to each element(s) in <code>itrs</code>, and then reduce the result using the binary function <code>op</code>. If provided, <code>init</code> must be a neutral element for <code>op</code> that will be returned for empty collections. It is unspecified whether <code>init</code> is used for non-empty collections. In general, it will be necessary to provide <code>init</code> to work with empty collections.</p><p><a href="collections.html#Base.mapreduce-Tuple{Any, Any, Any}"><code>mapreduce</code></a> is functionally equivalent to calling <code>reduce(op, map(f, itr); init=init)</code>, but will in general execute faster since no intermediate collection needs to be created. See documentation for <a href="collections.html#Base.reduce-Tuple{Any, Any}"><code>reduce</code></a> and <a href="collections.html#Base.map"><code>map</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p><code>mapreduce</code> with multiple iterators requires Julia 1.2 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; mapreduce(x-&gt;x^2, +, [1:3;]) # == 1 + 4 + 9
14</code></pre><p>The associativity of the reduction is implementation-dependent. Additionally, some implementations may reuse the return value of <code>f</code> for elements that appear multiple times in <code>itr</code>. Use <a href="collections.html#Base.mapfoldl-Tuple{Any, Any, Any}"><code>mapfoldl</code></a> or <a href="collections.html#Base.mapfoldr-Tuple{Any, Any, Any}"><code>mapfoldr</code></a> instead for guaranteed left or right associativity and invocation of <code>f</code> for every value.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L280-L306">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.mapfoldl-Tuple{Any, Any, Any}" href="#Base.mapfoldl-Tuple{Any, Any, Any}"><code>Base.mapfoldl</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mapfoldl(f, op, itr; [init])</code></pre><p>Like <a href="collections.html#Base.mapreduce-Tuple{Any, Any, Any}"><code>mapreduce</code></a>, but with guaranteed left associativity, as in <a href="collections.html#Base.foldl-Tuple{Any, Any}"><code>foldl</code></a>. If provided, the keyword argument <code>init</code> will be used exactly once. In general, it will be necessary to provide <code>init</code> to work with empty collections.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L168-L174">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.mapfoldr-Tuple{Any, Any, Any}" href="#Base.mapfoldr-Tuple{Any, Any, Any}"><code>Base.mapfoldr</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mapfoldr(f, op, itr; [init])</code></pre><p>Like <a href="collections.html#Base.mapreduce-Tuple{Any, Any, Any}"><code>mapreduce</code></a>, but with guaranteed right associativity, as in <a href="collections.html#Base.foldr-Tuple{Any, Any}"><code>foldr</code></a>. If provided, the keyword argument <code>init</code> will be used exactly once. In general, it will be necessary to provide <code>init</code> to work with empty collections.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L216-L222">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.first" href="#Base.first"><code>Base.first</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">first(coll)</code></pre><p>Get the first element of an iterable collection. Return the start point of an <a href="collections.html#Base.AbstractRange"><code>AbstractRange</code></a> even if it is empty.</p><p>See also: <a href="iterators.html#Base.Iterators.only"><code>only</code></a>, <a href="collections.html#Base.firstindex"><code>firstindex</code></a>, <a href="collections.html#Base.last"><code>last</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; first(2:2:10)
2

julia&gt; first([1; 2; 3; 4])
1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L454-L470">source</a></section><section><div><pre><code class="language-julia hljs">first(itr, n::Integer)</code></pre><p>Get the first <code>n</code> elements of the iterable collection <code>itr</code>, or fewer elements if <code>itr</code> is not long enough.</p><p>See also: <a href="strings.html#Base.startswith"><code>startswith</code></a>, <a href="iterators.html#Base.Iterators.take"><code>Iterators.take</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>This method requires at least Julia 1.6.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; first([&quot;foo&quot;, &quot;bar&quot;, &quot;qux&quot;], 2)
2-element Vector{String}:
 &quot;foo&quot;
 &quot;bar&quot;

julia&gt; first(1:6, 10)
1:6

julia&gt; first(Bool[], 1)
Bool[]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L477-L501">source</a></section><section><div><pre><code class="language-julia hljs">first(s::AbstractString, n::Integer)</code></pre><p>Get a string consisting of the first <code>n</code> characters of <code>s</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; first(&quot;∀ϵ≠0: ϵ²&gt;0&quot;, 0)
&quot;&quot;

julia&gt; first(&quot;∀ϵ≠0: ϵ²&gt;0&quot;, 1)
&quot;∀&quot;

julia&gt; first(&quot;∀ϵ≠0: ϵ²&gt;0&quot;, 3)
&quot;∀ϵ≠&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L690-L706">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.last" href="#Base.last"><code>Base.last</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">last(coll)</code></pre><p>Get the last element of an ordered collection, if it can be computed in O(1) time. This is accomplished by calling <a href="collections.html#Base.lastindex"><code>lastindex</code></a> to get the last index. Return the end point of an <a href="collections.html#Base.AbstractRange"><code>AbstractRange</code></a> even if it is empty.</p><p>See also <a href="collections.html#Base.first"><code>first</code></a>, <a href="strings.html#Base.endswith"><code>endswith</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; last(1:2:10)
9

julia&gt; last([1; 2; 3; 4])
4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L509-L526">source</a></section><section><div><pre><code class="language-julia hljs">last(itr, n::Integer)</code></pre><p>Get the last <code>n</code> elements of the iterable collection <code>itr</code>, or fewer elements if <code>itr</code> is not long enough.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>This method requires at least Julia 1.6.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; last([&quot;foo&quot;, &quot;bar&quot;, &quot;qux&quot;], 2)
2-element Vector{String}:
 &quot;bar&quot;
 &quot;qux&quot;

julia&gt; last(1:6, 10)
1:6

julia&gt; last(Float64[], 1)
Float64[]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L529-L551">source</a></section><section><div><pre><code class="language-julia hljs">last(s::AbstractString, n::Integer)</code></pre><p>Get a string consisting of the last <code>n</code> characters of <code>s</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; last(&quot;∀ϵ≠0: ϵ²&gt;0&quot;, 0)
&quot;&quot;

julia&gt; last(&quot;∀ϵ≠0: ϵ²&gt;0&quot;, 1)
&quot;0&quot;

julia&gt; last(&quot;∀ϵ≠0: ϵ²&gt;0&quot;, 3)
&quot;²&gt;0&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L709-L725">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.front" href="#Base.front"><code>Base.front</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">front(x::Tuple)::Tuple</code></pre><p>Return a <code>Tuple</code> consisting of all but the last component of <code>x</code>.</p><p>See also: <a href="collections.html#Base.first"><code>first</code></a>, <a href="collections.html#Base.tail"><code>tail</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Base.front((1,2,3))
(1, 2)

julia&gt; Base.front(())
ERROR: ArgumentError: Cannot call front on an empty tuple.</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/tuple.jl#L324-L339">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.tail" href="#Base.tail"><code>Base.tail</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">tail(x::Tuple)::Tuple</code></pre><p>Return a <code>Tuple</code> consisting of all but the first component of <code>x</code>.</p><p>See also: <a href="collections.html#Base.front"><code>front</code></a>, <a href="collections.html#Base.rest"><code>rest</code></a>, <a href="collections.html#Base.first"><code>first</code></a>, <a href="iterators.html#Base.Iterators.peel"><code>Iterators.peel</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Base.tail((1,2,3))
(2, 3)

julia&gt; Base.tail(())
ERROR: ArgumentError: Cannot call tail on an empty tuple.</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/essentials.jl#L498-L513">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.step" href="#Base.step"><code>Base.step</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">step(r)</code></pre><p>Get the step size of an <a href="collections.html#Base.AbstractRange"><code>AbstractRange</code></a> object.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; step(1:10)
1

julia&gt; step(1:2:10)
2

julia&gt; step(2.5:0.3:10.9)
0.3

julia&gt; step(range(2.5, stop=10.9, length=85))
0.1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/range.jl#L685-L704">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.collect-Tuple{Any}" href="#Base.collect-Tuple{Any}"><code>Base.collect</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">collect(collection)</code></pre><p>Return an <code>Array</code> of all items in a collection or iterator. For dictionaries, returns a <code>Vector</code> of <code>key=&gt;value</code> <a href="collections.html#Core.Pair">Pair</a>s. If the argument is array-like or is an iterator with the <a href="collections.html#Base.IteratorSize"><code>HasShape</code></a> trait, the result will have the same shape and number of dimensions as the argument.</p><p>Used by <a href="../manual/arrays.html#man-comprehensions">comprehensions</a> to turn a <a href="../manual/arrays.html#man-generators">generator expression</a> into an <code>Array</code>. Thus, <em>on generators</em>, the square-brackets notation may be used instead of calling <code>collect</code>, see second example.</p><p><strong>Examples</strong></p><p>Collect items from a <code>UnitRange{Int64}</code> collection:</p><pre><code class="language-julia-repl hljs">julia&gt; collect(1:3)
3-element Vector{Int64}:
 1
 2
 3</code></pre><p>Collect items from a generator (same output as <code>[x^2 for x in 1:3]</code>):</p><pre><code class="language-julia-repl hljs">julia&gt; collect(x^2 for x in 1:3)
3-element Vector{Int64}:
 1
 4
 9</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L682-L715">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.collect-Tuple{Type, Any}" href="#Base.collect-Tuple{Type, Any}"><code>Base.collect</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">collect(element_type, collection)</code></pre><p>Return an <code>Array</code> with the given element type of all items in a collection or iterable. The result has the same shape and number of dimensions as <code>collection</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; collect(Float64, 1:2:5)
3-element Vector{Float64}:
 1.0
 3.0
 5.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L632-L646">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.filter" href="#Base.filter"><code>Base.filter</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">filter(f, a)</code></pre><p>Return a copy of collection <code>a</code>, removing elements for which <code>f</code> is <code>false</code>. The function <code>f</code> is passed one argument.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.4</header><div class="admonition-body"><p>Support for <code>a</code> as a tuple requires at least Julia 1.4.</p></div></div><p>See also: <a href="collections.html#Base.filter!"><code>filter!</code></a>, <a href="iterators.html#Base.Iterators.filter"><code>Iterators.filter</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = 1:10
1:10

julia&gt; filter(isodd, a)
5-element Vector{Int64}:
 1
 3
 5
 7
 9</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L2847-L2871">source</a></section><section><div><pre><code class="language-julia hljs">filter(f)</code></pre><p>Create a function that filters its arguments with function <code>f</code> using <a href="collections.html#Base.filter"><code>filter</code></a>, i.e. a function equivalent to <code>x -&gt; filter(f, x)</code>.</p><p>The returned function is of type <code>Base.Fix1{typeof(filter)}</code>, which can be used to implement specialized methods.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; (1, 2, Inf, 4, NaN, 6) |&gt; filter(isfinite)
(1, 2, 4, 6)

julia&gt; map(filter(iseven), [1:3, 2:4, 3:5])
3-element Vector{Vector{Int64}}:
 [2]
 [2, 4]
 [4]</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p>This method requires at least Julia 1.9.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L2934-L2956">source</a></section><section><div><pre><code class="language-julia hljs">filter(f, d::AbstractDict)</code></pre><p>Return a copy of <code>d</code>, removing elements for which <code>f</code> is <code>false</code>. The function <code>f</code> is passed <code>key=&gt;value</code> pairs.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; d = Dict(1=&gt;&quot;a&quot;, 2=&gt;&quot;b&quot;)
Dict{Int64, String} with 2 entries:
  2 =&gt; &quot;b&quot;
  1 =&gt; &quot;a&quot;

julia&gt; filter(p-&gt;isodd(p.first), d)
Dict{Int64, String} with 1 entry:
  1 =&gt; &quot;a&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractdict.jl#L459-L476">source</a></section><section><div><pre><code class="language-julia hljs">filter(f, itr::SkipMissing{&lt;:AbstractArray})</code></pre><p>Return a vector similar to the array wrapped by the given <code>SkipMissing</code> iterator but with all missing elements and those for which <code>f</code> returns <code>false</code> removed.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p>This method requires Julia 1.2 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = [1 2; missing 4]
2×2 Matrix{Union{Missing, Int64}}:
 1         2
  missing  4

julia&gt; filter(isodd, skipmissing(x))
1-element Vector{Int64}:
 1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/missing.jl#L364-L384">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.filter!" href="#Base.filter!"><code>Base.filter!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">filter!(f, a)</code></pre><p>Update collection <code>a</code>, removing elements for which <code>f</code> is <code>false</code>. The function <code>f</code> is passed one argument.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; filter!(isodd, Vector(1:10))
5-element Vector{Int64}:
 1
 3
 5
 7
 9</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L2901-L2917">source</a></section><section><div><pre><code class="language-julia hljs">filter!(f, d::AbstractDict)</code></pre><p>Update <code>d</code>, removing elements for which <code>f</code> is <code>false</code>. The function <code>f</code> is passed <code>key=&gt;value</code> pairs.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; d = Dict(1=&gt;&quot;a&quot;, 2=&gt;&quot;b&quot;, 3=&gt;&quot;c&quot;)
Dict{Int64, String} with 3 entries:
  2 =&gt; &quot;b&quot;
  3 =&gt; &quot;c&quot;
  1 =&gt; &quot;a&quot;

julia&gt; filter!(p-&gt;isodd(p.first), d)
Dict{Int64, String} with 2 entries:
  3 =&gt; &quot;c&quot;
  1 =&gt; &quot;a&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractdict.jl#L417-L436">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.replace-Tuple{Any, Vararg{Pair}}" href="#Base.replace-Tuple{Any, Vararg{Pair}}"><code>Base.replace</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">replace(A, old_new::Pair...; [count::Integer])</code></pre><p>Return a copy of collection <code>A</code> where, for each pair <code>old=&gt;new</code> in <code>old_new</code>, all occurrences of <code>old</code> are replaced by <code>new</code>. Equality is determined using <a href="base.html#Base.isequal"><code>isequal</code></a>. If <code>count</code> is specified, then replace at most <code>count</code> occurrences in total.</p><p>The element type of the result is chosen using promotion (see <a href="base.html#Base.promote_type"><code>promote_type</code></a>) based on the element type of <code>A</code> and on the types of the <code>new</code> values in pairs. If <code>count</code> is omitted and the element type of <code>A</code> is a <code>Union</code>, the element type of the result will not include singleton types which are replaced with values of a different type: for example, <code>Union{T,Missing}</code> will become <code>T</code> if <code>missing</code> is replaced.</p><p>See also <a href="collections.html#Base.replace!"><code>replace!</code></a>, <a href="collections.html#Base.splice!"><code>splice!</code></a>, <a href="collections.html#Base.delete!"><code>delete!</code></a>, <a href="collections.html#Base.insert!"><code>insert!</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Version 1.7 is required to replace elements of a <code>Tuple</code>.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; replace([1, 2, 1, 3], 1=&gt;0, 2=&gt;4, count=2)
4-element Vector{Int64}:
 0
 4
 1
 3

julia&gt; replace([1, missing], missing=&gt;0)
2-element Vector{Int64}:
 1
 0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/set.jl#L780-L814">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.replace-Tuple{Union{Function, Type}, Any}" href="#Base.replace-Tuple{Union{Function, Type}, Any}"><code>Base.replace</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">replace(new::Union{Function, Type}, A; [count::Integer])</code></pre><p>Return a copy of <code>A</code> where each value <code>x</code> in <code>A</code> is replaced by <code>new(x)</code>. If <code>count</code> is specified, then replace at most <code>count</code> values in total (replacements being defined as <code>new(x) !== x</code>).</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Version 1.7 is required to replace elements of a <code>Tuple</code>.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; replace(x -&gt; isodd(x) ? 2x : x, [1, 2, 3, 4])
4-element Vector{Int64}:
 2
 2
 6
 4

julia&gt; replace(Dict(1=&gt;2, 3=&gt;4)) do kv
           first(kv) &lt; 3 ? first(kv)=&gt;3 : kv
       end
Dict{Int64, Int64} with 2 entries:
  3 =&gt; 4
  1 =&gt; 3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/set.jl#L841-L867">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.replace!" href="#Base.replace!"><code>Base.replace!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">replace!(A, old_new::Pair...; [count::Integer])</code></pre><p>For each pair <code>old=&gt;new</code> in <code>old_new</code>, replace all occurrences of <code>old</code> in collection <code>A</code> by <code>new</code>. Equality is determined using <a href="base.html#Base.isequal"><code>isequal</code></a>. If <code>count</code> is specified, then replace at most <code>count</code> occurrences in total. See also <a href="collections.html#Base.replace-Tuple{Any, Vararg{Pair}}"><code>replace</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; replace!([1, 2, 1, 3], 1=&gt;0, 2=&gt;4, count=2)
4-element Vector{Int64}:
 0
 4
 1
 3

julia&gt; replace!(Set([1, 2, 3]), 1=&gt;0)
Set{Int64} with 3 elements:
  0
  2
  3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/set.jl#L710-L734">source</a></section><section><div><pre><code class="language-julia hljs">replace!(new::Union{Function, Type}, A; [count::Integer])</code></pre><p>Replace each element <code>x</code> in collection <code>A</code> by <code>new(x)</code>. If <code>count</code> is specified, then replace at most <code>count</code> values in total (replacements being defined as <code>new(x) !== x</code>).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; replace!(x -&gt; isodd(x) ? 2x : x, [1, 2, 3, 4])
4-element Vector{Int64}:
 2
 2
 6
 4

julia&gt; replace!(Dict(1=&gt;2, 3=&gt;4)) do kv
           first(kv) &lt; 3 ? first(kv)=&gt;3 : kv
       end
Dict{Int64, Int64} with 2 entries:
  3 =&gt; 4
  1 =&gt; 3

julia&gt; replace!(x-&gt;2x, Set([3, 6]))
Set{Int64} with 2 elements:
  6
  12</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/set.jl#L748-L776">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.rest" href="#Base.rest"><code>Base.rest</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.rest(collection[, itr_state])</code></pre><p>Generic function for taking the tail of <code>collection</code>, starting from a specific iteration state <code>itr_state</code>. Return a <code>Tuple</code>, if <code>collection</code> itself is a <code>Tuple</code>, a subtype of <code>AbstractVector</code>, if <code>collection</code> is an <code>AbstractArray</code>, a subtype of <code>AbstractString</code> if <code>collection</code> is an <code>AbstractString</code>, and an arbitrary iterator, falling back to <code>Iterators.rest(collection[, itr_state])</code>, otherwise.</p><p>Can be overloaded for user-defined collection types to customize the behavior of <a href="../manual/functions.html#destructuring-assignment">slurping in assignments</a> in final position, like <code>a, b... = collection</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p><code>Base.rest</code> requires at least Julia 1.6.</p></div></div><p>See also: <a href="collections.html#Base.first"><code>first</code></a>, <a href="iterators.html#Base.Iterators.rest"><code>Iterators.rest</code></a>, <a href="collections.html#Base.split_rest"><code>Base.split_rest</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; first, state = iterate(a)
(1, 2)

julia&gt; first, Base.rest(a, state)
(1, [3, 2, 4])</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/tuple.jl#L172-L202">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.split_rest" href="#Base.split_rest"><code>Base.split_rest</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.split_rest(collection, n::Int[, itr_state]) -&gt; (rest_but_n, last_n)</code></pre><p>Generic function for splitting the tail of <code>collection</code>, starting from a specific iteration state <code>itr_state</code>. Returns a tuple of two new collections. The first one contains all elements of the tail but the <code>n</code> last ones, which make up the second collection.</p><p>The type of the first collection generally follows that of <a href="collections.html#Base.rest"><code>Base.rest</code></a>, except that the fallback case is not lazy, but is collected eagerly into a vector.</p><p>Can be overloaded for user-defined collection types to customize the behavior of <a href="../manual/functions.html#destructuring-assignment">slurping in assignments</a> in non-final position, like <code>a, b..., c = collection</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p><code>Base.split_rest</code> requires at least Julia 1.9.</p></div></div><p>See also: <a href="collections.html#Base.rest"><code>Base.rest</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; first, state = iterate(a)
(1, 2)

julia&gt; first, Base.split_rest(a, 1, state)
(1, ([3, 2], [4]))</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/tuple.jl#L210-L241">source</a></section></article><h2 id="Indexable-Collections"><a class="docs-heading-anchor" href="#Indexable-Collections">Indexable Collections</a><a id="Indexable-Collections-1"></a><a class="docs-heading-anchor-permalink" href="#Indexable-Collections" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.getindex" href="#Base.getindex"><code>Base.getindex</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">getindex(collection, key...)</code></pre><p>Retrieve the value(s) stored at the given key or index within a collection. The syntax <code>a[i,j,...]</code> is converted by the compiler to <code>getindex(a, i, j, ...)</code>.</p><p>See also <a href="collections.html#Base.get"><code>get</code></a>, <a href="collections.html#Base.keys"><code>keys</code></a>, <a href="arrays.html#Base.eachindex"><code>eachindex</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = Dict(&quot;a&quot; =&gt; 1, &quot;b&quot; =&gt; 2)
Dict{String, Int64} with 2 entries:
  &quot;b&quot; =&gt; 2
  &quot;a&quot; =&gt; 1

julia&gt; getindex(A, &quot;a&quot;)
1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L906-L924">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.setindex!" href="#Base.setindex!"><code>Base.setindex!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">setindex!(collection, value, key...)</code></pre><p>Store the given value at the given key or index within a collection. The syntax <code>a[i,j,...] = x</code> is converted by the compiler to <code>(setindex!(a, x, i, j, ...); x)</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = Dict(&quot;a&quot;=&gt;1)
Dict{String, Int64} with 1 entry:
  &quot;a&quot; =&gt; 1

julia&gt; setindex!(a, 2, &quot;b&quot;)
Dict{String, Int64} with 2 entries:
  &quot;b&quot; =&gt; 2
  &quot;a&quot; =&gt; 1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L964-L981">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.firstindex" href="#Base.firstindex"><code>Base.firstindex</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">firstindex(collection) -&gt; Integer
firstindex(collection, d) -&gt; Integer</code></pre><p>Return the first index of <code>collection</code>. If <code>d</code> is given, return the first index of <code>collection</code> along dimension <code>d</code>.</p><p>The syntaxes <code>A[begin]</code> and <code>A[1, begin]</code> lower to <code>A[firstindex(A)]</code> and <code>A[1, firstindex(A, 2)]</code>, respectively.</p><p>See also: <a href="collections.html#Base.first"><code>first</code></a>, <a href="arrays.html#Base.axes-Tuple{Any}"><code>axes</code></a>, <a href="collections.html#Base.lastindex"><code>lastindex</code></a>, <a href="arrays.html#Base.nextind"><code>nextind</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; firstindex([1,2,4])
1

julia&gt; firstindex(rand(3,4,5), 2)
1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L429-L448">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.lastindex" href="#Base.lastindex"><code>Base.lastindex</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lastindex(collection) -&gt; Integer
lastindex(collection, d) -&gt; Integer</code></pre><p>Return the last index of <code>collection</code>. If <code>d</code> is given, return the last index of <code>collection</code> along dimension <code>d</code>.</p><p>The syntaxes <code>A[end]</code> and <code>A[end, end]</code> lower to <code>A[lastindex(A)]</code> and <code>A[lastindex(A, 1), lastindex(A, 2)]</code>, respectively.</p><p>See also: <a href="arrays.html#Base.axes-Tuple{Any}"><code>axes</code></a>, <a href="collections.html#Base.firstindex"><code>firstindex</code></a>, <a href="arrays.html#Base.eachindex"><code>eachindex</code></a>, <a href="arrays.html#Base.prevind"><code>prevind</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; lastindex([1,2,4])
3

julia&gt; lastindex(rand(3,4,5), 2)
4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L406-L425">source</a></section></article><p>Fully implemented by:</p><ul><li><a href="arrays.html#Core.Array"><code>Array</code></a></li><li><a href="arrays.html#Base.BitArray"><code>BitArray</code></a></li><li><a href="arrays.html#Core.AbstractArray"><code>AbstractArray</code></a></li><li><code>SubArray</code></li></ul><p>Partially implemented by:</p><ul><li><a href="collections.html#Base.AbstractRange"><code>AbstractRange</code></a></li><li><a href="collections.html#Base.UnitRange"><code>UnitRange</code></a></li><li><a href="base.html#Core.Tuple"><code>Tuple</code></a></li><li><a href="strings.html#Core.AbstractString"><code>AbstractString</code></a></li><li><a href="collections.html#Base.Dict"><code>Dict</code></a></li><li><a href="collections.html#Base.IdDict"><code>IdDict</code></a></li><li><a href="collections.html#Base.WeakKeyDict"><code>WeakKeyDict</code></a></li><li><a href="base.html#Core.NamedTuple"><code>NamedTuple</code></a></li></ul><h2 id="Dictionaries"><a class="docs-heading-anchor" href="#Dictionaries">Dictionaries</a><a id="Dictionaries-1"></a><a class="docs-heading-anchor-permalink" href="#Dictionaries" title="Permalink"></a></h2><p><a href="collections.html#Base.Dict"><code>Dict</code></a> is the standard dictionary. Its implementation uses <a href="base.html#Base.hash"><code>hash</code></a> as the hashing function for the key, and <a href="base.html#Base.isequal"><code>isequal</code></a> to determine equality. Define these two functions for custom types to override how they are stored in a hash table.</p><p><a href="collections.html#Base.IdDict"><code>IdDict</code></a> is a special hash table where the keys are always object identities.</p><p><a href="collections.html#Base.WeakKeyDict"><code>WeakKeyDict</code></a> is a hash table implementation where the keys are weak references to objects, and thus may be garbage collected even when referenced in a hash table. Like <code>Dict</code> it uses <code>hash</code> for hashing and <code>isequal</code> for equality, unlike <code>Dict</code> it does not convert keys on insertion.</p><p><a href="collections.html#Base.Dict"><code>Dict</code></a>s can be created by passing pair objects constructed with <code>=&gt;</code> to a <a href="collections.html#Base.Dict"><code>Dict</code></a> constructor: <code>Dict(&quot;A&quot;=&gt;1, &quot;B&quot;=&gt;2)</code>. This call will attempt to infer type information from the keys and values (i.e. this example creates a <code>Dict{String, Int64}</code>). To explicitly specify types use the syntax <code>Dict{KeyType,ValueType}(...)</code>. For example, <code>Dict{String,Int32}(&quot;A&quot;=&gt;1, &quot;B&quot;=&gt;2)</code>.</p><p>Dictionaries may also be created with generators. For example, <code>Dict(i =&gt; f(i) for i = 1:10)</code>.</p><p>Given a dictionary <code>D</code>, the syntax <code>D[x]</code> returns the value of key <code>x</code> (if it exists) or throws an error, and <code>D[x] = y</code> stores the key-value pair <code>x =&gt; y</code> in <code>D</code> (replacing any existing value for the key <code>x</code>).  Multiple arguments to <code>D[...]</code> are converted to tuples; for example, the syntax <code>D[x,y]</code>  is equivalent to <code>D[(x,y)]</code>, i.e. it refers to the value keyed by the tuple <code>(x,y)</code>.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.AbstractDict" href="#Base.AbstractDict"><code>Base.AbstractDict</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AbstractDict{K, V}</code></pre><p>Supertype for dictionary-like types with keys of type <code>K</code> and values of type <code>V</code>. <a href="collections.html#Base.Dict"><code>Dict</code></a>, <a href="collections.html#Base.IdDict"><code>IdDict</code></a> and other types are subtypes of this. An <code>AbstractDict{K, V}</code> should be an iterator of <code>Pair{K, V}</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/essentials.jl#L28-L34">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Dict" href="#Base.Dict"><code>Base.Dict</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Dict([itr])</code></pre><p><code>Dict{K,V}()</code> constructs a hash table with keys of type <code>K</code> and values of type <code>V</code>. Keys are compared with <a href="base.html#Base.isequal"><code>isequal</code></a> and hashed with <a href="base.html#Base.hash"><code>hash</code></a>.</p><p>Given a single iterable argument, constructs a <a href="collections.html#Base.Dict"><code>Dict</code></a> whose key-value pairs are taken from 2-tuples <code>(key,value)</code> generated by the argument.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Dict([(&quot;A&quot;, 1), (&quot;B&quot;, 2)])
Dict{String, Int64} with 2 entries:
  &quot;B&quot; =&gt; 2
  &quot;A&quot; =&gt; 1</code></pre><p>Alternatively, a sequence of pair arguments may be passed.</p><pre><code class="language-julia-repl hljs">julia&gt; Dict(&quot;A&quot;=&gt;1, &quot;B&quot;=&gt;2)
Dict{String, Int64} with 2 entries:
  &quot;B&quot; =&gt; 2
  &quot;A&quot; =&gt; 1</code></pre><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Keys are allowed to be mutable, but if you do mutate stored keys, the hash table may become internally inconsistent, in which case the <code>Dict</code> will not work properly. <a href="collections.html#Base.IdDict"><code>IdDict</code></a> can be an alternative if you need to mutate keys.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/dict.jl#L31-L64">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.IdDict" href="#Base.IdDict"><code>Base.IdDict</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">IdDict([itr])</code></pre><p><code>IdDict{K,V}()</code> constructs a hash table using <a href="base.html#Base.objectid"><code>objectid</code></a> as hash and <code>===</code> as equality with keys of type <code>K</code> and values of type <code>V</code>. See <a href="collections.html#Base.Dict"><code>Dict</code></a> for further help and <a href="collections.html#Base.IdSet"><code>IdSet</code></a> for the set version of this.</p><p>In the example below, the <code>Dict</code> keys are all <code>isequal</code> and therefore get hashed the same, so they get overwritten. The <code>IdDict</code> hashes by object-id, and thus preserves the 3 different keys.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Dict(true =&gt; &quot;yes&quot;, 1 =&gt; &quot;no&quot;, 1.0 =&gt; &quot;maybe&quot;)
Dict{Real, String} with 1 entry:
  1.0 =&gt; &quot;maybe&quot;

julia&gt; IdDict(true =&gt; &quot;yes&quot;, 1 =&gt; &quot;no&quot;, 1.0 =&gt; &quot;maybe&quot;)
IdDict{Any, String} with 3 entries:
  true =&gt; &quot;yes&quot;
  1.0  =&gt; &quot;maybe&quot;
  1    =&gt; &quot;no&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/iddict.jl#L3-L26">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.WeakKeyDict" href="#Base.WeakKeyDict"><code>Base.WeakKeyDict</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">WeakKeyDict([itr])</code></pre><p><code>WeakKeyDict()</code> constructs a hash table where the keys are weak references to objects which may be garbage collected even when referenced in a hash table.</p><p>See <a href="collections.html#Base.Dict"><code>Dict</code></a> for further help.  Note, unlike <a href="collections.html#Base.Dict"><code>Dict</code></a>, <code>WeakKeyDict</code> does not convert keys on insertion, as this would imply the key object was unreferenced anywhere before insertion.</p><p>See also <a href="base.html#Core.WeakRef"><code>WeakRef</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/weakkeydict.jl#L5-L17">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.ImmutableDict" href="#Base.ImmutableDict"><code>Base.ImmutableDict</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ImmutableDict</code></pre><p><code>ImmutableDict</code> is a dictionary implemented as an immutable linked list, which is optimal for small dictionaries that are constructed over many individual insertions. Note that it is not possible to remove a value, although it can be partially overridden and hidden by inserting a new value with the same key.</p><pre><code class="nohighlight hljs">ImmutableDict(KV::Pair)</code></pre><p>Create a new entry in the <code>ImmutableDict</code> for a <code>key =&gt; value</code> pair</p><ul><li>use <code>(key =&gt; value) in dict</code> to see if this particular combination is in the properties set</li><li>use <code>get(dict, key, default)</code> to retrieve the most recent value for a particular key</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/dict.jl#L778-L793">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.PersistentDict" href="#Base.PersistentDict"><code>Base.PersistentDict</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">PersistentDict</code></pre><p><code>PersistentDict</code> is a dictionary implemented as an hash array mapped trie, which is optimal for situations where you need persistence, each operation returns a new dictionary separate from the previous one, but the underlying implementation is space-efficient and may share storage across multiple separate dictionaries.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>It behaves like an IdDict.</p></div></div><pre><code class="language-julia hljs">PersistentDict(KV::Pair)</code></pre><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; dict = Base.PersistentDict(:a=&gt;1)
Base.PersistentDict{Symbol, Int64} with 1 entry:
  :a =&gt; 1

julia&gt; dict2 = Base.delete(dict, :a)
Base.PersistentDict{Symbol, Int64}()

julia&gt; dict3 = Base.PersistentDict(dict, :a=&gt;2)
Base.PersistentDict{Symbol, Int64} with 1 entry:
  :a =&gt; 2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/dict.jl#L907-L937">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.haskey" href="#Base.haskey"><code>Base.haskey</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">haskey(collection, key) -&gt; Bool</code></pre><p>Determine whether a collection has a mapping for a given <code>key</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; D = Dict(&#39;a&#39;=&gt;2, &#39;b&#39;=&gt;3)
Dict{Char, Int64} with 2 entries:
  &#39;a&#39; =&gt; 2
  &#39;b&#39; =&gt; 3

julia&gt; haskey(D, &#39;a&#39;)
true

julia&gt; haskey(D, &#39;c&#39;)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/dict.jl#L529-L547">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.get" href="#Base.get"><code>Base.get</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">get(collection, key, default)</code></pre><p>Return the value stored for the given key, or the given default value if no mapping for the key is present.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>For tuples and numbers, this function requires at least Julia 1.7.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; d = Dict(&quot;a&quot;=&gt;1, &quot;b&quot;=&gt;2);

julia&gt; get(d, &quot;a&quot;, 3)
1

julia&gt; get(d, &quot;c&quot;, 3)
3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/dict.jl#L480-L499">source</a></section><section><div><pre><code class="language-julia hljs">get(f::Union{Function, Type}, collection, key)</code></pre><p>Return the value stored for the given key, or if no mapping for the key is present, return <code>f()</code>.  Use <a href="collections.html#Base.get!"><code>get!</code></a> to also store the default value in the dictionary.</p><p>This is intended to be called using <code>do</code> block syntax</p><pre><code class="language-julia hljs">get(dict, key) do
    # default value calculated here
    time()
end</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/dict.jl#L507-L521">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.get!" href="#Base.get!"><code>Base.get!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">get!(collection, key, default)</code></pre><p>Return the value stored for the given key, or if no mapping for the key is present, store <code>key =&gt; default</code>, and return <code>default</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; d = Dict(&quot;a&quot;=&gt;1, &quot;b&quot;=&gt;2, &quot;c&quot;=&gt;3);

julia&gt; get!(d, &quot;a&quot;, 5)
1

julia&gt; get!(d, &quot;d&quot;, 4)
4

julia&gt; d
Dict{String, Int64} with 4 entries:
  &quot;c&quot; =&gt; 3
  &quot;b&quot; =&gt; 2
  &quot;a&quot; =&gt; 1
  &quot;d&quot; =&gt; 4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/dict.jl#L385-L408">source</a></section><section><div><pre><code class="language-julia hljs">get!(f::Union{Function, Type}, collection, key)</code></pre><p>Return the value stored for the given key, or if no mapping for the key is present, store <code>key =&gt; f()</code>, and return <code>f()</code>.</p><p>This is intended to be called using <code>do</code> block syntax.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; squares = Dict{Int, Int}();

julia&gt; function get_square!(d, i)
           get!(d, i) do
               i^2
           end
       end
get_square! (generic function with 1 method)

julia&gt; get_square!(squares, 2)
4

julia&gt; squares
Dict{Int64, Int64} with 1 entry:
  2 =&gt; 4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/dict.jl#L411-L437">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.getkey" href="#Base.getkey"><code>Base.getkey</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">getkey(collection, key, default)</code></pre><p>Return the key matching argument <code>key</code> if one exists in <code>collection</code>, otherwise return <code>default</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; D = Dict(&#39;a&#39;=&gt;2, &#39;b&#39;=&gt;3)
Dict{Char, Int64} with 2 entries:
  &#39;a&#39; =&gt; 2
  &#39;b&#39; =&gt; 3

julia&gt; getkey(D, &#39;a&#39;, 1)
&#39;a&#39;: ASCII/Unicode U+0061 (category Ll: Letter, lowercase)

julia&gt; getkey(D, &#39;d&#39;, &#39;a&#39;)
&#39;a&#39;: ASCII/Unicode U+0061 (category Ll: Letter, lowercase)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/dict.jl#L551-L569">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.delete!" href="#Base.delete!"><code>Base.delete!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">delete!(collection, key)</code></pre><p>Delete the mapping for the given key in a collection, if any, and return the collection.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; d = Dict(&quot;a&quot;=&gt;1, &quot;b&quot;=&gt;2)
Dict{String, Int64} with 2 entries:
  &quot;b&quot; =&gt; 2
  &quot;a&quot; =&gt; 1

julia&gt; delete!(d, &quot;b&quot;)
Dict{String, Int64} with 1 entry:
  &quot;a&quot; =&gt; 1

julia&gt; delete!(d, &quot;b&quot;) # d is left unchanged
Dict{String, Int64} with 1 entry:
  &quot;a&quot; =&gt; 1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/dict.jl#L651-L671">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.pop!-Tuple{Any, Any, Any}" href="#Base.pop!-Tuple{Any, Any, Any}"><code>Base.pop!</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">pop!(collection, key[, default])</code></pre><p>Delete and return the mapping for <code>key</code> if it exists in <code>collection</code>, otherwise return <code>default</code>, or throw an error if <code>default</code> is not specified.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; d = Dict(&quot;a&quot;=&gt;1, &quot;b&quot;=&gt;2, &quot;c&quot;=&gt;3);

julia&gt; pop!(d, &quot;a&quot;)
1

julia&gt; pop!(d, &quot;d&quot;)
ERROR: KeyError: key &quot;d&quot; not found
Stacktrace:
[...]

julia&gt; pop!(d, &quot;e&quot;, 4)
4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/dict.jl#L586-L607">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.keys" href="#Base.keys"><code>Base.keys</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">keys(iterator)</code></pre><p>For an iterator or collection that has keys and values (e.g. arrays and dictionaries), return an iterator over the keys.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractdict.jl#L75-L80">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.values" href="#Base.values"><code>Base.values</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">values(iterator)</code></pre><p>For an iterator or collection that has keys and values, return an iterator over the values. This function simply returns its argument by default, since the elements of a general iterator are normally considered its &quot;values&quot;.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; d = Dict(&quot;a&quot;=&gt;1, &quot;b&quot;=&gt;2);

julia&gt; values(d)
ValueIterator for a Dict{String, Int64} with 2 entries. Values:
  2
  1

julia&gt; values([2])
1-element Vector{Int64}:
 2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/essentials.jl#L1124-L1145">source</a></section><section><div><pre><code class="language-julia hljs">values(a::AbstractDict)</code></pre><p>Return an iterator over all values in a collection. <code>collect(values(a))</code> returns an array of values. When the values are stored internally in a hash table, as is the case for <code>Dict</code>, the order in which they are returned may vary. But <code>keys(a)</code>, <code>values(a)</code> and <code>pairs(a)</code> all iterate <code>a</code> and return the elements in the same order.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; D = Dict(&#39;a&#39;=&gt;2, &#39;b&#39;=&gt;3)
Dict{Char, Int64} with 2 entries:
  &#39;a&#39; =&gt; 2
  &#39;b&#39; =&gt; 3

julia&gt; collect(values(D))
2-element Vector{Int64}:
 2
 3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractdict.jl#L109-L132">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.pairs" href="#Base.pairs"><code>Base.pairs</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">pairs(IndexLinear(), A)
pairs(IndexCartesian(), A)
pairs(IndexStyle(A), A)</code></pre><p>An iterator that accesses each element of the array <code>A</code>, returning <code>i =&gt; x</code>, where <code>i</code> is the index for the element and <code>x = A[i]</code>. Identical to <code>pairs(A)</code>, except that the style of index can be selected. Also similar to <code>enumerate(A)</code>, except <code>i</code> will be a valid index for <code>A</code>, while <code>enumerate</code> always counts from 1 regardless of the indices of <code>A</code>.</p><p>Specifying <a href="arrays.html#Base.IndexLinear"><code>IndexLinear()</code></a> ensures that <code>i</code> will be an integer; specifying <a href="arrays.html#Base.IndexCartesian"><code>IndexCartesian()</code></a> ensures that <code>i</code> will be a <a href="arrays.html#Base.IteratorsMD.CartesianIndex"><code>Base.CartesianIndex</code></a>; specifying <code>IndexStyle(A)</code> chooses whichever has been defined as the native indexing style for array <code>A</code>.</p><p>Mutation of the bounds of the underlying array will invalidate this iterator.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [&quot;a&quot; &quot;d&quot;; &quot;b&quot; &quot;e&quot;; &quot;c&quot; &quot;f&quot;];

julia&gt; for (index, value) in pairs(IndexStyle(A), A)
           println(&quot;$index $value&quot;)
       end
1 a
2 b
3 c
4 d
5 e
6 f

julia&gt; S = view(A, 1:2, :);

julia&gt; for (index, value) in pairs(IndexStyle(S), S)
           println(&quot;$index $value&quot;)
       end
CartesianIndex(1, 1) a
CartesianIndex(2, 1) b
CartesianIndex(1, 2) d
CartesianIndex(2, 2) e</code></pre><p>See also <a href="arrays.html#Base.IndexStyle"><code>IndexStyle</code></a>, <a href="arrays.html#Base.axes-Tuple{Any}"><code>axes</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/iterators.jl#L228-L273">source</a></section><section><div><pre><code class="language-julia hljs">pairs(collection)</code></pre><p>Return an iterator over <code>key =&gt; value</code> pairs for any collection that maps a set of keys to a set of values. This includes arrays, where the keys are the array indices. When the entries are stored internally in a hash table, as is the case for <code>Dict</code>, the order in which they are returned may vary. But <code>keys(a)</code>, <code>values(a)</code> and <code>pairs(a)</code> all iterate <code>a</code> and return the elements in the same order.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = Dict(zip([&quot;a&quot;, &quot;b&quot;, &quot;c&quot;], [1, 2, 3]))
Dict{String, Int64} with 3 entries:
  &quot;c&quot; =&gt; 3
  &quot;b&quot; =&gt; 2
  &quot;a&quot; =&gt; 1

julia&gt; pairs(a)
Dict{String, Int64} with 3 entries:
  &quot;c&quot; =&gt; 3
  &quot;b&quot; =&gt; 2
  &quot;a&quot; =&gt; 1

julia&gt; foreach(println, pairs([&quot;a&quot;, &quot;b&quot;, &quot;c&quot;]))
1 =&gt; &quot;a&quot;
2 =&gt; &quot;b&quot;
3 =&gt; &quot;c&quot;

julia&gt; (;a=1, b=2, c=3) |&gt; pairs |&gt; collect
3-element Vector{Pair{Symbol, Int64}}:
 :a =&gt; 1
 :b =&gt; 2
 :c =&gt; 3

julia&gt; (;a=1, b=2, c=3) |&gt; collect
3-element Vector{Int64}:
 1
 2
 3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractdict.jl#L135-L177">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.merge" href="#Base.merge"><code>Base.merge</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">merge(initial::Face, others::Face...)</code></pre><p>Merge the properties of the <code>initial</code> face and <code>others</code>, with later faces taking priority.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/StyledStrings.jl/blob/056e843b2d428bb9735b03af0cff97e738ac7e14/src/faces.jl#L496-L501">source</a></section><section><div><pre><code class="language-julia hljs">merge(d::AbstractDict, others::AbstractDict...)</code></pre><p>Construct a merged collection from the given collections. If necessary, the types of the resulting collection will be promoted to accommodate the types of the merged collections. If the same key is present in another collection, the value for that key will be the value it has in the last collection listed. See also <a href="collections.html#Base.mergewith"><code>mergewith</code></a> for custom handling of values with the same key.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = Dict(&quot;foo&quot; =&gt; 0.0, &quot;bar&quot; =&gt; 42.0)
Dict{String, Float64} with 2 entries:
  &quot;bar&quot; =&gt; 42.0
  &quot;foo&quot; =&gt; 0.0

julia&gt; b = Dict(&quot;baz&quot; =&gt; 17, &quot;bar&quot; =&gt; 4711)
Dict{String, Int64} with 2 entries:
  &quot;bar&quot; =&gt; 4711
  &quot;baz&quot; =&gt; 17

julia&gt; merge(a, b)
Dict{String, Float64} with 3 entries:
  &quot;bar&quot; =&gt; 4711.0
  &quot;baz&quot; =&gt; 17.0
  &quot;foo&quot; =&gt; 0.0

julia&gt; merge(b, a)
Dict{String, Float64} with 3 entries:
  &quot;bar&quot; =&gt; 42.0
  &quot;baz&quot; =&gt; 17.0
  &quot;foo&quot; =&gt; 0.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractdict.jl#L325-L358">source</a></section><section><div><pre><code class="language-julia hljs">merge(a::NamedTuple, bs::NamedTuple...)</code></pre><p>Construct a new named tuple by merging two or more existing ones, in a left-associative manner. Merging proceeds left-to-right, between pairs of named tuples, and so the order of fields present in both the leftmost and rightmost named tuples take the same position as they are found in the leftmost named tuple. However, values are taken from matching fields in the rightmost named tuple that contains that field. Fields present in only the rightmost named tuple of a pair are appended at the end. A fallback is implemented for when only a single named tuple is supplied, with signature <code>merge(a::NamedTuple)</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p>Merging 3 or more <code>NamedTuple</code> requires at least Julia 1.1.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; merge((a=1, b=2, c=3), (b=4, d=5))
(a = 1, b = 4, c = 3, d = 5)</code></pre><pre><code class="language-julia-repl hljs">julia&gt; merge((a=1, b=2), (b=3, c=(d=1,)), (c=(d=2,),))
(a = 1, b = 3, c = (d = 2,))</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/namedtuple.jl#L309-L333">source</a></section><section><div><pre><code class="language-julia hljs">merge(a::NamedTuple, iterable)</code></pre><p>Interpret an iterable of key-value pairs as a named tuple, and perform a merge.</p><pre><code class="language-julia-repl hljs">julia&gt; merge((a=1, b=2, c=3), [:b=&gt;4, :d=&gt;5])
(a = 1, b = 4, c = 3, d = 5)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/namedtuple.jl#L357-L366">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.mergewith" href="#Base.mergewith"><code>Base.mergewith</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mergewith(combine, d::AbstractDict, others::AbstractDict...)
mergewith(combine)
merge(combine, d::AbstractDict, others::AbstractDict...)</code></pre><p>Construct a merged collection from the given collections. If necessary, the types of the resulting collection will be promoted to accommodate the types of the merged collections. Values with the same key will be combined using the combiner function.  The curried form <code>mergewith(combine)</code> returns the function <code>(args...) -&gt; mergewith(combine, args...)</code>.</p><p>Method <code>merge(combine::Union{Function,Type}, args...)</code> as an alias of <code>mergewith(combine, args...)</code> is still available for backward compatibility.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p><code>mergewith</code> requires Julia 1.5 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = Dict(&quot;foo&quot; =&gt; 0.0, &quot;bar&quot; =&gt; 42.0)
Dict{String, Float64} with 2 entries:
  &quot;bar&quot; =&gt; 42.0
  &quot;foo&quot; =&gt; 0.0

julia&gt; b = Dict(&quot;baz&quot; =&gt; 17, &quot;bar&quot; =&gt; 4711)
Dict{String, Int64} with 2 entries:
  &quot;bar&quot; =&gt; 4711
  &quot;baz&quot; =&gt; 17

julia&gt; mergewith(+, a, b)
Dict{String, Float64} with 3 entries:
  &quot;bar&quot; =&gt; 4753.0
  &quot;baz&quot; =&gt; 17.0
  &quot;foo&quot; =&gt; 0.0

julia&gt; ans == mergewith(+)(a, b)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractdict.jl#L362-L400">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.merge!" href="#Base.merge!"><code>Base.merge!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">merge!(d::AbstractDict, others::AbstractDict...)</code></pre><p>Update collection with pairs from the other collections. See also <a href="collections.html#Base.merge"><code>merge</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; d1 = Dict(1 =&gt; 2, 3 =&gt; 4);

julia&gt; d2 = Dict(1 =&gt; 4, 4 =&gt; 5);

julia&gt; merge!(d1, d2);

julia&gt; d1
Dict{Int64, Int64} with 3 entries:
  4 =&gt; 5
  3 =&gt; 4
  1 =&gt; 4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractdict.jl#L203-L223">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.mergewith!" href="#Base.mergewith!"><code>Base.mergewith!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mergewith!(combine, d::AbstractDict, others::AbstractDict...) -&gt; d
mergewith!(combine)
merge!(combine, d::AbstractDict, others::AbstractDict...) -&gt; d</code></pre><p>Update collection with pairs from the other collections. Values with the same key will be combined using the combiner function.  The curried form <code>mergewith!(combine)</code> returns the function <code>(args...) -&gt; mergewith!(combine, args...)</code>.</p><p>Method <code>merge!(combine::Union{Function,Type}, args...)</code> as an alias of <code>mergewith!(combine, args...)</code> is still available for backward compatibility.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p><code>mergewith!</code> requires Julia 1.5 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; d1 = Dict(1 =&gt; 2, 3 =&gt; 4);

julia&gt; d2 = Dict(1 =&gt; 4, 4 =&gt; 5);

julia&gt; mergewith!(+, d1, d2);

julia&gt; d1
Dict{Int64, Int64} with 3 entries:
  4 =&gt; 5
  3 =&gt; 4
  1 =&gt; 6

julia&gt; mergewith!(-, d1, d1);

julia&gt; d1
Dict{Int64, Int64} with 3 entries:
  4 =&gt; 0
  3 =&gt; 0
  1 =&gt; 0

julia&gt; foldl(mergewith!(+), [d1, d2]; init=Dict{Int64, Int64}())
Dict{Int64, Int64} with 3 entries:
  4 =&gt; 5
  3 =&gt; 0
  1 =&gt; 4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractdict.jl#L236-L281">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.sizehint!" href="#Base.sizehint!"><code>Base.sizehint!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sizehint!(s, n; first::Bool=false, shrink::Bool=true) -&gt; s</code></pre><p>Suggest that collection <code>s</code> reserve capacity for at least <code>n</code> elements. That is, if you expect that you&#39;re going to have to push a lot of values onto <code>s</code>, you can avoid the cost of incremental reallocation by doing it once up front; this can improve performance.</p><p>If <code>first</code> is <code>true</code>, then any additional space is reserved before the start of the collection. This way, subsequent calls to <code>pushfirst!</code> (instead of <code>push!</code>) may become faster. Supplying this keyword may result in an error if the collection is not ordered or if <code>pushfirst!</code> is not supported for this collection.</p><p>If <code>shrink=true</code> (the default), the collection&#39;s capacity may be reduced if its current capacity is greater than <code>n</code>.</p><p>See also <a href="collections.html#Base.resize!"><code>resize!</code></a>.</p><p><strong>Notes on the performance model</strong></p><p>For types that support <code>sizehint!</code>,</p><ol><li><p><code>push!</code> and <code>append!</code> methods generally may (but are not required to) preallocate extra storage. For types implemented in <code>Base</code>, they typically do, using a heuristic optimized for a general use case.</p></li><li><p><code>sizehint!</code> may control this preallocation. Again, it typically does this for types in <code>Base</code>.</p></li><li><p><code>empty!</code> is nearly costless (and O(1)) for types that support this kind of preallocation.</p></li></ol><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>The <code>shrink</code> and <code>first</code> arguments were added in Julia 1.11.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L1461-L1494">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.keytype" href="#Base.keytype"><code>Base.keytype</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">keytype(T::Type{&lt;:AbstractArray})
keytype(A::AbstractArray)</code></pre><p>Return the key type of an array. This is equal to the <a href="collections.html#Base.eltype"><code>eltype</code></a> of the result of <code>keys(...)</code>, and is provided mainly for compatibility with the dictionary interface.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; keytype([1, 2, 3]) == Int
true

julia&gt; keytype([1 2; 3 4])
CartesianIndex{2}</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p>For arrays, this function requires at least Julia 1.2.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L168-L187">source</a></section><section><div><pre><code class="language-julia hljs">keytype(type)</code></pre><p>Get the key type of a dictionary type. Behaves similarly to <a href="collections.html#Base.eltype"><code>eltype</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; keytype(Dict(Int32(1) =&gt; &quot;foo&quot;))
Int32</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractdict.jl#L297-L307">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.valtype" href="#Base.valtype"><code>Base.valtype</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">valtype(T::Type{&lt;:AbstractArray})
valtype(A::AbstractArray)</code></pre><p>Return the value type of an array. This is identical to <a href="collections.html#Base.eltype"><code>eltype</code></a> and is provided mainly for compatibility with the dictionary interface.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; valtype([&quot;one&quot;, &quot;two&quot;, &quot;three&quot;])
String</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p>For arrays, this function requires at least Julia 1.2.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarray.jl#L197-L212">source</a></section><section><div><pre><code class="language-julia hljs">valtype(type)</code></pre><p>Get the value type of a dictionary type. Behaves similarly to <a href="collections.html#Base.eltype"><code>eltype</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; valtype(Dict(Int32(1) =&gt; &quot;foo&quot;))
String</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractdict.jl#L311-L321">source</a></section></article><p>Fully implemented by:</p><ul><li><a href="collections.html#Base.Dict"><code>Dict</code></a></li><li><a href="collections.html#Base.IdDict"><code>IdDict</code></a></li><li><a href="collections.html#Base.WeakKeyDict"><code>WeakKeyDict</code></a></li></ul><p>Partially implemented by:</p><ul><li><a href="collections.html#Base.Set"><code>Set</code></a></li><li><a href="collections.html#Base.BitSet"><code>BitSet</code></a></li><li><a href="collections.html#Base.IdSet"><code>IdSet</code></a></li><li><a href="base.html#Base.EnvDict"><code>EnvDict</code></a></li><li><a href="arrays.html#Core.Array"><code>Array</code></a></li><li><a href="arrays.html#Base.BitArray"><code>BitArray</code></a></li><li><a href="collections.html#Base.ImmutableDict"><code>ImmutableDict</code></a></li><li><a href="collections.html#Base.PersistentDict"><code>PersistentDict</code></a></li><li><a href="collections.html#Base.Pairs"><code>Iterators.Pairs</code></a></li></ul><h2 id="Set-Like-Collections"><a class="docs-heading-anchor" href="#Set-Like-Collections">Set-Like Collections</a><a id="Set-Like-Collections-1"></a><a class="docs-heading-anchor-permalink" href="#Set-Like-Collections" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.AbstractSet" href="#Base.AbstractSet"><code>Base.AbstractSet</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AbstractSet{T}</code></pre><p>Supertype for set-like types whose elements are of type <code>T</code>. <a href="collections.html#Base.Set"><code>Set</code></a>, <a href="collections.html#Base.BitSet"><code>BitSet</code></a> and other types are subtypes of this.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/essentials.jl#L20-L25">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Set" href="#Base.Set"><code>Base.Set</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Set{T} &lt;: AbstractSet{T}</code></pre><p><code>Set</code>s are mutable containers that provide fast membership testing.</p><p><code>Set</code>s have efficient implementations of set operations such as <code>in</code>, <code>union</code> and <code>intersect</code>. Elements in a <code>Set</code> are unique, as determined by the elements&#39; definition of <code>isequal</code>. The order of elements in a <code>Set</code> is an implementation detail and cannot be relied on.</p><p>See also: <a href="collections.html#Base.AbstractSet"><code>AbstractSet</code></a>, <a href="collections.html#Base.BitSet"><code>BitSet</code></a>, <a href="collections.html#Base.Dict"><code>Dict</code></a>, <a href="collections.html#Base.push!"><code>push!</code></a>, <a href="collections.html#Base.empty!"><code>empty!</code></a>, <a href="collections.html#Base.union!"><code>union!</code></a>, <a href="collections.html#Base.in"><code>in</code></a>, <a href="base.html#Base.isequal"><code>isequal</code></a></p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; s = Set(&quot;aaBca&quot;)
Set{Char} with 3 elements:
  &#39;a&#39;
  &#39;c&#39;
  &#39;B&#39;

julia&gt; push!(s, &#39;b&#39;)
Set{Char} with 4 elements:
  &#39;a&#39;
  &#39;b&#39;
  &#39;B&#39;
  &#39;c&#39;

julia&gt; s = Set([NaN, 0.0, 1.0, 2.0]);

julia&gt; -0.0 in s # isequal(0.0, -0.0) is false
false

julia&gt; NaN in s # isequal(NaN, NaN) is true
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/set.jl#L3-L38">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.BitSet" href="#Base.BitSet"><code>Base.BitSet</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">BitSet([itr])</code></pre><p>Construct a sorted set of <code>Int</code>s generated by the given iterable object, or an empty set. Implemented as a bit string, and therefore designed for dense integer sets. If the set will be sparse (for example, holding a few very large integers), use <a href="collections.html#Base.Set"><code>Set</code></a> instead.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/bitset.jl#L25-L32">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.IdSet" href="#Base.IdSet"><code>Base.IdSet</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">IdSet{T}([itr])
IdSet()</code></pre><p>IdSet{T}() constructs a set (see <a href="collections.html#Base.Set"><code>Set</code></a>) using <code>===</code> as equality with values of type <code>T</code>.</p><p>In the example below, the values are all <code>isequal</code> so they get overwritten in the ordinary <code>Set</code>. The <code>IdSet</code> compares by <code>===</code> and so preserves the 3 different values.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Set(Any[true, 1, 1.0])
Set{Any} with 1 element:
  1.0

julia&gt; IdSet{Any}(Any[true, 1, 1.0])
IdSet{Any} with 3 elements:
  1.0
  1
  true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/idset.jl#L3-L25">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.union" href="#Base.union"><code>Base.union</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">union(s, itrs...)
∪(s, itrs...)</code></pre><p>Construct an object containing all distinct elements from all of the arguments.</p><p>The first argument controls what kind of container is returned. If this is an array, it maintains the order in which elements first appear.</p><p>Unicode <code>∪</code> can be typed by writing <code>\cup</code> then pressing tab in the Julia REPL, and in many editors. This is an infix operator, allowing <code>s ∪ itr</code>.</p><p>See also <a href="collections.html#Base.unique"><code>unique</code></a>, <a href="collections.html#Base.intersect"><code>intersect</code></a>, <a href="collections.html#Base.isdisjoint"><code>isdisjoint</code></a>, <a href="arrays.html#Base.vcat"><code>vcat</code></a>, <a href="iterators.html#Base.Iterators.flatten"><code>Iterators.flatten</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; union([1, 2], [3])
3-element Vector{Int64}:
 1
 2
 3

julia&gt; union([4 2 3 4 4], 1:3, 3.0)
4-element Vector{Float64}:
 4.0
 2.0
 3.0
 1.0

julia&gt; (0, 0.0) ∪ (-0.0, NaN)
3-element Vector{Real}:
   0
  -0.0
 NaN

julia&gt; union(Set([1, 2]), 2:3)
Set{Int64} with 3 elements:
  2
  3
  1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractset.jl#L13-L54">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.union!" href="#Base.union!"><code>Base.union!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">union!(s::Union{AbstractSet,AbstractVector}, itrs...)</code></pre><p>Construct the <a href="collections.html#Base.union"><code>union</code></a> of passed in sets and overwrite <code>s</code> with the result. Maintain order with arrays.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = Set([3, 4, 5]);

julia&gt; union!(a, 1:2:7);

julia&gt; a
Set{Int64} with 5 elements:
  5
  4
  7
  3
  1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractset.jl#L62-L84">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.intersect" href="#Base.intersect"><code>Base.intersect</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">intersect(s, itrs...)
∩(s, itrs...)</code></pre><p>Construct the set containing those elements which appear in all of the arguments.</p><p>The first argument controls what kind of container is returned. If this is an array, it maintains the order in which elements first appear.</p><p>Unicode <code>∩</code> can be typed by writing <code>\cap</code> then pressing tab in the Julia REPL, and in many editors. This is an infix operator, allowing <code>s ∩ itr</code>.</p><p>See also <a href="collections.html#Base.setdiff"><code>setdiff</code></a>, <a href="collections.html#Base.isdisjoint"><code>isdisjoint</code></a>, <a href="collections.html#Base.issubset"><code>issubset</code></a>, <a href="collections.html#Base.issetequal"><code>issetequal</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>As of Julia 1.8 intersect returns a result with the eltype of the type-promoted eltypes of the two inputs</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; intersect([1, 2, 3], [3, 4, 5])
1-element Vector{Int64}:
 3

julia&gt; intersect([1, 4, 4, 5, 6], [6, 4, 6, 7, 8])
2-element Vector{Int64}:
 4
 6

julia&gt; intersect(1:16, 7:99)
7:16

julia&gt; (0, 0.0) ∩ (-0.0, 0)
1-element Vector{Real}:
 0

julia&gt; intersect(Set([1, 2]), BitSet([2, 3]), 1.0:10.0)
Set{Float64} with 1 element:
  2.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractset.jl#L112-L152">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.setdiff" href="#Base.setdiff"><code>Base.setdiff</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">setdiff(s, itrs...)</code></pre><p>Construct the set of elements in <code>s</code> but not in any of the iterables in <code>itrs</code>. Maintain order with arrays.</p><p>See also <a href="collections.html#Base.setdiff!"><code>setdiff!</code></a>, <a href="collections.html#Base.union"><code>union</code></a> and <a href="collections.html#Base.intersect"><code>intersect</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; setdiff([1,2,3], [3,4,5])
2-element Vector{Int64}:
 1
 2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractset.jl#L200-L215">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.setdiff!" href="#Base.setdiff!"><code>Base.setdiff!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">setdiff!(s, itrs...)</code></pre><p>Remove from set <code>s</code> (in-place) each element of each iterable from <code>itrs</code>. Maintain order with arrays.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = Set([1, 3, 4, 5]);

julia&gt; setdiff!(a, 1:2:6);

julia&gt; a
Set{Int64} with 1 element:
  4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractset.jl#L219-L237">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.symdiff" href="#Base.symdiff"><code>Base.symdiff</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">symdiff(s, itrs...)</code></pre><p>Construct the symmetric difference of elements in the passed in sets. When <code>s</code> is not an <code>AbstractSet</code>, the order is maintained.</p><p>See also <a href="collections.html#Base.symdiff!"><code>symdiff!</code></a>, <a href="collections.html#Base.setdiff"><code>setdiff</code></a>, <a href="collections.html#Base.union"><code>union</code></a> and <a href="collections.html#Base.intersect"><code>intersect</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; symdiff([1,2,3], [3,4,5], [4,5,6])
3-element Vector{Int64}:
 1
 2
 6

julia&gt; symdiff([1,2,1], [2, 1, 2])
Int64[]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractset.jl#L252-L271">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.symdiff!" href="#Base.symdiff!"><code>Base.symdiff!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">symdiff!(s::Union{AbstractSet,AbstractVector}, itrs...)</code></pre><p>Construct the symmetric difference of the passed in sets, and overwrite <code>s</code> with the result. When <code>s</code> is an array, the order is maintained. Note that in this case the multiplicity of elements matters.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractset.jl#L275-L283">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.intersect!" href="#Base.intersect!"><code>Base.intersect!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">intersect!(s::Union{AbstractSet,AbstractVector}, itrs...)</code></pre><p>Intersect all passed in sets and overwrite <code>s</code> with the result. Maintain order with arrays.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractset.jl#L182-L189">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.issubset" href="#Base.issubset"><code>Base.issubset</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">issubset(a, b) -&gt; Bool
⊆(a, b) -&gt; Bool
⊇(b, a) -&gt; Bool</code></pre><p>Determine whether every element of <code>a</code> is also in <code>b</code>, using <a href="collections.html#Base.in"><code>in</code></a>.</p><p>See also <a href="collections.html#Base.:⊊"><code>⊊</code></a>, <a href="collections.html#Base.:⊈"><code>⊈</code></a>, <a href="collections.html#Base.intersect"><code>∩</code></a>, <a href="collections.html#Base.union"><code>∪</code></a>, <a href="strings.html#Base.contains"><code>contains</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; issubset([1, 2], [1, 2, 3])
true

julia&gt; [1, 2, 3] ⊆ [1, 2]
false

julia&gt; [1, 2, 3] ⊇ [1, 2]
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractset.jl#L304-L324">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.in!" href="#Base.in!"><code>Base.in!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">in!(x, s::AbstractSet) -&gt; Bool</code></pre><p>If <code>x</code> is in <code>s</code>, return <code>true</code>. If not, push <code>x</code> into <code>s</code> and return <code>false</code>. This is equivalent to <code>in(x, s) ? true : (push!(s, x); false)</code>, but may have a more efficient implementation.</p><p>See also: <a href="collections.html#Base.in"><code>in</code></a>, <a href="collections.html#Base.push!"><code>push!</code></a>, <a href="collections.html#Base.Set"><code>Set</code></a></p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>This function requires at least 1.11.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; s = Set{Any}([1, 2, 3]); in!(4, s)
false

julia&gt; length(s)
4

julia&gt; in!(0x04, s)
true

julia&gt; s
Set{Any} with 4 elements:
  4
  2
  3
  1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/set.jl#L94-L124">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:⊈" href="#Base.:⊈"><code>Base.:⊈</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">⊈(a, b) -&gt; Bool
⊉(b, a) -&gt; Bool</code></pre><p>Negation of <code>⊆</code> and <code>⊇</code>, i.e. checks that <code>a</code> is not a subset of <code>b</code>.</p><p>See also <a href="collections.html#Base.issubset"><code>issubset</code></a> (<code>⊆</code>), <a href="collections.html#Base.:⊊"><code>⊊</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; (1, 2) ⊈ (2, 3)
true

julia&gt; (1, 2) ⊈ (1, 2, 3)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractset.jl#L448-L464">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:⊊" href="#Base.:⊊"><code>Base.:⊊</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">⊊(a, b) -&gt; Bool
⊋(b, a) -&gt; Bool</code></pre><p>Determines if <code>a</code> is a subset of, but not equal to, <code>b</code>.</p><p>See also <a href="collections.html#Base.issubset"><code>issubset</code></a> (<code>⊆</code>), <a href="collections.html#Base.:⊈"><code>⊈</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; (1, 2) ⊊ (1, 2, 3)
true

julia&gt; (1, 2) ⊊ (1, 2)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractset.jl#L396-L412">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.issetequal" href="#Base.issetequal"><code>Base.issetequal</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">issetequal(a, b) -&gt; Bool</code></pre><p>Determine whether <code>a</code> and <code>b</code> have the same elements. Equivalent to <code>a ⊆ b &amp;&amp; b ⊆ a</code> but more efficient when possible.</p><p>See also: <a href="collections.html#Base.isdisjoint"><code>isdisjoint</code></a>, <a href="collections.html#Base.union"><code>union</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; issetequal([1, 2], [1, 2, 3])
false

julia&gt; issetequal([1, 2], [2, 1])
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractset.jl#L498-L514">source</a></section><section><div><pre><code class="language-julia hljs">issetequal(x)</code></pre><p>Create a function that compares its argument to <code>x</code> using <a href="collections.html#Base.issetequal"><code>issetequal</code></a>, i.e. a function equivalent to <code>y -&gt; issetequal(y, x)</code>. The returned function is of type <code>Base.Fix2{typeof(issetequal)}</code>, which can be used to implement specialized methods.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>This functionality requires at least Julia 1.11.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractset.jl#L532-L542">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isdisjoint" href="#Base.isdisjoint"><code>Base.isdisjoint</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isdisjoint(a, b) -&gt; Bool</code></pre><p>Determine whether the collections <code>a</code> and <code>b</code> are disjoint. Equivalent to <code>isempty(a ∩ b)</code> but more efficient when possible.</p><p>See also: <a href="collections.html#Base.intersect"><code>intersect</code></a>, <a href="collections.html#Base.isempty"><code>isempty</code></a>, <a href="collections.html#Base.issetequal"><code>issetequal</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p>This function requires at least Julia 1.5.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isdisjoint([1, 2], [2, 3, 4])
false

julia&gt; isdisjoint([3, 1], [2, 4])
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractset.jl#L546-L565">source</a></section><section><div><pre><code class="language-julia hljs">isdisjoint(x)</code></pre><p>Create a function that compares its argument to <code>x</code> using <a href="collections.html#Base.isdisjoint"><code>isdisjoint</code></a>, i.e. a function equivalent to <code>y -&gt; isdisjoint(y, x)</code>. The returned function is of type <code>Base.Fix2{typeof(isdisjoint)}</code>, which can be used to implement specialized methods.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>This functionality requires at least Julia 1.11.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractset.jl#L591-L601">source</a></section></article><p>Fully implemented by:</p><ul><li><a href="collections.html#Base.Set"><code>Set</code></a></li><li><a href="collections.html#Base.BitSet"><code>BitSet</code></a></li><li><a href="collections.html#Base.IdSet"><code>IdSet</code></a></li></ul><p>Partially implemented by:</p><ul><li><a href="arrays.html#Core.Array"><code>Array</code></a></li></ul><h2 id="Dequeues"><a class="docs-heading-anchor" href="#Dequeues">Dequeues</a><a id="Dequeues-1"></a><a class="docs-heading-anchor-permalink" href="#Dequeues" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.push!" href="#Base.push!"><code>Base.push!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">push!(collection, items...) -&gt; collection</code></pre><p>Insert one or more <code>items</code> in <code>collection</code>. If <code>collection</code> is an ordered container, the items are inserted at the end (in the given order).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; push!([1, 2, 3], 4, 5, 6)
6-element Vector{Int64}:
 1
 2
 3
 4
 5
 6</code></pre><p>If <code>collection</code> is ordered, use <a href="collections.html#Base.append!"><code>append!</code></a> to add all the elements of another collection to it. The result of the preceding example is equivalent to <code>append!([1, 2, 3], [4, 5, 6])</code>. For <code>AbstractSet</code> objects, <a href="collections.html#Base.union!"><code>union!</code></a> can be used instead.</p><p>See <a href="collections.html#Base.sizehint!"><code>sizehint!</code></a> for notes about the performance model.</p><p>See also <a href="collections.html#Base.pushfirst!"><code>pushfirst!</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L1230-L1255">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.pop!" href="#Base.pop!"><code>Base.pop!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">pop!(collection) -&gt; item</code></pre><p>Remove an item in <code>collection</code> and return it. If <code>collection</code> is an ordered container, the last item is returned; for unordered containers, an arbitrary element is returned.</p><p>See also: <a href="collections.html#Base.popfirst!"><code>popfirst!</code></a>, <a href="collections.html#Base.popat!"><code>popat!</code></a>, <a href="collections.html#Base.delete!"><code>delete!</code></a>, <a href="collections.html#Base.deleteat!"><code>deleteat!</code></a>, <a href="collections.html#Base.splice!"><code>splice!</code></a>, and <a href="collections.html#Base.push!"><code>push!</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A=[1, 2, 3]
3-element Vector{Int64}:
 1
 2
 3

julia&gt; pop!(A)
3

julia&gt; A
2-element Vector{Int64}:
 1
 2

julia&gt; S = Set([1, 2])
Set{Int64} with 2 elements:
  2
  1

julia&gt; pop!(S)
2

julia&gt; S
Set{Int64} with 1 element:
  1

julia&gt; pop!(Dict(1=&gt;2))
1 =&gt; 2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L1539-L1579">source</a></section><section><div><pre><code class="language-julia hljs">pop!(collection, key[, default])</code></pre><p>Delete and return the mapping for <code>key</code> if it exists in <code>collection</code>, otherwise return <code>default</code>, or throw an error if <code>default</code> is not specified.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; d = Dict(&quot;a&quot;=&gt;1, &quot;b&quot;=&gt;2, &quot;c&quot;=&gt;3);

julia&gt; pop!(d, &quot;a&quot;)
1

julia&gt; pop!(d, &quot;d&quot;)
ERROR: KeyError: key &quot;d&quot; not found
Stacktrace:
[...]

julia&gt; pop!(d, &quot;e&quot;, 4)
4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/dict.jl#L586-L607">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.popat!" href="#Base.popat!"><code>Base.popat!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">popat!(a::Vector, i::Integer, [default])</code></pre><p>Remove the item at the given <code>i</code> and return it. Subsequent items are shifted to fill the resulting gap. When <code>i</code> is not a valid index for <code>a</code>, return <code>default</code>, or throw an error if <code>default</code> is not specified.</p><p>See also: <a href="collections.html#Base.pop!"><code>pop!</code></a>, <a href="collections.html#Base.popfirst!"><code>popfirst!</code></a>, <a href="collections.html#Base.deleteat!"><code>deleteat!</code></a>, <a href="collections.html#Base.splice!"><code>splice!</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p>This function is available as of Julia 1.5.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [4, 3, 2, 1]; popat!(a, 2)
3

julia&gt; a
3-element Vector{Int64}:
 4
 2
 1

julia&gt; popat!(a, 4, missing)
missing

julia&gt; popat!(a, 4)
ERROR: BoundsError: attempt to access 3-element Vector{Int64} at index [4]
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L1589-L1620">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.pushfirst!" href="#Base.pushfirst!"><code>Base.pushfirst!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">pushfirst!(collection, items...) -&gt; collection</code></pre><p>Insert one or more <code>items</code> at the beginning of <code>collection</code>.</p><p>This function is called <code>unshift</code> in many other programming languages.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; pushfirst!([1, 2, 3, 4], 5, 6)
6-element Vector{Int64}:
 5
 6
 1
 2
 3
 4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L1637-L1655">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.popfirst!" href="#Base.popfirst!"><code>Base.popfirst!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">popfirst!(collection) -&gt; item</code></pre><p>Remove the first <code>item</code> from <code>collection</code>.</p><p>This function is called <code>shift</code> in many other programming languages.</p><p>See also: <a href="collections.html#Base.pop!"><code>pop!</code></a>, <a href="collections.html#Base.popat!"><code>popat!</code></a>, <a href="collections.html#Base.delete!"><code>delete!</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1, 2, 3, 4, 5, 6]
6-element Vector{Int64}:
 1
 2
 3
 4
 5
 6

julia&gt; popfirst!(A)
1

julia&gt; A
5-element Vector{Int64}:
 2
 3
 4
 5
 6</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L1680-L1711">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.insert!" href="#Base.insert!"><code>Base.insert!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">insert!(a::Vector, index::Integer, item)</code></pre><p>Insert an <code>item</code> into <code>a</code> at the given <code>index</code>. <code>index</code> is the index of <code>item</code> in the resulting <code>a</code>.</p><p>See also: <a href="collections.html#Base.push!"><code>push!</code></a>, <a href="collections.html#Base.replace-Tuple{Any, Vararg{Pair}}"><code>replace</code></a>, <a href="collections.html#Base.popat!"><code>popat!</code></a>, <a href="collections.html#Base.splice!"><code>splice!</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; insert!(Any[1:6;], 3, &quot;here&quot;)
7-element Vector{Any}:
 1
 2
  &quot;here&quot;
 3
 4
 5
 6</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L1721-L1741">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.deleteat!" href="#Base.deleteat!"><code>Base.deleteat!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">deleteat!(a::Vector, i::Integer)</code></pre><p>Remove the item at the given <code>i</code> and return the modified <code>a</code>. Subsequent items are shifted to fill the resulting gap.</p><p>See also: <a href="collections.html#Base.keepat!"><code>keepat!</code></a>, <a href="collections.html#Base.delete!"><code>delete!</code></a>, <a href="collections.html#Base.popat!"><code>popat!</code></a>, <a href="collections.html#Base.splice!"><code>splice!</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; deleteat!([6, 5, 4, 3, 2, 1], 2)
5-element Vector{Int64}:
 6
 4
 3
 2
 1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L1752-L1770">source</a></section><section><div><pre><code class="language-julia hljs">deleteat!(a::Vector, inds)</code></pre><p>Remove the items at the indices given by <code>inds</code>, and return the modified <code>a</code>. Subsequent items are shifted to fill the resulting gap.</p><p><code>inds</code> can be either an iterator or a collection of sorted and unique integer indices, or a boolean vector of the same length as <code>a</code> with <code>true</code> indicating entries to delete.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; deleteat!([6, 5, 4, 3, 2, 1], 1:2:5)
3-element Vector{Int64}:
 5
 3
 1

julia&gt; deleteat!([6, 5, 4, 3, 2, 1], [true, false, true, false, true, false])
3-element Vector{Int64}:
 5
 3
 1

julia&gt; deleteat!([6, 5, 4, 3, 2, 1], (2, 2))
ERROR: ArgumentError: indices must be unique and sorted
Stacktrace:
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L1789-L1817">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.keepat!" href="#Base.keepat!"><code>Base.keepat!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">keepat!(a::Vector, inds)
keepat!(a::BitVector, inds)</code></pre><p>Remove the items at all the indices which are not given by <code>inds</code>, and return the modified <code>a</code>. Items which are kept are shifted to fill the resulting gaps.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p><code>inds</code> must be an iterator of sorted and unique integer indices. See also <a href="collections.html#Base.deleteat!"><code>deleteat!</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>This function is available as of Julia 1.7.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; keepat!([6, 5, 4, 3, 2, 1], 1:2:5)
3-element Vector{Int64}:
 6
 4
 2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L2961-L2985">source</a></section><section><div><pre><code class="language-julia hljs">keepat!(a::Vector, m::AbstractVector{Bool})
keepat!(a::BitVector, m::AbstractVector{Bool})</code></pre><p>The in-place version of logical indexing <code>a = a[m]</code>. That is, <code>keepat!(a, m)</code> on vectors of equal length <code>a</code> and <code>m</code> will remove all elements from <code>a</code> for which <code>m</code> at the corresponding index is <code>false</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [:a, :b, :c];

julia&gt; keepat!(a, [true, false, true])
2-element Vector{Symbol}:
 :a
 :c

julia&gt; a
2-element Vector{Symbol}:
 :a
 :c</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L2988-L3010">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.splice!" href="#Base.splice!"><code>Base.splice!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">splice!(a::Vector, index::Integer, [replacement]) -&gt; item</code></pre><p>Remove the item at the given index, and return the removed item. Subsequent items are shifted left to fill the resulting gap. If specified, replacement values from an ordered collection will be spliced in place of the removed item.</p><p>See also: <a href="collections.html#Base.replace-Tuple{Any, Vararg{Pair}}"><code>replace</code></a>, <a href="collections.html#Base.delete!"><code>delete!</code></a>, <a href="collections.html#Base.deleteat!"><code>deleteat!</code></a>, <a href="collections.html#Base.pop!"><code>pop!</code></a>, <a href="collections.html#Base.popat!"><code>popat!</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [6, 5, 4, 3, 2, 1]; splice!(A, 5)
2

julia&gt; A
5-element Vector{Int64}:
 6
 5
 4
 3
 1

julia&gt; splice!(A, 5, -1)
1

julia&gt; A
5-element Vector{Int64}:
  6
  5
  4
  3
 -1

julia&gt; splice!(A, 1, [-1, -2, -3])
6

julia&gt; A
7-element Vector{Int64}:
 -1
 -2
 -3
  5
  4
  3
 -1</code></pre><p>To insert <code>replacement</code> before an index <code>n</code> without removing any items, use <code>splice!(collection, n:n-1, replacement)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L1892-L1942">source</a></section><section><div><pre><code class="language-julia hljs">splice!(a::Vector, indices, [replacement]) -&gt; items</code></pre><p>Remove items at specified indices, and return a collection containing the removed items. Subsequent items are shifted left to fill the resulting gaps. If specified, replacement values from an ordered collection will be spliced in place of the removed items; in this case, <code>indices</code> must be a <code>AbstractUnitRange</code>.</p><p>To insert <code>replacement</code> before an index <code>n</code> without removing any items, use <code>splice!(collection, n:n-1, replacement)</code>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p>Prior to Julia 1.5, <code>indices</code> must always be a <code>UnitRange</code>.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>Prior to Julia 1.8, <code>indices</code> must be a <code>UnitRange</code> if splicing in replacement values.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [-1, -2, -3, 5, 4, 3, -1]; splice!(A, 4:3, 2)
Int64[]

julia&gt; A
8-element Vector{Int64}:
 -1
 -2
 -3
  2
  5
  4
  3
 -1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L1961-L1997">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.resize!" href="#Base.resize!"><code>Base.resize!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">resize!(a::Vector, n::Integer) -&gt; Vector</code></pre><p>Resize <code>a</code> to contain <code>n</code> elements. If <code>n</code> is smaller than the current collection length, the first <code>n</code> elements will be retained. If <code>n</code> is larger, the new elements are not guaranteed to be initialized.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; resize!([6, 5, 4, 3, 2, 1], 3)
3-element Vector{Int64}:
 6
 5
 4

julia&gt; a = resize!([6, 5, 4, 3, 2, 1], 8);

julia&gt; length(a)
8

julia&gt; a[1:6]
6-element Vector{Int64}:
 6
 5
 4
 3
 2
 1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L1417-L1446">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.append!" href="#Base.append!"><code>Base.append!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">append!(collection, collections...) -&gt; collection.</code></pre><p>For an ordered container <code>collection</code>, add the elements of each <code>collections</code> to the end of it.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>Specifying multiple collections to be appended requires at least Julia 1.6.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; append!([1], [2, 3])
3-element Vector{Int64}:
 1
 2
 3

julia&gt; append!([1, 2, 3], [4, 5], [6])
6-element Vector{Int64}:
 1
 2
 3
 4
 5
 6</code></pre><p>Use <a href="collections.html#Base.push!"><code>push!</code></a> to add individual items to <code>collection</code> which are not already themselves in another collection. The result of the preceding example is equivalent to <code>push!([1, 2, 3], 4, 5, 6)</code>.</p><p>See <a href="collections.html#Base.sizehint!"><code>sizehint!</code></a> for notes about the performance model.</p><p>See also <a href="arrays.html#Base.vcat"><code>vcat</code></a> for vectors, <a href="collections.html#Base.union!"><code>union!</code></a> for sets, and <a href="collections.html#Base.prepend!"><code>prepend!</code></a> and <a href="collections.html#Base.pushfirst!"><code>pushfirst!</code></a> for the opposite order.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L1283-L1318">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.prepend!" href="#Base.prepend!"><code>Base.prepend!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">prepend!(a::Vector, collections...) -&gt; collection</code></pre><p>Insert the elements of each <code>collections</code> to the beginning of <code>a</code>.</p><p>When <code>collections</code> specifies multiple collections, order is maintained: elements of <code>collections[1]</code> will appear leftmost in <code>a</code>, and so on.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>Specifying multiple collections to be prepended requires at least Julia 1.6.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; prepend!([3], [1, 2])
3-element Vector{Int64}:
 1
 2
 3

julia&gt; prepend!([6], [1, 2], [3, 4, 5])
6-element Vector{Int64}:
 1
 2
 3
 4
 5
 6</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/array.jl#L1349-L1377">source</a></section></article><p>Fully implemented by:</p><ul><li><code>Vector</code> (a.k.a. 1-dimensional <a href="arrays.html#Core.Array"><code>Array</code></a>)</li><li><code>BitVector</code> (a.k.a. 1-dimensional <a href="arrays.html#Base.BitArray"><code>BitArray</code></a>)</li></ul><h2 id="Utility-Collections"><a class="docs-heading-anchor" href="#Utility-Collections">Utility Collections</a><a id="Utility-Collections-1"></a><a class="docs-heading-anchor-permalink" href="#Utility-Collections" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Pair" href="#Core.Pair"><code>Core.Pair</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Pair(x, y)
x =&gt; y</code></pre><p>Construct a <code>Pair</code> object with type <code>Pair{typeof(x), typeof(y)}</code>. The elements are stored in the fields <code>first</code> and <code>second</code>. They can also be accessed via iteration (but a <code>Pair</code> is treated as a single &quot;scalar&quot; for broadcasting operations).</p><p>See also <a href="collections.html#Base.Dict"><code>Dict</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; p = &quot;foo&quot; =&gt; 7
&quot;foo&quot; =&gt; 7

julia&gt; typeof(p)
Pair{String, Int64}

julia&gt; p.first
&quot;foo&quot;

julia&gt; for x in p
           println(x)
       end
foo
7

julia&gt; replace.([&quot;xops&quot;, &quot;oxps&quot;], &quot;x&quot; =&gt; &quot;o&quot;)
2-element Vector{String}:
 &quot;oops&quot;
 &quot;oops&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/pair.jl#L5-L37">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Pairs" href="#Base.Pairs"><code>Base.Pairs</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.Pairs(values, keys) &lt;: AbstractDict{eltype(keys), eltype(values)}</code></pre><p>Transforms an indexable container into a Dictionary-view of the same data. Modifying the key-space of the underlying data may invalidate this object.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/essentials.jl#L488-L493">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="base.html">« Essentials</a><a class="docs-footer-nextpage" href="math.html">Mathematics »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
