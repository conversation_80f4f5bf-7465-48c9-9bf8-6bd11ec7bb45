<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Constructors · The Julia Language</title><meta name="title" content="Constructors · The Julia Language"/><meta property="og:title" content="Constructors · The Julia Language"/><meta property="twitter:title" content="Constructors · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li class="is-active"><a class="tocitem" href="constructors.html">Constructors</a><ul class="internal"><li><a class="tocitem" href="#man-outer-constructor-methods"><span>Outer Constructor Methods</span></a></li><li><a class="tocitem" href="#man-inner-constructor-methods"><span>Inner Constructor Methods</span></a></li><li><a class="tocitem" href="#Incomplete-Initialization"><span>Incomplete Initialization</span></a></li><li><a class="tocitem" href="#Parametric-Constructors"><span>Parametric Constructors</span></a></li><li><a class="tocitem" href="#Case-Study:-Rational"><span>Case Study: Rational</span></a></li><li><a class="tocitem" href="#Outer-only-constructors"><span>Outer-only constructors</span></a></li><li><a class="tocitem" href="#Constructors-are-just-callable-objects"><span>Constructors are just callable objects</span></a></li></ul></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="constructors.html">Constructors</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="constructors.html">Constructors</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/constructors.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="man-constructors"><a class="docs-heading-anchor" href="#man-constructors">Constructors</a><a id="man-constructors-1"></a><a class="docs-heading-anchor-permalink" href="#man-constructors" title="Permalink"></a></h1><p>Constructors <sup class="footnote-reference"><a id="citeref-1" href="#footnote-1">[1]</a></sup> are functions that create new objects – specifically, instances of <a href="types.html#Composite-Types">Composite Types</a>. In Julia, type objects also serve as constructor functions: they create new instances of themselves when applied to an argument tuple as a function. This much was already mentioned briefly when composite types were introduced. For example:</p><pre><code class="language-julia-repl hljs">julia&gt; struct Foo
           bar
           baz
       end

julia&gt; foo = Foo(1, 2)
Foo(1, 2)

julia&gt; foo.bar
1

julia&gt; foo.baz
2</code></pre><p>For many types, forming new objects by binding their field values together is all that is ever needed to create instances. However, in some cases more functionality is required when creating composite objects. Sometimes invariants must be enforced, either by checking arguments or by transforming them. <a href="https://en.wikipedia.org/wiki/Recursion_%28computer_science%29#Recursive_data_structures_.28structural_recursion.29">Recursive data structures</a>, especially those that may be self-referential, often cannot be constructed cleanly without first being created in an incomplete state and then altered programmatically to be made whole, as a separate step from object creation. Sometimes, it&#39;s just convenient to be able to construct objects with fewer or different types of parameters than they have fields. Julia&#39;s system for object construction addresses all of these cases and more.</p><h2 id="man-outer-constructor-methods"><a class="docs-heading-anchor" href="#man-outer-constructor-methods">Outer Constructor Methods</a><a id="man-outer-constructor-methods-1"></a><a class="docs-heading-anchor-permalink" href="#man-outer-constructor-methods" title="Permalink"></a></h2><p>A constructor is just like any other function in Julia in that its overall behavior is defined by the combined behavior of its methods. Accordingly, you can add functionality to a constructor by simply defining new methods. For example, let&#39;s say you want to add a constructor method for <code>Foo</code> objects that takes only one argument and uses the given value for both the <code>bar</code> and <code>baz</code> fields. This is simple:</p><pre><code class="language-julia-repl hljs">julia&gt; Foo(x) = Foo(x,x)
Foo

julia&gt; Foo(1)
Foo(1, 1)</code></pre><p>You could also add a zero-argument <code>Foo</code> constructor method that supplies default values for both of the <code>bar</code> and <code>baz</code> fields:</p><pre><code class="language-julia-repl hljs">julia&gt; Foo() = Foo(0)
Foo

julia&gt; Foo()
Foo(0, 0)</code></pre><p>Here the zero-argument constructor method calls the single-argument constructor method, which in turn calls the automatically provided two-argument constructor method. For reasons that will become clear very shortly, additional constructor methods declared as normal methods like this are called <em>outer</em> constructor methods. Outer constructor methods can only ever create a new instance by calling another constructor method, such as the automatically provided default ones.</p><h2 id="man-inner-constructor-methods"><a class="docs-heading-anchor" href="#man-inner-constructor-methods">Inner Constructor Methods</a><a id="man-inner-constructor-methods-1"></a><a class="docs-heading-anchor-permalink" href="#man-inner-constructor-methods" title="Permalink"></a></h2><p>While outer constructor methods succeed in addressing the problem of providing additional convenience methods for constructing objects, they fail to address the other two use cases mentioned in the introduction of this chapter: enforcing invariants, and allowing construction of self-referential objects. For these problems, one needs <em>inner</em> constructor methods. An inner constructor method is like an outer constructor method, except for two differences:</p><ol><li>It is declared inside the block of a type declaration, rather than outside of it like normal methods.</li><li>It has access to a special locally existent function called <a href="../base/base.html#new"><code>new</code></a> that creates objects of the block&#39;s type.</li></ol><p>For example, suppose one wants to declare a type that holds a pair of real numbers, subject to the constraint that the first number is not greater than the second one. One could declare it like this:</p><pre><code class="language-julia-repl hljs">julia&gt; struct OrderedPair
           x::Real
           y::Real
           OrderedPair(x,y) = x &gt; y ? error(&quot;out of order&quot;) : new(x,y)
       end</code></pre><p>Now <code>OrderedPair</code> objects can only be constructed such that <code>x &lt;= y</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; OrderedPair(1, 2)
OrderedPair(1, 2)

julia&gt; OrderedPair(2,1)
ERROR: out of order
Stacktrace:
 [1] error at ./error.jl:33 [inlined]
 [2] OrderedPair(::Int64, ::Int64) at ./none:4
 [3] top-level scope</code></pre><p>If the type were declared <code>mutable</code>, you could reach in and directly change the field values to violate this invariant. Of course, messing around with an object&#39;s internals uninvited is bad practice. You (or someone else) can also provide additional outer constructor methods at any later point, but once a type is declared, there is no way to add more inner constructor methods. Since outer constructor methods can only create objects by calling other constructor methods, ultimately, some inner constructor must be called to create an object. This guarantees that all objects of the declared type must come into existence by a call to one of the inner constructor methods provided with the type, thereby giving some degree of enforcement of a type&#39;s invariants.</p><p>If any inner constructor method is defined, no default constructor method is provided: it is presumed that you have supplied yourself with all the inner constructors you need. The default constructor is equivalent to writing your own inner constructor method that takes all of the object&#39;s fields as parameters (constrained to be of the correct type, if the corresponding field has a type), and passes them to <code>new</code>, returning the resulting object:</p><pre><code class="language-julia-repl hljs">julia&gt; struct Foo
           bar
           baz
           Foo(bar,baz) = new(bar,baz)
       end
</code></pre><p>This declaration has the same effect as the earlier definition of the <code>Foo</code> type without an explicit inner constructor method. The following two types are equivalent – one with a default constructor, the other with an explicit constructor:</p><pre><code class="language-julia-repl hljs">julia&gt; struct T1
           x::Int64
       end

julia&gt; struct T2
           x::Int64
           T2(x) = new(x)
       end

julia&gt; T1(1)
T1(1)

julia&gt; T2(1)
T2(1)

julia&gt; T1(1.0)
T1(1)

julia&gt; T2(1.0)
T2(1)</code></pre><p>It is good practice to provide as few inner constructor methods as possible: only those taking all arguments explicitly and enforcing essential error checking and transformation. Additional convenience constructor methods, supplying default values or auxiliary transformations, should be provided as outer constructors that call the inner constructors to do the heavy lifting. This separation is typically quite natural.</p><h2 id="Incomplete-Initialization"><a class="docs-heading-anchor" href="#Incomplete-Initialization">Incomplete Initialization</a><a id="Incomplete-Initialization-1"></a><a class="docs-heading-anchor-permalink" href="#Incomplete-Initialization" title="Permalink"></a></h2><p>The final problem which has still not been addressed is construction of self-referential objects, or more generally, recursive data structures. Since the fundamental difficulty may not be immediately obvious, let us briefly explain it. Consider the following recursive type declaration:</p><pre><code class="language-julia-repl hljs">julia&gt; mutable struct SelfReferential
           obj::SelfReferential
       end
</code></pre><p>This type may appear innocuous enough, until one considers how to construct an instance of it. If <code>a</code> is an instance of <code>SelfReferential</code>, then a second instance can be created by the call:</p><pre><code class="language-julia-repl hljs">julia&gt; b = SelfReferential(a)</code></pre><p>But how does one construct the first instance when no instance exists to provide as a valid value for its <code>obj</code> field? The only solution is to allow creating an incompletely initialized instance of <code>SelfReferential</code> with an unassigned <code>obj</code> field, and using that incomplete instance as a valid value for the <code>obj</code> field of another instance, such as, for example, itself.</p><p>To allow for the creation of incompletely initialized objects, Julia allows the <a href="../base/base.html#new"><code>new</code></a> function to be called with fewer than the number of fields that the type has, returning an object with the unspecified fields uninitialized. The inner constructor method can then use the incomplete object, finishing its initialization before returning it. Here, for example, is another attempt at defining the <code>SelfReferential</code> type, this time using a zero-argument inner constructor returning instances having <code>obj</code> fields pointing to themselves:</p><pre><code class="language-julia-repl hljs">julia&gt; mutable struct SelfReferential
           obj::SelfReferential
           SelfReferential() = (x = new(); x.obj = x)
       end
</code></pre><p>We can verify that this constructor works and constructs objects that are, in fact, self-referential:</p><pre><code class="language-julia-repl hljs">julia&gt; x = SelfReferential();

julia&gt; x === x
true

julia&gt; x === x.obj
true

julia&gt; x === x.obj.obj
true</code></pre><p>Although it is generally a good idea to return a fully initialized object from an inner constructor, it is possible to return incompletely initialized objects:</p><pre><code class="language-julia-repl hljs">julia&gt; mutable struct Incomplete
           data
           Incomplete() = new()
       end

julia&gt; z = Incomplete();</code></pre><p>While you are allowed to create objects with uninitialized fields, any access to an uninitialized reference is an immediate error:</p><pre><code class="language-julia-repl hljs">julia&gt; z.data
ERROR: UndefRefError: access to undefined reference</code></pre><p>This avoids the need to continually check for <code>null</code> values. However, not all object fields are references. Julia considers some types to be &quot;plain data&quot;, meaning all of their data is self-contained and does not reference other objects. The plain data types consist of primitive types (e.g. <code>Int</code>) and immutable structs of other plain data types (see also: <a href="../base/base.html#Base.isbits"><code>isbits</code></a>, <a href="../base/base.html#Base.isbitstype"><code>isbitstype</code></a>). The initial contents of a plain data type is undefined:</p><pre><code class="language-julia-repl hljs">julia&gt; struct HasPlain
           n::Int
           HasPlain() = new()
       end

julia&gt; HasPlain()
HasPlain(438103441441)</code></pre><p>Arrays of plain data types exhibit the same behavior.</p><p>You can pass incomplete objects to other functions from inner constructors to delegate their completion:</p><pre><code class="language-julia-repl hljs">julia&gt; mutable struct Lazy
           data
           Lazy(v) = complete_me(new(), v)
       end</code></pre><p>As with incomplete objects returned from constructors, if <code>complete_me</code> or any of its callees try to access the <code>data</code> field of the <code>Lazy</code> object before it has been initialized, an error will be thrown immediately.</p><h2 id="Parametric-Constructors"><a class="docs-heading-anchor" href="#Parametric-Constructors">Parametric Constructors</a><a id="Parametric-Constructors-1"></a><a class="docs-heading-anchor-permalink" href="#Parametric-Constructors" title="Permalink"></a></h2><p>Parametric types add a few wrinkles to the constructor story. Recall from <a href="types.html#Parametric-Types">Parametric Types</a> that, by default, instances of parametric composite types can be constructed either with explicitly given type parameters or with type parameters implied by the types of the arguments given to the constructor. Here are some examples:</p><pre><code class="language-julia-repl hljs">julia&gt; struct Point{T&lt;:Real}
           x::T
           y::T
       end

julia&gt; Point(1,2) ## implicit T ##
Point{Int64}(1, 2)

julia&gt; Point(1.0,2.5) ## implicit T ##
Point{Float64}(1.0, 2.5)

julia&gt; Point(1,2.5) ## implicit T ##
ERROR: MethodError: no method matching Point(::Int64, ::Float64)
The type `Point` exists, but no method is defined for this combination of argument types when trying to construct it.

Closest candidates are:
  Point(::T, ::T) where T&lt;:Real at none:2

julia&gt; Point{Int64}(1, 2) ## explicit T ##
Point{Int64}(1, 2)

julia&gt; Point{Int64}(1.0,2.5) ## explicit T ##
ERROR: InexactError: Int64(2.5)
Stacktrace:
[...]

julia&gt; Point{Float64}(1.0, 2.5) ## explicit T ##
Point{Float64}(1.0, 2.5)

julia&gt; Point{Float64}(1,2) ## explicit T ##
Point{Float64}(1.0, 2.0)</code></pre><p>As you can see, for constructor calls with explicit type parameters, the arguments are converted to the implied field types: <code>Point{Int64}(1,2)</code> works, but <code>Point{Int64}(1.0,2.5)</code> raises an <a href="../base/base.html#Core.InexactError"><code>InexactError</code></a> when converting <code>2.5</code> to <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>. When the type is implied by the arguments to the constructor call, as in <code>Point(1,2)</code>, then the types of the arguments must agree – otherwise the <code>T</code> cannot be determined – but any pair of real arguments with matching type may be given to the generic <code>Point</code> constructor.</p><p>What&#39;s really going on here is that <code>Point</code>, <code>Point{Float64}</code> and <code>Point{Int64}</code> are all different constructor functions. In fact, <code>Point{T}</code> is a distinct constructor function for each type <code>T</code>. Without any explicitly provided inner constructors, the declaration of the composite type <code>Point{T&lt;:Real}</code> automatically provides an inner constructor, <code>Point{T}</code>, for each possible type <code>T&lt;:Real</code>, that behaves just like non-parametric default inner constructors do. It also provides a single general outer <code>Point</code> constructor that takes pairs of real arguments, which must be of the same type. This automatic provision of constructors is equivalent to the following explicit declaration:</p><pre><code class="language-julia-repl hljs">julia&gt; struct Point{T&lt;:Real}
           x::T
           y::T
           Point{T}(x,y) where {T&lt;:Real} = new(x,y)
       end

julia&gt; Point(x::T, y::T) where {T&lt;:Real} = Point{T}(x,y);</code></pre><p>Notice that each definition looks like the form of constructor call that it handles. The call <code>Point{Int64}(1,2)</code> will invoke the definition <code>Point{T}(x,y)</code> inside the <code>struct</code> block. The outer constructor declaration, on the other hand, defines a method for the general <code>Point</code> constructor which only applies to pairs of values of the same real type. This declaration makes constructor calls without explicit type parameters, like <code>Point(1,2)</code> and <code>Point(1.0,2.5)</code>, work. Since the method declaration restricts the arguments to being of the same type, calls like <code>Point(1,2.5)</code>, with arguments of different types, result in &quot;no method&quot; errors.</p><p>Suppose we wanted to make the constructor call <code>Point(1,2.5)</code> work by &quot;promoting&quot; the integer value <code>1</code> to the floating-point value <code>1.0</code>. The simplest way to achieve this is to define the following additional outer constructor method:</p><pre><code class="language-julia-repl hljs">julia&gt; Point(x::Int64, y::Float64) = Point(convert(Float64,x),y);</code></pre><p>This method uses the <a href="../base/base.html#Base.convert"><code>convert</code></a> function to explicitly convert <code>x</code> to <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> and then delegates construction to the general constructor for the case where both arguments are <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a>. With this method definition what was previously a <a href="../base/base.html#Core.MethodError"><code>MethodError</code></a> now successfully creates a point of type <code>Point{Float64}</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; p = Point(1,2.5)
Point{Float64}(1.0, 2.5)

julia&gt; typeof(p)
Point{Float64}</code></pre><p>However, other similar calls still don&#39;t work:</p><pre><code class="language-julia-repl hljs">julia&gt; Point(1.5,2)
ERROR: MethodError: no method matching Point(::Float64, ::Int64)
The type `Point` exists, but no method is defined for this combination of argument types when trying to construct it.

Closest candidates are:
  Point(::T, !Matched::T) where T&lt;:Real
   @ Main none:1
  Point(!Matched::Int64, !Matched::Float64)
   @ Main none:1

Stacktrace:
[...]</code></pre><p>For a more general way to make all such calls work sensibly, see <a href="conversion-and-promotion.html#conversion-and-promotion">Conversion and Promotion</a>. At the risk of spoiling the suspense, we can reveal here that all it takes is the following outer method definition to make all calls to the general <code>Point</code> constructor work as one would expect:</p><pre><code class="language-julia-repl hljs">julia&gt; Point(x::Real, y::Real) = Point(promote(x,y)...);</code></pre><p>The <code>promote</code> function converts all its arguments to a common type – in this case <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a>. With this method definition, the <code>Point</code> constructor promotes its arguments the same way that numeric operators like <a href="../base/math.html#Base.:+"><code>+</code></a> do, and works for all kinds of real numbers:</p><pre><code class="language-julia-repl hljs">julia&gt; Point(1.5,2)
Point{Float64}(1.5, 2.0)

julia&gt; Point(1,1//2)
Point{Rational{Int64}}(1//1, 1//2)

julia&gt; Point(1.0,1//2)
Point{Float64}(1.0, 0.5)</code></pre><p>Thus, while the implicit type parameter constructors provided by default in Julia are fairly strict, it is possible to make them behave in a more relaxed but sensible manner quite easily. Moreover, since constructors can leverage all of the power of the type system, methods, and multiple dispatch, defining sophisticated behavior is typically quite simple.</p><h2 id="Case-Study:-Rational"><a class="docs-heading-anchor" href="#Case-Study:-Rational">Case Study: Rational</a><a id="Case-Study:-Rational-1"></a><a class="docs-heading-anchor-permalink" href="#Case-Study:-Rational" title="Permalink"></a></h2><p>Perhaps the best way to tie all these pieces together is to present a real world example of a parametric composite type and its constructor methods. To that end, we implement our own rational number type <code>OurRational</code>, similar to Julia&#39;s built-in <a href="../base/numbers.html#Base.Rational"><code>Rational</code></a> type, defined in <a href="https://github.com/JuliaLang/julia/blob/master/base/rational.jl"><code>rational.jl</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; struct OurRational{T&lt;:Integer} &lt;: Real
           num::T
           den::T
           function OurRational{T}(num::T, den::T) where T&lt;:Integer
               if num == 0 &amp;&amp; den == 0
                    error(&quot;invalid rational: 0//0&quot;)
               end
               num = flipsign(num, den)
               den = flipsign(den, den)
               g = gcd(num, den)
               num = div(num, g)
               den = div(den, g)
               new(num, den)
           end
       end

julia&gt; OurRational(n::T, d::T) where {T&lt;:Integer} = OurRational{T}(n,d)
OurRational

julia&gt; OurRational(n::Integer, d::Integer) = OurRational(promote(n,d)...)
OurRational

julia&gt; OurRational(n::Integer) = OurRational(n,one(n))
OurRational

julia&gt; ⊘(n::Integer, d::Integer) = OurRational(n,d)
⊘ (generic function with 1 method)

julia&gt; ⊘(x::OurRational, y::Integer) = x.num ⊘ (x.den*y)
⊘ (generic function with 2 methods)

julia&gt; ⊘(x::Integer, y::OurRational) = (x*y.den) ⊘ y.num
⊘ (generic function with 3 methods)

julia&gt; ⊘(x::Complex, y::Real) = complex(real(x) ⊘ y, imag(x) ⊘ y)
⊘ (generic function with 4 methods)

julia&gt; ⊘(x::Real, y::Complex) = (x*y&#39;) ⊘ real(y*y&#39;)
⊘ (generic function with 5 methods)

julia&gt; function ⊘(x::Complex, y::Complex)
           xy = x*y&#39;
           yy = real(y*y&#39;)
           complex(real(xy) ⊘ yy, imag(xy) ⊘ yy)
       end
⊘ (generic function with 6 methods)</code></pre><p>The first line – <code>struct OurRational{T&lt;:Integer} &lt;: Real</code> – declares that <code>OurRational</code> takes one type parameter of an integer type, and is itself a real type. The field declarations <code>num::T</code> and <code>den::T</code> indicate that the data held in a <code>OurRational{T}</code> object are a pair of integers of type <code>T</code>, one representing the rational value&#39;s numerator and the other representing its denominator.</p><p>Now things get interesting. <code>OurRational</code> has a single inner constructor method which checks that <code>num</code> and <code>den</code> aren&#39;t both zero and ensures that every rational is constructed in &quot;lowest terms&quot; with a non-negative denominator. This is accomplished by first flipping the signs of numerator and denominator if the denominator is negative. Then, both are divided by their greatest common divisor (<code>gcd</code> always returns a non-negative number, regardless of the sign of its arguments). Because this is the only inner constructor for <code>OurRational</code>, we can be certain that <code>OurRational</code> objects are always constructed in this normalized form.</p><p><code>OurRational</code> also provides several outer constructor methods for convenience. The first is the &quot;standard&quot; general constructor that infers the type parameter <code>T</code> from the type of the numerator and denominator when they have the same type. The second applies when the given numerator and denominator values have different types: it promotes them to a common type and then delegates construction to the outer constructor for arguments of matching type. The third outer constructor turns integer values into rationals by supplying a value of <code>1</code> as the denominator.</p><p>Following the outer constructor definitions, we defined a number of methods for the <code>⊘</code> operator, which provides a syntax for writing rationals (e.g. <code>1 ⊘ 2</code>). Julia&#39;s <code>Rational</code> type uses the <a href="../base/math.html#Base.://"><code>//</code></a> operator for this purpose. Before these definitions, <code>⊘</code> is a completely undefined operator with only syntax and no meaning. Afterwards, it behaves just as described in <a href="complex-and-rational-numbers.html#Rational-Numbers">Rational Numbers</a> – its entire behavior is defined in these few lines. Note that the infix use of <code>⊘</code> works because Julia has a set of symbols that are recognized to be infix operators. The first and most basic definition just makes <code>a ⊘ b</code> construct a <code>OurRational</code> by applying the <code>OurRational</code> constructor to <code>a</code> and <code>b</code> when they are integers. When one of the operands of <code>⊘</code> is already a rational number, we construct a new rational for the resulting ratio slightly differently; this behavior is actually identical to division of a rational with an integer. Finally, applying <code>⊘</code> to complex integral values creates an instance of <code>Complex{&lt;:OurRational}</code> – a complex number whose real and imaginary parts are rationals:</p><pre><code class="language-julia-repl hljs">julia&gt; z = (1 + 2im) ⊘ (1 - 2im);

julia&gt; typeof(z)
Complex{OurRational{Int64}}

julia&gt; typeof(z) &lt;: Complex{&lt;:OurRational}
true</code></pre><p>Thus, although the <code>⊘</code> operator usually returns an instance of <code>OurRational</code>, if either of its arguments are complex integers, it will return an instance of <code>Complex{&lt;:OurRational}</code> instead. The interested reader should consider perusing the rest of <a href="https://github.com/JuliaLang/julia/blob/master/base/rational.jl"><code>rational.jl</code></a>: it is short, self-contained, and implements an entire basic Julia type.</p><h2 id="Outer-only-constructors"><a class="docs-heading-anchor" href="#Outer-only-constructors">Outer-only constructors</a><a id="Outer-only-constructors-1"></a><a class="docs-heading-anchor-permalink" href="#Outer-only-constructors" title="Permalink"></a></h2><p>As we have seen, a typical parametric type has inner constructors that are called when type parameters are known; e.g. they apply to <code>Point{Int}</code> but not to <code>Point</code>. Optionally, outer constructors that determine type parameters automatically can be added, for example constructing a <code>Point{Int}</code> from the call <code>Point(1,2)</code>. Outer constructors call inner constructors to actually make instances. However, in some cases one would rather not provide inner constructors, so that specific type parameters cannot be requested manually.</p><p>For example, say we define a type that stores a vector along with an accurate representation of its sum:</p><pre><code class="language-julia-repl hljs">julia&gt; struct SummedArray{T&lt;:Number,S&lt;:Number}
           data::Vector{T}
           sum::S
       end

julia&gt; SummedArray(Int32[1; 2; 3], Int32(6))
SummedArray{Int32, Int32}(Int32[1, 2, 3], 6)</code></pre><p>The problem is that we want <code>S</code> to be a larger type than <code>T</code>, so that we can sum many elements with less information loss. For example, when <code>T</code> is <a href="../base/numbers.html#Core.Int32"><code>Int32</code></a>, we would like <code>S</code> to be <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>. Therefore we want to avoid an interface that allows the user to construct instances of the type <code>SummedArray{Int32,Int32}</code>. One way to do this is to provide a constructor only for <code>SummedArray</code>, but inside the <code>struct</code> definition block to suppress generation of default constructors:</p><pre><code class="language-julia-repl hljs">julia&gt; struct SummedArray{T&lt;:Number,S&lt;:Number}
           data::Vector{T}
           sum::S
           function SummedArray(a::Vector{T}) where T
               S = widen(T)
               new{T,S}(a, sum(S, a))
           end
       end

julia&gt; SummedArray(Int32[1; 2; 3], Int32(6))
ERROR: MethodError: no method matching SummedArray(::Vector{Int32}, ::Int32)
The type `SummedArray` exists, but no method is defined for this combination of argument types when trying to construct it.

Closest candidates are:
  SummedArray(::Vector{T}) where T
   @ Main none:4

Stacktrace:
[...]</code></pre><p>This constructor will be invoked by the syntax <code>SummedArray(a)</code>. The syntax <code>new{T,S}</code> allows specifying parameters for the type to be constructed, i.e. this call will return a <code>SummedArray{T,S}</code>. <code>new{T,S}</code> can be used in any constructor definition, but for convenience the parameters to <code>new{}</code> are automatically derived from the type being constructed when possible.</p><h2 id="Constructors-are-just-callable-objects"><a class="docs-heading-anchor" href="#Constructors-are-just-callable-objects">Constructors are just callable objects</a><a id="Constructors-are-just-callable-objects-1"></a><a class="docs-heading-anchor-permalink" href="#Constructors-are-just-callable-objects" title="Permalink"></a></h2><p>An object of any type may be <a href="methods.html#Function-like-objects">made callable</a> by defining a method. This includes types, i.e., objects of type <a href="../base/base.html#Core.Type"><code>Type</code></a>; and constructors may, in fact, be viewed as just callable type objects. For example, there are many methods defined on <code>Bool</code> and various supertypes of it:</p><pre><code class="language-julia-repl hljs">julia&gt; methods(Bool)
# 10 methods for type constructor:
  [1] Bool(x::BigFloat)
     @ Base.MPFR mpfr.jl:393
  [2] Bool(x::Float16)
     @ Base float.jl:338
  [3] Bool(x::Rational)
     @ Base rational.jl:138
  [4] Bool(x::Real)
     @ Base float.jl:233
  [5] (dt::Type{&lt;:Integer})(ip::Sockets.IPAddr)
     @ Sockets ~/tmp/jl/jl/julia-nightly-assert/share/julia/stdlib/v1.11/Sockets/src/IPAddr.jl:11
  [6] (::Type{T})(x::Enum{T2}) where {T&lt;:Integer, T2&lt;:Integer}
     @ Base.Enums Enums.jl:19
  [7] (::Type{T})(z::Complex) where T&lt;:Real
     @ Base complex.jl:44
  [8] (::Type{T})(x::Base.TwicePrecision) where T&lt;:Number
     @ Base twiceprecision.jl:265
  [9] (::Type{T})(x::T) where T&lt;:Number
     @ boot.jl:894
 [10] (::Type{T})(x::AbstractChar) where T&lt;:Union{AbstractChar, Number}
     @ char.jl:50</code></pre><p>The usual constructor syntax is exactly equivalent to the function-like object syntax, so trying to define a method with each syntax will cause the first method to be overwritten by the next one:</p><pre><code class="language-julia-repl hljs">julia&gt; struct S
           f::Int
       end

julia&gt; S() = S(7)
S

julia&gt; (::Type{S})() = S(8)  # overwrites the previous constructor method

julia&gt; S()
S(8)</code></pre><section class="footnotes is-size-7"><ul><li class="footnote" id="footnote-1"><a class="tag is-link" href="#citeref-1">1</a>Nomenclature: while the term &quot;constructor&quot; generally refers to the entire function which constructs objects of a type, it is common to abuse terminology slightly and refer to specific constructor methods as &quot;constructors&quot;. In such situations, it is generally clear from the context that the term is used to mean &quot;constructor method&quot; rather than &quot;constructor function&quot;, especially as it is often used in the sense of singling out a particular method of the constructor from all of the others.</li></ul></section></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="methods.html">« Methods</a><a class="docs-footer-nextpage" href="conversion-and-promotion.html">Conversion and Promotion »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
