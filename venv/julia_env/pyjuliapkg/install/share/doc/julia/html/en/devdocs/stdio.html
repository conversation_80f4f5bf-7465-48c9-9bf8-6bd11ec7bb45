<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>printf() and stdio in the Julia runtime · The Julia Language</title><meta name="title" content="printf() and stdio in the Julia runtime · The Julia Language"/><meta property="og:title" content="printf() and stdio in the Julia runtime · The Julia Language"/><meta property="twitter:title" content="printf() and stdio in the Julia runtime · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox" checked/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="ast.html">Julia ASTs</a></li><li><a class="tocitem" href="types.html">More about types</a></li><li><a class="tocitem" href="object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="functions.html">Julia Functions</a></li><li><a class="tocitem" href="cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="subarrays.html">SubArrays</a></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="llvm.html">Working with LLVM</a></li><li class="is-active"><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a><ul class="internal"><li><a class="tocitem" href="#Libuv-wrappers-for-stdio"><span>Libuv wrappers for stdio</span></a></li><li><a class="tocitem" href="#Interface-between-JL_STD*-and-Julia-code"><span>Interface between JL_STD* and Julia code</span></a></li><li><a class="tocitem" href="#printf()-during-initialization"><span>printf() during initialization</span></a></li><li><a class="tocitem" href="#Legacy-ios.c-library"><span>Legacy <code>ios.c</code> library</span></a></li></ul></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Documentation of Julia&#39;s Internals</a></li><li class="is-active"><a href="stdio.html">printf() and stdio in the Julia runtime</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="stdio.html">printf() and stdio in the Julia runtime</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/stdio.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="printf()-and-stdio-in-the-Julia-runtime"><a class="docs-heading-anchor" href="#printf()-and-stdio-in-the-Julia-runtime">printf() and stdio in the Julia runtime</a><a id="printf()-and-stdio-in-the-Julia-runtime-1"></a><a class="docs-heading-anchor-permalink" href="#printf()-and-stdio-in-the-Julia-runtime" title="Permalink"></a></h1><h2 id="Libuv-wrappers-for-stdio"><a class="docs-heading-anchor" href="#Libuv-wrappers-for-stdio">Libuv wrappers for stdio</a><a id="Libuv-wrappers-for-stdio-1"></a><a class="docs-heading-anchor-permalink" href="#Libuv-wrappers-for-stdio" title="Permalink"></a></h2><p><code>julia.h</code> defines <a href="https://docs.libuv.org">libuv</a> wrappers for the <code>stdio.h</code> streams:</p><pre><code class="language-c hljs">uv_stream_t *JL_STDIN;
uv_stream_t *JL_STDOUT;
uv_stream_t *JL_STDERR;</code></pre><p>... and corresponding output functions:</p><pre><code class="language-c hljs">int jl_printf(uv_stream_t *s, const char *format, ...);
int jl_vprintf(uv_stream_t *s, const char *format, va_list args);</code></pre><p>These <code>printf</code> functions are used by the <code>.c</code> files in the <code>src/</code> and <code>cli/</code> directories wherever stdio is needed to ensure that output buffering is handled in a unified way.</p><p>In special cases, like signal handlers, where the full libuv infrastructure is too heavy, <code>jl_safe_printf()</code> can be used to <a href="../base/file.html#Base.write-Tuple{String, Any}"><code>write(2)</code></a> directly to <code>STDERR_FILENO</code>:</p><pre><code class="language-c hljs">void jl_safe_printf(const char *str, ...);</code></pre><h2 id="Interface-between-JL_STD*-and-Julia-code"><a class="docs-heading-anchor" href="#Interface-between-JL_STD*-and-Julia-code">Interface between JL_STD* and Julia code</a><a id="Interface-between-JL_STD*-and-Julia-code-1"></a><a class="docs-heading-anchor-permalink" href="#Interface-between-JL_STD*-and-Julia-code" title="Permalink"></a></h2><p><a href="../base/io-network.html#Base.stdin"><code>Base.stdin</code></a>, <a href="../base/io-network.html#Base.stdout"><code>Base.stdout</code></a> and <a href="../base/io-network.html#Base.stderr"><code>Base.stderr</code></a> are bound to the <code>JL_STD*</code> libuv streams defined in the runtime.</p><p>Julia&#39;s <code>__init__()</code> function (in <code>base/sysimg.jl</code>) calls <code>reinit_stdio()</code> (in <code>base/stream.jl</code>) to create Julia objects for <a href="../base/io-network.html#Base.stdin"><code>Base.stdin</code></a>, <a href="../base/io-network.html#Base.stdout"><code>Base.stdout</code></a> and <a href="../base/io-network.html#Base.stderr"><code>Base.stderr</code></a>.</p><p><code>reinit_stdio()</code> uses <a href="../base/c.html#ccall"><code>ccall</code></a> to retrieve pointers to <code>JL_STD*</code> and calls <code>jl_uv_handle_type()</code> to inspect the type of each stream.  It then creates a Julia <code>Base.IOStream</code>, <code>Base.TTY</code> or <code>Base.PipeEndpoint</code> object to represent each stream, e.g.:</p><pre><code class="nohighlight hljs">$ julia -e &#39;println(typeof((stdin, stdout, stderr)))&#39;
Tuple{Base.TTY,Base.TTY,Base.TTY}

$ julia -e &#39;println(typeof((stdin, stdout, stderr)))&#39; &lt; /dev/null 2&gt;/dev/null
Tuple{IOStream,Base.TTY,IOStream}

$ echo hello | julia -e &#39;println(typeof((stdin, stdout, stderr)))&#39; | cat
Tuple{Base.PipeEndpoint,Base.PipeEndpoint,Base.TTY}</code></pre><p>The <a href="../base/io-network.html#Base.read"><code>Base.read</code></a> and <a href="../base/io-network.html#Base.write"><code>Base.write</code></a> methods for these streams use <a href="../base/c.html#ccall"><code>ccall</code></a> to call libuv wrappers in <code>src/jl_uv.c</code>, e.g.:</p><pre><code class="nohighlight hljs">stream.jl: function write(s::IO, p::Ptr, nb::Integer)
               -&gt; ccall(:jl_uv_write, ...)
  jl_uv.c:          -&gt; int jl_uv_write(uv_stream_t *stream, ...)
                        -&gt; uv_write(uvw, stream, buf, ...)</code></pre><h2 id="printf()-during-initialization"><a class="docs-heading-anchor" href="#printf()-during-initialization">printf() during initialization</a><a id="printf()-during-initialization-1"></a><a class="docs-heading-anchor-permalink" href="#printf()-during-initialization" title="Permalink"></a></h2><p>The libuv streams relied upon by <code>jl_printf()</code> etc., are not available until midway through initialization of the runtime (see <code>init.c</code>, <code>init_stdio()</code>).  Error messages or warnings that need to be printed before this are routed to the standard C library <code>fwrite()</code> function by the following mechanism:</p><p>In <code>sys.c</code>, the <code>JL_STD*</code> stream pointers are statically initialized to integer constants: <code>STD*_FILENO (0, 1 and 2)</code>. In <code>jl_uv.c</code> the <code>jl_uv_puts()</code> function checks its <code>uv_stream_t* stream</code> argument and calls <code>fwrite()</code> if stream is set to <code>STDOUT_FILENO</code> or <code>STDERR_FILENO</code>.</p><p>This allows for uniform use of <code>jl_printf()</code> throughout the runtime regardless of whether or not any particular piece of code is reachable before initialization is complete.</p><h2 id="Legacy-ios.c-library"><a class="docs-heading-anchor" href="#Legacy-ios.c-library">Legacy <code>ios.c</code> library</a><a id="Legacy-ios.c-library-1"></a><a class="docs-heading-anchor-permalink" href="#Legacy-ios.c-library" title="Permalink"></a></h2><p>The <code>src/support/ios.c</code> library is inherited from <a href="https://github.com/JeffBezanson/femtolisp">femtolisp</a>. It provides cross-platform buffered file IO and in-memory temporary buffers.</p><p><code>ios.c</code> is still used by:</p><ul><li><code>src/flisp/*.c</code></li><li><code>src/dump.c</code> – for serialization file IO and for memory buffers.</li><li><code>src/staticdata.c</code> – for serialization file IO and for memory buffers.</li><li><code>base/iostream.jl</code> – for file IO (see <code>base/fs.jl</code> for libuv equivalent).</li></ul><p>Use of <code>ios.c</code> in these modules is mostly self-contained and separated from the libuv I/O system. However, there is <a href="https://github.com/JuliaLang/julia/blob/master/src/flisp/print.c#L654">one place</a> where femtolisp calls through to <code>jl_printf()</code> with a legacy <code>ios_t</code> stream.</p><p>There is a hack in <code>ios.h</code> that makes the <code>ios_t.bm</code> field line up with the <code>uv_stream_t.type</code> and ensures that the values used for <code>ios_t.bm</code> to not overlap with valid <code>UV_HANDLE_TYPE</code> values.  This allows <code>uv_stream_t</code> pointers to point to <code>ios_t</code> streams.</p><p>This is needed because <code>jl_printf()</code> caller <code>jl_static_show()</code> is passed an <code>ios_t</code> stream by femtolisp&#39;s <code>fl_print()</code> function. Julia&#39;s <code>jl_uv_puts()</code> function has special handling for this:</p><pre><code class="language-c hljs">if (stream-&gt;type &gt; UV_HANDLE_TYPE_MAX) {
    return ios_write((ios_t*)stream, str, n);
}</code></pre></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="llvm.html">« Working with LLVM</a><a class="docs-footer-nextpage" href="boundscheck.html">Bounds checking »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
