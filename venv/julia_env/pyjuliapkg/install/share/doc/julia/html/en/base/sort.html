<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Sorting and Related Functions · The Julia Language</title><meta name="title" content="Sorting and Related Functions · The Julia Language"/><meta property="og:title" content="Sorting and Related Functions · The Julia Language"/><meta property="twitter:title" content="Sorting and Related Functions · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox" checked/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="base.html">Essentials</a></li><li><a class="tocitem" href="collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="math.html">Mathematics</a></li><li><a class="tocitem" href="numbers.html">Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="arrays.html">Arrays</a></li><li><a class="tocitem" href="parallel.html">Tasks</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="constants.html">Constants</a></li><li><a class="tocitem" href="file.html">Filesystem</a></li><li><a class="tocitem" href="io-network.html">I/O and Network</a></li><li><a class="tocitem" href="punctuation.html">Punctuation</a></li><li class="is-active"><a class="tocitem" href="sort.html">Sorting and Related Functions</a><ul class="internal"><li><a class="tocitem" href="#Sorting-Functions"><span>Sorting Functions</span></a></li><li><a class="tocitem" href="#Order-Related-Functions"><span>Order-Related Functions</span></a></li><li><a class="tocitem" href="#Sorting-Algorithms"><span>Sorting Algorithms</span></a></li><li><a class="tocitem" href="#Alternate-Orderings"><span>Alternate Orderings</span></a></li></ul></li><li><a class="tocitem" href="iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="c.html">C Interface</a></li><li><a class="tocitem" href="libc.html">C Standard Library</a></li><li><a class="tocitem" href="stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Base</a></li><li class="is-active"><a href="sort.html">Sorting and Related Functions</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="sort.html">Sorting and Related Functions</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/base/sort.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Sorting-and-Related-Functions"><a class="docs-heading-anchor" href="#Sorting-and-Related-Functions">Sorting and Related Functions</a><a id="Sorting-and-Related-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Sorting-and-Related-Functions" title="Permalink"></a></h1><p>Julia has an extensive, flexible API for sorting and interacting with already-sorted arrays of values. By default, Julia picks reasonable algorithms and sorts in ascending order:</p><pre><code class="language-julia-repl hljs">julia&gt; sort([2,3,1])
3-element Vector{Int64}:
 1
 2
 3</code></pre><p>You can sort in reverse order as well:</p><pre><code class="language-julia-repl hljs">julia&gt; sort([2,3,1], rev=true)
3-element Vector{Int64}:
 3
 2
 1</code></pre><p><code>sort</code> constructs a sorted copy leaving its input unchanged. Use the &quot;bang&quot; version of the sort function to mutate an existing array:</p><pre><code class="language-julia-repl hljs">julia&gt; a = [2,3,1];

julia&gt; sort!(a);

julia&gt; a
3-element Vector{Int64}:
 1
 2
 3</code></pre><p>Instead of directly sorting an array, you can compute a permutation of the array&#39;s indices that puts the array into sorted order:</p><pre><code class="language-julia-repl hljs">julia&gt; v = randn(5)
5-element Array{Float64,1}:
  0.297288
  0.382396
 -0.597634
 -0.0104452
 -0.839027

julia&gt; p = sortperm(v)
5-element Array{Int64,1}:
 5
 3
 4
 1
 2

julia&gt; v[p]
5-element Array{Float64,1}:
 -0.839027
 -0.597634
 -0.0104452
  0.297288
  0.382396</code></pre><p>Arrays can be sorted according to an arbitrary transformation of their values:</p><pre><code class="language-julia-repl hljs">julia&gt; sort(v, by=abs)
5-element Array{Float64,1}:
 -0.0104452
  0.297288
  0.382396
 -0.597634
 -0.839027</code></pre><p>Or in reverse order by a transformation:</p><pre><code class="language-julia-repl hljs">julia&gt; sort(v, by=abs, rev=true)
5-element Array{Float64,1}:
 -0.839027
 -0.597634
  0.382396
  0.297288
 -0.0104452</code></pre><p>If needed, the sorting algorithm can be chosen:</p><pre><code class="language-julia-repl hljs">julia&gt; sort(v, alg=InsertionSort)
5-element Array{Float64,1}:
 -0.839027
 -0.597634
 -0.0104452
  0.297288
  0.382396</code></pre><p>All the sorting and order related functions rely on a &quot;less than&quot; relation defining a <a href="https://en.wikipedia.org/wiki/Weak_ordering#Strict_weak_orderings">strict weak order</a> on the values to be manipulated. The <code>isless</code> function is invoked by default, but the relation can be specified via the <code>lt</code> keyword, a function that takes two array elements and returns <code>true</code> if and only if the first argument is &quot;less than&quot; the second. See <a href="sort.html#Base.sort!"><code>sort!</code></a> and <a href="sort.html#Alternate-Orderings">Alternate Orderings</a> for more information.</p><h2 id="Sorting-Functions"><a class="docs-heading-anchor" href="#Sorting-Functions">Sorting Functions</a><a id="Sorting-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Sorting-Functions" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.sort!" href="#Base.sort!"><code>Base.sort!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sort!(v; alg::Algorithm=defalg(v), lt=isless, by=identity, rev::Bool=false, order::Ordering=Forward)</code></pre><p>Sort the vector <code>v</code> in place. A stable algorithm is used by default: the ordering of elements that compare equal is preserved. A specific algorithm can be selected via the <code>alg</code> keyword (see <a href="sort.html#Sorting-Algorithms">Sorting Algorithms</a> for available algorithms).</p><p>Elements are first transformed with the function <code>by</code> and then compared according to either the function <code>lt</code> or the ordering <code>order</code>. Finally, the resulting order is reversed if <code>rev=true</code> (this preserves forward stability: elements that compare equal are not reversed). The current implementation applies the <code>by</code> transformation before each comparison rather than once per element.</p><p>Passing an <code>lt</code> other than <code>isless</code> along with an <code>order</code> other than <a href="sort.html#Base.Order.Forward"><code>Base.Order.Forward</code></a> or <a href="sort.html#Base.Order.Reverse"><code>Base.Order.Reverse</code></a> is not permitted, otherwise all options are independent and can be used together in all possible combinations. Note that <code>order</code> can also include a &quot;by&quot; transformation, in which case it is applied after that defined with the <code>by</code> keyword. For more information on <code>order</code> values see the documentation on <a href="sort.html#Alternate-Orderings">Alternate Orderings</a>.</p><p>Relations between two elements are defined as follows (with &quot;less&quot; and &quot;greater&quot; exchanged when <code>rev=true</code>):</p><ul><li><code>x</code> is less than <code>y</code> if <code>lt(by(x), by(y))</code> (or <code>Base.Order.lt(order, by(x), by(y))</code>) yields true.</li><li><code>x</code> is greater than <code>y</code> if <code>y</code> is less than <code>x</code>.</li><li><code>x</code> and <code>y</code> are equivalent if neither is less than the other (&quot;incomparable&quot; is sometimes used as a synonym for &quot;equivalent&quot;).</li></ul><p>The result of <code>sort!</code> is sorted in the sense that every element is greater than or equivalent to the previous one.</p><p>The <code>lt</code> function must define a strict weak order, that is, it must be</p><ul><li>irreflexive: <code>lt(x, x)</code> always yields <code>false</code>,</li><li>asymmetric: if <code>lt(x, y)</code> yields <code>true</code> then <code>lt(y, x)</code> yields <code>false</code>,</li><li>transitive: <code>lt(x, y) &amp;&amp; lt(y, z)</code> implies <code>lt(x, z)</code>,</li><li>transitive in equivalence: <code>!lt(x, y) &amp;&amp; !lt(y, x)</code> and <code>!lt(y, z) &amp;&amp; !lt(z, y)</code> together imply <code>!lt(x, z) &amp;&amp; !lt(z, x)</code>. In words: if <code>x</code> and <code>y</code> are equivalent and <code>y</code> and <code>z</code> are equivalent then <code>x</code> and <code>z</code> must be equivalent.</li></ul><p>For example <code>&lt;</code> is a valid <code>lt</code> function for <code>Int</code> values but <code>≤</code> is not: it violates irreflexivity. For <code>Float64</code> values even <code>&lt;</code> is invalid as it violates the fourth condition: <code>1.0</code> and <code>NaN</code> are equivalent and so are <code>NaN</code> and <code>2.0</code> but <code>1.0</code> and <code>2.0</code> are not equivalent.</p><p>See also <a href="sort.html#Base.sort"><code>sort</code></a>, <a href="sort.html#Base.sortperm"><code>sortperm</code></a>, <a href="sort.html#Base.sortslices"><code>sortslices</code></a>, <a href="sort.html#Base.Sort.partialsort!"><code>partialsort!</code></a>, <a href="sort.html#Base.Sort.partialsortperm"><code>partialsortperm</code></a>, <a href="sort.html#Base.issorted"><code>issorted</code></a>, <a href="sort.html#Base.Sort.searchsorted"><code>searchsorted</code></a>, <a href="sort.html#Base.Sort.insorted"><code>insorted</code></a>, <a href="sort.html#Base.Order.ord"><code>Base.Order.ord</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; v = [3, 1, 2]; sort!(v); v
3-element Vector{Int64}:
 1
 2
 3

julia&gt; v = [3, 1, 2]; sort!(v, rev = true); v
3-element Vector{Int64}:
 3
 2
 1

julia&gt; v = [(1, &quot;c&quot;), (3, &quot;a&quot;), (2, &quot;b&quot;)]; sort!(v, by = x -&gt; x[1]); v
3-element Vector{Tuple{Int64, String}}:
 (1, &quot;c&quot;)
 (2, &quot;b&quot;)
 (3, &quot;a&quot;)

julia&gt; v = [(1, &quot;c&quot;), (3, &quot;a&quot;), (2, &quot;b&quot;)]; sort!(v, by = x -&gt; x[2]); v
3-element Vector{Tuple{Int64, String}}:
 (3, &quot;a&quot;)
 (2, &quot;b&quot;)
 (1, &quot;c&quot;)

julia&gt; sort(0:3, by=x-&gt;x-2, order=Base.Order.By(abs)) # same as sort(0:3, by=abs(x-&gt;x-2))
4-element Vector{Int64}:
 2
 1
 3
 0

julia&gt; sort([2, NaN, 1, NaN, 3]) # correct sort with default lt=isless
5-element Vector{Float64}:
   1.0
   2.0
   3.0
 NaN
 NaN

julia&gt; sort([2, NaN, 1, NaN, 3], lt=&lt;) # wrong sort due to invalid lt. This behavior is undefined.
5-element Vector{Float64}:
   2.0
 NaN
   1.0
 NaN
   3.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L1585-L1686">source</a></section><section><div><pre><code class="language-julia hljs">sort!(A; dims::Integer, alg::Algorithm=defalg(A), lt=isless, by=identity, rev::Bool=false, order::Ordering=Forward)</code></pre><p>Sort the multidimensional array <code>A</code> along dimension <code>dims</code>. See the one-dimensional version of <a href="sort.html#Base.sort!"><code>sort!</code></a> for a description of possible keyword arguments.</p><p>To sort slices of an array, refer to <a href="sort.html#Base.sortslices"><code>sortslices</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p>This function requires at least Julia 1.1.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [4 3; 1 2]
2×2 Matrix{Int64}:
 4  3
 1  2

julia&gt; sort!(A, dims = 1); A
2×2 Matrix{Int64}:
 1  2
 4  3

julia&gt; sort!(A, dims = 2); A
2×2 Matrix{Int64}:
 1  2
 3  4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L2059-L2088">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.sort" href="#Base.sort"><code>Base.sort</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sort(v; alg::Algorithm=defalg(v), lt=isless, by=identity, rev::Bool=false, order::Ordering=Forward)</code></pre><p>Variant of <a href="sort.html#Base.sort!"><code>sort!</code></a> that returns a sorted copy of <code>v</code> leaving <code>v</code> itself unmodified.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; v = [3, 1, 2];

julia&gt; sort(v)
3-element Vector{Int64}:
 1
 2
 3

julia&gt; v
3-element Vector{Int64}:
 3
 1
 2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L1698-L1719">source</a></section><section><div><pre><code class="language-julia hljs">sort(A; dims::Integer, alg::Algorithm=defalg(A), lt=isless, by=identity, rev::Bool=false, order::Ordering=Forward)</code></pre><p>Sort a multidimensional array <code>A</code> along the given dimension. See <a href="sort.html#Base.sort!"><code>sort!</code></a> for a description of possible keyword arguments.</p><p>To sort slices of an array, refer to <a href="sort.html#Base.sortslices"><code>sortslices</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [4 3; 1 2]
2×2 Matrix{Int64}:
 4  3
 1  2

julia&gt; sort(A, dims = 1)
2×2 Matrix{Int64}:
 1  2
 4  3

julia&gt; sort(A, dims = 2)
2×2 Matrix{Int64}:
 3  4
 1  2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L1987-L2013">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.sortperm" href="#Base.sortperm"><code>Base.sortperm</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sortperm(A; alg::Algorithm=DEFAULT_UNSTABLE, lt=isless, by=identity, rev::Bool=false, order::Ordering=Forward, [dims::Integer])</code></pre><p>Return a permutation vector or array <code>I</code> that puts <code>A[I]</code> in sorted order along the given dimension. If <code>A</code> has more than one dimension, then the <code>dims</code> keyword argument must be specified. The order is specified using the same keywords as <a href="sort.html#Base.sort!"><code>sort!</code></a>. The permutation is guaranteed to be stable even if the sorting algorithm is unstable: the indices of equal elements will appear in ascending order.</p><p>See also <a href="sort.html#Base.Sort.sortperm!"><code>sortperm!</code></a>, <a href="sort.html#Base.Sort.partialsortperm"><code>partialsortperm</code></a>, <a href="arrays.html#Base.invperm"><code>invperm</code></a>, <a href="collections.html#Base.indexin"><code>indexin</code></a>. To sort slices of an array, refer to <a href="sort.html#Base.sortslices"><code>sortslices</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p>The method accepting <code>dims</code> requires at least Julia 1.9.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; v = [3, 1, 2];

julia&gt; p = sortperm(v)
3-element Vector{Int64}:
 2
 3
 1

julia&gt; v[p]
3-element Vector{Int64}:
 1
 2
 3

julia&gt; A = [8 7; 5 6]
2×2 Matrix{Int64}:
 8  7
 5  6

julia&gt; sortperm(A, dims = 1)
2×2 Matrix{Int64}:
 2  4
 1  3

julia&gt; sortperm(A, dims = 2)
2×2 Matrix{Int64}:
 3  1
 2  4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L1821-L1867">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Sort.InsertionSort" href="#Base.Sort.InsertionSort"><code>Base.Sort.InsertionSort</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">InsertionSort</code></pre><p>Use the insertion sort algorithm.</p><p>Insertion sort traverses the collection one element at a time, inserting each element into its correct, sorted position in the output vector.</p><p>Characteristics:</p><ul><li><em>stable</em>: preserves the ordering of elements that compare equal</li></ul><p>(e.g. &quot;a&quot; and &quot;A&quot; in a sort of letters that ignores case).</p><ul><li><em>in-place</em> in memory.</li><li><em>quadratic performance</em> in the number of elements to be sorted:</li></ul><p>it is well-suited to small collections but should not be used for large ones.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L799-L813">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Sort.MergeSort" href="#Base.Sort.MergeSort"><code>Base.Sort.MergeSort</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">MergeSort</code></pre><p>Indicate that a sorting function should use the merge sort algorithm. Merge sort divides the collection into subcollections and repeatedly merges them, sorting each subcollection at each step, until the entire collection has been recombined in sorted form.</p><p>Characteristics:</p><ul><li><em>stable</em>: preserves the ordering of elements that compare equal (e.g. &quot;a&quot; and &quot;A&quot; in a sort of letters that ignores case).</li><li><em>not in-place</em> in memory.</li><li><em>divide-and-conquer</em> sort strategy.</li><li><em>good performance</em> for large collections but typically not quite as fast as <a href="sort.html#Base.Sort.QuickSort"><code>QuickSort</code></a>.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L2278-L2295">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Sort.QuickSort" href="#Base.Sort.QuickSort"><code>Base.Sort.QuickSort</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">QuickSort</code></pre><p>Indicate that a sorting function should use the quick sort algorithm, which is <em>not</em> stable.</p><p>Characteristics:</p><ul><li><em>not stable</em>: does not preserve the ordering of elements that compare equal (e.g. &quot;a&quot; and &quot;A&quot; in a sort of letters that ignores case).</li><li><em>in-place</em> in memory.</li><li><em>divide-and-conquer</em>: sort strategy similar to <a href="sort.html#Base.Sort.MergeSort"><code>MergeSort</code></a>.</li><li><em>good performance</em> for large collections.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L2262-L2275">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Sort.PartialQuickSort" href="#Base.Sort.PartialQuickSort"><code>Base.Sort.PartialQuickSort</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">PartialQuickSort{T &lt;: Union{Integer,OrdinalRange}}</code></pre><p>Indicate that a sorting function should use the partial quick sort algorithm. <code>PartialQuickSort(k)</code> is like <code>QuickSort</code>, but is only required to find and sort the elements that would end up in <code>v[k]</code> were <code>v</code> fully sorted.</p><p>Characteristics:</p><ul><li><em>not stable</em>: does not preserve the ordering of elements that compare equal (e.g. &quot;a&quot; and &quot;A&quot; in a sort of letters that ignores case).</li><li><em>in-place</em> in memory.</li><li><em>divide-and-conquer</em>: sort strategy similar to <a href="sort.html#Base.Sort.MergeSort"><code>MergeSort</code></a>.</li></ul><p>Note that <code>PartialQuickSort(k)</code> does not necessarily sort the whole array. For example,</p><pre><code class="language-julia-repl hljs">julia&gt; x = rand(100);

julia&gt; k = 50:100;

julia&gt; s1 = sort(x; alg=QuickSort);

julia&gt; s2 = sort(x; alg=PartialQuickSort(k));

julia&gt; map(issorted, (s1, s2))
(true, false)

julia&gt; map(x-&gt;issorted(x[k]), (s1, s2))
(true, true)

julia&gt; s1[k] == s2[k]
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L2223-L2257">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Sort.sortperm!" href="#Base.Sort.sortperm!"><code>Base.Sort.sortperm!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sortperm!(ix, A; alg::Algorithm=DEFAULT_UNSTABLE, lt=isless, by=identity, rev::Bool=false, order::Ordering=Forward, [dims::Integer])</code></pre><p>Like <a href="sort.html#Base.sortperm"><code>sortperm</code></a>, but accepts a preallocated index vector or array <code>ix</code> with the same <code>axes</code> as <code>A</code>. <code>ix</code> is initialized to contain the values <code>LinearIndices(A)</code>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p>The method accepting <code>dims</code> requires at least Julia 1.9.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; v = [3, 1, 2]; p = zeros(Int, 3);

julia&gt; sortperm!(p, v); p
3-element Vector{Int64}:
 2
 3
 1

julia&gt; v[p]
3-element Vector{Int64}:
 1
 2
 3

julia&gt; A = [8 7; 5 6]; p = zeros(Int,2, 2);

julia&gt; sortperm!(p, A; dims=1); p
2×2 Matrix{Int64}:
 2  4
 1  3

julia&gt; sortperm!(p, A; dims=2); p
2×2 Matrix{Int64}:
 3  1
 2  4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L1899-L1938">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.sortslices" href="#Base.sortslices"><code>Base.sortslices</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sortslices(A; dims, alg::Algorithm=DEFAULT_UNSTABLE, lt=isless, by=identity, rev::Bool=false, order::Ordering=Forward)</code></pre><p>Sort slices of an array <code>A</code>. The required keyword argument <code>dims</code> must be either an integer or a tuple of integers. It specifies the dimension(s) over which the slices are sorted.</p><p>E.g., if <code>A</code> is a matrix, <code>dims=1</code> will sort rows, <code>dims=2</code> will sort columns. Note that the default comparison function on one dimensional slices sorts lexicographically.</p><p>For the remaining keyword arguments, see the documentation of <a href="sort.html#Base.sort!"><code>sort!</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; sortslices([7 3 5; -1 6 4; 9 -2 8], dims=1) # Sort rows
3×3 Matrix{Int64}:
 -1   6  4
  7   3  5
  9  -2  8

julia&gt; sortslices([7 3 5; -1 6 4; 9 -2 8], dims=1, lt=(x,y)-&gt;isless(x[2],y[2]))
3×3 Matrix{Int64}:
  9  -2  8
  7   3  5
 -1   6  4

julia&gt; sortslices([7 3 5; -1 6 4; 9 -2 8], dims=1, rev=true)
3×3 Matrix{Int64}:
  9  -2  8
  7   3  5
 -1   6  4

julia&gt; sortslices([7 3 5; 6 -1 -4; 9 -2 8], dims=2) # Sort columns
3×3 Matrix{Int64}:
  3   5  7
 -1  -4  6
 -2   8  9

julia&gt; sortslices([7 3 5; 6 -1 -4; 9 -2 8], dims=2, alg=InsertionSort, lt=(x,y)-&gt;isless(x[2],y[2]))
3×3 Matrix{Int64}:
  5   3  7
 -4  -1  6
  8  -2  9

julia&gt; sortslices([7 3 5; 6 -1 -4; 9 -2 8], dims=2, rev=true)
3×3 Matrix{Int64}:
 7   5   3
 6  -4  -1
 9   8  -2</code></pre><p><strong>Higher dimensions</strong></p><p><code>sortslices</code> extends naturally to higher dimensions. E.g., if <code>A</code> is a a 2x2x2 array, <code>sortslices(A, dims=3)</code> will sort slices within the 3rd dimension, passing the 2x2 slices <code>A[:, :, 1]</code> and <code>A[:, :, 2]</code> to the comparison function. Note that while there is no default order on higher-dimensional slices, you may use the <code>by</code> or <code>lt</code> keyword argument to specify such an order.</p><p>If <code>dims</code> is a tuple, the order of the dimensions in <code>dims</code> is relevant and specifies the linear order of the slices. E.g., if <code>A</code> is three dimensional and <code>dims</code> is <code>(1, 2)</code>, the orderings of the first two dimensions are re-arranged such that the slices (of the remaining third dimension) are sorted. If <code>dims</code> is <code>(2, 1)</code> instead, the same slices will be taken, but the result order will be row-major instead.</p><p><strong>Higher dimensional examples</strong></p><pre><code class="nohighlight hljs">julia&gt; A = [4 3; 2 1 ;;; &#39;A&#39; &#39;B&#39;; &#39;C&#39; &#39;D&#39;]
2×2×2 Array{Any, 3}:
[:, :, 1] =
 4  3
 2  1

[:, :, 2] =
 &#39;A&#39;  &#39;B&#39;
 &#39;C&#39;  &#39;D&#39;

julia&gt; sortslices(A, dims=(1,2))
2×2×2 Array{Any, 3}:
[:, :, 1] =
 1  3
 2  4

[:, :, 2] =
 &#39;D&#39;  &#39;B&#39;
 &#39;C&#39;  &#39;A&#39;

julia&gt; sortslices(A, dims=(2,1))
2×2×2 Array{Any, 3}:
[:, :, 1] =
 1  2
 3  4

[:, :, 2] =
 &#39;D&#39;  &#39;C&#39;
 &#39;B&#39;  &#39;A&#39;

julia&gt; sortslices(reshape([5; 4; 3; 2; 1], (1,1,5)), dims=3, by=x-&gt;x[1,1])
1×1×5 Array{Int64, 3}:
[:, :, 1] =
 1

[:, :, 2] =
 2

[:, :, 3] =
 3

[:, :, 4] =
 4

[:, :, 5] =
 5</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/multidimensional.jl#L1812-L1928">source</a></section></article><h2 id="Order-Related-Functions"><a class="docs-heading-anchor" href="#Order-Related-Functions">Order-Related Functions</a><a id="Order-Related-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Order-Related-Functions" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.issorted" href="#Base.issorted"><code>Base.issorted</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">issorted(v, lt=isless, by=identity, rev::Bool=false, order::Ordering=Forward)</code></pre><p>Test whether a collection is in sorted order. The keywords modify what order is considered sorted, as described in the <a href="sort.html#Base.sort!"><code>sort!</code></a> documentation.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; issorted([1, 2, 3])
true

julia&gt; issorted([(1, &quot;b&quot;), (2, &quot;a&quot;)], by = x -&gt; x[1])
true

julia&gt; issorted([(1, &quot;b&quot;), (2, &quot;a&quot;)], by = x -&gt; x[2])
false

julia&gt; issorted([(1, &quot;b&quot;), (2, &quot;a&quot;)], by = x -&gt; x[2], rev=true)
true

julia&gt; issorted([1, 2, -2, 3], by=abs)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L64-L87">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Sort.searchsorted" href="#Base.Sort.searchsorted"><code>Base.Sort.searchsorted</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">searchsorted(v, x; by=identity, lt=isless, rev=false)</code></pre><p>Return the range of indices in <code>v</code> where values are equivalent to <code>x</code>, or an empty range located at the insertion point if <code>v</code> does not contain values equivalent to <code>x</code>. The vector <code>v</code> must be sorted according to the order defined by the keywords. Refer to <a href="sort.html#Base.sort!"><code>sort!</code></a> for the meaning of the keywords and the definition of equivalence. Note that the <code>by</code> function is applied to the searched value <code>x</code> as well as the values in <code>v</code>.</p><p>The range is generally found using binary search, but there are optimized implementations for some inputs.</p><p>See also: <a href="sort.html#Base.Sort.searchsortedfirst"><code>searchsortedfirst</code></a>, <a href="sort.html#Base.sort!"><code>sort!</code></a>, <a href="sort.html#Base.Sort.insorted"><code>insorted</code></a>, <a href="arrays.html#Base.findall-Tuple{Any}"><code>findall</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; searchsorted([1, 2, 4, 5, 5, 7], 4) # single match
3:3

julia&gt; searchsorted([1, 2, 4, 5, 5, 7], 5) # multiple matches
4:5

julia&gt; searchsorted([1, 2, 4, 5, 5, 7], 3) # no match, insert in the middle
3:2

julia&gt; searchsorted([1, 2, 4, 5, 5, 7], 9) # no match, insert at end
7:6

julia&gt; searchsorted([1, 2, 4, 5, 5, 7], 0) # no match, insert at start
1:0

julia&gt; searchsorted([1=&gt;&quot;one&quot;, 2=&gt;&quot;two&quot;, 2=&gt;&quot;two&quot;, 4=&gt;&quot;four&quot;], 2=&gt;&quot;two&quot;, by=first) # compare the keys of the pairs
2:3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L308-L343">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Sort.searchsortedfirst" href="#Base.Sort.searchsortedfirst"><code>Base.Sort.searchsortedfirst</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">searchsortedfirst(v, x; by=identity, lt=isless, rev=false)</code></pre><p>Return the index of the first value in <code>v</code> that is not ordered before <code>x</code>. If all values in <code>v</code> are ordered before <code>x</code>, return <code>lastindex(v) + 1</code>.</p><p>The vector <code>v</code> must be sorted according to the order defined by the keywords. <code>insert!</code>ing <code>x</code> at the returned index will maintain the sorted order. Refer to <a href="sort.html#Base.sort!"><code>sort!</code></a> for the meaning and use of the keywords. Note that the <code>by</code> function is applied to the searched value <code>x</code> as well as the values in <code>v</code>.</p><p>The index is generally found using binary search, but there are optimized implementations for some inputs.</p><p>See also: <a href="sort.html#Base.Sort.searchsortedlast"><code>searchsortedlast</code></a>, <a href="sort.html#Base.Sort.searchsorted"><code>searchsorted</code></a>, <a href="arrays.html#Base.findfirst-Tuple{Any}"><code>findfirst</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; searchsortedfirst([1, 2, 4, 5, 5, 7], 4) # single match
3

julia&gt; searchsortedfirst([1, 2, 4, 5, 5, 7], 5) # multiple matches
4

julia&gt; searchsortedfirst([1, 2, 4, 5, 5, 7], 3) # no match, insert in the middle
3

julia&gt; searchsortedfirst([1, 2, 4, 5, 5, 7], 9) # no match, insert at end
7

julia&gt; searchsortedfirst([1, 2, 4, 5, 5, 7], 0) # no match, insert at start
1

julia&gt; searchsortedfirst([1=&gt;&quot;one&quot;, 2=&gt;&quot;two&quot;, 4=&gt;&quot;four&quot;], 3=&gt;&quot;three&quot;, by=first) # compare the keys of the pairs
3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L345-L382">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Sort.searchsortedlast" href="#Base.Sort.searchsortedlast"><code>Base.Sort.searchsortedlast</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">searchsortedlast(v, x; by=identity, lt=isless, rev=false)</code></pre><p>Return the index of the last value in <code>v</code> that is not ordered after <code>x</code>. If all values in <code>v</code> are ordered after <code>x</code>, return <code>firstindex(v) - 1</code>.</p><p>The vector <code>v</code> must be sorted according to the order defined by the keywords. <code>insert!</code>ing <code>x</code> immediately after the returned index will maintain the sorted order. Refer to <a href="sort.html#Base.sort!"><code>sort!</code></a> for the meaning and use of the keywords. Note that the <code>by</code> function is applied to the searched value <code>x</code> as well as the values in <code>v</code>.</p><p>The index is generally found using binary search, but there are optimized implementations for some inputs</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; searchsortedlast([1, 2, 4, 5, 5, 7], 4) # single match
3

julia&gt; searchsortedlast([1, 2, 4, 5, 5, 7], 5) # multiple matches
5

julia&gt; searchsortedlast([1, 2, 4, 5, 5, 7], 3) # no match, insert in the middle
2

julia&gt; searchsortedlast([1, 2, 4, 5, 5, 7], 9) # no match, insert at end
6

julia&gt; searchsortedlast([1, 2, 4, 5, 5, 7], 0) # no match, insert at start
0

julia&gt; searchsortedlast([1=&gt;&quot;one&quot;, 2=&gt;&quot;two&quot;, 4=&gt;&quot;four&quot;], 3=&gt;&quot;three&quot;, by=first) # compare the keys of the pairs
2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L384-L419">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Sort.insorted" href="#Base.Sort.insorted"><code>Base.Sort.insorted</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">insorted(x, v; by=identity, lt=isless, rev=false) -&gt; Bool</code></pre><p>Determine whether a vector <code>v</code> contains any value equivalent to <code>x</code>. The vector <code>v</code> must be sorted according to the order defined by the keywords. Refer to <a href="sort.html#Base.sort!"><code>sort!</code></a> for the meaning of the keywords and the definition of equivalence. Note that the <code>by</code> function is applied to the searched value <code>x</code> as well as the values in <code>v</code>.</p><p>The check is generally done using binary search, but there are optimized implementations for some inputs.</p><p>See also <a href="collections.html#Base.in"><code>in</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; insorted(4, [1, 2, 4, 5, 5, 7]) # single match
true

julia&gt; insorted(5, [1, 2, 4, 5, 5, 7]) # multiple matches
true

julia&gt; insorted(3, [1, 2, 4, 5, 5, 7]) # no match
false

julia&gt; insorted(9, [1, 2, 4, 5, 5, 7]) # no match
false

julia&gt; insorted(0, [1, 2, 4, 5, 5, 7]) # no match
false

julia&gt; insorted(2=&gt;&quot;TWO&quot;, [1=&gt;&quot;one&quot;, 2=&gt;&quot;two&quot;, 4=&gt;&quot;four&quot;], by=first) # compare the keys of the pairs
true</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p><code>insorted</code> was added in Julia 1.6.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L421-L458">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Sort.partialsort!" href="#Base.Sort.partialsort!"><code>Base.Sort.partialsort!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">partialsort!(v, k; by=identity, lt=isless, rev=false)</code></pre><p>Partially sort the vector <code>v</code> in place so that the value at index <code>k</code> (or range of adjacent values if <code>k</code> is a range) occurs at the position where it would appear if the array were fully sorted. If <code>k</code> is a single index, that value is returned; if <code>k</code> is a range, an array of values at those indices is returned. Note that <code>partialsort!</code> may not fully sort the input array.</p><p>For the keyword arguments, see the documentation of <a href="sort.html#Base.sort!"><code>sort!</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [1, 2, 4, 3, 4]
5-element Vector{Int64}:
 1
 2
 4
 3
 4

julia&gt; partialsort!(a, 4)
4

julia&gt; a
5-element Vector{Int64}:
 1
 2
 3
 4
 4

julia&gt; a = [1, 2, 4, 3, 4]
5-element Vector{Int64}:
 1
 2
 4
 3
 4

julia&gt; partialsort!(a, 4, rev=true)
2

julia&gt; a
5-element Vector{Int64}:
 4
 4
 3
 2
 1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L108-L160">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Sort.partialsort" href="#Base.Sort.partialsort"><code>Base.Sort.partialsort</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">partialsort(v, k, by=identity, lt=isless, rev=false)</code></pre><p>Variant of <a href="sort.html#Base.Sort.partialsort!"><code>partialsort!</code></a> that copies <code>v</code> before partially sorting it, thereby returning the same thing as <code>partialsort!</code> but leaving <code>v</code> unmodified.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L165-L170">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Sort.partialsortperm" href="#Base.Sort.partialsortperm"><code>Base.Sort.partialsortperm</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">partialsortperm(v, k; by=identity, lt=isless, rev=false)</code></pre><p>Return a partial permutation <code>I</code> of the vector <code>v</code>, so that <code>v[I]</code> returns values of a fully sorted version of <code>v</code> at index <code>k</code>. If <code>k</code> is a range, a vector of indices is returned; if <code>k</code> is an integer, a single index is returned. The order is specified using the same keywords as <code>sort!</code>. The permutation is stable: the indices of equal elements will appear in ascending order.</p><p>This function is equivalent to, but more efficient than, calling <code>sortperm(...)[k]</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; v = [3, 1, 2, 1];

julia&gt; v[partialsortperm(v, 1)]
1

julia&gt; p = partialsortperm(v, 1:3)
3-element view(::Vector{Int64}, 1:3) with eltype Int64:
 2
 4
 3

julia&gt; v[p]
3-element Vector{Int64}:
 1
 1
 2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L1724-L1754">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Sort.partialsortperm!" href="#Base.Sort.partialsortperm!"><code>Base.Sort.partialsortperm!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">partialsortperm!(ix, v, k; by=identity, lt=isless, rev=false)</code></pre><p>Like <a href="sort.html#Base.Sort.partialsortperm"><code>partialsortperm</code></a>, but accepts a preallocated index vector <code>ix</code> the same size as <code>v</code>, which is used to store (a permutation of) the indices of <code>v</code>.</p><p><code>ix</code> is initialized to contain the indices of <code>v</code>.</p><p>(Typically, the indices of <code>v</code> will be <code>1:length(v)</code>, although if <code>v</code> has an alternative array type with non-one-based indices, such as an <code>OffsetArray</code>, <code>ix</code> must share those same indices)</p><p>Upon return, <code>ix</code> is guaranteed to have the indices <code>k</code> in their sorted positions, such that</p><pre><code class="language-julia hljs">partialsortperm!(ix, v, k);
v[ix[k]] == partialsort(v, k)</code></pre><p>The return value is the <code>k</code>th element of <code>ix</code> if <code>k</code> is an integer, or view into <code>ix</code> if <code>k</code> is a range.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; v = [3, 1, 2, 1];

julia&gt; ix = Vector{Int}(undef, 4);

julia&gt; partialsortperm!(ix, v, 1)
2

julia&gt; ix = [1:4;];

julia&gt; partialsortperm!(ix, v, 2:3)
2-element view(::Vector{Int64}, 2:3) with eltype Int64:
 4
 3</code></pre><pre><code class="nohighlight hljs"></code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/sort.jl#L1758-L1797">source</a></section></article><h2 id="Sorting-Algorithms"><a class="docs-heading-anchor" href="#Sorting-Algorithms">Sorting Algorithms</a><a id="Sorting-Algorithms-1"></a><a class="docs-heading-anchor-permalink" href="#Sorting-Algorithms" title="Permalink"></a></h2><p>There are currently four sorting algorithms publicly available in base Julia:</p><ul><li><a href="sort.html#Base.Sort.InsertionSort"><code>InsertionSort</code></a></li><li><a href="sort.html#Base.Sort.QuickSort"><code>QuickSort</code></a></li><li><a href="sort.html#Base.Sort.PartialQuickSort"><code>PartialQuickSort(k)</code></a></li><li><a href="sort.html#Base.Sort.MergeSort"><code>MergeSort</code></a></li></ul><p>By default, the <code>sort</code> family of functions uses stable sorting algorithms that are fast on most inputs. The exact algorithm choice is an implementation detail to allow for future performance improvements. Currently, a hybrid of <code>RadixSort</code>, <code>ScratchQuickSort</code>, <code>InsertionSort</code>, and <code>CountingSort</code> is used based on input type, size, and composition. Implementation details are subject to change but currently available in the extended help of <code>??Base.DEFAULT_STABLE</code> and the docstrings of internal sorting algorithms listed there.</p><p>You can explicitly specify your preferred algorithm with the <code>alg</code> keyword (e.g. <code>sort!(v, alg=PartialQuickSort(10:20))</code>) or reconfigure the default sorting algorithm for custom types by adding a specialized method to the <code>Base.Sort.defalg</code> function. For example, <a href="https://github.com/JuliaStrings/InlineStrings.jl/blob/v1.3.2/src/InlineStrings.jl#L903">InlineStrings.jl</a> defines the following method:</p><pre><code class="language-julia hljs">Base.Sort.defalg(::AbstractArray{&lt;:Union{SmallInlineStrings, Missing}}) = InlineStringSort</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p>The default sorting algorithm (returned by <code>Base.Sort.defalg</code>) is guaranteed to be stable since Julia 1.9. Previous versions had unstable edge cases when sorting numeric arrays.</p></div></div><h2 id="Alternate-Orderings"><a class="docs-heading-anchor" href="#Alternate-Orderings">Alternate Orderings</a><a id="Alternate-Orderings-1"></a><a class="docs-heading-anchor-permalink" href="#Alternate-Orderings" title="Permalink"></a></h2><p>By default, <code>sort</code>, <code>searchsorted</code>, and related functions use <a href="base.html#Base.isless"><code>isless</code></a> to compare two elements in order to determine which should come first. The <a href="sort.html#Base.Order.Ordering"><code>Base.Order.Ordering</code></a> abstract type provides a mechanism for defining alternate orderings on the same set of elements: when calling a sorting function like <code>sort!</code>, an instance of <code>Ordering</code> can be provided with the keyword argument <code>order</code>.</p><p>Instances of <code>Ordering</code> define an order through the <a href="sort.html#Base.Order.lt"><code>Base.Order.lt</code></a> function, which works as a generalization of <code>isless</code>. This function&#39;s behavior on custom <code>Ordering</code>s must satisfy all the conditions of a <a href="https://en.wikipedia.org/wiki/Weak_ordering#Strict_weak_orderings">strict weak order</a>. See <a href="sort.html#Base.sort!"><code>sort!</code></a> for details and examples of valid and invalid <code>lt</code> functions.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Order.Ordering" href="#Base.Order.Ordering"><code>Base.Order.Ordering</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.Order.Ordering</code></pre><p>Abstract type which represents a strict weak order on some set of elements. See <a href="sort.html#Base.sort!"><code>sort!</code></a> for more.</p><p>Use <a href="sort.html#Base.Order.lt"><code>Base.Order.lt</code></a> to compare two elements according to the ordering.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ordering.jl#L21-L28">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Order.lt" href="#Base.Order.lt"><code>Base.Order.lt</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lt(o::Ordering, a, b) -&gt; Bool</code></pre><p>Test whether <code>a</code> is less than <code>b</code> according to the ordering <code>o</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ordering.jl#L113-L117">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Order.ord" href="#Base.Order.ord"><code>Base.Order.ord</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ord(lt, by, rev::Union{Bool, Nothing}, order::Ordering=Forward)</code></pre><p>Construct an <a href="sort.html#Base.Order.Ordering"><code>Ordering</code></a> object from the same arguments used by <a href="sort.html#Base.sort!"><code>sort!</code></a>. Elements are first transformed by the function <code>by</code> (which may be <a href="base.html#Base.identity"><code>identity</code></a>) and are then compared according to either the function <code>lt</code> or an existing ordering <code>order</code>. <code>lt</code> should be <a href="base.html#Base.isless"><code>isless</code></a> or a function that obeys the same rules as the <code>lt</code> parameter of <a href="sort.html#Base.sort!"><code>sort!</code></a>. Finally, the resulting order is reversed if <code>rev=true</code>.</p><p>Passing an <code>lt</code> other than <code>isless</code> along with an <code>order</code> other than <a href="sort.html#Base.Order.Forward"><code>Base.Order.Forward</code></a> or <a href="sort.html#Base.Order.Reverse"><code>Base.Order.Reverse</code></a> is not permitted, otherwise all options are independent and can be used together in all possible combinations.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ordering.jl#L139-L154">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Order.Forward" href="#Base.Order.Forward"><code>Base.Order.Forward</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.Order.Forward</code></pre><p>Default ordering according to <a href="base.html#Base.isless"><code>isless</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ordering.jl#L60-L64">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Order.ReverseOrdering" href="#Base.Order.ReverseOrdering"><code>Base.Order.ReverseOrdering</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ReverseOrdering(fwd::Ordering=Forward)</code></pre><p>A wrapper which reverses an ordering.</p><p>For a given <code>Ordering</code> <code>o</code>, the following holds for all  <code>a</code>, <code>b</code>:</p><pre><code class="nohighlight hljs">lt(ReverseOrdering(o), a, b) == lt(o, b, a)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ordering.jl#L33-L41">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Order.Reverse" href="#Base.Order.Reverse"><code>Base.Order.Reverse</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.Order.Reverse</code></pre><p>Reverse ordering according to <a href="base.html#Base.isless"><code>isless</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ordering.jl#L67-L71">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Order.By" href="#Base.Order.By"><code>Base.Order.By</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">By(by, order::Ordering=Forward)</code></pre><p><code>Ordering</code> which applies <code>order</code> to elements after they have been transformed by the function <code>by</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ordering.jl#L74-L79">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Order.Lt" href="#Base.Order.Lt"><code>Base.Order.Lt</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Lt(lt)</code></pre><p><code>Ordering</code> that calls <code>lt(a, b)</code> to compare elements. <code>lt</code> must obey the same rules as the <code>lt</code> parameter of <a href="sort.html#Base.sort!"><code>sort!</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ordering.jl#L88-L93">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Order.Perm" href="#Base.Order.Perm"><code>Base.Order.Perm</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Perm(order::Ordering, data::AbstractVector)</code></pre><p><code>Ordering</code> on the indices of <code>data</code> where <code>i</code> is less than <code>j</code> if <code>data[i]</code> is less than <code>data[j]</code> according to <code>order</code>. In the case that <code>data[i]</code> and <code>data[j]</code> are equal, <code>i</code> and <code>j</code> are compared by numeric value.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/ordering.jl#L98-L104">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="punctuation.html">« Punctuation</a><a class="docs-footer-nextpage" href="iterators.html">Iteration utilities »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
