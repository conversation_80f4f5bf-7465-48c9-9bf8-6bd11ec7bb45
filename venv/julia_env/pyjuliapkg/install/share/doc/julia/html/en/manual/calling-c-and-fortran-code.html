<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Calling C and Fortran Code · The Julia Language</title><meta name="title" content="Calling C and Fortran Code · The Julia Language"/><meta property="og:title" content="Calling C and Fortran Code · The Julia Language"/><meta property="twitter:title" content="Calling C and Fortran Code · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li class="is-active"><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a><ul class="internal"><li><a class="tocitem" href="#Creating-C-Compatible-Julia-Function-Pointers"><span>Creating C-Compatible Julia Function Pointers</span></a></li><li><a class="tocitem" href="#mapping-c-types-to-julia"><span>Mapping C Types to Julia</span></a></li><li><a class="tocitem" href="#Mapping-C-Functions-to-Julia"><span>Mapping C Functions to Julia</span></a></li><li><a class="tocitem" href="#C-Wrapper-Examples"><span>C Wrapper Examples</span></a></li><li><a class="tocitem" href="#Fortran-Wrapper-Example"><span>Fortran Wrapper Example</span></a></li><li><a class="tocitem" href="#Garbage-Collection-Safety"><span>Garbage Collection Safety</span></a></li><li><a class="tocitem" href="#Non-constant-Function-Specifications"><span>Non-constant Function Specifications</span></a></li><li><a class="tocitem" href="#Indirect-Calls"><span>Indirect Calls</span></a></li><li><a class="tocitem" href="#Closure-cfunctions"><span>Closure cfunctions</span></a></li><li><a class="tocitem" href="#Closing-a-Library"><span>Closing a Library</span></a></li><li><a class="tocitem" href="#Variadic-function-calls"><span>Variadic function calls</span></a></li><li><a class="tocitem" href="#ccall-interface"><span><code>ccall</code> interface</span></a></li><li><a class="tocitem" href="#calling-convention"><span>Calling Convention</span></a></li><li><a class="tocitem" href="#Accessing-Global-Variables"><span>Accessing Global Variables</span></a></li><li><a class="tocitem" href="#Accessing-Data-through-a-Pointer"><span>Accessing Data through a Pointer</span></a></li><li><a class="tocitem" href="#Thread-safety"><span>Thread-safety</span></a></li><li><a class="tocitem" href="#More-About-Callbacks"><span>More About Callbacks</span></a></li><li><a class="tocitem" href="#C"><span>C++</span></a></li></ul></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/calling-c-and-fortran-code.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Calling-C-and-Fortran-Code"><a class="docs-heading-anchor" href="#Calling-C-and-Fortran-Code">Calling C and Fortran Code</a><a id="Calling-C-and-Fortran-Code-1"></a><a class="docs-heading-anchor-permalink" href="#Calling-C-and-Fortran-Code" title="Permalink"></a></h1><p>Though most code can be written in Julia, there are many high-quality, mature libraries for numerical computing already written in C and Fortran. To allow easy use of this existing code, Julia makes it simple and efficient to call C and Fortran functions. Julia has a &quot;no boilerplate&quot; philosophy: functions can be called directly from Julia without any &quot;glue&quot; code, code generation, or compilation – even from the interactive prompt. This is accomplished just by making an appropriate call with the <a href="../base/c.html#Base.@ccall"><code>@ccall</code></a> macro (or the less convenient <a href="../base/c.html#ccall"><code>ccall</code></a> syntax, see the <a href="calling-c-and-fortran-code.html#ccall-interface"><code>ccall</code> syntax section</a>).</p><p>The code to be called must be available as a shared library. Most C and Fortran libraries ship compiled as shared libraries already, but if you are compiling the code yourself using GCC (or Clang), you will need to use the <code>-shared</code> and <code>-fPIC</code> options. The machine instructions generated by Julia&#39;s JIT are the same as a native C call would be, so the resulting overhead is the same as calling a library function from C code. <sup class="footnote-reference"><a id="citeref-1" href="#footnote-1">[1]</a></sup></p><p>By default, Fortran compilers <a href="https://en.wikipedia.org/wiki/Name_mangling#Fortran">generate mangled names</a> (for example, converting function names to lowercase or uppercase, often appending an underscore), and so to call a Fortran function you must pass the mangled identifier corresponding to the rule followed by your Fortran compiler. Also, when calling a Fortran function, all inputs must be passed as pointers to allocated values on the heap or stack. This applies not only to arrays and other mutable objects which are normally heap-allocated, but also to scalar values such as integers and floats which are normally stack-allocated and commonly passed in registers when using C or Julia calling conventions.</p><p>The syntax for <a href="../base/c.html#Base.@ccall"><code>@ccall</code></a> to generate a call to the library function is:</p><pre><code class="language-julia hljs">  @ccall library.function_name(argvalue1::argtype1, ...)::returntype
  @ccall function_name(argvalue1::argtype1, ...)::returntype
  @ccall $function_pointer(argvalue1::argtype1, ...)::returntype</code></pre><p>where <code>library</code> is a string constant or literal (but see <a href="calling-c-and-fortran-code.html#Non-constant-Function-Specifications">Non-constant Function Specifications</a> below). The library may be omitted, in which case the function name is resolved in the current process. This form can be used to call C library functions, functions in the Julia runtime, or functions in an application linked to Julia. The full path to the library may also be specified. Alternatively, <code>@ccall</code> may also be used to call a function pointer <code>$function_pointer</code>, such as one returned by <code>Libdl.dlsym</code>. The <code>argtype</code>s corresponds to the C-function signature and the <code>argvalue</code>s are the actual argument values to be passed to the function.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>See below for how to <a href="calling-c-and-fortran-code.html#mapping-c-types-to-julia">map C types to Julia types</a>.</p></div></div><p>As a complete but simple example, the following calls the <code>clock</code> function from the standard C library on most Unix-derived systems:</p><pre><code class="language-julia-repl hljs">julia&gt; t = @ccall clock()::Int32
2292761

julia&gt; typeof(t)
Int32</code></pre><p><code>clock</code> takes no arguments and returns an <code>Int32</code>. To call the <code>getenv</code> function to get a pointer to the value of an environment variable, one makes a call like this:</p><pre><code class="language-julia-repl hljs">julia&gt; path = @ccall getenv(&quot;SHELL&quot;::Cstring)::Cstring
Cstring(@0x00007fff5fbffc45)

julia&gt; unsafe_string(path)
&quot;/bin/bash&quot;</code></pre><p>In practice, especially when providing reusable functionality, one generally wraps <code>@ccall</code> uses in Julia functions that set up arguments and then check for errors in whatever manner the C or Fortran function specifies. And if an error occurs it is thrown as a normal Julia exception. This is especially important since C and Fortran APIs are notoriously inconsistent about how they indicate error conditions. For example, the <code>getenv</code> C library function is wrapped in the following Julia function, which is a simplified version of the actual definition from <a href="https://github.com/JuliaLang/julia/blob/master/base/env.jl"><code>env.jl</code></a>:</p><pre><code class="language-julia hljs">function getenv(var::AbstractString)
    val = @ccall getenv(var::Cstring)::Cstring
    if val == C_NULL
        error(&quot;getenv: undefined variable: &quot;, var)
    end
    return unsafe_string(val)
end</code></pre><p>The C <code>getenv</code> function indicates an error by returning <code>C_NULL</code>, but other standard C functions indicate errors in different ways, including by returning -1, 0, 1, and other special values. This wrapper throws an exception indicating the problem if the caller tries to get a non-existent environment variable:</p><pre><code class="language-julia-repl hljs">julia&gt; getenv(&quot;SHELL&quot;)
&quot;/bin/bash&quot;

julia&gt; getenv(&quot;FOOBAR&quot;)
ERROR: getenv: undefined variable: FOOBAR</code></pre><p>Here is a slightly more complex example that discovers the local machine&#39;s hostname.</p><pre><code class="language-julia hljs">function gethostname()
    hostname = Vector{UInt8}(undef, 256) # MAXHOSTNAMELEN
    err = @ccall gethostname(hostname::Ptr{UInt8}, sizeof(hostname)::Csize_t)::Int32
    Base.systemerror(&quot;gethostname&quot;, err != 0)
    hostname[end] = 0 # ensure null-termination
    return GC.@preserve hostname unsafe_string(pointer(hostname))
end</code></pre><p>This example first allocates an array of bytes. It then calls the C library function <code>gethostname</code> to populate the array with the hostname. Finally, it takes a pointer to the hostname buffer, and converts the pointer to a Julia string, assuming that it is a null-terminated C string.</p><p>It is common for C libraries to use this pattern of requiring the caller to allocate memory to be passed to the callee and populated. Allocation of memory from Julia like this is generally accomplished by creating an uninitialized array and passing a pointer to its data to the C function. This is why we don&#39;t use the <code>Cstring</code> type here: as the array is uninitialized, it could contain null bytes. Converting to a <code>Cstring</code> as part of the <code>@ccall</code> checks for contained null bytes and could therefore throw a conversion error.</p><p>Dereferencing <code>pointer(hostname)</code> with <code>unsafe_string</code> is an unsafe operation as it requires access to the memory allocated for <code>hostname</code> that may have been in the meanwhile garbage collected. The macro <a href="../base/base.html#Base.GC.@preserve"><code>GC.@preserve</code></a> prevents this from happening and therefore accessing an invalid memory location.</p><p>Finally, here is an example of specifying a library via a path. We create a shared library with the following content</p><pre><code class="language-c hljs">#include &lt;stdio.h&gt;

void say_y(int y)
{
    printf(&quot;Hello from C: got y = %d.\n&quot;, y);
}</code></pre><p>and compile it with <code>gcc -fPIC -shared -o mylib.so mylib.c</code>. It can then be called by specifying the (absolute) path as the library name:</p><pre><code class="language-julia-repl hljs">julia&gt; @ccall &quot;./mylib.so&quot;.say_y(5::Cint)::Cvoid
Hello from C: got y = 5.</code></pre><h2 id="Creating-C-Compatible-Julia-Function-Pointers"><a class="docs-heading-anchor" href="#Creating-C-Compatible-Julia-Function-Pointers">Creating C-Compatible Julia Function Pointers</a><a id="Creating-C-Compatible-Julia-Function-Pointers-1"></a><a class="docs-heading-anchor-permalink" href="#Creating-C-Compatible-Julia-Function-Pointers" title="Permalink"></a></h2><p>It is possible to pass Julia functions to native C functions that accept function pointer arguments. For example, to match C prototypes of the form:</p><pre><code class="language-c hljs">typedef returntype (*functiontype)(argumenttype, ...)</code></pre><p>The macro <a href="../base/c.html#Base.@cfunction"><code>@cfunction</code></a> generates the C-compatible function pointer for a call to a Julia function. The arguments to <a href="../base/c.html#Base.@cfunction"><code>@cfunction</code></a> are:</p><ol><li>A Julia function</li><li>The function&#39;s return type</li><li>A tuple of input types, corresponding to the function signature</li></ol><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>As with <code>@ccall</code>, the return type and the input types must be literal constants.</p></div></div><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Currently, only the platform-default C calling convention is supported. This means that <code>@cfunction</code>-generated pointers cannot be used in calls where WINAPI expects a <code>stdcall</code> function on 32-bit Windows, but can be used on WIN64 (where <code>stdcall</code> is unified with the C calling convention).</p></div></div><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Callback functions exposed via <code>@cfunction</code> should not throw errors, as that will return control to the Julia runtime unexpectedly and may leave the program in an undefined state.</p></div></div><p>A classic example is the standard C library <code>qsort</code> function, declared as:</p><pre><code class="language-c hljs">void qsort(void *base, size_t nitems, size_t size,
           int (*compare)(const void*, const void*));</code></pre><p>The <code>base</code> argument is a pointer to an array of length <code>nitems</code>, with elements of <code>size</code> bytes each. <code>compare</code> is a callback function which takes pointers to two elements <code>a</code> and <code>b</code> and returns an integer less/greater than zero if <code>a</code> should appear before/after <code>b</code> (or zero if any order is permitted).</p><p>Now, suppose that we have a 1-d array <code>A</code> of values in Julia that we want to sort using the <code>qsort</code> function (rather than Julia&#39;s built-in <code>sort</code> function). Before we consider calling <code>qsort</code> and passing arguments, we need to write a comparison function:</p><pre><code class="language-julia-repl hljs">julia&gt; function mycompare(a, b)::Cint
           return (a &lt; b) ? -1 : ((a &gt; b) ? +1 : 0)
       end;</code></pre><p><code>qsort</code> expects a comparison function that return a C <code>int</code>, so we annotate the return type to be <code>Cint</code>.</p><p>In order to pass this function to C, we obtain its address using the macro <code>@cfunction</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; mycompare_c = @cfunction(mycompare, Cint, (Ref{Cdouble}, Ref{Cdouble}));</code></pre><p><a href="../base/c.html#Base.@cfunction"><code>@cfunction</code></a> requires three arguments: the Julia function (<code>mycompare</code>), the return type (<code>Cint</code>), and a literal tuple of the input argument types, in this case to sort an array of <code>Cdouble</code> (<a href="../base/numbers.html#Core.Float64"><code>Float64</code></a>) elements.</p><p>The final call to <code>qsort</code> looks like this:</p><pre><code class="language-julia-repl hljs">julia&gt; A = [1.3, -2.7, 4.4, 3.1];

julia&gt; @ccall qsort(A::Ptr{Cdouble}, length(A)::Csize_t, sizeof(eltype(A))::Csize_t, mycompare_c::Ptr{Cvoid})::Cvoid

julia&gt; A
4-element Vector{Float64}:
 -2.7
  1.3
  3.1
  4.4</code></pre><p>As the example shows, the original Julia array <code>A</code> has now been sorted: <code>[-2.7, 1.3, 3.1, 4.4]</code>. Note that Julia <a href="calling-c-and-fortran-code.html#automatic-type-conversion">takes care of converting the array to a <code>Ptr{Cdouble}</code></a>), computing the size of the element type in bytes, and so on.</p><p>For fun, try inserting a <code>println(&quot;mycompare($a, $b)&quot;)</code> line into <code>mycompare</code>, which will allow you to see the comparisons that <code>qsort</code> is performing (and to verify that it is really calling the Julia function that you passed to it).</p><h2 id="mapping-c-types-to-julia"><a class="docs-heading-anchor" href="#mapping-c-types-to-julia">Mapping C Types to Julia</a><a id="mapping-c-types-to-julia-1"></a><a class="docs-heading-anchor-permalink" href="#mapping-c-types-to-julia" title="Permalink"></a></h2><p>It is critical to exactly match the declared C type with its declaration in Julia. Inconsistencies can cause code that works correctly on one system to fail or produce indeterminate results on a different system.</p><p>Note that no C header files are used anywhere in the process of calling C functions: you are responsible for making sure that your Julia types and call signatures accurately reflect those in the C header file.<sup class="footnote-reference"><a id="citeref-2" href="#footnote-2">[2]</a></sup></p><h3 id="automatic-type-conversion"><a class="docs-heading-anchor" href="#automatic-type-conversion">Automatic Type Conversion</a><a id="automatic-type-conversion-1"></a><a class="docs-heading-anchor-permalink" href="#automatic-type-conversion" title="Permalink"></a></h3><p>Julia automatically inserts calls to the <a href="../base/c.html#Base.cconvert"><code>Base.cconvert</code></a> function to convert each argument to the specified type. For example, the following call:</p><pre><code class="language-julia hljs">@ccall &quot;libfoo&quot;.foo(x::Int32, y::Float64)::Cvoid</code></pre><p>will behave as if it were written like this:</p><pre><code class="language-julia hljs">c_x = Base.cconvert(Int32, x)
c_y = Base.cconvert(Float64, y)
GC.@preserve c_x c_y begin
    @ccall &quot;libfoo&quot;.foo(
        Base.unsafe_convert(Int32, c_x)::Int32,
        Base.unsafe_convert(Float64, c_y)::Float64
    )::Cvoid
end</code></pre><p><a href="../base/c.html#Base.cconvert"><code>Base.cconvert</code></a> normally just calls <a href="../base/base.html#Base.convert"><code>convert</code></a>, but can be defined to return an arbitrary new object more appropriate for passing to C. This should be used to perform all allocations of memory that will be accessed by the C code. For example, this is used to convert an <code>Array</code> of objects (e.g. strings) to an array of pointers.</p><p><a href="../base/c.html#Base.unsafe_convert"><code>Base.unsafe_convert</code></a> handles conversion to <a href="../base/c.html#Core.Ptr"><code>Ptr</code></a> types. It is considered unsafe because converting an object to a native pointer can hide the object from the garbage collector, causing it to be freed prematurely.</p><h3 id="Type-Correspondences"><a class="docs-heading-anchor" href="#Type-Correspondences">Type Correspondences</a><a id="Type-Correspondences-1"></a><a class="docs-heading-anchor-permalink" href="#Type-Correspondences" title="Permalink"></a></h3><p>First, let&#39;s review some relevant Julia type terminology:</p><table><tr><th style="text-align: left">Syntax / Keyword</th><th style="text-align: left">Example</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left"><code>mutable struct</code></td><td style="text-align: left"><code>BitSet</code></td><td style="text-align: left">&quot;Leaf Type&quot; :: A group of related data that includes a type-tag, is managed by the Julia GC, and is defined by object-identity. The type parameters of a leaf type must be fully defined (no <code>TypeVars</code> are allowed) in order for the instance to be constructed.</td></tr><tr><td style="text-align: left"><code>abstract type</code></td><td style="text-align: left"><code>Any</code>, <code>AbstractArray{T, N}</code>, <code>Complex{T}</code></td><td style="text-align: left">&quot;Super Type&quot; :: A super-type (not a leaf-type) that cannot be instantiated, but can be used to describe a group of types.</td></tr><tr><td style="text-align: left"><code>T{A}</code></td><td style="text-align: left"><code>Vector{Int}</code></td><td style="text-align: left">&quot;Type Parameter&quot; :: A specialization of a type (typically used for dispatch or storage optimization).</td></tr><tr><td style="text-align: left"></td><td style="text-align: left"></td><td style="text-align: left">&quot;TypeVar&quot; :: The <code>T</code> in the type parameter declaration is referred to as a TypeVar (short for type variable).</td></tr><tr><td style="text-align: left"><code>primitive type</code></td><td style="text-align: left"><code>Int</code>, <code>Float64</code></td><td style="text-align: left">&quot;Primitive Type&quot; :: A type with no fields, but a size. It is stored and defined by-value.</td></tr><tr><td style="text-align: left"><code>struct</code></td><td style="text-align: left"><code>Pair{Int, Int}</code></td><td style="text-align: left">&quot;Struct&quot; :: A type with all fields defined to be constant. It is defined by-value, and may be stored with a type-tag.</td></tr><tr><td style="text-align: left"></td><td style="text-align: left"><code>ComplexF64</code> (<code>isbits</code>)</td><td style="text-align: left">&quot;Is-Bits&quot;   :: A <code>primitive type</code>, or a <code>struct</code> type where all fields are other <code>isbits</code> types. It is defined by-value, and is stored without a type-tag.</td></tr><tr><td style="text-align: left"><code>struct ...; end</code></td><td style="text-align: left"><code>nothing</code></td><td style="text-align: left">&quot;Singleton&quot; :: a Leaf Type or Struct with no fields.</td></tr><tr><td style="text-align: left"><code>(...)</code> or <code>tuple(...)</code></td><td style="text-align: left"><code>(1, 2, 3)</code></td><td style="text-align: left">&quot;Tuple&quot; :: an immutable data-structure similar to an anonymous struct type, or a constant array. Represented as either an array or a struct.</td></tr></table><h3 id="man-bits-types"><a class="docs-heading-anchor" href="#man-bits-types">Bits Types</a><a id="man-bits-types-1"></a><a class="docs-heading-anchor-permalink" href="#man-bits-types" title="Permalink"></a></h3><p>There are several special types to be aware of, as no other type can be defined to behave the same:</p><ul><li><p><code>Float32</code></p><p>Exactly corresponds to the <code>float</code> type in C (or <code>REAL*4</code> in Fortran).</p></li><li><p><code>Float64</code></p><p>Exactly corresponds to the <code>double</code> type in C (or <code>REAL*8</code> in Fortran).</p></li><li><p><code>ComplexF32</code></p><p>Exactly corresponds to the <code>complex float</code> type in C (or <code>COMPLEX*8</code> in Fortran).</p></li><li><p><code>ComplexF64</code></p><p>Exactly corresponds to the <code>complex double</code> type in C (or <code>COMPLEX*16</code> in Fortran).</p></li><li><p><code>Signed</code></p><p>Exactly corresponds to the <code>signed</code> type annotation in C (or any <code>INTEGER</code> type in Fortran). Any Julia type that is not a subtype of <a href="../base/numbers.html#Core.Signed"><code>Signed</code></a> is assumed to be unsigned.</p></li></ul><ul><li><p><code>Ref{T}</code></p><p>Behaves like a <code>Ptr{T}</code> that can manage its memory via the Julia GC.</p></li></ul><ul><li><p><code>Array{T,N}</code></p><p>When an array is passed to C as a <code>Ptr{T}</code> argument, it is not reinterpret-cast: Julia requires that the element type of the array matches <code>T</code>, and the address of the first element is passed.</p><p>Therefore, if an <code>Array</code> contains data in the wrong format, it will have to be explicitly converted using a call such as <code>trunc.(Int32, A)</code>.</p><p>To pass an array <code>A</code> as a pointer of a different type <em>without</em> converting the data beforehand (for example, to pass a <code>Float64</code> array to a function that operates on uninterpreted bytes), you can declare the argument as <code>Ptr{Cvoid}</code>.</p><p>If an array of eltype <code>Ptr{T}</code> is passed as a <code>Ptr{Ptr{T}}</code> argument, <a href="../base/c.html#Base.cconvert"><code>Base.cconvert</code></a> will attempt to first make a null-terminated copy of the array with each element replaced by its <a href="../base/c.html#Base.cconvert"><code>Base.cconvert</code></a> version. This allows, for example, passing an <code>argv</code> pointer array of type <code>Vector{String}</code> to an argument of type <code>Ptr{Ptr{Cchar}}</code>.</p></li></ul><p>On all systems we currently support, basic C/C++ value types may be translated to Julia types as follows. Every C type also has a corresponding Julia type with the same name, prefixed by C. This can help when writing portable code (and remembering that an <code>int</code> in C is not the same as an <code>Int</code> in Julia).</p><p><strong>System Independent Types</strong></p><table><tr><th style="text-align: left">C name</th><th style="text-align: left">Fortran name</th><th style="text-align: left">Standard Julia Alias</th><th style="text-align: left">Julia Base Type</th></tr><tr><td style="text-align: left"><code>unsigned char</code></td><td style="text-align: left"><code>CHARACTER</code></td><td style="text-align: left"><code>Cuchar</code></td><td style="text-align: left"><code>UInt8</code></td></tr><tr><td style="text-align: left"><code>bool</code> (_Bool in C99+)</td><td style="text-align: left"></td><td style="text-align: left"><code>Cuchar</code></td><td style="text-align: left"><code>UInt8</code></td></tr><tr><td style="text-align: left"><code>short</code></td><td style="text-align: left"><code>INTEGER*2</code>, <code>LOGICAL*2</code></td><td style="text-align: left"><code>Cshort</code></td><td style="text-align: left"><code>Int16</code></td></tr><tr><td style="text-align: left"><code>unsigned short</code></td><td style="text-align: left"></td><td style="text-align: left"><code>Cushort</code></td><td style="text-align: left"><code>UInt16</code></td></tr><tr><td style="text-align: left"><code>int</code>, <code>BOOL</code> (C, typical)</td><td style="text-align: left"><code>INTEGER*4</code>, <code>LOGICAL*4</code></td><td style="text-align: left"><code>Cint</code></td><td style="text-align: left"><code>Int32</code></td></tr><tr><td style="text-align: left"><code>unsigned int</code></td><td style="text-align: left"></td><td style="text-align: left"><code>Cuint</code></td><td style="text-align: left"><code>UInt32</code></td></tr><tr><td style="text-align: left"><code>long long</code></td><td style="text-align: left"><code>INTEGER*8</code>, <code>LOGICAL*8</code></td><td style="text-align: left"><code>Clonglong</code></td><td style="text-align: left"><code>Int64</code></td></tr><tr><td style="text-align: left"><code>unsigned long long</code></td><td style="text-align: left"></td><td style="text-align: left"><code>Culonglong</code></td><td style="text-align: left"><code>UInt64</code></td></tr><tr><td style="text-align: left"><code>intmax_t</code></td><td style="text-align: left"></td><td style="text-align: left"><code>Cintmax_t</code></td><td style="text-align: left"><code>Int64</code></td></tr><tr><td style="text-align: left"><code>uintmax_t</code></td><td style="text-align: left"></td><td style="text-align: left"><code>Cuintmax_t</code></td><td style="text-align: left"><code>UInt64</code></td></tr><tr><td style="text-align: left"><code>float</code></td><td style="text-align: left"><code>REAL*4i</code></td><td style="text-align: left"><code>Cfloat</code></td><td style="text-align: left"><code>Float32</code></td></tr><tr><td style="text-align: left"><code>double</code></td><td style="text-align: left"><code>REAL*8</code></td><td style="text-align: left"><code>Cdouble</code></td><td style="text-align: left"><code>Float64</code></td></tr><tr><td style="text-align: left"><code>complex float</code></td><td style="text-align: left"><code>COMPLEX*8</code></td><td style="text-align: left"><code>ComplexF32</code></td><td style="text-align: left"><code>Complex{Float32}</code></td></tr><tr><td style="text-align: left"><code>complex double</code></td><td style="text-align: left"><code>COMPLEX*16</code></td><td style="text-align: left"><code>ComplexF64</code></td><td style="text-align: left"><code>Complex{Float64}</code></td></tr><tr><td style="text-align: left"><code>ptrdiff_t</code></td><td style="text-align: left"></td><td style="text-align: left"><code>Cptrdiff_t</code></td><td style="text-align: left"><code>Int</code></td></tr><tr><td style="text-align: left"><code>ssize_t</code></td><td style="text-align: left"></td><td style="text-align: left"><code>Cssize_t</code></td><td style="text-align: left"><code>Int</code></td></tr><tr><td style="text-align: left"><code>size_t</code></td><td style="text-align: left"></td><td style="text-align: left"><code>Csize_t</code></td><td style="text-align: left"><code>UInt</code></td></tr><tr><td style="text-align: left"><code>void</code></td><td style="text-align: left"></td><td style="text-align: left"></td><td style="text-align: left"><code>Cvoid</code></td></tr><tr><td style="text-align: left"><code>void</code> and <code>[[noreturn]]</code> or <code>_Noreturn</code></td><td style="text-align: left"></td><td style="text-align: left"></td><td style="text-align: left"><code>Union{}</code></td></tr><tr><td style="text-align: left"><code>void*</code></td><td style="text-align: left"></td><td style="text-align: left"></td><td style="text-align: left"><code>Ptr{Cvoid}</code> (or similarly <code>Ref{Cvoid}</code>)</td></tr><tr><td style="text-align: left"><code>T*</code> (where T represents an appropriately defined type)</td><td style="text-align: left"></td><td style="text-align: left"></td><td style="text-align: left"><code>Ref{T}</code> (T may be safely mutated only if T is an isbits type)</td></tr><tr><td style="text-align: left"><code>char*</code> (or <code>char[]</code>, e.g. a string)</td><td style="text-align: left"><code>CHARACTER*N</code></td><td style="text-align: left"></td><td style="text-align: left"><code>Cstring</code> if null-terminated, or <code>Ptr{UInt8}</code> if not</td></tr><tr><td style="text-align: left"><code>char**</code> (or <code>*char[]</code>)</td><td style="text-align: left"></td><td style="text-align: left"></td><td style="text-align: left"><code>Ptr{Ptr{UInt8}}</code></td></tr><tr><td style="text-align: left"><code>jl_value_t*</code> (any Julia Type)</td><td style="text-align: left"></td><td style="text-align: left"></td><td style="text-align: left"><code>Any</code></td></tr><tr><td style="text-align: left"><code>jl_value_t* const*</code> (a reference to a Julia value)</td><td style="text-align: left"></td><td style="text-align: left"></td><td style="text-align: left"><code>Ref{Any}</code> (const, since mutation would require a write barrier, which is not possible to insert correctly)</td></tr><tr><td style="text-align: left"><code>va_arg</code></td><td style="text-align: left"></td><td style="text-align: left"></td><td style="text-align: left">Not supported</td></tr><tr><td style="text-align: left"><code>...</code> (variadic function specification)</td><td style="text-align: left"></td><td style="text-align: left"></td><td style="text-align: left"><code>T...</code> (where <code>T</code> is one of the above types, when using the <code>ccall</code> function)</td></tr><tr><td style="text-align: left"><code>...</code> (variadic function specification)</td><td style="text-align: left"></td><td style="text-align: left"></td><td style="text-align: left"><code>; va_arg1::T, va_arg2::S, etc.</code> (only supported with <code>@ccall</code> macro)</td></tr></table><p>The <a href="../base/c.html#Base.Cstring"><code>Cstring</code></a> type is essentially a synonym for <code>Ptr{UInt8}</code>, except the conversion to <code>Cstring</code> throws an error if the Julia string contains any embedded null characters (which would cause the string to be silently truncated if the C routine treats null as the terminator). If you are passing a <code>char*</code> to a C routine that does not assume null termination (e.g. because you pass an explicit string length), or if you know for certain that your Julia string does not contain null and want to skip the check, you can use <code>Ptr{UInt8}</code> as the argument type. <code>Cstring</code> can also be used as the <a href="../base/c.html#ccall"><code>ccall</code></a> return type, but in that case it obviously does not introduce any extra checks and is only meant to improve the readability of the call.</p><p><strong>System Dependent Types</strong></p><table><tr><th style="text-align: left">C name</th><th style="text-align: left">Standard Julia Alias</th><th style="text-align: left">Julia Base Type</th></tr><tr><td style="text-align: left"><code>char</code></td><td style="text-align: left"><code>Cchar</code></td><td style="text-align: left"><code>Int8</code> (x86, x86_64), <code>UInt8</code> (powerpc, arm)</td></tr><tr><td style="text-align: left"><code>long</code></td><td style="text-align: left"><code>Clong</code></td><td style="text-align: left"><code>Int</code> (UNIX), <code>Int32</code> (Windows)</td></tr><tr><td style="text-align: left"><code>unsigned long</code></td><td style="text-align: left"><code>Culong</code></td><td style="text-align: left"><code>UInt</code> (UNIX), <code>UInt32</code> (Windows)</td></tr><tr><td style="text-align: left"><code>wchar_t</code></td><td style="text-align: left"><code>Cwchar_t</code></td><td style="text-align: left"><code>Int32</code> (UNIX), <code>UInt16</code> (Windows)</td></tr></table><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>When calling Fortran, all inputs must be passed by pointers to heap- or stack-allocated values, so all type correspondences above should contain an additional <code>Ptr{..}</code> or <code>Ref{..}</code> wrapper around their type specification.</p></div></div><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>For string arguments (<code>char*</code>) the Julia type should be <code>Cstring</code> (if null-terminated data is expected), or either <code>Ptr{Cchar}</code> or <code>Ptr{UInt8}</code> otherwise (these two pointer types have the same effect), as described above, not <code>String</code>. Similarly, for array arguments (<code>T[]</code> or <code>T*</code>), the Julia type should again be <code>Ptr{T}</code>, not <code>Vector{T}</code>.</p></div></div><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Julia&#39;s <code>Char</code> type is 32 bits, which is not the same as the wide-character type (<code>wchar_t</code> or <code>wint_t</code>) on all platforms.</p></div></div><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>A return type of <code>Union{}</code> means the function will not return, i.e., C++11 <code>[[noreturn]]</code> or C11 <code>_Noreturn</code> (e.g. <code>jl_throw</code> or <code>longjmp</code>). Do not use this for functions that return no value (<code>void</code>) but do return, for those, use <code>Cvoid</code> instead.</p></div></div><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>For <code>wchar_t*</code> arguments, the Julia type should be <a href="../base/c.html#Base.Cwstring"><code>Cwstring</code></a> (if the C routine expects a null-terminated string), or <code>Ptr{Cwchar_t}</code> otherwise. Note also that UTF-8 string data in Julia is internally null-terminated, so it can be passed to C functions expecting null-terminated data without making a copy (but using the <code>Cwstring</code> type will cause an error to be thrown if the string itself contains null characters).</p></div></div><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>C functions that take an argument of type <code>char**</code> can be called by using a <code>Ptr{Ptr{UInt8}}</code> type within Julia. For example, C functions of the form:</p><pre><code class="language-c hljs">int main(int argc, char **argv);</code></pre><p>can be called via the following Julia code:</p><pre><code class="language-julia hljs">argv = [ &quot;a.out&quot;, &quot;arg1&quot;, &quot;arg2&quot; ]
@ccall main(length(argv)::Int32, argv::Ptr{Ptr{UInt8}})::Int32</code></pre></div></div><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>For Fortran functions taking variable length strings of type <code>character(len=*)</code> the string lengths are provided as <em>hidden arguments</em>. Type and position of these arguments in the list are compiler specific, where compiler vendors usually default to using <code>Csize_t</code> as type and append the hidden arguments at the end of the argument list. While this behaviour is fixed for some compilers (GNU), others <em>optionally</em> permit placing hidden arguments directly after the character argument (Intel, PGI). For example, Fortran subroutines of the form</p><pre><code class="language-fortran hljs">subroutine test(str1, str2)
character(len=*) :: str1,str2</code></pre><p>can be called via the following Julia code, where the lengths are appended</p><pre><code class="language-julia hljs">str1 = &quot;foo&quot;
str2 = &quot;bar&quot;
ccall(:test, Cvoid, (Ptr{UInt8}, Ptr{UInt8}, Csize_t, Csize_t),
                    str1, str2, sizeof(str1), sizeof(str2))</code></pre></div></div><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Fortran compilers <em>may</em> also add other hidden arguments for pointers, assumed-shape (<code>:</code>) and assumed-size (<code>*</code>) arrays. Such behaviour can be avoided by using <code>ISO_C_BINDING</code> and including <code>bind(c)</code> in the definition of the subroutine, which is strongly recommended for interoperable code. In this case, there will be no hidden arguments, at the cost of some language features (e.g. only <code>character(len=1)</code> will be permitted to pass strings).</p></div></div><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>A C function declared to return <code>Cvoid</code> will return the value <code>nothing</code> in Julia.</p></div></div><h3 id="Struct-Type-Correspondences"><a class="docs-heading-anchor" href="#Struct-Type-Correspondences">Struct Type Correspondences</a><a id="Struct-Type-Correspondences-1"></a><a class="docs-heading-anchor-permalink" href="#Struct-Type-Correspondences" title="Permalink"></a></h3><p>Composite types such as <code>struct</code> in C or <code>TYPE</code> in Fortran90 (or <code>STRUCTURE</code> / <code>RECORD</code> in some variants of F77), can be mirrored in Julia by creating a <code>struct</code> definition with the same field layout.</p><p>When used recursively, <code>isbits</code> types are stored inline. All other types are stored as a pointer to the data. When mirroring a struct used by-value inside another struct in C, it is imperative that you do not attempt to manually copy the fields over, as this will not preserve the correct field alignment. Instead, declare an <code>isbits</code> struct type and use that instead. Unnamed structs are not possible in the translation to Julia.</p><p>Packed structs and union declarations are not supported by Julia.</p><p>You can get an approximation of a <code>union</code> if you know, a priori, the field that will have the greatest size (potentially including padding). When translating your fields to Julia, declare the Julia field to be only of that type.</p><p>Arrays of parameters can be expressed with <code>NTuple</code>. For example, the struct in C notation is written as</p><pre><code class="language-c hljs">struct B {
    int A[3];
};

b_a_2 = B.A[2];</code></pre><p>can be written in Julia as</p><pre><code class="language-julia hljs">struct B
    A::NTuple{3, Cint}
end

b_a_2 = B.A[3]  # note the difference in indexing (1-based in Julia, 0-based in C)</code></pre><p>Arrays of unknown size (C99-compliant variable length structs specified by <code>[]</code> or <code>[0]</code>) are not directly supported. Often the best way to deal with these is to deal with the byte offsets directly. For example, if a C library declared a proper string type and returned a pointer to it:</p><pre><code class="language-c hljs">struct String {
    int strlen;
    char data[];
};</code></pre><p>In Julia, we can access the parts independently to make a copy of that string:</p><pre><code class="language-julia hljs">str = from_c::Ptr{Cvoid}
len = unsafe_load(Ptr{Cint}(str))
unsafe_string(str + Core.sizeof(Cint), len)</code></pre><h3 id="Type-Parameters"><a class="docs-heading-anchor" href="#Type-Parameters">Type Parameters</a><a id="Type-Parameters-1"></a><a class="docs-heading-anchor-permalink" href="#Type-Parameters" title="Permalink"></a></h3><p>The type arguments to <code>@ccall</code> and <code>@cfunction</code> are evaluated statically, when the method containing the usage is defined. They therefore must take the form of a literal tuple, not a variable, and cannot reference local variables.</p><p>This may sound like a strange restriction, but remember that since C is not a dynamic language like Julia, its functions can only accept argument types with a statically-known, fixed signature.</p><p>However, while the type layout must be known statically to compute the intended C ABI, the static parameters of the function are considered to be part of this static environment. The static parameters of the function may be used as type parameters in the call signature, as long as they don&#39;t affect the layout of the type. For example, <code>f(x::T) where {T} = @ccall valid(x::Ptr{T})::Ptr{T}</code> is valid, since <code>Ptr</code> is always a word-size primitive type. But, <code>g(x::T) where {T} = @ccall notvalid(x::T)::T</code> is not valid, since the type layout of <code>T</code> is not known statically.</p><h3 id="SIMD-Values"><a class="docs-heading-anchor" href="#SIMD-Values">SIMD Values</a><a id="SIMD-Values-1"></a><a class="docs-heading-anchor-permalink" href="#SIMD-Values" title="Permalink"></a></h3><p>If a C/C++ routine has an argument or return value that is a native SIMD type, the corresponding Julia type is a homogeneous tuple of <code>VecElement</code> that naturally maps to the SIMD type. Specifically:</p><blockquote><ul><li>The tuple must be the same size and elements as the SIMD type. For example, a tuple representing an <code>__m128</code> on x86 must have a size of 16 bytes and Float32 elements.</li><li>The element type of the tuple must be an instance of <code>VecElement{T}</code> where <code>T</code> is a primitive type with a power-of-two number of bytes (e.g. 1, 2, 4, 8, 16, etc) such as Int8 or Float64.</li></ul></blockquote><p>For instance, consider this C routine that uses AVX intrinsics:</p><pre><code class="language-c hljs">#include &lt;immintrin.h&gt;

__m256 dist( __m256 a, __m256 b ) {
    return _mm256_sqrt_ps(_mm256_add_ps(_mm256_mul_ps(a, a),
                                        _mm256_mul_ps(b, b)));
}</code></pre><p>The following Julia code calls <code>dist</code> using <code>ccall</code>:</p><pre><code class="language-julia hljs">const m256 = NTuple{8, VecElement{Float32}}

a = m256(ntuple(i -&gt; VecElement(sin(Float32(i))), 8))
b = m256(ntuple(i -&gt; VecElement(cos(Float32(i))), 8))

function call_dist(a::m256, b::m256)
    @ccall &quot;libdist&quot;.dist(a::m256, b::m256)::m256
end

println(call_dist(a,b))</code></pre><p>The host machine must have the requisite SIMD registers. For example, the code above will not work on hosts without AVX support.</p><h3 id="Memory-Ownership"><a class="docs-heading-anchor" href="#Memory-Ownership">Memory Ownership</a><a id="Memory-Ownership-1"></a><a class="docs-heading-anchor-permalink" href="#Memory-Ownership" title="Permalink"></a></h3><p><strong><code>malloc</code>/<code>free</code></strong></p><p>Memory allocation and deallocation of such objects must be handled by calls to the appropriate cleanup routines in the libraries being used, just like in any C program. Do not try to free an object received from a C library with <a href="../base/libc.html#Base.Libc.free"><code>Libc.free</code></a> in Julia, as this may result in the <code>free</code> function being called via the wrong library and cause the process to abort. The reverse (passing an object allocated in Julia to be freed by an external library) is equally invalid.</p><h3 id="When-to-use-T,-Ptr{T}-and-Ref{T}"><a class="docs-heading-anchor" href="#When-to-use-T,-Ptr{T}-and-Ref{T}">When to use <code>T</code>, <code>Ptr{T}</code> and <code>Ref{T}</code></a><a id="When-to-use-T,-Ptr{T}-and-Ref{T}-1"></a><a class="docs-heading-anchor-permalink" href="#When-to-use-T,-Ptr{T}-and-Ref{T}" title="Permalink"></a></h3><p>In Julia code wrapping calls to external C routines, ordinary (non-pointer) data should be declared to be of type <code>T</code> inside the <code>@ccall</code>, as they are passed by value. For C code accepting pointers, <a href="../base/c.html#Core.Ref"><code>Ref{T}</code></a> should generally be used for the types of input arguments, allowing the use of pointers to memory managed by either Julia or C through the implicit call to <a href="../base/c.html#Base.cconvert"><code>Base.cconvert</code></a>. In contrast, pointers returned by the C function called should be declared to be of the output type <a href="../base/c.html#Core.Ptr"><code>Ptr{T}</code></a>, reflecting that the memory pointed to is managed by C only. Pointers contained in C structs should be represented as fields of type <code>Ptr{T}</code> within the corresponding Julia struct types designed to mimic the internal structure of corresponding C structs.</p><p>In Julia code wrapping calls to external Fortran routines, all input arguments should be declared as of type <code>Ref{T}</code>, as Fortran passes all variables by pointers to memory locations. The return type should either be <code>Cvoid</code> for Fortran subroutines, or a <code>T</code> for Fortran functions returning the type <code>T</code>.</p><h2 id="Mapping-C-Functions-to-Julia"><a class="docs-heading-anchor" href="#Mapping-C-Functions-to-Julia">Mapping C Functions to Julia</a><a id="Mapping-C-Functions-to-Julia-1"></a><a class="docs-heading-anchor-permalink" href="#Mapping-C-Functions-to-Julia" title="Permalink"></a></h2><h3 id="@ccall-/-@cfunction-argument-translation-guide"><a class="docs-heading-anchor" href="#@ccall-/-@cfunction-argument-translation-guide"><code>@ccall</code> / <code>@cfunction</code> argument translation guide</a><a id="@ccall-/-@cfunction-argument-translation-guide-1"></a><a class="docs-heading-anchor-permalink" href="#@ccall-/-@cfunction-argument-translation-guide" title="Permalink"></a></h3><p>For translating a C argument list to Julia:</p><ul><li><p><code>T</code>, where <code>T</code> is one of the primitive types: <code>char</code>, <code>int</code>, <code>long</code>, <code>short</code>, <code>float</code>, <code>double</code>, <code>complex</code>, <code>enum</code> or any of their <code>typedef</code> equivalents</p><ul><li><code>T</code>, where <code>T</code> is an equivalent Julia Bits Type (per the table above)</li><li>if <code>T</code> is an <code>enum</code>, the argument type should be equivalent to <code>Cint</code> or <code>Cuint</code></li><li>argument value will be copied (passed by value)</li></ul></li><li><p><code>struct T</code> (including typedef to a struct)</p><ul><li><code>T</code>, where <code>T</code> is a Julia leaf type</li><li>argument value will be copied (passed by value)</li></ul></li><li><p><code>vector T</code> (or <code>__attribute__ vector_size</code>, or a typedef such as <code>__m128</code>)</p><ul><li><code>NTuple{N, VecElement{T}}</code>, where <code>T</code> is a primitive Julia type of the correct size and N is the number of elements in the vector (equal to <code>vector_size / sizeof T</code>).</li></ul></li><li><p><code>void*</code></p><ul><li>depends on how this parameter is used, first translate this to the intended pointer type, then determine the Julia equivalent using the remaining rules in this list</li><li>this argument may be declared as <code>Ptr{Cvoid}</code> if it really is just an unknown pointer</li></ul></li><li><p><code>jl_value_t*</code></p><ul><li><code>Any</code></li><li>argument value must be a valid Julia object</li></ul></li><li><p><code>jl_value_t* const*</code></p><ul><li><code>Ref{Any}</code></li><li>argument list must be a valid Julia object (or <code>C_NULL</code>)</li><li>cannot be used for an output parameter, unless the user is able to separately arrange for the object to be GC-preserved</li></ul></li><li><p><code>T*</code></p><ul><li><code>Ref{T}</code>, where <code>T</code> is the Julia type corresponding to <code>T</code></li><li>argument value will be copied if it is an <code>inlinealloc</code> type (which includes <code>isbits</code> otherwise, the value must be a valid Julia object</li></ul></li><li><p><code>T (*)(...)</code> (e.g. a pointer to a function)</p><ul><li><code>Ptr{Cvoid}</code> (you may need to use <a href="../base/c.html#Base.@cfunction"><code>@cfunction</code></a> explicitly to create this pointer)</li></ul></li><li><p><code>...</code> (e.g. a vararg)</p><ul><li>[for <code>ccall</code>]: <code>T...</code>, where <code>T</code> is the single Julia type of all remaining arguments</li><li>[for <code>@ccall</code>]: <code>; va_arg1::T, va_arg2::S, etc</code>, where <code>T</code> and <code>S</code> are the Julia type (i.e. separate the regular arguments from varargs with a <code>;</code>)</li><li>currently unsupported by <code>@cfunction</code></li></ul></li><li><p><code>va_arg</code></p><ul><li>not supported by <code>ccall</code> or <code>@cfunction</code></li></ul></li></ul><h3 id="@ccall-/-@cfunction-return-type-translation-guide"><a class="docs-heading-anchor" href="#@ccall-/-@cfunction-return-type-translation-guide"><code>@ccall</code> / <code>@cfunction</code> return type translation guide</a><a id="@ccall-/-@cfunction-return-type-translation-guide-1"></a><a class="docs-heading-anchor-permalink" href="#@ccall-/-@cfunction-return-type-translation-guide" title="Permalink"></a></h3><p>For translating a C return type to Julia:</p><ul><li><p><code>void</code></p><ul><li><code>Cvoid</code> (this will return the singleton instance <code>nothing::Cvoid</code>)</li></ul></li><li><p><code>T</code>, where <code>T</code> is one of the primitive types: <code>char</code>, <code>int</code>, <code>long</code>, <code>short</code>, <code>float</code>, <code>double</code>, <code>complex</code>, <code>enum</code> or any of their <code>typedef</code> equivalents</p><ul><li>same as C argument list</li><li>argument value will be copied (returned by-value)</li></ul></li><li><p><code>struct T</code> (including typedef to a struct)</p><ul><li>same as C argument list</li><li>argument value will be copied (returned by-value)</li></ul></li><li><p><code>vector T</code></p><ul><li>same as C argument list</li></ul></li><li><p><code>void*</code></p><ul><li>depends on how this parameter is used, first translate this to the intended pointer type, then determine the Julia equivalent using the remaining rules in this list</li><li>this argument may be declared as <code>Ptr{Cvoid}</code> if it really is just an unknown pointer</li></ul></li><li><p><code>jl_value_t*</code></p><ul><li><code>Any</code></li><li>argument value must be a valid Julia object</li></ul></li><li><p><code>jl_value_t**</code></p><ul><li><code>Ptr{Any}</code> (<code>Ref{Any}</code> is invalid as a return type)</li></ul></li><li><p><code>T*</code></p><ul><li><p>If the memory is already owned by Julia, or is an <code>isbits</code> type, and is known to be non-null:</p><ul><li><code>Ref{T}</code>, where <code>T</code> is the Julia type corresponding to <code>T</code></li><li>a return type of <code>Ref{Any}</code> is invalid, it should either be <code>Any</code> (corresponding to <code>jl_value_t*</code>) or <code>Ptr{Any}</code> (corresponding to <code>jl_value_t**</code>)</li><li>C <strong>MUST NOT</strong> modify the memory returned via <code>Ref{T}</code> if <code>T</code> is an <code>isbits</code> type</li></ul></li><li><p>If the memory is owned by C:</p><ul><li><code>Ptr{T}</code>, where <code>T</code> is the Julia type corresponding to <code>T</code></li></ul></li></ul></li><li><p><code>T (*)(...)</code> (e.g. a pointer to a function)</p><ul><li><code>Ptr{Cvoid}</code> to call this directly from Julia you will need to pass this as the first argument to <code>@ccall</code>. See <a href="calling-c-and-fortran-code.html#Indirect-Calls">Indirect Calls</a>.</li></ul></li></ul><h3 id="Passing-Pointers-for-Modifying-Inputs"><a class="docs-heading-anchor" href="#Passing-Pointers-for-Modifying-Inputs">Passing Pointers for Modifying Inputs</a><a id="Passing-Pointers-for-Modifying-Inputs-1"></a><a class="docs-heading-anchor-permalink" href="#Passing-Pointers-for-Modifying-Inputs" title="Permalink"></a></h3><p>Because C doesn&#39;t support multiple return values, often C functions will take pointers to data that the function will modify. To accomplish this within a <code>@ccall</code>, you need to first encapsulate the value inside a <a href="../base/c.html#Core.Ref"><code>Ref{T}</code></a> of the appropriate type. When you pass this <code>Ref</code> object as an argument, Julia will automatically pass a C pointer to the encapsulated data:</p><pre><code class="language-julia hljs">width = Ref{Cint}(0)
range = Ref{Cfloat}(0)
@ccall foo(width::Ref{Cint}, range::Ref{Cfloat})::Cvoid</code></pre><p>Upon return, the contents of <code>width</code> and <code>range</code> can be retrieved (if they were changed by <code>foo</code>) by <code>width[]</code> and <code>range[]</code>; that is, they act like zero-dimensional arrays.</p><h2 id="C-Wrapper-Examples"><a class="docs-heading-anchor" href="#C-Wrapper-Examples">C Wrapper Examples</a><a id="C-Wrapper-Examples-1"></a><a class="docs-heading-anchor-permalink" href="#C-Wrapper-Examples" title="Permalink"></a></h2><p>Let&#39;s start with a simple example of a C wrapper that returns a <code>Ptr</code> type:</p><pre><code class="language-julia hljs">mutable struct gsl_permutation
end

# The corresponding C signature is
#     gsl_permutation * gsl_permutation_alloc (size_t n);
function permutation_alloc(n::Integer)
    output_ptr = @ccall &quot;libgsl&quot;.gsl_permutation_alloc(n::Csize_t)::Ptr{gsl_permutation}
    if output_ptr == C_NULL # Could not allocate memory
        throw(OutOfMemoryError())
    end
    return output_ptr
end</code></pre><p>The <a href="https://www.gnu.org/software/gsl/">GNU Scientific Library</a> (here assumed to be accessible through <code>:libgsl</code>) defines an opaque pointer, <code>gsl_permutation *</code>, as the return type of the C function <code>gsl_permutation_alloc</code>. As user code never has to look inside the <code>gsl_permutation</code> struct, the corresponding Julia wrapper simply needs a new type declaration, <code>gsl_permutation</code>, that has no internal fields and whose sole purpose is to be placed in the type parameter of a <code>Ptr</code> type. The return type of the <a href="../base/c.html#ccall"><code>ccall</code></a> is declared as <code>Ptr{gsl_permutation}</code>, since the memory allocated and pointed to by <code>output_ptr</code> is controlled by C.</p><p>The input <code>n</code> is passed by value, and so the function&#39;s input signature is simply declared as <code>::Csize_t</code> without any <code>Ref</code> or <code>Ptr</code> necessary. (If the wrapper was calling a Fortran function instead, the corresponding function input signature would instead be <code>::Ref{Csize_t}</code>, since Fortran variables are passed by pointers.) Furthermore, <code>n</code> can be any type that is convertible to a <code>Csize_t</code> integer; the <a href="../base/c.html#ccall"><code>ccall</code></a> implicitly calls <a href="../base/c.html#Base.cconvert"><code>Base.cconvert(Csize_t, n)</code></a>.</p><p>Here is a second example wrapping the corresponding destructor:</p><pre><code class="language-julia hljs"># The corresponding C signature is
#     void gsl_permutation_free (gsl_permutation * p);
function permutation_free(p::Ptr{gsl_permutation})
    @ccall &quot;libgsl&quot;.gsl_permutation_free(p::Ptr{gsl_permutation})::Cvoid
end</code></pre><p>Here is a third example passing Julia arrays:</p><pre><code class="language-julia hljs"># The corresponding C signature is
#    int gsl_sf_bessel_Jn_array (int nmin, int nmax, double x,
#                                double result_array[])
function sf_bessel_Jn_array(nmin::Integer, nmax::Integer, x::Real)
    if nmax &lt; nmin
        throw(DomainError())
    end
    result_array = Vector{Cdouble}(undef, nmax - nmin + 1)
    errorcode = @ccall &quot;libgsl&quot;.gsl_sf_bessel_Jn_array(
                    nmin::Cint, nmax::Cint, x::Cdouble, result_array::Ref{Cdouble})::Cint
    if errorcode != 0
        error(&quot;GSL error code $errorcode&quot;)
    end
    return result_array
end</code></pre><p>The C function wrapped returns an integer error code; the results of the actual evaluation of the Bessel J function populate the Julia array <code>result_array</code>. This variable is declared as a <code>Ref{Cdouble}</code>, since its memory is allocated and managed by Julia. The implicit call to <a href="../base/c.html#Base.cconvert"><code>Base.cconvert(Ref{Cdouble}, result_array)</code></a> unpacks the Julia pointer to a Julia array data structure into a form understandable by C.</p><h2 id="Fortran-Wrapper-Example"><a class="docs-heading-anchor" href="#Fortran-Wrapper-Example">Fortran Wrapper Example</a><a id="Fortran-Wrapper-Example-1"></a><a class="docs-heading-anchor-permalink" href="#Fortran-Wrapper-Example" title="Permalink"></a></h2><p>The following example utilizes <code>ccall</code> to call a function in a common Fortran library (libBLAS) to compute a dot product. Notice that the argument mapping is a bit different here than above, as we need to map from Julia to Fortran. On every argument type, we specify <code>Ref</code> or <code>Ptr</code>. This mangling convention may be specific to your Fortran compiler and operating system and is likely undocumented. However, wrapping each in a <code>Ref</code> (or <code>Ptr</code>, where equivalent) is a frequent requirement of Fortran compiler implementations:</p><pre><code class="language-julia hljs">function compute_dot(DX::Vector{Float64}, DY::Vector{Float64})
    @assert length(DX) == length(DY)
    n = length(DX)
    incx = incy = 1
    product = @ccall &quot;libLAPACK&quot;.ddot(
        n::Ref{Int32}, DX::Ptr{Float64}, incx::Ref{Int32}, DY::Ptr{Float64}, incy::Ref{Int32})::Float64
    return product
end</code></pre><h2 id="Garbage-Collection-Safety"><a class="docs-heading-anchor" href="#Garbage-Collection-Safety">Garbage Collection Safety</a><a id="Garbage-Collection-Safety-1"></a><a class="docs-heading-anchor-permalink" href="#Garbage-Collection-Safety" title="Permalink"></a></h2><p>When passing data to a <code>@ccall</code>, it is best to avoid using the <a href="../base/c.html#Base.pointer"><code>pointer</code></a> function. Instead define a <a href="../base/c.html#Base.cconvert"><code>Base.cconvert</code></a> method and pass the variables directly to the <code>@ccall</code>. <code>@ccall</code> automatically arranges that all of its arguments will be preserved from garbage collection until the call returns. If a C API will store a reference to memory allocated by Julia, after the <code>@ccall</code> returns, you must ensure that the object remains visible to the garbage collector. The suggested way to do this is to make a global variable of type <code>Array{Ref,1}</code> to hold these values until the C library notifies you that it is finished with them.</p><p>Whenever you have created a pointer to Julia data, you must ensure the original data exists until you have finished using the pointer. Many methods in Julia such as <a href="../base/c.html#Base.unsafe_load"><code>unsafe_load</code></a> and <a href="../base/strings.html#Core.String-Tuple{AbstractString}"><code>String</code></a> make copies of data instead of taking ownership of the buffer, so that it is safe to free (or alter) the original data without affecting Julia. A notable exception is <a href="../base/c.html#Base.unsafe_wrap-Union{Tuple{N}, Tuple{T}, Tuple{Union{Type{Array}, Type{Array{T}}, Type{Array{T, N}}}, Ptr{T}, NTuple{N, Int64}}} where {T, N}"><code>unsafe_wrap</code></a> which, for performance reasons, shares (or can be told to take ownership of) the underlying buffer.</p><p>The garbage collector does not guarantee any order of finalization. That is, if <code>a</code> contained a reference to <code>b</code> and both <code>a</code> and <code>b</code> are due for garbage collection, there is no guarantee that <code>b</code> would be finalized after <code>a</code>. If proper finalization of <code>a</code> depends on <code>b</code> being valid, it must be handled in other ways.</p><h2 id="Non-constant-Function-Specifications"><a class="docs-heading-anchor" href="#Non-constant-Function-Specifications">Non-constant Function Specifications</a><a id="Non-constant-Function-Specifications-1"></a><a class="docs-heading-anchor-permalink" href="#Non-constant-Function-Specifications" title="Permalink"></a></h2><p>In some cases, the exact name or path of the needed library is not known in advance and must be computed at run time. To handle such cases, the library component specification can be a function call, e.g. <code>find_blas().dgemm</code>. The call expression will be executed when the <code>ccall</code> itself is executed. However, it is assumed that the library location does not change once it is determined, so the result of the call can be cached and reused. Therefore, the number of times the expression executes is unspecified, and returning different values for multiple calls results in unspecified behavior.</p><p>If even more flexibility is needed, it is possible to use computed values as function names by staging through <a href="../base/base.html#eval"><code>eval</code></a> as follows:</p><pre><code class="language-julia hljs">@eval @ccall &quot;lib&quot;.$(string(&quot;a&quot;, &quot;b&quot;))()::Cint</code></pre><p>This expression constructs a name using <code>string</code>, then substitutes this name into a new <code>@ccall</code> expression, which is then evaluated. Keep in mind that <code>eval</code> only operates at the top level, so within this expression local variables will not be available (unless their values are substituted with <code>$</code>). For this reason, <code>eval</code> is typically only used to form top-level definitions, for example when wrapping libraries that contain many similar functions. A similar example can be constructed for <a href="../base/c.html#Base.@cfunction"><code>@cfunction</code></a>.</p><p>However, doing this will also be very slow and leak memory, so you should usually avoid this and instead keep reading. The next section discusses how to use indirect calls to efficiently achieve a similar effect.</p><h2 id="Indirect-Calls"><a class="docs-heading-anchor" href="#Indirect-Calls">Indirect Calls</a><a id="Indirect-Calls-1"></a><a class="docs-heading-anchor-permalink" href="#Indirect-Calls" title="Permalink"></a></h2><p>The first argument to <code>@ccall</code> can also be an expression evaluated at run time. In this case, the expression must evaluate to a <code>Ptr</code>, which will be used as the address of the native function to call. This behavior occurs when the first <code>@ccall</code> argument contains references to non-constants, such as local variables, function arguments, or non-constant globals.</p><p>For example, you might look up the function via <code>dlsym</code>, then cache it in a shared reference for that session. For example:</p><pre><code class="language-julia hljs">macro dlsym(lib, func)
    z = Ref{Ptr{Cvoid}}(C_NULL)
    quote
        let zlocal = $z[]
            if zlocal == C_NULL
                zlocal = dlsym($(esc(lib))::Ptr{Cvoid}, $(esc(func)))::Ptr{Cvoid}
                $z[] = zlocal
            end
            zlocal
        end
    end
end

mylibvar = Libdl.dlopen(&quot;mylib&quot;)
@ccall $(@dlsym(mylibvar, &quot;myfunc&quot;))()::Cvoid</code></pre><h2 id="Closure-cfunctions"><a class="docs-heading-anchor" href="#Closure-cfunctions">Closure cfunctions</a><a id="Closure-cfunctions-1"></a><a class="docs-heading-anchor-permalink" href="#Closure-cfunctions" title="Permalink"></a></h2><p>The first argument to <a href="../base/c.html#Base.@cfunction"><code>@cfunction</code></a> can be marked with a <code>$</code>, in which case the return value will instead be a <code>struct CFunction</code> which closes over the argument. You must ensure that this return object is kept alive until all uses of it are done. The contents and code at the cfunction pointer will be erased via a <a href="../base/base.html#Base.finalizer"><code>finalizer</code></a> when this reference is dropped and atexit. This is not usually needed, since this functionality is not present in C, but can be useful for dealing with ill-designed APIs which don&#39;t provide a separate closure environment parameter.</p><pre><code class="language-julia hljs">function qsort(a::Vector{T}, cmp) where T
    isbits(T) || throw(ArgumentError(&quot;this method can only qsort isbits arrays&quot;))
    callback = @cfunction $cmp Cint (Ref{T}, Ref{T})
    # Here, `callback` isa Base.CFunction, which will be converted to Ptr{Cvoid}
    # (and protected against finalization) by the ccall
    @ccall qsort(a::Ptr{T}, length(a)::Csize_t, Base.elsize(a)::Csize_t, callback::Ptr{Cvoid})
    # We could instead use:
    #    GC.@preserve callback begin
    #        use(Base.unsafe_convert(Ptr{Cvoid}, callback))
    #    end
    # if we needed to use it outside of a `ccall`
    return a
end</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Closure <a href="../base/c.html#Base.@cfunction"><code>@cfunction</code></a> relies on LLVM trampolines, which are not available on all platforms (for example ARM and PowerPC).</p></div></div><h2 id="Closing-a-Library"><a class="docs-heading-anchor" href="#Closing-a-Library">Closing a Library</a><a id="Closing-a-Library-1"></a><a class="docs-heading-anchor-permalink" href="#Closing-a-Library" title="Permalink"></a></h2><p>It is sometimes useful to close (unload) a library so that it can be reloaded. For instance, when developing C code for use with Julia, one may need to compile, call the C code from Julia, then close the library, make an edit, recompile, and load in the new changes. One can either restart Julia or use the <code>Libdl</code> functions to manage the library explicitly, such as:</p><pre><code class="language-julia hljs">lib = Libdl.dlopen(&quot;./my_lib.so&quot;) # Open the library explicitly.
sym = Libdl.dlsym(lib, :my_fcn)   # Get a symbol for the function to call.
@ccall $sym(...) # Use the pointer `sym` instead of the library.symbol tuple.
Libdl.dlclose(lib) # Close the library explicitly.</code></pre><p>Note that when using <code>@ccall</code> with the input (e.g., <code>@ccall &quot;./my_lib.so&quot;.my_fcn(...)::Cvoid</code>), the library is opened implicitly and it may not be explicitly closed.</p><h2 id="Variadic-function-calls"><a class="docs-heading-anchor" href="#Variadic-function-calls">Variadic function calls</a><a id="Variadic-function-calls-1"></a><a class="docs-heading-anchor-permalink" href="#Variadic-function-calls" title="Permalink"></a></h2><p>To call variadic C functions a <code>semicolon</code> can be used in the argument list to separate required arguments from variadic arguments. An example with the <code>printf</code> function is given below:</p><pre><code class="language-julia-repl hljs">julia&gt; @ccall printf(&quot;%s = %d\n&quot;::Cstring ; &quot;foo&quot;::Cstring, foo::Cint)::Cint
foo = 3
8</code></pre><h2 id="ccall-interface"><a class="docs-heading-anchor" href="#ccall-interface"><code>ccall</code> interface</a><a id="ccall-interface-1"></a><a class="docs-heading-anchor-permalink" href="#ccall-interface" title="Permalink"></a></h2><p>There is another alternative interface to <code>@ccall</code>. This interface is slightly less convenient but it does allow one to specify a <a href="calling-c-and-fortran-code.html#calling-convention">calling convention</a>.</p><p>The arguments to <a href="../base/c.html#ccall"><code>ccall</code></a> are:</p><ol><li><p>A <code>(:function, &quot;library&quot;)</code> pair (most common),</p><p>OR</p><p>a <code>:function</code> name symbol or <code>&quot;function&quot;</code> name string (for symbols in the current process or libc),</p><p>OR</p><p>a function pointer (for example, from <code>dlsym</code>).</p></li><li><p>The function&#39;s return type</p></li><li><p>A tuple of input types, corresponding to the function signature. One common mistake is forgetting that a 1-tuple of argument types must be written with a trailing comma.</p></li><li><p>The actual argument values to be passed to the function, if any; each is a separate parameter.</p></li></ol><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The <code>(:function, &quot;library&quot;)</code> pair, return type, and input types must be literal constants (i.e., they can&#39;t be variables, but see <a href="calling-c-and-fortran-code.html#Non-constant-Function-Specifications">Non-constant Function Specifications</a>).</p><p>The remaining parameters are evaluated at compile-time, when the containing method is defined.</p></div></div><p>A table of translations between the macro and function interfaces is given below.</p><table><tr><th style="text-align: right"><code>@ccall</code></th><th style="text-align: right"><code>ccall</code></th></tr><tr><td style="text-align: right"><code>@ccall clock()::Int32</code></td><td style="text-align: right"><code>ccall(:clock, Int32, ())</code></td></tr><tr><td style="text-align: right"><code>@ccall f(a::Cint)::Cint</code></td><td style="text-align: right"><code>ccall(:a, Cint, (Cint,), a)</code></td></tr><tr><td style="text-align: right"><code>@ccall &quot;mylib&quot;.f(a::Cint, b::Cdouble)::Cvoid</code></td><td style="text-align: right"><code>ccall((:f, &quot;mylib&quot;), Cvoid, (Cint, Cdouble), (a, b))</code></td></tr><tr><td style="text-align: right"><code>@ccall $fptr.f()::Cvoid</code></td><td style="text-align: right"><code>ccall(fptr, f, Cvoid, ())</code></td></tr><tr><td style="text-align: right"><code>@ccall printf(&quot;%s = %d\n&quot;::Cstring ; &quot;foo&quot;::Cstring, foo::Cint)::Cint</code></td><td style="text-align: right"><code>&lt;unavailable&gt;</code></td></tr><tr><td style="text-align: right"><code>@ccall printf(&quot;%s = %s\n&quot;::Cstring ; &quot;2 + 2&quot;::Cstring, &quot;5&quot;::Cstring)::Cint</code></td><td style="text-align: right"><code>ccall(:printf, Cint, (Cstring, Cstring...), &quot;%s = %s\n&quot;, &quot;2 + 2&quot;, &quot;5&quot;)</code></td></tr><tr><td style="text-align: right"><code>&lt;unavailable&gt;</code></td><td style="text-align: right"><code>ccall(:gethostname, stdcall, Int32, (Ptr{UInt8}, UInt32), hn, length(hn))</code></td></tr></table><h2 id="calling-convention"><a class="docs-heading-anchor" href="#calling-convention">Calling Convention</a><a id="calling-convention-1"></a><a class="docs-heading-anchor-permalink" href="#calling-convention" title="Permalink"></a></h2><p>The second argument to <code>ccall</code> (immediately preceding return type) can optionally be a calling convention specifier (the <code>@ccall</code> macro currently does not support giving a calling convention). Without any specifier, the platform-default C calling convention is used. Other supported conventions are: <code>stdcall</code>, <code>cdecl</code>, <code>fastcall</code>, and <code>thiscall</code> (no-op on 64-bit Windows). For example (from <code>base/libc.jl</code>) we see the same <code>gethostname</code><code>ccall</code> as above, but with the correct signature for Windows:</p><pre><code class="language-julia hljs">hn = Vector{UInt8}(undef, 256)
err = ccall(:gethostname, stdcall, Int32, (Ptr{UInt8}, UInt32), hn, length(hn))</code></pre><p>For more information, please see the <a href="https://llvm.org/docs/LangRef.html#calling-conventions">LLVM Language Reference</a>.</p><p>There is one additional special calling convention <a href="../base/c.html#Core.Intrinsics.llvmcall"><code>llvmcall</code></a>, which allows inserting calls to LLVM intrinsics directly. This can be especially useful when targeting unusual platforms such as GPGPUs. For example, for <a href="https://llvm.org/docs/NVPTXUsage.html">CUDA</a>, we need to be able to read the thread index:</p><pre><code class="language-julia hljs">ccall(&quot;llvm.nvvm.read.ptx.sreg.tid.x&quot;, llvmcall, Int32, ())</code></pre><p>As with any <code>ccall</code>, it is essential to get the argument signature exactly correct. Also, note that there is no compatibility layer that ensures the intrinsic makes sense and works on the current target, unlike the equivalent Julia functions exposed by <code>Core.Intrinsics</code>.</p><h2 id="Accessing-Global-Variables"><a class="docs-heading-anchor" href="#Accessing-Global-Variables">Accessing Global Variables</a><a id="Accessing-Global-Variables-1"></a><a class="docs-heading-anchor-permalink" href="#Accessing-Global-Variables" title="Permalink"></a></h2><p>Global variables exported by native libraries can be accessed by name using the <a href="../base/c.html#Core.Intrinsics.cglobal"><code>cglobal</code></a> function. The arguments to <a href="../base/c.html#Core.Intrinsics.cglobal"><code>cglobal</code></a> are a symbol specification identical to that used by <a href="../base/c.html#ccall"><code>ccall</code></a>, and a type describing the value stored in the variable:</p><pre><code class="language-julia-repl hljs">julia&gt; cglobal((:errno, :libc), Int32)
Ptr{Int32} @0x00007f418d0816b8</code></pre><p>The result is a pointer giving the address of the value. The value can be manipulated through this pointer using <a href="../base/c.html#Base.unsafe_load"><code>unsafe_load</code></a> and <a href="../base/c.html#Base.unsafe_store!"><code>unsafe_store!</code></a>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This <code>errno</code> symbol may not be found in a library named &quot;libc&quot;, as this is an implementation detail of your system compiler. Typically standard library symbols should be accessed just by name, allowing the compiler to fill in the correct one. Also, however, the <code>errno</code> symbol shown in this example is special in most compilers, and so the value seen here is probably not what you expect or want. Compiling the equivalent code in C on any multi-threaded-capable system would typically actually call a different function (via macro preprocessor overloading), and may give a different result than the legacy value printed here.</p></div></div><h2 id="Accessing-Data-through-a-Pointer"><a class="docs-heading-anchor" href="#Accessing-Data-through-a-Pointer">Accessing Data through a Pointer</a><a id="Accessing-Data-through-a-Pointer-1"></a><a class="docs-heading-anchor-permalink" href="#Accessing-Data-through-a-Pointer" title="Permalink"></a></h2><p>The following methods are described as &quot;unsafe&quot; because a bad pointer or type declaration can cause Julia to terminate abruptly.</p><p>Given a <code>Ptr{T}</code>, the contents of type <code>T</code> can generally be copied from the referenced memory into a Julia object using <code>unsafe_load(ptr, [index])</code>. The index argument is optional (default is 1), and follows the Julia-convention of 1-based indexing. This function is intentionally similar to the behavior of <a href="../base/collections.html#Base.getindex"><code>getindex</code></a> and <a href="../base/collections.html#Base.setindex!"><code>setindex!</code></a> (e.g. <code>[]</code> access syntax).</p><p>The return value will be a new object initialized to contain a copy of the contents of the referenced memory. The referenced memory can safely be freed or released.</p><p>If <code>T</code> is <code>Any</code>, then the memory is assumed to contain a reference to a Julia object (a <code>jl_value_t*</code>), the result will be a reference to this object, and the object will not be copied. You must be careful in this case to ensure that the object was always visible to the garbage collector (pointers do not count, but the new reference does) to ensure the memory is not prematurely freed. Note that if the object was not originally allocated by Julia, the new object will never be finalized by Julia&#39;s garbage collector. If the <code>Ptr</code> itself is actually a <code>jl_value_t*</code>, it can be converted back to a Julia object reference by <a href="../base/c.html#Base.unsafe_pointer_to_objref"><code>unsafe_pointer_to_objref(ptr)</code></a>. (Julia values <code>v</code> can be converted to <code>jl_value_t*</code> pointers, as <code>Ptr{Cvoid}</code>, by calling <a href="../base/c.html#Base.pointer_from_objref"><code>pointer_from_objref(v)</code></a>.)</p><p>The reverse operation (writing data to a <code>Ptr{T}</code>), can be performed using <a href="../base/c.html#Base.unsafe_store!"><code>unsafe_store!(ptr, value, [index])</code></a>. Currently, this is only supported for primitive types or other pointer-free (<code>isbits</code>) immutable struct types.</p><p>Any operation that throws an error is probably currently unimplemented and should be posted as a bug so that it can be resolved.</p><p>If the pointer of interest is a plain-data array (primitive type or immutable struct), the function <a href="../base/c.html#Base.unsafe_wrap-Union{Tuple{N}, Tuple{T}, Tuple{Union{Type{Array}, Type{Array{T}}, Type{Array{T, N}}}, Ptr{T}, NTuple{N, Int64}}} where {T, N}"><code>unsafe_wrap(Array, ptr,dims, own = false)</code></a> may be more useful. The final parameter should be true if Julia should &quot;take ownership&quot; of the underlying buffer and call <code>free(ptr)</code> when the returned <code>Array</code> object is finalized. If the <code>own</code> parameter is omitted or false, the caller must ensure the buffer remains in existence until all access is complete.</p><p>Arithmetic on the <code>Ptr</code> type in Julia (e.g. using <code>+</code>) does not behave the same as C&#39;s pointer arithmetic. Adding an integer to a <code>Ptr</code> in Julia always moves the pointer by some number of <em>bytes</em>, not elements. This way, the address values obtained from pointer arithmetic do not depend on the element types of pointers.</p><h2 id="Thread-safety"><a class="docs-heading-anchor" href="#Thread-safety">Thread-safety</a><a id="Thread-safety-1"></a><a class="docs-heading-anchor-permalink" href="#Thread-safety" title="Permalink"></a></h2><p>Some C libraries execute their callbacks from a different thread, and since Julia isn&#39;t thread-safe you&#39;ll need to take some extra precautions. In particular, you&#39;ll need to set up a two-layered system: the C callback should only <em>schedule</em> (via Julia&#39;s event loop) the execution of your &quot;real&quot; callback. To do this, create an <a href="../base/base.html#Base.AsyncCondition"><code>AsyncCondition</code></a> object and <a href="../base/parallel.html#Base.wait"><code>wait</code></a> on it:</p><pre><code class="language-julia hljs">cond = Base.AsyncCondition()
wait(cond)</code></pre><p>The callback you pass to C should only execute a <a href="../base/c.html#ccall"><code>ccall</code></a> to <code>:uv_async_send</code>, passing <code>cond.handle</code> as the argument, taking care to avoid any allocations or other interactions with the Julia runtime.</p><p>Note that events may be coalesced, so multiple calls to <code>uv_async_send</code> may result in a single wakeup notification to the condition.</p><h2 id="More-About-Callbacks"><a class="docs-heading-anchor" href="#More-About-Callbacks">More About Callbacks</a><a id="More-About-Callbacks-1"></a><a class="docs-heading-anchor-permalink" href="#More-About-Callbacks" title="Permalink"></a></h2><p>For more details on how to pass callbacks to C libraries, see this <a href="https://julialang.org/blog/2013/05/callback">blog post</a>.</p><h2 id="C"><a class="docs-heading-anchor" href="#C">C++</a><a id="C-1"></a><a class="docs-heading-anchor-permalink" href="#C" title="Permalink"></a></h2><p>For tools to create C++ bindings, see the <a href="https://github.com/JuliaInterop/CxxWrap.jl">CxxWrap</a> package.</p><section class="footnotes is-size-7"><ul><li class="footnote" id="footnote-1"><a class="tag is-link" href="#citeref-1">1</a>Non-library function calls in both C and Julia can be inlined and thus may have even less overhead than calls to shared library functions. The point above is that the cost of actually doing foreign function call is about the same as doing a call in either native language.</li><li class="footnote" id="footnote-2"><a class="tag is-link" href="#citeref-2">2</a>The <a href="https://github.com/ihnorton/Clang.jl">Clang package</a> can be used to auto-generate Julia code from a C header file.</li></ul></section></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="running-external-programs.html">« Running External Programs</a><a class="docs-footer-nextpage" href="handling-operating-system-variation.html">Handling Operating System Variation »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
