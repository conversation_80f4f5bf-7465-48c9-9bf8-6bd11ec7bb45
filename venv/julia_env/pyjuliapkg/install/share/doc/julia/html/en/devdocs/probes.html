<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Instrumenting Julia with DTrace, and bpftrace · The Julia Language</title><meta name="title" content="Instrumenting Julia with DTrace, and bpftrace · The Julia Language"/><meta property="og:title" content="Instrumenting Julia with DTrace, and bpftrace · The Julia Language"/><meta property="twitter:title" content="Instrumenting Julia with DTrace, and bpftrace · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="ast.html">Julia ASTs</a></li><li><a class="tocitem" href="types.html">More about types</a></li><li><a class="tocitem" href="object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="functions.html">Julia Functions</a></li><li><a class="tocitem" href="cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="subarrays.html">SubArrays</a></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox" checked/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li class="is-active"><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a><ul class="internal"><li><a class="tocitem" href="#Enabling-support"><span>Enabling support</span></a></li><li><a class="tocitem" href="#Adding-probes-in-libjulia"><span>Adding probes in libjulia</span></a></li><li><a class="tocitem" href="#Available-probes"><span>Available probes</span></a></li><li><a class="tocitem" href="#Probe-usage-examples"><span>Probe usage examples</span></a></li><li><a class="tocitem" href="#Notes-on-using-bpftrace"><span>Notes on using <code>bpftrace</code></span></a></li><li><a class="tocitem" href="#Useful-references:"><span>Useful references:</span></a></li></ul></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Developing/debugging Julia&#39;s C code</a></li><li class="is-active"><a href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/probes.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Instrumenting-Julia-with-DTrace,-and-bpftrace"><a class="docs-heading-anchor" href="#Instrumenting-Julia-with-DTrace,-and-bpftrace">Instrumenting Julia with DTrace, and bpftrace</a><a id="Instrumenting-Julia-with-DTrace,-and-bpftrace-1"></a><a class="docs-heading-anchor-permalink" href="#Instrumenting-Julia-with-DTrace,-and-bpftrace" title="Permalink"></a></h1><p>DTrace and bpftrace are tools that enable lightweight instrumentation of processes. You can turn the instrumentation on and off while the process is running, and with instrumentation off the overhead is minimal.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>Support for probes was added in Julia 1.8</p></div></div><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This documentation has been written from a Linux perspective, most of this should hold on Mac OS/Darwin and FreeBSD.</p></div></div><h2 id="Enabling-support"><a class="docs-heading-anchor" href="#Enabling-support">Enabling support</a><a id="Enabling-support-1"></a><a class="docs-heading-anchor-permalink" href="#Enabling-support" title="Permalink"></a></h2><p>On Linux install the <code>systemtap</code> package that has a version of <code>dtrace</code> and create a <code>Make.user</code> file containing</p><pre><code class="nohighlight hljs">WITH_DTRACE=1</code></pre><p>to enable USDT probes.</p><h3 id="Verifying"><a class="docs-heading-anchor" href="#Verifying">Verifying</a><a id="Verifying-1"></a><a class="docs-heading-anchor-permalink" href="#Verifying" title="Permalink"></a></h3><pre><code class="nohighlight hljs">&gt; readelf -n usr/lib/libjulia-internal.so.1

Displaying notes found in: .note.gnu.build-id
  Owner                Data size  Description
  GNU                  0x00000014 NT_GNU_BUILD_ID (unique build ID bitstring)
    Build ID: 57161002f35548772a87418d2385c284ceb3ead8

Displaying notes found in: .note.stapsdt
  Owner                Data size  Description
  stapsdt              0x00000029 NT_STAPSDT (SystemTap probe descriptors)
    Provider: julia
    Name: gc__begin
    Location: 0x000000000013213e, Base: 0x00000000002bb4da, Semaphore: 0x0000000000346cac
    Arguments:
  stapsdt              0x00000032 NT_STAPSDT (SystemTap probe descriptors)
    Provider: julia
    Name: gc__stop_the_world
    Location: 0x0000000000132144, Base: 0x00000000002bb4da, Semaphore: 0x0000000000346cae
    Arguments:
  stapsdt              0x00000027 NT_STAPSDT (SystemTap probe descriptors)
    Provider: julia
    Name: gc__end
    Location: 0x000000000013214a, Base: 0x00000000002bb4da, Semaphore: 0x0000000000346cb0
    Arguments:
  stapsdt              0x0000002d NT_STAPSDT (SystemTap probe descriptors)
    Provider: julia
    Name: gc__finalizer
    Location: 0x0000000000132150, Base: 0x00000000002bb4da, Semaphore: 0x0000000000346cb2
    Arguments:</code></pre><h2 id="Adding-probes-in-libjulia"><a class="docs-heading-anchor" href="#Adding-probes-in-libjulia">Adding probes in libjulia</a><a id="Adding-probes-in-libjulia-1"></a><a class="docs-heading-anchor-permalink" href="#Adding-probes-in-libjulia" title="Permalink"></a></h2><p>Probes are declared in dtraces format in the file <code>src/uprobes.d</code>. The generated header file is included in <code>src/julia_internal.h</code> and if you add probes you should provide a noop implementation there.</p><p>The header will contain a semaphore <code>*_ENABLED</code> and the actual call to the probe. If the probe arguments are expensive to compute you should first check if the probe is enabled and then compute the arguments and call the probe.</p><pre><code class="language-c hljs">  if (JL_PROBE_{PROBE}_ENABLED())
    auto expensive_arg = ...;
    JL_PROBE_{PROBE}(expensive_arg);</code></pre><p>If your probe has no arguments it is preferred to not include the semaphore check. With USDT probes enabled the cost of a semaphore is a memory load, irrespective of the fact that the probe is enabled or not.</p><pre><code class="language-c hljs">#define JL_PROBE_GC_BEGIN_ENABLED() __builtin_expect (julia_gc__begin_semaphore, 0)
__extension__ extern unsigned short julia_gc__begin_semaphore __attribute__ ((unused)) __attribute__ ((section (&quot;.probes&quot;)));</code></pre><p>Whereas the probe itself is a noop sled that will be patched to a trampoline to the probe handler.</p><h2 id="Available-probes"><a class="docs-heading-anchor" href="#Available-probes">Available probes</a><a id="Available-probes-1"></a><a class="docs-heading-anchor-permalink" href="#Available-probes" title="Permalink"></a></h2><h3 id="GC-probes"><a class="docs-heading-anchor" href="#GC-probes">GC probes</a><a id="GC-probes-1"></a><a class="docs-heading-anchor-permalink" href="#GC-probes" title="Permalink"></a></h3><ol><li><code>julia:gc__begin</code>: GC begins running on one thread and triggers stop-the-world.</li><li><code>julia:gc__stop_the_world</code>: All threads have reached a safepoint and GC runs.</li><li><code>julia:gc__mark__begin</code>: Beginning the mark phase</li><li><code>julia:gc__mark_end(scanned_bytes, perm_scanned)</code>: Mark phase ended</li><li><code>julia:gc__sweep_begin(full)</code>: Starting sweep</li><li><code>julia:gc__sweep_end</code>: Sweep phase finished</li><li><code>julia:gc__end</code>: GC is finished, other threads continue work</li><li><code>julia:gc__finalizer</code>: Initial GC thread has finished running finalizers</li></ol><h3 id="Task-runtime-probes"><a class="docs-heading-anchor" href="#Task-runtime-probes">Task runtime probes</a><a id="Task-runtime-probes-1"></a><a class="docs-heading-anchor-permalink" href="#Task-runtime-probes" title="Permalink"></a></h3><ol><li><code>julia:rt__run__task(task)</code>: Switching to task <code>task</code> on current thread.</li><li><code>julia:rt__pause__task(task)</code>: Switching from task <code>task</code> on current thread.</li><li><code>julia:rt__new__task(parent, child)</code>: Task <code>parent</code> created task <code>child</code> on current thread.</li><li><code>julia:rt__start__task(task)</code>: Task <code>task</code> started for the first time with a new stack.</li><li><code>julia:rt__finish__task(task)</code>: Task <code>task</code> finished and will no longer execute.</li><li><code>julia:rt__start__process__events(task)</code>: Task <code>task</code> started processing libuv events.</li><li><code>julia:rt__finish__process__events(task)</code>: Task <code>task</code> finished processing libuv events.</li></ol><h3 id="Task-queue-probes"><a class="docs-heading-anchor" href="#Task-queue-probes">Task queue probes</a><a id="Task-queue-probes-1"></a><a class="docs-heading-anchor-permalink" href="#Task-queue-probes" title="Permalink"></a></h3><ol><li><code>julia:rt__taskq__insert(ptls, task)</code>: Thread <code>ptls</code> attempted to insert <code>task</code> into a PARTR multiq.</li><li><code>julia:rt__taskq__get(ptls, task)</code>: Thread <code>ptls</code> popped <code>task</code> from a PARTR multiq.</li></ol><h3 id="Thread-sleep/wake-probes"><a class="docs-heading-anchor" href="#Thread-sleep/wake-probes">Thread sleep/wake probes</a><a id="Thread-sleep/wake-probes-1"></a><a class="docs-heading-anchor-permalink" href="#Thread-sleep/wake-probes" title="Permalink"></a></h3><ol><li><code>julia:rt__sleep__check__wake(ptls, old_state)</code>: Thread (PTLS <code>ptls</code>) waking up, previously in state <code>old_state</code>.</li><li><code>julia:rt__sleep__check__wakeup(ptls)</code>: Thread (PTLS <code>ptls</code>) woke itself up.</li><li><code>julia:rt__sleep__check__sleep(ptls)</code>: Thread (PTLS <code>ptls</code>) is attempting to sleep.</li><li><code>julia:rt__sleep__check__taskq__wake(ptls)</code>: Thread (PTLS <code>ptls</code>) fails to sleep due to tasks in PARTR multiq.</li><li><code>julia:rt__sleep__check__task__wake(ptls)</code>: Thread (PTLS <code>ptls</code>) fails to sleep due to tasks in Base workqueue.</li><li><code>julia:rt__sleep__check__uv__wake(ptls)</code>: Thread (PTLS <code>ptls</code>) fails to sleep due to libuv wakeup.</li></ol><h2 id="Probe-usage-examples"><a class="docs-heading-anchor" href="#Probe-usage-examples">Probe usage examples</a><a id="Probe-usage-examples-1"></a><a class="docs-heading-anchor-permalink" href="#Probe-usage-examples" title="Permalink"></a></h2><h3 id="GC-stop-the-world-latency"><a class="docs-heading-anchor" href="#GC-stop-the-world-latency">GC stop-the-world latency</a><a id="GC-stop-the-world-latency-1"></a><a class="docs-heading-anchor-permalink" href="#GC-stop-the-world-latency" title="Permalink"></a></h3><p>An example <code>bpftrace</code> script is given in <code>contrib/gc_stop_the_world_latency.bt</code> and it creates a histogram of the latency for all threads to reach a safepoint.</p><p>Running this Julia code, with <code>julia -t 2</code></p><pre><code class="nohighlight hljs">using Base.Threads

fib(x) = x &lt;= 1 ? 1 : fib(x-1) + fib(x-2)

beaver = @spawn begin
    while true
        fib(30)
        # A manual safepoint is necessary since otherwise this loop
        # may never yield to GC.
        GC.safepoint()
    end
end

allocator = @spawn begin
    while true
        zeros(1024)
    end
end

wait(allocator)</code></pre><p>and in a second terminal</p><pre><code class="nohighlight hljs">&gt; sudo contrib/bpftrace/gc_stop_the_world_latency.bt
Attaching 4 probes...
Tracing Julia GC Stop-The-World Latency... Hit Ctrl-C to end.
^C


@usecs[1743412]:
[4, 8)               971 |@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@|
[8, 16)              837 |@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@        |
[16, 32)             129 |@@@@@@                                              |
[32, 64)              10 |                                                    |
[64, 128)              1 |                                                    |</code></pre><p>We can see the latency distribution of the stop-the-world phase in the executed Julia process.</p><h3 id="Task-spawn-monitor"><a class="docs-heading-anchor" href="#Task-spawn-monitor">Task spawn monitor</a><a id="Task-spawn-monitor-1"></a><a class="docs-heading-anchor-permalink" href="#Task-spawn-monitor" title="Permalink"></a></h3><p>It&#39;s sometimes useful to know when a task is spawning other tasks. This is very easy to see with <code>rt__new__task</code>. The first argument to the probe, <code>parent</code>, is the existing task which is creating a new task. This means that if you know the address of the task you want to monitor, you can easily just look at the tasks that that specific task spawned. Let&#39;s see how to do this; first let&#39;s start a Julia session and get the PID and REPL&#39;s task address:</p><pre><code class="nohighlight hljs">&gt; julia
               _
   _       _ _(_)_     |  Documentation: https://docs.julialang.org
  (_)     | (_) (_)    |
   _ _   _| |_  __ _   |  Type &quot;?&quot; for help, &quot;]?&quot; for Pkg help.
  | | | | | | |/ _` |  |
  | | |_| | | | (_| |  |  Version 1.6.2 (2021-07-14)
 _/ |\__&#39;_|_|_|\__&#39;_|  |  Official https://julialang.org/ release
|__/                   |

1&gt; getpid()
997825

2&gt; current_task()
Task (runnable) @0x00007f524d088010</code></pre><p>Now we can start <code>bpftrace</code> and have it monitor <code>rt__new__task</code> for <em>only</em> this parent:</p><p><code>sudo bpftrace -p 997825 -e &#39;usdt:usr/lib/libjulia-internal.so:julia:rt__new__task /arg0==0x00007f524d088010/{ printf(&quot;Task: %x\n&quot;, arg0); }&#39;</code></p><p>(Note that in the above, <code>arg0</code> is the first argument, <code>parent</code>).</p><p>And if we spawn a single task:</p><p><code>@async 1+1</code></p><p>we see this task being created:</p><p><code>Task: 4d088010</code></p><p>However, if we spawn a bunch of tasks from that newly-spawned task:</p><pre><code class="language-julia hljs">@async for i in 1:10
   @async 1+1
end</code></pre><p>we still only see one task from <code>bpftrace</code>:</p><p><code>Task: 4d088010</code></p><p>and it&#39;s still the same task we were monitoring! Of course, we can remove this filter to see <em>all</em> newly-created tasks just as easily:</p><p><code>sudo bpftrace -p 997825 -e &#39;usdt:usr/lib/libjulia-internal.so:julia:rt__new__task { printf(&quot;Task: %x\n&quot;, arg0); }&#39;</code></p><pre><code class="nohighlight hljs">Task: 4d088010
Task: 4dc4e290
Task: 4dc4e290
Task: 4dc4e290
Task: 4dc4e290
Task: 4dc4e290
Task: 4dc4e290
Task: 4dc4e290
Task: 4dc4e290
Task: 4dc4e290
Task: 4dc4e290</code></pre><p>We can see our root task, and the newly-spawned task as the parent of the ten even newer tasks.</p><h3 id="Thundering-herd-detection"><a class="docs-heading-anchor" href="#Thundering-herd-detection">Thundering herd detection</a><a id="Thundering-herd-detection-1"></a><a class="docs-heading-anchor-permalink" href="#Thundering-herd-detection" title="Permalink"></a></h3><p>Task runtimes can often suffer from the &quot;thundering herd&quot; problem: when some work is added to a quiet task runtime, all threads may be woken up from their slumber, even if there isn&#39;t enough work for each thread to process. This can cause extra latency and CPU cycles while all threads awaken (and simultaneously go back to sleep, not finding any work to execute).</p><p>We can see this problem illustrated with <code>bpftrace</code> quite easily. First, in one terminal we start Julia with multiple threads (6 in this example), and get the PID of that process:</p><pre><code class="nohighlight hljs">&gt; julia -t 6
               _
   _       _ _(_)_     |  Documentation: https://docs.julialang.org
  (_)     | (_) (_)    |
   _ _   _| |_  __ _   |  Type &quot;?&quot; for help, &quot;]?&quot; for Pkg help.
  | | | | | | |/ _` |  |
  | | |_| | | | (_| |  |  Version 1.6.2 (2021-07-14)
 _/ |\__&#39;_|_|_|\__&#39;_|  |  Official https://julialang.org/ release
|__/                   |

1&gt; getpid()
997825</code></pre><p>And in another terminal we start <code>bpftrace</code> monitoring our process, specifically probing the <code>rt__sleep__check__wake</code> hook:</p><p><code>sudo bpftrace -p 997825 -e &#39;usdt:usr/lib/libjulia-internal.so:julia:rt__sleep__check__wake { printf(&quot;Thread wake up! %x\n&quot;, arg0); }&#39;</code></p><p>Now, we create and execute a single task in Julia:</p><p><code>Threads.@spawn 1+1</code></p><p>And in <code>bpftrace</code> we see printed out something like:</p><pre><code class="nohighlight hljs">Thread wake up! 3f926100
Thread wake up! 3ebd5140
Thread wake up! 3f876130
Thread wake up! 3e2711a0
Thread wake up! 3e312190</code></pre><p>Even though we only spawned a single task (which only one thread could process at a time), we woke up all of our other threads! In the future, a smarter task runtime might only wake up a single thread (or none at all; the spawning thread could execute this task!), and we should see this behavior go away.</p><h3 id="Task-Monitor-with-BPFnative.jl"><a class="docs-heading-anchor" href="#Task-Monitor-with-BPFnative.jl">Task Monitor with BPFnative.jl</a><a id="Task-Monitor-with-BPFnative.jl-1"></a><a class="docs-heading-anchor-permalink" href="#Task-Monitor-with-BPFnative.jl" title="Permalink"></a></h3><p>BPFnative.jl is able to attach to USDT probe points just like <code>bpftrace</code>. There is a demo available for monitoring the task runtime, GC, and thread sleep/wake transitions <a href="https://github.com/jpsamaroo/BPFnative.jl/blob/master/examples/task-runtime.jl">here</a>.</p><h2 id="Notes-on-using-bpftrace"><a class="docs-heading-anchor" href="#Notes-on-using-bpftrace">Notes on using <code>bpftrace</code></a><a id="Notes-on-using-bpftrace-1"></a><a class="docs-heading-anchor-permalink" href="#Notes-on-using-bpftrace" title="Permalink"></a></h2><p>An example probe in the bpftrace format looks like:</p><pre><code class="nohighlight hljs">usdt:usr/lib/libjulia-internal.so:julia:gc__begin
{
  @start[pid] = nsecs;
}</code></pre><p>The probe declaration takes the kind <code>usdt</code>, then either the path to the library or the PID, the provider name <code>julia</code> and the probe name <code>gc__begin</code>. Note that I am using a relative path to the <code>libjulia-internal.so</code>, but this might need to be an absolute path on a production system.</p><h2 id="Useful-references:"><a class="docs-heading-anchor" href="#Useful-references:">Useful references:</a><a id="Useful-references:-1"></a><a class="docs-heading-anchor-permalink" href="#Useful-references:" title="Permalink"></a></h2><ul><li><a href="https://jvns.ca/blog/2017/07/05/linux-tracing-systems">Julia Evans blog on Linux tracing systems</a></li><li><a href="https://lwn.net/Articles/753601/">LWN article on USDT and BPF</a></li><li><a href="https://sourceware.org/gdb/onlinedocs/gdb/Static-Probe-Points.html">GDB support for probes</a></li><li><a href="https://www.brendangregg.com/linuxperf.html">Brendan Gregg – Linux Performance</a></li></ul></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="sanitizers.html">« Sanitizer support</a><a class="docs-footer-nextpage" href="build/build.html">Building Julia (Detailed) »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
