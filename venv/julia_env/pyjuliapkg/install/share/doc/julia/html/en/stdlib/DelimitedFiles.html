<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Delimited Files · The Julia Language</title><meta name="title" content="Delimited Files · The Julia Language"/><meta property="og:title" content="Delimited Files · The Julia Language"/><meta property="twitter:title" content="Delimited Files · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li class="is-active"><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="DelimitedFiles.html">Delimited Files</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="DelimitedFiles.html">Delimited Files</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaData/DelimitedFiles.jl/blob/master/docs/src/index.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Delimited-Files"><a class="docs-heading-anchor" href="#Delimited-Files">Delimited Files</a><a id="Delimited-Files-1"></a><a class="docs-heading-anchor-permalink" href="#Delimited-Files" title="Permalink"></a></h1><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="DelimitedFiles.readdlm-Tuple{Any, AbstractChar, Type, AbstractChar}" href="#DelimitedFiles.readdlm-Tuple{Any, AbstractChar, Type, AbstractChar}"><code>DelimitedFiles.readdlm</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">readdlm(source, delim::AbstractChar, T::Type, eol::AbstractChar; header=false, skipstart=0, skipblanks=true, use_mmap, quotes=true, dims, comments=false, comment_char=&#39;#&#39;)</code></pre><p>Read a matrix from the source where each line (separated by <code>eol</code>) gives one row, with elements separated by the given delimiter. The source can be a text file, stream or byte array. Memory mapped files can be used by passing the byte array representation of the mapped segment as source.</p><p>If <code>T</code> is a numeric type, the result is an array of that type, with any non-numeric elements as <code>NaN</code> for floating-point types, or zero. Other useful values of <code>T</code> include <code>String</code>, <code>AbstractString</code>, and <code>Any</code>.</p><p>If <code>header</code> is <code>true</code>, the first row of data will be read as header and the tuple <code>(data_cells, header_cells)</code> is returned instead of only <code>data_cells</code>.</p><p>Specifying <code>skipstart</code> will ignore the corresponding number of initial lines from the input.</p><p>If <code>skipblanks</code> is <code>true</code>, blank lines in the input will be ignored.</p><p>If <code>use_mmap</code> is <code>true</code>, the file specified by <code>source</code> is memory mapped for potential speedups if the file is large. Default is <code>false</code>. On a Windows filesystem, <code>use_mmap</code> should not be set to <code>true</code> unless the file is only read once and is also not written to. Some edge cases exist where an OS is Unix-like but the filesystem is Windows-like.</p><p>If <code>quotes</code> is <code>true</code>, columns enclosed within double-quote (&quot;) characters are allowed to contain new lines and column delimiters. Double-quote characters within a quoted field must be escaped with another double-quote.  Specifying <code>dims</code> as a tuple of the expected rows and columns (including header, if any) may speed up reading of large files.  If <code>comments</code> is <code>true</code>, lines beginning with <code>comment_char</code> and text following <code>comment_char</code> in any line are ignored.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; using DelimitedFiles

julia&gt; x = [1; 2; 3; 4];

julia&gt; y = [5; 6; 7; 8];

julia&gt; open(&quot;delim_file.txt&quot;, &quot;w&quot;) do io
           writedlm(io, [x y])
       end

julia&gt; readdlm(&quot;delim_file.txt&quot;, &#39;\t&#39;, Int, &#39;\n&#39;)
4×2 Matrix{Int64}:
 1  5
 2  6
 3  7
 4  8

julia&gt; rm(&quot;delim_file.txt&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaData/DelimitedFiles.jl/blob/db79c842f95f55b1f8d8037c0d3363ab21cd3b90/src/DelimitedFiles.jl#L173-L225">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="DelimitedFiles.readdlm-Tuple{Any, AbstractChar, AbstractChar}" href="#DelimitedFiles.readdlm-Tuple{Any, AbstractChar, AbstractChar}"><code>DelimitedFiles.readdlm</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">readdlm(source, delim::AbstractChar, eol::AbstractChar; options...)</code></pre><p>If all data is numeric, the result will be a numeric array. If some elements cannot be parsed as numbers, a heterogeneous array of numbers and strings is returned.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaData/DelimitedFiles.jl/blob/db79c842f95f55b1f8d8037c0d3363ab21cd3b90/src/DelimitedFiles.jl#L164-L169">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="DelimitedFiles.readdlm-Tuple{Any, AbstractChar, Type}" href="#DelimitedFiles.readdlm-Tuple{Any, AbstractChar, Type}"><code>DelimitedFiles.readdlm</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">readdlm(source, delim::AbstractChar, T::Type; options...)</code></pre><p>The end of line delimiter is taken as <code>\n</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; using DelimitedFiles

julia&gt; x = [1; 2; 3; 4];

julia&gt; y = [1.1; 2.2; 3.3; 4.4];

julia&gt; open(&quot;delim_file.txt&quot;, &quot;w&quot;) do io
           writedlm(io, [x y], &#39;,&#39;)
       end;

julia&gt; readdlm(&quot;delim_file.txt&quot;, &#39;,&#39;, Float64)
4×2 Matrix{Float64}:
 1.0  1.1
 2.0  2.2
 3.0  3.3
 4.0  4.4

julia&gt; rm(&quot;delim_file.txt&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaData/DelimitedFiles.jl/blob/db79c842f95f55b1f8d8037c0d3363ab21cd3b90/src/DelimitedFiles.jl#L59-L85">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="DelimitedFiles.readdlm-Tuple{Any, AbstractChar}" href="#DelimitedFiles.readdlm-Tuple{Any, AbstractChar}"><code>DelimitedFiles.readdlm</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">readdlm(source, delim::AbstractChar; options...)</code></pre><p>The end of line delimiter is taken as <code>\n</code>. If all data is numeric, the result will be a numeric array. If some elements cannot be parsed as numbers, a heterogeneous array of numbers and strings is returned.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; using DelimitedFiles

julia&gt; x = [1; 2; 3; 4];

julia&gt; y = [1.1; 2.2; 3.3; 4.4];

julia&gt; open(&quot;delim_file.txt&quot;, &quot;w&quot;) do io
           writedlm(io, [x y], &#39;,&#39;)
       end;

julia&gt; readdlm(&quot;delim_file.txt&quot;, &#39;,&#39;)
4×2 Matrix{Float64}:
 1.0  1.1
 2.0  2.2
 3.0  3.3
 4.0  4.4

julia&gt; z = [&quot;a&quot;; &quot;b&quot;; &quot;c&quot;; &quot;d&quot;];

julia&gt; open(&quot;delim_file.txt&quot;, &quot;w&quot;) do io
           writedlm(io, [x z], &#39;,&#39;)
       end;

julia&gt; readdlm(&quot;delim_file.txt&quot;, &#39;,&#39;)
4×2 Matrix{Any}:
 1  &quot;a&quot;
 2  &quot;b&quot;
 3  &quot;c&quot;
 4  &quot;d&quot;

julia&gt; rm(&quot;delim_file.txt&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaData/DelimitedFiles.jl/blob/db79c842f95f55b1f8d8037c0d3363ab21cd3b90/src/DelimitedFiles.jl#L120-L161">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="DelimitedFiles.readdlm-Tuple{Any, Type}" href="#DelimitedFiles.readdlm-Tuple{Any, Type}"><code>DelimitedFiles.readdlm</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">readdlm(source, T::Type; options...)</code></pre><p>The columns are assumed to be separated by one or more whitespaces. The end of line delimiter is taken as <code>\n</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; using DelimitedFiles

julia&gt; x = [1; 2; 3; 4];

julia&gt; y = [5; 6; 7; 8];

julia&gt; open(&quot;delim_file.txt&quot;, &quot;w&quot;) do io
           writedlm(io, [x y])
       end;

julia&gt; readdlm(&quot;delim_file.txt&quot;, Int64)
4×2 Matrix{Int64}:
 1  5
 2  6
 3  7
 4  8

julia&gt; readdlm(&quot;delim_file.txt&quot;, Float64)
4×2 Matrix{Float64}:
 1.0  5.0
 2.0  6.0
 3.0  7.0
 4.0  8.0

julia&gt; rm(&quot;delim_file.txt&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaData/DelimitedFiles.jl/blob/db79c842f95f55b1f8d8037c0d3363ab21cd3b90/src/DelimitedFiles.jl#L22-L56">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="DelimitedFiles.readdlm-Tuple{Any}" href="#DelimitedFiles.readdlm-Tuple{Any}"><code>DelimitedFiles.readdlm</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">readdlm(source; options...)</code></pre><p>The columns are assumed to be separated by one or more whitespaces. The end of line delimiter is taken as <code>\n</code>. If all data is numeric, the result will be a numeric array. If some elements cannot be parsed as numbers, a heterogeneous array of numbers and strings is returned.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; using DelimitedFiles

julia&gt; x = [1; 2; 3; 4];

julia&gt; y = [&quot;a&quot;; &quot;b&quot;; &quot;c&quot;; &quot;d&quot;];

julia&gt; open(&quot;delim_file.txt&quot;, &quot;w&quot;) do io
           writedlm(io, [x y])
       end;

julia&gt; readdlm(&quot;delim_file.txt&quot;)
4×2 Matrix{Any}:
 1  &quot;a&quot;
 2  &quot;b&quot;
 3  &quot;c&quot;
 4  &quot;d&quot;

julia&gt; rm(&quot;delim_file.txt&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaData/DelimitedFiles.jl/blob/db79c842f95f55b1f8d8037c0d3363ab21cd3b90/src/DelimitedFiles.jl#L88-L117">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="DelimitedFiles.writedlm" href="#DelimitedFiles.writedlm"><code>DelimitedFiles.writedlm</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">writedlm(f, A, delim=&#39;\t&#39;; opts)</code></pre><p>Write <code>A</code> (a vector, matrix, or an iterable collection of iterable rows) as text to <code>f</code> (either a filename string or an <code>IO</code> stream) using the given delimiter <code>delim</code> (which defaults to tab, but can be any printable Julia object, typically a <code>Char</code> or <code>AbstractString</code>).</p><p>For example, two vectors <code>x</code> and <code>y</code> of the same length can be written as two columns of tab-delimited text to <code>f</code> by either <code>writedlm(f, [x y])</code> or by <code>writedlm(f, zip(x, y))</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; using DelimitedFiles

julia&gt; x = [1; 2; 3; 4];

julia&gt; y = [5; 6; 7; 8];

julia&gt; open(&quot;delim_file.txt&quot;, &quot;w&quot;) do io
           writedlm(io, [x y])
       end

julia&gt; readdlm(&quot;delim_file.txt&quot;, &#39;\t&#39;, Int, &#39;\n&#39;)
4×2 Matrix{Int64}:
 1  5
 2  6
 3  7
 4  8

julia&gt; rm(&quot;delim_file.txt&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaData/DelimitedFiles.jl/blob/db79c842f95f55b1f8d8037c0d3363ab21cd3b90/src/DelimitedFiles.jl#L799-L831">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="Dates.html">« Dates</a><a class="docs-footer-nextpage" href="Distributed.html">Distributed Computing »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
