<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Noteworthy Differences from other Languages · The Julia Language</title><meta name="title" content="Noteworthy Differences from other Languages · The Julia Language"/><meta property="og:title" content="Noteworthy Differences from other Languages · The Julia Language"/><meta property="twitter:title" content="Noteworthy Differences from other Languages · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li class="is-active"><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a><ul class="internal"><li><a class="tocitem" href="#Noteworthy-differences-from-MATLAB"><span>Noteworthy differences from MATLAB</span></a></li><li><a class="tocitem" href="#Noteworthy-differences-from-R"><span>Noteworthy differences from R</span></a></li><li><a class="tocitem" href="#Noteworthy-differences-from-Python"><span>Noteworthy differences from Python</span></a></li><li><a class="tocitem" href="#Noteworthy-differences-from-C/C"><span>Noteworthy differences from C/C++</span></a></li><li><a class="tocitem" href="#Noteworthy-differences-from-Common-Lisp"><span>Noteworthy differences from Common Lisp</span></a></li></ul></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/noteworthy-differences.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Noteworthy-Differences-from-other-Languages"><a class="docs-heading-anchor" href="#Noteworthy-Differences-from-other-Languages">Noteworthy Differences from other Languages</a><a id="Noteworthy-Differences-from-other-Languages-1"></a><a class="docs-heading-anchor-permalink" href="#Noteworthy-Differences-from-other-Languages" title="Permalink"></a></h1><h2 id="Noteworthy-differences-from-MATLAB"><a class="docs-heading-anchor" href="#Noteworthy-differences-from-MATLAB">Noteworthy differences from MATLAB</a><a id="Noteworthy-differences-from-MATLAB-1"></a><a class="docs-heading-anchor-permalink" href="#Noteworthy-differences-from-MATLAB" title="Permalink"></a></h2><p>Although MATLAB users may find Julia&#39;s syntax familiar, Julia is not a MATLAB clone. There are major syntactic and functional differences. The following are some noteworthy differences that may trip up Julia users accustomed to MATLAB:</p><ul><li>Julia arrays are indexed with square brackets, <code>A[i,j]</code>.</li><li>Julia arrays are not copied when assigned to another variable. After <code>A = B</code>, changing elements of <code>B</code> will modify <code>A</code> as well. To avoid this, use <code>A = copy(B)</code>.</li><li>Julia values are not copied when passed to a function. If a function modifies an array, the changes will be visible in the caller.</li><li>Julia does not automatically grow arrays in an assignment statement. Whereas in MATLAB <code>a(4) = 3.2</code> can create the array <code>a = [0 0 0 3.2]</code> and <code>a(5) = 7</code> can grow it into <code>a = [0 0 0 3.2 7]</code>, the corresponding Julia statement <code>a[5] = 7</code> throws an error if the length of <code>a</code> is less than 5 or if this statement is the first use of the identifier <code>a</code>. Julia has <a href="../base/collections.html#Base.push!"><code>push!</code></a> and <a href="../base/collections.html#Base.append!"><code>append!</code></a>, which grow <code>Vector</code>s much more efficiently than MATLAB&#39;s <code>a(end+1) = val</code>.</li><li>The imaginary unit <code>sqrt(-1)</code> is represented in Julia as <a href="../base/numbers.html#Base.im"><code>im</code></a>, not <code>i</code> or <code>j</code> as in MATLAB.</li><li>In Julia, literal numbers without a decimal point (such as <code>42</code>) create integers instead of floating point numbers. As a result, some operations can throw a domain error if they expect a float; for example, <code>julia&gt; a = -1; 2^a</code> throws a domain error, as the result is not an integer (see <a href="faq.html#faq-domain-errors">the FAQ entry on domain errors</a> for details).</li><li>In Julia, multiple values are returned and assigned as tuples, e.g. <code>(a, b) = (1, 2)</code> or <code>a, b = 1, 2</code>. MATLAB&#39;s <code>nargout</code>, which is often used in MATLAB to do optional work based on the number of returned values, does not exist in Julia. Instead, users can use optional and keyword arguments to achieve similar capabilities.</li><li>Julia has true one-dimensional arrays. Column vectors are of size <code>N</code>, not <code>Nx1</code>. For example, <a href="../stdlib/Random.html#Base.rand"><code>rand(N)</code></a> makes a 1-dimensional array.</li><li>In Julia, <code>[x,y,z]</code> will always construct a 3-element array containing <code>x</code>, <code>y</code> and <code>z</code>.<ul><li>To concatenate in the first (&quot;vertical&quot;) dimension use either <a href="../base/arrays.html#Base.vcat"><code>vcat(x,y,z)</code></a> or separate with semicolons (<code>[x; y; z]</code>).</li><li>To concatenate in the second (&quot;horizontal&quot;) dimension use either <a href="../base/arrays.html#Base.hcat"><code>hcat(x,y,z)</code></a> or separate with spaces (<code>[x y z]</code>).</li><li>To construct block matrices (concatenating in the first two dimensions), use either <a href="../base/arrays.html#Base.hvcat"><code>hvcat</code></a> or combine spaces and semicolons (<code>[a b; c d]</code>).</li></ul></li><li>In Julia, <code>a:b</code> and <code>a:b:c</code> construct <code>AbstractRange</code> objects. To construct a full vector like in MATLAB, use <a href="../base/collections.html#Base.collect-Tuple{Any}"><code>collect(a:b)</code></a>. Generally, there is no need to call <code>collect</code> though. An <code>AbstractRange</code> object will act like a normal array in most cases but is more efficient because it lazily computes its values. This pattern of creating specialized objects instead of full arrays is used frequently, and is also seen in functions such as <a href="../base/math.html#Base.range"><code>range</code></a>, or with iterators such as <code>enumerate</code>, and <code>zip</code>. The special objects can mostly be used as if they were normal arrays.</li><li>Functions in Julia return values from their last expression or the <code>return</code> keyword instead of listing the names of variables to return in the function definition (see <a href="functions.html#The-return-Keyword">The return Keyword</a> for details).</li><li>A Julia script may contain any number of functions, and all definitions will be externally visible when the file is loaded. Function definitions can be loaded from files outside the current working directory.</li><li>In Julia, reductions such as <a href="../base/collections.html#Base.sum"><code>sum</code></a>, <a href="../base/collections.html#Base.prod"><code>prod</code></a>, and <a href="../base/collections.html#Base.maximum"><code>maximum</code></a> are performed over every element of an array when called with a single argument, as in <code>sum(A)</code>, even if <code>A</code> has more than one dimension.</li><li>In Julia, parentheses must be used to call a function with zero arguments, like in <a href="../stdlib/Random.html#Base.rand"><code>rand()</code></a>.</li><li>Julia discourages the use of semicolons to end statements. The results of statements are not automatically printed (except at the interactive prompt), and lines of code do not need to end with semicolons. <a href="../base/io-network.html#Base.println"><code>println</code></a> or <a href="../stdlib/Printf.html#Printf.@printf"><code>@printf</code></a> can be used to print specific output.</li><li>In Julia, if <code>A</code> and <code>B</code> are arrays, logical comparison operations like <code>A == B</code> do not return an array of booleans. Instead, use <code>A .== B</code>, and similarly for the other boolean operators like <a href="../base/math.html#Base.:&lt;"><code>&lt;</code></a>, <a href="../base/math.html#Base.:&gt;"><code>&gt;</code></a>.</li><li>In Julia, the operators <a href="../base/math.html#Base.:&amp;"><code>&amp;</code></a>, <a href="../base/math.html#Base.:|"><code>|</code></a>, and <a href="../base/math.html#Base.xor"><code>⊻</code></a> (<a href="../base/math.html#Base.xor"><code>xor</code></a>) perform the bitwise operations equivalent to <code>and</code>, <code>or</code>, and <code>xor</code> respectively in MATLAB, and have precedence similar to Python&#39;s bitwise operators (unlike C). They can operate on scalars or element-wise across arrays and can be used to combine logical arrays, but note the difference in order of operations: parentheses may be required (e.g., to select elements of <code>A</code> equal to 1 or 2 use <code>(A .== 1) .| (A .== 2)</code>).</li><li>In Julia, the elements of a collection can be passed as arguments to a function using the splat operator <code>...</code>, as in <code>xs=[1,2]; f(xs...)</code>.</li><li>Julia&#39;s <a href="../stdlib/LinearAlgebra.html#LinearAlgebra.svd"><code>svd</code></a> returns singular values as a vector instead of as a dense diagonal matrix.</li><li>In Julia, <code>...</code> is not used to continue lines of code. Instead, incomplete expressions automatically continue onto the next line.</li><li>In both Julia and MATLAB, the variable <code>ans</code> is set to the value of the last expression issued in an interactive session. In Julia, unlike MATLAB, <code>ans</code> is not set when Julia code is run in non-interactive mode.</li><li>Julia&#39;s <code>struct</code>s do not support dynamically adding fields at runtime, unlike MATLAB&#39;s <code>class</code>es. Instead, use a <a href="../base/collections.html#Base.Dict"><code>Dict</code></a>. Dict in Julia isn&#39;t ordered.</li><li>In Julia each module has its own global scope/namespace, whereas in MATLAB there is just one global scope.</li><li>In MATLAB, an idiomatic way to remove unwanted values is to use logical indexing, like in the expression <code>x(x&gt;3)</code> or in the statement <code>x(x&gt;3) = []</code> to modify <code>x</code> in-place. In contrast, Julia provides the higher order functions <a href="../base/collections.html#Base.filter"><code>filter</code></a> and <a href="../base/collections.html#Base.filter!"><code>filter!</code></a>, allowing users to write <code>filter(z-&gt;z&gt;3, x)</code> and <code>filter!(z-&gt;z&gt;3, x)</code> as alternatives to the corresponding transliterations <code>x[x.&gt;3]</code> and <code>x = x[x.&gt;3]</code>. Using <a href="../base/collections.html#Base.filter!"><code>filter!</code></a> reduces the use of temporary arrays.</li><li>The analogue of extracting (or &quot;dereferencing&quot;) all elements of a cell array, e.g. in <code>vertcat(A{:})</code> in MATLAB, is written using the splat operator in Julia, e.g. as <code>vcat(A...)</code>.</li><li>In Julia, the <code>adjoint</code> function performs conjugate transposition; in MATLAB, <code>adjoint</code> provides the &quot;adjugate&quot; or classical adjoint, which is the transpose of the matrix of cofactors.</li><li>In Julia, a^b^c is evaluated a^(b^c) while in MATLAB it&#39;s (a^b)^c.</li></ul><h2 id="Noteworthy-differences-from-R"><a class="docs-heading-anchor" href="#Noteworthy-differences-from-R">Noteworthy differences from R</a><a id="Noteworthy-differences-from-R-1"></a><a class="docs-heading-anchor-permalink" href="#Noteworthy-differences-from-R" title="Permalink"></a></h2><p>One of Julia&#39;s goals is to provide an effective language for data analysis and statistical programming. For users coming to Julia from R, these are some noteworthy differences:</p><ul><li><p>Julia&#39;s single quotes enclose characters, not strings.</p></li><li><p>Julia can create substrings by indexing into strings. In R, strings must be converted into character vectors before creating substrings.</p></li><li><p>In Julia, like Python but unlike R, strings can be created with triple quotes <code>&quot;&quot;&quot; ... &quot;&quot;&quot;</code>. This syntax is convenient for constructing strings that contain line breaks.</p></li><li><p>In Julia, varargs are specified using the splat operator <code>...</code>, which always follows the name of a specific variable, unlike R, for which <code>...</code> can occur in isolation.</p></li><li><p>In Julia, modulus is <code>mod(a, b)</code>, not <code>a %% b</code>. <code>%</code> in Julia is the remainder operator.</p></li><li><p>Julia constructs vectors using brackets. Julia&#39;s <code>[1, 2, 3]</code> is the equivalent of R&#39;s <code>c(1, 2, 3)</code>.</p></li><li><p>In Julia, not all data structures support logical indexing. Furthermore, logical indexing in Julia is supported only with vectors of length equal to the object being indexed. For example:</p><ul><li>In R, <code>c(1, 2, 3, 4)[c(TRUE, FALSE)]</code> is equivalent to <code>c(1, 3)</code>.</li><li>In R, <code>c(1, 2, 3, 4)[c(TRUE, FALSE, TRUE, FALSE)]</code> is equivalent to <code>c(1, 3)</code>.</li><li>In Julia, <code>[1, 2, 3, 4][[true, false]]</code> throws a <a href="../base/base.html#Core.BoundsError"><code>BoundsError</code></a>.</li><li>In Julia, <code>[1, 2, 3, 4][[true, false, true, false]]</code> produces <code>[1, 3]</code>.</li></ul></li><li><p>Like many languages, Julia does not always allow operations on vectors of different lengths, unlike R where the vectors only need to share a common index range.  For example, <code>c(1, 2, 3, 4) + c(1, 2)</code> is valid R but the equivalent <code>[1, 2, 3, 4] + [1, 2]</code> will throw an error in Julia.</p></li><li><p>Julia allows an optional trailing comma when that comma does not change the meaning of code. This can cause confusion among R users when indexing into arrays. For example, <code>x[1,]</code> in R would return the first row of a matrix; in Julia, however, the comma is ignored, so <code>x[1,] == x[1]</code>, and will return the first element. To extract a row, be sure to use <code>:</code>, as in <code>x[1,:]</code>.</p></li><li><p>Julia&#39;s <a href="../base/collections.html#Base.map"><code>map</code></a> takes the function first, then its arguments, unlike <code>lapply(&lt;structure&gt;, function, ...)</code> in R. Similarly Julia&#39;s equivalent of <code>apply(X, MARGIN, FUN, ...)</code> in R is <a href="../base/arrays.html#Base.mapslices"><code>mapslices</code></a> where the function is the first argument.</p></li><li><p>Multivariate apply in R, e.g. <code>mapply(choose, 11:13, 1:3)</code>, can be written as <code>broadcast(binomial, 11:13, 1:3)</code> in Julia. Equivalently Julia offers a shorter dot syntax for vectorizing functions <code>binomial.(11:13, 1:3)</code>.</p></li><li><p>Julia uses <code>end</code> to denote the end of conditional blocks, like <code>if</code>, loop blocks, like <code>while</code>/ <code>for</code>, and functions. In lieu of the one-line <code>if ( cond ) statement</code>, Julia allows statements of the form <code>if cond; statement; end</code>, <code>cond &amp;&amp; statement</code> and <code>!cond || statement</code>. Assignment statements in the latter two syntaxes must be explicitly wrapped in parentheses, e.g. <code>cond &amp;&amp; (x = value)</code>.</p></li><li><p>In Julia, <code>&lt;-</code>, <code>&lt;&lt;-</code> and <code>-&gt;</code> are not assignment operators.</p></li><li><p>Julia&#39;s <code>-&gt;</code> creates an anonymous function.</p></li><li><p>Julia&#39;s <a href="../base/math.html#Base.:*-Tuple{Any, Vararg{Any}}"><code>*</code></a> operator can perform matrix multiplication, unlike in R. If <code>A</code> and <code>B</code> are matrices, then <code>A * B</code> denotes a matrix multiplication in Julia, equivalent to R&#39;s <code>A %*% B</code>. In R, this same notation would perform an element-wise (Hadamard) product. To get the element-wise multiplication operation, you need to write <code>A .* B</code> in Julia.</p></li><li><p>Julia performs matrix transposition using the <code>transpose</code> function and conjugated transposition using the <code>&#39;</code> operator or the <code>adjoint</code> function. Julia&#39;s <code>transpose(A)</code> is therefore equivalent to R&#39;s <code>t(A)</code>. Additionally a non-recursive transpose in Julia is provided by the <code>permutedims</code> function.</p></li><li><p>Julia does not require parentheses when writing <code>if</code> statements or <code>for</code>/<code>while</code> loops: use <code>for i in [1, 2, 3]</code> instead of <code>for (i in c(1, 2, 3))</code> and <code>if i == 1</code> instead of <code>if (i == 1)</code>.</p></li><li><p>Julia does not treat the numbers <code>0</code> and <code>1</code> as Booleans. You cannot write <code>if (1)</code> in Julia, because <code>if</code> statements accept only booleans. Instead, you can write <code>if true</code>, <code>if Bool(1)</code>, or <code>if 1==1</code>.</p></li><li><p>Julia does not provide <code>nrow</code> and <code>ncol</code>. Instead, use <code>size(M, 1)</code> for <code>nrow(M)</code> and <code>size(M, 2)</code> for <code>ncol(M)</code>.</p></li><li><p>Julia is careful to distinguish scalars, vectors and matrices.  In R, <code>1</code> and <code>c(1)</code> are the same. In Julia, they cannot be used interchangeably.</p></li><li><p>Julia&#39;s <a href="../stdlib/LinearAlgebra.html#LinearAlgebra.diag"><code>diag</code></a> and <a href="../stdlib/LinearAlgebra.html#LinearAlgebra.diagm"><code>diagm</code></a> are not like R&#39;s.</p></li><li><p>Julia cannot assign to the results of function calls on the left hand side of an assignment operation: you cannot write <code>diag(M) = fill(1, n)</code>.</p></li><li><p>Julia discourages populating the main namespace with functions. Most statistical functionality for Julia is found in <a href="https://pkg.julialang.org/">packages</a> under the <a href="https://github.com/JuliaStats">JuliaStats organization</a>. For example:</p><ul><li>Functions pertaining to probability distributions are provided by the <a href="https://github.com/JuliaStats/Distributions.jl">Distributions package</a>.</li><li>The <a href="https://github.com/JuliaData/DataFrames.jl">DataFrames package</a> provides data frames.</li><li>Generalized linear models are provided by the <a href="https://github.com/JuliaStats/GLM.jl">GLM package</a>.</li></ul></li><li><p>Julia provides tuples and real hash tables, but not R-style lists. When returning multiple items, you should typically use a tuple or a named tuple: instead of <code>list(a = 1, b = 2)</code>, use <code>(1, 2)</code> or <code>(a=1, b=2)</code>.</p></li><li><p>Julia encourages users to write their own types, which are easier to use than S3 or S4 objects in R. Julia&#39;s multiple dispatch system means that <code>table(x::TypeA)</code> and <code>table(x::TypeB)</code> act like R&#39;s <code>table.TypeA(x)</code> and <code>table.TypeB(x)</code>.</p></li><li><p>In Julia, values are not copied when assigned or passed to a function. If a function modifies an array, the changes will be visible in the caller. This is very different from R and allows new functions to operate on large data structures much more efficiently.</p></li><li><p>In Julia, vectors and matrices are concatenated using <a href="../base/arrays.html#Base.hcat"><code>hcat</code></a>, <a href="../base/arrays.html#Base.vcat"><code>vcat</code></a> and <a href="../base/arrays.html#Base.hvcat"><code>hvcat</code></a>, not <code>c</code>, <code>rbind</code> and <code>cbind</code> like in R.</p></li><li><p>In Julia, a range like <code>a:b</code> is not shorthand for a vector like in R, but is a specialized <code>AbstractRange</code> object that is used for iteration. To convert a range into a vector, use <a href="../base/collections.html#Base.collect-Tuple{Any}"><code>collect(a:b)</code></a>.</p></li><li><p>The <code>:</code> operator has a different precedence in R and Julia. In particular, in Julia arithmetic operators have higher precedence than the <code>:</code> operator, whereas the reverse is true in R. For example, <code>1:n-1</code> in Julia is equivalent to <code>1:(n-1)</code> in R.</p></li><li><p>Julia&#39;s <a href="../base/math.html#Base.max"><code>max</code></a> and <a href="../base/math.html#Base.min"><code>min</code></a> are the equivalent of <code>pmax</code> and <code>pmin</code> respectively in R, but both arguments need to have the same dimensions.  While <a href="../base/collections.html#Base.maximum"><code>maximum</code></a> and <a href="../base/collections.html#Base.minimum"><code>minimum</code></a> replace <code>max</code> and <code>min</code> in R, there are important differences.</p></li><li><p>Julia&#39;s <a href="../base/collections.html#Base.sum"><code>sum</code></a>, <a href="../base/collections.html#Base.prod"><code>prod</code></a>, <a href="../base/collections.html#Base.maximum"><code>maximum</code></a>, and <a href="../base/collections.html#Base.minimum"><code>minimum</code></a> are different from their counterparts in R. They all accept an optional keyword argument <code>dims</code>, which indicates the dimensions, over which the operation is carried out.  For instance, let <code>A = [1 2; 3 4]</code> in Julia and <code>B &lt;- rbind(c(1,2),c(3,4))</code> be the same matrix in R.  Then <code>sum(A)</code> gives the same result as <code>sum(B)</code>, but <code>sum(A, dims=1)</code> is a row vector containing the sum over each column and <code>sum(A, dims=2)</code> is a column vector containing the sum over each row. This contrasts to the behavior of R, where separate <code>colSums(B)</code> and <code>rowSums(B)</code> functions provide these functionalities. If the <code>dims</code> keyword argument is a vector, then it specifies all the dimensions over which the sum is performed, while retaining the dimensions of the summed array, e.g. <code>sum(A, dims=(1,2)) == hcat(10)</code>. It should be noted that there is no error checking regarding the second argument.</p></li><li><p>Julia has several functions that can mutate their arguments. For example, it has both <a href="../base/sort.html#Base.sort"><code>sort</code></a> and <a href="../base/sort.html#Base.sort!"><code>sort!</code></a>.</p></li><li><p>In R, performance requires vectorization. In Julia, almost the opposite is true: the best performing code is often achieved by using devectorized loops.</p></li><li><p>Julia is eagerly evaluated and does not support R-style lazy evaluation. For most users, this means that there are very few unquoted expressions or column names.</p></li><li><p>Julia does not support the <code>NULL</code> type. The closest equivalent is <a href="../base/constants.html#Core.nothing"><code>nothing</code></a>, but it behaves like a scalar value rather than like a list. Use <code>x === nothing</code> instead of <code>is.null(x)</code>.</p></li><li><p>In Julia, missing values are represented by the <a href="missing.html#missing"><code>missing</code></a> object rather than by <code>NA</code>. Use <a href="../base/base.html#Base.ismissing"><code>ismissing(x)</code></a> (or <code>ismissing.(x)</code> for element-wise operation on vectors) instead of <code>is.na(x)</code>. The <a href="../base/base.html#Base.skipmissing"><code>skipmissing</code></a> function is generally used instead of <code>na.rm=TRUE</code> (though in some particular cases functions take a <code>skipmissing</code> argument).</p></li><li><p>Julia lacks the equivalent of R&#39;s <code>assign</code> or <code>get</code>.</p></li><li><p>In Julia, <code>return</code> does not require parentheses.</p></li><li><p>In R, an idiomatic way to remove unwanted values is to use logical indexing, like in the expression <code>x[x&gt;3]</code> or in the statement <code>x = x[x&gt;3]</code> to modify <code>x</code> in-place. In contrast, Julia provides the higher order functions <a href="../base/collections.html#Base.filter"><code>filter</code></a> and <a href="../base/collections.html#Base.filter!"><code>filter!</code></a>, allowing users to write <code>filter(z-&gt;z&gt;3, x)</code> and <code>filter!(z-&gt;z&gt;3, x)</code> as alternatives to the corresponding transliterations <code>x[x.&gt;3]</code> and <code>x = x[x.&gt;3]</code>. Using <a href="../base/collections.html#Base.filter!"><code>filter!</code></a> reduces the use of temporary arrays.</p></li></ul><h2 id="Noteworthy-differences-from-Python"><a class="docs-heading-anchor" href="#Noteworthy-differences-from-Python">Noteworthy differences from Python</a><a id="Noteworthy-differences-from-Python-1"></a><a class="docs-heading-anchor-permalink" href="#Noteworthy-differences-from-Python" title="Permalink"></a></h2><ul><li>Julia&#39;s <code>for</code>, <code>if</code>, <code>while</code>, etc. blocks are terminated by the <code>end</code> keyword. Indentation level is not significant as it is in Python. Unlike Python, Julia has no <code>pass</code> keyword.</li><li>Strings are denoted by double quotation marks (<code>&quot;text&quot;</code>) in Julia (with three double quotation marks for multi-line strings), whereas in Python they can be denoted either by single (<code>&#39;text&#39;</code>) or double quotation marks (<code>&quot;text&quot;</code>). Single quotation marks are used for characters in Julia (<code>&#39;c&#39;</code>).</li><li>String concatenation is done with <code>*</code> in Julia, not <code>+</code> like in Python. Analogously, string repetition is done with <code>^</code>, not <code>*</code>. Implicit string concatenation of string literals like in Python (e.g. <code>&#39;ab&#39; &#39;cd&#39; == &#39;abcd&#39;</code>) is not done in Julia.</li><li>Python Lists—flexible but slow—correspond to the Julia <code>Vector{Any}</code> type or more generally <code>Vector{T}</code> where <code>T</code> is some non-concrete element type. &quot;Fast&quot; arrays like NumPy arrays that store elements in-place (i.e., <code>dtype</code> is <code>np.float64</code>, <code>[(&#39;f1&#39;, np.uint64), (&#39;f2&#39;, np.int32)]</code>, etc.) can be represented by <code>Array{T}</code> where <code>T</code> is a concrete, immutable element type. This includes built-in types like <code>Float64</code>, <code>Int32</code>, <code>Int64</code> but also more complex types like <code>Tuple{UInt64,Float64}</code> and many user-defined types as well.</li><li>In Julia, indexing of arrays, strings, etc. is 1-based not 0-based.</li><li>Julia&#39;s slice indexing includes the last element, unlike in Python. <code>a[2:3]</code> in Julia is <code>a[1:3]</code> in Python.</li><li>Unlike Python, Julia allows <a href="https://julialang.org/blog/2017/04/offset-arrays/">AbstractArrays with arbitrary indexes</a>. Python&#39;s special interpretation of negative indexing, <code>a[-1]</code> and <code>a[-2]</code>, should be written <code>a[end]</code> and <code>a[end-1]</code> in Julia.</li><li>Julia requires <code>end</code> for indexing until the last element. <code>x[1:]</code> in Python is equivalent to <code>x[2:end]</code> in Julia.</li><li>In Julia, <code>:</code> before any object creates a <a href="../base/base.html#Core.Symbol"><code>Symbol</code></a> or <em>quotes</em> an expression; so, <code>x[:5]</code> is same as <code>x[5]</code>. If you want to get the first <code>n</code> elements of an array, then use range indexing.</li><li>Julia&#39;s range indexing has the format of <code>x[start:step:stop]</code>, whereas Python&#39;s format is <code>x[start:(stop+1):step]</code>. Hence, <code>x[0:10:2]</code> in Python is equivalent to <code>x[1:2:10]</code> in Julia. Similarly, <code>x[::-1]</code> in Python, which refers to the reversed array, is equivalent to <code>x[end:-1:1]</code> in Julia.</li><li>In Julia, ranges can be constructed independently as <code>start:step:stop</code>, the same syntax it uses in array-indexing.  The <code>range</code> function is also supported.</li><li>In Julia, indexing a matrix with arrays like <code>X[[1,2], [1,3]]</code> refers to a sub-matrix that contains the intersections of the first and second rows with the first and third columns. In Python, <code>X[[1,2], [1,3]]</code> refers to a vector that contains the values of cell <code>[1,1]</code> and <code>[2,3]</code> in the matrix. <code>X[[1,2], [1,3]]</code> in Julia is equivalent with <code>X[np.ix_([0,1],[0,2])]</code> in Python. <code>X[[0,1], [0,2]]</code> in Python is equivalent with <code>X[[CartesianIndex(1,1), CartesianIndex(2,3)]]</code> in Julia.</li><li>Julia has no line continuation syntax: if, at the end of a line, the input so far is a complete expression, it is considered done; otherwise the input continues. One way to force an expression to continue is to wrap it in parentheses.</li><li>Julia arrays are column-major (Fortran-ordered) whereas NumPy arrays are row-major (C-ordered) by default. To get optimal performance when looping over arrays, the order of the loops should be reversed in Julia relative to NumPy (see <a href="performance-tips.html#man-performance-column-major">relevant section of Performance Tips</a>).</li><li>Julia&#39;s updating operators (e.g. <code>+=</code>, <code>-=</code>, ...) are <em>not in-place</em> whereas NumPy&#39;s are. This means <code>A = [1, 1]; B = A; B += [3, 3]</code> doesn&#39;t change values in <code>A</code>, it rather rebinds the name <code>B</code> to the result of the right-hand side <code>B = B + 3</code>, which is a new array. For in-place operation, use <code>B .+= 3</code> (see also <a href="mathematical-operations.html#man-dot-operators">dot operators</a>), explicit loops, or <code>InplaceOps.jl</code>.</li><li>Julia evaluates default values of function arguments every time the method is invoked, unlike in Python where the default values are evaluated only once when the function is defined. For example, the function <code>f(x=rand()) = x</code> returns a new random number every time it is invoked without argument. On the other hand, the function <code>g(x=[1,2]) = push!(x,3)</code> returns <code>[1,2,3]</code> every time it is called as <code>g()</code>.</li><li>In Julia, keyword arguments must be passed using keywords, unlike Python in which it is usually possible to pass them positionally. Attempting to pass a keyword argument positionally alters the method signature leading to a <code>MethodError</code> or calling of the wrong method.</li><li>In Julia <code>%</code> is the remainder operator, whereas in Python it is the modulus.</li><li>In Julia, the commonly used <code>Int</code> type corresponds to the machine integer type (<code>Int32</code> or <code>Int64</code>), unlike in Python, where <code>int</code> is an arbitrary length integer. This means in Julia the <code>Int</code> type will overflow, such that <code>2^64 == 0</code>. If you need larger values use another appropriate type, such as <code>Int128</code>, <a href="../base/numbers.html#Base.GMP.BigInt"><code>BigInt</code></a> or a floating point type like <code>Float64</code>.</li><li>The imaginary unit <code>sqrt(-1)</code> is represented in Julia as <code>im</code>, not <code>j</code> as in Python.</li><li>In Julia, the exponentiation operator is <code>^</code>, not <code>**</code> as in Python.</li><li>Julia uses <code>nothing</code> of type <code>Nothing</code> to represent a null value, whereas Python uses <code>None</code> of type <code>NoneType</code>.</li><li>In Julia, the standard operators over a matrix type are matrix operations, whereas, in Python, the standard operators are element-wise operations. When both <code>A</code> and <code>B</code> are matrices, <code>A * B</code> in Julia performs matrix multiplication, not element-wise multiplication as in Python. <code>A * B</code> in Julia is equivalent with <code>A @ B</code> in Python, whereas <code>A * B</code> in Python is equivalent with <code>A .* B</code> in Julia.</li><li>The adjoint operator <code>&#39;</code> in Julia returns an adjoint of a vector (a lazy representation of row vector), whereas the transpose operator <code>.T</code> over a vector in Python returns the original vector (non-op).</li><li>In Julia, a function may contain multiple concrete implementations (called <em>methods</em>), which are selected via multiple dispatch based on the types of all arguments to the call, as compared to functions in Python, which have a single implementation and no polymorphism (as opposed to Python method calls which use a different syntax and allows dispatch on the receiver of the method).</li><li>There are no classes in Julia. Instead there are structures (mutable or immutable), containing data but no methods.</li><li>Calling a method of a class instance in Python (<code>x = MyClass(*args); x.f(y)</code>) corresponds to a function call in Julia, e.g. <code>x = MyType(args...); f(x, y)</code>. In general, multiple dispatch is more flexible and powerful than the Python class system.</li><li>Julia structures may have exactly one abstract supertype, whereas Python classes can inherit from one or more (abstract or concrete) superclasses.</li><li>The logical Julia program structure (Packages and Modules) is independent of the file structure, whereas the Python code structure is defined by directories (Packages) and files (Modules).</li><li>In Julia, it is idiomatic to split the text of large modules into multiple files, without introducing a new module per file. The code is reassembled inside a single module in a main file via <code>include</code>. While the Python equivalent (<code>exec</code>) is not typical for this use (it will silently clobber prior definitions), Julia programs are defined as a unit at the <code>module</code> level with <code>using</code> or <code>import</code>, which will only get executed once when first needed–like <code>include</code> in Python. Within those modules, the individual files that make up that module are loaded with <code>include</code> by listing them once in the intended order.</li><li>The ternary operator <code>x &gt; 0 ? 1 : -1</code> in Julia corresponds to a conditional expression in Python <code>1 if x &gt; 0 else -1</code>.</li><li>In Julia the <code>@</code> symbol refers to a macro, whereas in Python it refers to a decorator.</li><li>Exception handling in Julia is done using <code>try</code> — <code>catch</code> — <code>finally</code>, instead of <code>try</code> — <code>except</code> — <code>finally</code>. In contrast to Python, it is not recommended to use exception handling as part of the normal workflow in Julia (compared with Python, Julia is faster at ordinary control flow but slower at exception-catching).</li><li>In Julia loops are fast, there is no need to write &quot;vectorized&quot; code for performance reasons.</li><li>Be careful with non-constant global variables in Julia, especially in tight loops. Since you can write close-to-metal code in Julia (unlike Python), the effect of globals can be drastic (see <a href="performance-tips.html#man-performance-tips">Performance Tips</a>).</li><li>In Julia, rounding and truncation are explicit. Python&#39;s <code>int(3.7)</code> should be <code>floor(Int, 3.7)</code> or <code>Int(floor(3.7))</code> and is distinguished from <code>round(Int, 3.7)</code>. <code>floor(x)</code> and <code>round(x)</code> on their own return an integer value of the same type as <code>x</code> rather than always returning <code>Int</code>.</li><li>In Julia, parsing is explicit. Python&#39;s <code>float(&quot;3.7&quot;)</code> would be <code>parse(Float64, &quot;3.7&quot;)</code> in Julia.</li><li>In Python, the majority of values can be used in logical contexts (e.g. <code>if &quot;a&quot;:</code> means the following block is executed, and <code>if &quot;&quot;:</code> means it is not). In Julia, you need explicit conversion to <code>Bool</code> (e.g. <code>if &quot;a&quot;</code> throws an exception). If you want to test for a non-empty string in Julia, you would explicitly write <code>if !isempty(&quot;&quot;)</code>.  Perhaps surprisingly, in Python <code>if &quot;False&quot;</code> and <code>bool(&quot;False&quot;)</code> both evaluate to <code>True</code> (because <code>&quot;False&quot;</code> is a non-empty string); in Julia, <code>parse(Bool, &quot;false&quot;)</code> returns <code>false</code>.</li><li>In Julia, a new local scope is introduced by most code blocks, including loops and <code>try</code> — <code>catch</code> — <code>finally</code>. Note that comprehensions (list, generator, etc.) introduce a new local scope both in Python and Julia, whereas <code>if</code> blocks do not introduce a new local scope in both languages.</li></ul><h2 id="Noteworthy-differences-from-C/C"><a class="docs-heading-anchor" href="#Noteworthy-differences-from-C/C">Noteworthy differences from C/C++</a><a id="Noteworthy-differences-from-C/C-1"></a><a class="docs-heading-anchor-permalink" href="#Noteworthy-differences-from-C/C" title="Permalink"></a></h2><ul><li>Julia arrays are indexed with square brackets, and can have more than one dimension <code>A[i,j]</code>. This syntax is not just syntactic sugar for a reference to a pointer or address as in C/C++. See <a href="arrays.html#man-multi-dim-arrays">the manual entry about array construction</a>.</li><li>In Julia, indexing of arrays, strings, etc. is 1-based not 0-based.</li><li>Julia arrays are not copied when assigned to another variable. After <code>A = B</code>, changing elements of <code>B</code> will modify <code>A</code> as well. Updating operators like <code>+=</code> do not operate in-place, they are equivalent to <code>A = A + B</code> which rebinds the left-hand side to the result of the right-hand side expression.</li><li>Julia arrays are column major (Fortran ordered) whereas C/C++ arrays are row major ordered by default. To get optimal performance when looping over arrays, the order of the loops should be reversed in Julia relative to C/C++ (see <a href="performance-tips.html#man-performance-column-major">relevant section of Performance Tips</a>).</li><li>Julia values are not copied when assigned or passed to a function. If a function modifies an array, the changes will be visible in the caller.</li><li>In Julia, whitespace is significant, unlike C/C++, so care must be taken when adding/removing whitespace from a Julia program.</li><li>In Julia, literal numbers without a decimal point (such as <code>42</code>) create signed integers, of type <code>Int</code>, but literals too large to fit in the machine word size will automatically be promoted to a larger size type, such as <code>Int64</code> (if <code>Int</code> is <code>Int32</code>), <code>Int128</code>, or the arbitrarily large <code>BigInt</code> type. There are no numeric literal suffixes, such as <code>L</code>, <code>LL</code>, <code>U</code>, <code>UL</code>, <code>ULL</code> to indicate unsigned and/or signed vs. unsigned. Decimal literals are always signed, and hexadecimal literals (which start with <code>0x</code> like C/C++), are unsigned, unless when they encode more than 128 bits, in which case they are of type <code>BigInt</code>. Hexadecimal literals also, unlike C/C++/Java and unlike decimal literals in Julia, have a type based on the <em>length</em> of the literal, including leading 0s. For example, <code>0x0</code> and <code>0x00</code> have type <a href="../base/numbers.html#Core.UInt8"><code>UInt8</code></a>, <code>0x000</code> and <code>0x0000</code> have type <a href="../base/numbers.html#Core.UInt16"><code>UInt16</code></a>, then literals with 5 to 8 hex digits have type <code>UInt32</code>, 9 to 16 hex digits type <code>UInt64</code>, 17 to 32 hex digits type <code>UInt128</code>, and more that 32 hex digits type <code>BigInt</code>. This needs to be taken into account when defining hexadecimal masks, for example <code>~0xf == 0xf0</code> is very different from <code>~0x000f == 0xfff0</code>. 64 bit <code>Float64</code> and 32 bit <a href="../base/numbers.html#Core.Float32"><code>Float32</code></a> bit literals are expressed as <code>1.0</code> and <code>1.0f0</code> respectively. Floating point literals are rounded (and not promoted to the <code>BigFloat</code> type) if they can not be exactly represented.  Floating point literals are closer in behavior to C/C++. Octal (prefixed with <code>0o</code>) and binary (prefixed with <code>0b</code>) literals are also treated as unsigned (or <code>BigInt</code> for more than 128 bits).</li><li>In Julia, the division operator <a href="../base/math.html#Base.:/"><code>/</code></a> returns a floating point number when both operands are of integer type.  To perform integer division, use <a href="../base/math.html#Base.div"><code>div</code></a> or <a href="../base/math.html#Base.div"><code>÷</code></a>.</li><li>Indexing an <code>Array</code> with floating point types is generally an error in Julia. The Julia equivalent of the C expression <code>a[i / 2]</code> is <code>a[i ÷ 2 + 1]</code>, where <code>i</code> is of integer type.</li><li>String literals can be delimited with either <code>&quot;</code>  or <code>&quot;&quot;&quot;</code>, <code>&quot;&quot;&quot;</code> delimited literals can contain <code>&quot;</code> characters without quoting it like <code>&quot;\&quot;&quot;</code>. String literals can have values of other variables or expressions interpolated into them, indicated by <code>$variablename</code> or <code>$(expression)</code>, which evaluates the variable name or the expression in the context of the function.</li><li><code>//</code> indicates a <a href="../base/numbers.html#Base.Rational"><code>Rational</code></a> number, and not a single-line comment (which is <code>#</code> in Julia)</li><li><code>#=</code> indicates the start of a multiline comment, and <code>=#</code> ends it.</li><li>Functions in Julia return values from their last expression(s) or the <code>return</code> keyword.  Multiple values can be returned from functions and assigned as tuples, e.g. <code>(a, b) = myfunction()</code> or <code>a, b = myfunction()</code>, instead of having to pass pointers to values as one would have to do in C/C++ (i.e. <code>a = myfunction(&amp;b)</code>.</li><li>Julia does not require the use of semicolons to end statements. The results of expressions are not automatically printed (except at the interactive prompt, i.e. the REPL), and lines of code do not need to end with semicolons. <a href="../base/io-network.html#Base.println"><code>println</code></a> or <a href="../stdlib/Printf.html#Printf.@printf"><code>@printf</code></a> can be used to print specific output. In the REPL, <code>;</code> can be used to suppress output. <code>;</code> also has a different meaning within <code>[ ]</code>, something to watch out for. <code>;</code> can be used to separate expressions on a single line, but are not strictly necessary in many cases, and are more an aid to readability.</li><li>In Julia, the operator <a href="../base/math.html#Base.xor"><code>⊻</code></a> (<a href="../base/math.html#Base.xor"><code>xor</code></a>) performs the bitwise XOR operation, i.e. <a href="../base/math.html#Base.:^-Tuple{Number, Number}"><code>^</code></a> in C/C++.  Also, the bitwise operators do not have the same precedence as C/C++, so parenthesis may be required.</li><li>Julia&#39;s <a href="../base/math.html#Base.:^-Tuple{Number, Number}"><code>^</code></a> is exponentiation (pow), not bitwise XOR as in C/C++ (use <a href="../base/math.html#Base.xor"><code>⊻</code></a>, or <a href="../base/math.html#Base.xor"><code>xor</code></a>, in Julia)</li><li>Julia has two right-shift operators, <code>&gt;&gt;</code> and <code>&gt;&gt;&gt;</code>.  <code>&gt;&gt;</code> performs an arithmetic shift, <code>&gt;&gt;&gt;</code> always performs a logical shift, unlike C/C++, where the meaning of <code>&gt;&gt;</code> depends on the type of the value being shifted.</li><li>Julia&#39;s <code>-&gt;</code> creates an anonymous function, it does not access a member via a pointer.</li><li>Julia does not require parentheses when writing <code>if</code> statements or <code>for</code>/<code>while</code> loops: use <code>for i in [1, 2, 3]</code> instead of <code>for (int i=1; i &lt;= 3; i++)</code> and <code>if i == 1</code> instead of <code>if (i == 1)</code>.</li><li>Julia does not treat the numbers <code>0</code> and <code>1</code> as Booleans. You cannot write <code>if (1)</code> in Julia, because <code>if</code> statements accept only booleans. Instead, you can write <code>if true</code>, <code>if Bool(1)</code>, or <code>if 1==1</code>.</li><li>Julia uses <code>end</code> to denote the end of conditional blocks, like <code>if</code>, loop blocks, like <code>while</code>/ <code>for</code>, and functions. In lieu of the one-line <code>if ( cond ) statement</code>, Julia allows statements of the form <code>if cond; statement; end</code>, <code>cond &amp;&amp; statement</code> and <code>!cond || statement</code>. Assignment statements in the latter two syntaxes must be explicitly wrapped in parentheses, e.g. <code>cond &amp;&amp; (x = value)</code>, because of the operator precedence.</li><li>Julia has no line continuation syntax: if, at the end of a line, the input so far is a complete expression, it is considered done; otherwise the input continues. One way to force an expression to continue is to wrap it in parentheses.</li><li>Julia macros operate on parsed expressions, rather than the text of the program, which allows them to perform sophisticated transformations of Julia code. Macro names start with the <code>@</code> character, and have both a function-like syntax, <code>@mymacro(arg1, arg2, arg3)</code>, and a statement-like syntax, <code>@mymacro arg1 arg2 arg3</code>. The forms are interchangeable; the function-like form is particularly useful if the macro appears within another expression, and is often clearest. The statement-like form is often used to annotate blocks, as in the distributed <code>for</code> construct: <code>@distributed for i in 1:n; #= body =#; end</code>. Where the end of the macro construct may be unclear, use the function-like form.</li><li>Julia has an enumeration type, expressed using the macro <code>@enum(name, value1, value2, ...)</code> For example: <code>@enum(Fruit, banana=1, apple, pear)</code></li><li>By convention, functions that modify their arguments have a <code>!</code> at the end of the name, for example <code>push!</code>.</li><li>In C++, by default, you have static dispatch, i.e. you need to annotate a function as virtual, in order to have dynamic dispatch. On the other hand, in Julia every method is &quot;virtual&quot; (although it&#39;s more general than that since methods are dispatched on every argument type, not only <code>this</code>, using the most-specific-declaration rule).</li></ul><h3 id="Julia-C/C:-Namespaces"><a class="docs-heading-anchor" href="#Julia-C/C:-Namespaces">Julia ⇔ C/C++: Namespaces</a><a id="Julia-C/C:-Namespaces-1"></a><a class="docs-heading-anchor-permalink" href="#Julia-C/C:-Namespaces" title="Permalink"></a></h3><ul><li>C/C++ <code>namespace</code>s correspond roughly to Julia <code>module</code>s.</li><li>There are no private globals or fields in Julia.  Everything is publicly accessible through fully qualified paths (or relative paths, if desired).</li><li><code>using MyNamespace::myfun</code> (C++) corresponds roughly to <code>import MyModule: myfun</code> (Julia).</li><li><code>using namespace MyNamespace</code> (C++) corresponds roughly to <code>using MyModule</code> (Julia)<ul><li>In Julia, only <code>export</code>ed symbols are made available to the calling module.</li><li>In C++, only elements found in the included (public) header files are made available.</li></ul></li><li>Caveat: <code>import</code>/<code>using</code> keywords (Julia) also <em>load</em> modules (see below).</li><li>Caveat: <code>import</code>/<code>using</code> (Julia) works only at the global scope level (<code>module</code>s)<ul><li>In C++, <code>using namespace X</code> works within arbitrary scopes (ex: function scope).</li></ul></li></ul><h3 id="Julia-C/C:-Module-loading"><a class="docs-heading-anchor" href="#Julia-C/C:-Module-loading">Julia ⇔ C/C++: Module loading</a><a id="Julia-C/C:-Module-loading-1"></a><a class="docs-heading-anchor-permalink" href="#Julia-C/C:-Module-loading" title="Permalink"></a></h3><ul><li>When you think of a C/C++ &quot;<strong>library</strong>&quot;, you are likely looking for a Julia &quot;<strong>package</strong>&quot;.<ul><li>Caveat: C/C++ libraries often house multiple &quot;software modules&quot; whereas Julia &quot;packages&quot; typically house one.</li><li>Reminder: Julia <code>module</code>s are global scopes (not necessarily &quot;software modules&quot;).</li></ul></li><li><strong>Instead of build/<code>make</code> scripts</strong>, Julia uses &quot;Project Environments&quot; (sometimes called either &quot;Project&quot; or &quot;Environment&quot;).<ul><li>Build scripts are only needed for more complex applications (like those needing to compile or download C/C++ executables).</li><li>To develop application or project in Julia, you can initialize its root directory as a &quot;Project Environment&quot;, and house application-specific code/packages there. This provides good control over project dependencies, and future reproducibility.</li><li>Available packages are added to a &quot;Project Environment&quot; with the <code>Pkg.add()</code> function or Pkg REPL mode. (This does not <strong>load</strong> said package, however).</li><li>The list of available packages (direct dependencies) for a &quot;Project Environment&quot; are saved in its <code>Project.toml</code> file.</li><li>The <em>full</em> dependency information for a &quot;Project Environment&quot; is auto-generated &amp; saved in its <code>Manifest.toml</code> file by <code>Pkg.resolve()</code>.</li></ul></li><li>Packages (&quot;software modules&quot;) available to the &quot;Project Environment&quot; are loaded with <code>import</code> or <code>using</code>.<ul><li>In C/C++, you <code>#include &lt;moduleheader&gt;</code> to get object/function declarations, and link in libraries when you build the executable.</li><li>In Julia, calling using/import again just brings the existing module into scope, but does not load it again (similar to adding the non-standard <code>#pragma once</code> to C/C++).</li></ul></li><li><strong>Directory-based package repositories</strong> (Julia) can be made available by adding repository paths to the <code>Base.LOAD_PATH</code> array.<ul><li>Packages from directory-based repositories do not require the <code>Pkg.add()</code> tool prior to being loaded with <code>import</code> or <code>using</code>. They are simply available to the project.</li><li>Directory-based package repositories are the <strong>quickest solution</strong> to developing local libraries of &quot;software modules&quot;.</li></ul></li></ul><h3 id="Julia-C/C:-Assembling-modules"><a class="docs-heading-anchor" href="#Julia-C/C:-Assembling-modules">Julia ⇔ C/C++: Assembling modules</a><a id="Julia-C/C:-Assembling-modules-1"></a><a class="docs-heading-anchor-permalink" href="#Julia-C/C:-Assembling-modules" title="Permalink"></a></h3><ul><li>In C/C++, <code>.c</code>/<code>.cpp</code> files are compiled &amp; added to a library with build/<code>make</code> scripts.<ul><li>In Julia, <code>import [PkgName]</code>/<code>using [PkgName]</code> statements load <code>[PkgName].jl</code> located in a package&#39;s <code>[PkgName]/src/</code> subdirectory.</li><li>In turn, <code>[PkgName].jl</code> typically loads associated source files with calls to <code>include &quot;[someotherfile].jl&quot;</code>.</li></ul></li><li><code>include &quot;./path/to/somefile.jl&quot;</code> (Julia) is very similar to <code>#include &quot;./path/to/somefile.jl&quot;</code> (C/C++).<ul><li>However <code>include &quot;...&quot;</code> (Julia) is not used to include header files (not required).</li><li><strong>Do not use</strong> <code>include &quot;...&quot;</code> (Julia) to load code from other &quot;software modules&quot; (use <code>import</code>/<code>using</code> instead).</li><li><code>include &quot;path/to/some/module.jl&quot;</code> (Julia) would instantiate multiple versions of the same code in different modules (creating <em>distinct</em> types (etc.) with the <em>same</em> names).</li><li><code>include &quot;somefile.jl&quot;</code> is typically used to assemble multiple files <em>within the same Julia package</em> (&quot;software module&quot;). It is therefore relatively straightforward to ensure file are <code>include</code>d only once (No <code>#ifdef</code> confusion).</li></ul></li></ul><h3 id="Julia-C/C:-Module-interface"><a class="docs-heading-anchor" href="#Julia-C/C:-Module-interface">Julia ⇔ C/C++: Module interface</a><a id="Julia-C/C:-Module-interface-1"></a><a class="docs-heading-anchor-permalink" href="#Julia-C/C:-Module-interface" title="Permalink"></a></h3><ul><li>C++ exposes interfaces using &quot;public&quot; <code>.h</code>/<code>.hpp</code> files whereas Julia <code>module</code>s mark specific symbols that are intended for their users as <code>public</code>or <code>export</code>ed.<ul><li>Often, Julia <code>module</code>s simply add functionality by generating new &quot;methods&quot; to existing functions (ex: <code>Base.push!</code>).</li><li>Developers of Julia packages therefore cannot rely on header files for interface documentation.</li><li>Interfaces for Julia packages are typically described using docstrings, README.md, static web pages, ...</li></ul></li><li>Some developers choose not to <code>export</code> all symbols required to use their package/module, but should still mark unexported user facing symbols as <code>public</code>.<ul><li>Users might be expected to access these components by qualifying functions/structs/... with the package/module name (ex: <code>MyModule.run_this_task(...)</code>).</li></ul></li></ul><h3 id="Julia-C/C:-Quick-reference"><a class="docs-heading-anchor" href="#Julia-C/C:-Quick-reference">Julia ⇔ C/C++: Quick reference</a><a id="Julia-C/C:-Quick-reference-1"></a><a class="docs-heading-anchor-permalink" href="#Julia-C/C:-Quick-reference" title="Permalink"></a></h3><table><tr><th style="text-align: left">Software Concept</th><th style="text-align: left">Julia</th><th style="text-align: left">C/C++</th></tr><tr><td style="text-align: left">unnamed scope</td><td style="text-align: left"><code>begin</code> ... <code>end</code></td><td style="text-align: left"><code>{</code> ... <code>}</code></td></tr><tr><td style="text-align: left">function scope</td><td style="text-align: left"><code>function x()</code> ... <code>end</code></td><td style="text-align: left"><code>int x() {</code> ... <code>}</code></td></tr><tr><td style="text-align: left">global scope</td><td style="text-align: left"><code>module MyMod</code> ... <code>end</code></td><td style="text-align: left"><code>namespace MyNS {</code> ... <code>}</code></td></tr><tr><td style="text-align: left">software module</td><td style="text-align: left">A Julia &quot;package&quot;</td><td style="text-align: left"><code>.h</code>/<code>.hpp</code> files&lt;br&gt;+compiled <code>somelib.a</code></td></tr><tr><td style="text-align: left">assembling&lt;br&gt;software modules</td><td style="text-align: left"><code>SomePkg.jl</code>: ...&lt;br&gt;<code>import(&quot;subfile1.jl&quot;)</code>&lt;br&gt;<code>import(&quot;subfile2.jl&quot;)</code>&lt;br&gt;...</td><td style="text-align: left"><code>$(AR) *.o</code> &amp;rArr; <code>somelib.a</code></td></tr><tr><td style="text-align: left">import&lt;br&gt;software module</td><td style="text-align: left"><code>import SomePkg</code></td><td style="text-align: left"><code>#include &lt;somelib&gt;</code>&lt;br&gt;+link in <code>somelib.a</code></td></tr><tr><td style="text-align: left">module library</td><td style="text-align: left"><code>LOAD_PATH[]</code>, *Git repository,&lt;br&gt;**custom package registry</td><td style="text-align: left">more <code>.h</code>/<code>.hpp</code> files&lt;br&gt;+bigger compiled <code>somebiglib.a</code></td></tr></table><p>* The Julia package manager supports registering multiple packages from a single Git repository.&lt;br&gt; * This allows users to house a library of related packages in a single repository.&lt;br&gt; ** Julia registries are primarily designed to provide versioning \&amp; distribution of packages.&lt;br&gt; ** Custom package registries can be used to create a type of module library.</p><h2 id="Noteworthy-differences-from-Common-Lisp"><a class="docs-heading-anchor" href="#Noteworthy-differences-from-Common-Lisp">Noteworthy differences from Common Lisp</a><a id="Noteworthy-differences-from-Common-Lisp-1"></a><a class="docs-heading-anchor-permalink" href="#Noteworthy-differences-from-Common-Lisp" title="Permalink"></a></h2><ul><li><p>Julia uses 1-based indexing for arrays by default, and it can also handle arbitrary <a href="../devdocs/offset-arrays.html#man-custom-indices">index offsets</a>.</p></li><li><p>Functions and variables share the same namespace (“Lisp-1”).</p></li><li><p>There is a <a href="../base/collections.html#Core.Pair"><code>Pair</code></a> type, but it is not meant to be used as a <code>COMMON-LISP:CONS</code>. Various iterable collections can be used interchangeably in most parts of the language (eg splatting, tuples, etc). <code>Tuple</code>s are the closest to Common Lisp lists for <em>short</em> collections of heterogeneous elements. Use <code>NamedTuple</code>s in place of alists. For larger collections of homogeneous types, <code>Array</code>s and <code>Dict</code>s should be used.</p></li><li><p>The typical Julia workflow for prototyping also uses continuous manipulation of the image, implemented with the <a href="https://github.com/timholy/Revise.jl">Revise.jl</a> package.</p></li><li><p>For performance, Julia prefers that operations have <a href="faq.html#man-type-stability">type stability</a>. Where Common Lisp abstracts away from the underlying machine operations, Julia cleaves closer to them. For example:</p><ul><li>Integer division using <code>/</code> always returns a floating-point result, even if the computation is exact.<ul><li><code>//</code> always returns a rational result</li><li><code>÷</code> always returns a (truncated) integer result</li></ul></li><li>Bignums are supported, but conversion is not automatic; ordinary integers <a href="faq.html#faq-integer-arithmetic">overflow</a>.</li><li>Complex numbers are supported, but to get complex results, <a href="faq.html#faq-domain-errors">you need complex inputs</a>.</li><li>There are multiple Complex and Rational types, with different component types.</li></ul></li><li><p>Modules (namespaces) can be hierarchical. <a href="../base/base.html#import"><code>import</code></a> and <a href="../base/base.html#using"><code>using</code></a> have a dual role: they load the code and make it available in the namespace. <code>import</code> for only the module name is possible (roughly equivalent to <code>ASDF:LOAD-OP</code>). Slot names don&#39;t need to be exported separately. Global variables can&#39;t be assigned to from outside the module (except with <code>eval(mod, :(var = val))</code> as an escape hatch).</p></li><li><p>Macros start with <code>@</code>, and are not as seamlessly integrated into the language as Common Lisp; consequently, macro usage is not as widespread as in the latter. A form of hygiene for <a href="metaprogramming.html#Metaprogramming">macros</a> is supported by the language. Because of the different surface syntax, there is no equivalent to <code>COMMON-LISP:&amp;BODY</code>.</p></li><li><p><em>All</em> functions are generic and use multiple dispatch. Argument lists don&#39;t have to follow the same template, which leads to a powerful idiom (see <a href="../base/base.html#do"><code>do</code></a>). Optional and keyword arguments are handled differently. Method ambiguities are not resolved like in the Common Lisp Object System, necessitating the definition of a more specific method for the intersection.</p></li><li><p>Symbols do not belong to any package, and do not contain any values <em>per se</em>. <code>M.var</code> evaluates the symbol <code>var</code> in the module <code>M</code>.</p></li><li><p>A functional programming style is fully supported by the language, including closures, but isn&#39;t always the idiomatic solution for Julia. Some <a href="performance-tips.html#man-performance-captured">workarounds</a> may be necessary for performance when modifying captured variables.</p></li></ul></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="faq.html">« Frequently Asked Questions</a><a class="docs-footer-nextpage" href="unicode-input.html">Unicode Input »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
