<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Multi-Threading · The Julia Language</title><meta name="title" content="Multi-Threading · The Julia Language"/><meta property="og:title" content="Multi-Threading · The Julia Language"/><meta property="twitter:title" content="Multi-Threading · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li class="is-active"><a class="tocitem" href="multi-threading.html">Multi-Threading</a><ul class="internal"><li><a class="tocitem" href="#Starting-Julia-with-multiple-threads"><span>Starting Julia with multiple threads</span></a></li><li><a class="tocitem" href="#man-threadpools"><span>Threadpools</span></a></li><li><a class="tocitem" href="#The-@threads-Macro"><span>The <code>@threads</code> Macro</span></a></li><li><a class="tocitem" href="#man-communication-and-data-races"><span>Communication and data-races between threads</span></a></li><li><a class="tocitem" href="#Side-effects-and-mutable-function-arguments"><span>Side effects and mutable function arguments</span></a></li><li><a class="tocitem" href="#@threadcall"><span>@threadcall</span></a></li><li><a class="tocitem" href="#Caveats"><span>Caveats</span></a></li><li><a class="tocitem" href="#man-task-migration"><span>Task Migration</span></a></li><li><a class="tocitem" href="#man-finalizers"><span>Safe use of Finalizers</span></a></li></ul></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="multi-threading.html">Multi-Threading</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="multi-threading.html">Multi-Threading</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/multi-threading.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="man-multithreading"><a class="docs-heading-anchor" href="#man-multithreading">Multi-Threading</a><a id="man-multithreading-1"></a><a class="docs-heading-anchor-permalink" href="#man-multithreading" title="Permalink"></a></h1><p>Visit this <a href="https://julialang.org/blog/2019/07/multithreading/">blog post</a> for a presentation of Julia multi-threading features.</p><h2 id="Starting-Julia-with-multiple-threads"><a class="docs-heading-anchor" href="#Starting-Julia-with-multiple-threads">Starting Julia with multiple threads</a><a id="Starting-Julia-with-multiple-threads-1"></a><a class="docs-heading-anchor-permalink" href="#Starting-Julia-with-multiple-threads" title="Permalink"></a></h2><p>By default, Julia starts up with a single thread of execution. This can be verified by using the command <a href="../base/multi-threading.html#Base.Threads.nthreads"><code>Threads.nthreads()</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; Threads.nthreads()
1</code></pre><p>The number of execution threads is controlled either by using the <code>-t</code>/<code>--threads</code> command line argument or by using the <a href="environment-variables.html#JULIA_NUM_THREADS"><code>JULIA_NUM_THREADS</code></a> environment variable. When both are specified, then <code>-t</code>/<code>--threads</code> takes precedence.</p><p>The number of threads can either be specified as an integer (<code>--threads=4</code>) or as <code>auto</code> (<code>--threads=auto</code>), where <code>auto</code> tries to infer a useful default number of threads to use (see <a href="command-line-interface.html#command-line-interface">Command-line Options</a> for more details).</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p>The <code>-t</code>/<code>--threads</code> command line argument requires at least Julia 1.5. In older versions you must use the environment variable instead.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Using <code>auto</code> as value of the environment variable <a href="environment-variables.html#JULIA_NUM_THREADS"><code>JULIA_NUM_THREADS</code></a> requires at least Julia 1.7. In older versions, this value is ignored.</p></div></div><p>Lets start Julia with 4 threads:</p><pre><code class="language-bash hljs">$ julia --threads 4</code></pre><p>Let&#39;s verify there are 4 threads at our disposal.</p><pre><code class="language-julia-repl hljs">julia&gt; Threads.nthreads()
4</code></pre><p>But we are currently on the master thread. To check, we use the function <a href="../base/multi-threading.html#Base.Threads.threadid"><code>Threads.threadid</code></a></p><pre><code class="language-julia-repl hljs">julia&gt; Threads.threadid()
1</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>If you prefer to use the environment variable you can set it as follows in Bash (Linux/macOS):</p><pre><code class="language-bash hljs">export JULIA_NUM_THREADS=4</code></pre><p>C shell on Linux/macOS, CMD on Windows:</p><pre><code class="language-bash hljs">set JULIA_NUM_THREADS=4</code></pre><p>Powershell on Windows:</p><pre><code class="language-powershell hljs">$env:JULIA_NUM_THREADS=4</code></pre><p>Note that this must be done <em>before</em> starting Julia.</p></div></div><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The number of threads specified with <code>-t</code>/<code>--threads</code> is propagated to worker processes that are spawned using the <code>-p</code>/<code>--procs</code> or <code>--machine-file</code> command line options. For example, <code>julia -p2 -t2</code> spawns 1 main process with 2 worker processes, and all three processes have 2 threads enabled. For more fine grained control over worker threads use <a href="../stdlib/Distributed.html#Distributed.addprocs"><code>addprocs</code></a> and pass <code>-t</code>/<code>--threads</code> as <code>exeflags</code>.</p></div></div><h3 id="Multiple-GC-Threads"><a class="docs-heading-anchor" href="#Multiple-GC-Threads">Multiple GC Threads</a><a id="Multiple-GC-Threads-1"></a><a class="docs-heading-anchor-permalink" href="#Multiple-GC-Threads" title="Permalink"></a></h3><p>The Garbage Collector (GC) can use multiple threads. The amount used is either half the number of compute worker threads or configured by either the <code>--gcthreads</code> command line argument or by using the <a href="environment-variables.html#JULIA_NUM_GC_THREADS"><code>JULIA_NUM_GC_THREADS</code></a> environment variable.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.10</header><div class="admonition-body"><p>The <code>--gcthreads</code> command line argument requires at least Julia 1.10.</p></div></div><h2 id="man-threadpools"><a class="docs-heading-anchor" href="#man-threadpools">Threadpools</a><a id="man-threadpools-1"></a><a class="docs-heading-anchor-permalink" href="#man-threadpools" title="Permalink"></a></h2><p>When a program&#39;s threads are busy with many tasks to run, tasks may experience delays which may negatively affect the responsiveness and interactivity of the program. To address this, you can specify that a task is interactive when you <a href="../base/multi-threading.html#Base.Threads.@spawn"><code>Threads.@spawn</code></a> it:</p><pre><code class="language-julia hljs">using Base.Threads
@spawn :interactive f()</code></pre><p>Interactive tasks should avoid performing high latency operations, and if they are long duration tasks, should yield frequently.</p><p>Julia may be started with one or more threads reserved to run interactive tasks:</p><pre><code class="language-bash hljs">$ julia --threads 3,1</code></pre><p>The environment variable <a href="environment-variables.html#JULIA_NUM_THREADS"><code>JULIA_NUM_THREADS</code></a> can also be used similarly:</p><pre><code class="language-bash hljs">export JULIA_NUM_THREADS=3,1</code></pre><p>This starts Julia with 3 threads in the <code>:default</code> threadpool and 1 thread in the <code>:interactive</code> threadpool:</p><pre><code class="language-julia-repl hljs">julia&gt; using Base.Threads

julia&gt; nthreadpools()
2

julia&gt; threadpool() # the main thread is in the interactive thread pool
:interactive

julia&gt; nthreads(:default)
3

julia&gt; nthreads(:interactive)
1

julia&gt; nthreads()
3</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The zero-argument version of <code>nthreads</code> returns the number of threads in the default pool.</p></div></div><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Depending on whether Julia has been started with interactive threads, the main thread is either in the default or interactive thread pool.</p></div></div><p>Either or both numbers can be replaced with the word <code>auto</code>, which causes Julia to choose a reasonable default.</p><h2 id="The-@threads-Macro"><a class="docs-heading-anchor" href="#The-@threads-Macro">The <code>@threads</code> Macro</a><a id="The-@threads-Macro-1"></a><a class="docs-heading-anchor-permalink" href="#The-@threads-Macro" title="Permalink"></a></h2><p>Let&#39;s work a simple example using our native threads. Let us create an array of zeros:</p><pre><code class="language-julia-repl hljs">julia&gt; a = zeros(10)
10-element Vector{Float64}:
 0.0
 0.0
 0.0
 0.0
 0.0
 0.0
 0.0
 0.0
 0.0
 0.0</code></pre><p>Let us operate on this array simultaneously using 4 threads. We&#39;ll have each thread write its thread ID into each location.</p><p>Julia supports parallel loops using the <a href="../base/multi-threading.html#Base.Threads.@threads"><code>Threads.@threads</code></a> macro. This macro is affixed in front of a <code>for</code> loop to indicate to Julia that the loop is a multi-threaded region:</p><pre><code class="language-julia-repl hljs">julia&gt; Threads.@threads for i = 1:10
           a[i] = Threads.threadid()
       end</code></pre><p>The iteration space is split among the threads, after which each thread writes its thread ID to its assigned locations:</p><pre><code class="language-julia-repl hljs">julia&gt; a
10-element Vector{Float64}:
 1.0
 1.0
 1.0
 2.0
 2.0
 2.0
 3.0
 3.0
 4.0
 4.0</code></pre><p>Note that <a href="../base/multi-threading.html#Base.Threads.@threads"><code>Threads.@threads</code></a> does not have an optional reduction parameter like <a href="../stdlib/Distributed.html#Distributed.@distributed"><code>@distributed</code></a>.</p><h3 id="Using-@threads-without-data-races"><a class="docs-heading-anchor" href="#Using-@threads-without-data-races">Using <code>@threads</code> without data-races</a><a id="Using-@threads-without-data-races-1"></a><a class="docs-heading-anchor-permalink" href="#Using-@threads-without-data-races" title="Permalink"></a></h3><p>The concept of a data-race is elaborated on in <a href="multi-threading.html#man-communication-and-data-races">&quot;Communication and data races between threads&quot;</a>. For now, just known that a data race can result in incorrect results and dangerous errors.</p><p>Lets say we want to make the function <code>sum_single</code> below multithreaded.</p><pre><code class="language-julia-repl hljs">julia&gt; function sum_single(a)
           s = 0
           for i in a
               s += i
           end
           s
       end
sum_single (generic function with 1 method)

julia&gt; sum_single(1:1_000_000)
500000500000</code></pre><p>Simply adding <code>@threads</code> exposes a data race with multiple threads reading and writing <code>s</code> at the same time.</p><pre><code class="language-julia-repl hljs">julia&gt; function sum_multi_bad(a)
           s = 0
           Threads.@threads for i in a
               s += i
           end
           s
       end
sum_multi_bad (generic function with 1 method)

julia&gt; sum_multi_bad(1:1_000_000)
70140554652</code></pre><p>Note that the result is not <code>500000500000</code> as it should be, and will most likely change each evaluation.</p><p>To fix this, buffers that are specific to the task may be used to segment the sum into chunks that are race-free. Here <code>sum_single</code> is reused, with its own internal buffer <code>s</code>. The input vector <code>a</code> is split into <code>nthreads()</code> chunks for parallel work. We then use <code>Threads.@spawn</code> to create tasks that individually sum each chunk. Finally, we sum the results from each task using <code>sum_single</code> again:</p><pre><code class="language-julia-repl hljs">julia&gt; function sum_multi_good(a)
           chunks = Iterators.partition(a, length(a) ÷ Threads.nthreads())
           tasks = map(chunks) do chunk
               Threads.@spawn sum_single(chunk)
           end
           chunk_sums = fetch.(tasks)
           return sum_single(chunk_sums)
       end
sum_multi_good (generic function with 1 method)

julia&gt; sum_multi_good(1:1_000_000)
500000500000</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Buffers should not be managed based on <code>threadid()</code> i.e. <code>buffers = zeros(Threads.nthreads())</code> because concurrent tasks can yield, meaning multiple concurrent tasks may use the same buffer on a given thread, introducing risk of data races. Further, when more than one thread is available tasks may change thread at yield points, which is known as <a href="multi-threading.html#man-task-migration">task migration</a>.</p></div></div><p>Another option is the use of atomic operations on variables shared across tasks/threads, which may be more performant depending on the characteristics of the operations.</p><h2 id="man-communication-and-data-races"><a class="docs-heading-anchor" href="#man-communication-and-data-races">Communication and data-races between threads</a><a id="man-communication-and-data-races-1"></a><a class="docs-heading-anchor-permalink" href="#man-communication-and-data-races" title="Permalink"></a></h2><p>Although Julia&#39;s threads can communicate through shared memory, it is notoriously difficult to write correct and data-race free multi-threaded code. Julia&#39;s <a href="../base/parallel.html#Base.Channel"><code>Channel</code></a>s are thread-safe and may be used to communicate safely. There are also sections below that explain how to use <a href="multi-threading.html#man-using-locks">locks</a> and <a href="multi-threading.html#man-atomic-operations">atomics</a> to avoid data-races.</p><h3 id="Data-race-freedom"><a class="docs-heading-anchor" href="#Data-race-freedom">Data-race freedom</a><a id="Data-race-freedom-1"></a><a class="docs-heading-anchor-permalink" href="#Data-race-freedom" title="Permalink"></a></h3><p>You are entirely responsible for ensuring that your program is data-race free, and nothing promised here can be assumed if you do not observe that requirement. The observed results may be highly unintuitive.</p><p>If data-races are introduced, Julia is not memory safe. <strong>Be very careful about reading <em>any</em> data if another thread might write to it, as it could result in segmentation faults or worse</strong>. Below are a couple of unsafe ways to access global variables from different threads:</p><pre><code class="language-julia hljs">Thread 1:
global b = false
global a = rand()
global b = true

Thread 2:
while !b; end
bad_read1(a) # it is NOT safe to access `a` here!

Thread 3:
while !@isdefined(a); end
bad_read2(a) # it is NOT safe to access `a` here</code></pre><h3 id="man-using-locks"><a class="docs-heading-anchor" href="#man-using-locks">Using locks to avoid data-races</a><a id="man-using-locks-1"></a><a class="docs-heading-anchor-permalink" href="#man-using-locks" title="Permalink"></a></h3><p>An important tool to avoid data-races, and thereby write thread-safe code, is the concept of a &quot;lock&quot;. A lock can be locked and unlocked. If a thread has locked a lock, and not unlocked it, it is said to &quot;hold&quot; the lock. If there is only one lock, and we write code the requires holding the lock to access some data, we can ensure that multiple threads will never access the same data simultaneously. Note that the link between a lock and a variable is made by the programmer, and not the program.</p><p>For example, we can create a lock <code>my_lock</code>, and lock it while we mutate a variable <code>my_variable</code>. This is done most simply with the <code>@lock</code> macro:</p><pre><code class="language-julia-repl hljs">julia&gt; my_lock = ReentrantLock();

julia&gt; my_variable = [1, 2, 3];

julia&gt; @lock my_lock my_variable[1] = 100
100</code></pre><p>By using a similar pattern with the same lock and variable, but on another thread, the operations are free from data-races.</p><p>We could have performed the operation above with the functional version of <code>lock</code>, in the following two ways:</p><pre><code class="language-julia-repl hljs">julia&gt; lock(my_lock) do
           my_variable[1] = 100
       end
100

julia&gt; begin
           lock(my_lock)
           try
               my_variable[1] = 100
           finally
               unlock(my_lock)
           end
       end
100</code></pre><p>All three options are equivalent. Note how the final version requires an explicit <code>try</code>-block to ensure that the lock is always unlocked, whereas the first two version do this internally. One should always use the lock pattern above when changing data (such as assigning to a global or closure variable) accessed by other threads. Failing to do this could have unforeseen and serious consequences.</p><h3 id="man-atomic-operations"><a class="docs-heading-anchor" href="#man-atomic-operations">Atomic Operations</a><a id="man-atomic-operations-1"></a><a class="docs-heading-anchor-permalink" href="#man-atomic-operations" title="Permalink"></a></h3><p>Julia supports accessing and modifying values <em>atomically</em>, that is, in a thread-safe way to avoid <a href="https://en.wikipedia.org/wiki/Race_condition">race conditions</a>. A value (which must be of a primitive type) can be wrapped as <a href="../base/multi-threading.html#Base.Threads.Atomic"><code>Threads.Atomic</code></a> to indicate it must be accessed in this way. Here we can see an example:</p><pre><code class="language-julia-repl hljs">julia&gt; i = Threads.Atomic{Int}(0);

julia&gt; ids = zeros(4);

julia&gt; old_is = zeros(4);

julia&gt; Threads.@threads for id in 1:4
           old_is[id] = Threads.atomic_add!(i, id)
           ids[id] = id
       end

julia&gt; old_is
4-element Vector{Float64}:
 0.0
 1.0
 7.0
 3.0

julia&gt; i[]
 10

julia&gt; ids
4-element Vector{Float64}:
 1.0
 2.0
 3.0
 4.0</code></pre><p>Had we tried to do the addition without the atomic tag, we might have gotten the wrong answer due to a race condition. An example of what would happen if we didn&#39;t avoid the race:</p><pre><code class="language-julia-repl hljs">julia&gt; using Base.Threads

julia&gt; Threads.nthreads()
4

julia&gt; acc = Ref(0)
Base.RefValue{Int64}(0)

julia&gt; @threads for i in 1:1000
          acc[] += 1
       end

julia&gt; acc[]
926

julia&gt; acc = Atomic{Int64}(0)
Atomic{Int64}(0)

julia&gt; @threads for i in 1:1000
          atomic_add!(acc, 1)
       end

julia&gt; acc[]
1000</code></pre><h4 id="man-atomics"><a class="docs-heading-anchor" href="#man-atomics">Per-field atomics</a><a id="man-atomics-1"></a><a class="docs-heading-anchor-permalink" href="#man-atomics" title="Permalink"></a></h4><p>We can also use atomics on a more granular level using the <a href="../base/multi-threading.html#Base.@atomic"><code>@atomic</code></a>, <a href="../base/multi-threading.html#Base.@atomicswap"><code>@atomicswap</code></a>, <a href="../base/multi-threading.html#Base.@atomicreplace"><code>@atomicreplace</code></a> macros, and <a href="../base/multi-threading.html#Base.@atomiconce"><code>@atomiconce</code></a> macros.</p><p>Specific details of the memory model and other details of the design are written in the <a href="https://gist.github.com/vtjnash/11b0031f2e2a66c9c24d33e810b34ec0">Julia Atomics Manifesto</a>, which will later be published formally.</p><p>Any field in a struct declaration can be decorated with <code>@atomic</code>, and then any write must be marked with <code>@atomic</code> also, and must use one of the defined atomic orderings (<code>:monotonic</code>, <code>:acquire</code>, <code>:release</code>, <code>:acquire_release</code>, or <code>:sequentially_consistent</code>). Any read of an atomic field can also be annotated with an atomic ordering constraint, or will be done with monotonic (relaxed) ordering if unspecified.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Per-field atomics requires at least Julia 1.7.</p></div></div><h2 id="Side-effects-and-mutable-function-arguments"><a class="docs-heading-anchor" href="#Side-effects-and-mutable-function-arguments">Side effects and mutable function arguments</a><a id="Side-effects-and-mutable-function-arguments-1"></a><a class="docs-heading-anchor-permalink" href="#Side-effects-and-mutable-function-arguments" title="Permalink"></a></h2><p>When using multi-threading we have to be careful when using functions that are not <a href="https://en.wikipedia.org/wiki/Pure_function">pure</a> as we might get a wrong answer. For instance functions that have a <a href="style-guide.html#bang-convention">name ending with <code>!</code></a> by convention modify their arguments and thus are not pure.</p><h2 id="@threadcall"><a class="docs-heading-anchor" href="#@threadcall">@threadcall</a><a id="@threadcall-1"></a><a class="docs-heading-anchor-permalink" href="#@threadcall" title="Permalink"></a></h2><p>External libraries, such as those called via <a href="../base/c.html#ccall"><code>ccall</code></a>, pose a problem for Julia&#39;s task-based I/O mechanism. If a C library performs a blocking operation, that prevents the Julia scheduler from executing any other tasks until the call returns. (Exceptions are calls into custom C code that call back into Julia, which may then yield, or C code that calls <code>jl_yield()</code>, the C equivalent of <a href="../base/parallel.html#Base.yield"><code>yield</code></a>.)</p><p>The <a href="multi-threading.html#@threadcall"><code>@threadcall</code></a> macro provides a way to avoid stalling execution in such a scenario. It schedules a C function for execution in a separate thread. A threadpool with a default size of 4 is used for this. The size of the threadpool is controlled via environment variable <code>UV_THREADPOOL_SIZE</code>. While waiting for a free thread, and during function execution once a thread is available, the requesting task (on the main Julia event loop) yields to other tasks. Note that <code>@threadcall</code> does not return until the execution is complete. From a user point of view, it is therefore a blocking call like other Julia APIs.</p><p>It is very important that the called function does not call back into Julia, as it will segfault.</p><p><code>@threadcall</code> may be removed/changed in future versions of Julia.</p><h2 id="Caveats"><a class="docs-heading-anchor" href="#Caveats">Caveats</a><a id="Caveats-1"></a><a class="docs-heading-anchor-permalink" href="#Caveats" title="Permalink"></a></h2><p>At this time, most operations in the Julia runtime and standard libraries can be used in a thread-safe manner, if the user code is data-race free. However, in some areas work on stabilizing thread support is ongoing. Multi-threaded programming has many inherent difficulties, and if a program using threads exhibits unusual or undesirable behavior (e.g. crashes or mysterious results), thread interactions should typically be suspected first.</p><p>There are a few specific limitations and warnings to be aware of when using threads in Julia:</p><ul><li>Base collection types require manual locking if used simultaneously by multiple threads where at least one thread modifies the collection (common examples include <code>push!</code> on arrays, or inserting items into a <code>Dict</code>).</li><li>The schedule used by <a href="../base/multi-threading.html#Base.Threads.@spawn"><code>@spawn</code></a> is nondeterministic and should not be relied on.</li><li>Compute-bound, non-memory-allocating tasks can prevent garbage collection from running in other threads that are allocating memory. In these cases it may be necessary to insert a manual call to <code>GC.safepoint()</code> to allow GC to run. This limitation will be removed in the future.</li><li>Avoid running top-level operations, e.g. <code>include</code>, or <code>eval</code> of type, method, and module definitions in parallel.</li><li>Be aware that finalizers registered by a library may break if threads are enabled. This may require some transitional work across the ecosystem before threading can be widely adopted with confidence. See the section on <a href="multi-threading.html#man-finalizers">the safe use of finalizers</a> for further details.</li></ul><h2 id="man-task-migration"><a class="docs-heading-anchor" href="#man-task-migration">Task Migration</a><a id="man-task-migration-1"></a><a class="docs-heading-anchor-permalink" href="#man-task-migration" title="Permalink"></a></h2><p>After a task starts running on a certain thread it may move to a different thread if the task yields.</p><p>Such tasks may have been started with <a href="../base/multi-threading.html#Base.Threads.@spawn"><code>@spawn</code></a> or <a href="../base/multi-threading.html#Base.Threads.@threads"><code>@threads</code></a>, although the <code>:static</code> schedule option for <code>@threads</code> does freeze the threadid.</p><p>This means that in most cases <a href="../base/multi-threading.html#Base.Threads.threadid"><code>threadid()</code></a> should not be treated as constant within a task, and therefore should not be used to index into a vector of buffers or stateful objects.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Task migration was introduced in Julia 1.7. Before this tasks always remained on the same thread that they were started on.</p></div></div><h2 id="man-finalizers"><a class="docs-heading-anchor" href="#man-finalizers">Safe use of Finalizers</a><a id="man-finalizers-1"></a><a class="docs-heading-anchor-permalink" href="#man-finalizers" title="Permalink"></a></h2><p>Because finalizers can interrupt any code, they must be very careful in how they interact with any global state. Unfortunately, the main reason that finalizers are used is to update global state (a pure function is generally rather pointless as a finalizer). This leads us to a bit of a conundrum. There are a few approaches to dealing with this problem:</p><ol><li><p>When single-threaded, code could call the internal <code>jl_gc_enable_finalizers</code> C function to prevent finalizers from being scheduled inside a critical region. Internally, this is used inside some functions (such as our C locks) to prevent recursion when doing certain operations (incremental package loading, codegen, etc.). The combination of a lock and this flag can be used to make finalizers safe.</p></li><li><p>A second strategy, employed by Base in a couple places, is to explicitly delay a finalizer until it may be able to acquire its lock non-recursively. The following example demonstrates how this strategy could be applied to <code>Distributed.finalize_ref</code>:</p><pre><code class="language-julia hljs">function finalize_ref(r::AbstractRemoteRef)
    if r.where &gt; 0 # Check if the finalizer is already run
        if islocked(client_refs) || !trylock(client_refs)
            # delay finalizer for later if we aren&#39;t free to acquire the lock
            finalizer(finalize_ref, r)
            return nothing
        end
        try # `lock` should always be followed by `try`
            if r.where &gt; 0 # Must check again here
                # Do actual cleanup here
                r.where = 0
            end
        finally
            unlock(client_refs)
        end
    end
    nothing
end</code></pre></li><li><p>A related third strategy is to use a yield-free queue. We don&#39;t currently have a lock-free queue implemented in Base, but <code>Base.IntrusiveLinkedListSynchronized{T}</code> is suitable. This can frequently be a good strategy to use for code with event loops. For example, this strategy is employed by <code>Gtk.jl</code> to manage lifetime ref-counting. In this approach, we don&#39;t do any explicit work inside the <code>finalizer</code>, and instead add it to a queue to run at a safer time. In fact, Julia&#39;s task scheduler already uses this, so defining the finalizer as <code>x -&gt; @spawn do_cleanup(x)</code> is one example of this approach. Note however that this doesn&#39;t control which thread <code>do_cleanup</code> runs on, so <code>do_cleanup</code> would still need to acquire a lock. That doesn&#39;t need to be true if you implement your own queue, as you can explicitly only drain that queue from your thread.</p></li></ol></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="asynchronous-programming.html">« Asynchronous Programming</a><a class="docs-footer-nextpage" href="distributed-computing.html">Multi-processing and Distributed Computing »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
