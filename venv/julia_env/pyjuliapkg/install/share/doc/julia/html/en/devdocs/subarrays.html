<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>SubArrays · The Julia Language</title><meta name="title" content="SubArrays · The Julia Language"/><meta property="og:title" content="SubArrays · The Julia Language"/><meta property="twitter:title" content="SubArrays · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox" checked/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="ast.html">Julia ASTs</a></li><li><a class="tocitem" href="types.html">More about types</a></li><li><a class="tocitem" href="object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="functions.html">Julia Functions</a></li><li><a class="tocitem" href="cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li class="is-active"><a class="tocitem" href="subarrays.html">SubArrays</a><ul class="internal"><li><a class="tocitem" href="#Index-replacement"><span>Index replacement</span></a></li><li><a class="tocitem" href="#SubArray-design"><span>SubArray design</span></a></li></ul></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Documentation of Julia&#39;s Internals</a></li><li class="is-active"><a href="subarrays.html">SubArrays</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="subarrays.html">SubArrays</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/subarrays.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="SubArrays"><a class="docs-heading-anchor" href="#SubArrays">SubArrays</a><a id="SubArrays-1"></a><a class="docs-heading-anchor-permalink" href="#SubArrays" title="Permalink"></a></h1><p>Julia&#39;s <code>SubArray</code> type is a container encoding a &quot;view&quot; of a parent <a href="../base/arrays.html#Core.AbstractArray"><code>AbstractArray</code></a>.  This page documents some of the design principles and implementation of <code>SubArray</code>s.</p><p>One of the major design goals is to ensure high performance for views of both <a href="../base/arrays.html#Base.IndexLinear"><code>IndexLinear</code></a> and <a href="../base/arrays.html#Base.IndexCartesian"><code>IndexCartesian</code></a> arrays. Furthermore, views of <code>IndexLinear</code> arrays should themselves be <code>IndexLinear</code> to the extent that it is possible.</p><h2 id="Index-replacement"><a class="docs-heading-anchor" href="#Index-replacement">Index replacement</a><a id="Index-replacement-1"></a><a class="docs-heading-anchor-permalink" href="#Index-replacement" title="Permalink"></a></h2><p>Consider making 2d slices of a 3d array:</p><pre><code class="language-julia-repl hljs">julia&gt; A = rand(2,3,4);

julia&gt; S1 = view(A, :, 1, 2:3)
2×2 view(::Array{Float64, 3}, :, 1, 2:3) with eltype Float64:
 0.839622  0.711389
 0.967143  0.103929

julia&gt; S2 = view(A, 1, :, 2:3)
3×2 view(::Array{Float64, 3}, 1, :, 2:3) with eltype Float64:
 0.839622  0.711389
 0.789764  0.806704
 0.566704  0.962715</code></pre><p><code>view</code> drops &quot;singleton&quot; dimensions (ones that are specified by an <code>Int</code>), so both <code>S1</code> and <code>S2</code> are two-dimensional <code>SubArray</code>s. Consequently, the natural way to index these is with <code>S1[i,j]</code>. To extract the value from the parent array <code>A</code>, the natural approach is to replace <code>S1[i,j]</code> with <code>A[i,1,(2:3)[j]]</code> and <code>S2[i,j]</code> with <code>A[1,i,(2:3)[j]]</code>.</p><p>The key feature of the design of SubArrays is that this index replacement can be performed without any runtime overhead.</p><h2 id="SubArray-design"><a class="docs-heading-anchor" href="#SubArray-design">SubArray design</a><a id="SubArray-design-1"></a><a class="docs-heading-anchor-permalink" href="#SubArray-design" title="Permalink"></a></h2><h3 id="Type-parameters-and-fields"><a class="docs-heading-anchor" href="#Type-parameters-and-fields">Type parameters and fields</a><a id="Type-parameters-and-fields-1"></a><a class="docs-heading-anchor-permalink" href="#Type-parameters-and-fields" title="Permalink"></a></h3><p>The strategy adopted is first and foremost expressed in the definition of the type:</p><pre><code class="language-julia hljs">struct SubArray{T,N,P,I,L} &lt;: AbstractArray{T,N}
    parent::P
    indices::I
    offset1::Int       # for linear indexing and pointer, only valid when L==true
    stride1::Int       # used only for linear indexing
    ...
end</code></pre><p><code>SubArray</code> has 5 type parameters.  The first two are the standard element type and dimensionality.  The next is the type of the parent <code>AbstractArray</code>.  The most heavily-used is the fourth parameter, a <code>Tuple</code> of the types of the indices for each dimension. The final one, <code>L</code>, is only provided as a convenience for dispatch; it&#39;s a boolean that represents whether the index types support fast linear indexing. More on that later.</p><p>If in our example above <code>A</code> is a <code>Array{Float64, 3}</code>, our <code>S1</code> case above would be a <code>SubArray{Float64,2,Array{Float64,3},Tuple{Base.Slice{Base.OneTo{Int64}},Int64,UnitRange{Int64}},false}</code>. Note in particular the tuple parameter, which stores the types of the indices used to create <code>S1</code>. Likewise,</p><pre><code class="language-julia-repl hljs">julia&gt; S1.indices
(Base.Slice(Base.OneTo(2)), 1, 2:3)</code></pre><p>Storing these values allows index replacement, and having the types encoded as parameters allows one to dispatch to efficient algorithms.</p><h3 id="Index-translation"><a class="docs-heading-anchor" href="#Index-translation">Index translation</a><a id="Index-translation-1"></a><a class="docs-heading-anchor-permalink" href="#Index-translation" title="Permalink"></a></h3><p>Performing index translation requires that you do different things for different concrete <code>SubArray</code> types.  For example, for <code>S1</code>, one needs to apply the <code>i,j</code> indices to the first and third dimensions of the parent array, whereas for <code>S2</code> one needs to apply them to the second and third.  The simplest approach to indexing would be to do the type-analysis at runtime:</p><pre><code class="language-julia hljs">parentindices = Vector{Any}()
for thisindex in S.indices
    ...
    if isa(thisindex, Int)
        # Don&#39;t consume one of the input indices
        push!(parentindices, thisindex)
    elseif isa(thisindex, AbstractVector)
        # Consume an input index
        push!(parentindices, thisindex[inputindex[j]])
        j += 1
    elseif isa(thisindex, AbstractMatrix)
        # Consume two input indices
        push!(parentindices, thisindex[inputindex[j], inputindex[j+1]])
        j += 2
    elseif ...
end
S.parent[parentindices...]</code></pre><p>Unfortunately, this would be disastrous in terms of performance: each element access would allocate memory, and involves the running of a lot of poorly-typed code.</p><p>The better approach is to dispatch to specific methods to handle each type of stored index. That&#39;s what <code>reindex</code> does: it dispatches on the type of the first stored index and consumes the appropriate number of input indices, and then it recurses on the remaining indices. In the case of <code>S1</code>, this expands to</p><pre><code class="language-julia hljs">Base.reindex(S1, S1.indices, (i, j)) == (i, S1.indices[2], S1.indices[3][j])</code></pre><p>for any pair of indices <code>(i,j)</code> (except <a href="../base/arrays.html#Base.IteratorsMD.CartesianIndex"><code>CartesianIndex</code></a>s and arrays thereof, see below).</p><p>This is the core of a <code>SubArray</code>; indexing methods depend upon <code>reindex</code> to do this index translation. Sometimes, though, we can avoid the indirection and make it even faster.</p><h3 id="Linear-indexing"><a class="docs-heading-anchor" href="#Linear-indexing">Linear indexing</a><a id="Linear-indexing-1"></a><a class="docs-heading-anchor-permalink" href="#Linear-indexing" title="Permalink"></a></h3><p>Linear indexing can be implemented efficiently when the entire array has a single stride that separates successive elements, starting from some offset. This means that we can pre-compute these values and represent linear indexing simply as an addition and multiplication, avoiding the indirection of <code>reindex</code> and (more importantly) the slow computation of the cartesian coordinates entirely.</p><p>For <code>SubArray</code> types, the availability of efficient linear indexing is based purely on the types of the indices, and does not depend on values like the size of the parent array. You can ask whether a given set of indices supports fast linear indexing with the internal <code>Base.viewindexing</code> function:</p><pre><code class="language-julia-repl hljs">julia&gt; Base.viewindexing(S1.indices)
IndexCartesian()

julia&gt; Base.viewindexing(S2.indices)
IndexLinear()</code></pre><p>This is computed during construction of the <code>SubArray</code> and stored in the <code>L</code> type parameter as a boolean that encodes fast linear indexing support. While not strictly necessary, it means that we can define dispatch directly on <code>SubArray{T,N,A,I,true}</code> without any intermediaries.</p><p>Since this computation doesn&#39;t depend on runtime values, it can miss some cases in which the stride happens to be uniform:</p><pre><code class="language-julia-repl hljs">julia&gt; A = reshape(1:4*2, 4, 2)
4×2 reshape(::UnitRange{Int64}, 4, 2) with eltype Int64:
 1  5
 2  6
 3  7
 4  8

julia&gt; diff(A[2:2:4,:][:])
3-element Vector{Int64}:
 2
 2
 2</code></pre><p>A view constructed as <code>view(A, 2:2:4, :)</code> happens to have uniform stride, and therefore linear indexing indeed could be performed efficiently.  However, success in this case depends on the size of the array: if the first dimension instead were odd,</p><pre><code class="language-julia-repl hljs">julia&gt; A = reshape(1:5*2, 5, 2)
5×2 reshape(::UnitRange{Int64}, 5, 2) with eltype Int64:
 1   6
 2   7
 3   8
 4   9
 5  10

julia&gt; diff(A[2:2:4,:][:])
3-element Vector{Int64}:
 2
 3
 2</code></pre><p>then <code>A[2:2:4,:]</code> does not have uniform stride, so we cannot guarantee efficient linear indexing.  Since we have to base this decision based purely on types encoded in the parameters of the <code>SubArray</code>, <code>S = view(A, 2:2:4, :)</code> cannot implement efficient linear indexing.</p><h3 id="A-few-details"><a class="docs-heading-anchor" href="#A-few-details">A few details</a><a id="A-few-details-1"></a><a class="docs-heading-anchor-permalink" href="#A-few-details" title="Permalink"></a></h3><ul><li><p>Note that the <code>Base.reindex</code> function is agnostic to the types of the input indices; it simply determines how and where the stored indices should be reindexed. It not only supports integer indices, but it supports non-scalar indexing, too. This means that views of views don&#39;t need two levels of indirection; they can simply re-compute the indices into the original parent array!</p></li><li><p>Hopefully by now it&#39;s fairly clear that supporting slices means that the dimensionality, given by the parameter <code>N</code>, is not necessarily equal to the dimensionality of the parent array or the length of the <code>indices</code> tuple.  Neither do user-supplied indices necessarily line up with entries in the <code>indices</code> tuple (e.g., the second user-supplied index might correspond to the third dimension of the parent array, and the third element in the <code>indices</code> tuple).</p><p>What might be less obvious is that the dimensionality of the stored parent array must be equal to the number of effective indices in the <code>indices</code> tuple. Some examples:</p><pre><code class="language-julia hljs">A = reshape(1:35, 5, 7) # A 2d parent Array
S = view(A, 2:7)         # A 1d view created by linear indexing
S = view(A, :, :, 1:1)   # Appending extra indices is supported</code></pre><p>Naively, you&#39;d think you could just set <code>S.parent = A</code> and <code>S.indices = (:,:,1:1)</code>, but supporting this dramatically complicates the reindexing process, especially for views of views. Not only do you need to dispatch on the types of the stored indices, but you need to examine whether a given index is the final one and &quot;merge&quot; any remaining stored indices together. This is not an easy task, and even worse: it&#39;s slow since it implicitly depends upon linear indexing.</p><p>Fortunately, this is precisely the computation that <code>ReshapedArray</code> performs, and it does so linearly if possible. Consequently, <code>view</code> ensures that the parent array is the appropriate dimensionality for the given indices by reshaping it if needed. The inner <code>SubArray</code> constructor ensures that this invariant is satisfied.</p></li><li><p><a href="../base/arrays.html#Base.IteratorsMD.CartesianIndex"><code>CartesianIndex</code></a> and arrays thereof throw a nasty wrench into the <code>reindex</code> scheme. Recall that <code>reindex</code> simply dispatches on the type of the stored indices in order to determine how many passed indices should be used and where they should go. But with <code>CartesianIndex</code>, there&#39;s no longer a one-to-one correspondence between the number of passed arguments and the number of dimensions that they index into. If we return to the above example of <code>Base.reindex(S1, S1.indices, (i, j))</code>, you can see that the expansion is incorrect for <code>i, j = CartesianIndex(), CartesianIndex(2,1)</code>. It should <em>skip</em> the <code>CartesianIndex()</code> entirely and return:</p><pre><code class="language-julia hljs">(CartesianIndex(2,1)[1], S1.indices[2], S1.indices[3][CartesianIndex(2,1)[2]])</code></pre><p>Instead, though, we get:</p><pre><code class="language-julia hljs">(CartesianIndex(), S1.indices[2], S1.indices[3][CartesianIndex(2,1)])</code></pre><p>Doing this correctly would require <em>combined</em> dispatch on both the stored and passed indices across all combinations of dimensionalities in an intractable manner. As such, <code>reindex</code> must never be called with <code>CartesianIndex</code> indices. Fortunately, the scalar case is easily handled by first flattening the <code>CartesianIndex</code> arguments to plain integers. Arrays of <code>CartesianIndex</code>, however, cannot be split apart into orthogonal pieces so easily. Before attempting to use <code>reindex</code>, <code>view</code> must ensure that there are no arrays of <code>CartesianIndex</code> in the argument list. If there are, it can simply &quot;punt&quot; by avoiding the <code>reindex</code> calculation entirely, constructing a nested <code>SubArray</code> with two levels of indirection instead.</p></li></ul></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="meta.html">« Talking to the compiler (the <code>:meta</code> mechanism)</a><a class="docs-footer-nextpage" href="isbitsunionarrays.html">isbits Union Optimizations »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
