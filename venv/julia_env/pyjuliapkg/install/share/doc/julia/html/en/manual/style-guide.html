<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Style Guide · The Julia Language</title><meta name="title" content="Style Guide · The Julia Language"/><meta property="og:title" content="Style Guide · The Julia Language"/><meta property="twitter:title" content="Style Guide · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li class="is-active"><a class="tocitem" href="style-guide.html">Style Guide</a><ul class="internal"><li><a class="tocitem" href="#Indentation"><span>Indentation</span></a></li><li><a class="tocitem" href="#Write-functions,-not-just-scripts"><span>Write functions, not just scripts</span></a></li><li><a class="tocitem" href="#Avoid-writing-overly-specific-types"><span>Avoid writing overly-specific types</span></a></li><li><a class="tocitem" href="#Handle-excess-argument-diversity-in-the-caller"><span>Handle excess argument diversity in the caller</span></a></li><li><a class="tocitem" href="#bang-convention"><span>Append <code>!</code> to names of functions that modify their arguments</span></a></li><li><a class="tocitem" href="#Avoid-strange-type-Unions"><span>Avoid strange type <code>Union</code>s</span></a></li><li><a class="tocitem" href="#Avoid-elaborate-container-types"><span>Avoid elaborate container types</span></a></li><li><a class="tocitem" href="#Prefer-exported-methods-over-direct-field-access"><span>Prefer exported methods over direct field access</span></a></li><li><a class="tocitem" href="#Use-naming-conventions-consistent-with-Julia-base/"><span>Use naming conventions consistent with Julia <code>base/</code></span></a></li><li><a class="tocitem" href="#Write-functions-with-argument-ordering-similar-to-Julia-Base"><span>Write functions with argument ordering similar to Julia Base</span></a></li><li><a class="tocitem" href="#Don&#39;t-overuse-try-catch"><span>Don&#39;t overuse try-catch</span></a></li><li><a class="tocitem" href="#Don&#39;t-parenthesize-conditions"><span>Don&#39;t parenthesize conditions</span></a></li><li><a class="tocitem" href="#Don&#39;t-overuse-..."><span>Don&#39;t overuse <code>...</code></span></a></li><li><a class="tocitem" href="#Ensure-constructors-return-an-instance-of-their-own-type"><span>Ensure constructors return an instance of their own type</span></a></li><li><a class="tocitem" href="#Don&#39;t-use-unnecessary-static-parameters"><span>Don&#39;t use unnecessary static parameters</span></a></li><li><a class="tocitem" href="#Avoid-confusion-about-whether-something-is-an-instance-or-a-type"><span>Avoid confusion about whether something is an instance or a type</span></a></li><li><a class="tocitem" href="#Don&#39;t-overuse-macros"><span>Don&#39;t overuse macros</span></a></li><li><a class="tocitem" href="#Don&#39;t-expose-unsafe-operations-at-the-interface-level"><span>Don&#39;t expose unsafe operations at the interface level</span></a></li><li><a class="tocitem" href="#Don&#39;t-overload-methods-of-base-container-types"><span>Don&#39;t overload methods of base container types</span></a></li><li><a class="tocitem" href="#avoid-type-piracy"><span>Avoid type piracy</span></a></li><li><a class="tocitem" href="#Be-careful-with-type-equality"><span>Be careful with type equality</span></a></li><li><a class="tocitem" href="#Don&#39;t-write-a-trivial-anonymous-function-x-f(x)-for-a-named-function-f"><span>Don&#39;t write a trivial anonymous function <code>x-&gt;f(x)</code> for a named function <code>f</code></span></a></li><li><a class="tocitem" href="#Avoid-using-floats-for-numeric-literals-in-generic-code-when-possible"><span>Avoid using floats for numeric literals in generic code when possible</span></a></li></ul></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="style-guide.html">Style Guide</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="style-guide.html">Style Guide</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/style-guide.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Style-Guide"><a class="docs-heading-anchor" href="#Style-Guide">Style Guide</a><a id="Style-Guide-1"></a><a class="docs-heading-anchor-permalink" href="#Style-Guide" title="Permalink"></a></h1><p>The following sections explain a few aspects of idiomatic Julia coding style. None of these rules are absolute; they are only suggestions to help familiarize you with the language and to help you choose among alternative designs.</p><h2 id="Indentation"><a class="docs-heading-anchor" href="#Indentation">Indentation</a><a id="Indentation-1"></a><a class="docs-heading-anchor-permalink" href="#Indentation" title="Permalink"></a></h2><p>Use 4 spaces per indentation level.</p><h2 id="Write-functions,-not-just-scripts"><a class="docs-heading-anchor" href="#Write-functions,-not-just-scripts">Write functions, not just scripts</a><a id="Write-functions,-not-just-scripts-1"></a><a class="docs-heading-anchor-permalink" href="#Write-functions,-not-just-scripts" title="Permalink"></a></h2><p>Writing code as a series of steps at the top level is a quick way to get started solving a problem, but you should try to divide a program into functions as soon as possible. Functions are more reusable and testable, and clarify what steps are being done and what their inputs and outputs are. Furthermore, code inside functions tends to run much faster than top level code, due to how Julia&#39;s compiler works.</p><p>It is also worth emphasizing that functions should take arguments, instead of operating directly on global variables (aside from constants like <a href="../base/numbers.html#Base.MathConstants.pi"><code>pi</code></a>).</p><h2 id="Avoid-writing-overly-specific-types"><a class="docs-heading-anchor" href="#Avoid-writing-overly-specific-types">Avoid writing overly-specific types</a><a id="Avoid-writing-overly-specific-types-1"></a><a class="docs-heading-anchor-permalink" href="#Avoid-writing-overly-specific-types" title="Permalink"></a></h2><p>Code should be as generic as possible. Instead of writing:</p><pre><code class="language-julia hljs">Complex{Float64}(x)</code></pre><p>it&#39;s better to use available generic functions:</p><pre><code class="language-julia hljs">complex(float(x))</code></pre><p>The second version will convert <code>x</code> to an appropriate type, instead of always the same type.</p><p>This style point is especially relevant to function arguments. For example, don&#39;t declare an argument to be of type <code>Int</code> or <a href="../base/numbers.html#Core.Int32"><code>Int32</code></a> if it really could be any integer, expressed with the abstract type <a href="../base/numbers.html#Core.Integer"><code>Integer</code></a>. In fact, in many cases you can omit the argument type altogether, unless it is needed to disambiguate from other method definitions, since a <a href="../base/base.html#Core.MethodError"><code>MethodError</code></a> will be thrown anyway if a type is passed that does not support any of the requisite operations. (This is known as <a href="https://en.wikipedia.org/wiki/Duck_typing">duck typing</a>.)</p><p>For example, consider the following definitions of a function <code>addone</code> that returns one plus its argument:</p><pre><code class="language-julia hljs">addone(x::Int) = x + 1                 # works only for Int
addone(x::Integer) = x + oneunit(x)    # any integer type
addone(x::Number) = x + oneunit(x)     # any numeric type
addone(x) = x + oneunit(x)             # any type supporting + and oneunit</code></pre><p>The last definition of <code>addone</code> handles any type supporting <a href="../base/numbers.html#Base.oneunit"><code>oneunit</code></a> (which returns 1 in the same type as <code>x</code>, which avoids unwanted type promotion) and the <a href="../base/math.html#Base.:+"><code>+</code></a> function with those arguments. The key thing to realize is that there is <em>no performance penalty</em> to defining <em>only</em> the general <code>addone(x) = x + oneunit(x)</code>, because Julia will automatically compile specialized versions as needed. For example, the first time you call <code>addone(12)</code>, Julia will automatically compile a specialized <code>addone</code> function for <code>x::Int</code> arguments, with the call to <code>oneunit</code> replaced by its inlined value <code>1</code>. Therefore, the first three definitions of <code>addone</code> above are completely redundant with the fourth definition.</p><h2 id="Handle-excess-argument-diversity-in-the-caller"><a class="docs-heading-anchor" href="#Handle-excess-argument-diversity-in-the-caller">Handle excess argument diversity in the caller</a><a id="Handle-excess-argument-diversity-in-the-caller-1"></a><a class="docs-heading-anchor-permalink" href="#Handle-excess-argument-diversity-in-the-caller" title="Permalink"></a></h2><p>Instead of:</p><pre><code class="language-julia hljs">function foo(x, y)
    x = Int(x); y = Int(y)
    ...
end
foo(x, y)</code></pre><p>use:</p><pre><code class="language-julia hljs">function foo(x::Int, y::Int)
    ...
end
foo(Int(x), Int(y))</code></pre><p>This is better style because <code>foo</code> does not really accept numbers of all types; it really needs <code>Int</code> s.</p><p>One issue here is that if a function inherently requires integers, it might be better to force the caller to decide how non-integers should be converted (e.g. floor or ceiling). Another issue is that declaring more specific types leaves more &quot;space&quot; for future method definitions.</p><h2 id="bang-convention"><a class="docs-heading-anchor" href="#bang-convention">Append <code>!</code> to names of functions that modify their arguments</a><a id="bang-convention-1"></a><a class="docs-heading-anchor-permalink" href="#bang-convention" title="Permalink"></a></h2><p>Instead of:</p><pre><code class="language-julia hljs">function double(a::AbstractArray{&lt;:Number})
    for i in eachindex(a)
        a[i] *= 2
    end
    return a
end</code></pre><p>use:</p><pre><code class="language-julia hljs">function double!(a::AbstractArray{&lt;:Number})
    for i in eachindex(a)
        a[i] *= 2
    end
    return a
end</code></pre><p>Julia Base uses this convention throughout and contains examples of functions with both copying and modifying forms (e.g., <a href="../base/sort.html#Base.sort"><code>sort</code></a> and <a href="../base/sort.html#Base.sort!"><code>sort!</code></a>), and others which are just modifying (e.g., <a href="../base/collections.html#Base.push!"><code>push!</code></a>, <a href="../base/collections.html#Base.pop!"><code>pop!</code></a>, <a href="../base/collections.html#Base.splice!"><code>splice!</code></a>).  It is typical for such functions to also return the modified array for convenience.</p><p>Functions related to IO or making use of random number generators (RNG) are notable exceptions: Since these functions almost invariably must mutate the IO or RNG, functions ending with <code>!</code> are used to signify a mutation <em>other</em> than mutating the IO or advancing the RNG state. For example, <code>rand(x)</code> mutates the RNG, whereas <code>rand!(x)</code> mutates both the RNG and <code>x</code>; similarly, <code>read(io)</code> mutates <code>io</code>, whereas <code>read!(io, x)</code> mutates both arguments.</p><h2 id="Avoid-strange-type-Unions"><a class="docs-heading-anchor" href="#Avoid-strange-type-Unions">Avoid strange type <code>Union</code>s</a><a id="Avoid-strange-type-Unions-1"></a><a class="docs-heading-anchor-permalink" href="#Avoid-strange-type-Unions" title="Permalink"></a></h2><p>Types such as <code>Union{Function,AbstractString}</code> are often a sign that some design could be cleaner.</p><h2 id="Avoid-elaborate-container-types"><a class="docs-heading-anchor" href="#Avoid-elaborate-container-types">Avoid elaborate container types</a><a id="Avoid-elaborate-container-types-1"></a><a class="docs-heading-anchor-permalink" href="#Avoid-elaborate-container-types" title="Permalink"></a></h2><p>It is usually not much help to construct arrays like the following:</p><pre><code class="language-julia hljs">a = Vector{Union{Int,AbstractString,Tuple,Array}}(undef, n)</code></pre><p>In this case <code>Vector{Any}(undef, n)</code> is better. It is also more helpful to the compiler to annotate specific uses (e.g. <code>a[i]::Int</code>) than to try to pack many alternatives into one type.</p><h2 id="Prefer-exported-methods-over-direct-field-access"><a class="docs-heading-anchor" href="#Prefer-exported-methods-over-direct-field-access">Prefer exported methods over direct field access</a><a id="Prefer-exported-methods-over-direct-field-access-1"></a><a class="docs-heading-anchor-permalink" href="#Prefer-exported-methods-over-direct-field-access" title="Permalink"></a></h2><p>Idiomatic Julia code should generally treat a module&#39;s exported methods as the interface to its types. An object&#39;s fields are generally considered implementation details and user code should only access them directly if this is stated to be the API. This has several benefits:</p><ul><li>Package developers are freer to change the implementation without breaking user code.</li><li>Methods can be passed to higher-order constructs like <a href="../base/collections.html#Base.map"><code>map</code></a> (e.g. <code>map(imag, zs)</code>) rather than <code>[z.im for z in zs]</code>).</li><li>Methods can be defined on abstract types.</li><li>Methods can describe a conceptual operation that can be shared across disparate types (e.g. <code>real(z)</code> works on Complex numbers or Quaternions).</li></ul><p>Julia&#39;s dispatch system encourages this style because <code>play(x::MyType)</code> only defines the <code>play</code> method on that particular type, leaving other types to have their own implementation.</p><p>Similarly, non-exported functions are typically internal and subject to change, unless the documentations states otherwise. Names sometimes are given a <code>_</code> prefix (or suffix) to further suggest that something is &quot;internal&quot; or an implementation-detail, but it is not a rule.</p><p>Counter-examples to this rule include <a href="../base/base.html#Core.NamedTuple"><code>NamedTuple</code></a>, <a href="../base/strings.html#Base.match"><code>RegexMatch</code></a>, <a href="../base/file.html#Base.stat"><code>StatStruct</code></a>.</p><h2 id="Use-naming-conventions-consistent-with-Julia-base/"><a class="docs-heading-anchor" href="#Use-naming-conventions-consistent-with-Julia-base/">Use naming conventions consistent with Julia <code>base/</code></a><a id="Use-naming-conventions-consistent-with-Julia-base/-1"></a><a class="docs-heading-anchor-permalink" href="#Use-naming-conventions-consistent-with-Julia-base/" title="Permalink"></a></h2><ul><li>modules and type names use capitalization and camel case: <code>module SparseArrays</code>, <code>struct UnitRange</code>.</li><li>functions are lowercase (<a href="../base/collections.html#Base.maximum"><code>maximum</code></a>, <a href="../base/base.html#Base.convert"><code>convert</code></a>) and, when readable, with multiple words squashed together (<a href="../base/base.html#Base.isequal"><code>isequal</code></a>, <a href="../base/collections.html#Base.haskey"><code>haskey</code></a>). When necessary, use underscores as word separators. Underscores are also used to indicate a combination of concepts (<a href="../stdlib/Distributed.html#Distributed.remotecall_fetch-Tuple{Any, Integer, Vararg{Any}}"><code>remotecall_fetch</code></a> as a more efficient implementation of <code>fetch(remotecall(...))</code>) or as modifiers.</li><li>functions mutating at least one of their arguments end in <code>!</code>.</li><li>conciseness is valued, but avoid abbreviation (<a href="../base/collections.html#Base.indexin"><code>indexin</code></a> rather than <code>indxin</code>) as it becomes difficult to remember whether and how particular words are abbreviated.</li></ul><p>If a function name requires multiple words, consider whether it might represent more than one concept and might be better split into pieces.</p><h2 id="Write-functions-with-argument-ordering-similar-to-Julia-Base"><a class="docs-heading-anchor" href="#Write-functions-with-argument-ordering-similar-to-Julia-Base">Write functions with argument ordering similar to Julia Base</a><a id="Write-functions-with-argument-ordering-similar-to-Julia-Base-1"></a><a class="docs-heading-anchor-permalink" href="#Write-functions-with-argument-ordering-similar-to-Julia-Base" title="Permalink"></a></h2><p>As a general rule, the Base library uses the following order of arguments to functions, as applicable:</p><ol><li><p><strong>Function argument</strong>. Putting a function argument first permits the use of <a href="../base/base.html#do"><code>do</code></a> blocks for passing multiline anonymous functions.</p></li><li><p><strong>I/O stream</strong>. Specifying the <code>IO</code> object first permits passing the function to functions such as <a href="../base/io-network.html#Base.sprint"><code>sprint</code></a>, e.g. <code>sprint(show, x)</code>.</p></li><li><p><strong>Input being mutated</strong>. For example, in <a href="../base/arrays.html#Base.fill!"><code>fill!(x, v)</code></a>, <code>x</code> is the object being mutated and it appears before the value to be inserted into <code>x</code>.</p></li><li><p><strong>Type</strong>. Passing a type typically means that the output will have the given type. In <a href="../base/numbers.html#Base.parse"><code>parse(Int, &quot;1&quot;)</code></a>, the type comes before the string to parse. There are many such examples where the type appears first, but it&#39;s useful to note that in <a href="../base/io-network.html#Base.read"><code>read(io, String)</code></a>, the <code>IO</code> argument appears before the type, which is in keeping with the order outlined here.</p></li><li><p><strong>Input not being mutated</strong>. In <code>fill!(x, v)</code>, <code>v</code> is <em>not</em> being mutated and it comes after <code>x</code>.</p></li><li><p><strong>Key</strong>. For associative collections, this is the key of the key-value pair(s). For other indexed collections, this is the index.</p></li><li><p><strong>Value</strong>. For associative collections, this is the value of the key-value pair(s). In cases like <a href="../base/arrays.html#Base.fill!"><code>fill!(x, v)</code></a>, this is <code>v</code>.</p></li><li><p><strong>Everything else</strong>. Any other arguments.</p></li><li><p><strong>Varargs</strong>. This refers to arguments that can be listed indefinitely at the end of a function call. For example, in <code>Matrix{T}(undef, dims)</code>, the dimensions can be given as a <a href="../base/base.html#Core.Tuple"><code>Tuple</code></a>, e.g. <code>Matrix{T}(undef, (1,2))</code>, or as <a href="../base/base.html#Core.Vararg"><code>Vararg</code></a>s, e.g. <code>Matrix{T}(undef, 1, 2)</code>.</p></li><li><p><strong>Keyword arguments</strong>. In Julia keyword arguments have to come last anyway in function definitions; they&#39;re listed here for the sake of completeness.</p></li></ol><p>The vast majority of functions will not take every kind of argument listed above; the numbers merely denote the precedence that should be used for any applicable arguments to a function.</p><p>There are of course a few exceptions. For example, in <a href="../base/base.html#Base.convert"><code>convert</code></a>, the type should always come first. In <a href="../base/collections.html#Base.setindex!"><code>setindex!</code></a>, the value comes before the indices so that the indices can be provided as varargs.</p><p>When designing APIs, adhering to this general order as much as possible is likely to give users of your functions a more consistent experience.</p><h2 id="Don&#39;t-overuse-try-catch"><a class="docs-heading-anchor" href="#Don&#39;t-overuse-try-catch">Don&#39;t overuse try-catch</a><a id="Don&#39;t-overuse-try-catch-1"></a><a class="docs-heading-anchor-permalink" href="#Don&#39;t-overuse-try-catch" title="Permalink"></a></h2><p>It is better to avoid errors than to rely on catching them.</p><h2 id="Don&#39;t-parenthesize-conditions"><a class="docs-heading-anchor" href="#Don&#39;t-parenthesize-conditions">Don&#39;t parenthesize conditions</a><a id="Don&#39;t-parenthesize-conditions-1"></a><a class="docs-heading-anchor-permalink" href="#Don&#39;t-parenthesize-conditions" title="Permalink"></a></h2><p>Julia doesn&#39;t require parens around conditions in <code>if</code> and <code>while</code>. Write:</p><pre><code class="language-julia hljs">if a == b</code></pre><p>instead of:</p><pre><code class="language-julia hljs">if (a == b)</code></pre><h2 id="Don&#39;t-overuse-..."><a class="docs-heading-anchor" href="#Don&#39;t-overuse-...">Don&#39;t overuse <code>...</code></a><a id="Don&#39;t-overuse-...-1"></a><a class="docs-heading-anchor-permalink" href="#Don&#39;t-overuse-..." title="Permalink"></a></h2><p>Splicing function arguments can be addictive. Instead of <code>[a..., b...]</code>, use simply <code>[a; b]</code>, which already concatenates arrays. <a href="../base/collections.html#Base.collect-Tuple{Any}"><code>collect(a)</code></a> is better than <code>[a...]</code>, but since <code>a</code> is already iterable it is often even better to leave it alone, and not convert it to an array.</p><h2 id="Ensure-constructors-return-an-instance-of-their-own-type"><a class="docs-heading-anchor" href="#Ensure-constructors-return-an-instance-of-their-own-type">Ensure constructors return an instance of their own type</a><a id="Ensure-constructors-return-an-instance-of-their-own-type-1"></a><a class="docs-heading-anchor-permalink" href="#Ensure-constructors-return-an-instance-of-their-own-type" title="Permalink"></a></h2><p>When a method <code>T(x)</code> is called on a type <code>T</code>, it is generally expected to return a value of type T. Defining a <a href="constructors.html#man-constructors">constructor</a> that returns an unexpected type can lead to confusing and unpredictable behavior:</p><pre><code class="language-julia-repl hljs">julia&gt; struct Foo{T}
           x::T
       end

julia&gt; Base.Float64(foo::Foo) = Foo(Float64(foo.x))  # Do not define methods like this

julia&gt; Float64(Foo(3))  # Should return `Float64`
Foo{Float64}(3.0)

julia&gt; Foo{Int}(x) = Foo{Float64}(x)  # Do not define methods like this

julia&gt; Foo{Int}(3)  # Should return `Foo{Int}`
Foo{Float64}(3.0)</code></pre><p>To maintain code clarity and ensure type consistency, always design constructors to return an instance of the type they are supposed to construct.</p><h2 id="Don&#39;t-use-unnecessary-static-parameters"><a class="docs-heading-anchor" href="#Don&#39;t-use-unnecessary-static-parameters">Don&#39;t use unnecessary static parameters</a><a id="Don&#39;t-use-unnecessary-static-parameters-1"></a><a class="docs-heading-anchor-permalink" href="#Don&#39;t-use-unnecessary-static-parameters" title="Permalink"></a></h2><p>A function signature:</p><pre><code class="language-julia hljs">foo(x::T) where {T&lt;:Real} = ...</code></pre><p>should be written as:</p><pre><code class="language-julia hljs">foo(x::Real) = ...</code></pre><p>instead, especially if <code>T</code> is not used in the function body. Even if <code>T</code> is used, it can be replaced with <a href="../base/base.html#Core.typeof"><code>typeof(x)</code></a> if convenient. There is no performance difference. Note that this is not a general caution against static parameters, just against uses where they are not needed.</p><p>Note also that container types, specifically may need type parameters in function calls. See the FAQ <a href="performance-tips.html#Avoid-fields-with-abstract-containers">Avoid fields with abstract containers</a> for more information.</p><h2 id="Avoid-confusion-about-whether-something-is-an-instance-or-a-type"><a class="docs-heading-anchor" href="#Avoid-confusion-about-whether-something-is-an-instance-or-a-type">Avoid confusion about whether something is an instance or a type</a><a id="Avoid-confusion-about-whether-something-is-an-instance-or-a-type-1"></a><a class="docs-heading-anchor-permalink" href="#Avoid-confusion-about-whether-something-is-an-instance-or-a-type" title="Permalink"></a></h2><p>Sets of definitions like the following are confusing:</p><pre><code class="language-julia hljs">foo(::Type{MyType}) = ...
foo(::MyType) = foo(MyType)</code></pre><p>Decide whether the concept in question will be written as <code>MyType</code> or <code>MyType()</code>, and stick to it.</p><p>The preferred style is to use instances by default, and only add methods involving <code>Type{MyType}</code> later if they become necessary to solve some problems.</p><p>If a type is effectively an enumeration, it should be defined as a single (ideally immutable struct or primitive) type, with the enumeration values being instances of it. Constructors and conversions can check whether values are valid. This design is preferred over making the enumeration an abstract type, with the &quot;values&quot; as subtypes.</p><h2 id="Don&#39;t-overuse-macros"><a class="docs-heading-anchor" href="#Don&#39;t-overuse-macros">Don&#39;t overuse macros</a><a id="Don&#39;t-overuse-macros-1"></a><a class="docs-heading-anchor-permalink" href="#Don&#39;t-overuse-macros" title="Permalink"></a></h2><p>Be aware of when a macro could really be a function instead.</p><p>Calling <a href="../base/base.html#eval"><code>eval</code></a> inside a macro is a particularly dangerous warning sign; it means the macro will only work when called at the top level. If such a macro is written as a function instead, it will naturally have access to the run-time values it needs.</p><h2 id="Don&#39;t-expose-unsafe-operations-at-the-interface-level"><a class="docs-heading-anchor" href="#Don&#39;t-expose-unsafe-operations-at-the-interface-level">Don&#39;t expose unsafe operations at the interface level</a><a id="Don&#39;t-expose-unsafe-operations-at-the-interface-level-1"></a><a class="docs-heading-anchor-permalink" href="#Don&#39;t-expose-unsafe-operations-at-the-interface-level" title="Permalink"></a></h2><p>If you have a type that uses a native pointer:</p><pre><code class="language-julia hljs">mutable struct NativeType
    p::Ptr{UInt8}
    ...
end</code></pre><p>don&#39;t write definitions like the following:</p><pre><code class="language-julia hljs">getindex(x::NativeType, i) = unsafe_load(x.p, i)</code></pre><p>The problem is that users of this type can write <code>x[i]</code> without realizing that the operation is unsafe, and then be susceptible to memory bugs.</p><p>Such a function should either check the operation to ensure it is safe, or have <code>unsafe</code> somewhere in its name to alert callers.</p><h2 id="Don&#39;t-overload-methods-of-base-container-types"><a class="docs-heading-anchor" href="#Don&#39;t-overload-methods-of-base-container-types">Don&#39;t overload methods of base container types</a><a id="Don&#39;t-overload-methods-of-base-container-types-1"></a><a class="docs-heading-anchor-permalink" href="#Don&#39;t-overload-methods-of-base-container-types" title="Permalink"></a></h2><p>It is possible to write definitions like the following:</p><pre><code class="language-julia hljs">show(io::IO, v::Vector{MyType}) = ...</code></pre><p>This would provide custom showing of vectors with a specific new element type. While tempting, this should be avoided. The trouble is that users will expect a well-known type like <code>Vector()</code> to behave in a certain way, and overly customizing its behavior can make it harder to work with.</p><h2 id="avoid-type-piracy"><a class="docs-heading-anchor" href="#avoid-type-piracy">Avoid type piracy</a><a id="avoid-type-piracy-1"></a><a class="docs-heading-anchor-permalink" href="#avoid-type-piracy" title="Permalink"></a></h2><p>&quot;Type piracy&quot; refers to the practice of extending or redefining methods in Base or other packages on types that you have not defined. In extreme cases, you can crash Julia (e.g. if your method extension or redefinition causes invalid input to be passed to a <code>ccall</code>). Type piracy can complicate reasoning about code, and may introduce incompatibilities that are hard to predict and diagnose.</p><p>As an example, suppose you wanted to define multiplication on symbols in a module:</p><pre><code class="language-julia hljs">module A
import Base.*
*(x::Symbol, y::Symbol) = Symbol(x,y)
end</code></pre><p>The problem is that now any other module that uses <code>Base.*</code> will also see this definition. Since <code>Symbol</code> is defined in Base and is used by other modules, this can change the behavior of unrelated code unexpectedly. There are several alternatives here, including using a different function name, or wrapping the <code>Symbol</code>s in another type that you define.</p><p>Sometimes, coupled packages may engage in type piracy to separate features from definitions, especially when the packages were designed by collaborating authors, and when the definitions are reusable. For example, one package might provide some types useful for working with colors; another package could define methods for those types that enable conversions between color spaces. Another example might be a package that acts as a thin wrapper for some C code, which another package might then pirate to implement a higher-level, Julia-friendly API.</p><h2 id="Be-careful-with-type-equality"><a class="docs-heading-anchor" href="#Be-careful-with-type-equality">Be careful with type equality</a><a id="Be-careful-with-type-equality-1"></a><a class="docs-heading-anchor-permalink" href="#Be-careful-with-type-equality" title="Permalink"></a></h2><p>You generally want to use <a href="../base/base.html#Core.isa"><code>isa</code></a> and <a href="../base/base.html#Core.:&lt;:"><code>&lt;:</code></a> for testing types, not <code>==</code>. Checking types for exact equality typically only makes sense when comparing to a known concrete type (e.g. <code>T == Float64</code>), or if you <em>really, really</em> know what you&#39;re doing.</p><h2 id="Don&#39;t-write-a-trivial-anonymous-function-x-f(x)-for-a-named-function-f"><a class="docs-heading-anchor" href="#Don&#39;t-write-a-trivial-anonymous-function-x-f(x)-for-a-named-function-f">Don&#39;t write a trivial anonymous function <code>x-&gt;f(x)</code> for a named function <code>f</code></a><a id="Don&#39;t-write-a-trivial-anonymous-function-x-f(x)-for-a-named-function-f-1"></a><a class="docs-heading-anchor-permalink" href="#Don&#39;t-write-a-trivial-anonymous-function-x-f(x)-for-a-named-function-f" title="Permalink"></a></h2><p>Since higher-order functions are often called with anonymous functions, it is easy to conclude that this is desirable or even necessary. But any function can be passed directly, without being &quot;wrapped&quot; in an anonymous function. Instead of writing <code>map(x-&gt;f(x), a)</code>, write <a href="../base/collections.html#Base.map"><code>map(f, a)</code></a>.</p><h2 id="Avoid-using-floats-for-numeric-literals-in-generic-code-when-possible"><a class="docs-heading-anchor" href="#Avoid-using-floats-for-numeric-literals-in-generic-code-when-possible">Avoid using floats for numeric literals in generic code when possible</a><a id="Avoid-using-floats-for-numeric-literals-in-generic-code-when-possible-1"></a><a class="docs-heading-anchor-permalink" href="#Avoid-using-floats-for-numeric-literals-in-generic-code-when-possible" title="Permalink"></a></h2><p>If you write generic code which handles numbers, and which can be expected to run with many different numeric type arguments, try using literals of a numeric type that will affect the arguments as little as possible through promotion.</p><p>For example,</p><pre><code class="language-julia-repl hljs">julia&gt; f(x) = 2.0 * x
f (generic function with 1 method)

julia&gt; f(1//2)
1.0

julia&gt; f(1/2)
1.0

julia&gt; f(1)
2.0</code></pre><p>while</p><pre><code class="language-julia-repl hljs">julia&gt; g(x) = 2 * x
g (generic function with 1 method)

julia&gt; g(1//2)
1//1

julia&gt; g(1/2)
1.0

julia&gt; g(1)
2</code></pre><p>As you can see, the second version, where we used an <code>Int</code> literal, preserved the type of the input argument, while the first didn&#39;t. This is because e.g. <code>promote_type(Int, Float64) == Float64</code>, and promotion happens with the multiplication. Similarly, <a href="../base/numbers.html#Base.Rational"><code>Rational</code></a> literals are less type disruptive than <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> literals, but more disruptive than <code>Int</code>s:</p><pre><code class="language-julia-repl hljs">julia&gt; h(x) = 2//1 * x
h (generic function with 1 method)

julia&gt; h(1//2)
1//1

julia&gt; h(1/2)
1.0

julia&gt; h(1)
2//1</code></pre><p>Thus, use <code>Int</code> literals when possible, with <code>Rational{Int}</code> for literal non-integer numbers, in order to make it easier to use your code.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="workflow-tips.html">« Workflow Tips</a><a class="docs-footer-nextpage" href="faq.html">Frequently Asked Questions »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
