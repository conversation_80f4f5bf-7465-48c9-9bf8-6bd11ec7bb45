<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Profiling · The Julia Language</title><meta name="title" content="Profiling · The Julia Language"/><meta property="og:title" content="Profiling · The Julia Language"/><meta property="twitter:title" content="Profiling · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li class="is-active"><a class="tocitem" href="Profile.html">Profiling</a><ul class="internal"><li><a class="tocitem" href="#CPU-Profiling"><span>CPU Profiling</span></a></li><li><a class="tocitem" href="#Via-@profile"><span>Via <code>@profile</code></span></a></li><li><a class="tocitem" href="#Triggered-During-Execution"><span>Triggered During Execution</span></a></li><li><a class="tocitem" href="#Reference"><span>Reference</span></a></li><li><a class="tocitem" href="#Memory-profiling"><span>Memory profiling</span></a></li><li><a class="tocitem" href="#Heap-Snapshots"><span>Heap Snapshots</span></a></li></ul></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="Profile.html">Profiling</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="Profile.html">Profiling</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/stdlib/Profile/docs/src/index.md" title="View source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="lib-profiling"><a class="docs-heading-anchor" href="#lib-profiling">Profiling</a><a id="lib-profiling-1"></a><a class="docs-heading-anchor-permalink" href="#lib-profiling" title="Permalink"></a></h1><h2 id="CPU-Profiling"><a class="docs-heading-anchor" href="#CPU-Profiling">CPU Profiling</a><a id="CPU-Profiling-1"></a><a class="docs-heading-anchor-permalink" href="#CPU-Profiling" title="Permalink"></a></h2><p>There are two main approaches to CPU profiling julia code:</p><h2 id="Via-@profile"><a class="docs-heading-anchor" href="#Via-@profile">Via <code>@profile</code></a><a id="Via-@profile-1"></a><a class="docs-heading-anchor-permalink" href="#Via-@profile" title="Permalink"></a></h2><p>Where profiling is enabled for a given call via the <code>@profile</code> macro.</p><pre><code class="language-julia-repl hljs">julia&gt; using Profile

julia&gt; @profile foo()

julia&gt; Profile.print()
Overhead ╎ [+additional indent] Count File:Line; Function
=========================================================
    ╎147  @Base/client.jl:506; _start()
        ╎ 147  @Base/client.jl:318; exec_options(opts::Base.JLOptions)
...</code></pre><h2 id="Triggered-During-Execution"><a class="docs-heading-anchor" href="#Triggered-During-Execution">Triggered During Execution</a><a id="Triggered-During-Execution-1"></a><a class="docs-heading-anchor-permalink" href="#Triggered-During-Execution" title="Permalink"></a></h2><p>Tasks that are already running can also be profiled for a fixed time period at any user-triggered time.</p><p>To trigger the profiling:</p><ul><li>MacOS &amp; FreeBSD (BSD-based platforms): Use <code>ctrl-t</code> or pass a <code>SIGINFO</code> signal to the julia process i.e. <code>% kill -INFO $julia_pid</code></li><li>Linux: Pass a <code>SIGUSR1</code> signal to the julia process i.e. <code>% kill -USR1 $julia_pid</code></li><li>Windows: Not currently supported.</li></ul><p>First, a single stack trace at the instant that the signal was thrown is shown, then a 1 second profile is collected, followed by the profile report at the next yield point, which may be at task completion for code without yield points e.g. tight loops.</p><p>Optionally set environment variable <a href="../manual/environment-variables.html#JULIA_PROFILE_PEEK_HEAP_SNAPSHOT"><code>JULIA_PROFILE_PEEK_HEAP_SNAPSHOT</code></a> to <code>1</code> to also automatically collect a <a href="Profile.html#Heap-Snapshots">heap snapshot</a>.</p><pre><code class="language-julia-repl hljs">julia&gt; foo()
##== the user sends a trigger while foo is running ==##
load: 2.53  cmd: julia 88903 running 6.16u 0.97s

======================================================================================
Information request received. A stacktrace will print followed by a 1.0 second profile
======================================================================================

signal (29): Information request: 29
__psynch_cvwait at /usr/lib/system/libsystem_kernel.dylib (unknown line)
_pthread_cond_wait at /usr/lib/system/libsystem_pthread.dylib (unknown line)
...

======================================================================
Profile collected. A report will print if the Profile module is loaded
======================================================================

Overhead ╎ [+additional indent] Count File:Line; Function
=========================================================
Thread 1 Task 0x000000011687c010 Total snapshots: 572. Utilization: 100%
   ╎147 @Base/client.jl:506; _start()
       ╎ 147 @Base/client.jl:318; exec_options(opts::Base.JLOptions)
...

Thread 2 Task 0x0000000116960010 Total snapshots: 572. Utilization: 0%
   ╎572 @Base/task.jl:587; task_done_hook(t::Task)
      ╎ 572 @Base/task.jl:879; wait()
...</code></pre><h3 id="Customization"><a class="docs-heading-anchor" href="#Customization">Customization</a><a id="Customization-1"></a><a class="docs-heading-anchor-permalink" href="#Customization" title="Permalink"></a></h3><p>The duration of the profiling can be adjusted via <a href="Profile.html#Profile.set_peek_duration"><code>Profile.set_peek_duration</code></a></p><p>The profile report is broken down by thread and task. Pass a no-arg function to <code>Profile.peek_report[]</code> to override this. i.e. <code>Profile.peek_report[] = () -&gt; Profile.print()</code> to remove any grouping. This could also be overridden by an external profile data consumer.</p><h2 id="Reference"><a class="docs-heading-anchor" href="#Reference">Reference</a><a id="Reference-1"></a><a class="docs-heading-anchor-permalink" href="#Reference" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Profile.@profile" href="#Profile.@profile"><code>Profile.@profile</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@profile</code></pre><p><code>@profile &lt;expression&gt;</code> runs your expression while taking periodic backtraces. These are appended to an internal buffer of backtraces.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Profile/src/Profile.jl#L49-L54">source</a></section></article><p>The methods in <code>Profile</code> are not exported and need to be called e.g. as <code>Profile.print()</code>.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Profile.clear" href="#Profile.clear"><code>Profile.clear</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">clear()</code></pre><p>Clear any existing backtraces from the internal buffer.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Profile/src/Profile.jl#L160-L164">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Profile.print" href="#Profile.print"><code>Profile.print</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">print([io::IO = stdout,] [data::Vector = fetch()], [lidict::Union{LineInfoDict, LineInfoFlatDict} = getdict(data)]; kwargs...)</code></pre><p>Prints profiling results to <code>io</code> (by default, <code>stdout</code>). If you do not supply a <code>data</code> vector, the internal buffer of accumulated backtraces will be used.</p><p>The keyword arguments can be any combination of:</p><ul><li><p><code>format</code> – Determines whether backtraces are printed with (default, <code>:tree</code>) or without (<code>:flat</code>) indentation indicating tree structure.</p></li><li><p><code>C</code> – If <code>true</code>, backtraces from C and Fortran code are shown (normally they are excluded).</p></li><li><p><code>combine</code> – If <code>true</code> (default), instruction pointers are merged that correspond to the same line of code.</p></li><li><p><code>maxdepth</code> – Limits the depth higher than <code>maxdepth</code> in the <code>:tree</code> format.</p></li><li><p><code>sortedby</code> – Controls the order in <code>:flat</code> format. <code>:filefuncline</code> (default) sorts by the source  line, <code>:count</code> sorts in order of number of collected samples, and <code>:overhead</code> sorts by the number of samples  incurred by each function by itself.</p></li><li><p><code>groupby</code> – Controls grouping over tasks and threads, or no grouping. Options are <code>:none</code> (default), <code>:thread</code>, <code>:task</code>,  <code>[:thread, :task]</code>, or <code>[:task, :thread]</code> where the last two provide nested grouping.</p></li><li><p><code>noisefloor</code> – Limits frames that exceed the heuristic noise floor of the sample (only applies to format <code>:tree</code>).  A suggested value to try for this is 2.0 (the default is 0). This parameter hides samples for which <code>n &lt;= noisefloor * √N</code>,  where <code>n</code> is the number of samples on this line, and <code>N</code> is the number of samples for the callee.</p></li><li><p><code>mincount</code> – Limits the printout to only those lines with at least <code>mincount</code> occurrences.</p></li><li><p><code>recur</code> – Controls the recursion handling in <code>:tree</code> format. <code>:off</code> (default) prints the tree as normal. <code>:flat</code> instead  compresses any recursion (by ip), showing the approximate effect of converting any self-recursion into an iterator.  <code>:flatc</code> does the same but also includes collapsing of C frames (may do odd things around <code>jl_apply</code>).</p></li><li><p><code>threads::Union{Int,AbstractVector{Int}}</code> – Specify which threads to include snapshots from in the report. Note that  this does not control which threads samples are collected on (which may also have been collected on another machine).</p></li><li><p><code>tasks::Union{Int,AbstractVector{Int}}</code> – Specify which tasks to include snapshots from in the report. Note that this  does not control which tasks samples are collected within.</p></li></ul><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>The <code>groupby</code>, <code>threads</code>, and <code>tasks</code> keyword arguments were introduced in Julia 1.8.</p></div></div><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Profiling on windows is limited to the main thread. Other threads have not been sampled and will not show in the report.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Profile/src/Profile.jl#L196-L243">source</a></section><section><div><pre><code class="language-julia hljs">print([io::IO = stdout,] data::Vector, lidict::LineInfoDict; kwargs...)</code></pre><p>Prints profiling results to <code>io</code>. This variant is used to examine results exported by a previous call to <a href="Profile.html#Profile.retrieve"><code>retrieve</code></a>. Supply the vector <code>data</code> of backtraces and a dictionary <code>lidict</code> of line information.</p><p>See <code>Profile.print([io], data)</code> for an explanation of the valid keyword arguments.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Profile/src/Profile.jl#L336-L344">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Profile.init" href="#Profile.init"><code>Profile.init</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">init(; n::Integer, delay::Real)</code></pre><p>Configure the <code>delay</code> between backtraces (measured in seconds), and the number <code>n</code> of instruction pointers that may be stored per thread. Each instruction pointer corresponds to a single line of code; backtraces generally consist of a long list of instruction pointers. Note that 6 spaces for instruction pointers per backtrace are used to store metadata and two NULL end markers. Current settings can be obtained by calling this function with no arguments, and each can be set independently using keywords or in the order <code>(n, delay)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Profile/src/Profile.jl#L95-L103">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Profile.fetch" href="#Profile.fetch"><code>Profile.fetch</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">fetch(;include_meta = true) -&gt; data</code></pre><p>Return a copy of the buffer of profile backtraces. Note that the values in <code>data</code> have meaning only on this machine in the current session, because it depends on the exact memory addresses used in JIT-compiling. This function is primarily for internal use; <a href="Profile.html#Profile.retrieve"><code>retrieve</code></a> may be a better choice for most users. By default metadata such as threadid and taskid is included. Set <code>include_meta</code> to <code>false</code> to strip metadata.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Profile/src/Profile.jl#L625-L633">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Profile.retrieve" href="#Profile.retrieve"><code>Profile.retrieve</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">retrieve(; kwargs...) -&gt; data, lidict</code></pre><p>&quot;Exports&quot; profiling results in a portable format, returning the set of all backtraces (<code>data</code>) and a dictionary that maps the (session-specific) instruction pointers in <code>data</code> to <code>LineInfo</code> values that store the file name, function name, and line number. This function allows you to save profiling results for future analysis.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Profile/src/Profile.jl#L415-L422">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Profile.callers" href="#Profile.callers"><code>Profile.callers</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">callers(funcname, [data, lidict], [filename=&lt;filename&gt;], [linerange=&lt;start:stop&gt;]) -&gt; Vector{Tuple{count, lineinfo}}</code></pre><p>Given a previous profiling run, determine who called a particular function. Supplying the filename (and optionally, range of line numbers over which the function is defined) allows you to disambiguate an overloaded method. The returned value is a vector containing a count of the number of calls and line information about the caller. One can optionally supply backtrace <code>data</code> obtained from <a href="Profile.html#Profile.retrieve"><code>retrieve</code></a>; otherwise, the current internal profile buffer is used.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Profile/src/Profile.jl#L545-L554">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Profile.clear_malloc_data" href="#Profile.clear_malloc_data"><code>Profile.clear_malloc_data</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">clear_malloc_data()</code></pre><p>Clears any stored memory allocation data when running julia with <code>--track-allocation</code>. Execute the command(s) you want to test (to force JIT-compilation), then call <a href="Profile.html#Profile.clear_malloc_data"><code>clear_malloc_data</code></a>. Then execute your command(s) again, quit Julia, and examine the resulting <code>*.mem</code> files.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Profile/src/Profile.jl#L586-L593">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Profile.get_peek_duration" href="#Profile.get_peek_duration"><code>Profile.get_peek_duration</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">get_peek_duration()</code></pre><p>Get the duration in seconds of the profile &quot;peek&quot; that is triggered via <code>SIGINFO</code> or <code>SIGUSR1</code>, depending on platform.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Profile/src/Profile.jl#L76-L80">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Profile.set_peek_duration" href="#Profile.set_peek_duration"><code>Profile.set_peek_duration</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">set_peek_duration(t::Float64)</code></pre><p>Set the duration in seconds of the profile &quot;peek&quot; that is triggered via <code>SIGINFO</code> or <code>SIGUSR1</code>, depending on platform.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Profile/src/Profile.jl#L82-L86">source</a></section></article><h2 id="Memory-profiling"><a class="docs-heading-anchor" href="#Memory-profiling">Memory profiling</a><a id="Memory-profiling-1"></a><a class="docs-heading-anchor-permalink" href="#Memory-profiling" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Profile.Allocs.@profile" href="#Profile.Allocs.@profile"><code>Profile.Allocs.@profile</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Profile.Allocs.@profile [sample_rate=0.1] expr</code></pre><p>Profile allocations that happen during <code>expr</code>, returning both the result and and AllocResults struct.</p><p>A sample rate of 1.0 will record everything; 0.0 will record nothing.</p><pre><code class="language-julia hljs">julia&gt; Profile.Allocs.@profile sample_rate=0.01 peakflops()
1.03733270279065e11

julia&gt; results = Profile.Allocs.fetch()

julia&gt; last(sort(results.allocs, by=x-&gt;x.size))
Profile.Allocs.Alloc(Vector{Any}, Base.StackTraces.StackFrame[_new_array_ at array.c:127, ...], 5576)</code></pre><p>See the profiling tutorial in the Julia documentation for more information.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>Older versions of Julia could not capture types in all cases. In older versions of Julia, if you see an allocation of type <code>Profile.Allocs.UnknownType</code>, it means that the profiler doesn&#39;t know what type of object was allocated. This mainly happened when the allocation was coming from generated code produced by the compiler. See <a href="https://github.com/JuliaLang/julia/issues/43688">issue #43688</a> for more info.</p><p>Since Julia 1.11, all allocations should have a type reported.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>The allocation profiler was added in Julia 1.8.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Profile/src/Allocs.jl#L39-L71">source</a></section></article><p>The methods in <code>Profile.Allocs</code> are not exported and need to be called e.g. as <code>Profile.Allocs.fetch()</code>.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Profile.Allocs.clear" href="#Profile.Allocs.clear"><code>Profile.Allocs.clear</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Profile.Allocs.clear()</code></pre><p>Clear all previously profiled allocation information from memory.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Profile/src/Allocs.jl#L109-L113">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Profile.Allocs.print" href="#Profile.Allocs.print"><code>Profile.Allocs.print</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Profile.Allocs.print([io::IO = stdout,] [data::AllocResults = fetch()]; kwargs...)</code></pre><p>Prints profiling results to <code>io</code> (by default, <code>stdout</code>). If you do not supply a <code>data</code> vector, the internal buffer of accumulated backtraces will be used.</p><p>See <code>Profile.print</code> for an explanation of the valid keyword arguments.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Profile/src/Allocs.jl#L234-L242">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Profile.Allocs.fetch" href="#Profile.Allocs.fetch"><code>Profile.Allocs.fetch</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Profile.Allocs.fetch()</code></pre><p>Retrieve the recorded allocations, and decode them into Julia objects which can be analyzed.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Profile/src/Allocs.jl#L119-L124">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Profile.Allocs.start" href="#Profile.Allocs.start"><code>Profile.Allocs.start</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Profile.Allocs.start(sample_rate::Real)</code></pre><p>Begin recording allocations with the given sample rate A sample rate of 1.0 will record everything; 0.0 will record nothing.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Profile/src/Allocs.jl#L90-L95">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Profile.Allocs.stop" href="#Profile.Allocs.stop"><code>Profile.Allocs.stop</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Profile.Allocs.stop()</code></pre><p>Stop recording allocations.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Profile/src/Allocs.jl#L100-L104">source</a></section></article><h2 id="Heap-Snapshots"><a class="docs-heading-anchor" href="#Heap-Snapshots">Heap Snapshots</a><a id="Heap-Snapshots-1"></a><a class="docs-heading-anchor-permalink" href="#Heap-Snapshots" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Profile.take_heap_snapshot" href="#Profile.take_heap_snapshot"><code>Profile.take_heap_snapshot</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Profile.take_heap_snapshot(filepath::String, all_one::Bool=false, streaming=false)
Profile.take_heap_snapshot(all_one::Bool=false; dir::String, streaming=false)</code></pre><p>Write a snapshot of the heap, in the JSON format expected by the Chrome Devtools Heap Snapshot viewer (.heapsnapshot extension) to a file (<code>$pid_$timestamp.heapsnapshot</code>) in the current directory by default (or tempdir if the current directory is unwritable), or in <code>dir</code> if given, or the given full file path, or IO stream.</p><p>If <code>all_one</code> is true, then report the size of every object as one so they can be easily counted. Otherwise, report the actual size.</p><p>If <code>streaming</code> is true, we will stream the snapshot data out into four files, using filepath as the prefix, to avoid having to hold the entire snapshot in memory. This option should be used for any setting where your memory is constrained. These files can then be reassembled by calling Profile.HeapSnapshot.assemble_snapshot(), which can be done offline.</p><p>NOTE: We strongly recommend setting streaming=true for performance reasons. Reconstructing the snapshot from the parts requires holding the entire snapshot in memory, so if the snapshot is large, you can run out of memory while processing it. Streaming allows you to reconstruct the snapshot offline, after your workload is done running. If you do attempt to collect a snapshot with streaming=false (the default, for backwards-compatibility) and your process is killed, note that this will always save the parts in the same directory as your provided filepath, so you can still reconstruct the snapshot after the fact, via <code>assemble_snapshot()</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Profile/src/Profile.jl#L1254-L1281">source</a></section></article><p>The methods in <code>Profile</code> are not exported and need to be called e.g. as <code>Profile.take_heap_snapshot()</code>.</p><pre><code class="language-julia-repl hljs">julia&gt; using Profile

julia&gt; Profile.take_heap_snapshot(&quot;snapshot.heapsnapshot&quot;)</code></pre><p>Traces and records julia objects on the heap. This only records objects known to the Julia garbage collector. Memory allocated by external libraries not managed by the garbage collector will not show up in the snapshot.</p><p>To avoid OOMing while recording the snapshot, we added a streaming option to stream out the heap snapshot into four files,</p><pre><code class="language-julia-repl hljs">julia&gt; using Profile

julia&gt; Profile.take_heap_snapshot(&quot;snapshot&quot;; streaming=true)</code></pre><p>where &quot;snapshot&quot; is the filepath as the prefix for the generated files.</p><p>Once the snapshot files are generated, they could be assembled offline with the following command:</p><pre><code class="language-julia-repl hljs">julia&gt; using Profile

julia&gt; Profile.HeapSnapshot.assemble_snapshot(&quot;snapshot&quot;, &quot;snapshot.heapsnapshot&quot;)</code></pre><p>The resulting heap snapshot file can be uploaded to chrome devtools to be viewed. For more information, see the <a href="https://developer.chrome.com/docs/devtools/memory-problems/heap-snapshots/#view_snapshots">chrome devtools docs</a>. An alternative for analyzing Chromium heap snapshots is with the VS Code extension <code>ms-vscode.vscode-js-profile-flame</code>.</p><p>The Firefox heap snapshots are of a different format, and Firefox currently may <em>not</em> be used for viewing the heap snapshots generated by Julia.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="Printf.html">« Printf</a><a class="docs-footer-nextpage" href="REPL.html">The Julia REPL »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
