<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Embedding Julia · The Julia Language</title><meta name="title" content="Embedding Julia · The Julia Language"/><meta property="og:title" content="Embedding Julia · The Julia Language"/><meta property="twitter:title" content="Embedding Julia · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li class="is-active"><a class="tocitem" href="embedding.html">Embedding Julia</a><ul class="internal"><li><a class="tocitem" href="#High-Level-Embedding"><span>High-Level Embedding</span></a></li><li><a class="tocitem" href="#High-Level-Embedding-on-Windows-with-Visual-Studio"><span>High-Level Embedding on Windows with Visual Studio</span></a></li><li><a class="tocitem" href="#Converting-Types"><span>Converting Types</span></a></li><li><a class="tocitem" href="#Calling-Julia-Functions"><span>Calling Julia Functions</span></a></li><li><a class="tocitem" href="#Memory-Management"><span>Memory Management</span></a></li><li><a class="tocitem" href="#Working-with-Arrays"><span>Working with Arrays</span></a></li><li><a class="tocitem" href="#Exceptions"><span>Exceptions</span></a></li></ul></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="embedding.html">Embedding Julia</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="embedding.html">Embedding Julia</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/embedding.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Embedding-Julia"><a class="docs-heading-anchor" href="#Embedding-Julia">Embedding Julia</a><a id="Embedding-Julia-1"></a><a class="docs-heading-anchor-permalink" href="#Embedding-Julia" title="Permalink"></a></h1><p>As we have seen in <a href="calling-c-and-fortran-code.html#Calling-C-and-Fortran-Code">Calling C and Fortran Code</a>, Julia has a simple and efficient way to call functions written in C. But there are situations where the opposite is needed: calling Julia functions from C code. This can be used to integrate Julia code into a larger C/C++ project, without the need to rewrite everything in C/C++. Julia has a C API to make this possible. As almost all programming languages have some way to call C functions, the Julia C API can also be used to build further language bridges (e.g. calling Julia from Python, Rust or C#). Even though Rust and C++ can use the C embedding API directly, both have packages helping with it, for C++ <a href="https://github.com/Clemapfel/jluna">Jluna</a> is useful.</p><h2 id="High-Level-Embedding"><a class="docs-heading-anchor" href="#High-Level-Embedding">High-Level Embedding</a><a id="High-Level-Embedding-1"></a><a class="docs-heading-anchor-permalink" href="#High-Level-Embedding" title="Permalink"></a></h2><p><strong>Note</strong>: This section covers embedding Julia code in C on Unix-like operating systems. For doing this on Windows, please see the section following this, <a href="embedding.html#High-Level-Embedding-on-Windows-with-Visual-Studio">High-Level Embedding on Windows with Visual Studio</a>.</p><p>We start with a simple C program that initializes Julia and calls some Julia code:</p><pre><code class="language-c hljs">#include &lt;julia.h&gt;
JULIA_DEFINE_FAST_TLS // only define this once, in an executable (not in a shared library) if you want fast code.

int main(int argc, char *argv[])
{
    /* required: setup the Julia context */
    jl_init();

    /* run Julia commands */
    jl_eval_string(&quot;print(sqrt(2.0))&quot;);

    /* strongly recommended: notify Julia that the
         program is about to terminate. this allows
         Julia time to cleanup pending write requests
         and run all finalizers
    */
    jl_atexit_hook(0);
    return 0;
}</code></pre><p>In order to build this program you must add the path to the Julia header to the include path and link against <code>libjulia</code>. For instance, when Julia is installed to <code>$JULIA_DIR</code>, one can compile the above test program <code>test.c</code> with <code>gcc</code> using:</p><pre><code class="nohighlight hljs">gcc -o test -fPIC -I$JULIA_DIR/include/julia -L$JULIA_DIR/lib -Wl,-rpath,$JULIA_DIR/lib test.c -ljulia</code></pre><p>Alternatively, look at the <code>embedding.c</code> program in the Julia source tree in the <code>test/embedding/</code> folder. The file <code>cli/loader_exe.c</code> program is another simple example of how to set <code>jl_options</code> options while linking against <code>libjulia</code>.</p><p>The first thing that must be done before calling any other Julia C function is to initialize Julia. This is done by calling <code>jl_init</code>, which tries to automatically determine Julia&#39;s install location. If you need to specify a custom location, or specify which system image to load, use <code>jl_init_with_image</code> instead.</p><p>The second statement in the test program evaluates a Julia statement using a call to <code>jl_eval_string</code>.</p><p>Before the program terminates, it is strongly recommended that <code>jl_atexit_hook</code> is called. The above example program calls this just before returning from <code>main</code>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Currently, dynamically linking with the <code>libjulia</code> shared library requires passing the <code>RTLD_GLOBAL</code> option. In Python, this looks like:</p><pre><code class="nohighlight hljs">&gt;&gt;&gt; julia=CDLL(&#39;./libjulia.dylib&#39;,RTLD_GLOBAL)
&gt;&gt;&gt; julia.jl_init.argtypes = []
&gt;&gt;&gt; julia.jl_init()
250593296</code></pre></div></div><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>If the julia program needs to access symbols from the main executable, it may be necessary to add the <code>-Wl,--export-dynamic</code> linker flag at compile time on Linux in addition to the ones generated by <code>julia-config.jl</code> described below. This is not necessary when compiling a shared library.</p></div></div><h3 id="Using-julia-config-to-automatically-determine-build-parameters"><a class="docs-heading-anchor" href="#Using-julia-config-to-automatically-determine-build-parameters">Using julia-config to automatically determine build parameters</a><a id="Using-julia-config-to-automatically-determine-build-parameters-1"></a><a class="docs-heading-anchor-permalink" href="#Using-julia-config-to-automatically-determine-build-parameters" title="Permalink"></a></h3><p>The script <code>julia-config.jl</code> was created to aid in determining what build parameters are required by a program that uses embedded Julia. This script uses the build parameters and system configuration of the particular Julia distribution it is invoked by to export the necessary compiler flags for an embedding program to interact with that distribution. This script is located in the Julia shared data directory.</p><h4 id="Example"><a class="docs-heading-anchor" href="#Example">Example</a><a id="Example-1"></a><a class="docs-heading-anchor-permalink" href="#Example" title="Permalink"></a></h4><pre><code class="language-c hljs">#include &lt;julia.h&gt;

int main(int argc, char *argv[])
{
    jl_init();
    (void)jl_eval_string(&quot;println(sqrt(2.0))&quot;);
    jl_atexit_hook(0);
    return 0;
}</code></pre><h4 id="On-the-command-line"><a class="docs-heading-anchor" href="#On-the-command-line">On the command line</a><a id="On-the-command-line-1"></a><a class="docs-heading-anchor-permalink" href="#On-the-command-line" title="Permalink"></a></h4><p>A simple use of this script is from the command line. Assuming that <code>julia-config.jl</code> is located in <code>/usr/local/julia/share/julia</code>, it can be invoked on the command line directly and takes any combination of three flags:</p><pre><code class="nohighlight hljs">/usr/local/julia/share/julia/julia-config.jl
Usage: julia-config [--cflags|--ldflags|--ldlibs]</code></pre><p>If the above example source is saved in the file <code>embed_example.c</code>, then the following command will compile it into an executable program on Linux and Windows (MSYS2 environment). On macOS, substitute <code>clang</code> for <code>gcc</code>.:</p><pre><code class="nohighlight hljs">/usr/local/julia/share/julia/julia-config.jl --cflags --ldflags --ldlibs | xargs gcc embed_example.c</code></pre><h4 id="Use-in-Makefiles"><a class="docs-heading-anchor" href="#Use-in-Makefiles">Use in Makefiles</a><a id="Use-in-Makefiles-1"></a><a class="docs-heading-anchor-permalink" href="#Use-in-Makefiles" title="Permalink"></a></h4><p>In general, embedding projects will be more complicated than the above example, and so the following allows general makefile support as well – assuming GNU make because of the use of the <strong>shell</strong> macro expansions. Furthermore, although <code>julia-config.jl</code> is usually in the <code>/usr/local</code> directory, if it isn&#39;t, then Julia itself can be used to find <code>julia-config.jl</code>, and the makefile can take advantage of this. The above example is extended to use a makefile:</p><pre><code class="nohighlight hljs">JL_SHARE = $(shell julia -e &#39;print(joinpath(Sys.BINDIR, Base.DATAROOTDIR, &quot;julia&quot;))&#39;)
CFLAGS   += $(shell $(JL_SHARE)/julia-config.jl --cflags)
CXXFLAGS += $(shell $(JL_SHARE)/julia-config.jl --cflags)
LDFLAGS  += $(shell $(JL_SHARE)/julia-config.jl --ldflags)
LDLIBS   += $(shell $(JL_SHARE)/julia-config.jl --ldlibs)

all: embed_example</code></pre><p>Now the build command is simply <code>make</code>.</p><h2 id="High-Level-Embedding-on-Windows-with-Visual-Studio"><a class="docs-heading-anchor" href="#High-Level-Embedding-on-Windows-with-Visual-Studio">High-Level Embedding on Windows with Visual Studio</a><a id="High-Level-Embedding-on-Windows-with-Visual-Studio-1"></a><a class="docs-heading-anchor-permalink" href="#High-Level-Embedding-on-Windows-with-Visual-Studio" title="Permalink"></a></h2><p>If the <code>JULIA_DIR</code> environment variable hasn&#39;t been setup, add it using the System panel before starting Visual Studio. The <code>bin</code> folder under JULIA_DIR should be on the system PATH.</p><p>We start by opening Visual Studio and creating a new Console Application project. Open the &#39;stdafx.h&#39; header file, and add the following lines at the end:</p><pre><code class="language-c hljs">#include &lt;julia.h&gt;</code></pre><p>Then, replace the main() function in the project with this code:</p><pre><code class="language-c hljs">int main(int argc, char *argv[])
{
    /* required: setup the Julia context */
    jl_init();

    /* run Julia commands */
    jl_eval_string(&quot;print(sqrt(2.0))&quot;);

    /* strongly recommended: notify Julia that the
         program is about to terminate. this allows
         Julia time to cleanup pending write requests
         and run all finalizers
    */
    jl_atexit_hook(0);
    return 0;
}</code></pre><p>The next step is to set up the project to find the Julia include files and the libraries. It&#39;s important to know whether the Julia installation is 32- or 64-bit. Remove any platform configuration that doesn&#39;t correspond to the Julia installation before proceeding.</p><p>Using the project Properties dialog, go to <code>C/C++</code> | <code>General</code> and add <code>$(JULIA_DIR)\include\julia\</code> to the Additional Include Directories property. Then, go to the <code>Linker</code> | <code>General</code> section and add <code>$(JULIA_DIR)\lib</code> to the Additional Library Directories property. Finally, under <code>Linker</code> | <code>Input</code>, add <code>libjulia.dll.a;libopenlibm.dll.a;</code> to the list of libraries.</p><p>At this point, the project should build and run.</p><h2 id="Converting-Types"><a class="docs-heading-anchor" href="#Converting-Types">Converting Types</a><a id="Converting-Types-1"></a><a class="docs-heading-anchor-permalink" href="#Converting-Types" title="Permalink"></a></h2><p>Real applications will not only need to execute expressions, but also return their values to the host program. <code>jl_eval_string</code> returns a <code>jl_value_t*</code>, which is a pointer to a heap-allocated Julia object. Storing simple data types like <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> in this way is called <code>boxing</code>, and extracting the stored primitive data is called <code>unboxing</code>. Our improved sample program that calculates the square root of 2 in Julia and reads back the result in C has a body that now contains this code:</p><pre><code class="language-c hljs">jl_value_t *ret = jl_eval_string(&quot;sqrt(2.0)&quot;);

if (jl_typeis(ret, jl_float64_type)) {
    double ret_unboxed = jl_unbox_float64(ret);
    printf(&quot;sqrt(2.0) in C: %e \n&quot;, ret_unboxed);
}
else {
    printf(&quot;ERROR: unexpected return type from sqrt(::Float64)\n&quot;);
}</code></pre><p>In order to check whether <code>ret</code> is of a specific Julia type, we can use the <code>jl_isa</code>, <code>jl_typeis</code>, or <code>jl_is_...</code> functions. By typing <code>typeof(sqrt(2.0))</code> into the Julia shell we can see that the return type is <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> (<code>double</code> in C). To convert the boxed Julia value into a C double the <code>jl_unbox_float64</code> function is used in the above code snippet.</p><p>Corresponding <code>jl_box_...</code> functions are used to convert the other way:</p><pre><code class="language-c hljs">jl_value_t *a = jl_box_float64(3.0);
jl_value_t *b = jl_box_float32(3.0f);
jl_value_t *c = jl_box_int32(3);</code></pre><p>As we will see next, boxing is required to call Julia functions with specific arguments.</p><h2 id="Calling-Julia-Functions"><a class="docs-heading-anchor" href="#Calling-Julia-Functions">Calling Julia Functions</a><a id="Calling-Julia-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Calling-Julia-Functions" title="Permalink"></a></h2><p>While <code>jl_eval_string</code> allows C to obtain the result of a Julia expression, it does not allow passing arguments computed in C to Julia. For this you will need to invoke Julia functions directly, using <code>jl_call</code>:</p><pre><code class="language-c hljs">jl_function_t *func = jl_get_function(jl_base_module, &quot;sqrt&quot;);
jl_value_t *argument = jl_box_float64(2.0);
jl_value_t *ret = jl_call1(func, argument);</code></pre><p>In the first step, a handle to the Julia function <code>sqrt</code> is retrieved by calling <code>jl_get_function</code>. The first argument passed to <code>jl_get_function</code> is a pointer to the <code>Base</code> module in which <code>sqrt</code> is defined. Then, the double value is boxed using <code>jl_box_float64</code>. Finally, in the last step, the function is called using <code>jl_call1</code>. <code>jl_call0</code>, <code>jl_call2</code>, and <code>jl_call3</code> functions also exist, to conveniently handle different numbers of arguments. To pass more arguments, use <code>jl_call</code>:</p><pre><code class="nohighlight hljs">jl_value_t *jl_call(jl_function_t *f, jl_value_t **args, int32_t nargs)</code></pre><p>Its second argument <code>args</code> is an array of <code>jl_value_t*</code> arguments and <code>nargs</code> is the number of arguments.</p><p>There is also an alternative, possibly simpler, way of calling Julia functions and that is via <a href="../base/c.html#Base.@cfunction"><code>@cfunction</code></a>. Using <code>@cfunction</code> allows you to do the type conversions on the Julia side, which is typically easier than doing it on the C side. The <code>sqrt</code> example above would with <code>@cfunction</code> be written as:</p><pre><code class="language-c hljs">double (*sqrt_jl)(double) = jl_unbox_voidpointer(jl_eval_string(&quot;@cfunction(sqrt, Float64, (Float64,))&quot;));
double ret = sqrt_jl(2.0);</code></pre><p>where we first define a C callable function in Julia, extract the function pointer from it, and finally call it. In addition to simplifying type conversions by doing them in the higher-level language, calling Julia functions via <code>@cfunction</code> pointers eliminates the dynamic-dispatch overhead required by <code>jl_call</code> (for which all of the arguments are &quot;boxed&quot;), and should have performance equivalent to native C function pointers.</p><h2 id="Memory-Management"><a class="docs-heading-anchor" href="#Memory-Management">Memory Management</a><a id="Memory-Management-1"></a><a class="docs-heading-anchor-permalink" href="#Memory-Management" title="Permalink"></a></h2><p>As we have seen, Julia objects are represented in C as pointers of type <code>jl_value_t*</code>. This raises the question of who is responsible for freeing these objects.</p><p>Typically, Julia objects are freed by the garbage collector (GC), but the GC does not automatically know that we are holding a reference to a Julia value from C. This means the GC can free objects out from under you, rendering pointers invalid.</p><p>The GC will only run when new Julia objects are being allocated. Calls like <code>jl_box_float64</code> perform allocation, but allocation might also happen at any point in running Julia code.</p><p>When writing code that embeds Julia, it is generally safe to use <code>jl_value_t*</code> values in between <code>jl_...</code> calls (as GC will only get triggered by those calls). But in order to make sure that values can survive <code>jl_...</code> calls, we have to tell Julia that we still hold a reference to Julia <a href="https://www.cs.purdue.edu/homes/hosking/690M/p611-fenichel.pdf">root</a> values, a process called &quot;GC rooting&quot;. Rooting a value will ensure that the garbage collector does not accidentally identify this value as unused and free the memory backing that value. This can be done using the <code>JL_GC_PUSH</code> macros:</p><pre><code class="language-c hljs">jl_value_t *ret = jl_eval_string(&quot;sqrt(2.0)&quot;);
JL_GC_PUSH1(&amp;ret);
// Do something with ret
JL_GC_POP();</code></pre><p>The <code>JL_GC_POP</code> call releases the references established by the previous <code>JL_GC_PUSH</code>. Note that <code>JL_GC_PUSH</code> stores references on the C stack, so it must be exactly paired with a <code>JL_GC_POP</code> before the scope is exited. That is, before the function returns, or control flow otherwise leaves the block in which the <code>JL_GC_PUSH</code> was invoked.</p><p>Several Julia values can be pushed at once using the <code>JL_GC_PUSH2</code> to <code>JL_GC_PUSH6</code> macros:</p><pre><code class="nohighlight hljs">JL_GC_PUSH2(&amp;ret1, &amp;ret2);
// ...
JL_GC_PUSH6(&amp;ret1, &amp;ret2, &amp;ret3, &amp;ret4, &amp;ret5, &amp;ret6);</code></pre><p>To push an array of Julia values one can use the <code>JL_GC_PUSHARGS</code> macro, which can be used as follows:</p><pre><code class="language-c hljs">jl_value_t **args;
JL_GC_PUSHARGS(args, 2); // args can now hold 2 `jl_value_t*` objects
args[0] = some_value;
args[1] = some_other_value;
// Do something with args (e.g. call jl_... functions)
JL_GC_POP();</code></pre><p>Each scope must have only one call to <code>JL_GC_PUSH*</code>, and should be paired with only a single <code>JL_GC_POP</code> call. If all necessary variables you want to root cannot be pushed by a one single call to <code>JL_GC_PUSH*</code>, or if there are more than 6 variables to be pushed and using an array of arguments is not an option, then one can use inner blocks:</p><pre><code class="language-c hljs">jl_value_t *ret1 = jl_eval_string(&quot;sqrt(2.0)&quot;);
JL_GC_PUSH1(&amp;ret1);
jl_value_t *ret2 = 0;
{
    jl_function_t *func = jl_get_function(jl_base_module, &quot;exp&quot;);
    ret2 = jl_call1(func, ret1);
    JL_GC_PUSH1(&amp;ret2);
    // Do something with ret2.
    JL_GC_POP();    // This pops ret2.
}
JL_GC_POP();    // This pops ret1.</code></pre><p>Note that it is not necessary to have valid <code>jl_value_t*</code> values before calling <code>JL_GC_PUSH*</code>. It is fine to have a number of them initialized to <code>NULL</code>, pass those to <code>JL_GC_PUSH*</code> and then create the actual Julia values. For example:</p><pre><code class="nohighlight hljs">jl_value_t *ret1 = NULL, *ret2 = NULL;
JL_GC_PUSH2(&amp;ret1, &amp;ret2);
ret1 = jl_eval_string(&quot;sqrt(2.0)&quot;);
ret2 = jl_eval_string(&quot;sqrt(3.0)&quot;);
// Use ret1 and ret2
JL_GC_POP();</code></pre><p>If it is required to hold the pointer to a variable between functions (or block scopes), then it is not possible to use <code>JL_GC_PUSH*</code>. In this case, it is necessary to create and keep a reference to the variable in the Julia global scope. One simple way to accomplish this is to use a global <code>IdDict</code> that will hold the references, avoiding deallocation by the GC. However, this method will only work properly with mutable types.</p><pre><code class="language-c hljs">// This functions shall be executed only once, during the initialization.
jl_value_t* refs = jl_eval_string(&quot;refs = IdDict()&quot;);
jl_function_t* setindex = jl_get_function(jl_base_module, &quot;setindex!&quot;);

...

// `var` is the variable we want to protect between function calls.
jl_value_t* var = 0;

...

// `var` is a `Vector{Float64}`, which is mutable.
var = jl_eval_string(&quot;[sqrt(2.0); sqrt(4.0); sqrt(6.0)]&quot;);

// To protect `var`, add its reference to `refs`.
jl_call3(setindex, refs, var, var);</code></pre><p>If the variable is immutable, then it needs to be wrapped in an equivalent mutable container or, preferably, in a <code>RefValue{Any}</code> before it is pushed to <code>IdDict</code>. In this approach, the container has to be created or filled in via C code using, for example, the function <code>jl_new_struct</code>. If the container is created by <code>jl_call*</code>, then you will need to reload the pointer to be used in C code.</p><pre><code class="language-c hljs">// This functions shall be executed only once, during the initialization.
jl_value_t* refs = jl_eval_string(&quot;refs = IdDict()&quot;);
jl_function_t* setindex = jl_get_function(jl_base_module, &quot;setindex!&quot;);
jl_datatype_t* reft = (jl_datatype_t*)jl_eval_string(&quot;Base.RefValue{Any}&quot;);

...

// `var` is the variable we want to protect between function calls.
jl_value_t* var = 0;

...

// `var` is a `Float64`, which is immutable.
var = jl_eval_string(&quot;sqrt(2.0)&quot;);

// Protect `var` until we add its reference to `refs`.
JL_GC_PUSH1(&amp;var);

// Wrap `var` in `RefValue{Any}` and push to `refs` to protect it.
jl_value_t* rvar = jl_new_struct(reft, var);
JL_GC_POP();

jl_call3(setindex, refs, rvar, rvar);</code></pre><p>The GC can be allowed to deallocate a variable by removing the reference to it from <code>refs</code> using the function <code>delete!</code>, provided that no other reference to the variable is kept anywhere:</p><pre><code class="language-c hljs">jl_function_t* delete = jl_get_function(jl_base_module, &quot;delete!&quot;);
jl_call2(delete, refs, rvar);</code></pre><p>As an alternative for very simple cases, it is possible to just create a global container of type <code>Vector{Any}</code> and fetch the elements from that when necessary, or even to create one global variable per pointer using</p><pre><code class="language-c hljs">jl_module_t *mod = jl_main_module;
jl_sym_t *var = jl_symbol(&quot;var&quot;);
jl_binding_t *bp = jl_get_binding_wr(mod, var, 1);
jl_checked_assignment(bp, mod, var, val);</code></pre><h3 id="Updating-fields-of-GC-managed-objects"><a class="docs-heading-anchor" href="#Updating-fields-of-GC-managed-objects">Updating fields of GC-managed objects</a><a id="Updating-fields-of-GC-managed-objects-1"></a><a class="docs-heading-anchor-permalink" href="#Updating-fields-of-GC-managed-objects" title="Permalink"></a></h3><p>The garbage collector also operates under the assumption that it is aware of every older-generation object pointing to a younger-generation one. Any time a pointer is updated breaking that assumption, it must be signaled to the collector with the <code>jl_gc_wb</code> (write barrier) function like so:</p><pre><code class="language-c hljs">jl_value_t *parent = some_old_value, *child = some_young_value;
((some_specific_type*)parent)-&gt;field = child;
jl_gc_wb(parent, child);</code></pre><p>It is in general impossible to predict which values will be old at runtime, so the write barrier must be inserted after all explicit stores. One notable exception is if the <code>parent</code> object has just been allocated and no garbage collection has run since then. Note that most <code>jl_...</code> functions can sometimes invoke garbage collection.</p><p>The write barrier is also necessary for arrays of pointers when updating their data directly. Calling <code>jl_array_ptr_set</code> is usually much preferred. But direct updates can be done. For example:</p><pre><code class="language-c hljs">jl_array_t *some_array = ...; // e.g. a Vector{Any}
void **data = jl_array_data(some_array, void*);
jl_value_t *some_value = ...;
data[0] = some_value;
jl_gc_wb(jl_array_owner(some_array), some_value);</code></pre><h3 id="Controlling-the-Garbage-Collector"><a class="docs-heading-anchor" href="#Controlling-the-Garbage-Collector">Controlling the Garbage Collector</a><a id="Controlling-the-Garbage-Collector-1"></a><a class="docs-heading-anchor-permalink" href="#Controlling-the-Garbage-Collector" title="Permalink"></a></h3><p>There are some functions to control the GC. In normal use cases, these should not be necessary.</p><table><tr><th style="text-align: left">Function</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left"><code>jl_gc_collect()</code></td><td style="text-align: left">Force a GC run</td></tr><tr><td style="text-align: left"><code>jl_gc_enable(0)</code></td><td style="text-align: left">Disable the GC, return previous state as int</td></tr><tr><td style="text-align: left"><code>jl_gc_enable(1)</code></td><td style="text-align: left">Enable the GC,  return previous state as int</td></tr><tr><td style="text-align: left"><code>jl_gc_is_enabled()</code></td><td style="text-align: left">Return current state as int</td></tr></table><h2 id="Working-with-Arrays"><a class="docs-heading-anchor" href="#Working-with-Arrays">Working with Arrays</a><a id="Working-with-Arrays-1"></a><a class="docs-heading-anchor-permalink" href="#Working-with-Arrays" title="Permalink"></a></h2><p>Julia and C can share array data without copying. The next example will show how this works.</p><p>Julia arrays are represented in C by the datatype <code>jl_array_t*</code>. Basically, <code>jl_array_t</code> is a struct that contains:</p><ul><li>Information about the datatype</li><li>A pointer to the data block</li><li>Information about the sizes of the array</li></ul><p>To keep things simple, we start with a 1D array. Creating an array containing Float64 elements of length 10 can be done like this:</p><pre><code class="language-c hljs">jl_value_t* array_type = jl_apply_array_type((jl_value_t*)jl_float64_type, 1);
jl_array_t* x          = jl_alloc_array_1d(array_type, 10);</code></pre><p>Alternatively, if you have already allocated the array you can generate a thin wrapper around its data:</p><pre><code class="language-c hljs">double *existingArray = (double*)malloc(sizeof(double)*10);
jl_array_t *x = jl_ptr_to_array_1d(array_type, existingArray, 10, 0);</code></pre><p>The last argument is a boolean indicating whether Julia should take ownership of the data. If this argument is non-zero, the GC will call <code>free</code> on the data pointer when the array is no longer referenced.</p><p>In order to access the data of <code>x</code>, we can use <code>jl_array_data</code>:</p><pre><code class="language-c hljs">double *xData = jl_array_data(x, double);</code></pre><p>Now we can fill the array:</p><pre><code class="language-c hljs">for (size_t i = 0; i &lt; jl_array_nrows(x); i++)
    xData[i] = i;</code></pre><p>Now let us call a Julia function that performs an in-place operation on <code>x</code>:</p><pre><code class="language-c hljs">jl_function_t *func = jl_get_function(jl_base_module, &quot;reverse!&quot;);
jl_call1(func, (jl_value_t*)x);</code></pre><p>By printing the array, one can verify that the elements of <code>x</code> are now reversed.</p><h3 id="Accessing-Returned-Arrays"><a class="docs-heading-anchor" href="#Accessing-Returned-Arrays">Accessing Returned Arrays</a><a id="Accessing-Returned-Arrays-1"></a><a class="docs-heading-anchor-permalink" href="#Accessing-Returned-Arrays" title="Permalink"></a></h3><p>If a Julia function returns an array, the return value of <code>jl_eval_string</code> and <code>jl_call</code> can be cast to a <code>jl_array_t*</code>:</p><pre><code class="language-c hljs">jl_function_t *func  = jl_get_function(jl_base_module, &quot;reverse&quot;);
jl_array_t *y = (jl_array_t*)jl_call1(func, (jl_value_t*)x);</code></pre><p>Now the content of <code>y</code> can be accessed as before using <code>jl_array_data</code>. As always, be sure to keep a reference to the array while it is in use.</p><h3 id="Multidimensional-Arrays"><a class="docs-heading-anchor" href="#Multidimensional-Arrays">Multidimensional Arrays</a><a id="Multidimensional-Arrays-1"></a><a class="docs-heading-anchor-permalink" href="#Multidimensional-Arrays" title="Permalink"></a></h3><p>Julia&#39;s multidimensional arrays are stored in memory in column-major order. Here is some code that creates a 2D array and accesses its properties:</p><pre><code class="language-c hljs">// Create 2D array of float64 type
jl_value_t *array_type = jl_apply_array_type((jl_value_t*)jl_float64_type, 2);
int dims[] = {10,5};
jl_array_t *x  = jl_alloc_array_nd(array_type, dims, 2);

// Get array pointer
double *p = jl_array_data(x, double);
// Get number of dimensions
int ndims = jl_array_ndims(x);
// Get the size of the i-th dim
size_t size0 = jl_array_dim(x,0);
size_t size1 = jl_array_dim(x,1);

// Fill array with data
for(size_t i=0; i&lt;size1; i++)
    for(size_t j=0; j&lt;size0; j++)
        p[j + size0*i] = i + j;</code></pre><p>Notice that while Julia arrays use 1-based indexing, the C API uses 0-based indexing (for example in calling <code>jl_array_dim</code>) in order to read as idiomatic C code.</p><h2 id="Exceptions"><a class="docs-heading-anchor" href="#Exceptions">Exceptions</a><a id="Exceptions-1"></a><a class="docs-heading-anchor-permalink" href="#Exceptions" title="Permalink"></a></h2><p>Julia code can throw exceptions. For example, consider:</p><pre><code class="language-c hljs">jl_eval_string(&quot;this_function_does_not_exist()&quot;);</code></pre><p>This call will appear to do nothing. However, it is possible to check whether an exception was thrown:</p><pre><code class="language-c hljs">if (jl_exception_occurred())
    printf(&quot;%s \n&quot;, jl_typeof_str(jl_exception_occurred()));</code></pre><p>If you are using the Julia C API from a language that supports exceptions (e.g. Python, C#, C++), it makes sense to wrap each call into <code>libjulia</code> with a function that checks whether an exception was thrown, and then rethrows the exception in the host language.</p><h3 id="Throwing-Julia-Exceptions"><a class="docs-heading-anchor" href="#Throwing-Julia-Exceptions">Throwing Julia Exceptions</a><a id="Throwing-Julia-Exceptions-1"></a><a class="docs-heading-anchor-permalink" href="#Throwing-Julia-Exceptions" title="Permalink"></a></h3><p>When writing Julia callable functions, it might be necessary to validate arguments and throw exceptions to indicate errors. A typical type check looks like:</p><pre><code class="language-c hljs">if (!jl_typeis(val, jl_float64_type)) {
    jl_type_error(function_name, (jl_value_t*)jl_float64_type, val);
}</code></pre><p>General exceptions can be raised using the functions:</p><pre><code class="language-c hljs">void jl_error(const char *str);
void jl_errorf(const char *fmt, ...);</code></pre><p><code>jl_error</code> takes a C string, and <code>jl_errorf</code> is called like <code>printf</code>:</p><pre><code class="language-c hljs">jl_errorf(&quot;argument x = %d is too large&quot;, x);</code></pre><p>where in this example <code>x</code> is assumed to be an integer.</p><h3 id="Thread-safety"><a class="docs-heading-anchor" href="#Thread-safety">Thread-safety</a><a id="Thread-safety-1"></a><a class="docs-heading-anchor-permalink" href="#Thread-safety" title="Permalink"></a></h3><p>In general, the Julia C API is not fully thread-safe. When embedding Julia in a multi-threaded application care needs to be taken not to violate the following restrictions:</p><ul><li><code>jl_init()</code> may only be called once in the application life-time. The same applies to <code>jl_atexit_hook()</code>, and it may only be called after <code>jl_init()</code>.</li><li><code>jl_...()</code> API functions may only be called from the thread in which <code>jl_init()</code> was called, <em>or from threads started by the Julia runtime</em>. Calling Julia API functions from user-started threads is not supported, and may lead to undefined behaviour and crashes.</li></ul><p>The second condition above implies that you can not safely call <code>jl_...()</code> functions from threads that were not started by Julia (the thread calling <code>jl_init()</code> being the exception). For example, the following is not supported and will most likely segfault:</p><pre><code class="language-c hljs">void *func(void*)
{
    // Wrong, jl_eval_string() called from thread that was not started by Julia
    jl_eval_string(&quot;println(Threads.threadid())&quot;);
    return NULL;
}

int main()
{
    pthread_t t;

    jl_init();

    // Start a new thread
    pthread_create(&amp;t, NULL, func, NULL);
    pthread_join(t, NULL);

    jl_atexit_hook(0);
}</code></pre><p>Instead, performing all Julia calls from the same user-created thread will work:</p><pre><code class="language-c hljs">void *func(void*)
{
    // Okay, all jl_...() calls from the same thread,
    // even though it is not the main application thread
    jl_init();
    jl_eval_string(&quot;println(Threads.threadid())&quot;);
    jl_atexit_hook(0);
    return NULL;
}

int main()
{
    pthread_t t;
    // Create a new thread, which runs func()
    pthread_create(&amp;t, NULL, func, NULL);
    pthread_join(t, NULL);
}</code></pre><p>An example of calling the Julia C API from a thread started by Julia itself:</p><pre><code class="language-c hljs">#include &lt;julia/julia.h&gt;
JULIA_DEFINE_FAST_TLS

double c_func(int i)
{
    printf(&quot;[C %08x] i = %d\n&quot;, pthread_self(), i);

    // Call the Julia sqrt() function to compute the square root of i, and return it
    jl_function_t *sqrt = jl_get_function(jl_base_module, &quot;sqrt&quot;);
    jl_value_t* arg = jl_box_int32(i);
    double ret = jl_unbox_float64(jl_call1(sqrt, arg));

    return ret;
}

int main()
{
    jl_init();

    // Define a Julia function func() that calls our c_func() defined in C above
    jl_eval_string(&quot;func(i) = ccall(:c_func, Float64, (Int32,), i)&quot;);

    // Call func() multiple times, using multiple threads to do so
    jl_eval_string(&quot;println(Threads.threadpoolsize())&quot;);
    jl_eval_string(&quot;use(i) = println(\&quot;[J $(Threads.threadid())] i = $(i) -&gt; $(func(i))\&quot;)&quot;);
    jl_eval_string(&quot;Threads.@threads for i in 1:5 use(i) end&quot;);

    jl_atexit_hook(0);
}</code></pre><p>If we run this code with 2 Julia threads we get the following output (note: the output will vary per run and system):</p><pre><code class="language-sh hljs">$ JULIA_NUM_THREADS=2 ./thread_example
2
[C 3bfd9c00] i = 1
[C 23938640] i = 4
[J 1] i = 1 -&gt; 1.0
[C 3bfd9c00] i = 2
[J 1] i = 2 -&gt; 1.4142135623730951
[C 3bfd9c00] i = 3
[J 2] i = 4 -&gt; 2.0
[C 23938640] i = 5
[J 1] i = 3 -&gt; 1.7320508075688772
[J 2] i = 5 -&gt; 2.23606797749979</code></pre><p>As can be seen, Julia thread 1 corresponds to pthread ID 3bfd9c00, and Julia thread 2 corresponds to ID 23938640, showing that indeed multiple threads are used at the C level, and that we can safely call Julia C API routines from those threads.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="environment-variables.html">« Environment Variables</a><a class="docs-footer-nextpage" href="code-loading.html">Code Loading »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
