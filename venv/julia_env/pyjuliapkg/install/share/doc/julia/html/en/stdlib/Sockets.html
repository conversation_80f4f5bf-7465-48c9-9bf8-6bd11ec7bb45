<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Sockets · The Julia Language</title><meta name="title" content="Sockets · The Julia Language"/><meta property="og:title" content="Sockets · The Julia Language"/><meta property="twitter:title" content="Sockets · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li class="is-active"><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="Sockets.html">Sockets</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="Sockets.html">Sockets</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/stdlib/Sockets/docs/src/index.md" title="View source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Sockets"><a class="docs-heading-anchor" href="#Sockets">Sockets</a><a id="Sockets-1"></a><a class="docs-heading-anchor-permalink" href="#Sockets" title="Permalink"></a></h1><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.Sockets" href="#Sockets.Sockets"><code>Sockets.Sockets</code></a> — <span class="docstring-category">Module</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>Support for sockets. Provides <a href="Sockets.html#Sockets.IPAddr"><code>IPAddr</code></a> and subtypes, <a href="Sockets.html#Sockets.TCPSocket"><code>TCPSocket</code></a>, and <a href="Sockets.html#Sockets.UDPSocket"><code>UDPSocket</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/Sockets.jl#L3-L5">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.connect-Tuple{TCPSocket, Integer}" href="#Sockets.connect-Tuple{TCPSocket, Integer}"><code>Sockets.connect</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">connect([host], port::Integer) -&gt; TCPSocket</code></pre><p>Connect to the host <code>host</code> on port <code>port</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/Sockets.jl#L539-L543">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.connect-Tuple{AbstractString}" href="#Sockets.connect-Tuple{AbstractString}"><code>Sockets.connect</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">connect(path::AbstractString) -&gt; PipeEndpoint</code></pre><p>Connect to the named pipe / UNIX domain socket at <code>path</code>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Path length on Unix is limited to somewhere between 92 and 108 bytes (cf. <code>man unix</code>).</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/PipeServer.jl#L95-L102">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.listen-Tuple{Any}" href="#Sockets.listen-Tuple{Any}"><code>Sockets.listen</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">listen([addr, ]port::Integer; backlog::Integer=BACKLOG_DEFAULT) -&gt; TCPServer</code></pre><p>Listen on port on the address specified by <code>addr</code>. By default this listens on <code>localhost</code> only. To listen on all interfaces pass <code>IPv4(0)</code> or <code>IPv6(0)</code> as appropriate. <code>backlog</code> determines how many connections can be pending (not having called <a href="Sockets.html#Sockets.accept"><code>accept</code></a>) before the server will begin to reject them. The default value of <code>backlog</code> is 511.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/Sockets.jl#L612-L621">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.listen-Tuple{AbstractString}" href="#Sockets.listen-Tuple{AbstractString}"><code>Sockets.listen</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">listen(path::AbstractString) -&gt; PipeServer</code></pre><p>Create and listen on a named pipe / UNIX domain socket.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Path length on Unix is limited to somewhere between 92 and 108 bytes (cf. <code>man unix</code>).</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/PipeServer.jl#L69-L76">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.getaddrinfo" href="#Sockets.getaddrinfo"><code>Sockets.getaddrinfo</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">getaddrinfo(host::AbstractString, IPAddr) -&gt; IPAddr</code></pre><p>Gets the first IP address of the <code>host</code> of the specified <code>IPAddr</code> type. Uses the operating system&#39;s underlying getaddrinfo implementation, which may do a DNS lookup.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; getaddrinfo(&quot;localhost&quot;, IPv6)
ip&quot;::1&quot;

julia&gt; getaddrinfo(&quot;localhost&quot;, IPv4)
ip&quot;127.0.0.1&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/addrinfo.jl#L124-L139">source</a></section><section><div><pre><code class="language-julia hljs">getaddrinfo(host::AbstractString) -&gt; IPAddr</code></pre><p>Gets the first available IP address of <code>host</code>, which may be either an <code>IPv4</code> or <code>IPv6</code> address. Uses the operating system&#39;s underlying getaddrinfo implementation, which may do a DNS lookup.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/addrinfo.jl#L151-L157">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.getipaddr" href="#Sockets.getipaddr"><code>Sockets.getipaddr</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">getipaddr() -&gt; IPAddr</code></pre><p>Get an IP address of the local machine, preferring IPv4 over IPv6. Throws if no addresses are available.</p><pre><code class="nohighlight hljs">getipaddr(addr_type::Type{T}) where T&lt;:IPAddr -&gt; T</code></pre><p>Get an IP address of the local machine of the specified type. Throws if no addresses of the specified type are available.</p><p>This function is a backwards-compatibility wrapper around <a href="Sockets.html#Sockets.getipaddrs"><code>getipaddrs</code></a>. New applications should use <a href="Sockets.html#Sockets.getipaddrs"><code>getipaddrs</code></a> instead.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; getipaddr()
ip&quot;************&quot;

julia&gt; getipaddr(IPv6)
ip&quot;fe80::9731:35af:e1c5:6e49&quot;</code></pre><p>See also <a href="Sockets.html#Sockets.getipaddrs"><code>getipaddrs</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/addrinfo.jl#L258-L282">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.getipaddrs" href="#Sockets.getipaddrs"><code>Sockets.getipaddrs</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">getipaddrs(addr_type::Type{T}=IPAddr; loopback::Bool=false) where T&lt;:IPAddr -&gt; Vector{T}</code></pre><p>Get the IP addresses of the local machine.</p><p>Setting the optional <code>addr_type</code> parameter to <code>IPv4</code> or <code>IPv6</code> causes only addresses of that type to be returned.</p><p>The <code>loopback</code> keyword argument dictates whether loopback addresses (e.g. <code>ip&quot;127.0.0.1&quot;</code>, <code>ip&quot;::1&quot;</code>) are included.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p>This function is available as of Julia 1.2.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; getipaddrs()
5-element Array{IPAddr,1}:
 ip&quot;*************&quot;
 ip&quot;***********&quot;
 ip&quot;2001:db8:8:4:445e:5fff:fe5d:5500&quot;
 ip&quot;2001:db8:8:4:c164:402e:7e3c:3668&quot;
 ip&quot;fe80::445e:5fff:fe5d:5500&quot;

julia&gt; getipaddrs(IPv6)
3-element Array{IPv6,1}:
 ip&quot;2001:db8:8:4:445e:5fff:fe5d:5500&quot;
 ip&quot;2001:db8:8:4:c164:402e:7e3c:3668&quot;
 ip&quot;fe80::445e:5fff:fe5d:5500&quot;</code></pre><p>See also <a href="Sockets.html#Sockets.islinklocaladdr"><code>islinklocaladdr</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/addrinfo.jl#L297-L327">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.islinklocaladdr" href="#Sockets.islinklocaladdr"><code>Sockets.islinklocaladdr</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">islinklocaladdr(addr::IPAddr)</code></pre><p>Tests if an IP address is a link-local address. Link-local addresses are not guaranteed to be unique beyond their network segment, therefore routers do not forward them. Link-local addresses are from the address blocks <code>***********/16</code> or <code>fe80::/10</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">filter(!islinklocaladdr, getipaddrs())</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/addrinfo.jl#L357-L369">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.getalladdrinfo" href="#Sockets.getalladdrinfo"><code>Sockets.getalladdrinfo</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">getalladdrinfo(host::AbstractString) -&gt; Vector{IPAddr}</code></pre><p>Gets all of the IP addresses of the <code>host</code>. Uses the operating system&#39;s underlying <code>getaddrinfo</code> implementation, which may do a DNS lookup.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; getalladdrinfo(&quot;google.com&quot;)
2-element Array{IPAddr,1}:
 ip&quot;*************&quot;
 ip&quot;2607:f8b0:4000:804::200e&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/addrinfo.jl#L52-L65">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.DNSError" href="#Sockets.DNSError"><code>Sockets.DNSError</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">DNSError</code></pre><p>The type of exception thrown when an error occurs in DNS lookup. The <code>host</code> field indicates the host URL string. The <code>code</code> field indicates the error code based on libuv.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/addrinfo.jl#L3-L9">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.getnameinfo" href="#Sockets.getnameinfo"><code>Sockets.getnameinfo</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">getnameinfo(host::IPAddr) -&gt; String</code></pre><p>Performs a reverse-lookup for IP address to return a hostname and service using the operating system&#39;s underlying <code>getnameinfo</code> implementation.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; getnameinfo(IPv4(&quot;*******&quot;))
&quot;google-public-dns-a.google.com&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/addrinfo.jl#L183-L194">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.getsockname" href="#Sockets.getsockname"><code>Sockets.getsockname</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">getsockname(sock::Union{TCPServer, TCPSocket}) -&gt; (IPAddr, UInt16)</code></pre><p>Get the IP address and port that the given socket is bound to.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/Sockets.jl#L793-L797">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.getpeername" href="#Sockets.getpeername"><code>Sockets.getpeername</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">getpeername(sock::TCPSocket) -&gt; (IPAddr, UInt16)</code></pre><p>Get the IP address and port of the remote endpoint that the given socket is connected to. Valid only for connected TCP sockets.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/Sockets.jl#L801-L806">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.IPAddr" href="#Sockets.IPAddr"><code>Sockets.IPAddr</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">IPAddr</code></pre><p>Abstract supertype for IP addresses. <a href="Sockets.html#Sockets.IPv4"><code>IPv4</code></a> and <a href="Sockets.html#Sockets.IPv6"><code>IPv6</code></a> are subtypes of this.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/IPAddr.jl#L3-L7">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.IPv4" href="#Sockets.IPv4"><code>Sockets.IPv4</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">IPv4(host::Integer) -&gt; IPv4</code></pre><p>Return an IPv4 object from IP address <code>host</code> formatted as an <a href="../base/numbers.html#Core.Integer"><code>Integer</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; IPv4(3223256218)
ip&quot;**************&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/IPAddr.jl#L31-L41">source</a></section><section><div><pre><code class="language-julia hljs">IPv4(str::AbstractString) -&gt; IPv4</code></pre><p>Parse an IPv4 address string into an <code>IPv4</code> object.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; IPv4(&quot;127.0.0.1&quot;)
ip&quot;127.0.0.1&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/IPAddr.jl#L52-L62">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.IPv6" href="#Sockets.IPv6"><code>Sockets.IPv6</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">IPv6(host::Integer) -&gt; IPv6</code></pre><p>Return an IPv6 object from IP address <code>host</code> formatted as an <a href="../base/numbers.html#Core.Integer"><code>Integer</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; IPv6(3223256218)
ip&quot;::c01e:fc9a&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/IPAddr.jl#L94-L104">source</a></section><section><div><pre><code class="language-julia hljs">IPv6(str::AbstractString) -&gt; IPv6</code></pre><p>Parse an IPv6 address string into an <code>IPv6</code> object.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; IPv6(&quot;::1&quot;)
ip&quot;::1&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/IPAddr.jl#L117-L127">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.@ip_str" href="#Sockets.@ip_str"><code>Sockets.@ip_str</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@ip_str str -&gt; IPAddr</code></pre><p>Parse <code>str</code> as an IP address.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; ip&quot;127.0.0.1&quot;
ip&quot;127.0.0.1&quot;

julia&gt; @ip_str &quot;2001:db8:0:0:0:0:2:1&quot;
ip&quot;2001:db8::2:1&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/IPAddr.jl#L275-L288">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.TCPSocket" href="#Sockets.TCPSocket"><code>Sockets.TCPSocket</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">TCPSocket(; delay=true)</code></pre><p>Open a TCP socket using libuv. If <code>delay</code> is true, libuv delays creation of the socket&#39;s file descriptor till the first <a href="Sockets.html#Base.bind"><code>bind</code></a> call. <code>TCPSocket</code> has various fields to denote the state of the socket as well as its send/receive buffers.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/Sockets.jl#L51-L57">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.UDPSocket" href="#Sockets.UDPSocket"><code>Sockets.UDPSocket</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">UDPSocket()</code></pre><p>Open a UDP socket using libuv. <code>UDPSocket</code> has various fields to denote the state of the socket.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/Sockets.jl#L169-L174">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.accept" href="#Sockets.accept"><code>Sockets.accept</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">accept(server[, client])</code></pre><p>Accepts a connection on the given server and returns a connection to the client. An uninitialized client stream may be provided, in which case it will be used instead of creating a new stream.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/Sockets.jl#L142-L148">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.listenany" href="#Sockets.listenany"><code>Sockets.listenany</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">listenany([host::IPAddr,] port_hint; backlog::Integer=BACKLOG_DEFAULT) -&gt; (UInt16, TCPServer)</code></pre><p>Create a <code>TCPServer</code> on any port, using hint as a starting point. Returns a tuple of the actual port that the server was created on and the server itself. The backlog argument defines the maximum length to which the queue of pending connections for sockfd may grow.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/Sockets.jl#L715-L721">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.bind" href="#Base.bind"><code>Base.bind</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">bind(socket::Union{TCPServer, UDPSocket, TCPSocket}, host::IPAddr, port::Integer; ipv6only=false, reuseaddr=false, kws...)</code></pre><p>Bind <code>socket</code> to the given <code>host:port</code>. Note that <code>0.0.0.0</code> will listen on all devices.</p><ul><li>The <code>ipv6only</code> parameter disables dual stack mode. If <code>ipv6only=true</code>, only an IPv6 stack is created.</li><li>If <code>reuseaddr=true</code>, multiple threads or processes can bind to the same address without error if they all set <code>reuseaddr=true</code>, but only the last to bind will receive any traffic.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/Sockets.jl#L245-L253">source</a></section><section><div><pre><code class="language-julia hljs">bind(chnl::Channel, task::Task)</code></pre><p>Associate the lifetime of <code>chnl</code> with a task. <code>Channel</code> <code>chnl</code> is automatically closed when the task terminates. Any uncaught exception in the task is propagated to all waiters on <code>chnl</code>.</p><p>The <code>chnl</code> object can be explicitly closed independent of task termination. Terminating tasks have no effect on already closed <code>Channel</code> objects.</p><p>When a channel is bound to multiple tasks, the first task to terminate will close the channel. When multiple channels are bound to the same task, termination of the task will close all of the bound channels.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; c = Channel(0);

julia&gt; task = @async foreach(i-&gt;put!(c, i), 1:4);

julia&gt; bind(c,task);

julia&gt; for i in c
           @show i
       end;
i = 1
i = 2
i = 3
i = 4

julia&gt; isopen(c)
false</code></pre><pre><code class="language-julia-repl hljs">julia&gt; c = Channel(0);

julia&gt; task = @async (put!(c, 1); error(&quot;foo&quot;));

julia&gt; bind(c, task);

julia&gt; take!(c)
1

julia&gt; put!(c, 1);
ERROR: TaskFailedException
Stacktrace:
[...]
    nested task error: foo
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/channels.jl#L237-L288">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.send" href="#Sockets.send"><code>Sockets.send</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">send(socket::UDPSocket, host::IPAddr, port::Integer, msg)</code></pre><p>Send <code>msg</code> over <code>socket</code> to <code>host:port</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/Sockets.jl#L430-L434">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.recv" href="#Sockets.recv"><code>Sockets.recv</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">recv(socket::UDPSocket)</code></pre><p>Read a UDP packet from the specified socket, and return the bytes received. This call blocks.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/Sockets.jl#L318-L322">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.recvfrom" href="#Sockets.recvfrom"><code>Sockets.recvfrom</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">recvfrom(socket::UDPSocket) -&gt; (host_port, data)</code></pre><p>Read a UDP packet from the specified socket, returning a tuple of <code>(host_port, data)</code>, where <code>host_port</code> will be an InetAddr{IPv4} or InetAddr{IPv6}, as appropriate.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p>Prior to Julia version 1.3, the first returned value was an address (<code>IPAddr</code>). In version 1.3 it was changed to an <code>InetAddr</code>.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/Sockets.jl#L330-L339">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.setopt" href="#Sockets.setopt"><code>Sockets.setopt</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">setopt(sock::UDPSocket; multicast_loop=nothing, multicast_ttl=nothing, enable_broadcast=nothing, ttl=nothing)</code></pre><p>Set UDP socket options.</p><ul><li><code>multicast_loop</code>: loopback for multicast packets (default: <code>true</code>).</li><li><code>multicast_ttl</code>: TTL for multicast packets (default: <code>nothing</code>).</li><li><code>enable_broadcast</code>: flag must be set to <code>true</code> if socket will be used for broadcast messages, or else the UDP system will return an access error (default: <code>false</code>).</li><li><code>ttl</code>: Time-to-live of packets sent on the socket (default: <code>nothing</code>).</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/Sockets.jl#L286-L296">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.nagle" href="#Sockets.nagle"><code>Sockets.nagle</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">nagle(socket::Union{TCPServer, TCPSocket}, enable::Bool)</code></pre><p>Nagle&#39;s algorithm batches multiple small TCP packets into larger ones. This can improve throughput but worsen latency. Nagle&#39;s algorithm is enabled by default. This function sets whether Nagle&#39;s algorithm is active on a given TCP server or socket. The opposite option is called <code>TCP_NODELAY</code> in other languages.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p>This function requires Julia 1.3 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/Sockets.jl#L567-L578">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Sockets.quickack" href="#Sockets.quickack"><code>Sockets.quickack</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">quickack(socket::Union{TCPServer, TCPSocket}, enable::Bool)</code></pre><p>On Linux systems, the TCP_QUICKACK is disabled or enabled on <code>socket</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Sockets/src/Sockets.jl#L589-L593">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="SharedArrays.html">« Shared Arrays</a><a class="docs-footer-nextpage" href="SparseArrays.html">Sparse Arrays »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
