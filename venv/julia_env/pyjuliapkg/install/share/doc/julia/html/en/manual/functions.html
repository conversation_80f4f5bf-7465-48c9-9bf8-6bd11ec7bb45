<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Functions · The Julia Language</title><meta name="title" content="Functions · The Julia Language"/><meta property="og:title" content="Functions · The Julia Language"/><meta property="twitter:title" content="Functions · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li class="is-active"><a class="tocitem" href="functions.html">Functions</a><ul class="internal"><li><a class="tocitem" href="#man-argument-passing"><span>Argument Passing Behavior</span></a></li><li><a class="tocitem" href="#Argument-type-declarations"><span>Argument-type declarations</span></a></li><li><a class="tocitem" href="#The-return-Keyword"><span>The <code>return</code> Keyword</span></a></li><li><a class="tocitem" href="#Operators-Are-Functions"><span>Operators Are Functions</span></a></li><li><a class="tocitem" href="#Operators-With-Special-Names"><span>Operators With Special Names</span></a></li><li><a class="tocitem" href="#man-anonymous-functions"><span>Anonymous Functions</span></a></li><li><a class="tocitem" href="#Tuples"><span>Tuples</span></a></li><li><a class="tocitem" href="#Named-Tuples"><span>Named Tuples</span></a></li><li><a class="tocitem" href="#destructuring-assignment"><span>Destructuring Assignment and Multiple Return Values</span></a></li><li><a class="tocitem" href="#Property-destructuring"><span>Property destructuring</span></a></li><li><a class="tocitem" href="#man-argument-destructuring"><span>Argument destructuring</span></a></li><li><a class="tocitem" href="#Varargs-Functions"><span>Varargs Functions</span></a></li><li><a class="tocitem" href="#Optional-Arguments"><span>Optional Arguments</span></a></li><li><a class="tocitem" href="#Keyword-Arguments"><span>Keyword Arguments</span></a></li><li><a class="tocitem" href="#Evaluation-Scope-of-Default-Values"><span>Evaluation Scope of Default Values</span></a></li><li><a class="tocitem" href="#Do-Block-Syntax-for-Function-Arguments"><span>Do-Block Syntax for Function Arguments</span></a></li><li><a class="tocitem" href="#Function-composition-and-piping"><span>Function composition and piping</span></a></li><li><a class="tocitem" href="#man-vectorized"><span>Dot Syntax for Vectorizing Functions</span></a></li><li><a class="tocitem" href="#Further-Reading"><span>Further Reading</span></a></li></ul></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="functions.html">Functions</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="functions.html">Functions</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/functions.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="man-functions"><a class="docs-heading-anchor" href="#man-functions">Functions</a><a id="man-functions-1"></a><a class="docs-heading-anchor-permalink" href="#man-functions" title="Permalink"></a></h1><p>In Julia, a function is an object that maps a tuple of argument values to a return value. Julia functions are not pure mathematical functions, because they can alter and be affected by the global state of the program. The basic syntax for defining functions in Julia is:</p><pre><code class="language-julia-repl hljs">julia&gt; function f(x, y)
           x + y
       end
f (generic function with 1 method)</code></pre><p>This function accepts two arguments <code>x</code> and <code>y</code> and returns the value of the last expression evaluated, which is <code>x + y</code>.</p><p>There is a second, more terse syntax for defining a function in Julia. The traditional function declaration syntax demonstrated above is equivalent to the following compact &quot;assignment form&quot;:</p><pre><code class="language-julia-repl hljs">julia&gt; f(x, y) = x + y
f (generic function with 1 method)</code></pre><p>In the assignment form, the body of the function must be a single expression, although it can be a compound expression (see <a href="control-flow.html#man-compound-expressions">Compound Expressions</a>). Short, simple function definitions are common in Julia. The short function syntax is accordingly quite idiomatic, considerably reducing both typing and visual noise.</p><p>A function is called using the traditional parenthesis syntax:</p><pre><code class="language-julia-repl hljs">julia&gt; f(2, 3)
5</code></pre><p>Without parentheses, the expression <code>f</code> refers to the function object, and can be passed around like any other value:</p><pre><code class="language-julia-repl hljs">julia&gt; g = f;

julia&gt; g(2, 3)
5</code></pre><p>As with variables, Unicode can also be used for function names:</p><pre><code class="language-julia-repl hljs">julia&gt; ∑(x, y) = x + y
∑ (generic function with 1 method)

julia&gt; ∑(2, 3)
5</code></pre><h2 id="man-argument-passing"><a class="docs-heading-anchor" href="#man-argument-passing">Argument Passing Behavior</a><a id="man-argument-passing-1"></a><a class="docs-heading-anchor-permalink" href="#man-argument-passing" title="Permalink"></a></h2><p>Julia function arguments follow a convention sometimes called &quot;pass-by-sharing&quot;, which means that values are not copied when they are passed to functions. Function arguments themselves act as new variable <em>bindings</em> (new &quot;names&quot; that can refer to values), much like <a href="variables.html#man-assignment-expressions">assignments</a> <code>argument_name = argument_value</code>, so that the objects they refer to are identical to the passed values. Modifications to mutable values (such as <code>Array</code>s) made within a function will be visible to the caller. (This is the same behavior found in Scheme, most Lisps, Python, Ruby and Perl, among other dynamic languages.)</p><p>For example, in the function</p><pre><code class="language-julia hljs">function f(x, y)
    x[1] = 42    # mutates x
    y = 7 + y    # new binding for y, no mutation
    return y
end</code></pre><p>The statement <code>x[1] = 42</code> <em>mutates</em> the object <code>x</code>, and hence this change <em>will</em> be visible in the array passed by the caller for this argument.   On the other hand, the assignment <code>y = 7 + y</code> changes the <em>binding</em> (&quot;name&quot;) <code>y</code> to refer to a new value <code>7 + y</code>, rather than mutating the <em>original</em> object referred to by <code>y</code>, and hence does <em>not</em> change the corresponding argument passed by the caller.   This can be seen if we call <code>f(x, y)</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; a = [4, 5, 6]
3-element Vector{Int64}:
 4
 5
 6

julia&gt; b = 3
3

julia&gt; f(a, b) # returns 7 + b == 10
10

julia&gt; a  # a[1] is changed to 42 by f
3-element Vector{Int64}:
 42
  5
  6

julia&gt; b  # not changed
3</code></pre><p>As a common convention in Julia (not a syntactic requirement), such a function would <a href="../base/punctuation.html#man-punctuation">typically be named <code>f!(x, y)</code></a> rather than <code>f(x, y)</code>, as a visual reminder at the call site that at least one of the arguments (often the first one) is being mutated.</p><div class="admonition is-warning"><header class="admonition-header">Shared memory between arguments</header><div class="admonition-body"><p>The behavior of a mutating function can be unexpected when a mutated argument shares memory with another argument, a situation known as aliasing (e.g. when one is a view of the other). Unless the function docstring explicitly indicates that aliasing produces the expected result, it is the responsibility of the caller to ensure proper behavior on such inputs.</p></div></div><h2 id="Argument-type-declarations"><a class="docs-heading-anchor" href="#Argument-type-declarations">Argument-type declarations</a><a id="Argument-type-declarations-1"></a><a class="docs-heading-anchor-permalink" href="#Argument-type-declarations" title="Permalink"></a></h2><p>You can declare the types of function arguments by appending <code>::TypeName</code> to the argument name, as usual for <a href="types.html#Type-Declarations">Type Declarations</a> in Julia. For example, the following function computes <a href="https://en.wikipedia.org/wiki/Fibonacci_number">Fibonacci numbers</a> recursively:</p><pre><code class="nohighlight hljs">fib(n::Integer) = n ≤ 2 ? one(n) : fib(n-1) + fib(n-2)</code></pre><p>and the <code>::Integer</code> specification means that it will only be callable when <code>n</code> is a subtype of the <a href="types.html#man-abstract-types">abstract</a> <code>Integer</code> type.</p><p>Argument-type declarations <strong>normally have no impact on performance</strong>: regardless of what argument types (if any) are declared, Julia compiles a specialized version of the function for the actual argument types passed by the caller.   For example, calling <code>fib(1)</code> will trigger the compilation of specialized version of <code>fib</code> optimized specifically for <code>Int</code> arguments, which is then re-used if <code>fib(7)</code> or <code>fib(15)</code> are called.  (There are rare exceptions when an argument-type declaration can trigger additional compiler specializations; see: <a href="performance-tips.html#Be-aware-of-when-Julia-avoids-specializing">Be aware of when Julia avoids specializing</a>.)  The most common reasons to declare argument types in Julia are, instead:</p><ul><li><strong>Dispatch:</strong> As explained in <a href="methods.html#Methods">Methods</a>, you can have different versions (&quot;methods&quot;) of a function for different argument types, in which case the argument types are used to determine which implementation is called for which arguments.  For example, you might implement a completely different algorithm <code>fib(x::Number) = ...</code> that works for any <code>Number</code> type by using <a href="https://en.wikipedia.org/wiki/Fibonacci_number#Binet%27s_formula">Binet&#39;s formula</a> to extend it to non-integer values.</li><li><strong>Correctness:</strong> Type declarations can be useful if your function only returns correct results for certain argument types.  For example, if we omitted argument types and wrote <code>fib(n) = n ≤ 2 ? one(n) : fib(n-1) + fib(n-2)</code>, then <code>fib(1.5)</code> would silently give us the nonsensical answer <code>1.0</code>.</li><li><strong>Clarity:</strong> Type declarations can serve as a form of documentation about the expected arguments.</li></ul><p>However, it is a <strong>common mistake to overly restrict the argument types</strong>, which can unnecessarily limit the applicability of the function and prevent it from being re-used in circumstances you did not anticipate.    For example, the <code>fib(n::Integer)</code> function above works equally well for <code>Int</code> arguments (machine integers) and <code>BigInt</code> arbitrary-precision integers (see <a href="../base/numbers.html#BigFloats-and-BigInts">BigFloats and BigInts</a>), which is especially useful because Fibonacci numbers grow exponentially rapidly and will quickly overflow any fixed-precision type like <code>Int</code> (see <a href="integers-and-floating-point-numbers.html#Overflow-behavior">Overflow behavior</a>).  If we had declared our function as <code>fib(n::Int)</code>, however, the application to <code>BigInt</code> would have been prevented for no reason.   In general, you should use the most general applicable abstract types for arguments, and <strong>when in doubt, omit the argument types</strong>.  You can always add argument-type specifications later if they become necessary, and you don&#39;t sacrifice performance or functionality by omitting them.</p><h2 id="The-return-Keyword"><a class="docs-heading-anchor" href="#The-return-Keyword">The <code>return</code> Keyword</a><a id="The-return-Keyword-1"></a><a class="docs-heading-anchor-permalink" href="#The-return-Keyword" title="Permalink"></a></h2><p>The value returned by a function is the value of the last expression evaluated, which, by default, is the last expression in the body of the function definition. In the example function, <code>f</code>, from the previous section this is the value of the expression <code>x + y</code>. As an alternative, as in many other languages, the <code>return</code> keyword causes a function to return immediately, providing an expression whose value is returned:</p><pre><code class="language-julia hljs">function g(x, y)
    return x * y
    x + y
end</code></pre><p>Since function definitions can be entered into interactive sessions, it is easy to compare these definitions:</p><pre><code class="language-julia-repl hljs">julia&gt; f(x, y) = x + y
f (generic function with 1 method)

julia&gt; function g(x, y)
           return x * y
           x + y
       end
g (generic function with 1 method)

julia&gt; f(2, 3)
5

julia&gt; g(2, 3)
6</code></pre><p>Of course, in a purely linear function body like <code>g</code>, the usage of <code>return</code> is pointless since the expression <code>x + y</code> is never evaluated and we could simply make <code>x * y</code> the last expression in the function and omit the <code>return</code>. In conjunction with other control flow, however, <code>return</code> is of real use. Here, for example, is a function that computes the hypotenuse length of a right triangle with sides of length <code>x</code> and <code>y</code>, avoiding overflow:</p><pre><code class="language-julia-repl hljs">julia&gt; function hypot(x, y)
           x = abs(x)
           y = abs(y)
           if x &gt; y
               r = y/x
               return x*sqrt(1 + r*r)
           end
           if y == 0
               return zero(x)
           end
           r = x/y
           return y*sqrt(1 + r*r)
       end
hypot (generic function with 1 method)

julia&gt; hypot(3, 4)
5.0</code></pre><p>There are three possible points of return from this function, returning the values of three different expressions, depending on the values of <code>x</code> and <code>y</code>. The <code>return</code> on the last line could be omitted since it is the last expression.</p><h3 id="man-functions-return-type"><a class="docs-heading-anchor" href="#man-functions-return-type">Return type</a><a id="man-functions-return-type-1"></a><a class="docs-heading-anchor-permalink" href="#man-functions-return-type" title="Permalink"></a></h3><p>A return type can be specified in the function declaration using the <code>::</code> operator. This converts the return value to the specified type.</p><pre><code class="language-julia-repl hljs">julia&gt; function g(x, y)::Int8
           return x * y
       end;

julia&gt; typeof(g(1, 2))
Int8</code></pre><p>This function will always return an <code>Int8</code> regardless of the types of <code>x</code> and <code>y</code>. See <a href="types.html#Type-Declarations">Type Declarations</a> for more on return types.</p><p>Return type declarations are <strong>rarely used</strong> in Julia: in general, you should instead write &quot;type-stable&quot; functions in which Julia&#39;s compiler can automatically infer the return type.  For more information, see the <a href="performance-tips.html#man-performance-tips">Performance Tips</a> chapter.</p><h3 id="Returning-nothing"><a class="docs-heading-anchor" href="#Returning-nothing">Returning nothing</a><a id="Returning-nothing-1"></a><a class="docs-heading-anchor-permalink" href="#Returning-nothing" title="Permalink"></a></h3><p>For functions that do not need to return a value (functions used only for some side effects), the Julia convention is to return the value <a href="../base/constants.html#Core.nothing"><code>nothing</code></a>:</p><pre><code class="language-julia hljs">function printx(x)
    println(&quot;x = $x&quot;)
    return nothing
end</code></pre><p>This is a <em>convention</em> in the sense that <code>nothing</code> is not a Julia keyword but only a singleton object of type <code>Nothing</code>. Also, you may notice that the <code>printx</code> function example above is contrived, because <code>println</code> already returns <code>nothing</code>, so that the <code>return</code> line is redundant.</p><p>There are two possible shortened forms for the <code>return nothing</code> expression. On the one hand, the <code>return</code> keyword implicitly returns <code>nothing</code>, so it can be used alone. On the other hand, since functions implicitly return their last expression evaluated, <code>nothing</code> can be used alone when it&#39;s the last expression. The preference for the expression <code>return nothing</code> as opposed to <code>return</code> or <code>nothing</code> alone is a matter of coding style.</p><h2 id="Operators-Are-Functions"><a class="docs-heading-anchor" href="#Operators-Are-Functions">Operators Are Functions</a><a id="Operators-Are-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Operators-Are-Functions" title="Permalink"></a></h2><p>In Julia, most operators are just functions with support for special syntax. (The exceptions are operators with special evaluation semantics like <code>&amp;&amp;</code> and <code>||</code>. These operators cannot be functions since <a href="control-flow.html#Short-Circuit-Evaluation">Short-Circuit Evaluation</a> requires that their operands are not evaluated before evaluation of the operator.) Accordingly, you can also apply them using parenthesized argument lists, just as you would any other function:</p><pre><code class="language-julia-repl hljs">julia&gt; 1 + 2 + 3
6

julia&gt; +(1, 2, 3)
6</code></pre><p>The infix form is exactly equivalent to the function application form – in fact the former is parsed to produce the function call internally. This also means that you can assign and pass around operators such as <a href="../base/math.html#Base.:+"><code>+</code></a> and <a href="../base/math.html#Base.:*-Tuple{Any, Vararg{Any}}"><code>*</code></a> just like you would with other function values:</p><pre><code class="language-julia-repl hljs">julia&gt; f = +;

julia&gt; f(1, 2, 3)
6</code></pre><p>Under the name <code>f</code>, the function does not support infix notation, however.</p><h2 id="Operators-With-Special-Names"><a class="docs-heading-anchor" href="#Operators-With-Special-Names">Operators With Special Names</a><a id="Operators-With-Special-Names-1"></a><a class="docs-heading-anchor-permalink" href="#Operators-With-Special-Names" title="Permalink"></a></h2><p>A few special expressions correspond to calls to functions with non-obvious names. These are:</p><table><tr><th style="text-align: left">Expression</th><th style="text-align: left">Calls</th></tr><tr><td style="text-align: left"><code>[A B C ...]</code></td><td style="text-align: left"><a href="../base/arrays.html#Base.hcat"><code>hcat</code></a></td></tr><tr><td style="text-align: left"><code>[A; B; C; ...]</code></td><td style="text-align: left"><a href="../base/arrays.html#Base.vcat"><code>vcat</code></a></td></tr><tr><td style="text-align: left"><code>[A B; C D; ...]</code></td><td style="text-align: left"><a href="../base/arrays.html#Base.hvcat"><code>hvcat</code></a></td></tr><tr><td style="text-align: left"><code>[A; B;; C; D;; ...]</code></td><td style="text-align: left"><a href="../base/arrays.html#Base.hvncat"><code>hvncat</code></a></td></tr><tr><td style="text-align: left"><code>A&#39;</code></td><td style="text-align: left"><a href="../stdlib/LinearAlgebra.html#Base.adjoint"><code>adjoint</code></a></td></tr><tr><td style="text-align: left"><code>A[i]</code></td><td style="text-align: left"><a href="../base/collections.html#Base.getindex"><code>getindex</code></a></td></tr><tr><td style="text-align: left"><code>A[i] = x</code></td><td style="text-align: left"><a href="../base/collections.html#Base.setindex!"><code>setindex!</code></a></td></tr><tr><td style="text-align: left"><code>A.n</code></td><td style="text-align: left"><a href="../base/base.html#Base.getproperty"><code>getproperty</code></a></td></tr><tr><td style="text-align: left"><code>A.n = x</code></td><td style="text-align: left"><a href="../base/base.html#Base.setproperty!"><code>setproperty!</code></a></td></tr></table><p>Note that expressions similar to <code>[A; B;; C; D;; ...]</code> but with more than two consecutive <code>;</code> also correspond to <code>hvncat</code> calls.</p><h2 id="man-anonymous-functions"><a class="docs-heading-anchor" href="#man-anonymous-functions">Anonymous Functions</a><a id="man-anonymous-functions-1"></a><a class="docs-heading-anchor-permalink" href="#man-anonymous-functions" title="Permalink"></a></h2><p>Functions in Julia are <a href="https://en.wikipedia.org/wiki/First-class_citizen">first-class objects</a>: they can be assigned to variables, and called using the standard function call syntax from the variable they have been assigned to. They can be used as arguments, and they can be returned as values. They can also be created anonymously, without being given a name, using either of these syntaxes:</p><pre><code class="language-julia-repl hljs">julia&gt; x -&gt; x^2 + 2x - 1
#1 (generic function with 1 method)

julia&gt; function (x)
           x^2 + 2x - 1
       end
#3 (generic function with 1 method)</code></pre><p>Each statement creates a function taking one argument <code>x</code> and returning the value of the polynomial <code>x^2 + 2x - 1</code> at that value. Notice that the result is a generic function, but with a compiler-generated name based on consecutive numbering.</p><p>The primary use for anonymous functions is passing them to functions which take other functions as arguments. A classic example is <a href="../base/collections.html#Base.map"><code>map</code></a>, which applies a function to each value of an array and returns a new array containing the resulting values:</p><pre><code class="language-julia-repl hljs">julia&gt; map(round, [1.2, 3.5, 1.7])
3-element Vector{Float64}:
 1.0
 4.0
 2.0</code></pre><p>This is fine if a named function effecting the transform already exists to pass as the first argument to <a href="../base/collections.html#Base.map"><code>map</code></a>. Often, however, a ready-to-use, named function does not exist. In these situations, the anonymous function construct allows easy creation of a single-use function object without needing a name:</p><pre><code class="language-julia-repl hljs">julia&gt; map(x -&gt; x^2 + 2x - 1, [1, 3, -1])
3-element Vector{Int64}:
  2
 14
 -2</code></pre><p>An anonymous function accepting multiple arguments can be written using the syntax <code>(x,y,z)-&gt;2x+y-z</code>.</p><p>Argument-type declarations for anonymous functions work as for named functions, for example <code>x::Integer-&gt;2x</code>. The return type of an anonymous function cannot be specified.</p><p>A zero-argument anonymous function can be written as <code>()-&gt;2+2</code>. The idea of a function with no arguments may seem strange, but is useful in cases where a result cannot (or should not) be precomputed. For example, Julia has a zero-argument <a href="../base/base.html#Base.Libc.time-Tuple{}"><code>time</code></a> function that returns the current time in seconds, and thus <code>seconds = ()-&gt;round(Int, time())</code> is an anonymous function that returns this time rounded to the nearest integer assigned to the variable <code>seconds</code>. Each time this anonymous function is called as <code>seconds()</code> the current time will be calculated and returned.</p><h2 id="Tuples"><a class="docs-heading-anchor" href="#Tuples">Tuples</a><a id="Tuples-1"></a><a class="docs-heading-anchor-permalink" href="#Tuples" title="Permalink"></a></h2><p>Julia has a built-in data structure called a <em>tuple</em> that is closely related to function arguments and return values. A tuple is a fixed-length container that can hold any values, but cannot be modified (it is <em>immutable</em>). Tuples are constructed with commas and parentheses, and can be accessed via indexing:</p><pre><code class="language-julia-repl hljs">julia&gt; (1, 1+1)
(1, 2)

julia&gt; (1,)
(1,)

julia&gt; x = (0.0, &quot;hello&quot;, 6*7)
(0.0, &quot;hello&quot;, 42)

julia&gt; x[2]
&quot;hello&quot;</code></pre><p>Notice that a length-1 tuple must be written with a comma, <code>(1,)</code>, since <code>(1)</code> would just be a parenthesized value. <code>()</code> represents the empty (length-0) tuple.</p><h2 id="Named-Tuples"><a class="docs-heading-anchor" href="#Named-Tuples">Named Tuples</a><a id="Named-Tuples-1"></a><a class="docs-heading-anchor-permalink" href="#Named-Tuples" title="Permalink"></a></h2><p>The components of tuples can optionally be named, in which case a <em>named tuple</em> is constructed:</p><pre><code class="language-julia-repl hljs">julia&gt; x = (a=2, b=1+2)
(a = 2, b = 3)

julia&gt; x[1]
2

julia&gt; x.a
2</code></pre><p>The fields of named tuples can be accessed by name using dot syntax (<code>x.a</code>) in addition to the regular indexing syntax (<code>x[1]</code> or <code>x[:a]</code>).</p><h2 id="destructuring-assignment"><a class="docs-heading-anchor" href="#destructuring-assignment">Destructuring Assignment and Multiple Return Values</a><a id="destructuring-assignment-1"></a><a class="docs-heading-anchor-permalink" href="#destructuring-assignment" title="Permalink"></a></h2><p>A comma-separated list of variables (optionally wrapped in parentheses) can appear on the left side of an assignment: the value on the right side is <em>destructured</em> by iterating over and assigning to each variable in turn:</p><pre><code class="language-julia-repl hljs">julia&gt; (a, b, c) = 1:3
1:3

julia&gt; b
2</code></pre><p>The value on the right should be an iterator (see <a href="interfaces.html#man-interface-iteration">Iteration interface</a>) at least as long as the number of variables on the left (any excess elements of the iterator are ignored).</p><p>This can be used to return multiple values from functions by returning a tuple or other iterable value. For example, the following function returns two values:</p><pre><code class="language-julia-repl hljs">julia&gt; function foo(a, b)
           a+b, a*b
       end
foo (generic function with 1 method)</code></pre><p>If you call it in an interactive session without assigning the return value anywhere, you will see the tuple returned:</p><pre><code class="language-julia-repl hljs">julia&gt; foo(2, 3)
(5, 6)</code></pre><p>Destructuring assignment extracts each value into a variable:</p><pre><code class="language-julia-repl hljs">julia&gt; x, y = foo(2, 3)
(5, 6)

julia&gt; x
5

julia&gt; y
6</code></pre><p>Another common use is for swapping variables:</p><pre><code class="language-julia-repl hljs">julia&gt; y, x = x, y
(5, 6)

julia&gt; x
6

julia&gt; y
5</code></pre><p>If only a subset of the elements of the iterator are required, a common convention is to assign ignored elements to a variable consisting of only underscores <code>_</code> (which is an otherwise invalid variable name, see <a href="variables.html#man-allowed-variable-names">Allowed Variable Names</a>):</p><pre><code class="language-julia-repl hljs">julia&gt; _, _, _, d = 1:10
1:10

julia&gt; d
4</code></pre><p>Other valid left-hand side expressions can be used as elements of the assignment list, which will call <a href="../base/collections.html#Base.setindex!"><code>setindex!</code></a> or <a href="../base/base.html#Base.setproperty!"><code>setproperty!</code></a>, or recursively destructure individual elements of the iterator:</p><pre><code class="language-julia-repl hljs">julia&gt; X = zeros(3);

julia&gt; X[1], (a, b) = (1, (2, 3))
(1, (2, 3))

julia&gt; X
3-element Vector{Float64}:
 1.0
 0.0
 0.0

julia&gt; a
2

julia&gt; b
3</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p><code>...</code> with assignment requires Julia 1.6</p></div></div><p>If the last symbol in the assignment list is suffixed by <code>...</code> (known as <em>slurping</em>), then it will be assigned a collection or lazy iterator of the remaining elements of the right-hand side iterator:</p><pre><code class="language-julia-repl hljs">julia&gt; a, b... = &quot;hello&quot;
&quot;hello&quot;

julia&gt; a
&#39;h&#39;: ASCII/Unicode U+0068 (category Ll: Letter, lowercase)

julia&gt; b
&quot;ello&quot;

julia&gt; a, b... = Iterators.map(abs2, 1:4)
Base.Generator{UnitRange{Int64}, typeof(abs2)}(abs2, 1:4)

julia&gt; a
1

julia&gt; b
Base.Iterators.Rest{Base.Generator{UnitRange{Int64}, typeof(abs2)}, Int64}(Base.Generator{UnitRange{Int64}, typeof(abs2)}(abs2, 1:4), 1)</code></pre><p>See <a href="../base/collections.html#Base.rest"><code>Base.rest</code></a> for details on the precise handling and customization for specific iterators.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p><code>...</code> in non-final position of an assignment requires Julia 1.9</p></div></div><p>Slurping in assignments can also occur in any other position. As opposed to slurping the end of a collection however, this will always be eager.</p><pre><code class="language-julia-repl hljs">julia&gt; a, b..., c = 1:5
1:5

julia&gt; a
1

julia&gt; b
3-element Vector{Int64}:
 2
 3
 4

julia&gt; c
5

julia&gt; front..., tail = &quot;Hi!&quot;
&quot;Hi!&quot;

julia&gt; front
&quot;Hi&quot;

julia&gt; tail
&#39;!&#39;: ASCII/Unicode U+0021 (category Po: Punctuation, other)</code></pre><p>This is implemented in terms of the function <a href="../base/collections.html#Base.split_rest"><code>Base.split_rest</code></a>.</p><p>Note that for variadic function definitions, slurping is still only allowed in final position. This does not apply to <a href="functions.html#man-argument-destructuring">single argument destructuring</a> though, as that does not affect method dispatch:</p><pre><code class="language-julia-repl hljs">julia&gt; f(x..., y) = x
ERROR: syntax: invalid &quot;...&quot; on non-final argument
Stacktrace:
[...]

julia&gt; f((x..., y)) = x
f (generic function with 1 method)

julia&gt; f((1, 2, 3))
(1, 2)</code></pre><h2 id="Property-destructuring"><a class="docs-heading-anchor" href="#Property-destructuring">Property destructuring</a><a id="Property-destructuring-1"></a><a class="docs-heading-anchor-permalink" href="#Property-destructuring" title="Permalink"></a></h2><p>Instead of destructuring based on iteration, the right side of assignments can also be destructured using property names. This follows the syntax for NamedTuples, and works by assigning to each variable on the left a property of the right side of the assignment with the same name using <code>getproperty</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; (; b, a) = (a=1, b=2, c=3)
(a = 1, b = 2, c = 3)

julia&gt; a
1

julia&gt; b
2</code></pre><h2 id="man-argument-destructuring"><a class="docs-heading-anchor" href="#man-argument-destructuring">Argument destructuring</a><a id="man-argument-destructuring-1"></a><a class="docs-heading-anchor-permalink" href="#man-argument-destructuring" title="Permalink"></a></h2><p>The destructuring feature can also be used within a function argument. If a function argument name is written as a tuple (e.g. <code>(x, y)</code>) instead of just a symbol, then an assignment <code>(x, y) = argument</code> will be inserted for you:</p><pre><code class="language-julia-repl hljs">julia&gt; minmax(x, y) = (y &lt; x) ? (y, x) : (x, y)

julia&gt; gap((min, max)) = max - min

julia&gt; gap(minmax(10, 2))
8</code></pre><p>Notice the extra set of parentheses in the definition of <code>gap</code>. Without those, <code>gap</code> would be a two-argument function, and this example would not work.</p><p>Similarly, property destructuring can also be used for function arguments:</p><pre><code class="language-julia-repl hljs">julia&gt; foo((; x, y)) = x + y
foo (generic function with 1 method)

julia&gt; foo((x=1, y=2))
3

julia&gt; struct A
           x
           y
       end

julia&gt; foo(A(3, 4))
7</code></pre><p>For anonymous functions, destructuring a single argument requires an extra comma:</p><pre><code class="nohighlight hljs">julia&gt; map(((x, y),) -&gt; x + y, [(1, 2), (3, 4)])
2-element Array{Int64,1}:
 3
 7</code></pre><h2 id="Varargs-Functions"><a class="docs-heading-anchor" href="#Varargs-Functions">Varargs Functions</a><a id="Varargs-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Varargs-Functions" title="Permalink"></a></h2><p>It is often convenient to be able to write functions taking an arbitrary number of arguments. Such functions are traditionally known as &quot;varargs&quot; functions, which is short for &quot;variable number of arguments&quot;. You can define a varargs function by following the last positional argument with an ellipsis:</p><pre><code class="language-julia-repl hljs">julia&gt; bar(a, b, x...) = (a, b, x)
bar (generic function with 1 method)</code></pre><p>The variables <code>a</code> and <code>b</code> are bound to the first two argument values as usual, and the variable <code>x</code> is bound to an iterable collection of the zero or more values passed to <code>bar</code> after its first two arguments:</p><pre><code class="language-julia-repl hljs">julia&gt; bar(1, 2)
(1, 2, ())

julia&gt; bar(1, 2, 3)
(1, 2, (3,))

julia&gt; bar(1, 2, 3, 4)
(1, 2, (3, 4))

julia&gt; bar(1, 2, 3, 4, 5, 6)
(1, 2, (3, 4, 5, 6))</code></pre><p>In all these cases, <code>x</code> is bound to a tuple of the trailing values passed to <code>bar</code>.</p><p>It is possible to constrain the number of values passed as a variable argument; this will be discussed later in <a href="methods.html#Parametrically-constrained-Varargs-methods">Parametrically-constrained Varargs methods</a>.</p><p>On the flip side, it is often handy to &quot;splat&quot; the values contained in an iterable collection into a function call as individual arguments. To do this, one also uses <code>...</code> but in the function call instead:</p><pre><code class="language-julia-repl hljs">julia&gt; x = (3, 4)
(3, 4)

julia&gt; bar(1, 2, x...)
(1, 2, (3, 4))</code></pre><p>In this case a tuple of values is spliced into a varargs call precisely where the variable number of arguments go. This need not be the case, however:</p><pre><code class="language-julia-repl hljs">julia&gt; x = (2, 3, 4)
(2, 3, 4)

julia&gt; bar(1, x...)
(1, 2, (3, 4))

julia&gt; x = (1, 2, 3, 4)
(1, 2, 3, 4)

julia&gt; bar(x...)
(1, 2, (3, 4))</code></pre><p>Furthermore, the iterable object splatted into a function call need not be a tuple:</p><pre><code class="language-julia-repl hljs">julia&gt; x = [3, 4]
2-element Vector{Int64}:
 3
 4

julia&gt; bar(1, 2, x...)
(1, 2, (3, 4))

julia&gt; x = [1, 2, 3, 4]
4-element Vector{Int64}:
 1
 2
 3
 4

julia&gt; bar(x...)
(1, 2, (3, 4))</code></pre><p>Also, the function that arguments are splatted into need not be a varargs function (although it often is):</p><pre><code class="language-julia-repl hljs">julia&gt; baz(a, b) = a + b;

julia&gt; args = [1, 2]
2-element Vector{Int64}:
 1
 2

julia&gt; baz(args...)
3

julia&gt; args = [1, 2, 3]
3-element Vector{Int64}:
 1
 2
 3

julia&gt; baz(args...)
ERROR: MethodError: no method matching baz(::Int64, ::Int64, ::Int64)
The function `baz` exists, but no method is defined for this combination of argument types.

Closest candidates are:
  baz(::Any, ::Any)
   @ Main none:1

Stacktrace:
[...]</code></pre><p>As you can see, if the wrong number of elements are in the splatted container, then the function call will fail, just as it would if too many arguments were given explicitly.</p><h2 id="Optional-Arguments"><a class="docs-heading-anchor" href="#Optional-Arguments">Optional Arguments</a><a id="Optional-Arguments-1"></a><a class="docs-heading-anchor-permalink" href="#Optional-Arguments" title="Permalink"></a></h2><p>It is often possible to provide sensible default values for function arguments. This can save users from having to pass every argument on every call. For example, the function <a href="../stdlib/Dates.html#Dates.Date"><code>Date(y, [m, d])</code></a> from <code>Dates</code> module constructs a <code>Date</code> type for a given year <code>y</code>, month <code>m</code> and day <code>d</code>. However, <code>m</code> and <code>d</code> arguments are optional and their default value is <code>1</code>. This behavior can be expressed concisely as:</p><pre><code class="language-julia-repl hljs">julia&gt; using Dates

julia&gt; function date(y::Int64, m::Int64=1, d::Int64=1)
           err = Dates.validargs(Date, y, m, d)
           err === nothing || throw(err)
           return Date(Dates.UTD(Dates.totaldays(y, m, d)))
       end
date (generic function with 3 methods)</code></pre><p>Observe, that this definition calls another method of the <code>Date</code> function that takes one argument of type <code>UTInstant{Day}</code>.</p><p>With this definition, the function can be called with either one, two or three arguments, and <code>1</code> is automatically passed when only one or two of the arguments are specified:</p><pre><code class="language-julia-repl hljs">julia&gt; date(2000, 12, 12)
2000-12-12

julia&gt; date(2000, 12)
2000-12-01

julia&gt; date(2000)
2000-01-01</code></pre><p>Optional arguments are actually just a convenient syntax for writing multiple method definitions with different numbers of arguments (see <a href="methods.html#Note-on-Optional-and-keyword-Arguments">Note on Optional and keyword Arguments</a>). This can be checked for our <code>date</code> function example by calling the <code>methods</code> function:</p><pre><code class="language-julia-repl hljs">julia&gt; methods(date)
# 3 methods for generic function &quot;date&quot;:
[1] date(y::Int64) in Main at REPL[1]:1
[2] date(y::Int64, m::Int64) in Main at REPL[1]:1
[3] date(y::Int64, m::Int64, d::Int64) in Main at REPL[1]:1</code></pre><h2 id="Keyword-Arguments"><a class="docs-heading-anchor" href="#Keyword-Arguments">Keyword Arguments</a><a id="Keyword-Arguments-1"></a><a class="docs-heading-anchor-permalink" href="#Keyword-Arguments" title="Permalink"></a></h2><p>Some functions need a large number of arguments, or have a large number of behaviors. Remembering how to call such functions can be difficult. Keyword arguments can make these complex interfaces easier to use and extend by allowing arguments to be identified by name instead of only by position.</p><p>For example, consider a function <code>plot</code> that plots a line. This function might have many options, for controlling line style, width, color, and so on. If it accepts keyword arguments, a possible call might look like <code>plot(x, y, width=2)</code>, where we have chosen to specify only line width. Notice that this serves two purposes. The call is easier to read, since we can label an argument with its meaning. It also becomes possible to pass any subset of a large number of arguments, in any order.</p><p>Functions with keyword arguments are defined using a semicolon in the signature:</p><pre><code class="language-julia hljs">function plot(x, y; style=&quot;solid&quot;, width=1, color=&quot;black&quot;)
    ###
end</code></pre><p>When the function is called, the semicolon is optional: one can either call <code>plot(x, y, width=2)</code> or <code>plot(x, y; width=2)</code>, but the former style is more common. An explicit semicolon is required only for passing varargs or computed keywords as described below.</p><p>Keyword argument default values are evaluated only when necessary (when a corresponding keyword argument is not passed), and in left-to-right order. Therefore default expressions may refer to prior keyword arguments.</p><p>The types of keyword arguments can be made explicit as follows:</p><pre><code class="language-julia hljs">function f(; x::Int=1)
    ###
end</code></pre><p>Keyword arguments can also be used in varargs functions:</p><pre><code class="language-julia hljs">function plot(x...; style=&quot;solid&quot;)
    ###
end</code></pre><p>Extra keyword arguments can be collected using <code>...</code>, as in varargs functions:</p><pre><code class="language-julia hljs">function f(x; y=0, kwargs...)
    ###
end</code></pre><p>Inside <code>f</code>, <code>kwargs</code> will be an immutable key-value iterator over a named tuple. Named tuples (as well as dictionaries with keys of <code>Symbol</code>, and other iterators yielding two-value collections with symbol as first values) can be passed as keyword arguments using a semicolon in a call, e.g. <code>f(x, z=1; kwargs...)</code>.</p><p>If a keyword argument is not assigned a default value in the method definition, then it is <em>required</em>: an <a href="../base/base.html#Core.UndefKeywordError"><code>UndefKeywordError</code></a> exception will be thrown if the caller does not assign it a value:</p><pre><code class="language-julia hljs">function f(x; y)
    ###
end
f(3, y=5) # ok, y is assigned
f(3)      # throws UndefKeywordError(:y)</code></pre><p>One can also pass <code>key =&gt; value</code> expressions after a semicolon. For example, <code>plot(x, y; :width =&gt; 2)</code> is equivalent to <code>plot(x, y, width=2)</code>. This is useful in situations where the keyword name is computed at runtime.</p><p>When a bare identifier or dot expression occurs after a semicolon, the keyword argument name is implied by the identifier or field name. For example <code>plot(x, y; width)</code> is equivalent to <code>plot(x, y; width=width)</code> and <code>plot(x, y; options.width)</code> is equivalent to <code>plot(x, y; width=options.width)</code>.</p><p>The nature of keyword arguments makes it possible to specify the same argument more than once. For example, in the call <code>plot(x, y; options..., width=2)</code> it is possible that the <code>options</code> structure also contains a value for <code>width</code>. In such a case the rightmost occurrence takes precedence; in this example, <code>width</code> is certain to have the value <code>2</code>. However, explicitly specifying the same keyword argument multiple times, for example <code>plot(x, y, width=2, width=3)</code>, is not allowed and results in a syntax error.</p><h2 id="Evaluation-Scope-of-Default-Values"><a class="docs-heading-anchor" href="#Evaluation-Scope-of-Default-Values">Evaluation Scope of Default Values</a><a id="Evaluation-Scope-of-Default-Values-1"></a><a class="docs-heading-anchor-permalink" href="#Evaluation-Scope-of-Default-Values" title="Permalink"></a></h2><p>When optional and keyword argument default expressions are evaluated, only <em>previous</em> arguments are in scope. For example, given this definition:</p><pre><code class="language-julia hljs">function f(x, a=b, b=1)
    ###
end</code></pre><p>the <code>b</code> in <code>a=b</code> refers to a <code>b</code> in an outer scope, not the subsequent argument <code>b</code>.</p><h2 id="Do-Block-Syntax-for-Function-Arguments"><a class="docs-heading-anchor" href="#Do-Block-Syntax-for-Function-Arguments">Do-Block Syntax for Function Arguments</a><a id="Do-Block-Syntax-for-Function-Arguments-1"></a><a class="docs-heading-anchor-permalink" href="#Do-Block-Syntax-for-Function-Arguments" title="Permalink"></a></h2><p>Passing functions as arguments to other functions is a powerful technique, but the syntax for it is not always convenient. Such calls are especially awkward to write when the function argument requires multiple lines. As an example, consider calling <a href="../base/collections.html#Base.map"><code>map</code></a> on a function with several cases:</p><pre><code class="language-julia hljs">map(x-&gt;begin
           if x &lt; 0 &amp;&amp; iseven(x)
               return 0
           elseif x == 0
               return 1
           else
               return x
           end
       end,
    [A, B, C])</code></pre><p>Julia provides a reserved word <code>do</code> for rewriting this code more clearly:</p><pre><code class="language-julia hljs">map([A, B, C]) do x
    if x &lt; 0 &amp;&amp; iseven(x)
        return 0
    elseif x == 0
        return 1
    else
        return x
    end
end</code></pre><p>The <code>do x</code> syntax creates an anonymous function with argument <code>x</code> and passes the anonymous function as the first argument to the &quot;outer&quot; function - <a href="../base/collections.html#Base.map"><code>map</code></a> in this example. Similarly, <code>do a,b</code> would create a two-argument anonymous function. Note that <code>do (a,b)</code> would create a one-argument anonymous function, whose argument is a tuple to be deconstructed. A plain <code>do</code> would declare that what follows is an anonymous function of the form <code>() -&gt; ...</code>.</p><p>How these arguments are initialized depends on the &quot;outer&quot; function; here, <a href="../base/collections.html#Base.map"><code>map</code></a> will sequentially set <code>x</code> to <code>A</code>, <code>B</code>, <code>C</code>, calling the anonymous function on each, just as would happen in the syntax <code>map(func, [A, B, C])</code>.</p><p>This syntax makes it easier to use functions to effectively extend the language, since calls look like normal code blocks. There are many possible uses quite different from <a href="../base/collections.html#Base.map"><code>map</code></a>, such as managing system state. For example, there is a version of <a href="../base/io-network.html#Base.open"><code>open</code></a> that runs code ensuring that the opened file is eventually closed:</p><pre><code class="language-julia hljs">open(&quot;outfile&quot;, &quot;w&quot;) do io
    write(io, data)
end</code></pre><p>This is accomplished by the following definition:</p><pre><code class="language-julia hljs">function open(f::Function, args...)
    io = open(args...)
    try
        f(io)
    finally
        close(io)
    end
end</code></pre><p>Here, <a href="../base/io-network.html#Base.open"><code>open</code></a> first opens the file for writing and then passes the resulting output stream to the anonymous function you defined in the <code>do ... end</code> block. After your function exits, <a href="../base/io-network.html#Base.open"><code>open</code></a> will make sure that the stream is properly closed, regardless of whether your function exited normally or threw an exception. (The <code>try/finally</code> construct will be described in <a href="control-flow.html#Control-Flow">Control Flow</a>.)</p><p>With the <code>do</code> block syntax, it helps to check the documentation or implementation to know how the arguments of the user function are initialized.</p><p>A <code>do</code> block, like any other inner function, can &quot;capture&quot; variables from its enclosing scope. For example, the variable <code>data</code> in the above example of <code>open...do</code> is captured from the outer scope. Captured variables can create performance challenges as discussed in <a href="performance-tips.html#man-performance-captured">performance tips</a>.</p><h2 id="Function-composition-and-piping"><a class="docs-heading-anchor" href="#Function-composition-and-piping">Function composition and piping</a><a id="Function-composition-and-piping-1"></a><a class="docs-heading-anchor-permalink" href="#Function-composition-and-piping" title="Permalink"></a></h2><p>Functions in Julia can be combined by composing or piping (chaining) them together.</p><p>Function composition is when you combine functions together and apply the resulting composition to arguments. You use the function composition operator (<code>∘</code>) to compose the functions, so <code>(f ∘ g)(args...; kw...)</code> is the same as <code>f(g(args...; kw...))</code>.</p><p>You can type the composition operator at the REPL and suitably-configured editors using <code>\circ&lt;tab&gt;</code>.</p><p>For example, the <code>sqrt</code> and <code>+</code> functions can be composed like this:</p><pre><code class="language-julia-repl hljs">julia&gt; (sqrt ∘ +)(3, 6)
3.0</code></pre><p>This adds the numbers first, then finds the square root of the result.</p><p>The next example composes three functions and maps the result over an array of strings:</p><pre><code class="language-julia-repl hljs">julia&gt; map(first ∘ reverse ∘ uppercase, split(&quot;you can compose functions like this&quot;))
6-element Vector{Char}:
 &#39;U&#39;: ASCII/Unicode U+0055 (category Lu: Letter, uppercase)
 &#39;N&#39;: ASCII/Unicode U+004E (category Lu: Letter, uppercase)
 &#39;E&#39;: ASCII/Unicode U+0045 (category Lu: Letter, uppercase)
 &#39;S&#39;: ASCII/Unicode U+0053 (category Lu: Letter, uppercase)
 &#39;E&#39;: ASCII/Unicode U+0045 (category Lu: Letter, uppercase)
 &#39;S&#39;: ASCII/Unicode U+0053 (category Lu: Letter, uppercase)</code></pre><p>Function chaining (sometimes called &quot;piping&quot; or &quot;using a pipe&quot; to send data to a subsequent function) is when you apply a function to the previous function&#39;s output:</p><pre><code class="language-julia-repl hljs">julia&gt; 1:10 |&gt; sum |&gt; sqrt
7.416198487095663</code></pre><p>Here, the total produced by <code>sum</code> is passed to the <code>sqrt</code> function. The equivalent composition would be:</p><pre><code class="language-julia-repl hljs">julia&gt; (sqrt ∘ sum)(1:10)
7.416198487095663</code></pre><p>The pipe operator can also be used with broadcasting, as <code>.|&gt;</code>, to provide a useful combination of the chaining/piping and dot vectorization syntax (described below).</p><pre><code class="language-julia-repl hljs">julia&gt; [&quot;a&quot;, &quot;list&quot;, &quot;of&quot;, &quot;strings&quot;] .|&gt; [uppercase, reverse, titlecase, length]
4-element Vector{Any}:
  &quot;A&quot;
  &quot;tsil&quot;
  &quot;Of&quot;
 7</code></pre><p>When combining pipes with anonymous functions, parentheses must be used if subsequent pipes are not to be parsed as part of the anonymous function&#39;s body. Compare:</p><pre><code class="language-julia-repl hljs">julia&gt; 1:3 .|&gt; (x -&gt; x^2) |&gt; sum |&gt; sqrt
3.7416573867739413

julia&gt; 1:3 .|&gt; x -&gt; x^2 |&gt; sum |&gt; sqrt
3-element Vector{Float64}:
 1.0
 2.0
 3.0</code></pre><h2 id="man-vectorized"><a class="docs-heading-anchor" href="#man-vectorized">Dot Syntax for Vectorizing Functions</a><a id="man-vectorized-1"></a><a class="docs-heading-anchor-permalink" href="#man-vectorized" title="Permalink"></a></h2><p>In technical-computing languages, it is common to have &quot;vectorized&quot; versions of functions, which simply apply a given function <code>f(x)</code> to each element of an array <code>A</code> to yield a new array via <code>f(A)</code>. This kind of syntax is convenient for data processing, but in other languages vectorization is also often required for performance: if loops are slow, the &quot;vectorized&quot; version of a function can call fast library code written in a low-level language. In Julia, vectorized functions are <em>not</em> required for performance, and indeed it is often beneficial to write your own loops (see <a href="performance-tips.html#man-performance-tips">Performance Tips</a>), but they can still be convenient. Therefore, <em>any</em> Julia function <code>f</code> can be applied elementwise to any array (or other collection) with the syntax <code>f.(A)</code>. For example, <code>sin</code> can be applied to all elements in the vector <code>A</code> like so:</p><pre><code class="language-julia-repl hljs">julia&gt; A = [1.0, 2.0, 3.0]
3-element Vector{Float64}:
 1.0
 2.0
 3.0

julia&gt; sin.(A)
3-element Vector{Float64}:
 0.8414709848078965
 0.9092974268256817
 0.1411200080598672</code></pre><p>Of course, you can omit the dot if you write a specialized &quot;vector&quot; method of <code>f</code>, e.g. via <code>f(A::AbstractArray) = map(f, A)</code>, and this is just as efficient as <code>f.(A)</code>. The advantage of the <code>f.(A)</code> syntax is that which functions are vectorizable need not be decided upon in advance by the library writer.</p><p>More generally, <code>f.(args...)</code> is actually equivalent to <code>broadcast(f, args...)</code>, which allows you to operate on multiple arrays (even of different shapes), or a mix of arrays and scalars (see <a href="arrays.html#Broadcasting">Broadcasting</a>). For example, if you have <code>f(x, y) = 3x + 4y</code>, then <code>f.(pi, A)</code> will return a new array consisting of <code>f(pi,a)</code> for each <code>a</code> in <code>A</code>, and <code>f.(vector1, vector2)</code> will return a new vector consisting of <code>f(vector1[i], vector2[i])</code> for each index <code>i</code> (throwing an exception if the vectors have different length).</p><pre><code class="language-julia-repl hljs">julia&gt; f(x, y) = 3x + 4y;

julia&gt; A = [1.0, 2.0, 3.0];

julia&gt; B = [4.0, 5.0, 6.0];

julia&gt; f.(pi, A)
3-element Vector{Float64}:
 13.42477796076938
 17.42477796076938
 21.42477796076938

julia&gt; f.(A, B)
3-element Vector{Float64}:
 19.0
 26.0
 33.0</code></pre><p>Keyword arguments are not broadcasted over, but are simply passed through to each call of the function.  For example, <code>round.(x, digits=3)</code> is equivalent to <code>broadcast(x -&gt; round(x, digits=3), x)</code>.</p><p>Moreover, <em>nested</em> <code>f.(args...)</code> calls are <em>fused</em> into a single <code>broadcast</code> loop. For example, <code>sin.(cos.(X))</code> is equivalent to <code>broadcast(x -&gt; sin(cos(x)), X)</code>, similar to <code>[sin(cos(x)) for x in X]</code>: there is only a single loop over <code>X</code>, and a single array is allocated for the result. [In contrast, <code>sin(cos(X))</code> in a typical &quot;vectorized&quot; language would first allocate one temporary array for <code>tmp=cos(X)</code>, and then compute <code>sin(tmp)</code> in a separate loop, allocating a second array.] This loop fusion is not a compiler optimization that may or may not occur, it is a <em>syntactic guarantee</em> whenever nested <code>f.(args...)</code> calls are encountered. Technically, the fusion stops as soon as a &quot;non-dot&quot; function call is encountered; for example, in <code>sin.(sort(cos.(X)))</code> the <code>sin</code> and <code>cos</code> loops cannot be merged because of the intervening <code>sort</code> function.</p><p>Finally, the maximum efficiency is typically achieved when the output array of a vectorized operation is <em>pre-allocated</em>, so that repeated calls do not allocate new arrays over and over again for the results (see <a href="performance-tips.html#Pre-allocating-outputs">Pre-allocating outputs</a>). A convenient syntax for this is <code>X .= ...</code>, which is equivalent to <code>broadcast!(identity, X, ...)</code> except that, as above, the <code>broadcast!</code> loop is fused with any nested &quot;dot&quot; calls. For example, <code>X .= sin.(Y)</code> is equivalent to <code>broadcast!(sin, X, Y)</code>, overwriting <code>X</code> with <code>sin.(Y)</code> in-place. If the left-hand side is an array-indexing expression, e.g. <code>X[begin+1:end] .= sin.(Y)</code>, then it translates to <code>broadcast!</code> on a <code>view</code>, e.g. <code>broadcast!(sin, view(X, firstindex(X)+1:lastindex(X)), Y)</code>, so that the left-hand side is updated in-place.</p><p>Since adding dots to many operations and function calls in an expression can be tedious and lead to code that is difficult to read, the macro <a href="../base/arrays.html#Base.Broadcast.@__dot__"><code>@.</code></a> is provided to convert <em>every</em> function call, operation, and assignment in an expression into the &quot;dotted&quot; version.</p><pre><code class="language-julia-repl hljs">julia&gt; Y = [1.0, 2.0, 3.0, 4.0];

julia&gt; X = similar(Y); # pre-allocate output array

julia&gt; @. X = sin(cos(Y)) # equivalent to X .= sin.(cos.(Y))
4-element Vector{Float64}:
  0.5143952585235492
 -0.4042391538522658
 -0.8360218615377305
 -0.6080830096407656</code></pre><p>Binary (or unary) operators like <code>.+</code> are handled with the same mechanism: they are equivalent to <code>broadcast</code> calls and are fused with other nested &quot;dot&quot; calls.  <code>X .+= Y</code> etcetera is equivalent to <code>X .= X .+ Y</code> and results in a fused in-place assignment;  see also <a href="mathematical-operations.html#man-dot-operators">dot operators</a>.</p><p>You can also combine dot operations with function chaining using <a href="../base/base.html#Base.:|&gt;"><code>|&gt;</code></a>, as in this example:</p><pre><code class="language-julia-repl hljs">julia&gt; 1:5 .|&gt; [x-&gt;x^2, inv, x-&gt;2*x, -, isodd]
5-element Vector{Real}:
    1
    0.5
    6
   -4
 true</code></pre><p>All functions in the fused broadcast are always called for every element of the result. Thus <code>X .+ σ .* randn.()</code> will add a mask of independent and identically sampled random values to each element of the array <code>X</code>, but <code>X .+ σ .* randn()</code> will add the <em>same</em> random sample to each element. In cases where the fused computation is constant along one or more axes of the broadcast iteration, it may be possible to leverage a space-time tradeoff and allocate intermediate values to reduce the number of computations. See more at <a href="performance-tips.html#man-performance-unfuse">performance tips</a>.</p><h2 id="Further-Reading"><a class="docs-heading-anchor" href="#Further-Reading">Further Reading</a><a id="Further-Reading-1"></a><a class="docs-heading-anchor-permalink" href="#Further-Reading" title="Permalink"></a></h2><p>We should mention here that this is far from a complete picture of defining functions. Julia has a sophisticated type system and allows multiple dispatch on argument types. None of the examples given here provide any type annotations on their arguments, meaning that they are applicable to all types of arguments. The type system is described in <a href="types.html#man-types">Types</a> and defining a function in terms of methods chosen by multiple dispatch on run-time argument types is described in <a href="methods.html#Methods">Methods</a>.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="strings.html">« Strings</a><a class="docs-footer-nextpage" href="control-flow.html">Control Flow »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
