<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Mathematics · The Julia Language</title><meta name="title" content="Mathematics · The Julia Language"/><meta property="og:title" content="Mathematics · The Julia Language"/><meta property="twitter:title" content="Mathematics · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox" checked/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="base.html">Essentials</a></li><li><a class="tocitem" href="collections.html">Collections and Data Structures</a></li><li class="is-active"><a class="tocitem" href="math.html">Mathematics</a><ul class="internal"><li><a class="tocitem" href="#math-ops"><span>Mathematical Operators</span></a></li><li><a class="tocitem" href="#Mathematical-Functions"><span>Mathematical Functions</span></a></li><li><a class="tocitem" href="#Customizable-binary-operators"><span>Customizable binary operators</span></a></li></ul></li><li><a class="tocitem" href="numbers.html">Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="arrays.html">Arrays</a></li><li><a class="tocitem" href="parallel.html">Tasks</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="constants.html">Constants</a></li><li><a class="tocitem" href="file.html">Filesystem</a></li><li><a class="tocitem" href="io-network.html">I/O and Network</a></li><li><a class="tocitem" href="punctuation.html">Punctuation</a></li><li><a class="tocitem" href="sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="c.html">C Interface</a></li><li><a class="tocitem" href="libc.html">C Standard Library</a></li><li><a class="tocitem" href="stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Base</a></li><li class="is-active"><a href="math.html">Mathematics</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="math.html">Mathematics</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/base/math.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Mathematics"><a class="docs-heading-anchor" href="#Mathematics">Mathematics</a><a id="Mathematics-1"></a><a class="docs-heading-anchor-permalink" href="#Mathematics" title="Permalink"></a></h1><h2 id="math-ops"><a class="docs-heading-anchor" href="#math-ops">Mathematical Operators</a><a id="math-ops-1"></a><a class="docs-heading-anchor-permalink" href="#math-ops" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:--Tuple{Any}" href="#Base.:--Tuple{Any}"><code>Base.:-</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">-(x)</code></pre><p>Unary minus operator.</p><p>See also: <a href="math.html#Base.abs"><code>abs</code></a>, <a href="math.html#Base.flipsign"><code>flipsign</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; -1
-1

julia&gt; -(2)
-2

julia&gt; -[1 2; 3 4]
2×2 Matrix{Int64}:
 -1  -2
 -3  -4

julia&gt; -(true)  # promotes to Int
-1

julia&gt; -(0x003)
0xfffd</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2978-L3004">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:+" href="#Base.:+"><code>Base.:+</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">dt::Date + t::Time -&gt; DateTime</code></pre><p>The addition of a <code>Date</code> with a <code>Time</code> produces a <code>DateTime</code>. The hour, minute, second, and millisecond parts of the <code>Time</code> are used along with the year, month, and day of the <code>Date</code> to create the new <code>DateTime</code>. Non-zero microseconds or nanoseconds in the <code>Time</code> type will result in an <code>InexactError</code> being thrown.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/arithmetic.jl#L14-L20">source</a></section><section><div><pre><code class="language-julia hljs">+(x, y...)</code></pre><p>Addition operator.</p><p>Infix <code>x+y+z+...</code> calls this function with all arguments, i.e. <code>+(x, y, z, ...)</code>, which by default then calls <code>(x+y) + z + ...</code> starting from the left.</p><p>Note that overflow is possible for most integer types, including the default <code>Int</code>, when adding large numbers.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; 1 + 20 + 4
25

julia&gt; +(1, 20, 4)
25

julia&gt; [1,2] + [3,4]
2-element Vector{Int64}:
 4
 6

julia&gt; typemax(Int) + 1 &lt; 0
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2948-L2975">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:--Tuple{Any, Any}" href="#Base.:--Tuple{Any, Any}"><code>Base.:-</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">-(x, y)</code></pre><p>Subtraction operator.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; 2 - 3
-1

julia&gt; -(2, 4.5)
-2.5</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L3007-L3020">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:*-Tuple{Any, Vararg{Any}}" href="#Base.:*-Tuple{Any, Vararg{Any}}"><code>Base.:*</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">*(x, y...)</code></pre><p>Multiplication operator.</p><p>Infix <code>x*y*z*...</code> calls this function with all arguments, i.e. <code>*(x, y, z, ...)</code>, which by default then calls <code>(x*y) * z * ...</code> starting from the left.</p><p>Juxtaposition such as <code>2pi</code> also calls <code>*(2, pi)</code>. Note that this operation has higher precedence than a literal <code>*</code>. Note also that juxtaposition &quot;0x...&quot; (integer zero times a variable whose name starts with <code>x</code>) is forbidden as it clashes with unsigned integer literals: <code>0x01 isa UInt8</code>.</p><p>Note that overflow is possible for most integer types, including the default <code>Int</code>, when multiplying large numbers.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; 2 * 7 * 8
112

julia&gt; *(2, 7, 8)
112

julia&gt; [2 0; 0 3] * [1, 10]  # matrix * vector
2-element Vector{Int64}:
  2
 30

julia&gt; 1/2pi, 1/2*pi  # juxtaposition has higher precedence
(0.15915494309189535, 1.5707963267948966)

julia&gt; x = [1, 2]; x&#39;x  # adjoint vector * vector
5</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L3023-L3058">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:/" href="#Base.:/"><code>Base.:/</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">/(x, y)</code></pre><p>Right division operator: multiplication of <code>x</code> by the inverse of <code>y</code> on the right.</p><p>Gives floating-point results for integer arguments. See <a href="math.html#Base.div"><code>÷</code></a> for integer division, or <a href="math.html#Base.://"><code>//</code></a> for <a href="numbers.html#Base.Rational"><code>Rational</code></a> results.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; 1/2
0.5

julia&gt; 4/2
2.0

julia&gt; 4.5/2
2.25</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L3061-L3080">source</a></section><section><div><pre><code class="language-julia hljs">A / B</code></pre><p>Matrix right-division: <code>A / B</code> is equivalent to <code>(B&#39; \ A&#39;)&#39;</code> where <a href="math.html#Base.:\\-Tuple{Any, Any}"><code>\</code></a> is the left-division operator. For square matrices, the result <code>X</code> is such that <code>A == X*B</code>.</p><p>See also: <a href="../stdlib/LinearAlgebra.html#LinearAlgebra.rdiv!"><code>rdiv!</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = Float64[1 4 5; 3 9 2]; B = Float64[1 4 2; 3 4 2; 8 7 1];

julia&gt; X = A / B
2×3 Matrix{Float64}:
 -0.65   3.75  -1.2
  3.25  -2.75   1.0

julia&gt; isapprox(A, X*B)
true

julia&gt; isapprox(X, A*pinv(B))
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LinearAlgebra/src/generic.jl#L1138-L1161">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:\\-Tuple{Any, Any}" href="#Base.:\\-Tuple{Any, Any}"><code>Base.:\</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">\(x, y)</code></pre><p>Left division operator: multiplication of <code>y</code> by the inverse of <code>x</code> on the left. Gives floating-point results for integer arguments.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; 3 \ 6
2.0

julia&gt; inv(3) * 6
2.0

julia&gt; A = [4 3; 2 1]; x = [5, 6];

julia&gt; A \ x
2-element Vector{Float64}:
  6.5
 -7.0

julia&gt; inv(A) * x
2-element Vector{Float64}:
  6.5
 -7.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L607-L633">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:^-Tuple{Number, Number}" href="#Base.:^-Tuple{Number, Number}"><code>Base.:^</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">^(x, y)</code></pre><p>Exponentiation operator.</p><p>If <code>x</code> and <code>y</code> are integers, the result may overflow. To enter numbers in scientific notation, use <a href="numbers.html#Core.Float64"><code>Float64</code></a> literals such as <code>1.2e3</code> rather than <code>1.2 * 10^3</code>.</p><p>If <code>y</code> is an <code>Int</code> literal (e.g. <code>2</code> in <code>x^2</code> or <code>-3</code> in <code>x^-3</code>), the Julia code <code>x^y</code> is transformed by the compiler to <code>Base.literal_pow(^, x, Val(y))</code>, to enable compile-time specialization on the value of the exponent. (As a default fallback we have <code>Base.literal_pow(^, x, Val(y)) = ^(x,y)</code>, where usually <code>^ == Base.^</code> unless <code>^</code> has been defined in the calling namespace.) If <code>y</code> is a negative integer literal, then <code>Base.literal_pow</code> transforms the operation to <code>inv(x)^-y</code> by default, where <code>-y</code> is positive.</p><p>See also <a href="math.html#Base.exp2"><code>exp2</code></a>, <a href="math.html#Base.:&lt;&lt;"><code>&lt;&lt;</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; 3^5
243

julia&gt; 3^-1  # uses Base.literal_pow
0.3333333333333333

julia&gt; p = -1;

julia&gt; 3^p
ERROR: DomainError with -1:
Cannot raise an integer x to a negative power -1.
[...]

julia&gt; 3.0^p
0.3333333333333333

julia&gt; 10^19 &gt; 0  # integer overflow
false

julia&gt; big(10)^19 == 1e19
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/promotion.jl#L434-L477">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.fma" href="#Base.fma"><code>Base.fma</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">fma(x, y, z)</code></pre><p>Computes <code>x*y+z</code> without rounding the intermediate result <code>x*y</code>. On some systems this is significantly more expensive than <code>x*y+z</code>. <code>fma</code> is used to improve accuracy in certain algorithms. See <a href="math.html#Base.muladd"><code>muladd</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/floatfuncs.jl#L271-L277">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.muladd" href="#Base.muladd"><code>Base.muladd</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">muladd(x, y, z)</code></pre><p>Combined multiply-add: computes <code>x*y+z</code>, but allowing the add and multiply to be merged with each other or with surrounding operations for performance. For example, this may be implemented as an <a href="math.html#Base.fma"><code>fma</code></a> if the hardware supports it efficiently. The result can be different on different machines and can also be different on the same machine due to constant propagation or other optimizations. See <a href="math.html#Base.fma"><code>fma</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; muladd(3, 2, 1)
7

julia&gt; 3 * 2 + 1
7</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L1450-L1469">source</a></section><section><div><pre><code class="language-julia hljs">muladd(A, y, z)</code></pre><p>Combined multiply-add, <code>A*y .+ z</code>, for matrix-matrix or matrix-vector multiplication. The result is always the same size as <code>A*y</code>, but <code>z</code> may be smaller, or a scalar.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>These methods require Julia 1.6 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A=[1.0 2.0; 3.0 4.0]; B=[1.0 1.0; 1.0 1.0]; z=[0, 100];

julia&gt; muladd(A, B, z)
2×2 Matrix{Float64}:
   3.0    3.0
 107.0  107.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LinearAlgebra/src/matmul.jl#L160-L178">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.inv-Tuple{Number}" href="#Base.inv-Tuple{Number}"><code>Base.inv</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">inv(x)</code></pre><p>Return the multiplicative inverse of <code>x</code>, such that <code>x*inv(x)</code> or <code>inv(x)*x</code> yields <a href="numbers.html#Base.one"><code>one(x)</code></a> (the multiplicative identity) up to roundoff errors.</p><p>If <code>x</code> is a number, this is essentially the same as <code>one(x)/x</code>, but for some types <code>inv(x)</code> may be slightly more efficient.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; inv(2)
0.5

julia&gt; inv(1 + 2im)
0.2 - 0.4im

julia&gt; inv(1 + 2im) * (1 + 2im)
1.0 + 0.0im

julia&gt; inv(2//3)
3//2</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p><code>inv(::Missing)</code> requires at least Julia 1.2.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/number.jl#L228-L254">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.div" href="#Base.div"><code>Base.div</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">div(x, y)
÷(x, y)</code></pre><p>The quotient from Euclidean (integer) division. Generally equivalent to a mathematical operation x/y without a fractional part.</p><p>See also: <a href="math.html#Base.cld"><code>cld</code></a>, <a href="math.html#Base.fld"><code>fld</code></a>, <a href="math.html#Base.rem"><code>rem</code></a>, <a href="math.html#Base.divrem"><code>divrem</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; 9 ÷ 4
2

julia&gt; -5 ÷ 3
-1

julia&gt; 5.0 ÷ 2
2.0

julia&gt; div.(-5:5, 3)&#39;
1×11 adjoint(::Vector{Int64}) with eltype Int64:
 -1  -1  -1  0  0  0  0  0  1  1  1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L785-L809">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.div-Tuple{Any, Any, RoundingMode}" href="#Base.div-Tuple{Any, Any, RoundingMode}"><code>Base.div</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">div(x, y, r::RoundingMode=RoundToZero)</code></pre><p>The quotient from Euclidean (integer) division. Computes <code>x / y</code>, rounded to an integer according to the rounding mode <code>r</code>. In other words, the quantity</p><pre><code class="nohighlight hljs">round(x / y, r)</code></pre><p>without any intermediate rounding.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.4</header><div class="admonition-body"><p>The three-argument method taking a <code>RoundingMode</code> requires Julia 1.4 or later.</p></div></div><p>See also <a href="math.html#Base.fld"><code>fld</code></a> and <a href="math.html#Base.cld"><code>cld</code></a>, which are special cases of this function.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p><code>RoundFromZero</code> requires at least Julia 1.9.</p></div></div><p><strong>Examples:</strong></p><pre><code class="language-julia-repl hljs">julia&gt; div(4, 3, RoundToZero) # Matches div(4, 3)
1
julia&gt; div(4, 3, RoundDown) # Matches fld(4, 3)
1
julia&gt; div(4, 3, RoundUp) # Matches cld(4, 3)
2
julia&gt; div(5, 2, RoundNearest)
2
julia&gt; div(5, 2, RoundNearestTiesAway)
3
julia&gt; div(-5, 2, RoundNearest)
-2
julia&gt; div(-5, 2, RoundNearestTiesAway)
-3
julia&gt; div(-5, 2, RoundNearestTiesUp)
-2
julia&gt; div(4, 3, RoundFromZero)
2
julia&gt; div(-4, 3, RoundFromZero)
-2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/div.jl#L5-L46">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.fld" href="#Base.fld"><code>Base.fld</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">fld(x, y)</code></pre><p>Largest integer less than or equal to <code>x / y</code>. Equivalent to <code>div(x, y, RoundDown)</code>.</p><p>See also <a href="math.html#Base.div"><code>div</code></a>, <a href="math.html#Base.cld"><code>cld</code></a>, <a href="math.html#Base.fld1"><code>fld1</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; fld(7.3, 5.5)
1.0

julia&gt; fld.(-5:5, 3)&#39;
1×11 adjoint(::Vector{Int64}) with eltype Int64:
 -2  -2  -1  -1  -1  0  0  0  1  1  1</code></pre><p>Because <code>fld(x, y)</code> implements strictly correct floored rounding based on the true value of floating-point numbers, unintuitive situations can arise. For example:</p><pre><code class="language-julia-repl hljs">julia&gt; fld(6.0, 0.1)
59.0
julia&gt; 6.0 / 0.1
60.0
julia&gt; 6.0 / big(0.1)
59.99999999999999666933092612453056361837965690217069245739573412231113406246995</code></pre><p>What is happening here is that the true value of the floating-point number written as <code>0.1</code> is slightly larger than the numerical value 1/10 while <code>6.0</code> represents the number 6 precisely. Therefore the true value of <code>6.0 / 0.1</code> is slightly less than 60. When doing division, this is rounded to precisely <code>60.0</code>, but <code>fld(6.0, 0.1)</code> always takes the floor of the true value, so the result is <code>59.0</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/div.jl#L109-L140">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.cld" href="#Base.cld"><code>Base.cld</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cld(x, y)</code></pre><p>Smallest integer larger than or equal to <code>x / y</code>. Equivalent to <code>div(x, y, RoundUp)</code>.</p><p>See also <a href="math.html#Base.div"><code>div</code></a>, <a href="math.html#Base.fld"><code>fld</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; cld(5.5, 2.2)
3.0

julia&gt; cld.(-5:5, 3)&#39;
1×11 adjoint(::Vector{Int64}) with eltype Int64:
 -1  -1  -1  0  0  0  1  1  1  2  2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/div.jl#L143-L159">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.mod" href="#Base.mod"><code>Base.mod</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mod(x::Integer, r::AbstractUnitRange)</code></pre><p>Find <code>y</code> in the range <code>r</code> such that <span>$x ≡ y (mod n)$</span>, where <code>n = length(r)</code>, i.e. <code>y = mod(x - first(r), n) + first(r)</code>.</p><p>See also <a href="math.html#Base.mod1"><code>mod1</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; mod(0, Base.OneTo(3))  # mod1(0, 3)
3

julia&gt; mod(3, 0:2)  # mod(3, 3)
0</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p>This method requires at least Julia 1.3.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/range.jl#L1475-L1494">source</a></section><section><div><pre><code class="language-julia hljs">mod(x, y)
rem(x, y, RoundDown)</code></pre><p>The reduction of <code>x</code> modulo <code>y</code>, or equivalently, the remainder of <code>x</code> after floored division by <code>y</code>, i.e. <code>x - y*fld(x,y)</code> if computed without intermediate rounding.</p><p>The result will have the same sign as <code>y</code>, and magnitude less than <code>abs(y)</code> (with some exceptions, see note below).</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>When used with floating point values, the exact result may not be representable by the type, and so rounding error may occur. In particular, if the exact result is very close to <code>y</code>, then it may be rounded to <code>y</code>.</p></div></div><p>See also: <a href="math.html#Base.rem"><code>rem</code></a>, <a href="math.html#Base.div"><code>div</code></a>, <a href="math.html#Base.fld"><code>fld</code></a>, <a href="math.html#Base.mod1"><code>mod1</code></a>, <a href="math.html#Base.invmod"><code>invmod</code></a>.</p><pre><code class="language-julia-repl hljs">julia&gt; mod(8, 3)
2

julia&gt; mod(9, 3)
0

julia&gt; mod(8.9, 3)
2.9000000000000004

julia&gt; mod(eps(), 3)
2.220446049250313e-16

julia&gt; mod(-eps(), 3)
3.0

julia&gt; mod.(-5:5, 3)&#39;
1×11 adjoint(::Vector{Int64}) with eltype Int64:
 1  2  0  1  2  0  1  2  0  1  2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L246-L284">source</a></section><section><div><pre><code class="language-julia hljs">rem(x::Integer, T::Type{&lt;:Integer}) -&gt; T
mod(x::Integer, T::Type{&lt;:Integer}) -&gt; T
%(x::Integer, T::Type{&lt;:Integer}) -&gt; T</code></pre><p>Find <code>y::T</code> such that <code>x</code> ≡ <code>y</code> (mod n), where n is the number of integers representable in <code>T</code>, and <code>y</code> is an integer in <code>[typemin(T),typemax(T)]</code>. If <code>T</code> can represent any integer (e.g. <code>T == BigInt</code>), then this operation corresponds to a conversion to <code>T</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = 129 % Int8
-127

julia&gt; typeof(x)
Int8

julia&gt; x = 129 % BigInt
129

julia&gt; typeof(x)
BigInt</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L595-L619">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.rem" href="#Base.rem"><code>Base.rem</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rem(x, y)
%(x, y)</code></pre><p>Remainder from Euclidean division, returning a value of the same sign as <code>x</code>, and smaller in magnitude than <code>y</code>. This value is always exact.</p><p>See also: <a href="math.html#Base.div"><code>div</code></a>, <a href="math.html#Base.mod"><code>mod</code></a>, <a href="math.html#Base.mod1"><code>mod1</code></a>, <a href="math.html#Base.divrem"><code>divrem</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = 15; y = 4;

julia&gt; x % y
3

julia&gt; x == div(x, y) * y + rem(x, y)
true

julia&gt; rem.(-5:5, 3)&#39;
1×11 adjoint(::Vector{Int64}) with eltype Int64:
 -2  -1  0  -2  -1  0  1  2  0  1  2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L758-L781">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.rem-Tuple{Any, Any, RoundingMode}" href="#Base.rem-Tuple{Any, Any, RoundingMode}"><code>Base.rem</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rem(x, y, r::RoundingMode=RoundToZero)</code></pre><p>Compute the remainder of <code>x</code> after integer division by <code>y</code>, with the quotient rounded according to the rounding mode <code>r</code>. In other words, the quantity</p><pre><code class="nohighlight hljs">x - y * round(x / y, r)</code></pre><p>without any intermediate rounding.</p><ul><li><p>if <code>r == RoundNearest</code>, then the result is exact, and in the interval <span>$[-|y| / 2, |y| / 2]$</span>. See also <a href="math.html#Base.Rounding.RoundNearest"><code>RoundNearest</code></a>.</p></li><li><p>if <code>r == RoundToZero</code> (default), then the result is exact, and in the interval <span>$[0, |y|)$</span> if <code>x</code> is positive, or <span>$(-|y|, 0]$</span> otherwise. See also <a href="math.html#Base.Rounding.RoundToZero"><code>RoundToZero</code></a>.</p></li><li><p>if <code>r == RoundDown</code>, then the result is in the interval <span>$[0, y)$</span> if <code>y</code> is positive, or <span>$(y, 0]$</span> otherwise. The result may not be exact if <code>x</code> and <code>y</code> have different signs, and <code>abs(x) &lt; abs(y)</code>. See also <a href="math.html#Base.Rounding.RoundDown"><code>RoundDown</code></a>.</p></li><li><p>if <code>r == RoundUp</code>, then the result is in the interval <span>$(-y, 0]$</span> if <code>y</code> is positive, or <span>$[0, -y)$</span> otherwise. The result may not be exact if <code>x</code> and <code>y</code> have the same sign, and <code>abs(x) &lt; abs(y)</code>. See also <a href="math.html#Base.Rounding.RoundUp"><code>RoundUp</code></a>.</p></li><li><p>if <code>r == RoundFromZero</code>, then the result is in the interval <span>$(-y, 0]$</span> if <code>y</code> is positive, or <span>$[0, -y)$</span> otherwise. The result may not be exact if <code>x</code> and <code>y</code> have the same sign, and <code>abs(x) &lt; abs(y)</code>. See also <a href="math.html#Base.Rounding.RoundFromZero"><code>RoundFromZero</code></a>.</p></li></ul><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p><code>RoundFromZero</code> requires at least Julia 1.9.</p></div></div><p><strong>Examples:</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = 9; y = 4;

julia&gt; x % y  # same as rem(x, y)
1

julia&gt; x ÷ y  # same as div(x, y)
2

julia&gt; x == div(x, y) * y + rem(x, y)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/div.jl#L51-L95">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.rem2pi" href="#Base.Math.rem2pi"><code>Base.Math.rem2pi</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rem2pi(x, r::RoundingMode)</code></pre><p>Compute the remainder of <code>x</code> after integer division by <code>2π</code>, with the quotient rounded according to the rounding mode <code>r</code>. In other words, the quantity</p><pre><code class="nohighlight hljs">x - 2π*round(x/(2π),r)</code></pre><p>without any intermediate rounding. This internally uses a high precision approximation of 2π, and so will give a more accurate result than <code>rem(x,2π,r)</code></p><ul><li><p>if <code>r == RoundNearest</code>, then the result is in the interval <span>$[-π, π]$</span>. This will generally be the most accurate result. See also <a href="math.html#Base.Rounding.RoundNearest"><code>RoundNearest</code></a>.</p></li><li><p>if <code>r == RoundToZero</code>, then the result is in the interval <span>$[0, 2π]$</span> if <code>x</code> is positive,. or <span>$[-2π, 0]$</span> otherwise. See also <a href="math.html#Base.Rounding.RoundToZero"><code>RoundToZero</code></a>.</p></li><li><p>if <code>r == RoundDown</code>, then the result is in the interval <span>$[0, 2π]$</span>. See also <a href="math.html#Base.Rounding.RoundDown"><code>RoundDown</code></a>.</p></li><li><p>if <code>r == RoundUp</code>, then the result is in the interval <span>$[-2π, 0]$</span>. See also <a href="math.html#Base.Rounding.RoundUp"><code>RoundUp</code></a>.</p></li></ul><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; rem2pi(7pi/4, RoundNearest)
-0.7853981633974485

julia&gt; rem2pi(7pi/4, RoundDown)
5.497787143782138</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L1263-L1293">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.mod2pi" href="#Base.Math.mod2pi"><code>Base.Math.mod2pi</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mod2pi(x)</code></pre><p>Modulus after division by <code>2π</code>, returning in the range <span>$[0,2π)$</span>.</p><p>This function computes a floating point representation of the modulus after division by numerically exact <code>2π</code>, and is therefore not exactly the same as <code>mod(x,2π)</code>, which would compute the modulus of <code>x</code> relative to division by the floating-point number <code>2π</code>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Depending on the format of the input value, the closest representable value to 2π may be less than 2π. For example, the expression <code>mod2pi(2π)</code> will not return <code>0</code>, because the intermediate value of <code>2*π</code> is a <code>Float64</code> and <code>2*Float64(π) &lt; 2*big(π)</code>. See <a href="math.html#Base.Math.rem2pi"><code>rem2pi</code></a> for more refined control of this behavior.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; mod2pi(9*pi/4)
0.7853981633974481</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L1425-L1445">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.divrem" href="#Base.divrem"><code>Base.divrem</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">divrem(x, y, r::RoundingMode=RoundToZero)</code></pre><p>The quotient and remainder from Euclidean division. Equivalent to <code>(div(x, y, r), rem(x, y, r))</code>. Equivalently, with the default value of <code>r</code>, this call is equivalent to <code>(x ÷ y, x % y)</code>.</p><p>See also: <a href="math.html#Base.fldmod"><code>fldmod</code></a>, <a href="math.html#Base.cld"><code>cld</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; divrem(3, 7)
(0, 3)

julia&gt; divrem(7, 3)
(2, 1)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/div.jl#L163-L180">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.fldmod" href="#Base.fldmod"><code>Base.fldmod</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">fldmod(x, y)</code></pre><p>The floored quotient and modulus after division. A convenience wrapper for <code>divrem(x, y, RoundDown)</code>. Equivalent to <code>(fld(x, y), mod(x, y))</code>.</p><p>See also: <a href="math.html#Base.fld"><code>fld</code></a>, <a href="math.html#Base.cld"><code>cld</code></a>, <a href="math.html#Base.fldmod1"><code>fldmod1</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/div.jl#L268-L275">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.fld1" href="#Base.fld1"><code>Base.fld1</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">fld1(x, y)</code></pre><p>Flooring division, returning a value consistent with <code>mod1(x,y)</code></p><p>See also <a href="math.html#Base.mod1"><code>mod1</code></a>, <a href="math.html#Base.fldmod1"><code>fldmod1</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = 15; y = 4;

julia&gt; fld1(x, y)
4

julia&gt; x == fld(x, y) * y + mod(x, y)
true

julia&gt; x == (fld1(x, y) - 1) * y + mod1(x, y)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L842-L862">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.mod1" href="#Base.mod1"><code>Base.mod1</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mod1(x, y)</code></pre><p>Modulus after flooring division, returning a value <code>r</code> such that <code>mod(r, y) == mod(x, y)</code> in the range <span>$(0, y]$</span> for positive <code>y</code> and in the range <span>$[y,0)$</span> for negative <code>y</code>.</p><p>With integer arguments and positive <code>y</code>, this is equal to <code>mod(x, 1:y)</code>, and hence natural for 1-based indexing. By comparison, <code>mod(x, y) == mod(x, 0:y-1)</code> is natural for computations with offsets or strides.</p><p>See also <a href="math.html#Base.mod"><code>mod</code></a>, <a href="math.html#Base.fld1"><code>fld1</code></a>, <a href="math.html#Base.fldmod1"><code>fldmod1</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; mod1(4, 2)
2

julia&gt; mod1.(-5:5, 3)&#39;
1×11 adjoint(::Vector{Int64}) with eltype Int64:
 1  2  3  1  2  3  1  2  3  1  2

julia&gt; mod1.([-0.1, 0, 0.1, 1, 2, 2.9, 3, 3.1]&#39;, 3)
1×8 Matrix{Float64}:
 2.9  3.0  0.1  1.0  2.0  2.9  3.0  0.1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L813-L838">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.fldmod1" href="#Base.fldmod1"><code>Base.fldmod1</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">fldmod1(x, y)</code></pre><p>Return <code>(fld1(x,y), mod1(x,y))</code>.</p><p>See also <a href="math.html#Base.fld1"><code>fld1</code></a>, <a href="math.html#Base.mod1"><code>mod1</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L869-L875">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.://" href="#Base.://"><code>Base.://</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">//(num, den)</code></pre><p>Divide two integers or rational numbers, giving a <a href="numbers.html#Base.Rational"><code>Rational</code></a> result. More generally, <code>//</code> can be used for exact rational division of other numeric types with integer or rational components, such as complex numbers with integer components.</p><p>Note that floating-point (<a href="numbers.html#Core.AbstractFloat"><code>AbstractFloat</code></a>) arguments are not permitted by <code>//</code> (even if the values are rational). The arguments must be subtypes of <a href="numbers.html#Core.Integer"><code>Integer</code></a>, <code>Rational</code>, or composites thereof.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; 3 // 5
3//5

julia&gt; (3 // 5) // (2 // 1)
3//10

julia&gt; (1+2im) // (3+4im)
11//25 + 2//25*im

julia&gt; 1.0 // 2
ERROR: MethodError: no method matching //(::Float64, ::Int64)
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rational.jl#L57-L83">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.rationalize" href="#Base.rationalize"><code>Base.rationalize</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rationalize([T&lt;:Integer=Int,] x; tol::Real=eps(x))</code></pre><p>Approximate floating point number <code>x</code> as a <a href="numbers.html#Base.Rational"><code>Rational</code></a> number with components of the given integer type. The result will differ from <code>x</code> by no more than <code>tol</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; rationalize(5.6)
28//5

julia&gt; a = rationalize(BigInt, 10.3)
103//10

julia&gt; typeof(numerator(a))
BigInt</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rational.jl#L188-L205">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.numerator" href="#Base.numerator"><code>Base.numerator</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">numerator(x)</code></pre><p>Numerator of the rational representation of <code>x</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; numerator(2//3)
2

julia&gt; numerator(4)
4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rational.jl#L282-L295">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.denominator" href="#Base.denominator"><code>Base.denominator</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">denominator(x)</code></pre><p>Denominator of the rational representation of <code>x</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; denominator(2//3)
3

julia&gt; denominator(4)
1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rational.jl#L299-L312">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:&lt;&lt;" href="#Base.:&lt;&lt;"><code>Base.:&lt;&lt;</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">&lt;&lt;(x, n)</code></pre><p>Left bit shift operator, <code>x &lt;&lt; n</code>. For <code>n &gt;= 0</code>, the result is <code>x</code> shifted left by <code>n</code> bits, filling with <code>0</code>s. This is equivalent to <code>x * 2^n</code>. For <code>n &lt; 0</code>, this is equivalent to <code>x &gt;&gt; -n</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Int8(3) &lt;&lt; 2
12

julia&gt; bitstring(Int8(3))
&quot;00000011&quot;

julia&gt; bitstring(Int8(12))
&quot;00001100&quot;</code></pre><p>See also <a href="math.html#Base.:&gt;&gt;"><code>&gt;&gt;</code></a>, <a href="math.html#Base.:&gt;&gt;&gt;"><code>&gt;&gt;&gt;</code></a>, <a href="math.html#Base.exp2"><code>exp2</code></a>, <a href="math.html#Base.Math.ldexp"><code>ldexp</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L640-L659">source</a></section><section><div><pre><code class="language-julia hljs">&lt;&lt;(B::BitVector, n) -&gt; BitVector</code></pre><p>Left bit shift operator, <code>B &lt;&lt; n</code>. For <code>n &gt;= 0</code>, the result is <code>B</code> with elements shifted <code>n</code> positions backwards, filling with <code>false</code> values. If <code>n &lt; 0</code>, elements are shifted forwards. Equivalent to <code>B &gt;&gt; -n</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; B = BitVector([true, false, true, false, false])
5-element BitVector:
 1
 0
 1
 0
 0

julia&gt; B &lt;&lt; 1
5-element BitVector:
 0
 1
 0
 0
 0

julia&gt; B &lt;&lt; -1
5-element BitVector:
 0
 1
 0
 1
 0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/bitarray.jl#L1378-L1412">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:&gt;&gt;" href="#Base.:&gt;&gt;"><code>Base.:&gt;&gt;</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">&gt;&gt;(x, n)</code></pre><p>Right bit shift operator, <code>x &gt;&gt; n</code>. For <code>n &gt;= 0</code>, the result is <code>x</code> shifted right by <code>n</code> bits, filling with <code>0</code>s if <code>x &gt;= 0</code>, <code>1</code>s if <code>x &lt; 0</code>, preserving the sign of <code>x</code>. This is equivalent to <code>fld(x, 2^n)</code>. For <code>n &lt; 0</code>, this is equivalent to <code>x &lt;&lt; -n</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Int8(13) &gt;&gt; 2
3

julia&gt; bitstring(Int8(13))
&quot;00001101&quot;

julia&gt; bitstring(Int8(3))
&quot;00000011&quot;

julia&gt; Int8(-14) &gt;&gt; 2
-4

julia&gt; bitstring(Int8(-14))
&quot;11110010&quot;

julia&gt; bitstring(Int8(-4))
&quot;11111100&quot;</code></pre><p>See also <a href="math.html#Base.:&gt;&gt;&gt;"><code>&gt;&gt;&gt;</code></a>, <a href="math.html#Base.:&lt;&lt;"><code>&lt;&lt;</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L675-L704">source</a></section><section><div><pre><code class="language-julia hljs">&gt;&gt;(B::BitVector, n) -&gt; BitVector</code></pre><p>Right bit shift operator, <code>B &gt;&gt; n</code>. For <code>n &gt;= 0</code>, the result is <code>B</code> with elements shifted <code>n</code> positions forward, filling with <code>false</code> values. If <code>n &lt; 0</code>, elements are shifted backwards. Equivalent to <code>B &lt;&lt; -n</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; B = BitVector([true, false, true, false, false])
5-element BitVector:
 1
 0
 1
 0
 0

julia&gt; B &gt;&gt; 1
5-element BitVector:
 0
 1
 0
 1
 0

julia&gt; B &gt;&gt; -1
5-element BitVector:
 0
 1
 0
 0
 0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/bitarray.jl#L1340-L1374">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:&gt;&gt;&gt;" href="#Base.:&gt;&gt;&gt;"><code>Base.:&gt;&gt;&gt;</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">&gt;&gt;&gt;(x, n)</code></pre><p>Unsigned right bit shift operator, <code>x &gt;&gt;&gt; n</code>. For <code>n &gt;= 0</code>, the result is <code>x</code> shifted right by <code>n</code> bits, filling with <code>0</code>s. For <code>n &lt; 0</code>, this is equivalent to <code>x &lt;&lt; -n</code>.</p><p>For <a href="numbers.html#Core.Unsigned"><code>Unsigned</code></a> integer types, this is equivalent to <a href="math.html#Base.:&gt;&gt;"><code>&gt;&gt;</code></a>. For <a href="numbers.html#Core.Signed"><code>Signed</code></a> integer types, this is equivalent to <code>signed(unsigned(x) &gt;&gt; n)</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Int8(-14) &gt;&gt;&gt; 2
60

julia&gt; bitstring(Int8(-14))
&quot;11110010&quot;

julia&gt; bitstring(Int8(60))
&quot;00111100&quot;</code></pre><p><a href="numbers.html#Base.GMP.BigInt"><code>BigInt</code></a>s are treated as if having infinite size, so no filling is required and this is equivalent to <a href="math.html#Base.:&gt;&gt;"><code>&gt;&gt;</code></a>.</p><p>See also <a href="math.html#Base.:&gt;&gt;"><code>&gt;&gt;</code></a>, <a href="math.html#Base.:&lt;&lt;"><code>&lt;&lt;</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L716-L742">source</a></section><section><div><pre><code class="language-julia hljs">&gt;&gt;&gt;(B::BitVector, n) -&gt; BitVector</code></pre><p>Unsigned right bitshift operator, <code>B &gt;&gt;&gt; n</code>. Equivalent to <code>B &gt;&gt; n</code>. See <a href="math.html#Base.:&gt;&gt;"><code>&gt;&gt;</code></a> for details and examples.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/bitarray.jl#L1415-L1420">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.bitrotate" href="#Base.bitrotate"><code>Base.bitrotate</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">bitrotate(x::Base.BitInteger, k::Integer)</code></pre><p><code>bitrotate(x, k)</code> implements bitwise rotation. It returns the value of <code>x</code> with its bits rotated left <code>k</code> times. A negative value of <code>k</code> will rotate to the right instead.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p>This function requires Julia 1.5 or later.</p></div></div><p>See also: <a href="math.html#Base.:&lt;&lt;"><code>&lt;&lt;</code></a>, <a href="arrays.html#Base.circshift"><code>circshift</code></a>, <a href="arrays.html#Base.BitArray"><code>BitArray</code></a>.</p><pre><code class="language-julia-repl hljs">julia&gt; bitrotate(UInt8(114), 2)
0xc9

julia&gt; bitstring(bitrotate(0b01110010, 2))
&quot;11001001&quot;

julia&gt; bitstring(bitrotate(0b01110010, -2))
&quot;10011100&quot;

julia&gt; bitstring(bitrotate(0b01110010, 8))
&quot;01110010&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L561-L586">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.::" href="#Base.::"><code>Base.::</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">:expr</code></pre><p>Quote an expression <code>expr</code>, returning the abstract syntax tree (AST) of <code>expr</code>. The AST may be of type <code>Expr</code>, <code>Symbol</code>, or a literal value. The syntax <code>:identifier</code> evaluates to a <code>Symbol</code>.</p><p>See also: <a href="base.html#Core.Expr"><code>Expr</code></a>, <a href="base.html#Core.Symbol"><code>Symbol</code></a>, <a href="base.html#Base.Meta.parse-Tuple{AbstractString, Int64}"><code>Meta.parse</code></a></p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; expr = :(a = b + 2*x)
:(a = b + 2x)

julia&gt; sym = :some_identifier
:some_identifier

julia&gt; value = :0xff
0xff

julia&gt; typeof((expr, sym, value))
Tuple{Expr, Symbol, UInt8}</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L732-L755">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.range" href="#Base.range"><code>Base.range</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">range(start, stop, length)
range(start, stop; length, step)
range(start; length, stop, step)
range(;start, length, stop, step)</code></pre><p>Construct a specialized array with evenly spaced elements and optimized storage (an <a href="collections.html#Base.AbstractRange"><code>AbstractRange</code></a>) from the arguments. Mathematically a range is uniquely determined by any three of <code>start</code>, <code>step</code>, <code>stop</code> and <code>length</code>. Valid invocations of range are:</p><ul><li>Call <code>range</code> with any three of <code>start</code>, <code>step</code>, <code>stop</code>, <code>length</code>.</li><li>Call <code>range</code> with two of <code>start</code>, <code>stop</code>, <code>length</code>. In this case <code>step</code> will be assumed to be one. If both arguments are Integers, a <a href="collections.html#Base.UnitRange"><code>UnitRange</code></a> will be returned.</li><li>Call <code>range</code> with one of <code>stop</code> or <code>length</code>. <code>start</code> and <code>step</code> will be assumed to be one.</li></ul><p>See Extended Help for additional details on the returned type. See also <a href="math.html#Base.logrange"><code>logrange</code></a> for logarithmically spaced points.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; range(1, length=100)
1:100

julia&gt; range(1, stop=100)
1:100

julia&gt; range(1, step=5, length=100)
1:5:496

julia&gt; range(1, step=5, stop=100)
1:5:96

julia&gt; range(1, 10, length=101)
1.0:0.09:10.0

julia&gt; range(1, 100, step=5)
1:5:96

julia&gt; range(stop=10, length=5)
6:10

julia&gt; range(stop=10, step=1, length=5)
6:1:10

julia&gt; range(start=1, step=1, stop=10)
1:1:10

julia&gt; range(; length = 10)
Base.OneTo(10)

julia&gt; range(; stop = 6)
Base.OneTo(6)

julia&gt; range(; stop = 6.5)
1.0:1.0:6.0</code></pre><p>If <code>length</code> is not specified and <code>stop - start</code> is not an integer multiple of <code>step</code>, a range that ends before <code>stop</code> will be produced.</p><pre><code class="language-julia-repl hljs">julia&gt; range(1, 3.5, step=2)
1.0:2.0:3.0</code></pre><p>Special care is taken to ensure intermediate values are computed rationally. To avoid this induced overhead, see the <a href="collections.html#Base.LinRange"><code>LinRange</code></a> constructor.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p><code>stop</code> as a positional argument requires at least Julia 1.1.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>The versions without keyword arguments and <code>start</code> as a keyword argument require at least Julia 1.7.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>The versions with <code>stop</code> as a sole keyword argument, or <code>length</code> as a sole keyword argument require at least Julia 1.8.</p></div></div><p><strong>Extended Help</strong></p><p><code>range</code> will produce a <code>Base.OneTo</code> when the arguments are Integers and</p><ul><li>Only <code>length</code> is provided</li><li>Only <code>stop</code> is provided</li></ul><p><code>range</code> will produce a <code>UnitRange</code> when the arguments are Integers and</p><ul><li>Only <code>start</code>  and <code>stop</code> are provided</li><li>Only <code>length</code> and <code>stop</code> are provided</li></ul><p>A <code>UnitRange</code> is not produced if <code>step</code> is provided even if specified as one.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/range.jl#L58-L145">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.OneTo" href="#Base.OneTo"><code>Base.OneTo</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.OneTo(n)</code></pre><p>Define an <code>AbstractUnitRange</code> that behaves like <code>1:n</code>, with the added distinction that the lower limit is guaranteed (by the type system) to be 1.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/range.jl#L447-L453">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.StepRangeLen" href="#Base.StepRangeLen"><code>Base.StepRangeLen</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">StepRangeLen(         ref::R, step::S, len, [offset=1]) where {  R,S}
StepRangeLen{T,R,S}(  ref::R, step::S, len, [offset=1]) where {T,R,S}
StepRangeLen{T,R,S,L}(ref::R, step::S, len, [offset=1]) where {T,R,S,L}</code></pre><p>A range <code>r</code> where <code>r[i]</code> produces values of type <code>T</code> (in the first form, <code>T</code> is deduced automatically), parameterized by a <code>ref</code>erence value, a <code>step</code>, and the <code>len</code>gth. By default <code>ref</code> is the starting value <code>r[1]</code>, but alternatively you can supply it as the value of <code>r[offset]</code> for some other index <code>1 &lt;= offset &lt;= len</code>. The syntax <code>a:b</code> or <code>a:b:c</code>, where any of <code>a</code>, <code>b</code>, or <code>c</code> are floating-point numbers, creates a <code>StepRangeLen</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>The 4th type parameter <code>L</code> requires at least Julia 1.7.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/range.jl#L480-L495">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.logrange" href="#Base.logrange"><code>Base.logrange</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">logrange(start, stop, length)
logrange(start, stop; length)</code></pre><p>Construct a specialized array whose elements are spaced logarithmically between the given endpoints. That is, the ratio of successive elements is a constant, calculated from the length.</p><p>This is similar to <code>geomspace</code> in Python. Unlike <code>PowerRange</code> in Mathematica, you specify the number of elements not the ratio. Unlike <code>logspace</code> in Python and Matlab, the <code>start</code> and <code>stop</code> arguments are always the first and last elements of the result, not powers applied to some base.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; logrange(10, 4000, length=3)
3-element Base.LogRange{Float64, Base.TwicePrecision{Float64}}:
 10.0, 200.0, 4000.0

julia&gt; ans[2] ≈ sqrt(10 * 4000)  # middle element is the geometric mean
true

julia&gt; range(10, 40, length=3)[2] ≈ (10 + 40)/2  # arithmetic mean
true

julia&gt; logrange(1f0, 32f0, 11)
11-element Base.LogRange{Float32, Float64}:
 1.0, 1.41421, 2.0, 2.82843, 4.0, 5.65685, 8.0, 11.3137, 16.0, 22.6274, 32.0

julia&gt; logrange(1, 1000, length=4) ≈ 10 .^ (0:3)
true</code></pre><p>See the <a href="math.html#Base.LogRange"><code>LogRange</code></a> type for further details.</p><p>See also <a href="math.html#Base.range"><code>range</code></a> for linearly spaced points.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>This function requires at least Julia 1.11.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/range.jl#L1499-L1538">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.LogRange" href="#Base.LogRange"><code>Base.LogRange</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LogRange{T}(start, stop, len) &lt;: AbstractVector{T}</code></pre><p>A range whose elements are spaced logarithmically between <code>start</code> and <code>stop</code>, with spacing controlled by <code>len</code>. Returned by <a href="math.html#Base.logrange"><code>logrange</code></a>.</p><p>Like <a href="collections.html#Base.LinRange"><code>LinRange</code></a>, the first and last elements will be exactly those provided, but intermediate values may have small floating-point errors. These are calculated using the logs of the endpoints, which are stored on construction, often in higher precision than <code>T</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; logrange(1, 4, length=5)
5-element Base.LogRange{Float64, Base.TwicePrecision{Float64}}:
 1.0, 1.41421, 2.0, 2.82843, 4.0

julia&gt; Base.LogRange{Float16}(1, 4, 5)
5-element Base.LogRange{Float16, Float64}:
 1.0, 1.414, 2.0, 2.828, 4.0

julia&gt; logrange(1e-310, 1e-300, 11)[1:2:end]
6-element Vector{Float64}:
 1.0e-310
 9.999999999999974e-309
 9.999999999999981e-307
 9.999999999999988e-305
 9.999999999999994e-303
 1.0e-300

julia&gt; prevfloat(1e-308, 5) == ans[2]
true</code></pre><p>Note that integer eltype <code>T</code> is not allowed. Use for instance <code>round.(Int, xs)</code>, or explicit powers of some integer base:</p><pre><code class="language-julia-repl hljs">julia&gt; xs = logrange(1, 512, 4)
4-element Base.LogRange{Float64, Base.TwicePrecision{Float64}}:
 1.0, 8.0, 64.0, 512.0

julia&gt; 2 .^ (0:3:9) |&gt; println
[1, 8, 64, 512]</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>This type requires at least Julia 1.11.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/range.jl#L1543-L1591">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:==" href="#Base.:=="><code>Base.:==</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">==(x, y)</code></pre><p>Generic equality operator. Falls back to <a href="base.html#Core.:==="><code>===</code></a>. Should be implemented for all types with a notion of equality, based on the abstract value that an instance represents. For example, all numeric types are compared by numeric value, ignoring type. Strings are compared as sequences of characters, ignoring encoding. Collections of the same type generally compare their key sets, and if those are <code>==</code>, then compare the values for each of those keys, returning true if all such pairs are <code>==</code>. Other properties are typically not taken into account (such as the exact type).</p><p>This operator follows IEEE semantics for floating-point numbers: <code>0.0 == -0.0</code> and <code>NaN != NaN</code>.</p><p>The result is of type <code>Bool</code>, except when one of the operands is <a href="../manual/missing.html#missing"><code>missing</code></a>, in which case <code>missing</code> is returned (<a href="https://en.wikipedia.org/wiki/Three-valued_logic">three-valued logic</a>). Collections generally implement three-valued logic akin to <a href="collections.html#Base.all-Tuple{Any}"><code>all</code></a>, returning missing if any operands contain missing values and all other pairs are equal. Use <a href="base.html#Base.isequal"><code>isequal</code></a> or <a href="base.html#Core.:==="><code>===</code></a> to always get a <code>Bool</code> result.</p><p><strong>Implementation</strong></p><p>New numeric types should implement this function for two arguments of the new type, and handle comparison to other types via promotion rules where possible.</p><p><a href="base.html#Base.isequal"><code>isequal</code></a> falls back to <code>==</code>, so new methods of <code>==</code> will be used by the <a href="collections.html#Base.Dict"><code>Dict</code></a> type to compare keys. If your type will be used as a dictionary key, it should therefore also implement <a href="base.html#Base.hash"><code>hash</code></a>.</p><p>If some type defines <code>==</code>, <a href="base.html#Base.isequal"><code>isequal</code></a>, and <a href="base.html#Base.isless"><code>isless</code></a> then it should also implement <a href="math.html#Base.:&lt;"><code>&lt;</code></a> to ensure consistency of comparisons.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L48-L79">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:!=" href="#Base.:!="><code>Base.:!=</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">!=(x, y)
≠(x,y)</code></pre><p>Not-equals comparison operator. Always gives the opposite answer as <a href="math.html#Base.:=="><code>==</code></a>.</p><p><strong>Implementation</strong></p><p>New types should generally not implement this, and rely on the fallback definition <code>!=(x,y) = !(x==y)</code> instead.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; 3 != 2
true

julia&gt; &quot;foo&quot; ≠ &quot;foo&quot;
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L258-L276">source</a></section><section><div><pre><code class="language-julia hljs">!=(x)</code></pre><p>Create a function that compares its argument to <code>x</code> using <a href="math.html#Base.:!="><code>!=</code></a>, i.e. a function equivalent to <code>y -&gt; y != x</code>. The returned function is of type <code>Base.Fix2{typeof(!=)}</code>, which can be used to implement specialized methods.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p>This functionality requires at least Julia 1.2.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L1168-L1178">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:!==" href="#Base.:!=="><code>Base.:!==</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">!==(x, y)
≢(x,y)</code></pre><p>Always gives the opposite answer as <a href="base.html#Core.:==="><code>===</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = [1, 2]; b = [1, 2];

julia&gt; a ≢ b
true

julia&gt; a ≢ a
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L307-L323">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:&lt;" href="#Base.:&lt;"><code>Base.:&lt;</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">&lt;(x, y)</code></pre><p>Less-than comparison operator. Falls back to <a href="base.html#Base.isless"><code>isless</code></a>. Because of the behavior of floating-point NaN values, this operator implements a partial order.</p><p><strong>Implementation</strong></p><p>New types with a canonical partial order should implement this function for two arguments of the new type. Types with a canonical total order should implement <a href="base.html#Base.isless"><code>isless</code></a> instead.</p><p>See also <a href="base.html#Base.isunordered"><code>isunordered</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; &#39;a&#39; &lt; &#39;b&#39;
true

julia&gt; &quot;abc&quot; &lt; &quot;abd&quot;
true

julia&gt; 5 &lt; 3
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L327-L352">source</a></section><section><div><pre><code class="language-julia hljs">&lt;(x)</code></pre><p>Create a function that compares its argument to <code>x</code> using <a href="math.html#Base.:&lt;"><code>&lt;</code></a>, i.e. a function equivalent to <code>y -&gt; y &lt; x</code>. The returned function is of type <code>Base.Fix2{typeof(&lt;)}</code>, which can be used to implement specialized methods.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p>This functionality requires at least Julia 1.2.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L1220-L1230">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:&lt;=" href="#Base.:&lt;="><code>Base.:&lt;=</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">&lt;=(x, y)
≤(x,y)</code></pre><p>Less-than-or-equals comparison operator. Falls back to <code>(x &lt; y) | (x == y)</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; &#39;a&#39; &lt;= &#39;b&#39;
true

julia&gt; 7 ≤ 7 ≤ 9
true

julia&gt; &quot;abc&quot; ≤ &quot;abc&quot;
true

julia&gt; 5 &lt;= 3
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L381-L401">source</a></section><section><div><pre><code class="language-julia hljs">&lt;=(x)</code></pre><p>Create a function that compares its argument to <code>x</code> using <a href="math.html#Base.:&lt;="><code>&lt;=</code></a>, i.e. a function equivalent to <code>y -&gt; y &lt;= x</code>. The returned function is of type <code>Base.Fix2{typeof(&lt;=)}</code>, which can be used to implement specialized methods.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p>This functionality requires at least Julia 1.2.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L1194-L1204">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:&gt;" href="#Base.:&gt;"><code>Base.:&gt;</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">&gt;(x, y)</code></pre><p>Greater-than comparison operator. Falls back to <code>y &lt; x</code>.</p><p><strong>Implementation</strong></p><p>Generally, new types should implement <a href="math.html#Base.:&lt;"><code>&lt;</code></a> instead of this function, and rely on the fallback definition <code>&gt;(x, y) = y &lt; x</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; &#39;a&#39; &gt; &#39;b&#39;
false

julia&gt; 7 &gt; 3 &gt; 1
true

julia&gt; &quot;abc&quot; &gt; &quot;abd&quot;
false

julia&gt; 5 &gt; 3
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L355-L378">source</a></section><section><div><pre><code class="language-julia hljs">&gt;(x)</code></pre><p>Create a function that compares its argument to <code>x</code> using <a href="math.html#Base.:&gt;"><code>&gt;</code></a>, i.e. a function equivalent to <code>y -&gt; y &gt; x</code>. The returned function is of type <code>Base.Fix2{typeof(&gt;)}</code>, which can be used to implement specialized methods.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p>This functionality requires at least Julia 1.2.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L1207-L1217">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:&gt;=" href="#Base.:&gt;="><code>Base.:&gt;=</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">&gt;=(x, y)
≥(x,y)</code></pre><p>Greater-than-or-equals comparison operator. Falls back to <code>y &lt;= x</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; &#39;a&#39; &gt;= &#39;b&#39;
false

julia&gt; 7 ≥ 7 ≥ 3
true

julia&gt; &quot;abc&quot; ≥ &quot;abc&quot;
true

julia&gt; 5 &gt;= 3
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L405-L425">source</a></section><section><div><pre><code class="language-julia hljs">&gt;=(x)</code></pre><p>Create a function that compares its argument to <code>x</code> using <a href="math.html#Base.:&gt;="><code>&gt;=</code></a>, i.e. a function equivalent to <code>y -&gt; y &gt;= x</code>. The returned function is of type <code>Base.Fix2{typeof(&gt;=)}</code>, which can be used to implement specialized methods.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p>This functionality requires at least Julia 1.2.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L1181-L1191">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.cmp" href="#Base.cmp"><code>Base.cmp</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cmp(x,y)</code></pre><p>Return -1, 0, or 1 depending on whether <code>x</code> is less than, equal to, or greater than <code>y</code>, respectively. Uses the total order implemented by <code>isless</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; cmp(1, 2)
-1

julia&gt; cmp(2, 1)
1

julia&gt; cmp(2+im, 3-im)
ERROR: MethodError: no method matching isless(::Complex{Int64}, ::Complex{Int64})
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L433-L451">source</a></section><section><div><pre><code class="language-julia hljs">cmp(&lt;, x, y)</code></pre><p>Return -1, 0, or 1 depending on whether <code>x</code> is less than, equal to, or greater than <code>y</code>, respectively. The first argument specifies a less-than comparison function to use.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L454-L459">source</a></section><section><div><pre><code class="language-julia hljs">cmp(a::AbstractString, b::AbstractString) -&gt; Int</code></pre><p>Compare two strings. Return <code>0</code> if both strings have the same length and the character at each index is the same in both strings. Return <code>-1</code> if <code>a</code> is a prefix of <code>b</code>, or if <code>a</code> comes before <code>b</code> in alphabetical order. Return <code>1</code> if <code>b</code> is a prefix of <code>a</code>, or if <code>b</code> comes before <code>a</code> in alphabetical order (technically, lexicographical order by Unicode code points).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; cmp(&quot;abc&quot;, &quot;abc&quot;)
0

julia&gt; cmp(&quot;ab&quot;, &quot;abc&quot;)
-1

julia&gt; cmp(&quot;abc&quot;, &quot;ab&quot;)
1

julia&gt; cmp(&quot;ab&quot;, &quot;ac&quot;)
-1

julia&gt; cmp(&quot;ac&quot;, &quot;ab&quot;)
1

julia&gt; cmp(&quot;α&quot;, &quot;a&quot;)
1

julia&gt; cmp(&quot;b&quot;, &quot;β&quot;)
-1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/basic.jl#L279-L311">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:~" href="#Base.:~"><code>Base.:~</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">~(x)</code></pre><p>Bitwise not.</p><p>See also: <a href="math.html#Base.:!"><code>!</code></a>, <a href="math.html#Base.:&amp;"><code>&amp;</code></a>, <a href="math.html#Base.:|"><code>|</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; ~4
-5

julia&gt; ~10
-11

julia&gt; ~true
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L302-L320">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:&amp;" href="#Base.:&amp;"><code>Base.:&amp;</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">x &amp; y</code></pre><p>Bitwise and. Implements <a href="https://en.wikipedia.org/wiki/Three-valued_logic">three-valued logic</a>, returning <a href="../manual/missing.html#missing"><code>missing</code></a> if one operand is <code>missing</code> and the other is <code>true</code>. Add parentheses for function application form: <code>(&amp;)(x, y)</code>.</p><p>See also: <a href="math.html#Base.:|"><code>|</code></a>, <a href="math.html#Base.xor"><code>xor</code></a>, <a href="math.html#&amp;&amp;"><code>&amp;&amp;</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; 4 &amp; 10
0

julia&gt; 4 &amp; 12
4

julia&gt; true &amp; missing
missing

julia&gt; false &amp; missing
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L323-L346">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:|" href="#Base.:|"><code>Base.:|</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">x | y</code></pre><p>Bitwise or. Implements <a href="https://en.wikipedia.org/wiki/Three-valued_logic">three-valued logic</a>, returning <a href="../manual/missing.html#missing"><code>missing</code></a> if one operand is <code>missing</code> and the other is <code>false</code>.</p><p>See also: <a href="math.html#Base.:&amp;"><code>&amp;</code></a>, <a href="math.html#Base.xor"><code>xor</code></a>, <a href="math.html#||"><code>||</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; 4 | 10
14

julia&gt; 4 | 1
5

julia&gt; true | missing
true

julia&gt; false | missing
missing</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L349-L371">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.xor" href="#Base.xor"><code>Base.xor</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">xor(x, y)
⊻(x, y)</code></pre><p>Bitwise exclusive or of <code>x</code> and <code>y</code>. Implements <a href="https://en.wikipedia.org/wiki/Three-valued_logic">three-valued logic</a>, returning <a href="../manual/missing.html#missing"><code>missing</code></a> if one of the arguments is <code>missing</code>.</p><p>The infix operation <code>a ⊻ b</code> is a synonym for <code>xor(a,b)</code>, and <code>⊻</code> can be typed by tab-completing <code>\xor</code> or <code>\veebar</code> in the Julia REPL.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; xor(true, false)
true

julia&gt; xor(true, true)
false

julia&gt; xor(true, missing)
missing

julia&gt; false ⊻ false
false

julia&gt; [true; true; false] .⊻ [true; false; false]
3-element BitVector:
 0
 1
 0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/bool.jl#L41-L72">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.nand" href="#Base.nand"><code>Base.nand</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">nand(x, y)
⊼(x, y)</code></pre><p>Bitwise nand (not and) of <code>x</code> and <code>y</code>. Implements <a href="https://en.wikipedia.org/wiki/Three-valued_logic">three-valued logic</a>, returning <a href="../manual/missing.html#missing"><code>missing</code></a> if one of the arguments is <code>missing</code>.</p><p>The infix operation <code>a ⊼ b</code> is a synonym for <code>nand(a,b)</code>, and <code>⊼</code> can be typed by tab-completing <code>\nand</code> or <code>\barwedge</code> in the Julia REPL.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; nand(true, false)
true

julia&gt; nand(true, true)
false

julia&gt; nand(true, missing)
missing

julia&gt; false ⊼ false
true

julia&gt; [true; true; false] .⊼ [true; false; false]
3-element BitVector:
 0
 1
 1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/bool.jl#L75-L106">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.nor" href="#Base.nor"><code>Base.nor</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">nor(x, y)
⊽(x, y)</code></pre><p>Bitwise nor (not or) of <code>x</code> and <code>y</code>. Implements <a href="https://en.wikipedia.org/wiki/Three-valued_logic">three-valued logic</a>, returning <a href="../manual/missing.html#missing"><code>missing</code></a> if one of the arguments is <code>missing</code> and the other is not <code>true</code>.</p><p>The infix operation <code>a ⊽ b</code> is a synonym for <code>nor(a,b)</code>, and <code>⊽</code> can be typed by tab-completing <code>\nor</code> or <code>\barvee</code> in the Julia REPL.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; nor(true, false)
false

julia&gt; nor(true, true)
false

julia&gt; nor(true, missing)
false

julia&gt; false ⊽ false
true

julia&gt; false ⊽ missing
missing

julia&gt; [true; true; false] .⊽ [true; false; false]
3-element BitVector:
 0
 0
 1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/bool.jl#L109-L144">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.:!" href="#Base.:!"><code>Base.:!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">!(x)</code></pre><p>Boolean not. Implements <a href="https://en.wikipedia.org/wiki/Three-valued_logic">three-valued logic</a>, returning <a href="../manual/missing.html#missing"><code>missing</code></a> if <code>x</code> is <code>missing</code>.</p><p>See also <a href="math.html#Base.:~"><code>~</code></a> for bitwise not.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; !true
false

julia&gt; !false
true

julia&gt; !missing
missing

julia&gt; .![true false true]
1×3 BitMatrix:
 0  1  0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/bool.jl#L11-L34">source</a></section><section><div><pre><code class="language-julia hljs">!f::Function</code></pre><p>Predicate function negation: when the argument of <code>!</code> is a function, it returns a composed function which computes the boolean negation of <code>f</code>.</p><p>See also <a href="base.html#Base.:∘"><code>∘</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; str = &quot;∀ ε &gt; 0, ∃ δ &gt; 0: |x-y| &lt; δ ⇒ |f(x)-f(y)| &lt; ε&quot;
&quot;∀ ε &gt; 0, ∃ δ &gt; 0: |x-y| &lt; δ ⇒ |f(x)-f(y)| &lt; ε&quot;

julia&gt; filter(isletter, str)
&quot;εδxyδfxfyε&quot;

julia&gt; filter(!isletter, str)
&quot;∀  &gt; 0, ∃  &gt; 0: |-| &lt;  ⇒ |()-()| &lt; &quot;</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p>Starting with Julia 1.9, <code>!f</code> returns a <a href="base.html#Base.ComposedFunction"><code>ComposedFunction</code></a> instead of an anonymous function.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L1085-L1106">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="&amp;&amp;" href="#&amp;&amp;"><code>&amp;&amp;</code></a> — <span class="docstring-category">Keyword</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">x &amp;&amp; y</code></pre><p>Short-circuiting boolean AND.</p><p>See also <a href="math.html#Base.:&amp;"><code>&amp;</code></a>, the ternary operator <code>? :</code>, and the manual section on <a href="../manual/control-flow.html#man-conditional-evaluation">control flow</a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = 3;

julia&gt; x &gt; 1 &amp;&amp; x &lt; 10 &amp;&amp; x isa Int
true

julia&gt; x &lt; 0 &amp;&amp; error(&quot;expected positive x&quot;)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L1266-L1283">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="||" href="#||"><code>||</code></a> — <span class="docstring-category">Keyword</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">x || y</code></pre><p>Short-circuiting boolean OR.</p><p>See also: <a href="math.html#Base.:|"><code>|</code></a>, <a href="math.html#Base.xor"><code>xor</code></a>, <a href="math.html#&amp;&amp;"><code>&amp;&amp;</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; pi &lt; 3 || ℯ &lt; 3
true

julia&gt; false || true || println(&quot;neither is true!&quot;)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L1286-L1301">source</a></section></article><h2 id="Mathematical-Functions"><a class="docs-heading-anchor" href="#Mathematical-Functions">Mathematical Functions</a><a id="Mathematical-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Mathematical-Functions" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isapprox" href="#Base.isapprox"><code>Base.isapprox</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isapprox(x, y; atol::Real=0, rtol::Real=atol&gt;0 ? 0 : √eps, nans::Bool=false[, norm::Function])</code></pre><p>Inexact equality comparison. Two numbers compare equal if their relative distance <em>or</em> their absolute distance is within tolerance bounds: <code>isapprox</code> returns <code>true</code> if <code>norm(x-y) &lt;= max(atol, rtol*max(norm(x), norm(y)))</code>. The default <code>atol</code> (absolute tolerance) is zero and the default <code>rtol</code> (relative tolerance) depends on the types of <code>x</code> and <code>y</code>. The keyword argument <code>nans</code> determines whether or not NaN values are considered equal (defaults to false).</p><p>For real or complex floating-point values, if an <code>atol &gt; 0</code> is not specified, <code>rtol</code> defaults to the square root of <a href="base.html#Base.eps-Tuple{Type{&lt;:AbstractFloat}}"><code>eps</code></a> of the type of <code>x</code> or <code>y</code>, whichever is bigger (least precise). This corresponds to requiring equality of about half of the significant digits. Otherwise, e.g. for integer arguments or if an <code>atol &gt; 0</code> is supplied, <code>rtol</code> defaults to zero.</p><p>The <code>norm</code> keyword defaults to <code>abs</code> for numeric <code>(x,y)</code> and to <code>LinearAlgebra.norm</code> for arrays (where an alternative <code>norm</code> choice is sometimes useful). When <code>x</code> and <code>y</code> are arrays, if <code>norm(x-y)</code> is not finite (i.e. <code>±Inf</code> or <code>NaN</code>), the comparison falls back to checking whether all elements of <code>x</code> and <code>y</code> are approximately equal component-wise.</p><p>The binary operator <code>≈</code> is equivalent to <code>isapprox</code> with the default arguments, and <code>x ≉ y</code> is equivalent to <code>!isapprox(x,y)</code>.</p><p>Note that <code>x ≈ 0</code> (i.e., comparing to zero with the default tolerances) is equivalent to <code>x == 0</code> since the default <code>atol</code> is <code>0</code>.  In such cases, you should either supply an appropriate <code>atol</code> (or use <code>norm(x) ≤ atol</code>) or rearrange your code (e.g. use <code>x ≈ y</code> rather than <code>x - y ≈ 0</code>).   It is not possible to pick a nonzero <code>atol</code> automatically because it depends on the overall scaling (the &quot;units&quot;) of your problem: for example, in <code>x - y ≈ 0</code>, <code>atol=1e-9</code> is an absurdly small tolerance if <code>x</code> is the <a href="https://en.wikipedia.org/wiki/Earth_radius">radius of the Earth</a> in meters, but an absurdly large tolerance if <code>x</code> is the <a href="https://en.wikipedia.org/wiki/Bohr_radius">radius of a Hydrogen atom</a> in meters.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>Passing the <code>norm</code> keyword argument when comparing numeric (non-array) arguments requires Julia 1.6 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isapprox(0.1, 0.15; atol=0.05)
true

julia&gt; isapprox(0.1, 0.15; rtol=0.34)
true

julia&gt; isapprox(0.1, 0.15; rtol=0.33)
false

julia&gt; 0.1 + 1e-10 ≈ 0.1
true

julia&gt; 1e-10 ≈ 0
false

julia&gt; isapprox(1e-10, 0, atol=1e-8)
true

julia&gt; isapprox([10.0^9, 1.0], [10.0^9, 2.0]) # using `norm`
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/floatfuncs.jl#L159-L219">source</a></section><section><div><pre><code class="language-julia hljs">isapprox(x; kwargs...) / ≈(x; kwargs...)</code></pre><p>Create a function that compares its argument to <code>x</code> using <code>≈</code>, i.e. a function equivalent to <code>y -&gt; y ≈ x</code>.</p><p>The keyword arguments supported here are the same as those in the 2-argument <code>isapprox</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p>This method requires Julia 1.5 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/floatfuncs.jl#L241-L250">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.sin-Tuple{Number}" href="#Base.sin-Tuple{Number}"><code>Base.sin</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sin(x)</code></pre><p>Compute sine of <code>x</code>, where <code>x</code> is in radians.</p><p>See also <a href="math.html#Base.Math.sind"><code>sind</code></a>, <a href="math.html#Base.Math.sinpi"><code>sinpi</code></a>, <a href="math.html#Base.Math.sincos-Tuple{Float64}"><code>sincos</code></a>, <a href="math.html#Base.cis"><code>cis</code></a>, <a href="math.html#Base.asin-Tuple{Number}"><code>asin</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; round.(sin.(range(0, 2pi, length=9)&#39;), digits=3)
1×9 Matrix{Float64}:
 0.0  0.707  1.0  0.707  0.0  -0.707  -1.0  -0.707  -0.0

julia&gt; sind(45)
0.7071067811865476

julia&gt; sinpi(1/4)
0.7071067811865475

julia&gt; round.(sincos(pi/6), digits=3)
(0.5, 0.866)

julia&gt; round(cis(pi/6), digits=3)
0.866 + 0.5im

julia&gt; round(exp(im*pi/6), digits=3)
0.866 + 0.5im</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L403-L431">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.cos-Tuple{Number}" href="#Base.cos-Tuple{Number}"><code>Base.cos</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cos(x)</code></pre><p>Compute cosine of <code>x</code>, where <code>x</code> is in radians.</p><p>See also <a href="math.html#Base.Math.cosd"><code>cosd</code></a>, <a href="math.html#Base.Math.cospi"><code>cospi</code></a>, <a href="math.html#Base.Math.sincos-Tuple{Float64}"><code>sincos</code></a>, <a href="math.html#Base.cis"><code>cis</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L434-L440">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.sincos-Tuple{Float64}" href="#Base.Math.sincos-Tuple{Float64}"><code>Base.Math.sincos</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sincos(x)</code></pre><p>Simultaneously compute the sine and cosine of <code>x</code>, where <code>x</code> is in radians, returning a tuple <code>(sine, cosine)</code>.</p><p>See also <a href="math.html#Base.cis"><code>cis</code></a>, <a href="math.html#Base.Math.sincospi"><code>sincospi</code></a>, <a href="math.html#Base.Math.sincosd"><code>sincosd</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L167-L174">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.tan-Tuple{Number}" href="#Base.tan-Tuple{Number}"><code>Base.tan</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">tan(x)</code></pre><p>Compute tangent of <code>x</code>, where <code>x</code> is in radians.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L443-L447">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.sind" href="#Base.Math.sind"><code>Base.Math.sind</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sind(x)</code></pre><p>Compute sine of <code>x</code>, where <code>x</code> is in degrees. If <code>x</code> is a matrix, <code>x</code> needs to be a square matrix.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Matrix arguments require Julia 1.7 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1260-L1268">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.cosd" href="#Base.Math.cosd"><code>Base.Math.cosd</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cosd(x)</code></pre><p>Compute cosine of <code>x</code>, where <code>x</code> is in degrees. If <code>x</code> is a matrix, <code>x</code> needs to be a square matrix.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Matrix arguments require Julia 1.7 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1260-L1268">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.tand" href="#Base.Math.tand"><code>Base.Math.tand</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">tand(x)</code></pre><p>Compute tangent of <code>x</code>, where <code>x</code> is in degrees. If <code>x</code> is a matrix, <code>x</code> needs to be a square matrix.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Matrix arguments require Julia 1.7 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1260-L1268">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.sincosd" href="#Base.Math.sincosd"><code>Base.Math.sincosd</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sincosd(x)</code></pre><p>Simultaneously compute the sine and cosine of <code>x</code>, where <code>x</code> is in degrees.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p>This function requires at least Julia 1.3.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1242-L1249">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.sinpi" href="#Base.Math.sinpi"><code>Base.Math.sinpi</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sinpi(x)</code></pre><p>Compute <span>$\sin(\pi x)$</span> more accurately than <code>sin(pi*x)</code>, especially for large <code>x</code>.</p><p>See also <a href="math.html#Base.Math.sind"><code>sind</code></a>, <a href="math.html#Base.Math.cospi"><code>cospi</code></a>, <a href="math.html#Base.Math.sincospi"><code>sincospi</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L785-L791">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.cospi" href="#Base.Math.cospi"><code>Base.Math.cospi</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cospi(x)</code></pre><p>Compute <span>$\cos(\pi x)$</span> more accurately than <code>cos(pi*x)</code>, especially for large <code>x</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L816-L820">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.tanpi" href="#Base.Math.tanpi"><code>Base.Math.tanpi</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">tanpi(x)</code></pre><p>Compute <span>$\tan(\pi x)$</span> more accurately than <code>tan(pi*x)</code>, especially for large <code>x</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.10</header><div class="admonition-body"><p>This function requires at least Julia 1.10.</p></div></div><p>See also <a href="math.html#Base.Math.tand"><code>tand</code></a>, <a href="math.html#Base.Math.sinpi"><code>sinpi</code></a>, <a href="math.html#Base.Math.cospi"><code>cospi</code></a>, <a href="math.html#Base.Math.sincospi"><code>sincospi</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L882-L891">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.sincospi" href="#Base.Math.sincospi"><code>Base.Math.sincospi</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sincospi(x)</code></pre><p>Simultaneously compute <a href="math.html#Base.Math.sinpi"><code>sinpi(x)</code></a> and <a href="math.html#Base.Math.cospi"><code>cospi(x)</code></a> (the sine and cosine of <code>π*x</code>, where <code>x</code> is in radians), returning a tuple <code>(sine, cosine)</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>This function requires Julia 1.6 or later.</p></div></div><p>See also: <a href="math.html#Base.cispi"><code>cispi</code></a>, <a href="math.html#Base.Math.sincosd"><code>sincosd</code></a>, <a href="math.html#Base.Math.sinpi"><code>sinpi</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L844-L854">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.sinh-Tuple{Number}" href="#Base.sinh-Tuple{Number}"><code>Base.sinh</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sinh(x)</code></pre><p>Compute hyperbolic sine of <code>x</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L315-L319">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.cosh-Tuple{Number}" href="#Base.cosh-Tuple{Number}"><code>Base.cosh</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cosh(x)</code></pre><p>Compute hyperbolic cosine of <code>x</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L322-L326">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.tanh-Tuple{Number}" href="#Base.tanh-Tuple{Number}"><code>Base.tanh</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">tanh(x)</code></pre><p>Compute hyperbolic tangent of <code>x</code>.</p><p>See also <a href="math.html#Base.tan-Tuple{Number}"><code>tan</code></a>, <a href="math.html#Base.atanh-Tuple{Number}"><code>atanh</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; tanh.(-3:3f0)  # Here 3f0 isa Float32
7-element Vector{Float32}:
 -0.9950548
 -0.9640276
 -0.7615942
  0.0
  0.7615942
  0.9640276
  0.9950548

julia&gt; tan.(im .* (1:3))
3-element Vector{ComplexF64}:
 0.0 + 0.7615941559557649im
 0.0 + 0.9640275800758169im
 0.0 + 0.9950547536867306im</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L329-L355">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.asin-Tuple{Number}" href="#Base.asin-Tuple{Number}"><code>Base.asin</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">asin(x)</code></pre><p>Compute the inverse sine of <code>x</code>, where the output is in radians.</p><p>See also <a href="math.html#Base.Math.asind"><code>asind</code></a> for output in degrees.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; asin.((0, 1/2, 1))
(0.0, 0.5235987755982989, 1.5707963267948966)

julia&gt; asind.((0, 1/2, 1))
(0.0, 30.000000000000004, 90.0)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L450-L465">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.acos-Tuple{Number}" href="#Base.acos-Tuple{Number}"><code>Base.acos</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">acos(x)</code></pre><p>Compute the inverse cosine of <code>x</code>, where the output is in radians</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L468-L472">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.atan-Tuple{Number}" href="#Base.atan-Tuple{Number}"><code>Base.atan</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">atan(y)
atan(y, x)</code></pre><p>Compute the inverse tangent of <code>y</code> or <code>y/x</code>, respectively.</p><p>For one real argument, this is the angle in radians between the positive <em>x</em>-axis and the point (1, <em>y</em>), returning a value in the interval <span>$[-\pi/2, \pi/2]$</span>.</p><p>For two arguments, this is the angle in radians between the positive <em>x</em>-axis and the point (<em>x</em>, <em>y</em>), returning a value in the interval <span>$[-\pi, \pi]$</span>. This corresponds to a standard <a href="https://en.wikipedia.org/wiki/Atan2"><code>atan2</code></a> function. Note that by convention <code>atan(0.0,x)</code> is defined as <span>$\pi$</span> and <code>atan(-0.0,x)</code> is defined as <span>$-\pi$</span> when <code>x &lt; 0</code>.</p><p>See also <a href="math.html#Base.Math.atand"><code>atand</code></a> for degrees.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; rad2deg(atan(-1/√3))
-30.000000000000004

julia&gt; rad2deg(atan(-1, √3))
-30.000000000000004

julia&gt; rad2deg(atan(1, -√3))
150.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L358-L386">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.asind" href="#Base.Math.asind"><code>Base.Math.asind</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">asind(x)</code></pre><p>Compute the inverse sine of <code>x</code>, where the output is in degrees. If <code>x</code> is a matrix, <code>x</code> needs to be a square matrix.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Matrix arguments require Julia 1.7 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1279-L1287">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.acosd" href="#Base.Math.acosd"><code>Base.Math.acosd</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">acosd(x)</code></pre><p>Compute the inverse cosine of <code>x</code>, where the output is in degrees. If <code>x</code> is a matrix, <code>x</code> needs to be a square matrix.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Matrix arguments require Julia 1.7 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1279-L1287">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.atand" href="#Base.Math.atand"><code>Base.Math.atand</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">atand(y)
atand(y,x)</code></pre><p>Compute the inverse tangent of <code>y</code> or <code>y/x</code>, respectively, where the output is in degrees.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>The one-argument method supports square matrix arguments as of Julia 1.7.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1292-L1300">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.sec-Tuple{Number}" href="#Base.Math.sec-Tuple{Number}"><code>Base.Math.sec</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sec(x)</code></pre><p>Compute the secant of <code>x</code>, where <code>x</code> is in radians.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1131-L1135">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.csc-Tuple{Number}" href="#Base.Math.csc-Tuple{Number}"><code>Base.Math.csc</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">csc(x)</code></pre><p>Compute the cosecant of <code>x</code>, where <code>x</code> is in radians.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1131-L1135">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.cot-Tuple{Number}" href="#Base.Math.cot-Tuple{Number}"><code>Base.Math.cot</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cot(x)</code></pre><p>Compute the cotangent of <code>x</code>, where <code>x</code> is in radians.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1131-L1135">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.secd" href="#Base.Math.secd"><code>Base.Math.secd</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">secd(x)</code></pre><p>Compute the secant of <code>x</code>, where <code>x</code> is in degrees.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1141-L1145">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.cscd" href="#Base.Math.cscd"><code>Base.Math.cscd</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cscd(x)</code></pre><p>Compute the cosecant of <code>x</code>, where <code>x</code> is in degrees.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1141-L1145">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.cotd" href="#Base.Math.cotd"><code>Base.Math.cotd</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cotd(x)</code></pre><p>Compute the cotangent of <code>x</code>, where <code>x</code> is in degrees.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1141-L1145">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.asec-Tuple{Number}" href="#Base.Math.asec-Tuple{Number}"><code>Base.Math.asec</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">asec(x)</code></pre><p>Compute the inverse secant of <code>x</code>, where the output is in radians. </p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1155-L1157">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.acsc-Tuple{Number}" href="#Base.Math.acsc-Tuple{Number}"><code>Base.Math.acsc</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">acsc(x)</code></pre><p>Compute the inverse cosecant of <code>x</code>, where the output is in radians. </p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1155-L1157">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.acot-Tuple{Number}" href="#Base.Math.acot-Tuple{Number}"><code>Base.Math.acot</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">acot(x)</code></pre><p>Compute the inverse cotangent of <code>x</code>, where the output is in radians. </p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1155-L1157">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.asecd" href="#Base.Math.asecd"><code>Base.Math.asecd</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">asecd(x)</code></pre><p>Compute the inverse secant of <code>x</code>, where the output is in degrees. If <code>x</code> is a matrix, <code>x</code> needs to be a square matrix.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Matrix arguments require Julia 1.7 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1279-L1287">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.acscd" href="#Base.Math.acscd"><code>Base.Math.acscd</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">acscd(x)</code></pre><p>Compute the inverse cosecant of <code>x</code>, where the output is in degrees. If <code>x</code> is a matrix, <code>x</code> needs to be a square matrix.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Matrix arguments require Julia 1.7 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1279-L1287">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.acotd" href="#Base.Math.acotd"><code>Base.Math.acotd</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">acotd(x)</code></pre><p>Compute the inverse cotangent of <code>x</code>, where the output is in degrees. If <code>x</code> is a matrix, <code>x</code> needs to be a square matrix.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Matrix arguments require Julia 1.7 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1279-L1287">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.sech-Tuple{Number}" href="#Base.Math.sech-Tuple{Number}"><code>Base.Math.sech</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sech(x)</code></pre><p>Compute the hyperbolic secant of <code>x</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1136-L1140">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.csch-Tuple{Number}" href="#Base.Math.csch-Tuple{Number}"><code>Base.Math.csch</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">csch(x)</code></pre><p>Compute the hyperbolic cosecant of <code>x</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1136-L1140">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.coth-Tuple{Number}" href="#Base.Math.coth-Tuple{Number}"><code>Base.Math.coth</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">coth(x)</code></pre><p>Compute the hyperbolic cotangent of <code>x</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1136-L1140">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.asinh-Tuple{Number}" href="#Base.asinh-Tuple{Number}"><code>Base.asinh</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">asinh(x)</code></pre><p>Compute the inverse hyperbolic sine of <code>x</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L389-L393">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.acosh-Tuple{Number}" href="#Base.acosh-Tuple{Number}"><code>Base.acosh</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">acosh(x)</code></pre><p>Compute the inverse hyperbolic cosine of <code>x</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L475-L479">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.atanh-Tuple{Number}" href="#Base.atanh-Tuple{Number}"><code>Base.atanh</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">atanh(x)</code></pre><p>Compute the inverse hyperbolic tangent of <code>x</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L482-L486">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.asech-Tuple{Number}" href="#Base.Math.asech-Tuple{Number}"><code>Base.Math.asech</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">asech(x)</code></pre><p>Compute the inverse hyperbolic secant of <code>x</code>. </p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1158-L1160">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.acsch-Tuple{Number}" href="#Base.Math.acsch-Tuple{Number}"><code>Base.Math.acsch</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">acsch(x)</code></pre><p>Compute the inverse hyperbolic cosecant of <code>x</code>. </p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1158-L1160">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.acoth-Tuple{Number}" href="#Base.Math.acoth-Tuple{Number}"><code>Base.Math.acoth</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">acoth(x)</code></pre><p>Compute the inverse hyperbolic cotangent of <code>x</code>. </p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1158-L1160">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.sinc" href="#Base.Math.sinc"><code>Base.Math.sinc</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sinc(x)</code></pre><p>Compute normalized sinc function <span>$\operatorname{sinc}(x) = \sin(\pi x) / (\pi x)$</span> if <span>$x \neq 0$</span>, and <span>$1$</span> if <span>$x = 0$</span>.</p><p>See also <a href="math.html#Base.Math.cosc"><code>cosc</code></a>, its derivative.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1065-L1071">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.cosc" href="#Base.Math.cosc"><code>Base.Math.cosc</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cosc(x)</code></pre><p>Compute <span>$\cos(\pi x) / x - \sin(\pi x) / (\pi x^2)$</span> if <span>$x \neq 0$</span>, and <span>$0$</span> if <span>$x = 0$</span>. This is the derivative of <code>sinc(x)</code>.</p><p>See also <a href="math.html#Base.Math.sinc"><code>sinc</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/trig.jl#L1082-L1089">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.deg2rad" href="#Base.Math.deg2rad"><code>Base.Math.deg2rad</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">deg2rad(x)</code></pre><p>Convert <code>x</code> from degrees to radians.</p><p>See also <a href="math.html#Base.Math.rad2deg"><code>rad2deg</code></a>, <a href="math.html#Base.Math.sind"><code>sind</code></a>, <a href="numbers.html#Base.MathConstants.pi"><code>pi</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; deg2rad(90)
1.5707963267948966</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L246-L258">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.rad2deg" href="#Base.Math.rad2deg"><code>Base.Math.rad2deg</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rad2deg(x)</code></pre><p>Convert <code>x</code> from radians to degrees.</p><p>See also <a href="math.html#Base.Math.deg2rad"><code>deg2rad</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; rad2deg(pi)
180.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L231-L243">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.hypot" href="#Base.Math.hypot"><code>Base.Math.hypot</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hypot(x, y)</code></pre><p>Compute the hypotenuse <span>$\sqrt{|x|^2+|y|^2}$</span> avoiding overflow and underflow.</p><p>This code is an implementation of the algorithm described in: An Improved Algorithm for <code>hypot(a,b)</code> by Carlos F. Borges The article is available online at arXiv at the link   https://arxiv.org/abs/1904.09481</p><pre><code class="nohighlight hljs">hypot(x...)</code></pre><p>Compute the hypotenuse <span>$\sqrt{\sum |x_i|^2}$</span> avoiding overflow and underflow.</p><p>See also <code>norm</code> in the <a href="../stdlib/LinearAlgebra.html#man-linalg"><code>LinearAlgebra</code></a> standard library.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = Int64(10)^10;

julia&gt; hypot(a, a)
1.4142135623730951e10

julia&gt; √(a^2 + a^2) # a^2 overflows
ERROR: DomainError with -2.914184810805068e18:
sqrt was called with a negative real argument but will only return a complex result if called with a complex argument. Try sqrt(Complex(x)).
Stacktrace:
[...]

julia&gt; hypot(3, 4im)
5.0

julia&gt; hypot(-5.7)
5.7

julia&gt; hypot(3, 4im, 12.0)
13.0

julia&gt; using LinearAlgebra

julia&gt; norm([a, a, a, a]) == hypot(a, a, a, a)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L660-L704">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.log-Tuple{Number}" href="#Base.log-Tuple{Number}"><code>Base.log</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">log(x)</code></pre><p>Compute the natural logarithm of <code>x</code>.</p><p>Throws <a href="base.html#Core.DomainError"><code>DomainError</code></a> for negative <a href="numbers.html#Core.Real"><code>Real</code></a> arguments. Use complex arguments to obtain complex results. Has a branch cut along the negative real axis, for which <code>-0.0im</code> is taken to be below the axis.</p><p>See also <a href="numbers.html#Base.MathConstants.ℯ"><code>ℯ</code></a>, <a href="math.html#Base.log1p"><code>log1p</code></a>, <a href="math.html#Base.log2"><code>log2</code></a>, <a href="math.html#Base.log10"><code>log10</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; log(2)
0.6931471805599453

julia&gt; log(-3)
ERROR: DomainError with -3.0:
log was called with a negative real argument but will only return a complex result if called with a complex argument. Try log(Complex(x)).
Stacktrace:
 [1] throw_complex_domainerror(::Symbol, ::Float64) at ./math.jl:31
[...]

julia&gt; log(-3 + 0im)
1.0986122886681098 + 3.141592653589793im

julia&gt; log(-3 - 0.0im)
1.0986122886681098 - 3.141592653589793im

julia&gt; log.(exp.(-1:1))
3-element Vector{Float64}:
 -1.0
  0.0
  1.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L489-L524">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.log-Tuple{Number, Number}" href="#Base.log-Tuple{Number, Number}"><code>Base.log</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">log(b,x)</code></pre><p>Compute the base <code>b</code> logarithm of <code>x</code>. Throws <a href="base.html#Core.DomainError"><code>DomainError</code></a> for negative <a href="numbers.html#Core.Real"><code>Real</code></a> arguments.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; log(4,8)
1.5

julia&gt; log(4,2)
0.5

julia&gt; log(-2, 3)
ERROR: DomainError with -2.0:
log was called with a negative real argument but will only return a complex result if called with a complex argument. Try log(Complex(x)).
Stacktrace:
 [1] throw_complex_domainerror(::Symbol, ::Float64) at ./math.jl:31
[...]

julia&gt; log(2, -3)
ERROR: DomainError with -3.0:
log was called with a negative real argument but will only return a complex result if called with a complex argument. Try log(Complex(x)).
Stacktrace:
 [1] throw_complex_domainerror(::Symbol, ::Float64) at ./math.jl:31
[...]</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>If <code>b</code> is a power of 2 or 10, <a href="math.html#Base.log2"><code>log2</code></a> or <a href="math.html#Base.log10"><code>log10</code></a> should be used, as these will typically be faster and more accurate. For example,</p><pre><code class="language-julia-repl hljs">julia&gt; log(100,1000000)
2.9999999999999996

julia&gt; log10(1000000)/2
3.0</code></pre></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L267-L307">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.log2" href="#Base.log2"><code>Base.log2</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">log2(x)</code></pre><p>Compute the logarithm of <code>x</code> to base 2. Throws <a href="base.html#Core.DomainError"><code>DomainError</code></a> for negative <a href="numbers.html#Core.Real"><code>Real</code></a> arguments.</p><p>See also: <a href="math.html#Base.exp2"><code>exp2</code></a>, <a href="math.html#Base.Math.ldexp"><code>ldexp</code></a>, <a href="math.html#Base.ispow2"><code>ispow2</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; log2(4)
2.0

julia&gt; log2(10)
3.321928094887362

julia&gt; log2(-2)
ERROR: DomainError with -2.0:
log2 was called with a negative real argument but will only return a complex result if called with a complex argument. Try log2(Complex(x)).
Stacktrace:
 [1] throw_complex_domainerror(f::Symbol, x::Float64) at ./math.jl:31
[...]

julia&gt; log2.(2.0 .^ (-1:1))
3-element Vector{Float64}:
 -1.0
  0.0
  1.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L527-L556">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.log10" href="#Base.log10"><code>Base.log10</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">log10(x)</code></pre><p>Compute the logarithm of <code>x</code> to base 10. Throws <a href="base.html#Core.DomainError"><code>DomainError</code></a> for negative <a href="numbers.html#Core.Real"><code>Real</code></a> arguments.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; log10(100)
2.0

julia&gt; log10(2)
0.3010299956639812

julia&gt; log10(-2)
ERROR: DomainError with -2.0:
log10 was called with a negative real argument but will only return a complex result if called with a complex argument. Try log10(Complex(x)).
Stacktrace:
 [1] throw_complex_domainerror(f::Symbol, x::Float64) at ./math.jl:31
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L559-L580">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.log1p" href="#Base.log1p"><code>Base.log1p</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">log1p(x)</code></pre><p>Accurate natural logarithm of <code>1+x</code>. Throws <a href="base.html#Core.DomainError"><code>DomainError</code></a> for <a href="numbers.html#Core.Real"><code>Real</code></a> arguments less than -1.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; log1p(-0.5)
-0.6931471805599453

julia&gt; log1p(0)
0.0

julia&gt; log1p(-2)
ERROR: DomainError with -2.0:
log1p was called with a real argument &lt; -1 but will only return a complex result if called with a complex argument. Try log1p(Complex(x)).
Stacktrace:
 [1] throw_complex_domainerror(::Symbol, ::Float64) at ./math.jl:31
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L583-L604">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.frexp" href="#Base.Math.frexp"><code>Base.Math.frexp</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">frexp(val)</code></pre><p>Return <code>(x,exp)</code> such that <code>x</code> has a magnitude in the interval <span>$[1/2, 1)$</span> or 0, and <code>val</code> is equal to <span>$x \times 2^{exp}$</span>.</p><p>See also <a href="numbers.html#Base.Math.significand"><code>significand</code></a>, <a href="numbers.html#Base.Math.exponent"><code>exponent</code></a>, <a href="math.html#Base.Math.ldexp"><code>ldexp</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; frexp(6.0)
(0.75, 3)

julia&gt; significand(6.0), exponent(6.0)  # interval [1, 2) instead
(1.5, 2)

julia&gt; frexp(0.0), frexp(NaN), frexp(-Inf)  # exponent would give an error
((0.0, 0), (NaN, 0), (-Inf, 0))</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L1013-L1032">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.exp-Tuple{Float64}" href="#Base.exp-Tuple{Float64}"><code>Base.exp</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">exp(x)</code></pre><p>Compute the natural base exponential of <code>x</code>, in other words <span>$ℯ^x$</span>.</p><p>See also <a href="math.html#Base.exp2"><code>exp2</code></a>, <a href="math.html#Base.exp10"><code>exp10</code></a> and <a href="math.html#Base.cis"><code>cis</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; exp(1.0)
2.718281828459045

julia&gt; exp(im * pi) ≈ cis(pi)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/exp.jl#L334-L349">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.exp2" href="#Base.exp2"><code>Base.exp2</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">exp2(x)</code></pre><p>Compute the base 2 exponential of <code>x</code>, in other words <span>$2^x$</span>.</p><p>See also <a href="math.html#Base.Math.ldexp"><code>ldexp</code></a>, <a href="math.html#Base.:&lt;&lt;"><code>&lt;&lt;</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; exp2(5)
32.0

julia&gt; 2^5
32

julia&gt; exp2(63) &gt; typemax(Int)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/exp.jl#L351-L369">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.exp10" href="#Base.exp10"><code>Base.exp10</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">exp10(x)</code></pre><p>Compute the base 10 exponential of <code>x</code>, in other words <span>$10^x$</span>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; exp10(2)
100.0

julia&gt; 10^2
100</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/exp.jl#L372-L385">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.ldexp" href="#Base.Math.ldexp"><code>Base.Math.ldexp</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ldexp(x, n)</code></pre><p>Compute <span>$x \times 2^n$</span>.</p><p>See also <a href="math.html#Base.Math.frexp"><code>frexp</code></a>, <a href="numbers.html#Base.Math.exponent"><code>exponent</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; ldexp(5.0, 2)
20.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L855-L867">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.modf" href="#Base.Math.modf"><code>Base.Math.modf</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">modf(x)</code></pre><p>Return a tuple <code>(fpart, ipart)</code> of the fractional and integral parts of a number. Both parts have the same sign as the argument.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; modf(3.5)
(0.5, 3.0)

julia&gt; modf(-3.5)
(-0.5, -3.0)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L1119-L1133">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.expm1" href="#Base.expm1"><code>Base.expm1</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">expm1(x)</code></pre><p>Accurately compute <span>$e^x-1$</span>. It avoids the loss of precision involved in the direct evaluation of exp(x)-1 for small values of x.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; expm1(1e-16)
1.0e-16

julia&gt; exp(1e-16) - 1
0.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/exp.jl#L490-L503">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.round" href="#Base.round"><code>Base.round</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">round([T,] x, [r::RoundingMode])
round(x, [r::RoundingMode]; digits::Integer=0, base = 10)
round(x, [r::RoundingMode]; sigdigits::Integer, base = 10)</code></pre><p>Rounds the number <code>x</code>.</p><p>Without keyword arguments, <code>x</code> is rounded to an integer value, returning a value of type <code>T</code>, or of the same type of <code>x</code> if no <code>T</code> is provided. An <a href="base.html#Core.InexactError"><code>InexactError</code></a> will be thrown if the value is not representable by <code>T</code>, similar to <a href="base.html#Base.convert"><code>convert</code></a>.</p><p>If the <code>digits</code> keyword argument is provided, it rounds to the specified number of digits after the decimal place (or before if negative), in base <code>base</code>.</p><p>If the <code>sigdigits</code> keyword argument is provided, it rounds to the specified number of significant digits, in base <code>base</code>.</p><p>The <a href="math.html#Base.Rounding.RoundingMode"><code>RoundingMode</code></a> <code>r</code> controls the direction of the rounding; the default is <a href="math.html#Base.Rounding.RoundNearest"><code>RoundNearest</code></a>, which rounds to the nearest integer, with ties (fractional values of 0.5) being rounded to the nearest even integer. Note that <code>round</code> may give incorrect results if the global rounding mode is changed (see <a href="numbers.html#Base.Rounding.rounding"><code>rounding</code></a>).</p><p>When rounding to a floating point type, will round to integers representable by that type (and Inf) rather than true integers. Inf is treated as one ulp greater than the <code>floatmax(T)</code> for purposes of determining &quot;nearest&quot;, similar to <a href="base.html#Base.convert"><code>convert</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; round(1.7)
2.0

julia&gt; round(Int, 1.7)
2

julia&gt; round(1.5)
2.0

julia&gt; round(2.5)
2.0

julia&gt; round(pi; digits=2)
3.14

julia&gt; round(pi; digits=3, base=2)
3.125

julia&gt; round(123.456; sigdigits=2)
120.0

julia&gt; round(357.913; sigdigits=4, base=2)
352.0

julia&gt; round(Float16, typemax(UInt128))
Inf16

julia&gt; floor(Float16, typemax(UInt128))
Float16(6.55e4)</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Rounding to specified digits in bases other than 2 can be inexact when operating on binary floating point numbers. For example, the <a href="numbers.html#Core.Float64"><code>Float64</code></a> value represented by <code>1.15</code> is actually <em>less</em> than 1.15, yet will be rounded to 1.2. For example:</p><pre><code class="language-julia-repl hljs">julia&gt; x = 1.15
1.15

julia&gt; big(1.15)
1.149999999999999911182158029987476766109466552734375

julia&gt; x &lt; 115//100
true

julia&gt; round(x, digits=1)
1.2</code></pre></div></div><p><strong>Extensions</strong></p><p>To extend <code>round</code> to new numeric types, it is typically sufficient to define <code>Base.round(x::NewType, r::RoundingMode)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rounding.jl#L319-L401">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Rounding.RoundingMode" href="#Base.Rounding.RoundingMode"><code>Base.Rounding.RoundingMode</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">RoundingMode</code></pre><p>A type used for controlling the rounding mode of floating point operations (via <a href="numbers.html#Base.Rounding.rounding"><code>rounding</code></a>/<a href="numbers.html#Base.Rounding.setrounding-Tuple{Type, Any}"><code>setrounding</code></a> functions), or as optional arguments for rounding to the nearest integer (via the <a href="math.html#Base.round"><code>round</code></a> function).</p><p>Currently supported rounding modes are:</p><ul><li><a href="math.html#Base.Rounding.RoundNearest"><code>RoundNearest</code></a> (default)</li><li><a href="math.html#Base.Rounding.RoundNearestTiesAway"><code>RoundNearestTiesAway</code></a></li><li><a href="math.html#Base.Rounding.RoundNearestTiesUp"><code>RoundNearestTiesUp</code></a></li><li><a href="math.html#Base.Rounding.RoundToZero"><code>RoundToZero</code></a></li><li><a href="math.html#Base.Rounding.RoundFromZero"><code>RoundFromZero</code></a></li><li><a href="math.html#Base.Rounding.RoundUp"><code>RoundUp</code></a></li><li><a href="math.html#Base.Rounding.RoundDown"><code>RoundDown</code></a></li></ul><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p><code>RoundFromZero</code> requires at least Julia 1.9. Prior versions support <code>RoundFromZero</code> for <code>BigFloat</code>s only.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rounding.jl#L26-L47">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Rounding.RoundNearest" href="#Base.Rounding.RoundNearest"><code>Base.Rounding.RoundNearest</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">RoundNearest</code></pre><p>The default rounding mode. Rounds to the nearest integer, with ties (fractional values of 0.5) being rounded to the nearest even integer.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rounding.jl#L50-L55">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Rounding.RoundNearestTiesAway" href="#Base.Rounding.RoundNearestTiesAway"><code>Base.Rounding.RoundNearestTiesAway</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">RoundNearestTiesAway</code></pre><p>Rounds to nearest integer, with ties rounded away from zero (C/C++ <a href="math.html#Base.round"><code>round</code></a> behaviour).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rounding.jl#L96-L101">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Rounding.RoundNearestTiesUp" href="#Base.Rounding.RoundNearestTiesUp"><code>Base.Rounding.RoundNearestTiesUp</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">RoundNearestTiesUp</code></pre><p>Rounds to nearest integer, with ties rounded toward positive infinity (Java/JavaScript <a href="math.html#Base.round"><code>round</code></a> behaviour).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rounding.jl#L104-L109">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Rounding.RoundToZero" href="#Base.Rounding.RoundToZero"><code>Base.Rounding.RoundToZero</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">RoundToZero</code></pre><p><a href="math.html#Base.round"><code>round</code></a> using this rounding mode is an alias for <a href="math.html#Base.trunc"><code>trunc</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rounding.jl#L58-L62">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Rounding.RoundFromZero" href="#Base.Rounding.RoundFromZero"><code>Base.Rounding.RoundFromZero</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">RoundFromZero</code></pre><p>Rounds away from zero.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p><code>RoundFromZero</code> requires at least Julia 1.9. Prior versions support <code>RoundFromZero</code> for <code>BigFloat</code>s only.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; BigFloat(&quot;1.0000000000000001&quot;, 5, RoundFromZero)
1.06</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rounding.jl#L79-L93">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Rounding.RoundUp" href="#Base.Rounding.RoundUp"><code>Base.Rounding.RoundUp</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">RoundUp</code></pre><p><a href="math.html#Base.round"><code>round</code></a> using this rounding mode is an alias for <a href="math.html#Base.ceil"><code>ceil</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rounding.jl#L65-L69">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Rounding.RoundDown" href="#Base.Rounding.RoundDown"><code>Base.Rounding.RoundDown</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">RoundDown</code></pre><p><a href="math.html#Base.round"><code>round</code></a> using this rounding mode is an alias for <a href="math.html#Base.floor"><code>floor</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rounding.jl#L72-L76">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.round-Tuple{Complex{&lt;:AbstractFloat}, RoundingMode, RoundingMode}" href="#Base.round-Tuple{Complex{&lt;:AbstractFloat}, RoundingMode, RoundingMode}"><code>Base.round</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">round(z::Complex[, RoundingModeReal, [RoundingModeImaginary]])
round(z::Complex[, RoundingModeReal, [RoundingModeImaginary]]; digits=0, base=10)
round(z::Complex[, RoundingModeReal, [RoundingModeImaginary]]; sigdigits, base=10)</code></pre><p>Return the nearest integral value of the same type as the complex-valued <code>z</code> to <code>z</code>, breaking ties using the specified <a href="math.html#Base.Rounding.RoundingMode"><code>RoundingMode</code></a>s. The first <a href="math.html#Base.Rounding.RoundingMode"><code>RoundingMode</code></a> is used for rounding the real components while the second is used for rounding the imaginary components.</p><p><code>RoundingModeReal</code> and <code>RoundingModeImaginary</code> default to <a href="math.html#Base.Rounding.RoundNearest"><code>RoundNearest</code></a>, which rounds to the nearest integer, with ties (fractional values of 0.5) being rounded to the nearest even integer.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; round(3.14 + 4.5im)
3.0 + 4.0im

julia&gt; round(3.14 + 4.5im, RoundUp, RoundNearestTiesUp)
4.0 + 5.0im

julia&gt; round(3.14159 + 4.512im; digits = 1)
3.1 + 4.5im

julia&gt; round(3.14159 + 4.512im; sigdigits = 3)
3.14 + 4.51im</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/complex.jl#L1082-L1111">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.ceil" href="#Base.ceil"><code>Base.ceil</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ceil([T,] x)
ceil(x; digits::Integer= [, base = 10])
ceil(x; sigdigits::Integer= [, base = 10])</code></pre><p><code>ceil(x)</code> returns the nearest integral value of the same type as <code>x</code> that is greater than or equal to <code>x</code>.</p><p><code>ceil(T, x)</code> converts the result to type <code>T</code>, throwing an <code>InexactError</code> if the ceiled value is not representable as a <code>T</code>.</p><p>Keywords <code>digits</code>, <code>sigdigits</code> and <code>base</code> work as for <a href="math.html#Base.round"><code>round</code></a>.</p><p>To support <code>ceil</code> for a new type, define <code>Base.round(x::NewType, ::RoundingMode{:Up})</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rounding.jl#L452-L466">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.floor" href="#Base.floor"><code>Base.floor</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">floor([T,] x)
floor(x; digits::Integer= [, base = 10])
floor(x; sigdigits::Integer= [, base = 10])</code></pre><p><code>floor(x)</code> returns the nearest integral value of the same type as <code>x</code> that is less than or equal to <code>x</code>.</p><p><code>floor(T, x)</code> converts the result to type <code>T</code>, throwing an <code>InexactError</code> if the floored value is not representable a <code>T</code>.</p><p>Keywords <code>digits</code>, <code>sigdigits</code> and <code>base</code> work as for <a href="math.html#Base.round"><code>round</code></a>.</p><p>To support <code>floor</code> for a new type, define <code>Base.round(x::NewType, ::RoundingMode{:Down})</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rounding.jl#L435-L449">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.trunc" href="#Base.trunc"><code>Base.trunc</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">trunc([T,] x)
trunc(x; digits::Integer= [, base = 10])
trunc(x; sigdigits::Integer= [, base = 10])</code></pre><p><code>trunc(x)</code> returns the nearest integral value of the same type as <code>x</code> whose absolute value is less than or equal to the absolute value of <code>x</code>.</p><p><code>trunc(T, x)</code> converts the result to type <code>T</code>, throwing an <code>InexactError</code> if the truncated value is not representable a <code>T</code>.</p><p>Keywords <code>digits</code>, <code>sigdigits</code> and <code>base</code> work as for <a href="math.html#Base.round"><code>round</code></a>.</p><p>To support <code>trunc</code> for a new type, define <code>Base.round(x::NewType, ::RoundingMode{:ToZero})</code>.</p><p>See also: <a href="math.html#Base.rem"><code>%</code></a>, <a href="math.html#Base.floor"><code>floor</code></a>, <a href="numbers.html#Base.unsigned"><code>unsigned</code></a>, <a href="math.html#Base.unsafe_trunc"><code>unsafe_trunc</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; trunc(2.22)
2.0

julia&gt; trunc(-2.22, digits=1)
-2.2

julia&gt; trunc(Int, -2.22)
-2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rounding.jl#L404-L432">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unsafe_trunc" href="#Base.unsafe_trunc"><code>Base.unsafe_trunc</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unsafe_trunc(T, x)</code></pre><p>Return the nearest integral value of type <code>T</code> whose absolute value is less than or equal to the absolute value of <code>x</code>. If the value is not representable by <code>T</code>, an arbitrary value will be returned. See also <a href="math.html#Base.trunc"><code>trunc</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; unsafe_trunc(Int, -2.2)
-2

julia&gt; unsafe_trunc(Int, NaN)
-9223372036854775808</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/float.jl#L395-L411">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.min" href="#Base.min"><code>Base.min</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">min(x, y, ...)</code></pre><p>Return the minimum of the arguments, with respect to <a href="base.html#Base.isless"><code>isless</code></a>. If any of the arguments is <a href="../manual/missing.html#missing"><code>missing</code></a>, return <code>missing</code>. See also the <a href="collections.html#Base.minimum"><code>minimum</code></a> function to take the minimum element from a collection.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; min(2, 5, 1)
1

julia&gt; min(4, missing, 6)
missing</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L483-L498">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.max" href="#Base.max"><code>Base.max</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">max(x, y, ...)</code></pre><p>Return the maximum of the arguments, with respect to <a href="base.html#Base.isless"><code>isless</code></a>. If any of the arguments is <a href="../manual/missing.html#missing"><code>missing</code></a>, return <code>missing</code>. See also the <a href="collections.html#Base.maximum"><code>maximum</code></a> function to take the maximum element from a collection.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; max(2, 5, 1)
5

julia&gt; max(5, missing, 6)
missing</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L465-L480">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.minmax" href="#Base.minmax"><code>Base.minmax</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">minmax(x, y)</code></pre><p>Return <code>(min(x,y), max(x,y))</code>.</p><p>See also <a href="collections.html#Base.extrema"><code>extrema</code></a> that returns <code>(minimum(x), maximum(x))</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; minmax(&#39;c&#39;,&#39;b&#39;)
(&#39;b&#39;, &#39;c&#39;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/operators.jl#L501-L513">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.clamp" href="#Base.clamp"><code>Base.clamp</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">clamp(x, lo, hi)</code></pre><p>Return <code>x</code> if <code>lo &lt;= x &lt;= hi</code>. If <code>x &gt; hi</code>, return <code>hi</code>. If <code>x &lt; lo</code>, return <code>lo</code>. Arguments are promoted to a common type.</p><p>See also <a href="math.html#Base.clamp!"><code>clamp!</code></a>, <a href="math.html#Base.min"><code>min</code></a>, <a href="math.html#Base.max"><code>max</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p><code>missing</code> as the first argument requires at least Julia 1.3.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; clamp.([pi, 1.0, big(10)], 2.0, 9.0)
3-element Vector{BigFloat}:
 3.141592653589793238462643383279502884197169399375105820974944592307816406286198
 2.0
 9.0

julia&gt; clamp.([11, 8, 5], 10, 6)  # an example where lo &gt; hi
3-element Vector{Int64}:
  6
  6
 10</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L1240-L1265">source</a></section><section><div><pre><code class="language-julia hljs">clamp(x, T)::T</code></pre><p>Clamp <code>x</code> between <code>typemin(T)</code> and <code>typemax(T)</code> and convert the result to type <code>T</code>.</p><p>See also <a href="math.html#Base.trunc"><code>trunc</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; clamp(200, Int8)
127

julia&gt; clamp(-200, Int8)
-128

julia&gt; trunc(Int, 4pi^2)
39</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L1271-L1289">source</a></section><section><div><pre><code class="language-julia hljs">clamp(x::Integer, r::AbstractUnitRange)</code></pre><p>Clamp <code>x</code> to lie within range <code>r</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>This method requires at least Julia 1.6.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L1329-L1336">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.clamp!" href="#Base.clamp!"><code>Base.clamp!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">clamp!(array::AbstractArray, lo, hi)</code></pre><p>Restrict values in <code>array</code> to the specified range, in-place. See also <a href="math.html#Base.clamp"><code>clamp</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p><code>missing</code> entries in <code>array</code> require at least Julia 1.3.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; row = collect(-4:4)&#39;;

julia&gt; clamp!(row, 0, Inf)
1×9 adjoint(::Vector{Int64}) with eltype Int64:
 0  0  0  0  0  1  2  3  4

julia&gt; clamp.((-4:4)&#39;, 0, Inf)
1×9 Matrix{Float64}:
 0.0  0.0  0.0  0.0  0.0  1.0  2.0  3.0  4.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L1300-L1321">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.abs" href="#Base.abs"><code>Base.abs</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">abs(x)</code></pre><p>The absolute value of <code>x</code>.</p><p>When <code>abs</code> is applied to signed integers, overflow may occur, resulting in the return of a negative value. This overflow occurs only when <code>abs</code> is applied to the minimum representable value of a signed integer. That is, when <code>x == typemin(typeof(x))</code>, <code>abs(x) == x &lt; 0</code>, not <code>-x</code> as might be expected.</p><p>See also: <a href="math.html#Base.abs2"><code>abs2</code></a>, <a href="numbers.html#Base.unsigned"><code>unsigned</code></a>, <a href="math.html#Base.sign"><code>sign</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; abs(-3)
3

julia&gt; abs(1 + im)
1.4142135623730951

julia&gt; abs.(Int8[-128 -127 -126 0 126 127])  # overflow at typemin(Int8)
1×6 Matrix{Int8}:
 -128  127  126  0  126  127

julia&gt; maximum(abs, [1, -2, 3, -4])
4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L156-L184">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Checked" href="#Base.Checked"><code>Base.Checked</code></a> — <span class="docstring-category">Module</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Checked</code></pre><p>The Checked module provides arithmetic functions for the built-in signed and unsigned Integer types which throw an error when an overflow occurs. They are named like <code>checked_sub</code>, <code>checked_div</code>, etc. In addition, <code>add_with_overflow</code>, <code>sub_with_overflow</code>, <code>mul_with_overflow</code> return both the unchecked results and a boolean value denoting the presence of an overflow.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/checked.jl#L5-L12">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Checked.checked_abs" href="#Base.Checked.checked_abs"><code>Base.Checked.checked_abs</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.checked_abs(x)</code></pre><p>Calculates <code>abs(x)</code>, checking for overflow errors where applicable. For example, standard two&#39;s complement signed integers (e.g. <code>Int</code>) cannot represent <code>abs(typemin(Int))</code>, thus leading to an overflow.</p><p>The overflow protection may impose a perceptible performance penalty.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/checked.jl#L113-L121">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Checked.checked_neg" href="#Base.Checked.checked_neg"><code>Base.Checked.checked_neg</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.checked_neg(x)</code></pre><p>Calculates <code>-x</code>, checking for overflow errors where applicable. For example, standard two&#39;s complement signed integers (e.g. <code>Int</code>) cannot represent <code>-typemin(Int)</code>, thus leading to an overflow.</p><p>The overflow protection may impose a perceptible performance penalty.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/checked.jl#L85-L93">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Checked.checked_add" href="#Base.Checked.checked_add"><code>Base.Checked.checked_add</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.checked_add(x, y)</code></pre><p>Calculates <code>x+y</code>, checking for overflow errors where applicable.</p><p>The overflow protection may impose a perceptible performance penalty.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/checked.jl#L165-L171">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Checked.checked_sub" href="#Base.Checked.checked_sub"><code>Base.Checked.checked_sub</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.checked_sub(x, y)</code></pre><p>Calculates <code>x-y</code>, checking for overflow errors where applicable.</p><p>The overflow protection may impose a perceptible performance penalty.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/checked.jl#L222-L228">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Checked.checked_mul" href="#Base.Checked.checked_mul"><code>Base.Checked.checked_mul</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.checked_mul(x, y)</code></pre><p>Calculates <code>x*y</code>, checking for overflow errors where applicable.</p><p>The overflow protection may impose a perceptible performance penalty.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/checked.jl#L287-L293">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Checked.checked_div" href="#Base.Checked.checked_div"><code>Base.Checked.checked_div</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.checked_div(x, y)</code></pre><p>Calculates <code>div(x,y)</code>, checking for overflow errors where applicable.</p><p>The overflow protection may impose a perceptible performance penalty.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/checked.jl#L316-L322">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Checked.checked_rem" href="#Base.Checked.checked_rem"><code>Base.Checked.checked_rem</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.checked_rem(x, y)</code></pre><p>Calculates <code>x%y</code>, checking for overflow errors where applicable.</p><p>The overflow protection may impose a perceptible performance penalty.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/checked.jl#L325-L331">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Checked.checked_fld" href="#Base.Checked.checked_fld"><code>Base.Checked.checked_fld</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.checked_fld(x, y)</code></pre><p>Calculates <code>fld(x,y)</code>, checking for overflow errors where applicable.</p><p>The overflow protection may impose a perceptible performance penalty.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/checked.jl#L334-L340">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Checked.checked_mod" href="#Base.Checked.checked_mod"><code>Base.Checked.checked_mod</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.checked_mod(x, y)</code></pre><p>Calculates <code>mod(x,y)</code>, checking for overflow errors where applicable.</p><p>The overflow protection may impose a perceptible performance penalty.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/checked.jl#L343-L349">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Checked.checked_cld" href="#Base.Checked.checked_cld"><code>Base.Checked.checked_cld</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.checked_cld(x, y)</code></pre><p>Calculates <code>cld(x,y)</code>, checking for overflow errors where applicable.</p><p>The overflow protection may impose a perceptible performance penalty.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/checked.jl#L352-L358">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Checked.checked_pow" href="#Base.Checked.checked_pow"><code>Base.Checked.checked_pow</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.checked_pow(x, y)</code></pre><p>Calculates <code>^(x,y)</code>, checking for overflow errors where applicable.</p><p>The overflow protection may impose a perceptible performance penalty.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/checked.jl#L361-L367">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Checked.add_with_overflow" href="#Base.Checked.add_with_overflow"><code>Base.Checked.add_with_overflow</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.add_with_overflow(x, y) -&gt; (r, f)</code></pre><p>Calculates <code>r = x+y</code>, with the flag <code>f</code> indicating whether overflow has occurred.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/checked.jl#L135-L139">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Checked.sub_with_overflow" href="#Base.Checked.sub_with_overflow"><code>Base.Checked.sub_with_overflow</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.sub_with_overflow(x, y) -&gt; (r, f)</code></pre><p>Calculates <code>r = x-y</code>, with the flag <code>f</code> indicating whether overflow has occurred.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/checked.jl#L197-L201">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Checked.mul_with_overflow" href="#Base.Checked.mul_with_overflow"><code>Base.Checked.mul_with_overflow</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.mul_with_overflow(x, y) -&gt; (r, f)</code></pre><p>Calculates <code>r = x*y</code>, with the flag <code>f</code> indicating whether overflow has occurred.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/checked.jl#L237-L241">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.abs2" href="#Base.abs2"><code>Base.abs2</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">abs2(x)</code></pre><p>Squared absolute value of <code>x</code>.</p><p>This can be faster than <code>abs(x)^2</code>, especially for complex numbers where <code>abs(x)</code> requires a square root via <a href="math.html#Base.Math.hypot"><code>hypot</code></a>.</p><p>See also <a href="math.html#Base.abs"><code>abs</code></a>, <a href="math.html#Base.conj"><code>conj</code></a>, <a href="math.html#Base.real"><code>real</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; abs2(-3)
9

julia&gt; abs2(3.0 + 4.0im)
25.0

julia&gt; sum(abs2, [1+2im, 3+4im])  # LinearAlgebra.norm(x)^2
30</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/number.jl#L166-L187">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.copysign" href="#Base.copysign"><code>Base.copysign</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">copysign(x, y) -&gt; z</code></pre><p>Return <code>z</code> which has the magnitude of <code>x</code> and the same sign as <code>y</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; copysign(1, -2)
-1

julia&gt; copysign(-1, 2)
1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/number.jl#L207-L220">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.sign" href="#Base.sign"><code>Base.sign</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sign(x)</code></pre><p>Return zero if <code>x==0</code> and <span>$x/|x|$</span> otherwise (i.e., ±1 for real <code>x</code>).</p><p>See also <a href="math.html#Base.signbit"><code>signbit</code></a>, <a href="numbers.html#Base.zero"><code>zero</code></a>, <a href="math.html#Base.copysign"><code>copysign</code></a>, <a href="math.html#Base.flipsign"><code>flipsign</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; sign(-4.0)
-1.0

julia&gt; sign(99)
1

julia&gt; sign(-0.0)
-0.0

julia&gt; sign(0 + im)
0.0 + 1.0im</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/number.jl#L139-L160">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.signbit" href="#Base.signbit"><code>Base.signbit</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">signbit(x)</code></pre><p>Return <code>true</code> if the value of the sign of <code>x</code> is negative, otherwise <code>false</code>.</p><p>See also <a href="math.html#Base.sign"><code>sign</code></a> and <a href="math.html#Base.copysign"><code>copysign</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; signbit(-4)
true

julia&gt; signbit(5)
false

julia&gt; signbit(5.5)
false

julia&gt; signbit(-4.1)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/number.jl#L115-L136">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.flipsign" href="#Base.flipsign"><code>Base.flipsign</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">flipsign(x, y)</code></pre><p>Return <code>x</code> with its sign flipped if <code>y</code> is negative. For example <code>abs(x) = flipsign(x,x)</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; flipsign(5, 3)
5

julia&gt; flipsign(5, -3)
-5</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/number.jl#L191-L204">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.sqrt-Tuple{Number}" href="#Base.sqrt-Tuple{Number}"><code>Base.sqrt</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sqrt(x)</code></pre><p>Return <span>$\sqrt{x}$</span>.</p><p>Throws <a href="base.html#Core.DomainError"><code>DomainError</code></a> for negative <a href="numbers.html#Core.Real"><code>Real</code></a> arguments. Use complex negative arguments instead. Note that <code>sqrt</code> has a branch cut along the negative real axis.</p><p>The prefix operator <code>√</code> is equivalent to <code>sqrt</code>.</p><p>See also: <a href="math.html#Base.Math.hypot"><code>hypot</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; sqrt(big(81))
9.0

julia&gt; sqrt(big(-81))
ERROR: DomainError with -81.0:
NaN result for non-NaN input.
Stacktrace:
 [1] sqrt(::BigFloat) at ./mpfr.jl:501
[...]

julia&gt; sqrt(big(complex(-81)))
0.0 + 9.0im

julia&gt; sqrt(-81 - 0.0im)  # -0.0im is below the branch cut
0.0 - 9.0im

julia&gt; .√(1:4)
4-element Vector{Float64}:
 1.0
 1.4142135623730951
 1.7320508075688772
 2.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L612-L650">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isqrt" href="#Base.isqrt"><code>Base.isqrt</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isqrt(n::Integer)</code></pre><p>Integer square root: the largest integer <code>m</code> such that <code>m*m &lt;= n</code>.</p><pre><code class="language-julia-repl hljs">julia&gt; isqrt(5)
2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L1090-L1099">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.cbrt-Tuple{AbstractFloat}" href="#Base.Math.cbrt-Tuple{AbstractFloat}"><code>Base.Math.cbrt</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cbrt(x::Real)</code></pre><p>Return the cube root of <code>x</code>, i.e. <span>$x^{1/3}$</span>. Negative values are accepted (returning the negative real root when <span>$x &lt; 0$</span>).</p><p>The prefix operator <code>∛</code> is equivalent to <code>cbrt</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; cbrt(big(27))
3.0

julia&gt; cbrt(big(-27))
-3.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/special/cbrt.jl#L17-L33">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.real" href="#Base.real"><code>Base.real</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">real(z)</code></pre><p>Return the real part of the complex number <code>z</code>.</p><p>See also: <a href="math.html#Base.imag"><code>imag</code></a>, <a href="math.html#Base.reim"><code>reim</code></a>, <a href="numbers.html#Base.complex-Tuple{Complex}"><code>complex</code></a>, <a href="numbers.html#Base.isreal"><code>isreal</code></a>, <a href="numbers.html#Core.Real"><code>Real</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; real(1 + 3im)
1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/complex.jl#L59-L71">source</a></section><section><div><pre><code class="language-julia hljs">real(T::Type)</code></pre><p>Return the type that represents the real part of a value of type <code>T</code>. e.g: for <code>T == Complex{R}</code>, returns <code>R</code>. Equivalent to <code>typeof(real(zero(T)))</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; real(Complex{Int})
Int64

julia&gt; real(Float64)
Float64</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/complex.jl#L104-L119">source</a></section><section><div><pre><code class="language-julia hljs">real(A::AbstractArray)</code></pre><p>Return an array containing the real part of each entry in array <code>A</code>.</p><p>Equivalent to <code>real.(A)</code>, except that when <code>eltype(A) &lt;: Real</code> <code>A</code> is returned without copying, and that when <code>A</code> has zero dimensions, a 0-dimensional array is returned (rather than a scalar).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; real([1, 2im, 3 + 4im])
3-element Vector{Int64}:
 1
 0
 3

julia&gt; real(fill(2 - im))
0-dimensional Array{Int64, 0}:
2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarraymath.jl#L149-L170">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.imag" href="#Base.imag"><code>Base.imag</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">imag(z)</code></pre><p>Return the imaginary part of the complex number <code>z</code>.</p><p>See also: <a href="math.html#Base.conj"><code>conj</code></a>, <a href="math.html#Base.reim"><code>reim</code></a>, <a href="../stdlib/LinearAlgebra.html#Base.adjoint"><code>adjoint</code></a>, <a href="math.html#Base.angle"><code>angle</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; imag(1 + 3im)
3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/complex.jl#L74-L86">source</a></section><section><div><pre><code class="language-julia hljs">imag(A::AbstractArray)</code></pre><p>Return an array containing the imaginary part of each entry in array <code>A</code>.</p><p>Equivalent to <code>imag.(A)</code>, except that when <code>A</code> has zero dimensions, a 0-dimensional array is returned (rather than a scalar).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; imag([1, 2im, 3 + 4im])
3-element Vector{Int64}:
 0
 2
 4

julia&gt; imag(fill(2 - im))
0-dimensional Array{Int64, 0}:
-1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarraymath.jl#L174-L194">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.reim" href="#Base.reim"><code>Base.reim</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">reim(z)</code></pre><p>Return a tuple of the real and imaginary parts of the complex number <code>z</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; reim(1 + 3im)
(1, 3)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/complex.jl#L91-L101">source</a></section><section><div><pre><code class="language-julia hljs">reim(A::AbstractArray)</code></pre><p>Return a tuple of two arrays containing respectively the real and the imaginary part of each entry in <code>A</code>.</p><p>Equivalent to <code>(real.(A), imag.(A))</code>, except that when <code>eltype(A) &lt;: Real</code> <code>A</code> is returned without copying to represent the real part, and that when <code>A</code> has zero dimensions, a 0-dimensional array is returned (rather than a scalar).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; reim([1, 2im, 3 + 4im])
([1, 0, 3], [0, 2, 4])

julia&gt; reim(fill(2 - im))
(fill(2), fill(-1))</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarraymath.jl#L198-L216">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.conj" href="#Base.conj"><code>Base.conj</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">conj(z)</code></pre><p>Compute the complex conjugate of a complex number <code>z</code>.</p><p>See also: <a href="math.html#Base.angle"><code>angle</code></a>, <a href="../stdlib/LinearAlgebra.html#Base.adjoint"><code>adjoint</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; conj(1 + 3im)
1 - 3im</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/complex.jl#L272-L284">source</a></section><section><div><pre><code class="language-julia hljs">conj(A::AbstractArray)</code></pre><p>Return an array containing the complex conjugate of each entry in array <code>A</code>.</p><p>Equivalent to <code>conj.(A)</code>, except that when <code>eltype(A) &lt;: Real</code> <code>A</code> is returned without copying, and that when <code>A</code> has zero dimensions, a 0-dimensional array is returned (rather than a scalar).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; conj([1, 2im, 3 + 4im])
3-element Vector{Complex{Int64}}:
 1 + 0im
 0 - 2im
 3 - 4im

julia&gt; conj(fill(2 - im))
0-dimensional Array{Complex{Int64}, 0}:
2 + 1im</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/abstractarraymath.jl#L124-L145">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.angle" href="#Base.angle"><code>Base.angle</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">angle(z)</code></pre><p>Compute the phase angle in radians of a complex number <code>z</code>.</p><p>Returns a number <code>-pi ≤ angle(z) ≤ pi</code>, and is thus discontinuous along the negative real axis.</p><p>See also: <a href="math.html#Base.atan-Tuple{Number}"><code>atan</code></a>, <a href="math.html#Base.cis"><code>cis</code></a>, <a href="math.html#Base.Math.rad2deg"><code>rad2deg</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; rad2deg(angle(1 + im))
45.0

julia&gt; rad2deg(angle(1 - im))
-45.0

julia&gt; rad2deg(angle(-1 + 1e-20im))
180.0

julia&gt; rad2deg(angle(-1 - 1e-20im))
-180.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/complex.jl#L623-L647">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.cis" href="#Base.cis"><code>Base.cis</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cis(x)</code></pre><p>More efficient method for <code>exp(im*x)</code> by using Euler&#39;s formula: <span>$\cos(x) + i \sin(x) = \exp(i x)$</span>.</p><p>See also <a href="math.html#Base.cispi"><code>cispi</code></a>, <a href="math.html#Base.Math.sincos-Tuple{Float64}"><code>sincos</code></a>, <a href="math.html#Base.exp-Tuple{Float64}"><code>exp</code></a>, <a href="math.html#Base.angle"><code>angle</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; cis(π) ≈ -1
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/complex.jl#L570-L582">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.cispi" href="#Base.cispi"><code>Base.cispi</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cispi(x)</code></pre><p>More accurate method for <code>cis(pi*x)</code> (especially for large <code>x</code>).</p><p>See also <a href="math.html#Base.cis"><code>cis</code></a>, <a href="math.html#Base.Math.sincospi"><code>sincospi</code></a>, <a href="math.html#Base.exp-Tuple{Float64}"><code>exp</code></a>, <a href="math.html#Base.angle"><code>angle</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; cispi(10000)
1.0 + 0.0im

julia&gt; cispi(0.25 + 1im)
0.030556854645954562 + 0.03055685464595456im</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>This function requires Julia 1.6 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/complex.jl#L595-L613">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.binomial" href="#Base.binomial"><code>Base.binomial</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">binomial(n::Integer, k::Integer)</code></pre><p>The <em>binomial coefficient</em> <span>$\binom{n}{k}$</span>, being the coefficient of the <span>$k$</span>th term in the polynomial expansion of <span>$(1+x)^n$</span>.</p><p>If <span>$n$</span> is non-negative, then it is the number of ways to choose <code>k</code> out of <code>n</code> items:</p><p class="math-container">\[\binom{n}{k} = \frac{n!}{k! (n-k)!}\]</p><p>where <span>$n!$</span> is the <a href="math.html#Base.factorial"><code>factorial</code></a> function.</p><p>If <span>$n$</span> is negative, then it is defined in terms of the identity</p><p class="math-container">\[\binom{n}{k} = (-1)^k \binom{k-n-1}{k}\]</p><p>See also <a href="math.html#Base.factorial"><code>factorial</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; binomial(5, 3)
10

julia&gt; factorial(5) ÷ (factorial(5-3) * factorial(3))
10

julia&gt; binomial(-5, 3)
-35</code></pre><p><strong>External links</strong></p><ul><li><a href="https://en.wikipedia.org/wiki/Binomial_coefficient">Binomial coefficient</a> on Wikipedia.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L1146-L1179">source</a></section><section><div><pre><code class="language-julia hljs">binomial(x::Number, k::Integer)</code></pre><p>The generalized binomial coefficient, defined for <code>k ≥ 0</code> by the polynomial</p><p class="math-container">\[\frac{1}{k!} \prod_{j=0}^{k-1} (x - j)\]</p><p>When <code>k &lt; 0</code> it returns zero.</p><p>For the case of integer <code>x</code>, this is equivalent to the ordinary integer binomial coefficient</p><p class="math-container">\[\binom{n}{k} = \frac{n!}{k! (n-k)!}\]</p><p>Further generalizations to non-integer <code>k</code> are mathematically possible, but involve the Gamma function and/or the beta function, which are not provided by the Julia standard library but are available in external packages such as <a href="https://github.com/JuliaMath/SpecialFunctions.jl">SpecialFunctions.jl</a>.</p><p><strong>External links</strong></p><ul><li><a href="https://en.wikipedia.org/wiki/Binomial_coefficient">Binomial coefficient</a> on Wikipedia.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L1209-L1232">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.factorial" href="#Base.factorial"><code>Base.factorial</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">factorial(n::Integer)</code></pre><p>Factorial of <code>n</code>. If <code>n</code> is an <a href="numbers.html#Core.Integer"><code>Integer</code></a>, the factorial is computed as an integer (promoted to at least 64 bits). Note that this may overflow if <code>n</code> is not small, but you can use <code>factorial(big(n))</code> to compute the result exactly in arbitrary precision.</p><p>See also <a href="math.html#Base.binomial"><code>binomial</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; factorial(6)
720

julia&gt; factorial(21)
ERROR: OverflowError: 21 is too large to look up in the table; consider using `factorial(big(21))` instead
Stacktrace:
[...]

julia&gt; factorial(big(21))
51090942171709440000</code></pre><p><strong>External links</strong></p><ul><li><a href="https://en.wikipedia.org/wiki/Factorial">Factorial</a> on Wikipedia.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L1111-L1136">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.gcd" href="#Base.gcd"><code>Base.gcd</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">gcd(x, y...)</code></pre><p>Greatest common (positive) divisor (or zero if all arguments are zero). The arguments may be integer and rational numbers.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.4</header><div class="admonition-body"><p>Rational arguments require Julia 1.4 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; gcd(6, 9)
3

julia&gt; gcd(6, -9)
3

julia&gt; gcd(6, 0)
6

julia&gt; gcd(0, 0)
0

julia&gt; gcd(1//3, 2//3)
1//3

julia&gt; gcd(1//3, -2//3)
1//3

julia&gt; gcd(1//3, 2)
1//3

julia&gt; gcd(0, 0, 10, 15)
5</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L5-L40">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.lcm" href="#Base.lcm"><code>Base.lcm</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lcm(x, y...)</code></pre><p>Least common (positive) multiple (or zero if any argument is zero). The arguments may be integer and rational numbers.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.4</header><div class="admonition-body"><p>Rational arguments require Julia 1.4 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; lcm(2, 3)
6

julia&gt; lcm(-2, 3)
6

julia&gt; lcm(0, 3)
0

julia&gt; lcm(0, 0)
0

julia&gt; lcm(1//3, 2//3)
2//3

julia&gt; lcm(1//3, -2//3)
2//3

julia&gt; lcm(1//3, 2)
2//1

julia&gt; lcm(1, 3, 5, 7)
105</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L94-L129">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.gcdx" href="#Base.gcdx"><code>Base.gcdx</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">gcdx(a, b)</code></pre><p>Computes the greatest common (positive) divisor of <code>a</code> and <code>b</code> and their Bézout coefficients, i.e. the integer coefficients <code>u</code> and <code>v</code> that satisfy <span>$ua+vb = d = gcd(a, b)$</span>. <span>$gcdx(a, b)$</span> returns <span>$(d, u, v)$</span>.</p><p>The arguments may be integer and rational numbers.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.4</header><div class="admonition-body"><p>Rational arguments require Julia 1.4 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; gcdx(12, 42)
(6, -3, 1)

julia&gt; gcdx(240, 46)
(2, -9, 47)</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Bézout coefficients are <em>not</em> uniquely defined. <code>gcdx</code> returns the minimal Bézout coefficients that are computed by the extended Euclidean algorithm. (Ref: D. Knuth, TAoCP, 2/e, p. 325, Algorithm X.) For signed integers, these coefficients <code>u</code> and <code>v</code> are minimal in the sense that <span>$|u| &lt; |b/d|$</span> and <span>$|v| &lt; |a/d|$</span>. Furthermore, the signs of <code>u</code> and <code>v</code> are chosen so that <code>d</code> is positive. For unsigned integers, the coefficients <code>u</code> and <code>v</code> might be near their <code>typemax</code>, and the identity then holds only via the unsigned integers&#39; modulo arithmetic.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L167-L198">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.ispow2" href="#Base.ispow2"><code>Base.ispow2</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ispow2(n::Number) -&gt; Bool</code></pre><p>Test whether <code>n</code> is an integer power of two.</p><p>See also <a href="numbers.html#Base.count_ones"><code>count_ones</code></a>, <a href="math.html#Base.prevpow"><code>prevpow</code></a>, <a href="math.html#Base.nextpow"><code>nextpow</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; ispow2(4)
true

julia&gt; ispow2(5)
false

julia&gt; ispow2(4.5)
false

julia&gt; ispow2(0.25)
true

julia&gt; ispow2(1//8)
true</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>Support for non-<code>Integer</code> arguments was added in Julia 1.6.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L463-L490">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.nextpow" href="#Base.nextpow"><code>Base.nextpow</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">nextpow(a, x)</code></pre><p>The smallest <code>a^n</code> not less than <code>x</code>, where <code>n</code> is a non-negative integer. <code>a</code> must be greater than 1, and <code>x</code> must be greater than 0.</p><p>See also <a href="math.html#Base.prevpow"><code>prevpow</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; nextpow(2, 7)
8

julia&gt; nextpow(2, 9)
16

julia&gt; nextpow(5, 20)
25

julia&gt; nextpow(4, 16)
16</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L495-L517">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.prevpow" href="#Base.prevpow"><code>Base.prevpow</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">prevpow(a, x)</code></pre><p>The largest <code>a^n</code> not greater than <code>x</code>, where <code>n</code> is a non-negative integer. <code>a</code> must be greater than 1, and <code>x</code> must not be less than 1.</p><p>See also <a href="math.html#Base.nextpow"><code>nextpow</code></a>, <a href="math.html#Base.isqrt"><code>isqrt</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; prevpow(2, 7)
4

julia&gt; prevpow(2, 9)
8

julia&gt; prevpow(5, 20)
5

julia&gt; prevpow(4, 16)
16</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L540-L562">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.nextprod" href="#Base.nextprod"><code>Base.nextprod</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">nextprod(factors::Union{Tuple,AbstractVector}, n)</code></pre><p>Next integer greater than or equal to <code>n</code> that can be written as <span>$\prod k_i^{p_i}$</span> for integers <span>$p_1$</span>, <span>$p_2$</span>, etcetera, for factors <span>$k_i$</span> in <code>factors</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; nextprod((2, 3), 105)
108

julia&gt; 2^2 * 3^3
108</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>The method that accepts a tuple requires Julia 1.6 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/combinatorics.jl#L313-L330">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.invmod" href="#Base.invmod"><code>Base.invmod</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">invmod(n::Integer, m::Integer)</code></pre><p>Take the inverse of <code>n</code> modulo <code>m</code>: <code>y</code> such that <span>$n y = 1 \pmod m$</span>, and <span>$div(y,m) = 0$</span>. This will throw an error if <span>$m = 0$</span>, or if <span>$gcd(n,m) \neq 1$</span>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; invmod(2, 5)
3

julia&gt; invmod(2, 3)
2

julia&gt; invmod(5, 6)
5</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L220-L238">source</a></section><section><div><pre><code class="language-julia hljs">invmod(n::Integer, T) where {T &lt;: Base.BitInteger}
invmod(n::T) where {T &lt;: Base.BitInteger}</code></pre><p>Compute the modular inverse of <code>n</code> in the integer ring of type <code>T</code>, i.e. modulo <code>2^N</code> where <code>N = 8*sizeof(T)</code> (e.g. <code>N = 32</code> for <code>Int32</code>). In other words these methods satisfy the following identities:</p><pre><code class="nohighlight hljs">n * invmod(n) == 1
(n * invmod(n, T)) % T == 1
(n % T) * invmod(n, T) == 1</code></pre><p>Note that <code>*</code> here is modular multiplication in the integer ring, <code>T</code>.</p><p>Specifying the modulus implied by an integer type as an explicit value is often inconvenient since the modulus is by definition too big to be represented by the type.</p><p>The modular inverse is computed much more efficiently than the general case using the algorithm described in https://arxiv.org/pdf/2204.04342.pdf.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>The <code>invmod(n)</code> and <code>invmod(n, T)</code> methods require Julia 1.11 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L260-L283">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.powermod" href="#Base.powermod"><code>Base.powermod</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">powermod(x::Integer, p::Integer, m)</code></pre><p>Compute <span>$x^p \pmod m$</span>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; powermod(2, 6, 5)
4

julia&gt; mod(2^6, 5)
4

julia&gt; powermod(5, 2, 20)
5

julia&gt; powermod(5, 2, 19)
6

julia&gt; powermod(5, 3, 19)
11</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L399-L421">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.ndigits" href="#Base.ndigits"><code>Base.ndigits</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ndigits(n::Integer; base::Integer=10, pad::Integer=1)</code></pre><p>Compute the number of digits in integer <code>n</code> written in base <code>base</code> (<code>base</code> must not be in <code>[-1, 0, 1]</code>), optionally padded with zeros to a specified size (the result will never be less than <code>pad</code>).</p><p>See also <a href="numbers.html#Base.digits"><code>digits</code></a>, <a href="numbers.html#Base.count_ones"><code>count_ones</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; ndigits(0)
1

julia&gt; ndigits(12345)
5

julia&gt; ndigits(1022, base=16)
3

julia&gt; string(1022, base=16)
&quot;3fe&quot;

julia&gt; ndigits(123, pad=5)
5

julia&gt; ndigits(-123)
3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L710-L739">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.add_sum" href="#Base.add_sum"><code>Base.add_sum</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Base.add_sum(x, y)</code></pre><p>The reduction operator used in <code>sum</code>. The main difference from <a href="math.html#Base.:+"><code>+</code></a> is that small integers are promoted to <code>Int</code>/<code>UInt</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/reduce.jl#L18-L23">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.widemul" href="#Base.widemul"><code>Base.widemul</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">widemul(x, y)</code></pre><p>Multiply <code>x</code> and <code>y</code>, giving the result as a larger type.</p><p>See also <a href="base.html#Base.promote"><code>promote</code></a>, <a href="math.html#Base.add_sum"><code>Base.add_sum</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; widemul(Float32(3.0), 4.0) isa BigFloat
true

julia&gt; typemax(Int8) * typemax(Int8)
1

julia&gt; widemul(typemax(Int8), typemax(Int8))  # == 127^2
16129</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/number.jl#L258-L276">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.evalpoly" href="#Base.Math.evalpoly"><code>Base.Math.evalpoly</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">evalpoly(x, p)</code></pre><p>Evaluate the polynomial <span>$\sum_k x^{k-1} p[k]$</span> for the coefficients <code>p[1]</code>, <code>p[2]</code>, ...; that is, the coefficients are given in ascending order by power of <code>x</code>. Loops are unrolled at compile time if the number of coefficients is statically known, i.e. when <code>p</code> is a <code>Tuple</code>. This function generates efficient code using Horner&#39;s method if <code>x</code> is real, or using a Goertzel-like <sup class="footnote-reference"><a id="citeref-DK62" href="#footnote-DK62">[DK62]</a></sup> algorithm if <code>x</code> is complex.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.4</header><div class="admonition-body"><p>This function requires Julia 1.4 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; evalpoly(2, (1, 2, 3))
17</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L73-L93">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.@evalpoly" href="#Base.Math.@evalpoly"><code>Base.Math.@evalpoly</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@evalpoly(z, c...)</code></pre><p>Evaluate the polynomial <span>$\sum_k z^{k-1} c[k]$</span> for the coefficients <code>c[1]</code>, <code>c[2]</code>, ...; that is, the coefficients are given in ascending order by power of <code>z</code>.  This macro expands to efficient inline code that uses either Horner&#39;s method or, for complex <code>z</code>, a more efficient Goertzel-like algorithm.</p><p>See also <a href="math.html#Base.Math.evalpoly"><code>evalpoly</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; @evalpoly(3, 1, 0, 1)
10

julia&gt; @evalpoly(2, 1, 0, 1)
5

julia&gt; @evalpoly(2, 1, 1, 1)
7</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L186-L207">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.FastMath.@fastmath" href="#Base.FastMath.@fastmath"><code>Base.FastMath.@fastmath</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@fastmath expr</code></pre><p>Execute a transformed version of the expression, which calls functions that may violate strict IEEE semantics. This allows the fastest possible operation, but results are undefined – be careful when doing this, as it may change numerical results.</p><p>This sets the <a href="https://llvm.org/docs/LangRef.html#fast-math-flags">LLVM Fast-Math flags</a>, and corresponds to the <code>-ffast-math</code> option in clang. See <a href="../manual/performance-tips.html#man-performance-annotations">the notes on performance annotations</a> for more details.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; @fastmath 1+2
3

julia&gt; @fastmath(sin(3))
0.1411200080598672</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/fastmath.jl#L131-L151">source</a></section></article><h2 id="Customizable-binary-operators"><a class="docs-heading-anchor" href="#Customizable-binary-operators">Customizable binary operators</a><a id="Customizable-binary-operators-1"></a><a class="docs-heading-anchor-permalink" href="#Customizable-binary-operators" title="Permalink"></a></h2><p>Some unicode characters can be used to define new binary operators that support infix notation. For example <code>⊗(x,y) = kron(x,y)</code> defines the <code>⊗</code> (otimes) function to be the Kronecker product, and one can call it as binary operator using infix syntax: <code>C = A ⊗ B</code> as well as with the usual prefix syntax <code>C = ⊗(A,B)</code>.</p><p>Other characters that support such extensions include \odot <code>⊙</code> and \oplus <code>⊕</code></p><p>The complete list is in the parser code: <a href="https://github.com/JuliaLang/julia/blob/master/src/julia-parser.scm">https://github.com/JuliaLang/julia/blob/master/src/julia-parser.scm</a></p><p>Those that are parsed like <code>*</code> (in terms of precedence) include <code>* / ÷ % &amp; ⋅ ∘ × |\\| ∩ ∧ ⊗ ⊘ ⊙ ⊚ ⊛ ⊠ ⊡ ⊓ ∗ ∙ ∤ ⅋ ≀ ⊼ ⋄ ⋆ ⋇ ⋉ ⋊ ⋋ ⋌ ⋏ ⋒ ⟑ ⦸ ⦼ ⦾ ⦿ ⧶ ⧷ ⨇ ⨰ ⨱ ⨲ ⨳ ⨴ ⨵ ⨶ ⨷ ⨸ ⨻ ⨼ ⨽ ⩀ ⩃ ⩄ ⩋ ⩍ ⩎ ⩑ ⩓ ⩕ ⩘ ⩚ ⩜ ⩞ ⩟ ⩠ ⫛ ⊍ ▷ ⨝ ⟕ ⟖ ⟗</code> and those that are parsed like <code>+</code> include <code>+ - |\|| ⊕ ⊖ ⊞ ⊟ |++| ∪ ∨ ⊔ ± ∓ ∔ ∸ ≏ ⊎ ⊻ ⊽ ⋎ ⋓ ⟇ ⧺ ⧻ ⨈ ⨢ ⨣ ⨤ ⨥ ⨦ ⨧ ⨨ ⨩ ⨪ ⨫ ⨬ ⨭ ⨮ ⨹ ⨺ ⩁ ⩂ ⩅ ⩊ ⩌ ⩏ ⩐ ⩒ ⩔ ⩖ ⩗ ⩛ ⩝ ⩡ ⩢ ⩣</code> There are many others that are related to arrows, comparisons, and powers.</p><section class="footnotes is-size-7"><ul><li class="footnote" id="footnote-DK62"><a class="tag is-link" href="#citeref-DK62">DK62</a>Donald Knuth, Art of Computer Programming, Volume 2: Seminumerical Algorithms, Sec. 4.6.4.</li></ul></section></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="collections.html">« Collections and Data Structures</a><a class="docs-footer-nextpage" href="numbers.html">Numbers »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
