<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Numbers · The Julia Language</title><meta name="title" content="Numbers · The Julia Language"/><meta property="og:title" content="Numbers · The Julia Language"/><meta property="twitter:title" content="Numbers · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox" checked/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="base.html">Essentials</a></li><li><a class="tocitem" href="collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="math.html">Mathematics</a></li><li class="is-active"><a class="tocitem" href="numbers.html">Numbers</a><ul class="internal"><li><a class="tocitem" href="#Standard-Numeric-Types"><span>Standard Numeric Types</span></a></li><li><a class="tocitem" href="#Data-Formats"><span>Data Formats</span></a></li><li><a class="tocitem" href="#General-Number-Functions-and-Constants"><span>General Number Functions and Constants</span></a></li><li><a class="tocitem" href="#BigFloats-and-BigInts"><span>BigFloats and BigInts</span></a></li></ul></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="arrays.html">Arrays</a></li><li><a class="tocitem" href="parallel.html">Tasks</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="constants.html">Constants</a></li><li><a class="tocitem" href="file.html">Filesystem</a></li><li><a class="tocitem" href="io-network.html">I/O and Network</a></li><li><a class="tocitem" href="punctuation.html">Punctuation</a></li><li><a class="tocitem" href="sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="c.html">C Interface</a></li><li><a class="tocitem" href="libc.html">C Standard Library</a></li><li><a class="tocitem" href="stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Base</a></li><li class="is-active"><a href="numbers.html">Numbers</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="numbers.html">Numbers</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/base/numbers.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="lib-numbers"><a class="docs-heading-anchor" href="#lib-numbers">Numbers</a><a id="lib-numbers-1"></a><a class="docs-heading-anchor-permalink" href="#lib-numbers" title="Permalink"></a></h1><h2 id="Standard-Numeric-Types"><a class="docs-heading-anchor" href="#Standard-Numeric-Types">Standard Numeric Types</a><a id="Standard-Numeric-Types-1"></a><a class="docs-heading-anchor-permalink" href="#Standard-Numeric-Types" title="Permalink"></a></h2><p>A type tree for all subtypes of <code>Number</code> in <code>Base</code> is shown below. Abstract types have been marked, the rest are concrete types.</p><pre><code class="nohighlight hljs">Number  (Abstract Type)
├─ Complex
└─ Real  (Abstract Type)
   ├─ AbstractFloat  (Abstract Type)
   │  ├─ Float16
   │  ├─ Float32
   │  ├─ Float64
   │  └─ BigFloat
   ├─ Integer  (Abstract Type)
   │  ├─ Bool
   │  ├─ Signed  (Abstract Type)
   │  │  ├─ Int8
   │  │  ├─ Int16
   │  │  ├─ Int32
   │  │  ├─ Int64
   │  │  ├─ Int128
   │  │  └─ BigInt
   │  └─ Unsigned  (Abstract Type)
   │     ├─ UInt8
   │     ├─ UInt16
   │     ├─ UInt32
   │     ├─ UInt64
   │     └─ UInt128
   ├─ Rational
   └─ AbstractIrrational  (Abstract Type)
      └─ Irrational</code></pre><h3 id="Abstract-number-types"><a class="docs-heading-anchor" href="#Abstract-number-types">Abstract number types</a><a id="Abstract-number-types-1"></a><a class="docs-heading-anchor-permalink" href="#Abstract-number-types" title="Permalink"></a></h3><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Number" href="#Core.Number"><code>Core.Number</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Number</code></pre><p>Abstract supertype for all number types.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2035-L2039">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Real" href="#Core.Real"><code>Core.Real</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Real &lt;: Number</code></pre><p>Abstract supertype for all real numbers.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2042-L2046">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.AbstractFloat" href="#Core.AbstractFloat"><code>Core.AbstractFloat</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AbstractFloat &lt;: Real</code></pre><p>Abstract supertype for all floating point numbers.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2049-L2053">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Integer" href="#Core.Integer"><code>Core.Integer</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Integer &lt;: Real</code></pre><p>Abstract supertype for all integers (e.g. <a href="numbers.html#Core.Signed"><code>Signed</code></a>, <a href="numbers.html#Core.Unsigned"><code>Unsigned</code></a>, and <a href="numbers.html#Core.Bool"><code>Bool</code></a>).</p><p>See also <a href="numbers.html#Base.isinteger"><code>isinteger</code></a>, <a href="math.html#Base.trunc"><code>trunc</code></a>, <a href="math.html#Base.div"><code>div</code></a>.</p><p><strong>Examples</strong></p><pre><code class="nohighlight hljs">julia&gt; 42 isa Integer
true

julia&gt; 1.0 isa Integer
false

julia&gt; isinteger(1.0)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2056-L2074">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Signed" href="#Core.Signed"><code>Core.Signed</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Signed &lt;: Integer</code></pre><p>Abstract supertype for all signed integers.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2077-L2081">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Unsigned" href="#Core.Unsigned"><code>Core.Unsigned</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Unsigned &lt;: Integer</code></pre><p>Abstract supertype for all unsigned integers.</p><p>Built-in unsigned integers are printed in hexadecimal, with prefix <code>0x</code>, and can be entered in the same way.</p><p><strong>Examples</strong></p><pre><code class="nohighlight hljs">julia&gt; typemax(UInt8)
0xff

julia&gt; Int(0x00d)
13

julia&gt; unsigned(true)
0x0000000000000001</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2084-L2103">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.AbstractIrrational" href="#Base.AbstractIrrational"><code>Base.AbstractIrrational</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AbstractIrrational &lt;: Real</code></pre><p>Number type representing an exact irrational value, which is automatically rounded to the correct precision in arithmetic operations with other numeric quantities.</p><p>Subtypes <code>MyIrrational &lt;: AbstractIrrational</code> should implement at least <code>==(::MyIrrational, ::MyIrrational)</code>, <code>hash(x::MyIrrational, h::UInt)</code>, and <code>convert(::Type{F}, x::MyIrrational) where {F &lt;: Union{BigFloat,Float32,Float64}}</code>.</p><p>If a subtype is used to represent values that may occasionally be rational (e.g. a square-root type that represents <code>√n</code> for integers <code>n</code> will give a rational result when <code>n</code> is a perfect square), then it should also implement <code>isinteger</code>, <code>iszero</code>, <code>isone</code>, and <code>==</code> with <code>Real</code> values (since all of these default to <code>false</code> for <code>AbstractIrrational</code> types), as well as defining <a href="base.html#Base.hash"><code>hash</code></a> to equal that of the corresponding <code>Rational</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/irrationals.jl#L5-L18">source</a></section></article><h3 id="Concrete-number-types"><a class="docs-heading-anchor" href="#Concrete-number-types">Concrete number types</a><a id="Concrete-number-types-1"></a><a class="docs-heading-anchor-permalink" href="#Concrete-number-types" title="Permalink"></a></h3><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Float16" href="#Core.Float16"><code>Core.Float16</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Float16 &lt;: AbstractFloat &lt;: Real</code></pre><p>16-bit floating point number type (IEEE 754 standard). Binary format is 1 sign, 5 exponent, 10 fraction bits.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2185-L2190">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Float32" href="#Core.Float32"><code>Core.Float32</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Float32 &lt;: AbstractFloat &lt;: Real</code></pre><p>32-bit floating point number type (IEEE 754 standard). Binary format is 1 sign, 8 exponent, 23 fraction bits.</p><p>The exponent for scientific notation should be entered as lower-case <code>f</code>, thus <code>2f3 === 2.0f0 * 10^3 === Float32(2_000)</code>. For array literals and comprehensions, the element type can be specified before the square brackets: <code>Float32[1,4,9] == Float32[i^2 for i in 1:3]</code>.</p><p>See also <a href="numbers.html#Base.Inf32"><code>Inf32</code></a>, <a href="numbers.html#Base.NaN32"><code>NaN32</code></a>, <a href="numbers.html#Core.Float16"><code>Float16</code></a>, <a href="numbers.html#Base.Math.exponent"><code>exponent</code></a>, <a href="math.html#Base.Math.frexp"><code>frexp</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2170-L2182">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Float64" href="#Core.Float64"><code>Core.Float64</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Float64 &lt;: AbstractFloat &lt;: Real</code></pre><p>64-bit floating point number type (IEEE 754 standard). Binary format is 1 sign, 11 exponent, 52 fraction bits. See <a href="numbers.html#Base.bitstring"><code>bitstring</code></a>, <a href="math.html#Base.signbit"><code>signbit</code></a>, <a href="numbers.html#Base.Math.exponent"><code>exponent</code></a>, <a href="math.html#Base.Math.frexp"><code>frexp</code></a>, and <a href="numbers.html#Base.Math.significand"><code>significand</code></a> to access various bits.</p><p>This is the default for floating point literals, <code>1.0 isa Float64</code>, and for many operations such as <code>1/2, 2pi, log(2), range(0,90,length=4)</code>. Unlike integers, this default does not change with <code>Sys.WORD_SIZE</code>.</p><p>The exponent for scientific notation can be entered as <code>e</code> or <code>E</code>, thus <code>2e3 === 2.0E3 === 2.0 * 10^3</code>. Doing so is strongly preferred over <code>10^n</code> because integers overflow, thus <code>2.0 * 10^19 &lt; 0</code> but <code>2e19 &gt; 0</code>.</p><p>See also <a href="numbers.html#Base.Inf"><code>Inf</code></a>, <a href="numbers.html#Base.NaN"><code>NaN</code></a>, <a href="base.html#Base.floatmax"><code>floatmax</code></a>, <a href="numbers.html#Core.Float32"><code>Float32</code></a>, <a href="numbers.html#Base.Complex"><code>Complex</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2150-L2167">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.MPFR.BigFloat" href="#Base.MPFR.BigFloat"><code>Base.MPFR.BigFloat</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">BigFloat &lt;: AbstractFloat</code></pre><p>Arbitrary precision floating point number type.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/mpfr.jl#L122-L126">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Bool" href="#Core.Bool"><code>Core.Bool</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Bool &lt;: Integer</code></pre><p>Boolean type, containing the values <code>true</code> and <code>false</code>.</p><p><code>Bool</code> is a kind of number: <code>false</code> is numerically equal to <code>0</code> and <code>true</code> is numerically equal to <code>1</code>. Moreover, <code>false</code> acts as a multiplicative &quot;strong zero&quot; against <a href="numbers.html#Base.NaN"><code>NaN</code></a> and <a href="numbers.html#Base.Inf"><code>Inf</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; [true, false] == [1, 0]
true

julia&gt; 42.0 + true
43.0

julia&gt; 0 .* (NaN, Inf, -Inf)
(NaN, NaN, NaN)

julia&gt; false .* (NaN, Inf, -Inf)
(0.0, 0.0, -0.0)</code></pre><p>Branches via <a href="base.html#if"><code>if</code></a> and other conditionals only accept <code>Bool</code>. There are no &quot;truthy&quot; values in Julia.</p><p>Comparisons typically return <code>Bool</code>, and broadcasted comparisons may return <a href="arrays.html#Base.BitArray"><code>BitArray</code></a> instead of an <code>Array{Bool}</code>.</p><pre><code class="language-julia-repl hljs">julia&gt; [1 2 3 4 5] .&lt; pi
1×5 BitMatrix:
 1  1  1  0  0

julia&gt; map(&gt;(pi), [1 2 3 4 5])
1×5 Matrix{Bool}:
 0  0  0  1  1</code></pre><p>See also <a href="arrays.html#Base.trues"><code>trues</code></a>, <a href="arrays.html#Base.falses"><code>falses</code></a>, <a href="base.html#Base.ifelse"><code>ifelse</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2106-L2147">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Int8" href="#Core.Int8"><code>Core.Int8</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Int8 &lt;: Signed &lt;: Integer</code></pre><p>8-bit signed integer type.</p><p>Represents numbers <code>n ∈ -128:127</code>. Note that such integers overflow without warning, thus <code>typemax(Int8) + Int8(1) &lt; 0</code>.</p><p>See also <a href="numbers.html#Core.Int64"><code>Int</code></a>, <a href="base.html#Base.widen"><code>widen</code></a>, <a href="numbers.html#Base.GMP.BigInt"><code>BigInt</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2199-L2208">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.UInt8" href="#Core.UInt8"><code>Core.UInt8</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">UInt8 &lt;: Unsigned &lt;: Integer</code></pre><p>8-bit unsigned integer type.</p><p>Printed in hexadecimal, thus 0x07 == 7.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2211-L2217">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Int16" href="#Core.Int16"><code>Core.Int16</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Int16 &lt;: Signed &lt;: Integer</code></pre><p>16-bit signed integer type.</p><p>Represents numbers <code>n ∈ -32768:32767</code>. Note that such integers overflow without warning, thus <code>typemax(Int16) + Int16(1) &lt; 0</code>.</p><p>See also <a href="numbers.html#Core.Int64"><code>Int</code></a>, <a href="base.html#Base.widen"><code>widen</code></a>, <a href="numbers.html#Base.GMP.BigInt"><code>BigInt</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2199-L2208">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.UInt16" href="#Core.UInt16"><code>Core.UInt16</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">UInt16 &lt;: Unsigned &lt;: Integer</code></pre><p>16-bit unsigned integer type.</p><p>Printed in hexadecimal, thus 0x000f == 15.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2211-L2217">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Int32" href="#Core.Int32"><code>Core.Int32</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Int32 &lt;: Signed &lt;: Integer</code></pre><p>32-bit signed integer type.</p><p>Note that such integers overflow without warning, thus <code>typemax(Int32) + Int32(1) &lt; 0</code>.</p><p>See also <a href="numbers.html#Core.Int64"><code>Int</code></a>, <a href="base.html#Base.widen"><code>widen</code></a>, <a href="numbers.html#Base.GMP.BigInt"><code>BigInt</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2199-L2208">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.UInt32" href="#Core.UInt32"><code>Core.UInt32</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">UInt32 &lt;: Unsigned &lt;: Integer</code></pre><p>32-bit unsigned integer type.</p><p>Printed in hexadecimal, thus 0x0000001f == 31.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2211-L2217">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Int64" href="#Core.Int64"><code>Core.Int64</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Int64 &lt;: Signed &lt;: Integer</code></pre><p>64-bit signed integer type.</p><p>Note that such integers overflow without warning, thus <code>typemax(Int64) + Int64(1) &lt; 0</code>.</p><p>See also <a href="numbers.html#Core.Int64"><code>Int</code></a>, <a href="base.html#Base.widen"><code>widen</code></a>, <a href="numbers.html#Base.GMP.BigInt"><code>BigInt</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2199-L2208">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.UInt64" href="#Core.UInt64"><code>Core.UInt64</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">UInt64 &lt;: Unsigned &lt;: Integer</code></pre><p>64-bit unsigned integer type.</p><p>Printed in hexadecimal, thus 0x000000000000003f == 63.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2211-L2217">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Int128" href="#Core.Int128"><code>Core.Int128</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Int128 &lt;: Signed &lt;: Integer</code></pre><p>128-bit signed integer type.</p><p>Note that such integers overflow without warning, thus <code>typemax(Int128) + Int128(1) &lt; 0</code>.</p><p>See also <a href="numbers.html#Core.Int64"><code>Int</code></a>, <a href="base.html#Base.widen"><code>widen</code></a>, <a href="numbers.html#Base.GMP.BigInt"><code>BigInt</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2199-L2208">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.UInt128" href="#Core.UInt128"><code>Core.UInt128</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">UInt128 &lt;: Unsigned &lt;: Integer</code></pre><p>128-bit unsigned integer type.</p><p>Printed in hexadecimal, thus 0x0000000000000000000000000000007f == 127.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2211-L2217">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Int" href="#Core.Int"><code>Core.Int</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Int</code></pre><p>Sys.WORD_SIZE-bit signed integer type, <code>Int &lt;: Signed &lt;: Integer &lt;: Real</code>.</p><p>This is the default type of most integer literals and is an alias for either <code>Int32</code> or <code>Int64</code>, depending on <code>Sys.WORD_SIZE</code>. It is the type returned by functions such as <a href="collections.html#Base.length"><code>length</code></a>, and the standard type for indexing arrays.</p><p>Note that integers overflow without warning, thus <code>typemax(Int) + 1 &lt; 0</code> and <code>10^19 &lt; 0</code>. Overflow can be avoided by using <a href="numbers.html#Base.GMP.BigInt"><code>BigInt</code></a>. Very large integer literals will use a wider type, for instance <code>10_000_000_000_000_000_000 isa Int128</code>.</p><p>Integer division is <a href="math.html#Base.div"><code>div</code></a> alias <code>÷</code>, whereas <a href="math.html#Base.:/"><code>/</code></a> acting on integers returns <a href="numbers.html#Core.Float64"><code>Float64</code></a>.</p><p>See also <a href="numbers.html#Core.Int64"><code>Int64</code></a>, <a href="base.html#Base.widen"><code>widen</code></a>, <a href="base.html#Base.typemax"><code>typemax</code></a>, <a href="numbers.html#Base.bitstring"><code>bitstring</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2222-L2239">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.UInt" href="#Core.UInt"><code>Core.UInt</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">UInt</code></pre><p>Sys.WORD_SIZE-bit unsigned integer type, <code>UInt &lt;: Unsigned &lt;: Integer</code>.</p><p>Like <a href="numbers.html#Core.Int"><code>Int</code></a>, the alias <code>UInt</code> may point to either <code>UInt32</code> or <code>UInt64</code>, according to the value of <code>Sys.WORD_SIZE</code> on a given computer.</p><p>Printed and parsed in hexadecimal: <code>UInt(15) === 0x000000000000000f</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L2242-L2251">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.GMP.BigInt" href="#Base.GMP.BigInt"><code>Base.GMP.BigInt</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">BigInt &lt;: Signed</code></pre><p>Arbitrary precision integer type.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/gmp.jl#L53-L57">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Complex" href="#Base.Complex"><code>Base.Complex</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Complex{T&lt;:Real} &lt;: Number</code></pre><p>Complex number type with real and imaginary part of type <code>T</code>.</p><p><code>ComplexF16</code>, <code>ComplexF32</code> and <code>ComplexF64</code> are aliases for <code>Complex{Float16}</code>, <code>Complex{Float32}</code> and <code>Complex{Float64}</code> respectively.</p><p>See also: <a href="numbers.html#Core.Real"><code>Real</code></a>, <a href="numbers.html#Base.complex-Tuple{Complex}"><code>complex</code></a>, <a href="math.html#Base.real"><code>real</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/complex.jl#L3-L12">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Rational" href="#Base.Rational"><code>Base.Rational</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Rational{T&lt;:Integer} &lt;: Real</code></pre><p>Rational number type, with numerator and denominator of type <code>T</code>. Rationals are checked for overflow.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rational.jl#L3-L8">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Irrational" href="#Base.Irrational"><code>Base.Irrational</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Irrational{sym} &lt;: AbstractIrrational</code></pre><p>Number type representing an exact irrational value denoted by the symbol <code>sym</code>, such as <a href="numbers.html#Base.MathConstants.pi"><code>π</code></a>, <a href="numbers.html#Base.MathConstants.ℯ"><code>ℯ</code></a> and <a href="numbers.html#Base.MathConstants.eulergamma"><code>γ</code></a>.</p><p>See also <a href="numbers.html#Base.AbstractIrrational"><code>AbstractIrrational</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/irrationals.jl#L21-L28">source</a></section></article><h2 id="Data-Formats"><a class="docs-heading-anchor" href="#Data-Formats">Data Formats</a><a id="Data-Formats-1"></a><a class="docs-heading-anchor-permalink" href="#Data-Formats" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.digits" href="#Base.digits"><code>Base.digits</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">digits([T&lt;:Integer], n::Integer; base::T = 10, pad::Integer = 1)</code></pre><p>Return an array with element type <code>T</code> (default <code>Int</code>) of the digits of <code>n</code> in the given base, optionally padded with zeros to a specified size. More significant digits are at higher indices, such that <code>n == sum(digits[k]*base^(k-1) for k in eachindex(digits))</code>.</p><p>See also <a href="math.html#Base.ndigits"><code>ndigits</code></a>, <a href="numbers.html#Base.digits!"><code>digits!</code></a>, and for base 2 also <a href="numbers.html#Base.bitstring"><code>bitstring</code></a>, <a href="numbers.html#Base.count_ones"><code>count_ones</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; digits(10)
2-element Vector{Int64}:
 0
 1

julia&gt; digits(10, base = 2)
4-element Vector{Int64}:
 0
 1
 0
 1

julia&gt; digits(-256, base = 10, pad = 5)
5-element Vector{Int64}:
 -6
 -5
 -2
  0
  0

julia&gt; n = rand(-999:999);

julia&gt; n == evalpoly(13, digits(n, base = 13))
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L976-L1013">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.digits!" href="#Base.digits!"><code>Base.digits!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">digits!(array, n::Integer; base::Integer = 10)</code></pre><p>Fills an array of the digits of <code>n</code> in the given base. More significant digits are at higher indices. If the array length is insufficient, the least significant digits are filled up to the array length. If the array length is excessive, the excess portion is filled with zeros.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; digits!([2, 2, 2, 2], 10, base = 2)
4-element Vector{Int64}:
 0
 1
 0
 1

julia&gt; digits!([2, 2, 2, 2, 2, 2], 10, base = 2)
6-element Vector{Int64}:
 0
 1
 0
 1
 0
 0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L1030-L1055">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.bitstring" href="#Base.bitstring"><code>Base.bitstring</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">bitstring(n)</code></pre><p>A string giving the literal bit representation of a primitive type.</p><p>See also <a href="numbers.html#Base.count_ones"><code>count_ones</code></a>, <a href="numbers.html#Base.count_zeros"><code>count_zeros</code></a>, <a href="numbers.html#Base.digits"><code>digits</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; bitstring(Int32(4))
&quot;00000000000000000000000000000100&quot;

julia&gt; bitstring(2.2)
&quot;0100000000000001100110011001100110011001100110011001100110011010&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/intfuncs.jl#L942-L957">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.parse" href="#Base.parse"><code>Base.parse</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">parse(::Type{SimpleColor}, rgb::String)</code></pre><p>An analogue of <code>tryparse(SimpleColor, rgb::String)</code> (which see), that raises an error instead of returning <code>nothing</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/StyledStrings.jl/blob/056e843b2d428bb9735b03af0cff97e738ac7e14/src/faces.jl#L73-L78">source</a></section><section><div><pre><code class="language-julia hljs">parse(::Type{Platform}, triplet::AbstractString)</code></pre><p>Parses a string platform triplet back into a <code>Platform</code> object.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/binaryplatforms.jl#L687-L691">source</a></section><section><div><pre><code class="language-julia hljs">parse(type, str; base)</code></pre><p>Parse a string as a number. For <code>Integer</code> types, a base can be specified (the default is 10). For floating-point types, the string is parsed as a decimal floating-point number.  <code>Complex</code> types are parsed from decimal strings of the form <code>&quot;R±Iim&quot;</code> as a <code>Complex(R,I)</code> of the requested type; <code>&quot;i&quot;</code> or <code>&quot;j&quot;</code> can also be used instead of <code>&quot;im&quot;</code>, and <code>&quot;R&quot;</code> or <code>&quot;Iim&quot;</code> are also permitted. If the string does not contain a valid number, an error is raised.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p><code>parse(Bool, str)</code> requires at least Julia 1.1.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; parse(Int, &quot;1234&quot;)
1234

julia&gt; parse(Int, &quot;1234&quot;, base = 5)
194

julia&gt; parse(Int, &quot;afc&quot;, base = 16)
2812

julia&gt; parse(Float64, &quot;1.2e-3&quot;)
0.0012

julia&gt; parse(Complex{Float64}, &quot;3.2e-1 + 4.5im&quot;)
0.32 + 4.5im</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/parse.jl#L7-L37">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.tryparse" href="#Base.tryparse"><code>Base.tryparse</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">tryparse(::Type{SimpleColor}, rgb::String)</code></pre><p>Attempt to parse <code>rgb</code> as a <code>SimpleColor</code>. If <code>rgb</code> starts with <code>#</code> and has a length of 7, it is converted into a <code>RGBTuple</code>-backed <code>SimpleColor</code>. If <code>rgb</code> starts with <code>a</code>-<code>z</code>, <code>rgb</code> is interpreted as a color name and converted to a <code>Symbol</code>-backed <code>SimpleColor</code>.</p><p>Otherwise, <code>nothing</code> is returned.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; tryparse(SimpleColor, &quot;blue&quot;)
SimpleColor(blue)

julia&gt; tryparse(SimpleColor, &quot;#9558b2&quot;)
SimpleColor(#9558b2)

julia&gt; tryparse(SimpleColor, &quot;#nocolor&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/StyledStrings.jl/blob/056e843b2d428bb9735b03af0cff97e738ac7e14/src/faces.jl#L38-L59">source</a></section><section><div><pre><code class="language-julia hljs">tryparse(type, str; base)</code></pre><p>Like <a href="numbers.html#Base.parse"><code>parse</code></a>, but returns either a value of the requested type, or <a href="constants.html#Core.nothing"><code>nothing</code></a> if the string does not contain a valid number.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/parse.jl#L242-L247">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.big" href="#Base.big"><code>Base.big</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">big(x)</code></pre><p>Convert a number to a maximum precision representation (typically <a href="numbers.html#Base.GMP.BigInt"><code>BigInt</code></a> or <code>BigFloat</code>). See <a href="numbers.html#Base.MPFR.BigFloat-Tuple{Any, RoundingMode}"><code>BigFloat</code></a> for information about some pitfalls with floating-point numbers.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/gmp.jl#L476-L482">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.signed" href="#Base.signed"><code>Base.signed</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">signed(T::Integer)</code></pre><p>Convert an integer bitstype to the signed type of the same size.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; signed(UInt16)
Int16
julia&gt; signed(UInt64)
Int64</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L61-L72">source</a></section><section><div><pre><code class="language-julia hljs">signed(x)</code></pre><p>Convert a number to a signed integer. If the argument is unsigned, it is reinterpreted as signed without checking for overflow.</p><p>See also: <a href="numbers.html#Base.unsigned"><code>unsigned</code></a>, <a href="math.html#Base.sign"><code>sign</code></a>, <a href="math.html#Base.signbit"><code>signbit</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L218-L225">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unsigned" href="#Base.unsigned"><code>Base.unsigned</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unsigned(T::Integer)</code></pre><p>Convert an integer bitstype to the unsigned type of the same size.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; unsigned(Int16)
UInt16
julia&gt; unsigned(UInt64)
UInt64</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L48-L59">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.float-Tuple{Any}" href="#Base.float-Tuple{Any}"><code>Base.float</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">float(x)</code></pre><p>Convert a number or array to a floating point data type.</p><p>See also: <a href="numbers.html#Base.complex-Tuple{Complex}"><code>complex</code></a>, <a href="base.html#Base.oftype"><code>oftype</code></a>, <a href="base.html#Base.convert"><code>convert</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; float(1:1000)
1.0:1.0:1000.0

julia&gt; float(typemax(Int32))
2.147483647e9</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/float.jl#L358-L373">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.significand" href="#Base.Math.significand"><code>Base.Math.significand</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">significand(x)</code></pre><p>Extract the significand (a.k.a. mantissa) of a floating-point number. If <code>x</code> is a non-zero finite number, then the result will be a number of the same type and sign as <code>x</code>, and whose absolute value is on the interval <span>$[1,2)$</span>. Otherwise <code>x</code> is returned.</p><p>See also <a href="math.html#Base.Math.frexp"><code>frexp</code></a>, <a href="numbers.html#Base.Math.exponent"><code>exponent</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; significand(15.2)
1.9

julia&gt; significand(-15.2)
-1.9

julia&gt; significand(-15.2) * 2^3
-15.2

julia&gt; significand(-Inf), significand(Inf), significand(NaN)
(-Inf, Inf, NaN)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L974-L998">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Math.exponent" href="#Base.Math.exponent"><code>Base.Math.exponent</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">exponent(x::Real) -&gt; Int</code></pre><p>Returns the largest integer <code>y</code> such that <code>2^y ≤ abs(x)</code>.</p><p>Throws a <code>DomainError</code> when <code>x</code> is zero, infinite, or <a href="numbers.html#Base.NaN"><code>NaN</code></a>. For any other non-subnormal floating-point number <code>x</code>, this corresponds to the exponent bits of <code>x</code>.</p><p>See also <a href="math.html#Base.signbit"><code>signbit</code></a>, <a href="numbers.html#Base.Math.significand"><code>significand</code></a>, <a href="math.html#Base.Math.frexp"><code>frexp</code></a>, <a href="numbers.html#Base.issubnormal"><code>issubnormal</code></a>, <a href="math.html#Base.log2"><code>log2</code></a>, <a href="math.html#Base.Math.ldexp"><code>ldexp</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; exponent(8)
3

julia&gt; exponent(6.5)
2

julia&gt; exponent(-1//4)
-2

julia&gt; exponent(3.142e-4)
-12

julia&gt; exponent(floatmin(Float32)), exponent(nextfloat(0.0f0))
(-126, -149)

julia&gt; exponent(0.0)
ERROR: DomainError with 0.0:
Cannot be ±0.0.
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/math.jl#L913-L944">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.complex-Tuple{Complex}" href="#Base.complex-Tuple{Complex}"><code>Base.complex</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">complex(r, [i])</code></pre><p>Convert real numbers or arrays to complex. <code>i</code> defaults to zero.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; complex(7)
7 + 0im

julia&gt; complex([1, 2, 3])
3-element Vector{Complex{Int64}}:
 1 + 0im
 2 + 0im
 3 + 0im</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/complex.jl#L156-L172">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.bswap" href="#Base.bswap"><code>Base.bswap</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">bswap(n)</code></pre><p>Reverse the byte order of <code>n</code>.</p><p>(See also <a href="io-network.html#Base.ntoh"><code>ntoh</code></a> and <a href="io-network.html#Base.hton"><code>hton</code></a> to convert between the current native byte order and big-endian order.)</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = bswap(0x10203040)
0x40302010

julia&gt; bswap(a)
0x10203040

julia&gt; string(1, base = 2)
&quot;1&quot;

julia&gt; string(bswap(1), base = 2)
&quot;100000000000000000000000000000000000000000000000000000000&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L375-L396">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.hex2bytes" href="#Base.hex2bytes"><code>Base.hex2bytes</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hex2bytes(itr)</code></pre><p>Given an iterable <code>itr</code> of ASCII codes for a sequence of hexadecimal digits, returns a <code>Vector{UInt8}</code> of bytes  corresponding to the binary representation: each successive pair of hexadecimal digits in <code>itr</code> gives the value of one byte in the return vector.</p><p>The length of <code>itr</code> must be even, and the returned array has half of the length of <code>itr</code>. See also <a href="numbers.html#Base.hex2bytes!"><code>hex2bytes!</code></a> for an in-place version, and <a href="numbers.html#Base.bytes2hex"><code>bytes2hex</code></a> for the inverse.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Calling <code>hex2bytes</code> with iterators producing <code>UInt8</code> values requires Julia 1.7 or later. In earlier versions, you can <code>collect</code> the iterator before calling <code>hex2bytes</code>.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; s = string(12345, base = 16)
&quot;3039&quot;

julia&gt; hex2bytes(s)
2-element Vector{UInt8}:
 0x30
 0x39

julia&gt; a = b&quot;01abEF&quot;
6-element Base.CodeUnits{UInt8, String}:
 0x30
 0x31
 0x61
 0x62
 0x45
 0x46

julia&gt; hex2bytes(a)
3-element Vector{UInt8}:
 0x01
 0xab
 0xef</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L944-L984">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.hex2bytes!" href="#Base.hex2bytes!"><code>Base.hex2bytes!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hex2bytes!(dest::AbstractVector{UInt8}, itr)</code></pre><p>Convert an iterable <code>itr</code> of bytes representing a hexadecimal string to its binary representation, similar to <a href="numbers.html#Base.hex2bytes"><code>hex2bytes</code></a> except that the output is written in-place to <code>dest</code>. The length of <code>dest</code> must be half the length of <code>itr</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Calling hex2bytes! with iterators producing UInt8 requires version 1.7. In earlier versions, you can collect the iterable before calling instead.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L996-L1007">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.bytes2hex" href="#Base.bytes2hex"><code>Base.bytes2hex</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">bytes2hex(itr) -&gt; String
bytes2hex(io::IO, itr)</code></pre><p>Convert an iterator <code>itr</code> of bytes to its hexadecimal string representation, either returning a <code>String</code> via <code>bytes2hex(itr)</code> or writing the string to an <code>io</code> stream via <code>bytes2hex(io, itr)</code>.  The hexadecimal characters are all lowercase.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Calling <code>bytes2hex</code> with arbitrary iterators producing <code>UInt8</code> values requires Julia 1.7 or later. In earlier versions, you can <code>collect</code> the iterator before calling <code>bytes2hex</code>.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a = string(12345, base = 16)
&quot;3039&quot;

julia&gt; b = hex2bytes(a)
2-element Vector{UInt8}:
 0x30
 0x39

julia&gt; bytes2hex(b)
&quot;3039&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/strings/util.jl#L1033-L1059">source</a></section></article><h2 id="General-Number-Functions-and-Constants"><a class="docs-heading-anchor" href="#General-Number-Functions-and-Constants">General Number Functions and Constants</a><a id="General-Number-Functions-and-Constants-1"></a><a class="docs-heading-anchor-permalink" href="#General-Number-Functions-and-Constants" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.one" href="#Base.one"><code>Base.one</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">one(x)
one(T::type)</code></pre><p>Return a multiplicative identity for <code>x</code>: a value such that <code>one(x)*x == x*one(x) == x</code>.  Alternatively <code>one(T)</code> can take a type <code>T</code>, in which case <code>one</code> returns a multiplicative identity for any <code>x</code> of type <code>T</code>.</p><p>If possible, <code>one(x)</code> returns a value of the same type as <code>x</code>, and <code>one(T)</code> returns a value of type <code>T</code>.  However, this may not be the case for types representing dimensionful quantities (e.g. time in days), since the multiplicative identity must be dimensionless.  In that case, <code>one(x)</code> should return an identity value of the same precision (and shape, for matrices) as <code>x</code>.</p><p>If you want a quantity that is of the same type as <code>x</code>, or of type <code>T</code>, even if <code>x</code> is dimensionful, use <a href="numbers.html#Base.oneunit"><code>oneunit</code></a> instead.</p><p>See also the <a href="base.html#Base.identity"><code>identity</code></a> function, and <code>I</code> in <a href="../stdlib/LinearAlgebra.html#man-linalg"><code>LinearAlgebra</code></a> for the identity matrix.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; one(3.7)
1.0

julia&gt; one(Int)
1

julia&gt; import Dates; one(Dates.Day(1))
1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/number.jl#L312-L346">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.oneunit" href="#Base.oneunit"><code>Base.oneunit</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">oneunit(x::T)
oneunit(T::Type)</code></pre><p>Return <code>T(one(x))</code>, where <code>T</code> is either the type of the argument or (if a type is passed) the argument.  This differs from <a href="numbers.html#Base.one"><code>one</code></a> for dimensionful quantities: <code>one</code> is dimensionless (a multiplicative identity) while <code>oneunit</code> is dimensionful (of the same type as <code>x</code>, or of type <code>T</code>).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; oneunit(3.7)
1.0

julia&gt; import Dates; oneunit(Dates.Day)
1 day</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/number.jl#L353-L370">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.zero" href="#Base.zero"><code>Base.zero</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">zero(x)
zero(::Type)</code></pre><p>Get the additive identity element for the type of <code>x</code> (<code>x</code> can also specify the type itself).</p><p>See also <a href="numbers.html#Base.iszero"><code>iszero</code></a>, <a href="numbers.html#Base.one"><code>one</code></a>, <a href="numbers.html#Base.oneunit"><code>oneunit</code></a>, <a href="base.html#Base.oftype"><code>oftype</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; zero(1)
0

julia&gt; zero(big&quot;2.0&quot;)
0.0

julia&gt; zero(rand(2,2))
2×2 Matrix{Float64}:
 0.0  0.0
 0.0  0.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/number.jl#L286-L307">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.im" href="#Base.im"><code>Base.im</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">im</code></pre><p>The imaginary unit.</p><p>See also: <a href="math.html#Base.imag"><code>imag</code></a>, <a href="math.html#Base.angle"><code>angle</code></a>, <a href="numbers.html#Base.complex-Tuple{Complex}"><code>complex</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; im * im
-1 + 0im

julia&gt; (2.0 + 3im)^2
-5.0 + 12.0im</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/complex.jl#L20-L35">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.MathConstants.pi" href="#Base.MathConstants.pi"><code>Base.MathConstants.pi</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">π
pi</code></pre><p>The constant pi.</p><p>Unicode <code>π</code> can be typed by writing <code>\pi</code> then pressing tab in the Julia REPL, and in many editors.</p><p>See also: <a href="math.html#Base.Math.sinpi"><code>sinpi</code></a>, <a href="math.html#Base.Math.sincospi"><code>sincospi</code></a>, <a href="math.html#Base.Math.deg2rad"><code>deg2rad</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; pi
π = 3.1415926535897...

julia&gt; 1/2pi
0.15915494309189535</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/mathconstants.jl#L40-L58">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.MathConstants.ℯ" href="#Base.MathConstants.ℯ"><code>Base.MathConstants.ℯ</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ℯ
e</code></pre><p>The constant ℯ.</p><p>Unicode <code>ℯ</code> can be typed by writing <code>\euler</code> and pressing tab in the Julia REPL, and in many editors.</p><p>See also: <a href="math.html#Base.exp-Tuple{Float64}"><code>exp</code></a>, <a href="math.html#Base.cis"><code>cis</code></a>, <a href="math.html#Base.cispi"><code>cispi</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; ℯ
ℯ = 2.7182818284590...

julia&gt; log(ℯ)
1

julia&gt; ℯ^(im)π ≈ -1
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/mathconstants.jl#L61-L82">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.MathConstants.catalan" href="#Base.MathConstants.catalan"><code>Base.MathConstants.catalan</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">catalan</code></pre><p>Catalan&#39;s constant.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Base.MathConstants.catalan
catalan = 0.9159655941772...

julia&gt; sum(log(x)/(1+x^2) for x in 1:0.01:10^6) * 0.01
0.9159466120554123</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/mathconstants.jl#L121-L134">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.MathConstants.eulergamma" href="#Base.MathConstants.eulergamma"><code>Base.MathConstants.eulergamma</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">γ
eulergamma</code></pre><p>Euler&#39;s constant.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Base.MathConstants.eulergamma
γ = 0.5772156649015...

julia&gt; dx = 10^-6;

julia&gt; sum(-exp(-x) * log(x) for x in dx:dx:100) * dx
0.5772078382499133</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/mathconstants.jl#L85-L101">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.MathConstants.golden" href="#Base.MathConstants.golden"><code>Base.MathConstants.golden</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">φ
golden</code></pre><p>The golden ratio.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Base.MathConstants.golden
φ = 1.6180339887498...

julia&gt; (2ans - 1)^2 ≈ 5
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/mathconstants.jl#L104-L118">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Inf" href="#Base.Inf"><code>Base.Inf</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Inf, Inf64</code></pre><p>Positive infinity of type <a href="numbers.html#Core.Float64"><code>Float64</code></a>.</p><p>See also: <a href="numbers.html#Base.isfinite"><code>isfinite</code></a>, <a href="base.html#Base.typemax"><code>typemax</code></a>, <a href="numbers.html#Base.NaN"><code>NaN</code></a>, <a href="numbers.html#Base.Inf32"><code>Inf32</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; π/0
Inf

julia&gt; +1.0 / -0.0
-Inf

julia&gt; ℯ^-Inf
0.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/float.jl#L39-L57">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Inf64" href="#Base.Inf64"><code>Base.Inf64</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Inf, Inf64</code></pre><p>Positive infinity of type <a href="numbers.html#Core.Float64"><code>Float64</code></a>.</p><p>See also: <a href="numbers.html#Base.isfinite"><code>isfinite</code></a>, <a href="base.html#Base.typemax"><code>typemax</code></a>, <a href="numbers.html#Base.NaN"><code>NaN</code></a>, <a href="numbers.html#Base.Inf32"><code>Inf32</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; π/0
Inf

julia&gt; +1.0 / -0.0
-Inf

julia&gt; ℯ^-Inf
0.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/float.jl#L39-L57">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Inf32" href="#Base.Inf32"><code>Base.Inf32</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Inf32</code></pre><p>Positive infinity of type <a href="numbers.html#Core.Float32"><code>Float32</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/float.jl#L21-L25">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Inf16" href="#Base.Inf16"><code>Base.Inf16</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Inf16</code></pre><p>Positive infinity of type <a href="numbers.html#Core.Float16"><code>Float16</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/float.jl#L7-L11">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.NaN" href="#Base.NaN"><code>Base.NaN</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">NaN, NaN64</code></pre><p>A not-a-number value of type <a href="numbers.html#Core.Float64"><code>Float64</code></a>.</p><p>See also: <a href="numbers.html#Base.isnan"><code>isnan</code></a>, <a href="../manual/missing.html#missing"><code>missing</code></a>, <a href="numbers.html#Base.NaN32"><code>NaN32</code></a>, <a href="numbers.html#Base.Inf"><code>Inf</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; 0/0
NaN

julia&gt; Inf - Inf
NaN

julia&gt; NaN == NaN, isequal(NaN, NaN), isnan(NaN)
(false, true, true)</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Always use <a href="numbers.html#Base.isnan"><code>isnan</code></a> or <a href="base.html#Base.isequal"><code>isequal</code></a> for checking for <code>NaN</code>. Using <code>x === NaN</code> may give unexpected results:</p><pre><code class="language-julia-repl hljs">julia&gt; reinterpret(UInt32, NaN32)
0x7fc00000

julia&gt; NaN32p1 = reinterpret(Float32, 0x7fc00001)
NaN32

julia&gt; NaN32p1 === NaN32, isequal(NaN32p1, NaN32), isnan(NaN32p1)
(false, true, true)</code></pre></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/float.jl#L61-L93">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.NaN64" href="#Base.NaN64"><code>Base.NaN64</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">NaN, NaN64</code></pre><p>A not-a-number value of type <a href="numbers.html#Core.Float64"><code>Float64</code></a>.</p><p>See also: <a href="numbers.html#Base.isnan"><code>isnan</code></a>, <a href="../manual/missing.html#missing"><code>missing</code></a>, <a href="numbers.html#Base.NaN32"><code>NaN32</code></a>, <a href="numbers.html#Base.Inf"><code>Inf</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; 0/0
NaN

julia&gt; Inf - Inf
NaN

julia&gt; NaN == NaN, isequal(NaN, NaN), isnan(NaN)
(false, true, true)</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Always use <a href="numbers.html#Base.isnan"><code>isnan</code></a> or <a href="base.html#Base.isequal"><code>isequal</code></a> for checking for <code>NaN</code>. Using <code>x === NaN</code> may give unexpected results:</p><pre><code class="language-julia-repl hljs">julia&gt; reinterpret(UInt32, NaN32)
0x7fc00000

julia&gt; NaN32p1 = reinterpret(Float32, 0x7fc00001)
NaN32

julia&gt; NaN32p1 === NaN32, isequal(NaN32p1, NaN32), isnan(NaN32p1)
(false, true, true)</code></pre></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/float.jl#L61-L93">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.NaN32" href="#Base.NaN32"><code>Base.NaN32</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">NaN32</code></pre><p>A not-a-number value of type <a href="numbers.html#Core.Float32"><code>Float32</code></a>.</p><p>See also: <a href="numbers.html#Base.NaN"><code>NaN</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/float.jl#L27-L33">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.NaN16" href="#Base.NaN16"><code>Base.NaN16</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">NaN16</code></pre><p>A not-a-number value of type <a href="numbers.html#Core.Float16"><code>Float16</code></a>.</p><p>See also: <a href="numbers.html#Base.NaN"><code>NaN</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/float.jl#L13-L19">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.issubnormal" href="#Base.issubnormal"><code>Base.issubnormal</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">issubnormal(f) -&gt; Bool</code></pre><p>Test whether a floating point number is subnormal.</p><p>An IEEE floating point number is <a href="https://en.wikipedia.org/wiki/Subnormal_number">subnormal</a> when its exponent bits are zero and its significand is not zero.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; floatmin(Float32)
1.1754944f-38

julia&gt; issubnormal(1.0f-37)
false

julia&gt; issubnormal(1.0f-38)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/float.jl#L1002-L1021">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isfinite" href="#Base.isfinite"><code>Base.isfinite</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isfinite(f) -&gt; Bool</code></pre><p>Test whether a number is finite.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isfinite(5)
true

julia&gt; isfinite(NaN32)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/number.jl#L64-L77">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isinf" href="#Base.isinf"><code>Base.isinf</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isinf(f) -&gt; Bool</code></pre><p>Test whether a number is infinite.</p><p>See also: <a href="numbers.html#Base.Inf"><code>Inf</code></a>, <a href="numbers.html#Base.iszero"><code>iszero</code></a>, <a href="numbers.html#Base.isfinite"><code>isfinite</code></a>, <a href="numbers.html#Base.isnan"><code>isnan</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/float.jl#L709-L715">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isnan" href="#Base.isnan"><code>Base.isnan</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isnan(f) -&gt; Bool</code></pre><p>Test whether a number value is a NaN, an indeterminate value which is neither an infinity nor a finite number (&quot;not a number&quot;).</p><p>See also: <a href="numbers.html#Base.iszero"><code>iszero</code></a>, <a href="numbers.html#Base.isone"><code>isone</code></a>, <a href="numbers.html#Base.isinf"><code>isinf</code></a>, <a href="base.html#Base.ismissing"><code>ismissing</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/float.jl#L694-L701">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.iszero" href="#Base.iszero"><code>Base.iszero</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">iszero(x)</code></pre><p>Return <code>true</code> if <code>x == zero(x)</code>; if <code>x</code> is an array, this checks whether all of the elements of <code>x</code> are zero.</p><p>See also: <a href="numbers.html#Base.isone"><code>isone</code></a>, <a href="numbers.html#Base.isinteger"><code>isinteger</code></a>, <a href="numbers.html#Base.isfinite"><code>isfinite</code></a>, <a href="numbers.html#Base.isnan"><code>isnan</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; iszero(0.0)
true

julia&gt; iszero([1, 9, 0])
false

julia&gt; iszero([false, 0, 0])
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/number.jl#L22-L41">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isone" href="#Base.isone"><code>Base.isone</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isone(x)</code></pre><p>Return <code>true</code> if <code>x == one(x)</code>; if <code>x</code> is an array, this checks whether <code>x</code> is an identity matrix.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isone(1.0)
true

julia&gt; isone([1 0; 0 2])
false

julia&gt; isone([1 0; 0 true])
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/number.jl#L44-L61">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.nextfloat" href="#Base.nextfloat"><code>Base.nextfloat</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">nextfloat(x::AbstractFloat, n::Integer)</code></pre><p>The result of <code>n</code> iterative applications of <code>nextfloat</code> to <code>x</code> if <code>n &gt;= 0</code>, or <code>-n</code> applications of <a href="numbers.html#Base.prevfloat"><code>prevfloat</code></a> if <code>n &lt; 0</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/float.jl#L882-L887">source</a></section><section><div><pre><code class="language-julia hljs">nextfloat(x::AbstractFloat)</code></pre><p>Return the smallest floating point number <code>y</code> of the same type as <code>x</code> such <code>x &lt; y</code>. If no such <code>y</code> exists (e.g. if <code>x</code> is <code>Inf</code> or <code>NaN</code>), then return <code>x</code>.</p><p>See also: <a href="numbers.html#Base.prevfloat"><code>prevfloat</code></a>, <a href="base.html#Base.eps-Tuple{Type{&lt;:AbstractFloat}}"><code>eps</code></a>, <a href="numbers.html#Base.issubnormal"><code>issubnormal</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/float.jl#L926-L933">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.prevfloat" href="#Base.prevfloat"><code>Base.prevfloat</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">prevfloat(x::AbstractFloat, n::Integer)</code></pre><p>The result of <code>n</code> iterative applications of <code>prevfloat</code> to <code>x</code> if <code>n &gt;= 0</code>, or <code>-n</code> applications of <a href="numbers.html#Base.nextfloat"><code>nextfloat</code></a> if <code>n &lt; 0</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/float.jl#L936-L941">source</a></section><section><div><pre><code class="language-julia hljs">prevfloat(x::AbstractFloat)</code></pre><p>Return the largest floating point number <code>y</code> of the same type as <code>x</code> such <code>y &lt; x</code>. If no such <code>y</code> exists (e.g. if <code>x</code> is <code>-Inf</code> or <code>NaN</code>), then return <code>x</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/float.jl#L944-L949">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isinteger" href="#Base.isinteger"><code>Base.isinteger</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isinteger(x) -&gt; Bool</code></pre><p>Test whether <code>x</code> is numerically equal to some integer.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isinteger(4.0)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/number.jl#L9-L19">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isreal" href="#Base.isreal"><code>Base.isreal</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isreal(x) -&gt; Bool</code></pre><p>Test whether <code>x</code> or all its elements are numerically equal to some real number including infinities and NaNs. <code>isreal(x)</code> is true if <code>isequal(x, real(x))</code> is true.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isreal(5.)
true

julia&gt; isreal(1 - 3im)
false

julia&gt; isreal(Inf + 0im)
true

julia&gt; isreal([4.; complex(0,1)])
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/complex.jl#L125-L146">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Float32-Tuple{Any}" href="#Core.Float32-Tuple{Any}"><code>Core.Float32</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Float32(x [, mode::RoundingMode])</code></pre><p>Create a <code>Float32</code> from <code>x</code>. If <code>x</code> is not exactly representable then <code>mode</code> determines how <code>x</code> is rounded.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Float32(1/3, RoundDown)
0.3333333f0

julia&gt; Float32(1/3, RoundUp)
0.33333334f0</code></pre><p>See <a href="math.html#Base.Rounding.RoundingMode"><code>RoundingMode</code></a> for available rounding modes.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L1682-L1698">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Float64-Tuple{Any}" href="#Core.Float64-Tuple{Any}"><code>Core.Float64</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Float64(x [, mode::RoundingMode])</code></pre><p>Create a <code>Float64</code> from <code>x</code>. If <code>x</code> is not exactly representable then <code>mode</code> determines how <code>x</code> is rounded.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Float64(pi, RoundDown)
3.141592653589793

julia&gt; Float64(pi, RoundUp)
3.1415926535897936</code></pre><p>See <a href="math.html#Base.Rounding.RoundingMode"><code>RoundingMode</code></a> for available rounding modes.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L1701-L1717">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Rounding.rounding" href="#Base.Rounding.rounding"><code>Base.Rounding.rounding</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rounding(T)</code></pre><p>Get the current floating point rounding mode for type <code>T</code>, controlling the rounding of basic arithmetic functions (<a href="math.html#Base.:+"><code>+</code></a>, <a href="math.html#Base.:--Tuple{Any}"><code>-</code></a>, <a href="math.html#Base.:*-Tuple{Any, Vararg{Any}}"><code>*</code></a>, <a href="math.html#Base.:/"><code>/</code></a> and <a href="math.html#Base.sqrt-Tuple{Number}"><code>sqrt</code></a>) and type conversion.</p><p>See <a href="math.html#Base.Rounding.RoundingMode"><code>RoundingMode</code></a> for available modes.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rounding.jl#L208-L216">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Rounding.setrounding-Tuple{Type, Any}" href="#Base.Rounding.setrounding-Tuple{Type, Any}"><code>Base.Rounding.setrounding</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">setrounding(T, mode)</code></pre><p>Set the rounding mode of floating point type <code>T</code>, controlling the rounding of basic arithmetic functions (<a href="math.html#Base.:+"><code>+</code></a>, <a href="math.html#Base.:--Tuple{Any}"><code>-</code></a>, <a href="math.html#Base.:*-Tuple{Any, Vararg{Any}}"><code>*</code></a>, <a href="math.html#Base.:/"><code>/</code></a> and <a href="math.html#Base.sqrt-Tuple{Number}"><code>sqrt</code></a>) and type conversion. Other numerical functions may give incorrect or invalid values when using rounding modes other than the default <a href="math.html#Base.Rounding.RoundNearest"><code>RoundNearest</code></a>.</p><p>Note that this is currently only supported for <code>T == BigFloat</code>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>This function is not thread-safe. It will affect code running on all threads, but its behavior is undefined if called concurrently with computations that use the setting.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rounding.jl#L189-L205">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Rounding.setrounding-Tuple{Function, Type, RoundingMode}" href="#Base.Rounding.setrounding-Tuple{Function, Type, RoundingMode}"><code>Base.Rounding.setrounding</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">setrounding(f::Function, T, mode)</code></pre><p>Change the rounding mode of floating point type <code>T</code> for the duration of <code>f</code>. It is logically equivalent to:</p><pre><code class="nohighlight hljs">old = rounding(T)
setrounding(T, mode)
f()
setrounding(T, old)</code></pre><p>See <a href="math.html#Base.Rounding.RoundingMode"><code>RoundingMode</code></a> for available rounding modes.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rounding.jl#L224-L236">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Rounding.get_zero_subnormals" href="#Base.Rounding.get_zero_subnormals"><code>Base.Rounding.get_zero_subnormals</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">get_zero_subnormals() -&gt; Bool</code></pre><p>Return <code>false</code> if operations on subnormal floating-point values (&quot;denormals&quot;) obey rules for IEEE arithmetic, and <code>true</code> if they might be converted to zeros.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>This function only affects the current thread.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rounding.jl#L304-L313">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Rounding.set_zero_subnormals" href="#Base.Rounding.set_zero_subnormals"><code>Base.Rounding.set_zero_subnormals</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">set_zero_subnormals(yes::Bool) -&gt; Bool</code></pre><p>If <code>yes</code> is <code>false</code>, subsequent floating-point operations follow rules for IEEE arithmetic on subnormal values (&quot;denormals&quot;). Otherwise, floating-point operations are permitted (but not required) to convert subnormal inputs or outputs to zero. Returns <code>true</code> unless <code>yes==true</code> but the hardware does not support zeroing of subnormal numbers.</p><p><code>set_zero_subnormals(true)</code> can speed up some computations on some hardware. However, it can break identities such as <code>(x-y==0) == (x==y)</code>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>This function only affects the current thread.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/rounding.jl#L287-L301">source</a></section></article><h3 id="Integers"><a class="docs-heading-anchor" href="#Integers">Integers</a><a id="Integers-1"></a><a class="docs-heading-anchor-permalink" href="#Integers" title="Permalink"></a></h3><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.count_ones" href="#Base.count_ones"><code>Base.count_ones</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">count_ones(x::Integer) -&gt; Integer</code></pre><p>Number of ones in the binary representation of <code>x</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; count_ones(7)
3

julia&gt; count_ones(Int32(-1))
32</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L401-L414">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.count_zeros" href="#Base.count_zeros"><code>Base.count_zeros</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">count_zeros(x::Integer) -&gt; Integer</code></pre><p>Number of zeros in the binary representation of <code>x</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; count_zeros(Int32(2 ^ 16 - 1))
16

julia&gt; count_zeros(-1)
0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L443-L456">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.leading_zeros" href="#Base.leading_zeros"><code>Base.leading_zeros</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">leading_zeros(x::Integer) -&gt; Integer</code></pre><p>Number of zeros leading the binary representation of <code>x</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; leading_zeros(Int32(1))
31</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L417-L427">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.leading_ones" href="#Base.leading_ones"><code>Base.leading_ones</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">leading_ones(x::Integer) -&gt; Integer</code></pre><p>Number of ones leading the binary representation of <code>x</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; leading_ones(UInt32(2 ^ 32 - 2))
31</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L459-L469">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.trailing_zeros" href="#Base.trailing_zeros"><code>Base.trailing_zeros</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">trailing_zeros(x::Integer) -&gt; Integer</code></pre><p>Number of zeros trailing the binary representation of <code>x</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; trailing_zeros(2)
1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L430-L440">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.trailing_ones" href="#Base.trailing_ones"><code>Base.trailing_ones</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">trailing_ones(x::Integer) -&gt; Integer</code></pre><p>Number of ones trailing the binary representation of <code>x</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; trailing_ones(3)
2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L472-L482">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isodd" href="#Base.isodd"><code>Base.isodd</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isodd(x::Number) -&gt; Bool</code></pre><p>Return <code>true</code> if <code>x</code> is an odd integer (that is, an integer not divisible by 2), and <code>false</code> otherwise.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Non-<code>Integer</code> arguments require Julia 1.7 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isodd(9)
true

julia&gt; isodd(10)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L99-L115">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.iseven" href="#Base.iseven"><code>Base.iseven</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">iseven(x::Number) -&gt; Bool</code></pre><p>Return <code>true</code> if <code>x</code> is an even integer (that is, an integer divisible by 2), and <code>false</code> otherwise.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Non-<code>Integer</code> arguments require Julia 1.7 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; iseven(9)
false

julia&gt; iseven(10)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L119-L135">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.@int128_str" href="#Core.@int128_str"><code>Core.@int128_str</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@int128_str str</code></pre><p>Parse <code>str</code> as an <a href="numbers.html#Core.Int128"><code>Int128</code></a>. Throw an <code>ArgumentError</code> if the string is not a valid integer.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; int128&quot;123456789123&quot;
123456789123

julia&gt; int128&quot;123456789123.4&quot;
ERROR: LoadError: ArgumentError: invalid base 10 digit &#39;.&#39; in &quot;123456789123.4&quot;
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L634-L649">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.@uint128_str" href="#Core.@uint128_str"><code>Core.@uint128_str</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@uint128_str str</code></pre><p>Parse <code>str</code> as an <a href="numbers.html#Core.UInt128"><code>UInt128</code></a>. Throw an <code>ArgumentError</code> if the string is not a valid integer.</p><p><strong>Examples</strong></p><pre><code class="nohighlight hljs">julia&gt; uint128&quot;123456789123&quot;
0x00000000000000000000001cbe991a83

julia&gt; uint128&quot;-123456789123&quot;
ERROR: LoadError: ArgumentError: invalid base 10 digit &#39;-&#39; in &quot;-123456789123&quot;
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L654-L669">source</a></section></article><h2 id="BigFloats-and-BigInts"><a class="docs-heading-anchor" href="#BigFloats-and-BigInts">BigFloats and BigInts</a><a id="BigFloats-and-BigInts-1"></a><a class="docs-heading-anchor-permalink" href="#BigFloats-and-BigInts" title="Permalink"></a></h2><p>The <a href="numbers.html#Base.MPFR.BigFloat"><code>BigFloat</code></a> and <a href="numbers.html#Base.GMP.BigInt"><code>BigInt</code></a> types implements arbitrary-precision floating point and integer arithmetic, respectively. For <a href="numbers.html#Base.MPFR.BigFloat"><code>BigFloat</code></a> the <a href="https://www.mpfr.org/">GNU MPFR library</a> is used, and for <a href="numbers.html#Base.GMP.BigInt"><code>BigInt</code></a> the <a href="https://gmplib.org">GNU Multiple Precision Arithmetic Library (GMP)</a> is used.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.MPFR.BigFloat-Tuple{Any, RoundingMode}" href="#Base.MPFR.BigFloat-Tuple{Any, RoundingMode}"><code>Base.MPFR.BigFloat</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">BigFloat(x::Union{Real, AbstractString} [, rounding::RoundingMode=rounding(BigFloat)]; [precision::Integer=precision(BigFloat)])</code></pre><p>Create an arbitrary precision floating point number from <code>x</code>, with precision <code>precision</code>. The <code>rounding</code> argument specifies the direction in which the result should be rounded if the conversion cannot be done exactly. If not provided, these are set by the current global values.</p><p><code>BigFloat(x::Real)</code> is the same as <code>convert(BigFloat,x)</code>, except if <code>x</code> itself is already <code>BigFloat</code>, in which case it will return a value with the precision set to the current global precision; <code>convert</code> will always return <code>x</code>.</p><p><code>BigFloat(x::AbstractString)</code> is identical to <a href="numbers.html#Base.parse"><code>parse</code></a>. This is provided for convenience since decimal literals are converted to <code>Float64</code> when parsed, so <code>BigFloat(2.1)</code> may not yield what you expect.</p><p>See also:</p><ul><li><a href="numbers.html#Core.@big_str"><code>@big_str</code></a></li><li><a href="numbers.html#Base.Rounding.rounding"><code>rounding</code></a> and <a href="numbers.html#Base.Rounding.setrounding-Tuple{Type, Any}"><code>setrounding</code></a></li><li><a href="numbers.html#Base.precision"><code>precision</code></a> and <a href="numbers.html#Base.MPFR.setprecision"><code>setprecision</code></a></li></ul><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p><code>precision</code> as a keyword argument requires at least Julia 1.1. In Julia 1.0 <code>precision</code> is the second positional argument (<code>BigFloat(x, precision)</code>).</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; BigFloat(2.1) # 2.1 here is a Float64
2.100000000000000088817841970012523233890533447265625

julia&gt; BigFloat(&quot;2.1&quot;) # the closest BigFloat to 2.1
2.099999999999999999999999999999999999999999999999999999999999999999999999999986

julia&gt; BigFloat(&quot;2.1&quot;, RoundUp)
2.100000000000000000000000000000000000000000000000000000000000000000000000000021

julia&gt; BigFloat(&quot;2.1&quot;, RoundUp, precision=128)
2.100000000000000000000000000000000000007</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/mpfr.jl#L179-L217">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.precision" href="#Base.precision"><code>Base.precision</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">precision(num::AbstractFloat; base::Integer=2)
precision(T::Type; base::Integer=2)</code></pre><p>Get the precision of a floating point number, as defined by the effective number of bits in the significand, or the precision of a floating-point type <code>T</code> (its current default, if <code>T</code> is a variable-precision type like <a href="numbers.html#Base.MPFR.BigFloat"><code>BigFloat</code></a>).</p><p>If <code>base</code> is specified, then it returns the maximum corresponding number of significand digits in that base.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>The <code>base</code> keyword requires at least Julia 1.8.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/float.jl#L854-L867">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.MPFR.setprecision" href="#Base.MPFR.setprecision"><code>Base.MPFR.setprecision</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">setprecision([T=BigFloat,] precision::Int; base=2)</code></pre><p>Set the precision (in bits, by default) to be used for <code>T</code> arithmetic. If <code>base</code> is specified, then the precision is the minimum required to give at least <code>precision</code> digits in the given <code>base</code>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>This function is not thread-safe. It will affect code running on all threads, but its behavior is undefined if called concurrently with computations that use the setting.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>The <code>base</code> keyword requires at least Julia 1.8.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/mpfr.jl#L964-L979">source</a></section><section><div><pre><code class="language-julia hljs">setprecision(f::Function, [T=BigFloat,] precision::Integer; base=2)</code></pre><p>Change the <code>T</code> arithmetic precision (in the given <code>base</code>) for the duration of <code>f</code>. It is logically equivalent to:</p><pre><code class="nohighlight hljs">old = precision(BigFloat)
setprecision(BigFloat, precision)
f()
setprecision(BigFloat, old)</code></pre><p>Often used as <code>setprecision(T, precision) do ... end</code></p><p>Note: <code>nextfloat()</code>, <code>prevfloat()</code> do not use the precision mentioned by <code>setprecision</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>The <code>base</code> keyword requires at least Julia 1.8.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/mpfr.jl#L1080-L1098">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.GMP.BigInt-Tuple{Any}" href="#Base.GMP.BigInt-Tuple{Any}"><code>Base.GMP.BigInt</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">BigInt(x)</code></pre><p>Create an arbitrary precision integer. <code>x</code> may be an <code>Int</code> (or anything that can be converted to an <code>Int</code>). The usual mathematical operators are defined for this type, and results are promoted to a <a href="numbers.html#Base.GMP.BigInt"><code>BigInt</code></a>.</p><p>Instances can be constructed from strings via <a href="numbers.html#Base.parse"><code>parse</code></a>, or using the <code>big</code> string literal.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; parse(BigInt, &quot;42&quot;)
42

julia&gt; big&quot;313&quot;
313

julia&gt; BigInt(10)^19
10000000000000000000</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/gmp.jl#L70-L91">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.@big_str" href="#Core.@big_str"><code>Core.@big_str</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@big_str str</code></pre><p>Parse a string into a <a href="numbers.html#Base.GMP.BigInt"><code>BigInt</code></a> or <a href="numbers.html#Base.MPFR.BigFloat"><code>BigFloat</code></a>, and throw an <code>ArgumentError</code> if the string is not a valid number. For integers <code>_</code> is allowed in the string as a separator.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; big&quot;123_456&quot;
123456

julia&gt; big&quot;7891.5&quot;
7891.5

julia&gt; big&quot;_&quot;
ERROR: ArgumentError: invalid number format _ for BigInt or BigFloat
[...]</code></pre><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Using <code>@big_str</code> for constructing <a href="numbers.html#Base.MPFR.BigFloat"><code>BigFloat</code></a> values may not result in the behavior that might be naively expected: as a macro, <code>@big_str</code> obeys the global precision (<a href="numbers.html#Base.MPFR.setprecision"><code>setprecision</code></a>) and rounding mode (<a href="numbers.html#Base.Rounding.setrounding-Tuple{Type, Any}"><code>setrounding</code></a>) settings as they are at <em>load time</em>. Thus, a function like <code>() -&gt; precision(big&quot;0.3&quot;)</code> returns a constant whose value depends on the value of the precision at the point when the function is defined, <strong>not</strong> at the precision at the time when the function is called.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/int.jl#L674-L702">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="math.html">« Mathematics</a><a class="docs-footer-nextpage" href="strings.html">Strings »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
