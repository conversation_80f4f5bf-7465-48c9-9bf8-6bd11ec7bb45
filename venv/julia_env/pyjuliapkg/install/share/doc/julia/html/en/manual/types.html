<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Types · The Julia Language</title><meta name="title" content="Types · The Julia Language"/><meta property="og:title" content="Types · The Julia Language"/><meta property="twitter:title" content="Types · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li class="is-active"><a class="tocitem" href="types.html">Types</a><ul class="internal"><li><a class="tocitem" href="#Type-Declarations"><span>Type Declarations</span></a></li><li><a class="tocitem" href="#man-abstract-types"><span>Abstract Types</span></a></li><li><a class="tocitem" href="#Primitive-Types"><span>Primitive Types</span></a></li><li><a class="tocitem" href="#Composite-Types"><span>Composite Types</span></a></li><li><a class="tocitem" href="#Mutable-Composite-Types"><span>Mutable Composite Types</span></a></li><li><a class="tocitem" href="#man-declared-types"><span>Declared Types</span></a></li><li><a class="tocitem" href="#Type-Unions"><span>Type Unions</span></a></li><li><a class="tocitem" href="#Parametric-Types"><span>Parametric Types</span></a></li><li><a class="tocitem" href="#UnionAll-Types"><span>UnionAll Types</span></a></li><li><a class="tocitem" href="#man-singleton-types"><span>Singleton types</span></a></li><li><a class="tocitem" href="#Types-of-functions"><span>Types of functions</span></a></li><li><a class="tocitem" href="#man-typet-type"><span><code>Type{T}</code> type selectors</span></a></li><li><a class="tocitem" href="#Type-Aliases"><span>Type Aliases</span></a></li><li><a class="tocitem" href="#Operations-on-Types"><span>Operations on Types</span></a></li><li><a class="tocitem" href="#man-custom-pretty-printing"><span>Custom pretty-printing</span></a></li><li><a class="tocitem" href="#&quot;Value-types&quot;"><span>&quot;Value types&quot;</span></a></li></ul></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="types.html">Types</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="types.html">Types</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/types.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="man-types"><a class="docs-heading-anchor" href="#man-types">Types</a><a id="man-types-1"></a><a class="docs-heading-anchor-permalink" href="#man-types" title="Permalink"></a></h1><p>Type systems have traditionally fallen into two quite different camps: static type systems, where every program expression must have a type computable before the execution of the program, and dynamic type systems, where nothing is known about types until run time, when the actual values manipulated by the program are available. Object orientation allows some flexibility in statically typed languages by letting code be written without the precise types of values being known at compile time. The ability to write code that can operate on different types is called polymorphism. All code in classic dynamically typed languages is polymorphic: only by explicitly checking types, or when objects fail to support operations at run-time, are the types of any values ever restricted.</p><p>Julia&#39;s type system is dynamic, but gains some of the advantages of static type systems by making it possible to indicate that certain values are of specific types. This can be of great assistance in generating efficient code, but even more significantly, it allows method dispatch on the types of function arguments to be deeply integrated with the language. Method dispatch is explored in detail in <a href="methods.html#Methods">Methods</a>, but is rooted in the type system presented here.</p><p>The default behavior in Julia when types are omitted is to allow values to be of any type. Thus, one can write many useful Julia functions without ever explicitly using types. When additional expressiveness is needed, however, it is easy to gradually introduce explicit type annotations into previously &quot;untyped&quot; code. Adding annotations serves three primary purposes: to take advantage of Julia&#39;s powerful multiple-dispatch mechanism,  to improve human readability, and to catch programmer errors.</p><p>Describing Julia in the lingo of <a href="https://en.wikipedia.org/wiki/Type_system">type systems</a>, it is: dynamic, nominative and parametric. Generic types can be parameterized, and the hierarchical relationships between types are <a href="https://en.wikipedia.org/wiki/Nominal_type_system">explicitly declared</a>, rather than <a href="https://en.wikipedia.org/wiki/Structural_type_system">implied by compatible structure</a>. One particularly distinctive feature of Julia&#39;s type system is that concrete types may not subtype each other: all concrete types are final and may only have abstract types as their supertypes. While this might at first seem unduly restrictive, it has many beneficial consequences with surprisingly few drawbacks. It turns out that being able to inherit behavior is much more important than being able to inherit structure, and inheriting both causes significant difficulties in traditional object-oriented languages. Other high-level aspects of Julia&#39;s type system that should be mentioned up front are:</p><ul><li>There is no division between object and non-object values: all values in Julia are true objects having a type that belongs to a single, fully connected type graph, all nodes of which are equally first-class as types.</li><li>There is no meaningful concept of a &quot;compile-time type&quot;: the only type a value has is its actual type when the program is running. This is called a &quot;run-time type&quot; in object-oriented languages where the combination of static compilation with polymorphism makes this distinction significant.</li><li>Only values, not variables, have types – variables are simply names bound to values, although for simplicity we may say &quot;type of a variable&quot; as shorthand for &quot;type of the value to which a variable refers&quot;.</li><li>Both abstract and concrete types can be parameterized by other types. They can also be parameterized by symbols, by values of any type for which <a href="../base/base.html#Base.isbits"><code>isbits</code></a> returns true (essentially, things like numbers and bools that are stored like C types or <code>struct</code>s with no pointers to other objects), and also by tuples thereof. Type parameters may be omitted when they do not need to be referenced or restricted.</li></ul><p>Julia&#39;s type system is designed to be powerful and expressive, yet clear, intuitive and unobtrusive. Many Julia programmers may never feel the need to write code that explicitly uses types. Some kinds of programming, however, become clearer, simpler, faster and more robust with declared types.</p><h2 id="Type-Declarations"><a class="docs-heading-anchor" href="#Type-Declarations">Type Declarations</a><a id="Type-Declarations-1"></a><a class="docs-heading-anchor-permalink" href="#Type-Declarations" title="Permalink"></a></h2><p>The <code>::</code> operator can be used to attach type annotations to expressions and variables in programs. There are two primary reasons to do this:</p><ol><li>As an assertion to help confirm that your program works the way you expect, and</li><li>To provide extra type information to the compiler, which can then improve performance in some cases.</li></ol><p>When appended to an expression computing a value, the <code>::</code> operator is read as &quot;is an instance of&quot;. It can be used anywhere to assert that the value of the expression on the left is an instance of the type on the right. When the type on the right is concrete, the value on the left must have that type as its implementation – recall that all concrete types are final, so no implementation is a subtype of any other. When the type is abstract, it suffices for the value to be implemented by a concrete type that is a subtype of the abstract type. If the type assertion is not true, an exception is thrown, otherwise, the left-hand value is returned:</p><pre><code class="language-julia-repl hljs">julia&gt; (1+2)::AbstractFloat
ERROR: TypeError: in typeassert, expected AbstractFloat, got a value of type Int64

julia&gt; (1+2)::Int
3</code></pre><p>This allows a type assertion to be attached to any expression in-place.</p><p>When appended to a variable on the left-hand side of an assignment, or as part of a <code>local</code> declaration, the <code>::</code> operator means something a bit different: it declares the variable to always have the specified type, like a type declaration in a statically-typed language such as C. Every value assigned to the variable will be converted to the declared type using <a href="../base/base.html#Base.convert"><code>convert</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; function foo()
           x::Int8 = 100
           x
       end
foo (generic function with 1 method)

julia&gt; x = foo()
100

julia&gt; typeof(x)
Int8</code></pre><p>This feature is useful for avoiding performance &quot;gotchas&quot; that could occur if one of the assignments to a variable changed its type unexpectedly.</p><p>This &quot;declaration&quot; behavior only occurs in specific contexts:</p><pre><code class="language-julia hljs">local x::Int8  # in a local declaration
x::Int8 = 10   # as the left-hand side of an assignment</code></pre><p>and applies to the whole current scope, even before the declaration.</p><p>As of Julia 1.8, type declarations can now be used in global scope i.e. type annotations can be added to global variables to make accessing them type stable.</p><pre><code class="language-julia hljs">julia&gt; x::Int = 10
10

julia&gt; x = 3.5
ERROR: InexactError: Int64(3.5)

julia&gt; function foo(y)
           global x = 15.8    # throws an error when foo is called
           return x + y
       end
foo (generic function with 1 method)

julia&gt; foo(10)
ERROR: InexactError: Int64(15.8)</code></pre><p>Declarations can also be attached to function definitions:</p><pre><code class="language-julia hljs">function sinc(x)::Float64
    if x == 0
        return 1
    end
    return sin(pi*x)/(pi*x)
end</code></pre><p>Returning from this function behaves just like an assignment to a variable with a declared type: the value is always converted to <code>Float64</code>.</p><h2 id="man-abstract-types"><a class="docs-heading-anchor" href="#man-abstract-types">Abstract Types</a><a id="man-abstract-types-1"></a><a class="docs-heading-anchor-permalink" href="#man-abstract-types" title="Permalink"></a></h2><p>Abstract types cannot be instantiated, and serve only as nodes in the type graph, thereby describing sets of related concrete types: those concrete types which are their descendants. We begin with abstract types even though they have no instantiation because they are the backbone of the type system: they form the conceptual hierarchy which makes Julia&#39;s type system more than just a collection of object implementations.</p><p>Recall that in <a href="integers-and-floating-point-numbers.html#Integers-and-Floating-Point-Numbers">Integers and Floating-Point Numbers</a>, we introduced a variety of concrete types of numeric values: <a href="../base/numbers.html#Core.Int8"><code>Int8</code></a>, <a href="../base/numbers.html#Core.UInt8"><code>UInt8</code></a>, <a href="../base/numbers.html#Core.Int16"><code>Int16</code></a>, <a href="../base/numbers.html#Core.UInt16"><code>UInt16</code></a>, <a href="../base/numbers.html#Core.Int32"><code>Int32</code></a>, <a href="../base/numbers.html#Core.UInt32"><code>UInt32</code></a>, <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>, <a href="../base/numbers.html#Core.UInt64"><code>UInt64</code></a>, <a href="../base/numbers.html#Core.Int128"><code>Int128</code></a>, <a href="../base/numbers.html#Core.UInt128"><code>UInt128</code></a>, <a href="../base/numbers.html#Core.Float16"><code>Float16</code></a>, <a href="../base/numbers.html#Core.Float32"><code>Float32</code></a>, and <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a>. Although they have different representation sizes, <code>Int8</code>, <code>Int16</code>, <code>Int32</code>, <code>Int64</code> and <code>Int128</code> all have in common that they are signed integer types. Likewise <code>UInt8</code>, <code>UInt16</code>, <code>UInt32</code>, <code>UInt64</code> and <code>UInt128</code> are all unsigned integer types, while <code>Float16</code>, <code>Float32</code> and <code>Float64</code> are distinct in being floating-point types rather than integers. It is common for a piece of code to make sense, for example, only if its arguments are some kind of integer, but not really depend on what particular <em>kind</em> of integer. For example, the greatest common denominator algorithm works for all kinds of integers, but will not work for floating-point numbers. Abstract types allow the construction of a hierarchy of types, providing a context into which concrete types can fit. This allows you, for example, to easily program to any type that is an integer, without restricting an algorithm to a specific type of integer.</p><p>Abstract types are declared using the <a href="../base/base.html#abstract type"><code>abstract type</code></a> keyword. The general syntaxes for declaring an abstract type are:</p><pre><code class="nohighlight hljs">abstract type «name» end
abstract type «name» &lt;: «supertype» end</code></pre><p>The <code>abstract type</code> keyword introduces a new abstract type, whose name is given by <code>«name»</code>. This name can be optionally followed by <a href="../base/base.html#Core.:&lt;:"><code>&lt;:</code></a> and an already-existing type, indicating that the newly declared abstract type is a subtype of this &quot;parent&quot; type.</p><p>When no supertype is given, the default supertype is <code>Any</code> – a predefined abstract type that all objects are instances of and all types are subtypes of. In type theory, <code>Any</code> is commonly called &quot;top&quot; because it is at the apex of the type graph. Julia also has a predefined abstract &quot;bottom&quot; type, at the nadir of the type graph, which is written as <code>Union{}</code>. It is the exact opposite of <code>Any</code>: no object is an instance of <code>Union{}</code> and all types are supertypes of <code>Union{}</code>.</p><p>Let&#39;s consider some of the abstract types that make up Julia&#39;s numerical hierarchy:</p><pre><code class="language-julia hljs">abstract type Number end
abstract type Real          &lt;: Number end
abstract type AbstractFloat &lt;: Real end
abstract type Integer       &lt;: Real end
abstract type Signed        &lt;: Integer end
abstract type Unsigned      &lt;: Integer end</code></pre><p>The <a href="../base/numbers.html#Core.Number"><code>Number</code></a> type is a direct child type of <code>Any</code>, and <a href="../base/numbers.html#Core.Real"><code>Real</code></a> is its child. In turn, <code>Real</code> has two children (it has more, but only two are shown here; we&#39;ll get to the others later): <a href="../base/numbers.html#Core.Integer"><code>Integer</code></a> and <a href="../base/numbers.html#Core.AbstractFloat"><code>AbstractFloat</code></a>, separating the world into representations of integers and representations of real numbers. Representations of real numbers include floating-point types, but also include other types, such as rationals. <code>AbstractFloat</code> includes only floating-point representations of real numbers. Integers are further subdivided into <a href="../base/numbers.html#Core.Signed"><code>Signed</code></a> and <a href="../base/numbers.html#Core.Unsigned"><code>Unsigned</code></a> varieties.</p><p>The <code>&lt;:</code> operator in general means &quot;is a subtype of&quot;, and, used in declarations like those above, declares the right-hand type to be an immediate supertype of the newly declared type. It can also be used in expressions as a subtype operator which returns <code>true</code> when its left operand is a subtype of its right operand:</p><pre><code class="language-julia-repl hljs">julia&gt; Integer &lt;: Number
true

julia&gt; Integer &lt;: AbstractFloat
false</code></pre><p>An important use of abstract types is to provide default implementations for concrete types. To give a simple example, consider:</p><pre><code class="language-julia hljs">function myplus(x,y)
    x+y
end</code></pre><p>The first thing to note is that the above argument declarations are equivalent to <code>x::Any</code> and <code>y::Any</code>. When this function is invoked, say as <code>myplus(2,5)</code>, the dispatcher chooses the most specific method named <code>myplus</code> that matches the given arguments. (See <a href="methods.html#Methods">Methods</a> for more information on multiple dispatch.)</p><p>Assuming no method more specific than the above is found, Julia next internally defines and compiles a method called <code>myplus</code> specifically for two <code>Int</code> arguments based on the generic function given above, i.e., it implicitly defines and compiles:</p><pre><code class="language-julia hljs">function myplus(x::Int,y::Int)
    x+y
end</code></pre><p>and finally, it invokes this specific method.</p><p>Thus, abstract types allow programmers to write generic functions that can later be used as the default method by many combinations of concrete types. Thanks to multiple dispatch, the programmer has full control over whether the default or more specific method is used.</p><p>An important point to note is that there is no loss in performance if the programmer relies on a function whose arguments are abstract types, because it is recompiled for each tuple of concrete argument types with which it is invoked. (There may be a performance issue, however, in the case of function arguments that are containers of abstract types; see <a href="performance-tips.html#man-performance-abstract-container">Performance Tips</a>.)</p><h2 id="Primitive-Types"><a class="docs-heading-anchor" href="#Primitive-Types">Primitive Types</a><a id="Primitive-Types-1"></a><a class="docs-heading-anchor-permalink" href="#Primitive-Types" title="Permalink"></a></h2><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>It is almost always preferable to wrap an existing primitive type in a new composite type than to define your own primitive type.</p><p>This functionality exists to allow Julia to bootstrap the standard primitive types that LLVM supports. Once they are defined, there is very little reason to define more.</p></div></div><p>A primitive type is a concrete type whose data consists of plain old bits. Classic examples of primitive types are integers and floating-point values. Unlike most languages, Julia lets you declare your own primitive types, rather than providing only a fixed set of built-in ones. In fact, the standard primitive types are all defined in the language itself:</p><pre><code class="language-julia hljs">primitive type Float16 &lt;: AbstractFloat 16 end
primitive type Float32 &lt;: AbstractFloat 32 end
primitive type Float64 &lt;: AbstractFloat 64 end

primitive type Bool &lt;: Integer 8 end
primitive type Char &lt;: AbstractChar 32 end

primitive type Int8    &lt;: Signed   8 end
primitive type UInt8   &lt;: Unsigned 8 end
primitive type Int16   &lt;: Signed   16 end
primitive type UInt16  &lt;: Unsigned 16 end
primitive type Int32   &lt;: Signed   32 end
primitive type UInt32  &lt;: Unsigned 32 end
primitive type Int64   &lt;: Signed   64 end
primitive type UInt64  &lt;: Unsigned 64 end
primitive type Int128  &lt;: Signed   128 end
primitive type UInt128 &lt;: Unsigned 128 end</code></pre><p>The general syntaxes for declaring a primitive type are:</p><pre><code class="nohighlight hljs">primitive type «name» «bits» end
primitive type «name» &lt;: «supertype» «bits» end</code></pre><p>The number of bits indicates how much storage the type requires and the name gives the new type a name. A primitive type can optionally be declared to be a subtype of some supertype. If a supertype is omitted, then the type defaults to having <code>Any</code> as its immediate supertype. The declaration of <a href="../base/numbers.html#Core.Bool"><code>Bool</code></a> above therefore means that a boolean value takes eight bits to store, and has <a href="../base/numbers.html#Core.Integer"><code>Integer</code></a> as its immediate supertype. Currently, only sizes that are multiples of 8 bits are supported and you are likely to experience LLVM bugs with sizes other than those used above. Therefore, boolean values, although they really need just a single bit, cannot be declared to be any smaller than eight bits.</p><p>The types <a href="../base/numbers.html#Core.Bool"><code>Bool</code></a>, <a href="../base/numbers.html#Core.Int8"><code>Int8</code></a> and <a href="../base/numbers.html#Core.UInt8"><code>UInt8</code></a> all have identical representations: they are eight-bit chunks of memory. Since Julia&#39;s type system is nominative, however, they are not interchangeable despite having identical structure. A fundamental difference between them is that they have different supertypes: <a href="../base/numbers.html#Core.Bool"><code>Bool</code></a>&#39;s direct supertype is <a href="../base/numbers.html#Core.Integer"><code>Integer</code></a>, <a href="../base/numbers.html#Core.Int8"><code>Int8</code></a>&#39;s is <a href="../base/numbers.html#Core.Signed"><code>Signed</code></a>, and <a href="../base/numbers.html#Core.UInt8"><code>UInt8</code></a>&#39;s is <a href="../base/numbers.html#Core.Unsigned"><code>Unsigned</code></a>. All other differences between <a href="../base/numbers.html#Core.Bool"><code>Bool</code></a>, <a href="../base/numbers.html#Core.Int8"><code>Int8</code></a>, and <a href="../base/numbers.html#Core.UInt8"><code>UInt8</code></a> are matters of behavior – the way functions are defined to act when given objects of these types as arguments. This is why a nominative type system is necessary: if structure determined type, which in turn dictates behavior, then it would be impossible to make <a href="../base/numbers.html#Core.Bool"><code>Bool</code></a> behave any differently than <a href="../base/numbers.html#Core.Int8"><code>Int8</code></a> or <a href="../base/numbers.html#Core.UInt8"><code>UInt8</code></a>.</p><h2 id="Composite-Types"><a class="docs-heading-anchor" href="#Composite-Types">Composite Types</a><a id="Composite-Types-1"></a><a class="docs-heading-anchor-permalink" href="#Composite-Types" title="Permalink"></a></h2><p><a href="https://en.wikipedia.org/wiki/Composite_data_type">Composite types</a> are called records, structs, or objects in various languages. A composite type is a collection of named fields, an instance of which can be treated as a single value. In many languages, composite types are the only kind of user-definable type, and they are by far the most commonly used user-defined type in Julia as well.</p><p>In mainstream object oriented languages, such as C++, Java, Python and Ruby, composite types also have named functions associated with them, and the combination is called an &quot;object&quot;. In purer object-oriented languages, such as Ruby or Smalltalk, all values are objects whether they are composites or not. In less pure object oriented languages, including C++ and Java, some values, such as integers and floating-point values, are not objects, while instances of user-defined composite types are true objects with associated methods. In Julia, all values are objects, but functions are not bundled with the objects they operate on. This is necessary since Julia chooses which method of a function to use by multiple dispatch, meaning that the types of <em>all</em> of a function&#39;s arguments are considered when selecting a method, rather than just the first one (see <a href="methods.html#Methods">Methods</a> for more information on methods and dispatch). Thus, it would be inappropriate for functions to &quot;belong&quot; to only their first argument. Organizing methods into function objects rather than having named bags of methods &quot;inside&quot; each object ends up being a highly beneficial aspect of the language design.</p><p>Composite types are introduced with the <a href="../base/base.html#struct"><code>struct</code></a> keyword followed by a block of field names, optionally annotated with types using the <code>::</code> operator:</p><pre><code class="language-julia-repl hljs">julia&gt; struct Foo
           bar
           baz::Int
           qux::Float64
       end</code></pre><p>Fields with no type annotation default to <code>Any</code>, and can accordingly hold any type of value.</p><p>New objects of type <code>Foo</code> are created by applying the <code>Foo</code> type object like a function to values for its fields:</p><pre><code class="language-julia-repl hljs">julia&gt; foo = Foo(&quot;Hello, world.&quot;, 23, 1.5)
Foo(&quot;Hello, world.&quot;, 23, 1.5)

julia&gt; typeof(foo)
Foo</code></pre><p>When a type is applied like a function it is called a <em>constructor</em>. Two constructors are generated automatically (these are called <em>default constructors</em>). One accepts any arguments and calls <a href="../base/base.html#Base.convert"><code>convert</code></a> to convert them to the types of the fields, and the other accepts arguments that match the field types exactly. The reason both of these are generated is that this makes it easier to add new definitions without inadvertently replacing a default constructor.</p><p>Since the <code>bar</code> field is unconstrained in type, any value will do. However, the value for <code>baz</code> must be convertible to <code>Int</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; Foo((), 23.5, 1)
ERROR: InexactError: Int64(23.5)
Stacktrace:
[...]</code></pre><p>You may find a list of field names using the <a href="../base/base.html#Base.fieldnames"><code>fieldnames</code></a> function.</p><pre><code class="language-julia-repl hljs">julia&gt; fieldnames(Foo)
(:bar, :baz, :qux)</code></pre><p>You can access the field values of a composite object using the traditional <code>foo.bar</code> notation:</p><pre><code class="language-julia-repl hljs">julia&gt; foo.bar
&quot;Hello, world.&quot;

julia&gt; foo.baz
23

julia&gt; foo.qux
1.5</code></pre><p>Composite objects declared with <code>struct</code> are <em>immutable</em>; they cannot be modified after construction. This may seem odd at first, but it has several advantages:</p><ul><li>It can be more efficient. Some structs can be packed efficiently into arrays, and in some cases the compiler is able to avoid allocating immutable objects entirely.</li><li>It is not possible to violate the invariants provided by the type&#39;s constructors.</li><li>Code using immutable objects can be easier to reason about.</li></ul><p>An immutable object might contain mutable objects, such as arrays, as fields. Those contained objects will remain mutable; only the fields of the immutable object itself cannot be changed to point to different objects.</p><p>Where required, mutable composite objects can be declared with the keyword <a href="../base/base.html#mutable struct"><code>mutable struct</code></a>, to be discussed in the next section.</p><p>If all the fields of an immutable structure are indistinguishable (<code>===</code>) then two immutable values containing those fields are also indistinguishable:</p><pre><code class="language-julia-repl hljs">julia&gt; struct X
           a::Int
           b::Float64
       end

julia&gt; X(1, 2) === X(1, 2)
true</code></pre><p>There is much more to say about how instances of composite types are created, but that discussion depends on both <a href="types.html#Parametric-Types">Parametric Types</a> and on <a href="methods.html#Methods">Methods</a>, and is sufficiently important to be addressed in its own section: <a href="constructors.html#man-constructors">Constructors</a>.</p><p>For many user-defined types <code>X</code>, you may want to define a method <a href="interfaces.html#man-interfaces-broadcasting"><code>Base.broadcastable(x::X) = Ref(x)</code></a> so that instances of that type act as 0-dimensional &quot;scalars&quot; for <a href="arrays.html#Broadcasting">broadcasting</a>.</p><h2 id="Mutable-Composite-Types"><a class="docs-heading-anchor" href="#Mutable-Composite-Types">Mutable Composite Types</a><a id="Mutable-Composite-Types-1"></a><a class="docs-heading-anchor-permalink" href="#Mutable-Composite-Types" title="Permalink"></a></h2><p>If a composite type is declared with <code>mutable struct</code> instead of <code>struct</code>, then instances of it can be modified:</p><pre><code class="language-julia-repl hljs">julia&gt; mutable struct Bar
           baz
           qux::Float64
       end

julia&gt; bar = Bar(&quot;Hello&quot;, 1.5);

julia&gt; bar.qux = 2.0
2.0

julia&gt; bar.baz = 1//2
1//2</code></pre><p>An extra interface between the fields and the user can be provided through <a href="interfaces.html#man-instance-properties">Instance Properties</a>. This grants more control on what can be accessed and modified using the <code>bar.baz</code> notation.</p><p>In order to support mutation, such objects are generally allocated on the heap, and have stable memory addresses. A mutable object is like a little container that might hold different values over time, and so can only be reliably identified with its address. In contrast, an instance of an immutable type is associated with specific field values –- the field values alone tell you everything about the object. In deciding whether to make a type mutable, ask whether two instances with the same field values would be considered identical, or if they might need to change independently over time. If they would be considered identical, the type should probably be immutable.</p><p>To recap, two essential properties define immutability in Julia:</p><ul><li>It is not permitted to modify the value of an immutable type.<ul><li>For bits types this means that the bit pattern of a value once set will never change and that value is the identity of a bits type.</li><li>For composite  types, this means that the identity of the values of its fields will never change. When the fields are bits types, that means their bits will never change, for fields whose values are mutable types like arrays, that means the fields will always refer to the same mutable value even though that mutable value&#39;s content may itself be modified.</li></ul></li><li>An object with an immutable type may be copied freely by the compiler since its immutability makes it impossible to programmatically distinguish between the original object and a copy.<ul><li>In particular, this means that small enough immutable values like integers and floats are typically passed to functions in registers (or stack allocated).</li><li>Mutable values, on the other hand are heap-allocated and passed to functions as pointers to heap-allocated values except in cases where the compiler is sure that there&#39;s no way to tell that this is not what is happening.</li></ul></li></ul><p>In cases where one or more fields of an otherwise mutable struct is known to be immutable, one can declare these fields as such using <code>const</code> as shown below. This enables some, but not all of the optimizations of immutable structs, and can be used to enforce invariants on the particular fields marked as <code>const</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p><code>const</code> annotating fields of mutable structs requires at least Julia 1.8.</p></div></div><pre><code class="language-julia-repl hljs">julia&gt; mutable struct Baz
           a::Int
           const b::Float64
       end

julia&gt; baz = Baz(1, 1.5);

julia&gt; baz.a = 2
2

julia&gt; baz.b = 2.0
ERROR: setfield!: const field .b of type Baz cannot be changed
[...]</code></pre><h2 id="man-declared-types"><a class="docs-heading-anchor" href="#man-declared-types">Declared Types</a><a id="man-declared-types-1"></a><a class="docs-heading-anchor-permalink" href="#man-declared-types" title="Permalink"></a></h2><p>The three kinds of types (abstract, primitive, composite) discussed in the previous sections are actually all closely related. They share the same key properties:</p><ul><li>They are explicitly declared.</li><li>They have names.</li><li>They have explicitly declared supertypes.</li><li>They may have parameters.</li></ul><p>Because of these shared properties, these types are internally represented as instances of the same concept, <code>DataType</code>, which is the type of any of these types:</p><pre><code class="language-julia-repl hljs">julia&gt; typeof(Real)
DataType

julia&gt; typeof(Int)
DataType</code></pre><p>A <code>DataType</code> may be abstract or concrete. If it is concrete, it has a specified size, storage layout, and (optionally) field names. Thus a primitive type is a <code>DataType</code> with nonzero size, but no field names. A composite type is a <code>DataType</code> that has field names or is empty (zero size).</p><p>Every concrete value in the system is an instance of some <code>DataType</code>.</p><h2 id="Type-Unions"><a class="docs-heading-anchor" href="#Type-Unions">Type Unions</a><a id="Type-Unions-1"></a><a class="docs-heading-anchor-permalink" href="#Type-Unions" title="Permalink"></a></h2><p>A type union is a special abstract type which includes as objects all instances of any of its argument types, constructed using the special <a href="../base/base.html#Core.Union"><code>Union</code></a> keyword:</p><pre><code class="language-julia-repl hljs">julia&gt; IntOrString = Union{Int,AbstractString}
Union{Int64, AbstractString}

julia&gt; 1 :: IntOrString
1

julia&gt; &quot;Hello!&quot; :: IntOrString
&quot;Hello!&quot;

julia&gt; 1.0 :: IntOrString
ERROR: TypeError: in typeassert, expected Union{Int64, AbstractString}, got a value of type Float64</code></pre><p>The compilers for many languages have an internal union construct for reasoning about types; Julia simply exposes it to the programmer. The Julia compiler is able to generate efficient code in the presence of <code>Union</code> types with a small number of types <sup class="footnote-reference"><a id="citeref-1" href="#footnote-1">[1]</a></sup>, by generating specialized code in separate branches for each possible type.</p><p>A particularly useful case of a <code>Union</code> type is <code>Union{T, Nothing}</code>, where <code>T</code> can be any type and <a href="../base/base.html#Core.Nothing"><code>Nothing</code></a> is the singleton type whose only instance is the object <a href="../base/constants.html#Core.nothing"><code>nothing</code></a>. This pattern is the Julia equivalent of <a href="https://en.wikipedia.org/wiki/Nullable_type"><code>Nullable</code>, <code>Option</code> or <code>Maybe</code></a> types in other languages. Declaring a function argument or a field as <code>Union{T, Nothing}</code> allows setting it either to a value of type <code>T</code>, or to <code>nothing</code> to indicate that there is no value. See <a href="faq.html#faq-nothing">this FAQ entry</a> for more information.</p><h2 id="Parametric-Types"><a class="docs-heading-anchor" href="#Parametric-Types">Parametric Types</a><a id="Parametric-Types-1"></a><a class="docs-heading-anchor-permalink" href="#Parametric-Types" title="Permalink"></a></h2><p>An important and powerful feature of Julia&#39;s type system is that it is parametric: types can take parameters, so that type declarations actually introduce a whole family of new types – one for each possible combination of parameter values. There are many languages that support some version of <a href="https://en.wikipedia.org/wiki/Generic_programming">generic programming</a>, wherein data structures and algorithms to manipulate them may be specified without specifying the exact types involved. For example, some form of generic programming exists in ML, Haskell, Ada, Eiffel, C++, Java, C#, F#, and Scala, just to name a few. Some of these languages support true parametric polymorphism (e.g. ML, Haskell, Scala), while others support ad-hoc, template-based styles of generic programming (e.g. C++, Java). With so many different varieties of generic programming and parametric types in various languages, we won&#39;t even attempt to compare Julia&#39;s parametric types to other languages, but will instead focus on explaining Julia&#39;s system in its own right. We will note, however, that because Julia is a dynamically typed language and doesn&#39;t need to make all type decisions at compile time, many traditional difficulties encountered in static parametric type systems can be relatively easily handled.</p><p>All declared types (the <code>DataType</code> variety) can be parameterized, with the same syntax in each case. We will discuss them in the following order: first, parametric composite types, then parametric abstract types, and finally parametric primitive types.</p><h3 id="man-parametric-composite-types"><a class="docs-heading-anchor" href="#man-parametric-composite-types">Parametric Composite Types</a><a id="man-parametric-composite-types-1"></a><a class="docs-heading-anchor-permalink" href="#man-parametric-composite-types" title="Permalink"></a></h3><p>Type parameters are introduced immediately after the type name, surrounded by curly braces:</p><pre><code class="language-julia-repl hljs">julia&gt; struct Point{T}
           x::T
           y::T
       end</code></pre><p>This declaration defines a new parametric type, <code>Point{T}</code>, holding two &quot;coordinates&quot; of type <code>T</code>. What, one may ask, is <code>T</code>? Well, that&#39;s precisely the point of parametric types: it can be any type at all (or a value of any bits type, actually, although here it&#39;s clearly used as a type). <code>Point{Float64}</code> is a concrete type equivalent to the type defined by replacing <code>T</code> in the definition of <code>Point</code> with <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a>. Thus, this single declaration actually declares an unlimited number of types: <code>Point{Float64}</code>, <code>Point{AbstractString}</code>, <code>Point{Int64}</code>, etc. Each of these is now a usable concrete type:</p><pre><code class="language-julia-repl hljs">julia&gt; Point{Float64}
Point{Float64}

julia&gt; Point{AbstractString}
Point{AbstractString}</code></pre><p>The type <code>Point{Float64}</code> is a point whose coordinates are 64-bit floating-point values, while the type <code>Point{AbstractString}</code> is a &quot;point&quot; whose &quot;coordinates&quot; are string objects (see <a href="../devdocs/ast.html#Strings">Strings</a>).</p><p><code>Point</code> itself is also a valid type object, containing all instances <code>Point{Float64}</code>, <code>Point{AbstractString}</code>, etc. as subtypes:</p><pre><code class="language-julia-repl hljs">julia&gt; Point{Float64} &lt;: Point
true

julia&gt; Point{AbstractString} &lt;: Point
true</code></pre><p>Other types, of course, are not subtypes of it:</p><pre><code class="language-julia-repl hljs">julia&gt; Float64 &lt;: Point
false

julia&gt; AbstractString &lt;: Point
false</code></pre><p>Concrete <code>Point</code> types with different values of <code>T</code> are never subtypes of each other:</p><pre><code class="language-julia-repl hljs">julia&gt; Point{Float64} &lt;: Point{Int64}
false

julia&gt; Point{Float64} &lt;: Point{Real}
false</code></pre><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>This last point is <em>very</em> important: even though <code>Float64 &lt;: Real</code> we <strong>DO NOT</strong> have <code>Point{Float64} &lt;: Point{Real}</code>.</p></div></div><p>In other words, in the parlance of type theory, Julia&#39;s type parameters are <em>invariant</em>, rather than being <a href="https://en.wikipedia.org/wiki/Covariance_and_contravariance_%28computer_science%29">covariant (or even contravariant)</a>. This is for practical reasons: while any instance of <code>Point{Float64}</code> may conceptually be like an instance of <code>Point{Real}</code> as well, the two types have different representations in memory:</p><ul><li>An instance of <code>Point{Float64}</code> can be represented compactly and efficiently as an immediate pair of 64-bit values;</li><li>An instance of <code>Point{Real}</code> must be able to hold any pair of instances of <a href="../base/numbers.html#Core.Real"><code>Real</code></a>. Since objects that are instances of <code>Real</code> can be of arbitrary size and structure, in practice an instance of <code>Point{Real}</code> must be represented as a pair of pointers to individually allocated <code>Real</code> objects.</li></ul><p>The efficiency gained by being able to store <code>Point{Float64}</code> objects with immediate values is magnified enormously in the case of arrays: an <code>Array{Float64}</code> can be stored as a contiguous memory block of 64-bit floating-point values, whereas an <code>Array{Real}</code> must be an array of pointers to individually allocated <a href="../base/numbers.html#Core.Real"><code>Real</code></a> objects – which may well be <a href="https://en.wikipedia.org/wiki/Object_type_%28object-oriented_programming%29#Boxing">boxed</a> 64-bit floating-point values, but also might be arbitrarily large, complex objects, which are declared to be implementations of the <code>Real</code> abstract type.</p><p>Since <code>Point{Float64}</code> is not a subtype of <code>Point{Real}</code>, the following method can&#39;t be applied to arguments of type <code>Point{Float64}</code>:</p><pre><code class="language-julia hljs">function norm(p::Point{Real})
    sqrt(p.x^2 + p.y^2)
end</code></pre><p>A correct way to define a method that accepts all arguments of type <code>Point{T}</code> where <code>T</code> is a subtype of <a href="../base/numbers.html#Core.Real"><code>Real</code></a> is:</p><pre><code class="language-julia hljs">function norm(p::Point{&lt;:Real})
    sqrt(p.x^2 + p.y^2)
end</code></pre><p>(Equivalently, one could define <code>function norm(p::Point{T} where T&lt;:Real)</code> or <code>function norm(p::Point{T}) where T&lt;:Real</code>; see <a href="types.html#UnionAll-Types">UnionAll Types</a>.)</p><p>More examples will be discussed later in <a href="methods.html#Methods">Methods</a>.</p><p>How does one construct a <code>Point</code> object? It is possible to define custom constructors for composite types, which will be discussed in detail in <a href="constructors.html#man-constructors">Constructors</a>, but in the absence of any special constructor declarations, there are two default ways of creating new composite objects, one in which the type parameters are explicitly given and the other in which they are implied by the arguments to the object constructor.</p><p>Since the type <code>Point{Float64}</code> is a concrete type equivalent to <code>Point</code> declared with <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> in place of <code>T</code>, it can be applied as a constructor accordingly:</p><pre><code class="language-julia-repl hljs">julia&gt; p = Point{Float64}(1.0, 2.0)
Point{Float64}(1.0, 2.0)

julia&gt; typeof(p)
Point{Float64}</code></pre><p>For the default constructor, exactly one argument must be supplied for each field:</p><pre><code class="language-julia-repl hljs">julia&gt; Point{Float64}(1.0)
ERROR: MethodError: no method matching Point{Float64}(::Float64)
The type `Point{Float64}` exists, but no method is defined for this combination of argument types when trying to construct it.
[...]

julia&gt; Point{Float64}(1.0, 2.0, 3.0)
ERROR: MethodError: no method matching Point{Float64}(::Float64, ::Float64, ::Float64)
The type `Point{Float64}` exists, but no method is defined for this combination of argument types when trying to construct it.
[...]</code></pre><p>Only one default constructor is generated for parametric types, since overriding it is not possible. This constructor accepts any arguments and converts them to the field types.</p><p>In many cases, it is redundant to provide the type of <code>Point</code> object one wants to construct, since the types of arguments to the constructor call already implicitly provide type information. For that reason, you can also apply <code>Point</code> itself as a constructor, provided that the implied value of the parameter type <code>T</code> is unambiguous:</p><pre><code class="language-julia-repl hljs">julia&gt; p1 = Point(1.0,2.0)
Point{Float64}(1.0, 2.0)

julia&gt; typeof(p1)
Point{Float64}

julia&gt; p2 = Point(1,2)
Point{Int64}(1, 2)

julia&gt; typeof(p2)
Point{Int64}</code></pre><p>In the case of <code>Point</code>, the type of <code>T</code> is unambiguously implied if and only if the two arguments to <code>Point</code> have the same type. When this isn&#39;t the case, the constructor will fail with a <a href="../base/base.html#Core.MethodError"><code>MethodError</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; Point(1,2.5)
ERROR: MethodError: no method matching Point(::Int64, ::Float64)
The type `Point` exists, but no method is defined for this combination of argument types when trying to construct it.

Closest candidates are:
  Point(::T, !Matched::T) where T
   @ Main none:2

Stacktrace:
[...]</code></pre><p>Constructor methods to appropriately handle such mixed cases can be defined, but that will not be discussed until later on in <a href="constructors.html#man-constructors">Constructors</a>.</p><h3 id="Parametric-Abstract-Types"><a class="docs-heading-anchor" href="#Parametric-Abstract-Types">Parametric Abstract Types</a><a id="Parametric-Abstract-Types-1"></a><a class="docs-heading-anchor-permalink" href="#Parametric-Abstract-Types" title="Permalink"></a></h3><p>Parametric abstract type declarations declare a collection of abstract types, in much the same way:</p><pre><code class="language-julia-repl hljs">julia&gt; abstract type Pointy{T} end</code></pre><p>With this declaration, <code>Pointy{T}</code> is a distinct abstract type for each type or integer value of <code>T</code>. As with parametric composite types, each such instance is a subtype of <code>Pointy</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; Pointy{Int64} &lt;: Pointy
true

julia&gt; Pointy{1} &lt;: Pointy
true</code></pre><p>Parametric abstract types are invariant, much as parametric composite types are:</p><pre><code class="language-julia-repl hljs">julia&gt; Pointy{Float64} &lt;: Pointy{Real}
false

julia&gt; Pointy{Real} &lt;: Pointy{Float64}
false</code></pre><p>The notation <code>Pointy{&lt;:Real}</code> can be used to express the Julia analogue of a <em>covariant</em> type, while <code>Pointy{&gt;:Int}</code> the analogue of a <em>contravariant</em> type, but technically these represent <em>sets</em> of types (see <a href="types.html#UnionAll-Types">UnionAll Types</a>).</p><pre><code class="language-julia-repl hljs">julia&gt; Pointy{Float64} &lt;: Pointy{&lt;:Real}
true

julia&gt; Pointy{Real} &lt;: Pointy{&gt;:Int}
true</code></pre><p>Much as plain old abstract types serve to create a useful hierarchy of types over concrete types, parametric abstract types serve the same purpose with respect to parametric composite types. We could, for example, have declared <code>Point{T}</code> to be a subtype of <code>Pointy{T}</code> as follows:</p><pre><code class="language-julia-repl hljs">julia&gt; struct Point{T} &lt;: Pointy{T}
           x::T
           y::T
       end</code></pre><p>Given such a declaration, for each choice of <code>T</code>, we have <code>Point{T}</code> as a subtype of <code>Pointy{T}</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; Point{Float64} &lt;: Pointy{Float64}
true

julia&gt; Point{Real} &lt;: Pointy{Real}
true

julia&gt; Point{AbstractString} &lt;: Pointy{AbstractString}
true</code></pre><p>This relationship is also invariant:</p><pre><code class="language-julia-repl hljs">julia&gt; Point{Float64} &lt;: Pointy{Real}
false

julia&gt; Point{Float64} &lt;: Pointy{&lt;:Real}
true</code></pre><p>What purpose do parametric abstract types like <code>Pointy</code> serve? Consider if we create a point-like implementation that only requires a single coordinate because the point is on the diagonal line <em>x = y</em>:</p><pre><code class="language-julia-repl hljs">julia&gt; struct DiagPoint{T} &lt;: Pointy{T}
           x::T
       end</code></pre><p>Now both <code>Point{Float64}</code> and <code>DiagPoint{Float64}</code> are implementations of the <code>Pointy{Float64}</code> abstraction, and similarly for every other possible choice of type <code>T</code>. This allows programming to a common interface shared by all <code>Pointy</code> objects, implemented for both <code>Point</code> and <code>DiagPoint</code>. This cannot be fully demonstrated, however, until we have introduced methods and dispatch in the next section, <a href="methods.html#Methods">Methods</a>.</p><p>There are situations where it may not make sense for type parameters to range freely over all possible types. In such situations, one can constrain the range of <code>T</code> like so:</p><pre><code class="language-julia-repl hljs">julia&gt; abstract type Pointy{T&lt;:Real} end</code></pre><p>With such a declaration, it is acceptable to use any type that is a subtype of <a href="../base/numbers.html#Core.Real"><code>Real</code></a> in place of <code>T</code>, but not types that are not subtypes of <code>Real</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; Pointy{Float64}
Pointy{Float64}

julia&gt; Pointy{Real}
Pointy{Real}

julia&gt; Pointy{AbstractString}
ERROR: TypeError: in Pointy, in T, expected T&lt;:Real, got Type{AbstractString}

julia&gt; Pointy{1}
ERROR: TypeError: in Pointy, in T, expected T&lt;:Real, got a value of type Int64</code></pre><p>Type parameters for parametric composite types can be restricted in the same manner:</p><pre><code class="language-julia hljs">struct Point{T&lt;:Real} &lt;: Pointy{T}
    x::T
    y::T
end</code></pre><p>To give a real-world example of how all this parametric type machinery can be useful, here is the actual definition of Julia&#39;s <a href="../base/numbers.html#Base.Rational"><code>Rational</code></a> immutable type (except that we omit the constructor here for simplicity), representing an exact ratio of integers:</p><pre><code class="language-julia hljs">struct Rational{T&lt;:Integer} &lt;: Real
    num::T
    den::T
end</code></pre><p>It only makes sense to take ratios of integer values, so the parameter type <code>T</code> is restricted to being a subtype of <a href="../base/numbers.html#Core.Integer"><code>Integer</code></a>, and a ratio of integers represents a value on the real number line, so any <a href="../base/numbers.html#Base.Rational"><code>Rational</code></a> is an instance of the <a href="../base/numbers.html#Core.Real"><code>Real</code></a> abstraction.</p><h3 id="Tuple-Types"><a class="docs-heading-anchor" href="#Tuple-Types">Tuple Types</a><a id="Tuple-Types-1"></a><a class="docs-heading-anchor-permalink" href="#Tuple-Types" title="Permalink"></a></h3><p>Tuples are an abstraction of the arguments of a function – without the function itself. The salient aspects of a function&#39;s arguments are their order and their types. Therefore a tuple type is similar to a parameterized immutable type where each parameter is the type of one field. For example, a 2-element tuple type resembles the following immutable type:</p><pre><code class="language-julia hljs">struct Tuple2{A,B}
    a::A
    b::B
end</code></pre><p>However, there are three key differences:</p><ul><li>Tuple types may have any number of parameters.</li><li>Tuple types are <em>covariant</em> in their parameters: <code>Tuple{Int}</code> is a subtype of <code>Tuple{Any}</code>. Therefore <code>Tuple{Any}</code> is considered an abstract type, and tuple types are only concrete if their parameters are.</li><li>Tuples do not have field names; fields are only accessed by index.</li></ul><p>Tuple values are written with parentheses and commas. When a tuple is constructed, an appropriate tuple type is generated on demand:</p><pre><code class="language-julia-repl hljs">julia&gt; typeof((1,&quot;foo&quot;,2.5))
Tuple{Int64, String, Float64}</code></pre><p>Note the implications of covariance:</p><pre><code class="language-julia-repl hljs">julia&gt; Tuple{Int,AbstractString} &lt;: Tuple{Real,Any}
true

julia&gt; Tuple{Int,AbstractString} &lt;: Tuple{Real,Real}
false

julia&gt; Tuple{Int,AbstractString} &lt;: Tuple{Real,}
false</code></pre><p>Intuitively, this corresponds to the type of a function&#39;s arguments being a subtype of the function&#39;s signature (when the signature matches).</p><h3 id="Vararg-Tuple-Types"><a class="docs-heading-anchor" href="#Vararg-Tuple-Types">Vararg Tuple Types</a><a id="Vararg-Tuple-Types-1"></a><a class="docs-heading-anchor-permalink" href="#Vararg-Tuple-Types" title="Permalink"></a></h3><p>The last parameter of a tuple type can be the special value <a href="../base/base.html#Core.Vararg"><code>Vararg</code></a>, which denotes any number of trailing elements:</p><pre><code class="language-julia-repl hljs">julia&gt; mytupletype = Tuple{AbstractString,Vararg{Int}}
Tuple{AbstractString, Vararg{Int64}}

julia&gt; isa((&quot;1&quot;,), mytupletype)
true

julia&gt; isa((&quot;1&quot;,1), mytupletype)
true

julia&gt; isa((&quot;1&quot;,1,2), mytupletype)
true

julia&gt; isa((&quot;1&quot;,1,2,3.0), mytupletype)
false</code></pre><p>Moreover <code>Vararg{T}</code> corresponds to zero or more elements of type <code>T</code>. Vararg tuple types are used to represent the arguments accepted by varargs methods (see <a href="functions.html#Varargs-Functions">Varargs Functions</a>).</p><p>The special value <code>Vararg{T,N}</code> (when used as the last parameter of a tuple type) corresponds to exactly <code>N</code> elements of type <code>T</code>.  <code>NTuple{N,T}</code> is a convenient alias for <code>Tuple{Vararg{T,N}}</code>, i.e. a tuple type containing exactly <code>N</code> elements of type <code>T</code>.</p><h3 id="Named-Tuple-Types"><a class="docs-heading-anchor" href="#Named-Tuple-Types">Named Tuple Types</a><a id="Named-Tuple-Types-1"></a><a class="docs-heading-anchor-permalink" href="#Named-Tuple-Types" title="Permalink"></a></h3><p>Named tuples are instances of the <a href="../base/base.html#Core.NamedTuple"><code>NamedTuple</code></a> type, which has two parameters: a tuple of symbols giving the field names, and a tuple type giving the field types. For convenience, <code>NamedTuple</code> types are printed using the <a href="../base/base.html#Base.@NamedTuple"><code>@NamedTuple</code></a> macro which provides a convenient <code>struct</code>-like syntax for declaring these types via <code>key::Type</code> declarations, where an omitted <code>::Type</code> corresponds to <code>::Any</code>.</p><pre><code class="language-julia-repl hljs">julia&gt; typeof((a=1,b=&quot;hello&quot;)) # prints in macro form
@NamedTuple{a::Int64, b::String}

julia&gt; NamedTuple{(:a, :b), Tuple{Int64, String}} # long form of the type
@NamedTuple{a::Int64, b::String}</code></pre><p>The <code>begin ... end</code> form of the <code>@NamedTuple</code> macro allows the declarations to be split across multiple lines (similar to a struct declaration), but is otherwise equivalent:</p><pre><code class="language-julia-repl hljs">julia&gt; @NamedTuple begin
           a::Int
           b::String
       end
@NamedTuple{a::Int64, b::String}</code></pre><p>A <code>NamedTuple</code> type can be used as a constructor, accepting a single tuple argument. The constructed <code>NamedTuple</code> type can be either a concrete type, with both parameters specified, or a type that specifies only field names:</p><pre><code class="language-julia-repl hljs">julia&gt; @NamedTuple{a::Float32,b::String}((1, &quot;&quot;))
(a = 1.0f0, b = &quot;&quot;)

julia&gt; NamedTuple{(:a, :b)}((1, &quot;&quot;))
(a = 1, b = &quot;&quot;)</code></pre><p>If field types are specified, the arguments are converted. Otherwise the types of the arguments are used directly.</p><h3 id="Parametric-Primitive-Types"><a class="docs-heading-anchor" href="#Parametric-Primitive-Types">Parametric Primitive Types</a><a id="Parametric-Primitive-Types-1"></a><a class="docs-heading-anchor-permalink" href="#Parametric-Primitive-Types" title="Permalink"></a></h3><p>Primitive types can also be declared parametrically. For example, pointers are represented as primitive types which would be declared in Julia like this:</p><pre><code class="language-julia hljs"># 32-bit system:
primitive type Ptr{T} 32 end

# 64-bit system:
primitive type Ptr{T} 64 end</code></pre><p>The slightly odd feature of these declarations as compared to typical parametric composite types, is that the type parameter <code>T</code> is not used in the definition of the type itself – it is just an abstract tag, essentially defining an entire family of types with identical structure, differentiated only by their type parameter. Thus, <code>Ptr{Float64}</code> and <code>Ptr{Int64}</code> are distinct types, even though they have identical representations. And of course, all specific pointer types are subtypes of the umbrella <a href="../base/c.html#Core.Ptr"><code>Ptr</code></a> type:</p><pre><code class="language-julia-repl hljs">julia&gt; Ptr{Float64} &lt;: Ptr
true

julia&gt; Ptr{Int64} &lt;: Ptr
true</code></pre><h2 id="UnionAll-Types"><a class="docs-heading-anchor" href="#UnionAll-Types">UnionAll Types</a><a id="UnionAll-Types-1"></a><a class="docs-heading-anchor-permalink" href="#UnionAll-Types" title="Permalink"></a></h2><p>We have said that a parametric type like <code>Ptr</code> acts as a supertype of all its instances (<code>Ptr{Int64}</code> etc.). How does this work? <code>Ptr</code> itself cannot be a normal data type, since without knowing the type of the referenced data the type clearly cannot be used for memory operations. The answer is that <code>Ptr</code> (or other parametric types like <code>Array</code>) is a different kind of type called a <a href="../base/base.html#Core.UnionAll"><code>UnionAll</code></a> type. Such a type expresses the <em>iterated union</em> of types for all values of some parameter.</p><p><code>UnionAll</code> types are usually written using the keyword <code>where</code>. For example <code>Ptr</code> could be more accurately written as <code>Ptr{T} where T</code>, meaning all values whose type is <code>Ptr{T}</code> for some value of <code>T</code>. In this context, the parameter <code>T</code> is also often called a &quot;type variable&quot; since it is like a variable that ranges over types. Each <code>where</code> introduces a single type variable, so these expressions are nested for types with multiple parameters, for example <code>Array{T,N} where N where T</code>.</p><p>The type application syntax <code>A{B,C}</code> requires <code>A</code> to be a <code>UnionAll</code> type, and first substitutes <code>B</code> for the outermost type variable in <code>A</code>. The result is expected to be another <code>UnionAll</code> type, into which <code>C</code> is then substituted. So <code>A{B,C}</code> is equivalent to <code>A{B}{C}</code>. This explains why it is possible to partially instantiate a type, as in <code>Array{Float64}</code>: the first parameter value has been fixed, but the second still ranges over all possible values. Using explicit <code>where</code> syntax, any subset of parameters can be fixed. For example, the type of all 1-dimensional arrays can be written as <code>Array{T,1} where T</code>.</p><p>Type variables can be restricted with subtype relations. <code>Array{T} where T&lt;:Integer</code> refers to all arrays whose element type is some kind of <a href="../base/numbers.html#Core.Integer"><code>Integer</code></a>. The syntax <code>Array{&lt;:Integer}</code> is a convenient shorthand for <code>Array{T} where T&lt;:Integer</code>. Type variables can have both lower and upper bounds. <code>Array{T} where Int&lt;:T&lt;:Number</code> refers to all arrays of <a href="../base/numbers.html#Core.Number"><code>Number</code></a>s that are able to contain <code>Int</code>s (since <code>T</code> must be at least as big as <code>Int</code>). The syntax <code>where T&gt;:Int</code> also works to specify only the lower bound of a type variable, and <code>Array{&gt;:Int}</code> is equivalent to <code>Array{T} where T&gt;:Int</code>.</p><p>Since <code>where</code> expressions nest, type variable bounds can refer to outer type variables. For example <code>Tuple{T,Array{S}} where S&lt;:AbstractArray{T} where T&lt;:Real</code> refers to 2-tuples whose first element is some <a href="../base/numbers.html#Core.Real"><code>Real</code></a>, and whose second element is an <code>Array</code> of any kind of array whose element type contains the type of the first tuple element.</p><p>The <code>where</code> keyword itself can be nested inside a more complex declaration. For example, consider the two types created by the following declarations:</p><pre><code class="language-julia-repl hljs">julia&gt; const T1 = Array{Array{T, 1} where T, 1}
Vector{Vector} (alias for Array{Array{T, 1} where T, 1})

julia&gt; const T2 = Array{Array{T, 1}, 1} where T
Array{Vector{T}, 1} where T</code></pre><p>Type <code>T1</code> defines a 1-dimensional array of 1-dimensional arrays; each of the inner arrays consists of objects of the same type, but this type may vary from one inner array to the next. On the other hand, type <code>T2</code> defines a 1-dimensional array of 1-dimensional arrays all of whose inner arrays must have the same type.  Note that <code>T2</code> is an abstract type, e.g., <code>Array{Array{Int,1},1} &lt;: T2</code>, whereas <code>T1</code> is a concrete type. As a consequence, <code>T1</code> can be constructed with a zero-argument constructor <code>a=T1()</code> but <code>T2</code> cannot.</p><p>There is a convenient syntax for naming such types, similar to the short form of function definition syntax:</p><pre><code class="language-julia hljs">Vector{T} = Array{T, 1}</code></pre><p>This is equivalent to <code>const Vector = Array{T,1} where T</code>. Writing <code>Vector{Float64}</code> is equivalent to writing <code>Array{Float64,1}</code>, and the umbrella type <code>Vector</code> has as instances all <code>Array</code> objects where the second parameter – the number of array dimensions – is 1, regardless of what the element type is. In languages where parametric types must always be specified in full, this is not especially helpful, but in Julia, this allows one to write just <code>Vector</code> for the abstract type including all one-dimensional dense arrays of any element type.</p><h2 id="man-singleton-types"><a class="docs-heading-anchor" href="#man-singleton-types">Singleton types</a><a id="man-singleton-types-1"></a><a class="docs-heading-anchor-permalink" href="#man-singleton-types" title="Permalink"></a></h2><p>Immutable composite types with no fields are called <em>singletons</em>. Formally, if</p><ol><li><code>T</code> is an immutable composite type (i.e. defined with <code>struct</code>),</li><li><code>a isa T &amp;&amp; b isa T</code> implies <code>a === b</code>,</li></ol><p>then <code>T</code> is a singleton type.<sup class="footnote-reference"><a id="citeref-2" href="#footnote-2">[2]</a></sup> <a href="../base/base.html#Base.issingletontype"><code>Base.issingletontype</code></a> can be used to check if a type is a singleton type. <a href="types.html#man-abstract-types">Abstract types</a> cannot be singleton types by construction.</p><p>From the definition, it follows that there can be only one instance of such types:</p><pre><code class="language-julia-repl hljs">julia&gt; struct NoFields
       end

julia&gt; NoFields() === NoFields()
true

julia&gt; Base.issingletontype(NoFields)
true</code></pre><p>The <a href="../base/base.html#Core.:==="><code>===</code></a> function confirms that the constructed instances of <code>NoFields</code> are actually one and the same.</p><p>Parametric types can be singleton types when the above condition holds. For example,</p><pre><code class="language-julia-repl hljs">julia&gt; struct NoFieldsParam{T}
       end

julia&gt; Base.issingletontype(NoFieldsParam) # Can&#39;t be a singleton type ...
false

julia&gt; NoFieldsParam{Int}() isa NoFieldsParam # ... because it has ...
true

julia&gt; NoFieldsParam{Bool}() isa NoFieldsParam # ... multiple instances.
true

julia&gt; Base.issingletontype(NoFieldsParam{Int}) # Parametrized, it is a singleton.
true

julia&gt; NoFieldsParam{Int}() === NoFieldsParam{Int}()
true</code></pre><h2 id="Types-of-functions"><a class="docs-heading-anchor" href="#Types-of-functions">Types of functions</a><a id="Types-of-functions-1"></a><a class="docs-heading-anchor-permalink" href="#Types-of-functions" title="Permalink"></a></h2><p>Each function has its own type, which is a subtype of <code>Function</code>.</p><pre><code class="language-julia-repl hljs">julia&gt; foo41(x) = x + 1
foo41 (generic function with 1 method)

julia&gt; typeof(foo41)
typeof(foo41) (singleton type of function foo41, subtype of Function)</code></pre><p>Note how <code>typeof(foo41)</code> prints as itself. This is merely a convention for printing, as it is a first-class object that can be used like any other value:</p><pre><code class="language-julia-repl hljs">julia&gt; T = typeof(foo41)
typeof(foo41) (singleton type of function foo41, subtype of Function)

julia&gt; T &lt;: Function
true</code></pre><p>Types of functions defined at top-level are singletons. When necessary, you can compare them with <a href="../base/base.html#Core.:==="><code>===</code></a>.</p><p><a href="functions.html#man-anonymous-functions">Closures</a> also have their own type, which is usually printed with names that end in <code>#&lt;number&gt;</code>. Names and types for functions defined at different locations are distinct, but not guaranteed to be printed the same way across sessions.</p><pre><code class="language-julia-repl hljs">julia&gt; typeof(x -&gt; x + 1)
var&quot;#9#10&quot;</code></pre><p>Types of closures are not necessarily singletons.</p><pre><code class="language-julia-repl hljs">julia&gt; addy(y) = x -&gt; x + y
addy (generic function with 1 method)

julia&gt; typeof(addy(1)) === typeof(addy(2))
true

julia&gt; addy(1) === addy(2)
false

julia&gt; Base.issingletontype(typeof(addy(1)))
false</code></pre><h2 id="man-typet-type"><a class="docs-heading-anchor" href="#man-typet-type"><code>Type{T}</code> type selectors</a><a id="man-typet-type-1"></a><a class="docs-heading-anchor-permalink" href="#man-typet-type" title="Permalink"></a></h2><p>For each type <code>T</code>, <code>Type{T}</code> is an abstract parametric type whose only instance is the object <code>T</code>. Until we discuss <a href="methods.html#Parametric-Methods">Parametric Methods</a> and <a href="conversion-and-promotion.html#conversion-and-promotion">conversions</a>, it is difficult to explain the utility of this construct, but in short, it allows one to specialize function behavior on specific types as <em>values</em>. This is useful for writing methods (especially parametric ones) whose behavior depends on a type that is given as an explicit argument rather than implied by the type of one of its arguments.</p><p>Since the definition is a little difficult to parse, let&#39;s look at some examples:</p><pre><code class="language-julia-repl hljs">julia&gt; isa(Float64, Type{Float64})
true

julia&gt; isa(Real, Type{Float64})
false

julia&gt; isa(Real, Type{Real})
true

julia&gt; isa(Float64, Type{Real})
false</code></pre><p>In other words, <a href="../base/base.html#Core.isa"><code>isa(A, Type{B})</code></a> is true if and only if <code>A</code> and <code>B</code> are the same object and that object is a type.</p><p>In particular, since parametric types are <a href="types.html#man-parametric-composite-types">invariant</a>, we have</p><pre><code class="language-julia-repl hljs">julia&gt; struct TypeParamExample{T}
           x::T
       end

julia&gt; TypeParamExample isa Type{TypeParamExample}
true

julia&gt; TypeParamExample{Int} isa Type{TypeParamExample}
false

julia&gt; TypeParamExample{Int} isa Type{TypeParamExample{Int}}
true</code></pre><p>Without the parameter, <code>Type</code> is simply an abstract type which has all type objects as its instances:</p><pre><code class="language-julia-repl hljs">julia&gt; isa(Type{Float64}, Type)
true

julia&gt; isa(Float64, Type)
true

julia&gt; isa(Real, Type)
true</code></pre><p>Any object that is not a type is not an instance of <code>Type</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; isa(1, Type)
false

julia&gt; isa(&quot;foo&quot;, Type)
false</code></pre><p>While <code>Type</code> is part of Julia&#39;s type hierarchy like any other abstract parametric type, it is not commonly used outside method signatures except in some special cases. Another important use case for <code>Type</code> is sharpening field types which would otherwise be captured less precisely, e.g. as <a href="types.html#man-declared-types"><code>DataType</code></a> in the example below where the default constructor could lead to performance problems in code relying on the precise wrapped type (similarly to <a href="performance-tips.html#man-performance-abstract-container">abstract type parameters</a>).</p><pre><code class="language-julia-repl hljs">julia&gt; struct WrapType{T}
       value::T
       end

julia&gt; WrapType(Float64) # default constructor, note DataType
WrapType{DataType}(Float64)

julia&gt; WrapType(::Type{T}) where T = WrapType{Type{T}}(T)
WrapType

julia&gt; WrapType(Float64) # sharpened constructor, note more precise Type{Float64}
WrapType{Type{Float64}}(Float64)</code></pre><h2 id="Type-Aliases"><a class="docs-heading-anchor" href="#Type-Aliases">Type Aliases</a><a id="Type-Aliases-1"></a><a class="docs-heading-anchor-permalink" href="#Type-Aliases" title="Permalink"></a></h2><p>Sometimes it is convenient to introduce a new name for an already expressible type. This can be done with a simple assignment statement. For example, <code>UInt</code> is aliased to either <a href="../base/numbers.html#Core.UInt32"><code>UInt32</code></a> or <a href="../base/numbers.html#Core.UInt64"><code>UInt64</code></a> as is appropriate for the size of pointers on the system:</p><pre><code class="language-julia-repl hljs"># 32-bit system:
julia&gt; UInt
UInt32

# 64-bit system:
julia&gt; UInt
UInt64</code></pre><p>This is accomplished via the following code in <code>base/boot.jl</code>:</p><pre><code class="language-julia hljs">if Int === Int64
    const UInt = UInt64
else
    const UInt = UInt32
end</code></pre><p>Of course, this depends on what <code>Int</code> is aliased to – but that is predefined to be the correct type – either <a href="../base/numbers.html#Core.Int32"><code>Int32</code></a> or <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p><p>(Note that unlike <code>Int</code>, <code>Float</code> does not exist as a type alias for a specific sized <a href="../base/numbers.html#Core.AbstractFloat"><code>AbstractFloat</code></a>. Unlike with integer registers, where the size of <code>Int</code> reflects the size of a native pointer on that machine, the floating point register sizes are specified by the IEEE-754 standard.)</p><p>Type aliases may be parametrized:</p><pre><code class="language-julia-repl hljs">julia&gt; const Family{T} = Set{T}
Set

julia&gt; Family{Char} === Set{Char}
true</code></pre><h2 id="Operations-on-Types"><a class="docs-heading-anchor" href="#Operations-on-Types">Operations on Types</a><a id="Operations-on-Types-1"></a><a class="docs-heading-anchor-permalink" href="#Operations-on-Types" title="Permalink"></a></h2><p>Since types in Julia are themselves objects, ordinary functions can operate on them. Some functions that are particularly useful for working with or exploring types have already been introduced, such as the <code>&lt;:</code> operator, which indicates whether its left hand operand is a subtype of its right hand operand.</p><p>The <a href="../base/base.html#Core.isa"><code>isa</code></a> function tests if an object is of a given type and returns true or false:</p><pre><code class="language-julia-repl hljs">julia&gt; isa(1, Int)
true

julia&gt; isa(1, AbstractFloat)
false</code></pre><p>The <a href="../base/base.html#Core.typeof"><code>typeof</code></a> function, already used throughout the manual in examples, returns the type of its argument. Since, as noted above, types are objects, they also have types, and we can ask what their types are:</p><pre><code class="language-julia-repl hljs">julia&gt; typeof(Rational{Int})
DataType

julia&gt; typeof(Union{Real,String})
Union</code></pre><p>What if we repeat the process? What is the type of a type of a type? As it happens, types are all composite values and thus all have a type of <code>DataType</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; typeof(DataType)
DataType

julia&gt; typeof(Union)
DataType</code></pre><p><code>DataType</code> is its own type.</p><p>Another operation that applies to some types is <a href="../base/base.html#Base.supertype"><code>supertype</code></a>, which reveals a type&#39;s supertype. Only declared types (<code>DataType</code>) have unambiguous supertypes:</p><pre><code class="language-julia-repl hljs">julia&gt; supertype(Float64)
AbstractFloat

julia&gt; supertype(Number)
Any

julia&gt; supertype(AbstractString)
Any

julia&gt; supertype(Any)
Any</code></pre><p>If you apply <a href="../base/base.html#Base.supertype"><code>supertype</code></a> to other type objects (or non-type objects), a <a href="../base/base.html#Core.MethodError"><code>MethodError</code></a> is raised:</p><pre><code class="language-julia-repl hljs">julia&gt; supertype(Union{Float64,Int64})
ERROR: MethodError: no method matching supertype(::Type{Union{Float64, Int64}})
The function `supertype` exists, but no method is defined for this combination of argument types.

Closest candidates are:
[...]</code></pre><h2 id="man-custom-pretty-printing"><a class="docs-heading-anchor" href="#man-custom-pretty-printing">Custom pretty-printing</a><a id="man-custom-pretty-printing-1"></a><a class="docs-heading-anchor-permalink" href="#man-custom-pretty-printing" title="Permalink"></a></h2><p>Often, one wants to customize how instances of a type are displayed.  This is accomplished by overloading the <a href="../base/io-network.html#Base.show-Tuple{IO, Any}"><code>show</code></a> function.  For example, suppose we define a type to represent complex numbers in polar form:</p><pre><code class="language-julia-repl hljs">julia&gt; struct Polar{T&lt;:Real} &lt;: Number
           r::T
           Θ::T
       end

julia&gt; Polar(r::Real,Θ::Real) = Polar(promote(r,Θ)...)
Polar</code></pre><p>Here, we&#39;ve added a custom constructor function so that it can take arguments of different <a href="../base/numbers.html#Core.Real"><code>Real</code></a> types and promote them to a common type (see <a href="constructors.html#man-constructors">Constructors</a> and <a href="conversion-and-promotion.html#conversion-and-promotion">Conversion and Promotion</a>). (Of course, we would have to define lots of other methods, too, to make it act like a <a href="../base/numbers.html#Core.Number"><code>Number</code></a>, e.g. <code>+</code>, <code>*</code>, <code>one</code>, <code>zero</code>, promotion rules and so on.) By default, instances of this type display rather simply, with information about the type name and the field values, as e.g. <code>Polar{Float64}(3.0,4.0)</code>.</p><p>If we want it to display instead as <code>3.0 * exp(4.0im)</code>, we would define the following method to print the object to a given output object <code>io</code> (representing a file, terminal, buffer, etcetera; see <a href="networking-and-streams.html#Networking-and-Streams">Networking and Streams</a>):</p><pre><code class="language-julia-repl hljs">julia&gt; Base.show(io::IO, z::Polar) = print(io, z.r, &quot; * exp(&quot;, z.Θ, &quot;im)&quot;)</code></pre><p>More fine-grained control over display of <code>Polar</code> objects is possible. In particular, sometimes one wants both a verbose multi-line printing format, used for displaying a single object in the REPL and other interactive environments, and also a more compact single-line format used for <a href="../base/io-network.html#Base.print"><code>print</code></a> or for displaying the object as part of another object (e.g. in an array). Although by default the <code>show(io, z)</code> function is called in both cases, you can define a <em>different</em> multi-line format for displaying an object by overloading a three-argument form of <code>show</code> that takes the <code>text/plain</code> MIME type as its second argument (see <a href="../base/io-network.html#Multimedia-I/O">Multimedia I/O</a>), for example:</p><pre><code class="language-julia-repl hljs">julia&gt; Base.show(io::IO, ::MIME&quot;text/plain&quot;, z::Polar{T}) where{T} =
           print(io, &quot;Polar{$T} complex number:\n   &quot;, z)</code></pre><p>(Note that <code>print(..., z)</code> here will call the 2-argument <code>show(io, z)</code> method.) This results in:</p><pre><code class="language-julia-repl hljs">julia&gt; Polar(3, 4.0)
Polar{Float64} complex number:
   3.0 * exp(4.0im)

julia&gt; [Polar(3, 4.0), Polar(4.0,5.3)]
2-element Vector{Polar{Float64}}:
 3.0 * exp(4.0im)
 4.0 * exp(5.3im)</code></pre><p>where the single-line <code>show(io, z)</code> form is still used for an array of <code>Polar</code> values.   Technically, the REPL calls <code>display(z)</code> to display the result of executing a line, which defaults to <code>show(stdout, MIME(&quot;text/plain&quot;), z)</code>, which in turn defaults to <code>show(stdout, z)</code>, but you should <em>not</em> define new <a href="../base/io-network.html#Base.Multimedia.display"><code>display</code></a> methods unless you are defining a new multimedia display handler (see <a href="../base/io-network.html#Multimedia-I/O">Multimedia I/O</a>).</p><p>Moreover, you can also define <code>show</code> methods for other MIME types in order to enable richer display (HTML, images, etcetera) of objects in environments that support this (e.g. IJulia).   For example, we can define formatted HTML display of <code>Polar</code> objects, with superscripts and italics, via:</p><pre><code class="language-julia-repl hljs">julia&gt; Base.show(io::IO, ::MIME&quot;text/html&quot;, z::Polar{T}) where {T} =
           println(io, &quot;&lt;code&gt;Polar{$T}&lt;/code&gt; complex number: &quot;,
                   z.r, &quot; &lt;i&gt;e&lt;/i&gt;&lt;sup&gt;&quot;, z.Θ, &quot; &lt;i&gt;i&lt;/i&gt;&lt;/sup&gt;&quot;)</code></pre><p>A <code>Polar</code> object will then display automatically using HTML in an environment that supports HTML display, but you can call <code>show</code> manually to get HTML output if you want:</p><pre><code class="language-julia-repl hljs">julia&gt; show(stdout, &quot;text/html&quot;, Polar(3.0,4.0))
&lt;code&gt;Polar{Float64}&lt;/code&gt; complex number: 3.0 &lt;i&gt;e&lt;/i&gt;&lt;sup&gt;4.0 &lt;i&gt;i&lt;/i&gt;&lt;/sup&gt;</code></pre><p>An HTML renderer would display this as: <code>Polar{Float64}</code> complex number: 3.0 <i>e</i><sup>4.0 <i>i</i></sup></p><p>As a rule of thumb, the single-line <code>show</code> method should print a valid Julia expression for creating the shown object.  When this <code>show</code> method contains infix operators, such as the multiplication operator (<code>*</code>) in our single-line <code>show</code> method for <code>Polar</code> above, it may not parse correctly when printed as part of another object.  To see this, consider the expression object (see <a href="metaprogramming.html#Program-representation">Program representation</a>) which takes the square of a specific instance of our <code>Polar</code> type:</p><pre><code class="language-julia-repl hljs">julia&gt; a = Polar(3, 4.0)
Polar{Float64} complex number:
   3.0 * exp(4.0im)

julia&gt; print(:($a^2))
3.0 * exp(4.0im) ^ 2</code></pre><p>Because the operator <code>^</code> has higher precedence than <code>*</code> (see <a href="mathematical-operations.html#Operator-Precedence-and-Associativity">Operator Precedence and Associativity</a>), this output does not faithfully represent the expression <code>a ^ 2</code> which should be equal to <code>(3.0 * exp(4.0im)) ^ 2</code>.  To solve this issue, we must make a custom method for <code>Base.show_unquoted(io::IO, z::Polar, indent::Int, precedence::Int)</code>, which is called internally by the expression object when printing:</p><pre><code class="language-julia-repl hljs">julia&gt; function Base.show_unquoted(io::IO, z::Polar, ::Int, precedence::Int)
           if Base.operator_precedence(:*) &lt;= precedence
               print(io, &quot;(&quot;)
               show(io, z)
               print(io, &quot;)&quot;)
           else
               show(io, z)
           end
       end

julia&gt; :($a^2)
:((3.0 * exp(4.0im)) ^ 2)</code></pre><p>The method defined above adds parentheses around the call to <code>show</code> when the precedence of the calling operator is higher than or equal to the precedence of multiplication.  This check allows expressions which parse correctly without the parentheses (such as <code>:($a + 2)</code> and <code>:($a == 2)</code>) to omit them when printing:</p><pre><code class="language-julia-repl hljs">julia&gt; :($a + 2)
:(3.0 * exp(4.0im) + 2)

julia&gt; :($a == 2)
:(3.0 * exp(4.0im) == 2)</code></pre><p>In some cases, it is useful to adjust the behavior of <code>show</code> methods depending on the context. This can be achieved via the <a href="../base/io-network.html#Base.IOContext"><code>IOContext</code></a> type, which allows passing contextual properties together with a wrapped IO stream. For example, we can build a shorter representation in our <code>show</code> method when the <code>:compact</code> property is set to <code>true</code>, falling back to the long representation if the property is <code>false</code> or absent:</p><pre><code class="language-julia-repl hljs">julia&gt; function Base.show(io::IO, z::Polar)
           if get(io, :compact, false)::Bool
               print(io, z.r, &quot;ℯ&quot;, z.Θ, &quot;im&quot;)
           else
               print(io, z.r, &quot; * exp(&quot;, z.Θ, &quot;im)&quot;)
           end
       end</code></pre><p>This new compact representation will be used when the passed IO stream is an <code>IOContext</code> object with the <code>:compact</code> property set. In particular, this is the case when printing arrays with multiple columns (where horizontal space is limited):</p><pre><code class="language-julia-repl hljs">julia&gt; show(IOContext(stdout, :compact=&gt;true), Polar(3, 4.0))
3.0ℯ4.0im

julia&gt; [Polar(3, 4.0) Polar(4.0,5.3)]
1×2 Matrix{Polar{Float64}}:
 3.0ℯ4.0im  4.0ℯ5.3im</code></pre><p>See the <a href="../base/io-network.html#Base.IOContext"><code>IOContext</code></a> documentation for a list of common properties which can be used to adjust printing.</p><h2 id="&quot;Value-types&quot;"><a class="docs-heading-anchor" href="#&quot;Value-types&quot;">&quot;Value types&quot;</a><a id="&quot;Value-types&quot;-1"></a><a class="docs-heading-anchor-permalink" href="#&quot;Value-types&quot;" title="Permalink"></a></h2><p>In Julia, you can&#39;t dispatch on a <em>value</em> such as <code>true</code> or <code>false</code>. However, you can dispatch on parametric types, and Julia allows you to include &quot;plain bits&quot; values (Types, Symbols, Integers, floating-point numbers, tuples, etc.) as type parameters.  A common example is the dimensionality parameter in <code>Array{T,N}</code>, where <code>T</code> is a type (e.g., <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a>) but <code>N</code> is just an <code>Int</code>.</p><p>You can create your own custom types that take values as parameters, and use them to control dispatch of custom types. By way of illustration of this idea, let&#39;s introduce the parametric type <code>Val{x}</code>, and its constructor <code>Val(x) = Val{x}()</code>, which serves as a customary way to exploit this technique for cases where you don&#39;t need a more elaborate hierarchy.</p><p><a href="../base/base.html#Base.Val"><code>Val</code></a> is defined as:</p><pre><code class="language-julia-repl hljs">julia&gt; struct Val{x}
       end

julia&gt; Val(x) = Val{x}()
Val</code></pre><p>There is no more to the implementation of <code>Val</code> than this.  Some functions in Julia&#39;s standard library accept <code>Val</code> instances as arguments, and you can also use it to write your own functions.  For example:</p><pre><code class="language-julia-repl hljs">julia&gt; firstlast(::Val{true}) = &quot;First&quot;
firstlast (generic function with 1 method)

julia&gt; firstlast(::Val{false}) = &quot;Last&quot;
firstlast (generic function with 2 methods)

julia&gt; firstlast(Val(true))
&quot;First&quot;

julia&gt; firstlast(Val(false))
&quot;Last&quot;</code></pre><p>For consistency across Julia, the call site should always pass a <code>Val</code> <em>instance</em> rather than using a <em>type</em>, i.e., use <code>foo(Val(:bar))</code> rather than <code>foo(Val{:bar})</code>.</p><p>It&#39;s worth noting that it&#39;s extremely easy to mis-use parametric &quot;value&quot; types, including <code>Val</code>; in unfavorable cases, you can easily end up making the performance of your code much <em>worse</em>.  In particular, you would never want to write actual code as illustrated above.  For more information about the proper (and improper) uses of <code>Val</code>, please read <a href="performance-tips.html#man-performance-value-type">the more extensive discussion in the performance tips</a>.</p><section class="footnotes is-size-7"><ul><li class="footnote" id="footnote-1"><a class="tag is-link" href="#citeref-1">1</a>&quot;Small&quot; is defined by the <code>max_union_splitting</code> configuration, which currently defaults to 4.</li><li class="footnote" id="footnote-2"><a class="tag is-link" href="#citeref-2">2</a>A few popular languages have singleton types, including Haskell, Scala and Ruby.</li></ul></section></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="variables-and-scoping.html">« Scope of Variables</a><a class="docs-footer-nextpage" href="methods.html">Methods »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
