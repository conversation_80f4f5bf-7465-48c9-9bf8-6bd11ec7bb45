<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>EscapeAnalysis · The Julia Language</title><meta name="title" content="EscapeAnalysis · The Julia Language"/><meta property="og:title" content="EscapeAnalysis · The Julia Language"/><meta property="twitter:title" content="EscapeAnalysis · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox" checked/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="ast.html">Julia ASTs</a></li><li><a class="tocitem" href="types.html">More about types</a></li><li><a class="tocitem" href="object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="functions.html">Julia Functions</a></li><li><a class="tocitem" href="cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="subarrays.html">SubArrays</a></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li class="is-active"><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a><ul class="internal"><li><a class="tocitem" href="#Try-it-out!"><span>Try it out!</span></a></li><li><a class="tocitem" href="#Analysis-Design"><span>Analysis Design</span></a></li><li><a class="tocitem" href="#Analysis-Usage"><span>Analysis Usage</span></a></li></ul></li><li><a class="tocitem" href="aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Documentation of Julia&#39;s Internals</a></li><li class="is-active"><a href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/EscapeAnalysis.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="EscapeAnalysis"><a class="docs-heading-anchor" href="#EscapeAnalysis"><code>EscapeAnalysis</code></a><a id="EscapeAnalysis-1"></a><a class="docs-heading-anchor-permalink" href="#EscapeAnalysis" title="Permalink"></a></h1><p><code>Core.Compiler.EscapeAnalysis</code> is a compiler utility module that aims to analyze escape information of <a href="ssair.html#Julia-SSA-form-IR">Julia&#39;s SSA-form IR</a> a.k.a. <code>IRCode</code>.</p><p>This escape analysis aims to:</p><ul><li>leverage Julia&#39;s high-level semantics, especially reason about escapes and aliasing via inter-procedural calls</li><li>be versatile enough to be used for various optimizations including <a href="https://github.com/JuliaLang/julia/pull/43888">alias-aware SROA</a>, <a href="https://github.com/JuliaLang/julia/pull/44056">early <code>finalize</code> insertion</a>, <a href="https://github.com/JuliaLang/julia/pull/42465">copy-free <code>ImmutableArray</code> construction</a>, stack allocation of mutable objects, and so on.</li><li>achieve a simple implementation based on a fully backward data-flow analysis implementation as well as a new lattice design that combines orthogonal lattice properties</li></ul><h2 id="Try-it-out!"><a class="docs-heading-anchor" href="#Try-it-out!">Try it out!</a><a id="Try-it-out!-1"></a><a class="docs-heading-anchor-permalink" href="#Try-it-out!" title="Permalink"></a></h2><p>You can give a try to the escape analysis by loading the <code>EAUtils.jl</code> utility script that defines the convenience entries <code>code_escapes</code> and <code>@code_escapes</code> for testing and debugging purposes:</p><pre><code class="language-julia-repl hljs" style="display:block;">julia&gt; let JULIA_DIR = normpath(Sys.BINDIR, &quot;..&quot;, &quot;share&quot;, &quot;julia&quot;)
           # load `EscapeAnalysis` module to define the core analysis code
           include(normpath(JULIA_DIR, &quot;base&quot;, &quot;compiler&quot;, &quot;ssair&quot;, &quot;EscapeAnalysis&quot;, &quot;EscapeAnalysis.jl&quot;))
           using .EscapeAnalysis
           # load `EAUtils` module to define the utilities
           include(normpath(JULIA_DIR, &quot;test&quot;, &quot;compiler&quot;, &quot;EscapeAnalysis&quot;, &quot;EAUtils.jl&quot;))
           using .EAUtils
       end</code><code class="nohighlight hljs ansi" style="display:block;"></code><br/><code class="language-julia-repl hljs" style="display:block;">julia&gt; mutable struct SafeRef{T}
           x::T
       end</code><code class="nohighlight hljs ansi" style="display:block;"></code><br/><code class="language-julia-repl hljs" style="display:block;">julia&gt; Base.getindex(x::SafeRef) = x.x;</code><code class="nohighlight hljs ansi" style="display:block;"></code><br/><code class="language-julia-repl hljs" style="display:block;">julia&gt; Base.setindex!(x::SafeRef, v) = x.x = v;</code><code class="nohighlight hljs ansi" style="display:block;"></code><br/><code class="language-julia-repl hljs" style="display:block;">julia&gt; Base.isassigned(x::SafeRef) = true;</code><code class="nohighlight hljs ansi" style="display:block;"></code><br/><code class="language-julia-repl hljs" style="display:block;">julia&gt; get′(x) = isassigned(x) ? x[] : throw(x);</code><code class="nohighlight hljs ansi" style="display:block;"></code><br/><code class="language-julia-repl hljs" style="display:block;">julia&gt; result = code_escapes((String,String,String,String)) do s1, s2, s3, s4
           r1 = Ref(s1)
           r2 = Ref(s2)
           r3 = SafeRef(s3)
           try
               s1 = get′(r1)
               ret = sizeof(s1)
           catch err
               global GV = err # will definitely escape `r1`
           end
           s2 = get′(r2)       # still `r2` doesn&#39;t escape fully
           s3 = get′(r3)       # still `r3` doesn&#39;t escape fully
           s4 = sizeof(s4)     # the argument `s4` doesn&#39;t escape here
           return s2, s3, s4
       end</code><code class="nohighlight hljs ansi" style="display:block;">#1(<span class="sgr31">X s1::String</span>, <span class="sgr33">↑ s2::String</span>, <span class="sgr34">↑ s3::String</span>, <span class="sgr36">✓ s4::String</span>)<span class="sgr1"> in Main at REPL[7]:2</span>
<span class="sgr31">X  </span><span class="sgr90">1 ──</span> %1  = %new(Base.RefValue{String}, _2)<span class="sgr36">::Base.RefValue{String}</span>
<span class="sgr33">*′ </span><span class="sgr90">│   </span> %2  = %new(Base.RefValue{String}, _3)<span class="sgr36">::Base.RefValue{String}</span>
<span class="sgr32">✓′ </span><span class="sgr90">└───</span> %3  = %new(Main.SafeRef{String}, _4)<span class="sgr36">::Main.SafeRef{String}</span>
<span class="sgr32">✓′ </span><span class="sgr90">2 ──</span> %4  = ϒ (%3)<span class="sgr36">::Main.SafeRef{String}</span>
<span class="sgr33">*′ </span><span class="sgr90">│   </span> %5  = ϒ (%2)<span class="sgr36">::Base.RefValue{String}</span>
<span class="sgr36">✓  </span><span class="sgr90">│   </span> %6  = ϒ (_5)<span class="sgr36">::String</span>
◌  <span class="sgr90">└───</span> %7  = enter #8
◌  <span class="sgr90">3 ──</span> %8  = Base.isdefined(%1, :x)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #5 if not %8
<span class="sgr31">X  </span><span class="sgr90">4 ──</span>       Base.getfield(%1, :x)<span class="sgr90">::String</span>
◌  <span class="sgr90">└───</span>       goto #6
◌  <span class="sgr90">5 ──</span>       Main.throw(%1)<span class="sgr90">::Union{}</span>
◌  <span class="sgr90">└───</span>       unreachable
◌  <span class="sgr90">6 ──</span>       $(Expr(:leave, :(%7)))
◌  <span class="sgr90">7 ──</span>       goto #11
<span class="sgr32">✓′ </span><span class="sgr90">8 ┄─</span> %16 = φᶜ (%4)<span class="sgr36">::Main.SafeRef{String}</span>
<span class="sgr33">*′ </span><span class="sgr90">│   </span> %17 = φᶜ (%5)<span class="sgr36">::Base.RefValue{String}</span>
<span class="sgr36">✓  </span><span class="sgr90">│   </span> %18 = φᶜ (%6)<span class="sgr36">::String</span>
<span class="sgr31">X  </span><span class="sgr90">└───</span> %19 = $(Expr(:the_exception))<span class="sgr36">::Any</span>
◌  <span class="sgr90">9 ──</span>       nothing<span class="sgr90">::Nothing</span>
◌  <span class="sgr90">10 ─</span>       (Main.GV = %19)<span class="sgr90">::Any</span>
◌  <span class="sgr90">└───</span>       $(Expr(:pop_exception, :(%7)))<span class="sgr90">::Core.Const(nothing)</span>
<span class="sgr32">✓′ </span><span class="sgr90">11 ┄</span> %23 = φ (#7 =&gt; %3, #10 =&gt; %16)<span class="sgr36">::Main.SafeRef{String}</span>
<span class="sgr33">*′ </span><span class="sgr90">│   </span> %24 = φ (#7 =&gt; %2, #10 =&gt; %17)<span class="sgr36">::Base.RefValue{String}</span>
<span class="sgr36">✓  </span><span class="sgr90">│   </span> %25 = φ (#7 =&gt; _5, #10 =&gt; %18)<span class="sgr36">::String</span>
◌  <span class="sgr90">│   </span> %26 = Base.isdefined(%24, :x)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #13 if not %26
<span class="sgr33">↑  </span><span class="sgr90">12 ─</span> %28 = Base.getfield(%24, :x)<span class="sgr36">::String</span>
◌  <span class="sgr90">└───</span>       goto #14
◌  <span class="sgr90">13 ─</span>       Main.throw(%24)<span class="sgr90">::Union{}</span>
◌  <span class="sgr90">└───</span>       unreachable
<span class="sgr34">↑  </span><span class="sgr90">14 ─</span> %32 = Base.getfield(%23, :x)<span class="sgr36">::String</span>
◌  <span class="sgr90">│   </span> %33 = Core.sizeof(%25)<span class="sgr36">::Int64</span>
<span class="sgr34">↑′ </span><span class="sgr90">│   </span> %34 = Core.tuple(%28, %32, %33)<span class="sgr36">::Tuple{String, String, Int64}</span>
◌  <span class="sgr90">└───</span>       return %34</code></pre><p>The symbols on the side of each call argument and SSA statements represent the following meaning:</p><ul><li><code>◌</code> (plain): this value is not analyzed because escape information of it won&#39;t be used anyway (when the object is <code>isbitstype</code> for example)</li><li><code>✓</code> (green or cyan): this value never escapes (<code>has_no_escape(result.state[x])</code> holds), colored blue if it has arg escape also (<code>has_arg_escape(result.state[x])</code> holds)</li><li><code>↑</code> (blue or yellow): this value can escape to the caller via return (<code>has_return_escape(result.state[x])</code> holds), colored yellow if it has unhandled thrown escape also (<code>has_thrown_escape(result.state[x])</code> holds)</li><li><code>X</code> (red): this value can escape to somewhere the escape analysis can&#39;t reason about like escapes to a global memory (<code>has_all_escape(result.state[x])</code> holds)</li><li><code>*</code> (bold): this value&#39;s escape state is between the <code>ReturnEscape</code> and <code>AllEscape</code> in the partial order of <a href="EscapeAnalysis.html#Core.Compiler.EscapeAnalysis.EscapeInfo"><code>EscapeInfo</code></a>, colored yellow if it has unhandled thrown escape also (<code>has_thrown_escape(result.state[x])</code> holds)</li><li><code>′</code>: this value has additional object field / array element information in its <code>AliasInfo</code> property</li></ul><p>Escape information of each call argument and SSA value can be inspected programmatically as like:</p><pre><code class="language-julia-repl hljs" style="display:block;">julia&gt; result.state[Core.Argument(3)] # get EscapeInfo of `s2`</code><code class="nohighlight hljs ansi" style="display:block;"><span class="sgr33">ReturnEscape</span></code><br/><code class="language-julia-repl hljs" style="display:block;">julia&gt; result.state[Core.SSAValue(3)] # get EscapeInfo of `r3`</code><code class="nohighlight hljs ansi" style="display:block;"><span class="sgr32">NoEscape′</span></code></pre><h2 id="Analysis-Design"><a class="docs-heading-anchor" href="#Analysis-Design">Analysis Design</a><a id="Analysis-Design-1"></a><a class="docs-heading-anchor-permalink" href="#Analysis-Design" title="Permalink"></a></h2><h3 id="Lattice-Design"><a class="docs-heading-anchor" href="#Lattice-Design">Lattice Design</a><a id="Lattice-Design-1"></a><a class="docs-heading-anchor-permalink" href="#Lattice-Design" title="Permalink"></a></h3><p><code>EscapeAnalysis</code> is implemented as a <a href="https://en.wikipedia.org/wiki/Data-flow_analysis">data-flow analysis</a> that works on a lattice of <a href="EscapeAnalysis.html#Core.Compiler.EscapeAnalysis.EscapeInfo"><code>x::EscapeInfo</code></a>, which is composed of the following properties:</p><ul><li><code>x.Analyzed::Bool</code>: not formally part of the lattice, only indicates <code>x</code> has not been analyzed or not</li><li><code>x.ReturnEscape::BitSet</code>: records SSA statements where <code>x</code> can escape to the caller via return</li><li><code>x.ThrownEscape::BitSet</code>: records SSA statements where <code>x</code> can be thrown as exception (used for the <a href="EscapeAnalysis.html#EA-Exception-Handling">exception handling</a> described below)</li><li><code>x.AliasInfo</code>: maintains all possible values that can be aliased to fields or array elements of <code>x</code> (used for the <a href="EscapeAnalysis.html#EA-Alias-Analysis">alias analysis</a> described below)</li><li><code>x.ArgEscape::Int</code> (not implemented yet): indicates it will escape to the caller through <code>setfield!</code> on argument(s)</li></ul><p>These attributes can be combined to create a partial lattice that has a finite height, given the invariant that an input program has a finite number of statements, which is assured by Julia&#39;s semantics. The clever part of this lattice design is that it enables a simpler implementation of lattice operations by allowing them to handle each lattice property separately<sup class="footnote-reference"><a id="citeref-LatticeDesign" href="#footnote-LatticeDesign">[LatticeDesign]</a></sup>.</p><h3 id="Backward-Escape-Propagation"><a class="docs-heading-anchor" href="#Backward-Escape-Propagation">Backward Escape Propagation</a><a id="Backward-Escape-Propagation-1"></a><a class="docs-heading-anchor-permalink" href="#Backward-Escape-Propagation" title="Permalink"></a></h3><p>This escape analysis implementation is based on the data-flow algorithm described in the paper<sup class="footnote-reference"><a id="citeref-MM02" href="#footnote-MM02">[MM02]</a></sup>. The analysis works on the lattice of <code>EscapeInfo</code> and transitions lattice elements from the bottom to the top until every lattice element gets converged to a fixed point by maintaining a (conceptual) working set that contains program counters corresponding to remaining SSA statements to be analyzed. The analysis manages a single global state that tracks <code>EscapeInfo</code> of each argument and SSA statement, but also note that some flow-sensitivity is encoded as program counters recorded in <code>EscapeInfo</code>&#39;s <code>ReturnEscape</code> property, which can be combined with domination analysis later to reason about flow-sensitivity if necessary.</p><p>One distinctive design of this escape analysis is that it is fully <em>backward</em>, i.e. escape information flows <em>from usages to definitions</em>. For example, in the code snippet below, EA first analyzes the statement <code>return %1</code> and imposes <code>ReturnEscape</code> on <code>%1</code> (corresponding to <code>obj</code>), and then it analyzes <code>%1 = %new(Base.RefValue{String, _2}))</code> and propagates the <code>ReturnEscape</code> imposed on <code>%1</code> to the call argument <code>_2</code> (corresponding to <code>s</code>):</p><pre><code class="language-julia-repl hljs" style="display:block;">julia&gt; code_escapes((String,)) do s
           obj = Ref(s)
           return obj
       end</code><code class="nohighlight hljs ansi" style="display:block;">#3(<span class="sgr34">↑ s::String</span>)<span class="sgr1"> in Main at REPL[1]:2</span>
<span class="sgr34">↑′ </span><span class="sgr90">1 ─</span> %1 = %new(Base.RefValue{String}, _2)<span class="sgr36">::Base.RefValue{String}</span>
◌  <span class="sgr90">└──</span>      return %1</code></pre><p>The key observation here is that this backward analysis allows escape information to flow naturally along the use-def chain rather than control-flow<sup class="footnote-reference"><a id="citeref-BackandForth" href="#footnote-BackandForth">[BackandForth]</a></sup>. As a result this scheme enables a simple implementation of escape analysis, e.g. <code>PhiNode</code> for example can be handled simply by propagating escape information imposed on a <code>PhiNode</code> to its predecessor values:</p><pre><code class="language-julia-repl hljs" style="display:block;">julia&gt; code_escapes((Bool, String, String)) do cnd, s, t
           if cnd
               obj = Ref(s)
           else
               obj = Ref(t)
           end
           return obj
       end</code><code class="nohighlight hljs ansi" style="display:block;">#5(<span class="sgr36">✓ cnd::Bool</span>, <span class="sgr34">↑ s::String</span>, <span class="sgr34">↑ t::String</span>)<span class="sgr1"> in Main at REPL[1]:2</span>
◌  <span class="sgr90">1 ─</span>      goto #3 if not _2
<span class="sgr34">↑′ </span><span class="sgr90">2 ─</span> %2 = %new(Base.RefValue{String}, _3)<span class="sgr36">::Base.RefValue{String}</span>
◌  <span class="sgr90">└──</span>      goto #4
<span class="sgr34">↑′ </span><span class="sgr90">3 ─</span> %4 = %new(Base.RefValue{String}, _4)<span class="sgr36">::Base.RefValue{String}</span>
<span class="sgr34">↑′ </span><span class="sgr90">4 ┄</span> %5 = φ (#2 =&gt; %2, #3 =&gt; %4)<span class="sgr36">::Base.RefValue{String}</span>
◌  <span class="sgr90">└──</span>      return %5</code></pre><h3 id="EA-Alias-Analysis"><a class="docs-heading-anchor" href="#EA-Alias-Analysis">Alias Analysis</a><a id="EA-Alias-Analysis-1"></a><a class="docs-heading-anchor-permalink" href="#EA-Alias-Analysis" title="Permalink"></a></h3><p><code>EscapeAnalysis</code> implements a backward field analysis in order to reason about escapes imposed on object fields with certain accuracy, and <code>x::EscapeInfo</code>&#39;s <code>x.AliasInfo</code> property exists for this purpose. It records all possible values that can be aliased to fields of <code>x</code> at &quot;usage&quot; sites, and then the escape information of that recorded values are propagated to the actual field values later at &quot;definition&quot; sites. More specifically, the analysis records a value that may be aliased to a field of object by analyzing <code>getfield</code> call, and then it propagates its escape information to the field when analyzing <code>%new(...)</code> expression or <code>setfield!</code> call<sup class="footnote-reference"><a id="citeref-Dynamism" href="#footnote-Dynamism">[Dynamism]</a></sup>.</p><pre><code class="language-julia-repl hljs" style="display:block;">julia&gt; code_escapes((String,)) do s
           obj = SafeRef(&quot;init&quot;)
           obj[] = s
           v = obj[]
           return v
       end</code><code class="nohighlight hljs ansi" style="display:block;">#7(<span class="sgr34">↑ s::String</span>)<span class="sgr1"> in Main at REPL[1]:2</span>
◌  <span class="sgr90">1 ─</span>     return _2</code></pre><p>In the example above, <code>ReturnEscape</code> imposed on <code>%3</code> (corresponding to <code>v</code>) is <em>not</em> directly propagated to <code>%1</code> (corresponding to <code>obj</code>) but rather that <code>ReturnEscape</code> is only propagated to <code>_2</code> (corresponding to <code>s</code>). Here <code>%3</code> is recorded in <code>%1</code>&#39;s <code>AliasInfo</code> property as it can be aliased to the first field of <code>%1</code>, and then when analyzing <code>Base.setfield!(%1, :x, _2)::String</code>, that escape information is propagated to <code>_2</code> but not to <code>%1</code>.</p><p>So <code>EscapeAnalysis</code> tracks which IR elements can be aliased across a <code>getfield</code>-<code>%new</code>/<code>setfield!</code> chain in order to analyze escapes of object fields, but actually this alias analysis needs to be generalized to handle other IR elements as well. This is because in Julia IR the same object is sometimes represented by different IR elements and so we should make sure that those different IR elements that actually can represent the same object share the same escape information. IR elements that return the same object as their operand(s), such as <code>PiNode</code> and <code>typeassert</code>, can cause that IR-level aliasing and thus requires escape information imposed on any of such aliased values to be shared between them. More interestingly, it is also needed for correctly reasoning about mutations on <code>PhiNode</code>. Let&#39;s consider the following example:</p><pre><code class="language-julia-repl hljs" style="display:block;">julia&gt; code_escapes((Bool, String,)) do cond, x
           if cond
               ϕ2 = ϕ1 = SafeRef(&quot;foo&quot;)
           else
               ϕ2 = ϕ1 = SafeRef(&quot;bar&quot;)
           end
           ϕ2[] = x
           y = ϕ1[]
           return y
       end</code><code class="nohighlight hljs ansi" style="display:block;">#9(<span class="sgr36">✓ cond::Bool</span>, <span class="sgr34">↑ x::String</span>)<span class="sgr1"> in Main at REPL[1]:2</span>
◌  <span class="sgr90">1 ─</span>      goto #3 if not _2
<span class="sgr32">✓′ </span><span class="sgr90">2 ─</span> %2 = %new(Main.SafeRef{String}, &quot;foo&quot;)<span class="sgr36">::Main.SafeRef{String}</span>
◌  <span class="sgr90">└──</span>      goto #4
<span class="sgr32">✓′ </span><span class="sgr90">3 ─</span> %4 = %new(Main.SafeRef{String}, &quot;bar&quot;)<span class="sgr36">::Main.SafeRef{String}</span>
<span class="sgr32">✓′ </span><span class="sgr90">4 ┄</span> %5 = φ (#2 =&gt; %2, #3 =&gt; %4)<span class="sgr36">::Main.SafeRef{String}</span>
<span class="sgr32">✓′ </span><span class="sgr90">│  </span> %6 = φ (#2 =&gt; %2, #3 =&gt; %4)<span class="sgr36">::Main.SafeRef{String}</span>
◌  <span class="sgr90">│  </span>      Base.setfield!(%5, :x, _3)<span class="sgr90">::String</span>
<span class="sgr34">↑  </span><span class="sgr90">│  </span> %8 = Base.getfield(%6, :x)<span class="sgr36">::String</span>
◌  <span class="sgr90">└──</span>      return %8</code></pre><p><code>ϕ1 = %5</code> and <code>ϕ2 = %6</code> are aliased and thus <code>ReturnEscape</code> imposed on <code>%8 = Base.getfield(%6, :x)::String</code> (corresponding to <code>y = ϕ1[]</code>) needs to be propagated to <code>Base.setfield!(%5, :x, _3)::String</code> (corresponding to <code>ϕ2[] = x</code>). In order for such escape information to be propagated correctly, the analysis should recognize that the <em>predecessors</em> of <code>ϕ1</code> and <code>ϕ2</code> can be aliased as well and equalize their escape information.</p><p>One interesting property of such aliasing information is that it is not known at &quot;usage&quot; site but can only be derived at &quot;definition&quot; site (as aliasing is conceptually equivalent to assignment), and thus it doesn&#39;t naturally fit in a backward analysis. In order to efficiently propagate escape information between related values, EscapeAnalysis.jl uses an approach inspired by the escape analysis algorithm explained in an old JVM paper<sup class="footnote-reference"><a id="citeref-JVM05" href="#footnote-JVM05">[JVM05]</a></sup>. That is, in addition to managing escape lattice elements, the analysis also maintains an &quot;equi&quot;-alias set, a disjoint set of aliased arguments and SSA statements. The alias set manages values that can be aliased to each other and allows escape information imposed on any of such aliased values to be equalized between them.</p><h3 id="EA-Array-Analysis"><a class="docs-heading-anchor" href="#EA-Array-Analysis">Array Analysis</a><a id="EA-Array-Analysis-1"></a><a class="docs-heading-anchor-permalink" href="#EA-Array-Analysis" title="Permalink"></a></h3><p>The alias analysis for object fields described above can also be generalized to analyze array operations. <code>EscapeAnalysis</code> implements handlings for various primitive array operations so that it can propagate escapes via <code>arrayref</code>-<code>arrayset</code> use-def chain and does not escape allocated arrays too conservatively:</p><pre><code class="language-julia-repl hljs" style="display:block;">julia&gt; code_escapes((String,)) do s
           ary = Any[]
           push!(ary, SafeRef(s))
           return ary[1], length(ary)
       end</code><code class="nohighlight hljs ansi" style="display:block;">#11(<span class="sgr31">X s::String</span>)<span class="sgr1"> in Main at REPL[1]:2</span>
<span class="sgr31">X  </span><span class="sgr90">1 ──</span> %1  = Core.getproperty(Memory{Any}, :instance)<span class="sgr36">::Memory{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %2  = invoke Core.memoryref(%1::Memory{Any})<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %3  = %new(Vector{Any}, %2, (0,))<span class="sgr36">::Vector{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %4  = %new(Main.SafeRef{String}, _2)<span class="sgr36">::Main.SafeRef{String}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %5  = Base.getfield(%3, :ref)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %6  = Base.getfield(%5, :mem)<span class="sgr36">::Memory{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %7  = Base.getfield(%6, :length)<span class="sgr36">::Int64</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %8  = Base.getfield(%3, :size)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %9  = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %10 = Base.getfield(%8, 1, %9)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %11 = Base.add_int(%10, 1)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %12 = Base.memoryrefoffset(%5)<span class="sgr36">::Int64</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %13 = Core.tuple(%11)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span>       Base.setfield!(%3, :size, %13)<span class="sgr90">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %15 = Base.add_int(%12, %11)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %16 = Base.sub_int(%15, 1)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %17 = Base.slt_int(%7, %16)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #3 if not %17
<span class="sgr31">X  </span><span class="sgr90">2 ──</span> %19 = %new(Base.var&quot;#133#134&quot;{Vector{Any}, Int64, Int64, Int64, Int64, Int64, Memory{Any}, MemoryRef{Any}}, %3, %16, %12, %11, %10, %7, %6, %5)<span class="sgr36">::Base.var&quot;#133#134&quot;{Vector{Any}, Int64, Int64, Int64, Int64, Int64, Memory{Any}, MemoryRef{Any}}</span>
<span class="sgr32">✓  </span><span class="sgr90">└───</span>       invoke %19()<span class="sgr90">::MemoryRef{Any}</span>
◌  <span class="sgr90">3 ┄─</span>       goto #4
<span class="sgr31">X  </span><span class="sgr90">4 ──</span> %22 = Base.getfield(%3, :size)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %23 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %24 = Base.getfield(%22, 1, %23)<span class="sgr36">::Int64</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %25 = Base.getfield(%3, :ref)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %26 = Base.memoryrefnew(%25, %24, false)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span>       Base.memoryrefset!(%26, %4, :not_atomic, false)<span class="sgr90">::Main.SafeRef{String}</span>
◌  <span class="sgr90">└───</span>       goto #5
◌  <span class="sgr90">5 ──</span> %29 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #9 if not %29
◌  <span class="sgr90">6 ──</span> %31 = Base.sub_int(1, 1)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %32 = Base.bitcast(Base.UInt, %31)<span class="sgr36">::UInt64</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %33 = Base.getfield(%3, :size)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %34 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %35 = Base.getfield(%33, 1, %34)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %36 = Base.bitcast(Base.UInt, %35)<span class="sgr36">::UInt64</span>
◌  <span class="sgr90">│   </span> %37 = Base.ult_int(%32, %36)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #8 if not %37
◌  <span class="sgr90">7 ──</span>       goto #9
◌  <span class="sgr90">8 ──</span> %40 = Core.tuple(1)<span class="sgr36">::Tuple{Int64}</span>
<span class="sgr32">✓  </span><span class="sgr90">│   </span>       invoke Base.throw_boundserror(%3::Vector{Any}, %40::Tuple{Int64})<span class="sgr90">::Union{}</span>
◌  <span class="sgr90">└───</span>       unreachable
<span class="sgr31">X  </span><span class="sgr90">9 ┄─</span> %43 = Base.getfield(%3, :ref)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %44 = Base.memoryrefnew(%43, 1, false)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %45 = Base.memoryrefget(%44, :not_atomic, false)<span class="sgr36">::Any</span>
◌  <span class="sgr90">└───</span>       goto #10
<span class="sgr31">X  </span><span class="sgr90">10 ─</span> %47 = Base.getfield(%3, :size)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %48 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %49 = Base.getfield(%47, 1, %48)<span class="sgr36">::Int64</span>
<span class="sgr34">↑′ </span><span class="sgr90">│   </span> %50 = Core.tuple(%45, %49)<span class="sgr36">::Tuple{Any, Int64}</span>
◌  <span class="sgr90">└───</span>       return %50</code></pre><p>In the above example <code>EscapeAnalysis</code> understands that <code>%20</code> and <code>%2</code> (corresponding to the allocated object <code>SafeRef(s)</code>) are aliased via the <code>arrayset</code>-<code>arrayref</code> chain and imposes <code>ReturnEscape</code> on them, but not impose it on the allocated array <code>%1</code> (corresponding to <code>ary</code>). <code>EscapeAnalysis</code> still imposes <code>ThrownEscape</code> on <code>ary</code> since it also needs to account for potential escapes via <code>BoundsError</code>, but also note that such unhandled <code>ThrownEscape</code> can often be ignored when optimizing the <code>ary</code> allocation.</p><p>Furthermore, in cases when array index information as well as array dimensions can be known <em>precisely</em>, <code>EscapeAnalysis</code> is able to even reason about &quot;per-element&quot; aliasing via <code>arrayref</code>-<code>arrayset</code> chain, as <code>EscapeAnalysis</code> does &quot;per-field&quot; alias analysis for objects:</p><pre><code class="language-julia-repl hljs" style="display:block;">julia&gt; code_escapes((String,String)) do s, t
           ary = Vector{Any}(undef, 2)
           ary[1] = SafeRef(s)
           ary[2] = SafeRef(t)
           return ary[1], length(ary)
       end</code><code class="nohighlight hljs ansi" style="display:block;">#13(<span class="sgr31">X s::String</span>, <span class="sgr31">X t::String</span>)<span class="sgr1"> in Main at REPL[1]:2</span>
<span class="sgr31">X  </span><span class="sgr90">1 ──</span> %1  = $(Expr(:foreigncall, :(:jl_alloc_genericmemory), Ref{Memory{Any}}, svec(Any, Int64), 0, :(:ccall), Memory{Any}, 2, 2))<span class="sgr36">::Memory{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %2  = Core.memoryrefnew(%1)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %3  = %new(Vector{Any}, %2, (2,))<span class="sgr36">::Vector{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %4  = %new(Main.SafeRef{String}, _2)<span class="sgr36">::Main.SafeRef{String}</span>
◌  <span class="sgr90">│   </span> %5  = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #5 if not %5
◌  <span class="sgr90">2 ──</span> %7  = Base.sub_int(1, 1)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %8  = Base.bitcast(Base.UInt, %7)<span class="sgr36">::UInt64</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %9  = Base.getfield(%3, :size)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %10 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %11 = Base.getfield(%9, 1, %10)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %12 = Base.bitcast(Base.UInt, %11)<span class="sgr36">::UInt64</span>
◌  <span class="sgr90">│   </span> %13 = Base.ult_int(%8, %12)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #4 if not %13
◌  <span class="sgr90">3 ──</span>       goto #5
◌  <span class="sgr90">4 ──</span> %16 = Core.tuple(1)<span class="sgr36">::Tuple{Int64}</span>
<span class="sgr32">✓  </span><span class="sgr90">│   </span>       invoke Base.throw_boundserror(%3::Vector{Any}, %16::Tuple{Int64})<span class="sgr90">::Union{}</span>
◌  <span class="sgr90">└───</span>       unreachable
<span class="sgr31">X  </span><span class="sgr90">5 ┄─</span> %19 = Base.getfield(%3, :ref)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %20 = Base.memoryrefnew(%19, 1, false)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span>       Base.memoryrefset!(%20, %4, :not_atomic, false)<span class="sgr90">::Main.SafeRef{String}</span>
◌  <span class="sgr90">└───</span>       goto #6
<span class="sgr31">X  </span><span class="sgr90">6 ──</span> %23 = %new(Main.SafeRef{String}, _3)<span class="sgr36">::Main.SafeRef{String}</span>
◌  <span class="sgr90">│   </span> %24 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #10 if not %24
◌  <span class="sgr90">7 ──</span> %26 = Base.sub_int(2, 1)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %27 = Base.bitcast(Base.UInt, %26)<span class="sgr36">::UInt64</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %28 = Base.getfield(%3, :size)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %29 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %30 = Base.getfield(%28, 1, %29)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %31 = Base.bitcast(Base.UInt, %30)<span class="sgr36">::UInt64</span>
◌  <span class="sgr90">│   </span> %32 = Base.ult_int(%27, %31)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #9 if not %32
◌  <span class="sgr90">8 ──</span>       goto #10
◌  <span class="sgr90">9 ──</span> %35 = Core.tuple(2)<span class="sgr36">::Tuple{Int64}</span>
<span class="sgr32">✓  </span><span class="sgr90">│   </span>       invoke Base.throw_boundserror(%3::Vector{Any}, %35::Tuple{Int64})<span class="sgr90">::Union{}</span>
◌  <span class="sgr90">└───</span>       unreachable
<span class="sgr31">X  </span><span class="sgr90">10 ┄</span> %38 = Base.getfield(%3, :ref)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %39 = Base.memoryrefnew(%38, 2, false)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span>       Base.memoryrefset!(%39, %23, :not_atomic, false)<span class="sgr90">::Main.SafeRef{String}</span>
◌  <span class="sgr90">└───</span>       goto #11
◌  <span class="sgr90">11 ─</span> %42 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #15 if not %42
◌  <span class="sgr90">12 ─</span> %44 = Base.sub_int(1, 1)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %45 = Base.bitcast(Base.UInt, %44)<span class="sgr36">::UInt64</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %46 = Base.getfield(%3, :size)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %47 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %48 = Base.getfield(%46, 1, %47)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %49 = Base.bitcast(Base.UInt, %48)<span class="sgr36">::UInt64</span>
◌  <span class="sgr90">│   </span> %50 = Base.ult_int(%45, %49)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #14 if not %50
◌  <span class="sgr90">13 ─</span>       goto #15
◌  <span class="sgr90">14 ─</span> %53 = Core.tuple(1)<span class="sgr36">::Tuple{Int64}</span>
<span class="sgr32">✓  </span><span class="sgr90">│   </span>       invoke Base.throw_boundserror(%3::Vector{Any}, %53::Tuple{Int64})<span class="sgr90">::Union{}</span>
◌  <span class="sgr90">└───</span>       unreachable
<span class="sgr31">X  </span><span class="sgr90">15 ┄</span> %56 = Base.getfield(%3, :ref)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %57 = Base.memoryrefnew(%56, 1, false)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %58 = Base.memoryrefget(%57, :not_atomic, false)<span class="sgr36">::Any</span>
◌  <span class="sgr90">└───</span>       goto #16
<span class="sgr31">X  </span><span class="sgr90">16 ─</span> %60 = Base.getfield(%3, :size)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %61 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %62 = Base.getfield(%60, 1, %61)<span class="sgr36">::Int64</span>
<span class="sgr34">↑′ </span><span class="sgr90">│   </span> %63 = Core.tuple(%58, %62)<span class="sgr36">::Tuple{Any, Int64}</span>
◌  <span class="sgr90">└───</span>       return %63</code></pre><p>Note that <code>ReturnEscape</code> is only imposed on <code>%2</code> (corresponding to <code>SafeRef(s)</code>) but not on <code>%4</code> (corresponding to <code>SafeRef(t)</code>). This is because the allocated array&#39;s dimension and indices involved with all <code>arrayref</code>/<code>arrayset</code> operations are available as constant information and <code>EscapeAnalysis</code> can understand that <code>%6</code> is aliased to <code>%2</code> but never be aliased to <code>%4</code>. In this kind of case, the succeeding optimization passes will be able to replace <code>Base.arrayref(true, %1, 1)::Any</code> with <code>%2</code> (a.k.a. &quot;load-forwarding&quot;) and eventually eliminate the allocation of array <code>%1</code> entirely (a.k.a. &quot;scalar-replacement&quot;).</p><p>When compared to object field analysis, where an access to object field can be analyzed trivially using type information derived by inference, array dimension isn&#39;t encoded as type information and so we need an additional analysis to derive that information. <code>EscapeAnalysis</code> at this moment first does an additional simple linear scan to analyze dimensions of allocated arrays before firing up the main analysis routine so that the succeeding escape analysis can precisely analyze operations on those arrays.</p><p>However, such precise &quot;per-element&quot; alias analysis is often hard. Essentially, the main difficulty inherit to array is that array dimension and index are often non-constant:</p><ul><li>loop often produces loop-variant, non-constant array indices</li><li>(specific to vectors) array resizing changes array dimension and invalidates its constant-ness</li></ul><p>Let&#39;s discuss those difficulties with concrete examples.</p><p>In the following example, <code>EscapeAnalysis</code> fails the precise alias analysis since the index at the <code>Base.arrayset(false, %4, %8, %6)::Vector{Any}</code> is not (trivially) constant. Especially <code>Any[nothing, nothing]</code> forms a loop and calls that <code>arrayset</code> operation in a loop, where <code>%6</code> is represented as a ϕ-node value (whose value is control-flow dependent). As a result, <code>ReturnEscape</code> ends up imposed on both <code>%23</code> (corresponding to <code>SafeRef(s)</code>) and <code>%25</code> (corresponding to <code>SafeRef(t)</code>), although ideally we want it to be imposed only on <code>%23</code> but not on <code>%25</code>:</p><pre><code class="language-julia-repl hljs" style="display:block;">julia&gt; code_escapes((String,String)) do s, t
           ary = Any[nothing, nothing]
           ary[1] = SafeRef(s)
           ary[2] = SafeRef(t)
           return ary[1], length(ary)
       end</code><code class="nohighlight hljs ansi" style="display:block;">#15(<span class="sgr31">X s::String</span>, <span class="sgr31">X t::String</span>)<span class="sgr1"> in Main at REPL[1]:2</span>
<span class="sgr31">X  </span><span class="sgr90">1 ──</span> %1  = $(Expr(:foreigncall, :(:jl_alloc_genericmemory), Ref{Memory{Any}}, svec(Any, Int64), 0, :(:ccall), Memory{Any}, 2, 2))<span class="sgr36">::Memory{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %2  = Core.memoryrefnew(%1)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">└───</span> %3  = %new(Vector{Any}, %2, (2,))<span class="sgr36">::Vector{Any}</span>
◌  <span class="sgr90">2 ┄─</span> %4  = φ (#1 =&gt; 1, #6 =&gt; %14)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %5  = φ (#1 =&gt; 1, #6 =&gt; %15)<span class="sgr36">::Int64</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %6  = Base.getfield(%3, :ref)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %7  = Base.memoryrefnew(%6, %4, false)<span class="sgr36">::MemoryRef{Any}</span>
◌  <span class="sgr90">│   </span>       Base.memoryrefset!(%7, nothing, :not_atomic, false)<span class="sgr90">::Nothing</span>
◌  <span class="sgr90">│   </span> %9  = (%5 === 2)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #4 if not %9
◌  <span class="sgr90">3 ──</span>       goto #5
◌  <span class="sgr90">4 ──</span> %12 = Base.add_int(%5, 1)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">└───</span>       goto #5
◌  <span class="sgr90">5 ┄─</span> %14 = φ (#4 =&gt; %12)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %15 = φ (#4 =&gt; %12)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %16 = φ (#3 =&gt; true, #4 =&gt; false)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">│   </span> %17 = Base.not_int(%16)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #7 if not %17
◌  <span class="sgr90">6 ──</span>       goto #2
◌  <span class="sgr90">7 ──</span>       goto #8
<span class="sgr31">X  </span><span class="sgr90">8 ──</span> %21 = %new(Main.SafeRef{String}, _2)<span class="sgr36">::Main.SafeRef{String}</span>
◌  <span class="sgr90">│   </span> %22 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #12 if not %22
◌  <span class="sgr90">9 ──</span> %24 = Base.sub_int(1, 1)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %25 = Base.bitcast(Base.UInt, %24)<span class="sgr36">::UInt64</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %26 = Base.getfield(%3, :size)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %27 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %28 = Base.getfield(%26, 1, %27)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %29 = Base.bitcast(Base.UInt, %28)<span class="sgr36">::UInt64</span>
◌  <span class="sgr90">│   </span> %30 = Base.ult_int(%25, %29)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #11 if not %30
◌  <span class="sgr90">10 ─</span>       goto #12
◌  <span class="sgr90">11 ─</span> %33 = Core.tuple(1)<span class="sgr36">::Tuple{Int64}</span>
<span class="sgr32">✓  </span><span class="sgr90">│   </span>       invoke Base.throw_boundserror(%3::Vector{Any}, %33::Tuple{Int64})<span class="sgr90">::Union{}</span>
◌  <span class="sgr90">└───</span>       unreachable
<span class="sgr31">X  </span><span class="sgr90">12 ┄</span> %36 = Base.getfield(%3, :ref)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %37 = Base.memoryrefnew(%36, 1, false)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span>       Base.memoryrefset!(%37, %21, :not_atomic, false)<span class="sgr90">::Main.SafeRef{String}</span>
◌  <span class="sgr90">└───</span>       goto #13
<span class="sgr31">X  </span><span class="sgr90">13 ─</span> %40 = %new(Main.SafeRef{String}, _3)<span class="sgr36">::Main.SafeRef{String}</span>
◌  <span class="sgr90">│   </span> %41 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #17 if not %41
◌  <span class="sgr90">14 ─</span> %43 = Base.sub_int(2, 1)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %44 = Base.bitcast(Base.UInt, %43)<span class="sgr36">::UInt64</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %45 = Base.getfield(%3, :size)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %46 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %47 = Base.getfield(%45, 1, %46)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %48 = Base.bitcast(Base.UInt, %47)<span class="sgr36">::UInt64</span>
◌  <span class="sgr90">│   </span> %49 = Base.ult_int(%44, %48)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #16 if not %49
◌  <span class="sgr90">15 ─</span>       goto #17
◌  <span class="sgr90">16 ─</span> %52 = Core.tuple(2)<span class="sgr36">::Tuple{Int64}</span>
<span class="sgr32">✓  </span><span class="sgr90">│   </span>       invoke Base.throw_boundserror(%3::Vector{Any}, %52::Tuple{Int64})<span class="sgr90">::Union{}</span>
◌  <span class="sgr90">└───</span>       unreachable
<span class="sgr31">X  </span><span class="sgr90">17 ┄</span> %55 = Base.getfield(%3, :ref)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %56 = Base.memoryrefnew(%55, 2, false)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span>       Base.memoryrefset!(%56, %40, :not_atomic, false)<span class="sgr90">::Main.SafeRef{String}</span>
◌  <span class="sgr90">└───</span>       goto #18
◌  <span class="sgr90">18 ─</span> %59 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #22 if not %59
◌  <span class="sgr90">19 ─</span> %61 = Base.sub_int(1, 1)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %62 = Base.bitcast(Base.UInt, %61)<span class="sgr36">::UInt64</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %63 = Base.getfield(%3, :size)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %64 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %65 = Base.getfield(%63, 1, %64)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %66 = Base.bitcast(Base.UInt, %65)<span class="sgr36">::UInt64</span>
◌  <span class="sgr90">│   </span> %67 = Base.ult_int(%62, %66)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #21 if not %67
◌  <span class="sgr90">20 ─</span>       goto #22
◌  <span class="sgr90">21 ─</span> %70 = Core.tuple(1)<span class="sgr36">::Tuple{Int64}</span>
<span class="sgr32">✓  </span><span class="sgr90">│   </span>       invoke Base.throw_boundserror(%3::Vector{Any}, %70::Tuple{Int64})<span class="sgr90">::Union{}</span>
◌  <span class="sgr90">└───</span>       unreachable
<span class="sgr31">X  </span><span class="sgr90">22 ┄</span> %73 = Base.getfield(%3, :ref)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %74 = Base.memoryrefnew(%73, 1, false)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %75 = Base.memoryrefget(%74, :not_atomic, false)<span class="sgr36">::Any</span>
◌  <span class="sgr90">└───</span>       goto #23
<span class="sgr31">X  </span><span class="sgr90">23 ─</span> %77 = Base.getfield(%3, :size)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %78 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %79 = Base.getfield(%77, 1, %78)<span class="sgr36">::Int64</span>
<span class="sgr34">↑′ </span><span class="sgr90">│   </span> %80 = Core.tuple(%75, %79)<span class="sgr36">::Tuple{Any, Int64}</span>
◌  <span class="sgr90">└───</span>       return %80</code></pre><p>The next example illustrates how vector resizing makes precise alias analysis hard. The essential difficulty is that the dimension of allocated array <code>%1</code> is first initialized as <code>0</code>, but it changes by the two <code>:jl_array_grow_end</code> calls afterwards. <code>EscapeAnalysis</code> currently simply gives up precise alias analysis whenever it encounters any array resizing operations and so <code>ReturnEscape</code> is imposed on both <code>%2</code> (corresponding to <code>SafeRef(s)</code>) and <code>%20</code> (corresponding to <code>SafeRef(t)</code>):</p><pre><code class="language-julia-repl hljs" style="display:block;">julia&gt; code_escapes((String,String)) do s, t
           ary = Any[]
           push!(ary, SafeRef(s))
           push!(ary, SafeRef(t))
           ary[1], length(ary)
       end</code><code class="nohighlight hljs ansi" style="display:block;">#17(<span class="sgr31">X s::String</span>, <span class="sgr31">X t::String</span>)<span class="sgr1"> in Main at REPL[1]:2</span>
<span class="sgr31">X  </span><span class="sgr90">1 ──</span> %1  = Core.getproperty(Memory{Any}, :instance)<span class="sgr36">::Memory{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %2  = invoke Core.memoryref(%1::Memory{Any})<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %3  = %new(Vector{Any}, %2, (0,))<span class="sgr36">::Vector{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %4  = %new(Main.SafeRef{String}, _2)<span class="sgr36">::Main.SafeRef{String}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %5  = Base.getfield(%3, :ref)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %6  = Base.getfield(%5, :mem)<span class="sgr36">::Memory{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %7  = Base.getfield(%6, :length)<span class="sgr36">::Int64</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %8  = Base.getfield(%3, :size)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %9  = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %10 = Base.getfield(%8, 1, %9)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %11 = Base.add_int(%10, 1)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %12 = Base.memoryrefoffset(%5)<span class="sgr36">::Int64</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %13 = Core.tuple(%11)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span>       Base.setfield!(%3, :size, %13)<span class="sgr90">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %15 = Base.add_int(%12, %11)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %16 = Base.sub_int(%15, 1)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %17 = Base.slt_int(%7, %16)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #3 if not %17
<span class="sgr31">X  </span><span class="sgr90">2 ──</span> %19 = %new(Base.var&quot;#133#134&quot;{Vector{Any}, Int64, Int64, Int64, Int64, Int64, Memory{Any}, MemoryRef{Any}}, %3, %16, %12, %11, %10, %7, %6, %5)<span class="sgr36">::Base.var&quot;#133#134&quot;{Vector{Any}, Int64, Int64, Int64, Int64, Int64, Memory{Any}, MemoryRef{Any}}</span>
<span class="sgr32">✓  </span><span class="sgr90">└───</span>       invoke %19()<span class="sgr90">::MemoryRef{Any}</span>
◌  <span class="sgr90">3 ┄─</span>       goto #4
<span class="sgr31">X  </span><span class="sgr90">4 ──</span> %22 = Base.getfield(%3, :size)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %23 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %24 = Base.getfield(%22, 1, %23)<span class="sgr36">::Int64</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %25 = Base.getfield(%3, :ref)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %26 = Base.memoryrefnew(%25, %24, false)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span>       Base.memoryrefset!(%26, %4, :not_atomic, false)<span class="sgr90">::Main.SafeRef{String}</span>
◌  <span class="sgr90">└───</span>       goto #5
<span class="sgr31">X  </span><span class="sgr90">5 ──</span> %29 = %new(Main.SafeRef{String}, _3)<span class="sgr36">::Main.SafeRef{String}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %30 = Base.getfield(%3, :ref)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %31 = Base.getfield(%30, :mem)<span class="sgr36">::Memory{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %32 = Base.getfield(%31, :length)<span class="sgr36">::Int64</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %33 = Base.getfield(%3, :size)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %34 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %35 = Base.getfield(%33, 1, %34)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %36 = Base.add_int(%35, 1)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %37 = Base.memoryrefoffset(%30)<span class="sgr36">::Int64</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %38 = Core.tuple(%36)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span>       Base.setfield!(%3, :size, %38)<span class="sgr90">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %40 = Base.add_int(%37, %36)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %41 = Base.sub_int(%40, 1)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %42 = Base.slt_int(%32, %41)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #7 if not %42
<span class="sgr31">X  </span><span class="sgr90">6 ──</span> %44 = %new(Base.var&quot;#133#134&quot;{Vector{Any}, Int64, Int64, Int64, Int64, Int64, Memory{Any}, MemoryRef{Any}}, %3, %41, %37, %36, %35, %32, %31, %30)<span class="sgr36">::Base.var&quot;#133#134&quot;{Vector{Any}, Int64, Int64, Int64, Int64, Int64, Memory{Any}, MemoryRef{Any}}</span>
<span class="sgr32">✓  </span><span class="sgr90">└───</span>       invoke %44()<span class="sgr90">::MemoryRef{Any}</span>
◌  <span class="sgr90">7 ┄─</span>       goto #8
<span class="sgr31">X  </span><span class="sgr90">8 ──</span> %47 = Base.getfield(%3, :size)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %48 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %49 = Base.getfield(%47, 1, %48)<span class="sgr36">::Int64</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %50 = Base.getfield(%3, :ref)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %51 = Base.memoryrefnew(%50, %49, false)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span>       Base.memoryrefset!(%51, %29, :not_atomic, false)<span class="sgr90">::Main.SafeRef{String}</span>
◌  <span class="sgr90">└───</span>       goto #9
◌  <span class="sgr90">9 ──</span> %54 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #13 if not %54
◌  <span class="sgr90">10 ─</span> %56 = Base.sub_int(1, 1)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %57 = Base.bitcast(Base.UInt, %56)<span class="sgr36">::UInt64</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %58 = Base.getfield(%3, :size)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %59 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %60 = Base.getfield(%58, 1, %59)<span class="sgr36">::Int64</span>
◌  <span class="sgr90">│   </span> %61 = Base.bitcast(Base.UInt, %60)<span class="sgr36">::UInt64</span>
◌  <span class="sgr90">│   </span> %62 = Base.ult_int(%57, %61)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #12 if not %62
◌  <span class="sgr90">11 ─</span>       goto #13
◌  <span class="sgr90">12 ─</span> %65 = Core.tuple(1)<span class="sgr36">::Tuple{Int64}</span>
<span class="sgr32">✓  </span><span class="sgr90">│   </span>       invoke Base.throw_boundserror(%3::Vector{Any}, %65::Tuple{Int64})<span class="sgr90">::Union{}</span>
◌  <span class="sgr90">└───</span>       unreachable
<span class="sgr31">X  </span><span class="sgr90">13 ┄</span> %68 = Base.getfield(%3, :ref)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %69 = Base.memoryrefnew(%68, 1, false)<span class="sgr36">::MemoryRef{Any}</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %70 = Base.memoryrefget(%69, :not_atomic, false)<span class="sgr36">::Any</span>
◌  <span class="sgr90">└───</span>       goto #14
<span class="sgr31">X  </span><span class="sgr90">14 ─</span> %72 = Base.getfield(%3, :size)<span class="sgr36">::Tuple{Int64}</span>
◌  <span class="sgr90">│   </span> %73 = $(Expr(:boundscheck, true))<span class="sgr36">::Bool</span>
<span class="sgr31">X  </span><span class="sgr90">│   </span> %74 = Base.getfield(%72, 1, %73)<span class="sgr36">::Int64</span>
<span class="sgr34">↑′ </span><span class="sgr90">│   </span> %75 = Core.tuple(%70, %74)<span class="sgr36">::Tuple{Any, Int64}</span>
◌  <span class="sgr90">└───</span>       return %75</code></pre><p>In order to address these difficulties, we need inference to be aware of array dimensions and propagate array dimensions in a flow-sensitive way<sup class="footnote-reference"><a id="citeref-ArrayDimension" href="#footnote-ArrayDimension">[ArrayDimension]</a></sup>, as well as come up with nice representation of loop-variant values.</p><p><code>EscapeAnalysis</code> at this moment quickly switches to the more imprecise analysis that doesn&#39;t track precise index information in cases when array dimensions or indices are trivially non constant. The switch can naturally be implemented as a lattice join operation of <code>EscapeInfo.AliasInfo</code> property in the data-flow analysis framework.</p><h3 id="EA-Exception-Handling"><a class="docs-heading-anchor" href="#EA-Exception-Handling">Exception Handling</a><a id="EA-Exception-Handling-1"></a><a class="docs-heading-anchor-permalink" href="#EA-Exception-Handling" title="Permalink"></a></h3><p>It would be also worth noting how <code>EscapeAnalysis</code> handles possible escapes via exceptions. Naively it seems enough to propagate escape information imposed on <code>:the_exception</code> object to all values that may be thrown in a corresponding <code>try</code> block. But there are actually several other ways to access to the exception object in Julia, such as <code>Base.current_exceptions</code> and <code>rethrow</code>. For example, escape analysis needs to account for potential escape of <code>r</code> in the example below:</p><pre><code class="language-julia-repl hljs" style="display:block;">julia&gt; const GR = Ref{Any}();</code><code class="nohighlight hljs ansi" style="display:block;"></code><br/><code class="language-julia-repl hljs" style="display:block;">julia&gt; @noinline function rethrow_escape!()
           try
               rethrow()
           catch err
               GR[] = err
           end
       end;</code><code class="nohighlight hljs ansi" style="display:block;"></code><br/><code class="language-julia-repl hljs" style="display:block;">julia&gt; get′(x) = isassigned(x) ? x[] : throw(x);</code><code class="nohighlight hljs ansi" style="display:block;"></code><br/><code class="language-julia-repl hljs" style="display:block;">julia&gt; code_escapes() do
           r = Ref{String}()
           local t
           try
               t = get′(r)
           catch err
               t = typeof(err)   # `err` (which `r` aliases to) doesn&#39;t escape here
               rethrow_escape!() # but `r` escapes here
           end
           return t
       end</code><code class="nohighlight hljs ansi" style="display:block;">#19()<span class="sgr1"> in Main at REPL[4]:2</span>
<span class="sgr31">X  </span><span class="sgr90">1 ─</span> %1  = %new(Base.RefValue{String})<span class="sgr36">::Base.RefValue{String}</span>
◌  <span class="sgr90">2 ─</span> %2  = enter #8
◌  <span class="sgr90">3 ─</span> %3  = Base.isdefined(%1, :x)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└──</span>       goto #5 if not %3
<span class="sgr31">X  </span><span class="sgr90">4 ─</span> %5  = Base.getfield(%1, :x)<span class="sgr36">::String</span>
◌  <span class="sgr90">└──</span>       goto #6
◌  <span class="sgr90">5 ─</span>       Main.throw(%1)<span class="sgr90">::Union{}</span>
◌  <span class="sgr90">└──</span>       unreachable
◌  <span class="sgr90">6 ─</span>       $(Expr(:leave, :(%2)))
◌  <span class="sgr90">7 ─</span>       goto #9
<span class="sgr32">✓  </span><span class="sgr90">8 ┄</span> %11 = $(Expr(:the_exception))<span class="sgr36">::Any</span>
<span class="sgr31">X  </span><span class="sgr90">│  </span> %12 = Main.typeof(%11)<span class="sgr36">::DataType</span>
<span class="sgr32">✓  </span><span class="sgr90">│  </span>       invoke Main.rethrow_escape!()<span class="sgr90">::Any</span>
◌  <span class="sgr90">└──</span>       $(Expr(:pop_exception, :(%2)))<span class="sgr90">::Core.Const(nothing)</span>
<span class="sgr31">X  </span><span class="sgr90">9 ┄</span> %15 = φ (#7 =&gt; %5, #8 =&gt; %12)<span class="sgr36">::Union{DataType, String}</span>
◌  <span class="sgr90">└──</span>       return %15</code></pre><p>It requires a global analysis in order to correctly reason about all possible escapes via existing exception interfaces. For now we always propagate the topmost escape information to all potentially thrown objects conservatively, since such an additional analysis might not be worthwhile to do given that exception handling and error path usually don&#39;t need to be very performance sensitive, and also optimizations of error paths might be very ineffective anyway since they are often even &quot;unoptimized&quot; intentionally for latency reasons.</p><p><code>x::EscapeInfo</code>&#39;s <code>x.ThrownEscape</code> property records SSA statements where <code>x</code> can be thrown as an exception. Using this information <code>EscapeAnalysis</code> can propagate possible escapes via exceptions limitedly to only those may be thrown in each <code>try</code> region:</p><pre><code class="language-julia-repl hljs" style="display:block;">julia&gt; result = code_escapes((String,String)) do s1, s2
           r1 = Ref(s1)
           r2 = Ref(s2)
           local ret
           try
               s1 = get′(r1)
               ret = sizeof(s1)
           catch err
               global GV = err # will definitely escape `r1`
           end
           s2 = get′(r2)       # still `r2` doesn&#39;t escape fully
           return s2
       end</code><code class="nohighlight hljs ansi" style="display:block;">#21(<span class="sgr31">X s1::String</span>, <span class="sgr33">↑ s2::String</span>)<span class="sgr1"> in Main at REPL[1]:2</span>
<span class="sgr31">X  </span><span class="sgr90">1 ──</span> %1  = %new(Base.RefValue{String}, _2)<span class="sgr36">::Base.RefValue{String}</span>
<span class="sgr33">*′ </span><span class="sgr90">└───</span> %2  = %new(Base.RefValue{String}, _3)<span class="sgr36">::Base.RefValue{String}</span>
<span class="sgr33">*′ </span><span class="sgr90">2 ──</span> %3  = ϒ (%2)<span class="sgr36">::Base.RefValue{String}</span>
◌  <span class="sgr90">└───</span> %4  = enter #8
◌  <span class="sgr90">3 ──</span> %5  = Base.isdefined(%1, :x)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #5 if not %5
<span class="sgr31">X  </span><span class="sgr90">4 ──</span>       Base.getfield(%1, :x)<span class="sgr90">::String</span>
◌  <span class="sgr90">└───</span>       goto #6
◌  <span class="sgr90">5 ──</span>       Main.throw(%1)<span class="sgr90">::Union{}</span>
◌  <span class="sgr90">└───</span>       unreachable
◌  <span class="sgr90">6 ──</span>       $(Expr(:leave, :(%4)))
◌  <span class="sgr90">7 ──</span>       goto #11
<span class="sgr33">*′ </span><span class="sgr90">8 ┄─</span> %13 = φᶜ (%3)<span class="sgr36">::Base.RefValue{String}</span>
<span class="sgr31">X  </span><span class="sgr90">└───</span> %14 = $(Expr(:the_exception))<span class="sgr36">::Any</span>
◌  <span class="sgr90">9 ──</span>       nothing<span class="sgr90">::Nothing</span>
◌  <span class="sgr90">10 ─</span>       (Main.GV = %14)<span class="sgr90">::Any</span>
◌  <span class="sgr90">└───</span>       $(Expr(:pop_exception, :(%4)))<span class="sgr90">::Core.Const(nothing)</span>
<span class="sgr33">*′ </span><span class="sgr90">11 ┄</span> %18 = φ (#7 =&gt; %2, #10 =&gt; %13)<span class="sgr36">::Base.RefValue{String}</span>
◌  <span class="sgr90">│   </span> %19 = Base.isdefined(%18, :x)<span class="sgr36">::Bool</span>
◌  <span class="sgr90">└───</span>       goto #13 if not %19
<span class="sgr33">↑  </span><span class="sgr90">12 ─</span> %21 = Base.getfield(%18, :x)<span class="sgr36">::String</span>
◌  <span class="sgr90">└───</span>       goto #14
◌  <span class="sgr90">13 ─</span>       Main.throw(%18)<span class="sgr90">::Union{}</span>
◌  <span class="sgr90">└───</span>       unreachable
◌  <span class="sgr90">14 ─</span>       return %21</code></pre><h2 id="Analysis-Usage"><a class="docs-heading-anchor" href="#Analysis-Usage">Analysis Usage</a><a id="Analysis-Usage-1"></a><a class="docs-heading-anchor-permalink" href="#Analysis-Usage" title="Permalink"></a></h2><p><code>analyze_escapes</code> is the entry point to analyze escape information of SSA-IR elements.</p><p>Most optimizations like SROA (<code>sroa_pass!</code>) are more effective when applied to an optimized source that the inlining pass (<code>ssa_inlining_pass!</code>) has simplified by resolving inter-procedural calls and expanding callee sources. Accordingly, <code>analyze_escapes</code> is also able to analyze post-inlining IR and collect escape information that is useful for certain memory-related optimizations.</p><p>However, since certain optimization passes like inlining can change control flows and eliminate dead code, they can break the inter-procedural validity of escape information. In particularity, in order to collect inter-procedurally valid escape information, we need to analyze a pre-inlining IR.</p><p>Because of this reason, <code>analyze_escapes</code> can analyze <code>IRCode</code> at any Julia-level optimization stage, and especially, it is supposed to be used at the following two stages:</p><ul><li><code>IPO EA</code>: analyze pre-inlining IR to generate IPO-valid escape information cache</li><li><code>Local EA</code>: analyze post-inlining IR to collect locally-valid escape information</li></ul><p>Escape information derived by <code>IPO EA</code> is transformed to the <code>ArgEscapeCache</code> data structure and cached globally. By passing an appropriate <code>get_escape_cache</code> callback to <code>analyze_escapes</code>, the escape analysis can improve analysis accuracy by utilizing cached inter-procedural information of non-inlined callees that has been derived by previous <code>IPO EA</code>. More interestingly, it is also valid to use <code>IPO EA</code> escape information for type inference, e.g., inference accuracy can be improved by forming <code>Const</code>/<code>PartialStruct</code>/<code>MustAlias</code> of mutable object.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Compiler.EscapeAnalysis.analyze_escapes" href="#Core.Compiler.EscapeAnalysis.analyze_escapes"><code>Core.Compiler.EscapeAnalysis.analyze_escapes</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">analyze_escapes(ir::IRCode, nargs::Int, get_escape_cache) -&gt; estate::EscapeState</code></pre><p>Analyzes escape information in <code>ir</code>:</p><ul><li><code>nargs</code>: the number of actual arguments of the analyzed call</li><li><code>get_escape_cache(::MethodInstance) -&gt; Union{Bool,ArgEscapeCache}</code>: retrieves cached argument escape information</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/compiler/ssair/EscapeAnalysis/EscapeAnalysis.jl#L608-L615">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Compiler.EscapeAnalysis.EscapeState" href="#Core.Compiler.EscapeAnalysis.EscapeState"><code>Core.Compiler.EscapeAnalysis.EscapeState</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">estate::EscapeState</code></pre><p>Extended lattice that maps arguments and SSA values to escape information represented as <a href="EscapeAnalysis.html#Core.Compiler.EscapeAnalysis.EscapeInfo"><code>EscapeInfo</code></a>. Escape information imposed on SSA IR element <code>x</code> can be retrieved by <code>estate[x]</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/compiler/ssair/EscapeAnalysis/EscapeAnalysis.jl#L440-L445">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Compiler.EscapeAnalysis.EscapeInfo" href="#Core.Compiler.EscapeAnalysis.EscapeInfo"><code>Core.Compiler.EscapeAnalysis.EscapeInfo</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">x::EscapeInfo</code></pre><p>A lattice for escape information, which holds the following properties:</p><ul><li><code>x.Analyzed::Bool</code>: not formally part of the lattice, only indicates whether <code>x</code> has been analyzed</li><li><code>x.ReturnEscape::Bool</code>: indicates <code>x</code> can escape to the caller via return</li><li><code>x.ThrownEscape::BitSet</code>: records SSA statement numbers where <code>x</code> can be thrown as exception:<ul><li><code>isempty(x.ThrownEscape)</code>: <code>x</code> will never be thrown in this call frame (the bottom)</li><li><code>pc ∈ x.ThrownEscape</code>: <code>x</code> may be thrown at the SSA statement at <code>pc</code></li><li><code>-1 ∈ x.ThrownEscape</code>: <code>x</code> may be thrown at arbitrary points of this call frame (the top)</li></ul>This information will be used by <code>escape_exception!</code> to propagate potential escapes via exception.</li><li><code>x.AliasInfo::Union{Bool,IndexableFields,IndexableElements,Unindexable}</code>: maintains all possible values that can be aliased to fields or array elements of <code>x</code>:<ul><li><code>x.AliasInfo === false</code> indicates the fields/elements of <code>x</code> aren&#39;t analyzed yet</li><li><code>x.AliasInfo === true</code> indicates the fields/elements of <code>x</code> can&#39;t be analyzed, e.g. the type of <code>x</code> is not known or is not concrete and thus its fields/elements can&#39;t be known precisely</li><li><code>x.AliasInfo::IndexableFields</code> records all the possible values that can be aliased to fields of object <code>x</code> with precise index information</li><li><code>x.AliasInfo::IndexableElements</code> records all the possible values that can be aliased to elements of array <code>x</code> with precise index information</li><li><code>x.AliasInfo::Unindexable</code> records all the possible values that can be aliased to fields/elements of <code>x</code> without precise index information</li></ul></li><li><code>x.Liveness::BitSet</code>: records SSA statement numbers where <code>x</code> should be live, e.g. to be used as a call argument, to be returned to a caller, or preserved for <code>:foreigncall</code>:<ul><li><code>isempty(x.Liveness)</code>: <code>x</code> is never be used in this call frame (the bottom)</li><li><code>0 ∈ x.Liveness</code> also has the special meaning that it&#39;s a call argument of the currently analyzed call frame (and thus it&#39;s visible from the caller immediately).</li><li><code>pc ∈ x.Liveness</code>: <code>x</code> may be used at the SSA statement at <code>pc</code></li><li><code>-1 ∈ x.Liveness</code>: <code>x</code> may be used at arbitrary points of this call frame (the top)</li></ul></li></ul><p>There are utility constructors to create common <code>EscapeInfo</code>s, e.g.,</p><ul><li><code>NoEscape()</code>: the bottom(-like) element of this lattice, meaning it won&#39;t escape to anywhere</li><li><code>AllEscape()</code>: the topmost element of this lattice, meaning it will escape to everywhere</li></ul><p><code>analyze_escapes</code> will transition these elements from the bottom to the top, in the same direction as Julia&#39;s native type inference routine. An abstract state will be initialized with the bottom(-like) elements:</p><ul><li>the call arguments are initialized as <code>ArgEscape()</code>, whose <code>Liveness</code> property includes <code>0</code> to indicate that it is passed as a call argument and visible from a caller immediately</li><li>the other states are initialized as <code>NotAnalyzed()</code>, which is a special lattice element that is slightly lower than <code>NoEscape</code>, but at the same time doesn&#39;t represent any meaning other than it&#39;s not analyzed yet (thus it&#39;s not formally part of the lattice)</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/compiler/ssair/EscapeAnalysis/EscapeAnalysis.jl#L41-L81">source</a></section></article><hr/><section class="footnotes is-size-7"><ul><li class="footnote" id="footnote-LatticeDesign"><a class="tag is-link" href="#citeref-LatticeDesign">LatticeDesign</a>Our type inference implementation takes the alternative approach, where each lattice property is represented by a special lattice element type object. It turns out that it started to complicate implementations of the lattice operations mainly because it often requires conversion rules between each lattice element type object. And we are working on <a href="https://github.com/JuliaLang/julia/pull/42596">overhauling our type inference lattice implementation</a> with <code>EscapeInfo</code>-like lattice design.</li><li class="footnote" id="footnote-MM02"><a class="tag is-link" href="#citeref-MM02">MM02</a><em>A Graph-Free approach to Data-Flow Analysis</em>.      Markas Mohnen, 2002, April.      <a href="https://api.semanticscholar.org/CorpusID:28519618">https://api.semanticscholar.org/CorpusID:28519618</a>.</li><li class="footnote" id="footnote-BackandForth"><a class="tag is-link" href="#citeref-BackandForth">BackandForth</a>Our type inference algorithm in contrast is implemented as a forward analysis, because type information usually flows from &quot;definition&quot; to &quot;usage&quot; and it is more natural and effective to propagate such information in a forward way.</li><li class="footnote" id="footnote-Dynamism"><a class="tag is-link" href="#citeref-Dynamism">Dynamism</a>In some cases, however, object fields can&#39;t be analyzed precisely. For example, object may escape to somewhere <code>EscapeAnalysis</code> can&#39;t account for possible memory effects on it, or fields of the objects simply can&#39;t be known because of the lack of type information. In such cases <code>AliasInfo</code> property is raised to the topmost element within its own lattice order, and it causes succeeding field analysis to be conservative and escape information imposed on fields of an unanalyzable object to be propagated to the object itself.</li><li class="footnote" id="footnote-JVM05"><a class="tag is-link" href="#citeref-JVM05">JVM05</a><em>Escape Analysis in the Context of Dynamic Compilation and Deoptimization</em>.       Thomas Kotzmann and Hanspeter Mössenböck, 2005, June.       <a href="https://dl.acm.org/doi/10.1145/1064979.1064996">https://dl.acm.org/doi/10.1145/1064979.1064996</a>.</li><li class="footnote" id="footnote-ArrayDimension"><a class="tag is-link" href="#citeref-ArrayDimension">ArrayDimension</a>Otherwise we will need yet another forward data-flow analysis on top of the escape analysis.</li></ul></section></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="ssair.html">« Julia SSA-form IR</a><a class="docs-footer-nextpage" href="aot.html">Ahead of Time Compilation »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
