<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>SHA · The Julia Language</title><meta name="title" content="SHA · The Julia Language"/><meta property="og:title" content="SHA · The Julia Language"/><meta property="twitter:title" content="SHA · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li class="is-active"><a class="tocitem" href="SHA.html">SHA</a><ul class="internal"><li><a class="tocitem" href="#SHA-functions"><span>SHA functions</span></a></li><li><a class="tocitem" href="#Working-with-context"><span>Working with context</span></a></li><li><a class="tocitem" href="#HMAC-functions"><span>HMAC functions</span></a></li></ul></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="SHA.html">SHA</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="SHA.html">SHA</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaCrypto/SHA.jl/blob/master/docs/src/index.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="SHA"><a class="docs-heading-anchor" href="#SHA">SHA</a><a id="SHA-1"></a><a class="docs-heading-anchor-permalink" href="#SHA" title="Permalink"></a></h1><h2 id="SHA-functions"><a class="docs-heading-anchor" href="#SHA-functions">SHA functions</a><a id="SHA-functions-1"></a><a class="docs-heading-anchor-permalink" href="#SHA-functions" title="Permalink"></a></h2><p>Usage is very straightforward:</p><pre><code class="language-julia-repl hljs">julia&gt; using SHA

julia&gt; bytes2hex(sha256(&quot;test&quot;))
&quot;9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08&quot;</code></pre><p>Each exported function (at the time of this writing, SHA-1, SHA-2 224, 256, 384 and 512, and SHA-3 224, 256, 384 and 512 functions are implemented) takes in either an <code>AbstractVector{UInt8}</code>, an <code>AbstractString</code> or an <code>IO</code> object.  This makes it trivial to checksum a file:</p><pre><code class="language-julia hljs">shell&gt; cat /tmp/test.txt
test
julia&gt; using SHA

julia&gt; open(&quot;/tmp/test.txt&quot;) do f
           sha2_256(f)
       end
32-element Array{UInt8,1}:
 0x9f
 0x86
 0xd0
 0x81
 0x88
 0x4c
 0x7d
 0x65
    ⋮
 0x5d
 0x6c
 0x15
 0xb0
 0xf0
 0x0a
 0x08</code></pre><h3 id="All-SHA-functions"><a class="docs-heading-anchor" href="#All-SHA-functions">All SHA functions</a><a id="All-SHA-functions-1"></a><a class="docs-heading-anchor-permalink" href="#All-SHA-functions" title="Permalink"></a></h3><p>Due to the colloquial usage of <code>sha256</code> to refer to <code>sha2_256</code>, convenience functions are provided, mapping <code>shaxxx()</code> function calls to <code>sha2_xxx()</code>. For SHA-3, no such colloquialisms exist and the user must use the full <code>sha3_xxx()</code> names.</p><p><code>shaxxx()</code> takes <code>AbstractString</code> and array-like objects (<code>NTuple</code> and <code>Array</code>) with elements of type <code>UInt8</code>.</p><p><strong>SHA-1</strong></p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.sha1" href="#SHA.sha1"><code>SHA.sha1</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sha1(data)</code></pre><p>Hash data using the <code>sha1</code> algorithm and return the resulting digest. See also <a href="SHA.html#SHA.SHA1_CTX"><code>SHA1_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L79-L84">source</a></section><section><div><pre><code class="language-julia hljs">sha1(io::IO)</code></pre><p>Hash data from io using <code>sha1</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L109-L113">source</a></section></article><p><strong>SHA-2</strong></p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.sha224" href="#SHA.sha224"><code>SHA.sha224</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sha224(data)</code></pre><p>Hash data using the <code>sha224</code> algorithm and return the resulting digest. See also <a href="SHA.html#SHA.SHA2_224_CTX"><code>SHA2_224_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L79-L84">source</a></section><section><div><pre><code class="language-julia hljs">sha224(io::IO)</code></pre><p>Hash data from io using <code>sha224</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L109-L113">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.sha256" href="#SHA.sha256"><code>SHA.sha256</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sha256(data)</code></pre><p>Hash data using the <code>sha256</code> algorithm and return the resulting digest. See also <a href="SHA.html#SHA.SHA2_256_CTX"><code>SHA2_256_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L79-L84">source</a></section><section><div><pre><code class="language-julia hljs">sha256(io::IO)</code></pre><p>Hash data from io using <code>sha256</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L109-L113">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.sha384" href="#SHA.sha384"><code>SHA.sha384</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sha384(data)</code></pre><p>Hash data using the <code>sha384</code> algorithm and return the resulting digest. See also <a href="SHA.html#SHA.SHA2_384_CTX"><code>SHA2_384_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L79-L84">source</a></section><section><div><pre><code class="language-julia hljs">sha384(io::IO)</code></pre><p>Hash data from io using <code>sha384</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L109-L113">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.sha512" href="#SHA.sha512"><code>SHA.sha512</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sha512(data)</code></pre><p>Hash data using the <code>sha512</code> algorithm and return the resulting digest. See also <a href="SHA.html#SHA.SHA2_512_CTX"><code>SHA2_512_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L79-L84">source</a></section><section><div><pre><code class="language-julia hljs">sha512(io::IO)</code></pre><p>Hash data from io using <code>sha512</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L109-L113">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.sha2_224" href="#SHA.sha2_224"><code>SHA.sha2_224</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sha2_224(data)</code></pre><p>Hash data using the <code>sha2_224</code> algorithm and return the resulting digest. See also <a href="SHA.html#SHA.SHA2_224_CTX"><code>SHA2_224_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L79-L84">source</a></section><section><div><pre><code class="language-julia hljs">sha2_224(io::IO)</code></pre><p>Hash data from io using <code>sha2_224</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L109-L113">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.sha2_256" href="#SHA.sha2_256"><code>SHA.sha2_256</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sha2_256(data)</code></pre><p>Hash data using the <code>sha2_256</code> algorithm and return the resulting digest. See also <a href="SHA.html#SHA.SHA2_256_CTX"><code>SHA2_256_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L79-L84">source</a></section><section><div><pre><code class="language-julia hljs">sha2_256(io::IO)</code></pre><p>Hash data from io using <code>sha2_256</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L109-L113">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.sha2_384" href="#SHA.sha2_384"><code>SHA.sha2_384</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sha2_384(data)</code></pre><p>Hash data using the <code>sha2_384</code> algorithm and return the resulting digest. See also <a href="SHA.html#SHA.SHA2_384_CTX"><code>SHA2_384_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L79-L84">source</a></section><section><div><pre><code class="language-julia hljs">sha2_384(io::IO)</code></pre><p>Hash data from io using <code>sha2_384</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L109-L113">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.sha2_512" href="#SHA.sha2_512"><code>SHA.sha2_512</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sha2_512(data)</code></pre><p>Hash data using the <code>sha2_512</code> algorithm and return the resulting digest. See also <a href="SHA.html#SHA.SHA2_512_CTX"><code>SHA2_512_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L79-L84">source</a></section><section><div><pre><code class="language-julia hljs">sha2_512(io::IO)</code></pre><p>Hash data from io using <code>sha2_512</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L109-L113">source</a></section></article><p><strong>SHA-3</strong></p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.sha3_224" href="#SHA.sha3_224"><code>SHA.sha3_224</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sha3_224(data)</code></pre><p>Hash data using the <code>sha3_224</code> algorithm and return the resulting digest. See also <a href="SHA.html#SHA.SHA3_224_CTX"><code>SHA3_224_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L79-L84">source</a></section><section><div><pre><code class="language-julia hljs">sha3_224(io::IO)</code></pre><p>Hash data from io using <code>sha3_224</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L109-L113">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.sha3_256" href="#SHA.sha3_256"><code>SHA.sha3_256</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sha3_256(data)</code></pre><p>Hash data using the <code>sha3_256</code> algorithm and return the resulting digest. See also <a href="SHA.html#SHA.SHA3_256_CTX"><code>SHA3_256_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L79-L84">source</a></section><section><div><pre><code class="language-julia hljs">sha3_256(io::IO)</code></pre><p>Hash data from io using <code>sha3_256</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L109-L113">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.sha3_384" href="#SHA.sha3_384"><code>SHA.sha3_384</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sha3_384(data)</code></pre><p>Hash data using the <code>sha3_384</code> algorithm and return the resulting digest. See also <a href="SHA.html#SHA.SHA3_384_CTX"><code>SHA3_384_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L79-L84">source</a></section><section><div><pre><code class="language-julia hljs">sha3_384(io::IO)</code></pre><p>Hash data from io using <code>sha3_384</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L109-L113">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.sha3_512" href="#SHA.sha3_512"><code>SHA.sha3_512</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sha3_512(data)</code></pre><p>Hash data using the <code>sha3_512</code> algorithm and return the resulting digest. See also <a href="SHA.html#SHA.SHA3_512_CTX"><code>SHA3_512_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L79-L84">source</a></section><section><div><pre><code class="language-julia hljs">sha3_512(io::IO)</code></pre><p>Hash data from io using <code>sha3_512</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L109-L113">source</a></section></article><h2 id="Working-with-context"><a class="docs-heading-anchor" href="#Working-with-context">Working with context</a><a id="Working-with-context-1"></a><a class="docs-heading-anchor-permalink" href="#Working-with-context" title="Permalink"></a></h2><p>To create a hash from multiple items the <code>SHAX_XXX_CTX()</code> types can be used to create a stateful hash object that is updated with <code>update!</code> and finalized with <code>digest!</code></p><pre><code class="language-julia-repl hljs">julia&gt; using SHA

julia&gt; ctx = SHA2_256_CTX()
SHA2 256-bit hash state

julia&gt; update!(ctx, b&quot;some data&quot;)
0x0000000000000009

julia&gt; update!(ctx, b&quot;some more data&quot;)
0x0000000000000017

julia&gt; digest!(ctx)
32-element Vector{UInt8}:
 0xbe
 0xcf
 0x23
 0xda
 0xaf
 0x02
 0xf7
 0xa3
 0x57
 0x92
    ⋮
 0x89
 0x4f
 0x59
 0xd8
 0xb3
 0xb4
 0x81
 0x8b
 0xc5</code></pre><p>Note that, at the time of this writing, the SHA3 code is not optimized, and as such is roughly an order of magnitude slower than SHA2.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.update!" href="#SHA.update!"><code>SHA.update!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">update!(context, data[, datalen])</code></pre><p>Update the SHA context with the bytes in data. See also <a href="SHA.html#SHA.digest!"><code>digest!</code></a> for finalizing the hash.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; ctx = SHA1_CTX()
SHA1 hash state

julia&gt; update!(ctx, b&quot;data to to be hashed&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/common.jl#L5-L18">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.digest!" href="#SHA.digest!"><code>SHA.digest!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">digest!(context)</code></pre><p>Finalize the SHA context and return the hash as array of bytes (Array{Uint8, 1}). Updating the context after calling <code>digest!</code> on it will error.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; ctx = SHA1_CTX()
SHA1 hash state

julia&gt; update!(ctx, b&quot;data to to be hashed&quot;)

julia&gt; digest!(ctx)
20-element Array{UInt8,1}:
 0x83
 0xe4
 ⋮
 0x89
 0xf5

julia&gt; update!(ctx, b&quot;more data&quot;)
ERROR: Cannot update CTX after `digest!` has been called on it
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/common.jl#L82-L107">source</a></section></article><h3 id="All-SHA-context-types"><a class="docs-heading-anchor" href="#All-SHA-context-types">All SHA context types</a><a id="All-SHA-context-types-1"></a><a class="docs-heading-anchor-permalink" href="#All-SHA-context-types" title="Permalink"></a></h3><p><strong>SHA-1</strong></p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.SHA1_CTX" href="#SHA.SHA1_CTX"><code>SHA.SHA1_CTX</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SHA1_CTX()</code></pre><p>Construct an empty SHA1 context.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/types.jl#L213-L217">source</a></section></article><p><strong>SHA-2</strong></p><p>Convenience types are also provided, where <code>SHAXXX_CTX</code> is a type alias for <code>SHA2_XXX_CTX</code>.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.SHA224_CTX" href="#SHA.SHA224_CTX"><code>SHA.SHA224_CTX</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SHA2_224_CTX()</code></pre><p>Construct an empty SHA2_224 context.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/types.jl#L156-L160">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.SHA256_CTX" href="#SHA.SHA256_CTX"><code>SHA.SHA256_CTX</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SHA2_256_CTX()</code></pre><p>Construct an empty SHA2_256 context.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/types.jl#L162-L166">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.SHA384_CTX" href="#SHA.SHA384_CTX"><code>SHA.SHA384_CTX</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SHA2_384()</code></pre><p>Construct an empty SHA2_384 context.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/types.jl#L168-L172">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.SHA512_CTX" href="#SHA.SHA512_CTX"><code>SHA.SHA512_CTX</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SHA2_512_CTX()</code></pre><p>Construct an empty SHA2_512 context.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/types.jl#L174-L178">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.SHA2_224_CTX" href="#SHA.SHA2_224_CTX"><code>SHA.SHA2_224_CTX</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SHA2_224_CTX()</code></pre><p>Construct an empty SHA2_224 context.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/types.jl#L156-L160">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.SHA2_256_CTX" href="#SHA.SHA2_256_CTX"><code>SHA.SHA2_256_CTX</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SHA2_256_CTX()</code></pre><p>Construct an empty SHA2_256 context.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/types.jl#L162-L166">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.SHA2_384_CTX" href="#SHA.SHA2_384_CTX"><code>SHA.SHA2_384_CTX</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SHA2_384()</code></pre><p>Construct an empty SHA2_384 context.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/types.jl#L168-L172">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.SHA2_512_CTX" href="#SHA.SHA2_512_CTX"><code>SHA.SHA2_512_CTX</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SHA2_512_CTX()</code></pre><p>Construct an empty SHA2_512 context.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/types.jl#L174-L178">source</a></section></article><p><strong>SHA-3</strong></p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.SHA3_224_CTX" href="#SHA.SHA3_224_CTX"><code>SHA.SHA3_224_CTX</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SHA3_224_CTX()</code></pre><p>Construct an empty SHA3_224 context.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/types.jl#L181-L185">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.SHA3_256_CTX" href="#SHA.SHA3_256_CTX"><code>SHA.SHA3_256_CTX</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SHA3_256_CTX()</code></pre><p>Construct an empty SHA3_256 context.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/types.jl#L187-L191">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.SHA3_384_CTX" href="#SHA.SHA3_384_CTX"><code>SHA.SHA3_384_CTX</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SHA3_384_CTX()</code></pre><p>Construct an empty SHA3_384 context.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/types.jl#L193-L197">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.SHA3_512_CTX" href="#SHA.SHA3_512_CTX"><code>SHA.SHA3_512_CTX</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SHA3_512_CTX()</code></pre><p>Construct an empty SHA3_512 context.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/types.jl#L199-L203">source</a></section></article><h2 id="HMAC-functions"><a class="docs-heading-anchor" href="#HMAC-functions">HMAC functions</a><a id="HMAC-functions-1"></a><a class="docs-heading-anchor-permalink" href="#HMAC-functions" title="Permalink"></a></h2><pre><code class="language-julia-repl hljs">julia&gt; using SHA

julia&gt; key = collect(codeunits(&quot;key_string&quot;))
10-element Vector{UInt8}:
 0x6b
 0x65
 0x79
 0x5f
 0x73
 0x74
 0x72
 0x69
 0x6e
 0x67

julia&gt; bytes2hex(hmac_sha3_256(key, &quot;test-message&quot;))
&quot;bc49a6f2aa29b27ee5ed1e944edd7f3d153e8a01535d98b5e24dac9a589a6248&quot;</code></pre><p>To create a hash from multiple items, the <code>HMAC_CTX()</code> types can be used to create a stateful hash object that is updated with <code>update!</code> and finalized with <code>digest!</code>.</p><pre><code class="language-julia-repl hljs">julia&gt; using SHA

julia&gt; key = collect(codeunits(&quot;key_string&quot;))
10-element Vector{UInt8}:
 0x6b
 0x65
 0x79
 0x5f
 0x73
 0x74
 0x72
 0x69
 0x6e
 0x67

julia&gt; ctx = HMAC_CTX(SHA3_256_CTX(), key);

julia&gt; update!(ctx, b&quot;test-&quot;)
0x0000000000000000000000000000008d

julia&gt; update!(ctx, b&quot;message&quot;)
0x00000000000000000000000000000094

julia&gt; bytes2hex(digest!(ctx))
&quot;bc49a6f2aa29b27ee5ed1e944edd7f3d153e8a01535d98b5e24dac9a589a6248&quot;</code></pre><h3 id="All-HMAC-functions"><a class="docs-heading-anchor" href="#All-HMAC-functions">All HMAC functions</a><a id="All-HMAC-functions-1"></a><a class="docs-heading-anchor-permalink" href="#All-HMAC-functions" title="Permalink"></a></h3><p><strong>HMAC context type</strong></p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.HMAC_CTX" href="#SHA.HMAC_CTX"><code>SHA.HMAC_CTX</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">HMAC_CTX(ctx::CTX, key::Vector{UInt8}) where {CTX&lt;:SHA_CTX}</code></pre><p>Construct an empty HMAC_CTX context.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/hmac.jl#L1-L5">source</a></section></article><p><strong>SHA-1</strong></p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.hmac_sha1" href="#SHA.hmac_sha1"><code>SHA.hmac_sha1</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hmac_sha1(key, data)</code></pre><p>Hash data using the <code>sha1</code> algorithm using the passed key. See also <a href="SHA.html#SHA.HMAC_CTX"><code>HMAC_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L91-L96">source</a></section><section><div><pre><code class="language-julia hljs">hmac_sha1(key, io::IO)</code></pre><p>Hash data from <code>io</code> with the passed key using <code>sha1</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L124-L128">source</a></section></article><p><strong>SHA-2</strong></p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.hmac_sha224" href="#SHA.hmac_sha224"><code>SHA.hmac_sha224</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hmac_sha224(key, data)</code></pre><p>Hash data using the <code>sha224</code> algorithm using the passed key. See also <a href="SHA.html#SHA.HMAC_CTX"><code>HMAC_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L91-L96">source</a></section><section><div><pre><code class="language-julia hljs">hmac_sha224(key, io::IO)</code></pre><p>Hash data from <code>io</code> with the passed key using <code>sha224</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L124-L128">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.hmac_sha256" href="#SHA.hmac_sha256"><code>SHA.hmac_sha256</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hmac_sha256(key, data)</code></pre><p>Hash data using the <code>sha256</code> algorithm using the passed key. See also <a href="SHA.html#SHA.HMAC_CTX"><code>HMAC_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L91-L96">source</a></section><section><div><pre><code class="language-julia hljs">hmac_sha256(key, io::IO)</code></pre><p>Hash data from <code>io</code> with the passed key using <code>sha256</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L124-L128">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.hmac_sha384" href="#SHA.hmac_sha384"><code>SHA.hmac_sha384</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hmac_sha384(key, data)</code></pre><p>Hash data using the <code>sha384</code> algorithm using the passed key. See also <a href="SHA.html#SHA.HMAC_CTX"><code>HMAC_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L91-L96">source</a></section><section><div><pre><code class="language-julia hljs">hmac_sha384(key, io::IO)</code></pre><p>Hash data from <code>io</code> with the passed key using <code>sha384</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L124-L128">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.hmac_sha512" href="#SHA.hmac_sha512"><code>SHA.hmac_sha512</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hmac_sha512(key, data)</code></pre><p>Hash data using the <code>sha512</code> algorithm using the passed key. See also <a href="SHA.html#SHA.HMAC_CTX"><code>HMAC_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L91-L96">source</a></section><section><div><pre><code class="language-julia hljs">hmac_sha512(key, io::IO)</code></pre><p>Hash data from <code>io</code> with the passed key using <code>sha512</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L124-L128">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.hmac_sha2_224" href="#SHA.hmac_sha2_224"><code>SHA.hmac_sha2_224</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hmac_sha2_224(key, data)</code></pre><p>Hash data using the <code>sha2_224</code> algorithm using the passed key. See also <a href="SHA.html#SHA.HMAC_CTX"><code>HMAC_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L91-L96">source</a></section><section><div><pre><code class="language-julia hljs">hmac_sha2_224(key, io::IO)</code></pre><p>Hash data from <code>io</code> with the passed key using <code>sha2_224</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L124-L128">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.hmac_sha2_256" href="#SHA.hmac_sha2_256"><code>SHA.hmac_sha2_256</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hmac_sha2_256(key, data)</code></pre><p>Hash data using the <code>sha2_256</code> algorithm using the passed key. See also <a href="SHA.html#SHA.HMAC_CTX"><code>HMAC_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L91-L96">source</a></section><section><div><pre><code class="language-julia hljs">hmac_sha2_256(key, io::IO)</code></pre><p>Hash data from <code>io</code> with the passed key using <code>sha2_256</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L124-L128">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.hmac_sha2_384" href="#SHA.hmac_sha2_384"><code>SHA.hmac_sha2_384</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hmac_sha2_384(key, data)</code></pre><p>Hash data using the <code>sha2_384</code> algorithm using the passed key. See also <a href="SHA.html#SHA.HMAC_CTX"><code>HMAC_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L91-L96">source</a></section><section><div><pre><code class="language-julia hljs">hmac_sha2_384(key, io::IO)</code></pre><p>Hash data from <code>io</code> with the passed key using <code>sha2_384</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L124-L128">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.hmac_sha2_512" href="#SHA.hmac_sha2_512"><code>SHA.hmac_sha2_512</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hmac_sha2_512(key, data)</code></pre><p>Hash data using the <code>sha2_512</code> algorithm using the passed key. See also <a href="SHA.html#SHA.HMAC_CTX"><code>HMAC_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L91-L96">source</a></section><section><div><pre><code class="language-julia hljs">hmac_sha2_512(key, io::IO)</code></pre><p>Hash data from <code>io</code> with the passed key using <code>sha2_512</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L124-L128">source</a></section></article><p><strong>SHA-3</strong></p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.hmac_sha3_224" href="#SHA.hmac_sha3_224"><code>SHA.hmac_sha3_224</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hmac_sha3_224(key, data)</code></pre><p>Hash data using the <code>sha3_224</code> algorithm using the passed key. See also <a href="SHA.html#SHA.HMAC_CTX"><code>HMAC_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L91-L96">source</a></section><section><div><pre><code class="language-julia hljs">hmac_sha3_224(key, io::IO)</code></pre><p>Hash data from <code>io</code> with the passed key using <code>sha3_224</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L124-L128">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.hmac_sha3_256" href="#SHA.hmac_sha3_256"><code>SHA.hmac_sha3_256</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hmac_sha3_256(key, data)</code></pre><p>Hash data using the <code>sha3_256</code> algorithm using the passed key. See also <a href="SHA.html#SHA.HMAC_CTX"><code>HMAC_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L91-L96">source</a></section><section><div><pre><code class="language-julia hljs">hmac_sha3_256(key, io::IO)</code></pre><p>Hash data from <code>io</code> with the passed key using <code>sha3_256</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L124-L128">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.hmac_sha3_384" href="#SHA.hmac_sha3_384"><code>SHA.hmac_sha3_384</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hmac_sha3_384(key, data)</code></pre><p>Hash data using the <code>sha3_384</code> algorithm using the passed key. See also <a href="SHA.html#SHA.HMAC_CTX"><code>HMAC_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L91-L96">source</a></section><section><div><pre><code class="language-julia hljs">hmac_sha3_384(key, io::IO)</code></pre><p>Hash data from <code>io</code> with the passed key using <code>sha3_384</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L124-L128">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SHA.hmac_sha3_512" href="#SHA.hmac_sha3_512"><code>SHA.hmac_sha3_512</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hmac_sha3_512(key, data)</code></pre><p>Hash data using the <code>sha3_512</code> algorithm using the passed key. See also <a href="SHA.html#SHA.HMAC_CTX"><code>HMAC_CTX</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L91-L96">source</a></section><section><div><pre><code class="language-julia hljs">hmac_sha3_512(key, io::IO)</code></pre><p>Hash data from <code>io</code> with the passed key using <code>sha3_512</code> algorithm.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaCrypto/SHA.jl/blob/aaf2df61ff8c3898196587a375d3cf213bd40b41/src/SHA.jl#L124-L128">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="Random.html">« Random Numbers</a><a class="docs-footer-nextpage" href="Serialization.html">Serialization »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
