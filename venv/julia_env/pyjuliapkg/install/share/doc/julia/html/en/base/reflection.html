<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Reflection and introspection · The Julia Language</title><meta name="title" content="Reflection and introspection · The Julia Language"/><meta property="og:title" content="Reflection and introspection · The Julia Language"/><meta property="twitter:title" content="Reflection and introspection · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox" checked/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="base.html">Essentials</a></li><li><a class="tocitem" href="collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="math.html">Mathematics</a></li><li><a class="tocitem" href="numbers.html">Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="arrays.html">Arrays</a></li><li><a class="tocitem" href="parallel.html">Tasks</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="constants.html">Constants</a></li><li><a class="tocitem" href="file.html">Filesystem</a></li><li><a class="tocitem" href="io-network.html">I/O and Network</a></li><li><a class="tocitem" href="punctuation.html">Punctuation</a></li><li><a class="tocitem" href="sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="iterators.html">Iteration utilities</a></li><li class="is-active"><a class="tocitem" href="reflection.html">Reflection and introspection</a><ul class="internal"><li><a class="tocitem" href="#Module-bindings"><span>Module bindings</span></a></li><li><a class="tocitem" href="#DataType-fields"><span>DataType fields</span></a></li><li><a class="tocitem" href="#Subtypes"><span>Subtypes</span></a></li><li><a class="tocitem" href="#DataType-layout"><span>DataType layout</span></a></li><li><a class="tocitem" href="#Function-methods"><span>Function methods</span></a></li><li><a class="tocitem" href="#Expansion-and-lowering"><span>Expansion and lowering</span></a></li><li><a class="tocitem" href="#Intermediate-and-compiled-representations"><span>Intermediate and compiled representations</span></a></li></ul></li><li><a class="tocitem" href="c.html">C Interface</a></li><li><a class="tocitem" href="libc.html">C Standard Library</a></li><li><a class="tocitem" href="stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Base</a></li><li class="is-active"><a href="reflection.html">Reflection and introspection</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="reflection.html">Reflection and introspection</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/base/reflection.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Reflection-and-introspection"><a class="docs-heading-anchor" href="#Reflection-and-introspection">Reflection and introspection</a><a id="Reflection-and-introspection-1"></a><a class="docs-heading-anchor-permalink" href="#Reflection-and-introspection" title="Permalink"></a></h1><p>Julia provides a variety of runtime reflection capabilities.</p><h2 id="Module-bindings"><a class="docs-heading-anchor" href="#Module-bindings">Module bindings</a><a id="Module-bindings-1"></a><a class="docs-heading-anchor-permalink" href="#Module-bindings" title="Permalink"></a></h2><p>The public names for a <code>Module</code> are available using <a href="base.html#Base.names"><code>names(m::Module)</code></a>, which will return an array of <a href="base.html#Core.Symbol"><code>Symbol</code></a> elements representing the public bindings. <code>names(m::Module, all = true)</code> returns symbols for all bindings in <code>m</code>, regardless of public status.</p><h2 id="DataType-fields"><a class="docs-heading-anchor" href="#DataType-fields">DataType fields</a><a id="DataType-fields-1"></a><a class="docs-heading-anchor-permalink" href="#DataType-fields" title="Permalink"></a></h2><p>The names of <code>DataType</code> fields may be interrogated using <a href="base.html#Base.fieldnames"><code>fieldnames</code></a>. For example, given the following type, <code>fieldnames(Point)</code> returns a tuple of <a href="base.html#Core.Symbol"><code>Symbol</code></a>s representing the field names:</p><pre><code class="language-julia-repl hljs">julia&gt; struct Point
           x::Int
           y
       end

julia&gt; fieldnames(Point)
(:x, :y)</code></pre><p>The type of each field in a <code>Point</code> object is stored in the <code>types</code> field of the <code>Point</code> variable itself:</p><pre><code class="language-julia-repl hljs">julia&gt; Point.types
svec(Int64, Any)</code></pre><p>While <code>x</code> is annotated as an <code>Int</code>, <code>y</code> was unannotated in the type definition, therefore <code>y</code> defaults to the <code>Any</code> type.</p><p>Types are themselves represented as a structure called <code>DataType</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; typeof(Point)
DataType</code></pre><p>Note that <code>fieldnames(DataType)</code> gives the names for each field of <code>DataType</code> itself, and one of these fields is the <code>types</code> field observed in the example above.</p><h2 id="Subtypes"><a class="docs-heading-anchor" href="#Subtypes">Subtypes</a><a id="Subtypes-1"></a><a class="docs-heading-anchor-permalink" href="#Subtypes" title="Permalink"></a></h2><p>The <em>direct</em> subtypes of any <code>DataType</code> may be listed using <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.subtypes"><code>subtypes</code></a>. For example, the abstract <code>DataType</code> <a href="numbers.html#Core.AbstractFloat"><code>AbstractFloat</code></a> has four (concrete) subtypes:</p><pre><code class="language-julia-repl hljs">julia&gt; InteractiveUtils.subtypes(AbstractFloat)
5-element Vector{Any}:
 BigFloat
 Core.BFloat16
 Float16
 Float32
 Float64</code></pre><p>Any abstract subtype will also be included in this list, but further subtypes thereof will not; recursive application of <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.subtypes"><code>subtypes</code></a> may be used to inspect the full type tree.</p><p>Note that <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.subtypes"><code>subtypes</code></a> is located inside <a href="../stdlib/InteractiveUtils.html#man-interactive-utils"><code>InteractiveUtils</code></a> but is automatically exported when using the REPL.</p><h2 id="DataType-layout"><a class="docs-heading-anchor" href="#DataType-layout">DataType layout</a><a id="DataType-layout-1"></a><a class="docs-heading-anchor-permalink" href="#DataType-layout" title="Permalink"></a></h2><p>The internal representation of a <code>DataType</code> is critically important when interfacing with C code and several functions are available to inspect these details. <a href="base.html#Base.isbitstype"><code>isbitstype(T::DataType)</code></a> returns true if <code>T</code> is stored with C-compatible alignment. <a href="base.html#Base.fieldoffset"><code>fieldoffset(T::DataType, i::Integer)</code></a> returns the (byte) offset for field <em>i</em> relative to the start of the type.</p><h2 id="Function-methods"><a class="docs-heading-anchor" href="#Function-methods">Function methods</a><a id="Function-methods-1"></a><a class="docs-heading-anchor-permalink" href="#Function-methods" title="Permalink"></a></h2><p>The methods of any generic function may be listed using <a href="base.html#Base.methods"><code>methods</code></a>. The method dispatch table may be searched for methods accepting a given type using <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.methodswith"><code>methodswith</code></a>.</p><h2 id="Expansion-and-lowering"><a class="docs-heading-anchor" href="#Expansion-and-lowering">Expansion and lowering</a><a id="Expansion-and-lowering-1"></a><a class="docs-heading-anchor-permalink" href="#Expansion-and-lowering" title="Permalink"></a></h2><p>As discussed in the <a href="../manual/metaprogramming.html#Metaprogramming">Metaprogramming</a> section, the <a href="base.html#Base.macroexpand"><code>macroexpand</code></a> function gives the unquoted and interpolated expression (<a href="base.html#Core.Expr"><code>Expr</code></a>) form for a given macro. To use <code>macroexpand</code>, <code>quote</code> the expression block itself (otherwise, the macro will be evaluated and the result will be passed instead!). For example:</p><pre><code class="language-julia-repl hljs">julia&gt; InteractiveUtils.macroexpand(@__MODULE__, :(@edit println(&quot;&quot;)) )
:(InteractiveUtils.edit(println, (Base.typesof)(&quot;&quot;)))</code></pre><p>The functions <code>Base.Meta.show_sexpr</code> and <a href="io-network.html#Base.dump"><code>dump</code></a> are used to display S-expr style views and depth-nested detail views for any expression.</p><p>Finally, the <a href="base.html#Base.Meta.lower"><code>Meta.lower</code></a> function gives the <code>lowered</code> form of any expression and is of particular interest for understanding how language constructs map to primitive operations such as assignments, branches, and calls:</p><pre><code class="language-julia-repl hljs">julia&gt; Meta.lower(@__MODULE__, :( [1+2, sin(0.5)] ))
:($(Expr(:thunk, CodeInfo(
    @ none within `top-level scope`
1 ─ %1 = 1 + 2
│   %2 = sin(0.5)
│   %3 = Base.vect(%1, %2)
└──      return %3
))))</code></pre><h2 id="Intermediate-and-compiled-representations"><a class="docs-heading-anchor" href="#Intermediate-and-compiled-representations">Intermediate and compiled representations</a><a id="Intermediate-and-compiled-representations-1"></a><a class="docs-heading-anchor-permalink" href="#Intermediate-and-compiled-representations" title="Permalink"></a></h2><p>Inspecting the lowered form for functions requires selection of the specific method to display, because generic functions may have many methods with different type signatures. For this purpose, method-specific code-lowering is available using <a href="base.html#Base.code_lowered"><code>code_lowered</code></a>, and the type-inferred form is available using <a href="base.html#Base.code_typed"><code>code_typed</code></a>. <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.code_warntype"><code>code_warntype</code></a> adds highlighting to the output of <a href="base.html#Base.code_typed"><code>code_typed</code></a>.</p><p>Closer to the machine, the LLVM intermediate representation of a function may be printed using by <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.code_llvm"><code>code_llvm</code></a>, and finally the compiled machine code is available using <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.code_native"><code>code_native</code></a> (this will trigger JIT compilation/code generation for any function which has not previously been called).</p><p>For convenience, there are macro versions of the above functions which take standard function calls and expand argument types automatically:</p><pre><code class="language-julia-repl hljs">julia&gt; @code_llvm +(1,1)
;  @ int.jl:87 within `+`
; Function Attrs: sspstrong uwtable
define i64 @&quot;julia_+_476&quot;(i64 signext %0, i64 signext %1) #0 {
top:
  %2 = add i64 %1, %0
  ret i64 %2
}</code></pre><p>For more information see <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.@code_lowered"><code>@code_lowered</code></a>, <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.@code_typed"><code>@code_typed</code></a>, <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.@code_warntype"><code>@code_warntype</code></a>, <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.@code_llvm"><code>@code_llvm</code></a>, and <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.@code_native"><code>@code_native</code></a>.</p><h3 id="Printing-of-debug-information"><a class="docs-heading-anchor" href="#Printing-of-debug-information">Printing of debug information</a><a id="Printing-of-debug-information-1"></a><a class="docs-heading-anchor-permalink" href="#Printing-of-debug-information" title="Permalink"></a></h3><p>The aforementioned functions and macros take the keyword argument <code>debuginfo</code> that controls the level debug information printed.</p><pre><code class="language-julia-repl hljs">julia&gt; InteractiveUtils.@code_typed debuginfo=:source +(1,1)
CodeInfo(
    @ int.jl:87 within `+`
1 ─ %1 = Base.add_int(x, y)::Int64
└──      return %1
) =&gt; Int64</code></pre><p>Possible values for <code>debuginfo</code> are: <code>:none</code>, <code>:source</code>, and <code>:default</code>. Per default debug information is not printed, but that can be changed by setting <code>Base.IRShow.default_debuginfo[] = :source</code>.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="iterators.html">« Iteration utilities</a><a class="docs-footer-nextpage" href="c.html">C Interface »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
