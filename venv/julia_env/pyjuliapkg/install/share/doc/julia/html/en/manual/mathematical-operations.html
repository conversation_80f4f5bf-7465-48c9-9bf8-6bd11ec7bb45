<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Mathematical Operations and Elementary Functions · The Julia Language</title><meta name="title" content="Mathematical Operations and Elementary Functions · The Julia Language"/><meta property="og:title" content="Mathematical Operations and Elementary Functions · The Julia Language"/><meta property="twitter:title" content="Mathematical Operations and Elementary Functions · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li class="is-active"><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a><ul class="internal"><li><a class="tocitem" href="#Arithmetic-Operators"><span>Arithmetic Operators</span></a></li><li><a class="tocitem" href="#Boolean-Operators"><span>Boolean Operators</span></a></li><li><a class="tocitem" href="#Bitwise-Operators"><span>Bitwise Operators</span></a></li><li><a class="tocitem" href="#Updating-operators"><span>Updating operators</span></a></li><li><a class="tocitem" href="#man-dot-operators"><span>Vectorized &quot;dot&quot; operators</span></a></li><li><a class="tocitem" href="#Numeric-Comparisons"><span>Numeric Comparisons</span></a></li><li><a class="tocitem" href="#Operator-Precedence-and-Associativity"><span>Operator Precedence and Associativity</span></a></li><li><a class="tocitem" href="#Numerical-Conversions"><span>Numerical Conversions</span></a></li></ul></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/mathematical-operations.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Mathematical-Operations-and-Elementary-Functions"><a class="docs-heading-anchor" href="#Mathematical-Operations-and-Elementary-Functions">Mathematical Operations and Elementary Functions</a><a id="Mathematical-Operations-and-Elementary-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Mathematical-Operations-and-Elementary-Functions" title="Permalink"></a></h1><p>Julia provides a complete collection of basic arithmetic and bitwise operators across all of its numeric primitive types, as well as providing portable, efficient implementations of a comprehensive collection of standard mathematical functions.</p><h2 id="Arithmetic-Operators"><a class="docs-heading-anchor" href="#Arithmetic-Operators">Arithmetic Operators</a><a id="Arithmetic-Operators-1"></a><a class="docs-heading-anchor-permalink" href="#Arithmetic-Operators" title="Permalink"></a></h2><p>The following <a href="https://en.wikipedia.org/wiki/Arithmetic#Arithmetic_operations">arithmetic operators</a> are supported on all primitive numeric types:</p><table><tr><th style="text-align: left">Expression</th><th style="text-align: left">Name</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left"><code>+x</code></td><td style="text-align: left">unary plus</td><td style="text-align: left">the identity operation</td></tr><tr><td style="text-align: left"><code>-x</code></td><td style="text-align: left">unary minus</td><td style="text-align: left">maps values to their additive inverses</td></tr><tr><td style="text-align: left"><code>x + y</code></td><td style="text-align: left">binary plus</td><td style="text-align: left">performs addition</td></tr><tr><td style="text-align: left"><code>x - y</code></td><td style="text-align: left">binary minus</td><td style="text-align: left">performs subtraction</td></tr><tr><td style="text-align: left"><code>x * y</code></td><td style="text-align: left">times</td><td style="text-align: left">performs multiplication</td></tr><tr><td style="text-align: left"><code>x / y</code></td><td style="text-align: left">divide</td><td style="text-align: left">performs division</td></tr><tr><td style="text-align: left"><code>x ÷ y</code></td><td style="text-align: left">integer divide</td><td style="text-align: left">x / y, truncated to an integer</td></tr><tr><td style="text-align: left"><code>x \ y</code></td><td style="text-align: left">inverse divide</td><td style="text-align: left">equivalent to <code>y / x</code></td></tr><tr><td style="text-align: left"><code>x ^ y</code></td><td style="text-align: left">power</td><td style="text-align: left">raises <code>x</code> to the <code>y</code>th power</td></tr><tr><td style="text-align: left"><code>x % y</code></td><td style="text-align: left">remainder</td><td style="text-align: left">equivalent to <code>rem(x, y)</code></td></tr></table><p>A numeric literal placed directly before an identifier or parentheses, e.g. <code>2x</code> or <code>2(x + y)</code>, is treated as a multiplication, except with higher precedence than other binary operations.  See <a href="integers-and-floating-point-numbers.html#man-numeric-literal-coefficients">Numeric Literal Coefficients</a> for details.</p><p>Julia&#39;s promotion system makes arithmetic operations on mixtures of argument types &quot;just work&quot; naturally and automatically. See <a href="conversion-and-promotion.html#conversion-and-promotion">Conversion and Promotion</a> for details of the promotion system.</p><p>The ÷ sign can be conveniently typed by writing <code>\div&lt;tab&gt;</code> to the REPL or Julia IDE. See the <a href="unicode-input.html#Unicode-Input">manual section on Unicode input</a> for more information.</p><p>Here are some simple examples using arithmetic operators:</p><pre><code class="language-julia-repl hljs">julia&gt; 1 + 2 + 3
6

julia&gt; 1 - 2
-1

julia&gt; 3*2/12
0.5</code></pre><p>(By convention, we tend to space operators more tightly if they get applied before other nearby operators. For instance, we would generally write <code>-x + 2</code> to reflect that first <code>x</code> gets negated, and then <code>2</code> is added to that result.)</p><p>When used in multiplication, <code>false</code> acts as a <em>strong zero</em>:</p><pre><code class="language-julia-repl hljs">julia&gt; NaN * false
0.0

julia&gt; false * Inf
0.0</code></pre><p>This is useful for preventing the propagation of <code>NaN</code> values in quantities that are known to be zero. See <a href="https://arxiv.org/abs/math/9205211">Knuth (1992)</a> for motivation.</p><h2 id="Boolean-Operators"><a class="docs-heading-anchor" href="#Boolean-Operators">Boolean Operators</a><a id="Boolean-Operators-1"></a><a class="docs-heading-anchor-permalink" href="#Boolean-Operators" title="Permalink"></a></h2><p>The following <a href="https://en.wikipedia.org/wiki/Boolean_algebra#Operations">Boolean operators</a> are supported on <a href="../base/numbers.html#Core.Bool"><code>Bool</code></a> types:</p><table><tr><th style="text-align: left">Expression</th><th style="text-align: left">Name</th></tr><tr><td style="text-align: left"><code>!x</code></td><td style="text-align: left">negation</td></tr><tr><td style="text-align: left"><code>x &amp;&amp; y</code></td><td style="text-align: left"><a href="control-flow.html#man-conditional-evaluation">short-circuiting and</a></td></tr><tr><td style="text-align: left"><code>x || y</code></td><td style="text-align: left"><a href="control-flow.html#man-conditional-evaluation">short-circuiting or</a></td></tr></table><p>Negation changes <code>true</code> to <code>false</code> and vice versa. The short-circuiting operations are explained on the linked page.</p><p>Note that <code>Bool</code> is an integer type and all the usual promotion rules and numeric operators are also defined on it.</p><h2 id="Bitwise-Operators"><a class="docs-heading-anchor" href="#Bitwise-Operators">Bitwise Operators</a><a id="Bitwise-Operators-1"></a><a class="docs-heading-anchor-permalink" href="#Bitwise-Operators" title="Permalink"></a></h2><p>The following <a href="https://en.wikipedia.org/wiki/Bitwise_operation#Bitwise_operators">bitwise operators</a> are supported on all primitive integer types:</p><table><tr><th style="text-align: left">Expression</th><th style="text-align: left">Name</th></tr><tr><td style="text-align: left"><code>~x</code></td><td style="text-align: left">bitwise not</td></tr><tr><td style="text-align: left"><code>x &amp; y</code></td><td style="text-align: left">bitwise and</td></tr><tr><td style="text-align: left"><code>x | y</code></td><td style="text-align: left">bitwise or</td></tr><tr><td style="text-align: left"><code>x ⊻ y</code></td><td style="text-align: left">bitwise xor (exclusive or)</td></tr><tr><td style="text-align: left"><code>x ⊼ y</code></td><td style="text-align: left">bitwise nand (not and)</td></tr><tr><td style="text-align: left"><code>x ⊽ y</code></td><td style="text-align: left">bitwise nor (not or)</td></tr><tr><td style="text-align: left"><code>x &gt;&gt;&gt; y</code></td><td style="text-align: left"><a href="https://en.wikipedia.org/wiki/Logical_shift">logical shift</a> right</td></tr><tr><td style="text-align: left"><code>x &gt;&gt; y</code></td><td style="text-align: left"><a href="https://en.wikipedia.org/wiki/Arithmetic_shift">arithmetic shift</a> right</td></tr><tr><td style="text-align: left"><code>x &lt;&lt; y</code></td><td style="text-align: left">logical/arithmetic shift left</td></tr></table><p>Here are some examples with bitwise operators:</p><pre><code class="language-julia-repl hljs">julia&gt; ~123
-124

julia&gt; 123 &amp; 234
106

julia&gt; 123 | 234
251

julia&gt; 123 ⊻ 234
145

julia&gt; xor(123, 234)
145

julia&gt; nand(123, 123)
-124

julia&gt; 123 ⊼ 123
-124

julia&gt; nor(123, 124)
-128

julia&gt; 123 ⊽ 124
-128

julia&gt; ~UInt32(123)
0xffffff84

julia&gt; ~UInt8(123)
0x84</code></pre><h2 id="Updating-operators"><a class="docs-heading-anchor" href="#Updating-operators">Updating operators</a><a id="Updating-operators-1"></a><a class="docs-heading-anchor-permalink" href="#Updating-operators" title="Permalink"></a></h2><p>Every binary arithmetic and bitwise operator also has an updating version that assigns the result of the operation back into its left operand. The updating version of the binary operator is formed by placing a <code>=</code> immediately after the operator. For example, writing <code>x += 3</code> is equivalent to writing <code>x = x + 3</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; x = 1
1

julia&gt; x += 3
4

julia&gt; x
4</code></pre><p>The updating versions of all the binary arithmetic and bitwise operators are:</p><pre><code class="nohighlight hljs">+=  -=  *=  /=  \=  ÷=  %=  ^=  &amp;=  |=  ⊻=  &gt;&gt;&gt;=  &gt;&gt;=  &lt;&lt;=</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>An updating operator rebinds the variable on the left-hand side. As a result, the type of the variable may change.</p><pre><code class="language-julia-repl hljs">julia&gt; x = 0x01; typeof(x)
UInt8

julia&gt; x *= 2 # Same as x = x * 2
2

julia&gt; typeof(x)
Int64</code></pre></div></div><h2 id="man-dot-operators"><a class="docs-heading-anchor" href="#man-dot-operators">Vectorized &quot;dot&quot; operators</a><a id="man-dot-operators-1"></a><a class="docs-heading-anchor-permalink" href="#man-dot-operators" title="Permalink"></a></h2><p>For <em>every</em> binary operation like <code>^</code>, there is a corresponding &quot;dot&quot; operation <code>.^</code> that is <em>automatically</em> defined to perform <code>^</code> element-by-element on arrays. For example, <code>[1, 2, 3] ^ 3</code> is not defined, since there is no standard mathematical meaning to &quot;cubing&quot; a (non-square) array, but <code>[1, 2, 3] .^ 3</code> is defined as computing the elementwise (or &quot;vectorized&quot;) result <code>[1^3, 2^3, 3^3]</code>.  Similarly for unary operators like <code>!</code> or <code>√</code>, there is a corresponding <code>.√</code> that applies the operator elementwise.</p><pre><code class="language-julia-repl hljs">julia&gt; [1, 2, 3] .^ 3
3-element Vector{Int64}:
  1
  8
 27</code></pre><p>More specifically, <code>a .^ b</code> is parsed as the <a href="functions.html#man-vectorized">&quot;dot&quot; call</a> <code>(^).(a,b)</code>, which performs a <a href="arrays.html#Broadcasting">broadcast</a> operation: it can combine arrays and scalars, arrays of the same size (performing the operation elementwise), and even arrays of different shapes (e.g. combining row and column vectors to produce a matrix). Moreover, like all vectorized &quot;dot calls,&quot; these &quot;dot operators&quot; are <em>fusing</em>. For example, if you compute <code>2 .* A.^2 .+ sin.(A)</code> (or equivalently <code>@. 2A^2 + sin(A)</code>, using the <a href="../base/arrays.html#Base.Broadcast.@__dot__"><code>@.</code></a> macro) for an array <code>A</code>, it performs a <em>single</em> loop over <code>A</code>, computing <code>2a^2 + sin(a)</code> for each element <code>a</code> of <code>A</code>. In particular, nested dot calls like <code>f.(g.(x))</code> are fused, and &quot;adjacent&quot; binary operators like <code>x .+ 3 .* x.^2</code> are equivalent to nested dot calls <code>(+).(x, (*).(3, (^).(x, 2)))</code>.</p><p>Furthermore, &quot;dotted&quot; updating operators like <code>a .+= b</code> (or <code>@. a += b</code>) are parsed as <code>a .= a .+ b</code>, where <code>.=</code> is a fused <em>in-place</em> assignment operation (see the <a href="functions.html#man-vectorized">dot syntax documentation</a>).</p><p>Note the dot syntax is also applicable to user-defined operators. For example, if you define <code>⊗(A, B) = kron(A, B)</code> to give a convenient infix syntax <code>A ⊗ B</code> for Kronecker products (<a href="../stdlib/LinearAlgebra.html#Base.kron"><code>kron</code></a>), then <code>[A, B] .⊗ [C, D]</code> will compute <code>[A⊗C, B⊗D]</code> with no additional coding.</p><p>Combining dot operators with numeric literals can be ambiguous. For example, it is not clear whether <code>1.+x</code> means <code>1. + x</code> or <code>1 .+ x</code>. Therefore this syntax is disallowed, and spaces must be used around the operator in such cases.</p><h2 id="Numeric-Comparisons"><a class="docs-heading-anchor" href="#Numeric-Comparisons">Numeric Comparisons</a><a id="Numeric-Comparisons-1"></a><a class="docs-heading-anchor-permalink" href="#Numeric-Comparisons" title="Permalink"></a></h2><p>Standard comparison operations are defined for all the primitive numeric types:</p><table><tr><th style="text-align: left">Operator</th><th style="text-align: left">Name</th></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.:=="><code>==</code></a></td><td style="text-align: left">equality</td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.:!="><code>!=</code></a>, <a href="../base/math.html#Base.:!="><code>≠</code></a></td><td style="text-align: left">inequality</td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.:&lt;"><code>&lt;</code></a></td><td style="text-align: left">less than</td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.:&lt;="><code>&lt;=</code></a>, <a href="../base/math.html#Base.:&lt;="><code>≤</code></a></td><td style="text-align: left">less than or equal to</td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.:&gt;"><code>&gt;</code></a></td><td style="text-align: left">greater than</td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.:&gt;="><code>&gt;=</code></a>, <a href="../base/math.html#Base.:&gt;="><code>≥</code></a></td><td style="text-align: left">greater than or equal to</td></tr></table><p>Here are some simple examples:</p><pre><code class="language-julia-repl hljs">julia&gt; 1 == 1
true

julia&gt; 1 == 2
false

julia&gt; 1 != 2
true

julia&gt; 1 == 1.0
true

julia&gt; 1 &lt; 2
true

julia&gt; 1.0 &gt; 3
false

julia&gt; 1 &gt;= 1.0
true

julia&gt; -1 &lt;= 1
true

julia&gt; -1 &lt;= -1
true

julia&gt; -1 &lt;= -2
false

julia&gt; 3 &lt; -0.5
false</code></pre><p>Integers are compared in the standard manner – by comparison of bits. Floating-point numbers are compared according to the <a href="https://en.wikipedia.org/wiki/IEEE_754-2008">IEEE 754 standard</a>:</p><ul><li>Finite numbers are ordered in the usual manner.</li><li>Positive zero is equal but not greater than negative zero.</li><li><code>Inf</code> is equal to itself and greater than everything else except <code>NaN</code>.</li><li><code>-Inf</code> is equal to itself and less than everything else except <code>NaN</code>.</li><li><code>NaN</code> is not equal to, not less than, and not greater than anything, including itself.</li></ul><p>The last point is potentially surprising and thus worth noting:</p><pre><code class="language-julia-repl hljs">julia&gt; NaN == NaN
false

julia&gt; NaN != NaN
true

julia&gt; NaN &lt; NaN
false

julia&gt; NaN &gt; NaN
false</code></pre><p>and can cause headaches when working with <a href="arrays.html#man-multi-dim-arrays">arrays</a>:</p><pre><code class="language-julia-repl hljs">julia&gt; [1 NaN] == [1 NaN]
false</code></pre><p>Julia provides additional functions to test numbers for special values, which can be useful in situations like hash key comparisons:</p><table><tr><th style="text-align: left">Function</th><th style="text-align: left">Tests if</th></tr><tr><td style="text-align: left"><a href="../base/base.html#Base.isequal"><code>isequal(x, y)</code></a></td><td style="text-align: left"><code>x</code> and <code>y</code> are identical</td></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Base.isfinite"><code>isfinite(x)</code></a></td><td style="text-align: left"><code>x</code> is a finite number</td></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Base.isinf"><code>isinf(x)</code></a></td><td style="text-align: left"><code>x</code> is infinite</td></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Base.isnan"><code>isnan(x)</code></a></td><td style="text-align: left"><code>x</code> is not a number</td></tr></table><p><a href="../base/base.html#Base.isequal"><code>isequal</code></a> considers <code>NaN</code>s equal to each other:</p><pre><code class="language-julia-repl hljs">julia&gt; isequal(NaN, NaN)
true

julia&gt; isequal([1 NaN], [1 NaN])
true

julia&gt; isequal(NaN, NaN32)
true</code></pre><p><code>isequal</code> can also be used to distinguish signed zeros:</p><pre><code class="language-julia-repl hljs">julia&gt; -0.0 == 0.0
true

julia&gt; isequal(-0.0, 0.0)
false</code></pre><p>Mixed-type comparisons between signed integers, unsigned integers, and floats can be tricky. A great deal of care has been taken to ensure that Julia does them correctly.</p><p>For other types, <code>isequal</code> defaults to calling <a href="../base/math.html#Base.:=="><code>==</code></a>, so if you want to define equality for your own types then you only need to add a <a href="../base/math.html#Base.:=="><code>==</code></a> method.  If you define your own equality function, you should probably define a corresponding <a href="../base/base.html#Base.hash"><code>hash</code></a> method to ensure that <code>isequal(x,y)</code> implies <code>hash(x) == hash(y)</code>.</p><h3 id="Chaining-comparisons"><a class="docs-heading-anchor" href="#Chaining-comparisons">Chaining comparisons</a><a id="Chaining-comparisons-1"></a><a class="docs-heading-anchor-permalink" href="#Chaining-comparisons" title="Permalink"></a></h3><p>Unlike most languages, with the <a href="https://en.wikipedia.org/wiki/Python_syntax_and_semantics#Comparison_operators">notable exception of Python</a>, comparisons can be arbitrarily chained:</p><pre><code class="language-julia-repl hljs">julia&gt; 1 &lt; 2 &lt;= 2 &lt; 3 == 3 &gt; 2 &gt;= 1 == 1 &lt; 3 != 5
true</code></pre><p>Chaining comparisons is often quite convenient in numerical code. Chained comparisons use the <code>&amp;&amp;</code> operator for scalar comparisons, and the <a href="../base/math.html#Base.:&amp;"><code>&amp;</code></a> operator for elementwise comparisons, which allows them to work on arrays. For example, <code>0 .&lt; A .&lt; 1</code> gives a boolean array whose entries are true where the corresponding elements of <code>A</code> are between 0 and 1.</p><p>Note the evaluation behavior of chained comparisons:</p><pre><code class="language-julia-repl hljs">julia&gt; v(x) = (println(x); x)
v (generic function with 1 method)

julia&gt; v(1) &lt; v(2) &lt;= v(3)
2
1
3
true

julia&gt; v(1) &gt; v(2) &lt;= v(3)
2
1
false</code></pre><p>The middle expression is only evaluated once, rather than twice as it would be if the expression were written as <code>v(1) &lt; v(2) &amp;&amp; v(2) &lt;= v(3)</code>. However, the order of evaluations in a chained comparison is undefined. It is strongly recommended not to use expressions with side effects (such as printing) in chained comparisons. If side effects are required, the short-circuit <code>&amp;&amp;</code> operator should be used explicitly (see <a href="control-flow.html#Short-Circuit-Evaluation">Short-Circuit Evaluation</a>).</p><h3 id="Elementary-Functions"><a class="docs-heading-anchor" href="#Elementary-Functions">Elementary Functions</a><a id="Elementary-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Elementary-Functions" title="Permalink"></a></h3><p>Julia provides a comprehensive collection of mathematical functions and operators. These mathematical operations are defined over as broad a class of numerical values as permit sensible definitions, including integers, floating-point numbers, rationals, and complex numbers, wherever such definitions make sense.</p><p>Moreover, these functions (like any Julia function) can be applied in &quot;vectorized&quot; fashion to arrays and other collections with the <a href="functions.html#man-vectorized">dot syntax</a> <code>f.(A)</code>, e.g. <code>sin.(A)</code> will compute the sine of each element of an array <code>A</code>.</p><h2 id="Operator-Precedence-and-Associativity"><a class="docs-heading-anchor" href="#Operator-Precedence-and-Associativity">Operator Precedence and Associativity</a><a id="Operator-Precedence-and-Associativity-1"></a><a class="docs-heading-anchor-permalink" href="#Operator-Precedence-and-Associativity" title="Permalink"></a></h2><p>Julia applies the following order and associativity of operations, from highest precedence to lowest:</p><table><tr><th style="text-align: left">Category</th><th style="text-align: left">Operators</th><th style="text-align: left">Associativity</th></tr><tr><td style="text-align: left">Syntax</td><td style="text-align: left"><code>.</code> followed by <code>::</code></td><td style="text-align: left">Left</td></tr><tr><td style="text-align: left">Exponentiation</td><td style="text-align: left"><code>^</code></td><td style="text-align: left">Right</td></tr><tr><td style="text-align: left">Unary</td><td style="text-align: left"><code>+ - ! ~ ¬ √ ∛ ∜ ⋆ ± ∓ &lt;: &gt;:</code></td><td style="text-align: left">Right<sup class="footnote-reference"><a id="citeref-1" href="#footnote-1">[1]</a></sup></td></tr><tr><td style="text-align: left">Bitshifts</td><td style="text-align: left"><code>&lt;&lt; &gt;&gt; &gt;&gt;&gt;</code></td><td style="text-align: left">Left</td></tr><tr><td style="text-align: left">Fractions</td><td style="text-align: left"><code>//</code></td><td style="text-align: left">Left</td></tr><tr><td style="text-align: left">Multiplication</td><td style="text-align: left"><code>* / % &amp; \ ÷</code></td><td style="text-align: left">Left<sup class="footnote-reference"><a id="citeref-2" href="#footnote-2">[2]</a></sup></td></tr><tr><td style="text-align: left">Addition</td><td style="text-align: left"><code>+ - | ⊻</code></td><td style="text-align: left">Left<sup class="footnote-reference"><a id="citeref-2" href="#footnote-2">[2]</a></sup></td></tr><tr><td style="text-align: left">Syntax</td><td style="text-align: left"><code>: ..</code></td><td style="text-align: left">Left</td></tr><tr><td style="text-align: left">Syntax</td><td style="text-align: left"><code>|&gt;</code></td><td style="text-align: left">Left</td></tr><tr><td style="text-align: left">Syntax</td><td style="text-align: left"><code>&lt;|</code></td><td style="text-align: left">Right</td></tr><tr><td style="text-align: left">Comparisons</td><td style="text-align: left"><code>&gt; &lt; &gt;= &lt;= == === != !== &lt;:</code></td><td style="text-align: left">Non-associative</td></tr><tr><td style="text-align: left">Control flow</td><td style="text-align: left"><code>&amp;&amp;</code> followed by <code>||</code> followed by <code>?</code></td><td style="text-align: left">Right</td></tr><tr><td style="text-align: left">Pair</td><td style="text-align: left"><code>=&gt;</code></td><td style="text-align: left">Right</td></tr><tr><td style="text-align: left">Assignments</td><td style="text-align: left"><code>= += -= *= /= //= \= ^= ÷= %= |= &amp;= ⊻= &lt;&lt;= &gt;&gt;= &gt;&gt;&gt;=</code></td><td style="text-align: left">Right</td></tr></table><p>For a complete list of <em>every</em> Julia operator&#39;s precedence, see the top of this file: <a href="https://github.com/JuliaLang/julia/blob/master/src/julia-parser.scm"><code>src/julia-parser.scm</code></a>. Note that some of the operators there are not defined in the <code>Base</code> module but may be given definitions by standard libraries, packages or user code.</p><p>You can also find the numerical precedence for any given operator via the built-in function <code>Base.operator_precedence</code>, where higher numbers take precedence:</p><pre><code class="language-julia-repl hljs">julia&gt; Base.operator_precedence(:+), Base.operator_precedence(:*), Base.operator_precedence(:.)
(11, 12, 17)

julia&gt; Base.operator_precedence(:sin), Base.operator_precedence(:+=), Base.operator_precedence(:(=))  # (Note the necessary parens on `:(=)`)
(0, 1, 1)</code></pre><p>A symbol representing the operator associativity can also be found by calling the built-in function <code>Base.operator_associativity</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; Base.operator_associativity(:-), Base.operator_associativity(:+), Base.operator_associativity(:^)
(:left, :none, :right)

julia&gt; Base.operator_associativity(:⊗), Base.operator_associativity(:sin), Base.operator_associativity(:→)
(:left, :none, :right)</code></pre><p>Note that symbols such as <code>:sin</code> return precedence <code>0</code>. This value represents invalid operators and not operators of lowest precedence. Similarly, such operators are assigned associativity <code>:none</code>.</p><p><a href="integers-and-floating-point-numbers.html#man-numeric-literal-coefficients">Numeric literal coefficients</a>, e.g. <code>2x</code>, are treated as multiplications with higher precedence than any other binary operation, with the exception of <code>^</code> where they have higher precedence only as the exponent.</p><pre><code class="language-julia-repl hljs">julia&gt; x = 3; 2x^2
18

julia&gt; x = 3; 2^2x
64</code></pre><p>Juxtaposition parses like a unary operator, which has the same natural asymmetry around exponents: <code>-x^y</code> and <code>2x^y</code> parse as <code>-(x^y)</code> and <code>2(x^y)</code> whereas <code>x^-y</code> and <code>x^2y</code> parse as <code>x^(-y)</code> and <code>x^(2y)</code>.</p><h2 id="Numerical-Conversions"><a class="docs-heading-anchor" href="#Numerical-Conversions">Numerical Conversions</a><a id="Numerical-Conversions-1"></a><a class="docs-heading-anchor-permalink" href="#Numerical-Conversions" title="Permalink"></a></h2><p>Julia supports three forms of numerical conversion, which differ in their handling of inexact conversions.</p><ul><li><p>The notation <code>T(x)</code> or <code>convert(T, x)</code> converts <code>x</code> to a value of type <code>T</code>.</p><ul><li>If <code>T</code> is a floating-point type, the result is the nearest representable value, which could be positive or negative infinity.</li><li>If <code>T</code> is an integer type, an <code>InexactError</code> is raised if <code>x</code> is not representable by <code>T</code>.</li></ul></li><li><p><code>x % T</code> converts an integer <code>x</code> to a value of integer type <code>T</code> congruent to <code>x</code> modulo <code>2^n</code>, where <code>n</code> is the number of bits in <code>T</code>. In other words, the binary representation is truncated to fit.</p></li><li><p>The <a href="mathematical-operations.html#Rounding-functions">Rounding functions</a> take a type <code>T</code> as an optional argument. For example, <code>round(Int,x)</code> is a shorthand for <code>Int(round(x))</code>.</p></li></ul><p>The following examples show the different forms.</p><pre><code class="language-julia-repl hljs">julia&gt; Int8(127)
127

julia&gt; Int8(128)
ERROR: InexactError: trunc(Int8, 128)
Stacktrace:
[...]

julia&gt; Int8(127.0)
127

julia&gt; Int8(3.14)
ERROR: InexactError: Int8(3.14)
Stacktrace:
[...]

julia&gt; Int8(128.0)
ERROR: InexactError: Int8(128.0)
Stacktrace:
[...]

julia&gt; 127 % Int8
127

julia&gt; 128 % Int8
-128

julia&gt; round(Int8,127.4)
127

julia&gt; round(Int8,127.6)
ERROR: InexactError: Int8(128.0)
Stacktrace:
[...]</code></pre><p>See <a href="conversion-and-promotion.html#conversion-and-promotion">Conversion and Promotion</a> for how to define your own conversions and promotions.</p><h3 id="Rounding-functions"><a class="docs-heading-anchor" href="#Rounding-functions">Rounding functions</a><a id="Rounding-functions-1"></a><a class="docs-heading-anchor-permalink" href="#Rounding-functions" title="Permalink"></a></h3><table><tr><th style="text-align: left">Function</th><th style="text-align: left">Description</th><th style="text-align: left">Return type</th></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.round"><code>round(x)</code></a></td><td style="text-align: left">round <code>x</code> to the nearest integer</td><td style="text-align: left"><code>typeof(x)</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.round"><code>round(T, x)</code></a></td><td style="text-align: left">round <code>x</code> to the nearest integer</td><td style="text-align: left"><code>T</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.floor"><code>floor(x)</code></a></td><td style="text-align: left">round <code>x</code> towards <code>-Inf</code></td><td style="text-align: left"><code>typeof(x)</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.floor"><code>floor(T, x)</code></a></td><td style="text-align: left">round <code>x</code> towards <code>-Inf</code></td><td style="text-align: left"><code>T</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.ceil"><code>ceil(x)</code></a></td><td style="text-align: left">round <code>x</code> towards <code>+Inf</code></td><td style="text-align: left"><code>typeof(x)</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.ceil"><code>ceil(T, x)</code></a></td><td style="text-align: left">round <code>x</code> towards <code>+Inf</code></td><td style="text-align: left"><code>T</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.trunc"><code>trunc(x)</code></a></td><td style="text-align: left">round <code>x</code> towards zero</td><td style="text-align: left"><code>typeof(x)</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.trunc"><code>trunc(T, x)</code></a></td><td style="text-align: left">round <code>x</code> towards zero</td><td style="text-align: left"><code>T</code></td></tr></table><h3 id="Division-functions"><a class="docs-heading-anchor" href="#Division-functions">Division functions</a><a id="Division-functions-1"></a><a class="docs-heading-anchor-permalink" href="#Division-functions" title="Permalink"></a></h3><table><tr><th style="text-align: left">Function</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.div"><code>div(x, y)</code></a>, <code>x÷y</code></td><td style="text-align: left">truncated division; quotient rounded towards zero</td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.fld"><code>fld(x, y)</code></a></td><td style="text-align: left">floored division; quotient rounded towards <code>-Inf</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.cld"><code>cld(x, y)</code></a></td><td style="text-align: left">ceiling division; quotient rounded towards <code>+Inf</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.rem"><code>rem(x, y)</code></a>, <code>x%y</code></td><td style="text-align: left">remainder; satisfies <code>x == div(x, y)*y + rem(x, y)</code>; sign matches <code>x</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.mod"><code>mod(x, y)</code></a></td><td style="text-align: left">modulus; satisfies <code>x == fld(x, y)*y + mod(x, y)</code>; sign matches <code>y</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.mod1"><code>mod1(x, y)</code></a></td><td style="text-align: left"><code>mod</code> with offset 1; returns <code>r∈(0, y]</code> for <code>y&gt;0</code> or <code>r∈[y, 0)</code> for <code>y&lt;0</code>, where <code>mod(r, y) == mod(x, y)</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.Math.mod2pi"><code>mod2pi(x)</code></a></td><td style="text-align: left">modulus with respect to 2pi;  <code>0 &lt;= mod2pi(x) &lt; 2pi</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.divrem"><code>divrem(x, y)</code></a></td><td style="text-align: left">returns <code>(div(x, y),rem(x, y))</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.fldmod"><code>fldmod(x, y)</code></a></td><td style="text-align: left">returns <code>(fld(x, y), mod(x, y))</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.gcd"><code>gcd(x, y...)</code></a></td><td style="text-align: left">greatest positive common divisor of <code>x</code>, <code>y</code>,...</td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.lcm"><code>lcm(x, y...)</code></a></td><td style="text-align: left">least positive common multiple of <code>x</code>, <code>y</code>,...</td></tr></table><h3 id="Sign-and-absolute-value-functions"><a class="docs-heading-anchor" href="#Sign-and-absolute-value-functions">Sign and absolute value functions</a><a id="Sign-and-absolute-value-functions-1"></a><a class="docs-heading-anchor-permalink" href="#Sign-and-absolute-value-functions" title="Permalink"></a></h3><table><tr><th style="text-align: left">Function</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.abs"><code>abs(x)</code></a></td><td style="text-align: left">a positive value with the magnitude of <code>x</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.abs2"><code>abs2(x)</code></a></td><td style="text-align: left">the squared magnitude of <code>x</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.sign"><code>sign(x)</code></a></td><td style="text-align: left">indicates the sign of <code>x</code>, returning -1, 0, or +1</td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.signbit"><code>signbit(x)</code></a></td><td style="text-align: left">indicates whether the sign bit is on (true) or off (false)</td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.copysign"><code>copysign(x, y)</code></a></td><td style="text-align: left">a value with the magnitude of <code>x</code> and the sign of <code>y</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.flipsign"><code>flipsign(x, y)</code></a></td><td style="text-align: left">a value with the magnitude of <code>x</code> and the sign of <code>x*y</code></td></tr></table><h3 id="Powers,-logs-and-roots"><a class="docs-heading-anchor" href="#Powers,-logs-and-roots">Powers, logs and roots</a><a id="Powers,-logs-and-roots-1"></a><a class="docs-heading-anchor-permalink" href="#Powers,-logs-and-roots" title="Permalink"></a></h3><table><tr><th style="text-align: left">Function</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.sqrt-Tuple{Number}"><code>sqrt(x)</code></a>, <code>√x</code></td><td style="text-align: left">square root of <code>x</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.Math.cbrt-Tuple{AbstractFloat}"><code>cbrt(x)</code></a>, <code>∛x</code></td><td style="text-align: left">cube root of <code>x</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.Math.hypot"><code>hypot(x, y)</code></a></td><td style="text-align: left">hypotenuse of right-angled triangle with other sides of length <code>x</code> and <code>y</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.exp-Tuple{Float64}"><code>exp(x)</code></a></td><td style="text-align: left">natural exponential function at <code>x</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.expm1"><code>expm1(x)</code></a></td><td style="text-align: left">accurate <code>exp(x) - 1</code> for <code>x</code> near zero</td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.Math.ldexp"><code>ldexp(x, n)</code></a></td><td style="text-align: left"><code>x * 2^n</code> computed efficiently for integer values of <code>n</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.log-Tuple{Number}"><code>log(x)</code></a></td><td style="text-align: left">natural logarithm of <code>x</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.log-Tuple{Number}"><code>log(b, x)</code></a></td><td style="text-align: left">base <code>b</code> logarithm of <code>x</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.log2"><code>log2(x)</code></a></td><td style="text-align: left">base 2 logarithm of <code>x</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.log10"><code>log10(x)</code></a></td><td style="text-align: left">base 10 logarithm of <code>x</code></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.log1p"><code>log1p(x)</code></a></td><td style="text-align: left">accurate <code>log(1 + x)</code> for <code>x</code> near zero</td></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Base.Math.exponent"><code>exponent(x)</code></a></td><td style="text-align: left">binary exponent of <code>x</code></td></tr><tr><td style="text-align: left"><a href="../base/numbers.html#Base.Math.significand"><code>significand(x)</code></a></td><td style="text-align: left">binary significand (a.k.a. mantissa) of a floating-point number <code>x</code></td></tr></table><p>For an overview of why functions like <a href="../base/math.html#Base.Math.hypot"><code>hypot</code></a>, <a href="../base/math.html#Base.expm1"><code>expm1</code></a>, and <a href="../base/math.html#Base.log1p"><code>log1p</code></a> are necessary and useful, see John D. Cook&#39;s excellent pair of blog posts on the subject: <a href="https://www.johndcook.com/blog/2010/06/07/math-library-functions-that-seem-unnecessary/">expm1, log1p, erfc</a>, and <a href="https://www.johndcook.com/blog/2010/06/02/whats-so-hard-about-finding-a-hypotenuse/">hypot</a>.</p><h3 id="Trigonometric-and-hyperbolic-functions"><a class="docs-heading-anchor" href="#Trigonometric-and-hyperbolic-functions">Trigonometric and hyperbolic functions</a><a id="Trigonometric-and-hyperbolic-functions-1"></a><a class="docs-heading-anchor-permalink" href="#Trigonometric-and-hyperbolic-functions" title="Permalink"></a></h3><p>All the standard trigonometric and hyperbolic functions are also defined:</p><pre><code class="nohighlight hljs">sin    cos    tan    cot    sec    csc
sinh   cosh   tanh   coth   sech   csch
asin   acos   atan   acot   asec   acsc
asinh  acosh  atanh  acoth  asech  acsch
sinc   cosc</code></pre><p>These are all single-argument functions, with <a href="../base/math.html#Base.atan-Tuple{Number}"><code>atan</code></a> also accepting two arguments corresponding to a traditional <a href="https://en.wikipedia.org/wiki/Atan2"><code>atan2</code></a> function.</p><p>Additionally, <a href="../base/math.html#Base.Math.sinpi"><code>sinpi(x)</code></a> and <a href="../base/math.html#Base.Math.cospi"><code>cospi(x)</code></a> are provided for more accurate computations of <a href="../base/math.html#Base.sin-Tuple{Number}"><code>sin(pi * x)</code></a> and <a href="../base/math.html#Base.cos-Tuple{Number}"><code>cos(pi * x)</code></a> respectively.</p><p>In order to compute trigonometric functions with degrees instead of radians, suffix the function with <code>d</code>. For example, <a href="../base/math.html#Base.Math.sind"><code>sind(x)</code></a> computes the sine of <code>x</code> where <code>x</code> is specified in degrees. The complete list of trigonometric functions with degree variants is:</p><pre><code class="nohighlight hljs">sind   cosd   tand   cotd   secd   cscd
asind  acosd  atand  acotd  asecd  acscd</code></pre><h3 id="Special-functions"><a class="docs-heading-anchor" href="#Special-functions">Special functions</a><a id="Special-functions-1"></a><a class="docs-heading-anchor-permalink" href="#Special-functions" title="Permalink"></a></h3><p>Many other special mathematical functions are provided by the package <a href="https://github.com/JuliaMath/SpecialFunctions.jl">SpecialFunctions.jl</a>.</p><section class="footnotes is-size-7"><ul><li class="footnote" id="footnote-1"><a class="tag is-link" href="#citeref-1">1</a>The unary operators <code>+</code> and <code>-</code> require explicit parentheses around their argument to disambiguate them from the operator <code>++</code>, etc. Other compositions of unary operators are parsed with right-associativity, e. g., <code>√√-a</code> as <code>√(√(-a))</code>.</li><li class="footnote" id="footnote-2"><a class="tag is-link" href="#citeref-2">2</a>The operators <code>+</code>, <code>++</code> and <code>*</code> are non-associative. <code>a + b + c</code> is parsed as <code>+(a, b, c)</code> not <code>+(+(a, b), c)</code>. However, the fallback methods for <code>+(a, b, c, d...)</code> and <code>*(a, b, c, d...)</code> both default to left-associative evaluation.</li></ul></section></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="integers-and-floating-point-numbers.html">« Integers and Floating-Point Numbers</a><a class="docs-footer-nextpage" href="complex-and-rational-numbers.html">Complex and Rational Numbers »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
