<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Initialization of the Julia runtime · The Julia Language</title><meta name="title" content="Initialization of the Julia runtime · The Julia Language"/><meta property="og:title" content="Initialization of the Julia runtime · The Julia Language"/><meta property="twitter:title" content="Initialization of the Julia runtime · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox" checked/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li class="is-active"><a class="tocitem" href="init.html">Initialization of the Julia runtime</a><ul class="internal"><li><a class="tocitem" href="#main()"><span><code>main()</code></span></a></li><li><a class="tocitem" href="#julia_init()"><span><code>julia_init()</code></span></a></li><li><a class="tocitem" href="#repl_entrypoint()"><span><code>repl_entrypoint()</code></span></a></li><li><a class="tocitem" href="#Base._start"><span><code>Base._start</code></span></a></li><li><a class="tocitem" href="#Core.eval"><span><code>Core.eval</code></span></a></li><li><a class="tocitem" href="#jl_atexit_hook()"><span><code>jl_atexit_hook()</code></span></a></li><li><a class="tocitem" href="#julia_save()"><span><code>julia_save()</code></span></a></li></ul></li><li><a class="tocitem" href="ast.html">Julia ASTs</a></li><li><a class="tocitem" href="types.html">More about types</a></li><li><a class="tocitem" href="object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="functions.html">Julia Functions</a></li><li><a class="tocitem" href="cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="subarrays.html">SubArrays</a></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Documentation of Julia&#39;s Internals</a></li><li class="is-active"><a href="init.html">Initialization of the Julia runtime</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="init.html">Initialization of the Julia runtime</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/init.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Initialization-of-the-Julia-runtime"><a class="docs-heading-anchor" href="#Initialization-of-the-Julia-runtime">Initialization of the Julia runtime</a><a id="Initialization-of-the-Julia-runtime-1"></a><a class="docs-heading-anchor-permalink" href="#Initialization-of-the-Julia-runtime" title="Permalink"></a></h1><p>How does the Julia runtime execute <code>julia -e &#39;println(&quot;Hello World!&quot;)&#39;</code> ?</p><h2 id="main()"><a class="docs-heading-anchor" href="#main()"><code>main()</code></a><a id="main()-1"></a><a class="docs-heading-anchor-permalink" href="#main()" title="Permalink"></a></h2><p>Execution starts at <a href="https://github.com/JuliaLang/julia/blob/master/cli/loader_exe.c"><code>main()</code> in <code>cli/loader_exe.c</code></a>, which calls <code>jl_load_repl()</code> in <a href="https://github.com/JuliaLang/julia/blob/master/cli/loader_lib.c"><code>cli/loader_lib.c</code></a> which loads a few libraries, eventually calling <a href="https://github.com/JuliaLang/julia/blob/master/src/jlapi.c"><code>jl_repl_entrypoint()</code> in <code>src/jlapi.c</code></a>.</p><p><code>jl_repl_entrypoint()</code> calls <a href="https://github.com/JuliaLang/julia/blob/master/src/support/libsupportinit.c"><code>libsupport_init()</code></a> to set the C library locale and to initialize the &quot;ios&quot; library (see <a href="https://github.com/JuliaLang/julia/blob/master/src/support/ios.c"><code>ios_init_stdstreams()</code></a> and <a href="stdio.html#Legacy-ios.c-library">Legacy <code>ios.c</code> library</a>).</p><p>Next <a href="https://github.com/JuliaLang/julia/blob/master/src/jloptions.c"><code>jl_parse_opts()</code></a> is called to process command line options. Note that <code>jl_parse_opts()</code> only deals with options that affect code generation or early initialization. Other options are handled later by <a href="https://github.com/JuliaLang/julia/blob/master/base/client.jl"><code>exec_options()</code> in <code>base/client.jl</code></a>.</p><p><code>jl_parse_opts()</code> stores command line options in the <a href="https://github.com/JuliaLang/julia/blob/master/src/julia.h">global <code>jl_options</code> struct</a>.</p><h2 id="julia_init()"><a class="docs-heading-anchor" href="#julia_init()"><code>julia_init()</code></a><a id="julia_init()-1"></a><a class="docs-heading-anchor-permalink" href="#julia_init()" title="Permalink"></a></h2><p><a href="https://github.com/JuliaLang/julia/blob/master/src/init.c"><code>julia_init()</code> in <code>init.c</code></a> is called by <code>main()</code> and calls <a href="https://github.com/JuliaLang/julia/blob/master/src/init.c"><code>_julia_init()</code> in <code>init.c</code></a>.</p><p><code>_julia_init()</code> begins by calling <code>libsupport_init()</code> again (it does nothing the second time).</p><p><a href="https://github.com/JuliaLang/julia/blob/master/src/signals-unix.c"><code>restore_signals()</code></a> is called to zero the signal handler mask.</p><p><a href="https://github.com/JuliaLang/julia/blob/master/src/init.c"><code>jl_resolve_sysimg_location()</code></a> searches configured paths for the base system image. See <a href="sysimg.html#Building-the-Julia-system-image">Building the Julia system image</a>.</p><p><a href="https://github.com/JuliaLang/julia/blob/master/src/gc.c"><code>jl_gc_init()</code></a> sets up allocation pools and lists for weak refs, preserved values and finalization.</p><p><a href="https://github.com/JuliaLang/julia/blob/master/src/ast.c"><code>jl_init_frontend()</code></a> loads and initializes a pre-compiled femtolisp image containing the scanner/parser.</p><p><a href="https://github.com/JuliaLang/julia/blob/master/src/jltypes.c"><code>jl_init_types()</code></a> creates <code>jl_datatype_t</code> type description objects for the <a href="https://github.com/JuliaLang/julia/blob/master/src/julia.h">built-in types defined in <code>julia.h</code></a>. e.g.</p><pre><code class="language-c hljs">jl_any_type = jl_new_abstracttype(jl_symbol(&quot;Any&quot;), core, NULL, jl_emptysvec);
jl_any_type-&gt;super = jl_any_type;

jl_type_type = jl_new_abstracttype(jl_symbol(&quot;Type&quot;), core, jl_any_type, jl_emptysvec);

jl_int32_type = jl_new_primitivetype(jl_symbol(&quot;Int32&quot;), core,
                                     jl_any_type, jl_emptysvec, 32);</code></pre><p><a href="https://github.com/JuliaLang/julia/blob/master/src/task.c"><code>jl_init_tasks()</code></a> creates the <code>jl_datatype_t* jl_task_type</code> object; initializes the global <code>jl_root_task</code> struct; and sets <code>jl_current_task</code> to the root task.</p><p><a href="https://github.com/JuliaLang/julia/blob/master/src/codegen.cpp"><code>jl_init_codegen()</code></a> initializes the <a href="https://llvm.org">LLVM library</a>.</p><p><a href="https://github.com/JuliaLang/julia/blob/master/src/staticdata.c"><code>jl_init_serializer()</code></a> initializes 8-bit serialization tags for builtin <code>jl_value_t</code> values.</p><p>If there is no sysimg file (<code>!jl_options.image_file</code>) then the <code>Core</code> and <code>Main</code> modules are created and <code>boot.jl</code> is evaluated:</p><p><code>jl_core_module = jl_new_module(jl_symbol(&quot;Core&quot;))</code> creates the Julia <code>Core</code> module.</p><p><a href="https://github.com/JuliaLang/julia/blob/master/src/intrinsics.cpp"><code>jl_init_intrinsic_functions()</code></a> creates a new Julia module <code>Intrinsics</code> containing constant <code>jl_intrinsic_type</code> symbols. These define an integer code for each <a href="https://github.com/JuliaLang/julia/blob/master/src/intrinsics.cpp">intrinsic function</a>. <a href="https://github.com/JuliaLang/julia/blob/master/src/intrinsics.cpp"><code>emit_intrinsic()</code></a> translates these symbols into LLVM instructions during code generation.</p><p><a href="https://github.com/JuliaLang/julia/blob/master/src/builtins.c"><code>jl_init_primitives()</code></a> hooks C functions up to Julia function symbols. e.g. the symbol <code>Core.:(===)()</code> is bound to C function pointer <code>jl_f_is()</code> by calling <code>add_builtin_func(&quot;===&quot;, jl_f_is)</code>.</p><p><a href="https://github.com/JuliaLang/julia/blob/master/src/toplevel.c"><code>jl_new_main_module()</code></a> creates the global &quot;Main&quot; module and sets <code>jl_current_task-&gt;current_module = jl_main_module</code>.</p><p>Note: <code>_julia_init()</code> <a href="https://github.com/JuliaLang/julia/blob/master/src/init.c">then sets</a> <code>jl_root_task-&gt;current_module = jl_core_module</code>. <code>jl_root_task</code> is an alias of <code>jl_current_task</code> at this point, so the <code>current_module</code> set by <code>jl_new_main_module()</code> above is overwritten.</p><p><a href="https://github.com/JuliaLang/julia/blob/master/src/init.c"><code>jl_load(&quot;boot.jl&quot;, sizeof(&quot;boot.jl&quot;))</code></a> calls <a href="https://github.com/JuliaLang/julia/blob/master/src/ast.c"><code>jl_parse_eval_all</code></a> which repeatedly calls <a href="https://github.com/JuliaLang/julia/blob/master/src/toplevel.c"><code>jl_toplevel_eval_flex()</code></a> to execute <a href="https://github.com/JuliaLang/julia/blob/master/base/boot.jl"><code>boot.jl</code></a>. &lt;!– TODO – drill down into eval? –&gt;</p><p><a href="https://github.com/JuliaLang/julia/blob/master/src/init.c"><code>jl_get_builtin_hooks()</code></a> initializes global C pointers to Julia globals defined in <code>boot.jl</code>.</p><p><a href="https://github.com/JuliaLang/julia/blob/master/src/datatype.c"><code>jl_init_box_caches()</code></a> pre-allocates global boxed integer value objects for values up to 1024. This speeds up allocation of boxed ints later on. e.g.:</p><pre><code class="language-c hljs">jl_value_t *jl_box_uint8(uint32_t x)
{
    return boxed_uint8_cache[(uint8_t)x];
}</code></pre><p><a href="https://github.com/JuliaLang/julia/blob/master/src/init.c"><code>_julia_init()</code> iterates</a> over the <code>jl_core_module-&gt;bindings.table</code> looking for <code>jl_datatype_t</code> values and sets the type name&#39;s module prefix to <code>jl_core_module</code>.</p><p><a href="https://github.com/JuliaLang/julia/blob/master/src/toplevel.c"><code>jl_add_standard_imports(jl_main_module)</code></a> does &quot;using Base&quot; in the &quot;Main&quot; module.</p><p>Note: <code>_julia_init()</code> now reverts to <code>jl_root_task-&gt;current_module = jl_main_module</code> as it was before being set to <code>jl_core_module</code> above.</p><p>Platform specific signal handlers are initialized for <code>SIGSEGV</code> (OSX, Linux), and <code>SIGFPE</code> (Windows).</p><p>Other signals (<code>SIGINFO, SIGBUS, SIGILL, SIGTERM, SIGABRT, SIGQUIT, SIGSYS</code> and <code>SIGPIPE</code>) are hooked up to <a href="https://github.com/JuliaLang/julia/blob/master/src/signals-unix.c"><code>sigdie_handler()</code></a> which prints a backtrace.</p><p><a href="https://github.com/JuliaLang/julia/blob/master/src/staticdata.c"><code>jl_init_restored_module()</code></a> calls <a href="https://github.com/JuliaLang/julia/blob/master/src/module.c"><code>jl_module_run_initializer()</code></a> for each deserialized module to run the <code>__init__()</code> function.</p><p>Finally <a href="https://github.com/JuliaLang/julia/blob/master/src/signals-unix.c"><code>sigint_handler()</code></a> is hooked up to <code>SIGINT</code> and calls <code>jl_throw(jl_interrupt_exception)</code>.</p><p><code>_julia_init()</code> then returns <a href="https://github.com/JuliaLang/julia/blob/master/cli/loader_exe.c">back to <code>main()</code> in <code>cli/loader_exe.c</code></a> and <code>main()</code> calls <code>repl_entrypoint(argc, (char**)argv)</code>.</p><div class="admonition is-category-sidebar"><header class="admonition-header">sysimg</header><div class="admonition-body"><p>If there is a sysimg file, it contains a pre-cooked image of the <code>Core</code> and <code>Main</code> modules (and whatever else is created by <code>boot.jl</code>). See <a href="sysimg.html#Building-the-Julia-system-image">Building the Julia system image</a>.</p><p><a href="https://github.com/JuliaLang/julia/blob/master/src/staticdata.c"><code>jl_restore_system_image()</code></a> deserializes the saved sysimg into the current Julia runtime environment and initialization continues after <code>jl_init_box_caches()</code> below...</p><p>Note: <a href="https://github.com/JuliaLang/julia/blob/master/src/staticdata.c"><code>jl_restore_system_image()</code> (and <code>staticdata.c</code> in general)</a> uses the <a href="stdio.html#Legacy-ios.c-library">Legacy <code>ios.c</code> library</a>.</p></div></div><h2 id="repl_entrypoint()"><a class="docs-heading-anchor" href="#repl_entrypoint()"><code>repl_entrypoint()</code></a><a id="repl_entrypoint()-1"></a><a class="docs-heading-anchor-permalink" href="#repl_entrypoint()" title="Permalink"></a></h2><p><a href="https://github.com/JuliaLang/julia/blob/master/src/jlapi.c"><code>repl_entrypoint()</code></a> loads the contents of <code>argv[]</code> into <a href="../base/constants.html#Base.ARGS"><code>Base.ARGS</code></a>.</p><p>If a <code>.jl</code> &quot;program&quot; file was supplied on the command line, then <a href="https://github.com/JuliaLang/julia/blob/master/src/jlapi.c"><code>exec_program()</code></a> calls <a href="https://github.com/JuliaLang/julia/blob/master/src/toplevel.c"><code>jl_load(program,len)</code></a> which calls <a href="https://github.com/JuliaLang/julia/blob/master/src/ast.c"><code>jl_parse_eval_all</code></a> which repeatedly calls <a href="https://github.com/JuliaLang/julia/blob/master/src/toplevel.c"><code>jl_toplevel_eval_flex()</code></a> to execute the program.</p><p>However, in our example (<code>julia -e &#39;println(&quot;Hello World!&quot;)&#39;</code>), <a href="https://github.com/JuliaLang/julia/blob/master/src/module.c"><code>jl_get_global(jl_base_module, jl_symbol(&quot;_start&quot;))</code></a> looks up <a href="https://github.com/JuliaLang/julia/blob/master/base/client.jl"><code>Base._start</code></a> and <a href="https://github.com/JuliaLang/julia/blob/master/src/julia.h"><code>jl_apply()</code></a> executes it.</p><h2 id="Base._start"><a class="docs-heading-anchor" href="#Base._start"><code>Base._start</code></a><a id="Base._start-1"></a><a class="docs-heading-anchor-permalink" href="#Base._start" title="Permalink"></a></h2><p><a href="https://github.com/JuliaLang/julia/blob/master/base/client.jl"><code>Base._start</code></a> calls <a href="https://github.com/JuliaLang/julia/blob/master/base/client.jl"><code>Base.exec_options</code></a> which calls <a href="https://github.com/JuliaLang/julia/blob/master/src/ast.c"><code>jl_parse_input_line(&quot;println(&quot;Hello World!&quot;)&quot;)</code></a> to create an expression object and <a href="init.html#Core.eval"><code>Core.eval(Main, ex)</code></a> to execute the parsed expression <code>ex</code> in the module context of <code>Main</code>.</p><h2 id="Core.eval"><a class="docs-heading-anchor" href="#Core.eval"><code>Core.eval</code></a><a id="Core.eval-1"></a><a class="docs-heading-anchor-permalink" href="#Core.eval" title="Permalink"></a></h2><p><a href="init.html#Core.eval"><code>Core.eval(Main, ex)</code></a> calls <a href="https://github.com/JuliaLang/julia/blob/master/src/toplevel.c"><code>jl_toplevel_eval_in(m, ex)</code></a>, which calls <a href="https://github.com/JuliaLang/julia/blob/master/src/toplevel.c"><code>jl_toplevel_eval_flex</code></a>. <code>jl_toplevel_eval_flex</code> implements a simple heuristic to decide whether to compile a given code thunk or run it by interpreter. When given <code>println(&quot;Hello World!&quot;)</code>, it would usually decide to run the code by interpreter, in which case it calls <a href="https://github.com/JuliaLang/julia/blob/master/src/interpreter.c"><code>jl_interpret_toplevel_thunk</code></a>, which then calls <a href="https://github.com/JuliaLang/julia/blob/master/src/interpreter.c"><code>eval_body</code></a>.</p><p>The stack dump below shows how the interpreter works its way through various methods of <a href="../base/io-network.html#Base.println"><code>Base.println()</code></a> and <a href="../base/io-network.html#Base.print"><code>Base.print()</code></a> before arriving at <a href="https://github.com/JuliaLang/julia/blob/master/base/stream.jl"><code>write(s::IO, a::Array{T}) where T</code></a>  which does <code>ccall(jl_uv_write())</code>.</p><p><a href="https://github.com/JuliaLang/julia/blob/master/src/jl_uv.c"><code>jl_uv_write()</code></a> calls <code>uv_write()</code> to write &quot;Hello World!&quot; to <code>JL_STDOUT</code>. See <a href="stdio.html#Libuv-wrappers-for-stdio">Libuv wrappers for stdio</a>.:</p><pre><code class="nohighlight hljs">Hello World!</code></pre><table><tr><th style="text-align: left">Stack frame</th><th style="text-align: left">Source code</th><th style="text-align: left">Notes</th></tr><tr><td style="text-align: left"><code>jl_uv_write()</code></td><td style="text-align: left"><code>jl_uv.c</code></td><td style="text-align: left">called though <a href="../base/c.html#ccall"><code>ccall</code></a></td></tr><tr><td style="text-align: left"><code>julia_write_282942</code></td><td style="text-align: left"><code>stream.jl</code></td><td style="text-align: left">function <code>write!(s::IO, a::Array{T}) where T</code></td></tr><tr><td style="text-align: left"><code>julia_print_284639</code></td><td style="text-align: left"><code>ascii.jl</code></td><td style="text-align: left"><code>print(io::IO, s::String) = (write(io, s); nothing)</code></td></tr><tr><td style="text-align: left"><code>jlcall_print_284639</code></td><td style="text-align: left"></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>jl_apply()</code></td><td style="text-align: left"><code>julia.h</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>jl_trampoline()</code></td><td style="text-align: left"><code>builtins.c</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>jl_apply()</code></td><td style="text-align: left"><code>julia.h</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>jl_apply_generic()</code></td><td style="text-align: left"><code>gf.c</code></td><td style="text-align: left"><code>Base.print(Base.TTY, String)</code></td></tr><tr><td style="text-align: left"><code>jl_apply()</code></td><td style="text-align: left"><code>julia.h</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>jl_trampoline()</code></td><td style="text-align: left"><code>builtins.c</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>jl_apply()</code></td><td style="text-align: left"><code>julia.h</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>jl_apply_generic()</code></td><td style="text-align: left"><code>gf.c</code></td><td style="text-align: left"><code>Base.print(Base.TTY, String, Char, Char...)</code></td></tr><tr><td style="text-align: left"><code>jl_apply()</code></td><td style="text-align: left"><code>julia.h</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>jl_f_apply()</code></td><td style="text-align: left"><code>builtins.c</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>jl_apply()</code></td><td style="text-align: left"><code>julia.h</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>jl_trampoline()</code></td><td style="text-align: left"><code>builtins.c</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>jl_apply()</code></td><td style="text-align: left"><code>julia.h</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>jl_apply_generic()</code></td><td style="text-align: left"><code>gf.c</code></td><td style="text-align: left"><code>Base.println(Base.TTY, String, String...)</code></td></tr><tr><td style="text-align: left"><code>jl_apply()</code></td><td style="text-align: left"><code>julia.h</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>jl_trampoline()</code></td><td style="text-align: left"><code>builtins.c</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>jl_apply()</code></td><td style="text-align: left"><code>julia.h</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>jl_apply_generic()</code></td><td style="text-align: left"><code>gf.c</code></td><td style="text-align: left"><code>Base.println(String,)</code></td></tr><tr><td style="text-align: left"><code>jl_apply()</code></td><td style="text-align: left"><code>julia.h</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>do_call()</code></td><td style="text-align: left"><code>interpreter.c</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>eval_body()</code></td><td style="text-align: left"><code>interpreter.c</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>jl_interpret_toplevel_thunk</code></td><td style="text-align: left"><code>interpreter.c</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>jl_toplevel_eval_flex</code></td><td style="text-align: left"><code>toplevel.c</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>jl_toplevel_eval_in</code></td><td style="text-align: left"><code>toplevel.c</code></td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><code>Core.eval</code></td><td style="text-align: left"><code>boot.jl</code></td><td style="text-align: left"></td></tr></table><p>Since our example has just one function call, which has done its job of printing &quot;Hello World!&quot;, the stack now rapidly unwinds back to <code>main()</code>.</p><h2 id="jl_atexit_hook()"><a class="docs-heading-anchor" href="#jl_atexit_hook()"><code>jl_atexit_hook()</code></a><a id="jl_atexit_hook()-1"></a><a class="docs-heading-anchor-permalink" href="#jl_atexit_hook()" title="Permalink"></a></h2><p><code>main()</code> calls <a href="https://github.com/JuliaLang/julia/blob/master/src/init.c"><code>jl_atexit_hook()</code></a>. This calls <code>Base._atexit</code>, then calls <a href="https://github.com/JuliaLang/julia/blob/master/src/gc.c"><code>jl_gc_run_all_finalizers()</code></a> and cleans up libuv handles.</p><h2 id="julia_save()"><a class="docs-heading-anchor" href="#julia_save()"><code>julia_save()</code></a><a id="julia_save()-1"></a><a class="docs-heading-anchor-permalink" href="#julia_save()" title="Permalink"></a></h2><p>Finally, <code>main()</code> calls <a href="https://github.com/JuliaLang/julia/blob/master/src/init.c"><code>julia_save()</code></a>, which if requested on the command line, saves the runtime state to a new system image. See <a href="https://github.com/JuliaLang/julia/blob/master/src/gf.c"><code>jl_compile_all()</code></a> and <a href="https://github.com/JuliaLang/julia/blob/master/src/staticdata.c"><code>jl_save_system_image()</code></a>.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="../stdlib/Unicode.html">« Unicode</a><a class="docs-footer-nextpage" href="ast.html">Julia ASTs »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
