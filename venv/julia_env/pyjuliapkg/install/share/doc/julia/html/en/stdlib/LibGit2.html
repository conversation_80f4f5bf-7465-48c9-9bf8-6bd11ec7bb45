<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>LibGit2 · The Julia Language</title><meta name="title" content="LibGit2 · The Julia Language"/><meta property="og:title" content="LibGit2 · The Julia Language"/><meta property="twitter:title" content="LibGit2 · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li class="is-active"><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="LibGit2.html">LibGit2</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="LibGit2.html">LibGit2</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/stdlib/LibGit2/docs/src/index.md" title="View source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="LibGit2"><a class="docs-heading-anchor" href="#LibGit2">LibGit2</a><a id="LibGit2-1"></a><a class="docs-heading-anchor-permalink" href="#LibGit2" title="Permalink"></a></h1><p>The LibGit2 module provides bindings to <a href="https://libgit2.org/">libgit2</a>, a portable C library that implements core functionality for the <a href="https://git-scm.com/">Git</a> version control system. These bindings are currently used to power Julia&#39;s package manager. It is expected that this module will eventually be moved into a separate package.</p><h3 id="Functionality"><a class="docs-heading-anchor" href="#Functionality">Functionality</a><a id="Functionality-1"></a><a class="docs-heading-anchor-permalink" href="#Functionality" title="Permalink"></a></h3><p>Some of this documentation assumes some prior knowledge of the libgit2 API. For more information on some of the objects and methods referenced here, consult the upstream <a href="https://libgit2.org/libgit2/#v1.0.0">libgit2 API reference</a>.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.Buffer" href="#LibGit2.Buffer"><code>LibGit2.Buffer</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.Buffer</code></pre><p>A data buffer for exporting data from libgit2. Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_buf"><code>git_buf</code></a> struct.</p><p>When fetching data from LibGit2, a typical usage would look like:</p><pre><code class="language-julia hljs">buf_ref = Ref(Buffer())
@check ccall(..., (Ptr{Buffer},), buf_ref)
# operation on buf_ref
free(buf_ref)</code></pre><p>In particular, note that <code>LibGit2.free</code> should be called afterward on the <code>Ref</code> object.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L105-L119">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.CheckoutOptions" href="#LibGit2.CheckoutOptions"><code>LibGit2.CheckoutOptions</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.CheckoutOptions</code></pre><p>Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_checkout_options"><code>git_checkout_options</code></a> struct.</p><p>The fields represent:</p><ul><li><code>version</code>: version of the struct in use, in case this changes later. For now, always <code>1</code>.</li><li><code>checkout_strategy</code>: determine how to handle conflicts and whether to force the  checkout/recreate missing files.</li><li><code>disable_filters</code>: if nonzero, do not apply filters like CLRF (to convert file newlines between UNIX and DOS).</li><li><code>dir_mode</code>: read/write/access mode for any directories involved in the checkout. Default is <code>0755</code>.</li><li><code>file_mode</code>: read/write/access mode for any files involved in the checkout.  Default is <code>0755</code> or <code>0644</code>, depending on the blob.</li><li><code>file_open_flags</code>: bitflags used to open any files during the checkout.</li><li><code>notify_flags</code>: Flags for what sort of conflicts the user should be notified about.</li><li><code>notify_cb</code>: An optional callback function to notify the user if a checkout conflict occurs.  If this function returns a non-zero value, the checkout will be cancelled.</li><li><code>notify_payload</code>: Payload for the notify callback function.</li><li><code>progress_cb</code>: An optional callback function to display checkout progress.</li><li><code>progress_payload</code>: Payload for the progress callback.</li><li><code>paths</code>: If not empty, describes which paths to search during the checkout.  If empty, the checkout will occur over all files in the repository.</li><li><code>baseline</code>: Expected content of the <a href="LibGit2.html#LibGit2.workdir"><code>workdir</code></a>, captured in a (pointer to a)  <a href="LibGit2.html#LibGit2.GitTree"><code>GitTree</code></a>. Defaults to the state of the tree at HEAD.</li><li><code>baseline_index</code>: Expected content of the <a href="LibGit2.html#LibGit2.workdir"><code>workdir</code></a>, captured in a (pointer to a)  <code>GitIndex</code>. Defaults to the state of the index at HEAD.</li><li><code>target_directory</code>: If not empty, checkout to this directory instead of the <code>workdir</code>.</li><li><code>ancestor_label</code>: In case of conflicts, the name of the common ancestor side.</li><li><code>our_label</code>: In case of conflicts, the name of &quot;our&quot; side.</li><li><code>their_label</code>: In case of conflicts, the name of &quot;their&quot; side.</li><li><code>perfdata_cb</code>: An optional callback function to display performance data.</li><li><code>perfdata_payload</code>: Payload for the performance callback.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L132-L164">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.CloneOptions" href="#LibGit2.CloneOptions"><code>LibGit2.CloneOptions</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.CloneOptions</code></pre><p>Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_clone_options"><code>git_clone_options</code></a> struct.</p><p>The fields represent:</p><ul><li><code>version</code>: version of the struct in use, in case this changes later. For now, always <code>1</code>.</li><li><code>checkout_opts</code>: The options for performing the checkout of the remote as part of the clone.</li><li><code>fetch_opts</code>: The options for performing the pre-checkout fetch of the remote as part of the clone.</li><li><code>bare</code>: If <code>0</code>, clone the full remote repository. If non-zero, perform a bare clone, in which  there is no local copy of the source files in the repository and the <a href="LibGit2.html#LibGit2.gitdir"><code>gitdir</code></a> and <a href="LibGit2.html#LibGit2.workdir"><code>workdir</code></a>  are the same.</li><li><code>localclone</code>: Flag whether to clone a local object database or do a fetch. The default is to let git decide.  It will not use the git-aware transport for a local clone, but will use it for URLs which begin with <code>file://</code>.</li><li><code>checkout_branch</code>: The name of the branch to checkout. If an empty string, the default branch of the  remote will be checked out.</li><li><code>repository_cb</code>: An optional callback which will be used to create the <em>new</em> repository into which  the clone is made.</li><li><code>repository_cb_payload</code>: The payload for the repository callback.</li><li><code>remote_cb</code>: An optional callback used to create the <a href="LibGit2.html#LibGit2.GitRemote"><code>GitRemote</code></a> before making the clone from it.</li><li><code>remote_cb_payload</code>: The payload for the remote callback.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L362-L383">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.DescribeOptions" href="#LibGit2.DescribeOptions"><code>LibGit2.DescribeOptions</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.DescribeOptions</code></pre><p>Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_describe_options"><code>git_describe_options</code></a> struct.</p><p>The fields represent:</p><ul><li><code>version</code>: version of the struct in use, in case this changes later. For now, always <code>1</code>.</li><li><code>max_candidates_tags</code>: consider this many most recent tags in <code>refs/tags</code> to describe a commit.  Defaults to 10 (so that the 10 most recent tags would be examined to see if they describe a commit).</li><li><code>describe_strategy</code>: whether to consider all entries in <code>refs/tags</code> (equivalent to <code>git-describe --tags</code>)  or all entries in <code>refs/</code> (equivalent to <code>git-describe --all</code>). The default is to only show annotated tags.  If <code>Consts.DESCRIBE_TAGS</code> is passed, all tags, annotated or not, will be considered.  If <code>Consts.DESCRIBE_ALL</code> is passed, any ref in <code>refs/</code> will be considered.</li><li><code>pattern</code>: only consider tags which match <code>pattern</code>. Supports glob expansion.</li><li><code>only_follow_first_parent</code>: when finding the distance from a matching reference to the described  object, only consider the distance from the first parent.</li><li><code>show_commit_oid_as_fallback</code>: if no matching reference can be found which describes a commit, show the  commit&#39;s <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> instead of throwing an error (the default behavior).</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L455-L473">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.DescribeFormatOptions" href="#LibGit2.DescribeFormatOptions"><code>LibGit2.DescribeFormatOptions</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.DescribeFormatOptions</code></pre><p>Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_describe_format_options"><code>git_describe_format_options</code></a> struct.</p><p>The fields represent:</p><ul><li><code>version</code>: version of the struct in use, in case this changes later. For now, always <code>1</code>.</li><li><code>abbreviated_size</code>: lower bound on the size of the abbreviated <code>GitHash</code> to use, defaulting to <code>7</code>.</li><li><code>always_use_long_format</code>: set to <code>1</code> to use the long format for strings even if a short format can be used.</li><li><code>dirty_suffix</code>: if set, this will be appended to the end of the description string if the <a href="LibGit2.html#LibGit2.workdir"><code>workdir</code></a> is dirty.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L485-L495">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.DiffDelta" href="#LibGit2.DiffDelta"><code>LibGit2.DiffDelta</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.DiffDelta</code></pre><p>Description of changes to one entry. Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_diff_delta"><code>git_diff_delta</code></a> struct.</p><p>The fields represent:</p><ul><li><code>status</code>: One of <code>Consts.DELTA_STATUS</code>, indicating whether the file has been added/modified/deleted.</li><li><code>flags</code>: Flags for the delta and the objects on each side. Determines whether to treat the file(s)  as binary/text, whether they exist on each side of the diff, and whether the object ids are known  to be correct.</li><li><code>similarity</code>: Used to indicate if a file has been renamed or copied.</li><li><code>nfiles</code>: The number of files in the delta (for instance, if the delta  was run on a submodule commit id, it may contain more than one file).</li><li><code>old_file</code>: A <a href="LibGit2.html#LibGit2.DiffFile"><code>DiffFile</code></a> containing information about the file(s) before the changes.</li><li><code>new_file</code>: A <a href="LibGit2.html#LibGit2.DiffFile"><code>DiffFile</code></a> containing information about the file(s) after the changes.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L540-L556">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.DiffFile" href="#LibGit2.DiffFile"><code>LibGit2.DiffFile</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.DiffFile</code></pre><p>Description of one side of a delta. Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_diff_file"><code>git_diff_file</code></a> struct.</p><p>The fields represent:</p><ul><li><code>id</code>: the <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> of the item in the diff. If the item is empty on this  side of the diff (for instance, if the diff is of the removal of a file), this will  be <code>GitHash(0)</code>.</li><li><code>path</code>: a <code>NULL</code> terminated path to the item relative to the working directory of the repository.</li><li><code>size</code>: the size of the item in bytes.</li><li><code>flags</code>: a combination of the <a href="https://libgit2.org/libgit2/#HEAD/type/git_diff_flag_t"><code>git_diff_flag_t</code></a>  flags. The <code>i</code>th bit of this integer sets the <code>i</code>th flag.</li><li><code>mode</code>: the <a href="../base/file.html#Base.stat"><code>stat</code></a> mode for the item.</li><li><code>id_abbrev</code>: only present in LibGit2 versions newer than or equal to <code>0.25.0</code>.  The length of the <code>id</code> field when converted using <a href="../base/strings.html#Base.string"><code>string</code></a>. Usually equal to <code>OID_HEXSZ</code> (40).</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L504-L521">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.DiffOptionsStruct" href="#LibGit2.DiffOptionsStruct"><code>LibGit2.DiffOptionsStruct</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.DiffOptionsStruct</code></pre><p>Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_diff_options"><code>git_diff_options</code></a> struct.</p><p>The fields represent:</p><ul><li><code>version</code>: version of the struct in use, in case this changes later. For now, always <code>1</code>.</li><li><code>flags</code>: flags controlling which files will appear in the diff. Defaults to <code>DIFF_NORMAL</code>.</li><li><code>ignore_submodules</code>: whether to look at files in submodules or not. Defaults to <code>SUBMODULE_IGNORE_UNSPECIFIED</code>, which means the submodule&#39;s configuration will control  whether it appears in the diff or not.</li><li><code>pathspec</code>: path to files to include in the diff. Default is to use all files in the repository.</li><li><code>notify_cb</code>: optional callback which will notify the user of changes to the diff as file deltas are  added to it.</li><li><code>progress_cb</code>: optional callback which will display diff progress. Only relevant on libgit2 versions  at least as new as 0.24.0.</li><li><code>payload</code>: the payload to pass to <code>notify_cb</code> and <code>progress_cb</code>.</li><li><code>context_lines</code>: the number of <em>unchanged</em> lines used to define the edges of a hunk.  This is also the number of lines which will be shown before/after a hunk to provide  context. Default is 3.</li><li><code>interhunk_lines</code>: the maximum number of <em>unchanged</em> lines <em>between</em> two separate  hunks allowed before the hunks will be combined. Default is 0.</li><li><code>id_abbrev</code>: sets the length of the abbreviated <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> to print.  Default is <code>7</code>.</li><li><code>max_size</code>: the maximum file size of a blob. Above this size, it will be treated  as a binary blob. The default is 512 MB.</li><li><code>old_prefix</code>: the virtual file directory in which to place old files on one side  of the diff. Default is <code>&quot;a&quot;</code>.</li><li><code>new_prefix</code>: the virtual file directory in which to place new files on one side  of the diff. Default is <code>&quot;b&quot;</code>.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L398-L428">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.FetchHead" href="#LibGit2.FetchHead"><code>LibGit2.FetchHead</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.FetchHead</code></pre><p>Contains the information about HEAD during a fetch, including the name and URL of the branch fetched from, the oid of the HEAD, and whether the fetched HEAD has been merged locally.</p><p>The fields represent:</p><ul><li><code>name</code>: The name in the local reference database of the fetch head, for example,  <code>&quot;refs/heads/master&quot;</code>.</li><li><code>url</code>: The URL of the fetch head.</li><li><code>oid</code>: The <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> of the tip of the fetch head.</li><li><code>ismerge</code>: Boolean flag indicating whether the changes at the  remote have been merged into the local copy yet or not. If <code>true</code>, the local  copy is up to date with the remote fetch head.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L875-L890">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.FetchOptions" href="#LibGit2.FetchOptions"><code>LibGit2.FetchOptions</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.FetchOptions</code></pre><p>Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_fetch_options"><code>git_fetch_options</code></a> struct.</p><p>The fields represent:</p><ul><li><code>version</code>: version of the struct in use, in case this changes later. For now, always <code>1</code>.</li><li><code>callbacks</code>: remote callbacks to use during the fetch.</li><li><code>prune</code>: whether to perform a prune after the fetch or not. The default is to  use the setting from the <code>GitConfig</code>.</li><li><code>update_fetchhead</code>: whether to update the <a href="LibGit2.html#LibGit2.FetchHead"><code>FetchHead</code></a> after the fetch.  The default is to perform the update, which is the normal git behavior.</li><li><code>download_tags</code>: whether to download tags present at the remote or not. The default  is to request the tags for objects which are being downloaded anyway from the server.</li><li><code>proxy_opts</code>: options for connecting to the remote through a proxy. See <a href="LibGit2.html#LibGit2.ProxyOptions"><code>ProxyOptions</code></a>.  Only present on libgit2 versions newer than or equal to 0.25.0.</li><li><code>custom_headers</code>: any extra headers needed for the fetch. Only present on libgit2 versions  newer than or equal to 0.24.0.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L321-L339">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.GitAnnotated" href="#LibGit2.GitAnnotated"><code>LibGit2.GitAnnotated</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">GitAnnotated(repo::GitRepo, commit_id::GitHash)
GitAnnotated(repo::GitRepo, ref::GitReference)
GitAnnotated(repo::GitRepo, fh::FetchHead)
GitAnnotated(repo::GitRepo, committish::AbstractString)</code></pre><p>An annotated git commit carries with it information about how it was looked up and why, so that rebase or merge operations have more information about the context of the commit. Conflict files contain information about the source/target branches in the merge which are conflicting, for instance. An annotated commit can refer to the tip of a remote branch, for instance when a <a href="LibGit2.html#LibGit2.FetchHead"><code>FetchHead</code></a> is passed, or to a branch head described using <code>GitReference</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/merge.jl#L3-L15">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.GitBlame" href="#LibGit2.GitBlame"><code>LibGit2.GitBlame</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">GitBlame(repo::GitRepo, path::AbstractString; options::BlameOptions=BlameOptions())</code></pre><p>Construct a <code>GitBlame</code> object for the file at <code>path</code>, using change information gleaned from the history of <code>repo</code>. The <code>GitBlame</code> object records who changed which chunks of the file when, and how. <code>options</code> controls how to separate the contents of the file and which commits to probe - see <a href="LibGit2.html#LibGit2.BlameOptions"><code>BlameOptions</code></a> for more information.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/blame.jl#L3-L10">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.GitBlob" href="#LibGit2.GitBlob"><code>LibGit2.GitBlob</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">GitBlob(repo::GitRepo, hash::AbstractGitHash)
GitBlob(repo::GitRepo, spec::AbstractString)</code></pre><p>Return a <code>GitBlob</code> object from <code>repo</code> specified by <code>hash</code>/<code>spec</code>.</p><ul><li><code>hash</code> is a full (<code>GitHash</code>) or partial (<code>GitShortHash</code>) hash.</li><li><code>spec</code> is a textual specification: see <a href="https://git-scm.com/docs/git-rev-parse.html#_specifying_revisions">the git docs</a> for a full list.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L127-L135">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.GitCommit" href="#LibGit2.GitCommit"><code>LibGit2.GitCommit</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">GitCommit(repo::GitRepo, hash::AbstractGitHash)
GitCommit(repo::GitRepo, spec::AbstractString)</code></pre><p>Return a <code>GitCommit</code> object from <code>repo</code> specified by <code>hash</code>/<code>spec</code>.</p><ul><li><code>hash</code> is a full (<code>GitHash</code>) or partial (<code>GitShortHash</code>) hash.</li><li><code>spec</code> is a textual specification: see <a href="https://git-scm.com/docs/git-rev-parse.html#_specifying_revisions">the git docs</a> for a full list.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L127-L135">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.GitConfig" href="#LibGit2.GitConfig"><code>LibGit2.GitConfig</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">GitConfig(path::AbstractString, level::Consts.GIT_CONFIG=Consts.CONFIG_LEVEL_APP, force::Bool=false)</code></pre><p>Create a new <code>GitConfig</code> by loading configuration information from the file at <code>path</code>. See <a href="LibGit2.html#LibGit2.addfile"><code>addfile</code></a> for more information about the <code>level</code>, <code>repo</code> and <code>force</code> options.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/config.jl#L3-L8">source</a></section><section><div><pre><code class="language-julia hljs">GitConfig(repo::GitRepo)</code></pre><p>Get the stored configuration for the git repository <code>repo</code>. If <code>repo</code> does not have a specific configuration file set, the default git configuration will be used.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/config.jl#L27-L33">source</a></section><section><div><pre><code class="language-julia hljs">GitConfig(level::Consts.GIT_CONFIG=Consts.CONFIG_LEVEL_DEFAULT)</code></pre><p>Get the default git configuration by loading the global and system configuration files into a prioritized configuration. This can be used to access default configuration options outside a specific git repository.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/config.jl#L42-L48">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.GitHash" href="#LibGit2.GitHash"><code>LibGit2.GitHash</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">GitHash</code></pre><p>A git object identifier, based on the sha-1 hash. It is a 20 byte string (40 hex digits) used to identify a <code>GitObject</code> in a repository.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L13-L18">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.GitObject" href="#LibGit2.GitObject"><code>LibGit2.GitObject</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">GitObject(repo::GitRepo, hash::AbstractGitHash)
GitObject(repo::GitRepo, spec::AbstractString)</code></pre><p>Return the specified object (<a href="LibGit2.html#LibGit2.GitCommit"><code>GitCommit</code></a>, <a href="LibGit2.html#LibGit2.GitBlob"><code>GitBlob</code></a>, <a href="LibGit2.html#LibGit2.GitTree"><code>GitTree</code></a> or <a href="LibGit2.html#LibGit2.GitTag"><code>GitTag</code></a>) from <code>repo</code> specified by <code>hash</code>/<code>spec</code>.</p><ul><li><code>hash</code> is a full (<code>GitHash</code>) or partial (<code>GitShortHash</code>) hash.</li><li><code>spec</code> is a textual specification: see <a href="https://git-scm.com/docs/git-rev-parse.html#_specifying_revisions">the git docs</a> for a full list.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L115-L124">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.GitRemote" href="#LibGit2.GitRemote"><code>LibGit2.GitRemote</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">GitRemote(repo::GitRepo, rmt_name::AbstractString, rmt_url::AbstractString) -&gt; GitRemote</code></pre><p>Look up a remote git repository using its name and URL. Uses the default fetch refspec.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">repo = LibGit2.init(repo_path)
remote = LibGit2.GitRemote(repo, &quot;upstream&quot;, repo_url)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/remote.jl#L3-L13">source</a></section><section><div><pre><code class="language-julia hljs">GitRemote(repo::GitRepo, rmt_name::AbstractString, rmt_url::AbstractString, fetch_spec::AbstractString) -&gt; GitRemote</code></pre><p>Look up a remote git repository using the repository&#39;s name and URL, as well as specifications for how to fetch from the remote (e.g. which remote branch to fetch from).</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">repo = LibGit2.init(repo_path)
refspec = &quot;+refs/heads/mybranch:refs/remotes/origin/mybranch&quot;
remote = LibGit2.GitRemote(repo, &quot;upstream&quot;, repo_url, refspec)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/remote.jl#L23-L36">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.GitRemoteAnon" href="#LibGit2.GitRemoteAnon"><code>LibGit2.GitRemoteAnon</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">GitRemoteAnon(repo::GitRepo, url::AbstractString) -&gt; GitRemote</code></pre><p>Look up a remote git repository using only its URL, not its name.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">repo = LibGit2.init(repo_path)
remote = LibGit2.GitRemoteAnon(repo, repo_url)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/remote.jl#L46-L56">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.GitRepo" href="#LibGit2.GitRepo"><code>LibGit2.GitRepo</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.GitRepo(path::AbstractString)</code></pre><p>Open a git repository at <code>path</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L3-L7">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.GitRepoExt" href="#LibGit2.GitRepoExt"><code>LibGit2.GitRepoExt</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.GitRepoExt(path::AbstractString, flags::Cuint = Cuint(Consts.REPOSITORY_OPEN_DEFAULT))</code></pre><p>Open a git repository at <code>path</code> with extended controls (for instance, if the current user must be a member of a special access group to read <code>path</code>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L16-L21">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.GitRevWalker" href="#LibGit2.GitRevWalker"><code>LibGit2.GitRevWalker</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">GitRevWalker(repo::GitRepo)</code></pre><p>A <code>GitRevWalker</code> <em>walks</em> through the <em>revisions</em> (i.e. commits) of a git repository <code>repo</code>. It is a collection of the commits in the repository, and supports iteration and calls to <a href="LibGit2.html#LibGit2.map"><code>LibGit2.map</code></a> and <a href="LibGit2.html#LibGit2.count"><code>LibGit2.count</code></a> (for instance, <code>LibGit2.count</code> could be used to determine what percentage of commits in a repository were made by a certain author).</p><pre><code class="language-julia hljs">cnt = LibGit2.with(LibGit2.GitRevWalker(repo)) do walker
    LibGit2.count((oid,repo)-&gt;(oid == commit_oid1), walker, oid=commit_oid1, by=LibGit2.Consts.SORT_TIME)
end</code></pre><p>Here, <code>LibGit2.count</code> finds the number of commits along the walk with a certain <code>GitHash</code>. Since the <code>GitHash</code> is unique to a commit, <code>cnt</code> will be <code>1</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/walker.jl#L3-L20">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.GitShortHash" href="#LibGit2.GitShortHash"><code>LibGit2.GitShortHash</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">GitShortHash(hash::GitHash, len::Integer)</code></pre><p>A shortened git object identifier, which can be used to identify a git object when it is unique, consisting of the initial <code>len</code> hexadecimal digits of <code>hash</code> (the remaining digits are ignored).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L26-L32">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.GitSignature" href="#LibGit2.GitSignature"><code>LibGit2.GitSignature</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.GitSignature</code></pre><p>This is a Julia wrapper around a pointer to a <a href="https://libgit2.org/libgit2/#HEAD/type/git_signature"><code>git_signature</code></a> object.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L1089-L1094">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.GitStatus" href="#LibGit2.GitStatus"><code>LibGit2.GitStatus</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.GitStatus(repo::GitRepo; status_opts=StatusOptions())</code></pre><p>Collect information about the status of each file in the git repository <code>repo</code> (e.g. is the file modified, staged, etc.). <code>status_opts</code> can be used to set various options, for instance whether or not to look at untracked files or whether to include submodules or not. See <a href="LibGit2.html#LibGit2.StatusOptions"><code>StatusOptions</code></a> for more information.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/status.jl#L3-L11">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.GitTag" href="#LibGit2.GitTag"><code>LibGit2.GitTag</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">GitTag(repo::GitRepo, hash::AbstractGitHash)
GitTag(repo::GitRepo, spec::AbstractString)</code></pre><p>Return a <code>GitTag</code> object from <code>repo</code> specified by <code>hash</code>/<code>spec</code>.</p><ul><li><code>hash</code> is a full (<code>GitHash</code>) or partial (<code>GitShortHash</code>) hash.</li><li><code>spec</code> is a textual specification: see <a href="https://git-scm.com/docs/git-rev-parse.html#_specifying_revisions">the git docs</a> for a full list.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L127-L135">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.GitTree" href="#LibGit2.GitTree"><code>LibGit2.GitTree</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">GitTree(repo::GitRepo, hash::AbstractGitHash)
GitTree(repo::GitRepo, spec::AbstractString)</code></pre><p>Return a <code>GitTree</code> object from <code>repo</code> specified by <code>hash</code>/<code>spec</code>.</p><ul><li><code>hash</code> is a full (<code>GitHash</code>) or partial (<code>GitShortHash</code>) hash.</li><li><code>spec</code> is a textual specification: see <a href="https://git-scm.com/docs/git-rev-parse.html#_specifying_revisions">the git docs</a> for a full list.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L127-L135">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.IndexEntry" href="#LibGit2.IndexEntry"><code>LibGit2.IndexEntry</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.IndexEntry</code></pre><p>In-memory representation of a file entry in the index. Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_index_entry"><code>git_index_entry</code></a> struct.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L732-L737">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.IndexTime" href="#LibGit2.IndexTime"><code>LibGit2.IndexTime</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.IndexTime</code></pre><p>Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_index_time"><code>git_index_time</code></a> struct.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L722-L726">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.BlameOptions" href="#LibGit2.BlameOptions"><code>LibGit2.BlameOptions</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.BlameOptions</code></pre><p>Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_blame_options"><code>git_blame_options</code></a> struct.</p><p>The fields represent:</p><ul><li><code>version</code>: version of the struct in use, in case this changes later. For now, always <code>1</code>.</li><li><code>flags</code>: one of <code>Consts.BLAME_NORMAL</code> or <code>Consts.BLAME_FIRST_PARENT</code> (the other blame flags  are not yet implemented by libgit2).</li><li><code>min_match_characters</code>: the minimum number of <em>alphanumeric</em> characters which much change in a commit in order for the change to be associated with that commit. The default is 20. Only takes effect if one of the <code>Consts.BLAME_*_COPIES</code> flags are used, which libgit2 does not implement yet.</li><li><code>newest_commit</code>: the <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> of the newest commit from which to look at changes.</li><li><code>oldest_commit</code>: the <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> of the oldest commit from which to look at changes.</li><li><code>min_line</code>: the first line of the file from which to starting blaming. The default is <code>1</code>.</li><li><code>max_line</code>: the last line of the file to which to blame. The default is <code>0</code>, meaning the last line of the file.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L634-L652">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.MergeOptions" href="#LibGit2.MergeOptions"><code>LibGit2.MergeOptions</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.MergeOptions</code></pre><p>Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_merge_options"><code>git_merge_options</code></a> struct.</p><p>The fields represent:</p><ul><li><code>version</code>: version of the struct in use, in case this changes later. For now, always <code>1</code>.</li><li><code>flags</code>: an <code>enum</code> for flags describing merge behavior.  Defined in <a href="https://github.com/libgit2/libgit2/blob/HEAD/include/git2/merge.h#L95"><code>git_merge_flag_t</code></a>.  The corresponding Julia enum is <code>GIT_MERGE</code> and has values:<ul><li><code>MERGE_FIND_RENAMES</code>: detect if a file has been renamed between the common ancestor and the &quot;ours&quot; or &quot;theirs&quot; side of the merge. Allows merges where a file has been renamed.</li><li><code>MERGE_FAIL_ON_CONFLICT</code>: exit immediately if a conflict is found rather than trying to resolve it.</li><li><code>MERGE_SKIP_REUC</code>: do not write the REUC extension on the index resulting from the merge.</li><li><code>MERGE_NO_RECURSIVE</code>: if the commits being merged have multiple merge bases, use the first one, rather than trying to recursively merge the bases.</li></ul></li><li><code>rename_threshold</code>: how similar two files must to consider one a rename of the other. This is an integer that sets the percentage similarity. The default is 50.</li><li><code>target_limit</code>: the maximum number of files to compare with to look for renames. The default is 200.</li><li><code>metric</code>: optional custom function to use to determine the similarity between two files for rename detection.</li><li><code>recursion_limit</code>: the upper limit on the number of merges of common ancestors to perform to try to build a new virtual merge base for the merge. The default is no limit. This field is only present on libgit2 versions newer than 0.24.0.</li><li><code>default_driver</code>: the merge driver to use if both sides have changed. This field is only present on libgit2 versions newer than 0.25.0.</li><li><code>file_favor</code>: how to handle conflicting file contents for the <code>text</code> driver.<ul><li><code>MERGE_FILE_FAVOR_NORMAL</code>: if both sides of the merge have changes to a section,  make a note of the conflict in the index which <code>git checkout</code> will use to create  a merge file, which the user can then reference to resolve the conflicts. This is  the default.</li><li><code>MERGE_FILE_FAVOR_OURS</code>: if both sides of the merge have changes to a section,  use the version in the &quot;ours&quot; side of the merge in the index.</li><li><code>MERGE_FILE_FAVOR_THEIRS</code>: if both sides of the merge have changes to a section,  use the version in the &quot;theirs&quot; side of the merge in the index.</li><li><code>MERGE_FILE_FAVOR_UNION</code>: if both sides of the merge have changes to a section,  include each unique line from both sides in the file which is put into the index.</li></ul></li><li><code>file_flags</code>: guidelines for merging files.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L574-L616">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.ProxyOptions" href="#LibGit2.ProxyOptions"><code>LibGit2.ProxyOptions</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.ProxyOptions</code></pre><p>Options for connecting through a proxy.</p><p>Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_proxy_options"><code>git_proxy_options</code></a> struct.</p><p>The fields represent:</p><ul><li><code>version</code>: version of the struct in use, in case this changes later. For now, always <code>1</code>.</li><li><code>proxytype</code>: an <code>enum</code> for the type of proxy to use.  Defined in <a href="https://libgit2.org/libgit2/#HEAD/type/git_proxy_t"><code>git_proxy_t</code></a>.  The corresponding Julia enum is <code>GIT_PROXY</code> and has values:<ul><li><code>PROXY_NONE</code>: do not attempt the connection through a proxy.</li><li><code>PROXY_AUTO</code>: attempt to figure out the proxy configuration from the git configuration.</li><li><code>PROXY_SPECIFIED</code>: connect using the URL given in the <code>url</code> field of this struct.</li></ul>Default is to auto-detect the proxy type.</li><li><code>url</code>: the URL of the proxy.</li><li><code>credential_cb</code>: a pointer to a callback function which will be called if the remote requires authentication to connect.</li><li><code>certificate_cb</code>: a pointer to a callback function which will be called if certificate verification fails. This lets the user decide whether or not to keep connecting. If the function returns <code>1</code>, connecting will be allowed. If it returns <code>0</code>, the connection will not be allowed. A negative value can be used to return errors.</li><li><code>payload</code>: the payload to be provided to the two callback functions.</li></ul><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; fo = LibGit2.FetchOptions(
           proxy_opts = LibGit2.ProxyOptions(url = Cstring(&quot;https://my_proxy_url.com&quot;)))

julia&gt; fetch(remote, &quot;master&quot;, options=fo)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L278-L310">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.PushOptions" href="#LibGit2.PushOptions"><code>LibGit2.PushOptions</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.PushOptions</code></pre><p>Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_push_options"><code>git_push_options</code></a> struct.</p><p>The fields represent:</p><ul><li><code>version</code>: version of the struct in use, in case this changes later. For now, always <code>1</code>.</li><li><code>parallelism</code>: if a pack file must be created, this variable sets the number of worker  threads which will be spawned by the packbuilder. If <code>0</code>, the packbuilder will auto-set  the number of threads to use. The default is <code>1</code>.</li><li><code>callbacks</code>: the callbacks (e.g. for authentication with the remote) to use for the push.</li><li><code>proxy_opts</code>: only relevant if the LibGit2 version is greater than or equal to <code>0.25.0</code>.  Sets options for using a proxy to communicate with a remote. See <a href="LibGit2.html#LibGit2.ProxyOptions"><code>ProxyOptions</code></a>  for more information.</li><li><code>custom_headers</code>: only relevant if the LibGit2 version is greater than or equal to <code>0.24.0</code>.  Extra headers needed for the push operation.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L665-L681">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.RebaseOperation" href="#LibGit2.RebaseOperation"><code>LibGit2.RebaseOperation</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.RebaseOperation</code></pre><p>Describes a single instruction/operation to be performed during the rebase. Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_rebase_operation_t"><code>git_rebase_operation</code></a> struct.</p><p>The fields represent:</p><ul><li><code>optype</code>: the type of rebase operation currently being performed. The options are:<ul><li><code>REBASE_OPERATION_PICK</code>: cherry-pick the commit in question.</li><li><code>REBASE_OPERATION_REWORD</code>: cherry-pick the commit in question, but rewrite its message using the prompt.</li><li><code>REBASE_OPERATION_EDIT</code>: cherry-pick the commit in question, but allow the user to edit the commit&#39;s contents and its message.</li><li><code>REBASE_OPERATION_SQUASH</code>: squash the commit in question into the previous commit. The commit messages of the two commits will be merged.</li><li><code>REBASE_OPERATION_FIXUP</code>: squash the commit in question into the previous commit. Only the commit message of the previous commit will be used.</li><li><code>REBASE_OPERATION_EXEC</code>: do not cherry-pick a commit. Run a command and continue if the command exits successfully.</li></ul></li><li><code>id</code>: the <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> of the commit being worked on during this rebase step.</li><li><code>exec</code>: in case <code>REBASE_OPERATION_EXEC</code> is used, the command to run during this step (for instance, running the test suite after each commit).</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L791-L813">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.RebaseOptions" href="#LibGit2.RebaseOptions"><code>LibGit2.RebaseOptions</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.RebaseOptions</code></pre><p>Matches the <code>git_rebase_options</code> struct.</p><p>The fields represent:</p><ul><li><code>version</code>: version of the struct in use, in case this changes later. For now, always <code>1</code>.</li><li><code>quiet</code>: inform other git clients helping with/working on the rebase that the rebase should be done &quot;quietly&quot;. Used for interoperability. The default is <code>1</code>.</li><li><code>inmemory</code>: start an in-memory rebase. Callers working on the rebase can go through its steps and commit any changes, but cannot rewind HEAD or update the repository. The <a href="LibGit2.html#LibGit2.workdir"><code>workdir</code></a> will not be modified. Only present on libgit2 versions newer than or equal to 0.24.0.</li><li><code>rewrite_notes_ref</code>: name of the reference to notes to use to rewrite the commit notes as the rebase is finished.</li><li><code>merge_opts</code>: merge options controlling how the trees will be merged at each rebase step.  Only present on libgit2 versions newer than or equal to 0.24.0.</li><li><code>checkout_opts</code>: checkout options for writing files when initializing the rebase, stepping through it, and aborting it. See <a href="LibGit2.html#LibGit2.CheckoutOptions"><code>CheckoutOptions</code></a> for more information.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L758-L776">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.RemoteCallbacks" href="#LibGit2.RemoteCallbacks"><code>LibGit2.RemoteCallbacks</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.RemoteCallbacks</code></pre><p>Callback settings. Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_remote_callbacks"><code>git_remote_callbacks</code></a> struct.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L214-L219">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.SignatureStruct" href="#LibGit2.SignatureStruct"><code>LibGit2.SignatureStruct</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.SignatureStruct</code></pre><p>An action signature (e.g. for committers, taggers, etc). Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_signature"><code>git_signature</code></a> struct.</p><p>The fields represent:</p><ul><li><code>name</code>: The full name of the committer or author of the commit.</li><li><code>email</code>: The email at which the committer/author can be contacted.</li><li><code>when</code>: a <a href="LibGit2.html#LibGit2.TimeStruct"><code>TimeStruct</code></a> indicating when the commit was  authored/committed into the repository.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L53-L64">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.StatusEntry" href="#LibGit2.StatusEntry"><code>LibGit2.StatusEntry</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.StatusEntry</code></pre><p>Providing the differences between the file as it exists in HEAD and the index, and providing the differences between the index and the working directory. Matches the <code>git_status_entry</code> struct.</p><p>The fields represent:</p><ul><li><code>status</code>: contains the status flags for the file, indicating if it is current, or has been changed in some way in the index or work tree.</li><li><code>head_to_index</code>: a pointer to a <a href="LibGit2.html#LibGit2.DiffDelta"><code>DiffDelta</code></a> which encapsulates the difference(s) between the file as it exists in HEAD and in the index.</li><li><code>index_to_workdir</code>: a pointer to a <code>DiffDelta</code> which encapsulates the difference(s) between the file as it exists in the index and in the <a href="LibGit2.html#LibGit2.workdir"><code>workdir</code></a>.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L854-L868">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.StatusOptions" href="#LibGit2.StatusOptions"><code>LibGit2.StatusOptions</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.StatusOptions</code></pre><p>Options to control how <code>git_status_foreach_ext()</code> will issue callbacks. Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_status_opt_t"><code>git_status_opt_t</code></a> struct.</p><p>The fields represent:</p><ul><li><code>version</code>: version of the struct in use, in case this changes later. For now, always <code>1</code>.</li><li><code>show</code>: a flag for which files to examine and in which order. The default is <code>Consts.STATUS_SHOW_INDEX_AND_WORKDIR</code>.</li><li><code>flags</code>: flags for controlling any callbacks used in a status call.</li><li><code>pathspec</code>: an array of paths to use for path-matching. The behavior of the path-matching will vary depending on the values of <code>show</code> and <code>flags</code>.</li><li>The <code>baseline</code> is the tree to be used for comparison to the working directory and index; defaults to HEAD.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L824-L839">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.StrArrayStruct" href="#LibGit2.StrArrayStruct"><code>LibGit2.StrArrayStruct</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.StrArrayStruct</code></pre><p>A LibGit2 representation of an array of strings. Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_strarray"><code>git_strarray</code></a> struct.</p><p>When fetching data from LibGit2, a typical usage would look like:</p><pre><code class="language-julia hljs">sa_ref = Ref(StrArrayStruct())
@check ccall(..., (Ptr{StrArrayStruct},), sa_ref)
res = convert(Vector{String}, sa_ref[])
free(sa_ref)</code></pre><p>In particular, note that <code>LibGit2.free</code> should be called afterward on the <code>Ref</code> object.</p><p>Conversely, when passing a vector of strings to LibGit2, it is generally simplest to rely on implicit conversion:</p><pre><code class="language-julia hljs">strs = String[...]
@check ccall(..., (Ptr{StrArrayStruct},), strs)</code></pre><p>Note that no call to <code>free</code> is required as the data is allocated by Julia.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L71-L93">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.TimeStruct" href="#LibGit2.TimeStruct"><code>LibGit2.TimeStruct</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.TimeStruct</code></pre><p>Time in a signature. Matches the <a href="https://libgit2.org/libgit2/#HEAD/type/git_time"><code>git_time</code></a> struct.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L39-L44">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.addfile" href="#LibGit2.addfile"><code>LibGit2.addfile</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">addfile(cfg::GitConfig, path::AbstractString,
        level::Consts.GIT_CONFIG=Consts.CONFIG_LEVEL_APP,
        repo::Union{GitRepo, Nothing} = nothing,
        force::Bool=false)</code></pre><p>Add an existing git configuration file located at <code>path</code> to the current <code>GitConfig</code> <code>cfg</code>. If the file does not exist, it will be created.</p><ul><li><code>level</code> sets the git configuration priority level and is determined by</li></ul><p><a href="LibGit2.html#LibGit2.Consts.GIT_CONFIG"><code>Consts.GIT_CONFIG</code></a>.</p><ul><li><code>repo</code> is an optional repository to allow parsing of conditional includes.</li><li>If <code>force</code> is <code>false</code> and a configuration for the given priority level already exists,</li></ul><p><code>addfile</code> will error. If <code>force</code> is <code>true</code>, the existing configuration will be replaced by the one in the file at <code>path</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/config.jl#L70-L86">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.add!" href="#LibGit2.add!"><code>LibGit2.add!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">add!(repo::GitRepo, files::AbstractString...; flags::Cuint = Consts.INDEX_ADD_DEFAULT)
add!(idx::GitIndex, files::AbstractString...; flags::Cuint = Consts.INDEX_ADD_DEFAULT)</code></pre><p>Add all the files with paths specified by <code>files</code> to the index <code>idx</code> (or the index of the <code>repo</code>). If the file already exists, the index entry will be updated. If the file does not exist already, it will be newly added into the index. <code>files</code> may contain glob patterns which will be expanded and any matching files will be added (unless <code>INDEX_ADD_DISABLE_PATHSPEC_MATCH</code> is set, see below). If a file has been ignored (in <code>.gitignore</code> or in the config), it <em>will not</em> be added, <em>unless</em> it is already being tracked in the index, in which case it <em>will</em> be updated. The keyword argument <code>flags</code> is a set of bit-flags which control the behavior with respect to ignored files:</p><ul><li><code>Consts.INDEX_ADD_DEFAULT</code> - default, described above.</li><li><code>Consts.INDEX_ADD_FORCE</code> - disregard the existing ignore rules and force addition of the file to the index even if it is already ignored.</li><li><code>Consts.INDEX_ADD_CHECK_PATHSPEC</code> - cannot be used at the same time as <code>INDEX_ADD_FORCE</code>. Check that each file in <code>files</code> which exists on disk is not in the ignore list. If one of the files <em>is</em> ignored, the function will return <code>EINVALIDSPEC</code>.</li><li><code>Consts.INDEX_ADD_DISABLE_PATHSPEC_MATCH</code> - turn off glob matching, and only add files to the index which exactly match the paths specified in <code>files</code>.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/index.jl#L82-L103">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.add_fetch!" href="#LibGit2.add_fetch!"><code>LibGit2.add_fetch!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">add_fetch!(repo::GitRepo, rmt::GitRemote, fetch_spec::String)</code></pre><p>Add a <em>fetch</em> refspec for the specified <code>rmt</code>. This refspec will contain information about which branch(es) to fetch from.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; LibGit2.add_fetch!(repo, remote, &quot;upstream&quot;);

julia&gt; LibGit2.fetch_refspecs(remote)
String[&quot;+refs/heads/*:refs/remotes/upstream/*&quot;]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/remote.jl#L247-L260">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.add_push!" href="#LibGit2.add_push!"><code>LibGit2.add_push!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">add_push!(repo::GitRepo, rmt::GitRemote, push_spec::String)</code></pre><p>Add a <em>push</em> refspec for the specified <code>rmt</code>. This refspec will contain information about which branch(es) to push to.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; LibGit2.add_push!(repo, remote, &quot;refs/heads/master&quot;);

julia&gt; remote = LibGit2.get(LibGit2.GitRemote, repo, branch);

julia&gt; LibGit2.push_refspecs(remote)
String[&quot;refs/heads/master&quot;]</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>You may need to <a href="../base/io-network.html#Base.close"><code>close</code></a> and reopen the <code>GitRemote</code> in question after updating its push refspecs in order for the change to take effect and for calls to <a href="LibGit2.html#LibGit2.push"><code>push</code></a> to work.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/remote.jl#L268-L289">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.addblob!" href="#LibGit2.addblob!"><code>LibGit2.addblob!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.addblob!(repo::GitRepo, path::AbstractString)</code></pre><p>Read the file at <code>path</code> and adds it to the object database of <code>repo</code> as a loose blob. Return the <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> of the resulting blob.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">hash_str = string(commit_oid)
blob_file = joinpath(repo_path, &quot;.git&quot;, &quot;objects&quot;, hash_str[1:2], hash_str[3:end])
id = LibGit2.addblob!(repo, blob_file)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/blob.jl#L54-L66">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.author" href="#LibGit2.author"><code>LibGit2.author</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">author(c::GitCommit)</code></pre><p>Return the <code>Signature</code> of the author of the commit <code>c</code>. The author is the person who made changes to the relevant file(s). See also <a href="LibGit2.html#LibGit2.committer"><code>committer</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/commit.jl#L27-L32">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.authors" href="#LibGit2.authors"><code>LibGit2.authors</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">authors(repo::GitRepo) -&gt; Vector{Signature}</code></pre><p>Return all authors of commits to the <code>repo</code> repository.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">repo = LibGit2.GitRepo(repo_path)
repo_file = open(joinpath(repo_path, test_file), &quot;a&quot;)

println(repo_file, commit_msg)
flush(repo_file)
LibGit2.add!(repo, test_file)
sig = LibGit2.Signature(&quot;TEST&quot;, &quot;<EMAIL>&quot;, round(time(), 0), 0)
commit_oid1 = LibGit2.commit(repo, &quot;commit1&quot;; author=sig, committer=sig)
println(repo_file, randstring(10))
flush(repo_file)
LibGit2.add!(repo, test_file)
commit_oid2 = LibGit2.commit(repo, &quot;commit2&quot;; author=sig, committer=sig)

# will be a Vector of [sig, sig]
auths = LibGit2.authors(repo)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L901-L924">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.branch" href="#LibGit2.branch"><code>LibGit2.branch</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">branch(repo::GitRepo)</code></pre><p>Equivalent to <code>git branch</code>. Create a new branch from the current HEAD.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L366-L371">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.branch!" href="#LibGit2.branch!"><code>LibGit2.branch!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">branch!(repo::GitRepo, branch_name::AbstractString, commit::AbstractString=&quot;&quot;; kwargs...)</code></pre><p>Checkout a new git branch in the <code>repo</code> repository. <code>commit</code> is the <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a>, in string form, which will be the start of the new branch. If <code>commit</code> is an empty string, the current HEAD will be used.</p><p>The keyword arguments are:</p><ul><li><code>track::AbstractString=&quot;&quot;</code>: the name of the remote branch this new branch should track, if any. If empty (the default), no remote branch will be tracked.</li><li><code>force::Bool=false</code>: if <code>true</code>, branch creation will be forced.</li><li><code>set_head::Bool=true</code>: if <code>true</code>, after the branch creation finishes the branch head will be set as the HEAD of <code>repo</code>.</li></ul><p>Equivalent to <code>git checkout [-b|-B] &lt;branch_name&gt; [&lt;commit&gt;] [--track &lt;track&gt;]</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">repo = LibGit2.GitRepo(repo_path)
LibGit2.branch!(repo, &quot;new_branch&quot;, set_head=false)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L381-L405">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.checkout!" href="#LibGit2.checkout!"><code>LibGit2.checkout!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">checkout!(repo::GitRepo, commit::AbstractString=&quot;&quot;; force::Bool=true)</code></pre><p>Equivalent to <code>git checkout [-f] --detach &lt;commit&gt;</code>. Checkout the git commit <code>commit</code> (a <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> in string form) in <code>repo</code>. If <code>force</code> is <code>true</code>, force the checkout and discard any current changes. Note that this detaches the current HEAD.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">repo = LibGit2.GitRepo(repo_path)
open(joinpath(LibGit2.path(repo), &quot;file1&quot;), &quot;w&quot;) do f
    write(f, &quot;111
&quot;)
end
LibGit2.add!(repo, &quot;file1&quot;)
commit_oid = LibGit2.commit(repo, &quot;add file1&quot;)
open(joinpath(LibGit2.path(repo), &quot;file1&quot;), &quot;w&quot;) do f
    write(f, &quot;112
&quot;)
end
# would fail without the force=true
# since there are modifications to the file
LibGit2.checkout!(repo, string(commit_oid), force=true)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L472-L497">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.clone" href="#LibGit2.clone"><code>LibGit2.clone</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">clone(repo_url::AbstractString, repo_path::AbstractString, clone_opts::CloneOptions)</code></pre><p>Clone the remote repository at <code>repo_url</code> (which can be a remote URL or a path on the local filesystem) to <code>repo_path</code> (which must be a path on the local filesystem). Options for the clone, such as whether to perform a bare clone or not, are set by <a href="LibGit2.html#LibGit2.CloneOptions"><code>CloneOptions</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">repo_url = &quot;https://github.com/JuliaLang/Example.jl&quot;
repo = LibGit2.clone(repo_url, &quot;/home/<USER>/projects/Example&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L441-L453">source</a></section><section><div><pre><code class="language-julia hljs">clone(repo_url::AbstractString, repo_path::AbstractString; kwargs...)</code></pre><p>Clone a remote repository located at <code>repo_url</code> to the local filesystem location <code>repo_path</code>.</p><p>The keyword arguments are:</p><ul><li><code>branch::AbstractString=&quot;&quot;</code>: which branch of the remote to clone, if not the default repository branch (usually <code>master</code>).</li><li><code>isbare::Bool=false</code>: if <code>true</code>, clone the remote as a bare repository, which will make <code>repo_path</code> itself the git directory instead of <code>repo_path/.git</code>. This means that a working tree cannot be checked out. Plays the role of the git CLI argument <code>--bare</code>.</li><li><code>remote_cb::Ptr{Cvoid}=C_NULL</code>: a callback which will be used to create the remote before it is cloned. If <code>C_NULL</code> (the default), no attempt will be made to create the remote - it will be assumed to already exist.</li><li><code>credentials::Creds=nothing</code>: provides credentials and/or settings when authenticating against a private repository.</li><li><code>callbacks::Callbacks=Callbacks()</code>: user provided callbacks and payloads.</li></ul><p>Equivalent to <code>git clone [-b &lt;branch&gt;] [--bare] &lt;repo_url&gt; &lt;repo_path&gt;</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">repo_url = &quot;https://github.com/JuliaLang/Example.jl&quot;
repo1 = LibGit2.clone(repo_url, &quot;test_path&quot;)
repo2 = LibGit2.clone(repo_url, &quot;test_path&quot;, isbare=true)
julia_url = &quot;https://github.com/JuliaLang/julia&quot;
julia_repo = LibGit2.clone(julia_url, &quot;julia_path&quot;, branch=&quot;release-0.6&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L528-L557">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.commit" href="#LibGit2.commit"><code>LibGit2.commit</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">commit(repo::GitRepo, msg::AbstractString; kwargs...) -&gt; GitHash</code></pre><p>Wrapper around <a href="https://libgit2.org/libgit2/#HEAD/group/commit/git_commit_create"><code>git_commit_create</code></a>. Create a commit in the repository <code>repo</code>. <code>msg</code> is the commit message. Return the OID of the new commit.</p><p>The keyword arguments are:</p><ul><li><code>refname::AbstractString=Consts.HEAD_FILE</code>: if not NULL, the name of the reference to update to point to the new commit. For example, <code>&quot;HEAD&quot;</code> will update the HEAD of the current branch. If the reference does not yet exist, it will be created.</li><li><code>author::Signature = Signature(repo)</code> is a <code>Signature</code> containing information about the person who authored the commit.</li><li><code>committer::Signature = Signature(repo)</code> is a <code>Signature</code> containing information about the person who committed the commit to the repository. Not necessarily the same as <code>author</code>, for instance if <code>author</code> emailed a patch to <code>committer</code> who committed it.</li><li><code>tree_id::GitHash = GitHash()</code> is a git tree to use to create the commit, showing its ancestry and relationship with any other history. <code>tree</code> must belong to <code>repo</code>.</li><li><code>parent_ids::Vector{GitHash}=GitHash[]</code> is a list of commits by <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> to use as parent commits for the new one, and may be empty. A commit might have multiple parents if it is a merge commit, for example.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/commit.jl#L89-L107">source</a></section><section><div><pre><code class="language-julia hljs">LibGit2.commit(rb::GitRebase, sig::GitSignature)</code></pre><p>Commit the current patch to the rebase <code>rb</code>, using <code>sig</code> as the committer. Is silent if the commit has already been applied.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/rebase.jl#L71-L76">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.committer" href="#LibGit2.committer"><code>LibGit2.committer</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">committer(c::GitCommit)</code></pre><p>Return the <code>Signature</code> of the committer of the commit <code>c</code>. The committer is the person who committed the changes originally authored by the <a href="LibGit2.html#LibGit2.author"><code>author</code></a>, but need not be the same as the <code>author</code>, for example, if the <code>author</code> emailed a patch to a <code>committer</code> who committed it.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/commit.jl#L43-L50">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.count" href="#LibGit2.count"><code>LibGit2.count</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.count(f::Function, walker::GitRevWalker; oid::GitHash=GitHash(), by::Cint=Consts.SORT_NONE, rev::Bool=false)</code></pre><p>Using the <a href="LibGit2.html#LibGit2.GitRevWalker"><code>GitRevWalker</code></a> <code>walker</code> to &quot;walk&quot; over every commit in the repository&#39;s history, find the number of commits which return <code>true</code> when <code>f</code> is applied to them. The keyword arguments are:     * <code>oid</code>: The <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> of the commit to begin the walk from. The default is to use       <a href="LibGit2.html#LibGit2.push_head!"><code>push_head!</code></a> and therefore the HEAD commit and all its ancestors.     * <code>by</code>: The sorting method. The default is not to sort. Other options are to sort by       topology (<code>LibGit2.Consts.SORT_TOPOLOGICAL</code>), to sort forwards in time       (<code>LibGit2.Consts.SORT_TIME</code>, most ancient first) or to sort backwards in time       (<code>LibGit2.Consts.SORT_REVERSE</code>, most recent first).     * <code>rev</code>: Whether to reverse the sorted order (for instance, if topological sorting is used).</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">cnt = LibGit2.with(LibGit2.GitRevWalker(repo)) do walker
    LibGit2.count((oid, repo)-&gt;(oid == commit_oid1), walker, oid=commit_oid1, by=LibGit2.Consts.SORT_TIME)
end</code></pre><p><code>LibGit2.count</code> finds the number of commits along the walk with a certain <code>GitHash</code> <code>commit_oid1</code>, starting the walk from that commit and moving forwards in time from it. Since the <code>GitHash</code> is unique to a commit, <code>cnt</code> will be <code>1</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/walker.jl#L132-L155">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.counthunks" href="#LibGit2.counthunks"><code>LibGit2.counthunks</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">counthunks(blame::GitBlame)</code></pre><p>Return the number of distinct &quot;hunks&quot; with a file. A hunk may contain multiple lines. A hunk is usually a piece of a file that was added/changed/removed together, for example, a function added to a source file or an inner loop that was optimized out of that function later.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/blame.jl#L20-L27">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.create_branch" href="#LibGit2.create_branch"><code>LibGit2.create_branch</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.create_branch(repo::GitRepo, bname::AbstractString, commit_obj::GitCommit; force::Bool=false)</code></pre><p>Create a new branch in the repository <code>repo</code> with name <code>bname</code>, which points to commit <code>commit_obj</code> (which has to be part of <code>repo</code>). If <code>force</code> is <code>true</code>, overwrite an existing branch named <code>bname</code> if it exists. If <code>force</code> is <code>false</code> and a branch already exists named <code>bname</code>, this function will throw an error.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/reference.jl#L223-L231">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.credentials_callback" href="#LibGit2.credentials_callback"><code>LibGit2.credentials_callback</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">credential_callback(...) -&gt; Cint</code></pre><p>A LibGit2 credential callback function which provides different credential acquisition functionality w.r.t. a connection protocol. The <code>payload_ptr</code> is required to contain a <code>LibGit2.CredentialPayload</code> object which will keep track of state and settings.</p><p>The <code>allowed_types</code> contains a bitmask of <code>LibGit2.Consts.GIT_CREDTYPE</code> values specifying which authentication methods should be attempted.</p><p>Credential authentication is done in the following order (if supported):</p><ul><li>SSH agent</li><li>SSH private/public key pair</li><li>Username/password plain text</li></ul><p>If a user is presented with a credential prompt they can abort the prompt by typing <code>^D</code> (pressing the control key together with the <code>d</code> key).</p><p><strong>Note</strong>: Due to the specifics of the <code>libgit2</code> authentication procedure, when authentication fails, this function is called again without any indication whether authentication was successful or not. To avoid an infinite loop from repeatedly using the same faulty credentials, we will keep track of state using the payload.</p><p>For addition details see the LibGit2 guide on <a href="https://libgit2.org/docs/guides/authentication/">authenticating against a server</a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/callbacks.jl#L244-L269">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.credentials_cb" href="#LibGit2.credentials_cb"><code>LibGit2.credentials_cb</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>C function pointer for <code>credentials_callback</code></p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/callbacks.jl#L513">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.default_signature" href="#LibGit2.default_signature"><code>LibGit2.default_signature</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>Return signature object. Free it after use.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/signature.jl#L65">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.delete_branch" href="#LibGit2.delete_branch"><code>LibGit2.delete_branch</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.delete_branch(branch::GitReference)</code></pre><p>Delete the branch pointed to by <code>branch</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/reference.jl#L244-L248">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.diff_files" href="#LibGit2.diff_files"><code>LibGit2.diff_files</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">diff_files(repo::GitRepo, branch1::AbstractString, branch2::AbstractString; kwarg...) -&gt; Vector{AbstractString}</code></pre><p>Show which files have changed in the git repository <code>repo</code> between branches <code>branch1</code> and <code>branch2</code>.</p><p>The keyword argument is:</p><ul><li><code>filter::Set{Consts.DELTA_STATUS}=Set([Consts.DELTA_ADDED, Consts.DELTA_MODIFIED, Consts.DELTA_DELETED]))</code>, and it sets options for the diff. The default is to show files added, modified, or deleted.</li></ul><p>Return only the <em>names</em> of the files which have changed, <em>not</em> their contents.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">LibGit2.branch!(repo, &quot;branch/a&quot;)
LibGit2.branch!(repo, &quot;branch/b&quot;)
# add a file to repo
open(joinpath(LibGit2.path(repo),&quot;file&quot;),&quot;w&quot;) do f
    write(f, &quot;hello repo
&quot;)
end
LibGit2.add!(repo, &quot;file&quot;)
LibGit2.commit(repo, &quot;add file&quot;)
# returns [&quot;file&quot;]
filt = Set([LibGit2.Consts.DELTA_ADDED])
files = LibGit2.diff_files(repo, &quot;branch/a&quot;, &quot;branch/b&quot;, filter=filt)
# returns [] because existing files weren&#39;t modified
filt = Set([LibGit2.Consts.DELTA_MODIFIED])
files = LibGit2.diff_files(repo, &quot;branch/a&quot;, &quot;branch/b&quot;, filter=filt)</code></pre><p>Equivalent to <code>git diff --name-only --diff-filter=&lt;filter&gt; &lt;branch1&gt; &lt;branch2&gt;</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L171-L203">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.entryid" href="#LibGit2.entryid"><code>LibGit2.entryid</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">entryid(te::GitTreeEntry)</code></pre><p>Return the <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> of the object to which <code>te</code> refers.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/tree.jl#L88-L92">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.entrytype" href="#LibGit2.entrytype"><code>LibGit2.entrytype</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">entrytype(te::GitTreeEntry)</code></pre><p>Return the type of the object to which <code>te</code> refers. The result will be one of the types which <a href="LibGit2.html#LibGit2.objtype"><code>objtype</code></a> returns, e.g. a <code>GitTree</code> or <code>GitBlob</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/tree.jl#L76-L81">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.fetch" href="#LibGit2.fetch"><code>LibGit2.fetch</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">fetch(rmt::GitRemote, refspecs; options::FetchOptions=FetchOptions(), msg=&quot;&quot;)</code></pre><p>Fetch from the specified <code>rmt</code> remote git repository, using <code>refspecs</code> to determine which remote branch(es) to fetch. The keyword arguments are:</p><ul><li><code>options</code>: determines the options for the fetch, e.g. whether to prune afterwards. See <a href="LibGit2.html#LibGit2.FetchOptions"><code>FetchOptions</code></a> for more information.</li><li><code>msg</code>: a message to insert into the reflogs.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/remote.jl#L297-L306">source</a></section><section><div><pre><code class="language-julia hljs">fetch(repo::GitRepo; kwargs...)</code></pre><p>Fetches updates from an upstream of the repository <code>repo</code>.</p><p>The keyword arguments are:</p><ul><li><code>remote::AbstractString=&quot;origin&quot;</code>: which remote, specified by name, of <code>repo</code> to fetch from. If this is empty, the URL will be used to construct an anonymous remote.</li><li><code>remoteurl::AbstractString=&quot;&quot;</code>: the URL of <code>remote</code>. If not specified, will be assumed based on the given name of <code>remote</code>.</li><li><code>refspecs=AbstractString[]</code>: determines properties of the fetch.</li><li><code>credentials=nothing</code>: provides credentials and/or settings when authenticating against a private <code>remote</code>.</li><li><code>callbacks=Callbacks()</code>: user provided callbacks and payloads.</li></ul><p>Equivalent to <code>git fetch [&lt;remoteurl&gt;|&lt;repo&gt;] [&lt;refspecs&gt;]</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L254-L271">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.fetchheads" href="#LibGit2.fetchheads"><code>LibGit2.fetchheads</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">fetchheads(repo::GitRepo) -&gt; Vector{FetchHead}</code></pre><p>Return the list of all the fetch heads for <code>repo</code>, each represented as a <a href="LibGit2.html#LibGit2.FetchHead"><code>FetchHead</code></a>, including their names, URLs, and merge statuses.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; fetch_heads = LibGit2.fetchheads(repo);

julia&gt; fetch_heads[1].name
&quot;refs/heads/master&quot;

julia&gt; fetch_heads[1].ismerge
true

julia&gt; fetch_heads[2].name
&quot;refs/heads/test_branch&quot;

julia&gt; fetch_heads[2].ismerge
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L465-L487">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.fetch_refspecs" href="#LibGit2.fetch_refspecs"><code>LibGit2.fetch_refspecs</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">fetch_refspecs(rmt::GitRemote) -&gt; Vector{String}</code></pre><p>Get the <em>fetch</em> refspecs for the specified <code>rmt</code>. These refspecs contain information about which branch(es) to fetch from.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; remote = LibGit2.get(LibGit2.GitRemote, repo, &quot;upstream&quot;);

julia&gt; LibGit2.add_fetch!(repo, remote, &quot;upstream&quot;);

julia&gt; LibGit2.fetch_refspecs(remote)
String[&quot;+refs/heads/*:refs/remotes/upstream/*&quot;]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/remote.jl#L191-L206">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.fetchhead_foreach_cb" href="#LibGit2.fetchhead_foreach_cb"><code>LibGit2.fetchhead_foreach_cb</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>C function pointer for <code>fetchhead_foreach_callback</code></p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/callbacks.jl#L515">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.merge_base" href="#LibGit2.merge_base"><code>LibGit2.merge_base</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">merge_base(repo::GitRepo, one::AbstractString, two::AbstractString) -&gt; GitHash</code></pre><p>Find a merge base (a common ancestor) between the commits <code>one</code> and <code>two</code>. <code>one</code> and <code>two</code> may both be in string form. Return the <code>GitHash</code> of the merge base.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/merge.jl#L252-L257">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.merge!-Tuple{GitRepo}" href="#LibGit2.merge!-Tuple{GitRepo}"><code>LibGit2.merge!</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">merge!(repo::GitRepo; kwargs...) -&gt; Bool</code></pre><p>Perform a git merge on the repository <code>repo</code>, merging commits with diverging history into the current branch. Return <code>true</code> if the merge succeeded, <code>false</code> if not.</p><p>The keyword arguments are:</p><ul><li><code>committish::AbstractString=&quot;&quot;</code>: Merge the named commit(s) in <code>committish</code>.</li><li><code>branch::AbstractString=&quot;&quot;</code>: Merge the branch <code>branch</code> and all its commits since it diverged from the current branch.</li><li><code>fastforward::Bool=false</code>: If <code>fastforward</code> is <code>true</code>, only merge if the merge is a fast-forward (the current branch head is an ancestor of the commits to be merged), otherwise refuse to merge and return <code>false</code>. This is equivalent to the git CLI option <code>--ff-only</code>.</li><li><code>merge_opts::MergeOptions=MergeOptions()</code>: <code>merge_opts</code> specifies options for the merge, such as merge strategy in case of conflicts.</li><li><code>checkout_opts::CheckoutOptions=CheckoutOptions()</code>: <code>checkout_opts</code> specifies options for the checkout step.</li></ul><p>Equivalent to <code>git merge [--ff-only] [&lt;committish&gt; | &lt;branch&gt;]</code>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>If you specify a <code>branch</code>, this must be done in reference format, since the string will be turned into a <code>GitReference</code>. For example, if you wanted to merge branch <code>branch_a</code>, you would call <code>merge!(repo, branch=&quot;refs/heads/branch_a&quot;)</code>.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L730-L757">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.merge!-Tuple{GitRepo, Vector{LibGit2.GitAnnotated}}" href="#LibGit2.merge!-Tuple{GitRepo, Vector{LibGit2.GitAnnotated}}"><code>LibGit2.merge!</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">merge!(repo::GitRepo, anns::Vector{GitAnnotated}; kwargs...) -&gt; Bool</code></pre><p>Merge changes from the annotated commits (captured as <a href="LibGit2.html#LibGit2.GitAnnotated"><code>GitAnnotated</code></a> objects) <code>anns</code> into the HEAD of the repository <code>repo</code>. The keyword arguments are:</p><ul><li><code>merge_opts::MergeOptions = MergeOptions()</code>: options for how to perform the merge, including whether fastforwarding is allowed. See <a href="LibGit2.html#LibGit2.MergeOptions"><code>MergeOptions</code></a> for more information.</li><li><code>checkout_opts::CheckoutOptions = CheckoutOptions()</code>: options for how to perform the checkout. See <a href="LibGit2.html#LibGit2.CheckoutOptions"><code>CheckoutOptions</code></a> for more information.</li></ul><p><code>anns</code> may refer to remote or local branch heads. Return <code>true</code> if the merge is successful, otherwise return <code>false</code> (for instance, if no merge is possible because the branches have no common ancestor).</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">upst_ann = LibGit2.GitAnnotated(repo, &quot;branch/a&quot;)

# merge the branch in
LibGit2.merge!(repo, [upst_ann])</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/merge.jl#L122-L144">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.merge!-Tuple{GitRepo, Vector{LibGit2.GitAnnotated}, Bool}" href="#LibGit2.merge!-Tuple{GitRepo, Vector{LibGit2.GitAnnotated}, Bool}"><code>LibGit2.merge!</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">merge!(repo::GitRepo, anns::Vector{GitAnnotated}, fastforward::Bool; kwargs...) -&gt; Bool</code></pre><p>Merge changes from the annotated commits (captured as <a href="LibGit2.html#LibGit2.GitAnnotated"><code>GitAnnotated</code></a> objects) <code>anns</code> into the HEAD of the repository <code>repo</code>. If <code>fastforward</code> is <code>true</code>, <em>only</em> a fastforward merge is allowed. In this case, if conflicts occur, the merge will fail. Otherwise, if <code>fastforward</code> is <code>false</code>, the merge may produce a conflict file which the user will need to resolve.</p><p>The keyword arguments are:</p><ul><li><code>merge_opts::MergeOptions = MergeOptions()</code>: options for how to perform the merge, including whether fastforwarding is allowed. See <a href="LibGit2.html#LibGit2.MergeOptions"><code>MergeOptions</code></a> for more information.</li><li><code>checkout_opts::CheckoutOptions = CheckoutOptions()</code>: options for how to perform the checkout. See <a href="LibGit2.html#LibGit2.CheckoutOptions"><code>CheckoutOptions</code></a> for more information.</li></ul><p><code>anns</code> may refer to remote or local branch heads. Return <code>true</code> if the merge is successful, otherwise return <code>false</code> (for instance, if no merge is possible because the branches have no common ancestor).</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">upst_ann_1 = LibGit2.GitAnnotated(repo, &quot;branch/a&quot;)

# merge the branch in, fastforward
LibGit2.merge!(repo, [upst_ann_1], true)

# merge conflicts!
upst_ann_2 = LibGit2.GitAnnotated(repo, &quot;branch/b&quot;)
# merge the branch in, try to fastforward
LibGit2.merge!(repo, [upst_ann_2], true) # will return false
LibGit2.merge!(repo, [upst_ann_2], false) # will return true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/merge.jl#L161-L194">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.ffmerge!" href="#LibGit2.ffmerge!"><code>LibGit2.ffmerge!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ffmerge!(repo::GitRepo, ann::GitAnnotated)</code></pre><p>Fastforward merge changes into current HEAD. This is only possible if the commit referred to by <code>ann</code> is descended from the current HEAD (e.g. if pulling changes from a remote branch which is simply ahead of the local branch tip).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/merge.jl#L97-L103">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.fullname" href="#LibGit2.fullname"><code>LibGit2.fullname</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.fullname(ref::GitReference)</code></pre><p>Return the name of the reference pointed to by the symbolic reference <code>ref</code>. If <code>ref</code> is not a symbolic reference, return an empty string.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/reference.jl#L91-L97">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.features" href="#LibGit2.features"><code>LibGit2.features</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">features()</code></pre><p>Return a list of git features the current version of libgit2 supports, such as threading or using HTTPS or SSH.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/utils.jl#L68-L73">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.filename" href="#LibGit2.filename"><code>LibGit2.filename</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">filename(te::GitTreeEntry)</code></pre><p>Return the filename of the object on disk to which <code>te</code> refers.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/tree.jl#L54-L58">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.filemode" href="#LibGit2.filemode"><code>LibGit2.filemode</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">filemode(te::GitTreeEntry) -&gt; Cint</code></pre><p>Return the UNIX filemode of the object on disk to which <code>te</code> refers as an integer.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/tree.jl#L66-L70">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.gitdir" href="#LibGit2.gitdir"><code>LibGit2.gitdir</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.gitdir(repo::GitRepo)</code></pre><p>Return the location of the &quot;git&quot; files of <code>repo</code>:</p><ul><li>for normal repositories, this is the location of the <code>.git</code> folder.</li><li>for bare repositories, this is the location of the repository itself.</li></ul><p>See also <a href="LibGit2.html#LibGit2.workdir"><code>workdir</code></a>, <a href="LibGit2.html#LibGit2.path"><code>path</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L180-L189">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.git_url" href="#LibGit2.git_url"><code>LibGit2.git_url</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.git_url(; kwargs...) -&gt; String</code></pre><p>Create a string based upon the URL components provided. When the <code>scheme</code> keyword is not provided the URL produced will use the alternative <a href="https://git-scm.com/docs/git-clone#_git_urls_a_id_urls_a">scp-like syntax</a>.</p><p><strong>Keywords</strong></p><ul><li><code>scheme::AbstractString=&quot;&quot;</code>: the URL scheme which identifies the protocol to be used. For HTTP use &quot;http&quot;, SSH use &quot;ssh&quot;, etc. When <code>scheme</code> is not provided the output format will be &quot;ssh&quot; but using the scp-like syntax.</li><li><code>username::AbstractString=&quot;&quot;</code>: the username to use in the output if provided.</li><li><code>password::AbstractString=&quot;&quot;</code>: the password to use in the output if provided.</li><li><code>host::AbstractString=&quot;&quot;</code>: the hostname to use in the output. A hostname is required to be specified.</li><li><code>port::Union{AbstractString,Integer}=&quot;&quot;</code>: the port number to use in the output if provided. Cannot be specified when using the scp-like syntax.</li><li><code>path::AbstractString=&quot;&quot;</code>: the path to use in the output if provided.</li></ul><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Avoid using passwords in URLs. Unlike the credential objects, Julia is not able to securely zero or destroy the sensitive data after use and the password may remain in memory; possibly to be exposed by an uninitialized memory.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; LibGit2.git_url(username=&quot;git&quot;, host=&quot;github.com&quot;, path=&quot;JuliaLang/julia.git&quot;)
&quot;**************:JuliaLang/julia.git&quot;

julia&gt; LibGit2.git_url(scheme=&quot;https&quot;, host=&quot;github.com&quot;, path=&quot;/JuliaLang/julia.git&quot;)
&quot;https://github.com/JuliaLang/julia.git&quot;

julia&gt; LibGit2.git_url(scheme=&quot;ssh&quot;, username=&quot;git&quot;, host=&quot;github.com&quot;, port=2222, path=&quot;JuliaLang/julia.git&quot;)
&quot;ssh://**************:2222/JuliaLang/julia.git&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/utils.jl#L95-L130">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.@githash_str" href="#LibGit2.@githash_str"><code>LibGit2.@githash_str</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@githash_str -&gt; AbstractGitHash</code></pre><p>Construct a git hash object from the given string, returning a <code>GitShortHash</code> if the string is shorter than 40 hexadecimal digits, otherwise a <code>GitHash</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; LibGit2.githash&quot;d114feb74ce633&quot;
GitShortHash(&quot;d114feb74ce633&quot;)

julia&gt; LibGit2.githash&quot;d114feb74ce63307afe878a5228ad014e0289a85&quot;
GitHash(&quot;d114feb74ce63307afe878a5228ad014e0289a85&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/oid.jl#L79-L93">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.head" href="#LibGit2.head"><code>LibGit2.head</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.head(repo::GitRepo) -&gt; GitReference</code></pre><p>Return a <code>GitReference</code> to the current HEAD of <code>repo</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/reference.jl#L36-L40">source</a></section><section><div><pre><code class="language-julia hljs">head(pkg::AbstractString) -&gt; String</code></pre><p>Return current HEAD <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> of the <code>pkg</code> repo as a string.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L59-L64">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.head!" href="#LibGit2.head!"><code>LibGit2.head!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.head!(repo::GitRepo, ref::GitReference) -&gt; GitReference</code></pre><p>Set the HEAD of <code>repo</code> to the object pointed to by <code>ref</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/reference.jl#L254-L258">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.head_oid" href="#LibGit2.head_oid"><code>LibGit2.head_oid</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.head_oid(repo::GitRepo) -&gt; GitHash</code></pre><p>Lookup the object id of the current HEAD of git repository <code>repo</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L54-L59">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.headname" href="#LibGit2.headname"><code>LibGit2.headname</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.headname(repo::GitRepo)</code></pre><p>Lookup the name of the current HEAD of git repository <code>repo</code>. If <code>repo</code> is currently detached, return the name of the HEAD it&#39;s detached from.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L69-L76">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.init" href="#LibGit2.init"><code>LibGit2.init</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.init(path::AbstractString, bare::Bool=false) -&gt; GitRepo</code></pre><p>Open a new git repository at <code>path</code>. If <code>bare</code> is <code>false</code>, the working tree will be created in <code>path/.git</code>. If <code>bare</code> is <code>true</code>, no working directory will be created.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L39-L45">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.is_ancestor_of" href="#LibGit2.is_ancestor_of"><code>LibGit2.is_ancestor_of</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">is_ancestor_of(a::AbstractString, b::AbstractString, repo::GitRepo) -&gt; Bool</code></pre><p>Return <code>true</code> if <code>a</code>, a <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> in string form, is an ancestor of <code>b</code>, a <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> in string form.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; repo = GitRepo(repo_path);

julia&gt; LibGit2.add!(repo, test_file1);

julia&gt; commit_oid1 = LibGit2.commit(repo, &quot;commit1&quot;);

julia&gt; LibGit2.add!(repo, test_file2);

julia&gt; commit_oid2 = LibGit2.commit(repo, &quot;commit2&quot;);

julia&gt; LibGit2.is_ancestor_of(string(commit_oid1), string(commit_oid2), repo)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L227-L248">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.isbinary" href="#LibGit2.isbinary"><code>LibGit2.isbinary</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isbinary(blob::GitBlob) -&gt; Bool</code></pre><p>Use a heuristic to guess if a file is binary: searching for NULL bytes and looking for a reasonable ratio of printable to non-printable characters among the first 8000 bytes.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/blob.jl#L41-L47">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.iscommit" href="#LibGit2.iscommit"><code>LibGit2.iscommit</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">iscommit(id::AbstractString, repo::GitRepo) -&gt; Bool</code></pre><p>Check if commit <code>id</code> (which is a <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> in string form) is in the repository.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; repo = GitRepo(repo_path);

julia&gt; LibGit2.add!(repo, test_file);

julia&gt; commit_oid = LibGit2.commit(repo, &quot;add test_file&quot;);

julia&gt; LibGit2.iscommit(string(commit_oid), repo)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L84-L101">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.isdiff" href="#LibGit2.isdiff"><code>LibGit2.isdiff</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.isdiff(repo::GitRepo, treeish::AbstractString, pathspecs::AbstractString=&quot;&quot;; cached::Bool=false)</code></pre><p>Checks if there are any differences between the tree specified by <code>treeish</code> and the tracked files in the working tree (if <code>cached=false</code>) or the index (if <code>cached=true</code>). <code>pathspecs</code> are the specifications for options for the diff.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">repo = LibGit2.GitRepo(repo_path)
LibGit2.isdiff(repo, &quot;HEAD&quot;) # should be false
open(joinpath(repo_path, new_file), &quot;a&quot;) do f
    println(f, &quot;here&#39;s my cool new file&quot;)
end
LibGit2.isdiff(repo, &quot;HEAD&quot;) # now true</code></pre><p>Equivalent to <code>git diff-index &lt;treeish&gt; [-- &lt;pathspecs&gt;]</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L140-L158">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.isdirty" href="#LibGit2.isdirty"><code>LibGit2.isdirty</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.isdirty(repo::GitRepo, pathspecs::AbstractString=&quot;&quot;; cached::Bool=false) -&gt; Bool</code></pre><p>Check if there have been any changes to tracked files in the working tree (if <code>cached=false</code>) or the index (if <code>cached=true</code>). <code>pathspecs</code> are the specifications for options for the diff.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">repo = LibGit2.GitRepo(repo_path)
LibGit2.isdirty(repo) # should be false
open(joinpath(repo_path, new_file), &quot;a&quot;) do f
    println(f, &quot;here&#39;s my cool new file&quot;)
end
LibGit2.isdirty(repo) # now true
LibGit2.isdirty(repo, new_file) # now true</code></pre><p>Equivalent to <code>git diff-index HEAD [-- &lt;pathspecs&gt;]</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L117-L136">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.isorphan" href="#LibGit2.isorphan"><code>LibGit2.isorphan</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.isorphan(repo::GitRepo)</code></pre><p>Check if the current branch is an &quot;orphan&quot; branch, i.e. has no commits. The first commit to this branch will have no parents.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/reference.jl#L23-L28">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.isset" href="#LibGit2.isset"><code>LibGit2.isset</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isset(val::Integer, flag::Integer)</code></pre><p>Test whether the bits of <code>val</code> indexed by <code>flag</code> are set (<code>1</code>) or unset (<code>0</code>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/utils.jl#L46-L50">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.iszero" href="#LibGit2.iszero"><code>LibGit2.iszero</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">iszero(id::GitHash) -&gt; Bool</code></pre><p>Determine whether all hexadecimal digits of the given <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> are zero.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/oid.jl#L209-L213">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.lookup_branch" href="#LibGit2.lookup_branch"><code>LibGit2.lookup_branch</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lookup_branch(repo::GitRepo, branch_name::AbstractString, remote::Bool=false) -&gt; Union{GitReference, Nothing}</code></pre><p>Determine if the branch specified by <code>branch_name</code> exists in the repository <code>repo</code>. If <code>remote</code> is <code>true</code>, <code>repo</code> is assumed to be a remote git repository. Otherwise, it is part of the local filesystem.</p><p>Return either a <code>GitReference</code> to the requested branch if it exists, or <a href="../base/constants.html#Core.nothing"><code>nothing</code></a> if not.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/reference.jl#L267-L276">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.map" href="#LibGit2.map"><code>LibGit2.map</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.map(f::Function, walker::GitRevWalker; oid::GitHash=GitHash(), range::AbstractString=&quot;&quot;, by::Cint=Consts.SORT_NONE, rev::Bool=false)</code></pre><p>Using the <a href="LibGit2.html#LibGit2.GitRevWalker"><code>GitRevWalker</code></a> <code>walker</code> to &quot;walk&quot; over every commit in the repository&#39;s history, apply <code>f</code> to each commit in the walk. The keyword arguments are:     * <code>oid</code>: The <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> of the commit to begin the walk from. The default is to use       <a href="LibGit2.html#LibGit2.push_head!"><code>push_head!</code></a> and therefore the HEAD commit and all its ancestors.     * <code>range</code>: A range of <code>GitHash</code>s in the format <code>oid1..oid2</code>. <code>f</code> will be       applied to all commits between the two.     * <code>by</code>: The sorting method. The default is not to sort. Other options are to sort by       topology (<code>LibGit2.Consts.SORT_TOPOLOGICAL</code>), to sort forwards in time       (<code>LibGit2.Consts.SORT_TIME</code>, most ancient first) or to sort backwards in time       (<code>LibGit2.Consts.SORT_REVERSE</code>, most recent first).     * <code>rev</code>: Whether to reverse the sorted order (for instance, if topological sorting is used).</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">oids = LibGit2.with(LibGit2.GitRevWalker(repo)) do walker
    LibGit2.map((oid, repo)-&gt;string(oid), walker, by=LibGit2.Consts.SORT_TIME)
end</code></pre><p>Here, <code>LibGit2.map</code> visits each commit using the <code>GitRevWalker</code> and finds its <code>GitHash</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/walker.jl#L86-L108">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.mirror_callback" href="#LibGit2.mirror_callback"><code>LibGit2.mirror_callback</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>Mirror callback function</p><p>Function sets <code>+refs/*:refs/*</code> refspecs and <code>mirror</code> flag for remote reference.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/callbacks.jl#L3-L7">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.mirror_cb" href="#LibGit2.mirror_cb"><code>LibGit2.mirror_cb</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>C function pointer for <code>mirror_callback</code></p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/callbacks.jl#L511">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.message" href="#LibGit2.message"><code>LibGit2.message</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">message(c::GitCommit, raw::Bool=false)</code></pre><p>Return the commit message describing the changes made in commit <code>c</code>. If <code>raw</code> is <code>false</code>, return a slightly &quot;cleaned up&quot; message (which has any leading newlines removed). If <code>raw</code> is <code>true</code>, the message is not stripped of any such newlines.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/commit.jl#L5-L12">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.merge_analysis" href="#LibGit2.merge_analysis"><code>LibGit2.merge_analysis</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">merge_analysis(repo::GitRepo, anns::Vector{GitAnnotated}) -&gt; analysis, preference</code></pre><p>Run analysis on the branches pointed to by the annotated branch tips <code>anns</code> and determine under what circumstances they can be merged. For instance, if <code>anns[1]</code> is simply an ancestor of <code>ann[2]</code>, then <code>merge_analysis</code> will report that a fast-forward merge is possible.</p><p>Return two outputs, <code>analysis</code> and <code>preference</code>. <code>analysis</code> has several possible values:     * <code>MERGE_ANALYSIS_NONE</code>: it is not possible to merge the elements of <code>anns</code>.     * <code>MERGE_ANALYSIS_NORMAL</code>: a regular merge, when HEAD and the commits that the       user wishes to merge have all diverged from a common ancestor. In this case the       changes have to be resolved and conflicts may occur.     * <code>MERGE_ANALYSIS_UP_TO_DATE</code>: all the input commits the user wishes to merge can       be reached from HEAD, so no merge needs to be performed.     * <code>MERGE_ANALYSIS_FASTFORWARD</code>: the input commit is a descendant of HEAD and so no       merge needs to be performed - instead, the user can simply checkout the       input commit(s).     * <code>MERGE_ANALYSIS_UNBORN</code>: the HEAD of the repository refers to a commit which does not       exist. It is not possible to merge, but it may be possible to checkout the input       commits. <code>preference</code> also has several possible values:     * <code>MERGE_PREFERENCE_NONE</code>: the user has no preference.     * <code>MERGE_PREFERENCE_NO_FASTFORWARD</code>: do not allow any fast-forward merges.     * <code>MERGE_PREFERENCE_FASTFORWARD_ONLY</code>: allow only fast-forward merges and no       other type (which may introduce conflicts). <code>preference</code> can be controlled through the repository or global git configuration.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/merge.jl#L57-L84">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.name" href="#LibGit2.name"><code>LibGit2.name</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.name(ref::GitReference)</code></pre><p>Return the full name of <code>ref</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/reference.jl#L110-L114">source</a></section><section><div><pre><code class="language-julia hljs">name(rmt::GitRemote)</code></pre><p>Get the name of a remote repository, for instance <code>&quot;origin&quot;</code>. If the remote is anonymous (see <a href="LibGit2.html#LibGit2.GitRemoteAnon"><code>GitRemoteAnon</code></a>) the name will be an empty string <code>&quot;&quot;</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; repo_url = &quot;https://github.com/JuliaLang/Example.jl&quot;;

julia&gt; repo = LibGit2.clone(cache_repo, &quot;test_directory&quot;);

julia&gt; remote = LibGit2.GitRemote(repo, &quot;origin&quot;, repo_url);

julia&gt; name(remote)
&quot;origin&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/remote.jl#L165-L183">source</a></section><section><div><pre><code class="language-julia hljs">LibGit2.name(tag::GitTag)</code></pre><p>The name of <code>tag</code> (e.g. <code>&quot;v0.5&quot;</code>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/tag.jl#L57-L61">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.need_update" href="#LibGit2.need_update"><code>LibGit2.need_update</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">need_update(repo::GitRepo)</code></pre><p>Equivalent to <code>git update-index</code>. Return <code>true</code> if <code>repo</code> needs updating.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L71-L76">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.objtype" href="#LibGit2.objtype"><code>LibGit2.objtype</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">objtype(obj_type::Consts.OBJECT)</code></pre><p>Return the type corresponding to the enum value.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L1209-L1213">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.path" href="#LibGit2.path"><code>LibGit2.path</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.path(repo::GitRepo)</code></pre><p>Return the base file path of the repository <code>repo</code>.</p><ul><li>for normal repositories, this will typically be the parent directory of the &quot;.git&quot; directory (note: this may be different than the working directory, see <code>workdir</code> for more details).</li><li>for bare repositories, this is the location of the &quot;git&quot; files.</li></ul><p>See also <a href="LibGit2.html#LibGit2.gitdir"><code>gitdir</code></a>, <a href="LibGit2.html#LibGit2.workdir"><code>workdir</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L220-L231">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.peel" href="#LibGit2.peel"><code>LibGit2.peel</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">peel([T,] ref::GitReference)</code></pre><p>Recursively peel <code>ref</code> until an object of type <code>T</code> is obtained. If no <code>T</code> is provided, then <code>ref</code> will be peeled until an object other than a <a href="LibGit2.html#LibGit2.GitTag"><code>GitTag</code></a> is obtained.</p><ul><li>A <code>GitTag</code> will be peeled to the object it references.</li><li>A <a href="LibGit2.html#LibGit2.GitCommit"><code>GitCommit</code></a> will be peeled to a <a href="LibGit2.html#LibGit2.GitTree"><code>GitTree</code></a>.</li></ul><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Only annotated tags can be peeled to <code>GitTag</code> objects. Lightweight tags (the default) are references under <code>refs/tags/</code> which point directly to <code>GitCommit</code> objects.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/reference.jl#L186-L198">source</a></section><section><div><pre><code class="language-julia hljs">peel([T,] obj::GitObject)</code></pre><p>Recursively peel <code>obj</code> until an object of type <code>T</code> is obtained. If no <code>T</code> is provided, then <code>obj</code> will be peeled until the type changes.</p><ul><li>A <code>GitTag</code> will be peeled to the object it references.</li><li>A <code>GitCommit</code> will be peeled to a <code>GitTree</code>.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L245-L253">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.posixpath" href="#LibGit2.posixpath"><code>LibGit2.posixpath</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.posixpath(path)</code></pre><p>Standardise the path string <code>path</code> to use POSIX separators.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/utils.jl#L83-L87">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.push" href="#LibGit2.push"><code>LibGit2.push</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">push(rmt::GitRemote, refspecs; force::Bool=false, options::PushOptions=PushOptions())</code></pre><p>Push to the specified <code>rmt</code> remote git repository, using <code>refspecs</code> to determine which remote branch(es) to push to. The keyword arguments are:</p><ul><li><code>force</code>: if <code>true</code>, a force-push will occur, disregarding conflicts.</li><li><code>options</code>: determines the options for the push, e.g. which proxy headers to use. See <a href="LibGit2.html#LibGit2.PushOptions"><code>PushOptions</code></a> for more information.</li></ul><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>You can add information about the push refspecs in two other ways: by setting an option in the repository&#39;s <code>GitConfig</code> (with <code>push.default</code> as the key) or by calling <a href="LibGit2.html#LibGit2.add_push!"><code>add_push!</code></a>. Otherwise you will need to explicitly specify a push refspec in the call to <code>push</code> for it to have any effect, like so: <code>LibGit2.push(repo, refspecs=[&quot;refs/heads/master&quot;])</code>.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/remote.jl#L317-L333">source</a></section><section><div><pre><code class="language-julia hljs">push(repo::GitRepo; kwargs...)</code></pre><p>Pushes updates to an upstream of <code>repo</code>.</p><p>The keyword arguments are:</p><ul><li><code>remote::AbstractString=&quot;origin&quot;</code>: the name of the upstream remote to push to.</li><li><code>remoteurl::AbstractString=&quot;&quot;</code>: the URL of <code>remote</code>.</li><li><code>refspecs=AbstractString[]</code>: determines properties of the push.</li><li><code>force::Bool=false</code>: determines if the push will be a force push,  overwriting the remote branch.</li><li><code>credentials=nothing</code>: provides credentials and/or settings when authenticating against  a private <code>remote</code>.</li><li><code>callbacks=Callbacks()</code>: user provided callbacks and payloads.</li></ul><p>Equivalent to <code>git push [&lt;remoteurl&gt;|&lt;repo&gt;] [&lt;refspecs&gt;]</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L310-L326">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.push!-Tuple{LibGit2.GitRevWalker, LibGit2.GitHash}" href="#LibGit2.push!-Tuple{LibGit2.GitRevWalker, LibGit2.GitHash}"><code>LibGit2.push!</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.push!(w::GitRevWalker, cid::GitHash)</code></pre><p>Start the <a href="LibGit2.html#LibGit2.GitRevWalker"><code>GitRevWalker</code></a> <code>walker</code> at commit <code>cid</code>. This function can be used to apply a function to all commits since a certain year, by passing the first commit of that year as <code>cid</code> and then passing the resulting <code>w</code> to <a href="LibGit2.html#LibGit2.map"><code>LibGit2.map</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/walker.jl#L58-L64">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.push_head!" href="#LibGit2.push_head!"><code>LibGit2.push_head!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.push_head!(w::GitRevWalker)</code></pre><p>Push the HEAD commit and its ancestors onto the <a href="LibGit2.html#LibGit2.GitRevWalker"><code>GitRevWalker</code></a> <code>w</code>. This ensures that HEAD and all its ancestor commits will be encountered during the walk.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/walker.jl#L45-L51">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.push_refspecs" href="#LibGit2.push_refspecs"><code>LibGit2.push_refspecs</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">push_refspecs(rmt::GitRemote) -&gt; Vector{String}</code></pre><p>Get the <em>push</em> refspecs for the specified <code>rmt</code>. These refspecs contain information about which branch(es) to push to.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; remote = LibGit2.get(LibGit2.GitRemote, repo, &quot;upstream&quot;);

julia&gt; LibGit2.add_push!(repo, remote, &quot;refs/heads/master&quot;);

julia&gt; close(remote);

julia&gt; remote = LibGit2.get(LibGit2.GitRemote, repo, &quot;upstream&quot;);

julia&gt; LibGit2.push_refspecs(remote)
String[&quot;refs/heads/master&quot;]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/remote.jl#L217-L236">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.raw" href="#LibGit2.raw"><code>LibGit2.raw</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">raw(id::GitHash) -&gt; Vector{UInt8}</code></pre><p>Obtain the raw bytes of the <a href="LibGit2.html#LibGit2.GitHash"><code>GitHash</code></a> as a vector of length 20.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/oid.jl#L169-L173">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.read_tree!" href="#LibGit2.read_tree!"><code>LibGit2.read_tree!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.read_tree!(idx::GitIndex, tree::GitTree)
LibGit2.read_tree!(idx::GitIndex, treehash::AbstractGitHash)</code></pre><p>Read the tree <code>tree</code> (or the tree pointed to by <code>treehash</code> in the repository owned by <code>idx</code>) into the index <code>idx</code>. The current index contents will be replaced.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/index.jl#L67-L73">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.rebase!" href="#LibGit2.rebase!"><code>LibGit2.rebase!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.rebase!(repo::GitRepo, upstream::AbstractString=&quot;&quot;, newbase::AbstractString=&quot;&quot;)</code></pre><p>Attempt an automatic merge rebase of the current branch, from <code>upstream</code> if provided, or otherwise from the upstream tracking branch. <code>newbase</code> is the branch to rebase onto. By default this is <code>upstream</code>.</p><p>If any conflicts arise which cannot be automatically resolved, the rebase will abort, leaving the repository and working tree in its original state, and the function will throw a <code>GitError</code>. This is roughly equivalent to the following command line statement:</p><pre><code class="nohighlight hljs">git rebase --merge [&lt;upstream&gt;]
if [ -d &quot;.git/rebase-merge&quot; ]; then
    git rebase --abort
fi</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L835-L851">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.ref_list" href="#LibGit2.ref_list"><code>LibGit2.ref_list</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.ref_list(repo::GitRepo) -&gt; Vector{String}</code></pre><p>Get a list of all reference names in the <code>repo</code> repository.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/reference.jl#L208-L212">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.reftype" href="#LibGit2.reftype"><code>LibGit2.reftype</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.reftype(ref::GitReference) -&gt; Cint</code></pre><p>Return a <code>Cint</code> corresponding to the type of <code>ref</code>:</p><ul><li><code>0</code> if the reference is invalid</li><li><code>1</code> if the reference is an object id</li><li><code>2</code> if the reference is symbolic</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/reference.jl#L78-L85">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.remotes" href="#LibGit2.remotes"><code>LibGit2.remotes</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.remotes(repo::GitRepo)</code></pre><p>Return a vector of the names of the remotes of <code>repo</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L499-L503">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.remove!" href="#LibGit2.remove!"><code>LibGit2.remove!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">remove!(repo::GitRepo, files::AbstractString...)
remove!(idx::GitIndex, files::AbstractString...)</code></pre><p>Remove all the files with paths specified by <code>files</code> in the index <code>idx</code> (or the index of the <code>repo</code>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/index.jl#L128-L134">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.reset" href="#LibGit2.reset"><code>LibGit2.reset</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">reset(val::Integer, flag::Integer)</code></pre><p>Unset the bits of <code>val</code> indexed by <code>flag</code>, returning them to <code>0</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/utils.jl#L53-L57">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.reset!" href="#LibGit2.reset!"><code>LibGit2.reset!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">reset!(payload, [config]) -&gt; CredentialPayload</code></pre><p>Reset the <code>payload</code> state back to the initial values so that it can be used again within the credential callback. If a <code>config</code> is provided the configuration will also be updated.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L1404-L1409">source</a></section><section><div><p>Updates some entries, determined by the <code>pathspecs</code>, in the index from the target commit tree.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L418">source</a></section><section><div><p>Sets the current head to the specified commit oid and optionally resets the index and working tree to match.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L430">source</a></section><section><div><p>git reset [&lt;committish&gt;] [–] &lt;pathspecs&gt;... </p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L637">source</a></section><section><div><pre><code class="language-julia hljs">reset!(repo::GitRepo, id::GitHash, mode::Cint=Consts.RESET_MIXED)</code></pre><p>Reset the repository <code>repo</code> to its state at <code>id</code>, using one of three modes set by <code>mode</code>:</p><ol><li><code>Consts.RESET_SOFT</code> - move HEAD to <code>id</code>.</li><li><code>Consts.RESET_MIXED</code> - default, move HEAD to <code>id</code> and reset the index to <code>id</code>.</li><li><code>Consts.RESET_HARD</code> - move HEAD to <code>id</code>, reset the index to <code>id</code>, and discard all working changes.</li></ol><p><strong>Examples</strong></p><pre><code class="language-julia hljs"># fetch changes
LibGit2.fetch(repo)
isfile(joinpath(repo_path, our_file)) # will be false

# fastforward merge the changes
LibGit2.merge!(repo, fastforward=true)

# because there was not any file locally, but there is
# a file remotely, we need to reset the branch
head_oid = LibGit2.head_oid(repo)
new_head = LibGit2.reset!(repo, head_oid, LibGit2.Consts.RESET_HARD)</code></pre><p>In this example, the remote which is being fetched from <em>does</em> have a file called <code>our_file</code> in its index, which is why we must reset.</p><p>Equivalent to <code>git reset [--soft | --mixed | --hard] &lt;id&gt;</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">repo = LibGit2.GitRepo(repo_path)
head_oid = LibGit2.head_oid(repo)
open(joinpath(repo_path, &quot;file1&quot;), &quot;w&quot;) do f
    write(f, &quot;111
&quot;)
end
LibGit2.add!(repo, &quot;file1&quot;)
mode = LibGit2.Consts.RESET_HARD
# will discard the changes to file1
# and unstage it
new_head = LibGit2.reset!(repo, head_oid, mode)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L644-L686">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.restore" href="#LibGit2.restore"><code>LibGit2.restore</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">restore(s::State, repo::GitRepo)</code></pre><p>Return a repository <code>repo</code> to a previous <code>State</code> <code>s</code>, for example the HEAD of a branch before a merge attempt. <code>s</code> can be generated using the <a href="LibGit2.html#LibGit2.snapshot"><code>snapshot</code></a> function.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L963-L969">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.revcount" href="#LibGit2.revcount"><code>LibGit2.revcount</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.revcount(repo::GitRepo, commit1::AbstractString, commit2::AbstractString)</code></pre><p>List the number of revisions between <code>commit1</code> and <code>commit2</code> (committish OIDs in string form). Since <code>commit1</code> and <code>commit2</code> may be on different branches, <code>revcount</code> performs a &quot;left-right&quot; revision list (and count), returning a tuple of <code>Int</code>s - the number of left and right commits, respectively. A left (or right) commit refers to which side of a symmetric difference in a tree the commit is reachable from.</p><p>Equivalent to <code>git rev-list --left-right --count &lt;commit1&gt; &lt;commit2&gt;</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">repo = LibGit2.GitRepo(repo_path)
repo_file = open(joinpath(repo_path, test_file), &quot;a&quot;)
println(repo_file, &quot;hello world&quot;)
flush(repo_file)
LibGit2.add!(repo, test_file)
commit_oid1 = LibGit2.commit(repo, &quot;commit 1&quot;)
println(repo_file, &quot;hello world again&quot;)
flush(repo_file)
LibGit2.add!(repo, test_file)
commit_oid2 = LibGit2.commit(repo, &quot;commit 2&quot;)
LibGit2.revcount(repo, string(commit_oid1), string(commit_oid2))</code></pre><p>This will return <code>(-1, 0)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L689-L716">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.set_remote_url" href="#LibGit2.set_remote_url"><code>LibGit2.set_remote_url</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">set_remote_url(repo::GitRepo, remote_name, url)
set_remote_url(repo::String, remote_name, url)</code></pre><p>Set both the fetch and push <code>url</code> for <code>remote_name</code> for the <a href="LibGit2.html#LibGit2.GitRepo"><code>GitRepo</code></a> or the git repository located at <code>path</code>. Typically git repos use <code>&quot;origin&quot;</code> as the remote name.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">repo_path = joinpath(tempdir(), &quot;Example&quot;)
repo = LibGit2.init(repo_path)
LibGit2.set_remote_url(repo, &quot;upstream&quot;, &quot;https://github.com/JuliaLang/Example.jl&quot;)
LibGit2.set_remote_url(repo_path, &quot;upstream2&quot;, &quot;https://github.com/JuliaLang/Example2.jl&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/remote.jl#L403-L417">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.shortname" href="#LibGit2.shortname"><code>LibGit2.shortname</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.shortname(ref::GitReference)</code></pre><p>Return a shortened version of the name of <code>ref</code> that&#39;s &quot;human-readable&quot;.</p><pre><code class="language-julia-repl hljs">julia&gt; repo = GitRepo(path_to_repo);

julia&gt; branch_ref = LibGit2.head(repo);

julia&gt; LibGit2.name(branch_ref)
&quot;refs/heads/master&quot;

julia&gt; LibGit2.shortname(branch_ref)
&quot;master&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/reference.jl#L49-L66">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.snapshot" href="#LibGit2.snapshot"><code>LibGit2.snapshot</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">snapshot(repo::GitRepo) -&gt; State</code></pre><p>Take a snapshot of the current state of the repository <code>repo</code>, storing the current HEAD, index, and any uncommitted work. The output <code>State</code> can be used later during a call to <a href="LibGit2.html#LibGit2.restore"><code>restore</code></a> to return the repository to the snapshotted state.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L934-L941">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.split_cfg_entry" href="#LibGit2.split_cfg_entry"><code>LibGit2.split_cfg_entry</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.split_cfg_entry(ce::LibGit2.ConfigEntry) -&gt; Tuple{String,String,String,String}</code></pre><p>Break the <code>ConfigEntry</code> up to the following pieces: section, subsection, name, and value.</p><p><strong>Examples</strong></p><p>Given the git configuration file containing:</p><pre><code class="nohighlight hljs">[credential &quot;https://example.com&quot;]
    username = me</code></pre><p>The <code>ConfigEntry</code> would look like the following:</p><pre><code class="language-julia-repl hljs">julia&gt; entry
ConfigEntry(&quot;credential.https://example.com.username&quot;, &quot;me&quot;)

julia&gt; LibGit2.split_cfg_entry(entry)
(&quot;credential&quot;, &quot;https://example.com&quot;, &quot;username&quot;, &quot;me&quot;)</code></pre><p>Refer to the <a href="https://git-scm.com/docs/git-config#_syntax">git config syntax documentation</a> for more details.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L927-L951">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.status" href="#LibGit2.status"><code>LibGit2.status</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.status(repo::GitRepo, path::String) -&gt; Union{Cuint, Cvoid}</code></pre><p>Lookup the status of the file at <code>path</code> in the git repository <code>repo</code>. For instance, this can be used to check if the file at <code>path</code> has been modified and needs to be staged and committed.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/status.jl#L41-L48">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.stage" href="#LibGit2.stage"><code>LibGit2.stage</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">stage(ie::IndexEntry) -&gt; Cint</code></pre><p>Get the stage number of <code>ie</code>. The stage number <code>0</code> represents the current state of the working tree, but other numbers can be used in the case of a merge conflict. In such a case, the various stage numbers on an <code>IndexEntry</code> describe which side(s) of the conflict the current state of the file belongs to. Stage <code>0</code> is the state before the attempted merge, stage <code>1</code> is the changes which have been made locally, stages <code>2</code> and larger are for changes from other branches (for instance, in the case of a multi-branch &quot;octopus&quot; merge, stages <code>2</code>, <code>3</code>, and <code>4</code> might be used).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/index.jl#L200-L210">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.tag_create" href="#LibGit2.tag_create"><code>LibGit2.tag_create</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.tag_create(repo::GitRepo, tag::AbstractString, commit; kwargs...)</code></pre><p>Create a new git tag <code>tag</code> (e.g. <code>&quot;v0.5&quot;</code>) in the repository <code>repo</code>, at the commit <code>commit</code>.</p><p>The keyword arguments are:</p><ul><li><code>msg::AbstractString=&quot;&quot;</code>: the message for the tag.</li><li><code>force::Bool=false</code>: if <code>true</code>, existing references will be overwritten.</li><li><code>sig::Signature=Signature(repo)</code>: the tagger&#39;s signature.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/tag.jl#L29-L39">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.tag_delete" href="#LibGit2.tag_delete"><code>LibGit2.tag_delete</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.tag_delete(repo::GitRepo, tag::AbstractString)</code></pre><p>Remove the git tag <code>tag</code> from the repository <code>repo</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/tag.jl#L18-L22">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.tag_list" href="#LibGit2.tag_list"><code>LibGit2.tag_list</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.tag_list(repo::GitRepo) -&gt; Vector{String}</code></pre><p>Get a list of all tags in the git repository <code>repo</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/tag.jl#L3-L7">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.target" href="#LibGit2.target"><code>LibGit2.target</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.target(tag::GitTag)</code></pre><p>The <code>GitHash</code> of the target object of <code>tag</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/tag.jl#L73-L77">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.toggle" href="#LibGit2.toggle"><code>LibGit2.toggle</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">toggle(val::Integer, flag::Integer)</code></pre><p>Flip the bits of <code>val</code> indexed by <code>flag</code>, so that if a bit is <code>0</code> it will be <code>1</code> after the toggle, and vice-versa.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/utils.jl#L60-L65">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.transact" href="#LibGit2.transact"><code>LibGit2.transact</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">transact(f::Function, repo::GitRepo)</code></pre><p>Apply function <code>f</code> to the git repository <code>repo</code>, taking a <a href="LibGit2.html#LibGit2.snapshot"><code>snapshot</code></a> before applying <code>f</code>. If an error occurs within <code>f</code>, <code>repo</code> will be returned to its snapshot state using <a href="LibGit2.html#LibGit2.restore"><code>restore</code></a>. The error which occurred will be rethrown, but the state of <code>repo</code> will not be corrupted.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/LibGit2.jl#L984-L991">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.treewalk" href="#LibGit2.treewalk"><code>LibGit2.treewalk</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">treewalk(f, tree::GitTree, post::Bool=false)</code></pre><p>Traverse the entries in <code>tree</code> and its subtrees in post or pre order. Preorder means beginning at the root and then traversing the leftmost subtree (and recursively on down through that subtree&#39;s leftmost subtrees) and moving right through the subtrees. Postorder means beginning at the bottom of the leftmost subtree, traversing upwards through it, then traversing the next right subtree (again beginning at the bottom) and finally visiting the tree root last of all.</p><p>The function parameter <code>f</code> should have following signature:</p><pre><code class="nohighlight hljs">(String, GitTreeEntry) -&gt; Cint</code></pre><p>A negative value returned from <code>f</code> stops the tree walk. A positive value means that the entry will be skipped if <code>post</code> is <code>false</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/tree.jl#L9-L25">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.upstream" href="#LibGit2.upstream"><code>LibGit2.upstream</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">upstream(ref::GitReference) -&gt; Union{GitReference, Nothing}</code></pre><p>Determine if the branch containing <code>ref</code> has a specified upstream branch.</p><p>Return either a <code>GitReference</code> to the upstream branch if it exists, or <a href="../base/constants.html#Core.nothing"><code>nothing</code></a> if the requested branch does not have an upstream counterpart.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/reference.jl#L298-L305">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.update!" href="#LibGit2.update!"><code>LibGit2.update!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">update!(repo::GitRepo, files::AbstractString...)
update!(idx::GitIndex, files::AbstractString...)</code></pre><p>Update all the files with paths specified by <code>files</code> in the index <code>idx</code> (or the index of the <code>repo</code>). Match the state of each file in the index with the current state on disk, removing it if it has been removed on disk, or updating its entry in the object database.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/index.jl#L112-L120">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.url" href="#LibGit2.url"><code>LibGit2.url</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">url(rmt::GitRemote)</code></pre><p>Get the fetch URL of a remote git repository.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; repo_url = &quot;https://github.com/JuliaLang/Example.jl&quot;;

julia&gt; repo = LibGit2.init(mktempdir());

julia&gt; remote = LibGit2.GitRemote(repo, &quot;origin&quot;, repo_url);

julia&gt; LibGit2.url(remote)
&quot;https://github.com/JuliaLang/Example.jl&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/remote.jl#L117-L133">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.version" href="#LibGit2.version"><code>LibGit2.version</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">version() -&gt; VersionNumber</code></pre><p>Return the version of libgit2 in use, as a <a href="../manual/strings.html#man-version-number-literals"><code>VersionNumber</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/utils.jl#L31-L35">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.with" href="#LibGit2.with"><code>LibGit2.with</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">with(f::Function, obj)</code></pre><p>Resource management helper function. Applies <code>f</code> to <code>obj</code>, making sure to call <code>close</code> on <code>obj</code> after <code>f</code> successfully returns or throws an error. Ensures that allocated git resources are finalized as soon as they are no longer needed.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L1157-L1163">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.with_warn" href="#LibGit2.with_warn"><code>LibGit2.with_warn</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">with_warn(f::Function, ::Type{T}, args...)</code></pre><p>Resource management helper function. Apply <code>f</code> to <code>args</code>, first constructing an instance of type <code>T</code> from <code>args</code>. Makes sure to call <code>close</code> on the resulting object after <code>f</code> successfully returns or throws an error. Ensures that allocated git resources are finalized as soon as they are no longer needed. If an error is thrown by <code>f</code>, a warning is shown containing the error.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L1174-L1182">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.workdir" href="#LibGit2.workdir"><code>LibGit2.workdir</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.workdir(repo::GitRepo)</code></pre><p>Return the location of the working directory of <code>repo</code>. This will throw an error for bare repositories.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This will typically be the parent directory of <code>gitdir(repo)</code>, but can be different in some cases: e.g. if either the <code>core.worktree</code> configuration variable or the <code>GIT_WORK_TREE</code> environment variable is set.</p></div></div><p>See also <a href="LibGit2.html#LibGit2.gitdir"><code>gitdir</code></a>, <a href="LibGit2.html#LibGit2.path"><code>path</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/repository.jl#L197-L210">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.GitObject-Tuple{LibGit2.GitTreeEntry}" href="#LibGit2.GitObject-Tuple{LibGit2.GitTreeEntry}"><code>LibGit2.GitObject</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">(::Type{T})(te::GitTreeEntry) where T&lt;:GitObject</code></pre><p>Get the git object to which <code>te</code> refers and return it as its actual type (the type <a href="LibGit2.html#LibGit2.entrytype"><code>entrytype</code></a> would show), for instance a <code>GitBlob</code> or <code>GitTag</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">tree = LibGit2.GitTree(repo, &quot;HEAD^{tree}&quot;)
tree_entry = tree[1]
blob = LibGit2.GitBlob(tree_entry)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/tree.jl#L118-L130">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.UserPasswordCredential" href="#LibGit2.UserPasswordCredential"><code>LibGit2.UserPasswordCredential</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>Credential that support only <code>user</code> and <code>password</code> parameters</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L1239">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.SSHCredential" href="#LibGit2.SSHCredential"><code>LibGit2.SSHCredential</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>SSH credential type</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L1270">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.isfilled" href="#LibGit2.isfilled"><code>LibGit2.isfilled</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isfilled(cred::AbstractCredential) -&gt; Bool</code></pre><p>Verifies that a credential is ready for use in authentication.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L1232-L1236">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.CachedCredentials" href="#LibGit2.CachedCredentials"><code>LibGit2.CachedCredentials</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>Caches credential information for re-use</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L1309">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.CredentialPayload" href="#LibGit2.CredentialPayload"><code>LibGit2.CredentialPayload</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LibGit2.CredentialPayload</code></pre><p>Retains the state between multiple calls to the credential callback for the same URL. A <code>CredentialPayload</code> instance is expected to be <code>reset!</code> whenever it will be used with a different URL.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L1344-L1350">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.approve" href="#LibGit2.approve"><code>LibGit2.approve</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">approve(payload::CredentialPayload; shred::Bool=true) -&gt; Nothing</code></pre><p>Store the <code>payload</code> credential for re-use in a future authentication. Should only be called when authentication was successful.</p><p>The <code>shred</code> keyword controls whether sensitive information in the payload credential field should be destroyed. Should only be set to <code>false</code> during testing.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L1426-L1434">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.reject" href="#LibGit2.reject"><code>LibGit2.reject</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">reject(payload::CredentialPayload; shred::Bool=true) -&gt; Nothing</code></pre><p>Discard the <code>payload</code> credential from begin re-used in future authentication. Should only be called when authentication was unsuccessful.</p><p>The <code>shred</code> keyword controls whether sensitive information in the payload credential field should be destroyed. Should only be set to <code>false</code> during testing.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/types.jl#L1457-L1465">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LibGit2.Consts.GIT_CONFIG" href="#LibGit2.Consts.GIT_CONFIG"><code>LibGit2.Consts.GIT_CONFIG</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>Priority level of a config file.</p><p>These priority levels correspond to the natural escalation logic (from higher to lower) when searching for config entries in git.</p><ul><li><code>CONFIG_LEVEL_DEFAULT</code> - Open the global, XDG and system configuration files if any available.</li><li><code>CONFIG_LEVEL_PROGRAMDATA</code> - System-wide on Windows, for compatibility with portable git</li><li><code>CONFIG_LEVEL_SYSTEM</code> - System-wide configuration file; <code>/etc/gitconfig</code> on Linux systems</li><li><code>CONFIG_LEVEL_XDG</code> - XDG compatible configuration file; typically <code>~/.config/git/config</code></li><li><code>CONFIG_LEVEL_GLOBAL</code> - User-specific configuration file (also called Global configuration file); typically <code>~/.gitconfig</code></li><li><code>CONFIG_LEVEL_LOCAL</code> - Repository specific configuration file; <code>$WORK_DIR/.git/config</code> on non-bare repos</li><li><code>CONFIG_LEVEL_APP</code> - Application specific configuration file; freely defined by applications</li><li><code>CONFIG_HIGHEST_LEVEL</code> - Represents the highest level available config file (i.e. the most specific config file available that actually is loaded)</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LibGit2/src/consts.jl#L421-L434">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="LibCURL.html">« LibCURL</a><a class="docs-footer-nextpage" href="Libdl.html">Dynamic Linker »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
