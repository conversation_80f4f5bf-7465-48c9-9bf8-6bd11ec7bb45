<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Proper maintenance and care of multi-threading locks · The Julia Language</title><meta name="title" content="Proper maintenance and care of multi-threading locks · The Julia Language"/><meta property="og:title" content="Proper maintenance and care of multi-threading locks · The Julia Language"/><meta property="twitter:title" content="Proper maintenance and care of multi-threading locks · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox" checked/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="ast.html">Julia ASTs</a></li><li><a class="tocitem" href="types.html">More about types</a></li><li><a class="tocitem" href="object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="functions.html">Julia Functions</a></li><li><a class="tocitem" href="cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="subarrays.html">SubArrays</a></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li class="is-active"><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a><ul class="internal"><li><a class="tocitem" href="#Locks"><span>Locks</span></a></li><li><a class="tocitem" href="#Broken-Locks"><span>Broken Locks</span></a></li><li><a class="tocitem" href="#Shared-Global-Data-Structures"><span>Shared Global Data Structures</span></a></li></ul></li><li><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Documentation of Julia&#39;s Internals</a></li><li class="is-active"><a href="locks.html">Proper maintenance and care of multi-threading locks</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="locks.html">Proper maintenance and care of multi-threading locks</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/locks.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Proper-maintenance-and-care-of-multi-threading-locks"><a class="docs-heading-anchor" href="#Proper-maintenance-and-care-of-multi-threading-locks">Proper maintenance and care of multi-threading locks</a><a id="Proper-maintenance-and-care-of-multi-threading-locks-1"></a><a class="docs-heading-anchor-permalink" href="#Proper-maintenance-and-care-of-multi-threading-locks" title="Permalink"></a></h1><p>The following strategies are used to ensure that the code is dead-lock free (generally by addressing the 4th Coffman condition: circular wait).</p><blockquote><ol><li>structure code such that only one lock will need to be acquired at a time</li><li>always acquire shared locks in the same order, as given by the table below</li><li>avoid constructs that expect to need unrestricted recursion</li></ol></blockquote><h2 id="Locks"><a class="docs-heading-anchor" href="#Locks">Locks</a><a id="Locks-1"></a><a class="docs-heading-anchor-permalink" href="#Locks" title="Permalink"></a></h2><p>Below are all of the locks that exist in the system and the mechanisms for using them that avoid the potential for deadlocks (no Ostrich algorithm allowed here):</p><p>The following are definitely leaf locks (level 1), and must not try to acquire any other lock:</p><blockquote><ul><li><p>safepoint</p><blockquote><p>Note that this lock is acquired implicitly by <code>JL_LOCK</code> and <code>JL_UNLOCK</code>. use the <code>_NOGC</code> variants to avoid that for level 1 locks.</p><p>While holding this lock, the code must not do any allocation or hit any safepoints. Note that there are safepoints when doing allocation, enabling / disabling GC, entering / restoring exception frames, and taking / releasing locks.</p></blockquote></li><li><p>shared_map</p></li><li><p>finalizers</p></li><li><p>pagealloc</p></li><li><p>gc<em>perm</em>lock</p></li><li><p>flisp</p></li><li><p>jl<em>in</em>stackwalk (Win32)</p></li><li><p>ResourcePool&lt;?&gt;::mutex</p></li><li><p>RLST_mutex</p></li><li><p>llvm<em>printing</em>mutex</p></li><li><p>jl<em>locked</em>stream::mutex</p></li><li><p>debuginfo_asyncsafe</p></li><li><p>inference<em>timing</em>mutex</p></li><li><p>ExecutionEngine::SessionLock</p><blockquote><p>flisp itself is already threadsafe, this lock only protects the <code>jl_ast_context_list_t</code> pool likewise, the ResourcePool&lt;?&gt;::mutexes just protect the associated resource pool</p></blockquote></li></ul></blockquote><p>The following is a leaf lock (level 2), and only acquires level 1 locks (safepoint) internally:</p><blockquote><ul><li>global<em>roots</em>lock</li><li>Module-&gt;lock</li><li>JLDebuginfoPlugin::PluginMutex</li><li>newly<em>inferred</em>mutex</li></ul></blockquote><p>The following is a level 3 lock, which can only acquire level 1 or level 2 locks internally:</p><blockquote><ul><li>Method-&gt;writelock</li><li>typecache</li></ul></blockquote><p>The following is a level 4 lock, which can only recurse to acquire level 1, 2, or 3 locks:</p><blockquote><ul><li>MethodTable-&gt;writelock</li></ul></blockquote><p>No Julia code may be called while holding a lock above this point.</p><p>orc::ThreadSafeContext (TSCtx) locks occupy a special spot in the locking hierarchy. They are used to protect LLVM&#39;s global non-threadsafe state, but there may be an arbitrary number of them. By default, all of these locks may be treated as level 5 locks for the purposes of comparing with the rest of the hierarchy. Acquiring a TSCtx should only be done from the JIT&#39;s pool of TSCtx&#39;s, and all locks on that TSCtx should be released prior to returning it to the pool. If multiple TSCtx locks must be acquired at the same time (due to recursive compilation), then locks should be acquired in the order that the TSCtxs were borrowed from the pool.</p><p>The following is a level 5 lock</p><blockquote><ul><li>JuliaOJIT::EmissionMutex</li></ul></blockquote><p>The following are a level 6 lock, which can only recurse to acquire locks at lower levels:</p><blockquote><ul><li>codegen</li><li>jl<em>modules</em>mutex</li></ul></blockquote><p>The following is an almost root lock (level end-1), meaning only the root look may be held when trying to acquire it:</p><blockquote><ul><li><p>typeinf</p><blockquote><p>this one is perhaps one of the most tricky ones, since type-inference can be invoked from many points</p><p>currently the lock is merged with the codegen lock, since they call each other recursively</p></blockquote></li></ul></blockquote><p>The following lock synchronizes IO operation. Be aware that doing any I/O (for example, printing warning messages or debug information) while holding any other lock listed above may result in pernicious and hard-to-find deadlocks. BE VERY CAREFUL!</p><blockquote><ul><li><p>iolock</p></li><li><p>Individual ThreadSynchronizers locks</p><blockquote><p>this may continue to be held after releasing the iolock, or acquired without it, but be very careful to never attempt to acquire the iolock while holding it</p></blockquote></li><li><p>Libdl.LazyLibrary lock</p></li></ul></blockquote><p>The following is the root lock, meaning no other lock shall be held when trying to acquire it:</p><blockquote><ul><li><p>toplevel</p><blockquote><p>this should be held while attempting a top-level action (such as making a new type or defining a new method): trying to obtain this lock inside a staged function will cause a deadlock condition!</p><p>additionally, it&#39;s unclear if <em>any</em> code can safely run in parallel with an arbitrary toplevel expression, so it may require all threads to get to a safepoint first</p></blockquote></li></ul></blockquote><h2 id="Broken-Locks"><a class="docs-heading-anchor" href="#Broken-Locks">Broken Locks</a><a id="Broken-Locks-1"></a><a class="docs-heading-anchor-permalink" href="#Broken-Locks" title="Permalink"></a></h2><p>The following locks are broken:</p><ul><li><p>toplevel</p><blockquote><p>doesn&#39;t exist right now</p><p>fix: create it</p></blockquote></li><li><p>Module-&gt;lock</p><blockquote><p>This is vulnerable to deadlocks since it can&#39;t be certain it is acquired in sequence. Some operations (such as <code>import_module</code>) are missing a lock.</p><p>fix: replace with <code>jl_modules_mutex</code>?</p></blockquote></li><li><p>loading.jl: <code>require</code> and <code>register_root_module</code></p><blockquote><p>This file potentially has numerous problems.</p><p>fix: needs locks</p></blockquote></li></ul><h2 id="Shared-Global-Data-Structures"><a class="docs-heading-anchor" href="#Shared-Global-Data-Structures">Shared Global Data Structures</a><a id="Shared-Global-Data-Structures-1"></a><a class="docs-heading-anchor-permalink" href="#Shared-Global-Data-Structures" title="Permalink"></a></h2><p>These data structures each need locks due to being shared mutable global state. It is the inverse list for the above lock priority list. This list does not include level 1 leaf resources due to their simplicity.</p><p>MethodTable modifications (def, cache) : MethodTable-&gt;writelock</p><p>Type declarations : toplevel lock</p><p>Type application : typecache lock</p><p>Global variable tables : Module-&gt;lock</p><p>Module serializer : toplevel lock</p><p>JIT &amp; type-inference : codegen lock</p><p>MethodInstance/CodeInstance updates : Method-&gt;writelock, codegen lock</p><blockquote><ul><li>These are set at construction and immutable:<ul><li>specTypes</li><li>sparam_vals</li><li>def</li><li>owner</li></ul></li></ul></blockquote><blockquote><ul><li>These are set by <code>jl_type_infer</code> (while holding codegen lock):<ul><li>cache</li><li>rettype</li><li>inferred</li></ul></li></ul></blockquote><pre><code class="nohighlight hljs">    * valid ages</code></pre><blockquote><ul><li><code>inInference</code> flag:<ul><li>optimization to quickly avoid recurring into <code>jl_type_infer</code> while it is already running</li><li>actual state (of setting <code>inferred</code>, then <code>fptr</code>) is protected by codegen lock</li></ul></li></ul></blockquote><blockquote><ul><li><p>Function pointers:</p><ul><li>these transition once, from <code>NULL</code> to a value, while the codegen lock is held</li></ul></li><li><p>Code-generator cache (the contents of <code>functionObjectsDecls</code>):</p><ul><li>these can transition multiple times, but only while the codegen lock is held</li><li>it is valid to use old version of this, or block for new versions of this, so races are benign, as long as the code is careful not to reference other data in the method instance (such as <code>rettype</code>) and assume it is coordinated, unless also holding the codegen lock</li></ul></li></ul></blockquote><p>LLVMContext : codegen lock</p><p>Method : Method-&gt;writelock</p><ul><li>roots array (serializer and codegen)</li><li>invoke / specializations / tfunc modifications</li></ul></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="boundscheck.html">« Bounds checking</a><a class="docs-footer-nextpage" href="offset-arrays.html">Arrays with custom indices »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
