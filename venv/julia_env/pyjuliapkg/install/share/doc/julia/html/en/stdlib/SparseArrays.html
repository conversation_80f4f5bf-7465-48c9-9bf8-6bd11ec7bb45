<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Sparse Arrays · The Julia Language</title><meta name="title" content="Sparse Arrays · The Julia Language"/><meta property="og:title" content="Sparse Arrays · The Julia Language"/><meta property="twitter:title" content="Sparse Arrays · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li class="is-active"><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a><ul class="internal"><li><a class="tocitem" href="#man-csc"><span>Compressed Sparse Column (CSC) Sparse Matrix Storage</span></a></li><li><a class="tocitem" href="#Sparse-Vector-Storage"><span>Sparse Vector Storage</span></a></li><li><a class="tocitem" href="#Sparse-Vector-and-Matrix-Constructors"><span>Sparse Vector and Matrix Constructors</span></a></li><li><a class="tocitem" href="#Sparse-matrix-operations"><span>Sparse matrix operations</span></a></li><li><a class="tocitem" href="#Correspondence-of-dense-and-sparse-methods"><span>Correspondence of dense and sparse methods</span></a></li><li><a class="tocitem" href="#stdlib-sparse-linalg"><span>Sparse Linear Algebra</span></a></li><li class="toplevel"><a class="tocitem" href="#stdlib-sparse-arrays"><span>SparseArrays API</span></a></li><li class="toplevel"><a class="tocitem" href="#stdlib-sparse-linalg-api"><span>Sparse Linear Algebra API</span></a></li><li class="toplevel"><a class="tocitem" href="#Noteworthy-External-Sparse-Packages"><span>Noteworthy External Sparse Packages</span></a></li></ul></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="SparseArrays.html">Sparse Arrays</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="SparseArrays.html">Sparse Arrays</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/master/docs/src/index.md" title="View source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Sparse-Arrays"><a class="docs-heading-anchor" href="#Sparse-Arrays">Sparse Arrays</a><a id="Sparse-Arrays-1"></a><a class="docs-heading-anchor-permalink" href="#Sparse-Arrays" title="Permalink"></a></h1><p>Julia has support for sparse vectors and <a href="https://en.wikipedia.org/wiki/Sparse_matrix">sparse matrices</a> in the <code>SparseArrays</code> stdlib module. Sparse arrays are arrays that contain enough zeros that storing them in a special data structure leads to savings in space and execution time, compared to dense arrays.</p><p>External packages which implement different sparse storage types, multidimensional sparse arrays, and more can be found in <a href="SparseArrays.html#Noteworthy-External-Sparse-Packages">Noteworthy External Sparse Packages</a></p><h2 id="man-csc"><a class="docs-heading-anchor" href="#man-csc">Compressed Sparse Column (CSC) Sparse Matrix Storage</a><a id="man-csc-1"></a><a class="docs-heading-anchor-permalink" href="#man-csc" title="Permalink"></a></h2><p>In Julia, sparse matrices are stored in the <a href="https://en.wikipedia.org/wiki/Sparse_matrix#Compressed_sparse_column_.28CSC_or_CCS.29">Compressed Sparse Column (CSC) format</a>. Julia sparse matrices have the type <a href="SparseArrays.html#SparseArrays.SparseMatrixCSC"><code>SparseMatrixCSC{Tv,Ti}</code></a>, where <code>Tv</code> is the type of the stored values, and <code>Ti</code> is the integer type for storing column pointers and row indices. The internal representation of <code>SparseMatrixCSC</code> is as follows:</p><pre><code class="language-julia hljs">struct SparseMatrixCSC{Tv,Ti&lt;:Integer} &lt;: AbstractSparseMatrixCSC{Tv,Ti}
    m::Int                  # Number of rows
    n::Int                  # Number of columns
    colptr::Vector{Ti}      # Column j is in colptr[j]:(colptr[j+1]-1)
    rowval::Vector{Ti}      # Row indices of stored values
    nzval::Vector{Tv}       # Stored values, typically nonzeros
end</code></pre><p>The compressed sparse column storage makes it easy and quick to access the elements in the column of a sparse matrix, whereas accessing the sparse matrix by rows is considerably slower. Operations such as insertion of previously unstored entries one at a time in the CSC structure tend to be slow. This is because all elements of the sparse matrix that are beyond the point of insertion have to be moved one place over.</p><p>All operations on sparse matrices are carefully implemented to exploit the CSC data structure for performance, and to avoid expensive operations.</p><p>If you have data in CSC format from a different application or library, and wish to import it in Julia, make sure that you use 1-based indexing. The row indices in every column need to be sorted, and if they are not, the matrix will display incorrectly.  If your <code>SparseMatrixCSC</code> object contains unsorted row indices, one quick way to sort them is by doing a double transpose. Since the transpose operation is lazy, make a copy to materialize each transpose.</p><p>In some applications, it is convenient to store explicit zero values in a <code>SparseMatrixCSC</code>. These <em>are</em> accepted by functions in <code>Base</code> (but there is no guarantee that they will be preserved in mutating operations). Such explicitly stored zeros are treated as structural nonzeros by many routines. The <a href="SparseArrays.html#SparseArrays.nnz"><code>nnz</code></a> function returns the number of elements explicitly stored in the sparse data structure, including non-structural zeros. In order to count the exact number of numerical nonzeros, use <a href="../base/collections.html#Base.count"><code>count(!iszero, x)</code></a>, which inspects every stored element of a sparse matrix. <a href="SparseArrays.html#SparseArrays.dropzeros"><code>dropzeros</code></a>, and the in-place <a href="SparseArrays.html#SparseArrays.dropzeros!"><code>dropzeros!</code></a>, can be used to remove stored zeros from the sparse matrix.</p><pre><code class="language-julia-repl hljs">julia&gt; A = sparse([1, 1, 2, 3], [1, 3, 2, 3], [0, 1, 2, 0])
3×3 SparseMatrixCSC{Int64, Int64} with 4 stored entries:
 0  ⋅  1
 ⋅  2  ⋅
 ⋅  ⋅  0

julia&gt; dropzeros(A)
3×3 SparseMatrixCSC{Int64, Int64} with 2 stored entries:
 ⋅  ⋅  1
 ⋅  2  ⋅
 ⋅  ⋅  ⋅</code></pre><h2 id="Sparse-Vector-Storage"><a class="docs-heading-anchor" href="#Sparse-Vector-Storage">Sparse Vector Storage</a><a id="Sparse-Vector-Storage-1"></a><a class="docs-heading-anchor-permalink" href="#Sparse-Vector-Storage" title="Permalink"></a></h2><p>Sparse vectors are stored in a close analog to compressed sparse column format for sparse matrices. In Julia, sparse vectors have the type <a href="SparseArrays.html#SparseArrays.SparseVector"><code>SparseVector{Tv,Ti}</code></a> where <code>Tv</code> is the type of the stored values and <code>Ti</code> the integer type for the indices. The internal representation is as follows:</p><pre><code class="language-julia hljs">struct SparseVector{Tv,Ti&lt;:Integer} &lt;: AbstractSparseVector{Tv,Ti}
    n::Int              # Length of the sparse vector
    nzind::Vector{Ti}   # Indices of stored values
    nzval::Vector{Tv}   # Stored values, typically nonzeros
end</code></pre><p>As for <a href="SparseArrays.html#SparseArrays.SparseMatrixCSC"><code>SparseMatrixCSC</code></a>, the <code>SparseVector</code> type can also contain explicitly stored zeros. (See <a href="SparseArrays.html#man-csc">Sparse Matrix Storage</a>.).</p><h2 id="Sparse-Vector-and-Matrix-Constructors"><a class="docs-heading-anchor" href="#Sparse-Vector-and-Matrix-Constructors">Sparse Vector and Matrix Constructors</a><a id="Sparse-Vector-and-Matrix-Constructors-1"></a><a class="docs-heading-anchor-permalink" href="#Sparse-Vector-and-Matrix-Constructors" title="Permalink"></a></h2><p>The simplest way to create a sparse array is to use a function equivalent to the <a href="../base/arrays.html#Base.zeros"><code>zeros</code></a> function that Julia provides for working with dense arrays. To produce a sparse array instead, you can use the same name with an <code>sp</code> prefix:</p><pre><code class="language-julia-repl hljs">julia&gt; spzeros(3)
3-element SparseVector{Float64, Int64} with 0 stored entries</code></pre><p>The <a href="SparseArrays.html#SparseArrays.sparse"><code>sparse</code></a> function is often a handy way to construct sparse arrays. For example, to construct a sparse matrix we can input a vector <code>I</code> of row indices, a vector <code>J</code> of column indices, and a vector <code>V</code> of stored values (this is also known as the <a href="https://en.wikipedia.org/wiki/Sparse_matrix#Coordinate_list_.28COO.29">COO (coordinate) format</a>). <code>sparse(I,J,V)</code> then constructs a sparse matrix such that <code>S[I[k], J[k]] = V[k]</code>. The equivalent sparse vector constructor is <a href="SparseArrays.html#SparseArrays.sparsevec"><code>sparsevec</code></a>, which takes the (row) index vector <code>I</code> and the vector <code>V</code> with the stored values and constructs a sparse vector <code>R</code> such that <code>R[I[k]] = V[k]</code>.</p><pre><code class="language-julia-repl hljs">julia&gt; I = [1, 4, 3, 5]; J = [4, 7, 18, 9]; V = [1, 2, -5, 3];

julia&gt; S = sparse(I,J,V)
5×18 SparseMatrixCSC{Int64, Int64} with 4 stored entries:
⎡⠀⠈⠀⠀⠀⠀⠀⠀⢀⎤
⎣⠀⠀⠀⠂⡀⠀⠀⠀⠀⎦

julia&gt; R = sparsevec(I,V)
5-element SparseVector{Int64, Int64} with 4 stored entries:
  [1]  =  1
  [3]  =  -5
  [4]  =  2
  [5]  =  3</code></pre><p>The inverse of the <a href="SparseArrays.html#SparseArrays.sparse"><code>sparse</code></a> and <a href="SparseArrays.html#SparseArrays.sparsevec"><code>sparsevec</code></a> functions is <a href="SparseArrays.html#SparseArrays.findnz"><code>findnz</code></a>, which retrieves the inputs used to create the sparse array (including stored entries equal to zero). <a href="../base/arrays.html#Base.findall-Tuple{Any}"><code>findall(!iszero, x)</code></a> returns the Cartesian indices of non-zero entries in <code>x</code> (not including stored entries equal to zero).</p><pre><code class="language-julia-repl hljs">julia&gt; findnz(S)
([1, 4, 5, 3], [4, 7, 9, 18], [1, 2, 3, -5])

julia&gt; findall(!iszero, S)
4-element Vector{CartesianIndex{2}}:
 CartesianIndex(1, 4)
 CartesianIndex(4, 7)
 CartesianIndex(5, 9)
 CartesianIndex(3, 18)

julia&gt; findnz(R)
([1, 3, 4, 5], [1, -5, 2, 3])

julia&gt; findall(!iszero, R)
4-element Vector{Int64}:
 1
 3
 4
 5</code></pre><p>Another way to create a sparse array is to convert a dense array into a sparse array using the <a href="SparseArrays.html#SparseArrays.sparse"><code>sparse</code></a> function:</p><pre><code class="language-julia-repl hljs">julia&gt; sparse(Matrix(1.0I, 5, 5))
5×5 SparseMatrixCSC{Float64, Int64} with 5 stored entries:
 1.0   ⋅    ⋅    ⋅    ⋅
  ⋅   1.0   ⋅    ⋅    ⋅
  ⋅    ⋅   1.0   ⋅    ⋅
  ⋅    ⋅    ⋅   1.0   ⋅
  ⋅    ⋅    ⋅    ⋅   1.0

julia&gt; sparse([1.0, 0.0, 1.0])
3-element SparseVector{Float64, Int64} with 2 stored entries:
  [1]  =  1.0
  [3]  =  1.0</code></pre><p>You can go in the other direction using the <a href="../base/arrays.html#Core.Array"><code>Array</code></a> constructor. The <a href="SparseArrays.html#SparseArrays.issparse"><code>issparse</code></a> function can be used to query if a matrix is sparse.</p><pre><code class="language-julia-repl hljs">julia&gt; issparse(spzeros(5))
true</code></pre><h2 id="Sparse-matrix-operations"><a class="docs-heading-anchor" href="#Sparse-matrix-operations">Sparse matrix operations</a><a id="Sparse-matrix-operations-1"></a><a class="docs-heading-anchor-permalink" href="#Sparse-matrix-operations" title="Permalink"></a></h2><p>Arithmetic operations on sparse matrices also work as they do on dense matrices. Indexing of, assignment into, and concatenation of sparse matrices work in the same way as dense matrices. Indexing operations, especially assignment, are expensive, when carried out one element at a time. In many cases it may be better to convert the sparse matrix into <code>(I,J,V)</code> format using <a href="SparseArrays.html#SparseArrays.findnz"><code>findnz</code></a>, manipulate the values or the structure in the dense vectors <code>(I,J,V)</code>, and then reconstruct the sparse matrix.</p><h2 id="Correspondence-of-dense-and-sparse-methods"><a class="docs-heading-anchor" href="#Correspondence-of-dense-and-sparse-methods">Correspondence of dense and sparse methods</a><a id="Correspondence-of-dense-and-sparse-methods-1"></a><a class="docs-heading-anchor-permalink" href="#Correspondence-of-dense-and-sparse-methods" title="Permalink"></a></h2><p>The following table gives a correspondence between built-in methods on sparse matrices and their corresponding methods on dense matrix types. In general, methods that generate sparse matrices differ from their dense counterparts in that the resulting matrix follows the same sparsity pattern as a given sparse matrix <code>S</code>, or that the resulting sparse matrix has density <code>d</code>, i.e. each matrix element has a probability <code>d</code> of being non-zero.</p><p>Details can be found in the <a href="SparseArrays.html#stdlib-sparse-arrays">Sparse Vectors and Matrices</a> section of the standard library reference.</p><table><tr><th style="text-align: left">Sparse</th><th style="text-align: left">Dense</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left"><a href="SparseArrays.html#SparseArrays.spzeros"><code>spzeros(m,n)</code></a></td><td style="text-align: left"><a href="../base/arrays.html#Base.zeros"><code>zeros(m,n)</code></a></td><td style="text-align: left">Creates a <em>m</em>-by-<em>n</em> matrix of zeros. (<a href="SparseArrays.html#SparseArrays.spzeros"><code>spzeros(m,n)</code></a> is empty.)</td></tr><tr><td style="text-align: left"><a href="SparseArrays.html#SparseArrays.sparse"><code>sparse(I,n,n)</code></a></td><td style="text-align: left"><a href="../base/arrays.html#Base.Matrix"><code>Matrix(I,n,n)</code></a></td><td style="text-align: left">Creates a <em>n</em>-by-<em>n</em> identity matrix.</td></tr><tr><td style="text-align: left"><a href="SparseArrays.html#SparseArrays.sparse"><code>sparse(A)</code></a></td><td style="text-align: left"><a href="../base/arrays.html#Core.Array"><code>Array(S)</code></a></td><td style="text-align: left">Interconverts between dense and sparse formats.</td></tr><tr><td style="text-align: left"><a href="SparseArrays.html#SparseArrays.sprand"><code>sprand(m,n,d)</code></a></td><td style="text-align: left"><a href="Random.html#Base.rand"><code>rand(m,n)</code></a></td><td style="text-align: left">Creates a <em>m</em>-by-<em>n</em> random matrix (of density <em>d</em>) with iid non-zero elements distributed uniformly on the half-open interval <span>$[0, 1)$</span>.</td></tr><tr><td style="text-align: left"><a href="SparseArrays.html#SparseArrays.sprandn"><code>sprandn(m,n,d)</code></a></td><td style="text-align: left"><a href="Random.html#Base.randn"><code>randn(m,n)</code></a></td><td style="text-align: left">Creates a <em>m</em>-by-<em>n</em> random matrix (of density <em>d</em>) with iid non-zero elements distributed according to the standard normal (Gaussian) distribution.</td></tr><tr><td style="text-align: left"><a href="SparseArrays.html#SparseArrays.sprandn"><code>sprandn(rng,m,n,d)</code></a></td><td style="text-align: left"><a href="Random.html#Base.randn"><code>randn(rng,m,n)</code></a></td><td style="text-align: left">Creates a <em>m</em>-by-<em>n</em> random matrix (of density <em>d</em>) with iid non-zero elements generated with the <code>rng</code> random number generator</td></tr></table><h2 id="stdlib-sparse-linalg"><a class="docs-heading-anchor" href="#stdlib-sparse-linalg">Sparse Linear Algebra</a><a id="stdlib-sparse-linalg-1"></a><a class="docs-heading-anchor-permalink" href="#stdlib-sparse-linalg" title="Permalink"></a></h2><p>Sparse matrix solvers call functions from <a href="http://suitesparse.com">SuiteSparse</a>. The following factorizations are available:</p><table><tr><th style="text-align: left">Type</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left"><code>CHOLMOD.Factor</code></td><td style="text-align: left">Cholesky and LDLt factorizations</td></tr><tr><td style="text-align: left"><code>UMFPACK.UmfpackLU</code></td><td style="text-align: left">LU factorization</td></tr><tr><td style="text-align: left"><code>SPQR.QRSparse</code></td><td style="text-align: left">QR factorization</td></tr></table><p>These factorizations are described in more detail in the <a href="SparseArrays.html#stdlib-sparse-linalg-api">Sparse Linear Algebra API section</a>:</p><ol><li><a href="LinearAlgebra.html#LinearAlgebra.cholesky"><code>cholesky</code></a></li><li><a href="LinearAlgebra.html#LinearAlgebra.ldlt"><code>ldlt</code></a></li><li><a href="LinearAlgebra.html#LinearAlgebra.lu"><code>lu</code></a></li><li><a href="LinearAlgebra.html#LinearAlgebra.qr"><code>qr</code></a></li></ol><h1 id="stdlib-sparse-arrays"><a class="docs-heading-anchor" href="#stdlib-sparse-arrays">SparseArrays API</a><a id="stdlib-sparse-arrays-1"></a><a class="docs-heading-anchor-permalink" href="#stdlib-sparse-arrays" title="Permalink"></a></h1><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.AbstractSparseArray" href="#SparseArrays.AbstractSparseArray"><code>SparseArrays.AbstractSparseArray</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AbstractSparseArray{Tv,Ti,N}</code></pre><p>Supertype for <code>N</code>-dimensional sparse arrays (or array-like types) with elements of type <code>Tv</code> and index type <code>Ti</code>. <a href="SparseArrays.html#SparseArrays.SparseMatrixCSC"><code>SparseMatrixCSC</code></a>, <a href="SparseArrays.html#SparseArrays.SparseVector"><code>SparseVector</code></a> and <code>SuiteSparse.CHOLMOD.Sparse</code> are subtypes of this.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/abstractsparse.jl#L3-L9">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.AbstractSparseVector" href="#SparseArrays.AbstractSparseVector"><code>SparseArrays.AbstractSparseVector</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AbstractSparseVector{Tv,Ti}</code></pre><p>Supertype for one-dimensional sparse arrays (or array-like types) with elements of type <code>Tv</code> and index type <code>Ti</code>. Alias for <code>AbstractSparseArray{Tv,Ti,1}</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/abstractsparse.jl#L12-L17">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.AbstractSparseMatrix" href="#SparseArrays.AbstractSparseMatrix"><code>SparseArrays.AbstractSparseMatrix</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AbstractSparseMatrix{Tv,Ti}</code></pre><p>Supertype for two-dimensional sparse arrays (or array-like types) with elements of type <code>Tv</code> and index type <code>Ti</code>. Alias for <code>AbstractSparseArray{Tv,Ti,2}</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/abstractsparse.jl#L27-L32">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.SparseVector" href="#SparseArrays.SparseVector"><code>SparseArrays.SparseVector</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SparseVector{Tv,Ti&lt;:Integer} &lt;: AbstractSparseVector{Tv,Ti}</code></pre><p>Vector type for storing sparse vectors. Can be created by passing the length of the vector, a <em>sorted</em> vector of non-zero indices, and a vector of non-zero values.</p><p>For instance, the vector <code>[5, 6, 0, 7]</code> can be represented as</p><pre><code class="language-julia hljs">SparseVector(4, [1, 2, 4], [5, 6, 7])</code></pre><p>This indicates that the element at index 1 is 5, at index 2 is 6, at index 3 is <code>zero(Int)</code>, and at index 4 is 7.</p><p>It may be more convenient to create sparse vectors directly from dense vectors using <code>sparse</code> as</p><pre><code class="language-julia hljs">sparse([5, 6, 0, 7])</code></pre><p>yields the same sparse vector.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsevector.jl#L13-L35">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.SparseMatrixCSC" href="#SparseArrays.SparseMatrixCSC"><code>SparseArrays.SparseMatrixCSC</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SparseMatrixCSC{Tv,Ti&lt;:Integer} &lt;: AbstractSparseMatrixCSC{Tv,Ti}</code></pre><p>Matrix type for storing sparse matrices in the <a href="SparseArrays.html#man-csc">Compressed Sparse Column</a> format. The standard way of constructing SparseMatrixCSC is through the <a href="SparseArrays.html#SparseArrays.sparse"><code>sparse</code></a> function. See also <a href="SparseArrays.html#SparseArrays.spzeros"><code>spzeros</code></a>, <a href="SparseArrays.html#SparseArrays.spdiagm"><code>spdiagm</code></a> and <a href="SparseArrays.html#SparseArrays.sprand"><code>sprand</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L11-L18">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.sparse" href="#SparseArrays.sparse"><code>SparseArrays.sparse</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sparse(A)</code></pre><p>Convert an AbstractMatrix <code>A</code> into a sparse matrix.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = Matrix(1.0I, 3, 3)
3×3 Matrix{Float64}:
 1.0  0.0  0.0
 0.0  1.0  0.0
 0.0  0.0  1.0

julia&gt; sparse(A)
3×3 SparseMatrixCSC{Float64, Int64} with 3 stored entries:
 1.0   ⋅    ⋅
  ⋅   1.0   ⋅
  ⋅    ⋅   1.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L996-L1015">source</a></section><section><div><pre><code class="language-julia hljs">sparse(I, J, V,[ m, n, combine])</code></pre><p>Create a sparse matrix <code>S</code> of dimensions <code>m x n</code> such that <code>S[I[k], J[k]] = V[k]</code>. The <code>combine</code> function is used to combine duplicates. If <code>m</code> and <code>n</code> are not specified, they are set to <code>maximum(I)</code> and <code>maximum(J)</code> respectively. If the <code>combine</code> function is not supplied, <code>combine</code> defaults to <code>+</code> unless the elements of <code>V</code> are Booleans in which case <code>combine</code> defaults to <code>|</code>. All elements of <code>I</code> must satisfy <code>1 &lt;= I[k] &lt;= m</code>, and all elements of <code>J</code> must satisfy <code>1 &lt;= J[k] &lt;= n</code>. Numerical zeros in (<code>I</code>, <code>J</code>, <code>V</code>) are retained as structural nonzeros; to drop numerical zeros, use <a href="SparseArrays.html#SparseArrays.dropzeros!"><code>dropzeros!</code></a>.</p><p>For additional documentation and an expert driver, see <code>SparseArrays.sparse!</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Is = [1; 2; 3];

julia&gt; Js = [1; 2; 3];

julia&gt; Vs = [1; 2; 3];

julia&gt; sparse(Is, Js, Vs)
3×3 SparseMatrixCSC{Int64, Int64} with 3 stored entries:
 1  ⋅  ⋅
 ⋅  2  ⋅
 ⋅  ⋅  3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L1030-L1057">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.sparse!" href="#SparseArrays.sparse!"><code>SparseArrays.sparse!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sparse!(I::AbstractVector{Ti}, J::AbstractVector{Ti}, V::AbstractVector{Tv},
        m::Integer, n::Integer, combine, klasttouch::Vector{Ti},
        csrrowptr::Vector{Ti}, csrcolval::Vector{Ti}, csrnzval::Vector{Tv},
        [csccolptr::Vector{Ti}], [cscrowval::Vector{Ti}, cscnzval::Vector{Tv}] ) where {Tv,Ti&lt;:Integer}</code></pre><p>Parent of and expert driver for <a href="SparseArrays.html#SparseArrays.sparse"><code>sparse</code></a>; see <a href="SparseArrays.html#SparseArrays.sparse"><code>sparse</code></a> for basic usage. This method allows the user to provide preallocated storage for <code>sparse</code>&#39;s intermediate objects and result as described below. This capability enables more efficient successive construction of <a href="SparseArrays.html#SparseArrays.SparseMatrixCSC"><code>SparseMatrixCSC</code></a>s from coordinate representations, and also enables extraction of an unsorted-column representation of the result&#39;s transpose at no additional cost.</p><p>This method consists of three major steps: (1) Counting-sort the provided coordinate representation into an unsorted-row CSR form including repeated entries. (2) Sweep through the CSR form, simultaneously calculating the desired CSC form&#39;s column-pointer array, detecting repeated entries, and repacking the CSR form with repeated entries combined; this stage yields an unsorted-row CSR form with no repeated entries. (3) Counting-sort the preceding CSR form into a fully-sorted CSC form with no repeated entries.</p><p>Input arrays <code>csrrowptr</code>, <code>csrcolval</code>, and <code>csrnzval</code> constitute storage for the intermediate CSR forms and require <code>length(csrrowptr) &gt;= m + 1</code>, <code>length(csrcolval) &gt;= length(I)</code>, and <code>length(csrnzval &gt;= length(I))</code>. Input array <code>klasttouch</code>, workspace for the second stage, requires <code>length(klasttouch) &gt;= n</code>. Optional input arrays <code>csccolptr</code>, <code>cscrowval</code>, and <code>cscnzval</code> constitute storage for the returned CSC form <code>S</code>. If necessary, these are resized automatically to satisfy <code>length(csccolptr) = n + 1</code>, <code>length(cscrowval) = nnz(S)</code> and <code>length(cscnzval) = nnz(S)</code>; hence, if <code>nnz(S)</code> is unknown at the outset, passing in empty vectors of the appropriate type (<code>Vector{Ti}()</code> and <code>Vector{Tv}()</code> respectively) suffices, or calling the <code>sparse!</code> method neglecting <code>cscrowval</code> and <code>cscnzval</code>.</p><p>On return, <code>csrrowptr</code>, <code>csrcolval</code>, and <code>csrnzval</code> contain an unsorted-column representation of the result&#39;s transpose.</p><p>You may reuse the input arrays&#39; storage (<code>I</code>, <code>J</code>, <code>V</code>) for the output arrays (<code>csccolptr</code>, <code>cscrowval</code>, <code>cscnzval</code>). For example, you may call <code>sparse!(I, J, V, csrrowptr, csrcolval, csrnzval, I, J, V)</code>. Note that they will be resized to satisfy the conditions above.</p><p>For the sake of efficiency, this method performs no argument checking beyond <code>1 &lt;= I[k] &lt;= m</code> and <code>1 &lt;= J[k] &lt;= n</code>. Use with care. Testing with <code>--check-bounds=yes</code> is wise.</p><p>This method runs in <code>O(m, n, length(I))</code> time. The HALFPERM algorithm described in F. Gustavson, &quot;Two fast algorithms for sparse matrices: multiplication and permuted transposition,&quot; ACM TOMS 4(3), 250-269 (1978) inspired this method&#39;s use of a pair of counting sorts.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L1102-L1149">source</a></section><section><div><pre><code class="language-julia hljs">SparseArrays.sparse!(I, J, V, [m, n, combine]) -&gt; SparseMatrixCSC</code></pre><p>Variant of <code>sparse!</code> that re-uses the input vectors (<code>I</code>, <code>J</code>, <code>V</code>) for the final matrix storage. After construction the input vectors will alias the matrix buffers; <code>S.colptr === I</code>, <code>S.rowval === J</code>, and <code>S.nzval === V</code> holds, and they will be <code>resize!</code>d as necessary.</p><p>Note that some work buffers will still be allocated. Specifically, this method is a convenience wrapper around <code>sparse!(I, J, V, m, n, combine, klasttouch, csrrowptr, csrcolval, csrnzval, csccolptr, cscrowval, cscnzval)</code> where this method allocates <code>klasttouch</code>, <code>csrrowptr</code>, <code>csrcolval</code>, and <code>csrnzval</code> of appropriate size, but reuses <code>I</code>, <code>J</code>, and <code>V</code> for <code>csccolptr</code>, <code>cscrowval</code>, and <code>cscnzval</code>.</p><p>Arguments <code>m</code>, <code>n</code>, and <code>combine</code> defaults to <code>maximum(I)</code>, <code>maximum(J)</code>, and <code>+</code>, respectively.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.10</header><div class="admonition-body"><p>This method requires Julia version 1.10 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L1292-L1310">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.sparsevec" href="#SparseArrays.sparsevec"><code>SparseArrays.sparsevec</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sparsevec(I, V, [m, combine])</code></pre><p>Create a sparse vector <code>S</code> of length <code>m</code> such that <code>S[I[k]] = V[k]</code>. Duplicates are combined using the <code>combine</code> function, which defaults to <code>+</code> if no <code>combine</code> argument is provided, unless the elements of <code>V</code> are Booleans in which case <code>combine</code> defaults to <code>|</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; II = [1, 3, 3, 5]; V = [0.1, 0.2, 0.3, 0.2];

julia&gt; sparsevec(II, V)
5-element SparseVector{Float64, Int64} with 3 stored entries:
  [1]  =  0.1
  [3]  =  0.5
  [5]  =  0.2

julia&gt; sparsevec(II, V, 8, -)
8-element SparseVector{Float64, Int64} with 3 stored entries:
  [1]  =  0.1
  [3]  =  -0.1
  [5]  =  0.2

julia&gt; sparsevec([1, 3, 1, 2, 2], [true, true, false, false, false])
3-element SparseVector{Bool, Int64} with 3 stored entries:
  [1]  =  1
  [2]  =  0
  [3]  =  1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsevector.jl#L296-L326">source</a></section><section><div><pre><code class="language-julia hljs">sparsevec(d::Dict, [m])</code></pre><p>Create a sparse vector of length <code>m</code> where the nonzero indices are keys from the dictionary, and the nonzero values are the values from the dictionary.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; sparsevec(Dict(1 =&gt; 3, 2 =&gt; 2))
2-element SparseVector{Int64, Int64} with 2 stored entries:
  [1]  =  3
  [2]  =  2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsevector.jl#L374-L387">source</a></section><section><div><pre><code class="language-julia hljs">sparsevec(A)</code></pre><p>Convert a vector <code>A</code> into a sparse vector of length <code>m</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; sparsevec([1.0, 2.0, 0.0, 0.0, 3.0, 0.0])
6-element SparseVector{Float64, Int64} with 3 stored entries:
  [1]  =  1.0
  [2]  =  2.0
  [5]  =  3.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsevector.jl#L510-L523">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.similar-Tuple{SparseArrays.AbstractSparseMatrixCSC, Type}" href="#Base.similar-Tuple{SparseArrays.AbstractSparseMatrixCSC, Type}"><code>Base.similar</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">similar(A::AbstractSparseMatrixCSC{Tv,Ti}, [::Type{TvNew}, ::Type{TiNew}, m::Integer, n::Integer]) where {Tv,Ti}</code></pre><p>Create an uninitialized mutable array with the given element type, index type, and size, based upon the given source <code>SparseMatrixCSC</code>. The new sparse matrix maintains the structure of the original sparse matrix, except in the case where dimensions of the output matrix are different from the output.</p><p>The output matrix has zeros in the same locations as the input, but uninitialized values for the nonzero locations.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L708-L719">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.issparse" href="#SparseArrays.issparse"><code>SparseArrays.issparse</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">issparse(S)</code></pre><p>Returns <code>true</code> if <code>S</code> is sparse, and <code>false</code> otherwise.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; sv = sparsevec([1, 4], [2.3, 2.2], 10)
10-element SparseVector{Float64, Int64} with 2 stored entries:
  [1]  =  2.3
  [4]  =  2.2

julia&gt; issparse(sv)
true

julia&gt; issparse(Array(sv))
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/abstractsparse.jl#L45-L63">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.nnz" href="#SparseArrays.nnz"><code>SparseArrays.nnz</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">nnz(A)</code></pre><p>Returns the number of stored (filled) elements in a sparse array.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = sparse(2I, 3, 3)
3×3 SparseMatrixCSC{Int64, Int64} with 3 stored entries:
 2  ⋅  ⋅
 ⋅  2  ⋅
 ⋅  ⋅  2

julia&gt; nnz(A)
3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L203-L219">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.findnz" href="#SparseArrays.findnz"><code>SparseArrays.findnz</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">findnz(A::SparseMatrixCSC)</code></pre><p>Return a tuple <code>(I, J, V)</code> where <code>I</code> and <code>J</code> are the row and column indices of the stored (&quot;structurally non-zero&quot;) values in sparse matrix <code>A</code>, and <code>V</code> is a vector of the values.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = sparse([1 2 0; 0 0 3; 0 4 0])
3×3 SparseMatrixCSC{Int64, Int64} with 4 stored entries:
 1  2  ⋅
 ⋅  ⋅  3
 ⋅  4  ⋅

julia&gt; findnz(A)
([1, 1, 3, 2], [1, 2, 2, 3], [1, 2, 4, 3])</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/abstractsparse.jl#L112-L129">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.spzeros" href="#SparseArrays.spzeros"><code>SparseArrays.spzeros</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">spzeros([type,]m[,n])</code></pre><p>Create a sparse vector of length <code>m</code> or sparse matrix of size <code>m x n</code>. This sparse array will not contain any nonzero values. No storage will be allocated for nonzero values during construction. The type defaults to <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> if not specified.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; spzeros(3, 3)
3×3 SparseMatrixCSC{Float64, Int64} with 0 stored entries:
  ⋅    ⋅    ⋅
  ⋅    ⋅    ⋅
  ⋅    ⋅    ⋅

julia&gt; spzeros(Float32, 4)
4-element SparseVector{Float32, Int64} with 0 stored entries</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L2081-L2100">source</a></section><section><div><pre><code class="language-julia hljs">spzeros([type], I::AbstractVector, J::AbstractVector, [m, n])</code></pre><p>Create a sparse matrix <code>S</code> of dimensions <code>m x n</code> with structural zeros at <code>S[I[k], J[k]]</code>.</p><p>This method can be used to construct the sparsity pattern of the matrix, and is more efficient than using e.g. <code>sparse(I, J, zeros(length(I)))</code>.</p><p>For additional documentation and an expert driver, see <code>SparseArrays.spzeros!</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.10</header><div class="admonition-body"><p>This methods requires Julia version 1.10 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L2114-L2126">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.spzeros!" href="#SparseArrays.spzeros!"><code>SparseArrays.spzeros!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">spzeros!(::Type{Tv}, I::AbstractVector{Ti}, J::AbstractVector{Ti}, m::Integer, n::Integer,
         klasttouch::Vector{Ti}, csrrowptr::Vector{Ti}, csrcolval::Vector{Ti},
         [csccolptr::Vector{Ti}], [cscrowval::Vector{Ti}, cscnzval::Vector{Tv}]) where {Tv,Ti&lt;:Integer}</code></pre><p>Parent of and expert driver for <code>spzeros(I, J)</code> allowing user to provide preallocated storage for intermediate objects. This method is to <code>spzeros</code> what <code>SparseArrays.sparse!</code> is to <code>sparse</code>. See documentation for <code>SparseArrays.sparse!</code> for details and required buffer lengths.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.10</header><div class="admonition-body"><p>This methods requires Julia version 1.10 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L2143-L2155">source</a></section><section><div><pre><code class="language-julia hljs">SparseArrays.spzeros!(::Type{Tv}, I, J, [m, n]) -&gt; SparseMatrixCSC{Tv}</code></pre><p>Variant of <code>spzeros!</code> that re-uses the input vectors <code>I</code> and <code>J</code> for the final matrix storage. After construction the input vectors will alias the matrix buffers; <code>S.colptr === I</code> and <code>S.rowval === J</code> holds, and they will be <code>resize!</code>d as necessary.</p><p>Note that some work buffers will still be allocated. Specifically, this method is a convenience wrapper around <code>spzeros!(Tv, I, J, m, n, klasttouch, csrrowptr, csrcolval, csccolptr, cscrowval)</code> where this method allocates <code>klasttouch</code>, <code>csrrowptr</code>, and <code>csrcolval</code> of appropriate size, but reuses <code>I</code> and <code>J</code> for <code>csccolptr</code> and <code>cscrowval</code>.</p><p>Arguments <code>m</code> and <code>n</code> defaults to <code>maximum(I)</code> and <code>maximum(J)</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.10</header><div class="admonition-body"><p>This method requires Julia version 1.10 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L2166-L2182">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.spdiagm" href="#SparseArrays.spdiagm"><code>SparseArrays.spdiagm</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">spdiagm(kv::Pair{&lt;:Integer,&lt;:AbstractVector}...)
spdiagm(m::Integer, n::Integer, kv::Pair{&lt;:Integer,&lt;:AbstractVector}...)</code></pre><p>Construct a sparse diagonal matrix from <code>Pair</code>s of vectors and diagonals. Each vector <code>kv.second</code> will be placed on the <code>kv.first</code> diagonal.  By default, the matrix is square and its size is inferred from <code>kv</code>, but a non-square size <code>m</code>×<code>n</code> (padded with zeros as needed) can be specified by passing <code>m,n</code> as the first arguments.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; spdiagm(-1 =&gt; [1,2,3,4], 1 =&gt; [4,3,2,1])
5×5 SparseMatrixCSC{Int64, Int64} with 8 stored entries:
 ⋅  4  ⋅  ⋅  ⋅
 1  ⋅  3  ⋅  ⋅
 ⋅  2  ⋅  2  ⋅
 ⋅  ⋅  3  ⋅  1
 ⋅  ⋅  ⋅  4  ⋅</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L4203-L4223">source</a></section><section><div><pre><code class="language-julia hljs">spdiagm(v::AbstractVector)
spdiagm(m::Integer, n::Integer, v::AbstractVector)</code></pre><p>Construct a sparse matrix with elements of the vector as diagonal elements. By default (no given <code>m</code> and <code>n</code>), the matrix is square and its size is given by <code>length(v)</code>, but a non-square size <code>m</code>×<code>n</code> can be specified by passing <code>m</code> and <code>n</code> as the first arguments.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>These functions require at least Julia 1.6.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; spdiagm([1,2,3])
3×3 SparseMatrixCSC{Int64, Int64} with 3 stored entries:
 1  ⋅  ⋅
 ⋅  2  ⋅
 ⋅  ⋅  3

julia&gt; spdiagm(sparse([1,0,3]))
3×3 SparseMatrixCSC{Int64, Int64} with 2 stored entries:
 1  ⋅  ⋅
 ⋅  ⋅  ⋅
 ⋅  ⋅  3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L4227-L4253">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.sparse_hcat" href="#SparseArrays.sparse_hcat"><code>SparseArrays.sparse_hcat</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sparse_hcat(A...)</code></pre><p>Concatenate along dimension 2. Return a SparseMatrixCSC object.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>This method was added in Julia 1.8. It mimics previous concatenation behavior, where the concatenation with specialized &quot;sparse&quot; matrix types from LinearAlgebra.jl automatically yielded sparse output even in the absence of any SparseArray argument.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsevector.jl#L1317-L1326">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.sparse_vcat" href="#SparseArrays.sparse_vcat"><code>SparseArrays.sparse_vcat</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sparse_vcat(A...)</code></pre><p>Concatenate along dimension 1. Return a SparseMatrixCSC object.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>This method was added in Julia 1.8. It mimics previous concatenation behavior, where the concatenation with specialized &quot;sparse&quot; matrix types from LinearAlgebra.jl automatically yielded sparse output even in the absence of any SparseArray argument.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsevector.jl#L1332-L1341">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.sparse_hvcat" href="#SparseArrays.sparse_hvcat"><code>SparseArrays.sparse_hvcat</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sparse_hvcat(rows::Tuple{Vararg{Int}}, values...)</code></pre><p>Sparse horizontal and vertical concatenation in one call. This function is called for block matrix syntax. The first argument specifies the number of arguments to concatenate in each block row.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>This method was added in Julia 1.8. It mimics previous concatenation behavior, where the concatenation with specialized &quot;sparse&quot; matrix types from LinearAlgebra.jl automatically yielded sparse output even in the absence of any SparseArray argument.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsevector.jl#L1347-L1358">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.blockdiag" href="#SparseArrays.blockdiag"><code>SparseArrays.blockdiag</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">blockdiag(A...)</code></pre><p>Concatenate matrices block-diagonally. Currently only implemented for sparse matrices.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; blockdiag(sparse(2I, 3, 3), sparse(4I, 2, 2))
5×5 SparseMatrixCSC{Int64, Int64} with 5 stored entries:
 2  ⋅  ⋅  ⋅  ⋅
 ⋅  2  ⋅  ⋅  ⋅
 ⋅  ⋅  2  ⋅  ⋅
 ⋅  ⋅  ⋅  4  ⋅
 ⋅  ⋅  ⋅  ⋅  4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L3976-L3991">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.sprand" href="#SparseArrays.sprand"><code>SparseArrays.sprand</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sprand([rng],[T::Type],m,[n],p::AbstractFloat)
sprand([rng],m,[n],p::AbstractFloat,[rfn=rand])</code></pre><p>Create a random length <code>m</code> sparse vector or <code>m</code> by <code>n</code> sparse matrix, in which the probability of any element being nonzero is independently given by <code>p</code> (and hence the mean density of nonzeros is also exactly <code>p</code>). The optional <code>rng</code> argument specifies a random number generator, see <a href="Random.html#Random-Numbers">Random Numbers</a>. The optional <code>T</code> argument specifies the element type, which defaults to <code>Float64</code>.</p><p>By default, nonzero values are sampled from a uniform distribution using the <a href="Random.html#Base.rand"><code>rand</code></a> function, i.e. by <code>rand(T)</code>, or <code>rand(rng, T)</code> if <code>rng</code> is supplied; for the default <code>T=Float64</code>, this corresponds to nonzero values sampled uniformly in <code>[0,1)</code>.</p><p>You can sample nonzero values from a different distribution by passing a custom <code>rfn</code> function instead of <code>rand</code>.   This should be a function <code>rfn(k)</code> that returns an array of <code>k</code> random numbers sampled from the desired distribution; alternatively, if <code>rng</code> is supplied, it should instead be a function <code>rfn(rng, k)</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; sprand(Bool, 2, 2, 0.5)
2×2 SparseMatrixCSC{Bool, Int64} with 2 stored entries:
 1  1
 ⋅  ⋅

julia&gt; sprand(Float64, 3, 0.75)
3-element SparseVector{Float64, Int64} with 2 stored entries:
  [1]  =  0.795547
  [2]  =  0.49425</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L1994-L2026">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.sprandn" href="#SparseArrays.sprandn"><code>SparseArrays.sprandn</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sprandn([rng][,Type],m[,n],p::AbstractFloat)</code></pre><p>Create a random sparse vector of length <code>m</code> or sparse matrix of size <code>m</code> by <code>n</code> with the specified (independent) probability <code>p</code> of any entry being nonzero, where nonzero values are sampled from the normal distribution. The optional <code>rng</code> argument specifies a random number generator, see <a href="Random.html#Random-Numbers">Random Numbers</a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p>Specifying the output element type <code>Type</code> requires at least Julia 1.1.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; sprandn(2, 2, 0.75)
2×2 SparseMatrixCSC{Float64, Int64} with 3 stored entries:
 -1.20577     ⋅
  0.311817  -0.234641</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L2051-L2069">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.nonzeros" href="#SparseArrays.nonzeros"><code>SparseArrays.nonzeros</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">nonzeros(A)</code></pre><p>Return a vector of the structural nonzero values in sparse array <code>A</code>. This includes zeros that are explicitly stored in the sparse array. The returned vector points directly to the internal nonzero storage of <code>A</code>, and any modifications to the returned vector will mutate <code>A</code> as well. See <a href="SparseArrays.html#SparseArrays.rowvals"><code>rowvals</code></a> and <a href="SparseArrays.html#SparseArrays.nzrange"><code>nzrange</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = sparse(2I, 3, 3)
3×3 SparseMatrixCSC{Int64, Int64} with 3 stored entries:
 2  ⋅  ⋅
 ⋅  2  ⋅
 ⋅  ⋅  2

julia&gt; nonzeros(A)
3-element Vector{Int64}:
 2
 2
 2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L232-L255">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.rowvals" href="#SparseArrays.rowvals"><code>SparseArrays.rowvals</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rowvals(A::AbstractSparseMatrixCSC)</code></pre><p>Return a vector of the row indices of <code>A</code>. Any modifications to the returned vector will mutate <code>A</code> as well. Providing access to how the row indices are stored internally can be useful in conjunction with iterating over structural nonzero values. See also <a href="SparseArrays.html#SparseArrays.nonzeros"><code>nonzeros</code></a> and <a href="SparseArrays.html#SparseArrays.nzrange"><code>nzrange</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = sparse(2I, 3, 3)
3×3 SparseMatrixCSC{Int64, Int64} with 3 stored entries:
 2  ⋅  ⋅
 ⋅  2  ⋅
 ⋅  ⋅  2

julia&gt; rowvals(A)
3-element Vector{Int64}:
 1
 2
 3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L261-L283">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.nzrange" href="#SparseArrays.nzrange"><code>SparseArrays.nzrange</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">nzrange(A::AbstractSparseMatrixCSC, col::Integer)</code></pre><p>Return the range of indices to the structural nonzero values of a sparse matrix column. In conjunction with <a href="SparseArrays.html#SparseArrays.nonzeros"><code>nonzeros</code></a> and <a href="SparseArrays.html#SparseArrays.rowvals"><code>rowvals</code></a>, this allows for convenient iterating over a sparse matrix :</p><pre><code class="nohighlight hljs">A = sparse(I,J,V)
rows = rowvals(A)
vals = nonzeros(A)
m, n = size(A)
for j = 1:n
   for i in nzrange(A, j)
      row = rows[i]
      val = vals[i]
      # perform sparse wizardry...
   end
end</code></pre><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Adding or removing nonzero elements to the matrix may invalidate the <code>nzrange</code>, one should not mutate the matrix while iterating.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L289-L310">source</a></section><section><div><pre><code class="language-julia hljs">nzrange(x::SparseVectorUnion, col)</code></pre><p>Give the range of indices to the structural nonzero values of a sparse vector. The column index <code>col</code> is ignored (assumed to be <code>1</code>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsevector.jl#L123-L128">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.droptol!" href="#SparseArrays.droptol!"><code>SparseArrays.droptol!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">droptol!(A::AbstractSparseMatrixCSC, tol)</code></pre><p>Removes stored values from <code>A</code> whose absolute value is less than or equal to <code>tol</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L1858-L1862">source</a></section><section><div><pre><code class="language-julia hljs">droptol!(x::AbstractCompressedVector, tol)</code></pre><p>Removes stored values from <code>x</code> whose absolute value is less than or equal to <code>tol</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsevector.jl#L2298-L2302">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.dropzeros!" href="#SparseArrays.dropzeros!"><code>SparseArrays.dropzeros!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">dropzeros!(x::AbstractCompressedVector)</code></pre><p>Removes stored numerical zeros from <code>x</code>.</p><p>For an out-of-place version, see <a href="SparseArrays.html#SparseArrays.dropzeros"><code>dropzeros</code></a>. For algorithmic information, see <code>fkeep!</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsevector.jl#L2305-L2312">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.dropzeros" href="#SparseArrays.dropzeros"><code>SparseArrays.dropzeros</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">dropzeros(A::AbstractSparseMatrixCSC;)</code></pre><p>Generates a copy of <code>A</code> and removes stored numerical zeros from that copy.</p><p>For an in-place version and algorithmic information, see <a href="SparseArrays.html#SparseArrays.dropzeros!"><code>dropzeros!</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = sparse([1, 2, 3], [1, 2, 3], [1.0, 0.0, 1.0])
3×3 SparseMatrixCSC{Float64, Int64} with 3 stored entries:
 1.0   ⋅    ⋅
  ⋅   0.0   ⋅
  ⋅    ⋅   1.0

julia&gt; dropzeros(A)
3×3 SparseMatrixCSC{Float64, Int64} with 2 stored entries:
 1.0   ⋅    ⋅
  ⋅    ⋅    ⋅
  ⋅    ⋅   1.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L1877-L1898">source</a></section><section><div><pre><code class="language-julia hljs">dropzeros(x::AbstractCompressedVector)</code></pre><p>Generates a copy of <code>x</code> and removes numerical zeros from that copy.</p><p>For an in-place version and algorithmic information, see <a href="SparseArrays.html#SparseArrays.dropzeros!"><code>dropzeros!</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = sparsevec([1, 2, 3], [1.0, 0.0, 1.0])
3-element SparseVector{Float64, Int64} with 3 stored entries:
  [1]  =  1.0
  [2]  =  0.0
  [3]  =  1.0

julia&gt; dropzeros(A)
3-element SparseVector{Float64, Int64} with 2 stored entries:
  [1]  =  1.0
  [3]  =  1.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsevector.jl#L2316-L2336">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.permute" href="#SparseArrays.permute"><code>SparseArrays.permute</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">permute(A::AbstractSparseMatrixCSC{Tv,Ti}, p::AbstractVector{&lt;:Integer},
        q::AbstractVector{&lt;:Integer}) where {Tv,Ti}</code></pre><p>Bilaterally permute <code>A</code>, returning <code>PAQ</code> (<code>A[p,q]</code>). Column-permutation <code>q</code>&#39;s length must match <code>A</code>&#39;s column count (<code>length(q) == size(A, 2)</code>). Row-permutation <code>p</code>&#39;s length must match <code>A</code>&#39;s row count (<code>length(p) == size(A, 1)</code>).</p><p>For expert drivers and additional information, see <a href="../base/arrays.html#Base.permute!-Tuple{Any, AbstractVector}"><code>permute!</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = spdiagm(0 =&gt; [1, 2, 3, 4], 1 =&gt; [5, 6, 7])
4×4 SparseMatrixCSC{Int64, Int64} with 7 stored entries:
 1  5  ⋅  ⋅
 ⋅  2  6  ⋅
 ⋅  ⋅  3  7
 ⋅  ⋅  ⋅  4

julia&gt; permute(A, [4, 3, 2, 1], [1, 2, 3, 4])
4×4 SparseMatrixCSC{Int64, Int64} with 7 stored entries:
 ⋅  ⋅  ⋅  4
 ⋅  ⋅  3  7
 ⋅  2  6  ⋅
 1  5  ⋅  ⋅

julia&gt; permute(A, [1, 2, 3, 4], [4, 3, 2, 1])
4×4 SparseMatrixCSC{Int64, Int64} with 7 stored entries:
 ⋅  ⋅  5  1
 ⋅  6  2  ⋅
 7  3  ⋅  ⋅
 4  ⋅  ⋅  ⋅</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L1717-L1750">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.permute!-Union{Tuple{Tq}, Tuple{Tp}, Tuple{Ti}, Tuple{Tv}, Tuple{SparseMatrixCSC{Tv, Ti}, SparseMatrixCSC{Tv, Ti}, AbstractVector{Tp}, AbstractVector{Tq}}} where {Tv, Ti, Tp&lt;:Integer, Tq&lt;:Integer}" href="#Base.permute!-Union{Tuple{Tq}, Tuple{Tp}, Tuple{Ti}, Tuple{Tv}, Tuple{SparseMatrixCSC{Tv, Ti}, SparseMatrixCSC{Tv, Ti}, AbstractVector{Tp}, AbstractVector{Tq}}} where {Tv, Ti, Tp&lt;:Integer, Tq&lt;:Integer}"><code>Base.permute!</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">permute!(X::AbstractSparseMatrixCSC{Tv,Ti}, A::AbstractSparseMatrixCSC{Tv,Ti},
         p::AbstractVector{&lt;:Integer}, q::AbstractVector{&lt;:Integer},
         [C::AbstractSparseMatrixCSC{Tv,Ti}]) where {Tv,Ti}</code></pre><p>Bilaterally permute <code>A</code>, storing result <code>PAQ</code> (<code>A[p,q]</code>) in <code>X</code>. Stores intermediate result <code>(AQ)^T</code> (<code>transpose(A[:,q])</code>) in optional argument <code>C</code> if present. Requires that none of <code>X</code>, <code>A</code>, and, if present, <code>C</code> alias each other; to store result <code>PAQ</code> back into <code>A</code>, use the following method lacking <code>X</code>:</p><pre><code class="nohighlight hljs">permute!(A::AbstractSparseMatrixCSC{Tv,Ti}, p::AbstractVector{&lt;:Integer},
         q::AbstractVector{&lt;:Integer}[, C::AbstractSparseMatrixCSC{Tv,Ti},
         [workcolptr::Vector{Ti}]]) where {Tv,Ti}</code></pre><p><code>X</code>&#39;s dimensions must match those of <code>A</code> (<code>size(X, 1) == size(A, 1)</code> and <code>size(X, 2) == size(A, 2)</code>), and <code>X</code> must have enough storage to accommodate all allocated entries in <code>A</code> (<code>length(rowvals(X)) &gt;= nnz(A)</code> and <code>length(nonzeros(X)) &gt;= nnz(A)</code>). Column-permutation <code>q</code>&#39;s length must match <code>A</code>&#39;s column count (<code>length(q) == size(A, 2)</code>). Row-permutation <code>p</code>&#39;s length must match <code>A</code>&#39;s row count (<code>length(p) == size(A, 1)</code>).</p><p><code>C</code>&#39;s dimensions must match those of <code>transpose(A)</code> (<code>size(C, 1) == size(A, 2)</code> and <code>size(C, 2) == size(A, 1)</code>), and <code>C</code> must have enough storage to accommodate all allocated entries in <code>A</code> (<code>length(rowvals(C)) &gt;= nnz(A)</code> and <code>length(nonzeros(C)) &gt;= nnz(A)</code>).</p><p>For additional (algorithmic) information, and for versions of these methods that forgo argument checking, see (unexported) parent methods <code>unchecked_noalias_permute!</code> and <code>unchecked_aliasing_permute!</code>.</p><p>See also <a href="SparseArrays.html#SparseArrays.permute"><code>permute</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L1639-L1668">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.halfperm!" href="#SparseArrays.halfperm!"><code>SparseArrays.halfperm!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">halfperm!(X::AbstractSparseMatrixCSC{Tv,Ti}, A::AbstractSparseMatrixCSC{TvA,Ti},
          q::AbstractVector{&lt;:Integer}, f::Function = identity) where {Tv,TvA,Ti}</code></pre><p>Column-permute and transpose <code>A</code>, simultaneously applying <code>f</code> to each entry of <code>A</code>, storing the result <code>(f(A)Q)^T</code> (<code>map(f, transpose(A[:,q]))</code>) in <code>X</code>.</p><p>Element type <code>Tv</code> of <code>X</code> must match <code>f(::TvA)</code>, where <code>TvA</code> is the element type of <code>A</code>. <code>X</code>&#39;s dimensions must match those of <code>transpose(A)</code> (<code>size(X, 1) == size(A, 2)</code> and <code>size(X, 2) == size(A, 1)</code>), and <code>X</code> must have enough storage to accommodate all allocated entries in <code>A</code> (<code>length(rowvals(X)) &gt;= nnz(A)</code> and <code>length(nonzeros(X)) &gt;= nnz(A)</code>). Column-permutation <code>q</code>&#39;s length must match <code>A</code>&#39;s column count (<code>length(q) == size(A, 2)</code>).</p><p>This method is the parent of several methods performing transposition and permutation operations on <a href="SparseArrays.html#SparseArrays.SparseMatrixCSC"><code>SparseMatrixCSC</code></a>s. As this method performs no argument checking, prefer the safer child methods (<code>[c]transpose[!]</code>, <code>permute[!]</code>) to direct use.</p><p>This method implements the <code>HALFPERM</code> algorithm described in F. Gustavson, &quot;Two fast algorithms for sparse matrices: multiplication and permuted transposition,&quot; ACM TOMS 4(3), 250-269 (1978). The algorithm runs in <code>O(size(A, 1), size(A, 2), nnz(A))</code> time and requires no space beyond that passed in.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L1336-L1357">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.ftranspose!" href="#SparseArrays.ftranspose!"><code>SparseArrays.ftranspose!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ftranspose!(X::AbstractSparseMatrixCSC{Tv,Ti}, A::AbstractSparseMatrixCSC{Tv,Ti}, f::Function) where {Tv,Ti}</code></pre><p>Transpose <code>A</code> and store it in <code>X</code> while applying the function <code>f</code> to the non-zero elements. Does not remove the zeros created by <code>f</code>. <code>size(X)</code> must be equal to <code>size(transpose(A))</code>. No additional memory is allocated other than resizing the rowval and nzval of <code>X</code>, if needed.</p><p>See <code>halfperm!</code></p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/sparsematrix.jl#L1405-L1413">source</a></section></article><h1 id="stdlib-sparse-linalg-api"><a class="docs-heading-anchor" href="#stdlib-sparse-linalg-api">Sparse Linear Algebra API</a><a id="stdlib-sparse-linalg-api-1"></a><a class="docs-heading-anchor-permalink" href="#stdlib-sparse-linalg-api" title="Permalink"></a></h1><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LinearAlgebra.cholesky-stdlib-SparseArrays" href="#LinearAlgebra.cholesky-stdlib-SparseArrays"><code>LinearAlgebra.cholesky</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cholesky(A, NoPivot(); check = true) -&gt; Cholesky</code></pre><p>Compute the Cholesky factorization of a dense symmetric positive definite matrix <code>A</code> and return a <a href="LinearAlgebra.html#LinearAlgebra.Cholesky"><code>Cholesky</code></a> factorization. The matrix <code>A</code> can either be a <a href="LinearAlgebra.html#LinearAlgebra.Symmetric"><code>Symmetric</code></a> or <a href="LinearAlgebra.html#LinearAlgebra.Hermitian"><code>Hermitian</code></a> <a href="../base/arrays.html#Base.AbstractMatrix"><code>AbstractMatrix</code></a> or a <em>perfectly</em> symmetric or Hermitian <code>AbstractMatrix</code>.</p><p>The triangular Cholesky factor can be obtained from the factorization <code>F</code> via <code>F.L</code> and <code>F.U</code>, where <code>A ≈ F.U&#39; * F.U ≈ F.L * F.L&#39;</code>.</p><p>The following functions are available for <code>Cholesky</code> objects: <a href="../base/arrays.html#Base.size"><code>size</code></a>, <a href="../base/math.html#Base.:\\-Tuple{Any, Any}"><code>\</code></a>, <a href="../base/math.html#Base.inv-Tuple{Number}"><code>inv</code></a>, <a href="LinearAlgebra.html#LinearAlgebra.det"><code>det</code></a>, <a href="LinearAlgebra.html#LinearAlgebra.logdet"><code>logdet</code></a> and <a href="LinearAlgebra.html#LinearAlgebra.isposdef"><code>isposdef</code></a>.</p><p>If you have a matrix <code>A</code> that is slightly non-Hermitian due to roundoff errors in its construction, wrap it in <code>Hermitian(A)</code> before passing it to <code>cholesky</code> in order to treat it as perfectly Hermitian.</p><p>When <code>check = true</code>, an error is thrown if the decomposition fails. When <code>check = false</code>, responsibility for checking the decomposition&#39;s validity (via <a href="LinearAlgebra.html#LinearAlgebra.issuccess"><code>issuccess</code></a>) lies with the user.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [4. 12. -16.; 12. 37. -43.; -16. -43. 98.]
3×3 Matrix{Float64}:
   4.0   12.0  -16.0
  12.0   37.0  -43.0
 -16.0  -43.0   98.0

julia&gt; C = cholesky(A)
Cholesky{Float64, Matrix{Float64}}
U factor:
3×3 UpperTriangular{Float64, Matrix{Float64}}:
 2.0  6.0  -8.0
  ⋅   1.0   5.0
  ⋅    ⋅    3.0

julia&gt; C.U
3×3 UpperTriangular{Float64, Matrix{Float64}}:
 2.0  6.0  -8.0
  ⋅   1.0   5.0
  ⋅    ⋅    3.0

julia&gt; C.L
3×3 LowerTriangular{Float64, Matrix{Float64}}:
  2.0   ⋅    ⋅
  6.0  1.0   ⋅
 -8.0  5.0  3.0

julia&gt; C.L * C.U == A
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LinearAlgebra/src/cholesky.jl#L349-L400">source</a></section><section><div><pre><code class="language-julia hljs">cholesky(A, RowMaximum(); tol = 0.0, check = true) -&gt; CholeskyPivoted</code></pre><p>Compute the pivoted Cholesky factorization of a dense symmetric positive semi-definite matrix <code>A</code> and return a <a href="LinearAlgebra.html#LinearAlgebra.CholeskyPivoted"><code>CholeskyPivoted</code></a> factorization. The matrix <code>A</code> can either be a <a href="LinearAlgebra.html#LinearAlgebra.Symmetric"><code>Symmetric</code></a> or <a href="LinearAlgebra.html#LinearAlgebra.Hermitian"><code>Hermitian</code></a> <a href="../base/arrays.html#Base.AbstractMatrix"><code>AbstractMatrix</code></a> or a <em>perfectly</em> symmetric or Hermitian <code>AbstractMatrix</code>.</p><p>The triangular Cholesky factor can be obtained from the factorization <code>F</code> via <code>F.L</code> and <code>F.U</code>, and the permutation via <code>F.p</code>, where <code>A[F.p, F.p] ≈ Ur&#39; * Ur ≈ Lr * Lr&#39;</code> with <code>Ur = F.U[1:F.rank, :]</code> and <code>Lr = F.L[:, 1:F.rank]</code>, or alternatively <code>A ≈ Up&#39; * Up ≈ Lp * Lp&#39;</code> with <code>Up = F.U[1:F.rank, invperm(F.p)]</code> and <code>Lp = F.L[invperm(F.p), 1:F.rank]</code>.</p><p>The following functions are available for <code>CholeskyPivoted</code> objects: <a href="../base/arrays.html#Base.size"><code>size</code></a>, <a href="../base/math.html#Base.:\\-Tuple{Any, Any}"><code>\</code></a>, <a href="../base/math.html#Base.inv-Tuple{Number}"><code>inv</code></a>, <a href="LinearAlgebra.html#LinearAlgebra.det"><code>det</code></a>, and <a href="LinearAlgebra.html#LinearAlgebra.rank"><code>rank</code></a>.</p><p>The argument <code>tol</code> determines the tolerance for determining the rank. For negative values, the tolerance is the machine precision.</p><p>If you have a matrix <code>A</code> that is slightly non-Hermitian due to roundoff errors in its construction, wrap it in <code>Hermitian(A)</code> before passing it to <code>cholesky</code> in order to treat it as perfectly Hermitian.</p><p>When <code>check = true</code>, an error is thrown if the decomposition fails. When <code>check = false</code>, responsibility for checking the decomposition&#39;s validity (via <a href="LinearAlgebra.html#LinearAlgebra.issuccess"><code>issuccess</code></a>) lies with the user.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; X = [1.0, 2.0, 3.0, 4.0];

julia&gt; A = X * X&#39;;

julia&gt; C = cholesky(A, RowMaximum(), check = false)
CholeskyPivoted{Float64, Matrix{Float64}, Vector{Int64}}
U factor with rank 1:
4×4 UpperTriangular{Float64, Matrix{Float64}}:
 4.0  2.0  3.0  1.0
  ⋅   0.0  6.0  2.0
  ⋅    ⋅   9.0  3.0
  ⋅    ⋅    ⋅   1.0
permutation:
4-element Vector{Int64}:
 4
 2
 3
 1

julia&gt; C.U[1:C.rank, :]&#39; * C.U[1:C.rank, :] ≈ A[C.p, C.p]
true

julia&gt; l, u = C; # destructuring via iteration

julia&gt; l == C.L &amp;&amp; u == C.U
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LinearAlgebra/src/cholesky.jl#L414-L468">source</a></section><section><div><pre><code class="language-julia hljs">cholesky(A::SparseMatrixCSC; shift = 0.0, check = true, perm = nothing) -&gt; CHOLMOD.Factor</code></pre><p>Compute the Cholesky factorization of a sparse positive definite matrix <code>A</code>. <code>A</code> must be a <a href="SparseArrays.html#SparseArrays.SparseMatrixCSC"><code>SparseMatrixCSC</code></a> or a <a href="LinearAlgebra.html#LinearAlgebra.Symmetric"><code>Symmetric</code></a>/<a href="LinearAlgebra.html#LinearAlgebra.Hermitian"><code>Hermitian</code></a> view of a <code>SparseMatrixCSC</code>. Note that even if <code>A</code> doesn&#39;t have the type tag, it must still be symmetric or Hermitian. If <code>perm</code> is not given, a fill-reducing permutation is used. <code>F = cholesky(A)</code> is most frequently used to solve systems of equations with <code>F\b</code>, but also the methods <a href="LinearAlgebra.html#LinearAlgebra.diag"><code>diag</code></a>, <a href="LinearAlgebra.html#LinearAlgebra.det"><code>det</code></a>, and <a href="LinearAlgebra.html#LinearAlgebra.logdet"><code>logdet</code></a> are defined for <code>F</code>. You can also extract individual factors from <code>F</code>, using <code>F.L</code>. However, since pivoting is on by default, the factorization is internally represented as <code>A == P&#39;*L*L&#39;*P</code> with a permutation matrix <code>P</code>; using just <code>L</code> without accounting for <code>P</code> will give incorrect answers. To include the effects of permutation, it&#39;s typically preferable to extract &quot;combined&quot; factors like <code>PtL = F.PtL</code> (the equivalent of <code>P&#39;*L</code>) and <code>LtP = F.UP</code> (the equivalent of <code>L&#39;*P</code>).</p><p>When <code>check = true</code>, an error is thrown if the decomposition fails. When <code>check = false</code>, responsibility for checking the decomposition&#39;s validity (via <a href="LinearAlgebra.html#LinearAlgebra.issuccess"><code>issuccess</code></a>) lies with the user.</p><p>Setting the optional <code>shift</code> keyword argument computes the factorization of <code>A+shift*I</code> instead of <code>A</code>. If the <code>perm</code> argument is provided, it should be a permutation of <code>1:size(A,1)</code> giving the ordering to use (instead of CHOLMOD&#39;s default AMD ordering).</p><p><strong>Examples</strong></p><p>In the following example, the fill-reducing permutation used is <code>[3, 2, 1]</code>. If <code>perm</code> is set to <code>1:3</code> to enforce no permutation, the number of nonzero elements in the factor is 6.</p><pre><code class="language-julia-repl hljs">julia&gt; A = [2 1 1; 1 2 0; 1 0 2]
3×3 Matrix{Int64}:
 2  1  1
 1  2  0
 1  0  2

julia&gt; C = cholesky(sparse(A))
SparseArrays.CHOLMOD.Factor{Float64, Int64}
type:    LLt
method:  simplicial
maxnnz:  5
nnz:     5
success: true

julia&gt; C.p
3-element Vector{Int64}:
 3
 2
 1

julia&gt; L = sparse(C.L);

julia&gt; Matrix(L)
3×3 Matrix{Float64}:
 1.41421   0.0       0.0
 0.0       1.41421   0.0
 0.707107  0.707107  1.0

julia&gt; L * L&#39; ≈ A[C.p, C.p]
true

julia&gt; P = sparse(1:3, C.p, ones(3))
3×3 SparseMatrixCSC{Float64, Int64} with 3 stored entries:
  ⋅    ⋅   1.0
  ⋅   1.0   ⋅
 1.0   ⋅    ⋅

julia&gt; P&#39; * L * L&#39; * P ≈ A
true

julia&gt; C = cholesky(sparse(A), perm=1:3)
SparseArrays.CHOLMOD.Factor{Float64, Int64}
type:    LLt
method:  simplicial
maxnnz:  6
nnz:     6
success: true

julia&gt; L = sparse(C.L);

julia&gt; Matrix(L)
3×3 Matrix{Float64}:
 1.41421    0.0       0.0
 0.707107   1.22474   0.0
 0.707107  -0.408248  1.1547

julia&gt; L * L&#39; ≈ A
true</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This method uses the CHOLMOD<sup class="footnote-reference"><a id="citeref-ACM887" href="#footnote-ACM887">[ACM887]</a></sup><sup class="footnote-reference"><a id="citeref-DavisHager2009" href="#footnote-DavisHager2009">[DavisHager2009]</a></sup> library from <a href="https://github.com/DrTimothyAldenDavis/SuiteSparse">SuiteSparse</a>. CHOLMOD only supports real or complex types in single or double precision. Input matrices not of those element types will be converted to these types as appropriate.</p><p>Many other functions from CHOLMOD are wrapped but not exported from the <code>Base.SparseArrays.CHOLMOD</code> module.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/solvers/cholmod.jl#L1502-L1608">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LinearAlgebra.cholesky!-stdlib-SparseArrays" href="#LinearAlgebra.cholesky!-stdlib-SparseArrays"><code>LinearAlgebra.cholesky!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cholesky!(A::AbstractMatrix, NoPivot(); check = true) -&gt; Cholesky</code></pre><p>The same as <a href="LinearAlgebra.html#LinearAlgebra.cholesky"><code>cholesky</code></a>, but saves space by overwriting the input <code>A</code>, instead of creating a copy. An <a href="../base/base.html#Core.InexactError"><code>InexactError</code></a> exception is thrown if the factorization produces a number not representable by the element type of <code>A</code>, e.g. for integer types.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [1 2; 2 50]
2×2 Matrix{Int64}:
 1   2
 2  50

julia&gt; cholesky!(A)
ERROR: InexactError: Int64(6.782329983125268)
Stacktrace:
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LinearAlgebra/src/cholesky.jl#L274-L294">source</a></section><section><div><pre><code class="language-julia hljs">cholesky!(A::AbstractMatrix, RowMaximum(); tol = 0.0, check = true) -&gt; CholeskyPivoted</code></pre><p>The same as <a href="LinearAlgebra.html#LinearAlgebra.cholesky"><code>cholesky</code></a>, but saves space by overwriting the input <code>A</code>, instead of creating a copy. An <a href="../base/base.html#Core.InexactError"><code>InexactError</code></a> exception is thrown if the factorization produces a number not representable by the element type of <code>A</code>, e.g. for integer types.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LinearAlgebra/src/cholesky.jl#L325-L332">source</a></section><section><div><pre><code class="language-julia hljs">cholesky!(F::CHOLMOD.Factor, A::SparseMatrixCSC; shift = 0.0, check = true) -&gt; CHOLMOD.Factor</code></pre><p>Compute the Cholesky (<span>$LL&#39;$</span>) factorization of <code>A</code>, reusing the symbolic factorization <code>F</code>. <code>A</code> must be a <a href="SparseArrays.html#SparseArrays.SparseMatrixCSC"><code>SparseMatrixCSC</code></a> or a <a href="LinearAlgebra.html#LinearAlgebra.Symmetric"><code>Symmetric</code></a>/ <a href="LinearAlgebra.html#LinearAlgebra.Hermitian"><code>Hermitian</code></a> view of a <code>SparseMatrixCSC</code>. Note that even if <code>A</code> doesn&#39;t have the type tag, it must still be symmetric or Hermitian.</p><p>See also <a href="LinearAlgebra.html#LinearAlgebra.cholesky"><code>cholesky</code></a>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This method uses the CHOLMOD library from SuiteSparse, which only supports real or complex types in single or double precision. Input matrices not of those element types will be converted to these types as appropriate.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/solvers/cholmod.jl#L1466-L1481">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LinearAlgebra.lowrankupdate-stdlib-SparseArrays" href="#LinearAlgebra.lowrankupdate-stdlib-SparseArrays"><code>LinearAlgebra.lowrankupdate</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lowrankupdate(C::Cholesky, v::AbstractVector) -&gt; CC::Cholesky</code></pre><p>Update a Cholesky factorization <code>C</code> with the vector <code>v</code>. If <code>A = C.U&#39;C.U</code> then <code>CC = cholesky(C.U&#39;C.U + v*v&#39;)</code> but the computation of <code>CC</code> only uses <code>O(n^2)</code> operations.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LinearAlgebra/src/cholesky.jl#L821-L827">source</a></section><section><div><pre><code class="language-julia hljs">lowrankupdate(F::CHOLMOD.Factor, C::AbstractArray) -&gt; FF::CHOLMOD.Factor</code></pre><p>Get an <code>LDLt</code> Factorization of <code>A + C*C&#39;</code> given an <code>LDLt</code> or <code>LLt</code> factorization <code>F</code> of <code>A</code>.</p><p>The returned factor is always an <code>LDLt</code> factorization.</p><p>See also <a href="LinearAlgebra.html#LinearAlgebra.lowrankupdate!"><code>lowrankupdate!</code></a>, <a href="LinearAlgebra.html#LinearAlgebra.lowrankdowndate"><code>lowrankdowndate</code></a>, <a href="LinearAlgebra.html#LinearAlgebra.lowrankdowndate!"><code>lowrankdowndate!</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/solvers/cholmod.jl#L1767-L1775">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LinearAlgebra.lowrankupdate!-stdlib-SparseArrays" href="#LinearAlgebra.lowrankupdate!-stdlib-SparseArrays"><code>LinearAlgebra.lowrankupdate!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lowrankupdate!(C::Cholesky, v::AbstractVector) -&gt; CC::Cholesky</code></pre><p>Update a Cholesky factorization <code>C</code> with the vector <code>v</code>. If <code>A = C.U&#39;C.U</code> then <code>CC = cholesky(C.U&#39;C.U + v*v&#39;)</code> but the computation of <code>CC</code> only uses <code>O(n^2)</code> operations. The input factorization <code>C</code> is updated in place such that on exit <code>C == CC</code>. The vector <code>v</code> is destroyed during the computation.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LinearAlgebra/src/cholesky.jl#L722-L729">source</a></section><section><div><pre><code class="language-julia hljs">lowrankupdate!(F::CHOLMOD.Factor, C::AbstractArray)</code></pre><p>Update an <code>LDLt</code> or <code>LLt</code> Factorization <code>F</code> of <code>A</code> to a factorization of <code>A + C*C&#39;</code>.</p><p><code>LLt</code> factorizations are converted to <code>LDLt</code>.</p><p>See also <a href="LinearAlgebra.html#LinearAlgebra.lowrankupdate"><code>lowrankupdate</code></a>, <a href="LinearAlgebra.html#LinearAlgebra.lowrankdowndate"><code>lowrankdowndate</code></a>, <a href="LinearAlgebra.html#LinearAlgebra.lowrankdowndate!"><code>lowrankdowndate!</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/solvers/cholmod.jl#L1737-L1745">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LinearAlgebra.lowrankdowndate-stdlib-SparseArrays" href="#LinearAlgebra.lowrankdowndate-stdlib-SparseArrays"><code>LinearAlgebra.lowrankdowndate</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lowrankdowndate(C::Cholesky, v::AbstractVector) -&gt; CC::Cholesky</code></pre><p>Downdate a Cholesky factorization <code>C</code> with the vector <code>v</code>. If <code>A = C.U&#39;C.U</code> then <code>CC = cholesky(C.U&#39;C.U - v*v&#39;)</code> but the computation of <code>CC</code> only uses <code>O(n^2)</code> operations.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LinearAlgebra/src/cholesky.jl#L830-L836">source</a></section><section><div><pre><code class="language-julia hljs">lowrankdowndate(F::CHOLMOD.Factor, C::AbstractArray) -&gt; FF::CHOLMOD.Factor</code></pre><p>Get an <code>LDLt</code> Factorization of <code>A + C*C&#39;</code> given an <code>LDLt</code> or <code>LLt</code> factorization <code>F</code> of <code>A</code>.</p><p>The returned factor is always an <code>LDLt</code> factorization.</p><p>See also <a href="LinearAlgebra.html#LinearAlgebra.lowrankdowndate!"><code>lowrankdowndate!</code></a>, <a href="LinearAlgebra.html#LinearAlgebra.lowrankupdate"><code>lowrankupdate</code></a>, <a href="LinearAlgebra.html#LinearAlgebra.lowrankupdate!"><code>lowrankupdate!</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/solvers/cholmod.jl#L1782-L1790">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LinearAlgebra.lowrankdowndate!-stdlib-SparseArrays" href="#LinearAlgebra.lowrankdowndate!-stdlib-SparseArrays"><code>LinearAlgebra.lowrankdowndate!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lowrankdowndate!(C::Cholesky, v::AbstractVector) -&gt; CC::Cholesky</code></pre><p>Downdate a Cholesky factorization <code>C</code> with the vector <code>v</code>. If <code>A = C.U&#39;C.U</code> then <code>CC = cholesky(C.U&#39;C.U - v*v&#39;)</code> but the computation of <code>CC</code> only uses <code>O(n^2)</code> operations. The input factorization <code>C</code> is updated in place such that on exit <code>C == CC</code>. The vector <code>v</code> is destroyed during the computation.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LinearAlgebra/src/cholesky.jl#L768-L775">source</a></section><section><div><pre><code class="language-julia hljs">lowrankdowndate!(F::CHOLMOD.Factor, C::AbstractArray)</code></pre><p>Update an <code>LDLt</code> or <code>LLt</code> Factorization <code>F</code> of <code>A</code> to a factorization of <code>A - C*C&#39;</code>.</p><p><code>LLt</code> factorizations are converted to <code>LDLt</code>.</p><p>See also <a href="LinearAlgebra.html#LinearAlgebra.lowrankdowndate"><code>lowrankdowndate</code></a>, <a href="LinearAlgebra.html#LinearAlgebra.lowrankupdate"><code>lowrankupdate</code></a>, <a href="LinearAlgebra.html#LinearAlgebra.lowrankupdate!"><code>lowrankupdate!</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/solvers/cholmod.jl#L1752-L1760">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="SparseArrays.CHOLMOD.lowrankupdowndate!-stdlib-SparseArrays" href="#SparseArrays.CHOLMOD.lowrankupdowndate!-stdlib-SparseArrays"><code>SparseArrays.CHOLMOD.lowrankupdowndate!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lowrankupdowndate!(F::CHOLMOD.Factor, C::Sparse, update::Cint)</code></pre><p>Update an <code>LDLt</code> or <code>LLt</code> Factorization <code>F</code> of <code>A</code> to a factorization of <code>A ± C*C&#39;</code>.</p><p>If sparsity preserving factorization is used, i.e. <code>L*L&#39; == P*A*P&#39;</code> then the new factor will be <code>L*L&#39; == P*A*P&#39; + C&#39;*C</code></p><p><code>update</code>: <code>Cint(1)</code> for <code>A + CC&#39;</code>, <code>Cint(0)</code> for <code>A - CC&#39;</code></p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/solvers/cholmod.jl#L1721-L1730">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LinearAlgebra.ldlt-stdlib-SparseArrays" href="#LinearAlgebra.ldlt-stdlib-SparseArrays"><code>LinearAlgebra.ldlt</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ldlt(S::SymTridiagonal) -&gt; LDLt</code></pre><p>Compute an <code>LDLt</code> (i.e., <span>$LDL^T$</span>) factorization of the real symmetric tridiagonal matrix <code>S</code> such that <code>S = L*Diagonal(d)*L&#39;</code> where <code>L</code> is a unit lower triangular matrix and <code>d</code> is a vector. The main use of an <code>LDLt</code> factorization <code>F = ldlt(S)</code> is to solve the linear system of equations <code>Sx = b</code> with <code>F\b</code>.</p><p>See also <a href="LinearAlgebra.html#LinearAlgebra.bunchkaufman"><code>bunchkaufman</code></a> for a similar, but pivoted, factorization of arbitrary symmetric or Hermitian matrices.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; S = SymTridiagonal([3., 4., 5.], [1., 2.])
3×3 SymTridiagonal{Float64, Vector{Float64}}:
 3.0  1.0   ⋅
 1.0  4.0  2.0
  ⋅   2.0  5.0

julia&gt; ldltS = ldlt(S);

julia&gt; b = [6., 7., 8.];

julia&gt; ldltS \ b
3-element Vector{Float64}:
 1.7906976744186047
 0.627906976744186
 1.3488372093023255

julia&gt; S \ b
3-element Vector{Float64}:
 1.7906976744186047
 0.627906976744186
 1.3488372093023255</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LinearAlgebra/src/ldlt.jl#L129-L162">source</a></section><section><div><pre><code class="language-julia hljs">ldlt(A::SparseMatrixCSC; shift = 0.0, check = true, perm=nothing) -&gt; CHOLMOD.Factor</code></pre><p>Compute the <span>$LDL&#39;$</span> factorization of a sparse matrix <code>A</code>. <code>A</code> must be a <a href="SparseArrays.html#SparseArrays.SparseMatrixCSC"><code>SparseMatrixCSC</code></a> or a <a href="LinearAlgebra.html#LinearAlgebra.Symmetric"><code>Symmetric</code></a>/<a href="LinearAlgebra.html#LinearAlgebra.Hermitian"><code>Hermitian</code></a> view of a <code>SparseMatrixCSC</code>. Note that even if <code>A</code> doesn&#39;t have the type tag, it must still be symmetric or Hermitian. A fill-reducing permutation is used. <code>F = ldlt(A)</code> is most frequently used to solve systems of equations <code>A*x = b</code> with <code>F\b</code>. The returned factorization object <code>F</code> also supports the methods <a href="LinearAlgebra.html#LinearAlgebra.diag"><code>diag</code></a>, <a href="LinearAlgebra.html#LinearAlgebra.det"><code>det</code></a>, <a href="LinearAlgebra.html#LinearAlgebra.logdet"><code>logdet</code></a>, and <a href="../base/math.html#Base.inv-Tuple{Number}"><code>inv</code></a>. You can extract individual factors from <code>F</code> using <code>F.L</code>. However, since pivoting is on by default, the factorization is internally represented as <code>A == P&#39;*L*D*L&#39;*P</code> with a permutation matrix <code>P</code>; using just <code>L</code> without accounting for <code>P</code> will give incorrect answers. To include the effects of permutation, it is typically preferable to extract &quot;combined&quot; factors like <code>PtL = F.PtL</code> (the equivalent of <code>P&#39;*L</code>) and <code>LtP = F.UP</code> (the equivalent of <code>L&#39;*P</code>). The complete list of supported factors is <code>:L, :PtL, :D, :UP, :U, :LD, :DU, :PtLD, :DUP</code>.</p><p>When <code>check = true</code>, an error is thrown if the decomposition fails. When <code>check = false</code>, responsibility for checking the decomposition&#39;s validity (via <a href="LinearAlgebra.html#LinearAlgebra.issuccess"><code>issuccess</code></a>) lies with the user.</p><p>Setting the optional <code>shift</code> keyword argument computes the factorization of <code>A+shift*I</code> instead of <code>A</code>. If the <code>perm</code> argument is provided, it should be a permutation of <code>1:size(A,1)</code> giving the ordering to use (instead of CHOLMOD&#39;s default AMD ordering).</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This method uses the CHOLMOD<sup class="footnote-reference"><a id="citeref-ACM887" href="#footnote-ACM887">[ACM887]</a></sup><sup class="footnote-reference"><a id="citeref-DavisHager2009" href="#footnote-DavisHager2009">[DavisHager2009]</a></sup> library from <a href="https://github.com/DrTimothyAldenDavis/SuiteSparse">SuiteSparse</a>. CHOLMOD only supports real or complex types in single or double precision. Input matrices not of those element types will be converted to these types as appropriate.</p><p>Many other functions from CHOLMOD are wrapped but not exported from the <code>Base.SparseArrays.CHOLMOD</code> module.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/solvers/cholmod.jl#L1675-L1712">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LinearAlgebra.lu-stdlib-SparseArrays" href="#LinearAlgebra.lu-stdlib-SparseArrays"><code>LinearAlgebra.lu</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lu(A::AbstractSparseMatrixCSC; check = true, q = nothing, control = get_umfpack_control()) -&gt; F::UmfpackLU</code></pre><p>Compute the LU factorization of a sparse matrix <code>A</code>.</p><p>For sparse <code>A</code> with real or complex element type, the return type of <code>F</code> is <code>UmfpackLU{Tv, Ti}</code>, with <code>Tv</code> = <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> or <code>ComplexF64</code> respectively and <code>Ti</code> is an integer type (<a href="../base/numbers.html#Core.Int32"><code>Int32</code></a> or <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>).</p><p>When <code>check = true</code>, an error is thrown if the decomposition fails. When <code>check = false</code>, responsibility for checking the decomposition&#39;s validity (via <a href="LinearAlgebra.html#LinearAlgebra.issuccess"><code>issuccess</code></a>) lies with the user.</p><p>The permutation <code>q</code> can either be a permutation vector or <code>nothing</code>. If no permutation vector is provided or <code>q</code> is <code>nothing</code>, UMFPACK&#39;s default is used. If the permutation is not zero-based, a zero-based copy is made.</p><p>The <code>control</code> vector defaults to the Julia SparseArrays package&#39;s default configuration for UMFPACK (NB: this is modified from the UMFPACK defaults to disable iterative refinement), but can be changed by passing a vector of length <code>UMFPACK_CONTROL</code>, see the UMFPACK manual for possible configurations.  For example to reenable iterative refinement:</p><pre><code class="nohighlight hljs">umfpack_control = SparseArrays.UMFPACK.get_umfpack_control(Float64, Int64) # read Julia default configuration for a Float64 sparse matrix
SparseArrays.UMFPACK.show_umf_ctrl(umfpack_control) # optional - display values
umfpack_control[SparseArrays.UMFPACK.JL_UMFPACK_IRSTEP] = 2.0 # reenable iterative refinement (2 is UMFPACK default max iterative refinement steps)

Alu = lu(A; control = umfpack_control)
x = Alu \ b   # solve Ax = b, including UMFPACK iterative refinement</code></pre><p>The individual components of the factorization <code>F</code> can be accessed by indexing:</p><table><tr><th style="text-align: left">Component</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left"><code>L</code></td><td style="text-align: left"><code>L</code> (lower triangular) part of <code>LU</code></td></tr><tr><td style="text-align: left"><code>U</code></td><td style="text-align: left"><code>U</code> (upper triangular) part of <code>LU</code></td></tr><tr><td style="text-align: left"><code>p</code></td><td style="text-align: left">right permutation <code>Vector</code></td></tr><tr><td style="text-align: left"><code>q</code></td><td style="text-align: left">left permutation <code>Vector</code></td></tr><tr><td style="text-align: left"><code>Rs</code></td><td style="text-align: left"><code>Vector</code> of scaling factors</td></tr><tr><td style="text-align: left"><code>:</code></td><td style="text-align: left"><code>(L,U,p,q,Rs)</code> components</td></tr></table><p>The relation between <code>F</code> and <code>A</code> is</p><p><code>F.L*F.U == (F.Rs .* A)[F.p, F.q]</code></p><p><code>F</code> further supports the following functions:</p><ul><li><a href="../base/math.html#Base.:\\-Tuple{Any, Any}"><code>\</code></a></li><li><a href="LinearAlgebra.html#LinearAlgebra.det"><code>det</code></a></li></ul><p>See also <a href="LinearAlgebra.html#LinearAlgebra.lu!"><code>lu!</code></a></p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p><code>lu(A::AbstractSparseMatrixCSC)</code> uses the UMFPACK<sup class="footnote-reference"><a id="citeref-ACM832" href="#footnote-ACM832">[ACM832]</a></sup> library that is part of <a href="https://github.com/DrTimothyAldenDavis/SuiteSparse">SuiteSparse</a>. As this library only supports sparse matrices with <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> or <code>ComplexF64</code> elements, <code>lu</code> converts <code>A</code> into a copy that is of type <code>SparseMatrixCSC{Float64}</code> or <code>SparseMatrixCSC{ComplexF64}</code> as appropriate.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/solvers/umfpack.jl#L325-L383">source</a></section><section><div><pre><code class="language-julia hljs">lu(A, pivot = RowMaximum(); check = true, allowsingular = false) -&gt; F::LU</code></pre><p>Compute the LU factorization of <code>A</code>.</p><p>When <code>check = true</code>, an error is thrown if the decomposition fails. When <code>check = false</code>, responsibility for checking the decomposition&#39;s validity (via <a href="LinearAlgebra.html#LinearAlgebra.issuccess"><code>issuccess</code></a>) lies with the user.</p><p>By default, with <code>check = true</code>, an error is also thrown when the decomposition produces valid factors, but the upper-triangular factor <code>U</code> is rank-deficient. This may be changed by passing <code>allowsingular = true</code>.</p><p>In most cases, if <code>A</code> is a subtype <code>S</code> of <code>AbstractMatrix{T}</code> with an element type <code>T</code> supporting <code>+</code>, <code>-</code>, <code>*</code> and <code>/</code>, the return type is <code>LU{T,S{T}}</code>.</p><p>In general, LU factorization involves a permutation of the rows of the matrix (corresponding to the <code>F.p</code> output described below), known as &quot;pivoting&quot; (because it corresponds to choosing which row contains the &quot;pivot&quot;, the diagonal entry of <code>F.U</code>). One of the following pivoting strategies can be selected via the optional <code>pivot</code> argument:</p><ul><li><code>RowMaximum()</code> (default): the standard pivoting strategy; the pivot corresponds to the element of maximum absolute value among the remaining, to be factorized rows. This pivoting strategy requires the element type to also support <a href="../base/math.html#Base.abs"><code>abs</code></a> and <a href="../base/math.html#Base.:&lt;"><code>&lt;</code></a>. (This is generally the only numerically stable option for floating-point matrices.)</li><li><code>RowNonZero()</code>: the pivot corresponds to the first non-zero element among the remaining, to be factorized rows.  (This corresponds to the typical choice in hand calculations, and is also useful for more general algebraic number types that support <a href="../base/numbers.html#Base.iszero"><code>iszero</code></a> but not <code>abs</code> or <code>&lt;</code>.)</li><li><code>NoPivot()</code>: pivoting turned off (will fail if a zero entry is encountered in a pivot position, even when <code>allowsingular = true</code>).</li></ul><p>The individual components of the factorization <code>F</code> can be accessed via <a href="../base/base.html#Base.getproperty"><code>getproperty</code></a>:</p><table><tr><th style="text-align: left">Component</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left"><code>F.L</code></td><td style="text-align: left"><code>L</code> (lower triangular) part of <code>LU</code></td></tr><tr><td style="text-align: left"><code>F.U</code></td><td style="text-align: left"><code>U</code> (upper triangular) part of <code>LU</code></td></tr><tr><td style="text-align: left"><code>F.p</code></td><td style="text-align: left">(right) permutation <code>Vector</code></td></tr><tr><td style="text-align: left"><code>F.P</code></td><td style="text-align: left">(right) permutation <code>Matrix</code></td></tr></table><p>Iterating the factorization produces the components <code>F.L</code>, <code>F.U</code>, and <code>F.p</code>.</p><p>The relationship between <code>F</code> and <code>A</code> is</p><p><code>F.L*F.U == A[F.p, :]</code></p><p><code>F</code> further supports the following functions:</p><table><tr><th style="text-align: left">Supported function</th><th style="text-align: left"><code>LU</code></th><th style="text-align: left"><code>LU{T,Tridiagonal{T}}</code></th></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.:/"><code>/</code></a></td><td style="text-align: left">✓</td><td style="text-align: left"></td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.:\\-Tuple{Any, Any}"><code>\</code></a></td><td style="text-align: left">✓</td><td style="text-align: left">✓</td></tr><tr><td style="text-align: left"><a href="../base/math.html#Base.inv-Tuple{Number}"><code>inv</code></a></td><td style="text-align: left">✓</td><td style="text-align: left">✓</td></tr><tr><td style="text-align: left"><a href="LinearAlgebra.html#LinearAlgebra.det"><code>det</code></a></td><td style="text-align: left">✓</td><td style="text-align: left">✓</td></tr><tr><td style="text-align: left"><a href="LinearAlgebra.html#LinearAlgebra.logdet"><code>logdet</code></a></td><td style="text-align: left">✓</td><td style="text-align: left">✓</td></tr><tr><td style="text-align: left"><a href="LinearAlgebra.html#LinearAlgebra.logabsdet"><code>logabsdet</code></a></td><td style="text-align: left">✓</td><td style="text-align: left">✓</td></tr><tr><td style="text-align: left"><a href="../base/arrays.html#Base.size"><code>size</code></a></td><td style="text-align: left">✓</td><td style="text-align: left">✓</td></tr></table><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>The <code>allowsingular</code> keyword argument was added in Julia 1.11.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [4 3; 6 3]
2×2 Matrix{Int64}:
 4  3
 6  3

julia&gt; F = lu(A)
LU{Float64, Matrix{Float64}, Vector{Int64}}
L factor:
2×2 Matrix{Float64}:
 1.0       0.0
 0.666667  1.0
U factor:
2×2 Matrix{Float64}:
 6.0  3.0
 0.0  1.0

julia&gt; F.L * F.U == A[F.p, :]
true

julia&gt; l, u, p = lu(A); # destructuring via iteration

julia&gt; l == F.L &amp;&amp; u == F.U &amp;&amp; p == F.p
true

julia&gt; lu([1 2; 1 2], allowsingular = true)
LU{Float64, Matrix{Float64}, Vector{Int64}}
L factor:
2×2 Matrix{Float64}:
 1.0  0.0
 1.0  1.0
U factor (rank-deficient):
2×2 Matrix{Float64}:
 1.0  2.0
 0.0  0.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LinearAlgebra/src/lu.jl#L240-L340">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="LinearAlgebra.qr-stdlib-SparseArrays" href="#LinearAlgebra.qr-stdlib-SparseArrays"><code>LinearAlgebra.qr</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">qr(A::SparseMatrixCSC; tol=_default_tol(A), ordering=ORDERING_DEFAULT) -&gt; QRSparse</code></pre><p>Compute the <code>QR</code> factorization of a sparse matrix <code>A</code>. Fill-reducing row and column permutations are used such that <code>F.R = F.Q&#39;*A[F.prow,F.pcol]</code>. The main application of this type is to solve least squares or underdetermined problems with <a href="../base/math.html#Base.:\\-Tuple{Any, Any}"><code>\</code></a>. The function calls the C library SPQR<sup class="footnote-reference"><a id="citeref-ACM933" href="#footnote-ACM933">[ACM933]</a></sup>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p><code>qr(A::SparseMatrixCSC)</code> uses the SPQR library that is part of <a href="https://github.com/DrTimothyAldenDavis/SuiteSparse">SuiteSparse</a>. As this library only supports sparse matrices with <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> or <code>ComplexF64</code> elements, as of Julia v1.4 <code>qr</code> converts <code>A</code> into a copy that is of type <code>SparseMatrixCSC{Float64}</code> or <code>SparseMatrixCSC{ComplexF64}</code> as appropriate.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = sparse([1,2,3,4], [1,1,2,2], [1.0,1.0,1.0,1.0])
4×2 SparseMatrixCSC{Float64, Int64} with 4 stored entries:
 1.0   ⋅
 1.0   ⋅
  ⋅   1.0
  ⋅   1.0

julia&gt; qr(A)
SparseArrays.SPQR.QRSparse{Float64, Int64}
Q factor:
4×4 SparseArrays.SPQR.QRSparseQ{Float64, Int64}
R factor:
2×2 SparseMatrixCSC{Float64, Int64} with 2 stored entries:
 -1.41421    ⋅
   ⋅       -1.41421
Row permutation:
4-element Vector{Int64}:
 1
 3
 4
 2
Column permutation:
2-element Vector{Int64}:
 1
 2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaSparse/SparseArrays.jl/blob/242035184c0d539bdb5e64bf26eb7726b123db14/src/solvers/spqr.jl#L151-L194">source</a></section><section><div><pre><code class="language-julia hljs">qr(A, pivot = NoPivot(); blocksize) -&gt; F</code></pre><p>Compute the QR factorization of the matrix <code>A</code>: an orthogonal (or unitary if <code>A</code> is complex-valued) matrix <code>Q</code>, and an upper triangular matrix <code>R</code> such that</p><p class="math-container">\[A = Q R\]</p><p>The returned object <code>F</code> stores the factorization in a packed format:</p><ul><li><p>if <code>pivot == ColumnNorm()</code> then <code>F</code> is a <a href="LinearAlgebra.html#LinearAlgebra.QRPivoted"><code>QRPivoted</code></a> object,</p></li><li><p>otherwise if the element type of <code>A</code> is a BLAS type (<a href="../base/numbers.html#Core.Float32"><code>Float32</code></a>, <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a>, <code>ComplexF32</code> or <code>ComplexF64</code>), then <code>F</code> is a <a href="LinearAlgebra.html#LinearAlgebra.QRCompactWY"><code>QRCompactWY</code></a> object,</p></li><li><p>otherwise <code>F</code> is a <a href="LinearAlgebra.html#LinearAlgebra.QR"><code>QR</code></a> object.</p></li></ul><p>The individual components of the decomposition <code>F</code> can be retrieved via property accessors:</p><ul><li><code>F.Q</code>: the orthogonal/unitary matrix <code>Q</code></li><li><code>F.R</code>: the upper triangular matrix <code>R</code></li><li><code>F.p</code>: the permutation vector of the pivot (<a href="LinearAlgebra.html#LinearAlgebra.QRPivoted"><code>QRPivoted</code></a> only)</li><li><code>F.P</code>: the permutation matrix of the pivot (<a href="LinearAlgebra.html#LinearAlgebra.QRPivoted"><code>QRPivoted</code></a> only)</li></ul><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>Each reference to the upper triangular factor via <code>F.R</code> allocates a new array. It is therefore advisable to cache that array, say, by <code>R = F.R</code> and continue working with <code>R</code>.</p></div></div><p>Iterating the decomposition produces the components <code>Q</code>, <code>R</code>, and if extant <code>p</code>.</p><p>The following functions are available for the <code>QR</code> objects: <a href="../base/math.html#Base.inv-Tuple{Number}"><code>inv</code></a>, <a href="../base/arrays.html#Base.size"><code>size</code></a>, and <a href="../base/math.html#Base.:\\-Tuple{Any, Any}"><code>\</code></a>. When <code>A</code> is rectangular, <code>\</code> will return a least squares solution and if the solution is not unique, the one with smallest norm is returned. When <code>A</code> is not full rank, factorization with (column) pivoting is required to obtain a minimum norm solution.</p><p>Multiplication with respect to either full/square or non-full/square <code>Q</code> is allowed, i.e. both <code>F.Q*F.R</code> and <code>F.Q*A</code> are supported. A <code>Q</code> matrix can be converted into a regular matrix with <a href="../base/arrays.html#Base.Matrix"><code>Matrix</code></a>. This operation returns the &quot;thin&quot; Q factor, i.e., if <code>A</code> is <code>m</code>×<code>n</code> with <code>m&gt;=n</code>, then <code>Matrix(F.Q)</code> yields an <code>m</code>×<code>n</code> matrix with orthonormal columns.  To retrieve the &quot;full&quot; Q factor, an <code>m</code>×<code>m</code> orthogonal matrix, use <code>F.Q*I</code> or <code>collect(F.Q)</code>. If <code>m&lt;=n</code>, then <code>Matrix(F.Q)</code> yields an <code>m</code>×<code>m</code> orthogonal matrix.</p><p>The block size for QR decomposition can be specified by keyword argument <code>blocksize :: Integer</code> when <code>pivot == NoPivot()</code> and <code>A isa StridedMatrix{&lt;:BlasFloat}</code>. It is ignored when <code>blocksize &gt; minimum(size(A))</code>. See <a href="LinearAlgebra.html#LinearAlgebra.QRCompactWY"><code>QRCompactWY</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.4</header><div class="admonition-body"><p>The <code>blocksize</code> keyword argument requires Julia 1.4 or later.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; A = [3.0 -6.0; 4.0 -8.0; 0.0 1.0]
3×2 Matrix{Float64}:
 3.0  -6.0
 4.0  -8.0
 0.0   1.0

julia&gt; F = qr(A)
LinearAlgebra.QRCompactWY{Float64, Matrix{Float64}, Matrix{Float64}}
Q factor: 3×3 LinearAlgebra.QRCompactWYQ{Float64, Matrix{Float64}, Matrix{Float64}}
R factor:
2×2 Matrix{Float64}:
 -5.0  10.0
  0.0  -1.0

julia&gt; F.Q * F.R == A
true</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p><code>qr</code> returns multiple types because LAPACK uses several representations that minimize the memory storage requirements of products of Householder elementary reflectors, so that the <code>Q</code> and <code>R</code> matrices can be stored compactly rather as two separate dense matrices.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/LinearAlgebra/src/qr.jl#L343-L421">source</a></section></article><h1 id="Noteworthy-External-Sparse-Packages"><a class="docs-heading-anchor" href="#Noteworthy-External-Sparse-Packages">Noteworthy External Sparse Packages</a><a id="Noteworthy-External-Sparse-Packages-1"></a><a class="docs-heading-anchor-permalink" href="#Noteworthy-External-Sparse-Packages" title="Permalink"></a></h1><p>Several other Julia packages provide sparse matrix implementations that should be mentioned:</p><ol><li><p><a href="https://github.com/JuliaSparse/SuiteSparseGraphBLAS.jl">SuiteSparseGraphBLAS.jl</a> is a wrapper over the fast, multithreaded SuiteSparse:GraphBLAS C library. On CPU this is typically the fastest option, often significantly outperforming MKLSparse.</p></li><li><p><a href="https://github.com/JuliaGPU/CUDA.jl">CUDA.jl</a> exposes the <a href="https://docs.nvidia.com/cuda/cusparse/index.html">CUSPARSE</a> library for GPU sparse matrix operations.</p></li><li><p><a href="https://github.com/gridap/SparseMatricesCSR.jl">SparseMatricesCSR.jl</a> provides a Julia native implementation of the Compressed Sparse Rows (CSR) format.</p></li><li><p><a href="https://github.com/JuliaSparse/MKLSparse.jl">MKLSparse.jl</a> accelerates SparseArrays sparse-dense matrix operations using Intel&#39;s MKL library.</p></li><li><p><a href="https://github.com/Jutho/SparseArrayKit.jl">SparseArrayKit.jl</a> available for multidimensional sparse arrays.</p></li><li><p><a href="https://github.com/QuantumBFS/LuxurySparse.jl">LuxurySparse.jl</a> provides static sparse array formats, as well as a coordinate format.</p></li><li><p><a href="https://github.com/j-fu/ExtendableSparse.jl">ExtendableSparse.jl</a> enables fast insertion into sparse matrices using a lazy approach to new stored indices.</p></li><li><p><a href="https://github.com/willow-ahrens/Finch.jl">Finch.jl</a> supports extensive multidimensional sparse array formats and operations through a mini tensor language and compiler, all in native Julia. Support for COO, CSF, CSR, CSC and more, as well as operations like broadcast, reduce, etc. and custom operations.</p></li></ol><p>External packages providing sparse direct solvers:</p><ol><li><a href="https://github.com/JuliaSparse/KLU.jl">KLU.jl</a></li><li><a href="https://github.com/JuliaSparse/Pardiso.jl/">Pardiso.jl</a></li></ol><p>External packages providing solvers for iterative solution of eigensystems and singular value decompositions:</p><ol><li><a href="https://github.com/JuliaLinearAlgebra/ArnoldiMethod.jl">ArnoldiMethods.jl</a></li><li><a href="https://github.com/Jutho/KrylovKit.jl">KrylovKit</a></li><li><a href="https://github.com/JuliaLinearAlgebra/Arpack.jl">Arpack.jl</a></li></ol><p>External packages for working with graphs:</p><ol><li><a href="https://github.com/JuliaGraphs/Graphs.jl">Graphs.jl</a></li></ol><section class="footnotes is-size-7"><ul><li class="footnote" id="footnote-ACM887"><a class="tag is-link" href="#citeref-ACM887">ACM887</a>Chen, Y., Davis, T. A., Hager, W. W., &amp; Rajamanickam, S. (2008). Algorithm 887: CHOLMOD, Supernodal Sparse Cholesky Factorization and Update/Downdate. ACM Trans. Math. Softw., 35(3). <a href="https://doi.org/10.1145/1391989.1391995">doi:10.1145/1391989.1391995</a></li><li class="footnote" id="footnote-DavisHager2009"><a class="tag is-link" href="#citeref-DavisHager2009">DavisHager2009</a>Davis, Timothy A., &amp; Hager, W. W. (2009). Dynamic Supernodes in Sparse Cholesky Update/Downdate and Triangular Solves. ACM Trans. Math. Softw., 35(4). <a href="https://doi.org/10.1145/1462173.1462176">doi:10.1145/1462173.1462176</a></li><li class="footnote" id="footnote-ACM832"><a class="tag is-link" href="#citeref-ACM832">ACM832</a>Davis, Timothy A. (2004b). Algorithm 832: UMFPACK V4.3–-an Unsymmetric-Pattern Multifrontal Method. ACM Trans. Math. Softw., 30(2), 196–199. <a href="https://doi.org/10.1145/992200.992206">doi:10.1145/992200.992206</a></li><li class="footnote" id="footnote-ACM933"><a class="tag is-link" href="#citeref-ACM933">ACM933</a>Foster, L. V., &amp; Davis, T. A. (2013). Algorithm 933: Reliable Calculation of Numerical Rank, Null Space Bases, Pseudoinverse Solutions, and Basic Solutions Using SuitesparseQR. ACM Trans. Math. Softw., 40(1). <a href="https://doi.org/10.1145/2513109.2513116">doi:10.1145/2513109.2513116</a></li></ul></section></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="Sockets.html">« Sockets</a><a class="docs-footer-nextpage" href="Statistics.html">Statistics »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
