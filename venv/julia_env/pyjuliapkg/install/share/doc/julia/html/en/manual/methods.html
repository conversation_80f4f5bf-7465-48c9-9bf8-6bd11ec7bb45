<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Methods · The Julia Language</title><meta name="title" content="Methods · The Julia Language"/><meta property="og:title" content="Methods · The Julia Language"/><meta property="twitter:title" content="Methods · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li class="is-active"><a class="tocitem" href="methods.html">Methods</a><ul class="internal"><li><a class="tocitem" href="#Defining-Methods"><span>Defining Methods</span></a></li><li><a class="tocitem" href="#man-method-specializations"><span>Method specializations</span></a></li><li><a class="tocitem" href="#man-ambiguities"><span>Method Ambiguities</span></a></li><li><a class="tocitem" href="#Parametric-Methods"><span>Parametric Methods</span></a></li><li><a class="tocitem" href="#Redefining-Methods"><span>Redefining Methods</span></a></li><li><a class="tocitem" href="#Design-Patterns-with-Parametric-Methods"><span>Design Patterns with Parametric Methods</span></a></li><li><a class="tocitem" href="#Parametrically-constrained-Varargs-methods"><span>Parametrically-constrained Varargs methods</span></a></li><li><a class="tocitem" href="#Note-on-Optional-and-keyword-Arguments"><span>Note on Optional and keyword Arguments</span></a></li><li><a class="tocitem" href="#Function-like-objects"><span>Function-like objects</span></a></li><li><a class="tocitem" href="#Empty-generic-functions"><span>Empty generic functions</span></a></li><li><a class="tocitem" href="#man-method-design-ambiguities"><span>Method design and the avoidance of ambiguities</span></a></li><li><a class="tocitem" href="#Defining-methods-in-local-scope"><span>Defining methods in local scope</span></a></li></ul></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="methods.html">Methods</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="methods.html">Methods</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/methods.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Methods"><a class="docs-heading-anchor" href="#Methods">Methods</a><a id="Methods-1"></a><a class="docs-heading-anchor-permalink" href="#Methods" title="Permalink"></a></h1><p>Recall from <a href="functions.html#man-functions">Functions</a> that a function is an object that maps a tuple of arguments to a return value, or throws an exception if no appropriate value can be returned. It is common for the same conceptual function or operation to be implemented quite differently for different types of arguments: adding two integers is very different from adding two floating-point numbers, both of which are distinct from adding an integer to a floating-point number. Despite their implementation differences, these operations all fall under the general concept of &quot;addition&quot;. Accordingly, in Julia, these behaviors all belong to a single object: the <code>+</code> function.</p><p>To facilitate using many different implementations of the same concept smoothly, functions need not be defined all at once, but can rather be defined piecewise by providing specific behaviors for certain combinations of argument types and counts. A definition of one possible behavior for a function is called a <em>method</em>. Thus far, we have presented only examples of functions defined with a single method, applicable to all types of arguments. However, the signatures of method definitions can be annotated to indicate the types of arguments in addition to their number, and more than a single method definition may be provided. When a function is applied to a particular tuple of arguments, the most specific method applicable to those arguments is applied. Thus, the overall behavior of a function is a patchwork of the behaviors of its various method definitions. If the patchwork is well designed, even though the implementations of the methods may be quite different, the outward behavior of the function will appear seamless and consistent.</p><p>The choice of which method to execute when a function is applied is called <em>dispatch</em>. Julia allows the dispatch process to choose which of a function&#39;s methods to call based on the number of arguments given, and on the types of all of the function&#39;s arguments. This is different than traditional object-oriented languages, where dispatch occurs based only on the first argument, which often has a special argument syntax, and is sometimes implied rather than explicitly written as an argument. <sup class="footnote-reference"><a id="citeref-1" href="#footnote-1">[1]</a></sup> Using all of a function&#39;s arguments to choose which method should be invoked, rather than just the first, is known as <a href="https://en.wikipedia.org/wiki/Multiple_dispatch">multiple dispatch</a>. Multiple dispatch is particularly useful for mathematical code, where it makes little sense to artificially deem the operations to &quot;belong&quot; to one argument more than any of the others: does the addition operation in <code>x + y</code> belong to <code>x</code> any more than it does to <code>y</code>? The implementation of a mathematical operator generally depends on the types of all of its arguments. Even beyond mathematical operations, however, multiple dispatch ends up being a powerful and convenient paradigm for structuring and organizing programs.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>All the examples in this chapter assume that you are defining methods for a function in the <em>same</em> module. If you want to add methods to a function in <em>another</em> module, you have to <code>import</code> it or use the name qualified with module names. See the section on <a href="modules.html#namespace-management">namespace management</a>.</p></div></div><h2 id="Defining-Methods"><a class="docs-heading-anchor" href="#Defining-Methods">Defining Methods</a><a id="Defining-Methods-1"></a><a class="docs-heading-anchor-permalink" href="#Defining-Methods" title="Permalink"></a></h2><p>Until now, we have, in our examples, defined only functions with a single method having unconstrained argument types. Such functions behave just like they would in traditional dynamically typed languages. Nevertheless, we have used multiple dispatch and methods almost continually without being aware of it: all of Julia&#39;s standard functions and operators, like the aforementioned <code>+</code> function, have many methods defining their behavior over various possible combinations of argument type and count.</p><p>When defining a function, one can optionally constrain the types of parameters it is applicable to, using the <code>::</code> type-assertion operator, introduced in the section on <a href="types.html#Composite-Types">Composite Types</a>:</p><pre><code class="language-julia-repl hljs">julia&gt; f(x::Float64, y::Float64) = 2x + y
f (generic function with 1 method)</code></pre><p>This function definition applies only to calls where <code>x</code> and <code>y</code> are both values of type <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; f(2.0, 3.0)
7.0</code></pre><p>Applying it to any other types of arguments will result in a <a href="../base/base.html#Core.MethodError"><code>MethodError</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; f(2.0, 3)
ERROR: MethodError: no method matching f(::Float64, ::Int64)
The function `f` exists, but no method is defined for this combination of argument types.

Closest candidates are:
  f(::Float64, !Matched::Float64)
   @ Main none:1

Stacktrace:
[...]

julia&gt; f(Float32(2.0), 3.0)
ERROR: MethodError: no method matching f(::Float32, ::Float64)
The function `f` exists, but no method is defined for this combination of argument types.

Closest candidates are:
  f(!Matched::Float64, ::Float64)
   @ Main none:1

Stacktrace:
[...]

julia&gt; f(2.0, &quot;3.0&quot;)
ERROR: MethodError: no method matching f(::Float64, ::String)
The function `f` exists, but no method is defined for this combination of argument types.

Closest candidates are:
  f(::Float64, !Matched::Float64)
   @ Main none:1

Stacktrace:
[...]

julia&gt; f(&quot;2.0&quot;, &quot;3.0&quot;)
ERROR: MethodError: no method matching f(::String, ::String)
The function `f` exists, but no method is defined for this combination of argument types.</code></pre><p>As you can see, the arguments must be precisely of type <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a>. Other numeric types, such as integers or 32-bit floating-point values, are not automatically converted to 64-bit floating-point, nor are strings parsed as numbers. Because <code>Float64</code> is a concrete type and concrete types cannot be subclassed in Julia, such a definition can only be applied to arguments that are exactly of type <code>Float64</code>. It may often be useful, however, to write more general methods where the declared parameter types are abstract:</p><pre><code class="language-julia-repl hljs">julia&gt; f(x::Number, y::Number) = 2x - y
f (generic function with 2 methods)

julia&gt; f(2.0, 3)
1.0</code></pre><p>This method definition applies to any pair of arguments that are instances of <a href="../base/numbers.html#Core.Number"><code>Number</code></a>. They need not be of the same type, so long as they are each numeric values. The problem of handling disparate numeric types is delegated to the arithmetic operations in the expression <code>2x - y</code>.</p><p>To define a function with multiple methods, one simply defines the function multiple times, with different numbers and types of arguments. The first method definition for a function creates the function object, and subsequent method definitions add new methods to the existing function object. The most specific method definition matching the number and types of the arguments will be executed when the function is applied. Thus, the two method definitions above, taken together, define the behavior for <code>f</code> over all pairs of instances of the abstract type <code>Number</code> – but with a different behavior specific to pairs of <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> values. If one of the arguments is a 64-bit float but the other one is not, then the <code>f(Float64,Float64)</code> method cannot be called and the more general <code>f(Number,Number)</code> method must be used:</p><pre><code class="language-julia-repl hljs">julia&gt; f(2.0, 3.0)
7.0

julia&gt; f(2, 3.0)
1.0

julia&gt; f(2.0, 3)
1.0

julia&gt; f(2, 3)
1</code></pre><p>The <code>2x + y</code> definition is only used in the first case, while the <code>2x - y</code> definition is used in the others. No automatic casting or conversion of function arguments is ever performed: all conversion in Julia is non-magical and completely explicit. <a href="conversion-and-promotion.html#conversion-and-promotion">Conversion and Promotion</a>, however, shows how clever application of sufficiently advanced technology can be indistinguishable from magic. <sup class="footnote-reference"><a id="citeref-Clarke61" href="#footnote-Clarke61">[Clarke61]</a></sup></p><p>For non-numeric values, and for fewer or more than two arguments, the function <code>f</code> remains undefined, and applying it will still result in a <a href="../base/base.html#Core.MethodError"><code>MethodError</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; f(&quot;foo&quot;, 3)
ERROR: MethodError: no method matching f(::String, ::Int64)
The function `f` exists, but no method is defined for this combination of argument types.

Closest candidates are:
  f(!Matched::Number, ::Number)
   @ Main none:1
  f(!Matched::Float64, !Matched::Float64)
   @ Main none:1

Stacktrace:
[...]

julia&gt; f()
ERROR: MethodError: no method matching f()
The function `f` exists, but no method is defined for this combination of argument types.

Closest candidates are:
  f(!Matched::Float64, !Matched::Float64)
   @ Main none:1
  f(!Matched::Number, !Matched::Number)
   @ Main none:1

Stacktrace:
[...]</code></pre><p>You can easily see which methods exist for a function by entering the function object itself in an interactive session:</p><pre><code class="language-julia-repl hljs">julia&gt; f
f (generic function with 2 methods)</code></pre><p>This output tells us that <code>f</code> is a function object with two methods. To find out what the signatures of those methods are, use the <a href="../base/base.html#Base.methods"><code>methods</code></a> function:</p><pre><code class="language-julia-repl hljs">julia&gt; methods(f)
# 2 methods for generic function &quot;f&quot; from Main:
 [1] f(x::Float64, y::Float64)
     @ none:1
 [2] f(x::Number, y::Number)
     @ none:1</code></pre><p>which shows that <code>f</code> has two methods, one taking two <code>Float64</code> arguments and one taking arguments of type <code>Number</code>. It also indicates the file and line number where the methods were defined: because these methods were defined at the REPL, we get the apparent line number <code>none:1</code>.</p><p>In the absence of a type declaration with <code>::</code>, the type of a method parameter is <code>Any</code> by default, meaning that it is unconstrained since all values in Julia are instances of the abstract type <code>Any</code>. Thus, we can define a catch-all method for <code>f</code> like so:</p><pre><code class="language-julia-repl hljs">julia&gt; f(x,y) = println(&quot;Whoa there, Nelly.&quot;)
f (generic function with 3 methods)

julia&gt; methods(f)
# 3 methods for generic function &quot;f&quot; from Main:
 [1] f(x::Float64, y::Float64)
     @ none:1
 [2] f(x::Number, y::Number)
     @ none:1
 [3] f(x, y)
     @ none:1

julia&gt; f(&quot;foo&quot;, 1)
Whoa there, Nelly.</code></pre><p>This catch-all is less specific than any other possible method definition for a pair of parameter values, so it will only be called on pairs of arguments to which no other method definition applies.</p><p>Note that in the signature of the third method, there is no type specified for the arguments <code>x</code> and <code>y</code>. This is a shortened way of expressing <code>f(x::Any, y::Any)</code>.</p><p>Although it seems a simple concept, multiple dispatch on the types of values is perhaps the single most powerful and central feature of the Julia language. Core operations typically have dozens of methods:</p><pre><code class="language-julia-repl hljs">julia&gt; methods(+)
# 180 methods for generic function &quot;+&quot;:
[1] +(x::Bool, z::Complex{Bool}) in Base at complex.jl:227
[2] +(x::Bool, y::Bool) in Base at bool.jl:89
[3] +(x::Bool) in Base at bool.jl:86
[4] +(x::Bool, y::T) where T&lt;:AbstractFloat in Base at bool.jl:96
[5] +(x::Bool, z::Complex) in Base at complex.jl:234
[6] +(a::Float16, b::Float16) in Base at float.jl:373
[7] +(x::Float32, y::Float32) in Base at float.jl:375
[8] +(x::Float64, y::Float64) in Base at float.jl:376
[9] +(z::Complex{Bool}, x::Bool) in Base at complex.jl:228
[10] +(z::Complex{Bool}, x::Real) in Base at complex.jl:242
[11] +(x::Char, y::Integer) in Base at char.jl:40
[12] +(c::BigInt, x::BigFloat) in Base.MPFR at mpfr.jl:307
[13] +(a::BigInt, b::BigInt, c::BigInt, d::BigInt, e::BigInt) in Base.GMP at gmp.jl:392
[14] +(a::BigInt, b::BigInt, c::BigInt, d::BigInt) in Base.GMP at gmp.jl:391
[15] +(a::BigInt, b::BigInt, c::BigInt) in Base.GMP at gmp.jl:390
[16] +(x::BigInt, y::BigInt) in Base.GMP at gmp.jl:361
[17] +(x::BigInt, c::Union{UInt16, UInt32, UInt64, UInt8}) in Base.GMP at gmp.jl:398
...
[180] +(a, b, c, xs...) in Base at operators.jl:424</code></pre><p>Multiple dispatch together with the flexible parametric type system give Julia its ability to abstractly express high-level algorithms decoupled from implementation details.</p><h2 id="man-method-specializations"><a class="docs-heading-anchor" href="#man-method-specializations">Method specializations</a><a id="man-method-specializations-1"></a><a class="docs-heading-anchor-permalink" href="#man-method-specializations" title="Permalink"></a></h2><p>When you create multiple methods of the same function, this is sometimes called &quot;specialization.&quot; In this case, you&#39;re specializing the <em>function</em> by adding additional methods to it: each new method is a new specialization of the function. As shown above, these specializations are returned by <code>methods</code>.</p><p>There&#39;s another kind of specialization that occurs without programmer intervention: Julia&#39;s compiler can automatically specialize the <em>method</em> for the specific argument types used. Such specializations are <em>not</em> listed by <code>methods</code>, as this doesn&#39;t create new <code>Method</code>s, but tools like <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.@code_typed"><code>@code_typed</code></a> allow you to inspect such specializations.</p><p>For example, if you create a method</p><pre><code class="nohighlight hljs">mysum(x::Real, y::Real) = x + y</code></pre><p>you&#39;ve given the function <code>mysum</code> one new method (possibly its only method), and that method takes any pair of <code>Real</code> number inputs. But if you then execute</p><pre><code class="language-julia-repl hljs">julia&gt; mysum(1, 2)
3

julia&gt; mysum(1.0, 2.0)
3.0</code></pre><p>Julia will compile <code>mysum</code> twice, once for <code>x::Int, y::Int</code> and again for <code>x::Float64, y::Float64</code>. The point of compiling twice is performance: the methods that get called for <code>+</code> (which <code>mysum</code> uses) vary depending on the specific types of <code>x</code> and <code>y</code>, and by compiling different specializations Julia can do all the method lookup ahead of time. This allows the program to run much more quickly, since it does not have to bother with method lookup while it is running. Julia&#39;s automatic specialization allows you to write generic algorithms and expect that the compiler will generate efficient, specialized code to handle each case you need.</p><p>In cases where the number of potential specializations might be effectively unlimited, Julia may avoid this default specialization. See <a href="performance-tips.html#Be-aware-of-when-Julia-avoids-specializing">Be aware of when Julia avoids specializing</a> for more information.</p><h2 id="man-ambiguities"><a class="docs-heading-anchor" href="#man-ambiguities">Method Ambiguities</a><a id="man-ambiguities-1"></a><a class="docs-heading-anchor-permalink" href="#man-ambiguities" title="Permalink"></a></h2><p>It is possible to define a set of function methods such that there is no unique most specific method applicable to some combinations of arguments:</p><pre><code class="language-julia-repl hljs">julia&gt; g(x::Float64, y) = 2x + y
g (generic function with 1 method)

julia&gt; g(x, y::Float64) = x + 2y
g (generic function with 2 methods)

julia&gt; g(2.0, 3)
7.0

julia&gt; g(2, 3.0)
8.0

julia&gt; g(2.0, 3.0)
ERROR: MethodError: g(::Float64, ::Float64) is ambiguous.

Candidates:
  g(x, y::Float64)
    @ Main none:1
  g(x::Float64, y)
    @ Main none:1

Possible fix, define
  g(::Float64, ::Float64)

Stacktrace:
[...]</code></pre><p>Here the call <code>g(2.0, 3.0)</code> could be handled by either the <code>g(::Float64, ::Any)</code> or the <code>g(::Any, ::Float64)</code> method. The order in which the methods are defined does not matter and neither is more specific than the other. In such cases, Julia raises a <a href="../base/base.html#Core.MethodError"><code>MethodError</code></a> rather than arbitrarily picking a method. You can avoid method ambiguities by specifying an appropriate method for the intersection case:</p><pre><code class="language-julia-repl hljs">julia&gt; g(x::Float64, y::Float64) = 2x + 2y
g (generic function with 3 methods)

julia&gt; g(2.0, 3)
7.0

julia&gt; g(2, 3.0)
8.0

julia&gt; g(2.0, 3.0)
10.0</code></pre><p>It is recommended that the disambiguating method be defined first, since otherwise the ambiguity exists, if transiently, until the more specific method is defined.</p><p>In more complex cases, resolving method ambiguities involves a certain element of design; this topic is explored further <a href="methods.html#man-method-design-ambiguities">below</a>.</p><h2 id="Parametric-Methods"><a class="docs-heading-anchor" href="#Parametric-Methods">Parametric Methods</a><a id="Parametric-Methods-1"></a><a class="docs-heading-anchor-permalink" href="#Parametric-Methods" title="Permalink"></a></h2><p>Method definitions can optionally have type parameters qualifying the signature:</p><pre><code class="language-julia-repl hljs">julia&gt; same_type(x::T, y::T) where {T} = true
same_type (generic function with 1 method)

julia&gt; same_type(x,y) = false
same_type (generic function with 2 methods)</code></pre><p>The first method applies whenever both arguments are of the same concrete type, regardless of what type that is, while the second method acts as a catch-all, covering all other cases. Thus, overall, this defines a boolean function that checks whether its two arguments are of the same type:</p><pre><code class="language-julia-repl hljs">julia&gt; same_type(1, 2)
true

julia&gt; same_type(1, 2.0)
false

julia&gt; same_type(1.0, 2.0)
true

julia&gt; same_type(&quot;foo&quot;, 2.0)
false

julia&gt; same_type(&quot;foo&quot;, &quot;bar&quot;)
true

julia&gt; same_type(Int32(1), Int64(2))
false</code></pre><p>Such definitions correspond to methods whose type signatures are <code>UnionAll</code> types (see <a href="types.html#UnionAll-Types">UnionAll Types</a>).</p><p>This kind of definition of function behavior by dispatch is quite common – idiomatic, even – in Julia. Method type parameters are not restricted to being used as the types of arguments: they can be used anywhere a value would be in the signature of the function or body of the function. Here&#39;s an example where the method type parameter <code>T</code> is used as the type parameter to the parametric type <code>Vector{T}</code> in the method signature:</p><pre><code class="language-julia-repl hljs">julia&gt; function myappend(v::Vector{T}, x::T) where {T}
           return [v..., x]
       end
myappend (generic function with 1 method)</code></pre><p>The type parameter <code>T</code> in this example ensures that the added element <code>x</code> is a subtype of the existing eltype of the vector <code>v</code>. The <code>where</code> keyword introduces a list of those constraints after the method signature definition. This works the same for one-line definitions, as seen above, and must appear <em>before</em> the <a href="functions.html#man-functions-return-type">return type declaration</a>, if present, as illustrated below:</p><pre><code class="language-julia-repl hljs">julia&gt; (myappend(v::Vector{T}, x::T)::Vector) where {T} = [v..., x]
myappend (generic function with 1 method)

julia&gt; myappend([1,2,3],4)
4-element Vector{Int64}:
 1
 2
 3
 4

julia&gt; myappend([1,2,3],2.5)
ERROR: MethodError: no method matching myappend(::Vector{Int64}, ::Float64)
The function `myappend` exists, but no method is defined for this combination of argument types.

Closest candidates are:
  myappend(::Vector{T}, !Matched::T) where T
   @ Main none:1

Stacktrace:
[...]

julia&gt; myappend([1.0,2.0,3.0],4.0)
4-element Vector{Float64}:
 1.0
 2.0
 3.0
 4.0

julia&gt; myappend([1.0,2.0,3.0],4)
ERROR: MethodError: no method matching myappend(::Vector{Float64}, ::Int64)
The function `myappend` exists, but no method is defined for this combination of argument types.

Closest candidates are:
  myappend(::Vector{T}, !Matched::T) where T
   @ Main none:1

Stacktrace:
[...]</code></pre><p>If the type of the appended element does not match the element type of the vector it is appended to, a <a href="../base/base.html#Core.MethodError"><code>MethodError</code></a> is raised. In the following example, the method&#39;s type parameter <code>T</code> is used as the return value:</p><pre><code class="language-julia-repl hljs">julia&gt; mytypeof(x::T) where {T} = T
mytypeof (generic function with 1 method)

julia&gt; mytypeof(1)
Int64

julia&gt; mytypeof(1.0)
Float64</code></pre><p>Just as you can put subtype constraints on type parameters in type declarations (see <a href="types.html#Parametric-Types">Parametric Types</a>), you can also constrain type parameters of methods:</p><pre><code class="language-julia-repl hljs">julia&gt; same_type_numeric(x::T, y::T) where {T&lt;:Number} = true
same_type_numeric (generic function with 1 method)

julia&gt; same_type_numeric(x::Number, y::Number) = false
same_type_numeric (generic function with 2 methods)

julia&gt; same_type_numeric(1, 2)
true

julia&gt; same_type_numeric(1, 2.0)
false

julia&gt; same_type_numeric(1.0, 2.0)
true

julia&gt; same_type_numeric(&quot;foo&quot;, 2.0)
ERROR: MethodError: no method matching same_type_numeric(::String, ::Float64)
The function `same_type_numeric` exists, but no method is defined for this combination of argument types.

Closest candidates are:
  same_type_numeric(!Matched::T, ::T) where T&lt;:Number
   @ Main none:1
  same_type_numeric(!Matched::Number, ::Number)
   @ Main none:1

Stacktrace:
[...]

julia&gt; same_type_numeric(&quot;foo&quot;, &quot;bar&quot;)
ERROR: MethodError: no method matching same_type_numeric(::String, ::String)
The function `same_type_numeric` exists, but no method is defined for this combination of argument types.

julia&gt; same_type_numeric(Int32(1), Int64(2))
false</code></pre><p>The <code>same_type_numeric</code> function behaves much like the <code>same_type</code> function defined above, but is only defined for pairs of numbers.</p><p>Parametric methods allow the same syntax as <code>where</code> expressions used to write types (see <a href="types.html#UnionAll-Types">UnionAll Types</a>). If there is only a single parameter, the enclosing curly braces (in <code>where {T}</code>) can be omitted, but are often preferred for clarity. Multiple parameters can be separated with commas, e.g. <code>where {T, S&lt;:Real}</code>, or written using nested <code>where</code>, e.g. <code>where S&lt;:Real where T</code>.</p><h2 id="Redefining-Methods"><a class="docs-heading-anchor" href="#Redefining-Methods">Redefining Methods</a><a id="Redefining-Methods-1"></a><a class="docs-heading-anchor-permalink" href="#Redefining-Methods" title="Permalink"></a></h2><p>When redefining a method or adding new methods, it is important to realize that these changes don&#39;t take effect immediately. This is key to Julia&#39;s ability to statically infer and compile code to run fast, without the usual JIT tricks and overhead. Indeed, any new method definition won&#39;t be visible to the current runtime environment, including Tasks and Threads (and any previously defined <code>@generated</code> functions). Let&#39;s start with an example to see what this means:</p><pre><code class="language-julia-repl hljs">julia&gt; function tryeval()
           @eval newfun() = 1
           newfun()
       end
tryeval (generic function with 1 method)

julia&gt; tryeval()
ERROR: MethodError: no method matching newfun()
The applicable method may be too new: running in world age xxxx1, while current world is xxxx2.
Closest candidates are:
  newfun() at none:1 (method too new to be called from this world context.)
 in tryeval() at none:1
 ...

julia&gt; newfun()
1</code></pre><p>In this example, observe that the new definition for <code>newfun</code> has been created, but can&#39;t be immediately called. The new global is immediately visible to the <code>tryeval</code> function, so you could write <code>return newfun</code> (without parentheses). But neither you, nor any of your callers, nor the functions they call, or etc. can call this new method definition!</p><p>But there&#39;s an exception: future calls to <code>newfun</code> <em>from the REPL</em> work as expected, being able to both see and call the new definition of <code>newfun</code>.</p><p>However, future calls to <code>tryeval</code> will continue to see the definition of <code>newfun</code> as it was <em>at the previous statement at the REPL</em>, and thus before that call to <code>tryeval</code>.</p><p>You may want to try this for yourself to see how it works.</p><p>The implementation of this behavior is a &quot;world age counter&quot;. This monotonically increasing value tracks each method definition operation. This allows describing &quot;the set of method definitions visible to a given runtime environment&quot; as a single number, or &quot;world age&quot;. It also allows comparing the methods available in two worlds just by comparing their ordinal value. In the example above, we see that the &quot;current world&quot; (in which the method <code>newfun</code> exists), is one greater than the task-local &quot;runtime world&quot; that was fixed when the execution of <code>tryeval</code> started.</p><p>Sometimes it is necessary to get around this (for example, if you are implementing the above REPL). Fortunately, there is an easy solution: call the function using <a href="../base/base.html#Base.invokelatest"><code>Base.invokelatest</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; function tryeval2()
           @eval newfun2() = 2
           Base.invokelatest(newfun2)
       end
tryeval2 (generic function with 1 method)

julia&gt; tryeval2()
2</code></pre><p>Finally, let&#39;s take a look at some more complex examples where this rule comes into play. Define a function <code>f(x)</code>, which initially has one method:</p><pre><code class="language-julia-repl hljs">julia&gt; f(x) = &quot;original definition&quot;
f (generic function with 1 method)</code></pre><p>Start some other operations that use <code>f(x)</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; g(x) = f(x)
g (generic function with 1 method)

julia&gt; t = @async f(wait()); yield();</code></pre><p>Now we add some new methods to <code>f(x)</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; f(x::Int) = &quot;definition for Int&quot;
f (generic function with 2 methods)

julia&gt; f(x::Type{Int}) = &quot;definition for Type{Int}&quot;
f (generic function with 3 methods)</code></pre><p>Compare how these results differ:</p><pre><code class="language-julia-repl hljs">julia&gt; f(1)
&quot;definition for Int&quot;

julia&gt; g(1)
&quot;definition for Int&quot;

julia&gt; fetch(schedule(t, 1))
&quot;original definition&quot;

julia&gt; t = @async f(wait()); yield();

julia&gt; fetch(schedule(t, 1))
&quot;definition for Int&quot;</code></pre><h2 id="Design-Patterns-with-Parametric-Methods"><a class="docs-heading-anchor" href="#Design-Patterns-with-Parametric-Methods">Design Patterns with Parametric Methods</a><a id="Design-Patterns-with-Parametric-Methods-1"></a><a class="docs-heading-anchor-permalink" href="#Design-Patterns-with-Parametric-Methods" title="Permalink"></a></h2><p>While complex dispatch logic is not required for performance or usability, sometimes it can be the best way to express some algorithm. Here are a few common design patterns that come up sometimes when using dispatch in this way.</p><h3 id="Extracting-the-type-parameter-from-a-super-type"><a class="docs-heading-anchor" href="#Extracting-the-type-parameter-from-a-super-type">Extracting the type parameter from a super-type</a><a id="Extracting-the-type-parameter-from-a-super-type-1"></a><a class="docs-heading-anchor-permalink" href="#Extracting-the-type-parameter-from-a-super-type" title="Permalink"></a></h3><p>Here is a correct code template for returning the element-type <code>T</code> of any arbitrary subtype of <code>AbstractArray</code> that has well-defined element type:</p><pre><code class="language-julia hljs">abstract type AbstractArray{T, N} end
eltype(::Type{&lt;:AbstractArray{T}}) where {T} = T</code></pre><p>using so-called triangular dispatch.  Note that <code>UnionAll</code> types, for example <code>eltype(AbstractArray{T} where T &lt;: Integer)</code>, do not match the above method. The implementation of <code>eltype</code> in <code>Base</code> adds a fallback method to <code>Any</code> for such cases.</p><p>One common mistake is to try and get the element-type by using introspection:</p><pre><code class="language-julia hljs">eltype_wrong(::Type{A}) where {A&lt;:AbstractArray} = A.parameters[1]</code></pre><p>However, it is not hard to construct cases where this will fail:</p><pre><code class="language-julia hljs">struct BitVector &lt;: AbstractArray{Bool, 1}; end</code></pre><p>Here we have created a type <code>BitVector</code> which has no parameters, but where the element-type is still fully specified, with <code>T</code> equal to <code>Bool</code>!</p><p>Another mistake is to try to walk up the type hierarchy using <code>supertype</code>:</p><pre><code class="language-julia hljs">eltype_wrong(::Type{AbstractArray{T}}) where {T} = T
eltype_wrong(::Type{AbstractArray{T, N}}) where {T, N} = T
eltype_wrong(::Type{A}) where {A&lt;:AbstractArray} = eltype_wrong(supertype(A))</code></pre><p>While this works for declared types, it fails for types without supertypes:</p><pre><code class="language-julia-repl hljs">julia&gt; eltype_wrong(Union{AbstractArray{Int}, AbstractArray{Float64}})
ERROR: MethodError: no method matching supertype(::Type{Union{AbstractArray{Float64,N} where N, AbstractArray{Int64,N} where N}})
Closest candidates are:
  supertype(::DataType) at operators.jl:43
  supertype(::UnionAll) at operators.jl:48</code></pre><h3 id="Building-a-similar-type-with-a-different-type-parameter"><a class="docs-heading-anchor" href="#Building-a-similar-type-with-a-different-type-parameter">Building a similar type with a different type parameter</a><a id="Building-a-similar-type-with-a-different-type-parameter-1"></a><a class="docs-heading-anchor-permalink" href="#Building-a-similar-type-with-a-different-type-parameter" title="Permalink"></a></h3><p>When building generic code, there is often a need for constructing a similar object with some change made to the layout of the type, also necessitating a change of the type parameters. For instance, you might have some sort of abstract array with an arbitrary element type and want to write your computation on it with a specific element type. We must implement a method for each <code>AbstractArray{T}</code> subtype that describes how to compute this type transform. There is no general transform of one subtype into another subtype with a different parameter.</p><p>The subtypes of <code>AbstractArray</code> typically implement two methods to achieve this: A method to convert the input array to a subtype of a specific <code>AbstractArray{T, N}</code> abstract type; and a method to make a new uninitialized array with a specific element type. Sample implementations of these can be found in Julia Base. Here is a basic example usage of them, guaranteeing that <code>input</code> and <code>output</code> are of the same type:</p><pre><code class="language-julia hljs">input = convert(AbstractArray{Eltype}, input)
output = similar(input, Eltype)</code></pre><p>As an extension of this, in cases where the algorithm needs a copy of the input array, <a href="../base/base.html#Base.convert"><code>convert</code></a> is insufficient as the return value may alias the original input. Combining <a href="../base/arrays.html#Base.similar"><code>similar</code></a> (to make the output array) and <a href="../base/c.html#Base.copyto!"><code>copyto!</code></a> (to fill it with the input data) is a generic way to express the requirement for a mutable copy of the input argument:</p><pre><code class="language-julia hljs">copy_with_eltype(input, Eltype) = copyto!(similar(input, Eltype), input)</code></pre><h3 id="Iterated-dispatch"><a class="docs-heading-anchor" href="#Iterated-dispatch">Iterated dispatch</a><a id="Iterated-dispatch-1"></a><a class="docs-heading-anchor-permalink" href="#Iterated-dispatch" title="Permalink"></a></h3><p>In order to dispatch a multi-level parametric argument list, often it is best to separate each level of dispatch into distinct functions. This may sound similar in approach to single-dispatch, but as we shall see below, it is still more flexible.</p><p>For example, trying to dispatch on the element-type of an array will often run into ambiguous situations. Instead, commonly code will dispatch first on the container type, then recurse down to a more specific method based on eltype. In most cases, the algorithms lend themselves conveniently to this hierarchical approach, while in other cases, this rigor must be resolved manually. This dispatching branching can be observed, for example, in the logic to sum two matrices:</p><pre><code class="language-julia hljs"># First dispatch selects the map algorithm for element-wise summation.
+(a::Matrix, b::Matrix) = map(+, a, b)
# Then dispatch handles each element and selects the appropriate
# common element type for the computation.
+(a, b) = +(promote(a, b)...)
# Once the elements have the same type, they can be added.
# For example, via primitive operations exposed by the processor.
+(a::Float64, b::Float64) = Core.add(a, b)</code></pre><h3 id="Trait-based-dispatch"><a class="docs-heading-anchor" href="#Trait-based-dispatch">Trait-based dispatch</a><a id="Trait-based-dispatch-1"></a><a class="docs-heading-anchor-permalink" href="#Trait-based-dispatch" title="Permalink"></a></h3><p>A natural extension to the iterated dispatch above is to add a layer to method selection that allows to dispatch on sets of types which are independent from the sets defined by the type hierarchy. We could construct such a set by writing out a <code>Union</code> of the types in question, but then this set would not be extensible as <code>Union</code>-types cannot be altered after creation. However, such an extensible set can be programmed with a design pattern often referred to as a <a href="https://github.com/JuliaLang/julia/issues/2345#issuecomment-54537633">&quot;Holy-trait&quot;</a>.</p><p>This pattern is implemented by defining a generic function which computes a different singleton value (or type) for each trait-set to which the function arguments may belong to.  If this function is pure there is no impact on performance compared to normal dispatch.</p><p>The example in the previous section glossed over the implementation details of <a href="../base/collections.html#Base.map"><code>map</code></a> and <a href="../base/base.html#Base.promote"><code>promote</code></a>, which both operate in terms of these traits. When iterating over a matrix, such as in the implementation of <code>map</code>, one important question is what order to use to traverse the data. When <code>AbstractArray</code> subtypes implement the <a href="../base/arrays.html#Base.IndexStyle"><code>Base.IndexStyle</code></a> trait, other functions such as <code>map</code> can dispatch on this information to pick the best algorithm (see <a href="interfaces.html#man-interface-array">Abstract Array Interface</a>). This means that each subtype does not need to implement a custom version of <code>map</code>, since the generic definitions + trait classes will enable the system to select the fastest version. Here is a toy implementation of <code>map</code> illustrating the trait-based dispatch:</p><pre><code class="language-julia hljs">map(f, a::AbstractArray, b::AbstractArray) = map(Base.IndexStyle(a, b), f, a, b)
# generic implementation:
map(::Base.IndexCartesian, f, a::AbstractArray, b::AbstractArray) = ...
# linear-indexing implementation (faster)
map(::Base.IndexLinear, f, a::AbstractArray, b::AbstractArray) = ...</code></pre><p>This trait-based approach is also present in the <a href="../base/base.html#Base.promote"><code>promote</code></a> mechanism employed by the scalar <code>+</code>. It uses <a href="../base/base.html#Base.promote_type"><code>promote_type</code></a>, which returns the optimal common type to compute the operation given the two types of the operands. This makes it possible to reduce the problem of implementing every function for every pair of possible type arguments, to the much smaller problem of implementing a conversion operation from each type to a common type, plus a table of preferred pair-wise promotion rules.</p><h3 id="Output-type-computation"><a class="docs-heading-anchor" href="#Output-type-computation">Output-type computation</a><a id="Output-type-computation-1"></a><a class="docs-heading-anchor-permalink" href="#Output-type-computation" title="Permalink"></a></h3><p>The discussion of trait-based promotion provides a transition into our next design pattern: computing the output element type for a matrix operation.</p><p>For implementing primitive operations, such as addition, we use the <a href="../base/base.html#Base.promote_type"><code>promote_type</code></a> function to compute the desired output type. (As before, we saw this at work in the <code>promote</code> call in the call to <code>+</code>).</p><p>For more complex functions on matrices, it may be necessary to compute the expected return type for a more complex sequence of operations. This is often performed by the following steps:</p><ol><li>Write a small function <code>op</code> that expresses the set of operations performed by the kernel of the algorithm.</li><li>Compute the element type <code>R</code> of the result matrix as <code>promote_op(op, argument_types...)</code>, where <code>argument_types</code> is computed from <code>eltype</code> applied to each input array.</li><li>Build the output matrix as <code>similar(R, dims)</code>, where <code>dims</code> are the desired dimensions of the output array.</li></ol><p>For a more specific example, a generic square-matrix multiply pseudo-code might look like:</p><pre><code class="language-julia hljs">function matmul(a::AbstractMatrix, b::AbstractMatrix)
    op = (ai, bi) -&gt; ai * bi + ai * bi

    ## this is insufficient because it assumes `one(eltype(a))` is constructable:
    # R = typeof(op(one(eltype(a)), one(eltype(b))))

    ## this fails because it assumes `a[1]` exists and is representative of all elements of the array
    # R = typeof(op(a[1], b[1]))

    ## this is incorrect because it assumes that `+` calls `promote_type`
    ## but this is not true for some types, such as Bool:
    # R = promote_type(ai, bi)

    # this is wrong, since depending on the return value
    # of type-inference is very brittle (as well as not being optimizable):
    # R = Base.return_types(op, (eltype(a), eltype(b)))

    ## but, finally, this works:
    R = promote_op(op, eltype(a), eltype(b))
    ## although sometimes it may give a larger type than desired
    ## it will always give a correct type

    output = similar(b, R, (size(a, 1), size(b, 2)))
    if size(a, 2) &gt; 0
        for j in 1:size(b, 2)
            for i in 1:size(a, 1)
                ## here we don&#39;t use `ab = zero(R)`,
                ## since `R` might be `Any` and `zero(Any)` is not defined
                ## we also must declare `ab::R` to make the type of `ab` constant in the loop,
                ## since it is possible that typeof(a * b) != typeof(a * b + a * b) == R
                ab::R = a[i, 1] * b[1, j]
                for k in 2:size(a, 2)
                    ab += a[i, k] * b[k, j]
                end
                output[i, j] = ab
            end
        end
    end
    return output
end</code></pre><h3 id="Separate-convert-and-kernel-logic"><a class="docs-heading-anchor" href="#Separate-convert-and-kernel-logic">Separate convert and kernel logic</a><a id="Separate-convert-and-kernel-logic-1"></a><a class="docs-heading-anchor-permalink" href="#Separate-convert-and-kernel-logic" title="Permalink"></a></h3><p>One way to significantly cut down on compile-times and testing complexity is to isolate the logic for converting to the desired type and the computation. This lets the compiler specialize and inline the conversion logic independent from the rest of the body of the larger kernel.</p><p>This is a common pattern seen when converting from a larger class of types to the one specific argument type that is actually supported by the algorithm:</p><pre><code class="language-julia hljs">complexfunction(arg::Int) = ...
complexfunction(arg::Any) = complexfunction(convert(Int, arg))

matmul(a::T, b::T) = ...
matmul(a, b) = matmul(promote(a, b)...)</code></pre><h2 id="Parametrically-constrained-Varargs-methods"><a class="docs-heading-anchor" href="#Parametrically-constrained-Varargs-methods">Parametrically-constrained Varargs methods</a><a id="Parametrically-constrained-Varargs-methods-1"></a><a class="docs-heading-anchor-permalink" href="#Parametrically-constrained-Varargs-methods" title="Permalink"></a></h2><p>Function parameters can also be used to constrain the number of arguments that may be supplied to a &quot;varargs&quot; function (<a href="functions.html#Varargs-Functions">Varargs Functions</a>).  The notation <code>Vararg{T,N}</code> is used to indicate such a constraint.  For example:</p><pre><code class="language-julia-repl hljs">julia&gt; bar(a,b,x::Vararg{Any,2}) = (a,b,x)
bar (generic function with 1 method)

julia&gt; bar(1,2,3)
ERROR: MethodError: no method matching bar(::Int64, ::Int64, ::Int64)
The function `bar` exists, but no method is defined for this combination of argument types.

Closest candidates are:
  bar(::Any, ::Any, ::Any, !Matched::Any)
   @ Main none:1

Stacktrace:
[...]

julia&gt; bar(1,2,3,4)
(1, 2, (3, 4))

julia&gt; bar(1,2,3,4,5)
ERROR: MethodError: no method matching bar(::Int64, ::Int64, ::Int64, ::Int64, ::Int64)
The function `bar` exists, but no method is defined for this combination of argument types.

Closest candidates are:
  bar(::Any, ::Any, ::Any, ::Any)
   @ Main none:1

Stacktrace:
[...]</code></pre><p>More usefully, it is possible to constrain varargs methods by a parameter. For example:</p><pre><code class="language-julia hljs">function getindex(A::AbstractArray{T,N}, indices::Vararg{Number,N}) where {T,N}</code></pre><p>would be called only when the number of <code>indices</code> matches the dimensionality of the array.</p><p>When only the type of supplied arguments needs to be constrained <code>Vararg{T}</code> can be equivalently written as <code>T...</code>. For instance <code>f(x::Int...) = x</code> is a shorthand for <code>f(x::Vararg{Int}) = x</code>.</p><h2 id="Note-on-Optional-and-keyword-Arguments"><a class="docs-heading-anchor" href="#Note-on-Optional-and-keyword-Arguments">Note on Optional and keyword Arguments</a><a id="Note-on-Optional-and-keyword-Arguments-1"></a><a class="docs-heading-anchor-permalink" href="#Note-on-Optional-and-keyword-Arguments" title="Permalink"></a></h2><p>As mentioned briefly in <a href="functions.html#man-functions">Functions</a>, optional arguments are implemented as syntax for multiple method definitions. For example, this definition:</p><pre><code class="language-julia hljs">f(a=1,b=2) = a+2b</code></pre><p>translates to the following three methods:</p><pre><code class="language-julia hljs">f(a,b) = a+2b
f(a) = f(a,2)
f() = f(1,2)</code></pre><p>This means that calling <code>f()</code> is equivalent to calling <code>f(1,2)</code>. In this case the result is <code>5</code>, because <code>f(1,2)</code> invokes the first method of <code>f</code> above. However, this need not always be the case. If you define a fourth method that is more specialized for integers:</p><pre><code class="language-julia hljs">f(a::Int,b::Int) = a-2b</code></pre><p>then the result of both <code>f()</code> and <code>f(1,2)</code> is <code>-3</code>. In other words, optional arguments are tied to a function, not to any specific method of that function. It depends on the types of the optional arguments which method is invoked. When optional arguments are defined in terms of a global variable, the type of the optional argument may even change at run-time.</p><p>Keyword arguments behave quite differently from ordinary positional arguments. In particular, they do not participate in method dispatch. Methods are dispatched based only on positional arguments, with keyword arguments processed after the matching method is identified.</p><h2 id="Function-like-objects"><a class="docs-heading-anchor" href="#Function-like-objects">Function-like objects</a><a id="Function-like-objects-1"></a><a class="docs-heading-anchor-permalink" href="#Function-like-objects" title="Permalink"></a></h2><p>Methods are associated with types, so it is possible to make any arbitrary Julia object &quot;callable&quot; by adding methods to its type. (Such &quot;callable&quot; objects are sometimes called &quot;functors.&quot;)</p><p>For example, you can define a type that stores the coefficients of a polynomial, but behaves like a function evaluating the polynomial:</p><pre><code class="language-julia-repl hljs">julia&gt; struct Polynomial{R}
           coeffs::Vector{R}
       end

julia&gt; function (p::Polynomial)(x)
           v = p.coeffs[end]
           for i = (length(p.coeffs)-1):-1:1
               v = v*x + p.coeffs[i]
           end
           return v
       end

julia&gt; (p::Polynomial)() = p(5)</code></pre><p>Notice that the function is specified by type instead of by name. As with normal functions there is a terse syntax form. In the function body, <code>p</code> will refer to the object that was called. A <code>Polynomial</code> can be used as follows:</p><pre><code class="language-julia-repl hljs">julia&gt; p = Polynomial([1,10,100])
Polynomial{Int64}([1, 10, 100])

julia&gt; p(3)
931

julia&gt; p()
2551</code></pre><p>This mechanism is also the key to how type constructors and closures (inner functions that refer to their surrounding environment) work in Julia.</p><h2 id="Empty-generic-functions"><a class="docs-heading-anchor" href="#Empty-generic-functions">Empty generic functions</a><a id="Empty-generic-functions-1"></a><a class="docs-heading-anchor-permalink" href="#Empty-generic-functions" title="Permalink"></a></h2><p>Occasionally it is useful to introduce a generic function without yet adding methods. This can be used to separate interface definitions from implementations. It might also be done for the purpose of documentation or code readability. The syntax for this is an empty <code>function</code> block without a tuple of arguments:</p><pre><code class="language-julia hljs">function emptyfunc end</code></pre><h2 id="man-method-design-ambiguities"><a class="docs-heading-anchor" href="#man-method-design-ambiguities">Method design and the avoidance of ambiguities</a><a id="man-method-design-ambiguities-1"></a><a class="docs-heading-anchor-permalink" href="#man-method-design-ambiguities" title="Permalink"></a></h2><p>Julia&#39;s method polymorphism is one of its most powerful features, yet exploiting this power can pose design challenges.  In particular, in more complex method hierarchies it is not uncommon for <a href="methods.html#man-ambiguities">ambiguities</a> to arise.</p><p>Above, it was pointed out that one can resolve ambiguities like</p><pre><code class="language-julia hljs">f(x, y::Int) = 1
f(x::Int, y) = 2</code></pre><p>by defining a method</p><pre><code class="language-julia hljs">f(x::Int, y::Int) = 3</code></pre><p>This is often the right strategy; however, there are circumstances where following this advice mindlessly can be counterproductive. In particular, the more methods a generic function has, the more possibilities there are for ambiguities. When your method hierarchies get more complicated than this simple example, it can be worth your while to think carefully about alternative strategies.</p><p>Below we discuss particular challenges and some alternative ways to resolve such issues.</p><h3 id="Tuple-and-NTuple-arguments"><a class="docs-heading-anchor" href="#Tuple-and-NTuple-arguments">Tuple and NTuple arguments</a><a id="Tuple-and-NTuple-arguments-1"></a><a class="docs-heading-anchor-permalink" href="#Tuple-and-NTuple-arguments" title="Permalink"></a></h3><p><code>Tuple</code> (and <code>NTuple</code>) arguments present special challenges. For example,</p><pre><code class="language-julia hljs">f(x::NTuple{N,Int}) where {N} = 1
f(x::NTuple{N,Float64}) where {N} = 2</code></pre><p>are ambiguous because of the possibility that <code>N == 0</code>: there are no elements to determine whether the <code>Int</code> or <code>Float64</code> variant should be called. To resolve the ambiguity, one approach is define a method for the empty tuple:</p><pre><code class="language-julia hljs">f(x::Tuple{}) = 3</code></pre><p>Alternatively, for all methods but one you can insist that there is at least one element in the tuple:</p><pre><code class="language-julia hljs">f(x::NTuple{N,Int}) where {N} = 1           # this is the fallback
f(x::Tuple{Float64, Vararg{Float64}}) = 2   # this requires at least one Float64</code></pre><h3 id="man-methods-orthogonalize"><a class="docs-heading-anchor" href="#man-methods-orthogonalize">Orthogonalize your design</a><a id="man-methods-orthogonalize-1"></a><a class="docs-heading-anchor-permalink" href="#man-methods-orthogonalize" title="Permalink"></a></h3><p>When you might be tempted to dispatch on two or more arguments, consider whether a &quot;wrapper&quot; function might make for a simpler design. For example, instead of writing multiple variants:</p><pre><code class="language-julia hljs">f(x::A, y::A) = ...
f(x::A, y::B) = ...
f(x::B, y::A) = ...
f(x::B, y::B) = ...</code></pre><p>you might consider defining</p><pre><code class="language-julia hljs">f(x::A, y::A) = ...
f(x, y) = f(g(x), g(y))</code></pre><p>where <code>g</code> converts the argument to type <code>A</code>. This is a very specific example of the more general principle of <a href="https://en.wikipedia.org/wiki/Orthogonality_(programming)">orthogonal design</a>, in which separate concepts are assigned to separate methods. Here, <code>g</code> will most likely need a fallback definition</p><pre><code class="language-julia hljs">g(x::A) = x</code></pre><p>A related strategy exploits <code>promote</code> to bring <code>x</code> and <code>y</code> to a common type:</p><pre><code class="language-julia hljs">f(x::T, y::T) where {T} = ...
f(x, y) = f(promote(x, y)...)</code></pre><p>One risk with this design is the possibility that if there is no suitable promotion method converting <code>x</code> and <code>y</code> to the same type, the second method will recurse on itself infinitely and trigger a stack overflow.</p><h3 id="Dispatch-on-one-argument-at-a-time"><a class="docs-heading-anchor" href="#Dispatch-on-one-argument-at-a-time">Dispatch on one argument at a time</a><a id="Dispatch-on-one-argument-at-a-time-1"></a><a class="docs-heading-anchor-permalink" href="#Dispatch-on-one-argument-at-a-time" title="Permalink"></a></h3><p>If you need to dispatch on multiple arguments, and there are many fallbacks with too many combinations to make it practical to define all possible variants, then consider introducing a &quot;name cascade&quot; where (for example) you dispatch on the first argument and then call an internal method:</p><pre><code class="language-julia hljs">f(x::A, y) = _fA(x, y)
f(x::B, y) = _fB(x, y)</code></pre><p>Then the internal methods <code>_fA</code> and <code>_fB</code> can dispatch on <code>y</code> without concern about ambiguities with each other with respect to <code>x</code>.</p><p>Be aware that this strategy has at least one major disadvantage: in many cases, it is not possible for users to further customize the behavior of <code>f</code> by defining further specializations of your exported function <code>f</code>. Instead, they have to define specializations for your internal methods <code>_fA</code> and <code>_fB</code>, and this blurs the lines between exported and internal methods.</p><h3 id="Abstract-containers-and-element-types"><a class="docs-heading-anchor" href="#Abstract-containers-and-element-types">Abstract containers and element types</a><a id="Abstract-containers-and-element-types-1"></a><a class="docs-heading-anchor-permalink" href="#Abstract-containers-and-element-types" title="Permalink"></a></h3><p>Where possible, try to avoid defining methods that dispatch on specific element types of abstract containers. For example,</p><pre><code class="language-julia hljs">-(A::AbstractArray{T}, b::Date) where {T&lt;:Date}</code></pre><p>generates ambiguities for anyone who defines a method</p><pre><code class="language-julia hljs">-(A::MyArrayType{T}, b::T) where {T}</code></pre><p>The best approach is to avoid defining <em>either</em> of these methods: instead, rely on a generic method <code>-(A::AbstractArray, b)</code> and make sure this method is implemented with generic calls (like <code>similar</code> and <code>-</code>) that do the right thing for each container type and element type <em>separately</em>. This is just a more complex variant of the advice to <a href="methods.html#man-methods-orthogonalize">orthogonalize</a> your methods.</p><p>When this approach is not possible, it may be worth starting a discussion with other developers about resolving the ambiguity; just because one method was defined first does not necessarily mean that it can&#39;t be modified or eliminated.  As a last resort, one developer can define the &quot;band-aid&quot; method</p><pre><code class="language-julia hljs">-(A::MyArrayType{T}, b::Date) where {T&lt;:Date} = ...</code></pre><p>that resolves the ambiguity by brute force.</p><h3 id="Complex-method-&quot;cascades&quot;-with-default-arguments"><a class="docs-heading-anchor" href="#Complex-method-&quot;cascades&quot;-with-default-arguments">Complex method &quot;cascades&quot; with default arguments</a><a id="Complex-method-&quot;cascades&quot;-with-default-arguments-1"></a><a class="docs-heading-anchor-permalink" href="#Complex-method-&quot;cascades&quot;-with-default-arguments" title="Permalink"></a></h3><p>If you are defining a method &quot;cascade&quot; that supplies defaults, be careful about dropping any arguments that correspond to potential defaults. For example, suppose you&#39;re writing a digital filtering algorithm and you have a method that handles the edges of the signal by applying padding:</p><pre><code class="language-julia hljs">function myfilter(A, kernel, ::Replicate)
    Apadded = replicate_edges(A, size(kernel))
    myfilter(Apadded, kernel)  # now perform the &quot;real&quot; computation
end</code></pre><p>This will run afoul of a method that supplies default padding:</p><pre><code class="language-julia hljs">myfilter(A, kernel) = myfilter(A, kernel, Replicate()) # replicate the edge by default</code></pre><p>Together, these two methods generate an infinite recursion with <code>A</code> constantly growing bigger.</p><p>The better design would be to define your call hierarchy like this:</p><pre><code class="language-julia hljs">struct NoPad end  # indicate that no padding is desired, or that it&#39;s already applied

myfilter(A, kernel) = myfilter(A, kernel, Replicate())  # default boundary conditions

function myfilter(A, kernel, ::Replicate)
    Apadded = replicate_edges(A, size(kernel))
    myfilter(Apadded, kernel, NoPad())  # indicate the new boundary conditions
end

# other padding methods go here

function myfilter(A, kernel, ::NoPad)
    # Here&#39;s the &quot;real&quot; implementation of the core computation
end</code></pre><p><code>NoPad</code> is supplied in the same argument position as any other kind of padding, so it keeps the dispatch hierarchy well organized and with reduced likelihood of ambiguities. Moreover, it extends the &quot;public&quot; <code>myfilter</code> interface: a user who wants to control the padding explicitly can call the <code>NoPad</code> variant directly.</p><h2 id="Defining-methods-in-local-scope"><a class="docs-heading-anchor" href="#Defining-methods-in-local-scope">Defining methods in local scope</a><a id="Defining-methods-in-local-scope-1"></a><a class="docs-heading-anchor-permalink" href="#Defining-methods-in-local-scope" title="Permalink"></a></h2><p>You can define methods within a <a href="variables-and-scoping.html#scope-of-variables">local scope</a>, for example</p><pre><code class="language-julia-repl hljs">julia&gt; function f(x)
           g(y::Int) = y + x
           g(y) = y - x
           g
       end
f (generic function with 1 method)

julia&gt; h = f(3);

julia&gt; h(4)
7

julia&gt; h(4.0)
1.0</code></pre><p>However, you should <em>not</em> define local methods conditionally or subject to control flow, as in</p><pre><code class="language-julia hljs">function f2(inc)
    if inc
        g(x) = x + 1
    else
        g(x) = x - 1
    end
end

function f3()
    function g end
    return g
    g() = 0
end</code></pre><p>as it is not clear what function will end up getting defined. In the future, it might be an error to define local methods in this manner.</p><p>For cases like this use anonymous functions instead:</p><pre><code class="language-julia hljs">function f2(inc)
    g = if inc
        x -&gt; x + 1
    else
        x -&gt; x - 1
    end
end</code></pre><section class="footnotes is-size-7"><ul><li class="footnote" id="footnote-1"><a class="tag is-link" href="#citeref-1">1</a>In C++ or Java, for example, in a method call like <code>obj.meth(arg1,arg2)</code>, the object obj &quot;receives&quot; the method call and is implicitly passed to the method via the <code>this</code> keyword, rather than as an explicit method argument. When the current <code>this</code> object is the receiver of a method call, it can be omitted altogether, writing just <code>meth(arg1,arg2)</code>, with <code>this</code> implied as the receiving object.</li><li class="footnote" id="footnote-Clarke61"><a class="tag is-link" href="#citeref-Clarke61">Clarke61</a>Arthur C. Clarke, <em>Profiles of the Future</em> (1961): Clarke&#39;s Third Law.</li></ul></section></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="types.html">« Types</a><a class="docs-footer-nextpage" href="constructors.html">Constructors »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
