<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Memory-mapped I/O · The Julia Language</title><meta name="title" content="Memory-mapped I/O · The Julia Language"/><meta property="og:title" content="Memory-mapped I/O · The Julia Language"/><meta property="twitter:title" content="Memory-mapped I/O · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li class="is-active"><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="Mmap.html">Memory-mapped I/O</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="Mmap.html">Memory-mapped I/O</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/stdlib/Mmap/docs/src/index.md" title="View source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Memory-mapped-I/O"><a class="docs-heading-anchor" href="#Memory-mapped-I/O">Memory-mapped I/O</a><a id="Memory-mapped-I/O-1"></a><a class="docs-heading-anchor-permalink" href="#Memory-mapped-I/O" title="Permalink"></a></h1><p>Low level module for mmap (memory mapping of files).</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Mmap.Anonymous" href="#Mmap.Anonymous"><code>Mmap.Anonymous</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Mmap.Anonymous(name::AbstractString=&quot;&quot;, readonly::Bool=false, create::Bool=true)</code></pre><p>Create an <code>IO</code>-like object for creating zeroed-out mmapped-memory that is not tied to a file for use in <a href="Mmap.html#Mmap.mmap"><code>mmap</code></a>. Used by <code>SharedArray</code> for creating shared memory arrays.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; using Mmap

julia&gt; anon = Mmap.Anonymous();

julia&gt; isreadable(anon)
true

julia&gt; iswritable(anon)
true

julia&gt; isopen(anon)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Mmap/src/Mmap.jl#L21-L42">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Mmap.mmap" href="#Mmap.mmap"><code>Mmap.mmap</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mmap(io::Union{IOStream,AbstractString,Mmap.AnonymousMmap}[, type::Type{Array{T,N}}, dims, offset]; grow::Bool=true, shared::Bool=true)
mmap(type::Type{Array{T,N}}, dims)</code></pre><p>Create an <code>Array</code> whose values are linked to a file, using memory-mapping. This provides a convenient way of working with data too large to fit in the computer&#39;s memory.</p><p>The type is an <code>Array{T,N}</code> with a bits-type element of <code>T</code> and dimension <code>N</code> that determines how the bytes of the array are interpreted. Note that the file must be stored in binary format, and no format conversions are possible (this is a limitation of operating systems, not Julia).</p><p><code>dims</code> is a tuple or single <a href="../base/numbers.html#Core.Integer"><code>Integer</code></a> specifying the size or length of the array.</p><p>The file is passed via the stream argument, either as an open <a href="../base/io-network.html#Base.IOStream"><code>IOStream</code></a> or filename string. When you initialize the stream, use <code>&quot;r&quot;</code> for a &quot;read-only&quot; array, and <code>&quot;w+&quot;</code> to create a new array used to write values to disk.</p><p>If no <code>type</code> argument is specified, the default is <code>Vector{UInt8}</code>.</p><p>Optionally, you can specify an offset (in bytes) if, for example, you want to skip over a header in the file. The default value for the offset is the current stream position for an <code>IOStream</code>.</p><p>The <code>grow</code> keyword argument specifies whether the disk file should be grown to accommodate the requested size of array (if the total file size is &lt; requested array size). Write privileges are required to grow the file.</p><p>The <code>shared</code> keyword argument specifies whether the resulting <code>Array</code> and changes made to it will be visible to other processes mapping the same file.</p><p>For example, the following code</p><pre><code class="language-julia hljs"># Create a file for mmapping
# (you could alternatively use mmap to do this step, too)
using Mmap
A = rand(1:20, 5, 30)
s = open(&quot;/tmp/mmap.bin&quot;, &quot;w+&quot;)
# We&#39;ll write the dimensions of the array as the first two Ints in the file
write(s, size(A,1))
write(s, size(A,2))
# Now write the data
write(s, A)
close(s)

# Test by reading it back in
s = open(&quot;/tmp/mmap.bin&quot;)   # default is read-only
m = read(s, Int)
n = read(s, Int)
A2 = mmap(s, Matrix{Int}, (m,n))</code></pre><p>creates a <code>m</code>-by-<code>n</code> <code>Matrix{Int}</code>, linked to the file associated with stream <code>s</code>.</p><p>A more portable file would need to encode the word size – 32 bit or 64 bit – and endianness information in the header. In practice, consider encoding binary data using standard formats like HDF5 (which can be used with memory-mapping).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Mmap/src/Mmap.jl#L129-L187">source</a></section><section><div><pre><code class="language-julia hljs">mmap(io, BitArray, [dims, offset])</code></pre><p>Create a <a href="../base/arrays.html#Base.BitArray"><code>BitArray</code></a> whose values are linked to a file, using memory-mapping; it has the same purpose, works in the same way, and has the same arguments, as <a href="Mmap.html#Mmap.mmap"><code>mmap</code></a>, but the byte representation is different.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; using Mmap

julia&gt; io = open(&quot;mmap.bin&quot;, &quot;w+&quot;);

julia&gt; B = mmap(io, BitArray, (25,30000));

julia&gt; B[3, 4000] = true;

julia&gt; Mmap.sync!(B);

julia&gt; close(io);

julia&gt; io = open(&quot;mmap.bin&quot;, &quot;r+&quot;);

julia&gt; C = mmap(io, BitArray, (25,30000));

julia&gt; C[3, 4000]
true

julia&gt; C[2, 4000]
false

julia&gt; close(io)

julia&gt; rm(&quot;mmap.bin&quot;)</code></pre><p>This creates a 25-by-30000 <code>BitArray</code>, linked to the file associated with stream <code>io</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Mmap/src/Mmap.jl#L289-L325">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Mmap.sync!" href="#Mmap.sync!"><code>Mmap.sync!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Mmap.sync!(array)</code></pre><p>Forces synchronization between the in-memory version of a memory-mapped <code>Array</code> or <a href="../base/arrays.html#Base.BitArray"><code>BitArray</code></a> and the on-disk version.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Mmap/src/Mmap.jl#L365-L370">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="Markdown.html">« Markdown</a><a class="docs-footer-nextpage" href="NetworkOptions.html">Network Options »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
