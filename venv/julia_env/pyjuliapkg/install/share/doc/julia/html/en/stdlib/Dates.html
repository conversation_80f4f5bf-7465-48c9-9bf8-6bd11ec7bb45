<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Dates · The Julia Language</title><meta name="title" content="Dates · The Julia Language"/><meta property="og:title" content="Dates · The Julia Language"/><meta property="twitter:title" content="Dates · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li class="is-active"><a class="tocitem" href="Dates.html">Dates</a><ul class="internal"><li><a class="tocitem" href="#Constructors"><span>Constructors</span></a></li><li><a class="tocitem" href="#Durations/Comparisons"><span>Durations/Comparisons</span></a></li><li><a class="tocitem" href="#Accessor-Functions"><span>Accessor Functions</span></a></li><li><a class="tocitem" href="#Query-Functions"><span>Query Functions</span></a></li><li><a class="tocitem" href="#TimeType-Period-Arithmetic"><span>TimeType-Period Arithmetic</span></a></li><li><a class="tocitem" href="#Adjuster-Functions"><span>Adjuster Functions</span></a></li><li><a class="tocitem" href="#Period-Types"><span>Period Types</span></a></li><li><a class="tocitem" href="#Rounding"><span>Rounding</span></a></li><li><a class="tocitem" href="#stdlib-dates-api"><span>API reference</span></a></li></ul></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="Dates.html">Dates</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="Dates.html">Dates</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/stdlib/Dates/docs/src/index.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Dates"><a class="docs-heading-anchor" href="#Dates">Dates</a><a id="Dates-1"></a><a class="docs-heading-anchor-permalink" href="#Dates" title="Permalink"></a></h1><p>The <code>Dates</code> module provides two types for working with dates: <a href="Dates.html#Dates.Date"><code>Date</code></a> and <a href="Dates.html#Dates.DateTime"><code>DateTime</code></a>, representing day and millisecond precision, respectively; both are subtypes of the abstract <a href="Dates.html#Dates.TimeType"><code>TimeType</code></a>. The motivation for distinct types is simple: some operations are much simpler, both in terms of code and mental reasoning, when the complexities of greater precision don&#39;t have to be dealt with. For example, since the <a href="Dates.html#Dates.Date"><code>Date</code></a> type only resolves to the precision of a single date (i.e. no hours, minutes, or seconds), normal considerations for time zones, daylight savings/summer time, and leap seconds are unnecessary and avoided.</p><p>Both <a href="Dates.html#Dates.Date"><code>Date</code></a> and <a href="Dates.html#Dates.DateTime"><code>DateTime</code></a> are basically immutable <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a> wrappers. The single <code>instant</code> field of either type is actually a <code>UTInstant{P}</code> type, which represents a continuously increasing machine timeline based on the UT second <sup class="footnote-reference"><a id="citeref-1" href="#footnote-1">[1]</a></sup>. The <a href="Dates.html#Dates.DateTime"><code>DateTime</code></a> type is not aware of time zones (<em>naive</em>, in Python parlance), analogous to a <em>LocalDateTime</em> in Java 8. Additional time zone functionality can be added through the <a href="https://github.com/JuliaTime/TimeZones.jl/">TimeZones.jl package</a>, which compiles the <a href="https://www.iana.org/time-zones">IANA time zone database</a>. Both <a href="Dates.html#Dates.Date"><code>Date</code></a> and <a href="Dates.html#Dates.DateTime"><code>DateTime</code></a> are based on the <a href="https://en.wikipedia.org/wiki/ISO_8601">ISO 8601</a> standard, which follows the proleptic Gregorian calendar. One note is that the ISO 8601 standard is particular about BC/BCE dates. In general, the last day of the BC/BCE era, 1-12-31 BC/BCE, was followed by 1-1-1 AD/CE, thus no year zero exists. The ISO standard, however, states that 1 BC/BCE is year zero, so <code>0000-12-31</code> is the day before <code>0001-01-01</code>, and year <code>-0001</code> (yes, negative one for the year) is 2 BC/BCE, year <code>-0002</code> is 3 BC/BCE, etc.</p><h2 id="Constructors"><a class="docs-heading-anchor" href="#Constructors">Constructors</a><a id="Constructors-1"></a><a class="docs-heading-anchor-permalink" href="#Constructors" title="Permalink"></a></h2><p><a href="Dates.html#Dates.Date"><code>Date</code></a> and <a href="Dates.html#Dates.DateTime"><code>DateTime</code></a> types can be constructed by integer or <a href="Dates.html#Dates.Period"><code>Period</code></a> types, by parsing, or through adjusters (more on those later):</p><pre><code class="language-julia-repl hljs">julia&gt; DateTime(2013)
2013-01-01T00:00:00

julia&gt; DateTime(2013,7)
2013-07-01T00:00:00

julia&gt; DateTime(2013,7,1)
2013-07-01T00:00:00

julia&gt; DateTime(2013,7,1,12)
2013-07-01T12:00:00

julia&gt; DateTime(2013,7,1,12,30)
2013-07-01T12:30:00

julia&gt; DateTime(2013,7,1,12,30,59)
2013-07-01T12:30:59

julia&gt; DateTime(2013,7,1,12,30,59,1)
2013-07-01T12:30:59.001

julia&gt; Date(2013)
2013-01-01

julia&gt; Date(2013,7)
2013-07-01

julia&gt; Date(2013,7,1)
2013-07-01

julia&gt; Date(Dates.Year(2013),Dates.Month(7),Dates.Day(1))
2013-07-01

julia&gt; Date(Dates.Month(7),Dates.Year(2013))
2013-07-01</code></pre><p><a href="Dates.html#Dates.Date"><code>Date</code></a> or <a href="Dates.html#Dates.DateTime"><code>DateTime</code></a> parsing is accomplished by the use of format strings. Format strings work by the notion of defining <em>delimited</em> or <em>fixed-width</em> &quot;slots&quot; that contain a period to parse and passing the text to parse and format string to a <a href="Dates.html#Dates.Date"><code>Date</code></a> or <a href="Dates.html#Dates.DateTime"><code>DateTime</code></a> constructor, of the form <code>Date(&quot;2015-01-01&quot;,dateformat&quot;y-m-d&quot;)</code> or <code>DateTime(&quot;20150101&quot;,dateformat&quot;yyyymmdd&quot;)</code>.</p><p>Delimited slots are marked by specifying the delimiter the parser should expect between two subsequent periods; so <code>&quot;y-m-d&quot;</code> lets the parser know that between the first and second slots in a date string like <code>&quot;2014-07-16&quot;</code>, it should find the <code>-</code> character. The <code>y</code>, <code>m</code>, and <code>d</code> characters let the parser know which periods to parse in each slot.</p><p>As in the case of constructors above such as <code>Date(2013)</code>, delimited <code>DateFormat</code>s allow for missing parts of dates and times so long as the preceding parts are given. The other parts are given the usual default values.  For example, <code>Date(&quot;1981-03&quot;, dateformat&quot;y-m-d&quot;)</code> returns <code>1981-03-01</code>, whilst <code>Date(&quot;31/12&quot;, dateformat&quot;d/m/y&quot;)</code> gives <code>0001-12-31</code>.  (Note that the default year is 1 AD/CE.) An empty string, however, always throws an <code>ArgumentError</code>.</p><p>Fixed-width slots are specified by repeating the period character the number of times corresponding to the width with no delimiter between characters. So <code>dateformat&quot;yyyymmdd&quot;</code> would correspond to a date string like <code>&quot;20140716&quot;</code>. The parser distinguishes a fixed-width slot by the absence of a delimiter, noting the transition <code>&quot;yyyymm&quot;</code> from one period character to the next.</p><p>Support for text-form month parsing is also supported through the <code>u</code> and <code>U</code> characters, for abbreviated and full-length month names, respectively. By default, only English month names are supported, so <code>u</code> corresponds to &quot;Jan&quot;, &quot;Feb&quot;, &quot;Mar&quot;, etc. And <code>U</code> corresponds to &quot;January&quot;, &quot;February&quot;, &quot;March&quot;, etc. Similar to other name=&gt;value mapping functions <a href="Dates.html#Dates.dayname"><code>dayname</code></a> and <a href="Dates.html#Dates.monthname"><code>monthname</code></a>, custom locales can be loaded by passing in the <code>locale=&gt;Dict{String,Int}</code> mapping to the <code>MONTHTOVALUEABBR</code> and <code>MONTHTOVALUE</code> dicts for abbreviated and full-name month names, respectively.</p><p>The above examples used the <code>dateformat&quot;&quot;</code> string macro. This macro creates a <code>DateFormat</code> object once when the macro is expanded and uses the same <code>DateFormat</code> object even if a code snippet is run multiple times.</p><pre><code class="language-julia-repl hljs">julia&gt; for i = 1:10^5
           Date(&quot;2015-01-01&quot;, dateformat&quot;y-m-d&quot;)
       end</code></pre><p>Or you can create the DateFormat object explicitly:</p><pre><code class="language-julia-repl hljs">julia&gt; df = DateFormat(&quot;y-m-d&quot;);

julia&gt; dt = Date(&quot;2015-01-01&quot;,df)
2015-01-01

julia&gt; dt2 = Date(&quot;2015-01-02&quot;,df)
2015-01-02</code></pre><p>Alternatively, use broadcasting:</p><pre><code class="language-julia-repl hljs">julia&gt; years = [&quot;2015&quot;, &quot;2016&quot;];

julia&gt; Date.(years, DateFormat(&quot;yyyy&quot;))
2-element Vector{Date}:
 2015-01-01
 2016-01-01</code></pre><p>For convenience, you may pass the format string directly (e.g., <code>Date(&quot;2015-01-01&quot;,&quot;y-m-d&quot;)</code>), although this form incurs performance costs if you are parsing the same format repeatedly, as it internally creates a new <code>DateFormat</code> object each time.</p><p>As well as via the constructors, a <code>Date</code> or <code>DateTime</code> can be constructed from strings using the <a href="../base/numbers.html#Base.parse"><code>parse</code></a> and <a href="../base/numbers.html#Base.tryparse"><code>tryparse</code></a> functions, but with an optional third argument of type <code>DateFormat</code> specifying the format; for example, <code>parse(Date, &quot;06.23.2013&quot;, dateformat&quot;m.d.y&quot;)</code>, or <code>tryparse(DateTime, &quot;1999-12-31T23:59:59&quot;)</code> which uses the default format. The notable difference between the functions is that with <a href="../base/numbers.html#Base.tryparse"><code>tryparse</code></a>, an error is not thrown if the string is empty or in an invalid format; instead <code>nothing</code> is returned.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p>Before Julia 1.9, empty strings could be passed to constructors and <code>parse</code> without error, returning as appropriate <code>DateTime(1)</code>, <code>Date(1)</code> or <code>Time(0)</code>. Likewise, <code>tryparse</code> did not return <code>nothing</code>.</p></div></div><p>A full suite of parsing and formatting tests and examples is available in <a href="https://github.com/JuliaLang/julia/blob/master/stdlib/Dates/test/io.jl"><code>stdlib/Dates/test/io.jl</code></a>.</p><h2 id="Durations/Comparisons"><a class="docs-heading-anchor" href="#Durations/Comparisons">Durations/Comparisons</a><a id="Durations/Comparisons-1"></a><a class="docs-heading-anchor-permalink" href="#Durations/Comparisons" title="Permalink"></a></h2><p>Finding the length of time between two <a href="Dates.html#Dates.Date"><code>Date</code></a> or <a href="Dates.html#Dates.DateTime"><code>DateTime</code></a> is straightforward given their underlying representation as <code>UTInstant{Day}</code> and <code>UTInstant{Millisecond}</code>, respectively. The difference between <a href="Dates.html#Dates.Date"><code>Date</code></a> is returned in the number of <a href="Dates.html#Dates.Day-Tuple{TimeType}"><code>Day</code></a>, and <a href="Dates.html#Dates.DateTime"><code>DateTime</code></a> in the number of <a href="Dates.html#Dates.Millisecond-Tuple{DateTime}"><code>Millisecond</code></a>. Similarly, comparing <a href="Dates.html#Dates.TimeType"><code>TimeType</code></a> is a simple matter of comparing the underlying machine instants (which in turn compares the internal <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a> values).</p><pre><code class="language-julia-repl hljs">julia&gt; dt = Date(2012,2,29)
2012-02-29

julia&gt; dt2 = Date(2000,2,1)
2000-02-01

julia&gt; dump(dt)
Date
  instant: Dates.UTInstant{Day}
    periods: Day
      value: Int64 734562

julia&gt; dump(dt2)
Date
  instant: Dates.UTInstant{Day}
    periods: Day
      value: Int64 730151

julia&gt; dt &gt; dt2
true

julia&gt; dt != dt2
true

julia&gt; dt + dt2
ERROR: MethodError: no method matching +(::Date, ::Date)
[...]

julia&gt; dt * dt2
ERROR: MethodError: no method matching *(::Date, ::Date)
[...]

julia&gt; dt / dt2
ERROR: MethodError: no method matching /(::Date, ::Date)

julia&gt; dt - dt2
4411 days

julia&gt; dt2 - dt
-4411 days

julia&gt; dt = DateTime(2012,2,29)
2012-02-29T00:00:00

julia&gt; dt2 = DateTime(2000,2,1)
2000-02-01T00:00:00

julia&gt; dt - dt2
381110400000 milliseconds</code></pre><h2 id="Accessor-Functions"><a class="docs-heading-anchor" href="#Accessor-Functions">Accessor Functions</a><a id="Accessor-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Accessor-Functions" title="Permalink"></a></h2><p>Because the <a href="Dates.html#Dates.Date"><code>Date</code></a> and <a href="Dates.html#Dates.DateTime"><code>DateTime</code></a> types are stored as single <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a> values, date parts or fields can be retrieved through accessor functions. The lowercase accessors return the field as an integer:</p><pre><code class="language-julia-repl hljs">julia&gt; t = Date(2014, 1, 31)
2014-01-31

julia&gt; Dates.year(t)
2014

julia&gt; Dates.month(t)
1

julia&gt; Dates.week(t)
5

julia&gt; Dates.day(t)
31</code></pre><p>While propercase return the same value in the corresponding <a href="Dates.html#Dates.Period"><code>Period</code></a> type:</p><pre><code class="language-julia-repl hljs">julia&gt; Dates.Year(t)
2014 years

julia&gt; Dates.Day(t)
31 days</code></pre><p>Compound methods are provided because it is more efficient to access multiple fields at the same time than individually:</p><pre><code class="language-julia-repl hljs">julia&gt; Dates.yearmonth(t)
(2014, 1)

julia&gt; Dates.monthday(t)
(1, 31)

julia&gt; Dates.yearmonthday(t)
(2014, 1, 31)</code></pre><p>One may also access the underlying <code>UTInstant</code> or integer value:</p><pre><code class="language-julia-repl hljs">julia&gt; dump(t)
Date
  instant: Dates.UTInstant{Day}
    periods: Day
      value: Int64 735264

julia&gt; t.instant
Dates.UTInstant{Day}(Day(735264))

julia&gt; Dates.value(t)
735264</code></pre><h2 id="Query-Functions"><a class="docs-heading-anchor" href="#Query-Functions">Query Functions</a><a id="Query-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Query-Functions" title="Permalink"></a></h2><p>Query functions provide calendrical information about a <a href="Dates.html#Dates.TimeType"><code>TimeType</code></a>. They include information about the day of the week:</p><pre><code class="language-julia-repl hljs">julia&gt; t = Date(2014, 1, 31)
2014-01-31

julia&gt; Dates.dayofweek(t)
5

julia&gt; Dates.dayname(t)
&quot;Friday&quot;

julia&gt; Dates.dayofweekofmonth(t) # 5th Friday of January
5</code></pre><p>Month of the year:</p><pre><code class="language-julia-repl hljs">julia&gt; Dates.monthname(t)
&quot;January&quot;

julia&gt; Dates.daysinmonth(t)
31</code></pre><p>As well as information about the <a href="Dates.html#Dates.TimeType"><code>TimeType</code></a>&#39;s year and quarter:</p><pre><code class="language-julia-repl hljs">julia&gt; Dates.isleapyear(t)
false

julia&gt; Dates.dayofyear(t)
31

julia&gt; Dates.quarterofyear(t)
1

julia&gt; Dates.dayofquarter(t)
31</code></pre><p>The <a href="Dates.html#Dates.dayname"><code>dayname</code></a> and <a href="Dates.html#Dates.monthname"><code>monthname</code></a> methods can also take an optional <code>locale</code> keyword that can be used to return the name of the day or month of the year for other languages/locales. There are also versions of these functions returning the abbreviated names, namely <a href="Dates.html#Dates.dayabbr"><code>dayabbr</code></a> and <a href="Dates.html#Dates.monthabbr"><code>monthabbr</code></a>. First the mapping is loaded into the <code>LOCALES</code> variable:</p><pre><code class="language-julia-repl hljs">julia&gt; french_months = [&quot;janvier&quot;, &quot;février&quot;, &quot;mars&quot;, &quot;avril&quot;, &quot;mai&quot;, &quot;juin&quot;,
                        &quot;juillet&quot;, &quot;août&quot;, &quot;septembre&quot;, &quot;octobre&quot;, &quot;novembre&quot;, &quot;décembre&quot;];

julia&gt; french_months_abbrev = [&quot;janv&quot;,&quot;févr&quot;,&quot;mars&quot;,&quot;avril&quot;,&quot;mai&quot;,&quot;juin&quot;,
                              &quot;juil&quot;,&quot;août&quot;,&quot;sept&quot;,&quot;oct&quot;,&quot;nov&quot;,&quot;déc&quot;];

julia&gt; french_days = [&quot;lundi&quot;,&quot;mardi&quot;,&quot;mercredi&quot;,&quot;jeudi&quot;,&quot;vendredi&quot;,&quot;samedi&quot;,&quot;dimanche&quot;];

julia&gt; Dates.LOCALES[&quot;french&quot;] = Dates.DateLocale(french_months, french_months_abbrev, french_days, [&quot;&quot;]);</code></pre><p>The above mentioned functions can then be used to perform the queries:</p><pre><code class="language-julia-repl hljs">julia&gt; Dates.dayname(t;locale=&quot;french&quot;)
&quot;vendredi&quot;

julia&gt; Dates.monthname(t;locale=&quot;french&quot;)
&quot;janvier&quot;

julia&gt; Dates.monthabbr(t;locale=&quot;french&quot;)
&quot;janv&quot;</code></pre><p>Since the abbreviated versions of the days are not loaded, trying to use the function <code>dayabbr</code> will throw an error.</p><pre><code class="language-julia-repl hljs">julia&gt; Dates.dayabbr(t;locale=&quot;french&quot;)
ERROR: BoundsError: attempt to access 1-element Vector{String} at index [5]
Stacktrace:
[...]</code></pre><h2 id="TimeType-Period-Arithmetic"><a class="docs-heading-anchor" href="#TimeType-Period-Arithmetic">TimeType-Period Arithmetic</a><a id="TimeType-Period-Arithmetic-1"></a><a class="docs-heading-anchor-permalink" href="#TimeType-Period-Arithmetic" title="Permalink"></a></h2><p>It&#39;s good practice when using any language/date framework to be familiar with how date-period arithmetic is handled as there are some <a href="https://codeblog.jonskeet.uk/2010/12/01/the-joys-of-date-time-arithmetic/">tricky issues</a> to deal with (though much less so for day-precision types).</p><p>The <code>Dates</code> module approach tries to follow the simple principle of trying to change as little as possible when doing <a href="Dates.html#Dates.Period"><code>Period</code></a> arithmetic. This approach is also often known as <em>calendrical</em> arithmetic or what you would probably guess if someone were to ask you the same calculation in a conversation. Why all the fuss about this? Let&#39;s take a classic example: add 1 month to January 31st, 2014. What&#39;s the answer? Javascript will say <a href="https://markhneedham.com/blog/2009/01/07/javascript-add-a-month-to-a-date/">March 3</a> (assumes 31 days). PHP says <a href="https://stackoverflow.com/questions/5760262/php-adding-months-to-a-date-while-not-exceeding-the-last-day-of-the-month">March 2</a> (assumes 30 days). The fact is, there is no right answer. In the <code>Dates</code> module, it gives the result of February 28th. How does it figure that out? Consider the classic 7-7-7 gambling game in casinos.</p><p>Now just imagine that instead of 7-7-7, the slots are Year-Month-Day, or in our example, 2014-01-31. When you ask to add 1 month to this date, the month slot is incremented, so now we have 2014-02-31. Then the day number is checked if it is greater than the last valid day of the new month; if it is (as in the case above), the day number is adjusted down to the last valid day (28). What are the ramifications with this approach? Go ahead and add another month to our date, <code>2014-02-28 + Month(1) == 2014-03-28</code>. What? Were you expecting the last day of March? Nope, sorry, remember the 7-7-7 slots. As few slots as possible are going to change, so we first increment the month slot by 1, 2014-03-28, and boom, we&#39;re done because that&#39;s a valid date. On the other hand, if we were to add 2 months to our original date, 2014-01-31, then we end up with 2014-03-31, as expected. The other ramification of this approach is a loss in associativity when a specific ordering is forced (i.e. adding things in different orders results in different outcomes). For example:</p><pre><code class="language-julia-repl hljs">julia&gt; (Date(2014,1,29)+Dates.Day(1)) + Dates.Month(1)
2014-02-28

julia&gt; (Date(2014,1,29)+Dates.Month(1)) + Dates.Day(1)
2014-03-01</code></pre><p>What&#39;s going on there? In the first line, we&#39;re adding 1 day to January 29th, which results in 2014-01-30; then we add 1 month, so we get 2014-02-30, which then adjusts down to 2014-02-28. In the second example, we add 1 month <em>first</em>, where we get 2014-02-29, which adjusts down to 2014-02-28, and <em>then</em> add 1 day, which results in 2014-03-01. One design principle that helps in this case is that, in the presence of multiple Periods, the operations will be ordered by the Periods&#39; <em>types</em>, not their value or positional order; this means <code>Year</code> will always be added first, then <code>Month</code>, then <code>Week</code>, etc. Hence the following <em>does</em> result in associativity and Just Works:</p><pre><code class="language-julia-repl hljs">julia&gt; Date(2014,1,29) + Dates.Day(1) + Dates.Month(1)
2014-03-01

julia&gt; Date(2014,1,29) + Dates.Month(1) + Dates.Day(1)
2014-03-01</code></pre><p>Tricky? Perhaps. What is an innocent <code>Dates</code> user to do? The bottom line is to be aware that explicitly forcing a certain associativity, when dealing with months, may lead to some unexpected results, but otherwise, everything should work as expected. Thankfully, that&#39;s pretty much the extent of the odd cases in date-period arithmetic when dealing with time in UT (avoiding the &quot;joys&quot; of dealing with daylight savings, leap seconds, etc.).</p><p>As a bonus, all period arithmetic objects work directly with ranges:</p><pre><code class="language-julia-repl hljs">julia&gt; dr = Date(2014,1,29):Day(1):Date(2014,2,3)
Date(&quot;2014-01-29&quot;):Day(1):Date(&quot;2014-02-03&quot;)

julia&gt; collect(dr)
6-element Vector{Date}:
 2014-01-29
 2014-01-30
 2014-01-31
 2014-02-01
 2014-02-02
 2014-02-03

julia&gt; dr = Date(2014,1,29):Dates.Month(1):Date(2014,07,29)
Date(&quot;2014-01-29&quot;):Month(1):Date(&quot;2014-07-29&quot;)

julia&gt; collect(dr)
7-element Vector{Date}:
 2014-01-29
 2014-02-28
 2014-03-29
 2014-04-29
 2014-05-29
 2014-06-29
 2014-07-29</code></pre><h2 id="Adjuster-Functions"><a class="docs-heading-anchor" href="#Adjuster-Functions">Adjuster Functions</a><a id="Adjuster-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Adjuster-Functions" title="Permalink"></a></h2><p>As convenient as date-period arithmetic is, often the kinds of calculations needed on dates take on a <em>calendrical</em> or <em>temporal</em> nature rather than a fixed number of periods. Holidays are a perfect example; most follow rules such as &quot;Memorial Day = Last Monday of May&quot;, or &quot;Thanksgiving = 4th Thursday of November&quot;. These kinds of temporal expressions deal with rules relative to the calendar, like first or last of the month, next Tuesday, or the first and third Wednesdays, etc.</p><p>The <code>Dates</code> module provides the <em>adjuster</em> API through several convenient methods that aid in simply and succinctly expressing temporal rules. The first group of adjuster methods deal with the first and last of weeks, months, quarters, and years. They each take a single <a href="Dates.html#Dates.TimeType"><code>TimeType</code></a> as input and return or <em>adjust to</em> the first or last of the desired period relative to the input.</p><pre><code class="language-julia-repl hljs">julia&gt; Dates.firstdayofweek(Date(2014,7,16)) # Adjusts the input to the Monday of the input&#39;s week
2014-07-14

julia&gt; Dates.lastdayofmonth(Date(2014,7,16)) # Adjusts to the last day of the input&#39;s month
2014-07-31

julia&gt; Dates.lastdayofquarter(Date(2014,7,16)) # Adjusts to the last day of the input&#39;s quarter
2014-09-30</code></pre><p>The next two higher-order methods, <a href="Dates.html#Dates.tonext-Tuple{TimeType, Int64}"><code>tonext</code></a>, and <a href="Dates.html#Dates.toprev-Tuple{TimeType, Int64}"><code>toprev</code></a>, generalize working with temporal expressions by taking a <code>DateFunction</code> as first argument, along with a starting <a href="Dates.html#Dates.TimeType"><code>TimeType</code></a>. A <code>DateFunction</code> is just a function, usually anonymous, that takes a single <a href="Dates.html#Dates.TimeType"><code>TimeType</code></a> as input and returns a <a href="../base/numbers.html#Core.Bool"><code>Bool</code></a>, <code>true</code> indicating a satisfied adjustment criterion. For example:</p><pre><code class="language-julia-repl hljs">julia&gt; istuesday = x-&gt;Dates.dayofweek(x) == Dates.Tuesday; # Returns true if the day of the week of x is Tuesday

julia&gt; Dates.tonext(istuesday, Date(2014,7,13)) # 2014-07-13 is a Sunday
2014-07-15

julia&gt; Dates.tonext(Date(2014,7,13), Dates.Tuesday) # Convenience method provided for day of the week adjustments
2014-07-15</code></pre><p>This is useful with the do-block syntax for more complex temporal expressions:</p><pre><code class="language-julia-repl hljs">julia&gt; Dates.tonext(Date(2014,7,13)) do x
           # Return true on the 4th Thursday of November (Thanksgiving)
           Dates.dayofweek(x) == Dates.Thursday &amp;&amp;
           Dates.dayofweekofmonth(x) == 4 &amp;&amp;
           Dates.month(x) == Dates.November
       end
2014-11-27</code></pre><p>The <a href="../base/collections.html#Base.filter"><code>Base.filter</code></a> method can be used to obtain all valid dates/moments in a specified range:</p><pre><code class="language-julia-repl hljs"># Pittsburgh street cleaning; Every 2nd Tuesday from April to November
# Date range from January 1st, 2014 to January 1st, 2015
julia&gt; dr = Dates.Date(2014):Day(1):Dates.Date(2015);

julia&gt; filter(dr) do x
           Dates.dayofweek(x) == Dates.Tue &amp;&amp;
           Dates.April &lt;= Dates.month(x) &lt;= Dates.Nov &amp;&amp;
           Dates.dayofweekofmonth(x) == 2
       end
8-element Vector{Date}:
 2014-04-08
 2014-05-13
 2014-06-10
 2014-07-08
 2014-08-12
 2014-09-09
 2014-10-14
 2014-11-11</code></pre><p>Additional examples and tests are available in <a href="https://github.com/JuliaLang/julia/blob/master/stdlib/Dates/test/adjusters.jl"><code>stdlib/Dates/test/adjusters.jl</code></a>.</p><h2 id="Period-Types"><a class="docs-heading-anchor" href="#Period-Types">Period Types</a><a id="Period-Types-1"></a><a class="docs-heading-anchor-permalink" href="#Period-Types" title="Permalink"></a></h2><p>Periods are a human view of discrete, sometimes irregular durations of time. Consider 1 month; it could represent, in days, a value of 28, 29, 30, or 31 depending on the year and month context. Or a year could represent 365 or 366 days in the case of a leap year. <a href="Dates.html#Dates.Period"><code>Period</code></a> types are simple <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a> wrappers and are constructed by wrapping any <code>Int64</code> convertible type, i.e. <code>Year(1)</code> or <code>Month(3.0)</code>. Arithmetic between <a href="Dates.html#Dates.Period"><code>Period</code></a> of the same type behave like integers, and limited <code>Period-Real</code> arithmetic is available.  You can extract the underlying integer with <a href="Dates.html#Dates.value"><code>Dates.value</code></a>.</p><pre><code class="language-julia-repl hljs">julia&gt; y1 = Dates.Year(1)
1 year

julia&gt; y2 = Dates.Year(2)
2 years

julia&gt; y3 = Dates.Year(10)
10 years

julia&gt; y1 + y2
3 years

julia&gt; div(y3,y2)
5

julia&gt; y3 - y2
8 years

julia&gt; y3 % y2
0 years

julia&gt; div(y3,3) # mirrors integer division
3 years

julia&gt; Dates.value(Dates.Millisecond(10))
10</code></pre><p>Representing periods or durations that are not integer multiples of the basic types can be achieved with the <a href="Dates.html#Dates.CompoundPeriod"><code>Dates.CompoundPeriod</code></a> type. Compound periods may be constructed manually from simple <a href="Dates.html#Dates.Period"><code>Period</code></a> types. Additionally, the <a href="Dates.html#Dates.canonicalize"><code>canonicalize</code></a> function can be used to break down a period into a <a href="Dates.html#Dates.CompoundPeriod"><code>Dates.CompoundPeriod</code></a>. This is particularly useful to convert a duration, e.g., a difference of two <code>DateTime</code>, into a more convenient representation.</p><pre><code class="language-julia-repl hljs">julia&gt; cp = Dates.CompoundPeriod(Day(1),Minute(1))
1 day, 1 minute

julia&gt; t1 = DateTime(2018,8,8,16,58,00)
2018-08-08T16:58:00

julia&gt; t2 = DateTime(2021,6,23,10,00,00)
2021-06-23T10:00:00

julia&gt; canonicalize(t2-t1) # creates a CompoundPeriod
149 weeks, 6 days, 17 hours, 2 minutes</code></pre><h2 id="Rounding"><a class="docs-heading-anchor" href="#Rounding">Rounding</a><a id="Rounding-1"></a><a class="docs-heading-anchor-permalink" href="#Rounding" title="Permalink"></a></h2><p><a href="Dates.html#Dates.Date"><code>Date</code></a> and <a href="Dates.html#Dates.DateTime"><code>DateTime</code></a> values can be rounded to a specified resolution (e.g., 1 month or 15 minutes) with <a href="../base/math.html#Base.floor"><code>floor</code></a>, <a href="../base/math.html#Base.ceil"><code>ceil</code></a>, or <a href="../base/math.html#Base.round"><code>round</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; floor(Date(1985, 8, 16), Dates.Month)
1985-08-01

julia&gt; ceil(DateTime(2013, 2, 13, 0, 31, 20), Dates.Minute(15))
2013-02-13T00:45:00

julia&gt; round(DateTime(2016, 8, 6, 20, 15), Dates.Day)
2016-08-07T00:00:00</code></pre><p>Unlike the numeric <a href="../base/math.html#Base.round"><code>round</code></a> method, which breaks ties toward the even number by default, the <a href="Dates.html#Dates.TimeType"><code>TimeType</code></a><a href="../base/math.html#Base.round"><code>round</code></a> method uses the <code>RoundNearestTiesUp</code> rounding mode. (It&#39;s difficult to guess what breaking ties to nearest &quot;even&quot; <a href="Dates.html#Dates.TimeType"><code>TimeType</code></a> would entail.) Further details on the available <code>RoundingMode</code> s can be found in the <a href="Dates.html#stdlib-dates-api">API reference</a>.</p><p>Rounding should generally behave as expected, but there are a few cases in which the expected behaviour is not obvious.</p><h3 id="Rounding-Epoch"><a class="docs-heading-anchor" href="#Rounding-Epoch">Rounding Epoch</a><a id="Rounding-Epoch-1"></a><a class="docs-heading-anchor-permalink" href="#Rounding-Epoch" title="Permalink"></a></h3><p>In many cases, the resolution specified for rounding (e.g., <code>Dates.Second(30)</code>) divides evenly into the next largest period (in this case, <code>Dates.Minute(1)</code>). But rounding behaviour in cases in which this is not true may lead to confusion. What is the expected result of rounding a <a href="Dates.html#Dates.DateTime"><code>DateTime</code></a> to the nearest 10 hours?</p><pre><code class="language-julia-repl hljs">julia&gt; round(DateTime(2016, 7, 17, 11, 55), Dates.Hour(10))
2016-07-17T12:00:00</code></pre><p>That may seem confusing, given that the hour (12) is not divisible by 10. The reason that <code>2016-07-17T12:00:00</code> was chosen is that it is 17,676,660 hours after <code>0000-01-01T00:00:00</code>, and 17,676,660 is divisible by 10.</p><p>As Julia <a href="Dates.html#Dates.Date"><code>Date</code></a> and <a href="Dates.html#Dates.DateTime"><code>DateTime</code></a> values are represented according to the ISO 8601 standard, <code>0000-01-01T00:00:00</code> was chosen as base (or &quot;rounding epoch&quot;) from which to begin the count of days (and milliseconds) used in rounding calculations. (Note that this differs slightly from Julia&#39;s internal representation of <a href="Dates.html#Dates.Date"><code>Date</code></a> s using <a href="https://en.wikipedia.org/wiki/Rata_Die">Rata Die notation</a>; but since the ISO 8601 standard is most visible to the end user, <code>0000-01-01T00:00:00</code> was chosen as the rounding epoch instead of the <code>0000-12-31T00:00:00</code> used internally to minimize confusion.)</p><p>The only exception to the use of <code>0000-01-01T00:00:00</code> as the rounding epoch is when rounding to weeks. Rounding to the nearest week will always return a Monday (the first day of the week as specified by ISO 8601). For this reason, we use <code>0000-01-03T00:00:00</code> (the first day of the first week of year 0000, as defined by ISO 8601) as the base when rounding to a number of weeks.</p><p>Here is a related case in which the expected behaviour is not necessarily obvious: What happens when we round to the nearest <code>P(2)</code>, where <code>P</code> is a <a href="Dates.html#Dates.Period"><code>Period</code></a> type? In some cases (specifically, when <code>P &lt;: Dates.TimePeriod</code>) the answer is clear:</p><pre><code class="language-julia-repl hljs">julia&gt; round(DateTime(2016, 7, 17, 8, 55, 30), Dates.Hour(2))
2016-07-17T08:00:00

julia&gt; round(DateTime(2016, 7, 17, 8, 55, 30), Dates.Minute(2))
2016-07-17T08:56:00</code></pre><p>This seems obvious, because two of each of these periods still divides evenly into the next larger order period. But in the case of two months (which still divides evenly into one year), the answer may be surprising:</p><pre><code class="language-julia-repl hljs">julia&gt; round(DateTime(2016, 7, 17, 8, 55, 30), Dates.Month(2))
2016-07-01T00:00:00</code></pre><p>Why round to the first day in July, even though it is month 7 (an odd number)? The key is that months are 1-indexed (the first month is assigned 1), unlike hours, minutes, seconds, and milliseconds (the first of which are assigned 0).</p><p>This means that rounding a <a href="Dates.html#Dates.DateTime"><code>DateTime</code></a> to an even multiple of seconds, minutes, hours, or years (because the ISO 8601 specification includes a year zero) will result in a <a href="Dates.html#Dates.DateTime"><code>DateTime</code></a> with an even value in that field, while rounding a <a href="Dates.html#Dates.DateTime"><code>DateTime</code></a> to an even multiple of months will result in the months field having an odd value. Because both months and years may contain an irregular number of days, whether rounding to an even number of days will result in an even value in the days field is uncertain.</p><p>See the <a href="Dates.html#stdlib-dates-api">API reference</a> for additional information on methods exported from the <code>Dates</code> module.</p><h2 id="stdlib-dates-api"><a class="docs-heading-anchor" href="#stdlib-dates-api">API reference</a><a id="stdlib-dates-api-1"></a><a class="docs-heading-anchor-permalink" href="#stdlib-dates-api" title="Permalink"></a></h2><h3 id="Dates-and-Time-Types"><a class="docs-heading-anchor" href="#Dates-and-Time-Types">Dates and Time Types</a><a id="Dates-and-Time-Types-1"></a><a class="docs-heading-anchor-permalink" href="#Dates-and-Time-Types" title="Permalink"></a></h3><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Period" href="#Dates.Period"><code>Dates.Period</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Period
Year
Quarter
Month
Week
Day
Hour
Minute
Second
Millisecond
Microsecond
Nanosecond</code></pre><p><code>Period</code> types represent discrete, human representations of time.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/types.jl#L5-L20">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.CompoundPeriod" href="#Dates.CompoundPeriod"><code>Dates.CompoundPeriod</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">CompoundPeriod</code></pre><p>A <code>CompoundPeriod</code> is useful for expressing time periods that are not a fixed multiple of smaller periods. For example, &quot;a year and a  day&quot; is not a fixed number of days, but can be expressed using a <code>CompoundPeriod</code>. In fact, a <code>CompoundPeriod</code> is automatically generated by addition of different period types, e.g. <code>Year(1) + Day(1)</code> produces a <code>CompoundPeriod</code> result.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/periods.jl#L121-L129">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Instant" href="#Dates.Instant"><code>Dates.Instant</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Instant</code></pre><p><code>Instant</code> types represent integer-based, machine representations of time as continuous timelines starting from an epoch.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/types.jl#L83-L88">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.UTInstant" href="#Dates.UTInstant"><code>Dates.UTInstant</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">UTInstant{T}</code></pre><p>The <code>UTInstant</code> represents a machine timeline based on UT time (1 day = one revolution of the earth). The <code>T</code> is a <code>Period</code> parameter that indicates the resolution or precision of the instant.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/types.jl#L91-L97">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.TimeType" href="#Dates.TimeType"><code>Dates.TimeType</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">TimeType</code></pre><p><code>TimeType</code> types wrap <code>Instant</code> machine instances to provide human representations of the machine instant. <code>Time</code>, <code>DateTime</code> and <code>Date</code> are subtypes of <code>TimeType</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/types.jl#L132-L137">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.DateTime" href="#Dates.DateTime"><code>Dates.DateTime</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">DateTime</code></pre><p><code>DateTime</code> represents a point in time according to the proleptic Gregorian calendar. The finest resolution of the time is millisecond (i.e., microseconds or nanoseconds cannot be represented by this type). The type supports fixed-point arithmetic, and thus is prone to underflowing (and overflowing). A notable consequence is rounding when adding a <code>Microsecond</code> or a <code>Nanosecond</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; dt = DateTime(2023, 8, 19, 17, 45, 32, 900)
2023-08-19T17:45:32.900

julia&gt; dt + Millisecond(1)
2023-08-19T17:45:32.901

julia&gt; dt + Microsecond(1000) # 1000us == 1ms
2023-08-19T17:45:32.901

julia&gt; dt + Microsecond(999) # 999us rounded to 1000us
2023-08-19T17:45:32.901

julia&gt; dt + Microsecond(1499) # 1499 rounded to 1000us
2023-08-19T17:45:32.901</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/types.jl#L142-L167">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Date" href="#Dates.Date"><code>Dates.Date</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Date</code></pre><p><code>Date</code> wraps a <code>UTInstant{Day}</code> and interprets it according to the proleptic Gregorian calendar.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/types.jl#L173-L177">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Time" href="#Dates.Time"><code>Dates.Time</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Time</code></pre><p><code>Time</code> wraps a <code>Nanosecond</code> and represents a specific moment in a 24-hour day.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/types.jl#L183-L187">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.TimeZone" href="#Dates.TimeZone"><code>Dates.TimeZone</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">TimeZone</code></pre><p>Geographic zone generally based on longitude determining what the time is at a certain location. Some time zones observe daylight savings (eg EST -&gt; EDT). For implementations and more support, see the <a href="https://github.com/JuliaTime/TimeZones.jl"><code>TimeZones.jl</code></a> package</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/types.jl#L115-L121">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.UTC" href="#Dates.UTC"><code>Dates.UTC</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">UTC</code></pre><p><code>UTC</code>, or Coordinated Universal Time, is the <a href="Dates.html#Dates.TimeZone"><code>TimeZone</code></a> from which all others are measured. It is associated with the time at 0° longitude. It is not adjusted for daylight savings.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/types.jl#L124-L129">source</a></section></article><h3 id="Dates-Functions"><a class="docs-heading-anchor" href="#Dates-Functions">Dates Functions</a><a id="Dates-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Dates-Functions" title="Permalink"></a></h3><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.DateTime-NTuple{7, Int64}" href="#Dates.DateTime-NTuple{7, Int64}"><code>Dates.DateTime</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">DateTime(y, [m, d, h, mi, s, ms]) -&gt; DateTime</code></pre><p>Construct a <code>DateTime</code> type by parts. Arguments must be convertible to <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/types.jl#L238-L242">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.DateTime-Tuple{Period}" href="#Dates.DateTime-Tuple{Period}"><code>Dates.DateTime</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">DateTime(periods::Period...) -&gt; DateTime</code></pre><p>Construct a <code>DateTime</code> type by <code>Period</code> type parts. Arguments may be in any order. DateTime parts not provided will default to the value of <code>Dates.default(period)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/types.jl#L335-L340">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.DateTime-Tuple{Function, Vararg{Any}}" href="#Dates.DateTime-Tuple{Function, Vararg{Any}}"><code>Dates.DateTime</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">DateTime(f::Function, y[, m, d, h, mi, s]; step=Day(1), limit=10000) -&gt; DateTime</code></pre><p>Create a <code>DateTime</code> through the adjuster API. The starting point will be constructed from the provided <code>y, m, d...</code> arguments, and will be adjusted until <code>f::Function</code> returns <code>true</code>. The step size in adjusting can be provided manually through the <code>step</code> keyword. <code>limit</code> provides a limit to the max number of iterations the adjustment API will pursue before throwing an error (in the case that <code>f::Function</code> is never satisfied).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; DateTime(dt -&gt; second(dt) == 40, 2010, 10, 20, 10; step = Second(1))
2010-10-20T10:00:40

julia&gt; DateTime(dt -&gt; hour(dt) == 20, 2010, 10, 20, 10; step = Hour(1), limit = 5)
ERROR: ArgumentError: Adjustment limit reached: 5 iterations
Stacktrace:
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/adjusters.jl#L275-L294">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.DateTime-Tuple{TimeType}" href="#Dates.DateTime-Tuple{TimeType}"><code>Dates.DateTime</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">DateTime(dt::Date) -&gt; DateTime</code></pre><p>Convert a <code>Date</code> to a <code>DateTime</code>. The hour, minute, second, and millisecond parts of the new <code>DateTime</code> are assumed to be zero.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/conversions.jl#L14-L19">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.DateTime-Tuple{AbstractString, AbstractString}" href="#Dates.DateTime-Tuple{AbstractString, AbstractString}"><code>Dates.DateTime</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">DateTime(dt::AbstractString, format::AbstractString; locale=&quot;english&quot;) -&gt; DateTime</code></pre><p>Construct a <code>DateTime</code> by parsing the <code>dt</code> date time string following the pattern given in the <code>format</code> string (see <a href="Dates.html#Dates.DateFormat"><code>DateFormat</code></a>  for syntax).</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This method creates a <code>DateFormat</code> object each time it is called. It is recommended that you create a <a href="Dates.html#Dates.DateFormat"><code>DateFormat</code></a> object instead and use that as the second argument to avoid performance loss when using the same format repeatedly.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; DateTime(&quot;2020-01-01&quot;, &quot;yyyy-mm-dd&quot;)
2020-01-01T00:00:00

julia&gt; a = (&quot;2020-01-01&quot;, &quot;2020-01-02&quot;);

julia&gt; [DateTime(d, dateformat&quot;yyyy-mm-dd&quot;) for d ∈ a] # preferred
2-element Vector{DateTime}:
 2020-01-01T00:00:00
 2020-01-02T00:00:00</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/io.jl#L530-L553">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.format-Tuple{TimeType, AbstractString}" href="#Dates.format-Tuple{TimeType, AbstractString}"><code>Dates.format</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">format(dt::TimeType, format::AbstractString; locale=&quot;english&quot;) -&gt; AbstractString</code></pre><p>Construct a string by using a <code>TimeType</code> object and applying the provided <code>format</code>. The following character codes can be used to construct the <code>format</code> string:</p><table><tr><th style="text-align: left">Code</th><th style="text-align: left">Examples</th><th style="text-align: left">Comment</th></tr><tr><td style="text-align: left"><code>y</code></td><td style="text-align: left">6</td><td style="text-align: left">Numeric year with a fixed width</td></tr><tr><td style="text-align: left"><code>Y</code></td><td style="text-align: left">1996</td><td style="text-align: left">Numeric year with a minimum width</td></tr><tr><td style="text-align: left"><code>m</code></td><td style="text-align: left">1, 12</td><td style="text-align: left">Numeric month with a minimum width</td></tr><tr><td style="text-align: left"><code>u</code></td><td style="text-align: left">Jan</td><td style="text-align: left">Month name shortened to 3-chars according to the <code>locale</code></td></tr><tr><td style="text-align: left"><code>U</code></td><td style="text-align: left">January</td><td style="text-align: left">Full month name according to the <code>locale</code> keyword</td></tr><tr><td style="text-align: left"><code>d</code></td><td style="text-align: left">1, 31</td><td style="text-align: left">Day of the month with a minimum width</td></tr><tr><td style="text-align: left"><code>H</code></td><td style="text-align: left">0, 23</td><td style="text-align: left">Hour (24-hour clock) with a minimum width</td></tr><tr><td style="text-align: left"><code>M</code></td><td style="text-align: left">0, 59</td><td style="text-align: left">Minute with a minimum width</td></tr><tr><td style="text-align: left"><code>S</code></td><td style="text-align: left">0, 59</td><td style="text-align: left">Second with a minimum width</td></tr><tr><td style="text-align: left"><code>s</code></td><td style="text-align: left">000, 500</td><td style="text-align: left">Millisecond with a minimum width of 3</td></tr><tr><td style="text-align: left"><code>e</code></td><td style="text-align: left">Mon, Tue</td><td style="text-align: left">Abbreviated days of the week</td></tr><tr><td style="text-align: left"><code>E</code></td><td style="text-align: left">Monday</td><td style="text-align: left">Full day of week name</td></tr></table><p>The number of sequential code characters indicate the width of the code. A format of <code>yyyy-mm</code> specifies that the code <code>y</code> should have a width of four while <code>m</code> a width of two. Codes that yield numeric digits have an associated mode: fixed-width or minimum-width. The fixed-width mode left-pads the value with zeros when it is shorter than the specified width and truncates the value when longer. Minimum-width mode works the same as fixed-width except that it does not truncate values longer than the width.</p><p>When creating a <code>format</code> you can use any non-code characters as a separator. For example to generate the string &quot;1996-01-15T00:00:00&quot; you could use <code>format</code>: &quot;yyyy-mm-ddTHH:MM:SS&quot;. Note that if you need to use a code character as a literal you can use the escape character backslash. The string &quot;1996y01m&quot; can be produced with the format &quot;yyyy\ymm\m&quot;.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/io.jl#L667-L699">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.DateFormat" href="#Dates.DateFormat"><code>Dates.DateFormat</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">DateFormat(format::AbstractString, locale=&quot;english&quot;) -&gt; DateFormat</code></pre><p>Construct a date formatting object that can be used for parsing date strings or formatting a date object as a string. The following character codes can be used to construct the <code>format</code> string:</p><table><tr><th style="text-align: left">Code</th><th style="text-align: left">Matches</th><th style="text-align: left">Comment</th></tr><tr><td style="text-align: left"><code>Y</code></td><td style="text-align: left">1996, 96</td><td style="text-align: left">Returns year of 1996, 0096</td></tr><tr><td style="text-align: left"><code>y</code></td><td style="text-align: left">1996, 96</td><td style="text-align: left">Same as <code>Y</code> on <code>parse</code> but discards excess digits on <code>format</code></td></tr><tr><td style="text-align: left"><code>m</code></td><td style="text-align: left">1, 01</td><td style="text-align: left">Matches 1 or 2-digit months</td></tr><tr><td style="text-align: left"><code>u</code></td><td style="text-align: left">Jan</td><td style="text-align: left">Matches abbreviated months according to the <code>locale</code> keyword</td></tr><tr><td style="text-align: left"><code>U</code></td><td style="text-align: left">January</td><td style="text-align: left">Matches full month names according to the <code>locale</code> keyword</td></tr><tr><td style="text-align: left"><code>d</code></td><td style="text-align: left">1, 01</td><td style="text-align: left">Matches 1 or 2-digit days</td></tr><tr><td style="text-align: left"><code>H</code></td><td style="text-align: left">00</td><td style="text-align: left">Matches hours (24-hour clock)</td></tr><tr><td style="text-align: left"><code>I</code></td><td style="text-align: left">00</td><td style="text-align: left">For outputting hours with 12-hour clock</td></tr><tr><td style="text-align: left"><code>M</code></td><td style="text-align: left">00</td><td style="text-align: left">Matches minutes</td></tr><tr><td style="text-align: left"><code>S</code></td><td style="text-align: left">00</td><td style="text-align: left">Matches seconds</td></tr><tr><td style="text-align: left"><code>s</code></td><td style="text-align: left">.500</td><td style="text-align: left">Matches milliseconds</td></tr><tr><td style="text-align: left"><code>e</code></td><td style="text-align: left">Mon, Tues</td><td style="text-align: left">Matches abbreviated days of the week</td></tr><tr><td style="text-align: left"><code>E</code></td><td style="text-align: left">Monday</td><td style="text-align: left">Matches full name days of the week</td></tr><tr><td style="text-align: left"><code>p</code></td><td style="text-align: left">AM</td><td style="text-align: left">Matches AM/PM (case-insensitive)</td></tr><tr><td style="text-align: left"><code>yyyymmdd</code></td><td style="text-align: left">19960101</td><td style="text-align: left">Matches fixed-width year, month, and day</td></tr></table><p>Characters not listed above are normally treated as delimiters between date and time slots. For example a <code>dt</code> string of &quot;1996-01-15T00:00:00.0&quot; would have a <code>format</code> string like &quot;y-m-dTH:M:S.s&quot;. If you need to use a code character as a delimiter you can escape it using backslash. The date &quot;1995y01m&quot; would have the format &quot;y\ym\m&quot;.</p><p>Note that 12:00AM corresponds 00:00 (midnight), and 12:00PM corresponds to 12:00 (noon). When parsing a time with a <code>p</code> specifier, any hour (either <code>H</code> or <code>I</code>) is interpreted as as a 12-hour clock, so the <code>I</code> code is mainly useful for output.</p><p>Creating a DateFormat object is expensive. Whenever possible, create it once and use it many times or try the <a href="Dates.html#Dates.@dateformat_str"><code>dateformat&quot;&quot;</code></a> string macro. Using this macro creates the DateFormat object once at macro expansion time and reuses it later. There are also several <a href="Dates.html#Common-Date-Formatters">pre-defined formatters</a>, listed later.</p><p>See <a href="Dates.html#Dates.DateTime"><code>DateTime</code></a> and <a href="Dates.html#Dates.format-Tuple{TimeType, AbstractString}"><code>format</code></a> for how to use a DateFormat object to parse and write Date strings respectively.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/io.jl#L352-L393">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.@dateformat_str" href="#Dates.@dateformat_str"><code>Dates.@dateformat_str</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">dateformat&quot;Y-m-d H:M:S&quot;</code></pre><p>Create a <a href="Dates.html#Dates.DateFormat"><code>DateFormat</code></a> object. Similar to <code>DateFormat(&quot;Y-m-d H:M:S&quot;)</code> but creates the DateFormat object once during macro expansion.</p><p>See <a href="Dates.html#Dates.DateFormat"><code>DateFormat</code></a> for details about format specifiers.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/io.jl#L455-L462">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.DateTime-Tuple{AbstractString, DateFormat}" href="#Dates.DateTime-Tuple{AbstractString, DateFormat}"><code>Dates.DateTime</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">DateTime(dt::AbstractString, df::DateFormat=ISODateTimeFormat) -&gt; DateTime</code></pre><p>Construct a <code>DateTime</code> by parsing the <code>dt</code> date time string following the pattern given in the <a href="Dates.html#Dates.DateFormat"><code>DateFormat</code></a> object, or dateformat&quot;yyyy-mm-dd\THH:MM:SS.s&quot; if omitted.</p><p>Similar to <code>DateTime(::AbstractString, ::AbstractString)</code> but more efficient when repeatedly parsing similarly formatted date time strings with a pre-created <code>DateFormat</code> object.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/io.jl#L558-L567">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Date-Tuple{Int64, Int64, Int64}" href="#Dates.Date-Tuple{Int64, Int64, Int64}"><code>Dates.Date</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Date(y, [m, d]) -&gt; Date</code></pre><p>Construct a <code>Date</code> type by parts. Arguments must be convertible to <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/types.jl#L270-L274">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Date-Tuple{Period}" href="#Dates.Date-Tuple{Period}"><code>Dates.Date</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Date(period::Period...) -&gt; Date</code></pre><p>Construct a <code>Date</code> type by <code>Period</code> type parts. Arguments may be in any order. <code>Date</code> parts not provided will default to the value of <code>Dates.default(period)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/types.jl#L356-L361">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Date-Tuple{Function, Any, Any, Any}" href="#Dates.Date-Tuple{Function, Any, Any, Any}"><code>Dates.Date</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Date(f::Function, y[, m, d]; step=Day(1), limit=10000) -&gt; Date</code></pre><p>Create a <code>Date</code> through the adjuster API. The starting point will be constructed from the provided <code>y, m, d</code> arguments, and will be adjusted until <code>f::Function</code> returns <code>true</code>. The step size in adjusting can be provided manually through the <code>step</code> keyword. <code>limit</code> provides a limit to the max number of iterations the adjustment API will pursue before throwing an error (given that <code>f::Function</code> is never satisfied).</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Date(date -&gt; week(date) == 20, 2010, 01, 01)
2010-05-17

julia&gt; Date(date -&gt; year(date) == 2010, 2000, 01, 01)
2010-01-01

julia&gt; Date(date -&gt; month(date) == 10, 2000, 01, 01; limit = 5)
ERROR: ArgumentError: Adjustment limit reached: 5 iterations
Stacktrace:
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/adjusters.jl#L248-L270">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Date-Tuple{TimeType}" href="#Dates.Date-Tuple{TimeType}"><code>Dates.Date</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Date(dt::DateTime) -&gt; Date</code></pre><p>Convert a <code>DateTime</code> to a <code>Date</code>. The hour, minute, second, and millisecond parts of the <code>DateTime</code> are truncated, so only the year, month and day parts are used in construction.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/conversions.jl#L5-L11">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Date-Tuple{AbstractString, AbstractString}" href="#Dates.Date-Tuple{AbstractString, AbstractString}"><code>Dates.Date</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Date(d::AbstractString, format::AbstractString; locale=&quot;english&quot;) -&gt; Date</code></pre><p>Construct a <code>Date</code> by parsing the <code>d</code> date string following the pattern given in the <code>format</code> string (see <a href="Dates.html#Dates.DateFormat"><code>DateFormat</code></a> for syntax).</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This method creates a <code>DateFormat</code> object each time it is called. It is recommended that you create a <a href="Dates.html#Dates.DateFormat"><code>DateFormat</code></a> object instead and use that as the second argument to avoid performance loss when using the same format repeatedly.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Date(&quot;2020-01-01&quot;, &quot;yyyy-mm-dd&quot;)
2020-01-01

julia&gt; a = (&quot;2020-01-01&quot;, &quot;2020-01-02&quot;);

julia&gt; [Date(d, dateformat&quot;yyyy-mm-dd&quot;) for d ∈ a] # preferred
2-element Vector{Date}:
 2020-01-01
 2020-01-02</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/io.jl#L570-L593">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Date-Tuple{AbstractString, DateFormat}" href="#Dates.Date-Tuple{AbstractString, DateFormat}"><code>Dates.Date</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Date(d::AbstractString, df::DateFormat=ISODateFormat) -&gt; Date</code></pre><p>Construct a <code>Date</code> by parsing the <code>d</code> date string following the pattern given in the <a href="Dates.html#Dates.DateFormat"><code>DateFormat</code></a> object, or dateformat&quot;yyyy-mm-dd&quot; if omitted.</p><p>Similar to <code>Date(::AbstractString, ::AbstractString)</code> but more efficient when repeatedly parsing similarly formatted date strings with a pre-created <code>DateFormat</code> object.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/io.jl#L598-L607">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Time-NTuple{5, Int64}" href="#Dates.Time-NTuple{5, Int64}"><code>Dates.Time</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Time(h, [mi, s, ms, us, ns]) -&gt; Time</code></pre><p>Construct a <code>Time</code> type by parts. Arguments must be convertible to <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/types.jl#L289-L293">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Time-Tuple{TimePeriod}" href="#Dates.Time-Tuple{TimePeriod}"><code>Dates.Time</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Time(period::TimePeriod...) -&gt; Time</code></pre><p>Construct a <code>Time</code> type by <code>Period</code> type parts. Arguments may be in any order. <code>Time</code> parts not provided will default to the value of <code>Dates.default(period)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/types.jl#L372-L377">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Time-Tuple{Function, Vararg{Any}}" href="#Dates.Time-Tuple{Function, Vararg{Any}}"><code>Dates.Time</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Time(f::Function, h, mi=0; step::Period=Second(1), limit::Int=10000)
Time(f::Function, h, mi, s; step::Period=Millisecond(1), limit::Int=10000)
Time(f::Function, h, mi, s, ms; step::Period=Microsecond(1), limit::Int=10000)
Time(f::Function, h, mi, s, ms, us; step::Period=Nanosecond(1), limit::Int=10000)</code></pre><p>Create a <code>Time</code> through the adjuster API. The starting point will be constructed from the provided <code>h, mi, s, ms, us</code> arguments, and will be adjusted until <code>f::Function</code> returns <code>true</code>. The step size in adjusting can be provided manually through the <code>step</code> keyword. <code>limit</code> provides a limit to the max number of iterations the adjustment API will pursue before throwing an error (in the case that <code>f::Function</code> is never satisfied). Note that the default step will adjust to allow for greater precision for the given arguments; i.e. if hour, minute, and second arguments are provided, the default step will be <code>Millisecond(1)</code> instead of <code>Second(1)</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Time(t -&gt; minute(t) == 30, 20)
20:30:00

julia&gt; Time(t -&gt; minute(t) == 0, 20)
20:00:00

julia&gt; Time(t -&gt; hour(t) == 10, 3; limit = 5)
ERROR: ArgumentError: Adjustment limit reached: 5 iterations
Stacktrace:
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/adjusters.jl#L313-L340">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Time-Tuple{DateTime}" href="#Dates.Time-Tuple{DateTime}"><code>Dates.Time</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Time(dt::DateTime) -&gt; Time</code></pre><p>Convert a <code>DateTime</code> to a <code>Time</code>. The hour, minute, second, and millisecond parts of the <code>DateTime</code> are used to create the new <code>Time</code>. Microsecond and nanoseconds are zero by default.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/conversions.jl#L22-L27">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Time-Tuple{AbstractString, AbstractString}" href="#Dates.Time-Tuple{AbstractString, AbstractString}"><code>Dates.Time</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Time(t::AbstractString, format::AbstractString; locale=&quot;english&quot;) -&gt; Time</code></pre><p>Construct a <code>Time</code> by parsing the <code>t</code> time string following the pattern given in the <code>format</code> string (see <a href="Dates.html#Dates.DateFormat"><code>DateFormat</code></a> for syntax).</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This method creates a <code>DateFormat</code> object each time it is called. It is recommended that you create a <a href="Dates.html#Dates.DateFormat"><code>DateFormat</code></a> object instead and use that as the second argument to avoid performance loss when using the same format repeatedly.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Time(&quot;12:34pm&quot;, &quot;HH:MMp&quot;)
12:34:00

julia&gt; a = (&quot;12:34pm&quot;, &quot;2:34am&quot;);

julia&gt; [Time(d, dateformat&quot;HH:MMp&quot;) for d ∈ a] # preferred
2-element Vector{Time}:
 12:34:00
 02:34:00</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/io.jl#L610-L633">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Time-Tuple{AbstractString, DateFormat}" href="#Dates.Time-Tuple{AbstractString, DateFormat}"><code>Dates.Time</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Time(t::AbstractString, df::DateFormat=ISOTimeFormat) -&gt; Time</code></pre><p>Construct a <code>Time</code> by parsing the <code>t</code> date time string following the pattern given in the <a href="Dates.html#Dates.DateFormat"><code>DateFormat</code></a> object, or dateformat&quot;HH:MM:SS.s&quot; if omitted.</p><p>Similar to <code>Time(::AbstractString, ::AbstractString)</code> but more efficient when repeatedly parsing similarly formatted time strings with a pre-created <code>DateFormat</code> object.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/io.jl#L638-L647">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.now-Tuple{}" href="#Dates.now-Tuple{}"><code>Dates.now</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">now() -&gt; DateTime</code></pre><p>Return a <code>DateTime</code> corresponding to the user&#39;s system time including the system timezone locale.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/conversions.jl#L62-L67">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.now-Tuple{Type{UTC}}" href="#Dates.now-Tuple{Type{UTC}}"><code>Dates.now</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">now(::Type{UTC}) -&gt; DateTime</code></pre><p>Return a <code>DateTime</code> corresponding to the user&#39;s system time as UTC/GMT. For other time zones, see the TimeZones.jl package.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">julia&gt; now(UTC)
2023-01-04T10:52:24.864</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/conversions.jl#L81-L92">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.eps-Tuple{Union{Type{Date}, Type{DateTime}, Type{Time}, TimeType}}" href="#Base.eps-Tuple{Union{Type{Date}, Type{DateTime}, Type{Time}, TimeType}}"><code>Base.eps</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">eps(::Type{DateTime}) -&gt; Millisecond
eps(::Type{Date}) -&gt; Day
eps(::Type{Time}) -&gt; Nanosecond
eps(::TimeType) -&gt; Period</code></pre><p>Return the smallest unit value supported by the <code>TimeType</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; eps(DateTime)
1 millisecond

julia&gt; eps(Date)
1 day

julia&gt; eps(Time)
1 nanosecond</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/types.jl#L430-L449">source</a></section></article><h4 id="Accessor-Functions-2"><a class="docs-heading-anchor" href="#Accessor-Functions-2">Accessor Functions</a><a class="docs-heading-anchor-permalink" href="#Accessor-Functions-2" title="Permalink"></a></h4><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.year" href="#Dates.year"><code>Dates.year</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">year(dt::TimeType) -&gt; Int64</code></pre><p>The year of a <code>Date</code> or <code>DateTime</code> as an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/accessors.jl#L81-L85">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.month" href="#Dates.month"><code>Dates.month</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">month(dt::TimeType) -&gt; Int64</code></pre><p>The month of a <code>Date</code> or <code>DateTime</code> as an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/accessors.jl#L81-L85">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.week" href="#Dates.week"><code>Dates.week</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">week(dt::TimeType) -&gt; Int64</code></pre><p>Return the <a href="https://en.wikipedia.org/wiki/ISO_week_date">ISO week date</a> of a <code>Date</code> or <code>DateTime</code> as an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>. Note that the first week of a year is the week that contains the first Thursday of the year, which can result in dates prior to January 4th being in the last week of the previous year. For example, <code>week(Date(2005, 1, 1))</code> is the 53rd week of 2004.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; week(Date(1989, 6, 22))
25

julia&gt; week(Date(2005, 1, 1))
53

julia&gt; week(Date(2004, 12, 31))
53</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/accessors.jl#L89-L109">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.day" href="#Dates.day"><code>Dates.day</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">day(dt::TimeType) -&gt; Int64</code></pre><p>The day of month of a <code>Date</code> or <code>DateTime</code> as an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/accessors.jl#L115-L119">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.hour" href="#Dates.hour"><code>Dates.hour</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">hour(dt::DateTime) -&gt; Int64</code></pre><p>The hour of day of a <code>DateTime</code> as an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/accessors.jl#L123-L127">source</a></section><section><div><pre><code class="language-julia hljs">hour(t::Time) -&gt; Int64</code></pre><p>The hour of a <code>Time</code> as an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/accessors.jl#L157-L161">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.minute" href="#Dates.minute"><code>Dates.minute</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">minute(dt::DateTime) -&gt; Int64</code></pre><p>The minute of a <code>DateTime</code> as an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/accessors.jl#L133-L137">source</a></section><section><div><pre><code class="language-julia hljs">minute(t::Time) -&gt; Int64</code></pre><p>The minute of a <code>Time</code> as an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/accessors.jl#L157-L161">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.second" href="#Dates.second"><code>Dates.second</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">second(dt::DateTime) -&gt; Int64</code></pre><p>The second of a <code>DateTime</code> as an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/accessors.jl#L133-L137">source</a></section><section><div><pre><code class="language-julia hljs">second(t::Time) -&gt; Int64</code></pre><p>The second of a <code>Time</code> as an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/accessors.jl#L157-L161">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.millisecond" href="#Dates.millisecond"><code>Dates.millisecond</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">millisecond(dt::DateTime) -&gt; Int64</code></pre><p>The millisecond of a <code>DateTime</code> as an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/accessors.jl#L133-L137">source</a></section><section><div><pre><code class="language-julia hljs">millisecond(t::Time) -&gt; Int64</code></pre><p>The millisecond of a <code>Time</code> as an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/accessors.jl#L157-L161">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.microsecond" href="#Dates.microsecond"><code>Dates.microsecond</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">microsecond(t::Time) -&gt; Int64</code></pre><p>The microsecond of a <code>Time</code> as an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/accessors.jl#L157-L161">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.nanosecond" href="#Dates.nanosecond"><code>Dates.nanosecond</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">nanosecond(t::Time) -&gt; Int64</code></pre><p>The nanosecond of a <code>Time</code> as an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/accessors.jl#L157-L161">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Year-Tuple{TimeType}" href="#Dates.Year-Tuple{TimeType}"><code>Dates.Year</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Year(v)</code></pre><p>Construct a <code>Year</code> object with the given <code>v</code> value. Input must be losslessly convertible to an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/periods.jl#L38-L43">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Month-Tuple{TimeType}" href="#Dates.Month-Tuple{TimeType}"><code>Dates.Month</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Month(v)</code></pre><p>Construct a <code>Month</code> object with the given <code>v</code> value. Input must be losslessly convertible to an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/periods.jl#L38-L43">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Week-Tuple{TimeType}" href="#Dates.Week-Tuple{TimeType}"><code>Dates.Week</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Week(v)</code></pre><p>Construct a <code>Week</code> object with the given <code>v</code> value. Input must be losslessly convertible to an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/periods.jl#L38-L43">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Day-Tuple{TimeType}" href="#Dates.Day-Tuple{TimeType}"><code>Dates.Day</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Day(v)</code></pre><p>Construct a <code>Day</code> object with the given <code>v</code> value. Input must be losslessly convertible to an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/periods.jl#L38-L43">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Hour-Tuple{DateTime}" href="#Dates.Hour-Tuple{DateTime}"><code>Dates.Hour</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Hour(dt::DateTime) -&gt; Hour</code></pre><p>The hour part of a DateTime as a <code>Hour</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/periods.jl#L30-L34">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Minute-Tuple{DateTime}" href="#Dates.Minute-Tuple{DateTime}"><code>Dates.Minute</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Minute(dt::DateTime) -&gt; Minute</code></pre><p>The minute part of a DateTime as a <code>Minute</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/periods.jl#L30-L34">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Second-Tuple{DateTime}" href="#Dates.Second-Tuple{DateTime}"><code>Dates.Second</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Second(dt::DateTime) -&gt; Second</code></pre><p>The second part of a DateTime as a <code>Second</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/periods.jl#L30-L34">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Millisecond-Tuple{DateTime}" href="#Dates.Millisecond-Tuple{DateTime}"><code>Dates.Millisecond</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Millisecond(dt::DateTime) -&gt; Millisecond</code></pre><p>The millisecond part of a DateTime as a <code>Millisecond</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/periods.jl#L30-L34">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Microsecond-Tuple{Time}" href="#Dates.Microsecond-Tuple{Time}"><code>Dates.Microsecond</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Microsecond(dt::Time) -&gt; Microsecond</code></pre><p>The microsecond part of a Time as a <code>Microsecond</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/periods.jl#L30-L34">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Nanosecond-Tuple{Time}" href="#Dates.Nanosecond-Tuple{Time}"><code>Dates.Nanosecond</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Nanosecond(dt::Time) -&gt; Nanosecond</code></pre><p>The nanosecond part of a Time as a <code>Nanosecond</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/periods.jl#L30-L34">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.yearmonth" href="#Dates.yearmonth"><code>Dates.yearmonth</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">yearmonth(dt::TimeType) -&gt; (Int64, Int64)</code></pre><p>Simultaneously return the year and month parts of a <code>Date</code> or <code>DateTime</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/accessors.jl#L145-L150">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.monthday" href="#Dates.monthday"><code>Dates.monthday</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">monthday(dt::TimeType) -&gt; (Int64, Int64)</code></pre><p>Simultaneously return the month and day parts of a <code>Date</code> or <code>DateTime</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/accessors.jl#L145-L150">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.yearmonthday" href="#Dates.yearmonthday"><code>Dates.yearmonthday</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">yearmonthday(dt::TimeType) -&gt; (Int64, Int64, Int64)</code></pre><p>Simultaneously return the year, month and day parts of a <code>Date</code> or <code>DateTime</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/accessors.jl#L145-L150">source</a></section></article><h4 id="Query-Functions-2"><a class="docs-heading-anchor" href="#Query-Functions-2">Query Functions</a><a class="docs-heading-anchor-permalink" href="#Query-Functions-2" title="Permalink"></a></h4><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.dayname" href="#Dates.dayname"><code>Dates.dayname</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">dayname(dt::TimeType; locale=&quot;english&quot;) -&gt; String
dayname(day::Integer; locale=&quot;english&quot;) -&gt; String</code></pre><p>Return the full day name corresponding to the day of the week of the <code>Date</code> or <code>DateTime</code> in the given <code>locale</code>. Also accepts <code>Integer</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; dayname(Date(&quot;2000-01-01&quot;))
&quot;Saturday&quot;

julia&gt; dayname(4)
&quot;Thursday&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/query.jl#L153-L168">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.dayabbr" href="#Dates.dayabbr"><code>Dates.dayabbr</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">dayabbr(dt::TimeType; locale=&quot;english&quot;) -&gt; String
dayabbr(day::Integer; locale=&quot;english&quot;) -&gt; String</code></pre><p>Return the abbreviated name corresponding to the day of the week of the <code>Date</code> or <code>DateTime</code> in the given <code>locale</code>. Also accepts <code>Integer</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; dayabbr(Date(&quot;2000-01-01&quot;))
&quot;Sat&quot;

julia&gt; dayabbr(3)
&quot;Wed&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/query.jl#L173-L188">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.dayofweek" href="#Dates.dayofweek"><code>Dates.dayofweek</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">dayofweek(dt::TimeType) -&gt; Int64</code></pre><p>Return the day of the week as an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a> with <code>1 = Monday, 2 = Tuesday, etc.</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; dayofweek(Date(&quot;2000-01-01&quot;))
6</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/query.jl#L110-L120">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.dayofmonth" href="#Dates.dayofmonth"><code>Dates.dayofmonth</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">dayofmonth(dt::TimeType) -&gt; Int64</code></pre><p>The day of month of a <code>Date</code> or <code>DateTime</code> as an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/accessors.jl#L115-L119">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.dayofweekofmonth" href="#Dates.dayofweekofmonth"><code>Dates.dayofweekofmonth</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">dayofweekofmonth(dt::TimeType) -&gt; Int</code></pre><p>For the day of week of <code>dt</code>, return which number it is in <code>dt</code>&#39;s month. So if the day of the week of <code>dt</code> is Monday, then <code>1 = First Monday of the month, 2 = Second Monday of the month, etc.</code> In the range 1:5.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; dayofweekofmonth(Date(&quot;2000-02-01&quot;))
1

julia&gt; dayofweekofmonth(Date(&quot;2000-02-08&quot;))
2

julia&gt; dayofweekofmonth(Date(&quot;2000-02-15&quot;))
3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/query.jl#L203-L221">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.daysofweekinmonth" href="#Dates.daysofweekinmonth"><code>Dates.daysofweekinmonth</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">daysofweekinmonth(dt::TimeType) -&gt; Int</code></pre><p>For the day of week of <code>dt</code>, return the total number of that day of the week in <code>dt</code>&#39;s month. Returns 4 or 5. Useful in temporal expressions for specifying the last day of a week in a month by including <code>dayofweekofmonth(dt) == daysofweekinmonth(dt)</code> in the adjuster function.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; daysofweekinmonth(Date(&quot;2005-01-01&quot;))
5

julia&gt; daysofweekinmonth(Date(&quot;2005-01-04&quot;))
4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/query.jl#L233-L249">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.monthname" href="#Dates.monthname"><code>Dates.monthname</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">monthname(dt::TimeType; locale=&quot;english&quot;) -&gt; String
monthname(month::Integer, locale=&quot;english&quot;) -&gt; String</code></pre><p>Return the full name of the month of the <code>Date</code> or <code>DateTime</code> or <code>Integer</code> in the given <code>locale</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; monthname(Date(&quot;2005-01-04&quot;))
&quot;January&quot;

julia&gt; monthname(2)
&quot;February&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/query.jl#L563-L578">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.monthabbr" href="#Dates.monthabbr"><code>Dates.monthabbr</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">monthabbr(dt::TimeType; locale=&quot;english&quot;) -&gt; String
monthabbr(month::Integer, locale=&quot;english&quot;) -&gt; String</code></pre><p>Return the abbreviated month name of the <code>Date</code> or <code>DateTime</code> or <code>Integer</code> in the given <code>locale</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; monthabbr(Date(&quot;2005-01-04&quot;))
&quot;Jan&quot;

julia&gt; monthabbr(2)
&quot;Feb&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/query.jl#L583-L597">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.daysinmonth" href="#Dates.daysinmonth"><code>Dates.daysinmonth</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">daysinmonth(dt::TimeType) -&gt; Int</code></pre><p>Return the number of days in the month of <code>dt</code>. Value will be 28, 29, 30, or 31.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; daysinmonth(Date(&quot;2000-01&quot;))
31

julia&gt; daysinmonth(Date(&quot;2001-02&quot;))
28

julia&gt; daysinmonth(Date(&quot;2000-02&quot;))
29</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/query.jl#L602-L618">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.isleapyear" href="#Dates.isleapyear"><code>Dates.isleapyear</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isleapyear(dt::TimeType) -&gt; Bool</code></pre><p>Return <code>true</code> if the year of <code>dt</code> is a leap year.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; isleapyear(Date(&quot;2004&quot;))
true

julia&gt; isleapyear(Date(&quot;2005&quot;))
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/query.jl#L622-L635">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.dayofyear" href="#Dates.dayofyear"><code>Dates.dayofyear</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">dayofyear(dt::TimeType) -&gt; Int</code></pre><p>Return the day of the year for <code>dt</code> with January 1st being day 1.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/query.jl#L638-L642">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.daysinyear" href="#Dates.daysinyear"><code>Dates.daysinyear</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">daysinyear(dt::TimeType) -&gt; Int</code></pre><p>Return 366 if the year of <code>dt</code> is a leap year, otherwise return 365.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; daysinyear(1999)
365

julia&gt; daysinyear(2000)
366</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/query.jl#L89-L102">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.quarterofyear" href="#Dates.quarterofyear"><code>Dates.quarterofyear</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">quarterofyear(dt::TimeType) -&gt; Int</code></pre><p>Return the quarter that <code>dt</code> resides in. Range of value is 1:4.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/query.jl#L648-L652">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.dayofquarter" href="#Dates.dayofquarter"><code>Dates.dayofquarter</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">dayofquarter(dt::TimeType) -&gt; Int</code></pre><p>Return the day of the current quarter of <code>dt</code>. Range of value is 1:92.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/query.jl#L657-L661">source</a></section></article><h4 id="Adjuster-Functions-2"><a class="docs-heading-anchor" href="#Adjuster-Functions-2">Adjuster Functions</a><a class="docs-heading-anchor-permalink" href="#Adjuster-Functions-2" title="Permalink"></a></h4><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.trunc-Tuple{TimeType, Type{Period}}" href="#Base.trunc-Tuple{TimeType, Type{Period}}"><code>Base.trunc</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">trunc(dt::TimeType, ::Type{Period}) -&gt; TimeType</code></pre><p>Truncates the value of <code>dt</code> according to the provided <code>Period</code> type.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; trunc(DateTime(&quot;1996-01-01T12:30:00&quot;), Day)
1996-01-01T00:00:00</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/adjusters.jl#L25-L35">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.firstdayofweek" href="#Dates.firstdayofweek"><code>Dates.firstdayofweek</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">firstdayofweek(dt::TimeType) -&gt; TimeType</code></pre><p>Adjusts <code>dt</code> to the Monday of its week.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; firstdayofweek(DateTime(&quot;1996-01-05T12:30:00&quot;))
1996-01-01T00:00:00</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/adjusters.jl#L39-L49">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.lastdayofweek" href="#Dates.lastdayofweek"><code>Dates.lastdayofweek</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lastdayofweek(dt::TimeType) -&gt; TimeType</code></pre><p>Adjusts <code>dt</code> to the Sunday of its week.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; lastdayofweek(DateTime(&quot;1996-01-05T12:30:00&quot;))
1996-01-07T00:00:00</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/adjusters.jl#L55-L65">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.firstdayofmonth" href="#Dates.firstdayofmonth"><code>Dates.firstdayofmonth</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">firstdayofmonth(dt::TimeType) -&gt; TimeType</code></pre><p>Adjusts <code>dt</code> to the first day of its month.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; firstdayofmonth(DateTime(&quot;1996-05-20&quot;))
1996-05-01T00:00:00</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/adjusters.jl#L71-L81">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.lastdayofmonth" href="#Dates.lastdayofmonth"><code>Dates.lastdayofmonth</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lastdayofmonth(dt::TimeType) -&gt; TimeType</code></pre><p>Adjusts <code>dt</code> to the last day of its month.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; lastdayofmonth(DateTime(&quot;1996-05-20&quot;))
1996-05-31T00:00:00</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/adjusters.jl#L87-L97">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.firstdayofyear" href="#Dates.firstdayofyear"><code>Dates.firstdayofyear</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">firstdayofyear(dt::TimeType) -&gt; TimeType</code></pre><p>Adjusts <code>dt</code> to the first day of its year.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; firstdayofyear(DateTime(&quot;1996-05-20&quot;))
1996-01-01T00:00:00</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/adjusters.jl#L106-L116">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.lastdayofyear" href="#Dates.lastdayofyear"><code>Dates.lastdayofyear</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lastdayofyear(dt::TimeType) -&gt; TimeType</code></pre><p>Adjusts <code>dt</code> to the last day of its year.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; lastdayofyear(DateTime(&quot;1996-05-20&quot;))
1996-12-31T00:00:00</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/adjusters.jl#L122-L132">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.firstdayofquarter" href="#Dates.firstdayofquarter"><code>Dates.firstdayofquarter</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">firstdayofquarter(dt::TimeType) -&gt; TimeType</code></pre><p>Adjusts <code>dt</code> to the first day of its quarter.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; firstdayofquarter(DateTime(&quot;1996-05-20&quot;))
1996-04-01T00:00:00

julia&gt; firstdayofquarter(DateTime(&quot;1996-08-20&quot;))
1996-07-01T00:00:00</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/adjusters.jl#L141-L154">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.lastdayofquarter" href="#Dates.lastdayofquarter"><code>Dates.lastdayofquarter</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lastdayofquarter(dt::TimeType) -&gt; TimeType</code></pre><p>Adjusts <code>dt</code> to the last day of its quarter.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; lastdayofquarter(DateTime(&quot;1996-05-20&quot;))
1996-06-30T00:00:00

julia&gt; lastdayofquarter(DateTime(&quot;1996-08-20&quot;))
1996-09-30T00:00:00</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/adjusters.jl#L164-L177">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.tonext-Tuple{TimeType, Int64}" href="#Dates.tonext-Tuple{TimeType, Int64}"><code>Dates.tonext</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">tonext(dt::TimeType, dow::Int; same::Bool=false) -&gt; TimeType</code></pre><p>Adjusts <code>dt</code> to the next day of week corresponding to <code>dow</code> with <code>1 = Monday, 2 = Tuesday, etc</code>. Setting <code>same=true</code> allows the current <code>dt</code> to be considered as the next <code>dow</code>, allowing for no adjustment to occur.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/adjusters.jl#L366-L372">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.toprev-Tuple{TimeType, Int64}" href="#Dates.toprev-Tuple{TimeType, Int64}"><code>Dates.toprev</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">toprev(dt::TimeType, dow::Int; same::Bool=false) -&gt; TimeType</code></pre><p>Adjusts <code>dt</code> to the previous day of week corresponding to <code>dow</code> with <code>1 = Monday, 2 = Tuesday, etc</code>. Setting <code>same=true</code> allows the current <code>dt</code> to be considered as the previous <code>dow</code>, allowing for no adjustment to occur.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/adjusters.jl#L387-L393">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.tofirst" href="#Dates.tofirst"><code>Dates.tofirst</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">tofirst(dt::TimeType, dow::Int; of=Month) -&gt; TimeType</code></pre><p>Adjusts <code>dt</code> to the first <code>dow</code> of its month. Alternatively, <code>of=Year</code> will adjust to the first <code>dow</code> of the year.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/adjusters.jl#L408-L413">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.tolast" href="#Dates.tolast"><code>Dates.tolast</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">tolast(dt::TimeType, dow::Int; of=Month) -&gt; TimeType</code></pre><p>Adjusts <code>dt</code> to the last <code>dow</code> of its month. Alternatively, <code>of=Year</code> will adjust to the last <code>dow</code> of the year.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/adjusters.jl#L420-L425">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.tonext-Tuple{Function, TimeType}" href="#Dates.tonext-Tuple{Function, TimeType}"><code>Dates.tonext</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">tonext(func::Function, dt::TimeType; step=Day(1), limit=10000, same=false) -&gt; TimeType</code></pre><p>Adjusts <code>dt</code> by iterating at most <code>limit</code> iterations by <code>step</code> increments until <code>func</code> returns <code>true</code>. <code>func</code> must take a single <code>TimeType</code> argument and return a <a href="../base/numbers.html#Core.Bool"><code>Bool</code></a>. <code>same</code> allows <code>dt</code> to be considered in satisfying <code>func</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/adjusters.jl#L376-L382">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.toprev-Tuple{Function, TimeType}" href="#Dates.toprev-Tuple{Function, TimeType}"><code>Dates.toprev</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">toprev(func::Function, dt::TimeType; step=Day(-1), limit=10000, same=false) -&gt; TimeType</code></pre><p>Adjusts <code>dt</code> by iterating at most <code>limit</code> iterations by <code>step</code> increments until <code>func</code> returns <code>true</code>. <code>func</code> must take a single <code>TimeType</code> argument and return a <a href="../base/numbers.html#Core.Bool"><code>Bool</code></a>. <code>same</code> allows <code>dt</code> to be considered in satisfying <code>func</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/adjusters.jl#L396-L402">source</a></section></article><h4 id="Periods"><a class="docs-heading-anchor" href="#Periods">Periods</a><a id="Periods-1"></a><a class="docs-heading-anchor-permalink" href="#Periods" title="Permalink"></a></h4><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.Period-Tuple{Any}" href="#Dates.Period-Tuple{Any}"><code>Dates.Period</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Year(v)
Quarter(v)
Month(v)
Week(v)
Day(v)
Hour(v)
Minute(v)
Second(v)
Millisecond(v)
Microsecond(v)
Nanosecond(v)</code></pre><p>Construct a <code>Period</code> type with the given <code>v</code> value. Input must be losslessly convertible to an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/types.jl#L65-L80">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.CompoundPeriod-Tuple{Vector{&lt;:Period}}" href="#Dates.CompoundPeriod-Tuple{Vector{&lt;:Period}}"><code>Dates.CompoundPeriod</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">CompoundPeriod(periods) -&gt; CompoundPeriod</code></pre><p>Construct a <code>CompoundPeriod</code> from a <code>Vector</code> of <code>Period</code>s. All <code>Period</code>s of the same type will be added together.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Dates.CompoundPeriod(Dates.Hour(12), Dates.Hour(13))
25 hours

julia&gt; Dates.CompoundPeriod(Dates.Hour(-1), Dates.Minute(1))
-1 hour, 1 minute

julia&gt; Dates.CompoundPeriod(Dates.Month(1), Dates.Week(-2))
1 month, -2 weeks

julia&gt; Dates.CompoundPeriod(Dates.Minute(50000))
50000 minutes</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/periods.jl#L177-L197">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.canonicalize" href="#Dates.canonicalize"><code>Dates.canonicalize</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">canonicalize(::CompoundPeriod) -&gt; CompoundPeriod</code></pre><p>Reduces the <code>CompoundPeriod</code> into its canonical form by applying the following rules:</p><ul><li>Any <code>Period</code> large enough be partially representable by a coarser <code>Period</code> will be broken into multiple <code>Period</code>s (eg. <code>Hour(30)</code> becomes <code>Day(1) + Hour(6)</code>)</li><li><code>Period</code>s with opposite signs will be combined when possible (eg. <code>Hour(1) - Day(1)</code> becomes <code>-Hour(23)</code>)</li></ul><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; canonicalize(Dates.CompoundPeriod(Dates.Hour(12), Dates.Hour(13)))
1 day, 1 hour

julia&gt; canonicalize(Dates.CompoundPeriod(Dates.Hour(-1), Dates.Minute(1)))
-59 minutes

julia&gt; canonicalize(Dates.CompoundPeriod(Dates.Month(1), Dates.Week(-2)))
1 month, -2 weeks

julia&gt; canonicalize(Dates.CompoundPeriod(Dates.Minute(50000)))
4 weeks, 6 days, 17 hours, 20 minutes</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/periods.jl#L206-L230">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.value" href="#Dates.value"><code>Dates.value</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Dates.value(x::Period) -&gt; Int64</code></pre><p>For a given period, return the value associated with that period.  For example, <code>value(Millisecond(10))</code> returns 10 as an integer.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/periods.jl#L4-L9">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.default" href="#Dates.default"><code>Dates.default</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">default(p::Period) -&gt; Period</code></pre><p>Return a sensible &quot;default&quot; value for the input Period by returning <code>T(1)</code> for Year, Month, and Day, and <code>T(0)</code> for Hour, Minute, Second, and Millisecond.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/periods.jl#L58-L63">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.periods" href="#Dates.periods"><code>Dates.periods</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Dates.periods(::CompoundPeriod) -&gt; Vector{Period}</code></pre><p>Return the <code>Vector</code> of <code>Period</code>s that comprise the given <code>CompoundPeriod</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>This function requires Julia 1.7 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/periods.jl#L167-L174">source</a></section></article><h4 id="Rounding-Functions"><a class="docs-heading-anchor" href="#Rounding-Functions">Rounding Functions</a><a id="Rounding-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Rounding-Functions" title="Permalink"></a></h4><p><code>Date</code> and <code>DateTime</code> values can be rounded to a specified resolution (e.g., 1 month or 15 minutes) with <code>floor</code>, <code>ceil</code>, or <code>round</code>.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.floor-Tuple{TimeType, Period}" href="#Base.floor-Tuple{TimeType, Period}"><code>Base.floor</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">floor(dt::TimeType, p::Period) -&gt; TimeType</code></pre><p>Return the nearest <code>Date</code> or <code>DateTime</code> less than or equal to <code>dt</code> at resolution <code>p</code>.</p><p>For convenience, <code>p</code> may be a type instead of a value: <code>floor(dt, Dates.Hour)</code> is a shortcut for <code>floor(dt, Dates.Hour(1))</code>.</p><pre><code class="language-julia-repl hljs">julia&gt; floor(Date(1985, 8, 16), Month)
1985-08-01

julia&gt; floor(DateTime(2013, 2, 13, 0, 31, 20), Minute(15))
2013-02-13T00:30:00

julia&gt; floor(DateTime(2016, 8, 6, 12, 0, 0), Day)
2016-08-06T00:00:00</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/rounding.jl#L116-L134">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.ceil-Tuple{TimeType, Period}" href="#Base.ceil-Tuple{TimeType, Period}"><code>Base.ceil</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ceil(dt::TimeType, p::Period) -&gt; TimeType</code></pre><p>Return the nearest <code>Date</code> or <code>DateTime</code> greater than or equal to <code>dt</code> at resolution <code>p</code>.</p><p>For convenience, <code>p</code> may be a type instead of a value: <code>ceil(dt, Dates.Hour)</code> is a shortcut for <code>ceil(dt, Dates.Hour(1))</code>.</p><pre><code class="language-julia-repl hljs">julia&gt; ceil(Date(1985, 8, 16), Month)
1985-09-01

julia&gt; ceil(DateTime(2013, 2, 13, 0, 31, 20), Minute(15))
2013-02-13T00:45:00

julia&gt; ceil(DateTime(2016, 8, 6, 12, 0, 0), Day)
2016-08-07T00:00:00</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/rounding.jl#L137-L155">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.round-Tuple{TimeType, Period, RoundingMode{:NearestTiesUp}}" href="#Base.round-Tuple{TimeType, Period, RoundingMode{:NearestTiesUp}}"><code>Base.round</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">round(dt::TimeType, p::Period, [r::RoundingMode]) -&gt; TimeType</code></pre><p>Return the <code>Date</code> or <code>DateTime</code> nearest to <code>dt</code> at resolution <code>p</code>. By default (<code>RoundNearestTiesUp</code>), ties (e.g., rounding 9:30 to the nearest hour) will be rounded up.</p><p>For convenience, <code>p</code> may be a type instead of a value: <code>round(dt, Dates.Hour)</code> is a shortcut for <code>round(dt, Dates.Hour(1))</code>.</p><pre><code class="language-julia-repl hljs">julia&gt; round(Date(1985, 8, 16), Month)
1985-08-01

julia&gt; round(DateTime(2013, 2, 13, 0, 31, 20), Minute(15))
2013-02-13T00:30:00

julia&gt; round(DateTime(2016, 8, 6, 12, 0, 0), Day)
2016-08-07T00:00:00</code></pre><p>Valid rounding modes for <code>round(::TimeType, ::Period, ::RoundingMode)</code> are <code>RoundNearestTiesUp</code> (default), <code>RoundDown</code> (<code>floor</code>), and <code>RoundUp</code> (<code>ceil</code>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/rounding.jl#L211-L233">source</a></section></article><p>Most <code>Period</code> values can also be rounded to a specified resolution:</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.floor-Union{Tuple{T}, Tuple{Union{Day, Week, TimePeriod}, T}} where T&lt;:Union{Day, Week, TimePeriod}" href="#Base.floor-Union{Tuple{T}, Tuple{Union{Day, Week, TimePeriod}, T}} where T&lt;:Union{Day, Week, TimePeriod}"><code>Base.floor</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">floor(x::Period, precision::T) where T &lt;: Union{TimePeriod, Week, Day} -&gt; T</code></pre><p>Round <code>x</code> down to the nearest multiple of <code>precision</code>. If <code>x</code> and <code>precision</code> are different subtypes of <code>Period</code>, the return value will have the same type as <code>precision</code>.</p><p>For convenience, <code>precision</code> may be a type instead of a value: <code>floor(x, Dates.Hour)</code> is a shortcut for <code>floor(x, Dates.Hour(1))</code>.</p><pre><code class="language-julia-repl hljs">julia&gt; floor(Day(16), Week)
2 weeks

julia&gt; floor(Minute(44), Minute(15))
30 minutes

julia&gt; floor(Hour(36), Day)
1 day</code></pre><p>Rounding to a <code>precision</code> of <code>Month</code>s or <code>Year</code>s is not supported, as these <code>Period</code>s are of inconsistent length.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/rounding.jl#L87-L109">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.ceil-Tuple{Union{Day, Week, TimePeriod}, Union{Day, Week, TimePeriod}}" href="#Base.ceil-Tuple{Union{Day, Week, TimePeriod}, Union{Day, Week, TimePeriod}}"><code>Base.ceil</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ceil(x::Period, precision::T) where T &lt;: Union{TimePeriod, Week, Day} -&gt; T</code></pre><p>Round <code>x</code> up to the nearest multiple of <code>precision</code>. If <code>x</code> and <code>precision</code> are different subtypes of <code>Period</code>, the return value will have the same type as <code>precision</code>.</p><p>For convenience, <code>precision</code> may be a type instead of a value: <code>ceil(x, Dates.Hour)</code> is a shortcut for <code>ceil(x, Dates.Hour(1))</code>.</p><pre><code class="language-julia-repl hljs">julia&gt; ceil(Day(16), Week)
3 weeks

julia&gt; ceil(Minute(44), Minute(15))
45 minutes

julia&gt; ceil(Hour(36), Day)
2 days</code></pre><p>Rounding to a <code>precision</code> of <code>Month</code>s or <code>Year</code>s is not supported, as these <code>Period</code>s are of inconsistent length.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/rounding.jl#L161-L183">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.round-Tuple{Union{Day, Week, TimePeriod}, Union{Day, Week, TimePeriod}, RoundingMode{:NearestTiesUp}}" href="#Base.round-Tuple{Union{Day, Week, TimePeriod}, Union{Day, Week, TimePeriod}, RoundingMode{:NearestTiesUp}}"><code>Base.round</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">round(x::Period, precision::T, [r::RoundingMode]) where T &lt;: Union{TimePeriod, Week, Day} -&gt; T</code></pre><p>Round <code>x</code> to the nearest multiple of <code>precision</code>. If <code>x</code> and <code>precision</code> are different subtypes of <code>Period</code>, the return value will have the same type as <code>precision</code>. By default (<code>RoundNearestTiesUp</code>), ties (e.g., rounding 90 minutes to the nearest hour) will be rounded up.</p><p>For convenience, <code>precision</code> may be a type instead of a value: <code>round(x, Dates.Hour)</code> is a shortcut for <code>round(x, Dates.Hour(1))</code>.</p><pre><code class="language-julia-repl hljs">julia&gt; round(Day(16), Week)
2 weeks

julia&gt; round(Minute(44), Minute(15))
45 minutes

julia&gt; round(Hour(36), Day)
2 days</code></pre><p>Valid rounding modes for <code>round(::Period, ::T, ::RoundingMode)</code> are <code>RoundNearestTiesUp</code> (default), <code>RoundDown</code> (<code>floor</code>), and <code>RoundUp</code> (<code>ceil</code>).</p><p>Rounding to a <code>precision</code> of <code>Month</code>s or <code>Year</code>s is not supported, as these <code>Period</code>s are of inconsistent length.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/rounding.jl#L239-L266">source</a></section></article><p>The following functions are not exported:</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.floorceil" href="#Dates.floorceil"><code>Dates.floorceil</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">floorceil(dt::TimeType, p::Period) -&gt; (TimeType, TimeType)</code></pre><p>Simultaneously return the <code>floor</code> and <code>ceil</code> of a <code>Date</code> or <code>DateTime</code> at resolution <code>p</code>. More efficient than calling both <code>floor</code> and <code>ceil</code> individually.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/rounding.jl#L189-L194">source</a></section><section><div><pre><code class="language-julia hljs">floorceil(x::Period, precision::T) where T &lt;: Union{TimePeriod, Week, Day} -&gt; (T, T)</code></pre><p>Simultaneously return the <code>floor</code> and <code>ceil</code> of <code>Period</code> at resolution <code>p</code>.  More efficient than calling both <code>floor</code> and <code>ceil</code> individually.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/rounding.jl#L200-L205">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.epochdays2date" href="#Dates.epochdays2date"><code>Dates.epochdays2date</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">epochdays2date(days) -&gt; Date</code></pre><p>Take the number of days since the rounding epoch (<code>0000-01-01T00:00:00</code>) and return the corresponding <code>Date</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/rounding.jl#L13-L18">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.epochms2datetime" href="#Dates.epochms2datetime"><code>Dates.epochms2datetime</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">epochms2datetime(milliseconds) -&gt; DateTime</code></pre><p>Take the number of milliseconds since the rounding epoch (<code>0000-01-01T00:00:00</code>) and return the corresponding <code>DateTime</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/rounding.jl#L21-L26">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.date2epochdays" href="#Dates.date2epochdays"><code>Dates.date2epochdays</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">date2epochdays(dt::Date) -&gt; Int64</code></pre><p>Take the given <code>Date</code> and return the number of days since the rounding epoch (<code>0000-01-01T00:00:00</code>) as an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/rounding.jl#L29-L34">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.datetime2epochms" href="#Dates.datetime2epochms"><code>Dates.datetime2epochms</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">datetime2epochms(dt::DateTime) -&gt; Int64</code></pre><p>Take the given <code>DateTime</code> and return the number of milliseconds since the rounding epoch (<code>0000-01-01T00:00:00</code>) as an <a href="../base/numbers.html#Core.Int64"><code>Int64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/rounding.jl#L37-L42">source</a></section></article><h4 id="Conversion-Functions"><a class="docs-heading-anchor" href="#Conversion-Functions">Conversion Functions</a><a id="Conversion-Functions-1"></a><a class="docs-heading-anchor-permalink" href="#Conversion-Functions" title="Permalink"></a></h4><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.today" href="#Dates.today"><code>Dates.today</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">today() -&gt; Date</code></pre><p>Return the date portion of <code>now()</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/conversions.jl#L74-L78">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.unix2datetime" href="#Dates.unix2datetime"><code>Dates.unix2datetime</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unix2datetime(x) -&gt; DateTime</code></pre><p>Take the number of seconds since unix epoch <code>1970-01-01T00:00:00</code> and convert to the corresponding <code>DateTime</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/conversions.jl#L42-L47">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.datetime2unix" href="#Dates.datetime2unix"><code>Dates.datetime2unix</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">datetime2unix(dt::DateTime) -&gt; Float64</code></pre><p>Take the given <code>DateTime</code> and return the number of seconds since the unix epoch <code>1970-01-01T00:00:00</code> as a <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/conversions.jl#L54-L59">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.julian2datetime" href="#Dates.julian2datetime"><code>Dates.julian2datetime</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">julian2datetime(julian_days) -&gt; DateTime</code></pre><p>Take the number of Julian calendar days since epoch <code>-4713-11-24T12:00:00</code> and return the corresponding <code>DateTime</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/conversions.jl#L113-L118">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.datetime2julian" href="#Dates.datetime2julian"><code>Dates.datetime2julian</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">datetime2julian(dt::DateTime) -&gt; Float64</code></pre><p>Take the given <code>DateTime</code> and return the number of Julian calendar days since the julian epoch <code>-4713-11-24T12:00:00</code> as a <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/conversions.jl#L124-L129">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.rata2datetime" href="#Dates.rata2datetime"><code>Dates.rata2datetime</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">rata2datetime(days) -&gt; DateTime</code></pre><p>Take the number of Rata Die days since epoch <code>0000-12-31T00:00:00</code> and return the corresponding <code>DateTime</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/conversions.jl#L95-L100">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.datetime2rata" href="#Dates.datetime2rata"><code>Dates.datetime2rata</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">datetime2rata(dt::TimeType) -&gt; Int64</code></pre><p>Return the number of Rata Die days since epoch from the given <code>Date</code> or <code>DateTime</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/conversions.jl#L103-L107">source</a></section></article><h3 id="Constants"><a class="docs-heading-anchor" href="#Constants">Constants</a><a id="Constants-1"></a><a class="docs-heading-anchor-permalink" href="#Constants" title="Permalink"></a></h3><p>Days of the Week:</p><table><tr><th style="text-align: left">Variable</th><th style="text-align: left">Abbr.</th><th style="text-align: left">Value (Int)</th></tr><tr><td style="text-align: left"><code>Monday</code></td><td style="text-align: left"><code>Mon</code></td><td style="text-align: left">1</td></tr><tr><td style="text-align: left"><code>Tuesday</code></td><td style="text-align: left"><code>Tue</code></td><td style="text-align: left">2</td></tr><tr><td style="text-align: left"><code>Wednesday</code></td><td style="text-align: left"><code>Wed</code></td><td style="text-align: left">3</td></tr><tr><td style="text-align: left"><code>Thursday</code></td><td style="text-align: left"><code>Thu</code></td><td style="text-align: left">4</td></tr><tr><td style="text-align: left"><code>Friday</code></td><td style="text-align: left"><code>Fri</code></td><td style="text-align: left">5</td></tr><tr><td style="text-align: left"><code>Saturday</code></td><td style="text-align: left"><code>Sat</code></td><td style="text-align: left">6</td></tr><tr><td style="text-align: left"><code>Sunday</code></td><td style="text-align: left"><code>Sun</code></td><td style="text-align: left">7</td></tr></table><p>Months of the Year:</p><table><tr><th style="text-align: left">Variable</th><th style="text-align: left">Abbr.</th><th style="text-align: left">Value (Int)</th></tr><tr><td style="text-align: left"><code>January</code></td><td style="text-align: left"><code>Jan</code></td><td style="text-align: left">1</td></tr><tr><td style="text-align: left"><code>February</code></td><td style="text-align: left"><code>Feb</code></td><td style="text-align: left">2</td></tr><tr><td style="text-align: left"><code>March</code></td><td style="text-align: left"><code>Mar</code></td><td style="text-align: left">3</td></tr><tr><td style="text-align: left"><code>April</code></td><td style="text-align: left"><code>Apr</code></td><td style="text-align: left">4</td></tr><tr><td style="text-align: left"><code>May</code></td><td style="text-align: left"><code>May</code></td><td style="text-align: left">5</td></tr><tr><td style="text-align: left"><code>June</code></td><td style="text-align: left"><code>Jun</code></td><td style="text-align: left">6</td></tr><tr><td style="text-align: left"><code>July</code></td><td style="text-align: left"><code>Jul</code></td><td style="text-align: left">7</td></tr><tr><td style="text-align: left"><code>August</code></td><td style="text-align: left"><code>Aug</code></td><td style="text-align: left">8</td></tr><tr><td style="text-align: left"><code>September</code></td><td style="text-align: left"><code>Sep</code></td><td style="text-align: left">9</td></tr><tr><td style="text-align: left"><code>October</code></td><td style="text-align: left"><code>Oct</code></td><td style="text-align: left">10</td></tr><tr><td style="text-align: left"><code>November</code></td><td style="text-align: left"><code>Nov</code></td><td style="text-align: left">11</td></tr><tr><td style="text-align: left"><code>December</code></td><td style="text-align: left"><code>Dec</code></td><td style="text-align: left">12</td></tr></table><h4 id="Common-Date-Formatters"><a class="docs-heading-anchor" href="#Common-Date-Formatters">Common Date Formatters</a><a id="Common-Date-Formatters-1"></a><a class="docs-heading-anchor-permalink" href="#Common-Date-Formatters" title="Permalink"></a></h4><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.ISODateTimeFormat" href="#Dates.ISODateTimeFormat"><code>Dates.ISODateTimeFormat</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Dates.ISODateTimeFormat</code></pre><p>Describes the ISO8601 formatting for a date and time. This is the default value for <code>Dates.format</code> of a <code>DateTime</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Dates.format(DateTime(2018, 8, 8, 12, 0, 43, 1), ISODateTimeFormat)
&quot;2018-08-08T12:00:43.001&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/io.jl#L469-L480">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.ISODateFormat" href="#Dates.ISODateFormat"><code>Dates.ISODateFormat</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Dates.ISODateFormat</code></pre><p>Describes the ISO8601 formatting for a date. This is the default value for <code>Dates.format</code> of a <code>Date</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Dates.format(Date(2018, 8, 8), ISODateFormat)
&quot;2018-08-08&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/io.jl#L484-L494">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.ISOTimeFormat" href="#Dates.ISOTimeFormat"><code>Dates.ISOTimeFormat</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Dates.ISOTimeFormat</code></pre><p>Describes the ISO8601 formatting for a time. This is the default value for <code>Dates.format</code> of a <code>Time</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Dates.format(Time(12, 0, 43, 1), ISOTimeFormat)
&quot;12:00:43.001&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/io.jl#L498-L508">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Dates.RFC1123Format" href="#Dates.RFC1123Format"><code>Dates.RFC1123Format</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Dates.RFC1123Format</code></pre><p>Describes the RFC1123 formatting for a date and time.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Dates.format(DateTime(2018, 8, 8, 12, 0, 43, 1), RFC1123Format)
&quot;Wed, 08 Aug 2018 12:00:43&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Dates/src/io.jl#L512-L522">source</a></section></article><section class="footnotes is-size-7"><ul><li class="footnote" id="footnote-1"><a class="tag is-link" href="#citeref-1">1</a>The notion of the UT second is actually quite fundamental. There are basically two different notions of time generally accepted, one based on the physical rotation of the earth (one full rotation = 1 day), the other based on the SI second (a fixed, constant value). These are radically different! Think about it, a &quot;UT second&quot;, as defined relative to the rotation of the earth, may have a different absolute length depending on the day! Anyway, the fact that <a href="Dates.html#Dates.Date"><code>Date</code></a> and <a href="Dates.html#Dates.DateTime"><code>DateTime</code></a> are based on UT seconds is a simplifying, yet honest assumption so that things like leap seconds and all their complexity can be avoided. This basis of time is formally called <a href="https://en.wikipedia.org/wiki/Universal_Time">UT</a> or UT1. Basing types on the UT second basically means that every minute has 60 seconds and every day has 24 hours and leads to more natural calculations when working with calendar dates.</li></ul></section></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="CRC32c.html">« CRC32c</a><a class="docs-footer-nextpage" href="DelimitedFiles.html">Delimited Files »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
