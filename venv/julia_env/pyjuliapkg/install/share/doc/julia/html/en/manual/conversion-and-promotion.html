<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Conversion and Promotion · The Julia Language</title><meta name="title" content="Conversion and Promotion · The Julia Language"/><meta property="og:title" content="Conversion and Promotion · The Julia Language"/><meta property="twitter:title" content="Conversion and Promotion · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li class="is-active"><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a><ul class="internal"><li><a class="tocitem" href="#Conversion"><span>Conversion</span></a></li><li><a class="tocitem" href="#Promotion"><span>Promotion</span></a></li></ul></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="conversion-and-promotion.html">Conversion and Promotion</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="conversion-and-promotion.html">Conversion and Promotion</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/conversion-and-promotion.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="conversion-and-promotion"><a class="docs-heading-anchor" href="#conversion-and-promotion">Conversion and Promotion</a><a id="conversion-and-promotion-1"></a><a class="docs-heading-anchor-permalink" href="#conversion-and-promotion" title="Permalink"></a></h1><p>Julia has a system for promoting arguments of mathematical operators to a common type, which has been mentioned in various other sections, including <a href="integers-and-floating-point-numbers.html#Integers-and-Floating-Point-Numbers">Integers and Floating-Point Numbers</a>, <a href="mathematical-operations.html#Mathematical-Operations-and-Elementary-Functions">Mathematical Operations and Elementary Functions</a>, <a href="types.html#man-types">Types</a>, and <a href="methods.html#Methods">Methods</a>. In this section, we explain how this promotion system works, as well as how to extend it to new types and apply it to functions besides built-in mathematical operators. Traditionally, programming languages fall into two camps with respect to promotion of arithmetic arguments:</p><ul><li><strong>Automatic promotion for built-in arithmetic types and operators.</strong> In most languages, built-in numeric types, when used as operands to arithmetic operators with infix syntax, such as <code>+</code>, <code>-</code>, <code>*</code>, and <code>/</code>, are automatically promoted to a common type to produce the expected results. C, Java, Perl, and Python, to name a few, all correctly compute the sum <code>1 + 1.5</code> as the floating-point value <code>2.5</code>, even though one of the operands to <code>+</code> is an integer. These systems are convenient and designed carefully enough that they are generally all-but-invisible to the programmer: hardly anyone consciously thinks of this promotion taking place when writing such an expression, but compilers and interpreters must perform conversion before addition since integers and floating-point values cannot be added as-is. Complex rules for such automatic conversions are thus inevitably part of specifications and implementations for such languages.</li><li><strong>No automatic promotion.</strong> This camp includes Ada and ML – very &quot;strict&quot; statically typed languages. In these languages, every conversion must be explicitly specified by the programmer. Thus, the example expression <code>1 + 1.5</code> would be a compilation error in both Ada and ML. Instead one must write <code>real(1) + 1.5</code>, explicitly converting the integer <code>1</code> to a floating-point value before performing addition. Explicit conversion everywhere is so inconvenient, however, that even Ada has some degree of automatic conversion: integer literals are promoted to the expected integer type automatically, and floating-point literals are similarly promoted to appropriate floating-point types.</li></ul><p>In a sense, Julia falls into the &quot;no automatic promotion&quot; category: mathematical operators are just functions with special syntax, and the arguments of functions are never automatically converted. However, one may observe that applying mathematical operations to a wide variety of mixed argument types is just an extreme case of polymorphic multiple dispatch – something which Julia&#39;s dispatch and type systems are particularly well-suited to handle. &quot;Automatic&quot; promotion of mathematical operands simply emerges as a special application: Julia comes with pre-defined catch-all dispatch rules for mathematical operators, invoked when no specific implementation exists for some combination of operand types. These catch-all rules first promote all operands to a common type using user-definable promotion rules, and then invoke a specialized implementation of the operator in question for the resulting values, now of the same type. User-defined types can easily participate in this promotion system by defining methods for conversion to and from other types, and providing a handful of promotion rules defining what types they should promote to when mixed with other types.</p><h2 id="Conversion"><a class="docs-heading-anchor" href="#Conversion">Conversion</a><a id="Conversion-1"></a><a class="docs-heading-anchor-permalink" href="#Conversion" title="Permalink"></a></h2><p>The standard way to obtain a value of a certain type <code>T</code> is to call the type&#39;s constructor, <code>T(x)</code>. However, there are cases where it&#39;s convenient to convert a value from one type to another without the programmer asking for it explicitly. One example is assigning a value into an array: if <code>A</code> is a <code>Vector{Float64}</code>, the expression <code>A[1] = 2</code> should work by automatically converting the <code>2</code> from <code>Int</code> to <code>Float64</code>, and storing the result in the array. This is done via the <a href="../base/base.html#Base.convert"><code>convert</code></a> function.</p><p>The <code>convert</code> function generally takes two arguments: the first is a type object and the second is a value to convert to that type. The returned value is the value converted to an instance of given type. The simplest way to understand this function is to see it in action:</p><pre><code class="language-julia-repl hljs">julia&gt; x = 12
12

julia&gt; typeof(x)
Int64

julia&gt; xu = convert(UInt8, x)
0x0c

julia&gt; typeof(xu)
UInt8

julia&gt; xf = convert(AbstractFloat, x)
12.0

julia&gt; typeof(xf)
Float64

julia&gt; a = Any[1 2 3; 4 5 6]
2×3 Matrix{Any}:
 1  2  3
 4  5  6

julia&gt; convert(Array{Float64}, a)
2×3 Matrix{Float64}:
 1.0  2.0  3.0
 4.0  5.0  6.0</code></pre><p>Conversion isn&#39;t always possible, in which case a <a href="../base/base.html#Core.MethodError"><code>MethodError</code></a> is thrown indicating that <code>convert</code> doesn&#39;t know how to perform the requested conversion:</p><pre><code class="language-julia-repl hljs">julia&gt; convert(AbstractFloat, &quot;foo&quot;)
ERROR: MethodError: Cannot `convert` an object of type String to an object of type AbstractFloat
[...]</code></pre><p>Some languages consider parsing strings as numbers or formatting numbers as strings to be conversions (many dynamic languages will even perform conversion for you automatically). This is not the case in Julia. Even though some strings can be parsed as numbers, most strings are not valid representations of numbers, and only a very limited subset of them are. Therefore in Julia the dedicated <a href="../base/numbers.html#Base.parse"><code>parse</code></a> function must be used to perform this operation, making it more explicit.</p><h3 id="When-is-convert-called?"><a class="docs-heading-anchor" href="#When-is-convert-called?">When is <code>convert</code> called?</a><a id="When-is-convert-called?-1"></a><a class="docs-heading-anchor-permalink" href="#When-is-convert-called?" title="Permalink"></a></h3><p>The following language constructs call <code>convert</code>:</p><ul><li>Assigning to an array converts to the array&#39;s element type.</li><li>Assigning to a field of an object converts to the declared type of the field.</li><li>Constructing an object with <a href="../base/base.html#new"><code>new</code></a> converts to the object&#39;s declared field types.</li><li>Assigning to a variable with a declared type (e.g. <code>local x::T</code>) converts to that type.</li><li>A function with a declared return type converts its return value to that type.</li><li>Passing a value to <a href="../base/c.html#ccall"><code>ccall</code></a> converts it to the corresponding argument type.</li></ul><h3 id="Conversion-vs.-Construction"><a class="docs-heading-anchor" href="#Conversion-vs.-Construction">Conversion vs. Construction</a><a id="Conversion-vs.-Construction-1"></a><a class="docs-heading-anchor-permalink" href="#Conversion-vs.-Construction" title="Permalink"></a></h3><p>Note that the behavior of <code>convert(T, x)</code> appears to be nearly identical to <code>T(x)</code>. Indeed, it usually is. However, there is a key semantic difference: since <code>convert</code> can be called implicitly, its methods are restricted to cases that are considered &quot;safe&quot; or &quot;unsurprising&quot;. <code>convert</code> will only convert between types that represent the same basic kind of thing (e.g. different representations of numbers, or different string encodings). It is also usually lossless; converting a value to a different type and back again should result in the exact same value.</p><p>There are four general kinds of cases where constructors differ from <code>convert</code>:</p><h4 id="Constructors-for-types-unrelated-to-their-arguments"><a class="docs-heading-anchor" href="#Constructors-for-types-unrelated-to-their-arguments">Constructors for types unrelated to their arguments</a><a id="Constructors-for-types-unrelated-to-their-arguments-1"></a><a class="docs-heading-anchor-permalink" href="#Constructors-for-types-unrelated-to-their-arguments" title="Permalink"></a></h4><p>Some constructors don&#39;t implement the concept of &quot;conversion&quot;. For example, <code>Timer(2)</code> creates a 2-second timer, which is not really a &quot;conversion&quot; from an integer to a timer.</p><h4 id="Mutable-collections"><a class="docs-heading-anchor" href="#Mutable-collections">Mutable collections</a><a id="Mutable-collections-1"></a><a class="docs-heading-anchor-permalink" href="#Mutable-collections" title="Permalink"></a></h4><p><code>convert(T, x)</code> is expected to return the original <code>x</code> if <code>x</code> is already of type <code>T</code>. In contrast, if <code>T</code> is a mutable collection type then <code>T(x)</code> should always make a new collection (copying elements from <code>x</code>).</p><h4 id="Wrapper-types"><a class="docs-heading-anchor" href="#Wrapper-types">Wrapper types</a><a id="Wrapper-types-1"></a><a class="docs-heading-anchor-permalink" href="#Wrapper-types" title="Permalink"></a></h4><p>For some types which &quot;wrap&quot; other values, the constructor may wrap its argument inside a new object even if it is already of the requested type. For example <code>Some(x)</code> wraps <code>x</code> to indicate that a value is present (in a context where the result might be a <code>Some</code> or <code>nothing</code>). However, <code>x</code> itself might be the object <code>Some(y)</code>, in which case the result is <code>Some(Some(y))</code>, with two levels of wrapping. <code>convert(Some, x)</code>, on the other hand, would just return <code>x</code> since it is already a <code>Some</code>.</p><h4 id="Constructors-that-don&#39;t-return-instances-of-their-own-type"><a class="docs-heading-anchor" href="#Constructors-that-don&#39;t-return-instances-of-their-own-type">Constructors that don&#39;t return instances of their own type</a><a id="Constructors-that-don&#39;t-return-instances-of-their-own-type-1"></a><a class="docs-heading-anchor-permalink" href="#Constructors-that-don&#39;t-return-instances-of-their-own-type" title="Permalink"></a></h4><p>In <em>very rare</em> cases it might make sense for the constructor <code>T(x)</code> to return an object not of type <code>T</code>. This could happen if a wrapper type is its own inverse (e.g. <code>Flip(Flip(x)) === x</code>), or to support an old calling syntax for backwards compatibility when a library is restructured. But <code>convert(T, x)</code> should always return a value of type <code>T</code>.</p><h3 id="Defining-New-Conversions"><a class="docs-heading-anchor" href="#Defining-New-Conversions">Defining New Conversions</a><a id="Defining-New-Conversions-1"></a><a class="docs-heading-anchor-permalink" href="#Defining-New-Conversions" title="Permalink"></a></h3><p>When defining a new type, initially all ways of creating it should be defined as constructors. If it becomes clear that implicit conversion would be useful, and that some constructors meet the above &quot;safety&quot; criteria, then <code>convert</code> methods can be added. These methods are typically quite simple, as they only need to call the appropriate constructor. Such a definition might look like this:</p><pre><code class="language-julia hljs">import Base: convert
convert(::Type{MyType}, x) = MyType(x)</code></pre><p>The type of the first argument of this method is <a href="types.html#man-typet-type"><code>Type{MyType}</code></a>, the only instance of which is <code>MyType</code>. Thus, this method is only invoked when the first argument is the type value <code>MyType</code>. Notice the syntax used for the first argument: the argument name is omitted prior to the <code>::</code> symbol, and only the type is given. This is the syntax in Julia for a function argument whose type is specified but whose value does not need to be referenced by name.</p><p>All instances of some abstract types are by default considered &quot;sufficiently similar&quot; that a universal <code>convert</code> definition is provided in Julia Base. For example, this definition states that it&#39;s valid to <code>convert</code> any <code>Number</code> type to any other by calling a 1-argument constructor:</p><pre><code class="language-julia hljs">convert(::Type{T}, x::Number) where {T&lt;:Number} = T(x)::T</code></pre><p>This means that new <code>Number</code> types only need to define constructors, since this definition will handle <code>convert</code> for them. An identity conversion is also provided to handle the case where the argument is already of the requested type:</p><pre><code class="language-julia hljs">convert(::Type{T}, x::T) where {T&lt;:Number} = x</code></pre><p>Similar definitions exist for <code>AbstractString</code>, <a href="../base/arrays.html#Core.AbstractArray"><code>AbstractArray</code></a>, and <a href="../base/collections.html#Base.AbstractDict"><code>AbstractDict</code></a>.</p><h2 id="Promotion"><a class="docs-heading-anchor" href="#Promotion">Promotion</a><a id="Promotion-1"></a><a class="docs-heading-anchor-permalink" href="#Promotion" title="Permalink"></a></h2><p>Promotion refers to converting values of mixed types to a single common type. Although it is not strictly necessary, it is generally implied that the common type to which the values are converted can faithfully represent all of the original values. In this sense, the term &quot;promotion&quot; is appropriate since the values are converted to a &quot;greater&quot; type – i.e. one which can represent all of the input values in a single common type. It is important, however, not to confuse this with object-oriented (structural) super-typing, or Julia&#39;s notion of abstract super-types: promotion has nothing to do with the type hierarchy, and everything to do with converting between alternate representations. For instance, although every <a href="../base/numbers.html#Core.Int32"><code>Int32</code></a> value can also be represented as a <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> value, <code>Int32</code> is not a subtype of <code>Float64</code>.</p><p>Promotion to a common &quot;greater&quot; type is performed in Julia by the <a href="../base/base.html#Base.promote"><code>promote</code></a> function, which takes any number of arguments, and returns a tuple of the same number of values, converted to a common type, or throws an exception if promotion is not possible. The most common use case for promotion is to convert numeric arguments to a common type:</p><pre><code class="language-julia-repl hljs">julia&gt; promote(1, 2.5)
(1.0, 2.5)

julia&gt; promote(1, 2.5, 3)
(1.0, 2.5, 3.0)

julia&gt; promote(2, 3//4)
(2//1, 3//4)

julia&gt; promote(1, 2.5, 3, 3//4)
(1.0, 2.5, 3.0, 0.75)

julia&gt; promote(1.5, im)
(1.5 + 0.0im, 0.0 + 1.0im)

julia&gt; promote(1 + 2im, 3//4)
(1//1 + 2//1*im, 3//4 + 0//1*im)</code></pre><p>Floating-point values are promoted to the largest of the floating-point argument types. Integer values are promoted to the largest of the integer argument types. If the types are the same size but differ in signedness, the unsigned type is chosen. Mixtures of integers and floating-point values are promoted to a floating-point type big enough to hold all the values. Integers mixed with rationals are promoted to rationals. Rationals mixed with floats are promoted to floats. Complex values mixed with real values are promoted to the appropriate kind of complex value.</p><p>That is really all there is to using promotions. The rest is just a matter of clever application, the most typical &quot;clever&quot; application being the definition of catch-all methods for numeric operations like the arithmetic operators <code>+</code>, <code>-</code>, <code>*</code> and <code>/</code>. Here are some of the catch-all method definitions given in <a href="https://github.com/JuliaLang/julia/blob/master/base/promotion.jl"><code>promotion.jl</code></a>:</p><pre><code class="language-julia hljs">+(x::Number, y::Number) = +(promote(x,y)...)
-(x::Number, y::Number) = -(promote(x,y)...)
*(x::Number, y::Number) = *(promote(x,y)...)
/(x::Number, y::Number) = /(promote(x,y)...)</code></pre><p>These method definitions say that in the absence of more specific rules for adding, subtracting, multiplying and dividing pairs of numeric values, promote the values to a common type and then try again. That&#39;s all there is to it: nowhere else does one ever need to worry about promotion to a common numeric type for arithmetic operations – it just happens automatically. There are definitions of catch-all promotion methods for a number of other arithmetic and mathematical functions in <a href="https://github.com/JuliaLang/julia/blob/master/base/promotion.jl"><code>promotion.jl</code></a>, but beyond that, there are hardly any calls to <code>promote</code> required in Julia Base. The most common usages of <code>promote</code> occur in outer constructors methods, provided for convenience, to allow constructor calls with mixed types to delegate to an inner type with fields promoted to an appropriate common type. For example, recall that <a href="https://github.com/JuliaLang/julia/blob/master/base/rational.jl"><code>rational.jl</code></a> provides the following outer constructor method:</p><pre><code class="language-julia hljs">Rational(n::Integer, d::Integer) = Rational(promote(n,d)...)</code></pre><p>This allows calls like the following to work:</p><pre><code class="language-julia-repl hljs">julia&gt; x = Rational(Int8(15),Int32(-5))
-3//1

julia&gt; typeof(x)
Rational{Int32}</code></pre><p>For most user-defined types, it is better practice to require programmers to supply the expected types to constructor functions explicitly, but sometimes, especially for numeric problems, it can be convenient to do promotion automatically.</p><h3 id="Defining-Promotion-Rules"><a class="docs-heading-anchor" href="#Defining-Promotion-Rules">Defining Promotion Rules</a><a id="Defining-Promotion-Rules-1"></a><a class="docs-heading-anchor-permalink" href="#Defining-Promotion-Rules" title="Permalink"></a></h3><p>Although one could, in principle, define methods for the <code>promote</code> function directly, this would require many redundant definitions for all possible permutations of argument types. Instead, the behavior of <code>promote</code> is defined in terms of an auxiliary function called <a href="../base/base.html#Base.promote_rule"><code>promote_rule</code></a>, which one can provide methods for. The <code>promote_rule</code> function takes a pair of type objects and returns another type object, such that instances of the argument types will be promoted to the returned type. Thus, by defining the rule:</p><pre><code class="language-julia hljs">import Base: promote_rule
promote_rule(::Type{Float64}, ::Type{Float32}) = Float64</code></pre><p>one declares that when 64-bit and 32-bit floating-point values are promoted together, they should be promoted to 64-bit floating-point. The promotion type does not need to be one of the argument types. For example, the following promotion rules both occur in Julia Base:</p><pre><code class="language-julia hljs">promote_rule(::Type{BigInt}, ::Type{Float64}) = BigFloat
promote_rule(::Type{BigInt}, ::Type{Int8}) = BigInt</code></pre><p>In the latter case, the result type is <a href="../base/numbers.html#Base.GMP.BigInt"><code>BigInt</code></a> since <code>BigInt</code> is the only type large enough to hold integers for arbitrary-precision integer arithmetic. Also note that one does not need to define both <code>promote_rule(::Type{A}, ::Type{B})</code> and <code>promote_rule(::Type{B}, ::Type{A})</code> – the symmetry is implied by the way <code>promote_rule</code> is used in the promotion process.</p><p>The <code>promote_rule</code> function is used as a building block to define a second function called <a href="../base/base.html#Base.promote_type"><code>promote_type</code></a>, which, given any number of type objects, returns the common type to which those values, as arguments to <code>promote</code> should be promoted. Thus, if one wants to know, in absence of actual values, what type a collection of values of certain types would promote to, one can use <code>promote_type</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; promote_type(Int8, Int64)
Int64</code></pre><p>Note that we do <strong>not</strong> overload <code>promote_type</code> directly: we overload <code>promote_rule</code> instead. <code>promote_type</code> uses <code>promote_rule</code>, and adds the symmetry. Overloading it directly can cause ambiguity errors. We overload <code>promote_rule</code> to define how things should be promoted, and we use <code>promote_type</code> to query that.</p><p>Internally, <code>promote_type</code> is used inside of <code>promote</code> to determine what type argument values should be converted to for promotion. The curious reader can read the code in <a href="https://github.com/JuliaLang/julia/blob/master/base/promotion.jl"><code>promotion.jl</code></a>, which defines the complete promotion mechanism in about 35 lines.</p><h3 id="Case-Study:-Rational-Promotions"><a class="docs-heading-anchor" href="#Case-Study:-Rational-Promotions">Case Study: Rational Promotions</a><a id="Case-Study:-Rational-Promotions-1"></a><a class="docs-heading-anchor-permalink" href="#Case-Study:-Rational-Promotions" title="Permalink"></a></h3><p>Finally, we finish off our ongoing case study of Julia&#39;s rational number type, which makes relatively sophisticated use of the promotion mechanism with the following promotion rules:</p><pre><code class="language-julia hljs">import Base: promote_rule
promote_rule(::Type{Rational{T}}, ::Type{S}) where {T&lt;:Integer,S&lt;:Integer} = Rational{promote_type(T,S)}
promote_rule(::Type{Rational{T}}, ::Type{Rational{S}}) where {T&lt;:Integer,S&lt;:Integer} = Rational{promote_type(T,S)}
promote_rule(::Type{Rational{T}}, ::Type{S}) where {T&lt;:Integer,S&lt;:AbstractFloat} = promote_type(T,S)</code></pre><p>The first rule says that promoting a rational number with any other integer type promotes to a rational type whose numerator/denominator type is the result of promotion of its numerator/denominator type with the other integer type. The second rule applies the same logic to two different types of rational numbers, resulting in a rational of the promotion of their respective numerator/denominator types. The third and final rule dictates that promoting a rational with a float results in the same type as promoting the numerator/denominator type with the float.</p><p>This small handful of promotion rules, together with the type&#39;s constructors and the default <code>convert</code> method for numbers, are sufficient to make rational numbers interoperate completely naturally with all of Julia&#39;s other numeric types – integers, floating-point numbers, and complex numbers. By providing appropriate conversion methods and promotion rules in the same manner, any user-defined numeric type can interoperate just as naturally with Julia&#39;s predefined numerics.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="constructors.html">« Constructors</a><a class="docs-footer-nextpage" href="interfaces.html">Interfaces »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
