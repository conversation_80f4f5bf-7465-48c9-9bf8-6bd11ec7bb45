<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Scope of Variables · The Julia Language</title><meta name="title" content="Scope of Variables · The Julia Language"/><meta property="og:title" content="Scope of Variables · The Julia Language"/><meta property="twitter:title" content="Scope of Variables · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li class="is-active"><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a><ul class="internal"><li><a class="tocitem" href="#Global-Scope"><span>Global Scope</span></a></li><li><a class="tocitem" href="#local-scope"><span>Local Scope</span></a></li><li><a class="tocitem" href="#Constants"><span>Constants</span></a></li><li><a class="tocitem" href="#man-typed-globals"><span>Typed Globals</span></a></li></ul></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="variables-and-scoping.html">Scope of Variables</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="variables-and-scoping.html">Scope of Variables</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/variables-and-scoping.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="scope-of-variables"><a class="docs-heading-anchor" href="#scope-of-variables">Scope of Variables</a><a id="scope-of-variables-1"></a><a class="docs-heading-anchor-permalink" href="#scope-of-variables" title="Permalink"></a></h1><p>The <em>scope</em> of a variable is the region of code within which a variable is accessible. Variable scoping helps avoid variable naming conflicts. The concept is intuitive: two functions can both have arguments called <code>x</code> without the two <code>x</code>&#39;s referring to the same thing. Similarly, there are many other cases where different blocks of code can use the same name without referring to the same thing. The rules for when the same variable name does or doesn&#39;t refer to the same thing are called scope rules; this section spells them out in detail.</p><p>Certain constructs in the language introduce <em>scope blocks</em>, which are regions of code that are eligible to be the scope of some set of variables. The scope of a variable cannot be an arbitrary set of source lines; instead, it will always line up with one of these blocks. There are two main types of scopes in Julia, <em>global scope</em> and <em>local scope</em>. The latter can be nested. There is also a distinction in Julia between constructs which introduce a &quot;hard scope&quot; and those which only introduce a &quot;soft scope&quot;, which affects whether <a href="https://en.wikipedia.org/wiki/Variable_shadowing">shadowing</a> a global variable by the same name is allowed or not.</p><h3 id="man-scope-table"><a class="docs-heading-anchor" href="#man-scope-table">Scope constructs</a><a id="man-scope-table-1"></a><a class="docs-heading-anchor-permalink" href="#man-scope-table" title="Permalink"></a></h3><p>The constructs introducing scope blocks are:</p><table><tr><th style="text-align: left">Construct</th><th style="text-align: left">Scope type</th><th style="text-align: left">Allowed within</th></tr><tr><td style="text-align: left"><a href="../base/base.html#module"><code>module</code></a>, <a href="../base/base.html#baremodule"><code>baremodule</code></a></td><td style="text-align: left">global</td><td style="text-align: left">global</td></tr><tr><td style="text-align: left"><a href="../base/base.html#struct"><code>struct</code></a></td><td style="text-align: left">local (soft)</td><td style="text-align: left">global</td></tr><tr><td style="text-align: left"><a href="../base/base.html#for"><code>for</code></a>, <a href="../base/base.html#while"><code>while</code></a>, <a href="../base/base.html#try"><code>try</code></a></td><td style="text-align: left">local (soft)</td><td style="text-align: left">global, local</td></tr><tr><td style="text-align: left"><a href="../base/base.html#macro"><code>macro</code></a></td><td style="text-align: left">local (hard)</td><td style="text-align: left">global</td></tr><tr><td style="text-align: left">functions, <a href="../base/base.html#do"><code>do</code></a> blocks, <a href="../base/base.html#let"><code>let</code></a> blocks, comprehensions, generators</td><td style="text-align: left">local (hard)</td><td style="text-align: left">global, local</td></tr></table><p>Notably missing from this table are <a href="control-flow.html#man-compound-expressions">begin blocks</a> and <a href="control-flow.html#man-conditional-evaluation">if blocks</a> which do <em>not</em> introduce new scopes. The three types of scopes follow somewhat different rules which will be explained below.</p><p>Julia uses <a href="https://en.wikipedia.org/wiki/Scope_(computer_science)#Lexical_scope_vs._dynamic_scope">lexical scoping</a>, meaning that a function&#39;s scope does not inherit from its caller&#39;s scope, but from the scope in which the function was defined. For example, in the following code the <code>x</code> inside <code>foo</code> refers to the <code>x</code> in the global scope of its module <code>Bar</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; module Bar
           x = 1
           foo() = x
       end;</code></pre><p>and not a <code>x</code> in the scope where <code>foo</code> is used:</p><pre><code class="language-julia-repl hljs">julia&gt; import .Bar

julia&gt; x = -1;

julia&gt; Bar.foo()
1</code></pre><p>Thus <em>lexical scope</em> means that what a variable in a particular piece of code refers to can be deduced from the code in which it appears alone and does not depend on how the program executes. A scope nested inside another scope can &quot;see&quot; variables in all the outer scopes in which it is contained. Outer scopes, on the other hand, cannot see variables in inner scopes.</p><h2 id="Global-Scope"><a class="docs-heading-anchor" href="#Global-Scope">Global Scope</a><a id="Global-Scope-1"></a><a class="docs-heading-anchor-permalink" href="#Global-Scope" title="Permalink"></a></h2><p>Each module introduces a new global scope, separate from the global scope of all other modules—there is no all-encompassing global scope. Modules can introduce variables of other modules into their scope through the <a href="modules.html#modules">using or import</a> statements or through qualified access using the dot-notation, i.e. each module is a so-called <em>namespace</em> as well as a first-class data structure associating names with values.</p><p>If a top-level expression contains a variable declaration with keyword <code>local</code>, then that variable is not accessible outside that expression. The variable inside the expression does not affect global variables of the same name. An example is to declare <code>local x</code> in a <code>begin</code> or <code>if</code> block at the top-level:</p><pre><code class="language-julia-repl hljs">julia&gt; x = 1
       begin
           local x = 0
           @show x
       end
       @show x;
x = 0
x = 1</code></pre><p>Note that the interactive prompt (aka REPL) is in the global scope of the module <code>Main</code>.</p><h2 id="local-scope"><a class="docs-heading-anchor" href="#local-scope">Local Scope</a><a id="local-scope-1"></a><a class="docs-heading-anchor-permalink" href="#local-scope" title="Permalink"></a></h2><p>A new local scope is introduced by most code blocks (see above <a href="variables-and-scoping.html#man-scope-table">table</a> for a complete list). If such a block is syntactically nested inside of another local scope, the scope it creates is nested inside of all the local scopes that it appears within, which are all ultimately nested inside of the global scope of the module in which the code is evaluated. Variables in outer scopes are visible from any scope they contain — meaning that they can be read and written in inner scopes — unless there is a local variable with the same name that &quot;shadows&quot; the outer variable of the same name. This is true even if the outer local is declared after (in the sense of textually below) an inner block. When we say that a variable &quot;exists&quot; in a given scope, this means that a variable by that name exists in any of the scopes that the current scope is nested inside of, including the current one.</p><p>Some programming languages require explicitly declaring new variables before using them. Explicit declaration works in Julia too: in any local scope, writing <code>local x</code> declares a new local variable in that scope, regardless of whether there is already a variable named <code>x</code> in an outer scope or not. Declaring each new variable like this is somewhat verbose and tedious, however, so Julia, like many other languages, considers assignment to a variable name that doesn&#39;t already exist to implicitly declare that variable. If the current scope is global, the new variable is global; if the current scope is local, the new variable is local to the innermost local scope and will be visible inside of that scope but not outside of it. If you assign to an existing local, it <em>always</em> updates that existing local: you can only shadow a local by explicitly declaring a new local in a nested scope with the <code>local</code> keyword. In particular, this applies to variables assigned in inner functions, which may surprise users coming from Python where assignment in an inner function creates a new local unless the variable is explicitly declared to be non-local.</p><p>Mostly this is pretty intuitive, but as with many things that behave intuitively, the details are more subtle than one might naïvely imagine.</p><p>When <code>x = &lt;value&gt;</code> occurs in a local scope, Julia applies the following rules to decide what the expression means based on where the assignment expression occurs and what <code>x</code> already refers to at that location:</p><ol><li><strong>Existing local:</strong> If <code>x</code> is <em>already a local variable</em>, then the existing local <code>x</code> is assigned;</li><li><strong>Hard scope:</strong> If <code>x</code> is <em>not already a local variable</em> and assignment occurs inside of any hard scope construct (i.e. within a <code>let</code> block, function or macro body, comprehension, or generator), a new local named <code>x</code> is created in the scope of the assignment;</li><li><strong>Soft scope:</strong> If <code>x</code> is <em>not already a local variable</em> and all of the scope constructs containing the assignment are soft scopes (loops, <code>try</code>/<code>catch</code> blocks, or <code>struct</code> blocks), the behavior depends on whether the global variable <code>x</code> is defined:<ul><li>if global <code>x</code> is <em>undefined</em>, a new local named <code>x</code> is created in the scope of the assignment;</li><li>if global <code>x</code> is <em>defined</em>, the assignment is considered ambiguous:<ul><li>in <em>non-interactive</em> contexts (files, eval), an ambiguity warning is printed and a new local is created;</li><li>in <em>interactive</em> contexts (REPL, notebooks), the global variable <code>x</code> is assigned.</li></ul></li></ul></li></ol><p>You may note that in non-interactive contexts the hard and soft scope behaviors are identical except that a warning is printed when an implicitly local variable (i.e. not declared with <code>local x</code>) shadows a global. In interactive contexts, the rules follow a more complex heuristic for the sake of convenience. This is covered in depth in examples that follow.</p><p>Now that you know the rules, let&#39;s look at some examples. Each example is assumed to be evaluated in a fresh REPL session so that the only globals in each snippet are the ones that are assigned in that block of code.</p><p>We&#39;ll begin with a nice and clear-cut situation—assignment inside of a hard scope, in this case a function body, when no local variable by that name already exists:</p><pre><code class="language-julia-repl hljs">julia&gt; function greet()
           x = &quot;hello&quot; # new local
           println(x)
       end
greet (generic function with 1 method)

julia&gt; greet()
hello

julia&gt; x # global
ERROR: UndefVarError: `x` not defined in `Main`</code></pre><p>Inside of the <code>greet</code> function, the assignment <code>x = &quot;hello&quot;</code> causes <code>x</code> to be a new local variable in the function&#39;s scope. There are two relevant facts: the assignment occurs in local scope and there is no existing local <code>x</code> variable. Since <code>x</code> is local, it doesn&#39;t matter if there is a global named <code>x</code> or not. Here for example we define <code>x = 123</code> before defining and calling <code>greet</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; x = 123 # global
123

julia&gt; function greet()
           x = &quot;hello&quot; # new local
           println(x)
       end
greet (generic function with 1 method)

julia&gt; greet()
hello

julia&gt; x # global
123</code></pre><p>Since the <code>x</code> in <code>greet</code> is local, the value (or lack thereof) of the global <code>x</code> is unaffected by calling <code>greet</code>. The hard scope rule doesn&#39;t care whether a global named <code>x</code> exists or not: assignment to <code>x</code> in a hard scope is local (unless <code>x</code> is declared global).</p><p>The next clear cut situation we&#39;ll consider is when there is already a local variable named <code>x</code>, in which case <code>x = &lt;value&gt;</code> always assigns to this existing local <code>x</code>. This is true whether the assignment occurs in the same local scope, an inner local scope in the same function body, or in the body of a function nested inside of another function, also known as a <a href="https://en.wikipedia.org/wiki/Closure_(computer_programming)">closure</a>.</p><p>We&#39;ll use the <code>sum_to</code> function, which computes the sum of integers from one up to <code>n</code>, as an example:</p><pre><code class="language-julia hljs">function sum_to(n)
    s = 0 # new local
    for i = 1:n
        s = s + i # assign existing local
    end
    return s # same local
end</code></pre><p>As in the previous example, the first assignment to <code>s</code> at the top of <code>sum_to</code> causes <code>s</code> to be a new local variable in the body of the function. The <code>for</code> loop has its own inner local scope within the function scope. At the point where <code>s = s + i</code> occurs, <code>s</code> is already a local variable, so the assignment updates the existing <code>s</code> instead of creating a new local. We can test this out by calling <code>sum_to</code> in the REPL:</p><pre><code class="language-julia-repl hljs">julia&gt; function sum_to(n)
           s = 0 # new local
           for i = 1:n
               s = s + i # assign existing local
           end
           return s # same local
       end
sum_to (generic function with 1 method)

julia&gt; sum_to(10)
55

julia&gt; s # global
ERROR: UndefVarError: `s` not defined in `Main`</code></pre><p>Since <code>s</code> is local to the function <code>sum_to</code>, calling the function has no effect on the global variable <code>s</code>. We can also see that the update <code>s = s + i</code> in the <code>for</code> loop must have updated the same <code>s</code> created by the initialization <code>s = 0</code> since we get the correct sum of 55 for the integers 1 through 10.</p><p>Let&#39;s dig into the fact that the <code>for</code> loop body has its own scope for a second by writing a slightly more verbose variation which we&#39;ll call <code>sum_to_def</code>, in which we save the sum <code>s + i</code> in a variable <code>t</code> before updating <code>s</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; function sum_to_def(n)
           s = 0 # new local
           for i = 1:n
               t = s + i # new local `t`
               s = t # assign existing local `s`
           end
           return s, @isdefined(t)
       end
sum_to_def (generic function with 1 method)

julia&gt; sum_to_def(10)
(55, false)</code></pre><p>This version returns <code>s</code> as before but it also uses the <code>@isdefined</code> macro to return a boolean indicating whether there is a local variable named <code>t</code> defined in the function&#39;s outermost local scope. As you can see, there is no <code>t</code> defined outside of the <code>for</code> loop body. This is because of the hard scope rule again: since the assignment to <code>t</code> occurs inside of a function, which introduces a hard scope, the assignment causes <code>t</code> to become a new local variable in the local scope where it appears, i.e. inside of the loop body. Even if there were a global named <code>t</code>, it would make no difference—the hard scope rule isn&#39;t affected by anything in global scope.</p><p>Note that the local scope of a for loop body is no different from the local scope of an inner function. This means that we could rewrite this example so that the loop body is implemented as a call to an inner helper function and it behaves the same way:</p><pre><code class="language-julia-repl hljs">julia&gt; function sum_to_def_closure(n)
           function loop_body(i)
               t = s + i # new local `t`
               s = t # assign same local `s` as below
           end
           s = 0 # new local
           for i = 1:n
               loop_body(i)
           end
           return s, @isdefined(t)
       end
sum_to_def_closure (generic function with 1 method)

julia&gt; sum_to_def_closure(10)
(55, false)</code></pre><p>This example illustrates a couple of key points:</p><ol><li><p>Inner function scopes are just like any other nested local scope. In particular, if a variable is already a local outside of an inner function and you assign to it in the inner function, the outer local variable is updated.</p></li><li><p>It doesn&#39;t matter if the definition of an outer local happens below where it is updated, the rule remains the same. The entire enclosing local scope is parsed and its locals determined before inner local meanings are resolved.</p></li></ol><p>This design means that you can generally move code in or out of an inner function without changing its meaning, which facilitates a number of common idioms in the language using closures (see <a href="functions.html#Do-Block-Syntax-for-Function-Arguments">do blocks</a>).</p><p>Let&#39;s move onto some more ambiguous cases covered by the soft scope rule. We&#39;ll explore this by extracting the bodies of the <code>greet</code> and <code>sum_to_def</code> functions into soft scope contexts. First, let&#39;s put the body of <code>greet</code> in a <code>for</code> loop—which is soft, rather than hard—and evaluate it in the REPL:</p><pre><code class="language-julia-repl hljs">julia&gt; for i = 1:3
           x = &quot;hello&quot; # new local
           println(x)
       end
hello
hello
hello

julia&gt; x
ERROR: UndefVarError: `x` not defined in `Main`</code></pre><p>Since the global <code>x</code> is not defined when the <code>for</code> loop is evaluated, the first clause of the soft scope rule applies and <code>x</code> is created as local to the <code>for</code> loop and therefore global <code>x</code> remains undefined after the loop executes. Next, let&#39;s consider the body of <code>sum_to_def</code> extracted into global scope, fixing its argument to <code>n = 10</code></p><pre><code class="language-julia hljs">s = 0
for i = 1:10
    t = s + i
    s = t
end
s
@isdefined(t)</code></pre><p>What does this code do? Hint: it&#39;s a trick question. The answer is &quot;it depends.&quot; If this code is entered interactively, it behaves the same way it does in a function body. But if the code appears in a file, it  prints an ambiguity warning and throws an undefined variable error. Let&#39;s see it working in the REPL first:</p><pre><code class="language-julia-repl hljs">julia&gt; s = 0 # global
0

julia&gt; for i = 1:10
           t = s + i # new local `t`
           s = t # assign global `s`
       end

julia&gt; s # global
55

julia&gt; @isdefined(t) # global
false</code></pre><p>The REPL approximates being in the body of a function by deciding whether assignment inside the loop assigns to a global or creates new local based on whether a global variable by that name is defined or not. If a global by the name exists, then the assignment updates it. If no global exists, then the assignment creates a new local variable. In this example we see both cases in action:</p><ul><li>There is no global named <code>t</code>, so <code>t = s + i</code> creates a new <code>t</code> that is local to the <code>for</code> loop;</li><li>There is a global named <code>s</code>, so <code>s = t</code> assigns to it.</li></ul><p>The second fact is why execution of the loop changes the global value of <code>s</code> and the first fact is why <code>t</code> is still undefined after the loop executes. Now, let&#39;s try evaluating this same code as though it were in a file instead:</p><pre><code class="language-julia-repl hljs">julia&gt; code = &quot;&quot;&quot;
       s = 0 # global
       for i = 1:10
           t = s + i # new local `t`
           s = t # new local `s` with warning
       end
       s, # global
       @isdefined(t) # global
       &quot;&quot;&quot;;

julia&gt; include_string(Main, code)
┌ Warning: Assignment to `s` in soft scope is ambiguous because a global variable by the same name exists: `s` will be treated as a new local. Disambiguate by using `local s` to suppress this warning or `global s` to assign to the existing global variable.
└ @ string:4
ERROR: LoadError: UndefVarError: `s` not defined in local scope</code></pre><p>Here we use <a href="../base/base.html#Base.include_string"><code>include_string</code></a>, to evaluate <code>code</code> as though it were the contents of a file. We could also save <code>code</code> to a file and then call <code>include</code> on that file—the result would be the same. As you can see, this behaves quite different from evaluating the same code in the REPL. Let&#39;s break down what&#39;s happening here:</p><ul><li>global <code>s</code> is defined with the value <code>0</code> before the loop is evaluated</li><li>the assignment <code>s = t</code> occurs in a soft scope—a <code>for</code> loop outside of any function body or other hard scope construct</li><li>therefore the second clause of the soft scope rule applies, and the assignment is ambiguous so a warning is emitted</li><li>execution continues, making <code>s</code> local to the <code>for</code> loop body</li><li>since <code>s</code> is local to the <code>for</code> loop, it is undefined when <code>t = s + i</code> is evaluated, causing an error</li><li>evaluation stops there, but if it got to <code>s</code> and <code>@isdefined(t)</code>, it would return <code>0</code> and <code>false</code>.</li></ul><p>This demonstrates some important aspects of scope: in a scope, each variable can only have one meaning, and that meaning is determined regardless of the order of expressions. The presence of the expression <code>s = t</code> in the loop causes <code>s</code> to be local to the loop, which means that it is also local when it appears on the right hand side of <code>t = s + i</code>, even though that expression appears first and is evaluated first. One might imagine that the <code>s</code> on the first line of the loop could be global while the <code>s</code> on the second line of the loop is local, but that&#39;s not possible since the two lines are in the same scope block and each variable can only mean one thing in a given scope.</p><h4 id="on-soft-scope"><a class="docs-heading-anchor" href="#on-soft-scope">On Soft Scope</a><a id="on-soft-scope-1"></a><a class="docs-heading-anchor-permalink" href="#on-soft-scope" title="Permalink"></a></h4><p>We have now covered all the local scope rules, but before wrapping up this section, perhaps a few words should be said about why the ambiguous soft scope case is handled differently in interactive and non-interactive contexts. There are two obvious questions one could ask:</p><ol><li>Why doesn&#39;t it just work like the REPL everywhere?</li><li>Why doesn&#39;t it just work like in files everywhere? And maybe skip the warning?</li></ol><p>In Julia ≤ 0.6, all global scopes did work like the current REPL: when <code>x = &lt;value&gt;</code> occurred in a loop (or <code>try</code>/<code>catch</code>, or <code>struct</code> body) but outside of a function body (or <code>let</code> block or comprehension), it was decided based on whether a global named <code>x</code> was defined or not whether <code>x</code> should be local to the loop. This behavior has the advantage of being intuitive and convenient since it approximates the behavior inside of a function body as closely as possible. In particular, it makes it easy to move code back and forth between a function body and the REPL when trying to debug the behavior of a function. However, it has some downsides. First, it&#39;s quite a complex behavior: many people over the years were confused about this behavior and complained that it was complicated and hard both to explain and understand. Fair point. Second, and arguably worse, is that it&#39;s bad for programming &quot;at scale.&quot; When you see a small piece of code in one place like this, it&#39;s quite clear what&#39;s going on:</p><pre><code class="language-julia hljs">s = 0
for i = 1:10
    s += i
end</code></pre><p>Obviously the intention is to modify the existing global variable <code>s</code>. What else could it mean? However, not all real world code is so short or so clear. We found that code like the following often occurs in the wild:</p><pre><code class="language-julia hljs">x = 123

# much later
# maybe in a different file

for i = 1:10
    x = &quot;hello&quot;
    println(x)
end

# much later
# maybe in yet another file
# or maybe back in the first one where `x = 123`

y = x + 234</code></pre><p>It&#39;s far less clear what should happen here. Since <code>x + &quot;hello&quot;</code> is a method error, it seems probable that the intention is for <code>x</code> to be local to the <code>for</code> loop. But runtime values and what methods happen to exist cannot be used to determine the scopes of variables. With the Julia ≤ 0.6 behavior, it&#39;s especially concerning that someone might have written the <code>for</code> loop first, had it working just fine, but later when someone else adds a new global far away—possibly in a different file—the code suddenly changes meaning and either breaks noisily or, worse still, silently does the wrong thing. This kind of <a href="https://en.wikipedia.org/wiki/Action_at_a_distance_(computer_programming)">&quot;spooky action at a distance&quot;</a> is something that good programming language designs should prevent.</p><p>So in Julia 1.0, we simplified the rules for scope: in any local scope, assignment to a name that wasn&#39;t already a local variable created a new local variable. This eliminated the notion of soft scope entirely as well as removing the potential for spooky action. We uncovered and fixed a significant number of bugs due to the removal of soft scope, vindicating the choice to get rid of it. And there was much rejoicing! Well, no, not really. Because some people were angry that they now had to write:</p><pre><code class="language-julia hljs">s = 0
for i = 1:10
    global s += i
end</code></pre><p>Do you see that <code>global</code> annotation in there? Hideous. Obviously this situation could not be tolerated. But seriously, there are two main issues with requiring <code>global</code> for this kind of top-level code:</p><ol><li><p>It&#39;s no longer convenient to copy and paste the code from inside a function body into the REPL to debug it—you have to add <code>global</code> annotations and then remove them again to go back;</p></li><li><p>Beginners will write this kind of code without the <code>global</code> and have no idea why their code doesn&#39;t work—the error that they get is that <code>s</code> is undefined, which does not seem to enlighten anyone who happens to make this mistake.</p></li></ol><p>As of Julia 1.5, this code works without the <code>global</code> annotation in interactive contexts like the REPL or Jupyter notebooks (just like Julia 0.6) and in files and other non-interactive contexts, it prints this very direct warning:</p><blockquote><p>Assignment to <code>s</code> in soft scope is ambiguous because a global variable by the same name exists: <code>s</code> will be treated as a new local. Disambiguate by using <code>local s</code> to suppress this warning or <code>global s</code> to assign to the existing global variable.</p></blockquote><p>This addresses both issues while preserving the &quot;programming at scale&quot; benefits of the 1.0 behavior: global variables have no spooky effect on the meaning of code that may be far away; in the REPL copy-and-paste debugging works and beginners don&#39;t have any issues; any time someone either forgets a <code>global</code> annotation or accidentally shadows an existing global with a local in a soft scope, which would be confusing anyway, they get a nice clear warning.</p><p>An important property of this design is that any code that executes in a file without a warning will behave the same way in a fresh REPL. And on the flip side, if you take a REPL session and save it to file, if it behaves differently than it did in the REPL, then you will get a warning.</p><h3 id="Let-Blocks"><a class="docs-heading-anchor" href="#Let-Blocks">Let Blocks</a><a id="Let-Blocks-1"></a><a class="docs-heading-anchor-permalink" href="#Let-Blocks" title="Permalink"></a></h3><p><code>let</code> statements create a new <em>hard scope</em> block (see above) and introduce new variable bindings each time they run. The variable need not be immediately assigned:</p><pre><code class="language-julia-repl hljs">julia&gt; var1 = let x
           for i in 1:5
               (i == 4) &amp;&amp; (x = i; break)
           end
           x
       end
4</code></pre><p>Whereas assignments might reassign a new value to an existing value location, <code>let</code> always creates a new location. This difference is usually not important, and is only detectable in the case of variables that outlive their scope via closures. The <code>let</code> syntax accepts a comma-separated series of assignments and variable names:</p><pre><code class="language-julia-repl hljs">julia&gt; x, y, z = -1, -1, -1;

julia&gt; let x = 1, z
           println(&quot;x: $x, y: $y&quot;) # x is local variable, y the global
           println(&quot;z: $z&quot;) # errors as z has not been assigned yet but is local
       end
x: 1, y: -1
ERROR: UndefVarError: `z` not defined in local scope</code></pre><p>The assignments are evaluated in order, with each right-hand side evaluated in the scope before the new variable on the left-hand side has been introduced. Therefore it makes sense to write something like <code>let x = x</code> since the two <code>x</code> variables are distinct and have separate storage. Here is an example where the behavior of <code>let</code> is needed:</p><pre><code class="language-julia-repl hljs">julia&gt; Fs = Vector{Any}(undef, 2); i = 1;

julia&gt; while i &lt;= 2
           Fs[i] = ()-&gt;i
           global i += 1
       end

julia&gt; Fs[1]()
3

julia&gt; Fs[2]()
3</code></pre><p>Here we create and store two closures that return variable <code>i</code>. However, it is always the same variable <code>i</code>, so the two closures behave identically. We can use <code>let</code> to create a new binding for <code>i</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; Fs = Vector{Any}(undef, 2); i = 1;

julia&gt; while i &lt;= 2
           let i = i
               Fs[i] = ()-&gt;i
           end
           global i += 1
       end

julia&gt; Fs[1]()
1

julia&gt; Fs[2]()
2</code></pre><p>Since the <code>begin</code> construct does not introduce a new scope, it can be useful to use a zero-argument <code>let</code> to just introduce a new scope block without creating any new bindings immediately:</p><pre><code class="language-julia-repl hljs">julia&gt; let
           local x = 1
           let
               local x = 2
           end
           x
       end
1</code></pre><p>Since <code>let</code> introduces a new scope block, the inner local <code>x</code> is a different variable than the outer local <code>x</code>. This particular example is equivalent to:</p><pre><code class="language-julia-repl hljs">julia&gt; let x = 1
           let x = 2
           end
           x
       end
1</code></pre><h3 id="Loops-and-Comprehensions"><a class="docs-heading-anchor" href="#Loops-and-Comprehensions">Loops and Comprehensions</a><a id="Loops-and-Comprehensions-1"></a><a class="docs-heading-anchor-permalink" href="#Loops-and-Comprehensions" title="Permalink"></a></h3><p>In loops and <a href="arrays.html#man-comprehensions">comprehensions</a>, new variables introduced in their body scopes are freshly allocated for each loop iteration, as if the loop body were surrounded by a <code>let</code> block, as demonstrated by this example:</p><pre><code class="language-julia-repl hljs">julia&gt; Fs = Vector{Any}(undef, 2);

julia&gt; for j = 1:2
           Fs[j] = ()-&gt;j
       end

julia&gt; Fs[1]()
1

julia&gt; Fs[2]()
2</code></pre><p>A <code>for</code> loop or comprehension iteration variable is always a new variable:</p><pre><code class="language-julia-repl hljs">julia&gt; function f()
           i = 0
           for i = 1:3
               # empty
           end
           return i
       end;

julia&gt; f()
0</code></pre><p>However, it is occasionally useful to reuse an existing local variable as the iteration variable. This can be done conveniently by adding the keyword <code>outer</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; function f()
           i = 0
           for outer i = 1:3
               # empty
           end
           return i
       end;

julia&gt; f()
3</code></pre><h2 id="Constants"><a class="docs-heading-anchor" href="#Constants">Constants</a><a id="Constants-1"></a><a class="docs-heading-anchor-permalink" href="#Constants" title="Permalink"></a></h2><p>A common use of variables is giving names to specific, unchanging values. Such variables are only assigned once. This intent can be conveyed to the compiler using the <a href="../base/base.html#const"><code>const</code></a> keyword:</p><pre><code class="language-julia-repl hljs">julia&gt; const e  = 2.71828182845904523536;

julia&gt; const pi = 3.14159265358979323846;</code></pre><p>Multiple variables can be declared in a single <code>const</code> statement:</p><pre><code class="language-julia-repl hljs">julia&gt; const a, b = 1, 2
(1, 2)</code></pre><p>The <code>const</code> declaration should only be used in global scope on globals. It is difficult for the compiler to optimize code involving global variables, since their values (or even their types) might change at almost any time. If a global variable will not change, adding a <code>const</code> declaration solves this performance problem.</p><p>Local constants are quite different. The compiler is able to determine automatically when a local variable is constant, so local constant declarations are not necessary, and in fact are currently not supported.</p><p>Special top-level assignments, such as those performed by the <code>function</code> and <code>struct</code> keywords, are constant by default.</p><p>Note that <code>const</code> only affects the variable binding; the variable may be bound to a mutable object (such as an array), and that object may still be modified. Additionally when one tries to assign a value to a variable that is declared constant the following scenarios are possible:</p><ul><li>if a new value has a different type than the type of the constant then an error is thrown:</li></ul><pre><code class="language-julia-repl hljs">julia&gt; const x = 1.0
1.0

julia&gt; x = 1
ERROR: invalid redefinition of constant x</code></pre><ul><li>if a new value has the same type as the constant then a warning is printed:</li></ul><pre><code class="language-julia-repl hljs">julia&gt; const y = 1.0
1.0

julia&gt; y = 2.0
WARNING: redefinition of constant y. This may fail, cause incorrect answers, or produce other errors.
2.0</code></pre><ul><li>if an assignment would not result in the change of variable value no message is given:</li></ul><pre><code class="language-julia-repl hljs">julia&gt; const z = 100
100

julia&gt; z = 100
100</code></pre><p>The last rule applies to immutable objects even if the variable binding would change, e.g.:</p><pre><code class="language-julia-repl hljs">julia&gt; const s1 = &quot;1&quot;
&quot;1&quot;

julia&gt; s2 = &quot;1&quot;
&quot;1&quot;

julia&gt; pointer.([s1, s2], 1)
2-element Array{Ptr{UInt8},1}:
 Ptr{UInt8} @0x00000000132c9638
 Ptr{UInt8} @0x0000000013dd3d18

julia&gt; s1 = s2
&quot;1&quot;

julia&gt; pointer.([s1, s2], 1)
2-element Array{Ptr{UInt8},1}:
 Ptr{UInt8} @0x0000000013dd3d18
 Ptr{UInt8} @0x0000000013dd3d18</code></pre><p>However, for mutable objects the warning is printed as expected:</p><pre><code class="language-julia-repl hljs">julia&gt; const a = [1]
1-element Vector{Int64}:
 1

julia&gt; a = [1]
WARNING: redefinition of constant a. This may fail, cause incorrect answers, or produce other errors.
1-element Vector{Int64}:
 1</code></pre><p>Note that although sometimes possible, changing the value of a <code>const</code> variable is strongly discouraged, and is intended only for convenience during interactive use. Changing constants can cause various problems or unexpected behaviors. For instance, if a method references a constant and is already compiled before the constant is changed, then it might keep using the old value:</p><pre><code class="language-julia-repl hljs">julia&gt; const x = 1
1

julia&gt; f() = x
f (generic function with 1 method)

julia&gt; f()
1

julia&gt; x = 2
WARNING: redefinition of constant x. This may fail, cause incorrect answers, or produce other errors.
2

julia&gt; f()
1</code></pre><h2 id="man-typed-globals"><a class="docs-heading-anchor" href="#man-typed-globals">Typed Globals</a><a id="man-typed-globals-1"></a><a class="docs-heading-anchor-permalink" href="#man-typed-globals" title="Permalink"></a></h2><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>Support for typed globals was added in Julia 1.8</p></div></div><p>Similar to being declared as constants, global bindings can also be declared to always be of a constant type. This can either be done without assigning an actual value using the syntax <code>global x::T</code> or upon assignment as <code>x::T = 123</code>.</p><pre><code class="language-julia-repl hljs">julia&gt; x::Float64 = 2.718
2.718

julia&gt; f() = x
f (generic function with 1 method)

julia&gt; Base.return_types(f)
1-element Vector{Any}:
 Float64</code></pre><p>For any assignment to a global, Julia will first try to convert it to the appropriate type using <a href="../base/base.html#Base.convert"><code>convert</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; global y::Int

julia&gt; y = 1.0
1.0

julia&gt; y
1

julia&gt; y = 3.14
ERROR: InexactError: Int64(3.14)
Stacktrace:
[...]</code></pre><p>The type does not need to be concrete, but annotations with abstract types typically have little performance benefit.</p><p>Once a global has either been assigned to or its type has been set, the binding type is not allowed to change:</p><pre><code class="language-julia-repl hljs">julia&gt; x = 1
1

julia&gt; global x::Int
ERROR: cannot set type for global x. It already has a value or is already set to a different type.
Stacktrace:
[...]</code></pre></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="control-flow.html">« Control Flow</a><a class="docs-footer-nextpage" href="types.html">Types »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
