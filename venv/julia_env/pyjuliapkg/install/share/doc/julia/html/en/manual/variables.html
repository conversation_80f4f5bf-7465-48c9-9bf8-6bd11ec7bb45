<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Variables · The Julia Language</title><meta name="title" content="Variables · The Julia Language"/><meta property="og:title" content="Variables · The Julia Language"/><meta property="twitter:title" content="Variables · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li class="is-active"><a class="tocitem" href="variables.html">Variables</a><ul class="internal"><li><a class="tocitem" href="#man-allowed-variable-names"><span>Allowed Variable Names</span></a></li><li><a class="tocitem" href="#man-assignment-expressions"><span>Assignment expressions and assignment versus mutation</span></a></li><li><a class="tocitem" href="#Stylistic-Conventions"><span>Stylistic Conventions</span></a></li></ul></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="variables.html">Variables</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="variables.html">Variables</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/variables.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="man-variables"><a class="docs-heading-anchor" href="#man-variables">Variables</a><a id="man-variables-1"></a><a class="docs-heading-anchor-permalink" href="#man-variables" title="Permalink"></a></h1><p>A variable, in Julia, is a name associated (or bound) to a value. It&#39;s useful when you want to store a value (that you obtained after some math, for example) for later use. For example:</p><pre><code class="language-julia-repl hljs"># Assign the value 10 to the variable x
julia&gt; x = 10
10

# Doing math with x&#39;s value
julia&gt; x + 1
11

# Reassign x&#39;s value
julia&gt; x = 1 + 1
2

# You can assign values of other types, like strings of text
julia&gt; x = &quot;Hello World!&quot;
&quot;Hello World!&quot;</code></pre><p>Julia provides an extremely flexible system for naming variables. Variable names are case-sensitive, and have no semantic meaning (that is, the language will not treat variables differently based on their names).</p><pre><code class="language-julia-repl hljs">julia&gt; x = 1.0
1.0

julia&gt; y = -3
-3

julia&gt; Z = &quot;My string&quot;
&quot;My string&quot;

julia&gt; customary_phrase = &quot;Hello world!&quot;
&quot;Hello world!&quot;

julia&gt; UniversalDeclarationOfHumanRightsStart = &quot;人人生而自由，在尊严和权利上一律平等。&quot;
&quot;人人生而自由，在尊严和权利上一律平等。&quot;</code></pre><p>Unicode names (in UTF-8 encoding) are allowed:</p><pre><code class="language-julia-repl hljs">julia&gt; δ = 0.00001
1.0e-5

julia&gt; 안녕하세요 = &quot;Hello&quot;
&quot;Hello&quot;</code></pre><p>In the Julia REPL and several other Julia editing environments, you can type many Unicode math symbols by typing the backslashed LaTeX symbol name followed by tab. For example, the variable name <code>δ</code> can be entered by typing <code>\delta</code>-<em>tab</em>, or even <code>α̂⁽²⁾</code> by <code>\alpha</code>-<em>tab</em>-<code>\hat</code>- <em>tab</em>-<code>\^(2)</code>-<em>tab</em>. (If you find a symbol somewhere, e.g. in someone else&#39;s code, that you don&#39;t know how to type, the REPL help will tell you: just type <code>?</code> and then paste the symbol.)</p><p>Julia will even let you shadow existing exported constants and functions with local ones (although this is not recommended to avoid potential confusions):</p><pre><code class="language-julia-repl hljs">julia&gt; pi = 3
3

julia&gt; pi
3

julia&gt; sqrt = 4
4

julia&gt; length() = 5
length (generic function with 1 method)

julia&gt; Base.length
length (generic function with 79 methods)</code></pre><p>However, if you try to redefine a built-in constant or function already in use, Julia will give you an error:</p><pre><code class="language-julia-repl hljs">julia&gt; pi
π = 3.1415926535897...

julia&gt; pi = 3
ERROR: cannot assign a value to imported variable Base.pi from module Main

julia&gt; sqrt(100)
10.0

julia&gt; sqrt = 4
ERROR: cannot assign a value to imported variable Base.sqrt from module Main</code></pre><h2 id="man-allowed-variable-names"><a class="docs-heading-anchor" href="#man-allowed-variable-names">Allowed Variable Names</a><a id="man-allowed-variable-names-1"></a><a class="docs-heading-anchor-permalink" href="#man-allowed-variable-names" title="Permalink"></a></h2><p>Variable names must begin with a letter (A-Z or a-z), underscore, or a subset of Unicode code points greater than 00A0; in particular, <a href="https://www.fileformat.info/info/unicode/category/index.htm">Unicode character categories</a> Lu/Ll/Lt/Lm/Lo/Nl (letters), Sc/So (currency and other symbols), and a few other letter-like characters (e.g. a subset of the Sm math symbols) are allowed. Subsequent characters may also include ! and digits (0-9 and other characters in categories Nd/No), as well as other Unicode code points: diacritics and other modifying marks (categories Mn/Mc/Me/Sk), some punctuation connectors (category Pc), primes, and a few other characters.</p><p>Operators like <code>+</code> are also valid identifiers, but are parsed specially. In some contexts, operators can be used just like variables; for example <code>(+)</code> refers to the addition function, and <code>(+) = f</code> will reassign it. Most of the Unicode infix operators (in category Sm), such as <code>⊕</code>, are parsed as infix operators and are available for user-defined methods (e.g. you can use <code>const ⊗ = kron</code> to define <code>⊗</code> as an infix Kronecker product).  Operators can also be suffixed with modifying marks, primes, and sub/superscripts, e.g. <code>+̂ₐ″</code> is parsed as an infix operator with the same precedence as <code>+</code>. A space is required between an operator that ends with a subscript/superscript letter and a subsequent variable name. For example, if <code>+ᵃ</code> is an operator, then <code>+ᵃx</code> must be written as <code>+ᵃ x</code> to distinguish it from <code>+ ᵃx</code> where <code>ᵃx</code> is the variable name.</p><p>A particular class of variable names is one that contains only underscores. These identifiers are write-only. I.e. they can only be assigned values, which are immediately discarded, and their values cannot be used in any way.</p><pre><code class="language-julia-repl hljs">julia&gt; x, ___ = size([2 2; 1 1])
(2, 2)

julia&gt; y = ___
ERROR: syntax: all-underscore identifiers are write-only and their values cannot be used in expressions

julia&gt; println(___)
ERROR: syntax: all-underscore identifiers are write-only and their values cannot be used in expressions</code></pre><p>The only explicitly disallowed names for variables are the names of the built-in <a href="../base/base.html#Keywords">Keywords</a>:</p><pre><code class="language-julia-repl hljs">julia&gt; else = false
ERROR: syntax: unexpected &quot;else&quot;

julia&gt; try = &quot;No&quot;
ERROR: syntax: unexpected &quot;=&quot;</code></pre><p>Some Unicode characters are considered to be equivalent in identifiers. Different ways of entering Unicode combining characters (e.g., accents) are treated as equivalent (specifically, Julia identifiers are <a href="https://en.wikipedia.org/wiki/Unicode_equivalence">NFC</a>. Julia also includes a few non-standard equivalences for characters that are visually similar and are easily entered by some input methods. The Unicode characters <code>ɛ</code> (U+025B: Latin small letter open e) and <code>µ</code> (U+00B5: micro sign) are treated as equivalent to the corresponding Greek letters. The middle dot <code>·</code> (U+00B7) and the Greek <a href="https://en.wikipedia.org/wiki/Interpunct">interpunct</a> <code>·</code> (U+0387) are both treated as the mathematical dot operator <code>⋅</code> (U+22C5). The minus sign <code>−</code> (U+2212) is treated as equivalent to the hyphen-minus sign <code>-</code> (U+002D).</p><h2 id="man-assignment-expressions"><a class="docs-heading-anchor" href="#man-assignment-expressions">Assignment expressions and assignment versus mutation</a><a id="man-assignment-expressions-1"></a><a class="docs-heading-anchor-permalink" href="#man-assignment-expressions" title="Permalink"></a></h2><p>An assignment <code>variable = value</code> &quot;binds&quot; the name <code>variable</code> to the <code>value</code> computed on the right-hand side, and the whole assignment is treated by Julia as an expression equal to the right-hand-side <code>value</code>.  This means that assignments can be <em>chained</em> (the same <code>value</code> assigned to multiple variables with <code>variable1 = variable2 = value</code>) or used in other expressions, and is also why their result is shown in the REPL as the value of the right-hand side.  (In general, the REPL displays the value of whatever expression you evaluate.)  For example, here the value <code>4</code> of <code>b = 2+2</code> is used in another arithmetic operation and assignment:</p><pre><code class="language-julia-repl hljs">julia&gt; a = (b = 2+2) + 3
7

julia&gt; a
7

julia&gt; b
4</code></pre><p>A common confusion is the distinction between <em>assignment</em> (giving a new &quot;name&quot; to a value) and <em>mutation</em> (changing a value).  If you run <code>a = 2</code> followed by <code>a = 3</code>, you have changed the &quot;name&quot; <code>a</code> to refer to a new value <code>3</code> … you haven&#39;t changed the number <code>2</code>, so <code>2+2</code> will still give <code>4</code> and not <code>6</code>!   This distinction becomes more clear when dealing with <em>mutable</em> types like <a href="../base/arrays.html#lib-arrays">arrays</a>, whose contents <em>can</em> be changed:</p><pre><code class="language-julia-repl hljs">julia&gt; a = [1,2,3] # an array of 3 integers
3-element Vector{Int64}:
 1
 2
 3

julia&gt; b = a   # both b and a are names for the same array!
3-element Vector{Int64}:
 1
 2
 3</code></pre><p>Here, the line <code>b = a</code> does <em>not</em> make a copy of the array <code>a</code>, it simply binds the name <code>b</code> to the <em>same</em> array <code>a</code>: both <code>b</code> and <code>a</code> &quot;point&quot; to one array <code>[1,2,3]</code> in memory. In contrast, an assignment <code>a[i] = value</code> <em>changes</em> the <em>contents</em> of the array, and the modified array will be visible through both the names <code>a</code> and <code>b</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; a[1] = 42     # change the first element
42

julia&gt; a = 3.14159   # a is now the name of a different object
3.14159

julia&gt; b   # b refers to the original array object, which has been mutated
3-element Vector{Int64}:
 42
  2
  3</code></pre><p>That is, <code>a[i] = value</code> (an alias for <a href="../base/collections.html#Base.setindex!"><code>setindex!</code></a>) <em>mutates</em> an existing array object in memory, accessible via either <code>a</code> or <code>b</code>.  Subsequently setting <code>a = 3.14159</code> does not change this array, it simply binds <code>a</code> to a different object; the array is still accessible via <code>b</code>. Another common syntax to mutate an existing object is <code>a.field = value</code> (an alias for <a href="../base/base.html#Base.setproperty!"><code>setproperty!</code></a>), which can be used to change a <a href="../base/base.html#mutable struct"><code>mutable struct</code></a>.  There is also mutation via dot assignment, for example <code>b .= 5:7</code> (which mutates our array <code>b</code> in-place to contain <code>[5,6,7]</code>), as part of Julia&#39;s <a href="mathematical-operations.html#man-dot-operators">vectorized &quot;dot&quot; syntax</a>.</p><p>When you call a <a href="functions.html#man-functions">function</a> in Julia, it behaves as if you <em>assigned</em> the argument values to new variable names corresponding to the function arguments, as discussed in <a href="functions.html#man-argument-passing">Argument-Passing Behavior</a>.  (By <a href="../base/punctuation.html#man-punctuation">convention</a>, functions that mutate one or more of their arguments have names ending with <code>!</code>.)</p><h2 id="Stylistic-Conventions"><a class="docs-heading-anchor" href="#Stylistic-Conventions">Stylistic Conventions</a><a id="Stylistic-Conventions-1"></a><a class="docs-heading-anchor-permalink" href="#Stylistic-Conventions" title="Permalink"></a></h2><p>While Julia imposes few restrictions on valid names, it has become useful to adopt the following conventions:</p><ul><li>Names of variables are in lower case.</li><li>Word separation can be indicated by underscores (<code>&#39;_&#39;</code>), but use of underscores is discouraged unless the name would be hard to read otherwise.</li><li>Names of <code>Type</code>s and <code>Module</code>s begin with a capital letter and word separation is shown with upper camel case instead of underscores.</li><li>Names of <code>function</code>s and <code>macro</code>s are in lower case, without underscores.</li><li>Functions that write to their arguments have names that end in <code>!</code>. These are sometimes called &quot;mutating&quot; or &quot;in-place&quot; functions because they are intended to produce changes in their arguments after the function is called, not just return a value.</li></ul><p>For more information about stylistic conventions, see the <a href="style-guide.html#Style-Guide">Style Guide</a>.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="installation.html">« Installation</a><a class="docs-footer-nextpage" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
