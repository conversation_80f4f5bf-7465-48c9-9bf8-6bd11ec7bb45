<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Working with LLVM · The Julia Language</title><meta name="title" content="Working with LLVM · The Julia Language"/><meta property="og:title" content="Working with LLVM · The Julia Language"/><meta property="twitter:title" content="Working with LLVM · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox" checked/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="ast.html">Julia ASTs</a></li><li><a class="tocitem" href="types.html">More about types</a></li><li><a class="tocitem" href="object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="functions.html">Julia Functions</a></li><li><a class="tocitem" href="cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="subarrays.html">SubArrays</a></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a></li><li class="is-active"><a class="tocitem" href="llvm.html">Working with LLVM</a><ul class="internal"><li><a class="tocitem" href="#Overview-of-Julia-to-LLVM-Interface"><span>Overview of Julia to LLVM Interface</span></a></li><li><a class="tocitem" href="#Building-Julia-with-a-different-version-of-LLVM"><span>Building Julia with a different version of LLVM</span></a></li><li><a class="tocitem" href="#Passing-options-to-LLVM"><span>Passing options to LLVM</span></a></li><li><a class="tocitem" href="#Debugging-LLVM-transformations-in-isolation"><span>Debugging LLVM transformations in isolation</span></a></li><li><a class="tocitem" href="#Running-the-LLVM-test-suite"><span>Running the LLVM test suite</span></a></li><li><a class="tocitem" href="#Improving-LLVM-optimizations-for-Julia"><span>Improving LLVM optimizations for Julia</span></a></li><li><a class="tocitem" href="#The-jlcall-calling-convention"><span>The jlcall calling convention</span></a></li><li><a class="tocitem" href="#GC-root-placement"><span>GC root placement</span></a></li></ul></li><li><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Documentation of Julia&#39;s Internals</a></li><li class="is-active"><a href="llvm.html">Working with LLVM</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="llvm.html">Working with LLVM</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/llvm.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Working-with-LLVM"><a class="docs-heading-anchor" href="#Working-with-LLVM">Working with LLVM</a><a id="Working-with-LLVM-1"></a><a class="docs-heading-anchor-permalink" href="#Working-with-LLVM" title="Permalink"></a></h1><p>This is not a replacement for the LLVM documentation, but a collection of tips for working on LLVM for Julia.</p><h2 id="Overview-of-Julia-to-LLVM-Interface"><a class="docs-heading-anchor" href="#Overview-of-Julia-to-LLVM-Interface">Overview of Julia to LLVM Interface</a><a id="Overview-of-Julia-to-LLVM-Interface-1"></a><a class="docs-heading-anchor-permalink" href="#Overview-of-Julia-to-LLVM-Interface" title="Permalink"></a></h2><p>Julia dynamically links against LLVM by default. Build with <code>USE_LLVM_SHLIB=0</code> to link statically.</p><p>The code for lowering Julia AST to LLVM IR or interpreting it directly is in directory <code>src/</code>.</p><table><tr><th style="text-align: left">File</th><th style="text-align: left">Description</th></tr><tr><td style="text-align: left"><code>aotcompile.cpp</code></td><td style="text-align: left">Compiler C-interface entry and object file emission</td></tr><tr><td style="text-align: left"><code>builtins.c</code></td><td style="text-align: left">Builtin functions</td></tr><tr><td style="text-align: left"><code>ccall.cpp</code></td><td style="text-align: left">Lowering <a href="../base/c.html#ccall"><code>ccall</code></a></td></tr><tr><td style="text-align: left"><code>cgutils.cpp</code></td><td style="text-align: left">Lowering utilities, notably for array and tuple accesses</td></tr><tr><td style="text-align: left"><code>codegen.cpp</code></td><td style="text-align: left">Top-level of code generation, pass list, lowering builtins</td></tr><tr><td style="text-align: left"><code>debuginfo.cpp</code></td><td style="text-align: left">Tracks debug information for JIT code</td></tr><tr><td style="text-align: left"><code>disasm.cpp</code></td><td style="text-align: left">Handles native object file and JIT code diassembly</td></tr><tr><td style="text-align: left"><code>gf.c</code></td><td style="text-align: left">Generic functions</td></tr><tr><td style="text-align: left"><code>intrinsics.cpp</code></td><td style="text-align: left">Lowering intrinsics</td></tr><tr><td style="text-align: left"><code>jitlayers.cpp</code></td><td style="text-align: left">JIT-specific code, ORC compilation layers/utilities</td></tr><tr><td style="text-align: left"><code>llvm-alloc-helpers.cpp</code></td><td style="text-align: left">Julia-specific escape analysis</td></tr><tr><td style="text-align: left"><code>llvm-alloc-opt.cpp</code></td><td style="text-align: left">Custom LLVM pass to demote heap allocations to the stack</td></tr><tr><td style="text-align: left"><code>llvm-cpufeatures.cpp</code></td><td style="text-align: left">Custom LLVM pass to lower CPU-based functions (e.g. haveFMA)</td></tr><tr><td style="text-align: left"><code>llvm-demote-float16.cpp</code></td><td style="text-align: left">Custom LLVM pass to lower 16b float ops to 32b float ops</td></tr><tr><td style="text-align: left"><code>llvm-final-gc-lowering.cpp</code></td><td style="text-align: left">Custom LLVM pass to lower GC calls to their final form</td></tr><tr><td style="text-align: left"><code>llvm-gc-invariant-verifier.cpp</code></td><td style="text-align: left">Custom LLVM pass to verify Julia GC invariants</td></tr><tr><td style="text-align: left"><code>llvm-julia-licm.cpp</code></td><td style="text-align: left">Custom LLVM pass to hoist/sink Julia-specific intrinsics</td></tr><tr><td style="text-align: left"><code>llvm-late-gc-lowering.cpp</code></td><td style="text-align: left">Custom LLVM pass to root GC-tracked values</td></tr><tr><td style="text-align: left"><code>llvm-lower-handlers.cpp</code></td><td style="text-align: left">Custom LLVM pass to lower try-catch blocks</td></tr><tr><td style="text-align: left"><code>llvm-muladd.cpp</code></td><td style="text-align: left">Custom LLVM pass for fast-match FMA</td></tr><tr><td style="text-align: left"><code>llvm-multiversioning.cpp</code></td><td style="text-align: left">Custom LLVM pass to generate sysimg code on multiple architectures</td></tr><tr><td style="text-align: left"><code>llvm-propagate-addrspaces.cpp</code></td><td style="text-align: left">Custom LLVM pass to canonicalize addrspaces</td></tr><tr><td style="text-align: left"><code>llvm-ptls.cpp</code></td><td style="text-align: left">Custom LLVM pass to lower TLS operations</td></tr><tr><td style="text-align: left"><code>llvm-remove-addrspaces.cpp</code></td><td style="text-align: left">Custom LLVM pass to remove Julia addrspaces</td></tr><tr><td style="text-align: left"><code>llvm-remove-ni.cpp</code></td><td style="text-align: left">Custom LLVM pass to remove Julia non-integral addrspaces</td></tr><tr><td style="text-align: left"><code>llvm-simdloop.cpp</code></td><td style="text-align: left">Custom LLVM pass for <a href="../base/base.html#Base.SimdLoop.@simd"><code>@simd</code></a></td></tr><tr><td style="text-align: left"><code>pipeline.cpp</code></td><td style="text-align: left">New pass manager pipeline, pass pipeline parsing</td></tr><tr><td style="text-align: left"><code>sys.c</code></td><td style="text-align: left">I/O and operating system utility functions</td></tr></table><p>Some of the <code>.cpp</code> files form a group that compile to a single object.</p><p>The difference between an intrinsic and a builtin is that a builtin is a first class function that can be used like any other Julia function.  An intrinsic can operate only on unboxed data, and therefore its arguments must be statically typed.</p><h3 id="LLVM-Alias-Analysis"><a class="docs-heading-anchor" href="#LLVM-Alias-Analysis">Alias Analysis</a><a id="LLVM-Alias-Analysis-1"></a><a class="docs-heading-anchor-permalink" href="#LLVM-Alias-Analysis" title="Permalink"></a></h3><p>Julia currently uses LLVM&#39;s <a href="https://llvm.org/docs/LangRef.html#tbaa-metadata">Type Based Alias Analysis</a>. To find the comments that document the inclusion relationships, look for <code>static MDNode*</code> in <code>src/codegen.cpp</code>.</p><p>The <code>-O</code> option enables LLVM&#39;s <a href="https://llvm.org/docs/AliasAnalysis.html#the-basic-aa-pass">Basic Alias Analysis</a>.</p><h2 id="Building-Julia-with-a-different-version-of-LLVM"><a class="docs-heading-anchor" href="#Building-Julia-with-a-different-version-of-LLVM">Building Julia with a different version of LLVM</a><a id="Building-Julia-with-a-different-version-of-LLVM-1"></a><a class="docs-heading-anchor-permalink" href="#Building-Julia-with-a-different-version-of-LLVM" title="Permalink"></a></h2><p>The default version of LLVM is specified in <code>deps/llvm.version</code>. You can override it by creating a file called <code>Make.user</code> in the top-level directory and adding a line to it such as:</p><pre><code class="nohighlight hljs">LLVM_VER = 13.0.0</code></pre><p>Besides the LLVM release numerals, you can also use <code>DEPS_GIT = llvm</code> in combination with <code>USE_BINARYBUILDER_LLVM = 0</code> to build against the latest development version of LLVM.</p><p>You can also specify to build a debug version of LLVM, by setting either <code>LLVM_DEBUG = 1</code> or <code>LLVM_DEBUG = Release</code> in your <code>Make.user</code> file. The former will be a fully unoptimized build of LLVM and the latter will produce an optimized build of LLVM. Depending on your needs the latter will suffice and it quite a bit faster. If you use <code>LLVM_DEBUG = Release</code> you will also want to set <code>LLVM_ASSERTIONS = 1</code> to enable diagnostics for different passes. Only <code>LLVM_DEBUG = 1</code> implies that option by default.</p><h2 id="Passing-options-to-LLVM"><a class="docs-heading-anchor" href="#Passing-options-to-LLVM">Passing options to LLVM</a><a id="Passing-options-to-LLVM-1"></a><a class="docs-heading-anchor-permalink" href="#Passing-options-to-LLVM" title="Permalink"></a></h2><p>You can pass options to LLVM via the environment variable <a href="../manual/environment-variables.html#JULIA_LLVM_ARGS"><code>JULIA_LLVM_ARGS</code></a>. Here are example settings using <code>bash</code> syntax:</p><ul><li><code>export JULIA_LLVM_ARGS=-print-after-all</code> dumps IR after each pass.</li><li><code>export JULIA_LLVM_ARGS=-debug-only=loop-vectorize</code> dumps LLVM <code>DEBUG(...)</code> diagnostics for loop vectorizer. If you get warnings about &quot;Unknown command line argument&quot;, rebuild LLVM with <code>LLVM_ASSERTIONS = 1</code>.</li><li><code>export JULIA_LLVM_ARGS=-help</code> shows a list of available options. <code>export JULIA_LLVM_ARGS=-help-hidden</code> shows even more.</li><li><code>export JULIA_LLVM_ARGS=&quot;-fatal-warnings -print-options&quot;</code> is an example how to use multiple options.</li></ul><h3 id="Useful-JULIA_LLVM_ARGS-parameters"><a class="docs-heading-anchor" href="#Useful-JULIA_LLVM_ARGS-parameters">Useful <code>JULIA_LLVM_ARGS</code> parameters</a><a id="Useful-JULIA_LLVM_ARGS-parameters-1"></a><a class="docs-heading-anchor-permalink" href="#Useful-JULIA_LLVM_ARGS-parameters" title="Permalink"></a></h3><ul><li><code>-print-after=PASS</code>: prints the IR after any execution of <code>PASS</code>, useful for checking changes done by a pass.</li><li><code>-print-before=PASS</code>: prints the IR before any execution of <code>PASS</code>, useful for checking the input to a pass.</li><li><code>-print-changed</code>: prints the IR whenever a pass changes the IR, useful for narrowing down which passes are causing problems.</li><li><code>-print-(before|after)=MARKER-PASS</code>: the Julia pipeline ships with a number of marker passes in the pipeline, which can be used to identify where problems or optimizations are occurring. A marker pass is defined as a pass which appears once in the pipeline and performs no transformations on the IR, and is only useful for targeting print-before/print-after. Currently, the following marker passes exist in the pipeline:<ul><li>BeforeOptimization</li><li>BeforeEarlySimplification</li><li>AfterEarlySimplification</li><li>BeforeEarlyOptimization</li><li>AfterEarlyOptimization</li><li>BeforeLoopOptimization</li><li>BeforeLICM</li><li>AfterLICM</li><li>BeforeLoopSimplification</li><li>AfterLoopSimplification</li><li>AfterLoopOptimization</li><li>BeforeScalarOptimization</li><li>AfterScalarOptimization</li><li>BeforeVectorization</li><li>AfterVectorization</li><li>BeforeIntrinsicLowering</li><li>AfterIntrinsicLowering</li><li>BeforeCleanup</li><li>AfterCleanup</li><li>AfterOptimization</li></ul></li><li><code>-time-passes</code>: prints the time spent in each pass, useful for identifying which passes are taking a long time.</li><li><code>-print-module-scope</code>: used in conjunction with <code>-print-(before|after)</code>, gets the entire module rather than the IR unit received by the pass</li><li><code>-debug</code>: prints out a lot of debugging information throughout LLVM</li><li><code>-debug-only=NAME</code>, prints out debugging statements from files with <code>DEBUG_TYPE</code> defined to <code>NAME</code>, useful for getting additional context about a problem</li></ul><h2 id="Debugging-LLVM-transformations-in-isolation"><a class="docs-heading-anchor" href="#Debugging-LLVM-transformations-in-isolation">Debugging LLVM transformations in isolation</a><a id="Debugging-LLVM-transformations-in-isolation-1"></a><a class="docs-heading-anchor-permalink" href="#Debugging-LLVM-transformations-in-isolation" title="Permalink"></a></h2><p>On occasion, it can be useful to debug LLVM&#39;s transformations in isolation from the rest of the Julia system, e.g. because reproducing the issue inside <code>julia</code> would take too long, or because one wants to take advantage of LLVM&#39;s tooling (e.g. bugpoint).</p><p>To start with, you can install the developer tools to work with LLVM via:</p><pre><code class="nohighlight hljs">make -C deps install-llvm-tools</code></pre><p>To get unoptimized IR for the entire system image, pass the <code>--output-unopt-bc unopt.bc</code> option to the system image build process, which will output the unoptimized IR to an <code>unopt.bc</code> file. This file can then be passed to LLVM tools as usual. <code>libjulia</code> can function as an LLVM pass plugin and can be loaded into LLVM tools, to make julia-specific passes available in this environment. In addition, it exposes the <code>-julia</code> meta-pass, which runs the entire Julia pass-pipeline over the IR. As an example, to generate a system image with the old pass manager, one could do:</p><pre><code class="nohighlight hljs">
llc -o sys.o opt.bc
cc -shared -o sys.so sys.o</code></pre><p>To generate a system image with the new pass manager, one could do:</p><pre><code class="nohighlight hljs">opt -load-pass-plugin=libjulia-codegen.so --passes=&#39;julia&#39; -o opt.bc unopt.bc
llc -o sys.o opt.bc
cc -shared -o sys.so sys.o</code></pre><p>This system image can then be loaded by <code>julia</code> as usual.</p><p>It is also possible to dump an LLVM IR module for just one Julia function, using:</p><pre><code class="language-julia hljs">fun, T = +, Tuple{Int,Int} # Substitute your function of interest here
optimize = false
open(&quot;plus.ll&quot;, &quot;w&quot;) do file
    println(file, InteractiveUtils._dump_function(fun, T, false, false, false, true, :att, optimize, :default, false))
end</code></pre><p>These files can be processed the same way as the unoptimized sysimg IR shown above.</p><h2 id="Running-the-LLVM-test-suite"><a class="docs-heading-anchor" href="#Running-the-LLVM-test-suite">Running the LLVM test suite</a><a id="Running-the-LLVM-test-suite-1"></a><a class="docs-heading-anchor-permalink" href="#Running-the-LLVM-test-suite" title="Permalink"></a></h2><p>To run the llvm tests locally, you need to first install the tools, build julia, then you can run the tests:</p><pre><code class="nohighlight hljs">make -C deps install-llvm-tools
make -j julia-src-release
make -C test/llvmpasses</code></pre><p>If you want to run the individual test files directly, via the commands at the top of each test file, the first step here will have installed the tools into <code>./usr/tools/opt</code>. Then you&#39;ll want to manually replace <code>%s</code> with the name of the test file.</p><h2 id="Improving-LLVM-optimizations-for-Julia"><a class="docs-heading-anchor" href="#Improving-LLVM-optimizations-for-Julia">Improving LLVM optimizations for Julia</a><a id="Improving-LLVM-optimizations-for-Julia-1"></a><a class="docs-heading-anchor-permalink" href="#Improving-LLVM-optimizations-for-Julia" title="Permalink"></a></h2><p>Improving LLVM code generation usually involves either changing Julia lowering to be more friendly to LLVM&#39;s passes, or improving a pass.</p><p>If you are planning to improve a pass, be sure to read the <a href="https://llvm.org/docs/DeveloperPolicy.html">LLVM developer policy</a>. The best strategy is to create a code example in a form where you can use LLVM&#39;s <code>opt</code> tool to study it and the pass of interest in isolation.</p><ol><li>Create an example Julia code of interest.</li><li>Use <code>JULIA_LLVM_ARGS=-print-after-all</code> to dump the IR.</li><li>Pick out the IR at the point just before the pass of interest runs.</li><li>Strip the debug metadata and fix up the TBAA metadata by hand.</li></ol><p>The last step is labor intensive.  Suggestions on a better way would be appreciated.</p><h2 id="The-jlcall-calling-convention"><a class="docs-heading-anchor" href="#The-jlcall-calling-convention">The jlcall calling convention</a><a id="The-jlcall-calling-convention-1"></a><a class="docs-heading-anchor-permalink" href="#The-jlcall-calling-convention" title="Permalink"></a></h2><p>Julia has a generic calling convention for unoptimized code, which looks somewhat as follows:</p><pre><code class="language-c hljs">jl_value_t *any_unoptimized_call(jl_value_t *, jl_value_t **, int);</code></pre><p>where the first argument is the boxed function object, the second argument is an on-stack array of arguments and the third is the number of arguments. Now, we could perform a straightforward lowering and emit an alloca for the argument array. However, this would betray the SSA nature of the uses at the call site, making optimizations (including GC root placement), significantly harder. Instead, we emit it as follows:</p><pre><code class="language-llvm hljs">call %jl_value_t *@julia.call(jl_value_t *(*)(...) @any_unoptimized_call, %jl_value_t *%arg1, %jl_value_t *%arg2)</code></pre><p>This allows us to retain the SSA-ness of the uses throughout the optimizer. GC root placement will later lower this call to the original C ABI.</p><h2 id="GC-root-placement"><a class="docs-heading-anchor" href="#GC-root-placement">GC root placement</a><a id="GC-root-placement-1"></a><a class="docs-heading-anchor-permalink" href="#GC-root-placement" title="Permalink"></a></h2><p>GC root placement is done by an LLVM pass late in the pass pipeline. Doing GC root placement this late enables LLVM to make more aggressive optimizations around code that requires GC roots, as well as allowing us to reduce the number of required GC roots and GC root store operations (since LLVM doesn&#39;t understand our GC, it wouldn&#39;t otherwise know what it is and is not allowed to do with values stored to the GC frame, so it&#39;ll conservatively do very little). As an example, consider an error path</p><pre><code class="language-julia hljs">if some_condition()
    #= Use some variables maybe =#
    error(&quot;An error occurred&quot;)
end</code></pre><p>During constant folding, LLVM may discover that the condition is always false, and can remove the basic block. However, if GC root lowering is done early, the GC root slots used in the deleted block, as well as any values kept alive in those slots only because they were used in the error path, would be kept alive by LLVM. By doing GC root lowering late, we give LLVM the license to do any of its usual optimizations (constant folding, dead code elimination, etc.), without having to worry (too much) about which values may or may not be GC tracked.</p><p>However, in order to be able to do late GC root placement, we need to be able to identify a) which pointers are GC tracked and b) all uses of such pointers. The goal of the GC placement pass is thus simple:</p><p>Minimize the number of needed GC roots/stores to them subject to the constraint that at every safepoint, any live GC-tracked pointer (i.e. for which there is a path after this point that contains a use of this pointer) is in some GC slot.</p><h3 id="Representation"><a class="docs-heading-anchor" href="#Representation">Representation</a><a id="Representation-1"></a><a class="docs-heading-anchor-permalink" href="#Representation" title="Permalink"></a></h3><p>The primary difficulty is thus choosing an IR representation that allows us to identify GC-tracked pointers and their uses, even after the program has been run through the optimizer. Our design makes use of three LLVM features to achieve this:</p><ul><li>Custom address spaces</li><li>Operand Bundles</li><li>Non-integral pointers</li></ul><p>Custom address spaces allow us to tag every point with an integer that needs to be preserved through optimizations. The compiler may not insert casts between address spaces that did not exist in the original program and it must never change the address space of a pointer on a load/store/etc operation. This allows us to annotate which pointers are GC-tracked in an optimizer-resistant way. Note that metadata would not be able to achieve the same purpose. Metadata is supposed to always be discardable without altering the semantics of the program. However, failing to identify a GC-tracked pointer alters the resulting program behavior dramatically - it&#39;ll probably crash or return wrong results. We currently use three different address spaces (their numbers are defined in <code>src/codegen_shared.cpp</code>):</p><ul><li>GC Tracked Pointers (currently 10): These are pointers to boxed values that may be put into a GC frame. It is loosely equivalent to a <code>jl_value_t*</code> pointer on the C side. N.B. It is illegal to ever have a pointer in this address space that may not be stored to a GC slot.</li><li>Derived Pointers (currently 11): These are pointers that are derived from some GC tracked pointer. Uses of these pointers generate uses of the original pointer. However, they need not themselves be known to the GC. The GC root placement pass MUST always find the GC tracked pointer from which this pointer is derived and use that as the pointer to root.</li><li>Callee Rooted Pointers (currently 12): This is a utility address space to express the notion of a callee rooted value. All values of this address space MUST be storable to a GC root (though it is possible to relax this condition in the future), but unlike the other pointers need not be rooted if passed to a call (they do still need to be rooted if they are live across another safepoint between the definition and the call).</li><li>Pointers loaded from tracked object (currently 13): This is used by arrays, which themselves contain a pointer to the managed data. This data area is owned by the array, but is not a GC-tracked object by itself. The compiler guarantees that as long as this pointer is live, the object that this pointer was loaded from will keep being live.</li></ul><h3 id="Invariants"><a class="docs-heading-anchor" href="#Invariants">Invariants</a><a id="Invariants-1"></a><a class="docs-heading-anchor-permalink" href="#Invariants" title="Permalink"></a></h3><p>The GC root placement pass makes use of several invariants, which need to be observed by the frontend and are preserved by the optimizer.</p><p>First, only the following address space casts are allowed:</p><ul><li>0-&gt;{Tracked,Derived,CalleeRooted}: It is allowable to decay an untracked pointer to any of the others. However, do note that the optimizer has broad license to not root such a value. It is never safe to have a value in address space 0 in any part of the program if it is (or is derived from) a value that requires a GC root.</li><li>Tracked-&gt;Derived: This is the standard decay route for interior values. The placement pass will look for these to identify the base pointer for any use.</li><li>Tracked-&gt;CalleeRooted: Addrspace CalleeRooted serves merely as a hint that a GC root is not required. However, do note that the Derived-&gt;CalleeRooted decay is prohibited, since pointers should generally be storable to a GC slot, even in this address space.</li></ul><p>Now let us consider what constitutes a use:</p><ul><li>Loads whose loaded values is in one of the address spaces</li><li>Stores of a value in one of the address spaces to a location</li><li>Stores to a pointer in one of the address spaces</li><li>Calls for which a value in one of the address spaces is an operand</li><li>Calls in jlcall ABI, for which the argument array contains a value</li><li>Return instructions.</li></ul><p>We explicitly allow load/stores and simple calls in address spaces Tracked/Derived. Elements of jlcall argument arrays must always be in address space Tracked (it is required by the ABI that they are valid <code>jl_value_t*</code> pointers). The same is true for return instructions (though note that struct return arguments are allowed to have any of the address spaces). The only allowable use of an address space CalleeRooted pointer is to pass it to a call (which must have an appropriately typed operand).</p><p>Further, we disallow <code>getelementptr</code> in addrspace Tracked. This is because unless the operation is a noop, the resulting pointer will not be validly storable to a GC slot and may thus not be in this address space. If such a pointer is required, it should be decayed to addrspace Derived first.</p><p>Lastly, we disallow <code>inttoptr</code>/<code>ptrtoint</code> instructions in these address spaces. Having these instructions would mean that some <code>i64</code> values are really GC tracked. This is problematic, because it breaks that stated requirement that we&#39;re able to identify GC-relevant pointers. This invariant is accomplished using the LLVM &quot;non-integral pointers&quot; feature, which is new in LLVM 5.0. It prohibits the optimizer from making optimizations that would introduce these operations. Note we can still insert static constants at JIT time by using <code>inttoptr</code> in address space 0 and then decaying to the appropriate address space afterwards.</p><h3 id="Supporting-[ccall](@ref)"><a class="docs-heading-anchor" href="#Supporting-[ccall](@ref)">Supporting <a href="../base/c.html#ccall"><code>ccall</code></a></a><a id="Supporting-[ccall](@ref)-1"></a><a class="docs-heading-anchor-permalink" href="#Supporting-[ccall](@ref)" title="Permalink"></a></h3><p>One important aspect missing from the discussion so far is the handling of <a href="../base/c.html#ccall"><code>ccall</code></a>. <a href="../base/c.html#ccall"><code>ccall</code></a> has the peculiar feature that the location and scope of a use do not coincide. As an example consider:</p><pre><code class="language-julia hljs">A = randn(1024)
ccall(:foo, Cvoid, (Ptr{Float64},), A)</code></pre><p>In lowering, the compiler will insert a conversion from the array to the pointer which drops the reference to the array value. However, we of course need to make sure that the array does stay alive while we&#39;re doing the <a href="../base/c.html#ccall"><code>ccall</code></a>. To understand how this is done, lets look at a hypothetical approximate possible lowering of the above code:</p><pre><code class="language-julia hljs">return $(Expr(:foreigncall, :(:foo), Cvoid, svec(Ptr{Float64}), 0, :(:ccall), Expr(:foreigncall, :(:jl_array_ptr), Ptr{Float64}, svec(Any), 0, :(:ccall), :(A)), :(A)))</code></pre><p>The last <code>:(A)</code>, is an extra argument list inserted during lowering that informs the code generator which Julia level values need to be kept alive for the duration of this <a href="../base/c.html#ccall"><code>ccall</code></a>. We then take this information and represent it in an &quot;operand bundle&quot; at the IR level. An operand bundle is essentially a fake use that is attached to the call site. At the IR level, this looks like so:</p><pre><code class="language-llvm hljs">call void inttoptr (i64 ... to void (double*)*)(double* %5) [ &quot;jl_roots&quot;(%jl_value_t addrspace(10)* %A) ]</code></pre><p>The GC root placement pass will treat the <code>jl_roots</code> operand bundle as if it were a regular operand. However, as a final step, after the GC roots are inserted, it will drop the operand bundle to avoid confusing instruction selection.</p><h3 id="Supporting-[pointer_from_objref](@ref)"><a class="docs-heading-anchor" href="#Supporting-[pointer_from_objref](@ref)">Supporting <a href="../base/c.html#Base.pointer_from_objref"><code>pointer_from_objref</code></a></a><a id="Supporting-[pointer_from_objref](@ref)-1"></a><a class="docs-heading-anchor-permalink" href="#Supporting-[pointer_from_objref](@ref)" title="Permalink"></a></h3><p><a href="../base/c.html#Base.pointer_from_objref"><code>pointer_from_objref</code></a> is special because it requires the user to take explicit control of GC rooting. By our above invariants, this function is illegal, because it performs an address space cast from 10 to 0. However, it can be useful, in certain situations, so we provide a special intrinsic:</p><pre><code class="language-llvm hljs">declared %jl_value_t *julia.pointer_from_objref(%jl_value_t addrspace(10)*)</code></pre><p>which is lowered to the corresponding address space cast after GC root lowering. Do note however that by using this intrinsic, the caller assumes all responsibility for making sure that the value in question is rooted. Further this intrinsic is not considered a use, so the GC root placement pass will not provide a GC root for the function. As a result, the external rooting must be arranged while the value is still tracked by the system. I.e. it is not valid to attempt to use the result of this operation to establish a global root - the optimizer may have already dropped the value.</p><h3 id="Keeping-values-alive-in-the-absence-of-uses"><a class="docs-heading-anchor" href="#Keeping-values-alive-in-the-absence-of-uses">Keeping values alive in the absence of uses</a><a id="Keeping-values-alive-in-the-absence-of-uses-1"></a><a class="docs-heading-anchor-permalink" href="#Keeping-values-alive-in-the-absence-of-uses" title="Permalink"></a></h3><p>In certain cases it is necessary to keep an object alive, even though there is no compiler-visible use of said object. This may be case for low level code that operates on the memory-representation of an object directly or code that needs to interface with C code. In order to allow this, we provide the following intrinsics at the LLVM level:</p><pre><code class="nohighlight hljs">token @llvm.julia.gc_preserve_begin(...)
void @llvm.julia.gc_preserve_end(token)</code></pre><p>(The <code>llvm.</code> in the name is required in order to be able to use the <code>token</code> type). The semantics of these intrinsics are as follows: At any safepoint that is dominated by a <code>gc_preserve_begin</code> call, but that is not not dominated by a corresponding <code>gc_preserve_end</code> call (i.e. a call whose argument is the token returned by a <code>gc_preserve_begin</code> call), the values passed as arguments to that <code>gc_preserve_begin</code> will be kept live. Note that the <code>gc_preserve_begin</code> still counts as a regular use of those values, so the standard lifetime semantics will ensure that the values will be kept alive before entering the preserve region.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="llvm-passes.html">« Custom LLVM Passes</a><a class="docs-footer-nextpage" href="stdio.html">printf() and stdio in the Julia runtime »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
