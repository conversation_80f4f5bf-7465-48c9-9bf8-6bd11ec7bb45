<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Statistics · The Julia Language</title><meta name="title" content="Statistics · The Julia Language"/><meta property="og:title" content="Statistics · The Julia Language"/><meta property="twitter:title" content="Statistics · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li class="is-active"><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="Statistics.html">Statistics</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="Statistics.html">Statistics</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaStats/Statistics.jl/blob/master/docs/src/index.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Statistics"><a class="docs-heading-anchor" href="#Statistics">Statistics</a><a id="Statistics-1"></a><a class="docs-heading-anchor-permalink" href="#Statistics" title="Permalink"></a></h1><p>The Statistics standard library module contains basic statistics functionality.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Statistics.std" href="#Statistics.std"><code>Statistics.std</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">std(itr; corrected::Bool=true, mean=nothing[, dims])</code></pre><p>Compute the sample standard deviation of collection <code>itr</code>.</p><p>The algorithm returns an estimator of the generative distribution&#39;s standard deviation under the assumption that each entry of <code>itr</code> is a sample drawn from the same unknown distribution, with the samples uncorrelated. For arrays, this computation is equivalent to calculating <code>sqrt(sum((itr .- mean(itr)).^2) / (length(itr) - 1))</code>. If <code>corrected</code> is <code>true</code>, then the sum is scaled with <code>n-1</code>, whereas the sum is scaled with <code>n</code> if <code>corrected</code> is <code>false</code> with <code>n</code> the number of elements in <code>itr</code>.</p><p>If <code>itr</code> is an <code>AbstractArray</code>, <code>dims</code> can be provided to compute the standard deviation over dimensions.</p><p>A pre-computed <code>mean</code> may be provided. When <code>dims</code> is specified, <code>mean</code> must be an array with the same shape as <code>mean(itr, dims=dims)</code> (additional trailing singleton dimensions are allowed).</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>If array contains <code>NaN</code> or <a href="../manual/missing.html#missing"><code>missing</code></a> values, the result is also <code>NaN</code> or <code>missing</code> (<code>missing</code> takes precedence if array contains both). Use the <a href="../base/base.html#Base.skipmissing"><code>skipmissing</code></a> function to omit <code>missing</code> entries and compute the standard deviation of non-missing values.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L434-L460">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Statistics.stdm" href="#Statistics.stdm"><code>Statistics.stdm</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">stdm(itr, mean; corrected::Bool=true[, dims])</code></pre><p>Compute the sample standard deviation of collection <code>itr</code>, with known mean(s) <code>mean</code>.</p><p>The algorithm returns an estimator of the generative distribution&#39;s standard deviation under the assumption that each entry of <code>itr</code> is a sample drawn from the same unknown distribution, with the samples uncorrelated. For arrays, this computation is equivalent to calculating <code>sqrt(sum((itr .- mean(itr)).^2) / (length(itr) - 1))</code>. If <code>corrected</code> is <code>true</code>, then the sum is scaled with <code>n-1</code>, whereas the sum is scaled with <code>n</code> if <code>corrected</code> is <code>false</code> with <code>n</code> the number of elements in <code>itr</code>.</p><p>If <code>itr</code> is an <code>AbstractArray</code>, <code>dims</code> can be provided to compute the standard deviation over dimensions. In that case, <code>mean</code> must be an array with the same shape as <code>mean(itr, dims=dims)</code> (additional trailing singleton dimensions are allowed).</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>If array contains <code>NaN</code> or <a href="../manual/missing.html#missing"><code>missing</code></a> values, the result is also <code>NaN</code> or <code>missing</code> (<code>missing</code> takes precedence if array contains both). Use the <a href="../base/base.html#Base.skipmissing"><code>skipmissing</code></a> function to omit <code>missing</code> entries and compute the standard deviation of non-missing values.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L478-L501">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Statistics.var" href="#Statistics.var"><code>Statistics.var</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">var(itr; corrected::Bool=true, mean=nothing[, dims])</code></pre><p>Compute the sample variance of collection <code>itr</code>.</p><p>The algorithm returns an estimator of the generative distribution&#39;s variance under the assumption that each entry of <code>itr</code> is a sample drawn from the same unknown distribution, with the samples uncorrelated. For arrays, this computation is equivalent to calculating <code>sum((itr .- mean(itr)).^2) / (length(itr) - 1)</code>. If <code>corrected</code> is <code>true</code>, then the sum is scaled with <code>n-1</code>, whereas the sum is scaled with <code>n</code> if <code>corrected</code> is <code>false</code> where <code>n</code> is the number of elements in <code>itr</code>.</p><p>If <code>itr</code> is an <code>AbstractArray</code>, <code>dims</code> can be provided to compute the variance over dimensions.</p><p>A pre-computed <code>mean</code> may be provided. When <code>dims</code> is specified, <code>mean</code> must be an array with the same shape as <code>mean(itr, dims=dims)</code> (additional trailing singleton dimensions are allowed).</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>If array contains <code>NaN</code> or <a href="../manual/missing.html#missing"><code>missing</code></a> values, the result is also <code>NaN</code> or <code>missing</code> (<code>missing</code> takes precedence if array contains both). Use the <a href="../base/base.html#Base.skipmissing"><code>skipmissing</code></a> function to omit <code>missing</code> entries and compute the variance of non-missing values.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L353-L379">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Statistics.varm" href="#Statistics.varm"><code>Statistics.varm</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">varm(itr, mean; dims, corrected::Bool=true)</code></pre><p>Compute the sample variance of collection <code>itr</code>, with known mean(s) <code>mean</code>.</p><p>The algorithm returns an estimator of the generative distribution&#39;s variance under the assumption that each entry of <code>itr</code> is a sample drawn from the same unknown distribution, with the samples uncorrelated. For arrays, this computation is equivalent to calculating <code>sum((itr .- mean(itr)).^2) / (length(itr) - 1)</code>. If <code>corrected</code> is <code>true</code>, then the sum is scaled with <code>n-1</code>, whereas the sum is scaled with <code>n</code> if <code>corrected</code> is <code>false</code> with <code>n</code> the number of elements in <code>itr</code>.</p><p>If <code>itr</code> is an <code>AbstractArray</code>, <code>dims</code> can be provided to compute the variance over dimensions. In that case, <code>mean</code> must be an array with the same shape as <code>mean(itr, dims=dims)</code> (additional trailing singleton dimensions are allowed).</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>If array contains <code>NaN</code> or <a href="../manual/missing.html#missing"><code>missing</code></a> values, the result is also <code>NaN</code> or <code>missing</code> (<code>missing</code> takes precedence if array contains both). Use the <a href="../base/base.html#Base.skipmissing"><code>skipmissing</code></a> function to omit <code>missing</code> entries and compute the variance of non-missing values.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L315-L338">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Statistics.cor" href="#Statistics.cor"><code>Statistics.cor</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cor(x::AbstractVector)</code></pre><p>Return the number one.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L729-L733">source</a></section><section><div><pre><code class="language-julia hljs">cor(X::AbstractMatrix; dims::Int=1)</code></pre><p>Compute the Pearson correlation matrix of the matrix <code>X</code> along the dimension <code>dims</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L737-L741">source</a></section><section><div><pre><code class="language-julia hljs">cor(x::AbstractVector, y::AbstractVector)</code></pre><p>Compute the Pearson correlation between the vectors <code>x</code> and <code>y</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L744-L748">source</a></section><section><div><pre><code class="language-julia hljs">cor(X::AbstractVecOrMat, Y::AbstractVecOrMat; dims=1)</code></pre><p>Compute the Pearson correlation between the vectors or matrices <code>X</code> and <code>Y</code> along the dimension <code>dims</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L751-L755">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Statistics.cov" href="#Statistics.cov"><code>Statistics.cov</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">cov(x::AbstractVector; corrected::Bool=true)</code></pre><p>Compute the variance of the vector <code>x</code>. If <code>corrected</code> is <code>true</code> (the default) then the sum is scaled with <code>n-1</code>, whereas the sum is scaled with <code>n</code> if <code>corrected</code> is <code>false</code> where <code>n = length(x)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L581-L586">source</a></section><section><div><pre><code class="language-julia hljs">cov(X::AbstractMatrix; dims::Int=1, corrected::Bool=true)</code></pre><p>Compute the covariance matrix of the matrix <code>X</code> along the dimension <code>dims</code>. If <code>corrected</code> is <code>true</code> (the default) then the sum is scaled with <code>n-1</code>, whereas the sum is scaled with <code>n</code> if <code>corrected</code> is <code>false</code> where <code>n = size(X, dims)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L589-L595">source</a></section><section><div><pre><code class="language-julia hljs">cov(x::AbstractVector, y::AbstractVector; corrected::Bool=true)</code></pre><p>Compute the covariance between the vectors <code>x</code> and <code>y</code>. If <code>corrected</code> is <code>true</code> (the default), computes <span>$\frac{1}{n-1}\sum_{i=1}^n (x_i-\bar x) (y_i-\bar y)^*$</span> where <span>$*$</span> denotes the complex conjugate and <code>n = length(x) = length(y)</code>. If <code>corrected</code> is <code>false</code>, computes <span>$\frac{1}{n}\sum_{i=1}^n (x_i-\bar x) (y_i-\bar y)^*$</span>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L599-L606">source</a></section><section><div><pre><code class="language-julia hljs">cov(X::AbstractVecOrMat, Y::AbstractVecOrMat; dims::Int=1, corrected::Bool=true)</code></pre><p>Compute the covariance between the vectors or matrices <code>X</code> and <code>Y</code> along the dimension <code>dims</code>. If <code>corrected</code> is <code>true</code> (the default) then the sum is scaled with <code>n-1</code>, whereas the sum is scaled with <code>n</code> if <code>corrected</code> is <code>false</code> where <code>n = size(X, dims) = size(Y, dims)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L610-L616">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Statistics.mean!" href="#Statistics.mean!"><code>Statistics.mean!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mean!(r, v)</code></pre><p>Compute the mean of <code>v</code> over the singleton dimensions of <code>r</code>, and write results to <code>r</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; using Statistics

julia&gt; v = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; mean!([1., 1.], v)
2-element Vector{Float64}:
 1.5
 3.5

julia&gt; mean!([1. 1.], v)
1×2 Matrix{Float64}:
 2.0  3.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L120-L143">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Statistics.mean" href="#Statistics.mean"><code>Statistics.mean</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">mean(itr)</code></pre><p>Compute the mean of all elements in a collection.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>If <code>itr</code> contains <code>NaN</code> or <a href="../manual/missing.html#missing"><code>missing</code></a> values, the result is also <code>NaN</code> or <code>missing</code> (<code>missing</code> takes precedence if array contains both). Use the <a href="../base/base.html#Base.skipmissing"><code>skipmissing</code></a> function to omit <code>missing</code> entries and compute the mean of non-missing values.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; using Statistics

julia&gt; mean(1:20)
10.5

julia&gt; mean([1, missing, 3])
missing

julia&gt; mean(skipmissing([1, missing, 3]))
2.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L19-L43">source</a></section><section><div><pre><code class="language-julia hljs">mean(f, itr)</code></pre><p>Apply the function <code>f</code> to each element of collection <code>itr</code> and take the mean.</p><pre><code class="language-julia-repl hljs">julia&gt; using Statistics

julia&gt; mean(√, [1, 2, 3])
1.3820881233139908

julia&gt; mean([√1, √2, √3])
1.3820881233139908</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L46-L60">source</a></section><section><div><pre><code class="language-julia hljs">mean(f, A::AbstractArray; dims)</code></pre><p>Apply the function <code>f</code> to each element of array <code>A</code> and take the mean over dimensions <code>dims</code>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p>This method requires at least Julia 1.3.</p></div></div><pre><code class="language-julia-repl hljs">julia&gt; using Statistics

julia&gt; mean(√, [1, 2, 3])
1.3820881233139908

julia&gt; mean([√1, √2, √3])
1.3820881233139908

julia&gt; mean(√, [1 2 3; 4 5 6], dims=2)
2×1 Matrix{Float64}:
 1.3820881233139908
 2.2285192400943226</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L81-L103">source</a></section><section><div><pre><code class="language-julia hljs">mean(A::AbstractArray; dims)</code></pre><p>Compute the mean of an array over the given dimensions.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p><code>mean</code> for empty arrays requires at least Julia 1.1.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; using Statistics

julia&gt; A = [1 2; 3 4]
2×2 Matrix{Int64}:
 1  2
 3  4

julia&gt; mean(A, dims=1)
1×2 Matrix{Float64}:
 2.0  3.0

julia&gt; mean(A, dims=2)
2×1 Matrix{Float64}:
 1.5
 3.5</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L151-L177">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Statistics.median!" href="#Statistics.median!"><code>Statistics.median!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">median!(v)</code></pre><p>Like <a href="Statistics.html#Statistics.median"><code>median</code></a>, but may overwrite the input vector.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L809-L813">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Statistics.median" href="#Statistics.median"><code>Statistics.median</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">median(itr)</code></pre><p>Compute the median of all elements in a collection. For an even number of elements no exact median element exists, so the result is equivalent to calculating mean of two median elements.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>If <code>itr</code> contains <code>NaN</code> or <a href="../manual/missing.html#missing"><code>missing</code></a> values, the result is also <code>NaN</code> or <code>missing</code> (<code>missing</code> takes precedence if <code>itr</code> contains both). Use the <a href="../base/base.html#Base.skipmissing"><code>skipmissing</code></a> function to omit <code>missing</code> entries and compute the median of non-missing values.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; using Statistics

julia&gt; median([1, 2, 3])
2.0

julia&gt; median([1, 2, 3, 4])
2.5

julia&gt; median([1, 2, missing, 4])
missing

julia&gt; median(skipmissing([1, 2, missing, 4]))
2.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L830-L859">source</a></section><section><div><pre><code class="language-julia hljs">median(A::AbstractArray; dims)</code></pre><p>Compute the median of an array along the given dimensions.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; using Statistics

julia&gt; median([1 2; 3 4], dims=1)
1×2 Matrix{Float64}:
 2.0  3.0</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L862-L875">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Statistics.middle" href="#Statistics.middle"><code>Statistics.middle</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">middle(x)</code></pre><p>Compute the middle of a scalar value, which is equivalent to <code>x</code> itself, but of the type of <code>middle(x, x)</code> for consistency.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L761-L765">source</a></section><section><div><pre><code class="language-julia hljs">middle(x, y)</code></pre><p>Compute the middle of two numbers <code>x</code> and <code>y</code>, which is equivalent in both value and type to computing their mean (<code>(x + y) / 2</code>).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L771-L776">source</a></section><section><div><pre><code class="language-julia hljs">middle(a::AbstractArray)</code></pre><p>Compute the middle of an array <code>a</code>, which consists of finding its extrema and then computing their mean.</p><pre><code class="language-julia-repl hljs">julia&gt; using Statistics

julia&gt; middle(1:10)
5.5

julia&gt; a = [1,2,3.6,10.9]
4-element Vector{Float64}:
  1.0
  2.0
  3.6
 10.9

julia&gt; middle(a)
5.95</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L779-L801">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Statistics.quantile!" href="#Statistics.quantile!"><code>Statistics.quantile!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">quantile!([q::AbstractArray, ] v::AbstractVector, p; sorted=false, alpha::Real=1.0, beta::Real=alpha)</code></pre><p>Compute the quantile(s) of a vector <code>v</code> at a specified probability or vector or tuple of probabilities <code>p</code> on the interval [0,1]. If <code>p</code> is a vector, an optional output array <code>q</code> may also be specified. (If not provided, a new output array is created.) The keyword argument <code>sorted</code> indicates whether <code>v</code> can be assumed to be sorted; if <code>false</code> (the default), then the elements of <code>v</code> will be partially sorted in-place.</p><p>Samples quantile are defined by <code>Q(p) = (1-γ)*x[j] + γ*x[j+1]</code>, where <code>x[j]</code> is the j-th order statistic of <code>v</code>, <code>j = floor(n*p + m)</code>, <code>m = alpha + p*(1 - alpha - beta)</code> and <code>γ = n*p + m - j</code>.</p><p>By default (<code>alpha = beta = 1</code>), quantiles are computed via linear interpolation between the points <code>((k-1)/(n-1), x[k])</code>, for <code>k = 1:n</code> where <code>n = length(v)</code>. This corresponds to Definition 7 of Hyndman and Fan (1996), and is the same as the R and NumPy default.</p><p>The keyword arguments <code>alpha</code> and <code>beta</code> correspond to the same parameters in Hyndman and Fan, setting them to different values allows to calculate quantiles with any of the methods 4-9 defined in this paper:</p><ul><li>Def. 4: <code>alpha=0</code>, <code>beta=1</code></li><li>Def. 5: <code>alpha=0.5</code>, <code>beta=0.5</code> (MATLAB default)</li><li>Def. 6: <code>alpha=0</code>, <code>beta=0</code> (Excel <code>PERCENTILE.EXC</code>, Python default, Stata <code>altdef</code>)</li><li>Def. 7: <code>alpha=1</code>, <code>beta=1</code> (Julia, R and NumPy default, Excel <code>PERCENTILE</code> and <code>PERCENTILE.INC</code>, Python <code>&#39;inclusive&#39;</code>)</li><li>Def. 8: <code>alpha=1/3</code>, <code>beta=1/3</code></li><li>Def. 9: <code>alpha=3/8</code>, <code>beta=3/8</code></li></ul><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>An <code>ArgumentError</code> is thrown if <code>v</code> contains <code>NaN</code> or <a href="../manual/missing.html#missing"><code>missing</code></a> values.</p></div></div><p><strong>References</strong></p><ul><li><p>Hyndman, R.J and Fan, Y. (1996) &quot;Sample Quantiles in Statistical Packages&quot;, <em>The American Statistician</em>, Vol. 50, No. 4, pp. 361-365</p></li><li><p><a href="https://en.m.wikipedia.org/wiki/Quantile">Quantile on Wikipedia</a> details the different quantile definitions</p></li></ul><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; using Statistics

julia&gt; x = [3, 2, 1];

julia&gt; quantile!(x, 0.5)
2.0

julia&gt; x
3-element Vector{Int64}:
 1
 2
 3

julia&gt; y = zeros(3);

julia&gt; quantile!(y, x, [0.1, 0.5, 0.9]) === y
true

julia&gt; y
3-element Vector{Float64}:
 1.2000000000000002
 2.0
 2.8000000000000003</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L884-L946">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Statistics.quantile" href="#Statistics.quantile"><code>Statistics.quantile</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">quantile(itr, p; sorted=false, alpha::Real=1.0, beta::Real=alpha)</code></pre><p>Compute the quantile(s) of a collection <code>itr</code> at a specified probability or vector or tuple of probabilities <code>p</code> on the interval [0,1]. The keyword argument <code>sorted</code> indicates whether <code>itr</code> can be assumed to be sorted.</p><p>Samples quantile are defined by <code>Q(p) = (1-γ)*x[j] + γ*x[j+1]</code>, where <code>x[j]</code> is the j-th order statistic of <code>itr</code>, <code>j = floor(n*p + m)</code>, <code>m = alpha + p*(1 - alpha - beta)</code> and <code>γ = n*p + m - j</code>.</p><p>By default (<code>alpha = beta = 1</code>), quantiles are computed via linear interpolation between the points <code>((k-1)/(n-1), x[k])</code>, for <code>k = 1:n</code> where <code>n = length(itr)</code>. This corresponds to Definition 7 of Hyndman and Fan (1996), and is the same as the R and NumPy default.</p><p>The keyword arguments <code>alpha</code> and <code>beta</code> correspond to the same parameters in Hyndman and Fan, setting them to different values allows to calculate quantiles with any of the methods 4-9 defined in this paper:</p><ul><li>Def. 4: <code>alpha=0</code>, <code>beta=1</code></li><li>Def. 5: <code>alpha=0.5</code>, <code>beta=0.5</code> (MATLAB default)</li><li>Def. 6: <code>alpha=0</code>, <code>beta=0</code> (Excel <code>PERCENTILE.EXC</code>, Python default, Stata <code>altdef</code>)</li><li>Def. 7: <code>alpha=1</code>, <code>beta=1</code> (Julia, R and NumPy default, Excel <code>PERCENTILE</code> and <code>PERCENTILE.INC</code>, Python <code>&#39;inclusive&#39;</code>)</li><li>Def. 8: <code>alpha=1/3</code>, <code>beta=1/3</code></li><li>Def. 9: <code>alpha=3/8</code>, <code>beta=3/8</code></li></ul><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>An <code>ArgumentError</code> is thrown if <code>v</code> contains <code>NaN</code> or <a href="../manual/missing.html#missing"><code>missing</code></a> values. Use the <a href="../base/base.html#Base.skipmissing"><code>skipmissing</code></a> function to omit <code>missing</code> entries and compute the quantiles of non-missing values.</p></div></div><p><strong>References</strong></p><ul><li><p>Hyndman, R.J and Fan, Y. (1996) &quot;Sample Quantiles in Statistical Packages&quot;, <em>The American Statistician</em>, Vol. 50, No. 4, pp. 361-365</p></li><li><p><a href="https://en.m.wikipedia.org/wiki/Quantile">Quantile on Wikipedia</a> details the different quantile definitions</p></li></ul><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; using Statistics

julia&gt; quantile(0:20, 0.5)
10.0

julia&gt; quantile(0:20, [0.1, 0.5, 0.9])
3-element Vector{Float64}:
  2.0
 10.0
 18.000000000000004

julia&gt; quantile(skipmissing([1, 10, missing]), 0.5)
5.5</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaStats/Statistics.jl/blob/68869af06e8cdeb7aba1d5259de602da7328057f/src/Statistics.jl#L1037-L1089">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="SparseArrays.html">« Sparse Arrays</a><a class="docs-footer-nextpage" href="StyledStrings.html">StyledStrings »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
