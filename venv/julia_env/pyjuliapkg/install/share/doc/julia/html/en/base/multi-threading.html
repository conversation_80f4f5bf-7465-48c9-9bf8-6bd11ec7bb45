<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Multi-Threading · The Julia Language</title><meta name="title" content="Multi-Threading · The Julia Language"/><meta property="og:title" content="Multi-Threading · The Julia Language"/><meta property="twitter:title" content="Multi-Threading · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox" checked/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="base.html">Essentials</a></li><li><a class="tocitem" href="collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="math.html">Mathematics</a></li><li><a class="tocitem" href="numbers.html">Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="arrays.html">Arrays</a></li><li><a class="tocitem" href="parallel.html">Tasks</a></li><li class="is-active"><a class="tocitem" href="multi-threading.html">Multi-Threading</a><ul class="internal"><li><a class="tocitem" href="#Atomic-operations"><span>Atomic operations</span></a></li><li><a class="tocitem" href="#ccall-using-a-libuv-threadpool-(Experimental)"><span>ccall using a libuv threadpool (Experimental)</span></a></li><li><a class="tocitem" href="#Low-level-synchronization-primitives"><span>Low-level synchronization primitives</span></a></li></ul></li><li><a class="tocitem" href="scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="constants.html">Constants</a></li><li><a class="tocitem" href="file.html">Filesystem</a></li><li><a class="tocitem" href="io-network.html">I/O and Network</a></li><li><a class="tocitem" href="punctuation.html">Punctuation</a></li><li><a class="tocitem" href="sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="c.html">C Interface</a></li><li><a class="tocitem" href="libc.html">C Standard Library</a></li><li><a class="tocitem" href="stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Base</a></li><li class="is-active"><a href="multi-threading.html">Multi-Threading</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="multi-threading.html">Multi-Threading</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/base/multi-threading.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="lib-multithreading"><a class="docs-heading-anchor" href="#lib-multithreading">Multi-Threading</a><a id="lib-multithreading-1"></a><a class="docs-heading-anchor-permalink" href="#lib-multithreading" title="Permalink"></a></h1><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.@threads" href="#Base.Threads.@threads"><code>Base.Threads.@threads</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.@threads [schedule] for ... end</code></pre><p>A macro to execute a <code>for</code> loop in parallel. The iteration space is distributed to coarse-grained tasks. This policy can be specified by the <code>schedule</code> argument. The execution of the loop waits for the evaluation of all iterations.</p><p>See also: <a href="multi-threading.html#Base.Threads.@spawn"><code>@spawn</code></a> and <code>pmap</code> in <a href="../stdlib/Distributed.html#man-distributed"><code>Distributed</code></a>.</p><p><strong>Extended help</strong></p><p><strong>Semantics</strong></p><p>Unless stronger guarantees are specified by the scheduling option, the loop executed by <code>@threads</code> macro have the following semantics.</p><p>The <code>@threads</code> macro executes the loop body in an unspecified order and potentially concurrently. It does not specify the exact assignments of the tasks and the worker threads. The assignments can be different for each execution. The loop body code (including any code transitively called from it) must not make any assumptions about the distribution of iterations to tasks or the worker thread in which they are executed. The loop body for each iteration must be able to make forward progress independent of other iterations and be free from data races. As such, invalid synchronizations across iterations may deadlock while unsynchronized memory accesses may result in undefined behavior.</p><p>For example, the above conditions imply that:</p><ul><li>A lock taken in an iteration <em>must</em> be released within the same iteration.</li><li>Communicating between iterations using blocking primitives like <code>Channel</code>s is incorrect.</li><li>Write only to locations not shared across iterations (unless a lock or atomic operation is used).</li><li>Unless the <code>:static</code> schedule is used, the value of <a href="multi-threading.html#Base.Threads.threadid"><code>threadid()</code></a> may change even within a single iteration. See <a href="../manual/multi-threading.html#man-task-migration"><code>Task Migration</code></a>.</li></ul><p><strong>Schedulers</strong></p><p>Without the scheduler argument, the exact scheduling is unspecified and varies across Julia releases. Currently, <code>:dynamic</code> is used when the scheduler is not specified.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p>The <code>schedule</code> argument is available as of Julia 1.5.</p></div></div><p><strong><code>:dynamic</code> (default)</strong></p><p><code>:dynamic</code> scheduler executes iterations dynamically to available worker threads. Current implementation assumes that the workload for each iteration is uniform. However, this assumption may be removed in the future.</p><p>This scheduling option is merely a hint to the underlying execution mechanism. However, a few properties can be expected. The number of <code>Task</code>s used by <code>:dynamic</code> scheduler is bounded by a small constant multiple of the number of available worker threads (<a href="multi-threading.html#Base.Threads.threadpoolsize"><code>Threads.threadpoolsize()</code></a>). Each task processes contiguous regions of the iteration space. Thus, <code>@threads :dynamic for x in xs; f(x); end</code> is typically more efficient than <code>@sync for x in xs; @spawn f(x); end</code> if <code>length(xs)</code> is significantly larger than the number of the worker threads and the run-time of <code>f(x)</code> is relatively smaller than the cost of spawning and synchronizing a task (typically less than 10 microseconds).</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>The <code>:dynamic</code> option for the <code>schedule</code> argument is available and the default as of Julia 1.8.</p></div></div><p><strong><code>:greedy</code></strong></p><p><code>:greedy</code> scheduler spawns up to <a href="multi-threading.html#Base.Threads.threadpoolsize"><code>Threads.threadpoolsize()</code></a> tasks, each greedily working on the given iterated values as they are produced. As soon as one task finishes its work, it takes the next value from the iterator. Work done by any individual task is not necessarily on contiguous values from the iterator. The given iterator may produce values forever, only the iterator interface is required (no indexing).</p><p>This scheduling option is generally a good choice if the workload of individual iterations is not uniform/has a large spread.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>The <code>:greedy</code> option for the <code>schedule</code> argument is available as of Julia 1.11.</p></div></div><p><strong><code>:static</code></strong></p><p><code>:static</code> scheduler creates one task per thread and divides the iterations equally among them, assigning each task specifically to each thread. In particular, the value of <a href="multi-threading.html#Base.Threads.threadid"><code>threadid()</code></a> is guaranteed to be constant within one iteration. Specifying <code>:static</code> is an error if used from inside another <code>@threads</code> loop or from a thread other than 1.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p><code>:static</code> scheduling exists for supporting transition of code written before Julia 1.3. In newly written library functions, <code>:static</code> scheduling is discouraged because the functions using this option cannot be called from arbitrary worker threads.</p></div></div><p><strong>Examples</strong></p><p>To illustrate of the different scheduling strategies, consider the following function <code>busywait</code> containing a non-yielding timed loop that runs for a given number of seconds.</p><pre><code class="language-julia-repl hljs">julia&gt; function busywait(seconds)
            tstart = time_ns()
            while (time_ns() - tstart) / 1e9 &lt; seconds
            end
        end

julia&gt; @time begin
            Threads.@spawn busywait(5)
            Threads.@threads :static for i in 1:Threads.threadpoolsize()
                busywait(1)
            end
        end
6.003001 seconds (16.33 k allocations: 899.255 KiB, 0.25% compilation time)

julia&gt; @time begin
            Threads.@spawn busywait(5)
            Threads.@threads :dynamic for i in 1:Threads.threadpoolsize()
                busywait(1)
            end
        end
2.012056 seconds (16.05 k allocations: 883.919 KiB, 0.66% compilation time)</code></pre><p>The <code>:dynamic</code> example takes 2 seconds since one of the non-occupied threads is able to run two of the 1-second iterations to complete the for loop.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/threadingconstructs.jl#L260-L380">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.foreach" href="#Base.Threads.foreach"><code>Base.Threads.foreach</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.foreach(f, channel::Channel;
                schedule::Threads.AbstractSchedule=Threads.FairSchedule(),
                ntasks=Threads.threadpoolsize())</code></pre><p>Similar to <code>foreach(f, channel)</code>, but iteration over <code>channel</code> and calls to <code>f</code> are split across <code>ntasks</code> tasks spawned by <code>Threads.@spawn</code>. This function will wait for all internally spawned tasks to complete before returning.</p><p>If <code>schedule isa FairSchedule</code>, <code>Threads.foreach</code> will attempt to spawn tasks in a manner that enables Julia&#39;s scheduler to more freely load-balance work items across threads. This approach generally has higher per-item overhead, but may perform better than <code>StaticSchedule</code> in concurrence with other multithreaded workloads.</p><p>If <code>schedule isa StaticSchedule</code>, <code>Threads.foreach</code> will spawn tasks in a manner that incurs lower per-item overhead than <code>FairSchedule</code>, but is less amenable to load-balancing. This approach thus may be more suitable for fine-grained, uniform workloads, but may perform worse than <code>FairSchedule</code> in concurrence with other multithreaded workloads.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; n = 20

julia&gt; c = Channel{Int}(ch -&gt; foreach(i -&gt; put!(ch, i), 1:n), 1)

julia&gt; d = Channel{Int}(n) do ch
           f = i -&gt; put!(ch, i^2)
           Threads.foreach(f, c)
       end

julia&gt; collect(d)
collect(d) = [1, 4, 9, 16, 25, 36, 49, 64, 81, 100, 121, 144, 169, 196, 225, 256, 289, 324, 361, 400]</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>This function requires Julia 1.6 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/threads_overloads.jl#L3-L40">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.@spawn" href="#Base.Threads.@spawn"><code>Base.Threads.@spawn</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.@spawn [:default|:interactive] expr</code></pre><p>Create a <a href="parallel.html#Core.Task"><code>Task</code></a> and <a href="parallel.html#Base.schedule"><code>schedule</code></a> it to run on any available thread in the specified threadpool (<code>:default</code> if unspecified). The task is allocated to a thread once one becomes available. To wait for the task to finish, call <a href="parallel.html#Base.wait"><code>wait</code></a> on the result of this macro, or call <a href="parallel.html#Base.fetch-Tuple{Task}"><code>fetch</code></a> to wait and then obtain its return value.</p><p>Values can be interpolated into <code>@spawn</code> via <code>$</code>, which copies the value directly into the constructed underlying closure. This allows you to insert the <em>value</em> of a variable, isolating the asynchronous code from changes to the variable&#39;s value in the current task.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The thread that the task runs on may change if the task yields, therefore <code>threadid()</code> should not be treated as constant for a task. See <a href="../manual/multi-threading.html#man-task-migration"><code>Task Migration</code></a>, and the broader <a href="../manual/multi-threading.html#man-multithreading">multi-threading</a> manual for further important caveats. See also the chapter on <a href="../manual/multi-threading.html#man-threadpools">threadpools</a>.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p>This macro is available as of Julia 1.3.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.4</header><div class="admonition-body"><p>Interpolating values via <code>$</code> is available as of Julia 1.4.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p>A threadpool may be specified as of Julia 1.9.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; t() = println(&quot;Hello from &quot;, Threads.threadid());

julia&gt; tasks = fetch.([Threads.@spawn t() for i in 1:4]);
Hello from 1
Hello from 1
Hello from 3
Hello from 4</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/threadingconstructs.jl#L419-L458">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.threadid" href="#Base.Threads.threadid"><code>Base.Threads.threadid</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.threadid() -&gt; Int</code></pre><p>Get the ID number of the current thread of execution. The master thread has ID <code>1</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Threads.threadid()
1

julia&gt; Threads.@threads for i in 1:4
          println(Threads.threadid())
       end
4
2
5
4</code></pre><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The thread that a task runs on may change if the task yields, which is known as <a href="../manual/multi-threading.html#man-task-migration"><code>Task Migration</code></a>. For this reason in most cases it is not safe to use <code>threadid()</code> to index into, say, a vector of buffer or stateful objects.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/threadingconstructs.jl#L6-L30">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.maxthreadid" href="#Base.Threads.maxthreadid"><code>Base.Threads.maxthreadid</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.maxthreadid() -&gt; Int</code></pre><p>Get a lower bound on the number of threads (across all thread pools) available to the Julia process, with atomic-acquire semantics. The result will always be greater than or equal to <a href="multi-threading.html#Base.Threads.threadid"><code>threadid()</code></a> as well as <code>threadid(task)</code> for any task you were able to observe before calling <code>maxthreadid</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/threadingconstructs.jl#L34-L41">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.nthreads" href="#Base.Threads.nthreads"><code>Base.Threads.nthreads</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.nthreads(:default | :interactive) -&gt; Int</code></pre><p>Get the current number of threads within the specified thread pool. The threads in <code>:interactive</code> have id numbers <code>1:nthreads(:interactive)</code>, and the threads in <code>:default</code> have id numbers in <code>nthreads(:interactive) .+ (1:nthreads(:default))</code>.</p><p>See also <code>BLAS.get_num_threads</code> and <code>BLAS.set_num_threads</code> in the <a href="../stdlib/LinearAlgebra.html#man-linalg"><code>LinearAlgebra</code></a> standard library, and <code>nprocs()</code> in the <a href="../stdlib/Distributed.html#man-distributed"><code>Distributed</code></a> standard library and <a href="multi-threading.html#Base.Threads.maxthreadid"><code>Threads.maxthreadid()</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/threadingconstructs.jl#L44-L54">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.threadpool" href="#Base.Threads.threadpool"><code>Base.Threads.threadpool</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.threadpool(tid = threadid()) -&gt; Symbol</code></pre><p>Returns the specified thread&#39;s threadpool; either <code>:default</code>, <code>:interactive</code>, or <code>:foreign</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/threadingconstructs.jl#L86-L90">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.nthreadpools" href="#Base.Threads.nthreadpools"><code>Base.Threads.nthreadpools</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.nthreadpools() -&gt; Int</code></pre><p>Returns the number of threadpools currently configured.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/threadingconstructs.jl#L96-L100">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.threadpoolsize" href="#Base.Threads.threadpoolsize"><code>Base.Threads.threadpoolsize</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.threadpoolsize(pool::Symbol = :default) -&gt; Int</code></pre><p>Get the number of threads available to the default thread pool (or to the specified thread pool).</p><p>See also: <code>BLAS.get_num_threads</code> and <code>BLAS.set_num_threads</code> in the <a href="../stdlib/LinearAlgebra.html#man-linalg"><code>LinearAlgebra</code></a> standard library, and <code>nprocs()</code> in the <a href="../stdlib/Distributed.html#man-distributed"><code>Distributed</code></a> standard library.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/threadingconstructs.jl#L103-L112">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.ngcthreads" href="#Base.Threads.ngcthreads"><code>Base.Threads.ngcthreads</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.ngcthreads() -&gt; Int</code></pre><p>Returns the number of GC threads currently configured. This includes both mark threads and concurrent sweep threads.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/threadingconstructs.jl#L140-L145">source</a></section></article><p>See also <a href="../manual/multi-threading.html#man-multithreading">Multi-Threading</a>.</p><h2 id="Atomic-operations"><a class="docs-heading-anchor" href="#Atomic-operations">Atomic operations</a><a id="Atomic-operations-1"></a><a class="docs-heading-anchor-permalink" href="#Atomic-operations" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="atomic" href="#atomic"><code>atomic</code></a> — <span class="docstring-category">Keyword</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>Unsafe pointer operations are compatible with loading and storing pointers declared with <code>_Atomic</code> and <code>std::atomic</code> type in C11 and C++23 respectively. An error may be thrown if there is not support for atomically loading the Julia type <code>T</code>.</p><p>See also: <a href="c.html#Base.unsafe_load"><code>unsafe_load</code></a>, <a href="c.html#Base.unsafe_modify!"><code>unsafe_modify!</code></a>, <a href="c.html#Base.unsafe_replace!"><code>unsafe_replace!</code></a>, <a href="c.html#Base.unsafe_store!"><code>unsafe_store!</code></a>, <a href="c.html#Base.unsafe_swap!"><code>unsafe_swap!</code></a></p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L3617-L3623">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@atomic" href="#Base.@atomic"><code>Base.@atomic</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@atomic var
@atomic order ex</code></pre><p>Mark <code>var</code> or <code>ex</code> as being performed atomically, if <code>ex</code> is a supported expression. If no <code>order</code> is specified it defaults to :sequentially_consistent.</p><pre><code class="nohighlight hljs">@atomic a.b.x = new
@atomic a.b.x += addend
@atomic :release a.b.x = new
@atomic :acquire_release a.b.x += addend</code></pre><p>Perform the store operation expressed on the right atomically and return the new value.</p><p>With <code>=</code>, this operation translates to a <code>setproperty!(a.b, :x, new)</code> call. With any operator also, this operation translates to a <code>modifyproperty!(a.b, :x, +, addend)[2]</code> call.</p><pre><code class="nohighlight hljs">@atomic a.b.x max arg2
@atomic a.b.x + arg2
@atomic max(a.b.x, arg2)
@atomic :acquire_release max(a.b.x, arg2)
@atomic :acquire_release a.b.x + arg2
@atomic :acquire_release a.b.x max arg2</code></pre><p>Perform the binary operation expressed on the right atomically. Store the result into the field in the first argument and return the values <code>(old, new)</code>.</p><p>This operation translates to a <code>modifyproperty!(a.b, :x, func, arg2)</code> call.</p><p>See <a href="../manual/multi-threading.html#man-atomics">Per-field atomics</a> section in the manual for more details.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; mutable struct Atomic{T}; @atomic x::T; end

julia&gt; a = Atomic(1)
Atomic{Int64}(1)

julia&gt; @atomic a.x # fetch field x of a, with sequential consistency
1

julia&gt; @atomic :sequentially_consistent a.x = 2 # set field x of a, with sequential consistency
2

julia&gt; @atomic a.x += 1 # increment field x of a, with sequential consistency
3

julia&gt; @atomic a.x + 1 # increment field x of a, with sequential consistency
3 =&gt; 4

julia&gt; @atomic a.x # fetch field x of a, with sequential consistency
4

julia&gt; @atomic max(a.x, 10) # change field x of a to the max value, with sequential consistency
4 =&gt; 10

julia&gt; @atomic a.x max 5 # again change field x of a to the max value, with sequential consistency
10 =&gt; 10</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>This functionality requires at least Julia 1.7.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/expr.jl#L1123-L1188">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@atomicswap" href="#Base.@atomicswap"><code>Base.@atomicswap</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@atomicswap a.b.x = new
@atomicswap :sequentially_consistent a.b.x = new</code></pre><p>Stores <code>new</code> into <code>a.b.x</code> and returns the old value of <code>a.b.x</code>.</p><p>This operation translates to a <code>swapproperty!(a.b, :x, new)</code> call.</p><p>See <a href="../manual/multi-threading.html#man-atomics">Per-field atomics</a> section in the manual for more details.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; mutable struct Atomic{T}; @atomic x::T; end

julia&gt; a = Atomic(1)
Atomic{Int64}(1)

julia&gt; @atomicswap a.x = 2+2 # replace field x of a with 4, with sequential consistency
1

julia&gt; @atomic a.x # fetch field x of a, with sequential consistency
4</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>This functionality requires at least Julia 1.7.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/expr.jl#L1247-L1273">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@atomicreplace" href="#Base.@atomicreplace"><code>Base.@atomicreplace</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@atomicreplace a.b.x expected =&gt; desired
@atomicreplace :sequentially_consistent a.b.x expected =&gt; desired
@atomicreplace :sequentially_consistent :monotonic a.b.x expected =&gt; desired</code></pre><p>Perform the conditional replacement expressed by the pair atomically, returning the values <code>(old, success::Bool)</code>. Where <code>success</code> indicates whether the replacement was completed.</p><p>This operation translates to a <code>replaceproperty!(a.b, :x, expected, desired)</code> call.</p><p>See <a href="../manual/multi-threading.html#man-atomics">Per-field atomics</a> section in the manual for more details.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; mutable struct Atomic{T}; @atomic x::T; end

julia&gt; a = Atomic(1)
Atomic{Int64}(1)

julia&gt; @atomicreplace a.x 1 =&gt; 2 # replace field x of a with 2 if it was 1, with sequential consistency
(old = 1, success = true)

julia&gt; @atomic a.x # fetch field x of a, with sequential consistency
2

julia&gt; @atomicreplace a.x 1 =&gt; 2 # replace field x of a with 2 if it was 1, with sequential consistency
(old = 2, success = false)

julia&gt; xchg = 2 =&gt; 0; # replace field x of a with 0 if it was 2, with sequential consistency

julia&gt; @atomicreplace a.x xchg
(old = 2, success = true)

julia&gt; @atomic a.x # fetch field x of a, with sequential consistency
0</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>This functionality requires at least Julia 1.7.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/expr.jl#L1291-L1331">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@atomiconce" href="#Base.@atomiconce"><code>Base.@atomiconce</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@atomiconce a.b.x = value
@atomiconce :sequentially_consistent a.b.x = value
@atomiconce :sequentially_consistent :monotonic a.b.x = value</code></pre><p>Perform the conditional assignment of value atomically if it was previously unset, returning the value <code>success::Bool</code>. Where <code>success</code> indicates whether the assignment was completed.</p><p>This operation translates to a <code>setpropertyonce!(a.b, :x, value)</code> call.</p><p>See <a href="../manual/multi-threading.html#man-atomics">Per-field atomics</a> section in the manual for more details.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; mutable struct AtomicOnce
           @atomic x
           AtomicOnce() = new()
       end

julia&gt; a = AtomicOnce()
AtomicOnce(#undef)

julia&gt; @atomiconce a.x = 1 # set field x of a to 1, if unset, with sequential consistency
true

julia&gt; @atomic a.x # fetch field x of a, with sequential consistency
1

julia&gt; @atomiconce a.x = 1 # set field x of a to 1, if unset, with sequential consistency
false</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>This functionality requires at least Julia 1.11.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/expr.jl#L1357-L1392">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.AtomicMemory" href="#Core.AtomicMemory"><code>Core.AtomicMemory</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AtomicMemory{T} == GenericMemory{:atomic, T, Core.CPU}</code></pre><p>Fixed-size <a href="arrays.html#Base.DenseVector"><code>DenseVector{T}</code></a>. Access to its any of its elements is performed atomically (with <code>:monotonic</code> ordering). Setting any of the elements must be accomplished using the <code>@atomic</code> macro and explicitly specifying ordering.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Each element is independently atomic when accessed, and cannot be set non-atomically. Currently the <code>@atomic</code> macro and higher level interface have not been completed, but the building blocks for a future implementation are the internal intrinsics <code>Core.memoryrefget</code>, <code>Core.memoryrefset!</code>, <code>Core.memoryref_isassigned</code>, <code>Core.memoryrefswap!</code>, <code>Core.memoryrefmodify!</code>, and <code>Core.memoryrefreplace!</code>.</p></div></div><p>For details, see <a href="../manual/multi-threading.html#man-atomic-operations">Atomic Operations</a></p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>This type requires Julia 1.11 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/genericmemory.jl#L37-L55">source</a></section></article><p>There are also optional memory ordering parameters for the <code>unsafe</code> set of functions, that select the C/C++-compatible versions of these atomic operations, if that parameter is specified to <a href="c.html#Base.unsafe_load"><code>unsafe_load</code></a>, <a href="c.html#Base.unsafe_store!"><code>unsafe_store!</code></a>, <a href="c.html#Base.unsafe_swap!"><code>unsafe_swap!</code></a>, <a href="c.html#Base.unsafe_replace!"><code>unsafe_replace!</code></a>, and <a href="c.html#Base.unsafe_modify!"><code>unsafe_modify!</code></a>.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>The following APIs are deprecated, though support for them is likely to remain for several releases.</p></div></div><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.Atomic" href="#Base.Threads.Atomic"><code>Base.Threads.Atomic</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.Atomic{T}</code></pre><p>Holds a reference to an object of type <code>T</code>, ensuring that it is only accessed atomically, i.e. in a thread-safe manner.</p><p>Only certain &quot;simple&quot; types can be used atomically, namely the primitive boolean, integer, and float-point types. These are <code>Bool</code>, <code>Int8</code>...<code>Int128</code>, <code>UInt8</code>...<code>UInt128</code>, and <code>Float16</code>...<code>Float64</code>.</p><p>New atomic objects can be created from a non-atomic values; if none is specified, the atomic object is initialized with zero.</p><p>Atomic objects can be accessed using the <code>[]</code> notation:</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = Threads.Atomic{Int}(3)
Base.Threads.Atomic{Int64}(3)

julia&gt; x[] = 1
1

julia&gt; x[]
1</code></pre><p>Atomic operations use an <code>atomic_</code> prefix, such as <a href="multi-threading.html#Base.Threads.atomic_add!"><code>atomic_add!</code></a>, <a href="multi-threading.html#Base.Threads.atomic_xchg!"><code>atomic_xchg!</code></a>, etc.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/atomics.jl#L45-L74">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.atomic_cas!" href="#Base.Threads.atomic_cas!"><code>Base.Threads.atomic_cas!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.atomic_cas!(x::Atomic{T}, cmp::T, newval::T) where T</code></pre><p>Atomically compare-and-set <code>x</code></p><p>Atomically compares the value in <code>x</code> with <code>cmp</code>. If equal, write <code>newval</code> to <code>x</code>. Otherwise, leaves <code>x</code> unmodified. Returns the old value in <code>x</code>. By comparing the returned value to <code>cmp</code> (via <code>===</code>) one knows whether <code>x</code> was modified and now holds the new value <code>newval</code>.</p><p>For further details, see LLVM&#39;s <code>cmpxchg</code> instruction.</p><p>This function can be used to implement transactional semantics. Before the transaction, one records the value in <code>x</code>. After the transaction, the new value is stored only if <code>x</code> has not been modified in the mean time.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = Threads.Atomic{Int}(3)
Base.Threads.Atomic{Int64}(3)

julia&gt; Threads.atomic_cas!(x, 4, 2);

julia&gt; x
Base.Threads.Atomic{Int64}(3)

julia&gt; Threads.atomic_cas!(x, 3, 2);

julia&gt; x
Base.Threads.Atomic{Int64}(2)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/atomics.jl#L90-L122">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.atomic_xchg!" href="#Base.Threads.atomic_xchg!"><code>Base.Threads.atomic_xchg!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.atomic_xchg!(x::Atomic{T}, newval::T) where T</code></pre><p>Atomically exchange the value in <code>x</code></p><p>Atomically exchanges the value in <code>x</code> with <code>newval</code>. Returns the <strong>old</strong> value.</p><p>For further details, see LLVM&#39;s <code>atomicrmw xchg</code> instruction.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = Threads.Atomic{Int}(3)
Base.Threads.Atomic{Int64}(3)

julia&gt; Threads.atomic_xchg!(x, 2)
3

julia&gt; x[]
2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/atomics.jl#L125-L146">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.atomic_add!" href="#Base.Threads.atomic_add!"><code>Base.Threads.atomic_add!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.atomic_add!(x::Atomic{T}, val::T) where T &lt;: ArithmeticTypes</code></pre><p>Atomically add <code>val</code> to <code>x</code></p><p>Performs <code>x[] += val</code> atomically. Returns the <strong>old</strong> value. Not defined for <code>Atomic{Bool}</code>.</p><p>For further details, see LLVM&#39;s <code>atomicrmw add</code> instruction.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = Threads.Atomic{Int}(3)
Base.Threads.Atomic{Int64}(3)

julia&gt; Threads.atomic_add!(x, 2)
3

julia&gt; x[]
5</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/atomics.jl#L149-L170">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.atomic_sub!" href="#Base.Threads.atomic_sub!"><code>Base.Threads.atomic_sub!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.atomic_sub!(x::Atomic{T}, val::T) where T &lt;: ArithmeticTypes</code></pre><p>Atomically subtract <code>val</code> from <code>x</code></p><p>Performs <code>x[] -= val</code> atomically. Returns the <strong>old</strong> value. Not defined for <code>Atomic{Bool}</code>.</p><p>For further details, see LLVM&#39;s <code>atomicrmw sub</code> instruction.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = Threads.Atomic{Int}(3)
Base.Threads.Atomic{Int64}(3)

julia&gt; Threads.atomic_sub!(x, 2)
3

julia&gt; x[]
1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/atomics.jl#L173-L194">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.atomic_and!" href="#Base.Threads.atomic_and!"><code>Base.Threads.atomic_and!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.atomic_and!(x::Atomic{T}, val::T) where T</code></pre><p>Atomically bitwise-and <code>x</code> with <code>val</code></p><p>Performs <code>x[] &amp;= val</code> atomically. Returns the <strong>old</strong> value.</p><p>For further details, see LLVM&#39;s <code>atomicrmw and</code> instruction.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = Threads.Atomic{Int}(3)
Base.Threads.Atomic{Int64}(3)

julia&gt; Threads.atomic_and!(x, 2)
3

julia&gt; x[]
2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/atomics.jl#L197-L217">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.atomic_nand!" href="#Base.Threads.atomic_nand!"><code>Base.Threads.atomic_nand!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.atomic_nand!(x::Atomic{T}, val::T) where T</code></pre><p>Atomically bitwise-nand (not-and) <code>x</code> with <code>val</code></p><p>Performs <code>x[] = ~(x[] &amp; val)</code> atomically. Returns the <strong>old</strong> value.</p><p>For further details, see LLVM&#39;s <code>atomicrmw nand</code> instruction.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = Threads.Atomic{Int}(3)
Base.Threads.Atomic{Int64}(3)

julia&gt; Threads.atomic_nand!(x, 2)
3

julia&gt; x[]
-3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/atomics.jl#L220-L240">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.atomic_or!" href="#Base.Threads.atomic_or!"><code>Base.Threads.atomic_or!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.atomic_or!(x::Atomic{T}, val::T) where T</code></pre><p>Atomically bitwise-or <code>x</code> with <code>val</code></p><p>Performs <code>x[] |= val</code> atomically. Returns the <strong>old</strong> value.</p><p>For further details, see LLVM&#39;s <code>atomicrmw or</code> instruction.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = Threads.Atomic{Int}(5)
Base.Threads.Atomic{Int64}(5)

julia&gt; Threads.atomic_or!(x, 7)
5

julia&gt; x[]
7</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/atomics.jl#L243-L263">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.atomic_xor!" href="#Base.Threads.atomic_xor!"><code>Base.Threads.atomic_xor!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.atomic_xor!(x::Atomic{T}, val::T) where T</code></pre><p>Atomically bitwise-xor (exclusive-or) <code>x</code> with <code>val</code></p><p>Performs <code>x[] $= val</code> atomically. Returns the <strong>old</strong> value.</p><p>For further details, see LLVM&#39;s <code>atomicrmw xor</code> instruction.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = Threads.Atomic{Int}(5)
Base.Threads.Atomic{Int64}(5)

julia&gt; Threads.atomic_xor!(x, 7)
5

julia&gt; x[]
2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/atomics.jl#L266-L286">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.atomic_max!" href="#Base.Threads.atomic_max!"><code>Base.Threads.atomic_max!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.atomic_max!(x::Atomic{T}, val::T) where T</code></pre><p>Atomically store the maximum of <code>x</code> and <code>val</code> in <code>x</code></p><p>Performs <code>x[] = max(x[], val)</code> atomically. Returns the <strong>old</strong> value.</p><p>For further details, see LLVM&#39;s <code>atomicrmw max</code> instruction.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = Threads.Atomic{Int}(5)
Base.Threads.Atomic{Int64}(5)

julia&gt; Threads.atomic_max!(x, 7)
5

julia&gt; x[]
7</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/atomics.jl#L289-L309">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.atomic_min!" href="#Base.Threads.atomic_min!"><code>Base.Threads.atomic_min!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.atomic_min!(x::Atomic{T}, val::T) where T</code></pre><p>Atomically store the minimum of <code>x</code> and <code>val</code> in <code>x</code></p><p>Performs <code>x[] = min(x[], val)</code> atomically. Returns the <strong>old</strong> value.</p><p>For further details, see LLVM&#39;s <code>atomicrmw min</code> instruction.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; x = Threads.Atomic{Int}(7)
Base.Threads.Atomic{Int64}(7)

julia&gt; Threads.atomic_min!(x, 5)
7

julia&gt; x[]
5</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/atomics.jl#L312-L332">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.atomic_fence" href="#Base.Threads.atomic_fence"><code>Base.Threads.atomic_fence</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.atomic_fence()</code></pre><p>Insert a sequential-consistency memory fence</p><p>Inserts a memory fence with sequentially-consistent ordering semantics. There are algorithms where this is needed, i.e. where an acquire/release ordering is insufficient.</p><p>This is likely a very expensive operation. Given that all other atomic operations in Julia already have acquire/release semantics, explicit fences should not be necessary in most cases.</p><p>For further details, see LLVM&#39;s <code>fence</code> instruction.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/atomics.jl#L450-L464">source</a></section></article><h2 id="ccall-using-a-libuv-threadpool-(Experimental)"><a class="docs-heading-anchor" href="#ccall-using-a-libuv-threadpool-(Experimental)">ccall using a libuv threadpool (Experimental)</a><a id="ccall-using-a-libuv-threadpool-(Experimental)-1"></a><a class="docs-heading-anchor-permalink" href="#ccall-using-a-libuv-threadpool-(Experimental)" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@threadcall" href="#Base.@threadcall"><code>Base.@threadcall</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@threadcall((cfunc, clib), rettype, (argtypes...), argvals...)</code></pre><p>The <code>@threadcall</code> macro is called in the same way as <a href="c.html#ccall"><code>ccall</code></a> but does the work in a different thread. This is useful when you want to call a blocking C function without causing the current <code>julia</code> thread to become blocked. Concurrency is limited by size of the libuv thread pool, which defaults to 4 threads but can be increased by setting the <code>UV_THREADPOOL_SIZE</code> environment variable and restarting the <code>julia</code> process.</p><p>Note that the called function should never call back into Julia.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/threadcall.jl#L8-L19">source</a></section></article><h2 id="Low-level-synchronization-primitives"><a class="docs-heading-anchor" href="#Low-level-synchronization-primitives">Low-level synchronization primitives</a><a id="Low-level-synchronization-primitives-1"></a><a class="docs-heading-anchor-permalink" href="#Low-level-synchronization-primitives" title="Permalink"></a></h2><p>These building blocks are used to create the regular synchronization objects.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.SpinLock" href="#Base.Threads.SpinLock"><code>Base.Threads.SpinLock</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SpinLock()</code></pre><p>Create a non-reentrant, test-and-test-and-set spin lock. Recursive use will result in a deadlock. This kind of lock should only be used around code that takes little time to execute and does not block (e.g. perform I/O). In general, <a href="parallel.html#Base.ReentrantLock"><code>ReentrantLock</code></a> should be used instead.</p><p>Each <a href="parallel.html#Base.lock"><code>lock</code></a> must be matched with an <a href="parallel.html#Base.unlock"><code>unlock</code></a>. If <a href="parallel.html#Base.islocked"><code>!islocked(lck::SpinLock)</code></a> holds, <a href="parallel.html#Base.trylock"><code>trylock(lck)</code></a> succeeds unless there are other tasks attempting to hold the lock &quot;at the same time.&quot;</p><p>Test-and-test-and-set spin locks are quickest up to about 30ish contending threads. If you have more contention than that, different synchronization approaches should be considered.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/locks-mt.jl#L14-L30">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="parallel.html">« Tasks</a><a class="docs-footer-nextpage" href="scopedvalues.html">Scoped Values »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
