<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Binary distributions · The Julia Language</title><meta name="title" content="Binary distributions · The Julia Language"/><meta property="og:title" content="Binary distributions · The Julia Language"/><meta property="twitter:title" content="Binary distributions · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL="../.."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../../assets/documenter.js"></script><script src="../../search_index.js"></script><script src="../../siteinfo.js"></script><script src="../../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../../assets/themeswap.js"></script><link href="../../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../../index.html"><img class="docs-light-only" src="../../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../../manual/types.html">Types</a></li><li><a class="tocitem" href="../../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../../base/base.html">Essentials</a></li><li><a class="tocitem" href="../../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../../base/strings.html">Strings</a></li><li><a class="tocitem" href="../../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../../base/constants.html">Constants</a></li><li><a class="tocitem" href="../../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../../base/c.html">C Interface</a></li><li><a class="tocitem" href="../../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../types.html">More about types</a></li><li><a class="tocitem" href="../object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../functions.html">Julia Functions</a></li><li><a class="tocitem" href="../cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../require.html">Module loading</a></li><li><a class="tocitem" href="../inference.html">Inference</a></li><li><a class="tocitem" href="../ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox" checked/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="linux.html">Linux</a></li><li><a class="tocitem" href="macos.html">macOS</a></li><li><a class="tocitem" href="windows.html">Windows</a></li><li><a class="tocitem" href="freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="arm.html">ARM (Linux)</a></li><li class="is-active"><a class="tocitem" href="distributing.html">Binary distributions</a><ul class="internal"><li><a class="tocitem" href="#Versioning-and-Git"><span>Versioning and Git</span></a></li><li><a class="tocitem" href="#Target-Architectures"><span>Target Architectures</span></a></li><li><a class="tocitem" href="#Linux"><span>Linux</span></a></li><li><a class="tocitem" href="#OS-X"><span>OS X</span></a></li><li><a class="tocitem" href="#Windows"><span>Windows</span></a></li><li><a class="tocitem" href="#Notes-on-BLAS-and-LAPACK"><span>Notes on BLAS and LAPACK</span></a></li><li class="toplevel"><a class="tocitem" href="#Point-releasing-101"><span>Point releasing 101</span></a></li><li><a class="tocitem" href="#Backporting-commits"><span>Backporting commits</span></a></li><li><a class="tocitem" href="#Checking-for-performance-regressions"><span>Checking for performance regressions</span></a></li><li><a class="tocitem" href="#Building-test-binaries"><span>Building test binaries</span></a></li><li><a class="tocitem" href="#Checking-for-package-breakages"><span>Checking for package breakages</span></a></li><li><a class="tocitem" href="#Merging-backports-into-the-release-branch"><span>Merging backports into the release branch</span></a></li><li><a class="tocitem" href="#Tagging-the-release"><span>Tagging the release</span></a></li><li><a class="tocitem" href="#Signing-binaries"><span>Signing binaries</span></a></li><li><a class="tocitem" href="#Uploading-binaries"><span>Uploading binaries</span></a></li></ul></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Building Julia</a></li><li class="is-active"><a href="distributing.html">Binary distributions</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="distributing.html">Binary distributions</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/build/distributing.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Binary-distributions"><a class="docs-heading-anchor" href="#Binary-distributions">Binary distributions</a><a id="Binary-distributions-1"></a><a class="docs-heading-anchor-permalink" href="#Binary-distributions" title="Permalink"></a></h1><p>These notes are for those wishing to compile a binary distribution of Julia for distribution on various platforms.  We love users spreading Julia as far and wide as they can, trying it out on as wide an array of operating systems and hardware configurations as possible.  As each platform has specific gotchas and processes that must be followed in order to create a portable, working Julia distribution, we have separated most of the notes by OS.</p><p>Note that while the code for Julia is <a href="https://github.com/JuliaLang/julia/blob/master/LICENSE.md">MIT-licensed, with a few exceptions</a>, the distribution created by the techniques described herein will be GPL licensed, as various dependent libraries such as <code>SuiteSparse</code> are GPL licensed. We do hope to have a non-GPL distribution of Julia in the future.</p><h2 id="Versioning-and-Git"><a class="docs-heading-anchor" href="#Versioning-and-Git">Versioning and Git</a><a id="Versioning-and-Git-1"></a><a class="docs-heading-anchor-permalink" href="#Versioning-and-Git" title="Permalink"></a></h2><p>The Makefile uses both the <code>VERSION</code> file and commit hashes and tags from the git repository to generate the <code>base/version_git.jl</code> with information we use to fill the splash screen and the <code>versioninfo()</code> output. If you for some reason don&#39;t want to have the git repository available when building you should pregenerate the <code>base/version_git.jl</code> file with:</p><pre><code class="nohighlight hljs">make -C base version_git.jl.phony</code></pre><p>Julia has lots of build dependencies where we use patched versions that has not yet been included by the popular package managers. These dependencies will usually be automatically downloaded when you build, but if you want to be able to build Julia on a computer without internet access you should create a full-source-dist archive with the special make target</p><pre><code class="nohighlight hljs">make full-source-dist</code></pre><p>that creates a julia-version-commit.tar.gz archive with all required dependencies.</p><p>When compiling a tagged release in the git repository, we don&#39;t display the branch/commit hash info in the splash screen. You can use this line to show a release description of up to 45 characters. To set this line you have to create a Make.user file containing:</p><pre><code class="nohighlight hljs">override TAGGED_RELEASE_BANNER = &quot;my-package-repository build&quot;</code></pre><h2 id="Target-Architectures"><a class="docs-heading-anchor" href="#Target-Architectures">Target Architectures</a><a id="Target-Architectures-1"></a><a class="docs-heading-anchor-permalink" href="#Target-Architectures" title="Permalink"></a></h2><p>By default, Julia optimizes its system image to the native architecture of the build machine. This is usually not what you want when building packages, as it will make Julia fail at startup on any machine with incompatible CPUs (in particular older ones with more restricted instruction sets).</p><p>We therefore recommend that you pass the <code>MARCH</code> variable when calling <code>make</code>, setting it to the baseline target you intend to support. This will determine the target CPU for both the Julia executable and libraries, and the system image (the latter can also be set using <a href="../../manual/environment-variables.html#JULIA_CPU_TARGET"><code>JULIA_CPU_TARGET</code></a>). Typically useful values for x86 CPUs are <code>x86-64</code> and <code>core2</code> (for 64-bit builds) and <code>pentium4</code> (for 32-bit builds). Unfortunately, CPUs older than Pentium 4 are currently not supported (see <a href="https://github.com/JuliaLang/julia/issues/7185">this issue</a>).</p><p>The full list of CPU targets supported by LLVM can be obtained by running <code>llc -mattr=help</code>.</p><h2 id="Linux"><a class="docs-heading-anchor" href="#Linux">Linux</a><a id="Linux-1"></a><a class="docs-heading-anchor-permalink" href="#Linux" title="Permalink"></a></h2><p>On Linux, <code>make binary-dist</code> creates a tarball that contains a fully functional Julia installation. If you wish to create a distribution package such as a <code>.deb</code>, or <code>.rpm</code>, some extra effort is needed. See the <a href="https://github.com/staticfloat/julia-debian">julia-debian</a> repository for an example of what metadata is needed for creating <code>.deb</code> packages for Debian and Ubuntu-based systems. See the <a href="https://src.fedoraproject.org/rpms/julia">Fedora package</a> for RPM-based distributions. Although we have not yet experimented with it, <a href="https://wiki.debian.org/Alien">Alien</a> could be used to generate Julia packages for various Linux distributions.</p><p>Julia supports overriding standard installation directories via <code>prefix</code> and other environment variables you can pass when calling <code>make</code> and <code>make install</code>. See Make.inc for their list. <code>DESTDIR</code> can also be used to force the installation into a temporary directory.</p><p>By default, Julia loads <code>$prefix/etc/julia/startup.jl</code> as an installation-wide initialization file. This file can be used by distribution managers to set up custom paths or initialization code. For Linux distribution packages, if <code>$prefix</code> is set to <code>/usr</code>, there is no <code>/usr/etc</code> to look into. This requires the path to Julia&#39;s private <code>etc</code> directory to be changed.  This can be done via the <code>sysconfdir</code> make variable when building.  Simply pass <code>sysconfdir=/etc</code> to <code>make</code> when building and Julia will first check <code>/etc/julia/startup.jl</code> before trying <code>$prefix/etc/julia/startup.jl</code>.</p><h2 id="OS-X"><a class="docs-heading-anchor" href="#OS-X">OS X</a><a id="OS-X-1"></a><a class="docs-heading-anchor-permalink" href="#OS-X" title="Permalink"></a></h2><p>To create a binary distribution on OSX, build Julia first, then cd to <code>contrib/mac/app</code>, and run <code>make</code> with the same makevars that were used with <code>make</code> when building Julia proper.  This will then create a <code>.dmg</code> file in the <code>contrib/mac/app</code> directory holding a completely self-contained Julia.app.</p><p>Alternatively, Julia may be built as a framework by invoking <code>make</code> with the <code>darwinframework</code> target and <code>DARWIN_FRAMEWORK=1</code> set.  For example, <code>make DARWIN_FRAMEWORK=1 darwinframework</code>.</p><h2 id="Windows"><a class="docs-heading-anchor" href="#Windows">Windows</a><a id="Windows-1"></a><a class="docs-heading-anchor-permalink" href="#Windows" title="Permalink"></a></h2><p>Instructions for reating a Julia distribution on Windows are described in the <a href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/build/windows.md">build devdocs for Windows</a>.</p><h2 id="Notes-on-BLAS-and-LAPACK"><a class="docs-heading-anchor" href="#Notes-on-BLAS-and-LAPACK">Notes on BLAS and LAPACK</a><a id="Notes-on-BLAS-and-LAPACK-1"></a><a class="docs-heading-anchor-permalink" href="#Notes-on-BLAS-and-LAPACK" title="Permalink"></a></h2><p>Julia builds OpenBLAS by default, which includes the BLAS and LAPACK libraries. On 32-bit architectures, Julia builds OpenBLAS to use 32-bit integers, while on 64-bit architectures, Julia builds OpenBLAS to use 64-bit integers (ILP64). It is essential that all Julia functions that call BLAS and LAPACK API routines use integers of the correct width.</p><p>Most BLAS and LAPACK distributions provided on linux distributions, and even commercial implementations ship libraries that use 32-bit APIs. In many cases, a 64-bit API is provided as a separate library.</p><p>When using vendor provided or OS provided libraries, a <code>make</code> option called <code>USE_BLAS64</code> is available as part of the Julia build. When doing <code>make USE_BLAS64=0</code>, Julia will call BLAS and LAPACK assuming a 32-bit API, where all integers are 32-bit wide, even on a 64-bit architecture.</p><p>Other libraries that Julia uses, such as SuiteSparse also use BLAS and LAPACK internally. The APIs need to be consistent across all libraries that depend on BLAS and LAPACK. The Julia build process will build all these libraries correctly, but when overriding defaults and using system provided libraries, this consistency must be ensured.</p><p>Also note that Linux distributions sometimes ship several versions of OpenBLAS, some of which enable multithreading, and others only working in a serial fashion. For example, in Fedora, <code>libopenblasp.so</code> is threaded, but <code>libopenblas.so</code> is not. We recommend using the former for optimal performance. To choose an OpenBLAS library whose name is different from the default <code>libopenblas.so</code>, pass <code>LIBBLAS=-l$(YOURBLAS)</code> and <code>LIBBLASNAME=lib$(YOURBLAS)</code> to <code>make</code>, replacing <code>$(YOURBLAS)</code> with the name of your library. You can also add <code>.so.0</code> to the name of the library if you want your package to work without requiring the unversioned <code>.so</code> symlink.</p><p>Finally, OpenBLAS includes its own optimized version of LAPACK. If you set <code>USE_SYSTEM_BLAS=1</code> and <code>USE_SYSTEM_LAPACK=1</code>, you should also set <code>LIBLAPACK=-l$(YOURBLAS)</code> and <code>LIBLAPACKNAME=lib$(YOURBLAS)</code>. Else, the reference LAPACK will be used and performance will typically be much lower.</p><p>Starting with Julia 1.7, Julia uses <a href="https://github.com/JuliaLinearAlgebra/libblastrampoline">libblastrampoline</a> to pick a different BLAS at runtime.</p><h1 id="Point-releasing-101"><a class="docs-heading-anchor" href="#Point-releasing-101">Point releasing 101</a><a id="Point-releasing-101-1"></a><a class="docs-heading-anchor-permalink" href="#Point-releasing-101" title="Permalink"></a></h1><p>Creating a point/patch release consists of several distinct steps.</p><h2 id="Backporting-commits"><a class="docs-heading-anchor" href="#Backporting-commits">Backporting commits</a><a id="Backporting-commits-1"></a><a class="docs-heading-anchor-permalink" href="#Backporting-commits" title="Permalink"></a></h2><p>Some pull requests are labeled &quot;backport pending x.y&quot;, e.g. &quot;backport pending 0.6&quot;. This designates that the next subsequent release tagged from the release-x.y branch should include the commit(s) in that pull request. Once the pull request is merged into master, each of the commits should be <a href="https://git-scm.com/docs/git-cherry-pick">cherry picked</a> to a dedicated branch that will ultimately be merged into release-x.y.</p><h3 id="Creating-a-backports-branch"><a class="docs-heading-anchor" href="#Creating-a-backports-branch">Creating a backports branch</a><a id="Creating-a-backports-branch-1"></a><a class="docs-heading-anchor-permalink" href="#Creating-a-backports-branch" title="Permalink"></a></h3><p>First, create a new branch based on release-x.y. The typical convention for Julia branches is to prefix the branch name with your initials if it&#39;s intended to be a personal branch. For the sake of example, we&#39;ll say that the author of the branch is Jane Smith.</p><pre><code class="nohighlight hljs">git fetch origin
git checkout release-x.y
git rebase origin/release-x.y
git checkout -b js/backport-x.y</code></pre><p>This ensures that your local copy of release-x.y is up to date with origin before you create a new branch from it.</p><h3 id="Cherry-picking-commits"><a class="docs-heading-anchor" href="#Cherry-picking-commits">Cherry picking commits</a><a id="Cherry-picking-commits-1"></a><a class="docs-heading-anchor-permalink" href="#Cherry-picking-commits" title="Permalink"></a></h3><p>Now we do the actual backporting. Find all merged pull requests labeled &quot;backport pending x.y&quot; in the GitHub web UI. For each of these, scroll to the bottom where it says &quot;someperson merged commit <code>123abc</code> into <code>master</code> XX minutes ago&quot;. Note that the commit name is a link; if you click it, you&#39;ll be shown the contents of the commit. If this page shows that <code>123abc</code> is a merge commit, go back to the PR page–-we don&#39;t want merge commits, we want the actual commits. However, if this does not show a merge commit, it means that the PR was squash-merged. In that case, use the git SHA of the commit, listed next to commit on this page.</p><p>Once you have the SHA of the commit, cherry-pick it onto the backporting branch:</p><pre><code class="nohighlight hljs">git cherry-pick -x -e &lt;sha&gt;</code></pre><p>There may be conflicts which need to be resolved manually. Once conflicts are resolved (if applicable), add a reference to the GitHub pull request that introduced the commit in the body of the commit message.</p><p>After all of the relevant commits are on the backports branch, push the branch to GitHub.</p><h2 id="Checking-for-performance-regressions"><a class="docs-heading-anchor" href="#Checking-for-performance-regressions">Checking for performance regressions</a><a id="Checking-for-performance-regressions-1"></a><a class="docs-heading-anchor-permalink" href="#Checking-for-performance-regressions" title="Permalink"></a></h2><p>Point releases should never introduce performance regressions. Luckily the Julia benchmarking bot, Nanosoldier, can run benchmarks against any branch, not just master. In this case we want to check the benchmark results of js/backport-x.y against release-x.y. To do this, awaken the Nanosoldier from his robotic slumber using a comment on your backporting pull request:</p><pre><code class="language-markdown hljs">@nanosoldier `runbenchmarks(ALL, vs=&quot;:release-x.y&quot;)`</code></pre><p>This will run all registered benchmarks on release-x.y and js/backport-x.y and produce a summary of results, marking all improvements and regressions.</p><p>If Nanosoldier finds any regressions, try verifying locally and rerun Nanosoldier if necessary. If the regressions are deemed to be real rather than just noise, you&#39;ll have to find a commit on master to backport that fixes it if one exists, otherwise you should determine what caused the regression and submit a patch (or get someone who knows the code to submit a patch) to master, then backport the commit once that&#39;s merged. (Or submit a patch directly to the backport branch if appropriate.)</p><h2 id="Building-test-binaries"><a class="docs-heading-anchor" href="#Building-test-binaries">Building test binaries</a><a id="Building-test-binaries-1"></a><a class="docs-heading-anchor-permalink" href="#Building-test-binaries" title="Permalink"></a></h2><p>After the backport PR has been merged into the <code>release-x.y</code> branch, update your local clone of Julia, then get the SHA of the branch using</p><pre><code class="nohighlight hljs">git rev-parse origin/release-x.y</code></pre><p>Keep that handy, as it&#39;s what you&#39;ll enter in the &quot;Revision&quot; field in the buildbot UI.</p><p>For now, all you need are binaries for Linux x86-64, since this is what&#39;s used for running PackageEvaluator. Go to https://buildog.julialang.org, submit a job for <code>nuke_linux64</code>, then queue up a job for <code>package_linux64</code>, providing the SHA as the revision. When the packaging job completes, it will upload the binary to the <code>julialang2</code> bucket on AWS. Retrieve the URL, as it will be used for PackageEvaluator.</p><h2 id="Checking-for-package-breakages"><a class="docs-heading-anchor" href="#Checking-for-package-breakages">Checking for package breakages</a><a id="Checking-for-package-breakages-1"></a><a class="docs-heading-anchor-permalink" href="#Checking-for-package-breakages" title="Permalink"></a></h2><p>Point releases should never break packages, with the possible exception of packages that are doing some seriously questionable hacks using Base internals that are not intended to be user-facing. (In those cases, maybe have a word with the package author.)</p><p>Checking whether changes made in the forthcoming new version will break packages can be accomplished using <a href="https://github.com/JuliaCI/PackageEvaluator.jl">PackageEvaluator</a>, often called &quot;PkgEval&quot; for short. PkgEval is what populates the status badges on GitHub repos and on pkg.julialang.org. It typically runs on one of the non-benchmarking nodes of Nanosoldier and uses Vagrant to perform its duties in separate, parallel VirtualBox virtual machines.</p><h3 id="Setting-up-PackageEvaluator"><a class="docs-heading-anchor" href="#Setting-up-PackageEvaluator">Setting up PackageEvaluator</a><a id="Setting-up-PackageEvaluator-1"></a><a class="docs-heading-anchor-permalink" href="#Setting-up-PackageEvaluator" title="Permalink"></a></h3><p>Clone PackageEvaluator and create a branch called <code>backport-x.y.z</code>, and check it out. Note that the required changes are a little hacky and confusing, and hopefully that will be addressed in a future version of PackageEvaluator. The changes to make will be modeled off of <a href="https://github.com/JuliaCI/PackageEvaluator.jl/commit/5ba6a3b000e7a3793391d16f695c8704b91d6016">this commit</a>.</p><p>The setup script takes its first argument as the version of Julia to run and the second as the range of package names (AK for packages named A-K, LZ for L-Z). The basic idea is that we&#39;re going to tweak that a bit to run only two versions of Julia, the current x.y release and our backport version, each with three ranges of packages.</p><p>In the linked diff, we&#39;re saying that if the second argument is LZ, use the binaries built from our backport branch, otherwise (AK) use the release binaries. Then we&#39;re using the first argument to run a section of the package list: A-F for input 0.4, G-N for 0.5, and O-Z for 0.6.</p><h3 id="Running-PackageEvaluator"><a class="docs-heading-anchor" href="#Running-PackageEvaluator">Running PackageEvaluator</a><a id="Running-PackageEvaluator-1"></a><a class="docs-heading-anchor-permalink" href="#Running-PackageEvaluator" title="Permalink"></a></h3><p>To run PkgEval, find a hefty enough machine (such as Nanosoldier node 1), then run</p><pre><code class="nohighlight hljs">git clone https://github.com/JuliaCI/PackageEvaluator.jl.git
cd PackageEvaluator.jl/scripts
git checkout backport-x.y.z
./runvagrant.sh</code></pre><p>This produces some folders in the scripts/ directory. The folder names and their contents are decoded below:</p><table><tr><th style="text-align: center">Folder name</th><th style="text-align: center">Julia version</th><th style="text-align: center">Package range</th></tr><tr><td style="text-align: center">0.4AK</td><td style="text-align: center">Release</td><td style="text-align: center">A-F</td></tr><tr><td style="text-align: center">0.4LZ</td><td style="text-align: center">Backport</td><td style="text-align: center">A-F</td></tr><tr><td style="text-align: center">0.5AK</td><td style="text-align: center">Release</td><td style="text-align: center">G-N</td></tr><tr><td style="text-align: center">0.5LZ</td><td style="text-align: center">Backport</td><td style="text-align: center">G-N</td></tr><tr><td style="text-align: center">0.6AK</td><td style="text-align: center">Release</td><td style="text-align: center">O-Z</td></tr><tr><td style="text-align: center">0.6LZ</td><td style="text-align: center">Backport</td><td style="text-align: center">O-Z</td></tr></table><h3 id="Investigating-results"><a class="docs-heading-anchor" href="#Investigating-results">Investigating results</a><a id="Investigating-results-1"></a><a class="docs-heading-anchor-permalink" href="#Investigating-results" title="Permalink"></a></h3><p>Once that&#39;s done, you can use <code>./summary.sh</code> from that same directory to produce a summary report of the findings. We&#39;ll do so for each of the folders to aggregate overall results by version.</p><pre><code class="nohighlight hljs">./summary.sh 0.4AK/*.json &gt; summary_release.txt
./summary.sh 0.5AK/*.json &gt;&gt; summary_release.txt
./summary.sh 0.6AK/*.json &gt;&gt; summary_release.txt
./summary.sh 0.4LZ/*.json &gt; summary_backport.txt
./summary.sh 0.5LZ/*.json &gt;&gt; summary_backport.txt
./summary.sh 0.6LZ/*.json &gt;&gt; summary_backport.txt</code></pre><p>Now we have two files, <code>summary_release.txt</code> and <code>summary_backport.txt</code>, containing the PackageEvaluator test results (pass/fail) for each package for the two versions.</p><p>To make these easier to ingest into a Julia, we&#39;ll convert them into CSV files then use the DataFrames package to process the results. To convert to CSV, copy each .txt file to a corresponding .csv file, then enter Vim and execute <code>ggVGI&quot;&lt;esc&gt;</code> then <code>:%s/\.json /&quot;,/g</code>. (You don&#39;t have to use Vim; this just is one way to do it.) Now process the results with Julia code similar to the following.</p><pre><code class="language-julia hljs">using DataFrames

release = readtable(&quot;summary_release.csv&quot;, header=false, names=[:package, :release])
backport = readtable(&quot;summary_backport.csv&quot;, header=false, names=[:package, :backport])

results = join(release, backport, on=:package, kind=:outer)

for result in eachrow(results)
    a = result[:release]
    b = result[:backport]
    if (isna(a) &amp;&amp; !isna(b)) || (isna(b) &amp;&amp; !isna(a))
        color = :yellow
    elseif a != b &amp;&amp; occursin(&quot;pass&quot;, b)
        color = :green
    elseif a != b
        color = :red
    else
        continue
    end
    printstyled(result[:package], &quot;: Release &quot;, a, &quot; -&gt; Backport &quot;, b, &quot;\n&quot;, color=color)
end</code></pre><p>This will write color-coded lines to <code>stdout</code>. All lines in red must be investigated as they signify potential breakages caused by the backport version. Lines in yellow should be looked into since it means a package ran on one version but not on the other for some reason. If you find that your backported branch is causing breakages, use <code>git bisect</code> to identify the problematic commits, <code>git revert</code> those commits, and repeat the process.</p><h2 id="Merging-backports-into-the-release-branch"><a class="docs-heading-anchor" href="#Merging-backports-into-the-release-branch">Merging backports into the release branch</a><a id="Merging-backports-into-the-release-branch-1"></a><a class="docs-heading-anchor-permalink" href="#Merging-backports-into-the-release-branch" title="Permalink"></a></h2><p>After you have ensured that</p><ul><li>the backported commits pass all of Julia&#39;s unit tests,</li><li>there are no performance regressions introduced by the backported commits as compared to the release branch, and</li><li>the backported commits do not break any registered packages,</li></ul><p>then the backport branch is ready to be merged into release-x.y. Once it&#39;s merged, go through and remove the &quot;backport pending x.y&quot; label from all pull requests containing the commits that have been backported. Do not remove the label from PRs that have not been backported.</p><p>The release-x.y branch should now contain all of the new commits. The last thing we want to do to the branch is to adjust the version number. To do this, submit a PR against release-x.y that edits the VERSION file to remove <code>-pre</code> from the version number. Once that&#39;s merged, we&#39;re ready to tag.</p><h2 id="Tagging-the-release"><a class="docs-heading-anchor" href="#Tagging-the-release">Tagging the release</a><a id="Tagging-the-release-1"></a><a class="docs-heading-anchor-permalink" href="#Tagging-the-release" title="Permalink"></a></h2><p>It&#39;s time! Check out the release-x.y branch and make sure that your local copy of the branch is up to date with the remote branch. At the command line, run</p><pre><code class="nohighlight hljs">git tag v$(cat VERSION)
git push --tags</code></pre><p>This creates the tag locally and pushes it to GitHub.</p><p>After tagging the release, submit another PR to release-x.y to bump the patch number and add <code>-pre</code> back to the end. This denotes that the branch state reflects a prerelease version of the next point release in the x.y series.</p><p>Follow the remaining directions in the Makefile.</p><h2 id="Signing-binaries"><a class="docs-heading-anchor" href="#Signing-binaries">Signing binaries</a><a id="Signing-binaries-1"></a><a class="docs-heading-anchor-permalink" href="#Signing-binaries" title="Permalink"></a></h2><p>Some of these steps will require secure passwords. To obtain the appropriate passwords, contact Elliot Saba (staticfloat) or Alex Arslan (ararslan). Note that code signing for each platform must be performed on that platform (e.g. Windows signing must be done on Windows, etc.).</p><h3 id="Linux-2"><a class="docs-heading-anchor" href="#Linux-2">Linux</a><a class="docs-heading-anchor-permalink" href="#Linux-2" title="Permalink"></a></h3><p>Code signing must be done manually on Linux, but it&#39;s quite simple. First obtain the file <code>julia.key</code> from the CodeSigning folder in the <code>juliasecure</code> AWS bucket. Add this to your GnuPG keyring using</p><pre><code class="nohighlight hljs">gpg --import julia.key</code></pre><p>This will require entering a password that you must obtain from Elliot or Alex. Next, set the trust level for the key to maximum. Start by entering a <code>gpg</code> session:</p><pre><code class="nohighlight hljs">gpg --edit-key julia</code></pre><p>At the prompt, type <code>trust</code>, then when asked for a trust level, provide the maximum available (likely 5). Exit GnuPG.</p><p>Now, for each of the Linux tarballs that were built on the buildbots, enter</p><pre><code class="nohighlight hljs">gpg -u julia --armor --detach-sig julia-x.y.z-linux-&lt;arch&gt;.tar.gz</code></pre><p>This will produce a corresponding .asc file for each tarball. And that&#39;s it!</p><h3 id="macOS"><a class="docs-heading-anchor" href="#macOS">macOS</a><a id="macOS-1"></a><a class="docs-heading-anchor-permalink" href="#macOS" title="Permalink"></a></h3><p>Code signing should happen automatically on the macOS buildbots. However, it&#39;s important to verify that it was successful. On a system or virtual machine running macOS, download the .dmg file that was built on the buildbots. For the sake of example, say that the .dmg file is called <code>julia-x.y.z-osx.dmg</code>. Run</p><pre><code class="nohighlight hljs">mkdir ./jlmnt
hdiutil mount -readonly -mountpoint ./jlmnt julia-x.y.z-osx.dmg
codesign -v jlmnt/Julia-x.y.app</code></pre><p>Be sure to note the name of the mounted disk listed when mounting! For the sake of example, we&#39;ll assume this is <code>disk3</code>. If the code signing verification exited successfully, there will be no output from the <code>codesign</code> step. If it was indeed successful, you can detach the .dmg now:</p><pre><code class="nohighlight hljs">hdiutil eject /dev/disk3
rm -rf ./jlmnt</code></pre><p>If you get a message like</p><blockquote><p>Julia-x.y.app: code object is not signed at all</p></blockquote><p>then you&#39;ll need to sign manually.</p><p>To sign manually, first retrieve the OS X certificates from the CodeSigning folder in the <code>juliasecure</code> bucket on AWS. Add the .p12 file to your keychain using Keychain.app. Ask Elliot Saba (staticfloat) or Alex Arslan (ararslan) for the password for the key. Now run</p><pre><code class="nohighlight hljs">hdiutil convert julia-x.y.z-osx.dmg -format UDRW -o julia-x.y.z-osx_writable.dmg
mkdir ./jlmnt
hdiutil mount -mountpoint julia-x.y.z-osx_writable.dmg
codesign -s &quot;AFB379C0B4CBD9DB9A762797FC2AB5460A2B0DBE&quot; --deep jlmnt/Julia-x.y.app</code></pre><p>This may fail with a message like</p><blockquote><p>Julia-x.y.app: resource fork, Finder information, or similar detritus not allowed</p></blockquote><p>If that&#39;s the case, you&#39;ll need to remove extraneous attributes:</p><pre><code class="nohighlight hljs">xattr -cr jlmnt/Julia-x.y.app</code></pre><p>Then retry code signing. If that produces no errors, retry verification. If all is now well, unmount the writable .dmg and convert it back to read-only:</p><pre><code class="nohighlight hljs">hdiutil eject /dev/disk3
rm -rf ./jlmnt
hdiutil convert julia-x.y.z-osx_writable.dmg -format UDZO -o julia-x.y.z-osx_fixed.dmg</code></pre><p>Verify that the resulting .dmg is in fact fixed by double clicking it. If everything looks good, eject it then drop the <code>_fixed</code> suffix from the name. And that&#39;s it!</p><h3 id="Windows-2"><a class="docs-heading-anchor" href="#Windows-2">Windows</a><a class="docs-heading-anchor-permalink" href="#Windows-2" title="Permalink"></a></h3><p>Signing must be performed manually on Windows. First obtain the Windows 10 SDK, which contains the necessary signing utilities, from the Microsoft website. We need the <code>SignTool</code> utility which should have been installed somewhere like <code>C:\Program Files (x86)\Windows Kits\10\App Certification Kit</code>. Grab the Windows certificate files from CodeSigning on <code>juliasecure</code> and put them in the same directory as the executables. Open a Windows CMD window, <code>cd</code> to where all the files are, and run</p><pre><code class="nohighlight hljs">set PATH=%PATH%;C:\Program Files (x86)\Windows Kits\10\App Certification Kit;
signtool sign /f julia-windows-code-sign_2017.p12 /p &quot;PASSWORD&quot; ^
   /t http://timestamp.verisign.com/scripts/timstamp.dll ^
   /v julia-x.y.z-win32.exe</code></pre><p>Note that <code>^</code> is a line continuation character in Windows CMD and <code>PASSWORD</code> is a placeholder for the password for this certificate. As usual, contact Elliot or Alex for passwords. If there are no errors, we&#39;re all good!</p><h2 id="Uploading-binaries"><a class="docs-heading-anchor" href="#Uploading-binaries">Uploading binaries</a><a id="Uploading-binaries-1"></a><a class="docs-heading-anchor-permalink" href="#Uploading-binaries" title="Permalink"></a></h2><p>Now that everything is signed, we need to upload the binaries to AWS. You can use a program like Cyberduck or the <code>aws</code> command line utility. The binaries should go in the <code>julialang2</code> bucket in the appropriate folders. For example, Linux x86-64 goes in <code>julialang2/bin/linux/x.y</code>. Be sure to delete the current <code>julia-x.y-latest-linux-&lt;arch&gt;.tar.gz</code> file and replace it with a duplicate of <code>julia-x.y.z-linux-&lt;arch&gt;.tar.gz</code>.</p><p>We also need to upload the checksums for everything we&#39;ve built, including the source tarballs and all release binaries. This is simple:</p><pre><code class="nohighlight hljs">shasum -a 256 julia-x.y.z* | grep -v -e sha256 -e md5 -e asc &gt; julia-x.y.z.sha256
md5sum julia-x.y.z* | grep -v -e sha256 -e md5 -e asc &gt; julia-x.y.z.md5</code></pre><p>Note that if you&#39;re running those commands on macOS, you&#39;ll get very slightly different output, which can be reformatted by looking at an existing file. Mac users will also need to use <code>md5 -r</code> instead of <code>md5sum</code>. Upload the .md5 and .sha256 files to <code>julialang2/bin/checksums</code> on AWS.</p><p>Ensure that the permissions on AWS for all uploaded files are set to &quot;Everyone: READ.&quot;</p><p>For each file we&#39;ve uploaded, we need to purge the Fastly cache so that the links on the website point to the updated files. As an example:</p><pre><code class="nohighlight hljs">curl -X PURGE https://julialang-s3.julialang.org/bin/checksums/julia-x.y.z.sha256</code></pre><p>Sometimes this isn&#39;t necessary but it&#39;s good to do anyway.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="arm.html">« ARM (Linux)</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
