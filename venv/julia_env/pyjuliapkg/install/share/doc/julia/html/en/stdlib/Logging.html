<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Logging · The Julia Language</title><meta name="title" content="Logging · The Julia Language"/><meta property="og:title" content="Logging · The Julia Language"/><meta property="twitter:title" content="Logging · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li class="is-active"><a class="tocitem" href="Logging.html">Logging</a><ul class="internal"><li><a class="tocitem" href="#Log-event-structure"><span>Log event structure</span></a></li><li><a class="tocitem" href="#Processing-log-events"><span>Processing log events</span></a></li><li><a class="tocitem" href="#Testing-log-events"><span>Testing log events</span></a></li><li><a class="tocitem" href="#Environment-variables"><span>Environment variables</span></a></li><li><a class="tocitem" href="#Examples"><span>Examples</span></a></li><li><a class="tocitem" href="#Reference"><span>Reference</span></a></li></ul></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="Logging.html">Logging</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="Logging.html">Logging</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/stdlib/Logging/docs/src/index.md" title="View source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="man-logging"><a class="docs-heading-anchor" href="#man-logging">Logging</a><a id="man-logging-1"></a><a class="docs-heading-anchor-permalink" href="#man-logging" title="Permalink"></a></h1><p>The <a href="Logging.html#Logging.Logging"><code>Logging</code></a> module provides a way to record the history and progress of a computation as a log of events.  Events are created by inserting a logging statement into the source code, for example:</p><pre><code class="language-julia hljs">@warn &quot;Abandon printf debugging, all ye who enter here!&quot;
┌ Warning: Abandon printf debugging, all ye who enter here!
└ @ Main REPL[1]:1</code></pre><p>The system provides several advantages over peppering your source code with calls to <code>println()</code>.  First, it allows you to control the visibility and presentation of messages without editing the source code.  For example, in contrast to the <code>@warn</code> above</p><pre><code class="language-julia hljs">@debug &quot;The sum of some values $(sum(rand(100)))&quot;</code></pre><p>will produce no output by default.  Furthermore, it&#39;s very cheap to leave debug statements like this in the source code because the system avoids evaluating the message if it would later be ignored.  In this case <code>sum(rand(100))</code> and the associated string processing will never be executed unless debug logging is enabled.</p><p>Second, the logging tools allow you to attach arbitrary data to each event as a set of key–value pairs. This allows you to capture local variables and other program state for later analysis. For example, to attach the local array variable <code>A</code> and the sum of a vector <code>v</code> as the key <code>s</code> you can use</p><pre><code class="language-julia hljs">A = ones(Int, 4, 4)
v = ones(100)
@info &quot;Some variables&quot;  A  s=sum(v)

# output
┌ Info: Some variables
│   A =
│    4×4 Matrix{Int64}:
│     1  1  1  1
│     1  1  1  1
│     1  1  1  1
│     1  1  1  1
└   s = 100.0</code></pre><p>All of the logging macros <code>@debug</code>, <code>@info</code>, <code>@warn</code> and <code>@error</code> share common features that are described in detail in the documentation for the more general macro <a href="Logging.html#Logging.@logmsg"><code>@logmsg</code></a>.</p><h2 id="Log-event-structure"><a class="docs-heading-anchor" href="#Log-event-structure">Log event structure</a><a id="Log-event-structure-1"></a><a class="docs-heading-anchor-permalink" href="#Log-event-structure" title="Permalink"></a></h2><p>Each event generates several pieces of data, some provided by the user and some automatically extracted. Let&#39;s examine the user-defined data first:</p><ul><li><p>The <em>log level</em> is a broad category for the message that is used for early filtering. There are several standard levels of type <a href="Logging.html#Logging.LogLevel"><code>LogLevel</code></a>; user-defined levels are also possible. Each is distinct in purpose:</p><ul><li><a href="Logging.html#Logging.Debug"><code>Logging.Debug</code></a> (log level -1000) is information intended for the developer of the program. These events are disabled by default.</li><li><a href="Logging.html#Logging.Info"><code>Logging.Info</code></a> (log level 0) is for general information to the user. Think of it as an alternative to using <code>println</code> directly.</li><li><a href="Logging.html#Logging.Warn"><code>Logging.Warn</code></a> (log level 1000) means something is wrong and action is likely required but that for now the program is still working.</li><li><a href="Logging.html#Logging.Error"><code>Logging.Error</code></a> (log level 2000) means something is wrong and it is unlikely to be recovered, at least by this part of the code. Often this log-level is unneeded as throwing an exception can convey all the required information.</li></ul></li><li><p>The <em>message</em>  is an object describing the event. By convention <code>AbstractString</code>s passed as messages are assumed to be in markdown format. Other types will be displayed using <code>print(io, obj)</code> or <code>string(obj)</code> for text-based output and possibly <code>show(io,mime,obj)</code> for other multimedia displays used in the installed logger.</p></li><li><p>Optional <em>key–value pairs</em> allow arbitrary data to be attached to each event. Some keys have conventional meaning that can affect the way an event is interpreted (see <a href="Logging.html#Logging.@logmsg"><code>@logmsg</code></a>).</p></li></ul><p>The system also generates some standard information for each event:</p><ul><li>The <code>module</code> in which the logging macro was expanded.</li><li>The <code>file</code> and <code>line</code> where the logging macro occurs in the source code.</li><li>A message <code>id</code> that is a unique, fixed identifier for the <em>source code statement</em> where the logging macro appears. This identifier is designed to be fairly stable even if the source code of the file changes, as long as the logging statement itself remains the same.</li><li>A <code>group</code> for the event, which is set to the base name of the file by default, without extension.  This can be used to group messages into categories more finely than the log level (for example, all deprecation warnings have group <code>:depwarn</code>), or into logical groupings across or within modules.</li></ul><p>Notice that some useful information such as the event time is not included by default. This is because such information can be expensive to extract and is also <em>dynamically</em> available to the current logger. It&#39;s simple to define a <a href="Logging.html#AbstractLogger-interface">custom logger</a> to augment event data with the time, backtrace, values of global variables and other useful information as required.</p><h2 id="Processing-log-events"><a class="docs-heading-anchor" href="#Processing-log-events">Processing log events</a><a id="Processing-log-events-1"></a><a class="docs-heading-anchor-permalink" href="#Processing-log-events" title="Permalink"></a></h2><p>As you can see in the examples, logging statements make no mention of where log events go or how they are processed. This is a key design feature that makes the system composable and natural for concurrent use. It does this by separating two different concerns:</p><ul><li><em>Creating</em> log events is the concern of the module author who needs to decide where events are triggered and which information to include.</li><li><em>Processing</em> of log events — that is, display, filtering, aggregation and recording — is the concern of the application author who needs to bring multiple modules together into a cooperating application.</li></ul><h3 id="Loggers"><a class="docs-heading-anchor" href="#Loggers">Loggers</a><a id="Loggers-1"></a><a class="docs-heading-anchor-permalink" href="#Loggers" title="Permalink"></a></h3><p>Processing of events is performed by a <em>logger</em>, which is the first piece of user configurable code to see the event. All loggers must be subtypes of <a href="Logging.html#Logging.AbstractLogger"><code>AbstractLogger</code></a>.</p><p>When an event is triggered, the appropriate logger is found by looking for a task-local logger with the global logger as fallback.  The idea here is that the application code knows how log events should be processed and exists somewhere at the top of the call stack. So we should look up through the call stack to discover the logger — that is, the logger should be <em>dynamically scoped</em>. (This is a point of contrast with logging frameworks where the logger is <em>lexically scoped</em>; provided explicitly by the module author or as a simple global variable. In such a system it&#39;s awkward to control logging while composing functionality from multiple modules.)</p><p>The global logger may be set with <a href="Logging.html#Logging.global_logger"><code>global_logger</code></a>, and task-local loggers controlled using <a href="Logging.html#Logging.with_logger"><code>with_logger</code></a>.  Newly spawned tasks inherit the logger of the parent task.</p><p>There are three logger types provided by the library.  <a href="Logging.html#Base.CoreLogging.ConsoleLogger"><code>ConsoleLogger</code></a> is the default logger you see when starting the REPL.  It displays events in a readable text format and tries to give simple but user friendly control over formatting and filtering.  <a href="Logging.html#Logging.NullLogger"><code>NullLogger</code></a> is a convenient way to drop all messages where necessary; it is the logging equivalent of the <a href="../base/base.html#Base.devnull"><code>devnull</code></a> stream.  <a href="Logging.html#Logging.SimpleLogger"><code>SimpleLogger</code></a> is a very simplistic text formatting logger, mainly useful for debugging the logging system itself.</p><p>Custom loggers should come with overloads for the functions described in the <a href="Logging.html#AbstractLogger-interface">reference section</a>.</p><h3 id="Early-filtering-and-message-handling"><a class="docs-heading-anchor" href="#Early-filtering-and-message-handling">Early filtering and message handling</a><a id="Early-filtering-and-message-handling-1"></a><a class="docs-heading-anchor-permalink" href="#Early-filtering-and-message-handling" title="Permalink"></a></h3><p>When an event occurs, a few steps of early filtering occur to avoid generating messages that will be discarded:</p><ol><li>The message log level is checked against a global minimum level (set via <a href="Logging.html#Logging.disable_logging"><code>disable_logging</code></a>).  This is a crude but extremely cheap global setting.</li><li>The current logger state is looked up and the message level checked against the logger&#39;s cached minimum level, as found by calling <a href="Logging.html#Logging.min_enabled_level"><code>Logging.min_enabled_level</code></a>. This behavior can be overridden via environment variables (more on this later).</li><li>The <a href="Logging.html#Logging.shouldlog"><code>Logging.shouldlog</code></a> function is called with the current logger, taking some minimal information (level, module, group, id) which can be computed statically.  Most usefully, <code>shouldlog</code> is passed an event <code>id</code> which can be used to discard events early based on a cached predicate.</li></ol><p>If all these checks pass, the message and key–value pairs are evaluated in full and passed to the current logger via the <a href="Logging.html#Logging.handle_message"><code>Logging.handle_message</code></a> function. <code>handle_message()</code> may perform additional filtering as required and display the event to the screen, save it to a file, etc.</p><p>Exceptions that occur while generating the log event are captured and logged by default.  This prevents individual broken events from crashing the application, which is helpful when enabling little-used debug events in a production system.  This behavior can be customized per logger type by extending <a href="Logging.html#Logging.catch_exceptions"><code>Logging.catch_exceptions</code></a>.</p><h2 id="Testing-log-events"><a class="docs-heading-anchor" href="#Testing-log-events">Testing log events</a><a id="Testing-log-events-1"></a><a class="docs-heading-anchor-permalink" href="#Testing-log-events" title="Permalink"></a></h2><p>Log events are a side effect of running normal code, but you might find yourself wanting to test particular informational messages and warnings. The <code>Test</code> module provides a <a href="Test.html#Test.@test_logs"><code>@test_logs</code></a> macro that can be used to pattern match against the log event stream.</p><h2 id="Environment-variables"><a class="docs-heading-anchor" href="#Environment-variables">Environment variables</a><a id="Environment-variables-1"></a><a class="docs-heading-anchor-permalink" href="#Environment-variables" title="Permalink"></a></h2><p>Message filtering can be influenced through the <a href="../manual/environment-variables.html#JULIA_DEBUG"><code>JULIA_DEBUG</code></a> environment variable, and serves as an easy way to enable debug logging for a file or module. Loading julia with <code>JULIA_DEBUG=loading</code> will activate <code>@debug</code> log messages in <code>loading.jl</code>. For example, in Linux shells:</p><pre><code class="nohighlight hljs">$ JULIA_DEBUG=loading julia -e &#39;using OhMyREPL&#39;
┌ Debug: Rejecting cache file /home/<USER>/.julia/compiled/v0.7/OhMyREPL.ji due to it containing an incompatible cache header
└ @ Base loading.jl:1328
[ Info: Recompiling stale cache file /home/<USER>/.julia/compiled/v0.7/OhMyREPL.ji for module OhMyREPL
┌ Debug: Rejecting cache file /home/<USER>/.julia/compiled/v0.7/Tokenize.ji due to it containing an incompatible cache header
└ @ Base loading.jl:1328
...</code></pre><p>On windows, the same can be achieved in <code>CMD</code> via first running <code>set JULIA_DEBUG=&quot;loading&quot;</code> and in <code>Powershell</code> via <code>$env:JULIA_DEBUG=&quot;loading&quot;</code>.</p><p>Similarly, the environment variable can be used to enable debug logging of modules, such as <code>Pkg</code>, or module roots (see <a href="../base/base.html#Base.moduleroot"><code>Base.moduleroot</code></a>). To enable all debug logging, use the special value <code>all</code>.</p><p>To turn debug logging on from the REPL, set <code>ENV[&quot;JULIA_DEBUG&quot;]</code> to the name of the module of interest. Functions defined in the REPL belong to module <code>Main</code>; logging for them can be enabled like this:</p><pre><code class="language-julia-repl hljs">julia&gt; foo() = @debug &quot;foo&quot;
foo (generic function with 1 method)

julia&gt; foo()

julia&gt; ENV[&quot;JULIA_DEBUG&quot;] = Main
Main

julia&gt; foo()
┌ Debug: foo
└ @ Main REPL[1]:1
</code></pre><p>Use a comma separator to enable debug for multiple modules: <code>JULIA_DEBUG=loading,Main</code>.</p><h2 id="Examples"><a class="docs-heading-anchor" href="#Examples">Examples</a><a id="Examples-1"></a><a class="docs-heading-anchor-permalink" href="#Examples" title="Permalink"></a></h2><h3 id="Example:-Writing-log-events-to-a-file"><a class="docs-heading-anchor" href="#Example:-Writing-log-events-to-a-file">Example: Writing log events to a file</a><a id="Example:-Writing-log-events-to-a-file-1"></a><a class="docs-heading-anchor-permalink" href="#Example:-Writing-log-events-to-a-file" title="Permalink"></a></h3><p>Sometimes it can be useful to write log events to a file. Here is an example of how to use a task-local and global logger to write information to a text file:</p><pre><code class="language-julia-repl hljs"># Load the logging module
julia&gt; using Logging

# Open a textfile for writing
julia&gt; io = open(&quot;log.txt&quot;, &quot;w+&quot;)
IOStream(&lt;file log.txt&gt;)

# Create a simple logger
julia&gt; logger = SimpleLogger(io)
SimpleLogger(IOStream(&lt;file log.txt&gt;), Info, Dict{Any,Int64}())

# Log a task-specific message
julia&gt; with_logger(logger) do
           @info(&quot;a context specific log message&quot;)
       end

# Write all buffered messages to the file
julia&gt; flush(io)

# Set the global logger to logger
julia&gt; global_logger(logger)
SimpleLogger(IOStream(&lt;file log.txt&gt;), Info, Dict{Any,Int64}())

# This message will now also be written to the file
julia&gt; @info(&quot;a global log message&quot;)

# Close the file
julia&gt; close(io)</code></pre><h3 id="Example:-Enable-debug-level-messages"><a class="docs-heading-anchor" href="#Example:-Enable-debug-level-messages">Example: Enable debug-level messages</a><a id="Example:-Enable-debug-level-messages-1"></a><a class="docs-heading-anchor-permalink" href="#Example:-Enable-debug-level-messages" title="Permalink"></a></h3><p>Here is an example of creating a <a href="Logging.html#Base.CoreLogging.ConsoleLogger"><code>ConsoleLogger</code></a> that lets through any messages with log level higher than, or equal, to <a href="Logging.html#Logging.Debug"><code>Logging.Debug</code></a>.</p><pre><code class="language-julia-repl hljs">julia&gt; using Logging

# Create a ConsoleLogger that prints any log messages with level &gt;= Debug to stderr
julia&gt; debuglogger = ConsoleLogger(stderr, Logging.Debug)

# Enable debuglogger for a task
julia&gt; with_logger(debuglogger) do
           @debug &quot;a context specific log message&quot;
       end

# Set the global logger
julia&gt; global_logger(debuglogger)</code></pre><h2 id="Reference"><a class="docs-heading-anchor" href="#Reference">Reference</a><a id="Reference-1"></a><a class="docs-heading-anchor-permalink" href="#Reference" title="Permalink"></a></h2><h3 id="Logging-module"><a class="docs-heading-anchor" href="#Logging-module">Logging module</a><a id="Logging-module-1"></a><a class="docs-heading-anchor-permalink" href="#Logging-module" title="Permalink"></a></h3><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.Logging" href="#Logging.Logging"><code>Logging.Logging</code></a> — <span class="docstring-category">Module</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>Utilities for capturing, filtering and presenting streams of log events. Normally you don&#39;t need to import <code>Logging</code> to create log events; for this the standard logging macros such as <code>@info</code> are already exported by <code>Base</code> and available by default.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Logging/src/Logging.jl#L3-L8">source</a></section></article><h3 id="Creating-events"><a class="docs-heading-anchor" href="#Creating-events">Creating events</a><a id="Creating-events-1"></a><a class="docs-heading-anchor-permalink" href="#Creating-events" title="Permalink"></a></h3><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.@logmsg" href="#Logging.@logmsg"><code>Logging.@logmsg</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@debug message  [key=value | value ...]
@info  message  [key=value | value ...]
@warn  message  [key=value | value ...]
@error message  [key=value | value ...]

@logmsg level message [key=value | value ...]</code></pre><p>Create a log record with an informational <code>message</code>.  For convenience, four logging macros <code>@debug</code>, <code>@info</code>, <code>@warn</code> and <code>@error</code> are defined which log at the standard severity levels <code>Debug</code>, <code>Info</code>, <code>Warn</code> and <code>Error</code>.  <code>@logmsg</code> allows <code>level</code> to be set programmatically to any <code>LogLevel</code> or custom log level types.</p><p><code>message</code> should be an expression which evaluates to a string which is a human readable description of the log event.  By convention, this string will be formatted as markdown when presented.</p><p>The optional list of <code>key=value</code> pairs supports arbitrary user defined metadata which will be passed through to the logging backend as part of the log record.  If only a <code>value</code> expression is supplied, a key representing the expression will be generated using <a href="../base/base.html#Core.Symbol"><code>Symbol</code></a>. For example, <code>x</code> becomes <code>x=x</code>, and <code>foo(10)</code> becomes <code>Symbol(&quot;foo(10)&quot;)=foo(10)</code>.  For splatting a list of key value pairs, use the normal splatting syntax, <code>@info &quot;blah&quot; kws...</code>.</p><p>There are some keys which allow automatically generated log data to be overridden:</p><ul><li><code>_module=mod</code> can be used to specify a different originating module from the source location of the message.</li><li><code>_group=symbol</code> can be used to override the message group (this is normally derived from the base name of the source file).</li><li><code>_id=symbol</code> can be used to override the automatically generated unique message identifier.  This is useful if you need to very closely associate messages generated on different source lines.</li><li><code>_file=string</code> and <code>_line=integer</code> can be used to override the apparent source location of a log message.</li></ul><p>There&#39;s also some key value pairs which have conventional meaning:</p><ul><li><code>maxlog=integer</code> should be used as a hint to the backend that the message should be displayed no more than <code>maxlog</code> times.</li><li><code>exception=ex</code> should be used to transport an exception with a log message, often used with <code>@error</code>. An associated backtrace <code>bt</code> may be attached using the tuple <code>exception=(ex,bt)</code>.</li></ul><p><strong>Examples</strong></p><pre><code class="language-julia hljs">@debug &quot;Verbose debugging information.  Invisible by default&quot;
@info  &quot;An informational message&quot;
@warn  &quot;Something was odd.  You should pay attention&quot;
@error &quot;A non fatal error occurred&quot;

x = 10
@info &quot;Some variables attached to the message&quot; x a=42.0

@debug begin
    sA = sum(A)
    &quot;sum(A) = $sA is an expensive operation, evaluated only when `shouldlog` returns true&quot;
end

for i=1:10000
    @info &quot;With the default backend, you will only see (i = $i) ten times&quot;  maxlog=10
    @debug &quot;Algorithm1&quot; i progress=i/10000
end</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/logging/logging.jl#L277-L344">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.LogLevel" href="#Logging.LogLevel"><code>Logging.LogLevel</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">LogLevel(level)</code></pre><p>Severity/verbosity of a log record.</p><p>The log level provides a key against which potential log records may be filtered, before any other work is done to construct the log record data structure itself.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Logging.LogLevel(0) == Logging.Info
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/logging/logging.jl#L110-L124">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.Debug" href="#Logging.Debug"><code>Logging.Debug</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Debug</code></pre><p>Alias for <a href="Logging.html#Logging.LogLevel"><code>LogLevel(-1000)</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Logging/src/Logging.jl#L33-L37">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.Info" href="#Logging.Info"><code>Logging.Info</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Info</code></pre><p>Alias for <a href="Logging.html#Logging.LogLevel"><code>LogLevel(0)</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Logging/src/Logging.jl#L39-L43">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.Warn" href="#Logging.Warn"><code>Logging.Warn</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Warn</code></pre><p>Alias for <a href="Logging.html#Logging.LogLevel"><code>LogLevel(1000)</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Logging/src/Logging.jl#L45-L49">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.Error" href="#Logging.Error"><code>Logging.Error</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Error</code></pre><p>Alias for <a href="Logging.html#Logging.LogLevel"><code>LogLevel(2000)</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Logging/src/Logging.jl#L51-L55">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.BelowMinLevel" href="#Logging.BelowMinLevel"><code>Logging.BelowMinLevel</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">BelowMinLevel</code></pre><p>Alias for <a href="Logging.html#Logging.LogLevel"><code>LogLevel(-1_000_001)</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Logging/src/Logging.jl#L57-L61">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.AboveMaxLevel" href="#Logging.AboveMaxLevel"><code>Logging.AboveMaxLevel</code></a> — <span class="docstring-category">Constant</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AboveMaxLevel</code></pre><p>Alias for <a href="Logging.html#Logging.LogLevel"><code>LogLevel(1_000_001)</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/Logging/src/Logging.jl#L63-L67">source</a></section></article><h3 id="AbstractLogger-interface"><a class="docs-heading-anchor" href="#AbstractLogger-interface">Processing events with AbstractLogger</a><a id="AbstractLogger-interface-1"></a><a class="docs-heading-anchor-permalink" href="#AbstractLogger-interface" title="Permalink"></a></h3><p>Event processing is controlled by overriding functions associated with <code>AbstractLogger</code>:</p><table><tr><th style="text-align: left">Methods to implement</th><th style="text-align: left"></th><th style="text-align: left">Brief description</th></tr><tr><td style="text-align: left"><a href="Logging.html#Logging.handle_message"><code>Logging.handle_message</code></a></td><td style="text-align: left"></td><td style="text-align: left">Handle a log event</td></tr><tr><td style="text-align: left"><a href="Logging.html#Logging.shouldlog"><code>Logging.shouldlog</code></a></td><td style="text-align: left"></td><td style="text-align: left">Early filtering of events</td></tr><tr><td style="text-align: left"><a href="Logging.html#Logging.min_enabled_level"><code>Logging.min_enabled_level</code></a></td><td style="text-align: left"></td><td style="text-align: left">Lower bound for log level of accepted events</td></tr><tr><td style="text-align: left"><strong>Optional methods</strong></td><td style="text-align: left"><strong>Default definition</strong></td><td style="text-align: left"><strong>Brief description</strong></td></tr><tr><td style="text-align: left"><a href="Logging.html#Logging.catch_exceptions"><code>Logging.catch_exceptions</code></a></td><td style="text-align: left"><code>true</code></td><td style="text-align: left">Catch exceptions during event evaluation</td></tr></table><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.AbstractLogger" href="#Logging.AbstractLogger"><code>Logging.AbstractLogger</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>A logger controls how log records are filtered and dispatched.  When a log record is generated, the logger is the first piece of user configurable code which gets to inspect the record and decide what to do with it.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/logging/logging.jl#L25-L29">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.handle_message" href="#Logging.handle_message"><code>Logging.handle_message</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">handle_message(logger, level, message, _module, group, id, file, line; key1=val1, ...)</code></pre><p>Log a message to <code>logger</code> at <code>level</code>.  The logical location at which the message was generated is given by module <code>_module</code> and <code>group</code>; the source location by <code>file</code> and <code>line</code>. <code>id</code> is an arbitrary unique value (typically a <a href="../base/base.html#Core.Symbol"><code>Symbol</code></a>) to be used as a key to identify the log statement when filtering.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/logging/logging.jl#L32-L40">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.shouldlog" href="#Logging.shouldlog"><code>Logging.shouldlog</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">shouldlog(logger, level, _module, group, id)</code></pre><p>Return <code>true</code> when <code>logger</code> accepts a message at <code>level</code>, generated for <code>_module</code>, <code>group</code> and with unique log identifier <code>id</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/logging/logging.jl#L43-L48">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.min_enabled_level" href="#Logging.min_enabled_level"><code>Logging.min_enabled_level</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">min_enabled_level(logger)</code></pre><p>Return the minimum enabled level for <code>logger</code> for early filtering.  That is, the log level below or equal to which all messages are filtered.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/logging/logging.jl#L51-L56">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.catch_exceptions" href="#Logging.catch_exceptions"><code>Logging.catch_exceptions</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">catch_exceptions(logger)</code></pre><p>Return <code>true</code> if the logger should catch exceptions which happen during log record construction.  By default, messages are caught</p><p>By default all exceptions are caught to prevent log message generation from crashing the program.  This lets users confidently toggle little-used functionality - such as debug logging - in a production system.</p><p>If you want to use logging as an audit trail you should disable this for your logger type.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/logging/logging.jl#L59-L71">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.disable_logging" href="#Logging.disable_logging"><code>Logging.disable_logging</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">disable_logging(level)</code></pre><p>Disable all log messages at log levels equal to or less than <code>level</code>.  This is a <em>global</em> setting, intended to make debug logging extremely cheap when disabled.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">Logging.disable_logging(Logging.Info) # Disable debug and info</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/logging/logging.jl#L527-L538">source</a></section></article><h3 id="Using-Loggers"><a class="docs-heading-anchor" href="#Using-Loggers">Using Loggers</a><a id="Using-Loggers-1"></a><a class="docs-heading-anchor-permalink" href="#Using-Loggers" title="Permalink"></a></h3><p>Logger installation and inspection:</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.global_logger" href="#Logging.global_logger"><code>Logging.global_logger</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">global_logger()</code></pre><p>Return the global logger, used to receive messages when no specific logger exists for the current task.</p><pre><code class="nohighlight hljs">global_logger(logger)</code></pre><p>Set the global logger to <code>logger</code>, and return the previous global logger.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/logging/logging.jl#L595-L604">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.with_logger" href="#Logging.with_logger"><code>Logging.with_logger</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">with_logger(function, logger)</code></pre><p>Execute <code>function</code>, directing all log messages to <code>logger</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia hljs">function test(x)
    @info &quot;x = $x&quot;
end

with_logger(logger) do
    test(1)
    test([1,2])
end</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/logging/logging.jl#L613-L630">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.current_logger" href="#Logging.current_logger"><code>Logging.current_logger</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">current_logger()</code></pre><p>Return the logger for the current task, or the global logger if none is attached to the task.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/logging/logging.jl#L635-L640">source</a></section></article><p>Loggers that are supplied with the system:</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.NullLogger" href="#Logging.NullLogger"><code>Logging.NullLogger</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">NullLogger()</code></pre><p>Logger which disables all messages and produces no output - the logger equivalent of /dev/null.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/logging/logging.jl#L94-L99">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.CoreLogging.ConsoleLogger" href="#Base.CoreLogging.ConsoleLogger"><code>Base.CoreLogging.ConsoleLogger</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ConsoleLogger([stream,] min_level=Info; meta_formatter=default_metafmt,
              show_limited=true, right_justify=0)</code></pre><p>Logger with formatting optimized for readability in a text console, for example interactive work with the Julia REPL.</p><p>Log levels less than <code>min_level</code> are filtered out.</p><p>Message formatting can be controlled by setting keyword arguments:</p><ul><li><code>meta_formatter</code> is a function which takes the log event metadata <code>(level, _module, group, id, file, line)</code> and returns a color (as would be passed to printstyled), prefix and suffix for the log message.  The default is to prefix with the log level and a suffix containing the module, file and line location.</li><li><code>show_limited</code> limits the printing of large data structures to something which can fit on the screen by setting the <code>:limit</code> <code>IOContext</code> key during formatting.</li><li><code>right_justify</code> is the integer column which log metadata is right justified at. The default is zero (metadata goes on its own line).</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/logging/ConsoleLogger.jl#L3-L24">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Logging.SimpleLogger" href="#Logging.SimpleLogger"><code>Logging.SimpleLogger</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">SimpleLogger([stream,] min_level=Info)</code></pre><p>Simplistic logger for logging all messages with level greater than or equal to <code>min_level</code> to <code>stream</code>. If stream is closed then messages with log level greater or equal to <code>Warn</code> will be logged to <code>stderr</code> and below to <code>stdout</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/logging/logging.jl#L648-L654">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="LinearAlgebra.html">« Linear Algebra</a><a class="docs-footer-nextpage" href="Markdown.html">Markdown »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
