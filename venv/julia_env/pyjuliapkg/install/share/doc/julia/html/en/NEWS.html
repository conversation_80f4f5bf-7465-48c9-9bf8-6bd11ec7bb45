<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Julia v1.11 Release Notes · The Julia Language</title><meta name="title" content="Julia v1.11 Release Notes · The Julia Language"/><meta property="og:title" content="Julia v1.11 Release Notes · The Julia Language"/><meta property="twitter:title" content="Julia v1.11 Release Notes · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL="."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="assets/documenter.js"></script><script src="search_index.js"></script><script src="siteinfo.js"></script><script src="../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="assets/themeswap.js"></script><link href="assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="index.html"><img class="docs-light-only" src="assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="index.html">Julia Documentation</a></li><li class="is-active"><a class="tocitem" href="NEWS.html">Julia v1.11 Release Notes</a><ul class="internal"><li><a class="tocitem" href="#New-language-features"><span>New language features</span></a></li><li><a class="tocitem" href="#Language-changes"><span>Language changes</span></a></li><li><a class="tocitem" href="#Compiler/Runtime-improvements"><span>Compiler/Runtime improvements</span></a></li><li><a class="tocitem" href="#Command-line-option-changes"><span>Command-line option changes</span></a></li><li><a class="tocitem" href="#Multi-threading-changes"><span>Multi-threading changes</span></a></li><li><a class="tocitem" href="#Build-system-changes"><span>Build system changes</span></a></li><li><a class="tocitem" href="#New-library-functions"><span>New library functions</span></a></li><li><a class="tocitem" href="#New-library-features"><span>New library features</span></a></li><li><a class="tocitem" href="#Standard-library-changes"><span>Standard library changes</span></a></li><li><a class="tocitem" href="#Deprecated-or-removed"><span>Deprecated or removed</span></a></li><li><a class="tocitem" href="#External-dependencies"><span>External dependencies</span></a></li><li><a class="tocitem" href="#Tooling-Improvements"><span>Tooling Improvements</span></a></li></ul></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="manual/installation.html">Installation</a></li><li><a class="tocitem" href="manual/variables.html">Variables</a></li><li><a class="tocitem" href="manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="manual/strings.html">Strings</a></li><li><a class="tocitem" href="manual/functions.html">Functions</a></li><li><a class="tocitem" href="manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="manual/types.html">Types</a></li><li><a class="tocitem" href="manual/methods.html">Methods</a></li><li><a class="tocitem" href="manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="manual/modules.html">Modules</a></li><li><a class="tocitem" href="manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="manual/profile.html">Profiling</a></li><li><a class="tocitem" href="manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="base/base.html">Essentials</a></li><li><a class="tocitem" href="base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="base/math.html">Mathematics</a></li><li><a class="tocitem" href="base/numbers.html">Numbers</a></li><li><a class="tocitem" href="base/strings.html">Strings</a></li><li><a class="tocitem" href="base/arrays.html">Arrays</a></li><li><a class="tocitem" href="base/parallel.html">Tasks</a></li><li><a class="tocitem" href="base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="base/constants.html">Constants</a></li><li><a class="tocitem" href="base/file.html">Filesystem</a></li><li><a class="tocitem" href="base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="base/c.html">C Interface</a></li><li><a class="tocitem" href="base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="stdlib/Future.html">Future</a></li><li><a class="tocitem" href="stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="devdocs/types.html">More about types</a></li><li><a class="tocitem" href="devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li class="is-active"><a href="NEWS.html">Julia v1.11 Release Notes</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="NEWS.html">Julia v1.11 Release Notes</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/NEWS.md" title="View source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Julia-v1.11-Release-Notes"><a class="docs-heading-anchor" href="#Julia-v1.11-Release-Notes">Julia v1.11 Release Notes</a><a id="Julia-v1.11-Release-Notes-1"></a><a class="docs-heading-anchor-permalink" href="#Julia-v1.11-Release-Notes" title="Permalink"></a></h1><h2 id="New-language-features"><a class="docs-heading-anchor" href="#New-language-features">New language features</a><a id="New-language-features-1"></a><a class="docs-heading-anchor-permalink" href="#New-language-features" title="Permalink"></a></h2><ul><li>New <code>Memory</code> type that provides a lower-level container as an alternative to <code>Array</code>. <code>Memory</code> has less overhead and a faster constructor, making it a good choice for situations that do not need all the features of <code>Array</code> (e.g. multiple dimensions). Most of the <code>Array</code> type is now implemented in Julia on top of <code>Memory</code>, leading to significant speedups for several functions (e.g. <code>push!</code>) as well as more maintainable code (<a href="https://github.com/JuliaLang/julia/issues/51319">#51319</a>).</li><li><code>public</code> is a new keyword. Symbols marked with <code>public</code> are considered public API. Symbols marked with <code>export</code> are now also treated as public API. The difference between <code>public</code> and <code>export</code> is that <code>public</code> names do not become available when <code>using</code> a package/module (<a href="https://github.com/JuliaLang/julia/issues/50105">#50105</a>).</li><li><code>ScopedValue</code> implements dynamic scope with inheritance across tasks (<a href="https://github.com/JuliaLang/julia/issues/50958">#50958</a>).</li><li><code>Manifest.toml</code> files can now be renamed in the format <code>Manifest-v{major}.{minor}.toml</code> to be preferentially picked up by the given julia version. i.e. in the same folder, a <code>Manifest-v1.11.toml</code> would be used by v1.11 and <code>Manifest.toml</code> by every other julia version. This makes managing environments for multiple julia versions at the same time easier (<a href="https://github.com/JuliaLang/julia/issues/43845">#43845</a>).</li><li>Support for Unicode 15.1 (<a href="https://github.com/JuliaLang/julia/issues/51799">#51799</a>).</li></ul><h2 id="Language-changes"><a class="docs-heading-anchor" href="#Language-changes">Language changes</a><a id="Language-changes-1"></a><a class="docs-heading-anchor-permalink" href="#Language-changes" title="Permalink"></a></h2><ul><li>During precompilation, <code>atexit</code> hooks now run before saving the output file. This allows users to safely tear down background state (such as closing <code>Timer</code>s and sending disconnect notifications to heartbeat tasks) and cleanup other resources when the program wants to begin exiting.</li><li>Code coverage and malloc tracking is no longer generated during the package precompilation stage. Further, during these modes pkgimage caches are now used for packages that are not being tracked. This means that coverage testing (the default for <code>julia-actions/julia-runtest</code>) will by default use pkgimage caches for all other packages than the package being tested, likely meaning faster test execution (<a href="https://github.com/JuliaLang/julia/issues/52123">#52123</a>).</li><li>Specifying a path in <code>JULIA_DEPOT_PATH</code> now results in the expansion of empty strings to omit the default user depot (<a href="https://github.com/JuliaLang/julia/issues/51448">#51448</a>).</li><li>Precompilation cache files are now relocatable and their validity is now verified through a content hash of their source files instead of their <code>mtime</code> (<a href="https://github.com/JuliaLang/julia/issues/49866">#49866</a>).</li><li>Extensions may now depend on other extensions, if their triggers include all triggers of any extension they wish to depend upon (+ at least one other trigger). Ext-to-ext dependencies that don&#39;t meet this requirement are now blocked from using <code>Base.get_extension</code> during pre- compilation, to prevent extension cycles <a href="https://github.com/JuliaLang/julia/issues/55557">#55557</a>.</li></ul><h2 id="Compiler/Runtime-improvements"><a class="docs-heading-anchor" href="#Compiler/Runtime-improvements">Compiler/Runtime improvements</a><a id="Compiler/Runtime-improvements-1"></a><a class="docs-heading-anchor-permalink" href="#Compiler/Runtime-improvements" title="Permalink"></a></h2><ul><li>Updated GC heuristics to count allocated pages instead of individual objects (<a href="https://github.com/JuliaLang/julia/issues/50144">#50144</a>).</li><li>Added support for annotating <code>Base.@assume_effects</code> on code blocks (<a href="https://github.com/JuliaLang/julia/issues/52400">#52400</a>).</li></ul><h2 id="Command-line-option-changes"><a class="docs-heading-anchor" href="#Command-line-option-changes">Command-line option changes</a><a id="Command-line-option-changes-1"></a><a class="docs-heading-anchor-permalink" href="#Command-line-option-changes" title="Permalink"></a></h2><ul><li>The entry point for Julia has been standardized to <code>Main.main(args)</code>. This must be explicitly opted into using the <code>@main</code> macro (see the docstring for further details). When opted-in, and <code>julia</code> is invoked to run a script or expression (i.e. using <code>julia script.jl</code> or <code>julia -e expr</code>), <code>julia</code> will subsequently run the <code>Main.main</code> function automatically. This is intended to unify script and compilation workflows, where code loading may happen in the compiler and execution of <code>Main.main</code> may happen in the resulting executable. For interactive use, there is no semantic difference between defining a <code>main</code> function and executing the code directly at the end of the script (<a href="https://github.com/JuliaLang/julia/issues/50974">#50974</a>).</li><li>The <code>--compiled-modules</code> and <code>--pkgimages</code> flags can now be set to <code>existing</code>, which will cause Julia to consider loading existing cache files, but not to create new ones (<a href="https://github.com/JuliaLang/julia/issues/50586">#50586</a>, <a href="https://github.com/JuliaLang/julia/issues/52573">#52573</a>).</li><li>The <code>--project</code> argument now accepts <code>@script</code> to give a path to a directory with a Project.toml relative to the passed script file. <code>--project=@script/foo</code> for the <code>foo</code> subdirectory. If no path is given after (i.e. <code>--project=@script</code>) then (like <code>--project=@.</code>) the directory and its parents are searched for a Project.toml (<a href="https://github.com/JuliaLang/julia/issues/50864">#50864</a> and <a href="https://github.com/JuliaLang/julia/issues/53352">#53352</a>)</li></ul><h2 id="Multi-threading-changes"><a class="docs-heading-anchor" href="#Multi-threading-changes">Multi-threading changes</a><a id="Multi-threading-changes-1"></a><a class="docs-heading-anchor-permalink" href="#Multi-threading-changes" title="Permalink"></a></h2><ul><li><code>Threads.@threads</code> now supports the <code>:greedy</code> scheduler, intended for non-uniform workloads (<a href="https://github.com/JuliaLang/julia/issues/52096">#52096</a>).</li><li>A new public (but unexported) struct <code>Base.Lockable{T, L&lt;:AbstractLock}</code> makes it easy to bundle a resource and its lock together (<a href="https://github.com/JuliaLang/julia/issues/52898">#52898</a>).</li></ul><h2 id="Build-system-changes"><a class="docs-heading-anchor" href="#Build-system-changes">Build system changes</a><a id="Build-system-changes-1"></a><a class="docs-heading-anchor-permalink" href="#Build-system-changes" title="Permalink"></a></h2><ul><li>There is a new <code>Makefile</code> to build Julia and LLVM using the profile-guided and link-time optimizations (PGO and LTO) strategies, see <code>contrib/pgo-lto/Makefile</code> (<a href="https://github.com/JuliaLang/julia/issues/45641">#45641</a>).</li></ul><h2 id="New-library-functions"><a class="docs-heading-anchor" href="#New-library-functions">New library functions</a><a id="New-library-functions-1"></a><a class="docs-heading-anchor-permalink" href="#New-library-functions" title="Permalink"></a></h2><ul><li>Three new types around the idea of text with &quot;annotations&quot; (<code>Pair{Symbol, Any}</code> entries, e.g. <code>:lang =&gt; &quot;en&quot;</code> or <code>:face =&gt; :magenta</code>). These annotations are preserved across operations (e.g. string concatenation with <code>*</code>) when possible.<ul><li><code>AnnotatedString</code> is a new <code>AbstractString</code> type. It wraps an underlying string and allows for annotations to be attached to regions of the string. This type is used extensively in the new <code>StyledStrings</code> standard library to hold styling information.</li><li><code>AnnotatedChar</code> is a new <code>AbstractChar</code> type. It wraps another char and holds a list of annotations that apply to it.</li><li><code>AnnotatedIOBuffer</code> is a new <code>IO</code> type that mimics an <code>IOBuffer</code>, but has specialised <code>read</code>/<code>write</code> methods for annotated content. This can be thought of both as a &quot;string builder&quot; of sorts and also as glue between annotated and unannotated content.</li></ul></li><li><code>in!(x, s::AbstractSet)</code> will return whether <code>x</code> is in <code>s</code>, and insert <code>x</code> in <code>s</code> if not (<a href="https://github.com/JuliaLang/julia/issues/45156">#45156</a>, <a href="https://github.com/JuliaLang/julia/issues/51636">#51636</a>).</li><li>The new <code>Libc.mkfifo</code> function wraps the <code>mkfifo</code> C function on Unix platforms (<a href="https://github.com/JuliaLang/julia/issues/34587">#34587</a>).</li><li><code>logrange(start, stop; length)</code> makes a range of constant ratio, instead of constant step (<a href="https://github.com/JuliaLang/julia/issues/39071">#39071</a>)</li><li><code>copyuntil(out, io, delim)</code> and <code>copyline(out, io)</code> copy data into an <code>out::IO</code> stream (<a href="https://github.com/JuliaLang/julia/issues/48273">#48273</a>).</li><li><code>eachrsplit(string, pattern)</code> iterates split substrings right to left (<a href="https://github.com/JuliaLang/julia/issues/51646">#51646</a>).</li><li><code>Sys.username()</code> can be used to return the current user&#39;s username (<a href="https://github.com/JuliaLang/julia/issues/51897">#51897</a>).</li><li><code>Sys.isreadable(), Sys.iswritable()</code> can be used to check if the current user has access permissions that permit reading and writing, respectively. (<a href="https://github.com/JuliaLang/julia/issues/53320">#53320</a>).</li><li><code>GC.logging_enabled()</code> can be used to test whether GC logging has been enabled via <code>GC.enable_logging</code> (<a href="https://github.com/JuliaLang/julia/issues/51647">#51647</a>).</li><li><code>IdSet</code> is now exported from Base and considered public (<a href="https://github.com/JuliaLang/julia/issues/53262">#53262</a>).</li><li><code>@time</code> now reports a count of any lock conflicts where a <code>ReentrantLock</code> had to wait, plus a new macro <code>@lock_conflicts</code> which returns that count (<a href="https://github.com/JuliaLang/julia/issues/52883">#52883</a>).</li><li>The new macro <code>Base.Cartesian.@ncallkw</code> is analogous to <code>Base.Cartesian.@ncall</code>, but allows adding keyword arguments to the function call (<a href="https://github.com/JuliaLang/julia/issues/51501">#51501</a>).</li><li>New function <code>Docs.hasdoc(module, symbol)</code> tells whether a name has a docstring (<a href="https://github.com/JuliaLang/julia/issues/52139">#52139</a>).</li><li>New function <code>Docs.undocumented_names(module)</code> returns a module&#39;s undocumented public names (<a href="https://github.com/JuliaLang/julia/issues/52413">#52413</a>).</li></ul><h2 id="New-library-features"><a class="docs-heading-anchor" href="#New-library-features">New library features</a><a id="New-library-features-1"></a><a class="docs-heading-anchor-permalink" href="#New-library-features" title="Permalink"></a></h2><ul><li><code>invmod(n, T)</code> where <code>T</code> is a native integer type now computes the modular inverse of <code>n</code> in the modular integer ring that <code>T</code> defines (<a href="https://github.com/JuliaLang/julia/issues/52180">#52180</a>).</li><li><code>invmod(n)</code> is an abbreviation for <code>invmod(n, typeof(n))</code> for native integer types (<a href="https://github.com/JuliaLang/julia/issues/52180">#52180</a>).</li><li><code>replace(string, pattern...)</code> now supports an optional <code>IO</code> argument to write the output to a stream rather than returning a string (<a href="https://github.com/JuliaLang/julia/issues/48625">#48625</a>).</li><li>New methods <code>allequal(f, itr)</code> and <code>allunique(f, itr)</code> taking a predicate function (<a href="https://github.com/JuliaLang/julia/issues/47679">#47679</a>).</li><li><code>sizehint!(s, n)</code> now supports an optional <code>shrink</code> argument to disable shrinking (<a href="https://github.com/JuliaLang/julia/issues/51929">#51929</a>).</li><li>Passing an <code>IOBuffer</code> as a stdout argument for <code>Process</code> spawn now works as expected, synchronized with <code>wait</code> or <code>success</code>, so a <code>Base.BufferStream</code> is no longer required there for correctness to avoid data races (<a href="https://github.com/JuliaLang/julia/issues/52461">#52461</a>).</li><li>After a process exits, <code>closewrite</code> will no longer be automatically called on the stream passed to it. Call <code>wait</code> on the process instead to ensure the content is fully written, then call <code>closewrite</code> manually to avoid data races, or use the callback form of <code>open</code> to have all that handled automatically (<a href="https://github.com/JuliaLang/julia/issues/52461">#52461</a>).</li><li><code>@timed</code> now additionally returns the elapsed compilation and recompilation time (<a href="https://github.com/JuliaLang/julia/issues/52889">#52889</a>).</li><li><code>filter</code> can now act on a <code>NamedTuple</code> (<a href="https://github.com/JuliaLang/julia/issues/50795">#50795</a>).</li><li><code>Iterators.cycle(iter, n)</code> runs over <code>iter</code> a fixed number of times, instead of forever (<a href="https://github.com/JuliaLang/julia/issues/47354">#47354</a>).</li><li><code>zero(::AbstractArray)</code> now applies recursively, so <code>zero([[1,2],[3,4,5]])</code> now produces the additive identity <code>[[0,0],[0,0,0]]</code> rather than erroring (<a href="https://github.com/JuliaLang/julia/issues/38064">#38064</a>).</li><li><code>include_dependency(path; track_content=true)</code> allows switching from using <code>mtime</code> to hashing of the precompilation dependency in order to restore relocatability of precompilation caches (<a href="https://github.com/JuliaLang/julia/issues/51798">#51798</a>).</li></ul><h2 id="Standard-library-changes"><a class="docs-heading-anchor" href="#Standard-library-changes">Standard library changes</a><a id="Standard-library-changes-1"></a><a class="docs-heading-anchor-permalink" href="#Standard-library-changes" title="Permalink"></a></h2><ul><li>The fallback method <code>write(::IO, ::AbstractArray)</code> used to recursively call <code>write</code> on each element, but now writes the in-memory representation of each value. For example, <code>write(io, &#39;a&#39;:&#39;b&#39;)</code> now writes 4 bytes for each character, instead of writing the UTF-8 representation of each character. The new format is compatible with that used by <code>Array</code>, making it possible to use <code>read!</code> to get the data back (<a href="https://github.com/JuliaLang/julia/issues/42593">#42593</a>).</li><li>It&#39;s not possible to define <code>length</code> for stateful iterators in a generally consistent manner. The potential for silently incorrect results for <code>Stateful</code> iterators is addressed by deleting the <code>length(::Stateful)</code> method. The last type parameter of <code>Stateful</code> is gone, too. Issue: (<a href="https://github.com/JuliaLang/julia/issues/47790">#47790</a>), PR: (<a href="https://github.com/JuliaLang/julia/issues/51747">#51747</a>).</li></ul><h4 id="Package-Manager"><a class="docs-heading-anchor" href="#Package-Manager">Package Manager</a><a id="Package-Manager-1"></a><a class="docs-heading-anchor-permalink" href="#Package-Manager" title="Permalink"></a></h4><ul><li>It is now possible to specify &quot;sources&quot; for packages in a <code>[sources]</code> section in Project.toml. This can be used to add non-registered normal or test dependencies.</li><li>Pkg now obeys <code>[compat]</code> bounds for <code>julia</code> and raises an error if the version of the running Julia binary is incompatible with the bounds in <code>Project.toml</code>. Pkg has always obeyed this compat when working with Registry packages. This change affects mostly local packages</li><li><code>pkg&gt; add</code> and <code>Pkg.add</code> will now add compat entries for new direct dependencies if the active environment is a package (has a <code>name</code> and <code>uuid</code> entry).</li><li>Dependencies can now be directly added as weak deps or extras via the <code>pkg&gt; add --weak/extra Foo</code> or <code>Pkg.add(&quot;Foo&quot;, target=:weakdeps/:extras)</code> forms.</li></ul><h4 id="StyledStrings"><a class="docs-heading-anchor" href="#StyledStrings">StyledStrings</a><a id="StyledStrings-1"></a><a class="docs-heading-anchor-permalink" href="#StyledStrings" title="Permalink"></a></h4><ul><li>A new standard library for handling styling in a more comprehensive and structured way (<a href="https://github.com/JuliaLang/julia/issues/49586">#49586</a>).</li><li>The new <code>Faces</code> struct serves as a container for text styling information (think typeface, as well as color and decoration), and comes with a framework to provide a convenient, extensible (via <code>addface!</code>), and customisable (with a user&#39;s <code>Faces.toml</code> and <code>loadfaces!</code>) approach to styled content (<a href="https://github.com/JuliaLang/julia/issues/49586">#49586</a>).</li><li>The new <code>@styled_str</code> string macro provides a convenient way of creating a <code>AnnotatedString</code> with various faces or other attributes applied (<a href="https://github.com/JuliaLang/julia/issues/49586">#49586</a>).</li></ul><h4 id="Libdl"><a class="docs-heading-anchor" href="#Libdl">Libdl</a><a id="Libdl-1"></a><a class="docs-heading-anchor-permalink" href="#Libdl" title="Permalink"></a></h4><ul><li>A new <code>LazyLibrary</code> type is exported from <code>Libdl</code> for use in building chained lazy library loads, primarily to be used within JLLs (<a href="https://github.com/JuliaLang/julia/issues/50074">#50074</a>).</li></ul><h4 id="LinearAlgebra"><a class="docs-heading-anchor" href="#LinearAlgebra">LinearAlgebra</a><a id="LinearAlgebra-1"></a><a class="docs-heading-anchor-permalink" href="#LinearAlgebra" title="Permalink"></a></h4><ul><li><code>cbrt(::AbstractMatrix{&lt;:Real})</code> is now defined and returns real-valued matrix cube roots of real-valued matrices (<a href="https://github.com/JuliaLang/julia/issues/50661">#50661</a>).</li><li><code>eigvals/eigen(A, bunchkaufman(B))</code> and <code>eigvals/eigen(A, lu(B))</code>, which utilize the Bunchkaufman (LDL) and LU decomposition of <code>B</code>,  respectively, now efficiently compute the generalized eigenvalues (<code>eigen</code>: and eigenvectors) of <code>A</code> and <code>B</code>. Note: The second  argument is the output of <code>bunchkaufman</code> or <code>lu</code> (<a href="https://github.com/JuliaLang/julia/issues/50471">#50471</a>).</li><li>There is now a specialized dispatch for <code>eigvals/eigen(::Hermitian{&lt;:Tridiagonal})</code> which performs a similarity transformation to create a real symmetric tridiagonal matrix, and solve that using the LAPACK routines (<a href="https://github.com/JuliaLang/julia/issues/49546">#49546</a>).</li><li>Structured matrices now retain either the axes of the parent (for <code>Symmetric</code>/<code>Hermitian</code>/<code>AbstractTriangular</code>/<code>UpperHessenberg</code>), or that of the principal diagonal (for banded matrices) (<a href="https://github.com/JuliaLang/julia/issues/52480">#52480</a>).</li><li><code>bunchkaufman</code> and <code>bunchkaufman!</code> now work for any <code>AbstractFloat</code>, <code>Rational</code> and their complex variants. <code>bunchkaufman</code> now supports <code>Integer</code> types, by making an internal conversion to <code>Rational{BigInt}</code>. Added new function <code>inertia</code> that computes the inertia of the diagonal factor given by the <code>BunchKaufman</code> factorization object of a real symmetric or Hermitian matrix. For complex symmetric matrices, <code>inertia</code> only computes the number of zero eigenvalues of the diagonal factor (<a href="https://github.com/JuliaLang/julia/issues/51487">#51487</a>).</li><li>Packages that specialize matrix-matrix <code>mul!</code> with a method signature of the form <code>mul!(::AbstractMatrix, ::MyMatrix, ::AbstractMatrix, ::Number, ::Number)</code> no longer encounter method ambiguities when interacting with <code>LinearAlgebra</code>. Previously, ambiguities used to arise when multiplying a <code>MyMatrix</code> with a structured matrix type provided by LinearAlgebra, such as <code>AbstractTriangular</code>, which used to necessitate additional methods to resolve such ambiguities. Similar sources of ambiguities have also been removed for matrix-vector <code>mul!</code> operations (<a href="https://github.com/JuliaLang/julia/issues/52837">#52837</a>).</li><li><code>lu</code> and <code>issuccess(::LU)</code> now accept an <code>allowsingular</code> keyword argument. When set to <code>true</code>, a valid factorization with rank-deficient U factor will be treated as success instead of throwing an error. Such factorizations are now shown by printing the factors together with a &quot;rank-deficient&quot; note rather than printing a &quot;Failed Factorization&quot; message (<a href="https://github.com/JuliaLang/julia/issues/52957">#52957</a>).</li></ul><h4 id="Random"><a class="docs-heading-anchor" href="#Random">Random</a><a id="Random-1"></a><a class="docs-heading-anchor-permalink" href="#Random" title="Permalink"></a></h4><ul><li><code>rand</code> now supports sampling over <code>Tuple</code> types (<a href="https://github.com/JuliaLang/julia/issues/35856">#35856</a>, <a href="https://github.com/JuliaLang/julia/issues/50251">#50251</a>).</li><li><code>rand</code> now supports sampling over <code>Pair</code> types (<a href="https://github.com/JuliaLang/julia/issues/28705">#28705</a>).</li><li>When seeding RNGs provided by <code>Random</code>, negative integer seeds can now be used (<a href="https://github.com/JuliaLang/julia/issues/51416">#51416</a>).</li><li>Seedable random number generators from <code>Random</code> can now be seeded by a string, e.g. <code>seed!(rng, &quot;a random seed&quot;)</code> (<a href="https://github.com/JuliaLang/julia/issues/51527">#51527</a>).</li></ul><h4 id="REPL"><a class="docs-heading-anchor" href="#REPL">REPL</a><a id="REPL-1"></a><a class="docs-heading-anchor-permalink" href="#REPL" title="Permalink"></a></h4><ul><li>Tab complete hints now show in lighter text while typing in the repl. To disable set <code>Base.active_repl.options.hint_tab_completes = false</code> interactively, or in startup.jl:<pre><code class="nohighlight hljs">if VERSION &gt;= v&quot;1.11.0-0&quot;
  atreplinit() do repl
      repl.options.hint_tab_completes = false
  end
end</code></pre>(<a href="https://github.com/JuliaLang/julia/issues/51229">#51229</a>).</li><li>Meta-M with an empty prompt now toggles the contextual module between the previous non-Main contextual module and Main so that switching back and forth is simple (<a href="https://github.com/JuliaLang/julia/issues/51616">#51616</a>, <a href="https://github.com/JuliaLang/julia/issues/52670">#52670</a>).</li></ul><h4 id="Dates"><a class="docs-heading-anchor" href="#Dates">Dates</a><a id="Dates-1"></a><a class="docs-heading-anchor-permalink" href="#Dates" title="Permalink"></a></h4><p>The undocumented function <code>adjust</code> is no longer exported but is now documented (<a href="https://github.com/JuliaLang/julia/issues/53092">#53092</a>).</p><h4 id="Statistics"><a class="docs-heading-anchor" href="#Statistics">Statistics</a><a id="Statistics-1"></a><a class="docs-heading-anchor-permalink" href="#Statistics" title="Permalink"></a></h4><ul><li>Statistics is now an upgradeable standard library (<a href="https://github.com/JuliaLang/julia/issues/46501">#46501</a>).</li></ul><h4 id="Distributed"><a class="docs-heading-anchor" href="#Distributed">Distributed</a><a id="Distributed-1"></a><a class="docs-heading-anchor-permalink" href="#Distributed" title="Permalink"></a></h4><ul><li><code>pmap</code> now defaults to using a <code>CachingPool</code> (<a href="https://github.com/JuliaLang/julia/issues/33892">#33892</a>).</li></ul><h2 id="Deprecated-or-removed"><a class="docs-heading-anchor" href="#Deprecated-or-removed">Deprecated or removed</a><a id="Deprecated-or-removed-1"></a><a class="docs-heading-anchor-permalink" href="#Deprecated-or-removed" title="Permalink"></a></h2><ul><li><code>Base.map</code>, <code>Iterators.map</code>, and <code>foreach</code> lost their single-argument methods (<a href="https://github.com/JuliaLang/julia/issues/52631">#52631</a>).</li></ul><h2 id="External-dependencies"><a class="docs-heading-anchor" href="#External-dependencies">External dependencies</a><a id="External-dependencies-1"></a><a class="docs-heading-anchor-permalink" href="#External-dependencies" title="Permalink"></a></h2><ul><li>The libuv library has been updated from a base of v1.44.2 to v1.48.0 (<a href="https://github.com/JuliaLang/julia/issues/49937">#49937</a>).</li><li><code>tput</code> is no longer called to check terminal capabilities; it has been replaced with a pure-Julia terminfo parser (<a href="https://github.com/JuliaLang/julia/issues/50797">#50797</a>).</li><li>The terminal info database, <code>terminfo</code>, is now vendored by default, providing a better REPL user experience when <code>terminfo</code> is not available on the system. Julia can be built without vendoring the database using the Makefile option <code>WITH_TERMINFO=0</code>. (<a href="https://github.com/JuliaLang/julia/issues/55411">#55411</a>)</li></ul><h2 id="Tooling-Improvements"><a class="docs-heading-anchor" href="#Tooling-Improvements">Tooling Improvements</a><a id="Tooling-Improvements-1"></a><a class="docs-heading-anchor-permalink" href="#Tooling-Improvements" title="Permalink"></a></h2><ul><li>CI now performs limited automatic typo detection on all PRs. If you merge a PR with a failing typo CI check, then the reported typos will be automatically ignored in future CI runs on PRs that edit those same files (<a href="https://github.com/JuliaLang/julia/issues/51704">#51704</a>).</li></ul></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="index.html">« Julia Documentation</a><a class="docs-footer-nextpage" href="manual/getting-started.html">Getting Started »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
