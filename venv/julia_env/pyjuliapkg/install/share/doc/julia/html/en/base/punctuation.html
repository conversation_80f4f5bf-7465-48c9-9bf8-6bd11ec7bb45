<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Punctuation · The Julia Language</title><meta name="title" content="Punctuation · The Julia Language"/><meta property="og:title" content="Punctuation · The Julia Language"/><meta property="twitter:title" content="Punctuation · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox" checked/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="base.html">Essentials</a></li><li><a class="tocitem" href="collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="math.html">Mathematics</a></li><li><a class="tocitem" href="numbers.html">Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="arrays.html">Arrays</a></li><li><a class="tocitem" href="parallel.html">Tasks</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="constants.html">Constants</a></li><li><a class="tocitem" href="file.html">Filesystem</a></li><li><a class="tocitem" href="io-network.html">I/O and Network</a></li><li class="is-active"><a class="tocitem" href="punctuation.html">Punctuation</a></li><li><a class="tocitem" href="sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="c.html">C Interface</a></li><li><a class="tocitem" href="libc.html">C Standard Library</a></li><li><a class="tocitem" href="stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Base</a></li><li class="is-active"><a href="punctuation.html">Punctuation</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="punctuation.html">Punctuation</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/base/punctuation.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="man-punctuation"><a class="docs-heading-anchor" href="#man-punctuation">Punctuation</a><a id="man-punctuation-1"></a><a class="docs-heading-anchor-permalink" href="#man-punctuation" title="Permalink"></a></h1><p>Extended documentation for mathematical symbols &amp; functions is <a href="math.html#math-ops">here</a>.</p><table><tr><th style="text-align: left">symbol</th><th style="text-align: left">meaning</th></tr><tr><td style="text-align: left"><code>@</code></td><td style="text-align: left">the at-sign marks a <a href="../manual/metaprogramming.html#man-macros">macro</a> invocation; optionally followed by an argument list</td></tr><tr><td style="text-align: left"><a href="math.html#Base.:!"><code>!</code></a></td><td style="text-align: left">an exclamation mark is a prefix operator for logical negation (&quot;not&quot;)</td></tr><tr><td style="text-align: left"><code>a!</code></td><td style="text-align: left">function names that end with an exclamation mark modify one or more of their arguments by convention</td></tr><tr><td style="text-align: left"><code>#</code></td><td style="text-align: left">the number sign (or hash or pound) character begins single line comments</td></tr><tr><td style="text-align: left"><code>#=</code></td><td style="text-align: left">when followed by an equals sign, it begins a multi-line comment (these are nestable)</td></tr><tr><td style="text-align: left"><code>=#</code></td><td style="text-align: left">end a multi-line comment by immediately preceding the number sign with an equals sign</td></tr><tr><td style="text-align: left"><code>$</code></td><td style="text-align: left">the dollar sign is used for <a href="../manual/strings.html#string-interpolation">string</a> and <a href="../manual/metaprogramming.html#man-expression-interpolation">expression</a> interpolation</td></tr><tr><td style="text-align: left"><a href="math.html#Base.rem"><code>%</code></a></td><td style="text-align: left">the percent symbol is the remainder operator</td></tr><tr><td style="text-align: left"><a href="math.html#Base.:^-Tuple{Number, Number}"><code>^</code></a></td><td style="text-align: left">the caret is the exponentiation operator</td></tr><tr><td style="text-align: left"><a href="math.html#Base.:&amp;"><code>&amp;</code></a></td><td style="text-align: left">single ampersand is bitwise and</td></tr><tr><td style="text-align: left"><a href="math.html#&amp;&amp;"><code>&amp;&amp;</code></a></td><td style="text-align: left">double ampersands is short-circuiting boolean and</td></tr><tr><td style="text-align: left"><a href="math.html#Base.:|"><code>|</code></a></td><td style="text-align: left">single pipe character is bitwise or</td></tr><tr><td style="text-align: left"><a href="math.html#||"><code>||</code></a></td><td style="text-align: left">double pipe characters is short-circuiting boolean or</td></tr><tr><td style="text-align: left"><a href="math.html#Base.xor"><code>⊻</code></a></td><td style="text-align: left">the unicode xor character is bitwise exclusive or</td></tr><tr><td style="text-align: left"><a href="math.html#Base.:~"><code>~</code></a></td><td style="text-align: left">the tilde is an operator for bitwise not</td></tr><tr><td style="text-align: left"><code>&#39;</code></td><td style="text-align: left">a trailing apostrophe is the <a href="../stdlib/LinearAlgebra.html#Base.adjoint"><code>adjoint</code></a> (that is, the complex transpose) operator Aᴴ</td></tr><tr><td style="text-align: left"><a href="math.html#Base.:*-Tuple{Any, Vararg{Any}}"><code>*</code></a></td><td style="text-align: left">the asterisk is used for multiplication, including matrix multiplication and <a href="../manual/strings.html#man-concatenation">string concatenation</a></td></tr><tr><td style="text-align: left"><a href="math.html#Base.:/"><code>/</code></a></td><td style="text-align: left">forward slash divides the argument on its left by the one on its right</td></tr><tr><td style="text-align: left"><a href="math.html#Base.:\\-Tuple{Any, Any}"><code>\</code></a></td><td style="text-align: left">backslash operator divides the argument on its right by the one on its left, commonly used to solve matrix equations</td></tr><tr><td style="text-align: left"><code>()</code></td><td style="text-align: left">parentheses with no arguments constructs an empty <a href="base.html#Core.Tuple"><code>Tuple</code></a></td></tr><tr><td style="text-align: left"><code>(a,...)</code></td><td style="text-align: left">parentheses with comma-separated arguments constructs a tuple containing its arguments</td></tr><tr><td style="text-align: left"><code>(a=1,...)</code></td><td style="text-align: left">parentheses with comma-separated assignments constructs a <a href="base.html#Core.NamedTuple"><code>NamedTuple</code></a></td></tr><tr><td style="text-align: left"><code>(x;y)</code></td><td style="text-align: left">parentheses can also be used to group one or more semicolon separated expressions</td></tr><tr><td style="text-align: left"><code>a[]</code></td><td style="text-align: left"><a href="../manual/arrays.html#man-array-indexing">array indexing</a> (calling <a href="collections.html#Base.getindex"><code>getindex</code></a> or <a href="collections.html#Base.setindex!"><code>setindex!</code></a>)</td></tr><tr><td style="text-align: left"><code>[,]</code></td><td style="text-align: left"><a href="../manual/arrays.html#man-array-literals">vector literal constructor</a> (calling <a href="arrays.html#Base.vect"><code>vect</code></a>)</td></tr><tr><td style="text-align: left"><code>[;]</code></td><td style="text-align: left"><a href="../manual/arrays.html#man-array-concatenation">vertical concatenation</a> (calling <a href="arrays.html#Base.vcat"><code>vcat</code></a> or <a href="arrays.html#Base.hvcat"><code>hvcat</code></a>)</td></tr><tr><td style="text-align: left"><code>[    ]</code></td><td style="text-align: left">with space-separated expressions, <a href="../manual/strings.html#man-concatenation">horizontal concatenation</a> (calling <a href="arrays.html#Base.hcat"><code>hcat</code></a> or <a href="arrays.html#Base.hvcat"><code>hvcat</code></a>)</td></tr><tr><td style="text-align: left"><code>T{ }</code></td><td style="text-align: left">curly braces following a type list that type&#39;s <a href="../manual/types.html#Parametric-Types">parameters</a></td></tr><tr><td style="text-align: left"><code>{}</code></td><td style="text-align: left">curly braces can also be used to group multiple <a href="base.html#where"><code>where</code></a> expressions in function declarations</td></tr><tr><td style="text-align: left"><code>;</code></td><td style="text-align: left">semicolons separate statements, begin a list of keyword arguments in function declarations or calls, or are used to separate array literals for vertical concatenation</td></tr><tr><td style="text-align: left"><code>,</code></td><td style="text-align: left">commas separate function arguments or tuple or array components</td></tr><tr><td style="text-align: left"><code>?</code></td><td style="text-align: left">the question mark delimits the ternary conditional operator (used like: <code>conditional ? if_true : if_false</code>)</td></tr><tr><td style="text-align: left"><code>&quot; &quot;</code></td><td style="text-align: left">the single double-quote character delimits <a href="strings.html#Core.String-Tuple{AbstractString}"><code>String</code></a> literals</td></tr><tr><td style="text-align: left"><code>&quot;&quot;&quot; &quot;&quot;&quot;</code></td><td style="text-align: left">three double-quote characters delimits string literals that may contain <code>&quot;</code> and ignore leading indentation</td></tr><tr><td style="text-align: left"><code>&#39; &#39;</code></td><td style="text-align: left">the single-quote character delimits <a href="strings.html#Core.Char"><code>Char</code></a> (that is, character) literals</td></tr><tr><td style="text-align: left"><code>` `</code></td><td style="text-align: left">the backtick character delimits <a href="../manual/running-external-programs.html#Running-External-Programs">external process</a> (<a href="base.html#Base.Cmd"><code>Cmd</code></a>) literals</td></tr><tr><td style="text-align: left"><code>A...</code></td><td style="text-align: left">triple periods are a postfix operator that &quot;splat&quot; their arguments&#39; contents into many arguments of a function call or declare a varargs function that &quot;slurps&quot; up many arguments into a single tuple</td></tr><tr><td style="text-align: left"><code>a.b</code></td><td style="text-align: left">single periods access named fields in objects/modules (calling <a href="base.html#Base.getproperty"><code>getproperty</code></a> or <a href="base.html#Base.setproperty!"><code>setproperty!</code></a>)</td></tr><tr><td style="text-align: left"><code>f.()</code></td><td style="text-align: left">periods may also prefix parentheses (like <code>f.(...)</code>) or infix operators (like <code>.+</code>) to perform the function element-wise (calling <a href="arrays.html#Base.Broadcast.broadcast"><code>broadcast</code></a>)</td></tr><tr><td style="text-align: left"><code>a:b</code></td><td style="text-align: left">colons (<a href="math.html#Base.::"><code>:</code></a>) used as a binary infix operator construct a range from <code>a</code> to <code>b</code> (inclusive) with fixed step size <code>1</code></td></tr><tr><td style="text-align: left"><code>a:s:b</code></td><td style="text-align: left">colons (<a href="math.html#Base.::"><code>:</code></a>) used as a ternary infix operator construct a range from <code>a</code> to <code>b</code> (inclusive) with step size <code>s</code></td></tr><tr><td style="text-align: left"><code>:</code></td><td style="text-align: left">when used by themselves, <a href="arrays.html#Base.Colon"><code>Colon</code></a>s represent all indices within a dimension, frequently combined with <a href="../manual/arrays.html#man-array-indexing">indexing</a></td></tr><tr><td style="text-align: left"><code>::</code></td><td style="text-align: left">double-colons represent a type annotation or <a href="base.html#Core.typeassert"><code>typeassert</code></a>, depending on context, frequently used when declaring function arguments</td></tr><tr><td style="text-align: left"><code>:( )</code></td><td style="text-align: left">quoted expression</td></tr><tr><td style="text-align: left"><code>:a</code></td><td style="text-align: left"><a href="base.html#Core.Symbol"><code>Symbol</code></a> a</td></tr><tr><td style="text-align: left"><a href="base.html#Core.:&lt;:"><code>&lt;:</code></a></td><td style="text-align: left">subtype operator</td></tr><tr><td style="text-align: left"><a href="base.html#Base.:&gt;:"><code>&gt;:</code></a></td><td style="text-align: left">supertype operator (reverse of subtype operator)</td></tr><tr><td style="text-align: left"><code>=</code></td><td style="text-align: left">single equals sign is <a href="../manual/variables.html#man-variables">assignment</a></td></tr><tr><td style="text-align: left"><a href="math.html#Base.:=="><code>==</code></a></td><td style="text-align: left">double equals sign is value equality comparison</td></tr><tr><td style="text-align: left"><a href="base.html#Core.:==="><code>===</code></a></td><td style="text-align: left">triple equals sign is programmatically identical equality comparison</td></tr><tr><td style="text-align: left"><a href="collections.html#Core.Pair"><code>=&gt;</code></a></td><td style="text-align: left">right arrow using an equals sign defines a <a href="collections.html#Core.Pair"><code>Pair</code></a> typically used to populate <a href="collections.html#Dictionaries">dictionaries</a></td></tr><tr><td style="text-align: left"><code>-&gt;</code></td><td style="text-align: left">right arrow using a hyphen defines an <a href="../manual/functions.html#man-anonymous-functions">anonymous function</a> on a single line</td></tr><tr><td style="text-align: left"><a href="base.html#Base.:|&gt;"><code>|&gt;</code></a></td><td style="text-align: left">pipe operator passes output from the left argument to input of the right argument, usually a <a href="../manual/functions.html#Function-composition-and-piping">function</a></td></tr><tr><td style="text-align: left"><code>∘</code></td><td style="text-align: left">function composition operator (typed with \circ{tab}) combines two functions as though they are a single larger <a href="../manual/functions.html#Function-composition-and-piping">function</a></td></tr><tr><td style="text-align: left"><code>_</code></td><td style="text-align: left">underscores may be assigned values which will not be saved, often used to ignore <a href="../manual/functions.html#destructuring-assignment">multiple return values</a> or create repetitive <a href="../manual/arrays.html#man-comprehensions">comprehensions</a></td></tr></table></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="io-network.html">« I/O and Network</a><a class="docs-footer-nextpage" href="sort.html">Sorting and Related Functions »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
