<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Interactive Utilities · The Julia Language</title><meta name="title" content="Interactive Utilities · The Julia Language"/><meta property="og:title" content="Interactive Utilities · The Julia Language"/><meta property="twitter:title" content="Interactive Utilities · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li class="is-active"><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li><a class="tocitem" href="StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="InteractiveUtils.html">Interactive Utilities</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="InteractiveUtils.html">Interactive Utilities</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/stdlib/InteractiveUtils/docs/src/index.md" title="View source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="man-interactive-utils"><a class="docs-heading-anchor" href="#man-interactive-utils">Interactive Utilities</a><a id="man-interactive-utils-1"></a><a class="docs-heading-anchor-permalink" href="#man-interactive-utils" title="Permalink"></a></h1><p>The <code>InteractiveUtils</code> module provides utilities for interactive use of Julia, such as code introspection and clipboard access. It is intended for interactive work and is loaded automatically in <a href="../manual/command-line-interface.html#command-line-interface">interactive mode</a>.</p><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Docs.apropos" href="#Base.Docs.apropos"><code>Base.Docs.apropos</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">apropos([io::IO=stdout], pattern::Union{AbstractString,Regex})</code></pre><p>Search available docstrings for entries containing <code>pattern</code>.</p><p>When <code>pattern</code> is a string, case is ignored. Results are printed to <code>io</code>.</p><p><code>apropos</code> can be called from the help mode in the REPL by wrapping the query in double quotes:</p><pre><code class="nohighlight hljs">help?&gt; &quot;pattern&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/Docs.jl#L687-L698">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.varinfo" href="#InteractiveUtils.varinfo"><code>InteractiveUtils.varinfo</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">varinfo(m::Module=Main, pattern::Regex=r&quot;&quot;; all=false, imported=false, recursive=false, sortby::Symbol=:name, minsize::Int=0)</code></pre><p>Return a markdown table giving information about public global variables in a module, optionally restricted to those matching <code>pattern</code>.</p><p>The memory consumption estimate is an approximate lower bound on the size of the internal structure of the object.</p><ul><li><code>all</code> : also list non-public objects defined in the module, deprecated objects, and compiler-generated objects.</li><li><code>imported</code> : also list objects explicitly imported from other modules.</li><li><code>recursive</code> : recursively include objects in sub-modules, observing the same settings in each.</li><li><code>sortby</code> : the column to sort results by. Options are <code>:name</code> (default), <code>:size</code>, and <code>:summary</code>.</li><li><code>minsize</code> : only includes objects with size at least <code>minsize</code> bytes. Defaults to <code>0</code>.</li></ul><p>The output of <code>varinfo</code> is intended for display purposes only.  See also <a href="../base/base.html#Base.names"><code>names</code></a> to get an array of symbols defined in a module, which is suitable for more general manipulations.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/InteractiveUtils.jl#L28-L44">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.versioninfo" href="#InteractiveUtils.versioninfo"><code>InteractiveUtils.versioninfo</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">versioninfo(io::IO=stdout; verbose::Bool=false)</code></pre><p>Print information about the version of Julia in use. The output is controlled with boolean keyword arguments:</p><ul><li><code>verbose</code>: print all additional information</li></ul><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>The output of this function may contain sensitive information. Before sharing the output, please review the output and remove any data that should not be shared publicly.</p></div></div><p>See also: <a href="../base/constants.html#Base.VERSION"><code>VERSION</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/InteractiveUtils.jl#L88-L101">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.methodswith" href="#InteractiveUtils.methodswith"><code>InteractiveUtils.methodswith</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">methodswith(typ[, module or function]; supertypes::Bool=false])</code></pre><p>Return an array of methods with an argument of type <code>typ</code>.</p><p>The optional second argument restricts the search to a particular module or function (the default is all top-level modules).</p><p>If keyword <code>supertypes</code> is <code>true</code>, also return arguments with a parent type of <code>typ</code>, excluding type <code>Any</code>.</p><p>See also: <a href="../base/base.html#Base.methods"><code>methods</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/InteractiveUtils.jl#L200-L212">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.subtypes" href="#InteractiveUtils.subtypes"><code>InteractiveUtils.subtypes</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">subtypes(T::DataType)</code></pre><p>Return a list of immediate subtypes of DataType <code>T</code>. Note that all currently loaded subtypes are included, including those not visible in the current module.</p><p>See also <a href="../base/base.html#Base.supertype"><code>supertype</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.supertypes"><code>supertypes</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.methodswith"><code>methodswith</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; subtypes(Integer)
3-element Vector{Any}:
 Bool
 Signed
 Unsigned</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/InteractiveUtils.jl#L284-L300">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.supertypes" href="#InteractiveUtils.supertypes"><code>InteractiveUtils.supertypes</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">supertypes(T::Type)</code></pre><p>Return a tuple <code>(T, ..., Any)</code> of <code>T</code> and all its supertypes, as determined by successive calls to the <a href="../base/base.html#Base.supertype"><code>supertype</code></a> function, listed in order of <code>&lt;:</code> and terminated by <code>Any</code>.</p><p>See also <a href="InteractiveUtils.html#InteractiveUtils.subtypes"><code>subtypes</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; supertypes(Int)
(Int64, Signed, Integer, Real, Number, Any)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/InteractiveUtils.jl#L303-L317">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.edit-Tuple{AbstractString, Integer}" href="#InteractiveUtils.edit-Tuple{AbstractString, Integer}"><code>InteractiveUtils.edit</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">edit(path::AbstractString, line::Integer=0, column::Integer=0)</code></pre><p>Edit a file or directory optionally providing a line number to edit the file at. Return to the <code>julia</code> prompt when you quit the editor. The editor can be changed by setting <code>JULIA_EDITOR</code>, <code>VISUAL</code> or <code>EDITOR</code> as an environment variable.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p>The <code>column</code> argument requires at least Julia 1.9.</p></div></div><p>See also <a href="InteractiveUtils.html#InteractiveUtils.define_editor"><code>InteractiveUtils.define_editor</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/editless.jl#L219-L230">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.edit-Tuple{Any}" href="#InteractiveUtils.edit-Tuple{Any}"><code>InteractiveUtils.edit</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">edit(function, [types])
edit(module)</code></pre><p>Edit the definition of a function, optionally specifying a tuple of types to indicate which method to edit. For modules, open the main source file. The module needs to be loaded with <code>using</code> or <code>import</code> first.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p><code>edit</code> on modules requires at least Julia 1.1.</p></div></div><p>To ensure that the file can be opened at the given line, you may need to call <code>InteractiveUtils.define_editor</code> first.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/editless.jl#L249-L262">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.@edit" href="#InteractiveUtils.@edit"><code>InteractiveUtils.@edit</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@edit</code></pre><p>Evaluates the arguments to the function or macro call, determines their types, and calls the <a href="InteractiveUtils.html#InteractiveUtils.edit-Tuple{AbstractString, Integer}"><code>edit</code></a> function on the resulting expression.</p><p>See also: <a href="InteractiveUtils.html#InteractiveUtils.@less"><code>@less</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@which"><code>@which</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/macros.jl#L278-L285">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.define_editor" href="#InteractiveUtils.define_editor"><code>InteractiveUtils.define_editor</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">define_editor(fn, pattern; wait=false)</code></pre><p>Define a new editor matching <code>pattern</code> that can be used to open a file (possibly at a given line number) using <code>fn</code>.</p><p>The <code>fn</code> argument is a function that determines how to open a file with the given editor. It should take four arguments, as follows:</p><ul><li><code>cmd</code> - a base command object for the editor</li><li><code>path</code> - the path to the source file to open</li><li><code>line</code> - the line number to open the editor at</li><li><code>column</code> - the column number to open the editor at</li></ul><p>Editors which cannot open to a specific line with a command or a specific column may ignore the <code>line</code> and/or <code>column</code> argument. The <code>fn</code> callback must return either an appropriate <code>Cmd</code> object to open a file or <code>nothing</code> to indicate that they cannot edit this file. Use <code>nothing</code> to indicate that this editor is not appropriate for the current environment and another editor should be attempted. It is possible to add more general editing hooks that need not spawn external commands by pushing a callback directly to the vector <code>EDITOR_CALLBACKS</code>.</p><p>The <code>pattern</code> argument is a string, regular expression, or an array of strings and regular expressions. For the <code>fn</code> to be called, one of the patterns must match the value of <code>EDITOR</code>, <code>VISUAL</code> or <code>JULIA_EDITOR</code>. For strings, the string must equal the <a href="../base/file.html#Base.Filesystem.basename"><code>basename</code></a> of the first word of the editor command, with its extension, if any, removed. E.g. &quot;vi&quot; doesn&#39;t match &quot;vim -g&quot; but matches &quot;/usr/bin/vi -m&quot;; it also matches <code>vi.exe</code>. If <code>pattern</code> is a regex it is matched against all of the editor command as a shell-escaped string. An array pattern matches if any of its items match. If multiple editors match, the one added most recently is used.</p><p>By default julia does not wait for the editor to close, running it in the background. However, if the editor is terminal based, you will probably want to set <code>wait=true</code> and julia will wait for the editor to close before resuming.</p><p>If one of the editor environment variables is set, but no editor entry matches it, the default editor entry is invoked:</p><pre><code class="nohighlight hljs">(cmd, path, line, column) -&gt; `$cmd $path`</code></pre><p>Note that many editors are already defined. All of the following commands should already work:</p><ul><li>emacs</li><li>emacsclient</li><li>vim</li><li>nvim</li><li>nano</li><li>micro</li><li>kak</li><li>helix</li><li>textmate</li><li>mate</li><li>kate</li><li>subl</li><li>atom</li><li>notepad++</li><li>Visual Studio Code</li><li>open</li><li>pycharm</li><li>bbedit</li></ul><p><strong>Examples</strong></p><p>The following defines the usage of terminal-based <code>emacs</code>:</p><pre><code class="nohighlight hljs">define_editor(
    r&quot;\bemacs\b.*\s(-nw|--no-window-system)\b&quot;, wait=true) do cmd, path, line
    `$cmd +$line $path`
end</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.4</header><div class="admonition-body"><p><code>define_editor</code> was introduced in Julia 1.4.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/editless.jl#L17-L91">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.less-Tuple{AbstractString}" href="#InteractiveUtils.less-Tuple{AbstractString}"><code>InteractiveUtils.less</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">less(file::AbstractString, [line::Integer])</code></pre><p>Show a file using the default pager, optionally providing a starting line number. Returns to the <code>julia</code> prompt when you quit the pager.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/editless.jl#L298-L303">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.less-Tuple{Any}" href="#InteractiveUtils.less-Tuple{Any}"><code>InteractiveUtils.less</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">less(function, [types])</code></pre><p>Show the definition of a function using the default pager, optionally specifying a tuple of types to indicate which method to see.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/editless.jl#L306-L311">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.@less" href="#InteractiveUtils.@less"><code>InteractiveUtils.@less</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@less</code></pre><p>Evaluates the arguments to the function or macro call, determines their types, and calls the <a href="InteractiveUtils.html#InteractiveUtils.less-Tuple{AbstractString}"><code>less</code></a> function on the resulting expression.</p><p>See also: <a href="InteractiveUtils.html#InteractiveUtils.@edit"><code>@edit</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@which"><code>@which</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_lowered"><code>@code_lowered</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/macros.jl#L268-L275">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.@which" href="#InteractiveUtils.@which"><code>InteractiveUtils.@which</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@which</code></pre><p>Applied to a function or macro call, it evaluates the arguments to the specified call, and returns the <code>Method</code> object for the method that would be called for those arguments. Applied to a variable, it returns the module in which the variable was bound. It calls out to the <a href="../base/base.html#Base.which-Tuple{Any, Any}"><code>which</code></a> function.</p><p>See also: <a href="InteractiveUtils.html#InteractiveUtils.@less"><code>@less</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@edit"><code>@edit</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/macros.jl#L256-L265">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.@functionloc" href="#InteractiveUtils.@functionloc"><code>InteractiveUtils.@functionloc</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@functionloc</code></pre><p>Applied to a function or macro call, it evaluates the arguments to the specified call, and returns a tuple <code>(filename,line)</code> giving the location for the method that would be called for those arguments. It calls out to the <a href="../base/base.html#Base.functionloc-Tuple{Any, Any}"><code>functionloc</code></a> function.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/macros.jl#L247-L253">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.@code_lowered" href="#InteractiveUtils.@code_lowered"><code>InteractiveUtils.@code_lowered</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@code_lowered</code></pre><p>Evaluates the arguments to the function or macro call, determines their types, and calls <a href="../base/base.html#Base.code_lowered"><code>code_lowered</code></a> on the resulting expression.</p><p>See also: <a href="../base/base.html#Base.code_lowered"><code>code_lowered</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_warntype"><code>@code_warntype</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_typed"><code>@code_typed</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_llvm"><code>@code_llvm</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_native"><code>@code_native</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/macros.jl#L302-L309">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.@code_typed" href="#InteractiveUtils.@code_typed"><code>InteractiveUtils.@code_typed</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@code_typed</code></pre><p>Evaluates the arguments to the function or macro call, determines their types, and calls <a href="../base/base.html#Base.code_typed"><code>code_typed</code></a> on the resulting expression. Use the optional argument <code>optimize</code> with</p><pre><code class="nohighlight hljs">@code_typed optimize=true foo(x)</code></pre><p>to control whether additional optimizations, such as inlining, are also applied.</p><p>See also: <a href="../base/base.html#Base.code_typed"><code>code_typed</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_warntype"><code>@code_warntype</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_lowered"><code>@code_lowered</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_llvm"><code>@code_llvm</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_native"><code>@code_native</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/macros.jl#L288-L299">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.code_warntype" href="#InteractiveUtils.code_warntype"><code>InteractiveUtils.code_warntype</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">code_warntype([io::IO], f, types; debuginfo=:default)</code></pre><p>Prints lowered and type-inferred ASTs for the methods matching the given generic function and type signature to <code>io</code> which defaults to <code>stdout</code>. The ASTs are annotated in such a way as to cause &quot;non-leaf&quot; types which may be problematic for performance to be emphasized (if color is available, displayed in red). This serves as a warning of potential type instability.</p><p>Not all non-leaf types are particularly problematic for performance, and the performance characteristics of a particular type is an implementation detail of the compiler. <code>code_warntype</code> will err on the side of coloring types red if they might be a performance concern, so some types may be colored red even if they do not impact performance. Small unions of concrete types are usually not a concern, so these are highlighted in yellow.</p><p>Keyword argument <code>debuginfo</code> may be one of <code>:source</code> or <code>:none</code> (default), to specify the verbosity of code comments.</p><p>See the <a href="../manual/performance-tips.html#man-code-warntype"><code>@code_warntype</code></a> section in the Performance Tips page of the manual for more information.</p><p>See also: <a href="InteractiveUtils.html#InteractiveUtils.@code_warntype"><code>@code_warntype</code></a>, <a href="../base/base.html#Base.code_typed"><code>code_typed</code></a>, <a href="../base/base.html#Base.code_lowered"><code>code_lowered</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.code_llvm"><code>code_llvm</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.code_native"><code>code_native</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/codeview.jl#L57-L76">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.@code_warntype" href="#InteractiveUtils.@code_warntype"><code>InteractiveUtils.@code_warntype</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@code_warntype</code></pre><p>Evaluates the arguments to the function or macro call, determines their types, and calls <a href="InteractiveUtils.html#InteractiveUtils.code_warntype"><code>code_warntype</code></a> on the resulting expression.</p><p>See also: <a href="InteractiveUtils.html#InteractiveUtils.code_warntype"><code>code_warntype</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_typed"><code>@code_typed</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_lowered"><code>@code_lowered</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_llvm"><code>@code_llvm</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_native"><code>@code_native</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/macros.jl#L312-L319">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.code_llvm" href="#InteractiveUtils.code_llvm"><code>InteractiveUtils.code_llvm</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">code_llvm([io=stdout,], f, types; raw=false, dump_module=false, optimize=true, debuginfo=:default)</code></pre><p>Prints the LLVM bitcodes generated for running the method matching the given generic function and type signature to <code>io</code>.</p><p>If the <code>optimize</code> keyword is unset, the code will be shown before LLVM optimizations. All metadata and dbg.* calls are removed from the printed bitcode. For the full IR, set the <code>raw</code> keyword to true. To dump the entire module that encapsulates the function (with declarations), set the <code>dump_module</code> keyword to true. Keyword argument <code>debuginfo</code> may be one of source (default) or none, to specify the verbosity of code comments.</p><p>See also: <a href="InteractiveUtils.html#InteractiveUtils.@code_llvm"><code>@code_llvm</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.code_warntype"><code>code_warntype</code></a>, <a href="../base/base.html#Base.code_typed"><code>code_typed</code></a>, <a href="../base/base.html#Base.code_lowered"><code>code_lowered</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.code_native"><code>code_native</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/codeview.jl#L268-L280">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.@code_llvm" href="#InteractiveUtils.@code_llvm"><code>InteractiveUtils.@code_llvm</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@code_llvm</code></pre><p>Evaluates the arguments to the function or macro call, determines their types, and calls <a href="InteractiveUtils.html#InteractiveUtils.code_llvm"><code>code_llvm</code></a> on the resulting expression. Set the optional keyword arguments <code>raw</code>, <code>dump_module</code>, <code>debuginfo</code>, <code>optimize</code> by putting them and their value before the function call, like this:</p><pre><code class="nohighlight hljs">@code_llvm raw=true dump_module=true debuginfo=:default f(x)
@code_llvm optimize=false f(x)</code></pre><p><code>optimize</code> controls whether additional optimizations, such as inlining, are also applied. <code>raw</code> makes all metadata and dbg.* calls visible. <code>debuginfo</code> may be one of <code>:source</code> (default) or <code>:none</code>,  to specify the verbosity of code comments. <code>dump_module</code> prints the entire module that encapsulates the function.</p><p>See also: <a href="InteractiveUtils.html#InteractiveUtils.code_llvm"><code>code_llvm</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_warntype"><code>@code_warntype</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_typed"><code>@code_typed</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_lowered"><code>@code_lowered</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_native"><code>@code_native</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/macros.jl#L322-L339">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.code_native" href="#InteractiveUtils.code_native"><code>InteractiveUtils.code_native</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">code_native([io=stdout,], f, types; syntax=:intel, debuginfo=:default, binary=false, dump_module=true)</code></pre><p>Prints the native assembly instructions generated for running the method matching the given generic function and type signature to <code>io</code>.</p><ul><li>Set assembly syntax by setting <code>syntax</code> to <code>:intel</code> (default) for intel syntax or <code>:att</code> for AT&amp;T syntax.</li><li>Specify verbosity of code comments by setting <code>debuginfo</code> to <code>:source</code> (default) or <code>:none</code>.</li><li>If <code>binary</code> is <code>true</code>, also print the binary machine code for each instruction precedented by an abbreviated address.</li><li>If <code>dump_module</code> is <code>false</code>, do not print metadata such as rodata or directives.</li><li>If <code>raw</code> is <code>false</code>, uninteresting instructions (like the safepoint function prologue) are elided.</li></ul><p>See also: <a href="InteractiveUtils.html#InteractiveUtils.@code_native"><code>@code_native</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.code_warntype"><code>code_warntype</code></a>, <a href="../base/base.html#Base.code_typed"><code>code_typed</code></a>, <a href="../base/base.html#Base.code_lowered"><code>code_lowered</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.code_llvm"><code>code_llvm</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/codeview.jl#L293-L306">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.@code_native" href="#InteractiveUtils.@code_native"><code>InteractiveUtils.@code_native</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@code_native</code></pre><p>Evaluates the arguments to the function or macro call, determines their types, and calls <a href="InteractiveUtils.html#InteractiveUtils.code_native"><code>code_native</code></a> on the resulting expression.</p><p>Set any of the optional keyword arguments <code>syntax</code>, <code>debuginfo</code>, <code>binary</code> or <code>dump_module</code> by putting it before the function call, like this:</p><pre><code class="nohighlight hljs">@code_native syntax=:intel debuginfo=:default binary=true dump_module=false f(x)</code></pre><ul><li>Set assembly syntax by setting <code>syntax</code> to <code>:intel</code> (default) for Intel syntax or <code>:att</code> for AT&amp;T syntax.</li><li>Specify verbosity of code comments by setting <code>debuginfo</code> to <code>:source</code> (default) or <code>:none</code>.</li><li>If <code>binary</code> is <code>true</code>, also print the binary machine code for each instruction precedented by an abbreviated address.</li><li>If <code>dump_module</code> is <code>false</code>, do not print metadata such as rodata or directives.</li></ul><p>See also: <a href="InteractiveUtils.html#InteractiveUtils.code_native"><code>code_native</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_warntype"><code>@code_warntype</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_typed"><code>@code_typed</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_lowered"><code>@code_lowered</code></a>, <a href="InteractiveUtils.html#InteractiveUtils.@code_llvm"><code>@code_llvm</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/macros.jl#L342-L359">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@time_imports" href="#Base.@time_imports"><code>Base.@time_imports</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@time_imports</code></pre><p>A macro to execute an expression and produce a report of any time spent importing packages and their dependencies. Any compilation time will be reported as a percentage, and how much of which was recompilation, if any.</p><p>One line is printed per package or package extension. The duration shown is the time to import that package itself, not including the time to load any of its dependencies.</p><p>On Julia 1.9+ <a href="../manual/code-loading.html#man-extensions">package extensions</a> will show as Parent → Extension.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>During the load process a package sequentially imports all of its dependencies, not just its direct dependencies.</p></div></div><pre><code class="language-julia-repl hljs">julia&gt; @time_imports using CSV
     50.7 ms  Parsers 17.52% compilation time
      0.2 ms  DataValueInterfaces
      1.6 ms  DataAPI
      0.1 ms  IteratorInterfaceExtensions
      0.1 ms  TableTraits
     17.5 ms  Tables
     26.8 ms  PooledArrays
    193.7 ms  SentinelArrays 75.12% compilation time
      8.6 ms  InlineStrings
     20.3 ms  WeakRefStrings
      2.0 ms  TranscodingStreams
      1.4 ms  Zlib_jll
      1.8 ms  CodecZlib
      0.8 ms  Compat
     13.1 ms  FilePathsBase 28.39% compilation time
   1681.2 ms  CSV 92.40% compilation time</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>This macro requires at least Julia 1.8</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/macros.jl#L362-L398">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="InteractiveUtils.clipboard" href="#InteractiveUtils.clipboard"><code>InteractiveUtils.clipboard</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">clipboard(x)</code></pre><p>Send a printed form of <code>x</code> to the operating system clipboard (&quot;copy&quot;).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/clipboard.jl#L149-L153">source</a></section><section><div><pre><code class="language-julia hljs">clipboard() -&gt; String</code></pre><p>Return a string with the contents of the operating system clipboard (&quot;paste&quot;).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/stdlib/InteractiveUtils/src/clipboard.jl#L156-L160">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="Future.html">« Future</a><a class="docs-footer-nextpage" href="LazyArtifacts.html">Lazy Artifacts »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
