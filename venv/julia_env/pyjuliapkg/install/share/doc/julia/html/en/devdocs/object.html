<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Memory layout of Julia Objects · The Julia Language</title><meta name="title" content="Memory layout of Julia Objects · The Julia Language"/><meta property="og:title" content="Memory layout of Julia Objects · The Julia Language"/><meta property="twitter:title" content="Memory layout of Julia Objects · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox" checked/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="ast.html">Julia ASTs</a></li><li><a class="tocitem" href="types.html">More about types</a></li><li class="is-active"><a class="tocitem" href="object.html">Memory layout of Julia Objects</a><ul class="internal"><li><a class="tocitem" href="#Object-layout-(jl_value_t)"><span>Object layout (<code>jl_value_t</code>)</span></a></li><li><a class="tocitem" href="#Garbage-collector-mark-bits"><span>Garbage collector mark bits</span></a></li><li><a class="tocitem" href="#Object-allocation"><span>Object allocation</span></a></li></ul></li><li><a class="tocitem" href="eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="functions.html">Julia Functions</a></li><li><a class="tocitem" href="cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="subarrays.html">SubArrays</a></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Documentation of Julia&#39;s Internals</a></li><li class="is-active"><a href="object.html">Memory layout of Julia Objects</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="object.html">Memory layout of Julia Objects</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/object.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Memory-layout-of-Julia-Objects"><a class="docs-heading-anchor" href="#Memory-layout-of-Julia-Objects">Memory layout of Julia Objects</a><a id="Memory-layout-of-Julia-Objects-1"></a><a class="docs-heading-anchor-permalink" href="#Memory-layout-of-Julia-Objects" title="Permalink"></a></h1><h2 id="Object-layout-(jl_value_t)"><a class="docs-heading-anchor" href="#Object-layout-(jl_value_t)">Object layout (<code>jl_value_t</code>)</a><a id="Object-layout-(jl_value_t)-1"></a><a class="docs-heading-anchor-permalink" href="#Object-layout-(jl_value_t)" title="Permalink"></a></h2><p>The <code>jl_value_t</code> struct is the name for a block of memory owned by the Julia Garbage Collector, representing the data associated with a Julia object in memory. Absent any type information, it is simply an opaque pointer:</p><pre><code class="language-c hljs">typedef struct jl_value_t* jl_pvalue_t;</code></pre><p>Each <code>jl_value_t</code> struct is contained in a <code>jl_typetag_t</code> struct that contains metadata information about the Julia object, such as its type and garbage collector (gc) reachability:</p><pre><code class="language-c hljs">typedef struct {
    opaque metadata;
    jl_value_t value;
} jl_typetag_t;</code></pre><p>The type of any Julia object is an instance of a leaf <code>jl_datatype_t</code> object. The <code>jl_typeof()</code> function can be used to query for it:</p><pre><code class="language-c hljs">jl_value_t *jl_typeof(jl_value_t *v);</code></pre><p>The layout of the object depends on its type. Reflection methods can be used to inspect that layout. A field can be accessed by calling one of the get-field methods:</p><pre><code class="language-c hljs">jl_value_t *jl_get_nth_field_checked(jl_value_t *v, size_t i);
jl_value_t *jl_get_field(jl_value_t *o, char *fld);</code></pre><p>If the field types are known, a priori, to be all pointers, the values can also be extracted directly as an array access:</p><pre><code class="language-c hljs">jl_value_t *v = value-&gt;fieldptr[n];</code></pre><p>As an example, a &quot;boxed&quot; <code>uint16_t</code> is stored as follows:</p><pre><code class="language-c hljs">struct {
    opaque metadata;
    struct {
        uint16_t data;        // -- 2 bytes
    } jl_value_t;
};</code></pre><p>This object is created by <code>jl_box_uint16()</code>. Note that the <code>jl_value_t</code> pointer references the data portion, not the metadata at the top of the struct.</p><p>A value may be stored &quot;unboxed&quot; in many circumstances (just the data, without the metadata, and possibly not even stored but just kept in registers), so it is unsafe to assume that the address of a box is a unique identifier. The &quot;egal&quot; test (corresponding to the <code>===</code> function in Julia), should instead be used to compare two unknown objects for equivalence:</p><pre><code class="language-c hljs">int jl_egal(jl_value_t *a, jl_value_t *b);</code></pre><p>This optimization should be relatively transparent to the API, since the object will be &quot;boxed&quot; on-demand, whenever a <code>jl_value_t</code> pointer is needed.</p><p>Note that modification of a <code>jl_value_t</code> pointer in memory is permitted only if the object is mutable. Otherwise, modification of the value may corrupt the program and the result will be undefined. The mutability property of a value can be queried for with:</p><pre><code class="language-c hljs">int jl_is_mutable(jl_value_t *v);</code></pre><p>If the object being stored is a <code>jl_value_t</code>, the Julia garbage collector must be notified also:</p><pre><code class="language-c hljs">void jl_gc_wb(jl_value_t *parent, jl_value_t *ptr);</code></pre><p>However, the <a href="../manual/embedding.html#Embedding-Julia">Embedding Julia</a> section of the manual is also required reading at this point, for covering other details of boxing and unboxing various types, and understanding the gc interactions.</p><p>Mirror structs for some of the built-in types are <a href="https://github.com/JuliaLang/julia/blob/master/src/julia.h">defined in <code>julia.h</code></a>. The corresponding global <code>jl_datatype_t</code> objects are created by <a href="https://github.com/JuliaLang/julia/blob/master/src/jltypes.c"><code>jl_init_types</code> in <code>jltypes.c</code></a>.</p><h2 id="Garbage-collector-mark-bits"><a class="docs-heading-anchor" href="#Garbage-collector-mark-bits">Garbage collector mark bits</a><a id="Garbage-collector-mark-bits-1"></a><a class="docs-heading-anchor-permalink" href="#Garbage-collector-mark-bits" title="Permalink"></a></h2><p>The garbage collector uses several bits from the metadata portion of the <code>jl_typetag_t</code> to track each object in the system. Further details about this algorithm can be found in the comments of the <a href="https://github.com/JuliaLang/julia/blob/master/src/gc.c">garbage collector implementation in <code>gc.c</code></a>.</p><h2 id="Object-allocation"><a class="docs-heading-anchor" href="#Object-allocation">Object allocation</a><a id="Object-allocation-1"></a><a class="docs-heading-anchor-permalink" href="#Object-allocation" title="Permalink"></a></h2><p>Most new objects are allocated by <code>jl_new_structv()</code>:</p><pre><code class="language-c hljs">jl_value_t *jl_new_struct(jl_datatype_t *type, ...);
jl_value_t *jl_new_structv(jl_datatype_t *type, jl_value_t **args, uint32_t na);</code></pre><p>Although, <a href="../base/base.html#Base.isbits"><code>isbits</code></a> objects can be also constructed directly from memory:</p><pre><code class="language-c hljs">jl_value_t *jl_new_bits(jl_value_t *bt, void *data)</code></pre><p>And some objects have special constructors that must be used instead of the above functions:</p><p>Types:</p><pre><code class="language-c hljs">jl_datatype_t *jl_apply_type(jl_datatype_t *tc, jl_tuple_t *params);
jl_datatype_t *jl_apply_array_type(jl_datatype_t *type, size_t dim);</code></pre><p>While these are the most commonly used options, there are more low-level constructors too, which you can find declared in <a href="https://github.com/JuliaLang/julia/blob/master/src/julia.h"><code>julia.h</code></a>. These are used in <code>jl_init_types()</code> to create the initial types needed to bootstrap the creation of the Julia system image.</p><p>Tuples:</p><pre><code class="language-c hljs">jl_tuple_t *jl_tuple(size_t n, ...);
jl_tuple_t *jl_tuplev(size_t n, jl_value_t **v);
jl_tuple_t *jl_alloc_tuple(size_t n);</code></pre><p>The representation of tuples is highly unique in the Julia object representation ecosystem. In some cases, a <a href="../base/base.html#Core.tuple"><code>Base.tuple()</code></a> object may be an array of pointers to the objects contained by the tuple equivalent to:</p><pre><code class="language-c hljs">typedef struct {
    size_t length;
    jl_value_t *data[length];
} jl_tuple_t;</code></pre><p>However, in other cases, the tuple may be converted to an anonymous <a href="../base/base.html#Base.isbits"><code>isbits</code></a> type and stored unboxed, or it may not stored at all (if it is not being used in a generic context as a <code>jl_value_t*</code>).</p><p>Symbols:</p><pre><code class="language-c hljs">jl_sym_t *jl_symbol(const char *str);</code></pre><p>Functions and MethodInstance:</p><pre><code class="language-c hljs">jl_function_t *jl_new_generic_function(jl_sym_t *name);
jl_method_instance_t *jl_new_method_instance(jl_value_t *ast, jl_tuple_t *sparams);</code></pre><p>Arrays:</p><pre><code class="language-c hljs">jl_array_t *jl_new_array(jl_value_t *atype, jl_tuple_t *dims);
jl_array_t *jl_alloc_array_1d(jl_value_t *atype, size_t nr);
jl_array_t *jl_alloc_array_nd(jl_value_t *atype, size_t *dims, size_t ndims);</code></pre><p>Note that many of these have alternative allocation functions for various special-purposes. The list here reflects the more common usages, but a more complete list can be found by reading the <a href="https://github.com/JuliaLang/julia/blob/master/src/julia.h"><code>julia.h</code> header file</a>.</p><p>Internal to Julia, storage is typically allocated by <code>newstruct()</code> (or <code>newobj()</code> for the special types):</p><pre><code class="language-c hljs">jl_value_t *newstruct(jl_value_t *type);
jl_value_t *newobj(jl_value_t *type, size_t nfields);</code></pre><p>And at the lowest level, memory is getting allocated by a call to the garbage collector (in <code>gc.c</code>), then tagged with its type:</p><pre><code class="language-c hljs">jl_value_t *jl_gc_allocobj(size_t nbytes);
void jl_set_typeof(jl_value_t *v, jl_datatype_t *type);</code></pre><div class="admonition is-info"><header class="admonition-header">Out of date Warning</header><div class="admonition-body"><p>The documentation and usage for the function <code>jl_gc_allocobj</code> may be out of date</p></div></div><p>Note that all objects are allocated in multiples of 4 bytes and aligned to the platform pointer size. Memory is allocated from a pool for smaller objects, or directly with <code>malloc()</code> for large objects.</p><div class="admonition is-category-sidebar"><header class="admonition-header">Singleton Types</header><div class="admonition-body"><p>Singleton types have only one instance and no data fields. Singleton instances have a size of 0 bytes, and consist only of their metadata. e.g. <code>nothing::Nothing</code>.</p><p>See <a href="../manual/types.html#man-singleton-types">Singleton Types</a> and <a href="../manual/faq.html#Nothingness-and-missing-values">Nothingness and missing values</a></p></div></div></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="types.html">« More about types</a><a class="docs-footer-nextpage" href="eval.html">Eval of Julia code »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
