%% Direct translation of the backsplash image in "JuliaLang/www.julialang.org"
%% And cropping of the image
%%  https://github.com/JuliaLang/www.julialang.org/blob/main/_assets/infra/backsplash-min-0.5.svg


\newcommand{\splashScaleFactor}{0.6}
\newcommand{\whiteMaskTransparency}{0.5}
\newcommand{\triangleTransparency}{0.6}
\begin{tikzpicture}[x=1,y=1,yscale=-\splashScaleFactor,xscale=\splashScaleFactor,draw=white]
% Clipping
\clip (510,15) rectangle (1570,350);
% Cropping
\useasboundingbox(510,0) rectangle (1570,350.0);
% gary background
% \draw[fill=splash_gary,opacity=0] (510.0,0.0)--++(1057.5,0.0)--++(0.0,350.0)--++(-1057.5,0.0)--cycle;

%% Draw triangles
\draw[fill=julia_red,opacity=\triangleTransparency] 
  (991.9,11.4)--++(51.5,19.7)--++(-56.2,25.3)
    ++(56.8,56.9)--++(-47.4,-27.5)--++(48.4,-52.2)
  (990.9,9.0)--++(-40.6,1.4)--++(125.4,-21.0)
  (969.5,111.5)--++(35.3,20.8)--++(-45.2,3.6)
  (952.4,205.8)--++(14.0,55.9)--++(-44.7,-29.8)
    ++(33.8,-160.3)--++(12.1,37.4)--++(-63.5,-1.9)
  (946.8,335.7)--++(-6.5,-33.3)--++(58.2,17.6)
  (920.0,235.8)--++(17.8,64.6)--++(-39.3,15.0)
    ++(-0.7,3.0)--++(45.6,18.6)--++(-51.2,22.3)
  (910.2,17.7)--++(43.6,51.2)--++(-72.1,-0.9)
  (901.8,109.5)--++(53.8,27.4)--++(-62.3,24.5)
  (885.4,204.6)--++(32.3,26.8)--++(-47.1,9.2)
  (879.5,70.3)--++(19.3,35.8)--++(-54.1,-22.0)
  (870.0,317.0)--++(0.0,-69.4)--++(25.6,68.5)
  (868.7,319.7)--++(20.5,40.0)--++(-53.1,-8.4)
  (867.4,190.9)--++(-12.9,-43.1)--++(35.8,15.6)
  (866.0,193.8)--++(1.9,45.8)--++(-63.6,-57.1)
  (815.2,79.7)--++(-16.2,-17.1)--++(65.6,-10.8)
    ++(3.4,-40.0)--++(0.9,35.9)--++(-50.7,-41.5)
  (801.8,362.7)--++(-7.3,-40.3)--++(37.5,29.3)
    ++(-16.2,-267.4)--++(35.8,61.3)--++(-41.5,22.6)
  (796.4,60.5)--++(-49.1,-26.4)--++(66.1,-27.4)
  (765.0,352.3)--++(-0.9,-61.7)--++(27.6,29.5)
  (758.0,131.6)--++(40.8,48.4)--++(-80.7,-0.9)
    ++(66.2,78.4)--++(-38.6,-22.6)--++(53.7,-50.9)
  (741.8,98.9)--++(14.7,30.4)--++(-45.1,7.4)
  (720.7,321.9)--++(38.2,30.3)--++(-40.8,-13.9)
  (716.2,182.1)--++(19.5,30.7)--++(-50.2,38.1)
  (708.3,140.2)--++(6.5,39.0)--++(-52.0,9.3)
    ++(4.7,130.8)--++(46.6,19.6)--++(-64.3,20.5)
    ++(33.5,-102.7)--++(34.9,62.2)--++(-50.0,-1.9)
  (697.1,1.3)--++(3.7,57.5)--++(-37.1,-47.3)
    ++(20.3,86.2)--++(-30.6,-59.4)--++(47.4,24.1)
  (660.1,193.2)--++(21.3,59.2)--++(-35.1,-12.0)
  (650.6,34.4)--++(-49.2,-41.9)--++(59.2,19.1)
  (641.9,103.0)--++(40.0,-1.9)--++(-45.6,59.5)
  (640.4,99.6)--++(-46.4,-58.7)--++(55.9,-3.8)
  (611.6,316.3)--++(34.5,43.8)--++(-43.8,2.8)
  (600.4,137.3)--++(-7.1,-17.7)--++(43.4,-15.0)
  (596.6,210.6)--++(44.0,28.8)--++(-49.4,-7.2)
  (591.0,41.9)--++(0.0,71.1)--++(-19.8,-57.6)
  (551.4,303.2)--++(-11.2,-49.5)--++(47.7,-18.7)
  (541.9,104.3)--++(47.4,14.9)--++(-51.2,25.1)
  (540.0,214.9)--++(-1.9,-66.3)--++(55.9,59.6)
    ++(-2.7,-170.7)--++(-44.8,-53.2)--++(49.5,6.5)
  (512.0,192.8)--++(-28.2,-58.3)--++(51.7,12.2)
    ++(-23.0,50.0)--++(24.9,19.4)--++(-44.3,38.8)
  (503.7,105.9)--++(-15.7,-57.3)--++(50.9,53.6)
  (487.0,42.5)--(487.0,6.7)--++(53.2,-21.1)
  (1602.3,36.6)--++(12.3,58.5)--++(-57.5,-32.1)
  (1545.2,19.0)--++(-49.1,-29.1)--++(117.4,19.1)
  (1538.8,89.3)--++(67.7,71.6)--++(-73.5,-3.8)
  (1537.9,366.1)--++(-2.9,-80.5)--++(70.9,33.6)
  (1532.0,159.8)--++(18.4,27.6)--++(-47.0,12.9)
    ++(34.3,-114.6)--++(-48.3,-30.1)--++(63.8,8.2)
  (1501.7,203.7)--++(63.0,41.4)--++(-71.5,0.9)
  (1499.9,137.4)--++(-43.1,-47.7)--++(59.6,25.7)
  (1493.0,315.9)--++(0.0,-66.7)--++(39.5,34.8)
  (1491.7,319.6)--++(15.7,27.7)--++(-60.1,12.0)
  (1469.9,265.9)--++(20.1,49.8)--++(-21.0,-12.2)
  (1445.7,193.2)--++(21.8,66.4)--++(-57.8,-16.1)
    ++(44.0,-158.6)--++(-7.4,-46.2)--++(37.0,15.7)
  (1433.7,139.9)--++(-33.8,-45.1)--++(51.7,-6.6)
    ++(-7.4,-52.1)--++(-30.1,1.8)--++(38.3,-51.9)
  (1418.7,179.9)--++(-19.1,-27.3)--++(32.7,-9.1)
  (1407.3,246.3)--++(7.5,63.2)--++(-49.1,-17.0)
  (1384.6,-17.4)--++(24.9,53.5)--++(-49.9,-34.2)
  (1367.2,191.1)--++(-28.2,-58.4)--++(57.4,19.8)
  (1364.4,296.2)--++(23.2,49.3)--++(-35.3,17.7)
  (1301.7,135.9)--++(-4.3,-16.4)--++(35.3,11.2)
  (1297.0,116.5)--++(-1.9,-78.9)--++(52.3,57.0)
    ++(-49.6,-80.8)--++(48.0,38.2)--++(-50.7,-17.8)
  (1296.9,251.9)--++(-8.5,-57.6)--++(69.0,52.0)
  (1295.1,254.1)--++(7.4,59.0)--++(-37.8,-54.3)
    ++(23.2,74.8)--++(-32.1,-28.5)--++(46.3,12.5)
  (1288.0,336.1)--++(55.2,26.3)--++(-77.0,-15.8)
  (1258.6,34.5)--++(-14.0,-47.5)--++(50.3,25.2)
    ++(-8.4,177.8)--++(-32.7,-39.3)--++(45.8,-12.2)
  (1255.1,194.9)--++(6.6,60.1)--++(-60.1,-69.5)
    ++(49.7,116.1)--++(-66.9,-32.0)--++(77.2,-10.4)
    ++(-10.2,45.7)--++(10.3,41.3)--++(-66.6,13.1)
  (1249.1,118.0)--++(1.8,31.1)--++(-43.9,1.8)
  (1187.6,-8.5)--++(17.0,65.3)--++(-53.0,-5.7)
  (1182.3,316.6)--++(8.3,42.4)--++(-40.6,-24.0)
  (1180.3,267.6)--++(-31.7,-12.1)--++(48.5,-67.1)
  (1180.1,271.2)--++(1.8,42.2)--++(-31.2,-14.7)
  (1150.7,162.1)--++(-7.6,-81.0)--++(57.2,7.6)
  (1146.0,256.9)--++(1.9,39.9)--++(-56.7,-44.6)
    ++(55.8,81.3)--++(-43.2,-14.7)--++(44.1,-18.4)
  (1141.6,77.9)--++(-32.5,-38.8)--++(39.7,13.5)
    ++(-40.4,87.8)--++(39.0,24.2)--++(-60.4,13.9)
  (1108.9,-14.3)--++(21.7,26.3)--++(-23.5,22.6)
  (1108.3,136.4)--++(-14.1,-53.4)--++(45.9,-1.9)
  (1066.9,169.8)--++(16.8,10.6)--++(-19.4,35.3)
    ++(-15.7,97.5)--++(-10.4,-69.9)--++(48.2,8.5)
  (1066.5,166.5)--++(-19.7,-49.7)--++(59.1,22.5)
  (1058.4,4.5)--++(43.9,31.1)--++(-54.9,-5.5)
  (1048.8,317.1)--++(48.4,2.7)--++(-55.7,27.4)
  (1037.4,240.4)--++(-15.5,-33.7)--++(39.1,13.6)
  (1007.4,135.4)--++(11.4,68.6)--++(-63.8,-0.9)
  (1005.9,249.3)--++(40.4,65.8)--++(-43.3,3.8);

\draw[fill=julia_purple,opacity=\triangleTransparency]
  (995.4,84.1)--++(-8.2,-25.5)--++(54.7,-24.6)
  (969.5,108.2)--++(-11.9,-36.5)--++(35.6,14.6)
  (967.9,265.8)--++(31.9,52.5)--++(-59.1,-17.8)
    ++(28.0,-37.9)--++(-14.0,-55.9)--++(48.5,39.1)
  (952.1,202.2)--++(-58.0,-39.0)--++(62.7,-24.7)
  (891.2,161.6)--++(-36.3,-15.8)--++(44.7,-35.4)
    ++(-19.6,-43.5)--++(-8.8,-16.7)--++(35.1,-29.8)
  (884.3,201.4)--++(-15.7,-8.7)--++(21.8,-26.2)
  (841.1,84.9)--++(9.9,55.8)--++(-34.2,-58.4)
    ++(-7.5,89.0)--++(47.9,19.0)--++(-54.5,-9.9)
  (815.2,4.0)--++(-37.7,-10.6)--++(69.6,14.2)
    ++(-45.4,175.3)--++(65.2,58.5)--++(-80.6,16.3)
  (784.3,261.5)--++(7.3,55.5)--++(-27.3,-29.1)
    ++(-18.4,-252.3)--++(49.1,26.4)--++(-51.9,33.1)
  (762.0,290.0)--++(0.9,62.9)--++(-41.3,-32.9)
  (758.3,128.4)--++(-14.9,-30.7)--++(67.9,-14.9)
  (744.5,233.2)--++(-6.3,-19.7)--++(57.2,-28.6)
  (744.2,32.2)--++(-43.9,-31.7)--++(49.5,-12.1)
  (709.1,136.3)--++(-23.3,-35.4)--++(54.0,-2.8)
  (635.0,165.5)--++(23.1,24.0)--++(-59.1,17.6)
  (611.3,311.1)--++(-19.9,-76.8)--++(52.2,7.6)
  (591.5,120.4)--++(7.1,17.8)--++(-56.0,6.2)
  (551.3,307.9)--++(47.1,54.6)--++(-64.0,-12.2)
  (540.0,251.5)--++(0.0,-34.1)--++(47.1,15.7)
    ++(-16.6,-179.6)--++(-23.7,-65.5)--++(43.7,51.9)
  (538.7,256.1)--++(11.1,49.2)--++(-38.0,8.4)
    ++(29.1,-212.2)--++(-52.2,-55.0)--++(79.7,9.5)
  (1608.2,159.7)--++(-67.6,-71.4)--++(74.2,9.5)
  (1567.8,247.7)--++(37.8,69.1)--++(-70.0,-33.1)
  (1555.7,61.5)--++(-9.3,-40.1)--++(54.0,14.0)
  (1518.1,113.1)--++(-30.4,-56.3)--++(48.9,30.4)
  (1517.6,117.1)--++(12.6,38.6)--++(-28.8,-17.1)
    ++(7.4,207.0)--++(-15.5,-27.4)--++(38.4,-31.1)
  (1484.1,52.5)--++(-37.4,-15.9)--++(43.0,-45.8)
    ++(-20.2,268.7)--++(-20.9,-63.7)--++(41.9,51.0)
  (1446.6,189.1)--++(-11.3,-46.1)--++(62.1,-2.8)
  (1443.9,358.7)--++(-26.3,-46.9)--++(48.8,-6.6)
  (1419.8,183.3)--++(24.6,8.2)--++(-34.6,48.3)
  (1412.5,41.1)--++(38.5,45.1)--++(-51.7,6.6)
  (1367.7,195.4)--++(38.3,47.7)--++(-44.9,2.8)
    ++(-2.4,-242.2)--++(50.9,34.8)--++(-57.4,15.1)
  (1359.1,248.1)--++(3.7,43.1)--++(-63.7,-37.4)
    ++(37.6,-120.9)--++(28.6,59.1)--++(-76.3,-1.0)
  (1351.9,57.0)--++(43.4,36.0)--++(-45.2,0.9)
  (1336.4,129.8)--++(-37.5,-11.9)--++(48.5,-21.0)
  (1304.0,318.5)--++(42.3,43.2)--++(-56.7,-27.0)
  (1293.0,36.0)--++(1.9,77.2)--++(-35.3,-76.3)
  (1256.9,192.9)--++(-3.6,-39.8)--++(31.7,38.0)
  (1252.9,148.1)--++(-1.8,-31.1)--++(42.0,1.8)
  (1204.9,153.0)--++(43.5,-1.8)--++(-48.0,30.8)
  (1201.2,86.2)--++(-47.4,-32.8)--++(51.1,5.5)
    ++(1.4,-3.7)--++(-17.0,-65.3)--++(52.0,-3.8)
  (1201.1,91.1)--++(1.9,60.1)--++(-50.7,12.2)
  (1184.0,313.8)--++(-1.9,-43.2)--++(66.7,31.9)
  (1155.9,186.0)--++(41.1,-0.9)--++(-48.6,67.3)
  (1149.3,166.2)--++(4.4,17.7)--++(-62.0,-4.4)
  (1132.3,10.8)--++(-22.0,-26.6)--++(73.4,5.5)
  (1131.7,13.7)--++(16.5,36.6)--++(-40.3,-13.7)
  (1101.8,317.6)--++(-12.3,-64.2)--++(57.6,45.3)
  (1092.5,80.0)--++(-44.0,-47.8)--++(56.2,5.6)
  (1092.4,83.6)--++(14.1,53.7)--++(-59.4,-22.6)
  (1062.9,221.4)--++(22.7,28.1)--++(-46.3,-8.2)
  (1057.8,2.0)--++(-50.7,5.3)--++(88.5,-20.4)
  (1036.2,243.1)--++(10.1,68.2)--++(-39.6,-64.5)
    ++(32.7,100.5)--++(-34.7,-26.5)--++(42.0,-3.7)
  (1020.7,203.2)--++(-11.4,-68.2)--++(54.9,33.1)
  (1008.6,131.6)--++(-12.0,-43.5)--++(46.3,26.8)
  (1001.4,321.2)--++(14.9,40.1)--++(-67.1,-24.2);

\draw[fill=julia_green,opacity=\triangleTransparency]
  (994.5,87.8)--++(11.9,43.0)--++(-35.7,-21.1)
    ++(-11.7,28.1)--++(46.9,-3.8)--++(-51.6,66.6)
    ++(-8.2,137.3)--++(66.0,23.8)--++(-116.4,-1.8)
  (985.4,59.3)--++(8.0,24.9)--++(-34.7,-14.2)
  (985.2,55.9)--++(-47.7,-43.0)--++(52.4,-1.9)
  (957.5,135.6)--++(-52.1,-26.5)--++(62.1,1.8)
  (935.0,11.0)--++(-170.3,-23.1)--++(330.5,-3.7)
  (934.3,13.1)--++(18.8,52.0)--++(-41.3,-48.4)
  (901.4,106.5)--++(-19.7,-36.5)--++(71.2,0.9)
  (896.9,313.7)--++(-26.5,-71.0)--++(48.3,-9.5)
  (892.7,164.7)--++(56.9,38.3)--++(-63.5,-0.9)
  (890.4,357.7)--++(-19.8,-38.8)--++(25.2,-0.9)
  (870.9,47.9)--++(-0.9,-35.8)--++(37.6,4.6)
  (869.8,51.8)--++(8.8,16.7)--++(-33.4,13.2)
    ++(24.6,155.7)--++(-1.7,-42.6)--++(15.7,8.7)
  (868.0,243.2)--++(0.0,72.9)--++(-80.5,-56.6)
    ++(28.0,-252.8)--++(52.1,42.7)--++(-69.2,11.4)
  (852.4,147.5)--++(13.1,43.8)--++(-54.1,-21.5)
  (833.9,350.7)--++(-38.1,-29.8)--++(70.7,-1.9)
  (800.9,179.4)--++(-35.9,-42.6)--++(42.6,33.4)
    ++(-55.8,-181.9)--++(60.1,16.9)--++(-65.7,27.2)
  (796.8,63.3)--++(16.2,17.1)--++(-65.8,14.4)
  (763.3,285.9)--++(-17.4,-48.7)--++(37.7,22.0)
  (736.5,214.7)--++(6.3,19.7)--++(-54.6,17.0)
  (720.3,318.5)--++(-35.0,-62.5)--++(75.7,32.2)
  (717.0,340.1)--++(41.8,14.2)--++(-103.1,5.3)
  (716.6,177.7)--++(-6.5,-38.9)--++(45.3,-7.4)
  (702.9,60.2)--++(-3.8,-58.2)--++(44.1,31.9)
  (702.3,63.6)--++(37.1,32.5)--++(-53.9,2.8)
  (683.9,101.7)--++(23.5,35.8)--++(-69.6,24.5)
    ++(45.3,89.4)--++(-21.8,-60.5)--++(53.0,-9.5)
  (666.9,314.1)--++(-20.4,-71.6)--++(35.3,12.1)
  (662.2,12.9)--++(36.2,46.1)--++(-46.1,-23.5)
  (651.5,39.2)--++(30.9,59.9)--++(-40.2,1.9)
  (647.8,359.1)--++(-34.6,-43.9)--++(52.3,3.7)
  (644.4,239.4)--++(-46.1,-30.1)--++(60.2,-17.9)
  (634.2,162.2)--++(-32.6,-23.3)--++(38.2,-34.4)
  (595.1,206.6)--++(-56.0,-59.8)--++(59.8,-6.6)
  (589.7,235.7)--++(20.0,77.1)--++(-57.1,-7.6)
  (569.8,57.3)--++(20.7,60.1)--++(-47.9,-15.0)
  (538.0,218.2)--++(0.0,33.9)--++(-44.0,4.6)
    ++(42.2,-113.1)--++(-31.2,-35.8)--++(34.9,-3.7)
  (532.8,348.8)--++(-21.2,-33.2)--++(37.8,-8.3)
  (496.9,336.0)--++(30.8,13.7)--++(-86.5,-4.3)
  (486.2,4.8)--++(-20.6,-17.9)--++(72.7,-2.7)
  (1607.2,320.8)--++(4.7,48.2)--++(-71.8,-1.9)
    ++(15.2,-302.8)--++(55.7,31.0)--++(-71.2,-9.1)
  (1602.6,33.9)--++(-52.5,-13.6)--++(67.9,-10.0)
  (1588.5,189.0)--++(-32.4,-1.8)--++(49.9,-22.8)
  (1551.3,189.2)--++(15.0,54.5)--++(-62.9,-41.3)
  (1544.3,21.5)--++(9.4,40.3)--++(-65.6,-8.4)
  (1533.2,288.9)--++(2.8,77.1)--++(-25.7,-18.4)
  (1531.4,153.0)--++(-12.3,-37.8)--++(17.6,-24.6)
  (1502.0,198.9)--++(-0.9,-58.1)--++(29.5,17.5)
  (1491.0,248.8)--++(0.0,64.1)--++(-20.8,-51.4)
  (1484.8,55.7)--++(31.0,57.3)--++(-61.0,-26.3)
  (1454.8,-16.9)--++(34.2,5.5)--++(-42.5,45.2)
  (1453.4,88.9)--++(44.4,49.2)--++(-62.4,2.8)
    ++(56.0,103.8)--++(-42.9,-52.2)--++(51.3,10.3)
  (1444.2,38.1)--++(7.3,45.6)--++(-37.4,-43.8)
  (1433.8,145.4)--++(10.8,44.1)--++(-24.3,-8.1)
  (1416.8,308.9)--++(-7.6,-63.5)--++(57.9,16.1)
  (1412.2,37.0)--++(-25.6,-55.0)--++(65.5,0.9)
  (1399.0,150.7)--(1399,97.0)--++(33.3,44.4)
  (1398.0,153.7)--++(19.3,27.6)--++(-47.9,10.1)
  (1397.4,92.2)--++(-44.3,-36.7)--++(57.5,-15.1)
    ++(-21.4,303.7)--++(-23.3,-49.4)--++(48.4,16.8)
  (1389.105,347.066)--++(48.442,13.211)--++(-81.91,3.524)
  (1364.8,290.6)--++(-3.7,-42.7)--++(44.5,-2.8)
  (1359.2,245.2)--++(-69.2,-52.1)--++(75.8,0.9)
  (1350.4,362.9)--++(-44.6,-45.5)--++(56.9,-22.8)
  (1350.2,53.1)--++(-50.9,-40.5)--++(57.4,-9.4)
  (1349.4,97.0)--++(45.4,52.9)--++(-56.6,-19.5)
  (1301.8,137.9)--++(32.7,-5.5)--++(-45.5,55.5)
  (1297.1,10.9)--++(-50.1,-25.0)--++(131.6,-3.7)
  (1295.4,119.7)--++(4.4,16.6)--++(-42.9,11.4)
    ++(6.5,112.7)--++(38.2,54.9)--++(-48.4,-13.0)
  (1286.6,196.1)--++(8.3,56.1)--++(-30.4,4.6)
    ++(28.6,-222.7)--++(-31.4,0.9)--++(34.0,-20.9)
  (1263.0,348.0)--++(76.7,15.7)--++(-138.7,-3.5)
  (1251.2,151.7)--++(3.7,41.1)--++(-53.2,-9.3)
  (1249.3,113.4)--++(-41.7,-54.9)--++(49.3,-20.8)
    ++(-57.3,148.6)--++(61.5,71.1)--++(-78.8,10.6)
  (1204.9,150.0)--++(-1.9,-60.2)--++(45.2,26.3)
  (1192.6,359.0)--++(-8.5,-43.2)--++(64.8,-11.3)
  (1155.8,184.0)--++(-4.3,-17.4)--++(42.5,16.5)
    ++(-43.4,-129.5)--++(47.4,32.8)--++(-54.7,-7.3)
  (1150.1,49.9)--++(-16.8,-37.3)--++(52.2,-21.5)
    ++(-37.5,345.1)--++(40.4,23.9)--++(-72.5,1.8)
  (1149.9,296.8)--++(-1.8,-39.3)--++(31.1,11.9)
    ++(-29.2,31.2)--++(30.9,14.5)--++(-31.8,18.2)
  (1141.3,83.2)--++(7.5,79.9)--++(-39.5,-24.4)
    ++(36.8,116.3)--++(-55.9,-4.7)--++(63.4,-62.5)
  (1106.4,39.1)--++(33.5,40.0)--++(-45.6,1.9)
  (1105.1,35.1)--++(-45.0,-31.9)--++(46.9,-18.7)
  (1100.4,320.4)--++(11.3,41.3)--++(-68.5,-13.1)
  (1087.5,253.7)--++(12.3,64.2)--++(-50.0,-2.8)
  (1084.2,183.6)--++(2.7,64.4)--++(-22.7,-28.1)
    ++(-17.2,-186.4)--++(44.5,48.3)--++(-45.5,31.3)
  (1045.5,29.7)--++(-50.2,-19.2)--++(61.1,-6.4)
  (1044.5,116.4)--++(19.5,49.2)--++(-53.9,-32.5)
  (1020.1,207.6)--++(15.4,33.5)--++(-29.0,3.6)
    ++(12.0,116.7)--++(-14.4,-38.6)--++(34.1,26.0)
  (1001.1,316.6)--++(-31.7,-52.2)--++(34.5,-16.8);

\draw[fill=julia_blue,opacity=\triangleTransparency] 
  (956.6,68.7)--++(-19.3,-53.3)--++(46.8,42.2)
  (939.4,298.8)--++(-17.8,-64.5)--++(44.9,29.9)
  (938.2,302.4)--++(6.4,33.0)--++(-44.9,-18.3)
  (920.0,230.7)--++(-32.2,-26.7)--++(62.5,0.9)
  (869.1,10.0)--++(-88.1,-17.9)--++(137.3,18.7)
  (853.7,144.2)--++(-10.4,-58.6)--++(55.7,22.7)
  (841.6,82.9)--++(-23.4,-2.6)--++(47.6,-26.8)
  (808.1,168.1)--++(-48.6,-38.1)--++(54.3,-46.7)
  (793.9,319.0)--++(-7.6,-57.9)--++(79.7,56.0)
  (792.4,322.3)--++(7.3,40.4)--++(-34.0,-8.3)
  (743.4,236.2)--++(17.9,49.9)--++(-75.4,-32.0)
  (741.1,94.9)--++(-37.5,-32.8)--++(40.3,-26.2)
  (737.4,211.7)--++(-19.5,-30.7)--++(79.0,0.9)
  (716.2,337.6)--++(-43.8,-18.4)--++(46.4,1.8)
  (662.0,10.0)--++(-55.8,-18.0)--++(86.8,8.6)
  (660.0,188.6)--++(-23.2,-24.1)--++(68.7,-24.1)
  (644.8,243.8)--++(20.9,73.0)--++(-53.1,-3.8)
  (600.4,361.8)--++(-47.0,-54.5)--++(56.3,7.5)
    ++(-8.9,-174.0)--++(32.6,23.3)--++(-36.4,42.0)
  (597.8,-8.0)--++(50.7,43.2)--++(-55.4,3.8)
  (593.0,117.6)--++(0.0,-74.7)--++(46.3,58.6)
  (589.3,231.7)--++(-45.8,-15.3)--++(51.2,-6.3)
  (543.6,-15.1)--++(24.9,68.9)--++(-80.4,-9.6)
  (536.1,149.9)--++(1.8,64.0)--++(-24.7,-19.2)
    ++(-3.1,117.6)--++(-17.8,-53.4)--++(45.0,-4.7)
  (510.0,316.9)--++(20.4,31.9)--++(-31.9,-14.2)
    ++(4.5,-226.0)--++(31.2,35.8)--++(-50.4,-11.9)
  (1552.4,186.7)--++(-18.4,-27.6)--++(70.9,3.7)
  (1533.9,282.6)--++(-39.3,-34.6)--++(71.1,-0.9)
    ++(2.4,-4.3)--++(-14.8,-53.7)--++(34.3,1.9)
  (1508.8,349.1)--++(24.7,17.7)--++(-82.2,-6.2)
  (1499.0,141.3)--++(0.9,59.5)--++(-51.9,-10.4)
  (1491.9,-10.3)--++(51.2,30.3)--++(-56.8,32.2)
  (1468.5,305.4)--++(21.8,12.7)--++(-43.7,39.1)
  (1467.0,303.1)--++(-48.1,6.5)--++(49.0,-46.2)
  (1415.9,312.8)--++(26.1,46.6)--++(-51.3,-14.0)
  (1407.4,241.7)--++(-38.6,-48.0)--++(49.0,-10.4)
  (1397.0,95.0)--++(0.0,54.3)--++(-45.9,-53.3)
  (1357.6,1.1)--++(-49.6,8.1)--++(71.5,-25.2)
  (1348.1,92.6)--++(-50.7,-55.3)--++(52.5,18.4)
  (1304.8,315.6)--++(-7.6,-60.7)--++(64.5,37.9)
  (1263.5,253.6)--++(-6.4,-58.7)--++(28.4,-1.8)
    ++(-21.9,152.5)--++(-9.9,-39.7)--++(32.5,28.9)
  (1258.6,39.7)--++(35.8,77.2)--++(-43.3,-1.9)
  (1242.8,-12.3)--++(14.0,47.7)--++(-48.7,20.6)
  (1206.7,60.5)--++(39.5,52.1)--++(-43.1,-25.1)
  (1198.2,182.6)--++(-44.9,-17.4)--++(49.4,-11.9)
  (1113.6,361.3)--++(-11.1,-40.7)--++(43.5,14.8)
  (1088.9,248.7)--++(-2.9,-67.6)--++(66.7,4.8)
  (1084.6,178.6)--++(-16.8,-10.6)--++(37.2,-25.7)
  (1062.1,218.6)--++(-40.0,-14.0)--++(42.8,-34.5)
  (1004.5,244.3)--++(-48.7,-39.3)--++(62.7,0.9);

% White Mask
\draw[fill=white,opacity=\whiteMaskTransparency] (500,0)--(1560,0)--(1560,360)--(500,360)--cycle;
\end{tikzpicture}
