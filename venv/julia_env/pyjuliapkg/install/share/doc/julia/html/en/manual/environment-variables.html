<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Environment Variables · The Julia Language</title><meta name="title" content="Environment Variables · The Julia Language"/><meta property="og:title" content="Environment Variables · The Julia Language"/><meta property="twitter:title" content="Environment Variables · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li class="is-active"><a class="tocitem" href="environment-variables.html">Environment Variables</a><ul class="internal"><li><a class="tocitem" href="#File-locations"><span>File locations</span></a></li><li><a class="tocitem" href="#Pkg.jl"><span>Pkg.jl</span></a></li><li><a class="tocitem" href="#Network-transport"><span>Network transport</span></a></li><li><a class="tocitem" href="#External-applications"><span>External applications</span></a></li><li><a class="tocitem" href="#Parallelization"><span>Parallelization</span></a></li><li><a class="tocitem" href="#REPL-formatting"><span>REPL formatting</span></a></li><li><a class="tocitem" href="#System-and-Package-Image-Building"><span>System and Package Image Building</span></a></li><li><a class="tocitem" href="#Debugging-and-profiling"><span>Debugging and profiling</span></a></li></ul></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="environment-variables.html">Environment Variables</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="environment-variables.html">Environment Variables</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/environment-variables.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Environment-Variables"><a class="docs-heading-anchor" href="#Environment-Variables">Environment Variables</a><a id="Environment-Variables-1"></a><a class="docs-heading-anchor-permalink" href="#Environment-Variables" title="Permalink"></a></h1><p>Julia can be configured with a number of environment variables, set either in the usual way for each operating system, or in a portable way from within Julia. Supposing that you want to set the environment variable <a href="environment-variables.html#JULIA_EDITOR"><code>JULIA_EDITOR</code></a> to <code>vim</code>, you can type <code>ENV[&quot;JULIA_EDITOR&quot;] = &quot;vim&quot;</code> (for instance, in the REPL) to make this change on a case by case basis, or add the same to the user configuration file <code>~/.julia/config/startup.jl</code> in the user&#39;s home directory to have a permanent effect. The current value of the same environment variable can be determined by evaluating <code>ENV[&quot;JULIA_EDITOR&quot;]</code>.</p><p>The environment variables that Julia uses generally start with <code>JULIA</code>. If <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.versioninfo"><code>InteractiveUtils.versioninfo</code></a> is called with the keyword <code>verbose=true</code>, then the output will list any defined environment variables relevant for Julia, including those which include <code>JULIA</code> in their names.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>It is recommended to avoid changing environment variables during runtime, such as within a <code>~/.julia/config/startup.jl</code>.</p><p>One reason is that some julia language variables, such as <a href="environment-variables.html#JULIA_NUM_THREADS"><code>JULIA_NUM_THREADS</code></a> and <a href="environment-variables.html#JULIA_PROJECT"><code>JULIA_PROJECT</code></a>, need to be set before Julia starts.</p><p>Similarly, <code>__init__()</code> functions of user modules in the sysimage (via PackageCompiler) are run before <code>startup.jl</code>, so setting environment variables in a <code>startup.jl</code> may be too late for user code.</p><p>Further, changing environment variables during runtime can introduce data races into otherwise benign code.</p><p>In Bash, environment variables can either be set manually by running, e.g., <code>export JULIA_NUM_THREADS=4</code> before starting Julia, or by adding the same command to <code>~/.bashrc</code> or <code>~/.bash_profile</code> to set the variable each time Bash is started.</p></div></div><h2 id="File-locations"><a class="docs-heading-anchor" href="#File-locations">File locations</a><a id="File-locations-1"></a><a class="docs-heading-anchor-permalink" href="#File-locations" title="Permalink"></a></h2><h3 id="JULIA_BINDIR"><a class="docs-heading-anchor" href="#JULIA_BINDIR"><code>JULIA_BINDIR</code></a><a id="JULIA_BINDIR-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_BINDIR" title="Permalink"></a></h3><p>The absolute path of the directory containing the Julia executable, which sets the global variable <a href="../base/constants.html#Base.Sys.BINDIR"><code>Sys.BINDIR</code></a>. If <code>$JULIA_BINDIR</code> is not set, then Julia determines the value <code>Sys.BINDIR</code> at run-time.</p><p>The executable itself is one of</p><pre><code class="nohighlight hljs">$JULIA_BINDIR/julia
$JULIA_BINDIR/julia-debug</code></pre><p>by default.</p><p>The global variable <code>Base.DATAROOTDIR</code> determines a relative path from <code>Sys.BINDIR</code> to the data directory associated with Julia. Then the path</p><pre><code class="nohighlight hljs">$JULIA_BINDIR/$DATAROOTDIR/julia/base</code></pre><p>determines the directory in which Julia initially searches for source files (via <code>Base.find_source_file()</code>).</p><p>Likewise, the global variable <code>Base.SYSCONFDIR</code> determines a relative path to the configuration file directory. Then Julia searches for a <code>startup.jl</code> file at</p><pre><code class="nohighlight hljs">$JULIA_BINDIR/$SYSCONFDIR/julia/startup.jl
$JULIA_BINDIR/../etc/julia/startup.jl</code></pre><p>by default (via <code>Base.load_julia_startup()</code>).</p><p>For example, a Linux installation with a Julia executable located at <code>/bin/julia</code>, a <code>DATAROOTDIR</code> of <code>../share</code>, and a <code>SYSCONFDIR</code> of <code>../etc</code> will have <a href="environment-variables.html#JULIA_BINDIR"><code>JULIA_BINDIR</code></a> set to <code>/bin</code>, a source-file search path of</p><pre><code class="nohighlight hljs">/share/julia/base</code></pre><p>and a global configuration search path of</p><pre><code class="nohighlight hljs">/etc/julia/startup.jl</code></pre><h3 id="JULIA_PROJECT"><a class="docs-heading-anchor" href="#JULIA_PROJECT"><code>JULIA_PROJECT</code></a><a id="JULIA_PROJECT-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_PROJECT" title="Permalink"></a></h3><p>A directory path that indicates which project should be the initial active project. Setting this environment variable has the same effect as specifying the <code>--project</code> start-up option, but <code>--project</code> has higher precedence. If the variable is set to <code>@.</code> (note the trailing dot) then Julia tries to find a project directory that contains <code>Project.toml</code> or <code>JuliaProject.toml</code> file from the current directory and its parents. See also the chapter on <a href="code-loading.html#code-loading">Code Loading</a>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p><a href="environment-variables.html#JULIA_PROJECT"><code>JULIA_PROJECT</code></a> must be defined before starting julia; defining it in <code>startup.jl</code> is too late in the startup process.</p></div></div><h3 id="JULIA_LOAD_PATH"><a class="docs-heading-anchor" href="#JULIA_LOAD_PATH"><code>JULIA_LOAD_PATH</code></a><a id="JULIA_LOAD_PATH-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_LOAD_PATH" title="Permalink"></a></h3><p>The <a href="environment-variables.html#JULIA_LOAD_PATH"><code>JULIA_LOAD_PATH</code></a> environment variable is used to populate the global Julia <a href="../base/constants.html#Base.LOAD_PATH"><code>LOAD_PATH</code></a> variable, which determines which packages can be loaded via <code>import</code> and <code>using</code> (see <a href="code-loading.html#code-loading">Code Loading</a>).</p><p>Unlike the shell <code>PATH</code> variable, empty entries in <a href="environment-variables.html#JULIA_LOAD_PATH"><code>JULIA_LOAD_PATH</code></a> are expanded to the default value of <code>LOAD_PATH</code>, <code>[&quot;@&quot;, &quot;@v#.#&quot;, &quot;@stdlib&quot;]</code> when populating <code>LOAD_PATH</code>. This allows easy appending, prepending, etc. of the load path value in shell scripts regardless of whether <a href="environment-variables.html#JULIA_LOAD_PATH"><code>JULIA_LOAD_PATH</code></a> is already set or not. For example, to prepend the directory <code>/foo/bar</code> to <code>LOAD_PATH</code> just do</p><pre><code class="language-sh hljs">export JULIA_LOAD_PATH=&quot;/foo/bar:$JULIA_LOAD_PATH&quot;</code></pre><p>If the <a href="environment-variables.html#JULIA_LOAD_PATH"><code>JULIA_LOAD_PATH</code></a> environment variable is already set, its old value will be prepended with <code>/foo/bar</code>. On the other hand, if <a href="environment-variables.html#JULIA_LOAD_PATH"><code>JULIA_LOAD_PATH</code></a> is not set, then it will be set to <code>/foo/bar:</code> which will expand to a <code>LOAD_PATH</code> value of <code>[&quot;/foo/bar&quot;, &quot;@&quot;, &quot;@v#.#&quot;, &quot;@stdlib&quot;]</code>. If <a href="environment-variables.html#JULIA_LOAD_PATH"><code>JULIA_LOAD_PATH</code></a> is set to the empty string, it expands to an empty <code>LOAD_PATH</code> array. In other words, the empty string is interpreted as a zero-element array, not a one-element array of the empty string. This behavior was chosen so that it would be possible to set an empty load path via the environment variable. If you want the default load path, either unset the environment variable or if it must have a value, set it to the string <code>:</code>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>On Windows, path elements are separated by the <code>;</code> character, as is the case with most path lists on Windows. Replace <code>:</code> with <code>;</code> in the above paragraph.</p></div></div><h3 id="JULIA_DEPOT_PATH"><a class="docs-heading-anchor" href="#JULIA_DEPOT_PATH"><code>JULIA_DEPOT_PATH</code></a><a id="JULIA_DEPOT_PATH-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_DEPOT_PATH" title="Permalink"></a></h3><p>The <a href="environment-variables.html#JULIA_DEPOT_PATH"><code>JULIA_DEPOT_PATH</code></a> environment variable is used to populate the global Julia <a href="../base/constants.html#Base.DEPOT_PATH"><code>DEPOT_PATH</code></a> variable, which controls where the package manager, as well as Julia&#39;s code loading mechanisms, look for package registries, installed packages, named environments, repo clones, cached compiled package images, configuration files, and the default location of the REPL&#39;s history file.</p><p>Unlike the shell <code>PATH</code> variable but similar to <a href="environment-variables.html#JULIA_LOAD_PATH"><code>JULIA_LOAD_PATH</code></a>, empty entries in <a href="environment-variables.html#JULIA_DEPOT_PATH"><code>JULIA_DEPOT_PATH</code></a> have special behavior:</p><ul><li>At the end, it is expanded to the default value of <code>DEPOT_PATH</code>, <em>excluding</em> the user depot.</li><li>At the start, it is expanded to the default value of <code>DEPOT_PATH</code>, <em>including</em> the user depot.</li></ul><p>This allows easy overriding of the user depot, while still retaining access to resources that are bundled with Julia, like cache files, artifacts, etc. For example, to switch the user depot to <code>/foo/bar</code> use a trailing <code>:</code></p><pre><code class="language-sh hljs">export JULIA_DEPOT_PATH=&quot;/foo/bar:&quot;</code></pre><p>All package operations, like cloning registrise or installing packages, will now write to <code>/foo/bar</code>, but since the empty entry is expanded to the default system depot, any bundled resources will still be available. If you really only want to use the depot at <code>/foo/bar</code>, and not load any bundled resources, simply set the environment variable to <code>/foo/bar</code> without the trailing colon.</p><p>To append a depot at the end of the full default list, including the default user depot, use a leading <code>:</code></p><pre><code class="language-sh hljs">export JULIA_DEPOT_PATH=&quot;:/foo/bar&quot;</code></pre><p>There are two exceptions to the above rule. First, if <a href="environment-variables.html#JULIA_DEPOT_PATH"><code>JULIA_DEPOT_PATH</code></a> is set to the empty string, it expands to an empty <code>DEPOT_PATH</code> array. In other words, the empty string is interpreted as a zero-element array, not a one-element array of the empty string. This behavior was chosen so that it would be possible to set an empty depot path via the environment variable.</p><p>Second, if no user depot is specified in <a href="environment-variables.html#JULIA_DEPOT_PATH"><code>JULIA_DEPOT_PATH</code></a>, then the empty entry is expanded to the default depot <em>including</em> the user depot. This makes it possible to use the default depot, as if the environment variable was unset, by setting it to the string <code>:</code>.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>On Windows, path elements are separated by the <code>;</code> character, as is the case with most path lists on Windows. Replace <code>:</code> with <code>;</code> in the above paragraph.</p></div></div><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p><a href="environment-variables.html#JULIA_DEPOT_PATH"><code>JULIA_DEPOT_PATH</code></a> must be defined before starting julia; defining it in <code>startup.jl</code> is too late in the startup process; at that point you can instead directly modify the <code>DEPOT_PATH</code> array, which is populated from the environment variable.</p></div></div><h3 id="JULIA_HISTORY"><a class="docs-heading-anchor" href="#JULIA_HISTORY"><code>JULIA_HISTORY</code></a><a id="JULIA_HISTORY-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_HISTORY" title="Permalink"></a></h3><p>The absolute path <code>REPL.find_hist_file()</code> of the REPL&#39;s history file. If <code>$JULIA_HISTORY</code> is not set, then <code>REPL.find_hist_file()</code> defaults to</p><pre><code class="nohighlight hljs">$(DEPOT_PATH[1])/logs/repl_history.jl</code></pre><h3 id="JULIA_MAX_NUM_PRECOMPILE_FILES"><a class="docs-heading-anchor" href="#JULIA_MAX_NUM_PRECOMPILE_FILES"><code>JULIA_MAX_NUM_PRECOMPILE_FILES</code></a><a id="JULIA_MAX_NUM_PRECOMPILE_FILES-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_MAX_NUM_PRECOMPILE_FILES" title="Permalink"></a></h3><p>Sets the maximum number of different instances of a single package that are to be stored in the precompile cache (default = 10).</p><h3 id="JULIA_VERBOSE_LINKING"><a class="docs-heading-anchor" href="#JULIA_VERBOSE_LINKING"><code>JULIA_VERBOSE_LINKING</code></a><a id="JULIA_VERBOSE_LINKING-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_VERBOSE_LINKING" title="Permalink"></a></h3><p>If set to true, linker commands will be displayed during precompilation.</p><h2 id="Pkg.jl"><a class="docs-heading-anchor" href="#Pkg.jl">Pkg.jl</a><a id="Pkg.jl-1"></a><a class="docs-heading-anchor-permalink" href="#Pkg.jl" title="Permalink"></a></h2><h3 id="JULIA_CI"><a class="docs-heading-anchor" href="#JULIA_CI"><code>JULIA_CI</code></a><a id="JULIA_CI-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_CI" title="Permalink"></a></h3><p>If set to <code>true</code>, this indicates to the package server that any package operations are part of a continuous integration (CI) system for the purposes of gathering package usage statistics.</p><h3 id="JULIA_NUM_PRECOMPILE_TASKS"><a class="docs-heading-anchor" href="#JULIA_NUM_PRECOMPILE_TASKS"><code>JULIA_NUM_PRECOMPILE_TASKS</code></a><a id="JULIA_NUM_PRECOMPILE_TASKS-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_NUM_PRECOMPILE_TASKS" title="Permalink"></a></h3><p>The number of parallel tasks to use when precompiling packages. See <a href="https://pkgdocs.julialang.org/v1/api/#Pkg.precompile"><code>Pkg.precompile</code></a>.</p><h3 id="JULIA_PKG_DEVDIR"><a class="docs-heading-anchor" href="#JULIA_PKG_DEVDIR"><code>JULIA_PKG_DEVDIR</code></a><a id="JULIA_PKG_DEVDIR-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_PKG_DEVDIR" title="Permalink"></a></h3><p>The default directory used by <a href="https://pkgdocs.julialang.org/v1/api/#Pkg.develop"><code>Pkg.develop</code></a> for downloading packages.</p><h3 id="JULIA_PKG_IGNORE_HASHES"><a class="docs-heading-anchor" href="#JULIA_PKG_IGNORE_HASHES"><code>JULIA_PKG_IGNORE_HASHES</code></a><a id="JULIA_PKG_IGNORE_HASHES-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_PKG_IGNORE_HASHES" title="Permalink"></a></h3><p>If set to <code>1</code>, this will ignore incorrect hashes in artifacts. This should be used carefully, as it disables verification of downloads, but can resolve issues when moving files across different types of file systems. See <a href="https://github.com/JuliaLang/Pkg.jl/issues/2317">Pkg.jl issue #2317</a> for more details.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.6</header><div class="admonition-body"><p>This is only supported in Julia 1.6 and above.</p></div></div><h3 id="JULIA_PKG_OFFLINE"><a class="docs-heading-anchor" href="#JULIA_PKG_OFFLINE"><code>JULIA_PKG_OFFLINE</code></a><a id="JULIA_PKG_OFFLINE-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_PKG_OFFLINE" title="Permalink"></a></h3><p>If set to <code>true</code>, this will enable offline mode: see <a href="https://pkgdocs.julialang.org/v1/api/#Pkg.offline"><code>Pkg.offline</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p>Pkg&#39;s offline mode requires Julia 1.5 or later.</p></div></div><h3 id="JULIA_PKG_PRECOMPILE_AUTO"><a class="docs-heading-anchor" href="#JULIA_PKG_PRECOMPILE_AUTO"><code>JULIA_PKG_PRECOMPILE_AUTO</code></a><a id="JULIA_PKG_PRECOMPILE_AUTO-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_PKG_PRECOMPILE_AUTO" title="Permalink"></a></h3><p>If set to <code>0</code>, this will disable automatic precompilation by package actions which change the manifest. See <a href="https://pkgdocs.julialang.org/v1/api/#Pkg.precompile"><code>Pkg.precompile</code></a>.</p><h3 id="JULIA_PKG_SERVER"><a class="docs-heading-anchor" href="#JULIA_PKG_SERVER"><code>JULIA_PKG_SERVER</code></a><a id="JULIA_PKG_SERVER-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_PKG_SERVER" title="Permalink"></a></h3><p>Specifies the URL of the package registry to use. By default, <code>Pkg</code> uses <code>https://pkg.julialang.org</code> to fetch Julia packages. In addition, you can disable the use of the PkgServer protocol, and instead access the packages directly from their hosts (GitHub, GitLab, etc.) by setting: <code>export JULIA_PKG_SERVER=&quot;&quot;</code></p><h3 id="JULIA_PKG_SERVER_REGISTRY_PREFERENCE"><a class="docs-heading-anchor" href="#JULIA_PKG_SERVER_REGISTRY_PREFERENCE"><code>JULIA_PKG_SERVER_REGISTRY_PREFERENCE</code></a><a id="JULIA_PKG_SERVER_REGISTRY_PREFERENCE-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_PKG_SERVER_REGISTRY_PREFERENCE" title="Permalink"></a></h3><p>Specifies the preferred registry flavor. Currently supported values are <code>conservative</code> (the default), which will only publish resources that have been processed by the storage server (and thereby have a higher probability of being available from the PkgServers), whereas <code>eager</code> will publish registries whose resources have not necessarily been processed by the storage servers.  Users behind restrictive firewalls that do not allow downloading from arbitrary servers should not use the <code>eager</code> flavor.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>This only affects Julia 1.7 and above.</p></div></div><h3 id="JULIA_PKG_UNPACK_REGISTRY"><a class="docs-heading-anchor" href="#JULIA_PKG_UNPACK_REGISTRY"><code>JULIA_PKG_UNPACK_REGISTRY</code></a><a id="JULIA_PKG_UNPACK_REGISTRY-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_PKG_UNPACK_REGISTRY" title="Permalink"></a></h3><p>If set to <code>true</code>, this will unpack the registry instead of storing it as a compressed tarball.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>This only affects Julia 1.7 and above. Earlier versions will always unpack the registry.</p></div></div><h3 id="JULIA_PKG_USE_CLI_GIT"><a class="docs-heading-anchor" href="#JULIA_PKG_USE_CLI_GIT"><code>JULIA_PKG_USE_CLI_GIT</code></a><a id="JULIA_PKG_USE_CLI_GIT-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_PKG_USE_CLI_GIT" title="Permalink"></a></h3><p>If set to <code>true</code>, Pkg operations which use the git protocol will use an external <code>git</code> executable instead of the default libgit2 library.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Use of the <code>git</code> executable is only supported on Julia 1.7 and above.</p></div></div><h3 id="JULIA_PKGRESOLVE_ACCURACY"><a class="docs-heading-anchor" href="#JULIA_PKGRESOLVE_ACCURACY"><code>JULIA_PKGRESOLVE_ACCURACY</code></a><a id="JULIA_PKGRESOLVE_ACCURACY-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_PKGRESOLVE_ACCURACY" title="Permalink"></a></h3><p>The accuracy of the package resolver. This should be a positive integer, the default is <code>1</code>.</p><h3 id="JULIA_PKG_PRESERVE_TIERED_INSTALLED"><a class="docs-heading-anchor" href="#JULIA_PKG_PRESERVE_TIERED_INSTALLED"><code>JULIA_PKG_PRESERVE_TIERED_INSTALLED</code></a><a id="JULIA_PKG_PRESERVE_TIERED_INSTALLED-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_PKG_PRESERVE_TIERED_INSTALLED" title="Permalink"></a></h3><p>Change the default package installation strategy to <code>Pkg.PRESERVE_TIERED_INSTALLED</code> to let the package manager try to install versions of packages while keeping as many versions of packages already installed as possible.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p>This only affects Julia 1.9 and above.</p></div></div><h2 id="Network-transport"><a class="docs-heading-anchor" href="#Network-transport">Network transport</a><a id="Network-transport-1"></a><a class="docs-heading-anchor-permalink" href="#Network-transport" title="Permalink"></a></h2><h3 id="JULIA_NO_VERIFY_HOSTS"><a class="docs-heading-anchor" href="#JULIA_NO_VERIFY_HOSTS"><code>JULIA_NO_VERIFY_HOSTS</code></a><a id="JULIA_NO_VERIFY_HOSTS-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_NO_VERIFY_HOSTS" title="Permalink"></a></h3><h3 id="JULIA_SSL_NO_VERIFY_HOSTS"><a class="docs-heading-anchor" href="#JULIA_SSL_NO_VERIFY_HOSTS"><code>JULIA_SSL_NO_VERIFY_HOSTS</code></a><a id="JULIA_SSL_NO_VERIFY_HOSTS-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_SSL_NO_VERIFY_HOSTS" title="Permalink"></a></h3><h3 id="JULIA_SSH_NO_VERIFY_HOSTS"><a class="docs-heading-anchor" href="#JULIA_SSH_NO_VERIFY_HOSTS"><code>JULIA_SSH_NO_VERIFY_HOSTS</code></a><a id="JULIA_SSH_NO_VERIFY_HOSTS-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_SSH_NO_VERIFY_HOSTS" title="Permalink"></a></h3><h3 id="JULIA_ALWAYS_VERIFY_HOSTS"><a class="docs-heading-anchor" href="#JULIA_ALWAYS_VERIFY_HOSTS"><code>JULIA_ALWAYS_VERIFY_HOSTS</code></a><a id="JULIA_ALWAYS_VERIFY_HOSTS-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_ALWAYS_VERIFY_HOSTS" title="Permalink"></a></h3><p>Specify hosts whose identity should or should not be verified for specific transport layers. See <a href="https://github.com/JuliaLang/NetworkOptions.jl#verify_host"><code>NetworkOptions.verify_host</code></a></p><h3 id="JULIA_SSL_CA_ROOTS_PATH"><a class="docs-heading-anchor" href="#JULIA_SSL_CA_ROOTS_PATH"><code>JULIA_SSL_CA_ROOTS_PATH</code></a><a id="JULIA_SSL_CA_ROOTS_PATH-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_SSL_CA_ROOTS_PATH" title="Permalink"></a></h3><p>Specify the file or directory containing the certificate authority roots. See <a href="https://github.com/JuliaLang/NetworkOptions.jl#ca_roots"><code>NetworkOptions.ca_roots</code></a></p><h2 id="External-applications"><a class="docs-heading-anchor" href="#External-applications">External applications</a><a id="External-applications-1"></a><a class="docs-heading-anchor-permalink" href="#External-applications" title="Permalink"></a></h2><h3 id="JULIA_SHELL"><a class="docs-heading-anchor" href="#JULIA_SHELL"><code>JULIA_SHELL</code></a><a id="JULIA_SHELL-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_SHELL" title="Permalink"></a></h3><p>The absolute path of the shell with which Julia should execute external commands (via <code>Base.repl_cmd()</code>). Defaults to the environment variable <code>$SHELL</code>, and falls back to <code>/bin/sh</code> if <code>$SHELL</code> is unset.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>On Windows, this environment variable is ignored, and external commands are executed directly.</p></div></div><h3 id="JULIA_EDITOR"><a class="docs-heading-anchor" href="#JULIA_EDITOR"><code>JULIA_EDITOR</code></a><a id="JULIA_EDITOR-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_EDITOR" title="Permalink"></a></h3><p>The editor returned by <code>InteractiveUtils.editor()</code> and used in, e.g., <a href="../stdlib/InteractiveUtils.html#InteractiveUtils.edit-Tuple{AbstractString, Integer}"><code>InteractiveUtils.edit</code></a>, referring to the command of the preferred editor, for instance <code>vim</code>.</p><p><code>$JULIA_EDITOR</code> takes precedence over <code>$VISUAL</code>, which in turn takes precedence over <code>$EDITOR</code>. If none of these environment variables is set, then the editor is taken to be <code>open</code> on Windows and OS X, or <code>/etc/alternatives/editor</code> if it exists, or <code>emacs</code> otherwise.</p><p>To use Visual Studio Code on Windows, set <code>$JULIA_EDITOR</code> to <code>code.cmd</code>.</p><h2 id="Parallelization"><a class="docs-heading-anchor" href="#Parallelization">Parallelization</a><a id="Parallelization-1"></a><a class="docs-heading-anchor-permalink" href="#Parallelization" title="Permalink"></a></h2><h3 id="JULIA_CPU_THREADS"><a class="docs-heading-anchor" href="#JULIA_CPU_THREADS"><code>JULIA_CPU_THREADS</code></a><a id="JULIA_CPU_THREADS-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_CPU_THREADS" title="Permalink"></a></h3><p>Overrides the global variable <a href="../base/constants.html#Base.Sys.CPU_THREADS"><code>Base.Sys.CPU_THREADS</code></a>, the number of logical CPU cores available.</p><h3 id="JULIA_WORKER_TIMEOUT"><a class="docs-heading-anchor" href="#JULIA_WORKER_TIMEOUT"><code>JULIA_WORKER_TIMEOUT</code></a><a id="JULIA_WORKER_TIMEOUT-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_WORKER_TIMEOUT" title="Permalink"></a></h3><p>A <a href="../base/numbers.html#Core.Float64"><code>Float64</code></a> that sets the value of <code>Distributed.worker_timeout()</code> (default: <code>60.0</code>). This function gives the number of seconds a worker process will wait for a master process to establish a connection before dying.</p><h3 id="JULIA_NUM_THREADS"><a class="docs-heading-anchor" href="#JULIA_NUM_THREADS"><code>JULIA_NUM_THREADS</code></a><a id="JULIA_NUM_THREADS-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_NUM_THREADS" title="Permalink"></a></h3><p>An unsigned 64-bit integer (<code>uint64_t</code>) that sets the maximum number of threads available to Julia.  If <code>$JULIA_NUM_THREADS</code> is not positive or is not set, or if the number of CPU threads cannot be determined through system calls, then the number of threads is set to <code>1</code>.</p><p>If <code>$JULIA_NUM_THREADS</code> is set to <code>auto</code>, then the number of threads will be set to the number of CPU threads.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p><code>JULIA_NUM_THREADS</code> must be defined before starting julia; defining it in <code>startup.jl</code> is too late in the startup process.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.5</header><div class="admonition-body"><p>In Julia 1.5 and above the number of threads can also be specified on startup using the <code>-t</code>/<code>--threads</code> command line argument.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>The <code>auto</code> value for <code>$JULIA_NUM_THREADS</code> requires Julia 1.7 or above.</p></div></div><h3 id="JULIA_THREAD_SLEEP_THRESHOLD"><a class="docs-heading-anchor" href="#JULIA_THREAD_SLEEP_THRESHOLD"><code>JULIA_THREAD_SLEEP_THRESHOLD</code></a><a id="JULIA_THREAD_SLEEP_THRESHOLD-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_THREAD_SLEEP_THRESHOLD" title="Permalink"></a></h3><p>If set to a string that starts with the case-insensitive substring <code>&quot;infinite&quot;</code>, then spinning threads never sleep. Otherwise, <code>$JULIA_THREAD_SLEEP_THRESHOLD</code> is interpreted as an unsigned 64-bit integer (<code>uint64_t</code>) and gives, in nanoseconds, the amount of time after which spinning threads should sleep.</p><h3 id="JULIA_NUM_GC_THREADS"><a class="docs-heading-anchor" href="#JULIA_NUM_GC_THREADS"><code>JULIA_NUM_GC_THREADS</code></a><a id="JULIA_NUM_GC_THREADS-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_NUM_GC_THREADS" title="Permalink"></a></h3><p>Sets the number of threads used by Garbage Collection. If unspecified is set to half of the number of worker threads.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.10</header><div class="admonition-body"><p>The environment variable was added in 1.10</p></div></div><h3 id="JULIA_IMAGE_THREADS"><a class="docs-heading-anchor" href="#JULIA_IMAGE_THREADS"><code>JULIA_IMAGE_THREADS</code></a><a id="JULIA_IMAGE_THREADS-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_IMAGE_THREADS" title="Permalink"></a></h3><p>An unsigned 32-bit integer that sets the number of threads used by image compilation in this Julia process. The value of this variable may be ignored if the module is a small module. If left unspecified, the smaller of the value of <a href="environment-variables.html#JULIA_CPU_THREADS"><code>JULIA_CPU_THREADS</code></a> or half the number of logical CPU cores is used in its place.</p><h3 id="JULIA_IMAGE_TIMINGS"><a class="docs-heading-anchor" href="#JULIA_IMAGE_TIMINGS"><code>JULIA_IMAGE_TIMINGS</code></a><a id="JULIA_IMAGE_TIMINGS-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_IMAGE_TIMINGS" title="Permalink"></a></h3><p>A boolean value that determines if detailed timing information is printed during during image compilation. Defaults to 0.</p><h3 id="JULIA_EXCLUSIVE"><a class="docs-heading-anchor" href="#JULIA_EXCLUSIVE"><code>JULIA_EXCLUSIVE</code></a><a id="JULIA_EXCLUSIVE-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_EXCLUSIVE" title="Permalink"></a></h3><p>If set to anything besides <code>0</code>, then Julia&#39;s thread policy is consistent with running on a dedicated machine: the master thread is on proc 0, and threads are affinitized. Otherwise, Julia lets the operating system handle thread policy.</p><h2 id="REPL-formatting"><a class="docs-heading-anchor" href="#REPL-formatting">REPL formatting</a><a id="REPL-formatting-1"></a><a class="docs-heading-anchor-permalink" href="#REPL-formatting" title="Permalink"></a></h2><p>Environment variables that determine how REPL output should be formatted at the terminal. Generally, these variables should be set to <a href="https://en.wikipedia.org/wiki/ANSI_escape_code">ANSI terminal escape sequences</a>. Julia provides a high-level interface with much of the same functionality; see the section on <a href="../stdlib/REPL.html#The-Julia-REPL">The Julia REPL</a>.</p><h3 id="JULIA_ERROR_COLOR"><a class="docs-heading-anchor" href="#JULIA_ERROR_COLOR"><code>JULIA_ERROR_COLOR</code></a><a id="JULIA_ERROR_COLOR-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_ERROR_COLOR" title="Permalink"></a></h3><p>The formatting <code>Base.error_color()</code> (default: light red, <code>&quot;\033[91m&quot;</code>) that errors should have at the terminal.</p><h3 id="JULIA_WARN_COLOR"><a class="docs-heading-anchor" href="#JULIA_WARN_COLOR"><code>JULIA_WARN_COLOR</code></a><a id="JULIA_WARN_COLOR-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_WARN_COLOR" title="Permalink"></a></h3><p>The formatting <code>Base.warn_color()</code> (default: yellow, <code>&quot;\033[93m&quot;</code>) that warnings should have at the terminal.</p><h3 id="JULIA_INFO_COLOR"><a class="docs-heading-anchor" href="#JULIA_INFO_COLOR"><code>JULIA_INFO_COLOR</code></a><a id="JULIA_INFO_COLOR-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_INFO_COLOR" title="Permalink"></a></h3><p>The formatting <code>Base.info_color()</code> (default: cyan, <code>&quot;\033[36m&quot;</code>) that info should have at the terminal.</p><h3 id="JULIA_INPUT_COLOR"><a class="docs-heading-anchor" href="#JULIA_INPUT_COLOR"><code>JULIA_INPUT_COLOR</code></a><a id="JULIA_INPUT_COLOR-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_INPUT_COLOR" title="Permalink"></a></h3><p>The formatting <code>Base.input_color()</code> (default: normal, <code>&quot;\033[0m&quot;</code>) that input should have at the terminal.</p><h3 id="JULIA_ANSWER_COLOR"><a class="docs-heading-anchor" href="#JULIA_ANSWER_COLOR"><code>JULIA_ANSWER_COLOR</code></a><a id="JULIA_ANSWER_COLOR-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_ANSWER_COLOR" title="Permalink"></a></h3><p>The formatting <code>Base.answer_color()</code> (default: normal, <code>&quot;\033[0m&quot;</code>) that output should have at the terminal.</p><h2 id="System-and-Package-Image-Building"><a class="docs-heading-anchor" href="#System-and-Package-Image-Building">System and Package Image Building</a><a id="System-and-Package-Image-Building-1"></a><a class="docs-heading-anchor-permalink" href="#System-and-Package-Image-Building" title="Permalink"></a></h2><h3 id="JULIA_CPU_TARGET"><a class="docs-heading-anchor" href="#JULIA_CPU_TARGET"><code>JULIA_CPU_TARGET</code></a><a id="JULIA_CPU_TARGET-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_CPU_TARGET" title="Permalink"></a></h3><p>Modify the target machine architecture for (pre)compiling <a href="../devdocs/sysimg.html#sysimg-multi-versioning">system</a> and <a href="../devdocs/pkgimg.html#pkgimgs-multi-versioning">package images</a>. <code>JULIA_CPU_TARGET</code> only affects machine code image generation being output to a disk cache. Unlike the <code>--cpu-target</code>, or <code>-C</code>, <a href="command-line-interface.html#cli">command line option</a>, it does not influence just-in-time (JIT) code generation within a Julia session where machine code is only stored in memory.</p><p>Valid values for <a href="environment-variables.html#JULIA_CPU_TARGET"><code>JULIA_CPU_TARGET</code></a> can be obtained by executing <code>julia -C help</code>.</p><p>Setting <a href="environment-variables.html#JULIA_CPU_TARGET"><code>JULIA_CPU_TARGET</code></a> is important for heterogeneous compute systems where processors of distinct types or features may be present. This is commonly encountered in high performance computing (HPC) clusters since the component nodes may be using distinct processors.</p><p>The CPU target string is a list of strings separated by <code>;</code> each string starts with a CPU or architecture name and followed by an optional list of features separated by <code>,</code>. A <code>generic</code> or empty CPU name means the basic required feature set of the target ISA which is at least the architecture the C/C++ runtime is compiled with. Each string is interpreted by LLVM.</p><p>A few special features are supported:</p><ol><li><p><code>clone_all</code></p><p>This forces the target to have all functions in sysimg cloned.   When used in negative form (i.e. <code>-clone_all</code>), this disables full clone that&#39;s   enabled by default for certain targets.</p></li><li><p><code>base([0-9]*)</code></p><p>This specifies the (0-based) base target index. The base target is the target   that the current target is based on, i.e. the functions that are not being cloned   will use the version in the base target. This option causes the base target to be   fully cloned (as if <code>clone_all</code> is specified for it) if it is not the default target (0).   The index can only be smaller than the current index.</p></li><li><p><code>opt_size</code></p><p>Optimize for size with minimum performance impact. Clang/GCC&#39;s <code>-Os</code>.</p></li><li><p><code>min_size</code></p><p>Optimize only for size. Clang&#39;s <code>-Oz</code>.</p></li></ol><h2 id="Debugging-and-profiling"><a class="docs-heading-anchor" href="#Debugging-and-profiling">Debugging and profiling</a><a id="Debugging-and-profiling-1"></a><a class="docs-heading-anchor-permalink" href="#Debugging-and-profiling" title="Permalink"></a></h2><h3 id="JULIA_DEBUG"><a class="docs-heading-anchor" href="#JULIA_DEBUG"><code>JULIA_DEBUG</code></a><a id="JULIA_DEBUG-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_DEBUG" title="Permalink"></a></h3><p>Enable debug logging for a file or module, see <a href="../stdlib/Logging.html#man-logging"><code>Logging</code></a> for more information.</p><h3 id="JULIA_PROFILE_PEEK_HEAP_SNAPSHOT"><a class="docs-heading-anchor" href="#JULIA_PROFILE_PEEK_HEAP_SNAPSHOT"><code>JULIA_PROFILE_PEEK_HEAP_SNAPSHOT</code></a><a id="JULIA_PROFILE_PEEK_HEAP_SNAPSHOT-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_PROFILE_PEEK_HEAP_SNAPSHOT" title="Permalink"></a></h3><p>Enable collecting of a heap snapshot during execution via the profiling peek mechanism. See <a href="../stdlib/Profile.html#Triggered-During-Execution">Triggered During Execution</a>.</p><h3 id="JULIA_TIMING_SUBSYSTEMS"><a class="docs-heading-anchor" href="#JULIA_TIMING_SUBSYSTEMS"><code>JULIA_TIMING_SUBSYSTEMS</code></a><a id="JULIA_TIMING_SUBSYSTEMS-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_TIMING_SUBSYSTEMS" title="Permalink"></a></h3><p>Allows you to enable or disable zones for a specific Julia run. For instance, setting the variable to <code>+GC,-INFERENCE</code> will enable the <code>GC</code> zones and disable the <code>INFERENCE</code> zones. See <a href="../devdocs/external_profilers.html#Dynamically-Enabling-and-Disabling-Zones">Dynamically Enabling and Disabling Zones</a>.</p><h3 id="JULIA_GC_ALLOC_POOL"><a class="docs-heading-anchor" href="#JULIA_GC_ALLOC_POOL"><code>JULIA_GC_ALLOC_POOL</code></a><a id="JULIA_GC_ALLOC_POOL-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_GC_ALLOC_POOL" title="Permalink"></a></h3><h3 id="JULIA_GC_ALLOC_OTHER"><a class="docs-heading-anchor" href="#JULIA_GC_ALLOC_OTHER"><code>JULIA_GC_ALLOC_OTHER</code></a><a id="JULIA_GC_ALLOC_OTHER-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_GC_ALLOC_OTHER" title="Permalink"></a></h3><h3 id="JULIA_GC_ALLOC_PRINT"><a class="docs-heading-anchor" href="#JULIA_GC_ALLOC_PRINT"><code>JULIA_GC_ALLOC_PRINT</code></a><a id="JULIA_GC_ALLOC_PRINT-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_GC_ALLOC_PRINT" title="Permalink"></a></h3><p>If set, these environment variables take strings that optionally start with the character <code>&#39;r&#39;</code>, followed by a string interpolation of a colon-separated list of three signed 64-bit integers (<code>int64_t</code>). This triple of integers <code>a:b:c</code> represents the arithmetic sequence <code>a</code>, <code>a + b</code>, <code>a + 2*b</code>, ... <code>c</code>.</p><ul><li>If it&#39;s the <code>n</code>th time that <code>jl_gc_pool_alloc()</code> has been called, and <code>n</code>   belongs to the arithmetic sequence represented by <code>$JULIA_GC_ALLOC_POOL</code>,   then garbage collection is forced.</li><li>If it&#39;s the <code>n</code>th time that <code>maybe_collect()</code> has been called, and <code>n</code> belongs   to the arithmetic sequence represented by <code>$JULIA_GC_ALLOC_OTHER</code>, then garbage   collection is forced.</li><li>If it&#39;s the <code>n</code>th time that <code>jl_gc_collect()</code> has been called, and <code>n</code> belongs   to the arithmetic sequence represented by <code>$JULIA_GC_ALLOC_PRINT</code>, then counts   for the number of calls to <code>jl_gc_pool_alloc()</code> and <code>maybe_collect()</code> are   printed.</li></ul><p>If the value of the environment variable begins with the character <code>&#39;r&#39;</code>, then the interval between garbage collection events is randomized.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>These environment variables only have an effect if Julia was compiled with garbage-collection debugging (that is, if <code>WITH_GC_DEBUG_ENV</code> is set to <code>1</code> in the build configuration).</p></div></div><h3 id="JULIA_GC_NO_GENERATIONAL"><a class="docs-heading-anchor" href="#JULIA_GC_NO_GENERATIONAL"><code>JULIA_GC_NO_GENERATIONAL</code></a><a id="JULIA_GC_NO_GENERATIONAL-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_GC_NO_GENERATIONAL" title="Permalink"></a></h3><p>If set to anything besides <code>0</code>, then the Julia garbage collector never performs &quot;quick sweeps&quot; of memory.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This environment variable only has an effect if Julia was compiled with garbage-collection debugging (that is, if <code>WITH_GC_DEBUG_ENV</code> is set to <code>1</code> in the build configuration).</p></div></div><h3 id="JULIA_GC_WAIT_FOR_DEBUGGER"><a class="docs-heading-anchor" href="#JULIA_GC_WAIT_FOR_DEBUGGER"><code>JULIA_GC_WAIT_FOR_DEBUGGER</code></a><a id="JULIA_GC_WAIT_FOR_DEBUGGER-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_GC_WAIT_FOR_DEBUGGER" title="Permalink"></a></h3><p>If set to anything besides <code>0</code>, then the Julia garbage collector will wait for a debugger to attach instead of aborting whenever there&#39;s a critical error.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This environment variable only has an effect if Julia was compiled with garbage-collection debugging (that is, if <code>WITH_GC_DEBUG_ENV</code> is set to <code>1</code> in the build configuration).</p></div></div><h3 id="ENABLE_JITPROFILING"><a class="docs-heading-anchor" href="#ENABLE_JITPROFILING"><code>ENABLE_JITPROFILING</code></a><a id="ENABLE_JITPROFILING-1"></a><a class="docs-heading-anchor-permalink" href="#ENABLE_JITPROFILING" title="Permalink"></a></h3><p>If set to anything besides <code>0</code>, then the compiler will create and register an event listener for just-in-time (JIT) profiling.</p><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>This environment variable only has an effect if Julia was compiled with JIT profiling support, using either</p><ul><li>Intel&#39;s <a href="https://software.intel.com/en-us/vtune">VTune™ Amplifier</a> (<code>USE_INTEL_JITEVENTS</code> set to <code>1</code> in the build configuration), or</li><li><a href="https://oprofile.sourceforge.io/news/">OProfile</a> (<code>USE_OPROFILE_JITEVENTS</code> set to <code>1</code> in the build configuration).</li><li><a href="https://perf.wiki.kernel.org">Perf</a> (<code>USE_PERF_JITEVENTS</code> set to <code>1</code> in the build configuration). This integration is enabled by default.</li></ul></div></div><h3 id="ENABLE_GDBLISTENER"><a class="docs-heading-anchor" href="#ENABLE_GDBLISTENER"><code>ENABLE_GDBLISTENER</code></a><a id="ENABLE_GDBLISTENER-1"></a><a class="docs-heading-anchor-permalink" href="#ENABLE_GDBLISTENER" title="Permalink"></a></h3><p>If set to anything besides <code>0</code> enables GDB registration of Julia code on release builds. On debug builds of Julia this is always enabled. Recommended to use with <code>-g 2</code>.</p><h3 id="JULIA_LLVM_ARGS"><a class="docs-heading-anchor" href="#JULIA_LLVM_ARGS"><code>JULIA_LLVM_ARGS</code></a><a id="JULIA_LLVM_ARGS-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_LLVM_ARGS" title="Permalink"></a></h3><p>Arguments to be passed to the LLVM backend.</p><h3 id="JULIA_FALLBACK_REPL"><a class="docs-heading-anchor" href="#JULIA_FALLBACK_REPL"><code>JULIA_FALLBACK_REPL</code></a><a id="JULIA_FALLBACK_REPL-1"></a><a class="docs-heading-anchor-permalink" href="#JULIA_FALLBACK_REPL" title="Permalink"></a></h3><p>Forces the fallback repl instead of REPL.jl.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="handling-operating-system-variation.html">« Handling Operating System Variation</a><a class="docs-footer-nextpage" href="embedding.html">Embedding Julia »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
