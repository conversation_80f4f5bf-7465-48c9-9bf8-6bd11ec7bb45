<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Modules · The Julia Language</title><meta name="title" content="Modules · The Julia Language"/><meta property="og:title" content="Modules · The Julia Language"/><meta property="twitter:title" content="Modules · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li><a class="tocitem" href="installation.html">Installation</a></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li class="is-active"><a class="tocitem" href="modules.html">Modules</a><ul class="internal"><li><a class="tocitem" href="#namespace-management"><span>Namespace management</span></a></li><li><a class="tocitem" href="#Submodules-and-relative-paths"><span>Submodules and relative paths</span></a></li><li><a class="tocitem" href="#Module-initialization-and-precompilation"><span>Module initialization and precompilation</span></a></li></ul></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="modules.html">Modules</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="modules.html">Modules</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/modules.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="modules"><a class="docs-heading-anchor" href="#modules">Modules</a><a id="modules-1"></a><a class="docs-heading-anchor-permalink" href="#modules" title="Permalink"></a></h1><p>Modules in Julia help organize code into coherent units. They are delimited syntactically inside <code>module NameOfModule ... end</code>, and have the following features:</p><ol><li><p>Modules are separate namespaces, each introducing a new global scope. This is useful, because it allows the same name to be used for different functions or global variables without conflict, as long as they are in separate modules.</p></li><li><p>Modules have facilities for detailed namespace management: each defines a set of names it <code>export</code>s and marks as <code>public</code>, and can import names from other modules with <code>using</code> and <code>import</code> (we explain these below).</p></li><li><p>Modules can be precompiled for faster loading, and may contain code for runtime initialization.</p></li></ol><p>Typically, in larger Julia packages you will see module code organized into files, eg</p><pre><code class="language-julia hljs">module SomeModule

# export, public, using, import statements are usually here; we discuss these below

include(&quot;file1.jl&quot;)
include(&quot;file2.jl&quot;)

end</code></pre><p>Files and file names are mostly unrelated to modules; modules are associated only with module expressions. One can have multiple files per module, and multiple modules per file. <code>include</code> behaves as if the contents of the source file were evaluated in the global scope of the including module. In this chapter, we use short and simplified examples, so we won&#39;t use <code>include</code>.</p><p>The recommended style is not to indent the body of the module, since that would typically lead to whole files being indented. Also, it is common to use <code>UpperCamelCase</code> for module names (just like types), and use the plural form if applicable, especially if the module contains a similarly named identifier, to avoid name clashes. For example,</p><pre><code class="language-julia hljs">module FastThings

struct FastThing
    ...
end

end</code></pre><h2 id="namespace-management"><a class="docs-heading-anchor" href="#namespace-management">Namespace management</a><a id="namespace-management-1"></a><a class="docs-heading-anchor-permalink" href="#namespace-management" title="Permalink"></a></h2><p>Namespace management refers to the facilities the language offers for making names in a module available in other modules. We discuss the related concepts and functionality below in detail.</p><h3 id="Qualified-names"><a class="docs-heading-anchor" href="#Qualified-names">Qualified names</a><a id="Qualified-names-1"></a><a class="docs-heading-anchor-permalink" href="#Qualified-names" title="Permalink"></a></h3><p>Names for functions, variables and types in the global scope like <code>sin</code>, <code>ARGS</code>, and <code>UnitRange</code> always belong to a module, called the <em>parent module</em>, which can be found interactively with <a href="../base/base.html#Base.parentmodule"><code>parentmodule</code></a>, for example</p><pre><code class="language-julia-repl hljs">julia&gt; parentmodule(UnitRange)
Base</code></pre><p>One can also refer to these names outside their parent module by prefixing them with their module, eg <code>Base.UnitRange</code>. This is called a <em>qualified name</em>. The parent module may be accessible using a chain of submodules like <code>Base.Math.sin</code>, where <code>Base.Math</code> is called the <em>module path</em>. Due to syntactic ambiguities, qualifying a name that contains only symbols, such as an operator, requires inserting a colon, e.g. <code>Base.:+</code>. A small number of operators additionally require parentheses, e.g. <code>Base.:(==)</code>.</p><p>If a name is qualified, then it is always <em>accessible</em>, and in case of a function, it can also have methods added to it by using the qualified name as the function name.</p><p>Within a module, a variable name can be “reserved” without assigning to it by declaring it as <code>global x</code>. This prevents name conflicts for globals initialized after load time. The syntax <code>M.x = y</code> does not work to assign a global in another module; global assignment is always module-local.</p><h3 id="Export-lists"><a class="docs-heading-anchor" href="#Export-lists">Export lists</a><a id="Export-lists-1"></a><a class="docs-heading-anchor-permalink" href="#Export-lists" title="Permalink"></a></h3><p>Names (referring to functions, types, global variables, and constants) can be added to the <em>export list</em> of a module with <code>export</code>: these are the symbols that are imported when <code>using</code> the module. Typically, they are at or near the top of the module definition so that readers of the source code can find them easily, as in</p><pre><code class="language-julia-repl hljs">julia&gt; module NiceStuff
       export nice, DOG
       struct Dog end      # singleton type, not exported
       const DOG = Dog()   # named instance, exported
       nice(x) = &quot;nice $x&quot; # function, exported
       end;
</code></pre><p>but this is just a style suggestion — a module can have multiple <code>export</code> statements in arbitrary locations.</p><p>It is common to export names which form part of the API (application programming interface). In the above code, the export list suggests that users should use <code>nice</code> and <code>DOG</code>. However, since qualified names always make identifiers accessible, this is just an option for organizing APIs: unlike other languages, Julia has no facilities for truly hiding module internals.</p><p>Also, some modules don&#39;t export names at all. This is usually done if they use common words, such as <code>derivative</code>, in their API, which could easily clash with the export lists of other modules. We will see how to manage name clashes below.</p><p>To mark a name as public without exporting it into the namespace of folks who call <code>using NiceStuff</code>, one can use <code>public</code> instead of <code>export</code>. This marks the public name(s) as part of the public API, but does not have any namespace implications. The <code>public</code> keyword is only available in Julia 1.11 and above. To maintain compatibility with Julia 1.10 and below, use the <code>@compat</code> macro from the <a href="https://github.com/JuliaLang/Compat.jl">Compat</a> package.</p><h3 id="Standalone-using-and-import"><a class="docs-heading-anchor" href="#Standalone-using-and-import">Standalone <code>using</code> and <code>import</code></a><a id="Standalone-using-and-import-1"></a><a class="docs-heading-anchor-permalink" href="#Standalone-using-and-import" title="Permalink"></a></h3><p>For interactive use, the most common way of loading a module is <code>using ModuleName</code>. This <a href="code-loading.html#code-loading">loads</a> the code associated with <code>ModuleName</code>, and brings</p><ol><li><p>the module name</p></li><li><p>and the elements of the export list into the surrounding global namespace.</p></li></ol><p>Technically, the statement <code>using ModuleName</code> means that a module called <code>ModuleName</code> will be available for resolving names as needed. When a global variable is encountered that has no definition in the current module, the system will search for it among variables exported by <code>ModuleName</code> and use it if it is found there. This means that all uses of that global within the current module will resolve to the definition of that variable in <code>ModuleName</code>.</p><p>To load a module from a package, the statement <code>using ModuleName</code> can be used. To load a module from a locally defined module, a dot needs to be added before the module name like <code>using .ModuleName</code>.</p><p>To continue with our example,</p><pre><code class="language-julia-repl hljs">julia&gt; using .NiceStuff</code></pre><p>would load the above code, making <code>NiceStuff</code> (the module name), <code>DOG</code> and <code>nice</code> available. <code>Dog</code> is not on the export list, but it can be accessed if the name is qualified with the module path (which here is just the module name) as <code>NiceStuff.Dog</code>.</p><p>Importantly, <strong><code>using ModuleName</code> is the only form for which export lists matter at all</strong>.</p><p>In contrast,</p><pre><code class="language-julia-repl hljs">julia&gt; import .NiceStuff</code></pre><p>brings <em>only</em> the module name into scope. Users would need to use <code>NiceStuff.DOG</code>, <code>NiceStuff.Dog</code>, and <code>NiceStuff.nice</code> to access its contents. Usually, <code>import ModuleName</code> is used in contexts when the user wants to keep the namespace clean. As we will see in the next section <code>import .NiceStuff</code> is equivalent to <code>using .NiceStuff: NiceStuff</code>.</p><p>You can combine multiple <code>using</code> and <code>import</code> statements of the same kind in a comma-separated expression, e.g.</p><pre><code class="language-julia-repl hljs">julia&gt; using LinearAlgebra, Random</code></pre><h3 id="using-and-import-with-specific-identifiers,-and-adding-methods"><a class="docs-heading-anchor" href="#using-and-import-with-specific-identifiers,-and-adding-methods"><code>using</code> and <code>import</code> with specific identifiers, and adding methods</a><a id="using-and-import-with-specific-identifiers,-and-adding-methods-1"></a><a class="docs-heading-anchor-permalink" href="#using-and-import-with-specific-identifiers,-and-adding-methods" title="Permalink"></a></h3><p>When <code>using ModuleName:</code> or <code>import ModuleName:</code> is followed by a comma-separated list of names, the module is loaded, but <em>only those specific names are brought into the namespace</em> by the statement. For example,</p><pre><code class="language-julia-repl hljs">julia&gt; using .NiceStuff: nice, DOG</code></pre><p>will import the names <code>nice</code> and <code>DOG</code>.</p><p>Importantly, the module name <code>NiceStuff</code> will <em>not</em> be in the namespace. If you want to make it accessible, you have to list it explicitly, as</p><pre><code class="language-julia-repl hljs">julia&gt; using .NiceStuff: nice, DOG, NiceStuff</code></pre><p>When two or more packages/modules export a name and that name does not refer to the same thing in each of the packages, and the packages are loaded via <code>using</code> without an explicit list of names, it is an error to reference that name without qualification. It is thus recommended that code intended to be forward-compatible with future versions of its dependencies and of Julia, e.g., code in released packages, list the names it uses from each loaded package, e.g., <code>using Foo: Foo, f</code> rather than <code>using Foo</code>.</p><p>Julia has two forms for seemingly the same thing because only <code>import ModuleName: f</code> allows adding methods to <code>f</code> <em>without a module path</em>. That is to say, the following example will give an error:</p><pre><code class="language-julia-repl hljs">julia&gt; using .NiceStuff: nice

julia&gt; struct Cat end

julia&gt; nice(::Cat) = &quot;nice 😸&quot;
ERROR: invalid method definition in Main: function NiceStuff.nice must be explicitly imported to be extended
Stacktrace:
 [1] top-level scope
   @ none:0
 [2] top-level scope
   @ none:1
</code></pre><p>This error prevents accidentally adding methods to functions in other modules that you only intended to use.</p><p>There are two ways to deal with this. You can always qualify function names with a module path:</p><pre><code class="language-julia-repl hljs">julia&gt; using .NiceStuff

julia&gt; struct Cat end

julia&gt; NiceStuff.nice(::Cat) = &quot;nice 😸&quot;
</code></pre><p>Alternatively, you can <code>import</code> the specific function name:</p><pre><code class="language-julia-repl hljs">julia&gt; import .NiceStuff: nice

julia&gt; struct Cat end

julia&gt; nice(::Cat) = &quot;nice 😸&quot;
nice (generic function with 2 methods)</code></pre><p>Which one you choose is a matter of style. The first form makes it clear that you are adding a method to a function in another module (remember, that the imports and the method definition may be in separate files), while the second one is shorter, which is especially convenient if you are defining multiple methods.</p><p>Once a variable is made visible via <code>using</code> or <code>import</code>, a module may not create its own variable with the same name. Imported variables are read-only; assigning to a global variable always affects a variable owned by the current module, or else raises an error.</p><h3 id="Renaming-with-as"><a class="docs-heading-anchor" href="#Renaming-with-as">Renaming with <code>as</code></a><a id="Renaming-with-as-1"></a><a class="docs-heading-anchor-permalink" href="#Renaming-with-as" title="Permalink"></a></h3><p>An identifier brought into scope by <code>import</code> or <code>using</code> can be renamed with the keyword <code>as</code>. This is useful for working around name conflicts as well as for shortening names. For example, <code>Base</code> exports the function name <code>read</code>, but the CSV.jl package also provides <code>CSV.read</code>. If we are going to invoke CSV reading many times, it would be convenient to drop the <code>CSV.</code> qualifier. But then it is ambiguous whether we are referring to <code>Base.read</code> or <code>CSV.read</code>:</p><pre><code class="language-julia-repl hljs">julia&gt; read;

julia&gt; import CSV: read
WARNING: ignoring conflicting import of CSV.read into Main</code></pre><p>Renaming provides a solution:</p><pre><code class="language-julia-repl hljs">julia&gt; import CSV: read as rd</code></pre><p>Imported packages themselves can also be renamed:</p><pre><code class="language-julia hljs">import BenchmarkTools as BT</code></pre><p><code>as</code> works with <code>using</code> only when a single identifier is brought into scope. For example <code>using CSV: read as rd</code> works, but <code>using CSV as C</code> does not, since it operates on all of the exported names in <code>CSV</code>.</p><h3 id="Mixing-multiple-using-and-import-statements"><a class="docs-heading-anchor" href="#Mixing-multiple-using-and-import-statements">Mixing multiple <code>using</code> and <code>import</code> statements</a><a id="Mixing-multiple-using-and-import-statements-1"></a><a class="docs-heading-anchor-permalink" href="#Mixing-multiple-using-and-import-statements" title="Permalink"></a></h3><p>When multiple <code>using</code> or <code>import</code> statements of any of the forms above are used, their effect is combined in the order they appear. For example,</p><pre><code class="language-julia-repl hljs">julia&gt; using .NiceStuff         # exported names and the module name

julia&gt; import .NiceStuff: nice  # allows adding methods to unqualified functions
</code></pre><p>would bring all the exported names of <code>NiceStuff</code> and the module name itself into scope, and also allow adding methods to <code>nice</code> without prefixing it with a module name.</p><h3 id="Handling-name-conflicts"><a class="docs-heading-anchor" href="#Handling-name-conflicts">Handling name conflicts</a><a id="Handling-name-conflicts-1"></a><a class="docs-heading-anchor-permalink" href="#Handling-name-conflicts" title="Permalink"></a></h3><p>Consider the situation where two (or more) packages export the same name, as in</p><pre><code class="language-julia-repl hljs">julia&gt; module A
       export f
       f() = 1
       end
A
julia&gt; module B
       export f
       f() = 2
       end
B</code></pre><p>The statement <code>using .A, .B</code> works, but when you try to call <code>f</code>, you get an error with a hint</p><pre><code class="language-julia-repl hljs">julia&gt; using .A, .B

julia&gt; f
ERROR: UndefVarError: `f` not defined in `Main`
Hint: It looks like two or more modules export different bindings with this name, resulting in ambiguity. Try explicitly importing it from a particular module, or qualifying the name with the module it should come from.</code></pre><p>Here, Julia cannot decide which <code>f</code> you are referring to, so you have to make a choice. The following solutions are commonly used:</p><ol><li><p>Simply proceed with qualified names like <code>A.f</code> and <code>B.f</code>. This makes the context clear to the reader of your code, especially if <code>f</code> just happens to coincide but has different meaning in various packages. For example, <code>degree</code> has various uses in mathematics, the natural sciences, and in everyday life, and these meanings should be kept separate.</p></li><li><p>Use the <code>as</code> keyword above to rename one or both identifiers, eg</p><pre><code class="language-julia-repl hljs">julia&gt; using .A: f as f

julia&gt; using .B: f as g
</code></pre><p>would make <code>B.f</code> available as <code>g</code>. Here, we are assuming that you did not use <code>using A</code> before, which would have brought <code>f</code> into the namespace.</p></li><li><p>When the names in question <em>do</em> share a meaning, it is common for one module to import it from another, or have a lightweight “base” package with the sole function of defining an interface like this, which can be used by other packages. It is conventional to have such package names end in <code>...Base</code> (which has nothing to do with Julia&#39;s <code>Base</code> module).</p></li></ol><h3 id="Default-top-level-definitions-and-bare-modules"><a class="docs-heading-anchor" href="#Default-top-level-definitions-and-bare-modules">Default top-level definitions and bare modules</a><a id="Default-top-level-definitions-and-bare-modules-1"></a><a class="docs-heading-anchor-permalink" href="#Default-top-level-definitions-and-bare-modules" title="Permalink"></a></h3><p>Modules automatically contain <code>using Core</code>, <code>using Base</code>, and definitions of the <a href="../base/base.html#eval"><code>eval</code></a> and <a href="../base/base.html#include"><code>include</code></a> functions, which evaluate expressions/files within the global scope of that module.</p><p>If these default definitions are not wanted, modules can be defined using the keyword <a href="../base/base.html#baremodule"><code>baremodule</code></a> instead (note: <code>Core</code> is still imported). In terms of <code>baremodule</code>, a standard <code>module</code> looks like this:</p><pre><code class="nohighlight hljs">baremodule Mod

using Base

eval(x) = Core.eval(Mod, x)
include(p) = Base.include(Mod, p)

...

end</code></pre><p>If even <code>Core</code> is not wanted, a module that imports nothing and defines no names at all can be defined with <code>Module(:YourNameHere, false, false)</code> and code can be evaluated into it with <a href="../base/base.html#Base.@eval"><code>@eval</code></a> or <a href="../devdocs/init.html#Core.eval"><code>Core.eval</code></a>:</p><pre><code class="language-julia-repl hljs">julia&gt; arithmetic = Module(:arithmetic, false, false)
Main.arithmetic

julia&gt; @eval arithmetic add(x, y) = $(+)(x, y)
add (generic function with 1 method)

julia&gt; arithmetic.add(12, 13)
25</code></pre><h3 id="Standard-modules"><a class="docs-heading-anchor" href="#Standard-modules">Standard modules</a><a id="Standard-modules-1"></a><a class="docs-heading-anchor-permalink" href="#Standard-modules" title="Permalink"></a></h3><p>There are three important standard modules:</p><ul><li><a href="../base/base.html#Core"><code>Core</code></a> contains all functionality &quot;built into&quot; the language.</li><li><a href="../base/base.html#Base"><code>Base</code></a> contains basic functionality that is useful in almost all cases.</li><li><a href="../base/base.html#Main"><code>Main</code></a> is the top-level module and the current module, when Julia is started.</li></ul><div class="admonition is-info"><header class="admonition-header">Standard library modules</header><div class="admonition-body"><p>By default Julia ships with some standard library modules. These behave like regular Julia packages except that you don&#39;t need to install them explicitly. For example, if you wanted to perform some unit testing, you could load the <code>Test</code> standard library as follows:</p><pre><code class="language-julia hljs">using Test</code></pre></div></div><h2 id="Submodules-and-relative-paths"><a class="docs-heading-anchor" href="#Submodules-and-relative-paths">Submodules and relative paths</a><a id="Submodules-and-relative-paths-1"></a><a class="docs-heading-anchor-permalink" href="#Submodules-and-relative-paths" title="Permalink"></a></h2><p>Modules can contain <em>submodules</em>, nesting the same syntax <code>module ... end</code>. They can be used to introduce separate namespaces, which can be helpful for organizing complex codebases. Note that each <code>module</code> introduces its own <a href="variables-and-scoping.html#scope-of-variables">scope</a>, so submodules do not automatically “inherit” names from their parent.</p><p>It is recommended that submodules refer to other modules within the enclosing parent module (including the latter) using <em>relative module qualifiers</em> in <code>using</code> and <code>import</code> statements. A relative module qualifier starts with a period (<code>.</code>), which corresponds to the current module, and each successive <code>.</code> leads to the parent of the current module. This should be followed by modules if necessary, and eventually the actual name to access, all separated by <code>.</code>s.</p><p>Consider the following example, where the submodule <code>SubA</code> defines a function, which is then extended in its “sibling” module:</p><pre><code class="language-julia-repl hljs">julia&gt; module ParentModule
       module SubA
       export add_D  # exported interface
       const D = 3
       add_D(x) = x + D
       end
       using .SubA  # brings `add_D` into the namespace
       export add_D # export it from ParentModule too
       module SubB
       import ..SubA: add_D # relative path for a “sibling” module
       struct Infinity end
       add_D(x::Infinity) = x
       end
       end;
</code></pre><p>You may see code in packages, which, in a similar situation, uses</p><pre><code class="language-julia-repl hljs">julia&gt; import .ParentModule.SubA: add_D
</code></pre><p>However, this operates through <a href="code-loading.html#code-loading">code loading</a>, and thus only works if <code>ParentModule</code> is in a package. It is better to use relative paths.</p><p>Note that the order of definitions also matters if you are evaluating values. Consider</p><pre><code class="language-julia hljs">module TestPackage

export x, y

x = 0

module Sub
using ..TestPackage
z = y # ERROR: UndefVarError: `y` not defined in `Main`
end

y = 1

end</code></pre><p>where <code>Sub</code> is trying to use <code>TestPackage.y</code> before it was defined, so it does not have a value.</p><p>For similar reasons, you cannot use a cyclic ordering:</p><pre><code class="language-julia hljs">module A

module B
using ..C # ERROR: UndefVarError: `C` not defined in `Main.A`
end

module C
using ..B
end

end</code></pre><h2 id="Module-initialization-and-precompilation"><a class="docs-heading-anchor" href="#Module-initialization-and-precompilation">Module initialization and precompilation</a><a id="Module-initialization-and-precompilation-1"></a><a class="docs-heading-anchor-permalink" href="#Module-initialization-and-precompilation" title="Permalink"></a></h2><p>Large modules can take several seconds to load because executing all of the statements in a module often involves compiling a large amount of code. Julia creates precompiled caches of the module to reduce this time.</p><p>Precompiled module files (sometimes called &quot;cache files&quot;) are created and used automatically when <code>import</code> or <code>using</code> loads a module.  If the cache file(s) do not yet exist, the module will be compiled and saved for future reuse. You can also manually call <a href="../base/base.html#Base.compilecache"><code>Base.compilecache(Base.identify_package(&quot;modulename&quot;))</code></a> to create these files without loading the module. The resulting cache files will be stored in the <code>compiled</code> subfolder of <code>DEPOT_PATH[1]</code>. If nothing about your system changes, such cache files will be used when you load the module with <code>import</code> or <code>using</code>.</p><p>Precompilation cache files store definitions of modules, types, methods, and constants. They may also store method specializations and the code generated for them, but this typically requires that the developer add explicit <a href="../base/base.html#Base.precompile"><code>precompile</code></a> directives or execute workloads that force compilation during the package build.</p><p>However, if you update the module&#39;s dependencies or change its source code, the module is automatically recompiled upon <code>using</code> or <code>import</code>. Dependencies are modules it imports, the Julia build, files it includes, or explicit dependencies declared by <a href="../base/base.html#Base.include_dependency"><code>include_dependency(path)</code></a> in the module file(s).</p><p>For file dependencies loaded by <code>include</code>, a change is determined by examining whether the file size (<code>fsize</code>) or content (condensed into a hash) is unchanged. For file dependencies loaded by <code>include_dependency</code> a change is determined by examining whether the modification time (<code>mtime</code>) is unchanged, or equal to the modification time truncated to the nearest second (to accommodate systems that can&#39;t copy mtime with sub-second accuracy). It also takes into account whether the path to the file chosen by the search logic in <code>require</code> matches the path that had created the precompile file. It also takes into account the set of dependencies already loaded into the current process and won&#39;t recompile those modules, even if their files change or disappear, in order to avoid creating incompatibilities between the running system and the precompile cache. Finally, it takes account of changes in any <a href="code-loading.html#preferences">compile-time preferences</a>.</p><p>If you know that a module is <em>not</em> safe to precompile (for example, for one of the reasons described below), you should put <code>__precompile__(false)</code> in the module file (typically placed at the top). This will cause <code>Base.compilecache</code> to throw an error, and will cause <code>using</code> / <code>import</code> to load it directly into the current process and skip the precompile and caching. This also thereby prevents the module from being imported by any other precompiled module.</p><p>You may need to be aware of certain behaviors inherent in the creation of incremental shared libraries which may require care when writing your module. For example, external state is not preserved. To accommodate this, explicitly separate any initialization steps that must occur at <em>runtime</em> from steps that can occur at <em>compile time</em>. For this purpose, Julia allows you to define an <code>__init__()</code> function in your module that executes any initialization steps that must occur at runtime. This function will not be called during compilation (<code>--output-*</code>). Effectively, you can assume it will be run exactly once in the lifetime of the code. You may, of course, call it manually if necessary, but the default is to assume this function deals with computing state for the local machine, which does not need to be – or even should not be – captured in the compiled image. It will be called after the module is loaded into a process, including if it is being loaded into an incremental compile (<code>--output-incremental=yes</code>), but not if it is being loaded into a full-compilation process.</p><p>In particular, if you define a <code>function __init__()</code> in a module, then Julia will call <code>__init__()</code> immediately <em>after</em> the module is loaded (e.g., by <code>import</code>, <code>using</code>, or <code>require</code>) at runtime for the <em>first</em> time (i.e., <code>__init__</code> is only called once, and only after all statements in the module have been executed). Because it is called after the module is fully imported, any submodules or other imported modules have their <code>__init__</code> functions called <em>before</em> the <code>__init__</code> of the enclosing module.</p><p>Two typical uses of <code>__init__</code> are calling runtime initialization functions of external C libraries and initializing global constants that involve pointers returned by external libraries.  For example, suppose that we are calling a C library <code>libfoo</code> that requires us to call a <code>foo_init()</code> initialization function at runtime. Suppose that we also want to define a global constant <code>foo_data_ptr</code> that holds the return value of a <code>void *foo_data()</code> function defined by <code>libfoo</code> – this constant must be initialized at runtime (not at compile time) because the pointer address will change from run to run.  You could accomplish this by defining the following <code>__init__</code> function in your module:</p><pre><code class="language-julia hljs">const foo_data_ptr = Ref{Ptr{Cvoid}}(0)
function __init__()
    ccall((:foo_init, :libfoo), Cvoid, ())
    foo_data_ptr[] = ccall((:foo_data, :libfoo), Ptr{Cvoid}, ())
    nothing
end</code></pre><p>Notice that it is perfectly possible to define a global inside a function like <code>__init__</code>; this is one of the advantages of using a dynamic language. But by making it a constant at global scope, we can ensure that the type is known to the compiler and allow it to generate better optimized code. Obviously, any other globals in your module that depends on <code>foo_data_ptr</code> would also have to be initialized in <code>__init__</code>.</p><p>Constants involving most Julia objects that are not produced by <a href="../base/c.html#ccall"><code>ccall</code></a> do not need to be placed in <code>__init__</code>: their definitions can be precompiled and loaded from the cached module image. This includes complicated heap-allocated objects like arrays. However, any routine that returns a raw pointer value must be called at runtime for precompilation to work (<a href="../base/c.html#Core.Ptr"><code>Ptr</code></a> objects will turn into null pointers unless they are hidden inside an <a href="../base/base.html#Base.isbits"><code>isbits</code></a> object). This includes the return values of the Julia functions <a href="../base/c.html#Base.@cfunction"><code>@cfunction</code></a> and <a href="../base/c.html#Base.pointer"><code>pointer</code></a>.</p><p>Dictionary and set types, or in general anything that depends on the output of a <code>hash(key)</code> method, are a trickier case.  In the common case where the keys are numbers, strings, symbols, ranges, <code>Expr</code>, or compositions of these types (via arrays, tuples, sets, pairs, etc.) they are safe to precompile.  However, for a few other key types, such as <code>Function</code> or <code>DataType</code> and generic user-defined types where you haven&#39;t defined a <code>hash</code> method, the fallback <code>hash</code> method depends on the memory address of the object (via its <code>objectid</code>) and hence may change from run to run. If you have one of these key types, or if you aren&#39;t sure, to be safe you can initialize this dictionary from within your <code>__init__</code> function. Alternatively, you can use the <a href="../base/collections.html#Base.IdDict"><code>IdDict</code></a> dictionary type, which is specially handled by precompilation so that it is safe to initialize at compile-time.</p><p>When using precompilation, it is important to keep a clear sense of the distinction between the compilation phase and the execution phase. In this mode, it will often be much more clearly apparent that Julia is a compiler which allows execution of arbitrary Julia code, not a standalone interpreter that also generates compiled code.</p><p>Other known potential failure scenarios include:</p><ol><li><p>Global counters (for example, for attempting to uniquely identify objects). Consider the following code snippet:</p><pre><code class="language-julia hljs">mutable struct UniquedById
    myid::Int
    let counter = 0
        UniquedById() = new(counter += 1)
    end
end</code></pre><p>while the intent of this code was to give every instance a unique id, the counter value is recorded at the end of compilation. All subsequent usages of this incrementally compiled module will start from that same counter value.</p><p>Note that <code>objectid</code> (which works by hashing the memory pointer) has similar issues (see notes on <code>Dict</code> usage below).</p><p>One alternative is to use a macro to capture <a href="../base/base.html#Base.@__MODULE__"><code>@__MODULE__</code></a> and store it alone with the current <code>counter</code> value, however, it may be better to redesign the code to not depend on this global state.</p></li><li><p>Associative collections (such as <code>Dict</code> and <code>Set</code>) need to be re-hashed in <code>__init__</code>. (In the future, a mechanism may be provided to register an initializer function.)</p></li><li><p>Depending on compile-time side-effects persisting through load-time. Example include: modifying arrays or other variables in other Julia modules; maintaining handles to open files or devices; storing pointers to other system resources (including memory);</p></li><li><p>Creating accidental &quot;copies&quot; of global state from another module, by referencing it directly instead of via its lookup path. For example, (in global scope):</p><pre><code class="language-julia hljs">#mystdout = Base.stdout #= will not work correctly, since this will copy Base.stdout into this module =#
# instead use accessor functions:
getstdout() = Base.stdout #= best option =#
# or move the assignment into the runtime:
__init__() = global mystdout = Base.stdout #= also works =#</code></pre></li></ol><p>Several additional restrictions are placed on the operations that can be done while precompiling code to help the user avoid other wrong-behavior situations:</p><ol><li>Calling <a href="../base/base.html#eval"><code>eval</code></a> to cause a side-effect in another module. This will also cause a warning to be emitted when the incremental precompile flag is set.</li><li><code>global const</code> statements from local scope after <code>__init__()</code> has been started (see issue #12010 for plans to add an error for this)</li><li>Replacing a module is a runtime error while doing an incremental precompile.</li></ol><p>A few other points to be aware of:</p><ol><li>No code reload / cache invalidation is performed after changes are made to the source files themselves, (including by <code>Pkg.update</code>), and no cleanup is done after <code>Pkg.rm</code></li><li>The memory sharing behavior of a reshaped array is disregarded by precompilation (each view gets its own copy)</li><li>Expecting the filesystem to be unchanged between compile-time and runtime e.g. <a href="../base/base.html#Base.@__FILE__"><code>@__FILE__</code></a>/<code>source_path()</code> to find resources at runtime, or the BinDeps <code>@checked_lib</code> macro. Sometimes this is unavoidable. However, when possible, it can be good practice to copy resources into the module at compile-time so they won&#39;t need to be found at runtime.</li><li><code>WeakRef</code> objects and finalizers are not currently handled properly by the serializer (this will be fixed in an upcoming release).</li><li>It is usually best to avoid capturing references to instances of internal metadata objects such as <code>Method</code>, <code>MethodInstance</code>, <code>MethodTable</code>, <code>TypeMapLevel</code>, <code>TypeMapEntry</code> and fields of those objects, as this can confuse the serializer and may not lead to the outcome you desire. It is not necessarily an error to do this, but you simply need to be prepared that the system will try to copy some of these and to create a single unique instance of others.</li></ol><p>It is sometimes helpful during module development to turn off incremental precompilation. The command line flag <code>--compiled-modules={yes|no|existing}</code> enables you to toggle module precompilation on and off. When Julia is started with <code>--compiled-modules=no</code> the serialized modules in the compile cache are ignored when loading modules and module dependencies. In some cases, you may want to load existing precompiled modules, but not create new ones. This can be done by starting Julia with <code>--compiled-modules=existing</code>. More fine-grained control is available with <code>--pkgimages={yes|no|existing}</code>, which only affects native-code storage during precompilation. <code>Base.compilecache</code> can still be called manually. The state of this command line flag is passed to <code>Pkg.build</code> to disable automatic precompilation triggering when installing, updating, and explicitly building packages.</p><p>You can also debug some precompilation failures with environment variables. Setting <code>JULIA_VERBOSE_LINKING=true</code> may help resolve failures in linking shared libraries of compiled native code. See the <strong>Developer Documentation</strong> part of the Julia manual, where you will find further details in the section documenting Julia&#39;s internals under &quot;Package Images&quot;.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="interfaces.html">« Interfaces</a><a class="docs-footer-nextpage" href="documentation.html">Documentation »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
