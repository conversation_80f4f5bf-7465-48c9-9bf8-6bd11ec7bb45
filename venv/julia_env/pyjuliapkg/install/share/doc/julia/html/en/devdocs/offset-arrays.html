<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Arrays with custom indices · The Julia Language</title><meta name="title" content="Arrays with custom indices · The Julia Language"/><meta property="og:title" content="Arrays with custom indices · The Julia Language"/><meta property="twitter:title" content="Arrays with custom indices · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox" checked/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox" checked/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="ast.html">Julia ASTs</a></li><li><a class="tocitem" href="types.html">More about types</a></li><li><a class="tocitem" href="object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="functions.html">Julia Functions</a></li><li><a class="tocitem" href="cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="subarrays.html">SubArrays</a></li><li><a class="tocitem" href="isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="sysimg.html">System Image Building</a></li><li><a class="tocitem" href="pkgimg.html">Package Images</a></li><li><a class="tocitem" href="llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="locks.html">Proper maintenance and care of multi-threading locks</a></li><li class="is-active"><a class="tocitem" href="offset-arrays.html">Arrays with custom indices</a><ul class="internal"><li><a class="tocitem" href="#Generalizing-existing-code"><span>Generalizing existing code</span></a></li><li><a class="tocitem" href="#Writing-custom-array-types-with-non-1-indexing"><span>Writing custom array types with non-1 indexing</span></a></li></ul></li><li><a class="tocitem" href="require.html">Module loading</a></li><li><a class="tocitem" href="inference.html">Inference</a></li><li><a class="tocitem" href="ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="build/linux.html">Linux</a></li><li><a class="tocitem" href="build/macos.html">macOS</a></li><li><a class="tocitem" href="build/windows.html">Windows</a></li><li><a class="tocitem" href="build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Developer Documentation</a></li><li><a class="is-disabled">Documentation of Julia&#39;s Internals</a></li><li class="is-active"><a href="offset-arrays.html">Arrays with custom indices</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="offset-arrays.html">Arrays with custom indices</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/devdocs/offset-arrays.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="man-custom-indices"><a class="docs-heading-anchor" href="#man-custom-indices">Arrays with custom indices</a><a id="man-custom-indices-1"></a><a class="docs-heading-anchor-permalink" href="#man-custom-indices" title="Permalink"></a></h1><p>Conventionally, Julia&#39;s arrays are indexed starting at 1, whereas some other languages start numbering at 0, and yet others (e.g., Fortran) allow you to specify arbitrary starting indices.  While there is much merit in picking a standard (i.e., 1 for Julia), there are some algorithms which simplify considerably if you can index outside the range <code>1:size(A,d)</code> (and not just <code>0:size(A,d)-1</code>, either). To facilitate such computations, Julia supports arrays with arbitrary indices.</p><p>The purpose of this page is to address the question, &quot;what do I have to do to support such arrays in my own code?&quot;  First, let&#39;s address the simplest case: if you know that your code will never need to handle arrays with unconventional indexing, hopefully the answer is &quot;nothing.&quot; Old code, on conventional arrays, should function essentially without alteration as long as it was using the exported interfaces of Julia. If you find it more convenient to just force your users to supply traditional arrays where indexing starts at one, you can add</p><pre><code class="language-julia hljs">Base.require_one_based_indexing(arrays...)</code></pre><p>where <code>arrays...</code> is a list of the array objects that you wish to check for anything that violates 1-based indexing.</p><h2 id="Generalizing-existing-code"><a class="docs-heading-anchor" href="#Generalizing-existing-code">Generalizing existing code</a><a id="Generalizing-existing-code-1"></a><a class="docs-heading-anchor-permalink" href="#Generalizing-existing-code" title="Permalink"></a></h2><p>As an overview, the steps are:</p><ul><li>replace many uses of <code>size</code> with <code>axes</code></li><li>replace <code>1:length(A)</code> with <code>eachindex(A)</code>, or in some cases <code>LinearIndices(A)</code></li><li>replace explicit allocations like <code>Array{Int}(undef, size(B))</code> with <code>similar(Array{Int}, axes(B))</code></li></ul><p>These are described in more detail below.</p><h3 id="Things-to-watch-out-for"><a class="docs-heading-anchor" href="#Things-to-watch-out-for">Things to watch out for</a><a id="Things-to-watch-out-for-1"></a><a class="docs-heading-anchor-permalink" href="#Things-to-watch-out-for" title="Permalink"></a></h3><p>Because unconventional indexing breaks many people&#39;s assumptions that all arrays start indexing with 1, there is always the chance that using such arrays will trigger errors. The most frustrating bugs would be incorrect results or segfaults (total crashes of Julia). For example, consider the following function:</p><pre><code class="language-julia hljs">function mycopy!(dest::AbstractVector, src::AbstractVector)
    length(dest) == length(src) || throw(DimensionMismatch(&quot;vectors must match&quot;))
    # OK, now we&#39;re safe to use @inbounds, right? (not anymore!)
    for i = 1:length(src)
        @inbounds dest[i] = src[i]
    end
    dest
end</code></pre><p>This code implicitly assumes that vectors are indexed from 1; if <code>dest</code> starts at a different index than <code>src</code>, there is a chance that this code would trigger a segfault. (If you do get segfaults, to help locate the cause try running julia with the option <code>--check-bounds=yes</code>.)</p><h3 id="Using-axes-for-bounds-checks-and-loop-iteration"><a class="docs-heading-anchor" href="#Using-axes-for-bounds-checks-and-loop-iteration">Using <code>axes</code> for bounds checks and loop iteration</a><a id="Using-axes-for-bounds-checks-and-loop-iteration-1"></a><a class="docs-heading-anchor-permalink" href="#Using-axes-for-bounds-checks-and-loop-iteration" title="Permalink"></a></h3><p><code>axes(A)</code> (reminiscent of <code>size(A)</code>) returns a tuple of <code>AbstractUnitRange{&lt;:Integer}</code> objects, specifying the range of valid indices along each dimension of <code>A</code>.  When <code>A</code> has unconventional indexing, the ranges may not start at 1.  If you just want the range for a particular dimension <code>d</code>, there is <code>axes(A, d)</code>.</p><p>Base implements a custom range type, <code>OneTo</code>, where <code>OneTo(n)</code> means the same thing as <code>1:n</code> but in a form that guarantees (via the type system) that the lower index is 1. For any new <a href="../base/arrays.html#Core.AbstractArray"><code>AbstractArray</code></a> type, this is the default returned by <code>axes</code>, and it indicates that this array type uses &quot;conventional&quot; 1-based indexing.</p><p>For bounds checking, note that there are dedicated functions <code>checkbounds</code> and <code>checkindex</code> which can sometimes simplify such tests.</p><h3 id="Linear-indexing-(LinearIndices)"><a class="docs-heading-anchor" href="#Linear-indexing-(LinearIndices)">Linear indexing (<code>LinearIndices</code>)</a><a id="Linear-indexing-(LinearIndices)-1"></a><a class="docs-heading-anchor-permalink" href="#Linear-indexing-(LinearIndices)" title="Permalink"></a></h3><p>Some algorithms are most conveniently (or efficiently) written in terms of a single linear index, <code>A[i]</code> even if <code>A</code> is multi-dimensional. Regardless of the array&#39;s native indices, linear indices always range from <code>1:length(A)</code>. However, this raises an ambiguity for one-dimensional arrays (a.k.a., <a href="../base/arrays.html#Base.AbstractVector"><code>AbstractVector</code></a>): does <code>v[i]</code> mean linear indexing , or Cartesian indexing with the array&#39;s native indices?</p><p>For this reason, your best option may be to iterate over the array with <code>eachindex(A)</code>, or, if you require the indices to be sequential integers, to get the index range by calling <code>LinearIndices(A)</code>. This will return <code>axes(A, 1)</code> if A is an AbstractVector, and the equivalent of <code>1:length(A)</code> otherwise.</p><p>By this definition, 1-dimensional arrays always use Cartesian indexing with the array&#39;s native indices. To help enforce this, it&#39;s worth noting that the index conversion functions will throw an error if shape indicates a 1-dimensional array with unconventional indexing (i.e., is a <code>Tuple{UnitRange}</code> rather than a tuple of <code>OneTo</code>). For arrays with conventional indexing, these functions continue to work the same as always.</p><p>Using <code>axes</code> and <code>LinearIndices</code>, here is one way you could rewrite <code>mycopy!</code>:</p><pre><code class="language-julia hljs">function mycopy!(dest::AbstractVector, src::AbstractVector)
    axes(dest) == axes(src) || throw(DimensionMismatch(&quot;vectors must match&quot;))
    for i in LinearIndices(src)
        @inbounds dest[i] = src[i]
    end
    dest
end</code></pre><h3 id="Allocating-storage-using-generalizations-of-similar"><a class="docs-heading-anchor" href="#Allocating-storage-using-generalizations-of-similar">Allocating storage using generalizations of <code>similar</code></a><a id="Allocating-storage-using-generalizations-of-similar-1"></a><a class="docs-heading-anchor-permalink" href="#Allocating-storage-using-generalizations-of-similar" title="Permalink"></a></h3><p>Storage is often allocated with <code>Array{Int}(undef, dims)</code> or <code>similar(A, args...)</code>. When the result needs to match the indices of some other array, this may not always suffice. The generic replacement for such patterns is to use <code>similar(storagetype, shape)</code>.  <code>storagetype</code> indicates the kind of underlying &quot;conventional&quot; behavior you&#39;d like, e.g., <code>Array{Int}</code> or <code>BitArray</code> or even <code>dims-&gt;zeros(Float32, dims)</code> (which would allocate an all-zeros array). <code>shape</code> is a tuple of <a href="../base/numbers.html#Core.Integer"><code>Integer</code></a> or <code>AbstractUnitRange</code> values, specifying the indices that you want the result to use. Note that a convenient way of producing an all-zeros array that matches the indices of A is simply <code>zeros(A)</code>.</p><p>Let&#39;s walk through a couple of explicit examples. First, if <code>A</code> has conventional indices, then <code>similar(Array{Int}, axes(A))</code> would end up calling <code>Array{Int}(undef, size(A))</code>, and thus return an array.  If <code>A</code> is an <code>AbstractArray</code> type with unconventional indexing, then <code>similar(Array{Int}, axes(A))</code> should return something that &quot;behaves like&quot; an <code>Array{Int}</code> but with a shape (including indices) that matches <code>A</code>.  (The most obvious implementation is to allocate an <code>Array{Int}(undef, size(A))</code> and then &quot;wrap&quot; it in a type that shifts the indices.)</p><p>Note also that <code>similar(Array{Int}, (axes(A, 2),))</code> would allocate an <code>AbstractVector{Int}</code> (i.e., 1-dimensional array) that matches the indices of the columns of <code>A</code>.</p><h2 id="Writing-custom-array-types-with-non-1-indexing"><a class="docs-heading-anchor" href="#Writing-custom-array-types-with-non-1-indexing">Writing custom array types with non-1 indexing</a><a id="Writing-custom-array-types-with-non-1-indexing-1"></a><a class="docs-heading-anchor-permalink" href="#Writing-custom-array-types-with-non-1-indexing" title="Permalink"></a></h2><p>Most of the methods you&#39;ll need to define are standard for any <code>AbstractArray</code> type, see <a href="../manual/interfaces.html#man-interface-array">Abstract Arrays</a>. This page focuses on the steps needed to define unconventional indexing.</p><h3 id="Custom-AbstractUnitRange-types"><a class="docs-heading-anchor" href="#Custom-AbstractUnitRange-types">Custom <code>AbstractUnitRange</code> types</a><a id="Custom-AbstractUnitRange-types-1"></a><a class="docs-heading-anchor-permalink" href="#Custom-AbstractUnitRange-types" title="Permalink"></a></h3><p>If you&#39;re writing a non-1 indexed array type, you will want to specialize <code>axes</code> so it returns a <code>UnitRange</code>, or (perhaps better) a custom <code>AbstractUnitRange</code>.  The advantage of a custom type is that it &quot;signals&quot; the allocation type for functions like <code>similar</code>. If we&#39;re writing an array type for which indexing will start at 0, we likely want to begin by creating a new <code>AbstractUnitRange</code>, <code>ZeroRange</code>, where <code>ZeroRange(n)</code> is equivalent to <code>0:n-1</code>.</p><p>In general, you should probably <em>not</em> export <code>ZeroRange</code> from your package: there may be other packages that implement their own <code>ZeroRange</code>, and having multiple distinct <code>ZeroRange</code> types is (perhaps counterintuitively) an advantage: <code>ModuleA.ZeroRange</code> indicates that <code>similar</code> should create a <code>ModuleA.ZeroArray</code>, whereas <code>ModuleB.ZeroRange</code> indicates a <code>ModuleB.ZeroArray</code> type.  This design allows peaceful coexistence among many different custom array types.</p><p>Note that the Julia package <a href="https://github.com/JuliaArrays/CustomUnitRanges.jl">CustomUnitRanges.jl</a> can sometimes be used to avoid the need to write your own <code>ZeroRange</code> type.</p><h3 id="Specializing-axes"><a class="docs-heading-anchor" href="#Specializing-axes">Specializing <code>axes</code></a><a id="Specializing-axes-1"></a><a class="docs-heading-anchor-permalink" href="#Specializing-axes" title="Permalink"></a></h3><p>Once you have your <code>AbstractUnitRange</code> type, then specialize <code>axes</code>:</p><pre><code class="language-julia hljs">Base.axes(A::ZeroArray) = map(n-&gt;ZeroRange(n), A.size)</code></pre><p>where here we imagine that <code>ZeroArray</code> has a field called <code>size</code> (there would be other ways to implement this).</p><p>In some cases, the fallback definition for <code>axes(A, d)</code>:</p><pre><code class="language-julia hljs">axes(A::AbstractArray{T,N}, d) where {T,N} = d &lt;= N ? axes(A)[d] : OneTo(1)</code></pre><p>may not be what you want: you may need to specialize it to return something other than <code>OneTo(1)</code> when <code>d &gt; ndims(A)</code>.  Likewise, in <code>Base</code> there is a dedicated function <code>axes1</code> which is equivalent to <code>axes(A, 1)</code> but which avoids checking (at runtime) whether <code>ndims(A) &gt; 0</code>. (This is purely a performance optimization.)  It is defined as:</p><pre><code class="language-julia hljs">axes1(A::AbstractArray{T,0}) where {T} = OneTo(1)
axes1(A::AbstractArray) = axes(A)[1]</code></pre><p>If the first of these (the zero-dimensional case) is problematic for your custom array type, be sure to specialize it appropriately.</p><h3 id="Specializing-similar"><a class="docs-heading-anchor" href="#Specializing-similar">Specializing <code>similar</code></a><a id="Specializing-similar-1"></a><a class="docs-heading-anchor-permalink" href="#Specializing-similar" title="Permalink"></a></h3><p>Given your custom <code>ZeroRange</code> type, then you should also add the following two specializations for <code>similar</code>:</p><pre><code class="language-julia hljs">function Base.similar(A::AbstractArray, T::Type, shape::Tuple{ZeroRange,Vararg{ZeroRange}})
    # body
end

function Base.similar(f::Union{Function,DataType}, shape::Tuple{ZeroRange,Vararg{ZeroRange}})
    # body
end</code></pre><p>Both of these should allocate your custom array type.</p><h3 id="Specializing-reshape"><a class="docs-heading-anchor" href="#Specializing-reshape">Specializing <code>reshape</code></a><a id="Specializing-reshape-1"></a><a class="docs-heading-anchor-permalink" href="#Specializing-reshape" title="Permalink"></a></h3><p>Optionally, define a method</p><pre><code class="nohighlight hljs">Base.reshape(A::AbstractArray, shape::Tuple{ZeroRange,Vararg{ZeroRange}}) = ...</code></pre><p>and you can <code>reshape</code> an array so that the result has custom indices.</p><h3 id="For-objects-that-mimic-AbstractArray-but-are-not-subtypes"><a class="docs-heading-anchor" href="#For-objects-that-mimic-AbstractArray-but-are-not-subtypes">For objects that mimic AbstractArray but are not subtypes</a><a id="For-objects-that-mimic-AbstractArray-but-are-not-subtypes-1"></a><a class="docs-heading-anchor-permalink" href="#For-objects-that-mimic-AbstractArray-but-are-not-subtypes" title="Permalink"></a></h3><p><code>has_offset_axes</code> depends on having <code>axes</code> defined for the objects you call it on. If there is some reason you don&#39;t have an <code>axes</code> method defined for your object, consider defining a method</p><pre><code class="language-julia hljs">Base.has_offset_axes(obj::MyNon1IndexedArraylikeObject) = true</code></pre><p>This will allow code that assumes 1-based indexing to detect a problem and throw a helpful error, rather than returning incorrect results or segfaulting julia.</p><h3 id="Catching-errors"><a class="docs-heading-anchor" href="#Catching-errors">Catching errors</a><a id="Catching-errors-1"></a><a class="docs-heading-anchor-permalink" href="#Catching-errors" title="Permalink"></a></h3><p>If your new array type triggers errors in other code, one helpful debugging step can be to comment out <code>@boundscheck</code> in your <code>getindex</code> and <code>setindex!</code> implementation. This will ensure that every element access checks bounds. Or, restart julia with <code>--check-bounds=yes</code>.</p><p>In some cases it may also be helpful to temporarily disable <code>size</code> and <code>length</code> for your new array type, since code that makes incorrect assumptions frequently uses these functions.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="locks.html">« Proper maintenance and care of multi-threading locks</a><a class="docs-footer-nextpage" href="require.html">Module loading »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
