<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Tasks · The Julia Language</title><meta name="title" content="Tasks · The Julia Language"/><meta property="og:title" content="Tasks · The Julia Language"/><meta property="twitter:title" content="Tasks · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox" checked/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="base.html">Essentials</a></li><li><a class="tocitem" href="collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="math.html">Mathematics</a></li><li><a class="tocitem" href="numbers.html">Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="arrays.html">Arrays</a></li><li class="is-active"><a class="tocitem" href="parallel.html">Tasks</a><ul class="internal"><li><a class="tocitem" href="#Scheduling"><span>Scheduling</span></a></li><li><a class="tocitem" href="#lib-task-sync"><span>Synchronization</span></a></li><li><a class="tocitem" href="#Channels"><span>Channels</span></a></li><li><a class="tocitem" href="#low-level-schedule-wait"><span>Low-level synchronization using <code>schedule</code> and <code>wait</code></span></a></li></ul></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="constants.html">Constants</a></li><li><a class="tocitem" href="file.html">Filesystem</a></li><li><a class="tocitem" href="io-network.html">I/O and Network</a></li><li><a class="tocitem" href="punctuation.html">Punctuation</a></li><li><a class="tocitem" href="sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="c.html">C Interface</a></li><li><a class="tocitem" href="libc.html">C Standard Library</a></li><li><a class="tocitem" href="stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Base</a></li><li class="is-active"><a href="parallel.html">Tasks</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="parallel.html">Tasks</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/base/parallel.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="Tasks"><a class="docs-heading-anchor" href="#Tasks">Tasks</a><a id="Tasks-1"></a><a class="docs-heading-anchor-permalink" href="#Tasks" title="Permalink"></a></h1><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Core.Task" href="#Core.Task"><code>Core.Task</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Task(func)</code></pre><p>Create a <code>Task</code> (i.e. coroutine) to execute the given function <code>func</code> (which must be callable with no arguments). The task exits when this function returns. The task will run in the &quot;world age&quot; from the parent at construction when <a href="parallel.html#Base.schedule"><code>schedule</code></a>d.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>By default tasks will have the sticky bit set to true <code>t.sticky</code>. This models the historic default for <a href="parallel.html#Base.@async"><code>@async</code></a>. Sticky tasks can only be run on the worker thread they are first scheduled on, and when scheduled will make the task that they were scheduled from sticky. To obtain the behavior of <a href="multi-threading.html#Base.Threads.@spawn"><code>Threads.@spawn</code></a> set the sticky bit manually to <code>false</code>.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a() = sum(i for i in 1:1000);

julia&gt; b = Task(a);</code></pre><p>In this example, <code>b</code> is a runnable <code>Task</code> that hasn&#39;t started yet.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/docs/basedocs.jl#L1786-L1808">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@task" href="#Base.@task"><code>Base.@task</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@task</code></pre><p>Wrap an expression in a <a href="parallel.html#Core.Task"><code>Task</code></a> without executing it, and return the <a href="parallel.html#Core.Task"><code>Task</code></a>. This only creates a task, and does not run it.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>By default tasks will have the sticky bit set to true <code>t.sticky</code>. This models the historic default for <a href="parallel.html#Base.@async"><code>@async</code></a>. Sticky tasks can only be run on the worker thread they are first scheduled on, and when scheduled will make the task that they were scheduled from sticky. To obtain the behavior of <a href="multi-threading.html#Base.Threads.@spawn"><code>Threads.@spawn</code></a> set the sticky bit manually to <code>false</code>.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a1() = sum(i for i in 1:1000);

julia&gt; b = @task a1();

julia&gt; istaskstarted(b)
false

julia&gt; schedule(b);

julia&gt; yield();

julia&gt; istaskdone(b)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/task.jl#L112-L141">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@async" href="#Base.@async"><code>Base.@async</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@async</code></pre><p>Wrap an expression in a <a href="parallel.html#Core.Task"><code>Task</code></a> and add it to the local machine&#39;s scheduler queue.</p><p>Values can be interpolated into <code>@async</code> via <code>$</code>, which copies the value directly into the constructed underlying closure. This allows you to insert the <em>value</em> of a variable, isolating the asynchronous code from changes to the variable&#39;s value in the current task.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>It is strongly encouraged to favor <code>Threads.@spawn</code> over <code>@async</code> always <strong>even when no parallelism is required</strong> especially in publicly distributed libraries.  This is because a use of <code>@async</code> disables the migration of the <em>parent</em> task across worker threads in the current implementation of Julia.  Thus, seemingly innocent use of <code>@async</code> in a library function can have a large impact on the performance of very different parts of user applications.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.4</header><div class="admonition-body"><p>Interpolating values via <code>$</code> is available as of Julia 1.4.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/task.jl#L507-L526">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.asyncmap" href="#Base.asyncmap"><code>Base.asyncmap</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">asyncmap(f, c...; ntasks=0, batch_size=nothing)</code></pre><p>Uses multiple concurrent tasks to map <code>f</code> over a collection (or multiple equal length collections). For multiple collection arguments, <code>f</code> is applied elementwise.</p><p><code>ntasks</code> specifies the number of tasks to run concurrently. Depending on the length of the collections, if <code>ntasks</code> is unspecified, up to 100 tasks will be used for concurrent mapping.</p><p><code>ntasks</code> can also be specified as a zero-arg function. In this case, the number of tasks to run in parallel is checked before processing every element and a new task started if the value of <code>ntasks_func</code> is greater than the current number of tasks.</p><p>If <code>batch_size</code> is specified, the collection is processed in batch mode. <code>f</code> must then be a function that must accept a <code>Vector</code> of argument tuples and must return a vector of results. The input vector will have a length of <code>batch_size</code> or less.</p><p>The following examples highlight execution in different tasks by returning the <code>objectid</code> of the tasks in which the mapping function is executed.</p><p>First, with <code>ntasks</code> undefined, each element is processed in a different task.</p><pre><code class="nohighlight hljs">julia&gt; tskoid() = objectid(current_task());

julia&gt; asyncmap(x-&gt;tskoid(), 1:5)
5-element Array{UInt64,1}:
 0x6e15e66c75c75853
 0x440f8819a1baa682
 0x9fb3eeadd0c83985
 0xebd3e35fe90d4050
 0x29efc93edce2b961

julia&gt; length(unique(asyncmap(x-&gt;tskoid(), 1:5)))
5</code></pre><p>With <code>ntasks=2</code> all elements are processed in 2 tasks.</p><pre><code class="nohighlight hljs">julia&gt; asyncmap(x-&gt;tskoid(), 1:5; ntasks=2)
5-element Array{UInt64,1}:
 0x027ab1680df7ae94
 0xa23d2f80cd7cf157
 0x027ab1680df7ae94
 0xa23d2f80cd7cf157
 0x027ab1680df7ae94

julia&gt; length(unique(asyncmap(x-&gt;tskoid(), 1:5; ntasks=2)))
2</code></pre><p>With <code>batch_size</code> defined, the mapping function needs to be changed to accept an array of argument tuples and return an array of results. <code>map</code> is used in the modified mapping function to achieve this.</p><pre><code class="nohighlight hljs">julia&gt; batch_func(input) = map(x-&gt;string(&quot;args_tuple: &quot;, x, &quot;, element_val: &quot;, x[1], &quot;, task: &quot;, tskoid()), input)
batch_func (generic function with 1 method)

julia&gt; asyncmap(batch_func, 1:5; ntasks=2, batch_size=2)
5-element Array{String,1}:
 &quot;args_tuple: (1,), element_val: 1, task: 9118321258196414413&quot;
 &quot;args_tuple: (2,), element_val: 2, task: 4904288162898683522&quot;
 &quot;args_tuple: (3,), element_val: 3, task: 9118321258196414413&quot;
 &quot;args_tuple: (4,), element_val: 4, task: 4904288162898683522&quot;
 &quot;args_tuple: (5,), element_val: 5, task: 9118321258196414413&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/asyncmap.jl#L5-L73">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.asyncmap!" href="#Base.asyncmap!"><code>Base.asyncmap!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">asyncmap!(f, results, c...; ntasks=0, batch_size=nothing)</code></pre><p>Like <a href="parallel.html#Base.asyncmap"><code>asyncmap</code></a>, but stores output in <code>results</code> rather than returning a collection.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>Behavior can be unexpected when any mutated argument shares memory with any other argument.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/asyncmap.jl#L392-L399">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.current_task" href="#Base.current_task"><code>Base.current_task</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">current_task()</code></pre><p>Get the currently running <a href="parallel.html#Core.Task"><code>Task</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/task.jl#L147-L151">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.istaskdone" href="#Base.istaskdone"><code>Base.istaskdone</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">istaskdone(t::Task) -&gt; Bool</code></pre><p>Determine whether a task has exited.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a2() = sum(i for i in 1:1000);

julia&gt; b = Task(a2);

julia&gt; istaskdone(b)
false

julia&gt; schedule(b);

julia&gt; yield();

julia&gt; istaskdone(b)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/task.jl#L204-L225">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.istaskstarted" href="#Base.istaskstarted"><code>Base.istaskstarted</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">istaskstarted(t::Task) -&gt; Bool</code></pre><p>Determine whether a task has started executing.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a3() = sum(i for i in 1:1000);

julia&gt; b = Task(a3);

julia&gt; istaskstarted(b)
false</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/task.jl#L228-L242">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.istaskfailed" href="#Base.istaskfailed"><code>Base.istaskfailed</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">istaskfailed(t::Task) -&gt; Bool</code></pre><p>Determine whether a task has exited because an exception was thrown.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a4() = error(&quot;task failed&quot;);

julia&gt; b = Task(a4);

julia&gt; istaskfailed(b)
false

julia&gt; schedule(b);

julia&gt; yield();

julia&gt; istaskfailed(b)
true</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p>This function requires at least Julia 1.3.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/task.jl#L245-L269">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.task_local_storage-Tuple{Any}" href="#Base.task_local_storage-Tuple{Any}"><code>Base.task_local_storage</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">task_local_storage(key)</code></pre><p>Look up the value of a key in the current task&#39;s task-local storage.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/task.jl#L288-L292">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.task_local_storage-Tuple{Any, Any}" href="#Base.task_local_storage-Tuple{Any, Any}"><code>Base.task_local_storage</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">task_local_storage(key, value)</code></pre><p>Assign a value to a key in the current task&#39;s task-local storage.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/task.jl#L295-L299">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.task_local_storage-Tuple{Function, Any, Any}" href="#Base.task_local_storage-Tuple{Function, Any, Any}"><code>Base.task_local_storage</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">task_local_storage(body, key, value)</code></pre><p>Call the function <code>body</code> with a modified task-local storage, in which <code>value</code> is assigned to <code>key</code>; the previous value of <code>key</code>, or lack thereof, is restored afterwards. Useful for emulating dynamic scoping.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/task.jl#L302-L308">source</a></section></article><h2 id="Scheduling"><a class="docs-heading-anchor" href="#Scheduling">Scheduling</a><a id="Scheduling-1"></a><a class="docs-heading-anchor-permalink" href="#Scheduling" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.yield" href="#Base.yield"><code>Base.yield</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">yield()</code></pre><p>Switch to the scheduler to allow another scheduled task to run. A task that calls this function is still runnable, and will be restarted immediately if there are no other runnable tasks.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/task.jl#L891-L897">source</a></section><section><div><pre><code class="language-julia hljs">yield(t::Task, arg = nothing)</code></pre><p>A fast, unfair-scheduling version of <code>schedule(t, arg); yield()</code> which immediately yields to <code>t</code> before calling the scheduler.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/task.jl#L911-L916">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.yieldto" href="#Base.yieldto"><code>Base.yieldto</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">yieldto(t::Task, arg = nothing)</code></pre><p>Switch to the given task. The first time a task is switched to, the task&#39;s function is called with no arguments. On subsequent switches, <code>arg</code> is returned from the task&#39;s last call to <code>yieldto</code>. This is a low-level call that only switches tasks, not considering states or scheduling in any way. Its use is discouraged.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/task.jl#L925-L932">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.sleep" href="#Base.sleep"><code>Base.sleep</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">sleep(seconds)</code></pre><p>Block the current task for a specified number of seconds. The minimum sleep time is 1 millisecond or input of <code>0.001</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/asyncevent.jl#L264-L269">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.schedule" href="#Base.schedule"><code>Base.schedule</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">schedule(t::Task, [val]; error=false)</code></pre><p>Add a <a href="parallel.html#Core.Task"><code>Task</code></a> to the scheduler&#39;s queue. This causes the task to run constantly when the system is otherwise idle, unless the task performs a blocking operation such as <a href="parallel.html#Base.wait"><code>wait</code></a>.</p><p>If a second argument <code>val</code> is provided, it will be passed to the task (via the return value of <a href="parallel.html#Base.yieldto"><code>yieldto</code></a>) when it runs again. If <code>error</code> is <code>true</code>, the value is raised as an exception in the woken task.</p><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>It is incorrect to use <code>schedule</code> on an arbitrary <code>Task</code> that has already been started. See <a href="parallel.html#low-level-schedule-wait">the API reference</a> for more information.</p></div></div><div class="admonition is-warning"><header class="admonition-header">Warning</header><div class="admonition-body"><p>By default tasks will have the sticky bit set to true <code>t.sticky</code>. This models the historic default for <a href="parallel.html#Base.@async"><code>@async</code></a>. Sticky tasks can only be run on the worker thread they are first scheduled on, and when scheduled will make the task that they were scheduled from sticky. To obtain the behavior of <a href="multi-threading.html#Base.Threads.@spawn"><code>Threads.@spawn</code></a> set the sticky bit manually to <code>false</code>.</p></div></div><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; a5() = sum(i for i in 1:1000);

julia&gt; b = Task(a5);

julia&gt; istaskstarted(b)
false

julia&gt; schedule(b);

julia&gt; yield();

julia&gt; istaskstarted(b)
true

julia&gt; istaskdone(b)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/task.jl#L835-L875">source</a></section></article><h2 id="lib-task-sync"><a class="docs-heading-anchor" href="#lib-task-sync">Synchronization</a><a id="lib-task-sync-1"></a><a class="docs-heading-anchor-permalink" href="#lib-task-sync" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.errormonitor" href="#Base.errormonitor"><code>Base.errormonitor</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">errormonitor(t::Task)</code></pre><p>Print an error log to <code>stderr</code> if task <code>t</code> fails.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Base._wait(errormonitor(Threads.@spawn error(&quot;task failed&quot;)))
Unhandled Task ERROR: task failed
Stacktrace:
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/task.jl#L579-L591">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@sync" href="#Base.@sync"><code>Base.@sync</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@sync</code></pre><p>Wait until all lexically-enclosed uses of <a href="parallel.html#Base.@async"><code>@async</code></a>, <a href="multi-threading.html#Base.Threads.@spawn"><code>@spawn</code></a>, <code>Distributed.@spawnat</code> and <code>Distributed.@distributed</code> are complete. All exceptions thrown by enclosed async operations are collected and thrown as a <a href="base.html#Base.CompositeException"><code>CompositeException</code></a>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; Threads.nthreads()
4

julia&gt; @sync begin
           Threads.@spawn println(&quot;Thread-id $(Threads.threadid()), task 1&quot;)
           Threads.@spawn println(&quot;Thread-id $(Threads.threadid()), task 2&quot;)
       end;
Thread-id 3, task 1
Thread-id 1, task 2</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/task.jl#L473-L493">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.wait" href="#Base.wait"><code>Base.wait</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>Special note for <a href="parallel.html#Base.Threads.Condition"><code>Threads.Condition</code></a>:</p><p>The caller must be holding the <a href="parallel.html#Base.lock"><code>lock</code></a> that owns a <code>Threads.Condition</code> before calling this method. The calling task will be blocked until some other task wakes it, usually by calling <a href="parallel.html#Base.notify"><code>notify</code></a> on the same <code>Threads.Condition</code> object. The lock will be atomically released when blocking (even if it was locked recursively), and will be reacquired before returning.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/lock.jl#L381-L389">source</a></section><section><div><pre><code class="language-julia hljs">wait(r::Future)</code></pre><p>Wait for a value to become available for the specified <a href="../stdlib/Future.html#Future"><code>Future</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/remotecall.jl#L581-L585">source</a></section><section><div><pre><code class="language-julia hljs">wait(r::RemoteChannel, args...)</code></pre><p>Wait for a value to become available on the specified <a href="../stdlib/Distributed.html#Distributed.RemoteChannel"><code>RemoteChannel</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/Distributed.jl/blob/6c7cdb5860fa5cb9ca191ce9c52a3d25a9ab3781/src/remotecall.jl#L588-L592">source</a></section><section><div><pre><code class="language-julia hljs">wait([x])</code></pre><p>Block the current task until some event occurs, depending on the type of the argument:</p><ul><li><a href="parallel.html#Base.Channel"><code>Channel</code></a>: Wait for a value to be appended to the channel.</li><li><a href="parallel.html#Base.Condition"><code>Condition</code></a>: Wait for <a href="parallel.html#Base.notify"><code>notify</code></a> on a condition and return the <code>val</code> parameter passed to <code>notify</code>. Waiting on a condition additionally allows passing <code>first=true</code> which results in the waiter being put <em>first</em> in line to wake up on <code>notify</code> instead of the usual first-in-first-out behavior.</li><li><code>Process</code>: Wait for a process or process chain to exit. The <code>exitcode</code> field of a process can be used to determine success or failure.</li><li><a href="parallel.html#Core.Task"><code>Task</code></a>: Wait for a <code>Task</code> to finish. If the task fails with an exception, a <code>TaskFailedException</code> (which wraps the failed task) is thrown.</li><li><a href="file.html#Base.Libc.RawFD"><code>RawFD</code></a>: Wait for changes on a file descriptor (see the <code>FileWatching</code> package).</li></ul><p>If no argument is passed, the task blocks for an undefined period. A task can only be restarted by an explicit call to <a href="parallel.html#Base.schedule"><code>schedule</code></a> or <a href="parallel.html#Base.yieldto"><code>yieldto</code></a>.</p><p>Often <code>wait</code> is called within a <code>while</code> loop to ensure a waited-for condition is met before proceeding.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/condition.jl#L103-L124">source</a></section><section><div><pre><code class="language-julia hljs">wait(c::Channel)</code></pre><p>Blocks until the <code>Channel</code> <a href="parallel.html#Base.isready-Tuple{Channel}"><code>isready</code></a>.</p><pre><code class="language-julia-repl hljs">julia&gt; c = Channel(1);

julia&gt; isready(c)
false

julia&gt; task = Task(() -&gt; wait(c));

julia&gt; schedule(task);

julia&gt; istaskdone(task)  # task is blocked because channel is not ready
false

julia&gt; put!(c, 1);

julia&gt; istaskdone(task)  # task is now unblocked
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/channels.jl#L567-L590">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.fetch-Tuple{Task}" href="#Base.fetch-Tuple{Task}"><code>Base.fetch</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">fetch(t::Task)</code></pre><p>Wait for a <a href="parallel.html#Core.Task"><code>Task</code></a> to finish, then return its result value. If the task fails with an exception, a <a href="base.html#Base.TaskFailedException"><code>TaskFailedException</code></a> (which wraps the failed task) is thrown.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/task.jl#L382-L388">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.fetch-Tuple{Any}" href="#Base.fetch-Tuple{Any}"><code>Base.fetch</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">fetch(x::Any)</code></pre><p>Return <code>x</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/task.jl#L375-L379">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.timedwait" href="#Base.timedwait"><code>Base.timedwait</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">timedwait(testcb, timeout::Real; pollint::Real=0.1)</code></pre><p>Wait until <code>testcb()</code> returns <code>true</code> or <code>timeout</code> seconds have passed, whichever is earlier. The test function is polled every <code>pollint</code> seconds. The minimum value for <code>pollint</code> is 0.001 seconds, that is, 1 millisecond.</p><p>Return <code>:ok</code> or <code>:timed_out</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; cb() = (sleep(5); return);

julia&gt; t = @async cb();

julia&gt; timedwait(()-&gt;istaskdone(t), 1)
:timed_out

julia&gt; timedwait(()-&gt;istaskdone(t), 6.5)
:ok</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/asyncevent.jl#L334-L355">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Condition" href="#Base.Condition"><code>Base.Condition</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Condition()</code></pre><p>Create an edge-triggered event source that tasks can wait for. Tasks that call <a href="parallel.html#Base.wait"><code>wait</code></a> on a <code>Condition</code> are suspended and queued. Tasks are woken up when <a href="parallel.html#Base.notify"><code>notify</code></a> is later called on the <code>Condition</code>. Waiting on a condition can return a value or raise an error if the optional arguments of <a href="parallel.html#Base.notify"><code>notify</code></a> are used. Edge triggering means that only tasks waiting at the time <a href="parallel.html#Base.notify"><code>notify</code></a> is called can be woken up. For level-triggered notifications, you must keep extra state to keep track of whether a notification has happened. The <a href="parallel.html#Base.Channel"><code>Channel</code></a> and <a href="parallel.html#Base.Event"><code>Threads.Event</code></a> types do this, and can be used for level-triggered events.</p><p>This object is NOT thread-safe. See <a href="parallel.html#Base.Threads.Condition"><code>Threads.Condition</code></a> for a thread-safe version.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/condition.jl#L173-L185">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Threads.Condition" href="#Base.Threads.Condition"><code>Base.Threads.Condition</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Threads.Condition([lock])</code></pre><p>A thread-safe version of <a href="parallel.html#Base.Condition"><code>Base.Condition</code></a>.</p><p>To call <a href="parallel.html#Base.wait"><code>wait</code></a> or <a href="parallel.html#Base.notify"><code>notify</code></a> on a <code>Threads.Condition</code>, you must first call <a href="parallel.html#Base.lock"><code>lock</code></a> on it. When <code>wait</code> is called, the lock is atomically released during blocking, and will be reacquired before <code>wait</code> returns. Therefore idiomatic use of a <code>Threads.Condition</code> <code>c</code> looks like the following:</p><pre><code class="nohighlight hljs">lock(c)
try
    while !thing_we_are_waiting_for
        wait(c)
    end
finally
    unlock(c)
end</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.2</header><div class="admonition-body"><p>This functionality requires at least Julia 1.2.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/lock.jl#L355-L378">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Event" href="#Base.Event"><code>Base.Event</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Event([autoreset=false])</code></pre><p>Create a level-triggered event source. Tasks that call <a href="parallel.html#Base.wait"><code>wait</code></a> on an <code>Event</code> are suspended and queued until <a href="parallel.html#Base.notify"><code>notify</code></a> is called on the <code>Event</code>. After <code>notify</code> is called, the <code>Event</code> remains in a signaled state and tasks will no longer block when waiting for it, until <code>reset</code> is called.</p><p>If <code>autoreset</code> is true, at most one task will be released from <code>wait</code> for each call to <code>notify</code>.</p><p>This provides an acquire &amp; release memory ordering on notify/wait.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p>This functionality requires at least Julia 1.1.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>The <code>autoreset</code> functionality and memory ordering guarantee requires at least Julia 1.8.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/lock.jl#L481-L499">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.notify" href="#Base.notify"><code>Base.notify</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">notify(condition, val=nothing; all=true, error=false)</code></pre><p>Wake up tasks waiting for a condition, passing them <code>val</code>. If <code>all</code> is <code>true</code> (the default), all waiting tasks are woken, otherwise only one is. If <code>error</code> is <code>true</code>, the passed value is raised as an exception in the woken tasks.</p><p>Return the count of tasks woken up. Return 0 if no tasks are waiting on <code>condition</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/condition.jl#L139-L147">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.reset-Tuple{Base.Event}" href="#Base.reset-Tuple{Base.Event}"><code>Base.reset</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">reset(::Event)</code></pre><p>Reset an <a href="parallel.html#Base.Event"><code>Event</code></a> back into an un-set state. Then any future calls to <code>wait</code> will block until <a href="parallel.html#Base.notify"><code>notify</code></a> is called again.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/lock.jl#L544-L549">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Semaphore" href="#Base.Semaphore"><code>Base.Semaphore</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Semaphore(sem_size)</code></pre><p>Create a counting semaphore that allows at most <code>sem_size</code> acquires to be in use at any time. Each acquire must be matched with a release.</p><p>This provides a acquire &amp; release memory ordering on acquire/release calls.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/lock.jl#L393-L401">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.acquire" href="#Base.acquire"><code>Base.acquire</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">acquire(s::Semaphore)</code></pre><p>Wait for one of the <code>sem_size</code> permits to be available, blocking until one can be acquired.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/lock.jl#L409-L414">source</a></section><section><div><pre><code class="language-julia hljs">acquire(f, s::Semaphore)</code></pre><p>Execute <code>f</code> after acquiring from Semaphore <code>s</code>, and <code>release</code> on completion or error.</p><p>For example, a do-block form that ensures only 2 calls of <code>foo</code> will be active at the same time:</p><pre><code class="language-julia hljs">s = Base.Semaphore(2)
@sync for _ in 1:100
    Threads.@spawn begin
        Base.acquire(s) do
            foo()
        end
    end
end</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.8</header><div class="admonition-body"><p>This method requires at least Julia 1.8.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/lock.jl#L428-L451">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.release" href="#Base.release"><code>Base.release</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">release(s::Semaphore)</code></pre><p>Return one permit to the pool, possibly allowing another task to acquire it and resume execution.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/lock.jl#L461-L467">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.AbstractLock" href="#Base.AbstractLock"><code>Base.AbstractLock</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AbstractLock</code></pre><p>Abstract supertype describing types that implement the synchronization primitives: <a href="parallel.html#Base.lock"><code>lock</code></a>, <a href="parallel.html#Base.trylock"><code>trylock</code></a>, <a href="parallel.html#Base.unlock"><code>unlock</code></a>, and <a href="parallel.html#Base.islocked"><code>islocked</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/condition.jl#L11-L17">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.lock" href="#Base.lock"><code>Base.lock</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">lock(lock)</code></pre><p>Acquire the <code>lock</code> when it becomes available. If the lock is already locked by a different task/thread, wait for it to become available.</p><p>Each <code>lock</code> must be matched by an <a href="parallel.html#Base.unlock"><code>unlock</code></a>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/lock.jl#L137-L145">source</a></section><section><div><pre><code class="language-julia hljs">lock(f::Function, lock)</code></pre><p>Acquire the <code>lock</code>, execute <code>f</code> with the <code>lock</code> held, and release the <code>lock</code> when <code>f</code> returns. If the lock is already locked by a different task/thread, wait for it to become available.</p><p>When this function returns, the <code>lock</code> has been released, so the caller should not attempt to <code>unlock</code> it.</p><p>See also: <a href="parallel.html#Base.@lock"><code>@lock</code></a>.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.7</header><div class="admonition-body"><p>Using a <a href="parallel.html#Base.Channel"><code>Channel</code></a> as the second argument requires Julia 1.7 or later.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/lock.jl#L214-L228">source</a></section><section><div><p>lock(f::Function, l::Lockable)</p><p>Acquire the lock associated with <code>l</code>, execute <code>f</code> with the lock held, and release the lock when <code>f</code> returns. <code>f</code> will receive one positional argument: the value wrapped by <code>l</code>. If the lock is already locked by a different task/thread, wait for it to become available. When this function returns, the <code>lock</code> has been released, so the caller should not attempt to <code>unlock</code> it.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>Requires at least Julia 1.11.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/lock.jl#L330-L342">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.unlock" href="#Base.unlock"><code>Base.unlock</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">unlock(lock)</code></pre><p>Releases ownership of the <code>lock</code>.</p><p>If this is a recursive lock which has been acquired before, decrement an internal counter and return immediately.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/lock.jl#L167-L174">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.trylock" href="#Base.trylock"><code>Base.trylock</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">trylock(lock) -&gt; Success (Boolean)</code></pre><p>Acquire the lock if it is available, and return <code>true</code> if successful. If the lock is already locked by a different task/thread, return <code>false</code>.</p><p>Each successful <code>trylock</code> must be matched by an <a href="parallel.html#Base.unlock"><code>unlock</code></a>.</p><p>Function <code>trylock</code> combined with <a href="parallel.html#Base.islocked"><code>islocked</code></a> can be used for writing the test-and-test-and-set or exponential backoff algorithms <em>if it is supported by the <code>typeof(lock)</code></em> (read its documentation).</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/lock.jl#L97-L110">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.islocked" href="#Base.islocked"><code>Base.islocked</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">islocked(lock) -&gt; Status (Boolean)</code></pre><p>Check whether the <code>lock</code> is held by any task/thread. This function alone should not be used for synchronization. However, <code>islocked</code> combined with <a href="parallel.html#Base.trylock"><code>trylock</code></a> can be used for writing the test-and-test-and-set or exponential backoff algorithms <em>if it is supported by the <code>typeof(lock)</code></em> (read its documentation).</p><p><strong>Extended help</strong></p><p>For example, an exponential backoff can be implemented as follows if the <code>lock</code> implementation satisfied the properties documented below.</p><pre><code class="language-julia hljs">nspins = 0
while true
    while islocked(lock)
        GC.safepoint()
        nspins += 1
        nspins &gt; LIMIT &amp;&amp; error(&quot;timeout&quot;)
    end
    trylock(lock) &amp;&amp; break
    backoff()
end</code></pre><p><strong>Implementation</strong></p><p>A lock implementation is advised to define <code>islocked</code> with the following properties and note it in its docstring.</p><ul><li><code>islocked(lock)</code> is data-race-free.</li><li>If <code>islocked(lock)</code> returns <code>false</code>, an immediate invocation of <code>trylock(lock)</code> must succeed (returns <code>true</code>) if there is no interference from other tasks.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/lock.jl#L54-L88">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.ReentrantLock" href="#Base.ReentrantLock"><code>Base.ReentrantLock</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">ReentrantLock()</code></pre><p>Creates a re-entrant lock for synchronizing <a href="parallel.html#Core.Task"><code>Task</code></a>s. The same task can acquire the lock as many times as required (this is what the &quot;Reentrant&quot; part of the name means). Each <a href="parallel.html#Base.lock"><code>lock</code></a> must be matched with an <a href="parallel.html#Base.unlock"><code>unlock</code></a>.</p><p>Calling <code>lock</code> will also inhibit running of finalizers on that thread until the corresponding <code>unlock</code>. Use of the standard lock pattern illustrated below should naturally be supported, but beware of inverting the try/lock order or missing the try block entirely (e.g. attempting to return with the lock still held):</p><p>This provides a acquire/release memory ordering on lock/unlock calls.</p><pre><code class="nohighlight hljs">lock(l)
try
    &lt;atomic work&gt;
finally
    unlock(l)
end</code></pre><p>If <a href="parallel.html#Base.islocked"><code>!islocked(lck::ReentrantLock)</code></a> holds, <a href="parallel.html#Base.trylock"><code>trylock(lck)</code></a> succeeds unless there are other tasks attempting to hold the lock &quot;at the same time.&quot;</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/lock.jl#L6-L32">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.@lock" href="#Base.@lock"><code>Base.@lock</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@lock l expr</code></pre><p>Macro version of <code>lock(f, l::AbstractLock)</code> but with <code>expr</code> instead of <code>f</code> function. Expands to:</p><pre><code class="language-julia hljs">lock(l)
try
    expr
finally
    unlock(l)
end</code></pre><p>This is similar to using <a href="parallel.html#Base.lock"><code>lock</code></a> with a <code>do</code> block, but avoids creating a closure and thus can improve the performance.</p><div class="admonition is-compat"><header class="admonition-header">Compat</header><div class="admonition-body"><p><code>@lock</code> was added in Julia 1.3, and exported in Julia 1.10.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/lock.jl#L249-L267">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Lockable" href="#Base.Lockable"><code>Base.Lockable</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>Lockable(value, lock = ReentrantLock())</p><p>Creates a <code>Lockable</code> object that wraps <code>value</code> and associates it with the provided <code>lock</code>. This object supports <a href="parallel.html#Base.@lock"><code>@lock</code></a>, <a href="parallel.html#Base.lock"><code>lock</code></a>, <a href="parallel.html#Base.trylock"><code>trylock</code></a>, <a href="parallel.html#Base.unlock"><code>unlock</code></a>. To access the value, index the lockable object while holding the lock.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.11</header><div class="admonition-body"><p>Requires at least Julia 1.11.</p></div></div><p><strong>Example</strong></p><pre><code class="language-julia-repl hljs">julia&gt; locked_list = Base.Lockable(Int[]);

julia&gt; @lock(locked_list, push!(locked_list[], 1)) # must hold the lock to access the value
1-element Vector{Int64}:
 1

julia&gt; lock(summary, locked_list)
&quot;1-element Vector{Int64}&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/lock.jl#L297-L321">source</a></section></article><h2 id="Channels"><a class="docs-heading-anchor" href="#Channels">Channels</a><a id="Channels-1"></a><a class="docs-heading-anchor-permalink" href="#Channels" title="Permalink"></a></h2><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.AbstractChannel" href="#Base.AbstractChannel"><code>Base.AbstractChannel</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">AbstractChannel{T}</code></pre><p>Representation of a channel passing objects of type <code>T</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/channels.jl#L3-L7">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Channel" href="#Base.Channel"><code>Base.Channel</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Channel{T=Any}(size::Int=0)</code></pre><p>Constructs a <code>Channel</code> with an internal buffer that can hold a maximum of <code>size</code> objects of type <code>T</code>. <a href="parallel.html#Base.put!-Tuple{Channel, Any}"><code>put!</code></a> calls on a full channel block until an object is removed with <a href="io-network.html#Base.take!-Tuple{Base.GenericIOBuffer}"><code>take!</code></a>.</p><p><code>Channel(0)</code> constructs an unbuffered channel. <code>put!</code> blocks until a matching <code>take!</code> is called. And vice-versa.</p><p>Other constructors:</p><ul><li><code>Channel()</code>: default constructor, equivalent to <code>Channel{Any}(0)</code></li><li><code>Channel(Inf)</code>: equivalent to <code>Channel{Any}(typemax(Int))</code></li><li><code>Channel(sz)</code>: equivalent to <code>Channel{Any}(sz)</code></li></ul><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p>The default constructor <code>Channel()</code> and default <code>size=0</code> were added in Julia 1.3.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/channels.jl#L13-L31">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.Channel-Tuple{Function}" href="#Base.Channel-Tuple{Function}"><code>Base.Channel</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">Channel{T=Any}(func::Function, size=0; taskref=nothing, spawn=false, threadpool=nothing)</code></pre><p>Create a new task from <code>func</code>, bind it to a new channel of type <code>T</code> and size <code>size</code>, and schedule the task, all in a single call. The channel is automatically closed when the task terminates.</p><p><code>func</code> must accept the bound channel as its only argument.</p><p>If you need a reference to the created task, pass a <code>Ref{Task}</code> object via the keyword argument <code>taskref</code>.</p><p>If <code>spawn=true</code>, the <code>Task</code> created for <code>func</code> may be scheduled on another thread in parallel, equivalent to creating a task via <a href="multi-threading.html#Base.Threads.@spawn"><code>Threads.@spawn</code></a>.</p><p>If <code>spawn=true</code> and the <code>threadpool</code> argument is not set, it defaults to <code>:default</code>.</p><p>If the <code>threadpool</code> argument is set (to <code>:default</code> or <code>:interactive</code>), this implies that <code>spawn=true</code> and the new Task is spawned to the specified threadpool.</p><p>Return a <code>Channel</code>.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; chnl = Channel() do ch
           foreach(i -&gt; put!(ch, i), 1:4)
       end;

julia&gt; typeof(chnl)
Channel{Any}

julia&gt; for i in chnl
           @show i
       end;
i = 1
i = 2
i = 3
i = 4</code></pre><p>Referencing the created task:</p><pre><code class="language-julia-repl hljs">julia&gt; taskref = Ref{Task}();

julia&gt; chnl = Channel(taskref=taskref) do ch
           println(take!(ch))
       end;

julia&gt; istaskdone(taskref[])
false

julia&gt; put!(chnl, &quot;Hello&quot;);
Hello

julia&gt; istaskdone(taskref[])
true</code></pre><div class="admonition is-compat"><header class="admonition-header">Julia 1.3</header><div class="admonition-body"><p>The <code>spawn=</code> parameter was added in Julia 1.3. This constructor was added in Julia 1.3. In earlier versions of Julia, Channel used keyword arguments to set <code>size</code> and <code>T</code>, but those constructors are deprecated.</p></div></div><div class="admonition is-compat"><header class="admonition-header">Julia 1.9</header><div class="admonition-body"><p>The <code>threadpool=</code> argument was added in Julia 1.9.</p></div></div><pre><code class="language-julia-repl hljs">julia&gt; chnl = Channel{Char}(1, spawn=true) do ch
           for c in &quot;hello world&quot;
               put!(ch, c)
           end
       end
Channel{Char}(1) (2 items available)

julia&gt; String(collect(chnl))
&quot;hello world&quot;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/channels.jl#L61-L139">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.put!-Tuple{Channel, Any}" href="#Base.put!-Tuple{Channel, Any}"><code>Base.put!</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">put!(c::Channel, v)</code></pre><p>Append an item <code>v</code> to the channel <code>c</code>. Blocks if the channel is full.</p><p>For unbuffered channels, blocks until a <a href="io-network.html#Base.take!-Tuple{Base.GenericIOBuffer}"><code>take!</code></a> is performed by a different task.</p><div class="admonition is-compat"><header class="admonition-header">Julia 1.1</header><div class="admonition-body"><p><code>v</code> now gets converted to the channel&#39;s type with <a href="base.html#Base.convert"><code>convert</code></a> as <code>put!</code> is called.</p></div></div></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/channels.jl#L344-L354">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.take!-Tuple{Channel}" href="#Base.take!-Tuple{Channel}"><code>Base.take!</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">take!(c::Channel)</code></pre><p>Removes and returns a value from a <a href="parallel.html#Base.Channel"><code>Channel</code></a> in order. Blocks until data is available. For unbuffered channels, blocks until a <a href="parallel.html#Base.put!-Tuple{Channel, Any}"><code>put!</code></a> is performed by a different task.</p><p><strong>Examples</strong></p><p>Buffered channel:</p><pre><code class="language-julia-repl hljs">julia&gt; c = Channel(1);

julia&gt; put!(c, 1);

julia&gt; take!(c)
1</code></pre><p>Unbuffered channel:</p><pre><code class="language-julia-repl hljs">julia&gt; c = Channel(0);

julia&gt; task = Task(() -&gt; put!(c, 1));

julia&gt; schedule(task);

julia&gt; take!(c)
1</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/channels.jl#L457-L486">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.isready-Tuple{Channel}" href="#Base.isready-Tuple{Channel}"><code>Base.isready</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">isready(c::Channel)</code></pre><p>Determines whether a <a href="parallel.html#Base.Channel"><code>Channel</code></a> has a value stored in it. Returns immediately, does not block.</p><p>For unbuffered channels returns <code>true</code> if there are tasks waiting on a <a href="parallel.html#Base.put!-Tuple{Channel, Any}"><code>put!</code></a>.</p><p><strong>Examples</strong></p><p>Buffered channel:</p><pre><code class="language-julia-repl hljs">julia&gt; c = Channel(1);

julia&gt; isready(c)
false

julia&gt; put!(c, 1);

julia&gt; isready(c)
true</code></pre><p>Unbuffered channel:</p><pre><code class="language-julia-repl hljs">julia&gt; c = Channel();

julia&gt; isready(c)  # no tasks waiting to put!
false

julia&gt; task = Task(() -&gt; put!(c, 1));

julia&gt; schedule(task);  # schedule a put! task

julia&gt; isready(c)
true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/channels.jl#L516-L554">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.fetch-Tuple{Channel}" href="#Base.fetch-Tuple{Channel}"><code>Base.fetch</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">fetch(c::Channel)</code></pre><p>Waits for and returns (without removing) the first available item from the <code>Channel</code>. Note: <code>fetch</code> is unsupported on an unbuffered (0-size) <code>Channel</code>.</p><p><strong>Examples</strong></p><p>Buffered channel:</p><pre><code class="language-julia-repl hljs">julia&gt; c = Channel(3) do ch
           foreach(i -&gt; put!(ch, i), 1:3)
       end;

julia&gt; fetch(c)
1

julia&gt; collect(c)  # item is not removed
3-element Vector{Any}:
 1
 2
 3</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/channels.jl#L417-L440">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.close-Tuple{Channel}" href="#Base.close-Tuple{Channel}"><code>Base.close</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">close(c::Channel[, excp::Exception])</code></pre><p>Close a channel. An exception (optionally given by <code>excp</code>), is thrown by:</p><ul><li><a href="parallel.html#Base.put!-Tuple{Channel, Any}"><code>put!</code></a> on a closed channel.</li><li><a href="io-network.html#Base.take!-Tuple{Base.GenericIOBuffer}"><code>take!</code></a> and <a href="parallel.html#Base.fetch-Tuple{Task}"><code>fetch</code></a> on an empty, closed channel.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/channels.jl#L192-L199">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.bind-Tuple{Channel, Task}" href="#Base.bind-Tuple{Channel, Task}"><code>Base.bind</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">bind(chnl::Channel, task::Task)</code></pre><p>Associate the lifetime of <code>chnl</code> with a task. <code>Channel</code> <code>chnl</code> is automatically closed when the task terminates. Any uncaught exception in the task is propagated to all waiters on <code>chnl</code>.</p><p>The <code>chnl</code> object can be explicitly closed independent of task termination. Terminating tasks have no effect on already closed <code>Channel</code> objects.</p><p>When a channel is bound to multiple tasks, the first task to terminate will close the channel. When multiple channels are bound to the same task, termination of the task will close all of the bound channels.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; c = Channel(0);

julia&gt; task = @async foreach(i-&gt;put!(c, i), 1:4);

julia&gt; bind(c,task);

julia&gt; for i in c
           @show i
       end;
i = 1
i = 2
i = 3
i = 4

julia&gt; isopen(c)
false</code></pre><pre><code class="language-julia-repl hljs">julia&gt; c = Channel(0);

julia&gt; task = @async (put!(c, 1); error(&quot;foo&quot;));

julia&gt; bind(c, task);

julia&gt; take!(c)
1

julia&gt; put!(c, 1);
ERROR: TaskFailedException
Stacktrace:
[...]
    nested task error: foo
[...]</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/julia/blob/760b2e5b7396f9cc0da5efce0cadd5d1974c4069/base/channels.jl#L237-L288">source</a></section></article><h2 id="low-level-schedule-wait"><a class="docs-heading-anchor" href="#low-level-schedule-wait">Low-level synchronization using <code>schedule</code> and <code>wait</code></a><a id="low-level-schedule-wait-1"></a><a class="docs-heading-anchor-permalink" href="#low-level-schedule-wait" title="Permalink"></a></h2><p>The easiest correct use of <a href="parallel.html#Base.schedule"><code>schedule</code></a> is on a <code>Task</code> that is not started (scheduled) yet.  However, it is possible to use <a href="parallel.html#Base.schedule"><code>schedule</code></a> and <a href="parallel.html#Base.wait"><code>wait</code></a> as a very low-level building block for constructing synchronization interfaces.  A crucial pre-condition of calling <code>schedule(task)</code> is that the caller must &quot;own&quot; the <code>task</code>; i.e., it must know that the call to <code>wait</code> in the given <code>task</code> is happening at the locations known to the code calling <code>schedule(task)</code>.  One strategy for ensuring such pre-condition is to use atomics, as demonstrated in the following example:</p><pre><code class="language-julia hljs">@enum OWEState begin
    OWE_EMPTY
    OWE_WAITING
    OWE_NOTIFYING
end

mutable struct OneWayEvent
    @atomic state::OWEState
    task::Task
    OneWayEvent() = new(OWE_EMPTY)
end

function Base.notify(ev::OneWayEvent)
    state = @atomic ev.state
    while state !== OWE_NOTIFYING
        # Spin until we successfully update the state to OWE_NOTIFYING:
        state, ok = @atomicreplace(ev.state, state =&gt; OWE_NOTIFYING)
        if ok
            if state == OWE_WAITING
                # OWE_WAITING -&gt; OWE_NOTIFYING transition means that the waiter task is
                # already waiting or about to call `wait`. The notifier task must wake up
                # the waiter task.
                schedule(ev.task)
            else
                @assert state == OWE_EMPTY
                # Since we are assuming that there is only one notifier task (for
                # simplicity), we know that the other possible case here is OWE_EMPTY.
                # We do not need to do anything because we know that the waiter task has
                # not called `wait(ev::OneWayEvent)` yet.
            end
            break
        end
    end
    return
end

function Base.wait(ev::OneWayEvent)
    ev.task = current_task()
    state, ok = @atomicreplace(ev.state, OWE_EMPTY =&gt; OWE_WAITING)
    if ok
        # OWE_EMPTY -&gt; OWE_WAITING transition means that the notifier task is guaranteed to
        # invoke OWE_WAITING -&gt; OWE_NOTIFYING transition.  The waiter task must call
        # `wait()` immediately.  In particular, it MUST NOT invoke any function that may
        # yield to the scheduler at this point in code.
        wait()
    else
        @assert state == OWE_NOTIFYING
        # Otherwise, the `state` must have already been moved to OWE_NOTIFYING by the
        # notifier task.
    end
    return
end

ev = OneWayEvent()
@sync begin
    @async begin
        wait(ev)
        println(&quot;done&quot;)
    end
    println(&quot;notifying...&quot;)
    notify(ev)
end

# output
notifying...
done</code></pre><p><code>OneWayEvent</code> lets one task to <code>wait</code> for another task&#39;s <code>notify</code>.  It is a limited communication interface since <code>wait</code> can only be used once from a single task (note the non-atomic assignment of <code>ev.task</code>)</p><p>In this example, <code>notify(ev::OneWayEvent)</code> is allowed to call <code>schedule(ev.task)</code> if and only if <em>it</em> modifies the state from <code>OWE_WAITING</code> to <code>OWE_NOTIFYING</code>.  This lets us know that the task executing <code>wait(ev::OneWayEvent)</code> is now in the <code>ok</code> branch and that there cannot be other tasks that tries to <code>schedule(ev.task)</code> since their <code>@atomicreplace(ev.state, state =&gt; OWE_NOTIFYING)</code> will fail.</p></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="arrays.html">« Arrays</a><a class="docs-footer-nextpage" href="multi-threading.html">Multi-Threading »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
