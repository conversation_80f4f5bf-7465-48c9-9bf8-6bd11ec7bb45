<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>StyledStrings · The Julia Language</title><meta name="title" content="StyledStrings · The Julia Language"/><meta property="og:title" content="StyledStrings · The Julia Language"/><meta property="twitter:title" content="StyledStrings · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox"/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../manual/getting-started.html">Getting Started</a></li><li><a class="tocitem" href="../manual/installation.html">Installation</a></li><li><a class="tocitem" href="../manual/variables.html">Variables</a></li><li><a class="tocitem" href="../manual/integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="../manual/mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="../manual/complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="../manual/strings.html">Strings</a></li><li><a class="tocitem" href="../manual/functions.html">Functions</a></li><li><a class="tocitem" href="../manual/control-flow.html">Control Flow</a></li><li><a class="tocitem" href="../manual/variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="../manual/types.html">Types</a></li><li><a class="tocitem" href="../manual/methods.html">Methods</a></li><li><a class="tocitem" href="../manual/constructors.html">Constructors</a></li><li><a class="tocitem" href="../manual/conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="../manual/interfaces.html">Interfaces</a></li><li><a class="tocitem" href="../manual/modules.html">Modules</a></li><li><a class="tocitem" href="../manual/documentation.html">Documentation</a></li><li><a class="tocitem" href="../manual/metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="../manual/arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="../manual/missing.html">Missing Values</a></li><li><a class="tocitem" href="../manual/networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="../manual/parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="../manual/asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="../manual/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../manual/distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="../manual/running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="../manual/calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="../manual/handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="../manual/environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="../manual/embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="../manual/code-loading.html">Code Loading</a></li><li><a class="tocitem" href="../manual/profile.html">Profiling</a></li><li><a class="tocitem" href="../manual/stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="../manual/performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="../manual/workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="../manual/style-guide.html">Style Guide</a></li><li><a class="tocitem" href="../manual/faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="../manual/noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="../manual/unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="../manual/command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox" checked/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="Base64.html">Base64</a></li><li><a class="tocitem" href="CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="Dates.html">Dates</a></li><li><a class="tocitem" href="DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="Downloads.html">Downloads</a></li><li><a class="tocitem" href="FileWatching.html">File Events</a></li><li><a class="tocitem" href="Future.html">Future</a></li><li><a class="tocitem" href="InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="Logging.html">Logging</a></li><li><a class="tocitem" href="Markdown.html">Markdown</a></li><li><a class="tocitem" href="Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="Pkg.html">Pkg</a></li><li><a class="tocitem" href="Printf.html">Printf</a></li><li><a class="tocitem" href="Profile.html">Profiling</a></li><li><a class="tocitem" href="REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="Random.html">Random Numbers</a></li><li><a class="tocitem" href="SHA.html">SHA</a></li><li><a class="tocitem" href="Serialization.html">Serialization</a></li><li><a class="tocitem" href="SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="Sockets.html">Sockets</a></li><li><a class="tocitem" href="SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="Statistics.html">Statistics</a></li><li class="is-active"><a class="tocitem" href="StyledStrings.html">StyledStrings</a><ul class="internal"><li><a class="tocitem" href="#stdlib-styledstrings-styling"><span>Styling</span></a></li><li><a class="tocitem" href="#man-annotated-strings"><span>Annotated Strings</span></a></li><li><a class="tocitem" href="#Styling-via-[AnnotatedString](@ref-Base.AnnotatedString)s"><span>Styling via <code>AnnotatedString</code>s</span></a></li><li><a class="tocitem" href="#stdlib-styledstrings-faces"><span>Faces</span></a></li><li><a class="tocitem" href="#stdlib-styledstring-literals"><span>Styled String Literals</span></a></li><li><a class="tocitem" href="#stdlib-styledstrings-api"><span>API reference</span></a></li></ul></li><li><a class="tocitem" href="TOML.html">TOML</a></li><li><a class="tocitem" href="Tar.html">Tar</a></li><li><a class="tocitem" href="Test.html">Unit Testing</a></li><li><a class="tocitem" href="UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Standard Library</a></li><li class="is-active"><a href="StyledStrings.html">StyledStrings</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="StyledStrings.html">StyledStrings</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/StyledStrings.jl/blob/master/docs/src/index.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="stdlib-styledstrings"><a class="docs-heading-anchor" href="#stdlib-styledstrings">StyledStrings</a><a id="stdlib-styledstrings-1"></a><a class="docs-heading-anchor-permalink" href="#stdlib-styledstrings" title="Permalink"></a></h1><div class="admonition is-info"><header class="admonition-header">Note</header><div class="admonition-body"><p>The API for StyledStrings and AnnotatedStrings is considered experimental and is subject to change between Julia versions.</p></div></div><h2 id="stdlib-styledstrings-styling"><a class="docs-heading-anchor" href="#stdlib-styledstrings-styling">Styling</a><a id="stdlib-styledstrings-styling-1"></a><a class="docs-heading-anchor-permalink" href="#stdlib-styledstrings-styling" title="Permalink"></a></h2><p>When working with strings, formatting and styling often appear as a secondary concern.</p><p>For instance, when printing to a terminal you might want to sprinkle <a href="https://en.wikipedia.org/wiki/ANSI_escape_code#SGR_(Select_Graphic_Rendition)_parameters">ANSI escape sequences</a> in the output, when outputting HTML styling constructs (<code>&lt;span style=&quot;...&quot;&gt;</code>, etc.) serve a similar purpose, and so on. It is possible to simply insert the raw styling constructs into the string next to the content itself, but it quickly becomes apparent that this is not well suited for anything but the most basic use cases. Not all terminals support the same ANSI codes, the styling constructs need to be painstakingly removed when calculating the width of already-styled content, and that&#39;s before you even get into handling multiple output formats.</p><p>Instead of leaving this headache to be widely experienced downstream, it is tackled head-on by the introduction of a special string type (<a href="../base/strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a>). This string type wraps any other <a href="../base/strings.html#Core.AbstractString"><code>AbstractString</code></a> type and allows for formatting information to be applied to regions (e.g. characters 1 through to 7 are bold and red).</p><p>Regions of a string are styled by applying <a href="StyledStrings.html#StyledStrings.Face"><code>Face</code></a>s (think &quot;typeface&quot;) to them — a structure that holds styling information. As a convenience, faces in the global faces dictionary (e.g. <code>shadow</code>) can just be named instead of giving the <a href="StyledStrings.html#StyledStrings.Face"><code>Face</code></a> directly.</p><p>Along with these capabilities, we also provide a convenient way for constructing <a href="../base/strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a>s, detailed in <a href="StyledStrings.html#stdlib-styledstring-literals">Styled String Literals</a>.</p><pre><code class="language-julia-repl hljs" style="display:block;">julia&gt; using StyledStrings</code><code class="nohighlight hljs ansi" style="display:block;"></code><br/><code class="language-julia-repl hljs" style="display:block;">julia&gt; styled&quot;{yellow:hello} {blue:there}&quot;</code><code class="nohighlight hljs ansi" style="display:block;">&quot;<span class="sgr33">hello</span> <span class="sgr34">there</span>&quot;</code></pre><h2 id="man-annotated-strings"><a class="docs-heading-anchor" href="#man-annotated-strings">Annotated Strings</a><a id="man-annotated-strings-1"></a><a class="docs-heading-anchor-permalink" href="#man-annotated-strings" title="Permalink"></a></h2><p>It is sometimes useful to be able to hold metadata relating to regions of a string. A <a href="../base/strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a> wraps another string and allows for regions of it to be annotated with labelled values (<code>:label =&gt; value</code>). All generic string operations are applied to the underlying string. However, when possible, styling information is preserved. This means you can manipulate a <a href="../base/strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a> —taking substrings, padding them, concatenating them with other strings— and the metadata annotations will &quot;come along for the ride&quot;.</p><p>This string type is fundamental to the <a href="StyledStrings.html#stdlib-styledstrings">StyledStrings stdlib</a>, which uses <code>:face</code>-labelled annotations to hold styling information.</p><p>When concatenating a <a href="../base/strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a>, take care to use <a href="../base/strings.html#Base.annotatedstring"><code>annotatedstring</code></a> instead of <a href="../base/strings.html#Base.string"><code>string</code></a> if you want to keep the string annotations.</p><pre><code class="language-julia-repl hljs">julia&gt; str = AnnotatedString(&quot;hello there&quot;, [(1:5, :word, :greeting), (7:11, :label, 1)])
&quot;hello there&quot;

julia&gt; length(str)
11

julia&gt; lpad(str, 14)
&quot;   hello there&quot;

julia&gt; typeof(lpad(str, 7))
AnnotatedString{String}

julia&gt; str2 = AnnotatedString(&quot; julia&quot;, [(2:6, :face, :magenta)])
&quot; julia&quot;

julia&gt; annotatedstring(str, str2)
&quot;hello there julia&quot;

julia&gt; str * str2 == annotatedstring(str, str2) # *-concatenation works
true</code></pre><p>The annotations of a <a href="../base/strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a> can be accessed and modified via the <a href="../base/strings.html#Base.annotations"><code>annotations</code></a> and <a href="../base/strings.html#Base.annotate!"><code>annotate!</code></a> functions.</p><h2 id="Styling-via-[AnnotatedString](@ref-Base.AnnotatedString)s"><a class="docs-heading-anchor" href="#Styling-via-[AnnotatedString](@ref-Base.AnnotatedString)s">Styling via <a href="../base/strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a>s</a><a id="Styling-via-[AnnotatedString](@ref-Base.AnnotatedString)s-1"></a><a class="docs-heading-anchor-permalink" href="#Styling-via-[AnnotatedString](@ref-Base.AnnotatedString)s" title="Permalink"></a></h2><h2 id="stdlib-styledstrings-faces"><a class="docs-heading-anchor" href="#stdlib-styledstrings-faces">Faces</a><a id="stdlib-styledstrings-faces-1"></a><a class="docs-heading-anchor-permalink" href="#stdlib-styledstrings-faces" title="Permalink"></a></h2><h3 id="The-Face-type"><a class="docs-heading-anchor" href="#The-Face-type">The <code>Face</code> type</a><a id="The-Face-type-1"></a><a class="docs-heading-anchor-permalink" href="#The-Face-type" title="Permalink"></a></h3><p>A <a href="StyledStrings.html#StyledStrings.Face"><code>Face</code></a> specifies details of a typeface that text can be set in. It covers a set of basic attributes that generalize well across different formats, namely:</p><ul><li><code>font</code></li><li><code>height</code></li><li><code>weight</code></li><li><code>slant</code></li><li><code>foreground</code></li><li><code>background</code></li><li><code>underline</code></li><li><code>strikethrough</code></li><li><code>inverse</code></li><li><code>inherit</code></li></ul><p>For details on the particular forms these attributes take, see the <a href="StyledStrings.html#StyledStrings.Face"><code>Face</code></a> docstring, but of particular interest is <code>inherit</code> as it allows you to <em>inherit</em> attributes from other <a href="StyledStrings.html#StyledStrings.Face"><code>Face</code></a>s.</p><h3 id="The-global-faces-dictionary"><a class="docs-heading-anchor" href="#The-global-faces-dictionary">The global faces dictionary</a><a id="The-global-faces-dictionary-1"></a><a class="docs-heading-anchor-permalink" href="#The-global-faces-dictionary" title="Permalink"></a></h3><p>To make referring to particular styles more convenient, there is a global <code>Dict{Symbol, Face}</code> that allows for <a href="StyledStrings.html#StyledStrings.Face"><code>Face</code></a>s to be referred to simply by name. Packages can add faces to this dictionary via the <a href="StyledStrings.html#StyledStrings.addface!"><code>addface!</code></a> function, and the loaded faces can be easily <a href="StyledStrings.html#stdlib-styledstrings-face-toml">customized</a>.</p><div class="admonition is-warning"><header class="admonition-header">Appropriate face naming</header><div class="admonition-body"><p>Any package registering new faces should ensure that they are prefixed by the package name, i.e. follow the format <code>mypackage_myface</code>. This is important for predictability, and to prevent name clashes.</p><p>Furthermore, packages should take care to use (and introduce) <em>semantic</em> faces (like <code>code</code>) over direct colours and styles (like <code>cyan</code>). This is helpful in a number of ways, from making the intent in usage more obvious, aiding composability, and making user customisation more intuitive.</p></div></div><p>There are two set of exemptions to the package-prefix rule:</p><ul><li>the set of basic faces that are part of the default value of the faces dictionary</li><li>faces introduced by Julia&#39;s own standard library, namely <code>JuliaSyntaxHighlighting</code></li></ul><h4 id="stdlib-styledstrings-basic-faces"><a class="docs-heading-anchor" href="#stdlib-styledstrings-basic-faces">Basic faces</a><a id="stdlib-styledstrings-basic-faces-1"></a><a class="docs-heading-anchor-permalink" href="#stdlib-styledstrings-basic-faces" title="Permalink"></a></h4><p>Basic faces are intended to represent a general idea that is widely applicable.</p><p>For setting some text with a certain attribute, we have the <code>bold</code>, <code>light</code>, <code>italic</code>, <code>underline</code>, <code>strikethrough</code>, and <code>inverse</code> faces.</p><p>There are also named faces for the 16 terminal colors: <code>black</code>, <code>red</code>, <code>green</code>, <code>yellow</code>, <code>blue</code>, <code>magenta</code>, <code>cyan</code>, <code>white</code>, <code>bright_black</code>/<code>grey</code>/<code>gray</code>, <code>bright_red</code>, <code>bright_green</code>, <code>bright_blue</code>, <code>bright_magenta</code>, <code>bright_cyan</code>, and <code>bright_white</code>.</p><p>For shadowed text (i.e. dim but there) there is the <code>shadow</code> face. To indicate a selected region, there is the <code>region</code> face. Similarly for emphasis and highlighting the <code>emphasis</code> and <code>highlight</code> faces are defined. There is also <code>code</code> for code-like text.</p><p>For visually indicating the severity of messages, the <code>error</code>, <code>warning</code>, <code>success</code>, <code>info</code>, <code>note</code>, and <code>tip</code> faces are defined.</p><h3 id="stdlib-styledstrings-face-toml"><a class="docs-heading-anchor" href="#stdlib-styledstrings-face-toml">Customisation of faces (<code>Faces.toml</code>)</a><a id="stdlib-styledstrings-face-toml-1"></a><a class="docs-heading-anchor-permalink" href="#stdlib-styledstrings-face-toml" title="Permalink"></a></h3><p>It is good for the name faces in the global face dictionary to be customizable. Theming and aesthetics are nice, and it is important for accessibility reasons too. A TOML file can be parsed into a list of <a href="StyledStrings.html#StyledStrings.Face"><code>Face</code></a> specifications that are merged with the pre-existing entry in the face dictionary.</p><p>A <a href="StyledStrings.html#StyledStrings.Face"><code>Face</code></a> is represented in TOML like so:</p><pre><code class="language-toml hljs">[facename]
attribute = &quot;value&quot;
...

[package.facename]
attribute = &quot;value&quot;</code></pre><p>For example, if the <code>shadow</code> face is too hard to read it can be made brighter like so:</p><pre><code class="language-toml hljs">[shadow]
foreground = &quot;white&quot;</code></pre><p>On initialization, the <code>config/faces.toml</code> file under the first Julia depot (usually <code>~/.julia</code>) is loaded.</p><h3 id="Applying-faces-to-a-AnnotatedString"><a class="docs-heading-anchor" href="#Applying-faces-to-a-AnnotatedString">Applying faces to a <code>AnnotatedString</code></a><a id="Applying-faces-to-a-AnnotatedString-1"></a><a class="docs-heading-anchor-permalink" href="#Applying-faces-to-a-AnnotatedString" title="Permalink"></a></h3><p>By convention, the <code>:face</code> attributes of a <a href="../base/strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a> hold information on the <a href="StyledStrings.html#StyledStrings.Face"><code>Face</code></a>s that currently apply. This can be given in multiple forms, as a single <code>Symbol</code> naming a <a href="StyledStrings.html#StyledStrings.Face"><code>Face</code></a>s in the global face dictionary, a <a href="StyledStrings.html#StyledStrings.Face"><code>Face</code></a> itself, or a vector of either.</p><p>The <code>show(::IO, ::MIME&quot;text/plain&quot;, ::AnnotatedString)</code> and <code>show(::IO, ::MIME&quot;text/html&quot;, ::AnnotatedString)</code> methods both look at the <code>:face</code> attributes and merge them all together when determining the overall styling.</p><p>We can supply <code>:face</code> attributes to a <code>AnnotatedString</code> during construction, add them to the properties list afterwards, or use the convenient <a href="StyledStrings.html#stdlib-styledstring-literals">Styled String literals</a>.</p><pre><code class="language-julia-repl hljs" style="display:block;">julia&gt; str1 = AnnotatedString(&quot;blue text&quot;, [(1:9, :face, :blue)])</code><code class="nohighlight hljs ansi" style="display:block;">&quot;<span class="sgr34">blue text</span>&quot;</code><br/><code class="language-julia-repl hljs" style="display:block;">julia&gt; str2 = styled&quot;{blue:blue text}&quot;</code><code class="nohighlight hljs ansi" style="display:block;">&quot;<span class="sgr34">blue text</span>&quot;</code><br/><code class="language-julia-repl hljs" style="display:block;">julia&gt; str1 == str2</code><code class="nohighlight hljs ansi" style="display:block;">true</code><br/><code class="language-julia-repl hljs" style="display:block;">julia&gt; sprint(print, str1, context = :color =&gt; true)</code><code class="nohighlight hljs ansi" style="display:block;">&quot;\e[34mblue text\e[39m&quot;</code><br/><code class="language-julia-repl hljs" style="display:block;">julia&gt; sprint(show, MIME(&quot;text/html&quot;), str1, context = :color =&gt; true)</code><code class="nohighlight hljs ansi" style="display:block;">&quot;&lt;span style=\&quot;color: #195eb3\&quot;&gt;blue text&lt;/span&gt;&quot;</code></pre><h2 id="stdlib-styledstring-literals"><a class="docs-heading-anchor" href="#stdlib-styledstring-literals">Styled String Literals</a><a id="stdlib-styledstring-literals-1"></a><a class="docs-heading-anchor-permalink" href="#stdlib-styledstring-literals" title="Permalink"></a></h2><p>To ease construction of <a href="../base/strings.html#Base.AnnotatedString"><code>AnnotatedString</code></a>s with <a href="StyledStrings.html#StyledStrings.Face"><code>Face</code></a>s applied, the <a href="StyledStrings.html#StyledStrings.StyledMarkup.@styled_str"><code>styled&quot;...&quot;</code></a> styled string literal allows for the content and attributes to be easily expressed together via a custom grammar.</p><p>Within a <a href="StyledStrings.html#StyledStrings.StyledMarkup.@styled_str"><code>styled&quot;...&quot;</code></a> literal, curly braces are considered special characters and must be escaped in normal usage (<code>\{</code>, <code>\}</code>). This allows them to be used to express annotations with (nestable) <code>{annotations...:text}</code> constructs.</p><p>The <code>annotations...</code> component is a comma-separated list of three types of annotations.</p><ul><li>Face names</li><li>Inline <code>Face</code> expressions <code>(key=val,...)</code></li><li><code>key=value</code> pairs</li></ul><p>Interpolation is possible everywhere except for inline face keys.</p><p>For more information on the grammar, see the extended help of the <a href="StyledStrings.html#StyledStrings.StyledMarkup.@styled_str"><code>styled&quot;...&quot;</code></a> docstring.</p><p>As an example, we can demonstrate the list of built-in faces mentioned above like so:</p><pre><code class="language-julia-repl hljs">julia&gt; println(styled&quot;
The basic font-style attributes are {bold:bold}, {light:light}, {italic:italic},
{underline:underline}, and {strikethrough:strikethrough}.

In terms of color, we have named faces for the 16 standard terminal colors:
 {black:■} {red:■} {green:■} {yellow:■} {blue:■} {magenta:■} {cyan:■} {white:■}
 {bright_black:■} {bright_red:■} {bright_green:■} {bright_yellow:■} {bright_blue:■} {bright_magenta:■} {bright_cyan:■} {bright_white:■}

Since {code:bright_black} is effectively grey, we define two aliases for it:
{code:grey} and {code:gray} to allow for regional spelling differences.

To flip the foreground and background colors of some text, you can use the
{code:inverse} face, for example: {magenta:some {inverse:inverse} text}.

The intent-based basic faces are {shadow:shadow} (for dim but visible text),
{region:region} for selections, {emphasis:emphasis}, and {highlight:highlight}.
As above, {code:code} is used for code-like text.

Lastly, we have the &#39;message severity&#39; faces: {error:error}, {warning:warning},
{success:success}, {info:info}, {note:note}, and {tip:tip}.

Remember that all these faces (and any user or package-defined ones) can
arbitrarily nest and overlap, {region,tip:like {bold,italic:so}}.&quot;)</code></pre><pre>
 The basic font-style attributes are <span style="font-weight: 700;">bold</span>, <span style="font-weight: 300;">light</span>, <span style="font-style: italic;">italic</span>,
 <span style="text-decoration: underline;">underline</span>, and <span style="text-decoration: line-through">strikethrough</span>.

 In terms of color, we have named faces for the 16 standard terminal colors:
  <span style="color: #1c1a23;">■</span> <span style="color: #a51c2c;">■</span> <span style="color: #25a268;">■</span> <span style="color: #e5a509;">■</span> <span style="color: #195eb3;">■</span> <span style="color: #803d9b;">■</span> <span style="color: #0097a7;">■</span> <span style="color: #dddcd9;">■</span>
  <span style="color: #76757a;">■</span> <span style="color: #ed333b;">■</span> <span style="color: #33d079;">■</span> <span style="color: #f6d22c;">■</span> <span style="color: #3583e4;">■</span> <span style="color: #bf60ca;">■</span> <span style="color: #26c6da;">■</span> <span style="color: #f6f5f4;">■</span>

 Since <span style="color: #0097a7;">bright_black</span> is effectively grey, we define two aliases for it:
 <span style="color: #0097a7;">grey</span> and <span style="color: #0097a7;">gray</span> to allow for regional spelling differences.

 To flip the foreground and background colors of some text, you can use the
 <span style="color: #0097a7;">inverse</span> face, for example: <span style="color: #803d9b;">some </span><span style="background-color: #803d9b;">inverse</span><span style="color: #803d9b;"> text</span>.

 The intent-based basic faces are <span style="color: #76757a;">shadow</span> (for dim but visible text),
 <span style="background-color: #3a3a3a;">region</span> for selections, <span style="color: #195eb3;">emphasis</span>, and <span style="background-color: #195eb3;">highlight</span>.
 As above, <span style="color: #0097a7;">code</span> is used for code-like text.

 Lastly, we have the 'message severity' faces: <span style="color: #ed333b;">error</span>, <span style="color: #e5a509;">warning</span>,
 <span style="color: #25a268;">success</span>, <span style="color: #26c6da;">info</span>, <span style="color: #76757a;">note</span>, and <span style="color: #33d079;">tip</span>.

 Remember that all these faces (and any user or package-defined ones) can
 arbitrarily nest and overlap, <span style="color: #33d079;background-color: #3a3a3a;">like <span style="font-weight: 700;font-style: italic;">so</span></span>.</pre><h2 id="stdlib-styledstrings-api"><a class="docs-heading-anchor" href="#stdlib-styledstrings-api">API reference</a><a id="stdlib-styledstrings-api-1"></a><a class="docs-heading-anchor-permalink" href="#stdlib-styledstrings-api" title="Permalink"></a></h2><h3 id="Styling-and-Faces"><a class="docs-heading-anchor" href="#Styling-and-Faces">Styling and Faces</a><a id="Styling-and-Faces-1"></a><a class="docs-heading-anchor-permalink" href="#Styling-and-Faces" title="Permalink"></a></h3><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="StyledStrings.StyledMarkup.@styled_str" href="#StyledStrings.StyledMarkup.@styled_str"><code>StyledStrings.StyledMarkup.@styled_str</code></a> — <span class="docstring-category">Macro</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">@styled_str -&gt; AnnotatedString</code></pre><p>Construct a styled string. Within the string, <code>{&lt;specs&gt;:&lt;content&gt;}</code> structures apply the formatting to <code>&lt;content&gt;</code>, according to the list of comma-separated specifications <code>&lt;specs&gt;</code>. Each spec can either take the form of a face name, an inline face specification, or a <code>key=value</code> pair. The value must be wrapped by <code>{...}</code> should it contain any of the characters <code>,=:{}</code>.</p><p>String interpolation with <code>$</code> functions in the same way as regular strings, except quotes need to be escaped. Faces, keys, and values can also be interpolated with <code>$</code>.</p><p><strong>Example</strong></p><pre><code class="language-julia hljs">styled&quot;The {bold:{italic:quick} {(foreground=#cd853f):brown} fox} jumped over the {link={https://en.wikipedia.org/wiki/Laziness}:lazy} dog&quot;</code></pre><p><strong>Extended help</strong></p><p>This macro can be described by the following EBNF grammar:</p><pre><code class="language-ebnf hljs">styledstring = { styled | interpolated | escaped | plain } ;

specialchar = &#39;{&#39; | &#39;}&#39; | &#39;$&#39; | &#39;\&quot;&#39; ;
anychar = [\u0-\u1fffff] ;
plain = { anychar - specialchar } ;
escaped = &#39;\\&#39;, specialchar ;

interpolated = &#39;$&#39;, ? expr ? | &#39;$(&#39;, ? expr ?, &#39;)&#39; ;

styled = &#39;{&#39;, ws, annotations, &#39;:&#39;, content, &#39;}&#39; ;
content = { interpolated | plain | escaped | styled } ;
annotations = annotation | annotations, ws, &#39;,&#39;, ws, annotation ;
annotation = face | inlineface | keyvalue ;
ws = { &#39; &#39; | &#39;\t&#39; | &#39;\n&#39; } ; (* whitespace *)

face = facename | interpolated ;
facename = [A-Za-z0-9_]+ ;

inlineface = &#39;(&#39;, ws, [ faceprop ], { ws, &#39;,&#39;, faceprop }, ws, &#39;)&#39; ;
faceprop = [a-z]+, ws, &#39;=&#39;, ws, ( [^,)]+ | interpolated) ;

keyvalue = key, ws, &#39;=&#39;, ws, value ;
key = ( [^\0${}=,:], [^\0=,:]* ) | interpolated ;
value = simplevalue | curlybraced | interpolated ;
curlybraced = &#39;{&#39; { escaped | plain } &#39;}&#39; ;
simplevalue = [^${},:], [^,:]* ;</code></pre><p>An extra stipulation not encoded in the above grammar is that <code>plain</code> should be a valid input to <a href="../base/strings.html#Base.unescape_string"><code>unescape_string</code></a>, with <code>specialchar</code> kept.</p><p>The above grammar for <code>inlineface</code> is simplified, as the actual implementation is a bit more sophisticated. The full behaviour is given below.</p><pre><code class="language-ebnf hljs">faceprop = ( &#39;face&#39;, ws, &#39;=&#39;, ws, ( ? string ? | interpolated ) ) |
           ( &#39;height&#39;, ws, &#39;=&#39;, ws, ( ? number ? | interpolated ) ) |
           ( &#39;weight&#39;, ws, &#39;=&#39;, ws, ( symbol | interpolated ) ) |
           ( &#39;slant&#39;, ws, &#39;=&#39;, ws, ( symbol | interpolated ) ) |
           ( ( &#39;foreground&#39; | &#39;fg&#39; | &#39;background&#39; | &#39;bg&#39; ),
               ws, &#39;=&#39;, ws, ( simplecolor | interpolated ) ) |
           ( &#39;underline&#39;, ws, &#39;=&#39;, ws, ( underline | interpolated ) ) |
           ( &#39;strikethrough&#39;, ws, &#39;=&#39;, ws, ( bool | interpolated ) ) |
           ( &#39;inverse&#39;, ws, &#39;=&#39;, ws, ( bool | interpolated ) ) |
           ( &#39;inherit&#39;, ws, &#39;=&#39;, ws, ( inherit | interpolated ) ) ;

nothing = &#39;nothing&#39; ;
bool = &#39;true&#39; | &#39;false&#39; ;
symbol = [^ ,)]+ ;
hexcolor = (&#39;#&#39; | &#39;0x&#39;), [0-9a-f]{6} ;
simplecolor = hexcolor | symbol | nothing ;

underline = nothing | bool | simplecolor | underlinestyled;
underlinestyled = &#39;(&#39;, ws, (&#39;&#39; | nothing | simplecolor | interpolated), ws,
                  &#39;,&#39;, ws, ( symbol | interpolated ), ws &#39;)&#39; ;

inherit = ( &#39;[&#39;, inheritval, { &#39;,&#39;, inheritval }, &#39;]&#39; ) | inheritval;
inheritval = ws, &#39;:&#39;?, symbol ;</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/StyledStrings.jl/blob/056e843b2d428bb9735b03af0cff97e738ac7e14/src/styledmarkup.jl#L894-L977">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="StyledStrings.StyledMarkup.styled" href="#StyledStrings.StyledMarkup.styled"><code>StyledStrings.StyledMarkup.styled</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">styled(content::AbstractString) -&gt; AnnotatedString</code></pre><p>Construct a styled string. Within the string, <code>{&lt;specs&gt;:&lt;content&gt;}</code> structures apply the formatting to <code>&lt;content&gt;</code>, according to the list of comma-separated specifications <code>&lt;specs&gt;</code>. Each spec can either take the form of a face name, an inline face specification, or a <code>key=value</code> pair. The value must be wrapped by <code>{...}</code> should it contain any of the characters <code>,=:{}</code>.</p><p>This is a functional equivalent of the <a href="StyledStrings.html#StyledStrings.StyledMarkup.@styled_str"><code>@styled_str</code></a> macro, just without interpolation capabilities.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/StyledStrings.jl/blob/056e843b2d428bb9735b03af0cff97e738ac7e14/src/styledmarkup.jl#L999-L1010">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="StyledStrings.Face" href="#StyledStrings.Face"><code>StyledStrings.Face</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><p>A <a href="StyledStrings.html#StyledStrings.Face"><code>Face</code></a> is a collection of graphical attributes for displaying text. Faces control how text is displayed in the terminal, and possibly other places too.</p><p>Most of the time, a <a href="StyledStrings.html#StyledStrings.Face"><code>Face</code></a> will be stored in the global faces dicts as a unique association with a <em>face name</em> Symbol, and will be most often referred to by this name instead of the <a href="StyledStrings.html#StyledStrings.Face"><code>Face</code></a> object itself.</p><p><strong>Attributes</strong></p><p>All attributes can be set via the keyword constructor, and default to <code>nothing</code>.</p><ul><li><code>height</code> (an <code>Int</code> or <code>Float64</code>): The height in either deci-pt (when an <code>Int</code>), or as a factor of the base size (when a <code>Float64</code>).</li><li><code>weight</code> (a <code>Symbol</code>): One of the symbols (from faintest to densest) <code>:thin</code>, <code>:extralight</code>, <code>:light</code>, <code>:semilight</code>, <code>:normal</code>, <code>:medium</code>, <code>:semibold</code>, <code>:bold</code>, <code>:extrabold</code>, or <code>:black</code>. In terminals any weight greater than <code>:normal</code> is displayed as bold, and in terminals that support variable-brightness text, any weight less than <code>:normal</code> is displayed as faint.</li><li><code>slant</code> (a <code>Symbol</code>): One of the symbols <code>:italic</code>, <code>:oblique</code>, or <code>:normal</code>.</li><li><code>foreground</code> (a <code>SimpleColor</code>): The text foreground color.</li><li><code>background</code> (a <code>SimpleColor</code>): The text background color.</li><li><code>underline</code>, the text underline, which takes one of the following forms:<ul><li>a <code>Bool</code>: Whether the text should be underlined or not.<br/></li><li>a <code>SimpleColor</code>: The text should be underlined with this color.<br/></li><li>a <code>Tuple{Nothing, Symbol}</code>: The text should be underlined using the style set by the Symbol, one of <code>:straight</code>, <code>:double</code>, <code>:curly</code>, <code>:dotted</code>, or <code>:dashed</code>.<br/></li><li>a <code>Tuple{SimpleColor, Symbol}</code>: The text should be underlined in the specified SimpleColor, and using the style specified by the Symbol, as before.</li></ul></li><li><code>strikethrough</code> (a <code>Bool</code>): Whether the text should be struck through.</li><li><code>inverse</code> (a <code>Bool</code>): Whether the foreground and background colors should be inverted.</li><li><code>inherit</code> (a <code>Vector{Symbol}</code>): Names of faces to inherit from, with earlier faces taking priority. All faces inherit from the <code>:default</code> face.</li></ul></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/StyledStrings.jl/blob/056e843b2d428bb9735b03af0cff97e738ac7e14/src/faces.jl#L86-L123">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="StyledStrings.addface!" href="#StyledStrings.addface!"><code>StyledStrings.addface!</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">addface!(name::Symbol =&gt; default::Face)</code></pre><p>Create a new face by the name <code>name</code>. So long as no face already exists by this name, <code>default</code> is added to both <code>FACES</code><code>.default</code> and (a copy of) to <code>FACES</code>.<code>current</code>, with the current value returned.</p><p>Should the face <code>name</code> already exist, <code>nothing</code> is returned.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; addface!(:mypkg_myface =&gt; Face(slant=:italic, underline=true))
Face (sample)
         slant: italic
     underline: true</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/StyledStrings.jl/blob/056e843b2d428bb9735b03af0cff97e738ac7e14/src/faces.jl#L381-L398">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="StyledStrings.withfaces" href="#StyledStrings.withfaces"><code>StyledStrings.withfaces</code></a> — <span class="docstring-category">Function</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">withfaces(f, kv::Pair...)
withfaces(f, kvpair_itr)</code></pre><p>Execute <code>f</code> with <code>FACES</code><code>.current</code> temporarily modified by zero or more <code>:name =&gt; val</code> arguments <code>kv</code>, or <code>kvpair_itr</code> which produces <code>kv</code>-form values.</p><p><code>withfaces</code> is generally used via the <code>withfaces(kv...) do ... end</code> syntax. A value of <code>nothing</code> can be used to temporarily unset a face (if it has been set). When <code>withfaces</code> returns, the original <code>FACES</code><code>.current</code> has been restored.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; withfaces(:yellow =&gt; Face(foreground=:red), :green =&gt; :blue) do
           println(styled&quot;{yellow:red} and {green:blue} mixed make {magenta:purple}&quot;)
       end
red and blue mixed make purple</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/StyledStrings.jl/blob/056e843b2d428bb9735b03af0cff97e738ac7e14/src/faces.jl#L446-L466">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="StyledStrings.SimpleColor" href="#StyledStrings.SimpleColor"><code>StyledStrings.SimpleColor</code></a> — <span class="docstring-category">Type</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">struct SimpleColor</code></pre><p>A basic representation of a color, intended for string styling purposes. It can either contain a named color (like <code>:red</code>), or an <code>RGBTuple</code> which is a NamedTuple specifying an <code>r</code>, <code>g</code>, <code>b</code> color with a bit-depth of 8.</p><p><strong>Constructors</strong></p><pre><code class="language-julia hljs">SimpleColor(name::Symbol)  # e.g. :red
SimpleColor(rgb::RGBTuple) # e.g. (r=1, b=2, g=3)
SimpleColor(r::Integer, b::Integer, b::Integer)
SimpleColor(rgb::UInt32)   # e.g. 0x123456</code></pre><p>Also see <code>tryparse(SimpleColor, rgb::String)</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/StyledStrings.jl/blob/056e843b2d428bb9735b03af0cff97e738ac7e14/src/faces.jl#L5-L22">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.parse-Tuple{Type{StyledStrings.SimpleColor}, String}" href="#Base.parse-Tuple{Type{StyledStrings.SimpleColor}, String}"><code>Base.parse</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">parse(::Type{SimpleColor}, rgb::String)</code></pre><p>An analogue of <code>tryparse(SimpleColor, rgb::String)</code> (which see), that raises an error instead of returning <code>nothing</code>.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/StyledStrings.jl/blob/056e843b2d428bb9735b03af0cff97e738ac7e14/src/faces.jl#L73-L78">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.tryparse-Tuple{Type{StyledStrings.SimpleColor}, String}" href="#Base.tryparse-Tuple{Type{StyledStrings.SimpleColor}, String}"><code>Base.tryparse</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">tryparse(::Type{SimpleColor}, rgb::String)</code></pre><p>Attempt to parse <code>rgb</code> as a <code>SimpleColor</code>. If <code>rgb</code> starts with <code>#</code> and has a length of 7, it is converted into a <code>RGBTuple</code>-backed <code>SimpleColor</code>. If <code>rgb</code> starts with <code>a</code>-<code>z</code>, <code>rgb</code> is interpreted as a color name and converted to a <code>Symbol</code>-backed <code>SimpleColor</code>.</p><p>Otherwise, <code>nothing</code> is returned.</p><p><strong>Examples</strong></p><pre><code class="language-julia-repl hljs">julia&gt; tryparse(SimpleColor, &quot;blue&quot;)
SimpleColor(blue)

julia&gt; tryparse(SimpleColor, &quot;#9558b2&quot;)
SimpleColor(#9558b2)

julia&gt; tryparse(SimpleColor, &quot;#nocolor&quot;)</code></pre></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/StyledStrings.jl/blob/056e843b2d428bb9735b03af0cff97e738ac7e14/src/faces.jl#L38-L59">source</a></section></article><article class="docstring"><header><a class="docstring-article-toggle-button fa-solid fa-chevron-down" href="javascript:;" title="Collapse docstring"></a><a class="docstring-binding" id="Base.merge-Tuple{StyledStrings.Face, StyledStrings.Face}" href="#Base.merge-Tuple{StyledStrings.Face, StyledStrings.Face}"><code>Base.merge</code></a> — <span class="docstring-category">Method</span><span class="is-flex-grow-1 docstring-article-toggle-button" title="Collapse docstring"></span></header><section><div><pre><code class="language-julia hljs">merge(initial::Face, others::Face...)</code></pre><p>Merge the properties of the <code>initial</code> face and <code>others</code>, with later faces taking priority.</p></div><a class="docs-sourcelink" target="_blank" href="https://github.com/JuliaLang/StyledStrings.jl/blob/056e843b2d428bb9735b03af0cff97e738ac7e14/src/faces.jl#L496-L501">source</a></section></article></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="Statistics.html">« Statistics</a><a class="docs-footer-nextpage" href="TOML.html">TOML »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
