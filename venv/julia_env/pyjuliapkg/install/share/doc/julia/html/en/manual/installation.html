<!DOCTYPE html>
<html lang="en"><head><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>Installation · The Julia Language</title><meta name="title" content="Installation · The Julia Language"/><meta property="og:title" content="Installation · The Julia Language"/><meta property="twitter:title" content="Installation · The Julia Language"/><meta name="description" content="Documentation for The Julia Language."/><meta property="og:description" content="Documentation for The Julia Language."/><meta property="twitter:description" content="Documentation for The Julia Language."/><script async src="https://www.googletagmanager.com/gtag/js?id=UA-28835595-6"></script><script>  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-28835595-6', {'page_path': location.pathname + location.search + location.hash});
</script><script data-outdated-warner src="../assets/warner.js"></script><link href="https://cdnjs.cloudflare.com/ajax/libs/lato-font/3.0.0/css/lato-font.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/juliamono/0.050/juliamono.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/fontawesome.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/solid.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css" rel="stylesheet" type="text/css"/><link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.8/katex.min.css" rel="stylesheet" type="text/css"/><script>documenterBaseURL=".."</script><script src="https://cdnjs.cloudflare.com/ajax/libs/require.js/2.3.6/require.min.js" data-main="../assets/documenter.js"></script><script src="../search_index.js"></script><script src="../siteinfo.js"></script><script src="../../versions.js"></script><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-mocha.css" data-theme-name="catppuccin-mocha"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-macchiato.css" data-theme-name="catppuccin-macchiato"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-frappe.css" data-theme-name="catppuccin-frappe"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/catppuccin-latte.css" data-theme-name="catppuccin-latte"/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-dark.css" data-theme-name="documenter-dark" data-theme-primary-dark/><link class="docs-theme-link" rel="stylesheet" type="text/css" href="../assets/themes/documenter-light.css" data-theme-name="documenter-light" data-theme-primary/><script src="../assets/themeswap.js"></script><link href="../assets/julia-manual.css" rel="stylesheet" type="text/css"/><link href="../assets/julia.ico" rel="icon" type="image/x-icon"/></head><body><div id="documenter"><nav class="docs-sidebar"><a class="docs-logo" href="../index.html"><img class="docs-light-only" src="../assets/logo.svg" alt="The Julia Language logo"/><img class="docs-dark-only" src="../assets/logo-dark.svg" alt="The Julia Language logo"/></a><button class="docs-search-query input is-rounded is-small is-clickable my-2 mx-auto py-1 px-2" id="documenter-search-query">Search docs (Ctrl + /)</button><ul class="docs-menu"><li><a class="tocitem" href="../index.html">Julia Documentation</a></li><li><input class="collapse-toggle" id="menuitem-3" type="checkbox" checked/><label class="tocitem" for="menuitem-3"><span class="docs-label">Manual</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="getting-started.html">Getting Started</a></li><li class="is-active"><a class="tocitem" href="installation.html">Installation</a><ul class="internal"><li><a class="tocitem" href="#Windows"><span>Windows</span></a></li><li><a class="tocitem" href="#Mac-and-Linux"><span>Mac and Linux</span></a></li><li><a class="tocitem" href="#Alternative-installation-methods"><span>Alternative installation methods</span></a></li></ul></li><li><a class="tocitem" href="variables.html">Variables</a></li><li><a class="tocitem" href="integers-and-floating-point-numbers.html">Integers and Floating-Point Numbers</a></li><li><a class="tocitem" href="mathematical-operations.html">Mathematical Operations and Elementary Functions</a></li><li><a class="tocitem" href="complex-and-rational-numbers.html">Complex and Rational Numbers</a></li><li><a class="tocitem" href="strings.html">Strings</a></li><li><a class="tocitem" href="functions.html">Functions</a></li><li><a class="tocitem" href="control-flow.html">Control Flow</a></li><li><a class="tocitem" href="variables-and-scoping.html">Scope of Variables</a></li><li><a class="tocitem" href="types.html">Types</a></li><li><a class="tocitem" href="methods.html">Methods</a></li><li><a class="tocitem" href="constructors.html">Constructors</a></li><li><a class="tocitem" href="conversion-and-promotion.html">Conversion and Promotion</a></li><li><a class="tocitem" href="interfaces.html">Interfaces</a></li><li><a class="tocitem" href="modules.html">Modules</a></li><li><a class="tocitem" href="documentation.html">Documentation</a></li><li><a class="tocitem" href="metaprogramming.html">Metaprogramming</a></li><li><a class="tocitem" href="arrays.html">Single- and multi-dimensional Arrays</a></li><li><a class="tocitem" href="missing.html">Missing Values</a></li><li><a class="tocitem" href="networking-and-streams.html">Networking and Streams</a></li><li><a class="tocitem" href="parallel-computing.html">Parallel Computing</a></li><li><a class="tocitem" href="asynchronous-programming.html">Asynchronous Programming</a></li><li><a class="tocitem" href="multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="distributed-computing.html">Multi-processing and Distributed Computing</a></li><li><a class="tocitem" href="running-external-programs.html">Running External Programs</a></li><li><a class="tocitem" href="calling-c-and-fortran-code.html">Calling C and Fortran Code</a></li><li><a class="tocitem" href="handling-operating-system-variation.html">Handling Operating System Variation</a></li><li><a class="tocitem" href="environment-variables.html">Environment Variables</a></li><li><a class="tocitem" href="embedding.html">Embedding Julia</a></li><li><a class="tocitem" href="code-loading.html">Code Loading</a></li><li><a class="tocitem" href="profile.html">Profiling</a></li><li><a class="tocitem" href="stacktraces.html">Stack Traces</a></li><li><a class="tocitem" href="performance-tips.html">Performance Tips</a></li><li><a class="tocitem" href="workflow-tips.html">Workflow Tips</a></li><li><a class="tocitem" href="style-guide.html">Style Guide</a></li><li><a class="tocitem" href="faq.html">Frequently Asked Questions</a></li><li><a class="tocitem" href="noteworthy-differences.html">Noteworthy Differences from other Languages</a></li><li><a class="tocitem" href="unicode-input.html">Unicode Input</a></li><li><a class="tocitem" href="command-line-interface.html">Command-line Interface</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-4" type="checkbox"/><label class="tocitem" for="menuitem-4"><span class="docs-label">Base</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../base/base.html">Essentials</a></li><li><a class="tocitem" href="../base/collections.html">Collections and Data Structures</a></li><li><a class="tocitem" href="../base/math.html">Mathematics</a></li><li><a class="tocitem" href="../base/numbers.html">Numbers</a></li><li><a class="tocitem" href="../base/strings.html">Strings</a></li><li><a class="tocitem" href="../base/arrays.html">Arrays</a></li><li><a class="tocitem" href="../base/parallel.html">Tasks</a></li><li><a class="tocitem" href="../base/multi-threading.html">Multi-Threading</a></li><li><a class="tocitem" href="../base/scopedvalues.html">Scoped Values</a></li><li><a class="tocitem" href="../base/constants.html">Constants</a></li><li><a class="tocitem" href="../base/file.html">Filesystem</a></li><li><a class="tocitem" href="../base/io-network.html">I/O and Network</a></li><li><a class="tocitem" href="../base/punctuation.html">Punctuation</a></li><li><a class="tocitem" href="../base/sort.html">Sorting and Related Functions</a></li><li><a class="tocitem" href="../base/iterators.html">Iteration utilities</a></li><li><a class="tocitem" href="../base/reflection.html">Reflection and introspection</a></li><li><a class="tocitem" href="../base/c.html">C Interface</a></li><li><a class="tocitem" href="../base/libc.html">C Standard Library</a></li><li><a class="tocitem" href="../base/stacktraces.html">StackTraces</a></li><li><a class="tocitem" href="../base/simd-types.html">SIMD Support</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-5" type="checkbox"/><label class="tocitem" for="menuitem-5"><span class="docs-label">Standard Library</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../stdlib/ArgTools.html">ArgTools</a></li><li><a class="tocitem" href="../stdlib/Artifacts.html">Artifacts</a></li><li><a class="tocitem" href="../stdlib/Base64.html">Base64</a></li><li><a class="tocitem" href="../stdlib/CRC32c.html">CRC32c</a></li><li><a class="tocitem" href="../stdlib/Dates.html">Dates</a></li><li><a class="tocitem" href="../stdlib/DelimitedFiles.html">Delimited Files</a></li><li><a class="tocitem" href="../stdlib/Distributed.html">Distributed Computing</a></li><li><a class="tocitem" href="../stdlib/Downloads.html">Downloads</a></li><li><a class="tocitem" href="../stdlib/FileWatching.html">File Events</a></li><li><a class="tocitem" href="../stdlib/Future.html">Future</a></li><li><a class="tocitem" href="../stdlib/InteractiveUtils.html">Interactive Utilities</a></li><li><a class="tocitem" href="../stdlib/LazyArtifacts.html">Lazy Artifacts</a></li><li><a class="tocitem" href="../stdlib/LibCURL.html">LibCURL</a></li><li><a class="tocitem" href="../stdlib/LibGit2.html">LibGit2</a></li><li><a class="tocitem" href="../stdlib/Libdl.html">Dynamic Linker</a></li><li><a class="tocitem" href="../stdlib/LinearAlgebra.html">Linear Algebra</a></li><li><a class="tocitem" href="../stdlib/Logging.html">Logging</a></li><li><a class="tocitem" href="../stdlib/Markdown.html">Markdown</a></li><li><a class="tocitem" href="../stdlib/Mmap.html">Memory-mapped I/O</a></li><li><a class="tocitem" href="../stdlib/NetworkOptions.html">Network Options</a></li><li><a class="tocitem" href="../stdlib/Pkg.html">Pkg</a></li><li><a class="tocitem" href="../stdlib/Printf.html">Printf</a></li><li><a class="tocitem" href="../stdlib/Profile.html">Profiling</a></li><li><a class="tocitem" href="../stdlib/REPL.html">The Julia REPL</a></li><li><a class="tocitem" href="../stdlib/Random.html">Random Numbers</a></li><li><a class="tocitem" href="../stdlib/SHA.html">SHA</a></li><li><a class="tocitem" href="../stdlib/Serialization.html">Serialization</a></li><li><a class="tocitem" href="../stdlib/SharedArrays.html">Shared Arrays</a></li><li><a class="tocitem" href="../stdlib/Sockets.html">Sockets</a></li><li><a class="tocitem" href="../stdlib/SparseArrays.html">Sparse Arrays</a></li><li><a class="tocitem" href="../stdlib/Statistics.html">Statistics</a></li><li><a class="tocitem" href="../stdlib/StyledStrings.html">StyledStrings</a></li><li><a class="tocitem" href="../stdlib/TOML.html">TOML</a></li><li><a class="tocitem" href="../stdlib/Tar.html">Tar</a></li><li><a class="tocitem" href="../stdlib/Test.html">Unit Testing</a></li><li><a class="tocitem" href="../stdlib/UUIDs.html">UUIDs</a></li><li><a class="tocitem" href="../stdlib/Unicode.html">Unicode</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6" type="checkbox"/><label class="tocitem" for="menuitem-6"><span class="docs-label">Developer Documentation</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><input class="collapse-toggle" id="menuitem-6-1" type="checkbox"/><label class="tocitem" for="menuitem-6-1"><span class="docs-label">Documentation of Julia&#39;s Internals</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/init.html">Initialization of the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/ast.html">Julia ASTs</a></li><li><a class="tocitem" href="../devdocs/types.html">More about types</a></li><li><a class="tocitem" href="../devdocs/object.html">Memory layout of Julia Objects</a></li><li><a class="tocitem" href="../devdocs/eval.html">Eval of Julia code</a></li><li><a class="tocitem" href="../devdocs/callconv.html">Calling Conventions</a></li><li><a class="tocitem" href="../devdocs/compiler.html">High-level Overview of the Native-Code Generation Process</a></li><li><a class="tocitem" href="../devdocs/functions.html">Julia Functions</a></li><li><a class="tocitem" href="../devdocs/cartesian.html">Base.Cartesian</a></li><li><a class="tocitem" href="../devdocs/meta.html">Talking to the compiler (the <code>:meta</code> mechanism)</a></li><li><a class="tocitem" href="../devdocs/subarrays.html">SubArrays</a></li><li><a class="tocitem" href="../devdocs/isbitsunionarrays.html">isbits Union Optimizations</a></li><li><a class="tocitem" href="../devdocs/sysimg.html">System Image Building</a></li><li><a class="tocitem" href="../devdocs/pkgimg.html">Package Images</a></li><li><a class="tocitem" href="../devdocs/llvm-passes.html">Custom LLVM Passes</a></li><li><a class="tocitem" href="../devdocs/llvm.html">Working with LLVM</a></li><li><a class="tocitem" href="../devdocs/stdio.html">printf() and stdio in the Julia runtime</a></li><li><a class="tocitem" href="../devdocs/boundscheck.html">Bounds checking</a></li><li><a class="tocitem" href="../devdocs/locks.html">Proper maintenance and care of multi-threading locks</a></li><li><a class="tocitem" href="../devdocs/offset-arrays.html">Arrays with custom indices</a></li><li><a class="tocitem" href="../devdocs/require.html">Module loading</a></li><li><a class="tocitem" href="../devdocs/inference.html">Inference</a></li><li><a class="tocitem" href="../devdocs/ssair.html">Julia SSA-form IR</a></li><li><a class="tocitem" href="../devdocs/EscapeAnalysis.html"><code>EscapeAnalysis</code></a></li><li><a class="tocitem" href="../devdocs/aot.html">Ahead of Time Compilation</a></li><li><a class="tocitem" href="../devdocs/gc-sa.html">Static analyzer annotations for GC correctness in C code</a></li><li><a class="tocitem" href="../devdocs/gc.html">Garbage Collection in Julia</a></li><li><a class="tocitem" href="../devdocs/jit.html">JIT Design and Implementation</a></li><li><a class="tocitem" href="../devdocs/builtins.html">Core.Builtins</a></li><li><a class="tocitem" href="../devdocs/precompile_hang.html">Fixing precompilation hangs due to open tasks or IO</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-2" type="checkbox"/><label class="tocitem" for="menuitem-6-2"><span class="docs-label">Developing/debugging Julia&#39;s C code</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/backtraces.html">Reporting and analyzing crashes (segfaults)</a></li><li><a class="tocitem" href="../devdocs/debuggingtips.html">gdb debugging tips</a></li><li><a class="tocitem" href="../devdocs/valgrind.html">Using Valgrind with Julia</a></li><li><a class="tocitem" href="../devdocs/external_profilers.html">External Profiler Support</a></li><li><a class="tocitem" href="../devdocs/sanitizers.html">Sanitizer support</a></li><li><a class="tocitem" href="../devdocs/probes.html">Instrumenting Julia with DTrace, and bpftrace</a></li></ul></li><li><input class="collapse-toggle" id="menuitem-6-3" type="checkbox"/><label class="tocitem" for="menuitem-6-3"><span class="docs-label">Building Julia</span><i class="docs-chevron"></i></label><ul class="collapsed"><li><a class="tocitem" href="../devdocs/build/build.html">Building Julia (Detailed)</a></li><li><a class="tocitem" href="../devdocs/build/linux.html">Linux</a></li><li><a class="tocitem" href="../devdocs/build/macos.html">macOS</a></li><li><a class="tocitem" href="../devdocs/build/windows.html">Windows</a></li><li><a class="tocitem" href="../devdocs/build/freebsd.html">FreeBSD</a></li><li><a class="tocitem" href="../devdocs/build/arm.html">ARM (Linux)</a></li><li><a class="tocitem" href="../devdocs/build/distributing.html">Binary distributions</a></li></ul></li></ul></li></ul><div class="docs-version-selector field has-addons"><div class="control"><span class="docs-label button is-static is-size-7">Version</span></div><div class="docs-selector control is-expanded"><div class="select is-fullwidth is-size-7"><select id="documenter-version-selector"></select></div></div></div></nav><div class="docs-main"><header class="docs-navbar"><a class="docs-sidebar-button docs-navbar-link fa-solid fa-bars is-hidden-desktop" id="documenter-sidebar-button" href="#"></a><nav class="breadcrumb"><ul class="is-hidden-mobile"><li><a class="is-disabled">Manual</a></li><li class="is-active"><a href="installation.html">Installation</a></li></ul><ul class="is-hidden-tablet"><li class="is-active"><a href="installation.html">Installation</a></li></ul></nav><div class="docs-right"><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia" title="View the repository on GitHub"><span class="docs-icon fa-brands"></span><span class="docs-label is-hidden-touch">GitHub</span></a><a class="docs-navbar-link" href="https://github.com/JuliaLang/julia/blob/master/doc/src/manual/installation.md" title="Edit source on GitHub"><span class="docs-icon fa-solid"></span></a><a class="docs-settings-button docs-navbar-link fa-solid fa-gear" id="documenter-settings-button" href="#" title="Settings"></a><a class="docs-article-toggle-button fa-solid fa-chevron-up" id="documenter-article-toggle-button" href="javascript:;" title="Collapse all docstrings"></a></div></header><article class="content" id="documenter-page"><h1 id="man-installation"><a class="docs-heading-anchor" href="#man-installation">Installation</a><a id="man-installation-1"></a><a class="docs-heading-anchor-permalink" href="#man-installation" title="Permalink"></a></h1><p>There are many ways to install Julia. The following sections highlight the recommended method for each of the main supported platforms, and then present alternative ways that might be useful in specialized situations.</p><p>The current installation recommendation is a solution based on Juliaup. If you installed Julia previously with a method that is <em>not</em> based on Juliaup and want to switch your system to an installation that is based on Juliaup, we recommend that you uninstall all previous Julia versions, ensure that you remove anything Julia related from your <code>PATH</code> variable and then install Julia with one of the methods described below.</p><h2 id="Windows"><a class="docs-heading-anchor" href="#Windows">Windows</a><a id="Windows-1"></a><a class="docs-heading-anchor-permalink" href="#Windows" title="Permalink"></a></h2><p>On Windows Julia can be installed directly from the Windows store <a href="https://www.microsoft.com/store/apps/9NJNWW8PVKMN">here</a>. One can also install exactly the same version by executing</p><pre><code class="nohighlight hljs">winget install julia -s msstore</code></pre><p>in any shell.</p><h2 id="Mac-and-Linux"><a class="docs-heading-anchor" href="#Mac-and-Linux">Mac and Linux</a><a id="Mac-and-Linux-1"></a><a class="docs-heading-anchor-permalink" href="#Mac-and-Linux" title="Permalink"></a></h2><p>Julia can be installed on Linux or Mac by executing</p><pre><code class="nohighlight hljs">curl -fsSL https://install.julialang.org | sh</code></pre><p>in a shell.</p><h3 id="Command-line-arguments"><a class="docs-heading-anchor" href="#Command-line-arguments">Command line arguments</a><a id="Command-line-arguments-1"></a><a class="docs-heading-anchor-permalink" href="#Command-line-arguments" title="Permalink"></a></h3><p>One can pass various command line arguments to the Julia installer. The syntax for installer arguments is</p><pre><code class="language-bash hljs">curl -fsSL https://install.julialang.org | sh -s -- &lt;ARGS&gt;</code></pre><p>Here <code>&lt;ARGS&gt;</code> should be replaced with one or more of the following arguments:</p><ul><li><code>--yes</code> (or <code>-y</code>): Run the installer in a non-interactive mode. All configuration values use their default or a value supplied as a command line argument.</li><li><code>--default-channel=&lt;NAME&gt;</code>: Configure the default Juliaup channel. For example <code>--default-channel lts</code> would install the <code>lts</code> channel and configure it as the default.</li><li><code>--add-to-path=&lt;yes|no&gt;</code>: Configure whether Julia should be added to the <code>PATH</code> environment variable. Valid values are <code>yes</code> (default) and <code>no</code>.</li><li><code>--background-selfupdate=&lt;SECONDS&gt;</code>: Configure an optional CRON job that auto-updates Juliaup if <code>&lt;SECONDS&gt;</code> has a value larger than 0. The actual value controls how often the CRON job will run to check for a new Juliaup version in seconds. The default value is 0, i.e. no CRON job will be created.</li><li><code>--startup-selfupdate=&lt;MINUTES&gt;</code>: Configure how often Julia will check for new versions of Juliaup when Julia is started. The default is every 1440 minutes.</li><li><code>-p=&lt;PATH&gt;</code> (or <code>--path</code>): Configure where the Julia and Juliaup binaries are installed. The default is <code>~/.juliaup</code>.</li></ul><h2 id="Alternative-installation-methods"><a class="docs-heading-anchor" href="#Alternative-installation-methods">Alternative installation methods</a><a id="Alternative-installation-methods-1"></a><a class="docs-heading-anchor-permalink" href="#Alternative-installation-methods" title="Permalink"></a></h2><p>Note that we recommend the following methods <em>only</em> if none of the installation methods described above work for your system.</p><p>Some of the installation methods described below recommend installing a package called <code>juliaup</code>. Note that this nevertheless installs a fully functional Julia system, not just Juliaup.</p><h3 id="App-Installer-(Windows)"><a class="docs-heading-anchor" href="#App-Installer-(Windows)">App Installer (Windows)</a><a id="App-Installer-(Windows)-1"></a><a class="docs-heading-anchor-permalink" href="#App-Installer-(Windows)" title="Permalink"></a></h3><p>If the Windows Store is blocked on a system, we have an alternative <a href="https://learn.microsoft.com/en-us/windows/msix/app-installer/app-installer-file-overview">MSIX App Installer</a> based setup. To use the App Installer version, download <a href="https://install.julialang.org/Julia.appinstaller">this</a> file and open it by double clicking on it.</p><h3 id="MSI-Installer-(Windows)"><a class="docs-heading-anchor" href="#MSI-Installer-(Windows)">MSI Installer (Windows)</a><a id="MSI-Installer-(Windows)-1"></a><a class="docs-heading-anchor-permalink" href="#MSI-Installer-(Windows)" title="Permalink"></a></h3><p>If neither the Windows Store nor the App Installer version work on your Windows system, you can also use a MSI based installer. Note that this installation methods comes with serious limitations and is generally not recommended unless no other method works. For example, there is no automatic update mechanism for Juliaup with this installation method. The 64 bit version of the MSI installer can be downloaded from <a href="https://install.julialang.org/Julia-x64.msi">here</a> and the 32 bit version from <a href="https://install.julialang.org/Julia-x86.msi">here</a>.</p><p>By default the install will be a per-user install that does not require  elevation. You can also do a system install by running the following command  from a shell:</p><pre><code class="nohighlight hljs">msiexec /i &lt;PATH_TO_JULIA_MSI&gt; ALLUSERS=1</code></pre><h3 id="[Homebrew](https://brew.sh)-(Mac-and-Linux)"><a class="docs-heading-anchor" href="#[Homebrew](https://brew.sh)-(Mac-and-Linux)"><a href="https://brew.sh">Homebrew</a> (Mac and Linux)</a><a id="[Homebrew](https://brew.sh)-(Mac-and-Linux)-1"></a><a class="docs-heading-anchor-permalink" href="#[Homebrew](https://brew.sh)-(Mac-and-Linux)" title="Permalink"></a></h3><p>On systems with brew, you can install Julia by running</p><pre><code class="nohighlight hljs">brew install juliaup</code></pre><p>in a shell. Note that you will have to update Juliaup with standard brew commands.</p><h3 id="[Arch-Linux-AUR](https://aur.archlinux.org/packages/juliaup/)-(Linux)"><a class="docs-heading-anchor" href="#[Arch-Linux-AUR](https://aur.archlinux.org/packages/juliaup/)-(Linux)"><a href="https://aur.archlinux.org/packages/juliaup/">Arch Linux - AUR</a> (Linux)</a><a id="[Arch-Linux-AUR](https://aur.archlinux.org/packages/juliaup/)-(Linux)-1"></a><a class="docs-heading-anchor-permalink" href="#[Arch-Linux-AUR](https://aur.archlinux.org/packages/juliaup/)-(Linux)" title="Permalink"></a></h3><p>On Arch Linux, Juliaup is available <a href="https://aur.archlinux.org/packages/juliaup/">in the Arch User Repository (AUR)</a>.</p><h3 id="[openSUSE-Tumbleweed](https://get.opensuse.org/tumbleweed/)-(Linux)"><a class="docs-heading-anchor" href="#[openSUSE-Tumbleweed](https://get.opensuse.org/tumbleweed/)-(Linux)"><a href="https://get.opensuse.org/tumbleweed/">openSUSE Tumbleweed</a> (Linux)</a><a id="[openSUSE-Tumbleweed](https://get.opensuse.org/tumbleweed/)-(Linux)-1"></a><a class="docs-heading-anchor-permalink" href="#[openSUSE-Tumbleweed](https://get.opensuse.org/tumbleweed/)-(Linux)" title="Permalink"></a></h3><p>On openSUSE Tumbleweed, you can install Julia by running</p><pre><code class="language-sh hljs">zypper install juliaup</code></pre><p>in a shell with root privileges.</p><h3 id="[cargo](https://crates.io/crates/juliaup/)-(Windows,-Mac-and-Linux)"><a class="docs-heading-anchor" href="#[cargo](https://crates.io/crates/juliaup/)-(Windows,-Mac-and-Linux)"><a href="https://crates.io/crates/juliaup/">cargo</a> (Windows, Mac and Linux)</a><a id="[cargo](https://crates.io/crates/juliaup/)-(Windows,-Mac-and-Linux)-1"></a><a class="docs-heading-anchor-permalink" href="#[cargo](https://crates.io/crates/juliaup/)-(Windows,-Mac-and-Linux)" title="Permalink"></a></h3><p>To install Julia via Rust&#39;s cargo, run:</p><pre><code class="language-sh hljs">cargo install juliaup</code></pre></article><nav class="docs-footer"><a class="docs-footer-prevpage" href="getting-started.html">« Getting Started</a><a class="docs-footer-nextpage" href="variables.html">Variables »</a><div class="flexbox-break"></div><p class="footer-message">Powered by <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> and the <a href="https://julialang.org/">Julia Programming Language</a>.</p></nav></div><div class="modal" id="documenter-settings"><div class="modal-background"></div><div class="modal-card"><header class="modal-card-head"><p class="modal-card-title">Settings</p><button class="delete"></button></header><section class="modal-card-body"><p><label class="label">Theme</label><div class="select"><select id="documenter-themepicker"><option value="auto">Automatic (OS)</option><option value="documenter-light">documenter-light</option><option value="documenter-dark">documenter-dark</option><option value="catppuccin-latte">catppuccin-latte</option><option value="catppuccin-frappe">catppuccin-frappe</option><option value="catppuccin-macchiato">catppuccin-macchiato</option><option value="catppuccin-mocha">catppuccin-mocha</option></select></div></p><hr/><p>This document was generated with <a href="https://github.com/JuliaDocs/Documenter.jl">Documenter.jl</a> version 1.8.0 on <span class="colophon-date" title="Monday 14 April 2025 01:56">Monday 14 April 2025</span>. Using Julia version 1.11.5.</p></section><footer class="modal-card-foot"></footer></div></div></div></body></html>
