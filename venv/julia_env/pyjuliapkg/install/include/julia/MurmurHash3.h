//-----------------------------------------------------------------------------
// MurmurHash3 was written by <PERSON>, and is placed in the public
// domain. The author hereby disclaims copyright to this source code.

#ifndef JL_MURMURHASH3_H
#define JL_MURMURHASH3_H

//-----------------------------------------------------------------------------
// Platform-specific functions and macros
#include <stdint.h>
#include <stddef.h>
//-----------------------------------------------------------------------------

void MurmurHash3_x86_32  ( const void * key, size_t len, uint32_t seed, void * out );

void MurmurHash3_x86_128 ( const void * key, size_t len, uint32_t seed, void * out );

void MurmurHash3_x64_128 ( const void * key, size_t len, uint32_t seed, void * out );

//-----------------------------------------------------------------------------

#endif // MURMURHASH3_H
