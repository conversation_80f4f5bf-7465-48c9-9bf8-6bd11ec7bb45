#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的可运行版本 - 专注于解决当前问题
"""

import numpy as np
import pandas as pd
import pretty_midi
import glob
import os
import logging
import math
from typing import List, Dict, Tuple, Union, Optional, Any

# 设置警告过滤
import warnings
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')

# 尝试导入pywt
try:
    import pywt
    PYWT_AVAILABLE = True
except ImportError:
    PYWT_AVAILABLE = False



class WaveletMethod:
    """
    基于绝对不规则性的绝对不规则性分析方法
    
    核心改进：
    1. 使用RMS测量细节系数的绝对幅度
    2. 停止归一化到total_energy  
    3. d1_rms作为主要的"方法2"得分
    4. 提供长度归一化的绝对不规则性度量
    """
    
    def __init__(self, 
                 wavelet_type: str = 'haar',
                 max_decomp_levels: int = 2,
                 use_intervals: bool = True,
                 weighting_scheme: str = 'absolute_rms'):
        self.wavelet_type = wavelet_type
        self.max_decomp_levels = max_decomp_levels
        self.use_intervals = use_intervals
        self.weighting_scheme = weighting_scheme
        self.last_analysis = None
    
    def analyze_single_work_smoothness(self, pitch_series):
        """分析单个作品的平滑度（基于d1_rms的倒数）"""
        features = self.analyze_single_work_features(pitch_series)
        
        if 'error' in features:
            return 0.0
        
        # 使用d1_rms作为主要的不规则性度量
        d1_rms = features.get('d1_rms', 0.0)
        
        # 转换为平滑度：RMS越大，平滑度越低
        smoothness = 1.0 / (1.0 + d1_rms)
        
        return smoothness
    
    def get_method2_score(self, pitch_series):
        """获取方法2得分（d1_rms）用于与E(log i)直接比较"""
        features = self.analyze_single_work_features(pitch_series)
        
        if 'error' in features:
            return 0.0
        
        return features.get('d1_rms', 0.0)
    
    def analyze_single_work_features(self, pitch_series):
        """分析单个作品的绝对不规则性特征"""
        if len(pitch_series) < 4:
            return {'error': 'sequence_too_short'}
        
        try:
            # 准备分析序列
            if self.use_intervals:
                analysis_sequence = np.diff(pitch_series)
            else:
                analysis_sequence = np.array(pitch_series)
            
            if len(analysis_sequence) < 2:
                return {'error': 'analysis_sequence_too_short'}
            
            # 绝对不规则性分析
            wavelet_result = self._compute_absolute_irregularity_features(analysis_sequence)
            
            # 缓存分析结果
            self.last_analysis = wavelet_result
            
            return wavelet_result
            
        except Exception as e:
            return {'error': f'analysis_failed: {e}'}
    
    def _compute_absolute_irregularity_features(self, sequence):
        """计算绝对不规则性特征（核心实现）"""
        # 确定分解层数
        max_levels = min(
            self.max_decomp_levels,
            int(np.floor(np.log2(len(sequence)))) - 1
        )
        
        if max_levels < 1:
            return {'error': 'insufficient_data_for_decomposition'}
        
        return self._haar_absolute_features(sequence, max_levels)
    
    def _haar_absolute_features(self, sequence, max_levels):
        """使用简化Haar小波的绝对不规则性特征计算"""
        current_signal = sequence.copy()
        detail_rms = []
        detail_stats = []
        
        for level in range(1, max_levels + 1):
            if len(current_signal) < 2:
                break
            
            # 确保偶数长度
            n = len(current_signal)
            if n % 2 == 1:
                current_signal = current_signal[:-1]
                n = len(current_signal)
            
            if n < 2:
                break
            
            # Haar小波分解
            pairs = current_signal.reshape(-1, 2)
            sqrt2 = math.sqrt(2)
            
            # 近似和细节系数
            approximation = (pairs[:, 0] + pairs[:, 1]) / sqrt2
            details = (pairs[:, 0] - pairs[:, 1]) / sqrt2
            
            # 计算RMS：sqrt(mean(coeffs^2))
            rms = np.sqrt(np.mean(details ** 2))
            detail_rms.append(rms)
            
            # 详细统计
            stats = {
                'level': level,
                'length': len(details),
                'rms': float(rms),
                'energy': float(np.sum(details ** 2)),
                'mean': float(np.mean(details)),
                'std': float(np.std(details)),
                'max_abs': float(np.max(np.abs(details))),
                'variance': float(np.var(details))
            }
            detail_stats.append(stats)
            
            # 为下一层准备
            current_signal = approximation
        
        # 最终近似RMS
        approx_rms = np.sqrt(np.mean(current_signal ** 2))
        
        return self._compute_absolute_features(
            detail_rms, approx_rms, detail_stats, max_levels
        )
    
    def _compute_absolute_features(self, detail_rms, approx_rms, detail_stats, max_levels):
        """计算绝对不规则性特征（用户建议的新特征向量）"""
        
        if not detail_rms:
            return {'error': 'no_detail_rms'}
        
        # 基础特征
        features = {
            'sequence_type': 'intervals' if self.use_intervals else 'pitches',
            'wavelet_type': self.wavelet_type,
            'decomposition_levels': len(detail_rms),
            'approx_rms': float(approx_rms),
            'detail_rms_values': detail_rms,
            'detail_stats': detail_stats
        }
        
        # === 核心绝对不规则性特征 ===
        
        # 1. d1_rms (Primary Roughness Score) - 主要的方法2得分
        features['d1_rms'] = detail_rms[0] if len(detail_rms) > 0 else 0.0
        
        # 2. d2_rms (Secondary Roughness Score) - 次要粗糙度得分
        features['d2_rms'] = detail_rms[1] if len(detail_rms) > 1 else 0.0
        
        # 3. rms_ratio (Roughness Character) - 粗糙度特征比率
        if len(detail_rms) > 1 and detail_rms[1] > 0:
            features['rms_ratio'] = detail_rms[0] / detail_rms[1]
        else:
            features['rms_ratio'] = float('inf') if detail_rms[0] > 0 else 0.0
        
        # 4. 总体RMS（反向加权）
        if len(detail_rms) > 1:
            weights = [1.0 / (i + 1) for i in range(len(detail_rms))]
            total_weight = sum(weights)
            weighted_rms = sum(w * rms for w, rms in zip(weights, detail_rms)) / total_weight
        else:
            weighted_rms = detail_rms[0]
        
        features['weighted_rms'] = weighted_rms
        
        # 5. RMS对比度（细节vs近似）
        if approx_rms > 0:
            features['detail_to_approx_rms_ratio'] = detail_rms[0] / approx_rms
        else:
            features['detail_to_approx_rms_ratio'] = float('inf') if detail_rms[0] > 0 else 0.0
        
        # 6. 多层级RMS特征
        features['max_detail_rms'] = max(detail_rms) if detail_rms else 0.0
        features['min_detail_rms'] = min(detail_rms) if detail_rms else 0.0
        features['rms_range'] = features['max_detail_rms'] - features['min_detail_rms']
        
        return features
    
    def get_analysis_details(self):
        """获取最后一次分析的详细信息"""
        return self.last_analysis or {}




# === 双方法家族分析系统 ===

class MethodFamilyA:
    """
    方法家族A：基于音程的指标（分析性处理的代理）
    专注于量化窗口内音程的大小和分布，测量局部波动性或"激动"程度
    """
    
    @staticmethod
    def mean_absolute_interval(i1: float, i2: float) -> float:
        """平均绝对音程：(|I1| + |I2|) / 2"""
        return (abs(i1) + abs(i2)) / 2
    
    @staticmethod
    def max_absolute_interval(i1: float, i2: float) -> float:
        """最大绝对音程：max(|I1|, |I2|)"""
        return max(abs(i1), abs(i2))
    
    @staticmethod
    def local_intervallic_rms(i1: float, i2: float) -> float:
        """
        局部音程RMS：sqrt((I1^2 + I2^2) / 2)
        优越的局部激动度量，对较大跳动给予更高权重
        """
        return math.sqrt((i1**2 + i2**2) / 2)
    
    @staticmethod
    def analyze_three_note_group(note1: float, note2: float, note3: float) -> Dict[str, float]:
        """分析单个三音组的所有家族A指标"""
        i1 = note2 - note1
        i2 = note3 - note2
        
        return {
            'mean_abs_interval': MethodFamilyA.mean_absolute_interval(i1, i2),
            'max_abs_interval': MethodFamilyA.max_absolute_interval(i1, i2),
            'local_rms': MethodFamilyA.local_intervallic_rms(i1, i2),
            'intervals': [i1, i2]
        }


class MethodFamilyB:
    """
    方法家族B：基于轮廓的指标（整体性处理的代理）
    专注于量化旋律线条的形状和方向变化，测量"曲折度"或"锯齿度"
    """
    
    @staticmethod
    def directional_change(i1: float, i2: float) -> int:
        """
        方向变化：二元指标
        如果同向返回0，如果方向改变返回1
        """
        if i1 == 0 or i2 == 0:
            return 0  # 处理平音的情况
        return 0 if (i1 > 0) == (i2 > 0) else 1
    
    @staticmethod
    def angle_of_inflection(i1: float, i2: float) -> float:
        """
        拐点角度：计算两个音程向量之间的夹角
        v1 = (1, I1), v2 = (1, I2)
        返回角度（度数）
        """
        # 向量表示：(时间步长=1, 音程值)
        v1 = np.array([1, i1])
        v2 = np.array([1, i2])
        
        # 计算夹角
        cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
        
        # 防止数值误差导致的域外值
        cos_angle = np.clip(cos_angle, -1.0, 1.0)
        
        # 转换为度数
        angle_rad = np.arccos(cos_angle)
        angle_deg = np.degrees(angle_rad)
        
        return angle_deg
    
    @staticmethod
    def contour_complexity(i1: float, i2: float) -> float:
        """
        轮廓复杂度：结合方向变化和角度的综合指标
        """
        direction_change = MethodFamilyB.directional_change(i1, i2)
        angle = MethodFamilyB.angle_of_inflection(i1, i2)
        
        # 如果有方向变化，角度越小（转折越急）复杂度越高
        if direction_change == 1:
            # 将角度转换为复杂度：90度最复杂，180度最简单
            complexity = 1.0 - (angle - 90) / 90 if angle >= 90 else 1.0
        else:
            # 无方向变化时复杂度较低
            complexity = 0.1
        
        return max(0.0, min(1.0, complexity))
    
    @staticmethod
    def analyze_three_note_group(note1: float, note2: float, note3: float) -> Dict[str, float]:
        """分析单个三音组的所有家族B指标"""
        i1 = note2 - note1
        i2 = note3 - note2
        
        return {
            'directional_change': MethodFamilyB.directional_change(i1, i2),
            'angle_of_inflection': MethodFamilyB.angle_of_inflection(i1, i2),
            'contour_complexity': MethodFamilyB.contour_complexity(i1, i2),
            'intervals': [i1, i2]
        }


class DualMethodAnalyzer:
    """双方法分析器（替代原来的一致性比较）"""
    
    def __init__(self):
        pass
        
    def analyze_melody_dual_methods(self, pitch_series):
        """
        分析整个旋律的两个方法家族指标
        
        Returns:
            (family_a_results, family_b_results): 两个结果列表
        """
        if len(pitch_series) < 3:
            return [], []
        
        family_a_results = []
        family_b_results = []
        
        # 滑动窗口分析每个三音组
        for i in range(len(pitch_series) - 2):
            note1, note2, note3 = pitch_series[i], pitch_series[i+1], pitch_series[i+2]
            
            # 分析家族A指标
            a_metrics = MethodFamilyA.analyze_three_note_group(note1, note2, note3)
            a_metrics['position'] = i
            family_a_results.append(a_metrics)
            
            # 分析家族B指标
            b_metrics = MethodFamilyB.analyze_three_note_group(note1, note2, note3)
            b_metrics['position'] = i
            family_b_results.append(b_metrics)
        
        return family_a_results, family_b_results
    
    def compute_orthogonality_analysis(self, family_a_results, family_b_results):
        """计算正交性分析"""
        
        if not family_a_results or not family_b_results:
            return {'error': 'insufficient_data'}
        
        # 提取关键指标
        a_rms = [result['local_rms'] for result in family_a_results]
        b_complexity = [result['contour_complexity'] for result in family_b_results]
        
        # 计算相关性（处理NaN情况）
        try:
            correlation_matrix = np.corrcoef(a_rms, b_complexity)
            if np.isnan(correlation_matrix).any():
                correlation = 0.0
            else:
                correlation = correlation_matrix[0, 1]
        except:
            correlation = 0.0
        
        # 正交性评估
        if abs(correlation) < 0.3:
            orthogonality_assessment = "高度正交"
        elif abs(correlation) < 0.6:
            orthogonality_assessment = "中度相关"
        else:
            orthogonality_assessment = "强相关"
        
        return {
            'correlation': correlation,
            'orthogonality_assessment': orthogonality_assessment,
            'a_rms_stats': {
                'mean': np.mean(a_rms),
                'std': np.std(a_rms),
                'min': np.min(a_rms),
                'max': np.max(a_rms)
            },
            'b_complexity_stats': {
                'mean': np.mean(b_complexity),
                'std': np.std(b_complexity),
                'min': np.min(b_complexity),
                'max': np.max(b_complexity)
            },
            'direction_changes': sum(result['directional_change'] for result in family_b_results),
            'total_groups': len(family_b_results)
        }



class EnhancedChineseMusicAnalyzer:
    """增强版中国传统音乐分析器"""
    
    def __init__(self, epsilon=1e-6, min_groups_for_analysis=10):
        self.epsilon = epsilon
        self.min_groups_for_analysis = min_groups_for_analysis
        self.pitch_series = None
        self.indexed_diffs = []
        self.three_note_groups = []
        self.strict_groups = []
        self.non_strict_groups = []
        self.swave_curve = []
        self.holder_indices = []
        self.wavelet_energies = []
        
        # 添加改进的小波方法
        self.wavelet_method = WaveletMethod(
            wavelet_type='haar',
            max_decomp_levels=2,  # 聚焦1-2层，适合三音组分析
            use_intervals=True,
            weighting_scheme='absolute_rms'  # 绝对不规则性度量
        )
        
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 添加双方法分析器
        self.dual_method_analyzer = DualMethodAnalyzer()
    
    def get_midi_files(self, folder_path: str) -> List[str]:
        """从文件夹中检索所有 MIDI 文件路径"""
        return glob.glob(os.path.join(folder_path, '*.mid'))
    
    def load_and_preprocess(self, midi_file_path: Union[str, List[str]]) -> Tuple[pd.Series, List[Tuple]]:
        """增强版MIDI加载和预处理"""
        self.three_note_groups = []
        self.strict_groups = []
        self.non_strict_groups = []
        self.swave_curve = []
        self.holder_indices = []
        self.wavelet_energies = []
        
        if os.path.isdir(midi_file_path):
            midi_file_path = self.get_midi_files(midi_file_path)
        elif isinstance(midi_file_path, str):
            midi_file_path = [midi_file_path]
        
        all_pitches = []
        all_indices = []
        current_index_offset = 0
        
        for single_path in midi_file_path:
            try:
                midi_data = pretty_midi.PrettyMIDI(single_path)
            except Exception as e:
                self.logger.error(f"处理文件 {single_path} 出错: {e}")
                continue
            
            melody_track = self._extract_melody_track(midi_data)
            if melody_track is None:
                self.logger.error(f"文件 {single_path} 未找到有效的旋律轨道")
                continue
            
            pitches = [note.pitch for note in melody_track.notes]
            pitch_series_part = pd.Series(pitches, name='pitch').astype(float)
            
            diffs = pitch_series_part.diff().dropna()
            valid_mask = (diffs != 0)
            valid_indices = diffs[valid_mask].index.tolist()
            signed_diffs = diffs[valid_mask].tolist()
            
            self.logger.info(f"[{single_path}] 加载了 {len(pitch_series_part)} 个音符")
            self.logger.info(f"[{single_path}] 有效音程数: {len(signed_diffs)}")
            
            all_pitches.extend(pitch_series_part.tolist())
            for idx, diff in zip(valid_indices, signed_diffs):
                global_idx = current_index_offset + idx
                all_indices.append((global_idx, diff))
            current_index_offset += len(pitch_series_part)
        
        self.pitch_series = pd.Series(all_pitches, name='pitch').astype(float)
        self.indexed_diffs = all_indices
        return self.pitch_series, all_indices
    
    def _extract_melody_track(self, midi_data):
        """提取主旋律轨道"""
        melody_track = None
        for instrument in midi_data.instruments:
            if not instrument.is_drum and len(instrument.notes) > 0:
                melody_track = instrument
                break
        return melody_track
    
    def identify_three_note_groups(self) -> List[Dict]:
        """识别和分类三音组"""
        if self.pitch_series is None or len(self.pitch_series) < 3:
            self.logger.error("音高序列不足，无法分析三音组")
            return []
        
        self.three_note_groups = []
        self.strict_groups = []
        self.non_strict_groups = []
        
        groups = []
        pitches = self.pitch_series.values
        
        for i in range(len(pitches) - 2):
            note1, note2, note3 = pitches[i], pitches[i + 1], pitches[i + 2]
            
            interval1 = note2 - note1
            interval2 = note3 - note2
            
            if interval1 == 0 and interval2 == 0:
                continue
            
            group = {
                'index': i,
                'notes': [note1, note2, note3],
                'intervals': [interval1, interval2],
                'is_strict': self._is_strict_three_note_group(interval1, interval2),
                'dynamic_range': max(note1, note2, note3) - min(note1, note2, note3),
                'total_displacement': abs(note3 - note1),
                'direction_changes': 1 if interval1 * interval2 < 0 else 0,
                'complexity': abs(interval1) + abs(interval2)
            }
            
            groups.append(group)
            
            if group['is_strict']:
                self.strict_groups.append(group)
            else:
                self.non_strict_groups.append(group)
        
        self.three_note_groups = groups
        self.logger.info(f"识别出 {len(groups)} 个三音组，其中严格定义 {len(self.strict_groups)} 个")
        
        return groups
    
    def _is_strict_three_note_group(self, interval1: float, interval2: float) -> bool:
        """判断是否为严格定义的三音组（方向相反）"""
        return (interval1 != 0 and interval2 != 0 and
                ((interval1 > 0 and interval2 < 0) or (interval1 < 0 and interval2 > 0)))
    
    def analyze_per_work_smoothness(self, midi_file_path: Union[str, List[str]]) -> Dict:
        """按作品分别分析平滑度收敛"""
        print("\n🔧 修正研究方法：按作品分别分析平滑度收敛")
        print("=" * 60)
        
        if os.path.isdir(midi_file_path):
            midi_file_path = self.get_midi_files(midi_file_path)
        elif isinstance(midi_file_path, str):
            midi_file_path = [midi_file_path]
        
        per_work_results = []
        failed_files = []
        
        for i, single_path in enumerate(midi_file_path):
            print(f"\n📋 分析第 {i + 1}/{len(midi_file_path)} 首: {os.path.basename(single_path)}")
            
            try:
                self._reset_analyzer_state()
                pitch_series, indexed_diffs = self._load_single_file(single_path)
                
                if pitch_series is None or len(pitch_series) < 10:
                    print(f"   ❌ 文件无效或音符过少")
                    failed_files.append(single_path)
                    continue
                
                work_result = self._calculate_single_work_smoothness(single_path)
                work_result['file_path'] = single_path
                work_result['file_name'] = os.path.basename(single_path)
                
                per_work_results.append(work_result)
                print(f"   ✅ 分析完成")
                
            except Exception as e:
                print(f"   ❌ 分析失败: {e}")
                failed_files.append(single_path)
                continue
        
        if per_work_results:
            summary = self._summarize_per_work_results(per_work_results)
            summary['failed_files'] = failed_files
            summary['total_files'] = len(midi_file_path)
            summary['successful_files'] = len(per_work_results)
            
            self.print_smoothness_summary(summary)
            
            print(f"\n📊 总体分析完成:")
            print(f"   成功: {len(per_work_results)} 首")
            print(f"   失败: {len(failed_files)} 首")
            
            return {
                'per_work_results': per_work_results,
                'failed_files': failed_files,
                'summary_statistics': summary
            }
        else:
            print(f"\n❌ 没有成功分析任何文件")
            return {'error': '没有成功分析任何文件', 'failed_files': failed_files}
    
    def _reset_analyzer_state(self):
        """重置分析器状态"""
        self.pitch_series = None
        self.indexed_diffs = []
        self.three_note_groups = []
        self.strict_groups = []
        self.non_strict_groups = []
    
    def _load_single_file(self, file_path: str) -> Tuple[pd.Series, List[Tuple]]:
        """加载单个文件"""
        return self.load_and_preprocess(file_path)
    
    def _calculate_single_work_smoothness(self, file_path: str) -> Dict:
        """计算单个作品的平滑度"""
        groups = self.identify_three_note_groups()
        
        if len(self.strict_groups) < 3:
            return {'error': '严格三音组数量不足'}
        
        method1_result = self._method1_single_work_smoothness()
        method2_result = self._method2_single_work_smoothness(self.pitch_series.values)
        
        # 使用双方法分析替代一致性比较
        dual_analysis = self._analyze_dual_methods(self.pitch_series.values)
        consistency = dual_analysis.get('orthogonality_assessment', 'unknown') == '高度正交' 
        
        print(f"   ✅ 成功分析: {len(self.strict_groups)} 个严格三音组")
        print(f"      方法1 E(log i): {method1_result['smoothness']:.4f}")
        print(f"      方法2 d1_rms: {method2_result['average_holder']:.4f}")
        print(f"      双方法分析: {dual_analysis.get('orthogonality_assessment', 'unknown')}")
        print(f"      相关系数: {dual_analysis.get('correlation', 0.0):.4f}")
        
        return {
            'file_path': file_path,
            'total_groups': len(groups),
            'strict_groups': len(self.strict_groups),
            'method1_smoothness': method1_result['smoothness'],
            'method2_smoothness': method2_result['average_holder'],
            'consistency': consistency,
            'method1_details': method1_result,
            'method2_details': method2_result,
            'dual_method_analysis': dual_analysis
        }
    
    def _method1_single_work_smoothness(self) -> Dict:
        """方法1: E(log i) 计算"""
        interval_logs = []
        
        for group in self.strict_groups:
            interval1, interval2 = group['intervals']
            if abs(interval1) > 0:
                interval_logs.append(np.log(abs(interval1)))
            if abs(interval2) > 0:
                interval_logs.append(np.log(abs(interval2)))
        
        smoothness = np.mean(interval_logs) if interval_logs else 0.0
        
        return {
            'smoothness': smoothness,
            'converged': True,
            'total_intervals': len(interval_logs),
            'interval_range': f"{np.min(interval_logs):.3f}~{np.max(interval_logs):.3f}" if interval_logs else "0.000~0.000",
            'interval_std': np.std(interval_logs) if len(interval_logs) > 1 else 0.0
        }
    
    def _method2_single_work_smoothness(self, pitch_series):
        """方法2: 基于绝对不规则性绝对不规则性分析"""
        try:
            # 获取d1_rms作为主要的方法2得分
            d1_rms = self.wavelet_method.get_method2_score(pitch_series)
            smoothness = self.wavelet_method.analyze_single_work_smoothness(pitch_series)
            
            # 获取详细特征
            features = self.wavelet_method.analyze_single_work_features(pitch_series)
            
            return {
                'average_holder': d1_rms,  # 现在返回d1_rms而不是平滑度
                'smoothness': smoothness,  # 额外提供平滑度
                'converged': True,
                'total_groups': len(pitch_series) - 2 if len(pitch_series) > 2 else 0,
                'holder_range': f"{d1_rms:.3f}~{d1_rms:.3f}",
                'holder_std': 0.0,
                'd1_rms': d1_rms,
                'd2_rms': features.get('d2_rms', 0.0) if 'error' not in features else 0.0,
                'rms_ratio': features.get('rms_ratio', 0.0) if 'error' not in features else 0.0
            }
        except Exception as e:
            print(f"绝对不规则性绝对不规则性分析出错: {e}")
            return {
                'average_holder': 0.0,
                'smoothness': 0.0,
                'converged': False,
                'total_groups': 0,
                'holder_range': "0.000~0.000",
                'holder_std': 0.0,
                'd1_rms': 0.0,
                'd2_rms': 0.0,
                'rms_ratio': 0.0
            }
    
    def _summarize_per_work_results(self, per_work_results: List[Dict]) -> Dict:
        """汇总所有作品的结果"""
        method1_values = [r['method1_smoothness'] for r in per_work_results]
        method2_values = [r['method2_smoothness'] for r in per_work_results]
        consistency_count = sum(1 for r in per_work_results if r.get('dual_method_analysis', {}).get('orthogonality_assessment') == '高度正交')
        
        return {
            'total_works': len(per_work_results),
            'consistency_percentage': (consistency_count / len(per_work_results)) * 100,
            'method1_stats': {
                'mean': np.mean(method1_values),
                'std': np.std(method1_values),
                'min': np.min(method1_values),
                'max': np.max(method1_values),
                'median': np.median(method1_values)
            },
            'method2_stats': {
                'mean': np.mean(method2_values),
                'std': np.std(method2_values),
                'min': np.min(method2_values),
                'max': np.max(method2_values),
                'median': np.median(method2_values)
            }
        }
    

    def _analyze_dual_methods(self, pitch_series):
        """分析双方法家族"""
        try:
            family_a_results, family_b_results = self.dual_method_analyzer.analyze_melody_dual_methods(pitch_series)
            orthogonality = self.dual_method_analyzer.compute_orthogonality_analysis(family_a_results, family_b_results)
            
            return {
                'family_a_results': family_a_results,
                'family_b_results': family_b_results,
                'orthogonality_assessment': orthogonality.get('orthogonality_assessment', 'unknown'),
                'correlation': orthogonality.get('correlation', 0.0),
                'a_rms_mean': orthogonality.get('a_rms_stats', {}).get('mean', 0.0),
                'b_complexity_mean': orthogonality.get('b_complexity_stats', {}).get('mean', 0.0),
                'direction_changes': orthogonality.get('direction_changes', 0),
                'total_groups': orthogonality.get('total_groups', 0)
            }
        except Exception as e:
            self.logger.error(f"双方法分析失败: {e}")
            return {'error': str(e)}

    def print_smoothness_summary(self, results: Dict):
        """打印平滑度分析摘要"""
        print("\n" + "=" * 60)
        print("🎼 平滑度收敛分析摘要报告")
        print("=" * 60)
        print(f"📊 基础统计:")
        print(f"   分析作品数: {results['total_works']} 首")
        print(f"   双方法正交性: {results['consistency_percentage']:.1f}%")
        
        print(f"\n📈 方法1 (E(log i)) 统计:")
        m1 = results['method1_stats']
        print(f"   均值: {m1['mean']:.4f}")
        print(f"   标准差: {m1['std']:.4f}")
        print(f"   范围: {m1['min']:.4f} ~ {m1['max']:.4f}")
        print(f"   中位数: {m1['median']:.4f}")
        
        print(f"\n📈 方法2 (绝对不规则性分析) 统计:")
        m2 = results['method2_stats']
        print(f"   均值: {m2['mean']:.4f}")
        print(f"   标准差: {m2['std']:.4f}")
        print(f"   范围: {m2['min']:.4f} ~ {m2['max']:.4f}")
        print(f"   中位数: {m2['median']:.4f}")
        
        print(f"\n✅ 核心结论:")
        if results['consistency_percentage'] > 70:
            print(f"   ✅ 两个方法家族高度正交")
        elif results['consistency_percentage'] > 40:
            print(f"   ⚠️ 两个方法家族中度相关")
        else:
            print(f"   ⚠️ 平滑度收敛特征需要进一步验证")
            print(f"   ⚠️ 两个方法家族强相关，需要进一步分析")

def main():
    """主函数"""
    print("🎼 第二个数学特征验证：平滑度收敛")
    print("=" * 80)
    
    analyzer = EnhancedChineseMusicAnalyzer()
    
    # 分析MIDI文件
    results = analyzer.analyze_per_work_smoothness('/Users/<USER>/Desktop/AI音乐/midi_files')
    
    print(f"\n🎼 第二个数学特征验证完成！")
    print(f"下一步：进行第三个数学特征（熵最大化）的验证")

if __name__ == "__main__":
    main()
