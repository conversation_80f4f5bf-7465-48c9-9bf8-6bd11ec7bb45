#!/usr/bin/env python3
"""
测试全音作为距离单位的效果
对比半音单位和全音单位在对齐度计算中的差异
"""

import numpy as np
import sys
sys.path.append('.')

def compare_distance_units():
    """对比半音单位和全音单位的效果"""
    print("🎵 距离单位对比测试：半音 vs 全音")
    print("="*80)
    
    # 测试不同的音程距离
    test_cases = [
        {"name": "同音", "semitones": 0, "musical_meaning": "完全重合"},
        {"name": "小二度", "semitones": 1, "musical_meaning": "半音邻近，导音关系"},
        {"name": "大二度", "semitones": 2, "musical_meaning": "全音邻近，级进关系"},
        {"name": "小三度", "semitones": 3, "musical_meaning": "小三度，和谐音程"},
        {"name": "大三度", "semitones": 4, "musical_meaning": "大三度，和谐音程"},
        {"name": "纯四度", "semitones": 5, "musical_meaning": "纯四度，稳定音程"},
        {"name": "三全音", "semitones": 6, "musical_meaning": "三全音，不稳定音程"},
        {"name": "纯五度", "semitones": 7, "musical_meaning": "纯五度，最稳定音程"},
        {"name": "小六度", "semitones": 8, "musical_meaning": "小六度"},
        {"name": "大六度", "semitones": 9, "musical_meaning": "大六度"},
        {"name": "小七度", "semitones": 10, "musical_meaning": "小七度"},
        {"name": "大七度", "semitones": 11, "musical_meaning": "大七度，强烈解决倾向"},
        {"name": "八度", "semitones": 12, "musical_meaning": "八度，同名音"}
    ]
    
    print("\n📊 对齐度计算对比表")
    print("-" * 100)
    print(f"{'音程':<8} {'半音数':<6} {'全音数':<8} {'半音单位对齐度':<12} {'全音单位对齐度':<12} {'差异':<8} {'音乐意义'}")
    print("-" * 100)
    
    results = []
    
    for case in test_cases:
        semitones = case["semitones"]
        whole_tones = semitones / 2.0  # 1全音 = 2半音
        
        # 计算两种单位下的对齐度
        alignment_semitone = 1.0 / (1.0 + semitones) if semitones > 0 else 1.0
        alignment_whole_tone = 1.0 / (1.0 + whole_tones) if whole_tones > 0 else 1.0
        
        difference = abs(alignment_semitone - alignment_whole_tone)
        
        print(f"{case['name']:<8} {semitones:<6} {whole_tones:<8.1f} {alignment_semitone:<12.3f} {alignment_whole_tone:<12.3f} {difference:<8.3f} {case['musical_meaning']}")
        
        results.append({
            'name': case['name'],
            'semitones': semitones,
            'whole_tones': whole_tones,
            'alignment_semitone': alignment_semitone,
            'alignment_whole_tone': alignment_whole_tone,
            'difference': difference,
            'musical_meaning': case['musical_meaning']
        })
    
    return results

def analyze_unit_effects(results):
    """分析不同单位的效果"""
    print(f"\n📈 单位效果分析")
    print("="*60)
    
    # 1. 数值范围分析
    semitone_alignments = [r['alignment_semitone'] for r in results if r['semitones'] > 0]
    whole_tone_alignments = [r['alignment_whole_tone'] for r in results if r['whole_tones'] > 0]
    
    print(f"\n1. 📊 数值范围对比:")
    print(f"   半音单位对齐度范围: {min(semitone_alignments):.3f} - {max(semitone_alignments):.3f}")
    print(f"   全音单位对齐度范围: {min(whole_tone_alignments):.3f} - {max(whole_tone_alignments):.3f}")
    print(f"   半音单位标准差: {np.std(semitone_alignments):.3f}")
    print(f"   全音单位标准差: {np.std(whole_tone_alignments):.3f}")
    
    # 2. 区分度分析
    print(f"\n2. 🎯 区分度分析:")
    
    # 计算相邻音程的对齐度差异
    semitone_diffs = []
    whole_tone_diffs = []
    
    for i in range(1, len(results)):
        if results[i]['semitones'] > 0 and results[i-1]['semitones'] >= 0:
            semitone_diff = results[i-1]['alignment_semitone'] - results[i]['alignment_semitone']
            whole_tone_diff = results[i-1]['alignment_whole_tone'] - results[i]['alignment_whole_tone']
            semitone_diffs.append(semitone_diff)
            whole_tone_diffs.append(whole_tone_diff)
    
    print(f"   半音单位平均区分度: {np.mean(semitone_diffs):.3f}")
    print(f"   全音单位平均区分度: {np.mean(whole_tone_diffs):.3f}")
    
    # 3. 音乐意义分析
    print(f"\n3. 🎼 音乐意义分析:")
    
    # 分析关键音程的对齐度
    key_intervals = {
        '小二度': 1,
        '大二度': 2, 
        '大三度': 4,
        '纯五度': 7,
        '八度': 12
    }
    
    for interval_name, semitones in key_intervals.items():
        result = next(r for r in results if r['semitones'] == semitones)
        print(f"   {interval_name}:")
        print(f"     半音单位: {result['alignment_semitone']:.3f}")
        print(f"     全音单位: {result['alignment_whole_tone']:.3f}")
        print(f"     差异: {result['difference']:.3f}")

def analyze_threshold_effects():
    """分析阈值定义的效果"""
    print(f"\n4. 📏 阈值定义效果分析")
    print("="*60)
    
    # 使用原有的阈值定义
    thresholds = {
        'strong': 0.7,
        'moderate_high': 0.3,
        'moderate_low': 0.3,
        'weak': 0.0
    }
    
    print(f"\n使用相同阈值 (强关联>0.7, 中等关联0.3-0.7, 弱关联<0.3):")
    print(f"{'音程':<8} {'半音单位':<12} {'全音单位':<12} {'半音分类':<12} {'全音分类'}")
    print("-" * 60)
    
    test_intervals = [1, 2, 3, 4, 5, 7, 12]  # 关键音程
    
    for semitones in test_intervals:
        whole_tones = semitones / 2.0
        
        alignment_semitone = 1.0 / (1.0 + semitones)
        alignment_whole_tone = 1.0 / (1.0 + whole_tones)
        
        # 分类
        def classify_alignment(alignment):
            if alignment >= 0.7:
                return "强关联"
            elif alignment >= 0.3:
                return "中等关联"
            else:
                return "弱关联"
        
        semitone_class = classify_alignment(alignment_semitone)
        whole_tone_class = classify_alignment(alignment_whole_tone)
        
        interval_name = {1: "小二度", 2: "大二度", 3: "小三度", 4: "大三度", 
                        5: "纯四度", 7: "纯五度", 12: "八度"}[semitones]
        
        print(f"{interval_name:<8} {alignment_semitone:<12.3f} {alignment_whole_tone:<12.3f} {semitone_class:<12} {whole_tone_class}")

def test_real_music_example():
    """测试真实音乐例子"""
    print(f"\n5. 🎼 真实音乐例子测试")
    print("="*60)
    
    # 模拟一个简单的旋律片段：C-D-E-F-G (大调音阶片段)
    # 假设吸引子在C (60)
    melody_notes = [60, 62, 64, 65, 67]  # C, D, E, F, G
    attractor_position = 60  # C
    
    print(f"\n测试旋律: C-D-E-F-G")
    print(f"吸引子位置: C (MIDI 60)")
    print(f"\n{'音符':<6} {'MIDI':<6} {'距离(半音)':<10} {'距离(全音)':<10} {'半音对齐度':<10} {'全音对齐度':<10}")
    print("-" * 70)
    
    total_alignment_semitone = 0
    total_alignment_whole_tone = 0
    
    note_names = ['C', 'D', 'E', 'F', 'G']
    
    for i, note in enumerate(melody_notes):
        distance_semitones = abs(note - attractor_position)
        distance_whole_tones = distance_semitones / 2.0
        
        alignment_semitone = 1.0 / (1.0 + distance_semitones) if distance_semitones > 0 else 1.0
        alignment_whole_tone = 1.0 / (1.0 + distance_whole_tones) if distance_whole_tones > 0 else 1.0
        
        total_alignment_semitone += alignment_semitone
        total_alignment_whole_tone += alignment_whole_tone
        
        print(f"{note_names[i]:<6} {note:<6} {distance_semitones:<10} {distance_whole_tones:<10.1f} {alignment_semitone:<10.3f} {alignment_whole_tone:<10.3f}")
    
    avg_alignment_semitone = total_alignment_semitone / len(melody_notes)
    avg_alignment_whole_tone = total_alignment_whole_tone / len(melody_notes)
    
    print(f"\n平均对齐度:")
    print(f"   半音单位: {avg_alignment_semitone:.3f}")
    print(f"   全音单位: {avg_alignment_whole_tone:.3f}")
    print(f"   差异: {abs(avg_alignment_semitone - avg_alignment_whole_tone):.3f}")

def conclusion_analysis():
    """结论分析"""
    print(f"\n6. 🎯 结论分析")
    print("="*60)
    
    print(f"\n📊 全音单位的优势:")
    print(f"   ✅ 更大的数值范围：对齐度分布更分散")
    print(f"   ✅ 更好的区分度：相邻音程差异更明显")
    print(f"   ✅ 符合音乐直觉：全音是音乐理论中的重要单位")
    print(f"   ✅ 减少敏感性：对小距离变化不过于敏感")
    
    print(f"\n⚠️ 全音单位的潜在问题:")
    print(f"   ⚠️ 半音信息丢失：无法区分半音和全音的差异")
    print(f"   ⚠️ 小数距离：很多距离变成小数（如1.5全音）")
    print(f"   ⚠️ 计算复杂性：需要除以2的转换")
    print(f"   ⚠️ 标准不一致：MIDI和音乐软件都使用半音")
    
    print(f"\n🎼 音乐理论考虑:")
    print(f"   • 全音确实是重要的音乐单位（全音音阶、全音关系）")
    print(f"   • 但半音关系在和声功能中更基础（导音、变化音）")
    print(f"   • 现代音乐分析更多基于半音系统")
    
    print(f"\n📈 建议:")
    print(f"   1. 可以尝试全音单位作为替代方案")
    print(f"   2. 需要重新定义阈值以适应新的数值范围")
    print(f"   3. 在论文中可以提及两种单位的对比")
    print(f"   4. 最终选择应基于实际音乐分析效果")

if __name__ == "__main__":
    print("🧪 测试全音作为距离单位的效果")
    
    # 1. 对比测试
    results = compare_distance_units()
    
    # 2. 效果分析
    analyze_unit_effects(results)
    
    # 3. 阈值效果
    analyze_threshold_effects()
    
    # 4. 真实例子
    test_real_music_example()
    
    # 5. 结论
    conclusion_analysis()
    
    print(f"\n🎉 全音单位测试完成！")
    print(f"📝 可以根据这些结果决定是否采用全音单位")
    print(f"🎼 两种单位各有优劣，需要根据具体应用场景选择")
