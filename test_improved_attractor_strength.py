#!/usr/bin/env python3
"""
测试改进的吸引子强度计算
验证标准化引力强度的效果和理论依据
"""

import sys
import os
import numpy as np

# 添加当前目录到路径
sys.path.append('.')

def test_improved_attractor_strength():
    """测试改进的吸引子强度计算"""
    print("💪 测试改进的吸引子强度计算")
    print("标准化引力强度，单位：全音/个数")
    print("="*80)
    
    try:
        # 导入修正后的统一分析器
        from unified_topological_analysis import UnifiedTopologicalAnalyzer
        
        # 创建分析器
        analyzer = UnifiedTopologicalAnalyzer()
        
        print("✅ 统一拓扑分析器创建成功")
        
        # 测试不同的吸引子配置
        test_cases = [
            {
                'name': '单一强吸引子',
                'attractors': [(60, 0.8), (67, 0.2)],
                'expected_category': '高强度',
                'description': '一个主导吸引子，权重集中'
            },
            {
                'name': '多个平衡吸引子',
                'attractors': [(60, 0.25), (64, 0.25), (67, 0.25), (72, 0.25)],
                'expected_category': '中等强度',
                'description': '四个平衡的吸引子，权重分散'
            },
            {
                'name': '分散弱吸引子',
                'attractors': [(60, 0.15), (65, 0.15), (70, 0.15), (75, 0.15), (80, 0.15), (85, 0.25)],
                'expected_category': '低强度',
                'description': '六个分散的吸引子，覆盖范围大'
            },
            {
                'name': '紧密强吸引子',
                'attractors': [(60, 0.6), (62, 0.4)],
                'expected_category': '高强度',
                'description': '两个紧密的强吸引子'
            },
            {
                'name': '极端分散型',
                'attractors': [(48, 0.2), (60, 0.2), (72, 0.2), (84, 0.2), (96, 0.2)],
                'expected_category': '低强度',
                'description': '五个吸引子跨越四个八度'
            }
        ]
        
        print(f"\n🧪 测试不同吸引子配置的强度计算:")
        print(f"{'配置名称':<15} {'改进强度':<12} {'单位':<15} {'分类':<10} {'预期':<10} {'验证'}")
        print("-" * 85)
        
        results = []
        
        for case in test_cases:
            attractors = case['attractors']
            
            # 计算改进的吸引子强度
            improved_strength = analyzer.calculate_improved_attractor_strength(attractors)
            
            # 分类强度
            if improved_strength >= 10.0:
                actual_category = "高强度"
            elif improved_strength >= 2.0:
                actual_category = "中等强度"
            else:
                actual_category = "低强度"
            
            # 验证是否符合预期
            validation = "✅" if actual_category == case['expected_category'] else "⚠️"
            
            print(f"{case['name']:<15} {improved_strength:<12.4f} {'全音/个数':<15} {actual_category:<10} {case['expected_category']:<10} {validation}")
            
            results.append({
                'name': case['name'],
                'attractors': attractors,
                'improved_strength': improved_strength,
                'actual_category': actual_category,
                'expected_category': case['expected_category'],
                'validation_passed': actual_category == case['expected_category'],
                'description': case['description']
            })
        
        # 分析测试结果
        print(f"\n📊 测试结果分析:")
        
        passed_tests = sum(1 for r in results if r['validation_passed'])
        total_tests = len(results)
        accuracy = passed_tests / total_tests * 100
        
        print(f"   验证通过: {passed_tests}/{total_tests} ({accuracy:.1f}%)")
        
        # 强度范围分析
        strengths = [r['improved_strength'] for r in results]
        print(f"   强度范围: {min(strengths):.4f} - {max(strengths):.4f} 全音/个数")
        print(f"   平均强度: {np.mean(strengths):.4f}")
        print(f"   标准差: {np.std(strengths):.4f}")
        
        # 详细分析每个案例
        print(f"\n🔍 详细案例分析:")
        
        for r in results:
            print(f"\n   {r['name']}:")
            print(f"     描述: {r['description']}")
            print(f"     吸引子配置: {r['attractors']}")
            print(f"     改进强度: {r['improved_strength']:.4f} 全音/个数")
            print(f"     分类: {r['actual_category']} (预期: {r['expected_category']})")
            
            # 计算组件分析
            positions = [pos for pos, weight in r['attractors']]
            weights = [weight for pos, weight in r['attractors']]
            n_attractors = len(r['attractors'])
            
            dominant_weight = max(weights)
            pitch_span_semitones = max(positions) - min(positions) + 1 if len(positions) > 1 else 1
            pitch_span_whole_tones = pitch_span_semitones / 2.0
            
            weight_entropy = -sum(w * np.log(w + 1e-10) for w in weights if w > 0)
            max_entropy = np.log(n_attractors) if n_attractors > 1 else 1.0
            concentration_index = 1 - weight_entropy / max_entropy if max_entropy > 0 else 1.0
            
            print(f"     组件分析:")
            print(f"       主导权重: {dominant_weight:.3f}")
            print(f"       吸引子数量: {n_attractors}")
            print(f"       音高跨度: {pitch_span_whole_tones:.1f} 全音")
            print(f"       集中度指数: {concentration_index:.3f}")
            print(f"       计算: ({dominant_weight:.3f}/{n_attractors}) × {pitch_span_whole_tones:.1f} × {concentration_index:.3f} = {r['improved_strength']:.4f}")
        
        return accuracy >= 80  # 80%以上准确率认为成功
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_real_music():
    """使用真实音乐测试改进的吸引子强度"""
    print(f"\n" + "="*80)
    print("🎼 真实音乐测试改进的吸引子强度")
    print("="*80)
    
    try:
        from unified_topological_analysis import UnifiedTopologicalAnalyzer
        
        analyzer = UnifiedTopologicalAnalyzer()
        
        # 创建不同风格的测试旋律
        test_melodies = [
            {
                'name': '五声音阶强调性',
                'pitches': [60, 62, 64, 67, 69, 72, 69, 67, 64, 62, 60],  # C D E G A C
                'expected_strength': '高强度',
                'description': '典型五声音阶，强调性音乐'
            },
            {
                'name': '十二音序列',
                'pitches': [60, 61, 63, 66, 70, 73, 77, 80, 84, 87, 91, 94],  # 模拟十二音
                'expected_strength': '低强度',
                'description': '无调性音乐，分散的音高'
            },
            {
                'name': '多调性混合',
                'pitches': [60, 64, 67, 72, 61, 65, 68, 73, 62, 66, 69, 74],  # 混合调性
                'expected_strength': '中等强度',
                'description': '多个调性中心的混合'
            }
        ]
        
        print(f"🎵 测试不同风格音乐的吸引子强度:")
        print(f"{'音乐风格':<15} {'改进强度':<12} {'分类':<10} {'预期':<10} {'验证'}")
        print("-" * 65)
        
        for melody in test_melodies:
            try:
                # 执行完整分析
                result = analyzer.analyze_work(melody['pitches'], melody['name'])
                
                if result and 'topology_metrics' in result and 'improved_attractor_strength' in result['topology_metrics']:
                    improved_strength = result['topology_metrics']['improved_attractor_strength']
                    
                    # 分类
                    if improved_strength >= 10.0:
                        actual_category = "高强度"
                    elif improved_strength >= 2.0:
                        actual_category = "中等强度"
                    else:
                        actual_category = "低强度"
                    
                    validation = "✅" if actual_category == melody['expected_strength'] else "⚠️"
                    
                    print(f"{melody['name']:<15} {improved_strength:<12.4f} {actual_category:<10} {melody['expected_strength']:<10} {validation}")
                    
                    # 显示详细信息
                    print(f"   描述: {melody['description']}")
                    print(f"   吸引子数量: {result['attractor_landscape']['attractor_count']}")
                    print(f"   强度单位: {result['topology_metrics']['strength_unit']}")
                    print(f"   强度定义: {result['topology_metrics']['strength_definition']}")
                    
                else:
                    print(f"{melody['name']:<15} {'分析失败':<12} {'N/A':<10} {melody['expected_strength']:<10} ❌")
                    
            except Exception as e:
                print(f"{melody['name']:<15} {'错误':<12} {'N/A':<10} {melody['expected_strength']:<10} ❌")
                print(f"   错误: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 真实音乐测试失败: {e}")
        return False

def compare_old_vs_new_strength():
    """对比原始强度和改进强度"""
    print(f"\n" + "="*80)
    print("🔄 原始强度 vs 改进强度对比")
    print("="*80)
    
    print("📊 理论对比:")
    print("   原始强度:")
    print("     公式: 主导权重 × √吸引子数量 × (1 + 位置分散度) × (1 + 权重集中度)")
    print("     单位: √个数 × 半音 (复杂，无明确物理意义)")
    print("     问题: 单位组合不合理，数值范围不稳定")
    
    print("\n   改进强度:")
    print("     公式: (主导权重 / 吸引子数量) × 音高跨度(全音) × 集中度指数")
    print("     单位: 全音/个数 (明确的物理意义)")
    print("     优势: 单位一致，物理意义清晰，数值范围稳定")
    
    print("\n✅ 改进效果:")
    print("   • 单位明确: 全音/个数，表示每个吸引子的平均影响范围")
    print("   • 物理意义: 标准化引力强度，符合中国传统音乐理论")
    print("   • 数值稳定: 典型范围0.1-20.0，便于解释和比较")
    print("   • 文化适应: 基于全音单位，符合中国音乐理论")

if __name__ == "__main__":
    print("💪 改进的吸引子强度测试")
    print("验证标准化引力强度的科学性和实用性")
    
    # 1. 基础强度计算测试
    basic_success = test_improved_attractor_strength()
    
    # 2. 真实音乐测试
    music_success = test_with_real_music()
    
    # 3. 对比分析
    compare_old_vs_new_strength()
    
    if basic_success and music_success:
        print(f"\n🎉 所有测试通过！")
        print(f"✅ 改进的吸引子强度计算验证成功")
        print(f"💪 标准化引力强度具有明确的物理意义和音乐学解释")
        print(f"🎼 单位为全音/个数，符合中国传统音乐理论")
        print(f"📊 数值范围稳定，便于解释和应用")
    else:
        print(f"\n⚠️ 部分测试需要进一步优化")
        if not basic_success:
            print(f"   - 基础强度计算需要调整")
        if not music_success:
            print(f"   - 真实音乐测试需要完善")
